<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonService_number extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id    = '#jwpf-addon-' . $this->addon->id;
        $settings    = $this->addon->settings;
        $number_type = (isset($settings->number_type) && $settings->number_type) ? $settings->number_type : 'type1';
        $number_color_one = (isset($settings->number_color_one) && $settings->number_color_one) ? $settings->number_color_one : '#2c2c2c';
        $number_color_two = (isset($settings->number_color_two) && $settings->number_color_two) ? $settings->number_color_two : '#0083ff';
        $title_color = (isset($settings->title_color) && $settings->title_color) ? $settings->title_color : '#696969';
        $unit_color = (isset($settings->unit_color) && $settings->unit_color) ? $settings->unit_color : '#333333';
        $border_color = (isset($settings->border_color) && $settings->border_color) ? $settings->border_color : '#696969';
        $output = '';
        if($number_type=='type1')
        {
            $jw_tab_item_one = (isset($settings->jw_tab_item_one) && $settings->jw_tab_item_one) ? $settings->jw_tab_item_one : '';
            $output .= '
                <style>
                    '.$addon_id.' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    '.$addon_id.' .fadeInUp {
                        -webkit-animation-name: fadeInUp;
                        animation-name: fadeInUp;
                    }
                    '.$addon_id.' .animated {
                        -webkit-animation-duration: 1s;
                        animation-duration: 1s;
                        -webkit-animation-fill-mode: both;
                        animation-fill-mode: both;
                    }
                    '.$addon_id.' .ind2-a2 {
                        width: 100%;
                        position: relative;
                        text-align: center;
                        margin: 0 auto 44px;
                    }
                    '.$addon_id.' .ind2-a3 {
                        display: inline-block;
                        margin: 0 45px;
                    }
                    '.$addon_id.' .ind2-a4 {
                        font-size: 60px;
                        line-height: 60px;
                        color: #2c2c2c;
                        text-align: center;
                        font-family: "Rubik";
                        margin-bottom: 12px;
                        display: none;
                    }
                    '.$addon_id.' .ind2-a5 {
                        font-size: 60px;
                        line-height: 60px;
                        color: '.$number_color_one.';
                        text-align: center;
                        font-family: "Rubik";
                        margin-bottom: 12px;
                    }
                    '.$addon_id.' .ind2-a5 span {
                        display: inline-block;
                        height: 60px;
                        margin: 0 0 12px;
                    }
                    '.$addon_id.' .ind2-a6 {
                        font-size: 16px;
                        line-height: 16px;
                        color: '.$title_color.';
                        text-align: center;
                    }
                    @keyframes counterDX {
                        0% {
                            opacity: 0;
                            -webkit-transform: translate3d(0, -100%, 0);
                            transform: translate3d(0, -100%, 0);
                            -webkit-filter: blur(10px);-moz-filter: blur(10px);-ms-filter: blur(10px);filter: blur(10px);
                        }
                
                        100% {
                            opacity: 1;
                            -webkit-transform: none;
                            transform: none;
                            -webkit-filter: blur(0px);-moz-filter: blur(0px);-ms-filter: blur(0px);filter: blur(0px);
                        }
                    }
                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            -webkit-transform: translate3d(0, 100%, 0);
                            transform: translate3d(0, 100%, 0);
                        }
                
                        to {
                            opacity: 1;
                            -webkit-transform: none;
                            transform: none;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="ind2-a2">
            ';
            $arr = [];
            foreach($jw_tab_item_one as $k => $v)
            {
                $output.='
                    <div class="ind2-a3">
                        <div class="ind2-a4">'.$v->number.'</div>
                        <div class="ind2-a5">
                ';
                $arr = str_split($v->number);
                $num = 0.5;
                foreach($arr as $kk => $vv)
                {
                    $output.='<span class="wow counterDX animated" data-wow-delay="'.$num.'s" style="visibility: visible; animation-duration: 1s; animation-delay: '.$num.'s; animation-name: counterDX;">'.$vv.'</span>';
                    $num+=0.2;
                }
                $output.='
                            </div>
                        <div class="ind2-a6 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">'.$v->title.'</div>
                    </div>
                ';
            }
            $output .='
                </div>
            ';
        }
        elseif($number_type=='type2')
        {
            $jw_tab_item_two = (isset($settings->jw_tab_item_two) && $settings->jw_tab_item_two) ? $settings->jw_tab_item_two : '';
            $type2_bg_img = (isset($settings->type2_bg_img) && $settings->type2_bg_img) ? $settings->type2_bg_img : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/05692aaa2eb7fadd95a4bf28633dacb9.jpeg';
            $output .= '
                <style>
                    '.$addon_id.' .p-index-a1 {
                        width: 100%;
                        height: 640px;
                        overflow: hidden;
                        position: relative;
                    }
                    '.$addon_id.' .p-index-a1 .p-index-a1-bg {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                    }
                    '.$addon_id.' .i300>img {
                        width: 100%;
                        height: 100%;
                    }
                    '.$addon_id.' .p-index-a1 .swiper-container8 {
                        width: 100%;
                        height: 100%;
                    }
                    '.$addon_id.' .swiper-container {
                        margin-left: auto;
                        margin-right: auto;
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                    }
                    '.$addon_id.' .p-index-a1 .swiper-container8 .swiper-wrapper {
                        width: 100%;
                        height: 100%;
                    }
                    '.$addon_id.' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }
                    '.$addon_id.' .p-index-a1 .swiper-container8 .swiper-wrapper .swiper-slide {
                        width: 100%;
                        height: 100%;
                        position: relative;
                    }
                    '.$addon_id.' .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                    }
                    '.$addon_id.' .p-index-a1 .p-index-a1-box {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        padding-top: 60px;
                        top: 0;
                        left: 0;
                    }
                    '.$addon_id.' .p-index-a1-ti1 {
                        text-align: center;
                        color: #333333;
                        font-size: 28px;
                        font-weight: bolder;
                        margin-bottom: 17.5px;
                    }
                    '.$addon_id.' .p-index-a1-ti2 {
                        text-align: center;
                        color: #5e5e5e;
                        font-size: 15px;
                        font-weight: lighter;
                        margin-bottom: 45px;
                    }
                    '.$addon_id.' .p-index-a1-ti3 {
                        width: 100%;
                        height: 135px;
                        margin: 0 auto 27.5px;
                    }
                    '.$addon_id.' .p-index-a1-ti3 img {
                        height: 100%;
                        width: auto;
                        margin: 0 auto;
                    }
                    '.$addon_id.' .p-index-a1-ti4 {
                        text-align: center;
                        font-size: 20px;
                        color: #2c2c2c;
                        margin-bottom: 58.5px;
                    }
                    '.$addon_id.' .p-index-a1-ti5 {
                        display: block;
                        text-align: center;
                        font-size: 14px;
                        line-height: 45px;
                        color: #fff;
                        background: #d00d30;
                        border-radius: 25px;
                        width: 140px;
                        height: 45px;
                        margin: 0 auto;
                    }
                    '.$addon_id.' img {
                        display: block;
                        border: none;
                    }
                    '.$addon_id.' .p-index-a1 .swiper-button-next {
                        width: 55px;
                        height: 55px;
                        position: absolute;
                        bottom: 15px;
                        background-size: 100% 100%;
                        right: 30%;
                        outline: none;
                    }
                    '.$addon_id.' .p-index-a1 .swiper-button-prev {
                        width: 55px;
                        height: 55px;
                        position: absolute;
                        bottom: 15px;
                        background-size: 100% 100%;
                        left: 30%;
                        outline: none;
                    }
                    '.$addon_id.' .swiper-button-prev, '.$addon_id.' .swiper-button-next {
                        background: none;
                        margin: 0;
                        padding: 0;
                        top: auto;
                        left: auto;
                        right: auto;
                        bottom: auto;
                        z-index: 999;
                    }
                    '.$addon_id.' .swiper-button-next:after, '.$addon_id.' .swiper-button-prev:after {
                        content: "";
                    }
                </style>
            ';
            $output .= '
                <div class="p-index-a1">
                    <div class="p-index-a1-bg i300"><img src="'.$type2_bg_img.'" alt="" oncontextmenu="return false;"></div>
                    <div class="swiper-container8 swiper-container swiper-container-horizontal swiper-container-ios">
                        <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-750px, 0px, 0px);">
            ';
            foreach($jw_tab_item_two as $k => $v)
            {
                if($v->detail_page_id){
                    $idurl= $this->removeqsvar($_SERVER['REQUEST_URI'],['page']);
                    $arrray=explode('&',$idurl);
                    foreach ($arrray as $key=>$value){
                        if(substr($value,0,3)=='id='){
                            $arrray[$key]='id='.base64_encode($v->detail_page_id);
                        }
                    }

                    $return=implode('&',$arrray);
                }
                else
                {
                    $return = $v->detail_page;
                }
                $output.='
                    <div class="swiper-slide" data-swiper-slide-index="'.$k.'" style="width: 375px;">
                        <div class="p-index-a1-box">
                            <div class="p-index-a1-ti1 " swiper-animate-effect="fadeInUp">'.$v->bigtitle.'</div>
                            <div class="p-index-a1-ti2 " swiper-animate-effect="fadeInUp">'.$v->bigdesc.'</div>
                            <div class="p-index-a1-ti3 ani" swiper-animate-effect="bounceIn" style="visibility: visible;" swiper-animate-style-cache="visibility: hidden;"><img src="'.$v->number_img.'" alt="" oncontextmenu="return false;"></div>
                            <div class="p-index-a1-ti4 ani" swiper-animate-effect="fadeInUp" style="visibility: visible;" swiper-animate-style-cache="visibility: hidden;">'.$v->title.'</div>
                            <a class="p-index-a1-ti5 " href="'.$return.'" target="'.$v->target.'" swiper-animate-effect="fadeInUp">'.$v->button.'</a>
                        </div>
                    </div>
                ';
            }
            $output.='                
                        </div>
                    </div>
                    <div class="swiper-button-next swiper-button-next8 i300"><img src="https://oss.lcweb01.cn/joomla/20220601/74f91ed2ee1fca59b8d830fd26446289.png" alt="" oncontextmenu="return false;"></div>
                    <div class="swiper-button-prev swiper-button-prev8 i300"><img src="https://oss.lcweb01.cn/joomla/20220601/5e771e9953240734e0804b66ae920d71.png" alt="" oncontextmenu="return false;"></div>
                </div>
                <script>
                    var Swiper13 = new Swiper("'.$addon_id.' .swiper-container", {
                        autoplay: true,
                        loop: true,
                        navigation: {
                            nextEl: "'.$addon_id.' .swiper-button-next",
                            prevEl: "'.$addon_id.' .swiper-button-prev",
                        },
                    });
                </script>
            ';
        }
        elseif($number_type=='type3')
        {
            $jw_tab_item_three = (isset($settings->jw_tab_item_three) && $settings->jw_tab_item_three) ? $settings->jw_tab_item_three : '';
            $output .= '
                <style>
                    '.$addon_id.' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    '.$addon_id.' .ju3_num {
                        margin: 1% 0 0;
                    }
                    '.$addon_id.' .flex {
                        display: -webkit-flex;
                    }
                    '.$addon_id.' a, '.$addon_id.' dd, '.$addon_id.' dl, '.$addon_id.' h1, '.$addon_id.' h2, '.$addon_id.' h3, '.$addon_id.' h4, '.$addon_id.' h5, '.$addon_id.' h6, '.$addon_id.' li, '.$addon_id.' p, '.$addon_id.' ul {
                        margin: 0;
                        padding: 0;
                        color: inherit;
                        font-size: inherit;
                        font-weight: inherit;
                    }
                    '.$addon_id.' dd, '.$addon_id.' dl, '.$addon_id.' li, '.$addon_id.' ul {
                        list-style: none;
                    }
                    '.$addon_id.' .ju3_num li:first-child {
                        padding-left: 0;
                    }
                    '.$addon_id.' .ju3_num li {
                        width: 20%;
                        position: relative;
                        padding-left: 3%;
                    }
                    '.$addon_id.' .ju3_num span {
                        color: '.$number_color_two.';
                        font-size: 80px;
                        font-family: "Rubik";
                        float: left;
                        vertical-align: middle;
                        margin-right: 20px;
                        letter-spacing: -3px;
                    }
                    '.$addon_id.' .ju3_num h3 {
                        color: '.$unit_color.';
                        font-size: 24px;
                        font-family: lighter;
                        padding: 10% 0 2%;
                    }
                    '.$addon_id.' .ju3_num p {
                        color: '.$title_color.';
                        font-size: 22px;
                    }
                    '.$addon_id.' .ju3_num li::after {
                        width: 2px;
                        height: 78px;
                        background: #f4f6f8;
                        content: "";
                        display: block;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        right: 0;
                        margin: auto 0;
                    }
                    '.$addon_id.' .ju3_num li:last-child::after {
                        width: 2px;
                        height: 0px;
                        background: #f4f6f8;
                        content: "";
                        display: block;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        right: 0;
                        margin: auto 0;
                    }
                    @media only screen and (max-width: 1024px){
                        '.$addon_id.' .ju3_num {
                            margin: 30px 0 0;
                            justify-content: center;
                            flex-wrap: wrap;
                        }
                        '.$addon_id.' .ju3_num li:first-child {
                            padding-left: 8%;
                        }
                        '.$addon_id.' .ju3_num li {
                            width: 33.33%;
                            position: relative;
                            margin-bottom: 25px;
                            padding-left: 8%;
                        }
                        '.$addon_id.' .ju3_num span {
                            font-size: 40px;
                            line-height: 1.2;
                            display: block;
                            float: none;
                        }
                        '.$addon_id.' .ju3_num h3 {
                            color: '.$unit_color.';
                            font-size: 13px;
                            font-family: lighter;
                        }
                        '.$addon_id.' .ju3_num p {
                            color: '.$title_color.';
                            font-size: 13px;
                        }
                    }
                </style>
            ';
            $output .= '
                <ul class="flex ju3_num wow fadeInUp animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;">
            ';
            $arr = [];
            foreach($jw_tab_item_three as $k => $v)
            {
                $output.='
                    <li>
                        <span>'.$v->number.'</span>
                        <h3>'.$v->unit.'</h3>
                        <p>'.$v->title.'</p>
                    </li>
                ';
            }
            $output .='
                </ul>
            ';
        }
        elseif($number_type=='type4')
        {
            $jw_tab_item_four = (isset($settings->jw_tab_item_four) && $settings->jw_tab_item_four) ? $settings->jw_tab_item_four : '';
            $ddd = intval(100/count($jw_tab_item_four))-2;
            $output .= '
                <style>
                    '.$addon_id.' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    @media only screen and (min-width: 768px){
                        '.$addon_id.' .t-ind3-a1d {
                            margin-top: 57px;
                        }
                        '.$addon_id.' .t-ind3-a1d1 {
                            width: '.$ddd.'%;
                            float: left;
                            margin-right: 2%;
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(1) {
                            font-size: 40px;
                            line-height: 40px;
                            position: relative;
                            width: max-content;
                            margin: 0 auto;
                            font-family: "uni";
                            color: '.$number_color_one.';
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(1)>div {
                            width: max-content;
                            position: absolute;
                            font-size: 16px;
                            line-height: 16px;
                            bottom: 0;
                            left: 100%;
                            color: '.$unit_color.';
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(2) {
                            text-align: center;
                            font-size: 16px;
                            color: '.$title_color.';
                            margin-top: 25px;
                        }
                    }
                    @media only screen and (max-width: 768px){
                        '.$addon_id.' .t-ind3-a1d {
                            margin-top: 25px;
                        }
                        '.$addon_id.' .t-ind3-a1d1 {
                            width: 50%;
                            float: left;
                            margin-bottom: 30px;
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(1) {
                            font-size: 33px;
                            line-height: 33px;
                            position: relative;
                            width: max-content;
                            font-family: "uni";
                            color: '.$number_color_one.';
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(1)>div {
                            width: max-content;
                            position: absolute;
                            font-size: 16.5px;
                            line-height: 16.5px;
                            bottom: 0;
                            left: 100%;
                            color: '.$unit_color.';
                        }
                        '.$addon_id.' .t-ind3-a1d1>div:nth-child(2) {
                            font-size: 15px;
                            line-height: 15px;
                            color: '.$title_color.';
                            margin-top: 12.5px;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="t-ind3-a1d clear">
            ';
            $arr = [];
            foreach($jw_tab_item_four as $k => $v)
            {
                $output.='
                    <div class="t-ind3-a1d1 wow fadeInDown animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
                        <div>'.$v->number.' <div>'.$v->unit.'</div></div>
                        <div>'.$v->title.'</div>
                    </div>
                ';
            }
            $output .='
                </div>
            ';
        }
        elseif($number_type=='type5')
        {
            $addonId = $this->addon->id;
            $jw_tab_item05 = isset($settings->jw_tab_item05) && $settings->jw_tab_item05 ? $settings->jw_tab_item05 : array(
                (Object)array(
                    'title' => '累计服务超30万会员',
                    'number' => '30',
                    'num_unit' => '万',
                ),
                (Object)array(
                    'title' => '国家软件著作权',
                    'number' => '5',
                    'num_unit' => '+',
                ),
                (Object)array(
                    'title' => '场馆选择了馆家',
                    'number' => '5000',
                    'num_unit' => '家',
                ),
                (Object)array(
                    'title' => '超百人的专业团队',
                    'number' => '100',
                    'num_unit' => '+',
                ),
                (Object)array(
                    'title' => '安全运行天数',
                    'number' => '1000',
                    'num_unit' => '+',
                ),
                (Object)array(
                    'title' => '客户续费率',
                    'number' => '90',
                    'num_unit' => '%',
                )
            );
            $output .= '
            <style>
                @font-face { 
                    font-family: \'AkzidenzGrotesk-BoldCondAlt\';
                    src: url(\''. JURI::base(true) .'/components/com_jwpagefactory/addons/service_number/assets/fonts/AkzidenzGrotesk-BoldCondAlt.woff\') format(\'woff\');
                    font-weight: normal;
                    font-style: normal;
                }
                '.$addon_id.' .index-data .item {
                    float:left;
                    width:33.33%;
                    list-style: none;
                    height:240px;
                }
                '.$addon_id.' .index-data .item-inner {
                    position:relative;
                    height:240px;
                    text-align:center;
                    border-right:1px solid '.$border_color.';
                }
                '.$addon_id.' .index-data .item:nth-child(3n) .item-inner {
                    border-right:0;
                }
                '.$addon_id.' .index-data .item:first-child .item-inner,
                '.$addon_id.' .index-data .item:nth-child(2) .item-inner,
                '.$addon_id.' .index-data .item:nth-child(3) .item-inner {
                    border-bottom:1px solid '.$border_color.';
                }
                '.$addon_id.' .index-data .text-wrap {
                    position:absolute;
                    top:50%;
                    width:100%;
                    font-size:16px;
                    color: '.$title_color.';
                    transform:translateY(-50%);
                }
                '.$addon_id.' .index-data .text-wrap .number-wrap {
                    display:block;
                    font:normal 108px/1 \'AkzidenzGrotesk-BoldCondAlt\';
                    color: '.$number_color_one.';
                }
                '.$addon_id.' .index-data .text-wrap .number-wrap .icon {
                    display:inline-block;
                    font-size:72px;
                    vertical-align:13px;
                    color: '.$unit_color.';
                }
                @media (max-width:1340px) {
                    '.$addon_id.' .index-data {
                        width:91%;
                        padding:80px 0;
                    }
                    '.$addon_id.' .index-data .item,
                    '.$addon_id.' .index-data .item-inner {
                        height:150px;
                    }
                    '.$addon_id.' .index-data .text-wrap .number-wrap {
                        font: normal 78px/1 \'AkzidenzGrotesk-BoldCondAlt\';
                    }
                    '.$addon_id.' .index-data .text-wrap .number-wrap .icon {
                        margin-left:-15px;2
                        font-size:48px;
                    }
                }
                @media (max-width:991px) {
                    '.$addon_id.' .index-data {
                        width:auto;
                        padding:7.5% 0;
                    }
                    '.$addon_id.' .index-data .item {
                        width:50%;
                        height:160px;
                    }
                    '.$addon_id.' .index-data .item-inner {
                        height:160px;
                    }
                    '.$addon_id.' .index-data .item:nth-child(2n+1) .item-inner {
                        border-right:1px solid '.$border_color.';
                    }
                    '.$addon_id.' .index-data .item:nth-child(2n) .item-inner {
                        border-right:0;
                    }
                    '.$addon_id.' .index-data .item:nth-child(4) .item-inner {
                        border-bottom:1px solid '.$border_color.';
                    }
                    '.$addon_id.' .index-data .text-wrap .number-wrap {
                        font-size:60px;
                    }
                    '.$addon_id.' .index-data .text-wrap .number-wrap .icon {
                        margin-left:-10px;
                        font-size:50px;
                        vertical-align:5px;
                    }
                    '.$addon_id.' .index-data .text-wrap {
                        font-size:14px;
                    }
                }
            </style>
            ';
            $output .= '
            <div class="index-data">
                <ul class="item-list clearfix">';
                    foreach ($jw_tab_item05 as $key => $item) {
                        $output .= '
                        <li class="item">
                            <div class="item-inner">
                                <h3 class="text-wrap">
                                    <span class="number-wrap">
                                        <strong class="number" data-num="' . $item->number . '">' . $item->number . '</strong>
                                        <strong class="icon">' . $item->num_unit . '</strong>
                                    </span>
                                    <span class="small-title">' . $item->title . '</span>
                                </h3>
                            </div>
                        </li>';
                    }
                $output .= '
                </ul>
            </div>
            <script>
                let indexDataTop'.$addonId.' = $(\''.$addon_id.' .index-data\').offset().top,
                    indexDataHeight'.$addonId.' = $(\''.$addon_id.' .index-data\').height(),
                    indexDatamin'.$addonId.' = indexDataTop'.$addonId.' - indexDataHeight'.$addonId.',
                    indexDataMax'.$addonId.' = indexDataTop'.$addonId.' + indexDataHeight'.$addonId.',
                    isChangeNumber'.$addonId.' = false;
                    //console.log(indexDataTop'.$addonId.');
                    //console.log(indexDataHeight'.$addonId.');
                    //console.log(indexDatamin'.$addonId.');
                    //console.log(indexDataMax'.$addonId.');
                $(window).on("scroll", function(){
                    let scorllTop = $(window).scrollTop();
                    //console.log(scorllTop);
                    if(!isChangeNumber'.$addonId.'){
                        if(indexDatamin'.$addonId.' < scorllTop && scorllTop < indexDataMax'.$addonId.') {
                            //console.log("执行了");
                            numberChange'.$addonId.'();
                            isChangeNumber'.$addonId.' = true;
                        }
                    }
                })
                //console.log("aaaaaaa",indexDataTop'.$addonId.');
                // 正则表达式
                let toThousands'.$addonId.' = (num = 0) => {
                   return num.toString().replace(/\d+/, function(n) {
                      return n.replace(/(\d)(?=(?:\d{3})+$)/g, \'$1,\');
                   });
                };
                $(\''.$addon_id.' .index-data .item-list .number\').each(function(){
                    let number = $(this).attr(\'data-num\'),
                        that = $(this),
                        html = that.html();
                    that.html(toThousands'.$addonId.'(html))
                });
				// 数字动画
				function numberChange'.$addonId.'() {
					$(\''.$addon_id.' .index-data .item-list .number\').each(function(){
						let number = $(this).attr(\'data-num\'),
							count = 0,
							timer = 2000,
							that = $(this);
						if(number > (timer / 4)) {
						    count = number - timer / 4;
						}
						let int = setInterval(function(){
							if(count < number) {
								count ++;
								that.html(toThousands'.$addonId.'(count))
							}else {
								clearInterval(int)
							}
						}, timer / number)
					})
				}
            </script>
            ';
        }
        elseif($number_type=='type6')
        {
            $addonId = $this->addon->id;
            $jw_tab_item06 = isset($settings->jw_tab_item06) && $settings->jw_tab_item06 ? $settings->jw_tab_item06 : array(
                
                (Object)array(
                    'title' => '覆盖全国26个省市区',
                    'intro' => '拥有加盟店260家',
                    'number' => '260',
                    'num_unit' => '+',
                    'num_start_offset' => '20',
                    'during' => '200',
                ),
                (Object)array(
                    'title' => '全国建立30000+销售终端',
                    'intro' => '打破东北地域限制，成功实现品牌焕新',
                    'number' => '30000',
                    'num_unit' => '+',
                    'num_start_offset' => '20',
                    'during' => '200',
                ),
                (Object)array(
                    'title' => '年营收3亿以上',
                    'intro' => '不断创新营销，拓展商业模式',
                    'number' => '3',
                    'num_unit' => '亿+',
                    'num_start_offset' => '2',
                    'during' => '2000',
                ),
                (Object)array(
                    'title' => '全国建立五大生产基地',
                    'intro' => '可实现30亿产值配置',
                    'number' => '30',
                    'num_unit' => '亿+',
                    'num_start_offset' => '20',
                    'during' => '200',
                ),
            );
            $jianjie6_color = (isset($settings->jianjie6_color) && $settings->jianjie6_color) ? $settings->jianjie6_color : '#9e9e9e';
            if (isset($settings->font_type6) && $settings->font_type6) {
                if (is_object($settings->font_type6)) {
                    $font_type6_md = $settings->font_type6->md;
                    $font_type6_sm = $settings->font_type6->sm;
                    $font_type6_xs = $settings->font_type6->xs;
                } else {
                    $font_type6_md = $settings->font_type6;
                    $font_type6_sm = $settings->font_type6_sm;
                    $font_type6_xs = $settings->font_type6_xs;
                }
            } else {
                $font_type6_md = '72';
                $font_type6_sm = '28';
                $font_type6_xs = '28';
            }

            if (isset($settings->dwfont_type6) && $settings->dwfont_type6) {
                if (is_object($settings->dwfont_type6)) {
                    $dwfont_type6_md = $settings->dwfont_type6->md;
                    $dwfont_type6_sm = $settings->dwfont_type6->sm;
                    $dwfont_type6_xs = $settings->dwfont_type6->xs;
                } else {
                    $dwfont_type6_md = $settings->dwfont_type6;
                    $dwfont_type6_sm = $settings->dwfont_type6_sm;
                    $dwfont_type6_xs = $settings->dwfont_type6_xs;
                }
            } else {
                $dwfont_type6_md = '36';
                $dwfont_type6_sm = '18';
                $dwfont_type6_xs = '18';
            }

            if (isset($settings->titlefont_type6) && $settings->titlefont_type6) {
                if (is_object($settings->titlefont_type6)) {
                    $titlefont_type6_md = $settings->titlefont_type6->md;
                    $titlefont_type6_sm = $settings->titlefont_type6->sm;
                    $titlefont_type6_xs = $settings->titlefont_type6->xs;
                } else {
                    $titlefont_type6_md = $settings->titlefont_type6;
                    $titlefont_type6_sm = $settings->titlefont_type6_sm;
                    $titlefont_type6_xs = $settings->titlefont_type6_xs;
                }
            } else {
                $titlefont_type6_md = '20';
                $titlefont_type6_sm = '16';
                $titlefont_type6_xs = '14';
            }

            if (isset($settings->jianjiefont_type6) && $settings->jianjiefont_type6) {
                if (is_object($settings->jianjiefont_type6)) {
                    $jianjiefont_type6_md = $settings->jianjiefont_type6->md;
                    $jianjiefont_type6_sm = $settings->jianjiefont_type6->sm;
                    $jianjiefont_type6_xs = $settings->jianjiefont_type6->xs;
                } else {
                    $jianjiefont_type6_md = $settings->jianjiefont_type6;
                    $jianjiefont_type6_sm = $settings->jianjiefont_type6_sm;
                    $jianjiefont_type6_xs = $settings->jianjiefont_type6_xs;
                }
            } else {
                $jianjiefont_type6_md = '16';
                $jianjiefont_type6_sm = '14';
                $jianjiefont_type6_xs = '14';
            }

            if (isset($settings->dwtop_type6) && $settings->dwtop_type6) {
                if (is_object($settings->dwtop_type6)) {
                    $dwtop_type6_md = $settings->dwtop_type6->md;
                    $dwtop_type6_sm = $settings->dwtop_type6->sm;
                    $dwtop_type6_xs = $settings->dwtop_type6->xs;
                } else {
                    $dwtop_type6_md = $settings->dwtop_type6;
                    $dwtop_type6_sm = $settings->dwtop_type6_sm;
                    $dwtop_type6_xs = $settings->dwtop_type6_xs;
                }
            } else {
                $dwtop_type6_md = '30';
                $dwtop_type6_sm = '12';
                $dwtop_type6_xs = '12';
            }


            $output .= '
                <style>
                    
                    '.$addon_id.' .clear::after {
                      content: "";
                      display: block;
                      clear: both;
                    }

                    @media only screen and (min-width: 1480px) {

                        '.$addon_id.' .madieer5-a4{width: 100%;position: relative;margin: 0 auto;display: flex;justify-content: space-between;}
                        '.$addon_id.' .madieer5-a5{position: relative;}
                        '.$addon_id.' .madieer5-a6{position: relative;margin-bottom: 30px;}
                        '.$addon_id.' .madieer5-a6>div{float: left;position: relative;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div{font-size: '.$font_type6_md.'px;line-height: '.$font_type6_md.'px;color:'.$number_color_one.';height: '.$font_type6_md.'px;font-family: "Cormorant";}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(1){position: relative;opacity: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(2){position: absolute;top: 0;right: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(2){font-size: '.$dwfont_type6_md.'px;line-height: '.$dwfont_type6_md.'px;color: '.$unit_color.';float: left;position: relative;top: '.$dwtop_type6_md.'px;}
                        '.$addon_id.' .madieer5-a7{font-size: '.$titlefont_type6_md.'px;line-height: '.$titlefont_type6_md.'px;color: '.$title_color.';margin-bottom: 10px;}
                        '.$addon_id.' .madieer5-a8{font-size: '.$jianjiefont_type6_md.'px;line-height: '.$jianjiefont_type6_md.'px;color: '.$jianjie6_color.';}
                        '.$addon_id.' .madieer5-a1 .bt2-a1{margin: 0 auto;}
                    }
                    @media only screen and (max-width: 1479px) and (min-width: 1024px) {

                        '.$addon_id.' .madieer5-a4{width: 100%;position: relative;margin: 0 auto;display: flex;justify-content: space-between;}
                        '.$addon_id.' .madieer5-a5{position: relative;}
                        '.$addon_id.' .madieer5-a6{position: relative;margin-bottom: 30px;}
                        '.$addon_id.' .madieer5-a6>div{float: left;position: relative;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div{font-size: '.$font_type6_md.'px;line-height: '.$font_type6_md.'px;color:'.$number_color_one.';height: '.$font_type6_md.'px;font-family: "Cormorant";}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(1){position: relative;opacity: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(2){position: absolute;top: 0;right: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(2){font-size: '.$dwfont_type6_md.'px;line-height: '.$dwfont_type6_md.'px;color: '.$unit_color.';float: left;position: relative;top: '.$dwtop_type6_md.'px;}
                        '.$addon_id.' .madieer5-a7{font-size: '.$titlefont_type6_md.'px;line-height: '.$titlefont_type6_md.'px;color: '.$title_color.';margin-bottom: 10px;}
                        '.$addon_id.' .madieer5-a8{font-size: '.$jianjiefont_type6_md.'px;line-height: '.$jianjiefont_type6_md.'px;color: '.$jianjie6_color.';}
                        '.$addon_id.' .madieer5-a1 .bt2-a1{margin: 0 auto;}
                    }
                    @media only screen and (max-width: 1023px) {

                        '.$addon_id.' .madieer5-a4{width: 100%;position: relative;text-align: center;overflow: hidden;overflow-x: auto;white-space: nowrap;}
                        '.$addon_id.' .madieer5-a5{position: relative;width: 50%;display: inline-block;padding: 0 14px;vertical-align: top;}
                        '.$addon_id.' .madieer5-a6{position: relative;margin-bottom: 8px;}
                        '.$addon_id.' .madieer5-a6>div{float: left;position: relative;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div{font-size: '.$font_type6_xs.'px;line-height: '.$font_type6_xs.'px;color:'.$number_color_one.';height: '.$font_type6_xs.'px;font-family: "Cormorant";}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(1){position: relative;opacity: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(1)>div:nth-child(2){position: absolute;top: 0;right: 0;}
                        '.$addon_id.' .madieer5-a6>div:nth-child(2){font-size: '.$dwfont_type6_xs.'px;line-height: '.$dwfont_type6_xs.'px;color: '.$unit_color.';float: left;position: relative;top: '.$dwtop_type6_xs.'px;}
                        '.$addon_id.' .madieer5-a7{font-size: '.$titlefont_type6_xs.'px;line-height: '.$titlefont_type6_xs.'px;color: '.$title_color.';margin-bottom: 4px;text-align: left;white-space: normal;}
                        '.$addon_id.' .madieer5-a8{font-size: '.$jianjiefont_type6_xs.'px;line-height: '.$jianjiefont_type6_xs.'px;color: '.$jianjie6_color.';text-align: left;white-space: normal;}
                        '.$addon_id.' .madieer5-a1 .bt2-a1{margin: 0 auto;display: block;}
                    }
                </style>
            ';

            $output .= '
                <div class="madieer5-a4 wow fadeInUp animated" data-wow-delay="0.4s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.4s; animation-name: fadeInUp;">';
                    foreach ($jw_tab_item06 as $key => $item) {
                        $keys=$key+1;
                        $output .= '
                            <div class="madieer5-a5">
                                <div class="madieer5-a6 clear">
                                    <div><div>' . $item->number . '</div><div id="madieernum'.$keys.'">' . $item->number . '</div></div><div>' . $item->num_unit . '</div>
                                </div>
                                <div class="madieer5-a7">' . $item->title . '</div>
                                <div class="madieer5-a8">'.$item->intro.'</div>
                            </div>
                       ';
                   }
            $output .= '</div>';

            if(isset($jw_tab_item06[0]->num_start_offset)){
                $output .= '<script>
                    if($("'.$addon_id.' .madieer5-a5").length){
                        var ind21111=false;';
                        foreach ($jw_tab_item06 as $key => $item) {
                            $keys = $key+1;
                            $output.='$("'.$addon_id.' #madieernum'.$keys.'").html(parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html())+('.(-$item->num_start_offset).'));
                            number("'.$addon_id.' #madieernum'.$keys.'",parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html())+('.(-$item->num_start_offset).'),parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html()),'.$item->during.',"madieernum'.$keys.'")
                            ';
                        }
                        
                        $output.='
                        setTimeout(function(){ind21111=true;},4000);
                        $(window).scroll(function(){
                            if(($(window).scrollTop()>($("'.$addon_id.' .madieer5-a5").offset().top-$(window).height()))&&($(window).scrollTop()<($("'.$addon_id.' .madieer5-a5").offset().top-$(window).height()+100))){
                                if(ind21111){
                                    ind21111=false;';
                                    foreach ($jw_tab_item06 as $key => $item) {
                                        $keys = $key+1;
                                        $output.='$("'.$addon_id.' #madieernum'.$keys.'").html(parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html())+('.(-$item->num_start_offset).'));
                                        number("'.$addon_id.' #madieernum'.$keys.'",parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html())+('.(-$item->num_start_offset).'),parseInt($("'.$addon_id.' #madieernum'.$keys.'").prev().html()),'.$item->during.',"madieernum'.$keys.'")
                                        ';
                                    }
                                    
                                    $output.='
                                }
                            }
                        });
                    }

                    function number(a1,b1,c1,d1=200,e1){var i=b1;clearInterval(e1);var e1=setInterval(function(){b1++;$(a1).html(b1);if(b1>=c1){clearInterval(e1);}},d1);}

                </script>
                ';
            }else{
                $output.=' <script>

                    if($("'.$addon_id.' .madieer5-a5").length){
                        var ind21111=false;
                        $("'.$addon_id.' #madieernum1").html(parseInt($("'.$addon_id.' #madieernum1").prev().html())-20);
                        $("'.$addon_id.' #madieernum2").html(parseInt($("'.$addon_id.' #madieernum2").prev().html())-20);
                        $("'.$addon_id.' #madieernum3").html(parseInt($("'.$addon_id.' #madieernum3").prev().html())-2);
                        $("'.$addon_id.' #madieernum4").html(parseInt($("'.$addon_id.' #madieernum4").prev().html())-20);
                        number("'.$addon_id.' #madieernum1",parseInt($("'.$addon_id.' #madieernum1").prev().html())-20,parseInt($("'.$addon_id.' #madieernum1").prev().html()),200,"madieernum1");
                        number("'.$addon_id.' #madieernum2",parseInt($("'.$addon_id.' #madieernum2").prev().html())-20,parseInt($("'.$addon_id.' #madieernum2").prev().html()),200,"madieernum2");
                        number("'.$addon_id.' #madieernum3",parseInt($("'.$addon_id.' #madieernum3").prev().html())-2,parseInt($("'.$addon_id.' #madieernum3").prev().html()),2000,"madieernum3");
                        number("'.$addon_id.' #madieernum4",parseInt($("'.$addon_id.' #madieernum4").prev().html())-20,parseInt($("'.$addon_id.' #madieernum4").prev().html()),200,"madieernum4");
                        setTimeout(function(){ind21111=true;},4000);
                        $(window).scroll(function(){
                            if(($(window).scrollTop()>($("'.$addon_id.' .madieer5-a5").offset().top-$(window).height()))&&($(window).scrollTop()<($("'.$addon_id.' .madieer5-a5").offset().top-$(window).height()+100))){
                                if(ind21111){
                                    ind21111=false;
                                    $("'.$addon_id.' #madieernum1").html(parseInt($("'.$addon_id.' #madieernum1").prev().html())-20);
                                    $("'.$addon_id.' #madieernum2").html(parseInt($("'.$addon_id.' #madieernum2").prev().html())-20);
                                    $("'.$addon_id.' #madieernum3").html(parseInt($("'.$addon_id.' #madieernum3").prev().html())-2);
                                    $("'.$addon_id.' #madieernum4").html(parseInt($("'.$addon_id.' #madieernum4").prev().html())-20);
                                    number("'.$addon_id.' #madieernum1",parseInt($("'.$addon_id.' #madieernum1").prev().html())-20,parseInt($("#madieernum1").prev().html()),200,"madieernum1");
                                    number("'.$addon_id.' #madieernum2",parseInt($("'.$addon_id.' #madieernum2").prev().html())-20,parseInt($("#madieernum2").prev().html()),200,"madieernum2");
                                    number("'.$addon_id.' #madieernum3",parseInt($("'.$addon_id.' #madieernum3").prev().html())-2,parseInt($("#madieernum3").prev().html()),2000,"madieernum3");
                                    number("'.$addon_id.' #madieernum4",parseInt($("'.$addon_id.' #madieernum4").prev().html())-20,parseInt($("#madieernum4").prev().html()),200,"madieernum4");
                                    setTimeout(function(){ind21111=true;},4000);
                                }
                            }
                        });
                    }
    
                    function number(a1,b1,c1,d1,e1){var i=b1;clearInterval(e1);var e1=setInterval(function(){b1++;$(a1).html(b1);if(b1>=c1){clearInterval(e1);}},d1);}
    
                </script>';
            }
        }
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
