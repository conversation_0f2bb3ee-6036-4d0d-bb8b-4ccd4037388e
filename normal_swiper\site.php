<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonNormal_swiper extends JwpagefactoryAddons
{
    function getStrWidthDot($value, $limit){
        if($limit){
            $titleStr = strip_tags($value);
            $show_dot = mb_strlen($titleStr,'utf8') > $limit;
            if($show_dot){
                $title = mb_substr($titleStr, 0, $limit, 'UTF-8') . '...';
            }else{
                $title = $titleStr;
            }
        }else{
            $title = $value;
        }
        return $title;
    }
    function getContent($item){
        $output = '';
        $settings = $this->addon->settings;
        $carousel_animation = isset($settings->carousel_animation)? $settings->carousel_animation : 1;
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';

        $content_title_fontsize_limit = (isset($settings->content_title_fontsize_limit) && $settings->content_title_fontsize_limit) ? $settings->content_title_fontsize_limit : '';
        $content_subtitle_fontsize_limit = (isset($settings->content_subtitle_fontsize_limit) && $settings->content_subtitle_fontsize_limit) ? $settings->content_subtitle_fontsize_limit : '';
        $description_fontsize_limit = (isset($settings->description_fontsize_limit) && $settings->description_fontsize_limit) ? $settings->description_fontsize_limit : '';

        if ($item->item_content_type === 'font') {
            $animate_class = $carousel_animation ? 'jwpf-animated fadeInDown' : '';
            $output .= '<div class="swiper-content ' . $animate_class . '">
                        <div class="first-title">' . $this -> getStrWidthDot($item->item_title, $content_title_fontsize_limit) . '</div>
                        <div class="subtitle">' . $this -> getStrWidthDot($item->item_subtitle, $content_subtitle_fontsize_limit) . '</div>
                        <div class="description">' . $this -> getStrWidthDot($item->item_description, $description_fontsize_limit) . '</div>
                    </div>';
        } else {
            $animate_class = $carousel_animation ? 'jwpf-animated fadeInDown' : '';
            $output .= '<div class="swiper-content ' . $animate_class . '">';
            if ($item->carousel_item_img_width) {
                $output .= '<div style="width:' . $item->carousel_item_img_width . '%;">';
            } else {
                $output .= '<div style="width: 100%;text-align: ' . $item_content_hori_align . '">';
            }
            if ($item->carousel_item_img) {
                $output .= '<img class="content-img" src="' . $item->carousel_item_img . '" alt="">';
            }

            $output .= '</div>';
            $output .= '</div>';
        }
        return $output;
    }

    public function render()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $output = '';

        $image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : 'layout1';

        $items = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : array();

        $carousel_arrow_type = (isset($settings->carousel_arrow_type) && $settings->carousel_arrow_type) ? $settings->carousel_arrow_type : 'icon';
        $arrow_img = (isset($settings->arrow_img) && $settings->arrow_img) ? $settings->arrow_img : '/components/com_jwpagefactory/addons/content_swiper/assets/images/normal_left.png';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $item_content_in_item = isset($settings->item_content_in_item) ? $settings->item_content_in_item : 1;
        if ($image_carousel_layout === 'layout3') {
            $items3 = (isset($settings->jw_image_carousel_item_3) && $settings->jw_image_carousel_item_3) ? $settings->jw_image_carousel_item_3 : array();
            $output .= '<section class="case" id="swiper_' . $addonId . '">
                <ul>';
                foreach ($items3 as $key => $item) {
                    $el = $item->is_link === 1 ? 'a' : 'div';
                    $link = $item->is_link === 1 ? 'href="' . $item->image_carousel_img_link . '"' : '';
                    $target = $item->link_open_new_window === 1 ? 'target="_blank"' : 'target="_self"';
                    $tagsStr = $item->tags;
                    $tags = explode(',', $tagsStr);
                    $tabs = $item->tabs;

                    // 文本块来源
                    $text_from = (isset($item->text_from) && $item->text_from) ? $item->text_from : 0;
                    // 文本块内容
                    $text_id = (isset($item->text_id) && $item->text_id) ? $item->text_id : null;
                    $text = '';
                    if($text_from == 1 && $text_id) {
                        $infos = JwPageFactoryBase::getInfoById($text_id);
                        if($infos){
                            $text=$infos->fulltext;
                        } else {
                            $text = '';
                        }
                    } else {
                        $text=$item->item_description;
                    }

                    $output .= '<li class="' . ($key === 0 ? 'active' : '') . '">
                        <' . $el . ' ' . $link . ' ' . $target . ' class="case-item-wrap">
                            <div class="case-item">
                                <h4 class="title">' . $item->item_title . '</h4>
                                <div class="desc">
                                    ' . $text . '
                                </div>
                            </div>
                            <div class="case-item-swiper">
                                <div class="logo">
                                    <img src="' . $item->logo . '" class="logo-img" alt="">
                                    <img src="' . $item->arrow . '" class="arrow-img" alt="">
                                </div>
                                <div class="desc">
                                    ' . $text . '
                                </div>';
                                if (is_array($tags) && count($tags) > 0) {
                                    $output .= '<div class="tags">';
                                    foreach ($tags as $tag) {
                                        $output .= '<span>' . $tag . '</span>';
                                    }
                                    $output .= '</div>';
                                }
                                if (is_array($tabs) && count($tabs) > 0) {
                                    $output .= '<div class="tabs">';
                                    foreach ($tabs as $tabkey => $tab) {
                                        $output .= '<div class="tab-item ' . ($tabkey === 0 ? 'active' : '') . '">
                                            <img src="' . $tab->tabs_normal_img . '" class="tab-img" alt="">
                                            <img src="' . $tab->tabs_active_img . '" class="tab-img-2" alt="">
                                            <span>' . $tab->title . '</span>
                                        </div>';
                                    }
                                    $output .= '</div>
                                    <div class="swiper-container">';
                                        // 图片轮播
                                        if (is_array($tabs) && count($tabs) > 0) {
                                            foreach ($tabs as $tabkey => $tab) {
                                                $swiper_tabs_content = $tab->swiper_tabs_content;
                                                $output .= '<div class="case-item-swiper-container swiper-' . $key . '-' . $tabkey . ' ' . ($tabkey === 0 ? 'active' : '') . '">
                                                    <div class="swiper-wrapper">';
                                                        if (is_array($swiper_tabs_content) && count($swiper_tabs_content) > 0) {
                                                            foreach ($swiper_tabs_content as $swiper_tabs_content_item) {

                                                                // 后台获取图片管理数据  2021.11.16
                                                                $ht_image_data = (isset($swiper_tabs_content_item->ht_image_data) && $swiper_tabs_content_item->ht_image_data) ? $swiper_tabs_content_item->ht_image_data : 0;
                                                                $ht_image_id = (isset($swiper_tabs_content_item->ht_image_id) && $swiper_tabs_content_item->ht_image_id) ? $swiper_tabs_content_item->ht_image_id : '';

                                                                $infos = JwPageFactoryBase::getimageById($ht_image_id);
                                                                $image = '';
                                                                if($infos){
                                                                    if($infos->state==1){
                                                                        $image =  $infos->image_intro ? $infos->image_intro : '';
                                                                    } else {
                                                                        $image = '';
                                                                    }
                                                                } else {
                                                                    $image = $swiper_tabs_content_item->swiper_img;
                                                                }

                                                                $output .= '<div class="swiper-slide">
                                                                    <img src="' . $image . '" alt="">
                                                                </div>';
                                                            }
                                                        }
                                                    $output .= '</div>
                                                    <div class="swiper-pagination"></div>
                                                </div>';
                                            }
                                        }
                                    $output .= '</div>';
                                }
                            $output .= '</div>
                        </' . $el . '>
                    </li>';
                }
                $output .= '</ul>
            </section>';
        } else if ($image_carousel_layout === 'layout4') {
            $arrow_background_4 = (isset($settings->arrow_background_4) && $settings->arrow_background_4)? $settings->arrow_background_4 : 'https://oss.lcweb01.cn/joomla/20250418/42e7e2ff6eac464ee5f2f54f67497fb0.png';
            $arrow_hover_background_4 = (isset($settings->arrow_hover_background_4) && $settings->arrow_hover_background_4)? $settings->arrow_hover_background_4 : 'https://oss.lcweb01.cn/joomla/20250418/42e7e2ff6eac464ee5f2f54f67497fb0.png';
            $img_4 = (isset($settings->img_4) && $settings->img_4)? $settings->img_4 : '';

            $output.='<div id="swiper_' . $addonId . '">
                <div class="w-full">
                    <div class="img-container">
                        <img src="'.$img_4.'" alt="">
                    </div>
                </div>
                <div class="btn-box">
                    <div class="prev-btn">
                        <img src="'.$arrow_background_4.'" alt="" class="img">
                        <img src="'.$arrow_hover_background_4.'" alt="" class="img2">
                    </div>
                    <div class="next-btn">
                        <img src="'.$arrow_background_4.'" alt="" class="img">
                        <img src="'.$arrow_hover_background_4.'" alt="" class="img2">
                    </div>
                </div>
            </div>';
        } else {
            $output .= '<div class="swiper-box" id="swiper_' . $addonId . '">
                <div class="swiper-container">
                    <div class="swiper-wrapper">';
                    foreach ($items as $key => $item) {
                        $output .= '<div class="swiper-slide" data-id="' . $key . '">';
                        if ($item->is_link === 1) {
                            $target = $item->link_open_new_window === 1 ? '_blank' : '';
                            $detail_page_link_config = $item -> detail_page_link_config ?? 'link';
                            if($detail_page_link_config === 'page'){
                                $id=base64_encode($item->detail_page_id);
                                $link = 'component/jwpagefactory/?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                $output .= '<a href="' . $link . '" target="' . $target . '" class="img-wrap">';
                            }else{
                                $output .= '<a href="' . $item->image_carousel_img_link . '" target="' . $target . '" class="img-wrap">';
                            }
                        }else{
                            $output.='<div class="img-wrap">';
                        }
                        // 后台获取图片管理数据  2021.11.16
                        $ht_image_data = (isset($item->ht_image_data) && $item->ht_image_data) ? $item->ht_image_data : 0;
                        $ht_image_id = (isset($item->ht_image_id) && $item->ht_image_id) ? $item->ht_image_id : '';

                        $infos = JwPageFactoryBase::getimageById($ht_image_id);
                        $image = '';
                        if($infos){
                            if($infos->state==1){
                                $image =  $infos->image_intro ? $infos->image_intro : '';
                            } else {
                                $image = '';
                            }
                        } else {
                            $image = $item->swiper_img;
                        }
                        if($ht_image_data){
                            $output .= '<img src=\' ' . $image . ' \' alt="">';
                        }else{
                            $output .= '<img src=\' ' . $item->image_carousel_img . ' \' alt="">';
                        }

                        if($item_content_in_item){
                            $output.=$this -> getContent($item);
                        }

                        if ($item->is_link === 1) {
                            $output .= '</a>';
                        }else{
                            $output.='</div>';
                        }

                        if(!$item_content_in_item){
                            $output.=$this -> getContent($item);
                        }
                        $output .= '</div>';
                    }
                    $output .= '</div>
                </div>';


            $output .= '<!-- Add Pagination -->
                <div class="swiper-pagination"></div>';
            if ($carousel_arrow_type === 'icon') {
                $output .= '
                            <!-- Add Arrows -->
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>';
            } else {
                $output .= '
                            <!-- Add Arrows -->
                        <div class="swiper-button-next img">
                            <img src=\'' . $arrow_img . '\' alt="">

                        </div>
                        <div class="swiper-button-prev img">
                            <img src=\'' . $arrow_img . '\' alt="">
                        </div>';
            }

            $output .= '</div>';
        }

        return $output;
    }

    public function stylesheets()
    {
        $swiper_status = $this->safeGetProp('swiper_status',0);
        if($swiper_status != 1){
            return '';
        }
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $swiper_status = $this->safeGetProp('swiper_status',0);
        if($swiper_status != 1){
            return '';
        }
        $js = array( JURI::base(true) .'/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : 'layout1';

        $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;
        $carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        //        按钮图片
        $arrow_img = (isset($settings->arrow_img) && $settings->arrow_img) ? $settings->arrow_img : '/components/com_jwpagefactory/addons/content_swiper/assets/images/normal_left.png';
        $arrow_img_active = (isset($settings->arrow_img_active) && $settings->arrow_img_active) ? $settings->arrow_img_active : '/components/com_jwpagefactory/addons/content_swiper/assets/images/active_left.png';

        //        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;
        //        分页器
        $pagination = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;

        if ($carousel_arrow === 1) {
            $navgition = "{
                nextEl: '#jwpf-addon-" . $addonId . " .swiper-button-next',
                prevEl: '#jwpf-addon-" . $addonId . " .swiper-button-prev',
            }";
        } else {
            $navgition = "{}";
        }

        if ($carousel_autoplay === 1) {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        } else {
            $autoplay = 'false';
        }

        if ($pagination === 1) {
            $pagination = '{
                    el: "#swiper_' . $addonId . ' .swiper-pagination",
                    clickable: true,
            }';
        } else {
            $pagination = '{}';
        }

        if ($image_carousel_layout === 'layout3') {
            $script = "jQuery(document).ready(function($){
                $('#swiper_$addonId.case ul li').hover(function() {
                    $(this).addClass('active');
                    $(this).siblings().removeClass('active');
                });
                $('#swiper_$addonId .tabs .tab-item').hover(function() {
                    $(this).addClass('active');
                    $(this).siblings().removeClass('active');
                    var index = $(this).index();
                    $(this).parents('.case-item-swiper').find('.case-item-swiper-container').eq(index).addClass('active');
                    $(this).parents('.case-item-swiper').find('.case-item-swiper-container').eq(index).siblings().removeClass('active');
                });";

            $items3 = (isset($settings->jw_image_carousel_item_3) && $settings->jw_image_carousel_item_3) ? $settings->jw_image_carousel_item_3 : array();
            if (is_array($items3) && count($items3) > 0) {
                foreach ($items3 as $key => $item) {
                    $tabs = $item->tabs;
                    if (is_array($tabs) && count($tabs) > 0) {
                        foreach ($tabs as $tabkey => $tab) {
                            $swiper_items_num = (isset($tab->swiper_items_num) && $tab->swiper_items_num)? $tab->swiper_items_num : 3;
                            $script .= "var swiper_$addonId$key$tabkey = new Swiper('#swiper_$addonId .case-item-swiper-container.swiper-$key-$tabkey', {
                                    observer:true,//修改swiper自己或子元素时，自动初始化swiper
                                    bserveParents:true,//修改swiper的父元素时，自动初始化swiper
                                    resizeObserver: true, //窗口容器变化
                                    on: {
                                        resize: function(){
                                            this.update(); //窗口变化时，更新Swiper的一些属性，如宽高等
                                        }
                                    },
                                    slidesPerView: $swiper_items_num,
                                    spaceBetween: 30,
                                    slidesPerGroup: $swiper_items_num,
                                    loop: false,
                                    autoplay: $autoplay,
                                    speed: $carousel_speed,
                                    pagination: {
                                        el: \"#swiper_$addonId .case-item-swiper-container.swiper-$key-$tabkey .swiper-pagination\",
                                        clickable: true,
                                    },
                                });";
                        }
                    }
                }
            }
            $script .= "})";
        } else if ($image_carousel_layout === 'layout4') {
            $arrow_move_distance_4 = (isset($settings->arrow_move_distance_4) && $settings->arrow_move_distance_4)? $settings->arrow_move_distance_4 : 100;
            $script = 'document.addEventListener(\'DOMContentLoaded\', function() {
                const imgContainer = document.querySelector(\'#swiper_'.$addonId.' .img-container\');
                const prevBtn = document.querySelector(\'#swiper_'.$addonId.' .prev-btn\');
                const nextBtn = document.querySelector(\'#swiper_'.$addonId.' .next-btn\');
                const images = document.querySelectorAll(\'#swiper_'.$addonId.' .img-container img\');
                const containerWidth = document.querySelector(\'#swiper_'.$addonId.' .w-full\').offsetWidth;

                let currentIndex = 0;
                let moveDistance = 0; // 定义移动距离变量
                const stepDistance = '.$arrow_move_distance_4.'; // 定义每次移动的距离，可以自由修改

                // 获取图片实际宽度
                let imageWidths = [];
                images.forEach(img => {
                    img.onload = function() {
                        calculateImageWidths();
                    };
                });

                // 计算图片宽度
                function calculateImageWidths() {
                    imageWidths = [];
                    images.forEach(img => {
                    // 由于图片等比例缩放，计算实际显示宽度
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    const displayWidth = img.offsetHeight * aspectRatio;
                    imageWidths.push(displayWidth);
                    });

                    // 初始化图片位置
                    updateImagePosition();
                }

                // 页面加载完立即计算一次
                calculateImageWidths();

                // 点击上一张按钮
                prevBtn.addEventListener(\'click\', function() {
                    // 在当前图片范围内移动
                    if (moveDistance > 0) {
                    moveDistance -= stepDistance;
                    if (moveDistance < 0) moveDistance = 0;
                    applyTransform();
                    } else if (currentIndex > 0) {
                    // 切换到上一张图片
                    currentIndex--;
                    moveDistance = 0;
                    updateImagePosition();
                    } else {
                    // 已经在第一张，不做操作
                    moveDistance = 0;
                    applyTransform();
                    }
                });

                // 点击下一张按钮
                nextBtn.addEventListener(\'click\', function() {
                    if (imageWidths.length === 0) {
                    calculateImageWidths();
                    return;
                    }

                    const currentImageWidth = imageWidths[currentIndex] || containerWidth;
                    const maxMoveDistance = Math.max(0, currentImageWidth - containerWidth);

                    // 在当前图片范围内移动
                    if (moveDistance < maxMoveDistance) {
                    moveDistance += stepDistance;
                    if (moveDistance > maxMoveDistance) {
                        moveDistance = maxMoveDistance;
                    }
                    applyTransform();
                    } else if (currentIndex < images.length - 1) {
                    // 切换到下一张图片
                    currentIndex++;
                    moveDistance = 0;
                    updateImagePosition();
                    } else {
                    // 已经是最后一张图片且滚动到最右边，不做操作
                    moveDistance = maxMoveDistance;
                    applyTransform();
                    }
                });

                // 更新图片位置
                function updateImagePosition() {
                    let baseDistance = 0;
                    for (let i = 0; i < currentIndex; i++) {
                    baseDistance += (imageWidths[i] || containerWidth);
                    }
                    imgContainer.style.transform = `translateX(-${baseDistance + moveDistance}px)`;
                }

                // 应用变换
                function applyTransform() {
                    let baseDistance = 0;
                    for (let i = 0; i < currentIndex; i++) {
                    baseDistance += (imageWidths[i] || containerWidth);
                    }
                    imgContainer.style.transform = `translateX(-${baseDistance + moveDistance}px)`;
                }

                // 窗口大小变化时重新计算
                window.addEventListener(\'resize\', function() {
                    calculateImageWidths();
                    moveDistance = 0;
                    updateImagePosition();
                });
            });';
        } else {
            $swiper_items_num = $this -> getResponsiveValues('swiper_items_num', ['md' => 1, 'sm' => 1, 'xs' => 1]);
            $swiper_items_space = $this -> getResponsiveValues('swiper_items_space', ['md' => 0, 'sm' => 0, 'xs' => 0]);
            $script = 'jQuery(document).ready(function($){
                //        初始化swiper配置项
                function initSwiper' . $addonId . '(needNavigation){
                    let settings={
                        loop: true,
                        loopFillGroupWithBlank: true,
                        observer: true,
                        observeParents:true,
                        pagination: ' . $pagination . ',
                        autoplay: ' . $autoplay . ',
                        speed: ' . $carousel_speed . ',
                        navigation: ' . $navgition . ',
                        slidesPerView: '.($swiper_items_num['md'] === '' ? 1 : $swiper_items_num['md']).',
                        spaceBetween: '.($swiper_items_space['md'] === '' ? 0 : $swiper_items_space['md']).',
                        breakpoints: {
                            0: {
                                slidesPerView: '.($swiper_items_num['xs'] === '' ? 1 : $swiper_items_num['xs']).',
                                spaceBetween: '.($swiper_items_space['xs'] === '' ? 0 : $swiper_items_space['xs']).',
                            },
                            768: {
                                slidesPerView: '.($swiper_items_num['sm'] === '' ? 1 : $swiper_items_num['sm']).',
                                spaceBetween: '.($swiper_items_space['sm'] === '' ? 0 : $swiper_items_space['sm']).',
                            },
                            992: {
                                slidesPerView: '.($swiper_items_num['md'] === '' ? 1 : $swiper_items_num['md']).',
                                spaceBetween: '.($swiper_items_space['md'] === '' ? 0 : $swiper_items_space['md']).',
                            },
                        },
                    }
                    let swiper = new Swiper("#jwpf-addon-' . $addonId . ' .swiper-container", settings);
                    return swiper;
                }
                //            根据屏幕初始化swiper
                initSwiper' . $addonId . '();
                //            屏幕改变大小再初始化一次
                window.onresize=function (){
                    initSwiper' . $addonId . '();
                }
                //          当按钮为图片时，鼠标滑过改变图片
                $("#jwpf-addon-' . $addonId . ' .img").mouseenter(e=>{
                    let img=$(e.currentTarget).find("img");
                    $(img).attr("src" , "' . $arrow_img_active . '");
                }).mouseout(e=>{
                    let img=$(e.currentTarget).find("img");
                    $(img).attr("src" , "' . $arrow_img . '");
                })
            })';
        }
        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;
        //        轮播
        $addonId = '#swiper_' . $this->addon->id;

        $image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : 'layout1';

        //        外部容器
        $carousel_height = '';
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
            } else {
                $carousel_height = $settings->carousel_height;
            }
        } else {
            $carousel_height = '';
        }
        $carousel_height_sm = '';
        if (isset($settings->carousel_height_sm) && $settings->carousel_height_sm) {
            if (is_object($settings->carousel_height_sm)) {
                $carousel_height_sm = $settings->carousel_height_sm->sm;
            } else {
                $carousel_height_sm = $settings->carousel_height_sm;
            }
        } else {
            $carousel_height_sm = '';
        }
        $carousel_height_xs = '';
        if (isset($settings->carousel_height_xs) && $settings->carousel_height_xs) {
            if (is_object($settings->carousel_height_xs)) {
                $carousel_height_xs = $settings->carousel_height_xs->xs;
            } else {
                $carousel_height_xs = $settings->carousel_height_xs;
            }
        } else {
            $carousel_height_xs = '';
        }

        //        分页器
        $pagination = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;
        $show_pagination = $pagination === 1 ? 'flex' : 'none';

        $pagination_left = '';
        if (isset($settings->bullet_position_hori) && $settings->bullet_position_hori) {
            if (is_object($settings->bullet_position_hori)) {
                $pagination_left = $settings->bullet_position_hori->md;
            } else {
                $pagination_left = $settings->bullet_position_hori;
            }
        } else {
            $pagination_left = '';
        }

        $pagination_left_sm = '';
        if (isset($settings->bullet_position_hori) && $settings->bullet_position_hori) {
            if (is_object($settings->bullet_position_hori)) {
                $pagination_left_sm = $settings->bullet_position_hori->sm;
            } else {
                $pagination_left_sm = $settings->bullet_position_hori_sm;
            }
        } else {
            $pagination_left_sm = '';
        }

        $bullet_height = (isset($settings->bullet_height) && $settings->bullet_height) ? $settings->bullet_height : 8;
        $bullet_width = (isset($settings->bullet_width) && $settings->bullet_width) ? $settings->bullet_width : 8;
        $bullet_background = (isset($settings->bullet_background) && $settings->bullet_background) ? $settings->bullet_background : '#000';
        $bullet_border_width = (isset($settings->bullet_border_width) && $settings->bullet_border_width) ? $settings->bullet_border_width : 0;
        $bullet_border_color = (isset($settings->bullet_border_color) && $settings->bullet_border_color) ? $settings->bullet_border_color : '';
        $bullet_margin = (isset($settings->bullet_margin) && $settings->bullet_margin) ? $settings->bullet_margin : 8;

        //        鼠标移入
        $arrow_animation = (isset($settings->arrow_animation) && $settings->arrow_animation) ? $settings->arrow_animation : 0;
        $bullet_active_height = (isset($settings->bullet_active_height) && $settings->bullet_active_height) ? $settings->bullet_active_height : 8;
        $bullet_active_width = (isset($settings->bullet_active_width) && $settings->bullet_active_width) ? $settings->bullet_active_width : 8;
        $bullet_active_background = (isset($settings->bullet_active_background) && $settings->bullet_active_background) ? $settings->bullet_active_background : '#007aff';
        $bullet_active_border_width = (isset($settings->bullet_active_border_width) && $settings->bullet_active_border_width) ? $settings->bullet_active_border_width : 0;
        $bullet_active_border_color = (isset($settings->bullet_active_border_color) && $settings->bullet_active_border_color) ? $settings->bullet_active_border_color : '';

        //      翻页按钮
        $carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        $show_arrow = $carousel_arrow === 1 ? 'flex' : 'none';
        $arrow_width = (isset($settings->arrow_width) && $settings->arrow_width) ? $settings->arrow_width : '';
        $arrow_height = (isset($settings->arrow_height) && $settings->arrow_height) ? $settings->arrow_height : '';
        $arrow_background = (isset($settings->arrow_background) && $settings->arrow_background) ? $settings->arrow_background : '';
        $arrow_color = (isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '';
        $arrow_font_size = (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? $settings->arrow_font_size : '';
        $arrow_border_width = (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? $settings->arrow_border_width : '';
        $arrow_border_color = (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? $settings->arrow_border_color : '';
        $arrow_border_radius = (isset($settings->arrow_border_radius) && $settings->arrow_border_radius) ? $settings->arrow_border_radius : '';
        $arrow_position_hori_verti = isset($settings->arrow_position_hori_verti) ? $settings->arrow_position_hori_verti : 'both';
        //        鼠标移入
        $arrow_hover_background = (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? $settings->arrow_hover_background : '';
        $arrow_hover_border_width = (isset($settings->arrow_hover_border_width) && $settings->arrow_hover_border_width) ? $settings->arrow_hover_border_width : '';
        $arrow_hover_border_color = (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? $settings->arrow_hover_border_color : '';
        $arrow_hover_border_radius = (isset($settings->arrow_hover_border_radius) && $settings->arrow_hover_border_radius) ? $settings->arrow_hover_border_radius : '';
        $arrow_hover_color = (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '';

        //        轮播项内容
        //        对齐
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';
        $item_content_verti_align = (isset($settings->item_content_verti_align) && $settings->item_content_verti_align) ? $settings->item_content_verti_align : 'center';
        $item_content_padding = $this -> getResponsiveValues('item_content_padding', ['md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0']);
        //          标题字体大小
        $content_title_fontsize = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize = $settings->content_title_fontsize->md;
            } else {
                $content_title_fontsize = $settings->content_title_fontsize;
            }
        } else {
            $content_title_fontsize = '';
        }

        $content_title_fontsize_sm = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_sm = $settings->content_title_fontsize->sm;
            } else {
                $content_title_fontsize_sm = $settings->content_title_fontsize_sm;
            }
        } else {
            $content_title_fontsize_sm = '';
        }
        $content_title_fontsize_xs = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_xs = $settings->content_title_fontsize->xs;
            } else {
                $content_title_fontsize_xs = $settings->content_title_fontsize_xs;
            }
        } else {
            $content_title_fontsize_xs = '';
        }
        //        行高
        $content_title_lineheight = (isset($settings->content_title_lineheight) && $settings->content_title_lineheight) ? $settings->content_title_lineheight : '60';
        $content_title_font_family = (isset($settings->content_title_font_family) && $settings->content_title_font_family) ? $settings->content_title_font_family : '';
        $title_style = '';
        $content_title_font_style = (isset($settings->content_title_font_style) && $settings->content_title_font_style) ? $settings->content_title_font_style : '';
        if (isset($content_title_font_style->underline) && $content_title_font_style->underline) {
            $title_style .= 'text-decoration:underline;';
        }
        if (isset($content_title_font_style->italic) && $content_title_font_style->italic) {
            $title_style .= 'font-style:italic;';
        }
        if (isset($content_title_font_style->uppercase) && $content_title_font_style->uppercase) {
            $title_style .= 'text-transform:uppercase;';
        }
        if (isset($content_title_font_style->weight) && $content_title_font_style->weight) {
            $title_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }
        $content_title_letterspace = (isset($settings->content_title_letterspace) && $settings->content_title_letterspace) ? $settings->content_title_letterspace : '';
        $content_title_text_color = (isset($settings->content_title_text_color) && $settings->content_title_text_color) ? $settings->content_title_text_color : '#333';

        $content_title_margin = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin = $settings->content_title_margin->md;
            } else {
                $content_title_margin = $settings->content_title_margin;
            }
        } else {
            $content_title_margin = '';
        }
        $content_title_margin_sm = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_sm = $settings->content_title_margin->sm;
            } else {
                $content_title_margin_sm = $settings->content_title_margin_sm;
            }
        } else {
            $content_title_margin_sm = '';
        }
        $content_title_margin_xs = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_xs = $settings->content_title_margin->xs;
            } else {
                $content_title_margin_xs = $settings->content_title_margin_xs;
            }
        } else {
            $content_title_margin_xs = '';
        }

        //      副标题
        $content_subtitle_fontsize = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize->md;
            } else {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize;
            }
        } else {
            $content_subtitle_fontsize = '';
        }
        $content_subtitle_fontsize_sm = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize->sm;
            } else {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize_sm;
            }
        } else {
            $content_subtitle_fontsize_sm = '';
        }
        $content_subtitle_fontsize_xs = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize->xs;
            } else {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize_xs;
            }
        } else {
            $content_subtitle_fontsize_xs = '';
        }
        $content_subtitle_letterspace = (isset($settings->content_subtitle_letterspace) && $settings->content_subtitle_letterspace) ? $settings->content_subtitle_letterspace : '';
        $content_subtitle_lineheight = (isset($settings->content_subtitle_lineheight) && $settings->content_subtitle_lineheight) ? $settings->content_subtitle_lineheight : '';
        $content_subtitle_font_family = (isset($settings->content_subtitle_font_family) && $settings->content_subtitle_font_family) ? $settings->content_subtitle_font_family : '';
        $content_subtitle_text_color = (isset($settings->content_subtitle_text_color) && $settings->content_subtitle_text_color) ? $settings->content_subtitle_text_color : '';
        $subtitle_style = '';
        //        描述
        $description_fontsize = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->md;
            } else {
                $description_fontsize = $settings->description_fontsize;
            }
        } else {
            $description_fontsize = '';
        }

        $description_fontsize_sm = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->sm;
            } else {
                $description_fontsize = $settings->description_fontsize_sm;
            }
        } else {
            $description_fontsize_sm = '';
        }
        $description_fontsize_xs = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize_xs = $settings->description_fontsize->xs;
            } else {
                $description_fontsize_xs = $settings->description_fontsize_xs;
            }
        } else {
            $description_fontsize_xs = '';
        }
        $description_letterspace = (isset($settings->description_letterspace) && $settings->description_letterspace) ? $settings->description_letterspace : '';
        $description_lineheight = (isset($settings->description_lineheight) && $settings->description_lineheight) ? $settings->description_lineheight : '';

        $description_font_family = (isset($settings->description_font_family) && $settings->description_font_family) ? $settings->description_font_family : '';
        $description_text_color = (isset($settings->description_text_color) && $settings->description_text_color) ? $settings->description_text_color : '';
        $description_style = '';

        $description_font_style = (isset($settings->description_font_style) && $settings->description_font_style) ? $settings->description_font_style : '';
        if (isset($description_font_style->underline) && $description_font_style->underline) {
            $description_style .= 'text-decoration:underline;';
        }
        if (isset($description_font_style->italic) && $description_font_style->italic) {
            $description_style .= 'font-style:italic;';
        }
        if (isset($description_font_style->uppercase) && $description_font_style->uppercase) {
            $description_style .= 'text-transform:uppercase;';
        }
        if (isset($description_font_style->weight) && $description_font_style->weight) {
            $description_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }

        $description_margin = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin = $settings->description_margin->md;
            } else {
                $description_margin = $settings->description_margin;
            }
        } else {
            $description_margin = '';
        }

        $description_margin_sm = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin_sm = $settings->description_margin->sm;
            } else {
                $description_margin_sm = $settings->description_margin_sm;
            }
        } else {
            $description_margin_sm = '';
        }

        $description_margin_xs = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin_xs = $settings->description_margin->xs;
            } else {
                $description_margin_xs = $settings->description_margin_xs;
            }
        } else {
            $description_margin_xs = '';
        }

        $arrow_position_hori = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori = $settings->arrow_position_hori->md;
            } else {
                $arrow_position_hori = $settings->arrow_position_hori;
            }
        } else {
            $arrow_position_hori = '';
        }
        $arrow_position_hori_sm = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori_sm = $settings->arrow_position_hori->sm;
            } else {
                $arrow_position_hori_sm = $settings->arrow_position_hori_sm;
            }
        } else {
            $arrow_position_hori_sm = '';
        }
        $arrow_position_hori_xs = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori_xs = $settings->arrow_position_hori->xs;
            } else {
                $arrow_position_hori_xs = $settings->arrow_position_hori_xs;
            }
        } else {
            $arrow_position_hori_xs = '';
        }

        $arrow_position_verti = '';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti = $settings->arrow_position_verti->md;
            } else {
                $arrow_position_verti = $settings->arrow_position_verti;
            }
        } else {
            $arrow_position_verti = '';
        }
        $arrow_position_verti_sm = '';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti_sm = $settings->arrow_position_verti->sm;
            } else {
                $arrow_position_verti_sm = $settings->arrow_position_verti;
            }
        } else {
            $arrow_position_verti_sm = '';
        }

        //      轮播项内容图是图片的时候
        $content_img_margin = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin = $settings->content_img_margin->md;
            } else {
                $content_img_margin = $settings->content_img_margin;
            }
        } else {
            $content_img_margin = '';
        }
        $content_img_margin_sm = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin_sm = $settings->content_img_margin->sm;
            } else {
                $content_img_margin_sm = $settings->content_img_margin_sm;
            }
        } else {
            $content_img_margin_sm = '';
        }
        $content_img_margin_xs = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin_xs = $settings->content_img_margin->xs;
            } else {
                $content_img_margin_xs = $settings->content_img_margin_xs;
            }
        } else {
            $content_img_margin_xs = '';
        }

        $bullet_opacity = (isset($settings->bullet_opacity) && $settings->bullet_opacity) ? ($settings->bullet_opacity / 100) : '';
        $arrow_gap = $this->getResponsiveValues('arrow_gap', ['md' => 0, 'sm' => 0, 'xs' => 0]);
        $item_content_bg = isset($settings->item_content_bg) ? $settings->item_content_bg : '0';
        $item_content_bg_style = (isset($settings->item_content_bg_style) && $settings->item_content_bg_style) ? $settings->item_content_bg_style : 'gradient';
        $item_content_bgColor = (isset($settings->item_content_bgColor) && $settings->item_content_bgColor) ? $settings->item_content_bgColor : 'rgba(0, 0, 0, 0.6)';
        $item_content_bgGradient = (isset($settings->item_content_bgGradient) && $settings->item_content_bgGradient) ? $settings->item_content_bgGradient : array(
            "color" => "rgba(0, 0, 0, 0.6)",
            "color2" => "transparent",
            "deg" => "0",
            "type" => "linear"
        );
        $img_show_type = (isset($settings->img_show_type) && $settings->img_show_type) ? $settings->img_show_type : 'cover';
        $img_fixed = isset($settings->img_fixed) ? $settings->img_fixed : '0';
        $img_width = $this -> getResponsiveValues('img_width', ['md' => 200, 'sm' => 200, 'xs' => 200]);
        $img_height = $this -> getResponsiveValues('img_height', ['md' => 250, 'sm' => 250, 'xs' => 250]);
        $swiper_items_border_layout1 = isset($settings->swiper_items_border_layout1) ? $settings->swiper_items_border_layout1 : '0';
        $swiper_items_border_layout1_width = isset($settings->swiper_items_border_layout1_width) ? $settings->swiper_items_border_layout1_width : '0';
        $swiper_items_border_layout1_radius = isset($settings->swiper_items_border_layout1_radius) ? $settings->swiper_items_border_layout1_radius : '0';
        $swiper_items_border_layout1_color = isset($settings->swiper_items_border_layout1_color) ? $settings->swiper_items_border_layout1_color : '0';
        $item_content_in_item = isset($settings->item_content_in_item) ? $settings->item_content_in_item : 1;
        $mobile_show_arrow = isset($settings->mobile_show_arrow) ? $settings->mobile_show_arrow : 0;
        $bullet_position_hori_mobile = isset($settings->bullet_position_hori_mobile) ? $settings->bullet_position_hori_mobile : 0;

        $output = '';

        if ($image_carousel_layout == 'layout3') {
            $items3 = (isset($settings->jw_image_carousel_item_3) && $settings->jw_image_carousel_item_3) ? $settings->jw_image_carousel_item_3 : array();
            if (isset($settings->items_num_3) && $settings->items_num_3) {
                if (is_object($settings->items_num_3)) {
                    $items_num_3 = $settings->items_num_3->md;
                    $items_num_3_sm = $settings->items_num_3->sm;
                    $items_num_3_xs = $settings->items_num_3->xs;
                } else {
                    $items_num_3 = $settings->items_num_3;
                    $items_num_3_sm = $settings->items_num_3_sm;
                    $items_num_3_xs = $settings->items_num_3_xs;
                }
            } else {
                $items_num_3 = '4';
                $items_num_3_sm = '2';
                $items_num_3_xs = '1';
            }
            if (isset($settings->title_height_3) && $settings->title_height_3) {
                if (is_object($settings->title_height_3)) {
                    $title_height_3 = $settings->title_height_3->md;
                    $title_height_3_sm = $settings->title_height_3->sm;
                    $title_height_3_xs = $settings->title_height_3->xs;
                } else {
                    $title_height_3 = $settings->title_height_3;
                    $title_height_3_sm = $settings->title_height_3_sm;
                    $title_height_3_xs = $settings->title_height_3_xs;
                }
            } else {
                $title_height_3 = '76';
                $title_height_3_sm = '';
                $title_height_3_xs = '';
            }
            $title_hide_line_3 = (isset($settings->title_hide_line_3) && $settings->title_hide_line_3) ? $settings->title_hide_line_3 : '2';

            if (isset($settings->description_height_3) && $settings->description_height_3) {
                if (is_object($settings->description_height_3)) {
                    $description_height_3 = $settings->description_height_3->md;
                    $description_height_3_sm = $settings->description_height_3->sm;
                    $description_height_3_xs = $settings->description_height_3->xs;
                } else {
                    $description_height_3 = $settings->description_height_3;
                    $description_height_3_sm = $settings->description_height_3_sm;
                    $description_height_3_xs = $settings->description_height_3_xs;
                }
            } else {
                $description_height_3 = '168';
                $description_height_3_sm = '';
                $description_height_3_xs = '';
            }
            $content_img_margin_3 = (isset($settings->content_img_margin_3) && $settings->content_img_margin_3) ? $settings->content_img_margin_3 : '0px 0px 21px 0px';
            $content_img_height_3 = (isset($settings->content_img_height_3) && $settings->content_img_height_3) ? $settings->content_img_height_3 : '44';
            $content_img_width_3 = (isset($settings->content_img_width_3) && $settings->content_img_width_3) ? $settings->content_img_width_3 : '25';

            //        描述
            $description_fontsize_3_inner = '';
            if (isset($settings->description_fontsize_3_inner) && $settings->description_fontsize_3_inner) {
                if (is_object($settings->description_fontsize_3_inner)) {
                    $description_fontsize_3_inner = $settings->description_fontsize_3_inner->md;
                } else {
                    $description_fontsize_3_inner = $settings->description_fontsize_3_inner;
                }
            } else {
                $description_fontsize_3_inner = '';
            }

            $description_fontsize_3_inner_sm = '';
            if (isset($settings->description_fontsize_3_inner) && $settings->description_fontsize_3_inner) {
                if (is_object($settings->description_fontsize_3_inner)) {
                    $description_fontsize_3_inner_sm = $settings->description_fontsize_3_inner->sm;
                } else {
                    $description_fontsize_3_inner_sm = $settings->description_fontsize_3_inner_sm;
                }
            } else {
                $description_fontsize_3_inner_sm = '';
            }
            $description_fontsize_3_inner_xs = '';
            if (isset($settings->description_fontsize_3_inner) && $settings->description_fontsize_3_inner) {
                if (is_object($settings->description_fontsize_3_inner)) {
                    $description_fontsize_3_inner_xs = $settings->description_fontsize_3_inner->xs;
                } else {
                    $description_fontsize_3_inner_xs = $settings->description_fontsize_3_inner_xs;
                }
            } else {
                $description_fontsize_3_inner_xs = '';
            }
            $description_lineheight_3_inner = (isset($settings->description_lineheight_3_inner) && $settings->description_lineheight_3_inner) ? $settings->description_lineheight_3_inner : '';
            $description_text_color_3_inner = (isset($settings->description_text_color_3_inner) && $settings->description_text_color_3_inner) ? $settings->description_text_color_3_inner : '';
            $description_style_3_inner = '';

            $description_font_style_3_inner = (isset($settings->description_font_style_3_inner) && $settings->description_font_style_3_inner) ? $settings->description_font_style_3_inner : '';
            if (isset($description_font_style_3_inner->underline) && $description_font_style_3_inner->underline) {
                $description_style_3_inner .= 'text-decoration:underline;';
            }
            if (isset($description_font_style_3_inner->italic) && $description_font_style_3_inner->italic) {
                $description_style_3_inner .= 'font-style:italic;';
            }
            if (isset($description_font_style_3_inner->uppercase) && $description_font_style_3_inner->uppercase) {
                $description_style_3_inner .= 'text-transform:uppercase;';
            }
            if (isset($description_font_style_3_inner->weight) && $description_font_style_3_inner->weight) {
                $description_style_3_inner .= 'font-weight:' . $content_title_font_style->weight . ';';
            }

            $description_margin_3_inner = '';
            if (isset($settings->description_margin_3_inner) && $settings->description_margin_3_inner) {
                if (is_object($settings->description_margin_3_inner)) {
                    $description_margin_3_inner = $settings->description_margin_3_inner->md;
                } else {
                    $description_margin_3_inner = $settings->description_margin_3_inner;
                }
            } else {
                $description_margin_3_inner = '';
            }

            $description_margin_3_inner_sm = '';
            if (isset($settings->description_margin_3_inner) && $settings->description_margin_3_inner) {
                if (is_object($settings->description_margin_3_inner)) {
                    $description_margin_3_inner_sm = $settings->description_margin_3_inner->sm;
                } else {
                    $description_margin_3_inner_sm = $settings->description_margin_3_inner_sm;
                }
            } else {
                $description_margin_3_inner_sm = '';
            }

            $description_margin_3_inner_xs = '';
            if (isset($settings->description_margin_3_inner) && $settings->description_margin_3_inner) {
                if (is_object($settings->description_margin_3_inner)) {
                    $description_margin_3_inner_xs = $settings->description_margin_3_inner->xs;
                } else {
                    $description_margin_3_inner_xs = $settings->description_margin_3_inner_xs;
                }
            } else {
                $description_margin_3_inner_xs = '';
            }
            $description_hide_line_3_inner = (isset($settings->description_hide_line_3_inner) && $settings->description_hide_line_3_inner)? $settings->description_hide_line_3_inner : '6';

            $items_space_3 = (isset($settings->items_space_3) && $settings->items_space_3)? $settings->items_space_3 : '15';

            if (isset($settings->description_height_3_inner) && $settings->description_height_3_inner) {
                if (is_object($settings->description_height_3_inner)) {
                    $description_height_3_inner = $settings->description_height_3_inner->md;
                    $description_height_3_inner_sm = $settings->description_height_3_inner->sm;
                    $description_height_3_inner_xs = $settings->description_height_3_inner->xs;
                } else {
                    $description_height_3_inner = $settings->description_height_3_inner;
                    $description_height_3_inner_sm = $settings->description_height_3_inner_sm;
                    $description_height_3_inner_xs = $settings->items_num_3_xs;
                }
            } else {
                $description_height_3_inner = '56';
                $description_height_3_inner_sm = '';
                $description_height_3_inner_xs = '';
            }

            $output .= $addonId . ' * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            ' . $addonId . ' {
                --swiper-pagination-bullet-width: ' . $bullet_width . 'px;
                --swiper-pagination-bullet-height: ' . $bullet_height . 'px;
                --swiper-pagination-color: #2854E4;
                --swiper-pagination-bullet-inactive-color: ' . $bullet_background . ';
                --swiper-pagination-bullet-inactive-opacity: ' . $bullet_opacity . ';
                --swiper-height: ' . $carousel_height . 'px;
                --swiper-pagination-bullet-horizontal-gap: ' . $bullet_margin . 'px;
                --item-width: 0 0 calc(100% / ' . $items_num_3 . ' - 6.67%);
                --item-active-width: 0 0 calc(100% / ' . $items_num_3 . ' + 20%);
                --item-gap: '.$items_space_3.'px;
            }

            ' . $addonId . ' a {
                text-decoration: none;
                color: #222;
            }

            ' . $addonId . ' ul,
            ' . $addonId . ' li {
                list-style: none;
            }

            ' . $addonId . '.case {
                width: 100%;
                overflow: hidden;
            }

            ' . $addonId . '.case ul {
                display: flex;
                align-items: stretch;
                justify-content: start;
                flex-wrap: wrap;
                margin: 0 calc(-1 * var(--item-gap) / 2);
                width: 100%;
                overflow: hidden;
            }

            ' . $addonId . '.case ul li {
                padding: 0 calc(var(--item-gap) / 2);
                margin-bottom: 30px;
                flex: var(--item-width);
                transition: all 0.3s ease-in-out;
                height: 100%;
                overflow: hidden;
            }

            ' . $addonId . '.case ul li .case-item-wrap {
                position: relative;
                overflow: hidden;
                height: 100%;
                min-height: 535px;
                display: block;
                overflow: hidden;
            }

            ' . $addonId . '.case ul li .case-item {
                padding: 49px 31px;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                transition: all 0.3s ease-in-out;
                overflow: hidden;
            }

            ' . $addonId . '.case ul li.active,
            ' . $addonId . '.case ul li:hover {
                flex: var(--item-active-width);
                width: var(--item-active-width);
            }

            ' . $addonId . '.case ul li.active .case-item-swiper,
            ' . $addonId . '.case ul li:hover .case-item-swiper {
                z-index: 2;
            }';

            foreach ($items3 as $key => $item) {
                $output .= $addonId . '.case ul li:nth-child(' . ($key + 1) . ') .case-item {
                    background: url("' . $item->image_carousel_img . '") no-repeat bottom right / cover;
                }';
            }

            $output .= $addonId . '.case ul li .case-item h4 {
                font-size: ' . $content_title_fontsize . 'px;
                line-height: ' . $content_title_lineheight . 'px;
                font-family: ' . $content_title_font_family . ';
                ' . $title_style . '
                color: ' . $content_title_text_color . ';
                margin: ' . $content_title_margin . ';
                height: ' . $title_height_3 . 'px;
                /* 超出2行显示省略号 */
                display: -webkit-box;
                -webkit-line-clamp: ' . $title_hide_line_3 . ';
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            ' . $addonId . '.case ul li .case-item .desc {
                font-size: ' . $description_fontsize . 'px;
                line-height: ' . $description_lineheight . 'px;
                font-family: ' . $description_font_family . ';
                ' . $description_style . '
                color: ' . $description_text_color . ';
                height: ' . $description_height_3 . 'px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: ' . $description_height_3 . ';
                -webkit-box-orient: vertical;
                margin: ' . $description_margin . ';
            }

            ' . $addonId . '.case ul li .case-item-swiper {
                background: #F1F6FE;
                height: 100%;
                position: absolute;
                z-index: 0;
                padding: 49px 32px;
                transition: all 0.3s ease-in-out;
                width: 100%;
            }

            ' . $addonId . '.case ul li .case-item-swiper .logo {
                margin: ' . $content_img_margin_3 . ';
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            ' . $addonId . '.case ul li .case-item-swiper .logo .logo-img {
                height: ' . $content_img_height_3 . 'px;
                object-fit: contain;
            }

            ' . $addonId . '.case ul li .case-item-swiper .logo .arrow-img {
                width: ' . $content_img_width_3 . 'px;
                object-fit: contain;
            }

            ' . $addonId . '.case ul li .case-item-swiper .desc {
                font-size: '.$description_fontsize_3_inner.'px;
                '.$description_style_3_inner.';
                line-height: '.$description_lineheight_3_inner['md'].'px;
                color: '. $description_text_color_3_inner. ';
                margin: '. $description_margin_3_inner. ';
                height: '. $description_height_3_inner. 'px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: '. $description_hide_line_3_inner. ';
            }

            ' . $addonId . '.case ul li .case-item-swiper .tags {
                display: flex;
                align-items: center;
                justify-content: start;
                background: linear-gradient(90deg, rgba(52, 102, 234, 0.08) 3%, rgba(52, 102, 234, 0) 100%);
                padding: 5px 17px;
                margin-bottom: 25px;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tags span {
                font-size: 16px;
                font-weight: normal;
                line-height: 28px;
                color: #3466EA;
                flex-shrink: 0;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tags span:not(:last-child) {
                margin-right: 30px;
                position: relative;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tags span:not(:last-child)::after {
                content: "";
                display: inline-block;
                width: 1px;
                height: 12px;
                background: #3466EA;
                position: absolute;
                right: -15px;
                top: 50%;
                transform: translateY(-50%);
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs,
            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item {
                display: flex;
                align-items: center;
                justify-content: start;
                cursor: pointer;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item + .tab-item {
                margin-left: 12px;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item img {
                margin-right: 6px;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item .tab-img-2 {
                display: none;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item.active .tab-img-2 {
                display: block;
            }

            ' . $addonId . '.case ul li .case-item-swiper .tabs .tab-item.active .tab-img {
                display: none;
            }

            ' . $addonId . '.case ul li .case-item-swiper .swiper-container {
            position: relative;
                height: calc(var(--swiper-height) + 29px + 25px + var(--swiper-pagination-bullet-height) + 13px);
            }

            ' . $addonId . '.case ul li .case-item-swiper-container {
                height: var(--swiper-height);
                width: 100%;
                position: absolute;
                top: 29px;
                left: 0;
                right: 0;
                bottom: 0;
                overflow: hidden;
                transition: all 0.3s ease-in-out;
                z-index: 0;
                opacity: 0;
                padding-bottom: calc(25px + var(--swiper-pagination-bullet-height));
                box-sizing: content-box;
            }

            ' . $addonId . '.case ul li .case-item-swiper-container.active {
                z-index: 1;
                opacity: 1;
            }

            ' . $addonId . '.case ul li .case-item-swiper .swiper-slide {
                width: 100%;
                height: 100%;
                text-align: center;
            }

            ' . $addonId . '.case ul li .case-item-swiper .swiper-slide img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            ' . $addonId . '.case ul li .case-item-swiper .swiper-pagination {
                bottom: 0;
                width: 100%;
                right: 0;
                box-sizing: border-box;
                margin: 0;
                height: calc(var(--swiper-pagination-bullet-height) + 13px);
            }
            ' . $addonId . ' .swiper-pagination-bullet {
                margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
                background: var(--swiper-pagination-bullet-inactive-color, #000);
                opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
                width: var(--swiper-pagination-bullet-width, 10px);
                height: var(--swiper-pagination-bullet-height, 10px);
            }
            ' . $addonId . ' .swiper-pagination-bullet-active {
                background: var(--swiper-pagination-color, #000);
                opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
            }
            @media screen and (max-width: 992px) and (min-width: 768px) {
                ' . $addonId . ' {
                    --swiper-height: ' . $carousel_height_sm . 'px;
                    --item-width: 0 0 calc(100% / ' . $items_num_3_sm . ');
                    --item-active-width: 0 0 calc(100% / ' . $items_num_3_sm . ');
                }
                ' . $addonId . '.case ul li .case-item h4 {
                    font-size: ' . $content_title_fontsize_sm . 'px;
                    margin: ' . $content_title_margin_sm . ';
                    height: ' . $title_height_3_sm . 'px;
                }
                ' . $addonId . '.case ul li .case-item .desc {
                    font-size: ' . $description_fontsize_sm . 'px;
                    height: ' . $description_height_3_sm . 'px;
                    margin: ' . $description_margin_sm . ';
                }
                ' . $addonId . '.case ul li .case-item-swiper .desc {
                    font-size: '.$description_fontsize_3_inner_sm.'px;
                    margin: '. $description_margin_3_inner_sm. ';
                    height: '. $description_height_3_inner_sm. 'px;
                }
            }
            @media screen and (max-width: 767px) {
                ' . $addonId . ' {
                    --swiper-height: ' . $carousel_height_xs . 'px;
                    --item-width: 0 0 calc(100% / ' . $items_num_3_xs . ');
                    --item-active-width: 0 0 calc(100% / ' . $items_num_3_xs . ');
                }
                ' . $addonId . '.case ul li .case-item h4 {
                    font-size: ' . $content_title_fontsize_xs . 'px;
                    margin: ' . $content_title_margin_xs . ';
                    height: ' . $title_height_3_xs . 'px;
                }
                ' . $addonId . '.case ul li .case-item .desc {
                    font-size: ' . $description_fontsize_xs . 'px;
                    height: ' . $description_height_3_xs . 'px;
                    margin: ' . $description_margin_xs . ';
                }
                ' . $addonId . '.case ul li .case-item-swiper .desc {
                    font-size: '.$description_fontsize_3_inner_xs.'px;
                    margin: '. $description_margin_3_inner_xs. ';
                    height: '. $description_height_3_inner_xs. 'px;
                }
            }';
        } elseif ($image_carousel_layout == 'layout4') {
            $arrow_width_4 = (isset($settings->arrow_width_4) && $settings->arrow_width_4)? $settings->arrow_width_4 : '28';
            $arrow_height_4 = (isset($settings->arrow_height_4) && $settings->arrow_height_4)? $settings->arrow_height_4 : '28';
            $arrow_gap_4 = (isset($settings->arrow_gap_4) && $settings->arrow_gap_4)? $settings->arrow_gap_4 : '32';
            $arrow_margin_4 = (isset($settings->arrow_margin_4) && $settings->arrow_margin_4)? $settings->arrow_margin_4 : '53px 0px 0px 0px';
            $carousel_height_4 = (isset($settings->carousel_height_4) && $settings->carousel_height_4)? $settings->carousel_height_4 : '400';

            $output.= $addonId . ' * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            '.$addonId . ' .w-full{
                width: 100%;
                height: '.$carousel_height_4.'px;
                margin: 0 auto;
                overflow: hidden;
                position: relative;
            }
            '.$addonId . ' .img-container {
                display: flex;
                transition: transform 0.5s ease;
                height: 100%;
            }
            '.$addonId . ' .img-container img{
                width: auto;
                height: 100%;
                max-width: none;
            }
            '.$addonId . ' .btn-box{
                display: flex;
                justify-content: center;
                align-items: center;
                margin: '.$arrow_margin_4.';
                object-fit: contain;
            }
            '.$addonId . ' .img-container .btn-box div{
                width: 50px;
                height: 50px;
                margin: 0 10px;
                cursor: pointer;
            }
            '. $addonId. ' .prev-btn,
            '. $addonId. ' .next-btn {
                width: '.$arrow_width_4.'px;
                height: '.$arrow_height_4.'px;
            }
            '. $addonId. ' .next-btn {
                margin-left: '.$arrow_gap_4.'px;
            }
            '. $addonId. ' .prev-btn img,
            '. $addonId. ' .next-btn img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
            '. $addonId. ' .prev-btn .img,
            '. $addonId. ' .next-btn .img {
                display: block;
            }
            '. $addonId. ' .prev-btn .img2,
            '. $addonId. ' .next-btn .img2 {
                display: none;
            }
            '. $addonId. ' .prev-btn:hover .img,
            '. $addonId. ' .next-btn:hover .img {
                display: none;
            }
            '. $addonId. ' .prev-btn:hover .img2,
            '. $addonId. ' .next-btn:hover .img2 {
                display: block;
            }
            '. $addonId. ' .next-btn img {
                transform: rotate(180deg);
            }';
        } else {
            $output .= $addonId . '{
                    position: relative;
                    width: 100%;
                    height: ' . $carousel_height . 'px;
                }
                ' . $addonId . ' a{
                    text-decoration: none;
                }
                ' . $addonId . ' .swiper-container{
                    width: 100%;
                    height: 100%;
                }
                ' . $addonId . ' .swiper-slide{
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;';
                    if($image_carousel_layout == 'layout1' && $swiper_items_border_layout1){
                        $output .= 'border-radius:'.$swiper_items_border_layout1_radius.';
                        border:'.$swiper_items_border_layout1_width.'px solid '.$swiper_items_border_layout1_color.';';
                    }
                $output.='}
                ' . $addonId . ' .swiper-pagination{
                    display: ' . $show_pagination . ';
                    width: ' . $bullet_active_width . 'px!important;
                    height: auto;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    right: ' . $pagination_left . 'px!important;
                    outline:none;
                }
                ' . $addonId . ' .swiper-pagination-bullet{
                    width: ' . $bullet_width . 'px;
                    height: ' . $bullet_height . 'px;
                    background: ' . $bullet_background . ';
                    border: ' . $bullet_border_width . 'px solid ' . $bullet_border_color . ';
                    border-radius: 50%;
                    opacity: 1;
                    margin-bottom:' . $bullet_margin . 'px;
                    transition: all ease-in-out 300ms;
                    outline:none;
                }
                ' . $addonId . ' .swiper-pagination-bullet:hover,
                ' . $addonId . ' .swiper-pagination-bullet.swiper-pagination-bullet-active{
                    width: ' . $bullet_active_width . 'px;
                    height: ' . $bullet_active_height . 'px;
                    background: ' . $bullet_active_background . ';
                    border: ' . $bullet_active_border_width . 'px solid ' . $bullet_active_border_color . ';
                    border-radius: 50%;
                    opacity: 1;
                }
                ' . $addonId . ' .swiper-button-next.img img,
                ' . $addonId . ' .swiper-button-prev.img img{
                    width: 100%;
                    height: 100%;
                }
                ' . $addonId . ' .swiper-button-next.img img{
                    transform: rotate(180deg);
                }
                ' . $addonId . ' .swiper-button-next.img::after,
                ' . $addonId . ' .swiper-button-prev.img::after{
                    display: none;
                }
                ' . $addonId . ' .swiper-button-next,
                ' . $addonId . ' .swiper-button-prev{
                    width: ' . $arrow_width . 'px;
                    height: ' . $arrow_height . 'px;
                    background: ' . $arrow_background . ';
                    border: ' . $arrow_border_width . 'px solid ' . $arrow_border_color . ';
                    border-radius: ' . $arrow_border_radius . 'px;
                    display: ' . $show_arrow . ';
                    top:' . $arrow_position_verti . 'px;
                    outline:none;
                    background-image: none;
                    position: absolute;
                }';
                if($arrow_position_hori_verti == 'both'){
                    $output .= $addonId . ' .swiper-button-prev{
                        left: ' . $arrow_position_hori . 'px;
                    }';
                    $output .= $addonId . ' .swiper-button-next{
                        right: ' . $arrow_position_hori . 'px;
                    }';
                } elseif($arrow_position_hori_verti == 'left') {
                    $output .= $addonId . ' .swiper-button-prev{
                        left: 0;
                        right: auto;
                    }';
                    $output .= $addonId . ' .swiper-button-next{
                        left: '.($arrow_gap['md'] + $arrow_width) . 'px;
                        right: auto;
                    }';
                } elseif($arrow_position_hori_verti == 'right'){
                    $output .= $addonId . ' .swiper-button-next{
                        right: 0;
                        left: auto;
                    }';
                    $output .= $addonId . ' .swiper-button-prev{
                        right: '.($arrow_gap['md'] + $arrow_width) . 'px;
                        left: auto;
                    }';
                }
                $output .= $addonId . ' .swiper-button-next img,' . $addonId . ' .swiper-button-prev img{
                    outline:none;
                }
                ' . $addonId . ' .swiper-button-next:hover,
                ' . $addonId . ' .swiper-button-prev:hover{
                    background: ' . $arrow_hover_background . ';
                    border: ' . $arrow_hover_border_width . 'px solid ' . $arrow_hover_border_color . ';
                    border-radius: ' . $arrow_hover_border_radius . 'px;
                    transition:all .5s;
                }';
                if($arrow_animation == 0){
                    $output .= $addonId . ' .swiper-button-next:hover{
                        transform:translateX(15px);
                    }';
                    $output .= $addonId . ' .swiper-button-prev:hover{
                        transform:translateX(-15px);
                    }';
                }
                $output .= $addonId . ' .swiper-button-next::after,
                ' . $addonId . ' .swiper-button-prev::after{
                    color: ' . $arrow_color . ';
                    font-size: ' . $arrow_font_size . 'px;
                }
                ' . $addonId . ' .swiper-button-next:hover::after,
                ' . $addonId . ' .swiper-button-prev:hover::after{
                    color: ' . $arrow_hover_color . ';
                    font-size: ' . $arrow_font_size . 'px;
                }
                ' . $addonId . ' .subtitle{
                    font-size: ' . $content_subtitle_fontsize . 'px;
                    line-height: ' . $content_subtitle_lineheight . 'px;
                    font-family: ' . $content_subtitle_font_family . 'px;
                    ' . $subtitle_style . ';
                    letter-spacing: ' . $content_subtitle_letterspace . ';
                    text-align: ' . $item_content_hori_align . ';
                    color: ' . $content_subtitle_text_color . ';
                }
                ' . $addonId . ' .description{
                    font-size: ' . $description_fontsize . 'px;
                    font-family: ' . $description_font_family . ';
                    color: ' . $description_text_color . ';
                    line-height: ' . $description_lineheight . 'px;
                    text-align: ' . $item_content_hori_align . ';
                    ' . $description_style . ';
                    letter-spacing: ' . $description_letterspace . ';
                    ' . $description_margin . '
                }
                ' . $addonId . ' .content-img{
                    width:auto!important;
                    height:auto!important;
                    display: inline-block!important;
                    margin: ' . $content_img_margin . ';
                }

                @media (min-width: 768px) and (max-width: 991px){
                    ' . $addonId . '{
                        height: ' . $carousel_height_sm . 'px;
                    }
                    ' . $addonId . ' .swiper-pagination{
                        right: ' . $pagination_left_sm . 'px;
                    }
                    ' . $addonId . ' .swiper-content{
                        padding: ' . $item_content_padding['sm'] . ';
                    }
                    ' . $addonId . ' .first-title{
                        font-size: ' . $content_title_fontsize_sm . 'px;
                        ' . $content_title_margin_sm . '
                    }
                    ' . $addonId . ' .subtitle{
                        font-size: ' . $content_subtitle_fontsize_sm . 'px;
                    }
                    ' . $addonId . ' .description{
                        font-size: ' . $description_fontsize_sm . 'px;
                        ' . $description_margin_sm . '
                    }
                    ' . $addonId . ' .content-img{
                        margin: ' . $content_img_margin_sm . ';
                    }
                    ' . $addonId . ' .swiper-button-next,
                    ' . $addonId . ' .swiper-button-prev{
                        top:' . $arrow_position_verti_sm . 'px;
                    }';

                    if($arrow_position_hori_verti == 'both'){
                        $output .= $addonId . ' .swiper-button-prev{
                            left: ' . $arrow_position_hori_sm . 'px;
                            right: auto;
                        }';
                        $output .= $addonId . ' .swiper-button-next{
                            right: ' . $arrow_position_hori_sm . 'px;
                            left: auto;
                        }';
                    } elseif($arrow_position_hori_verti == 'left') {
                        $output .= $addonId . ' .swiper-button-prev{
                            left: 0;
                            right: auto;
                        }';
                        $output .= $addonId . ' .swiper-button-next{
                            left: '.($arrow_gap['sm'] + $arrow_width) . 'px;
                            right: auto;
                        }';
                    } elseif($arrow_position_hori_verti == 'right'){
                        $output .= $addonId . ' .swiper-button-next{
                            right: 0;
                            left: auto;
                        }';
                        $output .= $addonId . ' .swiper-button-prev{
                            right: '.($arrow_gap['sm'] + $arrow_width) . 'px;
                            left: auto;
                        }';
                    }
                    $output.=$addonId . ' .swiper-slide img{';
                        if(!$img_fixed){
                            $output .= 'width: 100%;
                            height: 100%;';
                        }else{
                            $output .= 'width: ' . $img_width['sm'] . 'px;
                            height: ' . $img_height['sm'] . 'px;';
                        }
                    $output.='}';
                $output.='}';
                $output.='@media (max-width: 768px){
                    ' . $addonId . '{
                        height: ' . $carousel_height_xs . 'px;
                    }';
                    if(!$mobile_show_arrow){
                        $output .= $addonId . ' .swiper-button-next,
                        ' . $addonId . ' .swiper-button-prev{
                            display: none!important;
                        }';
                        $output .= $addonId . ' .swiper-button-next img,
                        ' . $addonId . ' .swiper-button-prev img{
                            display: none!important;
                        }';
                    }
                    if(!$bullet_position_hori_mobile){
                        $output .= $addonId . ' .swiper-pagination{
                            display: none!important;
                        }';
                    }
                    $output .= $addonId . ' .swiper-content{
                        padding: ' . $item_content_padding['xs'] . ';
                    }
                    ' . $addonId . ' .first-title{
                        font-size: ' . $content_title_fontsize_xs . 'px;
                        ' . $content_title_margin_xs . '
                    }
                    ' . $addonId . ' .subtitle{
                        font-size: ' . $content_subtitle_fontsize_xs . 'px;
                    }
                    ' . $addonId . ' .description{
                        font-size: ' . $description_fontsize_xs . 'px;
                        ' . $description_margin_xs . '
                    }
                    ' . $addonId . ' .content-img{
                        margin: ' . $content_img_margin_xs . ';
                    }';
                    if($arrow_position_hori_verti == 'both'){
                        $output .= $addonId . ' .swiper-button-prev{
                            left: ' . $arrow_position_hori_xs . 'px;
                        }';
                        $output .= $addonId . ' .swiper-button-next{
                            right: ' . $arrow_position_hori_xs . 'px;
                        }';
                    } elseif($arrow_position_hori_verti == 'left') {
                        $output .= $addonId . ' .swiper-button-prev{
                            left: 0;
                        }';
                        $output .= $addonId . ' .swiper-button-next{
                            left: '.($arrow_gap['xs'] + $arrow_width) . 'px;
                        }';
                    } elseif($arrow_position_hori_verti == 'right'){
                        $output .= $addonId . ' .swiper-button-next{
                            right: 0;
                        }';
                        $output .= $addonId . ' .swiper-button-prev{
                            right: '.($arrow_gap['xs'] + $arrow_width) . 'px;
                        }';
                    }
                    $output .= $addonId . ' .swiper-slide img{';
                        if(!$img_fixed){
                            $output .= 'width: 100%;
                            height: 100%;';
                        }else{
                            $output .= 'width: ' . $img_width['xs'] . 'px;
                            height: ' . $img_height['xs'] . 'px;';
                        }
                    $output.='}';
                $output.='}';

            $output .= $addonId . ' .swiper-slide img{';
                if(!$img_fixed){
                    $output .= 'width: 100%;
                    height: 100%;';
                }else{
                    $output .= 'width: ' . $img_width['md'] . 'px;
                    height: ' . $img_height['md'] . 'px;';
                }
                $output .= 'display: block;
                object-fit: '.$img_show_type.';
            }
            ' . $addonId . ' .img-wrap{
                width: 100%;
                height: 100%;
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            ' . $addonId . ' .swiper-content .first-title{
                font-size: ' . $content_title_fontsize . 'px;
                font-family: ' . $content_title_font_family . ';
                color: ' . $content_title_text_color . ';
                line-height: ' . $content_title_lineheight . 'px;
                text-align: ' . $item_content_hori_align . ';
                ' . $title_style . ';
                letter-spacing: ' . $content_title_letterspace . ';
                ' . $content_title_margin . '
            }
            ' . $addonId . ' .swiper-content{';
                if($item_content_in_item){
                    $output.='position:absolute;';
                }
                $output.='bottom: 0;
                left:0;
                right:0;
                margin:auto;
                display: flex;
                padding: ' . $item_content_padding['md'] . ';
                flex-direction: column;';
                if($item_content_bg){
                    if($item_content_bg_style == "gradient" && $item_content_bgGradient) {
                        $output .= 'background-image: ' . ($item_content_bgGradient->type ?: "linear") .'-gradient(';
                        if($item_content_bgGradient->type && $item_content_bgGradient->type == "radial") {
                            $output .= 'at ' . ($item_content_bgGradient->radialPos ?: "center center");
                        } else {
                            $output .= ($item_content_bgGradient->deg ?: 0) . 'deg';
                        }
                        $output .= ',
                            ' . $item_content_bgGradient->color . ' ' . ($item_content_bgGradient->pos ?: 0) .'%,';
                        $output .= $item_content_bgGradient->color2 . ' ' . ($item_content_bgGradient->pos2 ?: 100) .'%);';
                    } elseif($item_content_bg_style == "color") {
                        $output .= 'background-color: ' . $item_content_bgColor . ';';
                    }
                }
                if($item_content_hori_align === 'center'){
                    $output.='align-items: center;';
                }elseif($item_content_hori_align === 'left'){
                    $output.='align-items:start;';
                }elseif($item_content_hori_align === 'right'){
                    $output.='align-items:end;';
                }
                if ($item_content_verti_align === 'middle') {
                    $output .= 'top: 0;
                        justify-content: center;';
                } else if ($item_content_verti_align === 'bottom') {
                    $output .= 'justify-content: center';
                } else {
                    $output .= 'top:0;';
                }
            $output.='}';
            $output .= $addonId . ' .swiper-pagination{
                top:50%;
                right: 6px;
                margin:auto;
                transform:translateY(-43%);
                width:' . $bullet_width . 'px;
                height: auto;
            }';
        }

        return $output;
    }

    public static function getTemplate()
    {
        return '<#
            let id = data.id;
            let carousel_height=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.md:data.carousel_height;
            let carousel_height_sm=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.sm:data.carousel_height;
            let carousel_height_xs=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.xs:data.carousel_height;
        #>
        <style>
            #jwpf-addon-{{id}} .tips {
                height: 40px;
                line-height: 40px;
                margin-bottom: 30px;
                background: rgba(255,141,115,0.88);
                box-sizing: border-box;
                padding: 0 10px;
            }
        </style>
        <div class="tips">本图片仅为布局样式,请在预览页面中查看该插件滚动效果</div>
        <# if(data.image_carousel_layout === "layout3"){ #>
            <div style="margin: auto;">
                <img src="https://oss.lcweb01.cn/joomla/20250421/3502cb617d957fda749bf12d1f37de8f.png" alt="">
            </div>
        <# } else if(data.image_carousel_layout === "layout4") { #>
            <div style="margin: auto;">
                <img src="https://oss.lcweb01.cn/joomla/20250421/0bafdc1a951fda45001bb8fcdd643bcf.png" style="margin: auto;" alt="">
            </div>
        <# } else { #>
            <style>
                #jwpf-addon-{{id}} .swiper-container{
                    width: 100%;
                    height: {{carousel_height}}px;
                }
                #jwpf-addon-{{id}} .swiper-container img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                @media (min-width: 767px) and (max-width: 991px){
                    #jwpf-addon-{{id}} .swiper-container{
                        height: {{carousel_height_sm}}px;
                    }
                }
                @media (max-width: 767px){
                    #jwpf-addon-{{id}} .swiper-container{
                        height: {{carousel_height_xs}}px;
                    }
                }
            </style>
            <div class="swiper-box" id="swiper_{{data.id}}">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <# _.each(data.jw_image_carousel_item, function(carousel_item){ #>
                            <div class="swiper-slide">
                                <# if(carousel_item.is_link){
                                    let target=carousel_item.link_open_new_window===1?"_blank":"";
                                #>
                                    <a href="{{carousel_item.image_carousel_img_link}}" target="{{target}}">
                                        <img src=\'{{ carousel_item.image_carousel_img }}\' alt="">
                                    </a>
                                <# }else{ #>
                                    <img src=\'{{ carousel_item.image_carousel_img }}\' alt="">
                                <# } #>
                            </div>
                        <# }) #>
                    </div>
                </div>
            </div>
        <# } #>';
    }

    // 处理适配的值
    public function getResponsiveValues($baseName, $defaults = null) {

        $settings = $this->addon->settings;

        // 设置默认值（支持完整覆盖或部分覆盖）
        $finalDefaults = array_merge([
            'md' => '',
            'sm' => '',
            'xs' => ''
        ], (array)$defaults);

        // 动态构建属性名
        $value = $baseName;
        $valueSm = $baseName . '_sm';
        $valueXs = $baseName . '_xs';

        // 检查主属性是否存在
        if (isset($settings->$value)) {
            $mainValue = $settings->$value;

            if ($mainValue && is_object($mainValue)) {
                // 对象处理：从对象属性获取值
                return [
                    'md' => $mainValue->md ?? $finalDefaults['md'],
                    'sm' => $mainValue->sm ?? $finalDefaults['sm'],
                    'xs' => $mainValue->xs ?? $finalDefaults['xs']
                ];
            } elseif ($mainValue) {
                // 标量值处理：从后缀属性获取响应值
                return [
                    'md' => $mainValue,
                    'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
                    'xs' => $settings->$valueXs ?? $finalDefaults['xs']
                ];
            }
        }

        // 当主属性存在但为假值时（如0、空字符串等），返回默认值
        return $finalDefaults;
    }

    /**
     * 处理普通变量 安全获取对象属性值
     * @param object $obj 对象实例
     * @param string $prop 属性名称
     * @param mixed $default 默认值 (默认为'percent')
     * @param bool $strict 严格模式 (true: 0/false视为有效值; false: 0/false视为空值)
     * @return mixed 属性值或默认值
     */
    public function safeGetProp($prop, $default = '', $strict = false) {

        $settings = $this->addon->settings;
        // 检查属性是否存在
        if (!isset($settings->$prop)) {
            return $default;
        }
        $value = $settings->$prop;

        // 严格模式：0/false视为有效值
        if ($strict) {
            return $value;
        }

        // 非严格模式：空值检查
        return $value ? $value : $default;
    }
}
