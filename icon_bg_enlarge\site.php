<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonIcon_bg_enlarge extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        $iconImg = (isset($settings->iconImg) && $settings->iconImg)? $settings->iconImg : 'https://oss.lcweb01.cn/joomla/20210802/48a9be5887a8fbd7242cf6fb676c18bb.png';
        $content_text = (isset($settings->content_text) && $settings->content_text) ? $settings->content_text : '拒绝用户体验差';
        $is_link = (isset($settings->is_link) && $settings->is_link) ? $settings->is_link : 0;
        $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : null;

        $output = '<a ';
        $link = '';
        if($is_link == 1 && $detail_page_id){
            $id = base64_encode($detail_page_id);
            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($link != ''){
            $output .= 'target="_blank" href="' . $link .'" ';
        }
        $output .= 'class="icon-box">';
        $output .= '<div class="icon"><img src=\'' . $iconImg . '\'></div>
            <div class="text">' . $content_text . '</div>
        </a>';

        return $output;
    }

    public function scripts()
    {

    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;

        $js = 'jQuery(function($){
		    
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        // 标题文字大小
        $content_fontsize = (isset($settings->content_fontsize) && $settings->content_fontsize) ? $settings->content_fontsize : 14;
        // 背景宽度
        $icon_bg_width = (isset($settings->icon_bg_width) && $settings->icon_bg_width) ? $settings->icon_bg_width : 84;
        // 背景高度
        $icon_bg_height = (isset($settings->icon_bg_height) && $settings->icon_bg_height) ? $settings->icon_bg_height : 84;
        // 背景光圈宽度
        $icon_aperture_width = (isset($settings->icon_aperture_width) && $settings->icon_aperture_width) ? $settings->icon_aperture_width : 100;
        // 背景光圈高度
        $icon_aperture_height = (isset($settings->icon_aperture_height) && $settings->icon_aperture_height) ? $settings->icon_aperture_height : 100;
        /* 正常 */
        // 标题颜色
        $contentColor = (isset($settings->contentColor) && $settings->contentColor) ? $settings->contentColor : "#258ffc";
        // 图标背景选项
        $icon_bg_style = (isset($settings->icon_bg_style) && $settings->icon_bg_style) ? $settings->icon_bg_style : "color";
        // 图标背景颜色
        $bgColor = (isset($settings->bgColor) && $settings->bgColor) ? $settings->bgColor : "#2464F5";
        // 图标渐变色背景
        $bgGradient = (isset($settings->bgGradient) && $settings->bgGradient) ? $settings->bgGradient : null;
        // 光圈颜色
        $apertureColor = (isset($settings->apertureColor) && $settings->apertureColor) ? $settings->apertureColor : "rgba(37,143,252,0.3)";
        /* 滑过 */
        // 标题颜色
        $contentColor_hover = (isset($settings->contentColor_hover) && $settings->contentColor_hover) ? $settings->contentColor_hover : "#258ffc";
        // 图标背景颜色
        $bgColor_hover = (isset($settings->bgColor_hover) && $settings->bgColor_hover) ? $settings->bgColor_hover : "#F7810C";
        // 图标渐变色背景
        $bgGradient_hover = (isset($settings->bgGradient_hover) && $settings->bgGradient_hover) ? $settings->bgGradient_hover : null;
        // 光圈颜色
        $apertureColor_hover = (isset($settings->apertureColor_hover) && $settings->apertureColor_hover) ? $settings->apertureColor_hover : "rgba(251,167,8,0.3)";

        $css =
            $addonId . ' * {
				margin: 0;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .icon-box {
				width: 100%;
				height: auto;
				display: block;
			}
			' . $addonId . ' .icon-box .icon {
				width: ' . $icon_bg_width . 'px;
				height: ' . $icon_bg_height . 'px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;';
				if($icon_bg_style == "color") {
                    $css .= 'background-color: ' . $bgColor . ';';
                }
                if($icon_bg_style == "gradient" && $bgGradient) {
                    $css .= 'background-image: ' . ($bgGradient->type ?: "linear") .'-gradient(';
                    if($bgGradient->type && $bgGradient->type == "radial") {
                        $css .= 'at ' . ($bgGradient->radialPos ?: "center center");
                    } else {
                        $css .= ($bgGradient->deg ?: 0) . 'deg';
                    }
                    $css .= ',
                        ' . $bgGradient->color . ' ' . ($bgGradient->pos ?: 0) .'%,';
                    $css .= $bgGradient->color2 . ' ' . ($bgGradient->pos2 ?: 100) .'%);';
                }
            $css .= 'position: relative;
				margin: auto;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .icon-box .icon:after {
			    content: "";
			    width: ' . $icon_aperture_width . 'px;
				height: ' . $icon_aperture_height . 'px;
				border-radius: 50%;
				background-color: ' . $apertureColor . ';
				position: absolute;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .icon-box .text {
			    font-size: ' . $content_fontsize . 'px;
                line-height: 32px;
                color: ' . $contentColor . ';
                text-align: center;
                margin-top: 20px;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .icon-box:hover .icon {';
                if($icon_bg_style == "color") {
                    $css .= 'background-color: ' . $bgColor_hover . ';';
                }
                if($icon_bg_style == "gradient" && $bgGradient_hover) {
                    $css .= 'background-image: ' . ($bgGradient_hover->type ?: "linear") .'-gradient(';
                    if($bgGradient_hover->type && $bgGradient_hover->type == "radial") {
                        $css .= 'at ' . ($bgGradient_hover->radialPos ?: "center center");
                    } else {
                        $css .= ($bgGradient_hover->deg ?: 0) . 'deg';
                    }
                    $css .= ',
                                ' . $bgGradient_hover->color . ' ' . ($bgGradient_hover->pos ?: 0) .'%,';
                    $css .= $bgGradient_hover->color2 . ' ' . ($bgGradient_hover->pos2 ?: 100) .'%);';
                }
            $css .= '}
			' . $addonId . ' .icon-box:hover .icon:after {
				background-color: ' . $apertureColor_hover . ';
				animation: h505 2s linear infinite;
			}
			' . $addonId . ' .icon-box:hover .text {
                color: ' . $contentColor_hover . ';
			}
			@keyframes h505{
			    0% {
			        transform: scale(1);
			        opacity: 1;
			    }
			    100% {
			        transform: scale(1.5);
			        opacity: 0;
			    }
			}
			@media (min-width: 768px) and (max-width: 991px) {
			   
			}
			@media (max-width: 767px) {
			    
			}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		// 图标
		var iconImg = data.iconImg || "https://oss.lcweb01.cn/joomla/20210802/48a9be5887a8fbd7242cf6fb676c18bb.png";
		// 文字
		var content_text = data.content_text || "拒绝用户体验差";
		// 标题文字大小
		var content_fontsize = data.content_fontsize || 14;
		// 背景宽度
		var icon_bg_width = data.icon_bg_width || 84;
		// 背景高度
		var icon_bg_height = data.icon_bg_height || 84;
		// 背景光圈宽度
		var icon_aperture_width = data.icon_aperture_width || 100;
		// 背景光圈高度
		var icon_aperture_height = data.icon_aperture_height || 100;
		/* 正常 */
		// 标题颜色
		var contentColor = data.contentColor || "#258ffc";
		// 图标背景选项
		var icon_bg_style = data.icon_bg_style || "color";
		// 图标背景颜色
		var bgColor = data.bgColor || "#2464F5";
		// 图标渐变色背景
		var bgGradient = data.bgGradient;
		// 光圈颜色
		var apertureColor = data.apertureColor || "rgba(37,143,252,0.3)";
		/* 滑过 */
		// 标题颜色
		var contentColor_hover = data.contentColor_hover || "#258ffc";
		// 图标背景颜色
		var bgColor_hover = data.bgColor_hover || "#F7810C";
		// 图标渐变色背景
		var bgGradient_hover = data.bgGradient_hover;
		// 光圈颜色
		var apertureColor_hover = data.apertureColor_hover || "rgba(251,167,8,0.3)";
		#>
        <style type="text/css">
			{{ addonId }} * {
				margin: 0;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .icon-box {
				width: 100%;
				height: auto;
			}
			{{ addonId }} .icon-box .icon {
				width: {{icon_bg_width}}px;
				height: {{icon_bg_height}}px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;				
				<# if(icon_bg_style == "color") { #>
				background-color: {{bgColor}};
				<# } #>
				<# if(icon_bg_style == "gradient" && _.isObject(bgGradient)) { #>
				background-image: {{bgGradient.type || "linear"}}-gradient(
                    <# if(bgGradient.type && bgGradient.type == "radial") { #>
                    at {{bgGradient.radialPos || "center center"}}
                    <# } else { #>
                    {{bgGradient.deg || 0}}deg
                    <# } #>
                    , 
                        {{bgGradient.color}} {{bgGradient.pos || 0}}%, 
                        {{bgGradient.color2}} {{bgGradient.pos2 || 100}}%);
				<# } #>
				position: relative;
				margin: auto;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .icon-box .icon:after {
			    content: "";
			    width: {{icon_aperture_width}}px;
				height: {{icon_aperture_height}}px;
				border-radius: 50%;
				background-color: {{apertureColor}};
				position: absolute;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .icon-box .text {
			    font-size: {{content_fontsize}}px;
                line-height: 32px;
                color: {{contentColor}};
                text-align: center;
                margin-top: 20px;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .icon-box:hover .icon {
			    <# if(icon_bg_style == "color") { #>
				background-color: {{bgColor_hover}};
				<# } #>
				<# if(icon_bg_style == "gradient" && _.isObject(bgGradient_hover)) { #>
				background-image: {{bgGradient_hover.type || "linear"}}-gradient(
                    <# if(bgGradient_hover.type && bgGradient_hover.type == "radial") { #>
                    at {{bgGradient_hover.radialPos || "center center"}}
                    <# } else { #>
                    {{bgGradient_hover.deg || 0}}deg
                    <# } #>
                    , 
                        {{bgGradient_hover.color}} {{bgGradient_hover.pos || 0}}%, 
                        {{bgGradient_hover.color2}} {{bgGradient_hover.pos2 || 100}}%);
				<# } #>
			}
			{{ addonId }} .icon-box:hover .icon:after {
				background-color: {{apertureColor_hover}};
				animation: h505 2s linear infinite;
			}
			{{ addonId }} .icon-box:hover .text {
			    color: {{contentColor_hover}};
			}
			@keyframes h505{
			    0% {
			        transform: scale(1);
			        opacity: 1;
			    }
			    100% {
			        transform: scale(1.5);
			        opacity: 0;
			    }
			}
			@media (min-width: 768px) and (max-width: 991px) {
			   
			}
			@media (max-width: 767px) {
			    
			}
		</style>
        <div class="icon-box">
            <div class="icon"><img src=\'{{iconImg}}\'></div>
            <div class="text">{{content_text}}</div>
        </div>
		';

        return $output;
    }
}
