<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2024-02-27 11:55:16
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'advisory_box',
        'title' => JText::_('咨询浮窗'),
        'desc' => JText::_(''),
        'category' => '咨询/友情链接',
        'attr' => array(
            'general' => array(
                'art_type_selector' => array(
                    'type' => 'select',
                    'title' => '选择悬浮布局',
                    'values' => array(
                        'type1' => '布局1',
                        'type2' => '布局2',
                        'type3' => '布局3',
                        'type4' => '布局4',

                        'type5' => '布局5（打开弹窗，请在预览页查看效果）',
                    ),
                    'std' => 'type1'
                ),
                'kf_url' => array(
                    'type' => 'text',
                    'title' => JText::_('客服链接'),
                    'desc' => JText::_('客服链接'),
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'bg_color4' => array(
                    'type' => 'color',
                    'title' => JText::_('背景颜色'),
                    'std' => '#d9012a',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type4_icon1' => array(
                    'type' => 'media',
                    'title' => '电话图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220805/3e806ae6be42eb7ee6e442897f99b3bd.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type_text1' => array(
                    'type' => 'text',
                    'title' => JText::_('弹出内容'),
                    'std' => '400-622-8811',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type4_icon2' => array(
                    'type' => 'media',
                    'title' => 'QQ图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220805/b5027f3a2aed599ad2d4b2c014d73cc8.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type_text2' => array(
                    'type' => 'text',
                    'title' => JText::_('弹出内容'),
                    'std' => '联系客服',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type4_icon3' => array(
                    'type' => 'media',
                    'title' => '信息图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220805/57844ea89aba2aad549c84dbd904f151.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type_text3' => array(
                    'type' => 'text',
                    'title' => JText::_('弹出内容'),
                    'std' => '联系客服',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'type4_icon4' => array(
                    'type' => 'media',
                    'title' => '返回图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220805/053bf557e97a9f9f048fd2552d28f14e.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                //
                'theme' => array(
                    'type' => 'select',
                    'title' => '浮窗位置',
                    'values' => array(
                        'location1' => '左下',
                        'location2' => '右下',
                        'location3' => '左上',
                        'location4' => '右上',
                        'location5' => '左中',
                        'location6' => '右中',

                    ),
                    'std' => 'location1',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'theme2' => array(
                    'type' => 'select',
                    'title' => '浮窗位置',
                    'values' => array(
                        'location1' => '右下',
                        'location5' => '右中',
                        'location2' => '右上',
                        'location3' => '左下',
                        'location6' => '左中',
                        'location4' => '左上',

                    ),
                    'std' => 'location1',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type1'),
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),

                    ),
                ),
                'fc_width' => array(
                    'type' => 'slider',
                    'title' => '浮窗宽',
                    'max' => 600,
                    'min' => 0,
                    'std' => '188',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'padding_tb' => array(
                    'type' => 'slider',
                    'title' => '浮窗上下内边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => '22',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'padding_lr' => array(
                    'type' => 'slider',
                    'title' => '浮窗左右内边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => '22',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('浮窗背景颜色'),
                    'std' => '#187771',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'bg_color_2' => array(
                    'type' => 'color',
                    'title' => JText::_('浮窗弹出框背景颜色'),
                    'std' => '#187771',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'fc_border_radius' => array(
                    'type' => 'slider',
                    'title' => '浮窗圆角',
                    'max' => 40,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'image_code' => array(
                    'type' => 'media',
                    'title' => '咨询二维码',
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'wx_image_code' => array(
                    'type' => 'media',
                    'title' => '微信二维码',
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'image_code_height' => array(
                    'type' => 'slider',
                    'title' => '二维码高度',
                    'max' => 600,
                    'min' => 12,
                    'std' => '115',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'wx_image_code2' => array(
                    'type' => 'media',
                    'title' => '扫码',
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'image_code_height2' => array(
                    'type' => 'slider',
                    'title' => '扫码高度',
                    'max' => 600,
                    'min' => 12,
                    'std' => '115',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文字颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type2'),
                        array('art_type_selector', '!=', 'type3'),
                        array('art_type_selector', '!=', 'type4'),
                    ),
                ),
                'zx_text' => array(
                    'type' => 'text',
                    'title' => JText::_('客服咨询文字'),
                    'desc' => JText::_(''),
                    'std' => '客服咨询',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type2'),
                        array('art_type_selector', '!=', 'type3'),
                        array('art_type_selector', '!=', 'type4'),
                    ),
                ),
                'zx_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('客服咨询文字颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type2'),
                        array('art_type_selector', '!=', 'type3'),
                        array('art_type_selector', '!=', 'type4'),
                    ),
                ),
                'zx_font_size' => array(
                    'type' => 'slider',
                    'title' => '咨询字体大小',
                    'max' => 600,
                    'min' => 12,
                    'std' => '14',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type2'),
                        array('art_type_selector', '!=', 'type3'),
                        array('art_type_selector', '!=', 'type4'),
                    ),
                ),
                'qq' => array(
                    'type' => 'text',
                    'title' => JText::_('QQ'),
                    'desc' => JText::_(''),
                    'std' => '客服QQ号',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type1'),
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'qq_icon_type2' => array(
                    'type' =>'media',
                    'title' => 'QQ图标',
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'qq_desc_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('QQ咨询提示文字'),
                    'std' => '在线沟通，请点我',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'qq_btn_text_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('QQ咨询按钮文字'),
                    'std' => '在线咨询',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'qq_button_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('qq咨询按钮字体颜色'),
                    'std' => '#00DFB9',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'qq_button_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('qq咨询按钮边框颜色'),
                    'std' => '#00DFB9',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'phone_font_text' => array(
                    'type' => 'text',
                    'title' => JText::_('电话'),
                    'desc' => JText::_(''),
                    'std' => '10086',
                    'depends' => array(
                        array('art_type_selector', '!=', 'type4'),
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'phone_icon_type2' => array(
                    'type' =>'media',
                    'title' => '电话图标',
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'phone_text_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('电话标签'),
                    'std' => '咨询热线：',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'phone_qq_text_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('电话详情中qq客服标签'),
                    'std' => '客服qq：',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'wx' => array(
                    'type' => 'text',
                    'title' => JText::_('微信号'),
                    'desc' => JText::_('客服微信号'),
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'wx_icon_type2' => array(
                    'type' =>'media',
                    'title' => '微信图标',
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'wx_desc_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('微信咨询提示文字'),
                    'std' => '在线沟通，请点我',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'wx_btn_text_type2' => array(
                    'type' => 'text',
                    'title' => JText::_('微信咨询按钮文字'),
                    'std' => '复制并咨询',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'wx_button_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('微信咨询按钮字体颜色'),
                    'std' => '#00DFB9',
                    'depends' => array(
                        array('wx', '!=', ''),
                    ),
                ),
                'wx_button_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('微信咨询按钮边框颜色'),
                    'std' => '#00DFB9',
                    'depends' => array(
                        array('wx', '!=', ''),
                    ),
                ),
                'top_icon_type2' => array(
                    'type' =>'media',
                    'title' => '顶部图标',
                    'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'phone_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('电话文字颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'phone_font_size' => array(
                    'type' => 'slider',
                    'title' => '电话字体大小',
                    'max' => 600,
                    'min' => 12,
                    'std' => '14',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'wx_font_size' => array(
                    'type' => 'slider',
                    'title' => '微信字体大小',
                    'max' => 600,
                    'min' => 12,
                    'std' => '14',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'wx_font_text' => array(
                    'type' => 'text',
                    'title' => JText::_('微信'),
                    'desc' => JText::_(''),
                    'std' => '10086',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),
                'wx_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('微信文字颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('art_type_selector', '=', 'type1'),
                    ),
                ),


                'fix_img_height' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭跟随滚动'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),
                'close_tel_type2' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭电话'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'close_qq' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭QQ'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'close_wx_type2' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭微信'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type2'),
                    ),
                ),
                'theme3' => array(
                    'type' => 'select',
                    'title' => '关闭跟随后的浮窗位置',
                    'values' => array(
                        'location1' => '左',
                        'location2' => '右',
                    ),
                    'std' => 'location1',
                    'depends' => array(
                        array('fix_img_height', '=', '1'),
                    ),
                ),
                //样式3各项开关
                'text_1' => array(
                    'type' => 'text',
                    'title' => JText::_('标题1'),
                    'desc' => JText::_(''),
                    'std' => '微信客服',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'text_2' => array(
                    'type' => 'text',
                    'title' => JText::_('标题2'),
                    'desc' => JText::_(''),
                    'std' => '网站咨询',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'text_3' => array(
                    'type' => 'text',
                    'title' => JText::_('标题3'),
                    'desc' => JText::_(''),
                    'std' => '建站咨询',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'text_4' => array(
                    'type' => 'text',
                    'title' => JText::_('标题4'),
                    'desc' => JText::_(''),
                    'std' => '扫码',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),

                'wechat_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏微信'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'call_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏网站咨询'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'qq_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏建站咨询'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'scan_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏扫码'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'top_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏顶部'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'down_switch' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('隐藏底部'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'icon_1' => array(
                    'type' => 'media',
                    'title' => '图标1',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210818/7946af25c7d1940abd738932e71d3bc8.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),

                ),
                'icon_2' => array(
                    'type' => 'media',
                    'title' => '图标2',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210818/edbc585bb2d5e8c0f46532e3aea96bc7.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),

                ),
                'icon_3' => array(
                    'type' => 'media',
                    'title' => '图标3',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210818/76ccd47cdbb1e6b189477f7699f9a713.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),

                ),
                'icon_4' => array(
                    'type' => 'media',
                    'title' => '图标4',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210818/e7753758e7665cd0bb7f1152fbe0b597.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),

                ),
                'icon_5' => array(
                    'type' => 'media',
                    'title' => '图标5',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/fc206fae6db00d8071b9a398b7746a82.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'icon_6' => array(
                    'type' => 'media',
                    'title' => '图标6',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211011/04d305f561b5697110876c3a722365a4.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type3'),
                    ),
                ),
                'close_zd_ding' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭页面自带顶部按钮'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '!=', 'type5'),
                    ),
                ),

                'close_tel_type4' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭电话按钮'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'close_qq_type4' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭qq按钮'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),
                'close_message_type4' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭客服按钮'),
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector', '=', 'type4'),
                    ),
                ),

                // 是否需要点击按钮出自定义弹窗
                'dialog_show' => array(
                    'title' => '是否需要点击按钮出自定义弹窗',
                    'desc' => '需要搭配弹窗插件使用',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    ),
                ),
                // 点击按钮出弹窗
				'dialog_addon_id' => array(
					'title' => '弹窗插件ID（需要搭配弹窗插件使用）',
                    'desc' => '需要搭配弹窗插件使用',
					'type' => 'text',
					'std' => '',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_show', '=', '1'),
                    )
				),
                // 点击按钮弹出商桥
				'business_bridge_show' => array(
					'title' => '是否需要点击弹出商桥',
					'desc' => '需要搭配原始html使用，仅支持百度爱番番和快商通',
					'type' => 'checkbox',
					'std' => 0,
					'depends' => array(
                        array('art_type_selector', '=', 'type5'),
					),
				),
				// 点击按钮弹出商桥结束

                'float_img' => array(
                    'type' => 'media',
                    'title' => JText::_('悬浮图片'),
                    'desc' => JText::_('悬浮图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/1619/image/20240221/63de139cf8ba383e116f0d3100f07933.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    ),
                ),
                'phone_float_img' => array(
                    'type' => 'media',
                    'title' => JText::_('手机端悬浮图片'),
                    'desc' => JText::_('手机端悬浮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20240223/3a24d396a119f326bce87b1fd4902287.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    ),
                ),
                'float_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('漂浮图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    )
                ),
                'float_position' => array(
                    'type' => 'margin',
                    'title' => '悬浮图片位置',
                    'std' => '40% auto auto 0',
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    ),
                ),
                'float_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('悬浮图片宽度'),
                    'std' => array(
                        'md'=>'270',
                        'sm'=>'270',
                        'xs'=>'65',
                    ),
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    )
                ),
                'float_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('悬浮图片高度'),
                    'std' => array(
                        'md'=>'364',
                        'sm'=>'364',
                        'xs'=>'228',
                    ),
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    )
                ),
                'float_level' => array(
                    'type' => 'text',
                    'title' => JText::_('悬浮图片层级'),
                    'std' => '99999999',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                    )
                ),
				'dialog_settins' => array(
                    'type' => 'buttons',
                    'title' => JText::_('弹窗设置'),
                    'std' => 'content',
                    'values' => array(
                        array(
                                'label' => '弹窗主体',
                                'value' => 'content'
                        ),
                        array(
                                'label' => '关闭按钮',
                                'value' => 'close'
                        ),
                        array(
                                'label' => '遮罩',
                                'value' => 'mask'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_img' => array(
                    'type' => 'media',
                    'title' => JText::_('弹窗图片'),
                    'std' => 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_img' => array(
                    'type' => 'media',
                    'title' => JText::_('关闭按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('关闭按钮宽度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_position' => array(
                    'type' => 'margin',
                    'title' => JText::_('弹窗关闭按钮位置'),
                    'std' => '0 0 auto auto',
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_level' => array(
                    'type' => 'text',
                    'title' => JText::_('弹窗层级'),
                    'std' => '99999999999999999999999999999',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗宽度'),
                    'std' => '600',
                    'responsive' => true,
                    'max' => '2000',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '400',
                    'responsive' => true,
                    'max' => '1000',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'mask_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('遮罩背景色'),
                    'std' => 'rgba(0,0,0,0.5)',
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'mask'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'mask_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('点击遮罩关闭弹窗'),
                    'std' => 1,
                    'depends' => array(
                        array('art_type_selector', '=', 'type5'),
                        array('dialog_settins', '=', 'mask'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                // 点击按钮出弹窗结束
            ),
        ),
    )
);
