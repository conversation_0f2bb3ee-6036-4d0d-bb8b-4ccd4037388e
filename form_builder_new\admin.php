<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'form_builder_new',
        'title' => JText::_('新表单插件'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '表单',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array( 
                    'std' => $company_id, 
                ),
                'number_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                    ),
                    'std' => 'type1',
                ),
                'title_text' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'desc' => '标题',
                    'std' => '代理商申请登记表',
                ),
                'success_text' => array(
                    'type' => 'text',
                    'title' => '提交成功提示',
                    'desc' => '提交成功提示',
                    'std' => '了解更多代理商信息，欢迎拨打服务热线400 809 0990',
                ),
                'ys_text' => array(
                    'type' => 'text',
                    'title' => '隐私协议',
                    'desc' => '隐私协议',
                    'std' => '隐私协议',
                ),
            ),
        ),
    )
);
