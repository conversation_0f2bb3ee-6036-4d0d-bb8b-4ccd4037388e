<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonBlk_search_box extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0; 
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $search_type = (isset($settings->search_type)) ? $settings->search_type : 'type1';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $content_text = (isset($settings->content_text)) ? $settings->content_text : '巴洛克建筑 巴洛克音乐 巴洛克风格三天特征 巴洛克建筑艺术 巴洛克时期';
        $search_icon = (isset($settings->search_icon)) ? $settings->search_icon : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png';
        $search_icon_sj = (isset($settings->search_icon_sj)) ? $settings->search_icon_sj : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png';
        $but_color = (isset($settings->but_color)) ? $settings->but_color : '#222c36';
        $sech_kuang = (isset($settings->sech_kuang)) ? $settings->sech_kuang : '94';
        $sech_height = (isset($settings->sech_height)) ? $settings->sech_height : '30';

        // 官网
        $bg_color = (isset($settings->bg_color)) ? $settings->bg_color : '#d9012a';
        $top_logo = (isset($settings->top_logo)) ? $settings->top_logo : 'https://oss.lcweb01.cn/joomla/20220512/072b87c7f0a1b39665c391254710082d.png';
        $dt_img = (isset($settings->dt_img)) ? $settings->dt_img : 'https://oss.lcweb01.cn/joomla/20220512/cb47628729e2d5c63251a30a6a1f4f3a.gif';
        $tc_sechimg = (isset($settings->tc_sechimg)) ? $settings->tc_sechimg : 'https://oss.lcweb01.cn/joomla/20220512/ccbbc35634a9a09347a0beb9ed39574a.png';
        

        $bg_tm_num = (isset($settings->bg_tm_num)) ? $settings->bg_tm_num : 5;
        if ($config->jzt_url == 'http://'.$_SERVER['HTTP_HOST']) {
            $urlpath = $config->jzt_url;
        } else {
            $urlpath = $config->ijzt_url;
        }
        $thisUrl1 = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';

        $thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';

        $postUrl = '/html/' . base64_encode($detail_page_id) . '.html?site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';
        
        /*<a href="'.$thisUrl1.'" class="tzd" target="_blank">
            <img src="'.$tc_sechimg.'">
        </a>*/
       
        // 指定文章详情页ID
        $output = '';

        if($search_type=='type1'){

            $output .= "<style>
                {$addon_id} .input_serch{
                      width: 679px;
                      height: 58px;
                      background: #FFFFFF;
                      border-left: 5px solid #B3B3B3;
                      border-top: 5px solid #B3B3B3;
                      border-bottom: 5px solid #B3B3B3;
                      border-right: none;
                      border-radius:0;
                }
                {$addon_id} .input_submit{
                      width: 96px;
                      height: 58px;
                      background-color: #272831;
                      background-image: url('https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png');
                      background-repeat:no-repeat; 
                      background-position-x:center;
                      background-position-y:center;
                      border-right: 5px solid #B3B3B3;
                      border-top: 5px solid #B3B3B3;
                      border-bottom: 5px solid #B3B3B3;
                      border-left: none;
                      border-radius:0;
                }
                {$addon_id}  .box_serch{
                      display:flex;
                }
              
                  {$addon_id}  .box_content{
                      margin-top: 63px;
                      width: 556px;
                      height: 107px;
                     
                      
                  }
                  {$addon_id}  .box_content_title{
                      font-size: 18px;
                      font-family: Microsoft YaHei;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 30px;
                      
                  }
                  {$addon_id}  .box_content_desc{
                      margin-top: 40px;
                      font-size: 16px;
                      font-family: Microsoft YaHei;
                      font-weight: 400;
                      color: #B2B2B2;
                      line-height: 30px;
                      
                  }
              
                  {$addon_id}  .white_content { 
                      display: none;  
                      position: fixed;  
                      width:100%;
                      height: 100%;
                      background-color:rgba(0,0,0,0.".$bg_tm_num.");
                      z-index:1002;  
                      overflow: auto;
                      top:0;
                      left:0;   
                  } 
              
                  {$addon_id}  .white_content_div { 
                      position: absolute;  
                      left: 25%;
                      top: 25%;
                      width:50%;
                      height: 50%;
                      z-index:1003;  
                      overflow: auto;  
                  } 
              
                  {$addon_id}  .div-close { 
                      position: absolute;  
                      right: 50px;
                      top: 50px;  
                  }

                  
                  @media (max-width:768px) {
                    {$addon_id}  .div-close { 
                        position: absolute;  
                        right: 10px;
                        top: 10px;  
                    }
                    {$addon_id}  .white_content_div { 
                        position: absolute;  
                        left: 5%;
                        top: 25%;
                        width:100%;
                        height: 75%;
                        z-index:1003;  
                        overflow: auto;  
                    } 
                    {$addon_id}  .white_content { 
                        display: none;  
                        position: fixed;  
                        width:100vw;
                        height: 100%;
                        background-color:rgba(0,0,0,0.".$bg_tm_num.");
                        z-index:1002;  
                        overflow: auto;
                        top:0;
                        left:0;   
                    } 
                    {$addon_id} .input_serch{
                        width: 75%;
                        height: 58px;
                        background: #FFFFFF;
                        border-left: 5px solid #B3B3B3;
                        border-top: 5px solid #B3B3B3;
                        border-bottom: 5px solid #B3B3B3;
                        border-right: none;
                        border-radius:0;
                    }
                    {$addon_id} .input_submit{
                        width: 15%;
                        height: 58px;
                        background-color: #272831;
                        background-image: url('https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png');
                        background-repeat:no-repeat; 
                        background-position-x:center;
                        background-position-y:center;
                        border-right: 5px solid #B3B3B3;
                        border-top: 5px solid #B3B3B3;
                        border-bottom: 5px solid #B3B3B3;
                        border-left: none;
                        border-radius:0;
                    }
                    {$addon_id}  .box_content{
                        margin-top: 63px;
                        width: 90%;
                    }
                    {$addon_id}  .a_icon img{
                        content: url('".$search_icon_sj."');
                    }
                }    
                </style>";
                $output .= '<a class="a_icon div-btn" href="javascript:void(0)"><img src="'.$search_icon.'" style="margin:auto;" alt=""></a>
                <div id="light" class="white_content">
                    <div class="white_content_div">
                        <div class="box_serch">
                            <input class="input_serch" type="text">
                            <a href="'.$thisUrl1.'" class="input_submit search_btn tzd"></a>
                        </div>
                        <div class="box_content">
                            <div class="box_content_title">其他人都在搜</div>   
                            <div class="box_content_desc">'.$content_text.'</div>
                        </div>
                    </div>
                    <a href="javascript:void(0)"  class="div-close">
                        <img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/close.png" alt=""></a>
                </div>';
            $output.='
            <script>
                var hostname =\'http://\'+ window.location.hostname;
                var hostnames =\'https://\'+ window.location.hostname;
                jQuery("' . $addon_id . ' .div-btn").click(function(){
                    jQuery("' . $addon_id . ' .white_content").fadeIn(500);
                })
                jQuery("' . $addon_id . ' .div-close").click(function(){
                    jQuery("' . $addon_id . ' .white_content").fadeOut(500);
                });
                var jzt_url = "'.$config->jzt_url.'"; 
                var ijzt_url = "'.$config->ijzt_url.'";
                // jQuery("' . $addon_id . ' .search_btn").click(function(){
                //     var search_name=jQuery("' . $addon_id . ' .input_serch").val()
                //     search_name = encodeURI(encodeURI(search_name));
                //     console.log(search_name)
                //     if(search_name==""){
                //         alert("请输入要搜索的内容")
                //     }else{ 
                //             //                                    console.log(\'hostname:\'+hostname);
                //             //                                    console.log(\'jzt_url:\'+jzt_url);
                //             //                                    console.log(\'ijzt_url:\'+ijzt_url);
                //          if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                //              window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                //          } else {
                //              window.location.href = "' . $postUrl . 'search_name=" + search_name;
                //          }
                      
                //     }
                // });

                $("' . $addon_id . ' .input_serch").bind("input propertychange change",function(){
                    var aa=$("' . $addon_id . ' .input_serch").val();
                    a = encodeURI(encodeURI(aa));
                    if(a){
                      if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                        var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                      } else {
                        var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                      }
                        
                        $("' . $addon_id . ' .tzd").attr("href",ff);
                    }
                   

                })
            </script>
            ';
        }elseif($search_type=='type2'){
            $output .= "<style>
                    {$addon_id} {text-align:center;}
                    {$addon_id} .div-btn{    
                        width: 26px;
                        height: 26px;
                        display: inline-block;
                    }
                    {$addon_id} .div-btn img{    
                        width: 100%;
                        height: 100%;
                        object-fit: scale-down;
                        vertical-align:middle;
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a1.on1 {
                            top: 0;
                            transition: 0.5s;
                        }
                    }
                    {$addon_id} .search1-a1 {
                        z-index: 150;
                    }
                    {$addon_id} .search1-a6 img {
                        max-height:25px;
                    }
                    @media only screen and (min-width: 1024px){
                        {$addon_id} .zzj-pc {
                            display: block;
                        }
                    }

                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a1 {
                            width: 100%;
                            height: 100%;
                            position: fixed;
                            top: -100%;
                            left: 0;
                            background: ".$bg_color.";
                            z-index: 11;
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a12 {
                            height: calc(592/920*100%);
                            position: absolute;
                            right: calc(50% - 1440px/2);
                            top: calc(164/920*100%);
                        }
                    }
                    {$addon_id} .i200>img {
                        height: 100%;
                    }

                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a2 {
                            font-size: 72px;
                            line-height: 72px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: calc(50% - 160px);
                            left: calc(50% - 1440px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a1.on1 .search1-a3 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a3 {
                            font-size: 72px;
                            line-height: 72px;
                            color: #fff;
                            font-weight: lighter;
                            position: absolute;
                            top: calc(50% - 72px);
                            left: calc(50% - 1440px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 100);
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a1.on1 .search1-a4 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a4 {
                            width: 770px;
                            height: 56px;
                            border-radius: 28px;
                            background: rgba(255, 255, 255, 1);
                            position: absolute;
                            top: calc(50% + 44px);
                            left: calc(50% - 1440px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 60);
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a5 {
                            width: 600px;
                            height: 40px;
                            position: absolute;
                            top: calc(50% - 40px/2);
                            left: 44px;
                        }
                    }

                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a5 input {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            font-size: 14px;
                            line-height: 40px;
                            color: ".$bg_color.";
                            padding: 0;
                        }
                        {$addon_id} .search1-a5 input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input:-moz-placeholder,textarea:-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input::-moz-placeholder,textarea::-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        {$addon_id} .search1-a5 input:-ms-input-placeholder,textarea:-ms-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        {$addon_id} input {
                            border: 0;
                            display: block;
                            outline: none;
                            background: none;
                            box-sizing: border-box;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a6 {
                            width: 24px;
                            height: 24px;
                            position: absolute;
                            top: calc(50% - 24px/2);
                            right: 30px;
                            cursor: pointer;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a6+input {
                            display: none;
                        }
                    }

                    @media only screen and (min-width: 1600px){

                        {$addon_id} .search1-a1.on1 .search1-a7 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                    }

                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a7 {
                            width: 690px;
                            position: absolute;
                            top: calc(50% + 124px);
                            left: calc(50% - 1440px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 80);
                            transition: 1s;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a8 {
                            font-size: 18px;
                            line-height: 36px;
                            color: #fff;
                            font-weight: bold;
                            float: left;
                        }
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a9 {
                            width: calc(100% - 100px);
                            float: right;
                        }
                    }

                    @media only screen and (min-width: 1600px){

                        {$addon_id} .search1-a9>div {
                            font-size: 16px;
                            line-height: 36px;
                            color: #fff;
                            float: left;
                            margin-right: 24px;
                        }
                        {$addon_id} .search1-a9>div a{
                            font-size: 16px;
                            line-height: 36px;
                            color: #fff;
                            float: left;
                            margin-right: 24px;
                        }

                    }
                    {$addon_id} .clear:after {
                        content: '';
                        display: block;
                        clear: both;
                    }
                    {$addon_id} .i200 {
                        overflow: hidden;
                    }
                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a10 {
                            width: 56px;
                            height: 56px;
                            position: absolute;
                            top: 30px;
                            right: 40px;
                            cursor: pointer;
                        }
                    }
                    {$addon_id} .i200>img {
                        height: 100%;
                    }

                    @media only screen and (min-width: 1600px){
                        {$addon_id} .search1-a11 {
                            height: 60px;
                            position: absolute;
                            top: 30px;
                            left: 40px;
                        }
                    }
                    @media only screen and (max-width: 1599px) and (min-width: 1400px){
                        {$addon_id} .search1-a1.on1 {
                            top: 0;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a1 {
                            width: 100%;
                            height: 100%;
                            position: fixed;
                            top: -100%;
                            left: 0;
                            background: #d9012a;
                            z-index: 11;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a1 {
                            z-index: 150;
                        }
                        {$addon_id} .i200 {
                            overflow: hidden;
                        }
                        {$addon_id} .i200>img {
                            height: 100%;
                        }
                        {$addon_id} .search1-a12 {
                            height: calc(592/920*100%);
                            position: absolute;
                            right: calc(50% - 1360px/2);
                            top: calc(164/920*100%);
                        }
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a2 {
                            font-size: 72px;
                            line-height: 72px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: calc(50% - 160px);
                            left: calc(50% - 1360px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a2 {
                            font-size: 72px;
                            line-height: 72px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: calc(50% - 160px);
                            left: calc(50% - 1360px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a3 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a3 {
                            font-size: 72px;
                            line-height: 72px;
                            color: #fff;
                            font-weight: lighter;
                            position: absolute;
                            top: calc(50% - 72px);
                            left: calc(50% - 1360px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 100);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a4 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a4 {
                            width: 770px;
                            height: 56px;
                            border-radius: 28px;
                            background: rgba(255, 255, 255, 1);
                            position: absolute;
                            top: calc(50% + 44px);
                            left: calc(50% - 1360px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 60);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a5 {
                            width: 600px;
                            height: 40px;
                            position: absolute;
                            top: calc(50% - 40px/2);
                            left: 44px;
                        }
                        {$addon_id} .search1-a5 input {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            font-size: 16px;
                            line-height: 40px;
                            color: #d9012a;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a7 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a7 {
                            width: 690px;
                            position: absolute;
                            top: calc(50% + 124px);
                            left: calc(50% - 1360px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 80);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a8 {
                            font-size: 18px;
                            line-height: 36px;
                            color: #fff;
                            font-weight: bold;
                            float: left;
                        }
                        {$addon_id} .search1-a9 {
                            width: calc(100% - 100px);
                            float: right;
                        }
                        {$addon_id} .search1-a9>div {
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                        }
                        {$addon_id} .search1-a9>div a{
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                        }
                        {$addon_id} .search1-a11 {
                            height: 60px;
                            position: absolute;
                            top: 30px;
                            left: 40px;
                        }
                        {$addon_id} .search1-a10 {
                            width: 56px;
                            height: 56px;
                            position: absolute;
                            top: 30px;
                            right: 40px;
                            cursor: pointer;
                        }
                        {$addon_id} input {
                            border: 0;
                            display: block;
                            outline: none;
                            background: none;
                            box-sizing: border-box;
                        }
                        {$addon_id} .search1-a6 {
                            width: 24px; 
                            height: 24px;
                            position: absolute;
                            top: calc(50% - 24px/2);
                            right: 30px;
                            cursor: pointer;
                        }
                        {$addon_id} .search1-a6+input {
                            display: none;
                        }
                        {$addon_id} .search1-a5 input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input:-moz-placeholder,textarea:-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input::-moz-placeholder,textarea::-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        {$addon_id} .search1-a5 input:-ms-input-placeholder,textarea:-ms-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                    }
                    @media only screen and (max-width: 1399px) and (min-width: 1200px){
                        {$addon_id} .search1-a1.on1 {
                            top: 0;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a1 {
                            z-index: 150;
                        }
                        {$addon_id} .search1-a1 {
                            width: 100%;
                            height: 100%;
                            position: fixed;
                            top: -100%;
                            left: 0;
                            background: #d9012a;
                            z-index: 11;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a12 {
                            height: calc(592/920*100%);
                            position: absolute;
                            right: calc(50% - 1160px/2);
                            top: calc(164/920*100%);
                        }
                        {$addon_id} .i200 {
                            overflow: hidden;
                        }
                        {$addon_id} .i200>img {
                            height: 100%;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a2 {
                            font-size: 48px;
                            line-height: 48px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: calc(50% - 100px);
                            left: calc(50% - 1160px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a3 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a3 {
                            font-size: 48px;
                            line-height: 48px;
                            color: #fff;
                            font-weight: lighter;
                            position: absolute;
                            top: calc(50% - 48px);
                            left: calc(50% - 1160px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 100);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a4 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a4 {
                            width: 640px;
                            height: 48px;
                            border-radius: 24px;
                            background: rgba(255, 255, 255, 1);
                            position: absolute;
                            top: calc(50% + 28px);
                            left: calc(50% - 1160px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 60);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a5 {
                            width: 500px;
                            height: 40px;
                            position: absolute;
                            top: calc(50% - 40px/2);
                            left: 30px;
                        }
                        {$addon_id} .search1-a5 input {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            font-size: 14px;
                            line-height: 40px;
                            color: #d9012a;
                        }
                        {$addon_id} input {
                            border: 0;
                            display: block;
                            outline: none;
                            background: none;
                            box-sizing: border-box;
                        }
                        {$addon_id} .search1-a6 {
                            width: 20px;
                            height: 20px;
                            position: absolute;
                            top: calc(50% - 20px/2);
                            right: 30px;
                            cursor: pointer;
                        }
                        {$addon_id} .search1-a6+input {
                            display: none;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a7 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a7 {
                            width: 560px;
                            position: absolute;
                            top: calc(50% + 96px);
                            left: calc(50% - 1160px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 80);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a8 {
                            font-size: 16px;
                            line-height: 30px;
                            color: #fff;
                            font-weight: bold;
                            float: left;
                        }
                        {$addon_id} .search1-a9 {
                            width: calc(100% - 80px);
                            float: right;
                        }
                        {$addon_id} .search1-a9>div {
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                            text-align: left;
                        }
                        {$addon_id} .search1-a9>div a{
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                            text-align: left;
                        }
                        {$addon_id} .search1-a10 {
                            width: 56px;
                            height: 56px;
                            position: absolute;
                            top: 24px;
                            right: 30px;
                            cursor: pointer;
                        }
                        {$addon_id} .search1-a11 {
                            height: 60px;
                            position: absolute;
                            top: 30px;
                            left: 30px;
                        }
                        {$addon_id} .search1-a5 input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input:-moz-placeholder,textarea:-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input::-moz-placeholder,textarea::-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        {$addon_id} .search1-a5 input:-ms-input-placeholder,textarea:-ms-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                    }
                    @media only screen and (max-width: 1199px) and (min-width: 1024px){

                        {$addon_id} .search1-a1.on1 {
                            top: 0;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a1 {
                            width: 100%;
                            height: 100%;
                            position: fixed;
                            top: -100%;
                            left: 0;
                            background: #d9012a;
                            z-index: 11;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a2 {
                            font-size: 48px;
                            line-height: 48px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: calc(50% - 100px);
                            left: calc(50% - 960px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }

                        {$addon_id} .search1-a12 {
                            height: calc(592/920*100%);
                            position: absolute;
                            right: calc(50% - 960px/2);
                            top: calc(164/920*100%);
                        }
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a3 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a4 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a3 {
                            font-size: 48px;
                            line-height: 48px;
                            color: #fff;
                            font-weight: lighter;
                            position: absolute;
                            top: calc(50% - 48px);
                            left: calc(50% - 960px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 100);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a4 {
                            width: 540px;
                            height: 48px;
                            border-radius: 24px;
                            background: rgba(255, 255, 255, 1);
                            position: absolute;
                            top: calc(50% + 28px);
                            left: calc(50% - 960px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 60);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a5 {
                            width: 500px;
                            height: 40px;
                            position: absolute;
                            top: calc(50% - 40px/2);
                            left: 30px;
                        }
                        {$addon_id} .search1-a5 input {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            font-size: 14px;
                            line-height: 40px;
                            color: #d9012a;
                            border:none;
                        }
                        {$addon_id} .search1-a6 {
                            width: 20px;
                            height: 20px;
                            position: absolute;
                            top: calc(50% - 20px/2);
                            right: 30px;
                            cursor: pointer;
                        }
                        {$addon_id} .search1-a6+input {
                            display: none;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a7 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }
                        {$addon_id} .search1-a7 {
                            width: 560px;
                            position: absolute;
                            top: calc(50% + 96px);
                            left: calc(50% - 960px/2);
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 80);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a8 {
                            font-size: 16px;
                            line-height: 30px;
                            color: #fff;
                            font-weight: bold;
                            float: left;
                        }
                        {$addon_id} .search1-a9 {
                            width: calc(100% - 80px);
                            float: right;
                        }
                        {$addon_id} .search1-a9 div{
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                            text-align: left;
                        }
                        {$addon_id} .search1-a10 {
                            width: 56px;
                            height: 56px;
                            position: absolute;
                            top: 24px;
                            right: 30px;
                            cursor: pointer;
                        }
                        {$addon_id} .search1-a11 {
                            height: 60px;
                            position: absolute;
                            top: 30px;
                            left: 30px;
                        }
                        {$addon_id} .search1-a5 input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input:-moz-placeholder,textarea:-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        
                        {$addon_id} .search1-a5 input::-moz-placeholder,textarea::-moz-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }
                        {$addon_id} .search1-a5 input:-ms-input-placeholder,textarea:-ms-input-placeholder{
                            color: ".$bg_color.";
                            font-size: 14px;
                        }

                    }


                    @media only screen and (min-width: 1024px) {
                        {$addon_id} .zzj-pc {
                            display: block;
                        }
                        {$addon_id} .zmr-phone {
                            display: none;
                        }
                    }
                    @media only screen and (max-width: 1023px) {
                        {$addon_id} .zzj-pc {
                            display: none;
                        }
                        {$addon_id} .zmr-phone {
                            display: block;
                        }
                    }
                    @media only screen and (max-width: 1023px) {
                        {$addon_id} .search1-a1 {
                            display: block!important;
                        }
                        {$addon_id} .search1-a1 {
                            width: 100%;
                            height: 100%;
                            position: fixed;
                            top: -100%;
                            left: 0;
                            z-index: 11;
                            transition: 0.5s;
                            background: ".$bg_color.";
                            overflow: hidden;
                        }
                        {$addon_id} .search1-a1.on1 {
                            top: 0;
                            transition: 0.5s;
                        }
                        {$addon_id} .search1-a2 {
                            font-size: 30px;
                            line-height: 50px;
                            color: #fff;
                            font-weight: bold;
                            text-align: center;
                            width: 100%;
                            position: absolute;
                            top: calc(50% - 175px);
                            left: 0;
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 160);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a2 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }

                        {$addon_id} .search1-a3 {
                            font-size: 32px;
                            line-height: 50px;
                            color: #fff;
                            font-weight: lighter;
                            text-align: center;
                            width: 100%;
                            position: absolute;
                            top: calc(50% - 130px);
                            left: 0;
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 100);
                            transition: 1s;
                        }
                        {$addon_id} .search1-a1.on1 .search1-a3 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }

                        {$addon_id} .search1-a4 {
                            width: calc(100% - 40px);
                            height: 38px;
                            border-radius: 30px;
                            background: rgba(255, 255, 255, 1);
                            position: absolute;
                            top: calc(50% - 60px);
                            left: 20px;
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 60);
                            transition: 1s;
                            overflow: hidden;
                        }

                        {$addon_id} .search1-a1.on1 .search1-a4 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }

                        {$addon_id} .search1-a5 {
                            width: 70%;
                            height: 38px;
                            position: absolute;
                            left: 10px;
                        }

                        {$addon_id} .search1-a5 input {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            font-size: 12px;
                            line-height: 38px;
                            color: ".$bg_color.";
                            border:none;
                        }

                        {$addon_id} .search1-a5 input::-webkit-input-placeholder {
                            color: ".$bg_color.";
                        }

                        {$addon_id} .search1-a5 input:-moz-placeholder {
                            color: ".$bg_color.";
                        }

                        {$addon_id} .search1-a5 input:-ms-input-placeholder {
                            color: ".$bg_color.";
                        }

                        {$addon_id} .search1-a6 {
                            height: 38px;
                            position: absolute;
                            right: 10px;
                            cursor: pointer;
                            line-height:38px;
                            top: calc(50% - 24px/2);
                        }

                        {$addon_id} .search1-a6+input {
                            display: none;
                        }

                        {$addon_id} .search1-a7 {
                            width: calc(100% - 40px);
                            position: absolute;
                            top: calc(50% + 0rem);
                            left: 25px;
                            opacity: 0;
                            transform: matrix(1, 0, 0, 1, 0, 80);
                            transition: 1s;
                        }

                        {$addon_id} .search1-a1.on1 .search1-a7 {
                            transform: matrix(1, 0, 0, 1, 0, 0);
                            opacity: 1;
                            transition: 1s;
                        }

                        {$addon_id} .search1-a8 {
                            font-size: 14px;
                            line-height: 29px;
                            color: #fff;
                            font-weight: bold;
                            float: left;
                        }

                        {$addon_id} .search1-a9 {
                            width: calc(100% - 70px);
                            float: right;
                        }

                        {$addon_id} .search1-a9>div {
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                        }
                        {$addon_id} .search1-a9>div a{
                            font-size: 14px;
                            line-height: 30px;
                            color: #fff;
                            float: left;
                            margin-right: 18px;
                        }

                        {$addon_id} .search1-a10 {
                            height: 30px;
                            position: absolute;
                            top: 10px;
                            right: 10px;
                        }

                        {$addon_id} .search1-a11 {
                            height: 30px;
                            position: absolute;
                            top: 10px;
                            left: 10px;
                        }

                        {$addon_id} .search1-a12 {
                            height: 220px;
                            position: absolute;
                            top: calc(50% + 80px);
                            left: calc(50% - 90px);
                        }
                    }
                    
                    @media (max-width:768px) {
                        {$addon_id}  .a_icon img{
                            content: url('".$search_icon_sj."');
                        }
                    }    
                </style>";
                $output .= '<a class="a_icon div-btn" href="javascript:void(0)"><img src="'.$search_icon.'" style="margin:auto;" alt=""></a>
            
                        <div class="search1-a1 zzj-pc ">
                            <div class="search1-a12 i200">
                                <img src="'.$dt_img.'">
                            </div>
                            <div class="search1-a2">搜索</div>
                            <div class="search1-a3">你想找的</div>
                            <div class="search1-a4">
                                <div class="search1-a5">
                                    <input type="text" onkeydown="ev' . $this->addon->id . '()" name="keywords" class="kes" id="keywords" placeholder="请输入搜索内容" ></div>
                                    <label>
                                        <div class="search1-a6" >
          
                                            <a href="'.$thisUrl1.'" class="tzd" target="_blank">
                                                <img src="'.$tc_sechimg.'">
                                            </a>
                                        </div>
                                        <input type="submit">
                                    </label>
                                </div>
                            <div class="search1-a7 clear">
                                <div class="search1-a8">热搜词汇</div>
                                <div class="search1-a9 clear">
                                    <div>';
                                    if (isset($settings->tab_item2) && !empty($settings->tab_item2)) {
                                        foreach ($settings->tab_item2 as $item_key => $tab_item) {
                                            if ($tab_item->media_url_show){
                                                if($tab_item->tz_page_type == 'Internal_pages')
                                                {
                                                    $urks = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($tab_item->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                                    $output .='<a href="'.$urks.'" target="'.$tab_item->media_target.'">';
                                                }
                                                else
                                                {
                                                    $output .='<a href="'.$tab_item->media_url.'" target="'.$tab_item->media_target.'">';
                                                }
                                            }else{
                                                $output .='<a href="javacript:;" >';
                                            }

                                            $output .=  '
                                                '.$tab_item->title.'</a>
                                            ';
                                        }
                                    }
                                    $output .= '</div>
                                </div>
                            </div>
                            <div class="search1-a10 i200"><img src="https://oss.lcweb01.cn/joomla/20220512/079db40f20f46c87a6592ceae9942602.png"></div>
                            <div class="search1-a11 i200"><img src="'.$top_logo.'"></div>
                        </div>
                ';
            $output.='
                <script>
                $("head").append(\'<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">\');
                    var hostname =\'http://\'+ window.location.hostname;
                    var hostnames =\'https://\'+ window.location.hostname;
                    
                    jQuery("' . $addon_id . ' .div-btn").click(function(){
                        jQuery("' . $addon_id . ' .search1-a1").addClass("on1");
                    })

                    jQuery("' . $addon_id . ' .search1-a10").click(function(){
                        jQuery("' . $addon_id . ' .search1-a1").removeClass("on1");
                    });
                    var jzt_url = "'.$config->jzt_url.'"; 
                    var ijzt_url = "'.$config->ijzt_url.'";
                    
                    $(".tzd").click(function(){
                        if($("#keywords").val() == "")
                        {
                            alert("请输入搜索关键字");
                            $("#keywords").focus();
                            return false;
                        }
                        
                    })

                    $("' . $addon_id . ' .kes").bind("input propertychange change",function(){
                        var aa=$("' . $addon_id . ' #keywords").val();
                        a = encodeURI(encodeURI(aa));
                        console.log(a);
                        if(a){
                          if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                            var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                          } else {
                            var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                          }
                            $(".tzd").attr("href",ff);
                        }
                    })

                    
                    function ev' . $this->addon->id . '()
                        {
                            var event = window.event || arguments.callee.caller.arguments[0];
                            if (event.keyCode == 13)
                            {
                                var search_na=jQuery("' . $addon_id . ' #keywords").val();
                                search_name = encodeURI(encodeURI(search_na));
                                console.log(search_name)
                                if(search_name==""){
                                    alert("请输入搜索内容")
                                }else{ 

                                     if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                         window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                                     } else {
                                         window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                                     }
                                  
                                }
                            }
                        }
                   
                </script>
            ';
        }elseif($search_type=='type3'){
            $output .= "<style>
                            {$addon_id} .search {
                                width: ".$sech_height."px;height:".$sech_height."px;line-height: ".$sech_height."px;display: flex;align-items: center;
                            }
                            {$addon_id} .abs {
                                position: absolute;
                            }
                            {$addon_id} .srch {
                                position: relative;
                                width: ".$sech_kuang."%;
                                left:calc( (100% - ".$sech_kuang."%) / 2 );
                                top:calc( ".$sech_height."px + 10px );
                                -webkit-appearance: none;
                                border: solid 1px #eee;
                                display: none;
                                height:37px;
                            }
                            {$addon_id} .srch_txt {
                                width: 83%;
                                border: none;
                                color: #333;
                                background: #ffffff;
                                -webkit-appearance: none;
                                padding: 8px;
                            }
                            {$addon_id} .srch_sub {
                                position: absolute;
                                top: 0;
                                right: 0;
                                width: 17%;
                                height: 100%;
                                border: none;
                                color: #ffffff;
                                text-align: center;
                                background: ".$but_color.";
                                -webkit-appearance: none;
                                line-height:37px;
                            }

                            @media (max-width:768px) {
                                {$addon_id}  .search img{
                                    content: url('".$search_icon_sj."');
                                }
                            }  
                        </style>
            ";

            $output .= '
                <div style="position: relative;width:100%;">
                    <div class="search abs "><img src="'.$search_icon.'"></div>
                    <div class="srch flex" >
                        <input type="search" onkeydown="ev' . $this->addon->id . '()" name="key" class="srch_txt" id="key" placeholder="请输入搜索关键字..." >
                        <a href="'.$thisUrl1.'"  class="tzd " target="_blank">
                            <div class="srch_sub">
                                搜索
                            </div>
                        </a>
                    </div>
                </div>

                <script type="text/javascript">
                    
                    var hostname =\'http://\'+ window.location.hostname;
                    var hostnames =\'https://\'+ window.location.hostname;

                    var jzt_url = "'.$config->jzt_url.'"; 
                    var ijzt_url = "'.$config->ijzt_url.'";
                    

                    $(".tzd").click(function(){
                        if($("#key").val() == "")
                        {
                            alert("请输入搜索关键字");
                            $("#key").focus();
                            return false;
                        }
                        
                    })


                    $("' . $addon_id . ' .srch_txt").bind("input propertychange change",function(){
                        var aa=$("' . $addon_id . ' #key").val();
                        a = encodeURI(encodeURI(aa));
                        if(a){
                            var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                            $(".tzd").attr("href",ff);
                        }
                    })

                    function ev' . $this->addon->id . '()
                        {
                            var event = window.event || arguments.callee.caller.arguments[0];
                            if (event.keyCode == 13)
                            {
                                var search_na=jQuery("' . $addon_id . ' #key").val();
                                search_name = encodeURI(encodeURI(search_na));
                                console.log(search_name)
                                if(search_name==""){
                                    alert("请输入搜索内容")
                                }else{ 

                                    if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                        window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                                    } else {
                                        window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                                    }
                                  
                                }
                            }
                        }
                                
                    $(function(){
                        $("' . $addon_id . ' .search").click(function(){
                            $(".srch").slideToggle(300)
                        });
                        $("' . $addon_id . ' .menu").click(function(){
                            $(".menu_list").fadeIn(300)
                        })
                        $("' . $addon_id . ' .menu_close").click(function(){
                            $(".menu_list").fadeOut(300)
                        })
                    });
                </script>

            ';
        }

        return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = '';
            $output .= '<style>
            <#
            var addonId = "jwpf-addon-"+data.id;
             #>

            #{{ addonId }} .input_serch{
                width: 679px;
                height: 58px;
                background: #FFFFFF;
                border-left: 5px solid #B3B3B3;
                border-top: 5px solid #B3B3B3;
                border-bottom: 5px solid #B3B3B3;
                border-right: none;
                border-radius:0;
            }
            #{{ addonId }} .input_submit{
                width: 96px;
                height: 58px;
                background-color: #272831;
                background-image: url("/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png");
                background-repeat:no-repeat;  
                background-position-x:center;
                background-position-y:center;
                border-right: 5px solid #B3B3B3;
                border-top: 5px solid #B3B3B3;
                border-bottom: 5px solid #B3B3B3;
                border-left: none;
            }
            #{{ addonId }} .box_serch{
                display:flex;
                
            }
        
            #{{ addonId }} .box_content{
                margin-top: 63px;
                width: 556px;
                height: 107px;
               
                
            }
            #{{ addonId }} .box_content_title{
                font-size: 18px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #FFFFFF;
                line-height: 30px;
                
            }
            #{{ addonId }} .box_content_desc{
                margin-top: 40px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #B2B2B2;
                line-height: 30px;
                
            }
        
            #{{ addonId }} .white_content { 
                display: none;  
                position: fixed;  
                width:100%;
                height: 100%;
                background-color:rgba(0,0,0,0.1);
                z-index:1002;  
                overflow: auto;
                top:0;
                left:0;  
            } 
        
            #{{ addonId }} .white_content_div { 
                position: absolute;  
                left: 25%;
                top: 25%;
                width:50%;
                height: 50%;
                z-index:1003;  
                overflow: auto;  
            } 
        
            #{{ addonId }} .div-close { 
                position: absolute;  
                right: 50px;
                top: 50px;  
            }  
            
        </style>';
        $output .= '<a href="javascript:void(0)" onclick="document.getElementById(';
            $output.="'light').style.display='block';document.getElementById('fade').style.display='block'";
            $output.='"><img src="/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png" alt=""></a>
        <div id="light" class="white_content">
            <div class="white_content_div">
                <form class="box_serch">
                    <input class="input_serch" type="text">
                    <input class="input_submit" type="submit" value="">
                </form>
                <div class="box_content">
                    <div class="box_content_title">其他人都在搜</div>
                    <div class="box_content_desc">巴洛克建筑 巴洛克音乐 巴洛克风格三天特征 巴洛克建筑艺术 巴洛克时期</div>
                </div>
            </div>
            <a href="javascript:void(0)" onclick="document.getElementById(';
            $output.="'light').style.display='none';document.getElementById('fade').style.display='none'";
            $output.='" class="div-close">
                <img src="/components/com_jwpagefactory/addons/blk_search_box/assets/images/close.png" alt=""></a>
        </div>';
        return $output;
    }
}