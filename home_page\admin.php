<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'home_page',
		'title' => JText::_('设为首页'),
		'desc' => '',
		'category' => '其他',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_padding' => array(
					'type' => 'padding',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'std' => ''
				),

				'title_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_POSITION'),
					'values' => array(
						'top' => 'Top',
						'bottom' => 'Bottom',
					),
					'std' => 'top',
					'depends' => array(array('title', '!=', '')),
				),

				

				// 设置首页
                'start_sy' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启设置首页'),
                    'desc' => JText::_('默认开启'),
                    'std' => '1',
                ),
                'sy_text' => array(
                    'type' => 'text',
                    'title' => JText::_('标题文字'),
                    'std' => '设为首页',
                    'depends' => array(
                        array('start_sy' ,'=', '1')
                    ),
                ),
                'sy_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('行高'),
                    'std' => '35',
                    'depends' => array( array('start_sy' ,'=', '1')),
                    'max' => 400,
                ),
                'syhover' => array(
                    'type' => 'buttons',
                    'title' => '图标/字体颜色设置',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('start_sy' ,'=', '1')
                    ),
                ),
                'sy_img' => array(
                    'type' => 'media',
                    'title' => '正常图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png',
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','normal'),
                    )
                ),
                'sy_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('正常字体大小'),
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','normal'),
                    ),
                    'max' => 200,
                    'min' => 0,
                    'std' => '12',
                ),
                'sy_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常字体颜色'),
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','normal'),
                    ),
                    'std' => '#000',
                ),
                

                // 划过
                'sy_img_hover' => array(
                    'type' => 'media',
                    'title' => '移入图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png',
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','hover'),
                    )
                ),
                'sy_title_sizehover' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入后字体大小'),
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','hover'),
                    ),
                    'max' => 200,
                    'min' => 0,
                    'std' => '12',
                ),
                'sy_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后字体颜色'),
                    'depends' => array(
                        array('start_sy' ,'=', '1'),
                        array('syhover' ,'=','hover')
                    ),
                    'std' => '#333',
                ),


                // 加入收藏
                'start_sc' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启加入收藏'),
                    'desc' => JText::_('默认开启'),
                    'std' => '1',
                ),
                'sc_text' => array(
                    'type' => 'text',
                    'title' => JText::_('标题文字'),
                    'std' => '加入收藏',
                    'depends' => array(
                        array('start_sc' ,'=', '1')
                    ),
                ),
                'sc_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('行高'),
                    'std' => '35',
                    'depends' => array( array('start_sc' ,'=', '1')),
                    'max' => 400,
                ),
                'schover' => array(
                    'type' => 'buttons',
                    'title' => '图标/字体颜色设置',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('start_sc' ,'=', '1')
                    ),
                ),
                'sc_img' => array(
                    'type' => 'media',
                    'title' => '正常图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png',
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','normal'),
                    )
                ),
                'sc_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('正常字体大小'),
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','normal'),
                    ),
                    'max' => 200,
                    'min' => 0,
                    'std' => '12',
                ),
                'sc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常字体颜色'),
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','normal'),
                    ),
                    'std' => '#000',
                ),
                

                // 划过
                'sc_img_hover' => array(
                    'type' => 'media',
                    'title' => '移入图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png',
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','hover'),
                    )
                ),
                'sc_title_sizehover' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入后字体大小'),
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','hover'),
                    ),
                    'max' => 200,
                    'min' => 0,
                    'std' => '12',
                ),
                'sc_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后字体颜色'),
                    'depends' => array(
                        array('start_sc' ,'=', '1'),
                        array('schover' ,'=','hover')
                    ),
                    'std' => '#333',
                ),
			),
		),
	)
);
