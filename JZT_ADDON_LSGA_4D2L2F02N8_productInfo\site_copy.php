<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonJZT_ADDON_LSGA_4D2L2F02N8_productInfo extends JwpagefactoryAddons
{
    //在预览页面中渲染
    public function render()
    {
        $settings = $this->addon->settings;

        //此处载入所有配置项变量
        //$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        //详情页布局
        $art_type_selector_proinfo = (isset($settings->art_type_selector_proinfo) && $settings->art_type_selector_proinfo) ? $settings->art_type_selector_proinfo : 'type1';
        $addon_id                  = '#jwpf-addon-' . $this->addon->id;

        //翻页按钮是否开启
        $page_button          = (isset($settings->page_button) && $settings->page_button) ? $settings->page_button : 0;
        $next_page_text       = (isset($settings->next_page_text) && $settings->next_page_text) ? $settings->next_page_text : '下一页';
        $up_page_text         = (isset($settings->up_page_text) && $settings->up_page_text) ? $settings->up_page_text : '上一页';
        $pageColor            = (isset($settings->pageColor) && $settings->pageColor) ? $settings->pageColor : '';
        $pageBorderColor      = (isset($settings->pageBorderColor) && $settings->pageBorderColor) ? $settings->pageBorderColor : '';
        $pageBgColor          = (isset($settings->pageBgColor) && $settings->pageBgColor) ? $settings->pageBgColor : '';
        $pageColorhover       = (isset($settings->pageColorhover) && $settings->pageColorhover) ? $settings->pageColorhover : '';
        $pageBorderColorhover = (isset($settings->pageBorderColorhover) && $settings->pageBorderColorhover) ? $settings->pageBorderColorhover : '';
        $pageBgColorhover     = (isset($settings->pageBgColorhover) && $settings->pageBgColorhover) ? $settings->pageBgColorhover : '';
        $ordering_select     = (isset($settings->ordering_select) && $settings->ordering_select) ? $settings->ordering_select : '';
        $page_dttitle         = (isset($settings->page_dttitle) && $settings->page_dttitle) ? $settings->page_dttitle : 0;
        $keywords_h2_button=(isset($settings->keywords_h2_button) && $settings->keywords_h2_button) ? $settings->keywords_h2_button :'no';
        $tdk_button=(isset($settings->tdk_button) && $settings->tdk_button) ? $settings->tdk_button :'no';
        $description_h2_button=(isset($settings->description_h2_button) && $settings->description_h2_button) ? $settings->description_h2_button :'no';
        $title_bold=(isset($settings->title_bold) && $settings->title_bold) ? $settings->title_bold :0;

        $biaoqian_type = (isset($settings->biaoqian_type) && $settings->biaoqian_type) ? $settings->biaoqian_type : 'no';
        $biaoqian_peizhi = (isset($settings->biaoqian_peizhi) && $settings->biaoqian_peizhi) ? $settings->biaoqian_peizhi : '';
        $biaoqian_bg_color = (isset($settings->biaoqian_bg_color) && $settings->biaoqian_bg_color) ? $settings->biaoqian_bg_color : '';
        $biaoqian_line_height = (isset($settings->biaoqian_line_height) && $settings->biaoqian_line_height) ? $settings->biaoqian_line_height : 0;
        $biaoqian_title_type = (isset($settings->biaoqian_title_type) && $settings->biaoqian_title_type) ? $settings->biaoqian_title_type : 'no';
        $biaoqian_title_color = (isset($settings->biaoqian_title_color) && $settings->biaoqian_title_color) ? $settings->biaoqian_title_color : '';
        $biaoqian_title_size = (isset($settings->biaoqian_title_size) && $settings->biaoqian_title_size) ? $settings->biaoqian_title_size : 14;

        $output = '';
        $output .= '
            <style>
                ' . $addon_id . ' img, ' . $addon_id . ' video { max-width: 100% !important }
                ' . $addon_id . ' img { height: auto !important }
                ' . $addon_id . ' .detail-wrap { height: auto; overflow: auto; }
                ' . $addon_id . ' table p { margin: 0 }
            </style>
        ';
        if ($art_type_selector_proinfo == 'type1') {
            //配置项变量
            $color1615443385649  = (isset($settings->color1615443385649) && $settings->color1615443385649) ? $settings->color1615443385649 : '';
            $select1615452416171 = (isset($settings->select1615452416171) && $settings->select1615452416171) ? $settings->select1615452416171 : '';
            $color1615452944411  = (isset($settings->color1615452944411) && $settings->color1615452944411) ? $settings->color1615452944411 : '';
            $color1615453171634  = (isset($settings->color1615453171634) && $settings->color1615453171634) ? $settings->color1615453171634 : '';
            $font_size           = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '';
            $font_size_date      = (isset($settings->font_size_date) && $settings->font_size_date) ? $settings->font_size_date : '';
            $title_h2_button     = (isset($settings->title_h2_button) && $settings->title_h2_button) ? $settings->title_h2_button : 'yes';

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $catid_id   = $_GET['catid_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            //获取产品详情的数据源

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            print_r($article);die;
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= '<style>';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT { color:' . $color1615452944411 . '; }';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img{ display:inline-block !important;}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT video{ max-width:100% !important;}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2{ font-size:' . $font_size . 'px; ';
            if($title_bold)
            {
                $output .= 'font-weight: bold;';
            }
            $output .= '}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT p{ font-size:' . $font_size_date . 'px;}';
            if ($article->on) {
                $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox{  display:flex;justify-content:space-between;padding:20px; }';
                $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt{  display:flex;justify-content:space-between;padding:20px; }';

            } else {
                $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox{  display:flex;justify-content:flex-end;padding:20px; }';
                $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt{  display:flex;justify-content:flex-end;padding:20px; }';

            }

            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColor . ';color: ' . $pageColor . ';font-size: 14px;background:' . $pageBgColor . ';text-align: center;line-height: 40px;text-decoration: none;}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a:hover{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColorhover . ';color: ' . $pageColorhover . ';font-size: 14px;background:' . $pageBgColorhover . ';text-align: center;line-height: 40px;text-decoration: none;}';

            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a{     width: 50%;text-align:left; height: 40px; color: ' . $pageColor . ';font-size: 14px;background:' . $pageBgColor . ';line-height: 40px;text-decoration: none;}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:hover{     height: 40px; color: ' . $pageColorhover . ';font-size: 14px;background:' . $pageBgColorhover . ';line-height: 40px;text-decoration: none;}';
            $output .= '    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:nth-child(2){  text-align:right; }';

            $output .= '</>';
            $output .= '<style>
                            @media (max-width: 991px) {
                             ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img{ display:inline-block !important;max-width:100% !important;height:auto !important;}
                            }
                    </style>';
            $output .= '<div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">';
            $output .= '    <h2 id="cptitletb" style="text-align:' . $select1615452416171 . '">' . $article->title . '</h2>';
            $output .= '   <hr />';
            $output .= '    <div class="detail-wrap" style="color:' . $color1615443385649 . '">';
            $output .= '    ' . $article->fulltext . '';
            $output .= '    </div>';

            if ($page_dttitle == 1) {
                $output .= '    <div class="btnBoxt">';

                if ($article->ontitle) {
                    $output .= '<a href="' . $on . '">' . $up_page_text . '：' . $article->ontitle . '</a>';
                }
                if ($article->downtitle) {
                    $output .= '<a href="' . $down . '">' . $next_page_text . '：' . $article->downtitle . '</a>';
                }

                $output .= '    </div>';
            } else {
                $output .= '    <div class="btnBox">';
                if ($page_button == 0) {

                    if ($article->on) {
                        $output .= '<a href="' . $on . '">' . $up_page_text . '</a>';
                    }
                    if ($article->down) {
                        $output .= '<a href="' . $down . '">' . $next_page_text . '</a>';
                    }
                }
                $output .= '    </div>';
            }

            $output .= '</div>';
            $output .= '<script language="javascript">';
            if($tdk_button == 'yes')
            {
                // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';

                $output .= '$(\'title\').text("' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';
                $output .= '$(\'meta[name="keywords"]\').attr("content","' . mb_substr(strip_tags($article->introtext), 0, 100, 'UTF-8') . '");';
                $output .= '$(\'meta[name="description"]\').attr("content","' . mb_substr(strip_tags($article->fulltext), 0, 200, 'UTF-8') . '");';

            }
            $output .= '</script>';

            return $output;
        }
        if ($art_type_selector_proinfo == 'type2') {
            $tit_cle = (isset($settings->tit_cle) && $settings->tit_cle) ? $settings->tit_cle : '#000'; //标题颜色

            $tit_cle_f = (isset($settings->tit_cle_f) && $settings->tit_cle_f) ? $settings->tit_cle_f : '#000'; //副标题颜色

            $text_cle_j = (isset($settings->text_cle_j) && $settings->text_cle_j) ? $settings->text_cle_j : '#000'; //简介颜色

            $img_w           = (isset($settings->img_w) && $settings->img_w) ? $settings->img_w : 215; //图片宽
            $img_h           = (isset($settings->img_h) && $settings->img_h) ? $settings->img_h : 315; //图片高
            $img_carousel_w  = (isset($settings->img_carousel_w) && $settings->img_carousel_w) ? $settings->img_carousel_w : 388; //轮播图片宽
            $img_carousel_h  = (isset($settings->img_carousel_h) && $settings->img_carousel_h) ? $settings->img_carousel_h : 315; //轮播图片高
            $nav_back_col    = (isset($settings->nav_back_col) && $settings->nav_back_col) ? $settings->nav_back_col : 0; //轮播图片设置宽高
            $tit_size        = (isset($settings->tit_size) && $settings->tit_size) ? $settings->tit_size : 42; //标题字体大小
            $tit_cle_f_size  = (isset($settings->tit_cle_f_size) && $settings->tit_cle_f_size) ? $settings->tit_cle_f_size : 14; //副标题字体大小
            $jj_size         = (isset($settings->jj_size) && $settings->jj_size) ? $settings->jj_size : 12; //简介字体大小
            $title_h2_button = (isset($settings->title_h2_button) && $settings->title_h2_button) ? $settings->title_h2_button : 'yes';
            $pro_title       = (isset($settings->pro_title) && $settings->pro_title) ? $settings->pro_title : '教师风采';
            $pro_vice_title  = (isset($settings->pro_vice_title) && $settings->pro_vice_title) ? $settings->pro_vice_title : 'teacher style';
            $img_style       = (isset($settings->img_style) && $settings->img_style) ? $settings->img_style : 'fill';
            //上一页下一页
            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $catid_id   = $_GET['catid_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            //获取产品详情的数据源

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);

            $page_button = (isset($settings->page_button) && $settings->page_button) ? $settings->page_button : 0;
            $pageColor   = (isset($settings->pageColor) && $settings->pageColor) ? $settings->pageColor : '';

            $pageBorderColor      = (isset($settings->pageBorderColor) && $settings->pageBorderColor) ? $settings->pageBorderColor : '';
            $pageBgColor          = (isset($settings->pageBgColor) && $settings->pageBgColor) ? $settings->pageBgColor : '';
            $pageColorhover       = (isset($settings->pageColorhover) && $settings->pageColorhover) ? $settings->pageColorhover : '';
            $pageBorderColorhover = (isset($settings->pageBorderColorhover) && $settings->pageBorderColorhover) ? $settings->pageBorderColorhover : '';
            $pageBgColorhover     = (isset($settings->pageBgColorhover) && $settings->pageBgColorhover) ? $settings->pageBgColorhover : '';

            $img_mode_type2 = (isset($settings->img_mode_type2) && $settings->img_mode_type2) ? $settings->img_mode_type2 : 'cover';
            $tit_cle_line_color = (isset($settings->tit_cle_line_color) && $settings->tit_cle_line_color) ? $settings->tit_cle_line_color : '#fecf41';
            $pro_vice_title_line_color = (isset($settings->pro_vice_title_line_color) && $settings->pro_vice_title_line_color) ? $settings->pro_vice_title_line_color : '#fee800';
            $pro_vice_title_color = (isset($settings->pro_vice_title_color) && $settings->pro_vice_title_color) ? $settings->pro_vice_title_color : '#fed561';
            $pro_vice_title_padding = (isset($settings->pro_vice_title_padding) && $settings->pro_vice_title_padding) ? $settings->pro_vice_title_padding : '0 0 0 10px';
            $tit_cle_line_margin = (isset($settings->tit_cle_line_margin) && $settings->tit_cle_line_margin) ? $settings->tit_cle_line_margin : '25px 0 0 0';
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= '<link rel="stylesheet" href="' . JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css' . '" >';
            $output .= "

            <style>
                {$addon_id} .content p img{display:inline-block;}

                {$addon_id} .container1{
                    margin: 0 auto;
                }
                {$addon_id}  .person-introduce {
                    padding-bottom: 23px;
                    border-bottom: 1px solid #f4f4f4;
                    overflow: visible;
                }
                {$addon_id}  .person-introduce::after {
                    content: '';
                    display: block;
                    clear: both;
                }
                {$addon_id}  .person-introduce .introduce .left_img{
                    width: {$img_w}px;
                    height: {$img_h}px;
                    object-fit: {$img_mode_type2};
                    margin-right: 20px;
                    float: left;
                }
                {$addon_id}  .person-introduce .introduce {
                    width: 100%;
                    float: right;
                    padding-top: 10px;
                }
                {$addon_id}  .person-introduce .introduce .name {
                    color: {$tit_cle};
                    font-size: {$tit_size}px;
                ";
                if($title_bold)
                {
                    $output .= "font-weight: bold;";
                }
                $output .= "    margin-bottom: 8px;
                }
                {$addon_id}  .person-introduce .introduce .position {
                    font-size: {$tit_cle_f_size}px;
                    color: {$tit_cle_f};
                }
                {$addon_id}  .person-introduce .introduce .split {
                    margin-top: 25px;
                    margin: {$tit_cle_line_margin};
                    width: 24px;
                    height: 5px;
                    background: {$tit_cle_line_color};
                }
                {$addon_id}  .person-introduce .introduce ul {
                    color: {$text_cle_j};
                    float: left;
                    margin-top: 20px;
                    padding-left: 0!important;
                }
                {$addon_id}  .person-introduce .introduce ul li {
                    position: relative;
                    font-size: {$jj_size}px;
                    color: {$text_cle_j};
                    line-height: 2;
                }
                {$addon_id}  .person-introduce .introduce ul li::before {
                    content: '';
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    background: #fdcf3f;
                    transform: rotate(45deg);
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                }
                {$addon_id}  .person-introduce .introduce ul li:nth-of-type(-n+5) {
                    padding-left: 20px;
                    list-style: none;
                }

                {$addon_id}{
                    width: 100%;
                    margin-top: 34px;
                }
                {$addon_id} .module-title {
                    position: relative;
                    padding-left: 10px;
                    padding: {$pro_vice_title_padding};
                    font-size: 16px;
                }
                {$addon_id} .module-title span {
                    text-transform: uppercase;
                    font-size: 10px;
                    color: {$pro_vice_title_color};
                    vertical-align: 1px;
                }
                {$addon_id} .module-title::before {
                    content: '';
                    display: inline-block;
                    width: 2px;
                    height: 18px;
                    background: {$pro_vice_title_line_color};
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                }
                {$addon_id} .swiper-box {
                    position: relative;
                }
                {$addon_id} .swiper-box -swiper {
                    margin-top: 20px;
                    width: 100%;
                    height: 321px;
                }
                {$addon_id} .swiper-box -swiper .swiper-slide {
                    width: 228px;
                    height: 100%;
                }";
            if ($nav_back_col == 0) {

                $output .= "
                    {$addon_id}   .swiper-box -swiper .swiper-slide img {
                          width: 100%;
                          height: 100%;
                          object-fit: cover;
                          object-position: top;
                    }";
            } else {
                $output .= "
                    {$addon_id}  .swiper-box -swiper .swiper-slide img {
                        width: {$img_carousel_w}%;
                        height: {$img_carousel_h}%;
                        object-fit: cover;
                        object-position: top;
                    }";
            }
            if ($article->on) {
                $output .= '    ' . $addon_id . ' .btnBox{  display:flex;justify-content:space-between;padding:20px; }';

            } else {
                $output .= '    ' . $addon_id . ' .btnBox{  display:flex;justify-content:flex-end;padding:20px; }';
            }
            $output .= '    ' . $addon_id . ' .btnBox a{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColor . ';color: ' . $pageColor . ';font-size: 14px;background:' . $pageBgColor . ';text-align: center;line-height: 40px;text-decoration: none;}';
            $output .= '    ' . $addon_id . ' .btnBox a:hover{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColorhover . ';color: ' . $pageColorhover . ';font-size: 14px;background:' . $pageBgColorhover . ';text-align: center;line-height: 40px;text-decoration: none;}';

            $output .= "
                {$addon_id} .swiper-box .swiper-button-next,   .swiper-box .swiper-button-prev {
                    width: 42px;
                    height: 42px;
                    background: #e0e0e0;
                    border-radius: 50%;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                }
                {$addon_id} .swiper-box .swiper-button-next:hover, .swiper-box .swiper-button-prev:hover {
                    background: #ffecb2;
                }
                {$addon_id} .swiper-box .swiper-button-next:hover::after, .swiper-box .swiper-button-prev:hover::after {
                    color: #f8c114;
                }
                {$addon_id} .swiper-box .swiper-button-next::after, .swiper-box .swiper-button-prev::after {
                    font-size: 16px;
                    color: #161616;
                }
                {$addon_id} .swiper-box .swiper-button-next {
                    right: -60px;
                }
                {$addon_id} .swiper-box .swiper-button-prev {
                    left: -60px;
                }
                {$addon_id} .content .cover{
                    display: none;
                }
                {$addon_id} .left_img{
                    display: block;
                }
                {$addon_id} .content{
                    float: left;
                    width:calc(100% - {$img_w}px - 20px);
                }

                @media screen and (max-width: 768px) {
                    {$addon_id} .container {
                        width: 100%;
                    }
                    {$addon_id} .person-introduce .introduce {
                        height: auto;
                        padding-right: 20px;
                        box-sizing: border-box;
                        padding-top: 0;
                    }
                    {$addon_id} .person-introduce .introduce .left_img {
                        width: 100%;
                        height: {$img_h}px;
                        object-fit: cover;
                        margin-right: 0;
                        float: none;
                    }
                    {$addon_id} .person-introduce .introduce .name {
                        margin-bottom: 14px;
                    }
                    {$addon_id} .person-introduce .introduce .position {
                        margin-bottom: 14px;
                    }
                    {$addon_id} .person-introduce .introduce ul {
                        float: left;
                        margin-top: 30px;
                        padding-left: 0!important;
                    }
                    {$addon_id} .person-introduce .introduce ul li {
                        padding-left: 20px;
                        list-style: none;
                        line-height: 1.5;
                        margin-bottom: 10px;
                    }
                    {$addon_id} .person-introduce .introduce ul li::before {
                        top: 6px;
                        margin: 0;
                    }
                    {$addon_id} .swiper-box .swiper-button-next,   .swiper-box .swiper-button-prev {
                        display: none;
                    }
                    {$addon_id} .swiper-box .presentation-swiper {
                        width: 90%;
                        margin-left:0;
                        height: 380px;
                    }
                    {$addon_id} .content{
                        float: left;
                        width: 100%;
                    }
                    {$addon_id} .content .cover{
                        display: block;
                    }
                }
                </style>
            ";

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $catid_id   = $_GET['catid_id'] ?? '';

            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            if (!empty($article->many_img)) {
                $many_img = explode(";", $article->many_img);
            } else {
                $many_img = '';
            }
            $output .= "
            <div class=\"presentation container1\">
                <div class=\"person-introduce\">
                    <div class=\"introduce\">
                        <img class=\"left_img\" src=\"{$article->image_intro}\" alt=\"\">
                        <div class=\"content\">
                            <p class=\"name\">{$article->title}</p>
                            <div class=\"position\">{$article->introtext}</div>
                            <div class=\"split\"></div>
                            {$article->fulltext}
                        </div>
                    </div>
                </div>";

            if ($many_img != '') {
                $output .= "
                    <div class=\"presentation-style\">
                        <div class=\"module-title\">{$pro_title}/ <span>{$pro_vice_title}</span></div>
                        <div class=\"swiper-box\">
                            <div class=\"presentation-swiper swiper-container\">
                                <div class=\"swiper-wrapper\">
                                ";
                foreach ($many_img as $v) {
                    $output .= "
                                    <div class=\"swiper-slide\">
                                        <a href=\"javascript:\">
                                            <img style='width: {$img_carousel_w}px;height: {$img_carousel_h}px; object-fit:{$img_style};' src=\"$v\" alt=\"\">
                                        </a>
                                    </div>
                                    ";
                }
                $output .= "
                            </div>
                        </div>
                        <!-- Add Arrows -->
                        <div class=\"swiper-button-next\"></div>
                        <div class=\"swiper-button-prev\"></div>

                    </div>
                </div>";
            }
            //翻页
            $output .= '    <div class="btnBox">';
            if ($page_button == 0) {

                if ($article->on) {
                    $output .= '<a href="' . $on . '">' . $up_page_text . '</a>';
                }
                if ($article->down) {
                    $output .= '<a href="' . $down . '">' . $next_page_text . '</a>';
                }
            }
            $output .= '    </div>';

            $output .= "
            </div>";

            $output .= "
            <script>
            function initSwiper(slidesPerView,spaceBetween,needNavigation){
                let settings={
                    slidesPerView,
                    spaceBetween
                }
                if (needNavigation){
                    settings.navigation={
                        nextEl: '{$addon_id} .swiper-button-next',
                        prevEl: '{$addon_id} .swiper-button-prev',
                    }
                }
                let swiper = new Swiper('{$addon_id} .swiper-container', settings);
                return swiper;
            }
            function initSwiperByWidth(){
                if (innerWidth>768){
                    initSwiper(3,17,true);
                }else if(innerWidth>734){
                    initSwiper(2,17,false);
                }else {
                    initSwiper(1,0,false);
                }
            }
              initSwiperByWidth();
            window.onresize=function (){
                initSwiperByWidth();
            }
            </script>
            ";

            $output .= '<script language="javascript">';
            if($title_h2_button == 'yes')
            {
                // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';

                $output .= '$(\'title\').text("' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';

            }

            if($keywords_h2_button == 'yes')
            {

                // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';
                $output .= '$(\'meta[name="keywords"]\').attr("content","' . mb_substr(strip_tags($article->introtext), 0, 100, 'UTF-8') . '");';
            }

            if($description_h2_button == 'yes')
            {
                $output .= '$(\'meta[name="description"]\').attr("content","' . mb_substr(strip_tags($article->fulltext), 0, 200, 'UTF-8') . '");';
            }
            $output .= '</script>';

            return $output;

        }
        if ($art_type_selector_proinfo == 'type3') {
            //分类标题
            $type3_class_title = (isset($settings->type3_class_title) && $settings->type3_class_title) ? $settings->type3_class_title : '产品展示';
            //分类副标题
            $type3_class_vice_title = (isset($settings->type3_class_vice_title) && $settings->type3_class_vice_title) ? $settings->type3_class_vice_title : 'Products';
            //分类背景颜色
            $type3_bg_col = (isset($settings->type3_bg_col) && $settings->type3_bg_col) ? $settings->type3_bg_col : '#09a694';
            //分类标题字体颜色
            $type3_text_col = (isset($settings->type3_text_col) && $settings->type3_text_col) ? $settings->type3_text_col : '#fff';
            //列表高度
            $type3_img_carousel_h = (isset($settings->type3_img_carousel_h) && $settings->type3_img_carousel_h) ? $settings->type3_img_carousel_h : 50;
            //列表宽度
            $type3_img_carousel_w = (isset($settings->type3_img_carousel_w) && $settings->type3_img_carousel_w) ? $settings->type3_img_carousel_w : 100;
            //列表背景颜色
            $type3_list_bg_col = (isset($settings->type3_list_bg_col) && $settings->type3_list_bg_col) ? $settings->type3_list_bg_col : '#cdb7a3';
            //列表字体颜色
            $type3_list_text_col = (isset($settings->type3_list_text_col) && $settings->type3_list_text_col) ? $settings->type3_list_text_col : '#fff';
            //分类id
            $type26_goods_catid = (isset($settings->type3_goods_catid) && $settings->type3_goods_catid) ? $settings->type3_goods_catid : 0;
            //列表数量
            $type3_bumber = (isset($settings->type3_bumber) && $settings->type3_bumber) ? $settings->type3_bumber : 0;
            //列表字数限制
            $type3_text_number = (isset($settings->type3_text_number) && $settings->type3_text_number) ? $settings->type3_text_number : 10;
            //列表字体大小
            $type3_text_size = (isset($settings->type3_text_size) && $settings->type3_text_size) ? $settings->type3_text_size : 16;
            //排序
            $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';

            $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;

            $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';

            $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();

            $page = $_GET['page'] ?? 1;
            //内容垂直位置调整
            $type3_vertical_location = (isset($settings->type3_vertical_location) && $settings->type3_vertical_location) ? $settings->type3_vertical_location : 0;
            //内容左右位置调整
            $type3_left_location = (isset($settings->type3_left_location) && $settings->type3_left_location) ? $settings->type3_left_location : 0;

            // 小屏的列表展示个数
            $type3_small_img_carousel_w = (isset($settings->type3_small_img_carousel_w) && $settings->type3_small_img_carousel_w) ? $settings->type3_small_img_carousel_w : 3;

            // 小屏列表外边距
            $type3_small_img_carousel_margin_right = (isset($settings->type3_small_img_carousel_margin_right) && $settings->type3_small_img_carousel_margin_right) ? $settings->type3_small_img_carousel_margin_right : 0;
            $type3_small_img_carousel_margin_bottom = (isset($settings->type3_small_img_carousel_margin_bottom) && $settings->type3_small_img_carousel_margin_bottom) ? $settings->type3_small_img_carousel_margin_bottom : 0;

            // 选中列表背景色
            $type3_list_bg_col_select = (isset($settings->type3_list_bg_col_select) && $settings->type3_list_bg_col_select) ? $settings->type3_list_bg_col_select : '#cdb7a3';

            $output = "
                <style>
                    {$addon_id} .pro_list{
                        width: 200px;
                        margin: 0;
                        float: left;
                    }
                    {$addon_id} .list_ttile{
                        width: 100%;
                        height: 100px;
                        text-align: center;
                        background-color: {$type3_bg_col};
                        color: {$type3_text_col};
                        border-top-right-radius:25px;
                    }
                    {$addon_id} .list_ttile h2{
                        line-height: 55px;
                        margin: 0;
                    }
                    {$addon_id} .list_ttile h3{
                        margin: 0;
                    }
                    {$addon_id} .list ul{
                        padding: 0;
                        margin: 0;
                    }
                    {$addon_id} .list ul li{
                        width: {$type3_img_carousel_w}%;
                        text-align: center;
                        background-color: {$type3_list_bg_col};
                        list-style-type: none;
                        margin-top: 15px;
                        line-height: {$type3_img_carousel_h}px;
                        color: {$type3_list_text_col};
                        font-size: {$type3_text_size}px;
                        cursor: pointer;
                        cursor: pointer;
                    }
                    {$addon_id} .list ul li.active{
                        background-color: {$type3_list_bg_col_select};
                    }
                    {$addon_id} .details_content{
                        float: left;
                        margin-left: 50px;
                        width: calc(100% - 200px - 50px);
                        display: none;
                        position: relative;
                        top: {$type3_vertical_location}px;
                        left: {$type3_left_location}px;
                    }
                    {$addon_id} .details_content.active{
                        display: block;
                    }
                    @media (max-width: 1024px) {
                        {$addon_id} .pro_list{
                            width: 100%;
                            float: none;
                        }
                        {$addon_id} .list ul{
                            overflow: hidden;
                        }
                        {$addon_id} .list ul li {
                            width: calc(calc(100% / {$type3_small_img_carousel_w} - {$type3_small_img_carousel_margin_right}px + {$type3_small_img_carousel_margin_right}px / 3));
                            margin-right: {$type3_small_img_carousel_margin_right}px;
                            margin-bottom: {$type3_small_img_carousel_margin_bottom}px;
                            float: left;
                        }
                        {$addon_id} .list ul li:nth-child({$type3_small_img_carousel_w}n){
                            margin-right: 0!important;
                        }
                        {$addon_id} .details_content{
                            float: none!important;
                            width: 100%!important;
                            left: 0!important;
                            margin-left: 0!important;
                        }
                    }
                </style>
            ";

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';

            //查出所有内容数据
            $app        = JFactory::getApplication();
            $input      = $app->input;
            $article_id = $input->get('detail');
            $catid_id   = $_GET['catid_id'] ?? '';
            //获取列表数据
            $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
            require_once $article_helper;

            $article = JwpagefactoryHelperGoods::getGoodsListProduct($type3_bumber, $ordering, $type26_goods_catid, $include_subcat, $post_type, $tagids, 0, $page, $company_id, $layout_id, $site_id);
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= "
                <div class=\"center\">
                    <!--列表-->
                    <div class=\"pro_list\">

                        <div class=\"list_ttile\">
                            <h2>{$type3_class_title}</h2>
                            <h3>{$type3_class_vice_title}</h3>
                        </div>
                        <div class=\"list\">
                            <ul>
                            ";

            if (!empty($article_id)) {
                foreach ($article as $k => $v) {
                    if ($v->id == $article_id) {
                        $output .= '<li class=\'anv_li active\' data-id=\'con-' . $k . '\'> ' . mb_substr($v->title, 0, $type3_text_number, 'UTF-8') . ' </li>';
                    } else {
                        $output .= '<li class=\'anv_li\' data-id=\'con-' . $k . '\'>' . mb_substr($v->title, 0, $type3_text_number, 'UTF-8') . '</li>';
                    }
                }
            } else {
                foreach ($article as $k => $v) {
                    if ($k == 0) {
                        $output .= '<li class=\'anv_li active\' data-id=\'con-' . $k . '\'> ' . mb_substr($v->title, 0, $type3_text_number, 'UTF-8') . ' </li>';
                    } else {
                        $output .= '<li class=\'anv_li\' data-id=\'con-' . $k . '\'>' . mb_substr($v->title, 0, $type3_text_number, 'UTF-8') . '</li>';
                    }
                }
            }

            $output .= "
                            </ul>
                        </div>
                    </div>";
                    if (!empty($article_id)) {
                        foreach ($article as $k => $v) {
                            if ($v->id == $article_id) {
                                $output .= "
                                <!--详情内容-->
                                <div class=\"details_content active\" id='con-{$k}'>
                                {$v->introtext}
                                </div>";
                            } else {
                                $output .= "
                                <!--详情内容-->
                                <div class=\"details_content\" id='con-{$k}'>
                                {$v->fulltext}
                                </div>";
                            }
                        }
                    }else{
                        foreach ($article as $k => $v) {
                            if ($k == 0) {
                                $output .= "
                                <!--详情内容-->
                                <div class=\"details_content active\" id='con-{$k}'>
                                {$v->introtext}
                                </div>";
                            } else {
                                $output .= "
                                <!--详情内容-->
                                <div class=\"details_content\" id='con-{$k}'>
                                {$v->fulltext}
                                </div>";
                            }
                        }
                    }
            $output .= "
                </div>";
            $output .= "
                <script>
                jQuery('{$addon_id} .anv_li').click(function (){
                    jQuery(this).siblings().removeClass(\"active\")
                    jQuery(this).addClass('active');
                    var id =jQuery(this).attr('data-id');
                    jQuery('{$addon_id} #'+id).addClass('active').siblings().removeClass('active');
                });
                </script>
            ";

        }
        if ($art_type_selector_proinfo == 'type4') {
            $post_type      = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
            $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
            $ordering       = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
            $limit          = (isset($settings->limit) && $settings->limit) ? $settings->limit : 10;
            $intro_limit    = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 100;
            $tagids         = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
            $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
            $show_proData   = (isset($settings->show_pro_data) && $settings->show_pro_data) ? $settings->show_pro_data : 0;

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            $page       = $_GET['page'] ?? 1;
            if (!is_numeric($_GET['page'])) {
                $page = 1;
            }
            //查出所有内容数据
            $app        = JFactory::getApplication();
            $input      = $app->input;
            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            // print_r($detail_id);
            $catid_id    = $_GET['catid_id'] ?? '';
            $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;
            // 获取数据
            $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
            require_once $article_helper;

            $items = JwpagefactoryHelperGoods::getGoodsListProduct($limit, $ordering, $goods_catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
            // print_r($items);die;
            if ($show_proData) {
                //获取该分类下的所有数据
                $items = JwpagefactoryHelperGoods::getGoodsList_sub($limit, $ordering, $goods_catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
                // print_r($items);die;
                $items_count = $items['total'];
            }

            $gl_show           = (isset($settings->gl_show) && $settings->gl_show) ? $settings->gl_show : 5;
            $mr_title_color    = (isset($settings->mr_title_color) && $settings->mr_title_color) ? $settings->mr_title_color : '#333333';
            $mr_title_color_gl = (isset($settings->mr_title_color_gl) && $settings->mr_title_color_gl) ? $settings->mr_title_color_gl : '#0a4a9b';
            $right_title_color = (isset($settings->right_title_color) && $settings->right_title_color) ? $settings->right_title_color : '#0a4a9b';
            $right_desc_color  = (isset($settings->right_desc_color) && $settings->right_desc_color) ? $settings->right_desc_color : '#626262';

            $type4_list_padding = (isset($settings->type4_list_padding) && $settings->type4_list_padding) ? $settings->type4_list_padding : '0 0 0 40px';
            $top_padding = explode(' ', $type4_list_padding)[0];
            $right_padding = explode(' ', $type4_list_padding)[1];
            $bottom_padding = explode(' ', $type4_list_padding)[2];
            $left_padding = explode(' ', $type4_list_padding)[3];
            $type4_list_bg_color = (isset($settings->type4_list_bg_color) && $settings->type4_list_bg_color) ? $settings-> type4_list_bg_color : '#f8f8f8';
            $type4_list_light_bg_color = (isset($settings->type4_list_light_bg_color) && $settings->type4_list_light_bg_color) ? $settings-> type4_list_light_bg_color : '#e2e3e4';
            $right_title_size = (isset($settings->right_title_size) && $settings->right_title_size) ? $settings-> right_title_size : 25;
            $right_desc_size = (isset($settings->right_desc_size) && $settings->right_desc_size) ? $settings-> right_desc_size : 12;
            $right_img_title_size = (isset($settings->right_img_title_size) && $settings->right_img_title_size) ? $settings-> right_img_title_size : 16;
            $right_img_title_height = (isset($settings->right_img_title_height) && $settings->right_img_title_height) ? $settings-> right_img_title_height : 35;
            $right_img_bg_color = (isset($settings->right_img_bg_color) && $settings->right_img_bg_color) ? $settings-> right_img_bg_color : '#798ca4';
            $right_img_title_color = (isset($settings->right_img_title_color) && $settings->right_img_title_color) ? $settings-> right_img_title_color : '#fff';
            $right_arrow_color = (isset($settings->right_arrow_color) && $settings->right_arrow_color) ? $settings-> right_arrow_color : '#f7941d';
            if (isset($settings->right_img_width) && $settings->right_img_width) {
                if (is_object($settings->right_img_width)) {
                    $right_img_width = $settings->right_img_width->md;
                    $right_img_width_sm = $settings->right_img_width->sm;
                    $right_img_width_xs = $settings->right_img_width->xs;
                } else {
                    $right_img_width = $settings->right_img_width;
                    $right_img_width_sm = $settings->footer_label_font_size_sm;
                    $right_img_width_xs = $settings->footer_label_font_size_xs;
                }
            } else {
                $right_img_width = 30;
                $right_img_width_sm = 100;
                $right_img_width_xs = 100;
            }
            $type4_bg_color = (isset($settings->type4_bg_color) && $settings->type4_bg_color) ? $settings->type4_bg_color : '#f2f5fa';
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output = "<style>
            {$addon_id} .product {background: {$type4_bg_color};position: relative;padding: 0 0 20px 0;}
            {$addon_id} .product-lei {width: 270px;position: absolute;top: 0;left: 0;background: {$type4_bg_color};transition: all 1s;z-index: 9;padding-bottom: {$bottom_padding};}
            {$addon_id} .product-lei ul {width: auto;overflow: hidden;background: {$type4_list_bg_color};padding: {$top_padding} 0 0 0;}
            {$addon_id} .product-lei-on {overflow: visible!important;background: {$type4_bg_color}!important;height: auto!important;padding-bottom: 2%!important;}
            {$addon_id} .product-lei li {overflow: hidden;vertical-align: bottom;height: 44px;line-height: 44px;transition: all 1s;padding-left: {$left_padding};padding-right: {$right_padding};list-style: none;}
            {$addon_id} .product-lei li span {color: {$mr_title_color};font-size: 16px;line-height: 44px;padding-left: 20px;cursor: pointer;}
            @media (min-width: 1200px){{$addon_id} .container {width: 1170px;}}
            {$addon_id} .container {padding-right: 15px;padding-left: 285px;}
            {$addon_id} .product-leixq {padding-top: 30px;display: none;}
            {$addon_id} .lei-pic {width: 473px;float: left;margin-right: 40px;overflow: hidden;position: relative;transition: all 1s ease 0s;}
            {$addon_id} .lei-pic img {transition: all 1s ease 0s;width: 100%;}
            {$addon_id} .lei-con {width: 600px;float: right;}
            {$addon_id} .lei-title {color: {$mr_title_color_gl};font-size: 35px;font-weight: bold;padding: 30px 0 20px 0;}
            {$addon_id} .lei-content {color: #8193aa;font-size: 15px;line-height: 32px;}
            {$addon_id} .ui-box {text-decoration: none;position: relative;vertical-align: baseline;}
            {$addon_id} .more {color: {$mr_title_color_gl};font-size: 15px;display: block;width: 128px;height: 38px;text-align: center;line-height: 38px;border: 1px solid #00306d;margin-top: 35px;}
            {$addon_id} .clear {clear: both;}
            {$addon_id} .product-bg2 span {color: {$mr_title_color_gl}!important;}
            {$addon_id} .pro-san {width: 1120px;padding-top: 45px;}
            {$addon_id} .lei2-title {color: {$right_title_color};font-size: {$right_title_size}px;padding-bottom: 25px;";
            if($title_bold)
            {
                $output .= "font-weight: bold;";
            }
            $output            .= "
            }
            {$addon_id} .lei2-txt {line-height: 35px;color: {$right_desc_color};font-size: {$right_desc_size}px;padding-bottom: 50px;}
            {$addon_id} .col-md-4 {width: {$right_img_width}%;flex: 0 0 {$right_img_width}%;max-width: {$right_img_width}%;float: left;position: relative;min-height: 1px;padding-right: 15px;padding-left: 15px;}
            {$addon_id} .lei3-li a {transition: all 1s ease 0s;}
            {$addon_id} .lei3-pic img {width: 100%;}
            {$addon_id} .hbtn {position: relative;box-sizing: border-box;display: inline-block;overflow: hidden;text-decoration: none;white-space: nowrap;z-index: 0;}
            {$addon_id} .lei3-t {width: 100%;height: {$right_img_title_height}px;line-height: {$right_img_title_height}px;background: {$right_img_bg_color};}
            {$addon_id} .lei3-t .span {display: block;padding-left: 18px;color: {$right_img_title_color};font-size: {$right_img_title_size}px;}
            {$addon_id} .jiantou {width: 57px;height: 100%;line-height: 35px;background: {$right_arrow_color};position: absolute;right: 0;bottom: 0;text-align: center;}
            {$addon_id} .jiantou img {animation: 3s ease 0s normal none infinite running mymove;position: absolute;left: 0;top: 0;bottom: 0;right: 0;margin: auto;}
            {$addon_id} .lei3-li:hover .hbtn.hb-fill-right:before {width: 100%;height: 100%;opacity: 1;}
            {$addon_id} .hbtn.hb-fill-right:before {position: absolute;content: '';background: {$right_arrow_color};transition-duration: .3s;z-index: -1;top: 0;right: auto;bottom: auto;left: 0;width: 0;height: 100%;opacity: 1;}
            {$addon_id} .product-bg2 {background: {$type4_list_light_bg_color};color: #0a4a9b;}
            {$addon_id} .product-bg2 span{color: {$mr_title_color_gl}!important;font-weight:bold;}
            {$addon_id} .ppp p span{font-size:10px;}
            {$addon_id} .product-lei li span:hover {background: url(https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/lei2-jian.png) no-repeat center left;color: {$mr_title_color_gl};}
            @media (max-width: 991px) and (min-width: 768px) {
                {$addon_id} .col-md-4 {
                    width: {$right_img_width_sm}%;
                    float: none;
                    max-width: 100%;
                }
            }
            @media (max-width: 767px){
                {$addon_id} .product-lei {
                    width: 100%;
                    position: static;
                }
                {$addon_id} .container{
                    width: 100%;
                    padding-left: 15px;
                }
                {$addon_id} .col-md-4 {
                    width: {$right_img_width_xs}%;
                    float: none;
                }
            }
            </style>";
            $output .= '
            <div class="product ">
                <div class="product-lei " data-scroll-reveal="enter bottom over 1s and move 100px" data-scroll-reveal-id="10" style="-webkit-transform: translatey(0);transform: translatey(0);opacity: 1;-webkit-transition: -webkit-transform 1s ease-in-out 0s,  opacity 1s ease-in-out 0s;transition: transform 1s ease-in-out 0s, opacity 1s ease-in-out 0s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">
                    <ul class>';
            foreach ($items as $key => $item) {
                if ($key < $gl_show) {
                    if ($article_id == $item->id) {
                        $output .= '<li data-cid="' . $item->id . '" class="product-bg1 lei-on"><span>' . $item->title . '</span></li>';
                    } else {
                        $output .= '<li data-cid="' . $item->id . '" class="product-bg1"><span>' . $item->title . '</span></li>';
                    }

                } else {
                    if ($article_id == $item->id) {
                        $output .= '<li data-cid="' . $item->id . '" class="product-bg2 lei-on"><span>' . $item->title . '</span></li>';
                    } else {
                        $output .= '<li data-cid="' . $item->id . '" class="product-bg2"><span>' . $item->title . '</span></li>';
                    }

                }
            }
            $output .= '</ul>
                    </div>
                <div class="product-info container " data-scroll-reveal="enter right after 0.5s" data-scroll-reveal-id="11" style="-webkit-transform: translatex(0);transform: translatex(0);opacity: 1;-webkit-transition: -webkit-transform 0.66s ease-in-out 0.5s,  opacity 0.66s ease-in-out 0.5s;transition: transform 0.66s ease-in-out 0.5s, opacity 0.66s ease-in-out 0.5s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">';
            foreach ($items as $key => $item) {
                if ($article_id == $item->id) {
                    $output .= '<div class="product-leixq right g' . $item->id . '" style="display: block;">';
                } else {
                    $output .= '<div class="product-leixq right g' . $item->id . '" style="display: none;">';
                }

                $output .= '<div class="lei2-title">' . $item->title . '</div>';
                $output .= '<div class="lei2-txt"><p style="text-indent:2em;" class="ppp">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</p> <p style="text-indent:2em;"><br></p>';
                $output .= '</div>';
                $output .= '<div class="row">';
                $output .= '<div class="col-md-4 lei3-li " data-scroll-reveal="enter bottom after 0.5s" data-scroll-reveal-id="16" style="-webkit-transform: translatey(0);transform: translatey(0);opacity: 1;-webkit-transition: -webkit-transform 0.66s ease-in-out 0.5s,  opacity 0.66s ease-in-out 0.5s;transition: transform 0.66s ease-in-out 0.5s, opacity 0.66s ease-in-out 0.5s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">
                <a href="?p=products_list2&amp;c_id=40&amp;lanmu=2" style="display: block;">';
                $output .= '<div class="lei3-pic"><img src="' . $item->image_thumbnail . '" alt="' . $item->title . '" titel="' . $item->title . '"><br></div>';
                $output .= '<div class="lei3-t  hbtn hb-fill-right">';
                $output .= '<span class="span">' . $item->title . '</span>';
                $output .= '<div class="jiantou"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/jiantou.png" alt=""></div>';
                $output .= '</div></a></div></div></div>';

            }
            $output .= '<div class="clear"></div>

                </div>
            </div>';
            $output .= "
            <script>";
            if ($article_id == 0 && $article_id == '') {
                $output .= "$('{$addon_id} .product-leixq').eq(0).show();
                $('{$addon_id} .product-lei li').eq(0).addClass('lei-on');";
            }

            $output .= "
            $('{$addon_id} .product-lei li').click(function(){
                $(this).addClass('lei-on')
                $('{$addon_id} .product-lei li').not($(this)).removeClass('lei-on')
                var cid=$(this).attr('data-cid');
                $('{$addon_id} .product-leixq').hide();
                $('{$addon_id} .g'+cid).show();
            })
                function lodex()
                {
                    $('{$addon_id} .pro1-lei li:odd').css({'margin-right':'0px'})
                    $('{$addon_id} .pro-info').hide();
                    $('{$addon_id} .pro-info').eq(0).show();
                    $('{$addon_id} .pro-lei li').eq(0).addClass('lei-on2');
                    $('{$addon_id} .pro-lei li').click(function(){
                        $(this).addClass('lei-on2')
                        $('{$addon_id} .pro-lei li').not($(this)).removeClass('lei-on2')
                        var cid=$(this).attr('data-cid');
                        $('{$addon_id} .pro-info').hide();
                        $('{$addon_id} .prod'+cid).show();
                    })


                }
                window.onload=lodex;
                window.onresize=lodex;

            </script>
            ";
        }
        if ($art_type_selector_proinfo == 'type5') {
            // 产品详情的标题设置为网页标题
            $title_h2_button = (isset($settings->title_h2_button) && $settings->title_h2_button) ? $settings->title_h2_button : 'yes';
            // 标题布局
            $type5_title_align = (isset($settings->type5_title_align) && $settings->type5_title_align) ? $settings->type5_title_align : '';
            // 标题字体大小
            $type5_font_size = (isset($settings->type5_font_size) && $settings->type5_font_size) ? $settings->type5_font_size : '';
            // 标题文字颜色
            $type5_title_color = (isset($settings->type5_title_color) && $settings->type5_title_color) ? $settings->type5_title_color : '';
            // 关闭标题两侧横线
            $type5_title_l = (isset($settings->type5_title_l) && $settings->type5_title_l) ? $settings->type5_title_l : 0;
            // 标题两侧横线宽度
            $type5_title_l_w = (isset($settings->type5_title_l_w) && $settings->type5_title_l_w) ? $settings->type5_title_l_w : '';
            // 标题两侧横线高度
            $type5_title_l_h = (isset($settings->type5_title_l_h) && $settings->type5_title_l_h) ? $settings->type5_title_l_h : '';
            // 标题两侧横线与标题间距
            $type5_title_l_m = (isset($settings->type5_title_l_m) && $settings->type5_title_l_m) ? $settings->type5_title_l_m : '';
            // 日期布局
            $type5_date_align = (isset($settings->type5_date_align) && $settings->type5_date_align) ? $settings->type5_date_align : '';
            // 日期&页面导航字体大小
            $type5_font_size_date = (isset($settings->type5_font_size_date) && $settings->type5_font_size_date) ? $settings->type5_font_size_date : '';
            // 时间&页面导航颜色
            $type5_date_color = (isset($settings->type5_date_color) && $settings->type5_date_color) ? $settings->type5_date_color : '';
            // 关闭页面导航
            $type5_home = (isset($settings->type5_home) && $settings->type5_home) ? $settings->type5_home : 0;
            // 页面导航布局
            $type5_home_align = (isset($settings->type5_home_align) && $settings->type5_home_align) ? $settings->type5_home_align : '';
            // 页面导航标题颜色
            $type5_home_color = (isset($settings->type5_home_color) && $settings->type5_home_color) ? $settings->type5_home_color : '';
            // 页面导航图标
            $type5_home_icon = (isset($settings->type5_home_icon) && $settings->type5_home_icon) ? $settings->type5_home_icon : '';
            // 页面导航栏目名称文字
            $type5_home_text = (isset($settings->type5_home_text) && $settings->type5_home_text) ? $settings->type5_home_text : '';
            // 内容上放横线颜色
            $type5_titleL_color = (isset($settings->type5_titleL_color) && $settings->type5_titleL_color) ? $settings->type5_titleL_color : '';
            // 内容上边距
            $type5_content_top = (isset($settings->type5_content_top) && $settings->type5_content_top) ? $settings->type5_content_top : '';
            // 关闭内容部分左侧封面图显示
            $type5_content_img = (isset($settings->type5_content_img) && $settings->type5_content_img) ? $settings->type5_content_img : 0;
            // 内容部分图片宽度
            $type5_content_img_w = (isset($settings->type5_content_img_w) && $settings->type5_content_img_w) ? $settings->type5_content_img_w : '';
            // 内容部分图片与文字间距
            $type5_content_text_m = (isset($settings->type5_content_text_m) && $settings->type5_content_text_m) ? $settings->type5_content_text_m : '';

            $content_text_w = number_format($type5_content_img_w) + number_format($type5_content_text_m);

            //翻页按钮是否开启
            $type5_page_style = (isset($settings->type5_page_style) && $settings->type5_page_style) ? $settings->type5_page_style : 'page01';
            // 正常上一页图标
            $page_prev = (isset($settings->page_prev) && $settings->page_prev) ? $settings->page_prev : '';
            // 正常下一页图标
            $page_prev_hover = (isset($settings->page_prev_hover) && $settings->page_prev_hover) ? $settings->page_prev_hover : '';
            // 移入上一页图标
            $page_next = (isset($settings->page_next) && $settings->page_next) ? $settings->page_next : '';
            // 移入下一页图标
            $page_next_hover = (isset($settings->page_next_hover) && $settings->page_next_hover) ? $settings->page_next_hover : '';
            // 翻页上边距
            $type5_page_top = (isset($settings->type5_page_top) && $settings->type5_page_top) ? $settings->type5_page_top : '';
            // 翻页字体大小
            $type5_page_font_size = (isset($settings->type5_page_font_size) && $settings->type5_page_font_size) ? $settings->type5_page_font_size : '';
            // 翻页文字行高
            $type5_page_lineHeight = (isset($settings->type5_page_lineHeight) && $settings->type5_page_lineHeight) ? $settings->type5_page_lineHeight : '';
            // 翻页中间线样式
            $type5_page_line = (isset($settings->type5_page_line) && $settings->type5_page_line) ? $settings->type5_page_line : '';
            // 下划线颜色
            $type5_page_line_color = (isset($settings->type5_page_line_color) && $settings->type5_page_line_color) ? $settings->type5_page_line_color : '';

            // 作者从后台数据获取
            $type5_home_author_data = (isset($settings->type5_home_author_data) && $settings->type5_home_author_data) ? $settings->type5_home_author_data : 0;
            // 页面导航作者文字
            $type5_home_author = (isset($settings->type5_home_author) && $settings->type5_home_author) ? $settings->type5_home_author : '';

            // 导航标题字数限制
            $type5_home_title_limit_open = (isset($settings->type5_home_title_limit_open) && ($settings->type5_home_title_limit_open || $type5_home_title_limit_open == 0)) ? $settings -> type5_home_title_limit_open : 0;
            $type5_home_title_limit = (isset($settings->type5_home_title_limit) && $settings->type5_home_title_limit) ? $settings -> type5_home_title_limit : 10;

            // 页面导航是否和日期等同在一行
            $type5_home_title_inline = (isset($settings->type5_home_title_inline) && ($settings->type5_home_title_inline || $settings->type5_home_title_inline == 0)) ? $settings -> type5_home_title_inline : 1;
            $type5_position = $type5_home_title_inline ? 'absolute' : 'static';

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $catid_id   = $_GET['catid_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            //获取产品详情的数据源

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);

            $author = '';
            if ($type5_home_author_data == 1) {
                $author .= $article->label2;
            } else {
                $author .= $type5_home_author;
            }
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= "<style>
                {$addon_id} * {
                    margin: 0;
                }
                {$addon_id} .content-text p img{display:inline-block;}
                {$addon_id} .content-box hr {
                    border-top-color: {$type5_titleL_color};
                }
                {$addon_id} .content-box .title {
                    text-align:{$type5_title_align};
                    font-size: {$type5_font_size}px;
                    color: {$type5_title_color};
                ";
                if($title_bold)
                {
                    $output .= "font-weight: bold;";
                }
                $output .= "
                    margin-bottom: 20px;
                }
                {$addon_id} .content-box .title .line {
                    display: inline-block;
                    width: {$type5_title_l_w}px;
                    height: {$type5_title_l_h}px;
                    background-color: {$type5_title_color};
                    margin: 0 {$type5_title_l_m}px;
                    vertical-align: middle;
                }
                {$addon_id} .content-box .time-box {
                    position: relative;
                    margin-bottom: 20px;
                }
                {$addon_id} .content-box .time-info {
                    text-align: {$type5_date_align};
                    display: block;
                    padding-right: 10px;
                    color: {$type5_date_color};
                    font-size: {$type5_font_size_date}px;
                }
                {$addon_id} .content-box .home-menu {
                    position:{$type5_position};
                    {$type5_home_align}: 0;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    display: flex;
                    align-items: center;
                    color: {$type5_date_color};
                    font-size: {$type5_font_size_date}px;
                }
                {$addon_id} .content-box .home-menu .icon {
                    margin-right: 5px;
                    width: {$type5_font_size_date}px;
                }
                {$addon_id} .content-box .home-menu span {
                    color: {$type5_home_color};
                }
                {$addon_id} .content-box .content-info {
                    margin-top: {$type5_content_top}px;
                    display: flex;
                    justify-content: space-between;
                }
                {$addon_id} .content-box .content-info .content-img {
                    width: {$type5_content_img_w}px;
                }
                {$addon_id} .content-box .content-info .content-img img {
                    width: 100%;
                }
                {$addon_id} .content-box .content-info .content-text {";
            if ($type5_content_img == 1) {
                $output .= "width: 100%;";
            } else {
                $output .= "width: calc(100% - {$content_text_w}px);";
            }
            $output .= "}
                /*翻页 布局01*/
                {$addon_id} .btnBox {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                }
                {$addon_id} .btnBox a {
                    width: 120px;
                    height: 40px;
                    border: 1px solid {$pageBorderColor};
                    color: {$pageColor};
                    font-size: 14px;background:{$pageBgColor};
                    text-align: center;
                    line-height: 40px;
                    text-decoration: none;
                }
                {$addon_id} .btnBox a:hover {
                    width: 120px;
                    height: 40px;
                    border: 1px solid {$pageBorderColorhover};
                    color: {$pageColorhover};
                    font-size: 14px;
                    background:{$pageBgColorhover};
                    text-align: center;
                    line-height: 40px;
                    text-decoration: none;
                }
                {$addon_id} .btnBoxt {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                }
                {$addon_id} .btnBoxt a {
                    width: 50%;
                    height: 40px;
                    color: {$pageColor};
                    font-size: 14px;background:{$pageBgColor};
                    text-align: left;
                    line-height: 40px;
                    text-decoration: none;
                }
                {$addon_id} .btnBoxt a:nth-child(2) {
                    text-align: right;
                }
                {$addon_id} .btnBoxt a:hover {
                    height: 40px;
                    color: {$pageColorhover};
                    font-size: 14px;
                    background:{$pageBgColorhover};
                    line-height: 40px;
                    text-decoration: none;
                }

                /*翻页 布局02*/
                {$addon_id} .page02-btnBox {
                    margin-top: {$type5_page_top}px;
                }
                {$addon_id} .page02-btnBox a {
                    color: {$pageColor};
                    font-size: {$type5_page_font_size}px;
                    line-height: {$type5_page_lineHeight}px;
                    display: flex;
                    align-items: center;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                {$addon_id} .page02-btnBox a img {
                    margin-right: 5px;
                    display: none;
                    width: {$type5_page_font_size}px;
                }
                {$addon_id} .page02-btnBox a .normal {
                    display: block;
                }
                {$addon_id} .page02-btnBox a:hover {
                    color: {$pageColorhover};
                }
                {$addon_id} .page02-btnBox a:hover .normal {
                    display: none;
                }
                {$addon_id} .page02-btnBox a:hover .hover {
                    display: block;
                }
                {$addon_id} .page02-btnBox .page-line {
                    width: 100%;
                    border-top: 1px {$type5_page_line} {$type5_page_line_color};
                }
                @media (max-width: 991px) {
                    {$addon_id} .content-box .home-menu {
                        position: inherit;
                        margin-bottom: 10px;
                    }
                    {$addon_id} .content-box .content-info {
                        flex-direction: column;
                        align-items: center;
                    }
                    {$addon_id} .content-box .content-info .content-img {
                        max-width: 100%;
                    }
                    {$addon_id} .content-box .content-info .content-text {
                        width: 100%;
                        margin-top: {$type5_content_text_m}px;
                    }
                }
            </style>";
            //var_dump($article);

            $output .= '<div class="content-box">';
            $output .= '<h2 class="title">';
            if ($type5_title_l != 1) {
                $output .= '<span class="line"></span>';
            }
            $output .= $article->title;
            if ($type5_title_l != 1) {
                $output .= '<span class="line"></span>';
            }
            $output .= '</h2>';
            $output .= '<div class="time-box">';
            $output .= '<p class="time-info">发布时间：' . $article->created . '&nbsp;&nbsp;&nbsp;浏览量：' . $article->hits . '&nbsp;&nbsp;&nbsp;作者：' . $author . '</p>';
            if ($type5_home != 1) {
                $output .= '<div class="home-menu"><img src=\'' . $type5_home_icon . '\' class="icon" /> <p> 首页 > ' . $type5_home_text . ' >';
                if($type5_home_title_limit_open){
                    $output .= '<span>' . mb_substr(strip_tags($article->title), 0, $type5_home_title_limit, 'UTF-8') . '...</span>';
                }else{
                    $output .= '<span>' . $article->title . '</span>';
                }
                $output .= '</p></div>';
            }
            $output .= '</div>';
            $output .= '<hr />';
            $output .= '<div class="content-info">';
            if ($type5_content_img != 1) {
                $output .= '<div class="content-img">
                    <img src="' . $article->image_intro . '" />
                </div>';
            }
            $output .= '<div class="content-text">
                ' . $article->fulltext . '
            </div>';
            $output .= '</div>';
            if ($page_button == 0) {
                if ($type5_page_style == 'page02') {
                    $output .= '<div class="page02-btnBox">';
                    $on_link = '';
                    $on_text = '无';
                    if ($article->on) {
                        $on_link = 'href="' . $on . '"';
                        $on_text = JwPageFactoryBase::getGoodsById($article->on, $catid_id)->title;
                    }
                    $output .= '<a ' . $on_link . '>
                        <img src=\'' . $page_prev . '\' class="normal" />
                        <img src=\'' . $page_prev_hover . '\' class="hover" />
                        ' . $up_page_text . $on_text . '
                    </a>';
                    $output .= '<div class="page-line"></div>';
                    $down_link = '';
                    $down_text = '无';
                    if ($article->down) {
                        $down_link = 'href="' . $down . '"';
                        $down_text = JwPageFactoryBase::getGoodsById($article->down, $catid_id)->title;
                    }
                    $output .= '<a ' . $down_link . '>
                        <img src=\'' . $page_next . '\' class="normal" />
                        <img src=\'' . $page_next_hover . '\' class="hover" />
                        ' . $next_page_text . $down_text . '
                    </a>';
                    $output .= '</div>';
                } else {

                    if ($page_dttitle == 1) {
                        $output .= '    <div class="btnBoxt">';

                        if ($article->ontitle) {
                            $output .= '<a href="' . $on . '">' . $up_page_text . '：' . $article->ontitle . '</a>';
                        }
                        if ($article->downtitle) {
                            $output .= '<a href="' . $down . '">' . $next_page_text . '：' . $article->downtitle . '</a>';
                        }

                        $output .= '    </div>';
                    } else {

                        $output .= '    <div class="btnBox">';
                        if ($article->on) {
                            $output .= '<a href="' . $on . '">' . $up_page_text . '</a>';
                        }
                        if ($article->down) {
                            $output .= '<a href="' . $down . '">' . $next_page_text . '</a>';
                        }
                        $output .= '    </div>';
                    }
                }
            }
            $output .= '</div>';
            $output .= '<script language="javascript">';
            if($title_h2_button == 'yes')
            {
                // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';

                $output .= '$(\'title\').text("' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';

            }

            if($keywords_h2_button == 'yes')
            {

                // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';
                $output .= '$(\'meta[name="keywords"]\').attr("content","' . mb_substr(strip_tags($article->introtext), 0, 100, 'UTF-8') . '");';
            }

            if($description_h2_button == 'yes')
            {
                $output .= '$(\'meta[name="description"]\').attr("content","' . mb_substr(strip_tags($article->fulltext), 0, 200, 'UTF-8') . '");';
            }
            $output .= '</script>';

            return $output;
        }

        if ($art_type_selector_proinfo == 'type6') {
            $font_size           = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '';
            $color1615452944411  = (isset($settings->color1615452944411) && $settings->color1615452944411) ? $settings->color1615452944411 : '#333';
            $force_intro_font_size_type6  = (isset($settings->force_intro_font_size_type6) && ($settings->force_intro_font_size_type6 || $settings->force_intro_font_size == 0)) ? $settings->force_intro_font_size_type6 : 0;
            $intro_fontsize06 = (isset($settings->intro_fontsize06) && $settings->intro_fontsize06) ? $settings->intro_fontsize06 : '13';
            $intro_color06    = (isset($settings->intro_color06) && $settings->intro_color06) ? $settings->intro_color06 : '#888';
            $timg_width06     = (isset($settings->timg_width06) && $settings->timg_width06) ? $settings->timg_width06 : '380';
            $footer_title_font_size = (isset($settings->footer_title_font_size) && $settings->footer_title_font_size) ? $settings->footer_title_font_size : 16;
            $footer_title_color = (isset($settings->footer_title_color) && $settings->footer_title_color) ? $settings->footer_title_color : '';
            $select1615452416171 = (isset($settings->select1615452416171) && $settings->select1615452416171) ? $settings->select1615452416171 : '';
            if (isset($settings->footer_label_font_size) && $settings->footer_label_font_size) {
                if (is_object($settings->footer_label_font_size)) {
                    $footer_label_font_size = $settings->footer_label_font_size->md;
                    $footer_label_font_size_sm = $settings->footer_label_font_size->sm;
                    $footer_label_font_size_xs = $settings->footer_label_font_size->xs;
                } else {
                    $footer_label_font_size = $settings->footer_label_font_size;
                    $footer_label_font_size_sm = $settings->footer_label_font_size_sm;
                    $footer_label_font_size_xs = $settings->footer_label_font_size_xs;
                }
            } else {
                $footer_label_font_size = '';
                $footer_label_font_size_sm = '14';
                $footer_label_font_size_xs = '14';
            }
            $footer_label_color = (isset($settings->footer_label_color) && $settings->footer_label_color) ? $settings->footer_label_color : '';

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $catid_id   = $_GET['catid_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            //获取产品详情的数据源

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= '
                <style>
                    ' . $addon_id . ' .fa{font-family: "Font Awesome 5 Free";}
                    ' . $addon_id . ' .container_content {
                        clear: both;
                    }
                    ' . $addon_id . ' .sytext p img{display:inline-block;}
                    ' . $addon_id . ' .clear {
                        clear: both;
                    }
                    ' . $addon_id . ' .fl {
                        float: left;
                    }
                    ' . $addon_id . ' .fr {
                        float: right;
                    }
                    ' . $addon_id . ' p {
                        margin:0;
                    }

                    ' . $addon_id . ' .timg {
                        width:' . $timg_width06 . 'px;
                    }
                    ' . $addon_id . ' .sytext {
                        width:calc( 100% - ' . $timg_width06 . 'px - 20px );
                    }

                    ' . $addon_id . ' .postbody{color:' . $color1615443385649 . ';}
                    ' . $addon_id . ' .postContent {
                        margin-bottom: 40px;
                        padding-top: 40px;
                        max-width: 1200px;
                        margin:0 auto;
                    }
                    ' . $addon_id . ' .postbody p{margin:0;}
                    ' . $addon_id . ' .team.mlistpost .listWrap {
                        display: none;
                    }
                    ' . $addon_id . ' #postWrapper::after {
                        content: "";
                        display: block;
                        clear: both;
                    }
                    ' . $addon_id . ' .postInfo .title {
                        color: ' . $color1615452944411 . ';
                        font-size: ' . $font_size . 'px;
                        transition: all .3s ease-out 0s;
                    ';
                    if($title_bold)
                    {
                        $output.='font-weight: bold;';
                    }
                    $output.='
                        text-align:' . $select1615452416171 . ';
                    }
                    ' . $addon_id . ' .postInfo .subtitle {
                        color: ' . $intro_color06 . ';
                        font-size: ' . $intro_fontsize06 . 'px;
                        margin-top: 4px;
                        transition: all .3s ease-out 0s;
                        padding-top: 4px;
                        text-align:' . $select1615452416171 . ';
                    }
                    ' . $addon_id . ' .postInfo .subtitle *{
                        font-size: ' . $intro_fontsize06 . 'px'.($force_intro_font_size_type6 ? '!important' : '').';
                    }
                    ' . $addon_id . ' .team .postInfo .usetdate{
                        display: none;
                    }
                    ' . $addon_id . ' .postInfo .usetdate {
                        font-size: 13px;
                        line-height: 24px;
                    }
                    ' . $addon_id . ' .postInfo .description {
                        margin-top: 20px;
                        font-size: 13px;
                        line-height: 24px;
                        margin-bottom: 24px;
                        color: #888;
                    }
                    ' . $addon_id . ' .post .postbody {
                        position: relative;
                        z-index: 0;
                    }
                    ' . $addon_id . ' .postbody hr {
                        margin: 20px 0;
                        border: none;
                        border-top: 1px dotted rgba(170, 170, 170, 0.2);
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev {
                        left: 0px;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next {
                        width: 42px;
                        height: 100px;
                        position: fixed;
                        z-index: 20;
                        top: 50%;
                        transform: translateY(-50%);
                        transition: opacity 0.36s ease-out, visibility 0.36s ease-out;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap {
                        border-color: ' . $pageBgColorhover . ';
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap {
                        border-left: 0;
                        left: 100%;
                    }

                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap,' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap {
                        width: 170px;
                        opacity: 0;
                        visibility: hidden;
                    }
                    ' . $addon_id . ' .item_tags > a, .container_category > a, .imagelink .owl-nav .owl-prev, .postSlider .owl-nav .owl-prev, .mlist .owl-nav .owl-prev, .postSlider .owl-nav .owl-next, .mlist .owl-nav .owl-next,.postSlider .owl-nav .owl-prev:hover .iconfont, .mlist .owl-nav .owl-prev:hover .iconfont, .imagelink .owl-nav .owl-next:hover .iconfont, .postSlider .owl-nav .owl-next:hover .iconfont, .mlist .owl-nav .owl-next:hover .iconfont,.team .content_list .item_block .item_box .item_wrapper, .news.mlistpost .content_list .item_block .item_box .item_wrapper,.team.mlistpost .tabBtn .post-prev .img-wrap, .team.mlistpost .tabBtn .post-next .img-wrap,.team.mlistpost .tabBtn .post-prev .infor-wrap,.team.mlistpost .tabBtn .post-next .infor-wrap, .postSlider .tab_button .item_img img, body #header, body #headTop #logo img, .imagelink .content_list .item_block .item_box {
                            transition: all 0.36s ease;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap {
                        padding-left: 20px;
                        left: 100%;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap {
                        position: absolute;
                        top: 50%;
                        z-index: 10;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap div,' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap div {
                        padding-top: 70px;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap div,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap div {
                        height: 100%;
                        background-position: center center;
                        background-size: cover;
                        box-sizing:unset!important;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap img,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap img {
                        display: none;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap:after, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap:after {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap .title,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap .title {
                        width: 70%;
                        display: block;
                        font-size: 16px;
                        line-height: 20px;
                        color: #fff;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap .title, ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap .title, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        width: 80%;
                        display: block;
                        line-height: 18px;
                        font-size: 14px;
                        color: #fff;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .details {
                        left: 0;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .details,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .details {
                        position: absolute;
                        height: 100%;
                        width: 42px;
                        background: ' . $pageBgColor . ';
                        border: 1px solid ' . $pageBorderColor . ';
                        box-sizing: border-box;
                        transition: all 0.3s ease-out 0s;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .details:before {
                        content: "\f104";
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .details:before,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .details:before {

                        font-family: "Font Awesome 5 Free";
                        font-variant: normal;
                        text-transform: none;
                        font-size: 14px;
                        line-height: 1;
                        color: inherit;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        font-size: 24px;
                        color: ' . $pageColor . ';
                        transform: translate(-50%, -50%);
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next {
                        right: 0px;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev,' . $addon_id . ' .team.mlistpost .tabBtn .post-next {
                        width: 42px;
                        height: 100px;
                        position: fixed;
                        z-index: 20;
                        top: 50%;
                        transform: translateY(-50%);
                        transition: opacity 0.36s ease-out, visibility 0.36s ease-out;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap {
                        right: 100%;
                        border-right: 0;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap {
                        position: absolute;
                        box-sizing: border-box;
                        z-index: 10;
                        height: 100%;
                        border: 3px solid ' . $pageBorderColorhover . ';
                    }

                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .img-wrap:after, ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .img-wrap:after {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap {
                        right: 100%;
                        padding-left: 20px;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev .infor-wrap,' . $addon_id . ' .team.mlistpost .tabBtn .post-next .infor-wrap {
                        position: absolute;
                        top: 50%;
                        z-index: 10;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .details {
                        right: 0;
                    }
                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next .details:before {
                        content: "\f105";
                    }

                    ' . $addon_id . ' .team.mlistpost .post-prev:hover .infor-wrap, ' . $addon_id . ' .team.mlistpost .post-prev:hover .img-wrap,' . $addon_id . ' .team.mlistpost .post-next:hover .infor-wrap,' . $addon_id . ' .team.mlistpost .post-next:hover .img-wrap {
                        opacity: 1;
                        visibility: visible;
                    }

                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-next:hover .details, ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev:hover .details {
                        border-color: #909744;
                        background-color: #909744;
                    }

                    ' . $addon_id . ' .team.mlistpost .tabBtn .post-prev:hover .details:before,' . $addon_id . ' .team.mlistpost .tabBtn .post-next:hover .details:before {
                        color:' . $pageColorhover . ';
                    }
                    ' . $addon_id . ' .xlsb{
                        font-size: '.$footer_label_font_size.'px;
                        color: '.$footer_label_color.';
                    }

                    @media (max-width: 991px){
                        ' . $addon_id . ' .timg {
                            width:100%;
                        }
                        ' . $addon_id . ' .sytext {
                            width:100%;
                        }
                        ' . $addon_id . ' .conTabBtn{display:none;}
                        ' . $addon_id . ' .xlsb{font-size: '.$footer_label_font_size_sm.'px;}
                    }
                    @media (max-width: 767px) {
                        ' . $addon_id . ' .xlsb{font-size: '.$footer_label_font_size_xs.'px;}
                    }
                </style>
            ';
            $output .= '
                <div class="content">
                    <div class="mlistpost team module" data-thumb="">
                        <div class="module_container">
                            <div class="container_content">
                                <div class="content_wrapper">
                                    <div id="postWrapper">
                                        <div class="postContent">
                                            <div class="postInfo">
                                                <div class="mainInfor">
                                                    <p class="title">' . $article->title . '</p>
                                                    <div class="subtitle">' . $article->introtext . '</div>
                                                </div>
                                            </div>
                                            <div class="postbody">
                                                <hr>

                                                <div class="firstRow">
                                                    <div class="timg fl" >
                                                        <p><img src="' . $article->image_intro . '"></p><p><br></p>
                                                        <p><strong><span style="font-size: '.$footer_title_font_size.'px; color: '.$footer_title_color.'">' . $article->title . '</span></strong></p>
                                                        <p class="xlsb">' . $article->label1 . '</p><p><br></p>
                                                    </div>
                                                    <div class="sytext fr">
                                                        ' . $article->fulltext . '
                                                    </div>
                                                    <div class="clear"></div>
                                                </div>

                                            </div>';
            if ($page_button == 0) {
                $output .= '<div class="conTabBtn tabBtn">';

                if ($article->on) {
                    $article_on = JwPageFactoryBase::getGoodsById($article->on, $catid_id);
                    $img        = json_decode($article_on->images);

                    $output .= '

                                                            <a href="' . $on . '" class="post-prev">
                                                                <div class="img-wrap">
                                                                    <div style="background-image: url(' . $img->image_intro . ')"></div>
                                                                    <img src="' . $img->image_intro . '" alt="">
                                                                </div>
                                                                <div class="infor-wrap">
                                                                    <span class="title">' . $article_on->title . '</span>
                                                                    <span class="subtitle">' . $article_on->introtext . '</span>
                                                                </div>
                                                                <i class="details fa fa-angle-left"></i>
                                                                <div class="tabMask"></div>
                                                            </a>

                                                        ';
                } else {
                    $output .= '<a href="javascript:;" class="post-prev">
                                                                        <div class="img-wrap">
                                                                        </div>

                                                                        <div class="infor-wrap">
                                                                            <span class="title"></span>
                                                                        </div>
                                                                        <i class="fa fa-angle-left details"></i>
                                                                        <div class="tabMask"></div>
                                                                    </a>
                                                                ';
                }
                if ($article->down) {
                    $article_down = JwPageFactoryBase::getGoodsById($article->down, $catid_id);
                    $img          = json_decode($article_down->images);

                    $output .= '

                                                            <a href="' . $down . '" class="post-next">
                                                                <div class="img-wrap">
                                                                    <div style="background-image: url(' . $img->image_intro . ')"></div>
                                                                    <img src="' . $img->image_intro . '" alt="">
                                                                </div>
                                                                <div class="infor-wrap">
                                                                    <span class="title">' . $article_down->title . '</span>
                                                                    <span class="subtitle">' . $article_down->introtext . '</span>
                                                                </div>
                                                                <i class="details fa fa-angle-right"></i>
                                                                <div class="tabMask"></div>
                                                            </a>

                                                        ';
                } else {
                    $output .= '
                                                            <a href="javascript:;" class="post-next">
                                                                <div class="img-wrap">
                                                                </div>
                                                                <div class="infor-wrap">
                                                                    <span class="title"></span>
                                                                    <span class="subtitle"></span>
                                                                </div>
                                                                <i class="fa fa-angle-right details"></i>
                                                                <div class="tabMask"></div>
                                                            </a>
                                                        ';
                }

                $output .= '</div>';
            }

            $output .= '</div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            ';

        }
        if ($art_type_selector_proinfo == 'type7') {

            $addonId                  = '#jwpf-addon-' . $this->addon->id;
            $type7_theme_color        = (isset($settings->type7_theme_color) && $settings->type7_theme_color) ? $settings->type7_theme_color : '#909744';
            $type7_taobao_button      = (isset($settings->type7_taobao_button) && $settings->type7_taobao_button) ? $settings->type7_taobao_button : 'yes';
            $type7_taobao_button_url  = (isset($settings->type7_taobao_button_url) && $settings->type7_taobao_button_url) ? $settings->type7_taobao_button_url : 'https://www.taobao.com';
            $type7_weidian_button     = (isset($settings->type7_weidian_button) && $settings->type7_weidian_button) ? $settings->type7_weidian_button : 'yes';
            $type7_weidian_button_url = (isset($settings->type7_weidian_button_url) && $settings->type7_weidian_button_url) ? $settings->type7_weidian_button_url : 'https://www.weidian.com';
            $app                      = JFactory::getApplication();
            $input                    = $app->input;
            $article_id               = $input->get('detail');
            $detail_id                = base64_decode($input->get('id'));
            $article                  = JwPageFactoryBase::getType7GoodsById($article_id, $catid_id);
            $on                       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down                     = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $article2                 = JwPageFactoryBase::getType7GoodsById($article->on, $catid_id);

            $article3 = JwPageFactoryBase::getType7GoodsById($article->down, $catid_id);
            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            // 右侧宽度
            $type7_right_width = (isset($settings->type7_right_width) && $settings->type7_right_width) ? $settings->type7_right_width : '25';
            // 左侧封面图
            $img_width_type7 = (isset($settings->img_width_type7) && $settings->img_width_type7) ? $settings->img_width_type7 : '612';
            $img_height_type7 = (isset($settings->img_height_type7) && $settings->img_height_type7) ? $settings->img_height_type7 : '410';
            $img_padding_bottom_type7 = (isset($settings->img_padding_bottom_type7) && $settings->img_padding_bottom_type7) ? $settings->img_padding_bottom_type7 : '100';
            $img_margin_type7 = (isset($settings->img_margin_type7) && $settings->img_margin_type7) ? $settings->img_margin_type7 : '40px auto 0 auto';
            $img_fill_mode_type7 = (isset($settings->img_fill_mode_type7) && $settings->img_fill_mode_type7) ? $settings->img_fill_mode_type7 : 'auto';
            // 正文
            $content_font_size_cover_type07 = (isset($settings->content_font_size_cover_type07) && ($settings->content_font_size_cover_type07 || $settings -> content_font_size_cover_type07 == 0)) ? $settings->content_font_size_cover_type07 : '0';
            $content_font_size_type07 = (isset($settings->content_font_size_type07) && $settings->content_font_size_type07) ? $settings->content_font_size_type07 : '12';
            $content_font_color_type07 = (isset($settings->content_font_color_type07) && $settings->content_font_color_type07) ? $settings->content_font_color_type07 : '#666';
            $content_line_height_type07 = (isset($settings->content_line_height_type07) && $settings->content_line_height_type07) ? $settings->content_line_height_type07 : '24';
            $content_padding_top_type07 = (isset($settings->content_padding_top_type07) && $settings->content_padding_top_type07) ? $settings->content_padding_top_type07 : '10';
            $content_margin_type07 = (isset($settings->content_margin_type07) && $settings->content_margin_type07) ? $settings->content_margin_type07 : '0 4% 0 4%';
            // 左侧宽度
            $type7_left_width = (isset($settings->type7_left_width) && $settings-> type7_left_width) ? $settings->type7_left_width : '66';
            // 标题
            $title_font_size_type07 = (isset($settings->title_font_size_type07) && $settings->title_font_size_type07) ? $settings->title_font_size_type07 : '16';
            $title_font_color_type07 = (isset($settings->title_font_color_type07) && $settings->title_font_color_type07) ? $settings->title_font_color_type07 : '#444';
            $title_line_height_type07 = (isset($settings->title_line_height_type07) && $settings->title_line_height_type07) ? $settings->title_line_height_type07 : '20';
            // 标签一
            $label1_font_size_type07 = (isset($settings->label1_font_size_type07) && $settings->label1_font_size_type07) ? $settings->label1_font_size_type07 : '12';
            $label1_font_color_type07 = (isset($settings->label1_font_color_type07) && $settings->label1_font_color_type07) ? $settings->label1_font_color_type07 : '#999';
            $label1_line_height_type07 = (isset($settings->label1_line_height_type07) && $settings->label1_line_height_type07) ? $settings->label1_line_height_type07 : '14';
            $label1_margin_top_type07 = (isset($settings->label1_margin_top_type07) && $settings->label1_margin_top_type07) ? $settings->label1_margin_top_type07 : '4';
            // 时间
            $time_font_size_type07 = (isset($settings->time_font_size_type07) && $settings->time_font_size_type07) ? $settings->time_font_size_type07 : '13';
            $time_font_color_type07 = (isset($settings->time_font_color_type07) && $settings->time_font_color_type07) ? $settings->time_font_color_type07 : '#666';
            $time_line_height_type07 = (isset($settings->time_line_height_type07) && $settings->time_line_height_type07) ? $settings->time_line_height_type07 : '24';
            $time_margin_top_type07 = (isset($settings->time_margin_top_type07) && $settings->time_margin_top_type07) ? $settings->time_margin_top_type07 : '0';
            // 简介
            $des_font_size_cover_type07 = (isset($settings->des_font_size_cover_type07) && ($settings->des_font_size_cover_type07 || $settings -> des_font_size_cover_type07 == 0)) ? $settings->des_font_size_cover_type07 : '0';
            $des_font_size_type07 = (isset($settings->des_font_size_type07) && $settings->des_font_size_type07) ? $settings->des_font_size_type07 : '13';
            $desc_font_color_type07 = (isset($settings->desc_font_color_type07) && $settings->desc_font_color_type07) ? $settings->desc_font_color_type07 : '#888';
            $desc_line_height_type07 = (isset($settings->desc_line_height_type07) && $settings->desc_line_height_type07) ? $settings->desc_line_height_type07 : '24';
            $box_margin_type07 = (isset($settings->box_margin_type07) && $settings->box_margin_type07) ? $settings->box_margin_type07 : '20px 0 24px 0';
            // 导航
            $type7_nav_padding = (isset($settings->type7_nav_padding) && $settings->type7_nav_padding) ? $settings->type7_nav_padding : '20px 20px 20px 20px';
            $nav_font_size_type07 = (isset($settings->nav_font_size_type07) && $settings->nav_font_size_type07) ? $settings->nav_font_size_type07 : '12';
            $nav_font_color_type07 = (isset($settings->nav_font_color_type07) && $settings->nav_font_color_type07) ? $settings->nav_font_color_type07 : '#666';
            // 店铺按钮
            $button_item_type7 = (isset($settings->button_item_type7) && $settings->button_item_type7) ? $settings->button_item_type7 : array();
            $open_way_type7 = (isset($settings->open_way_type7) && $settings->open_way_type7) ? $settings->open_way_type7 : '_blank';
            $button_height_type7 = (isset($settings->button_height_type7) && $settings->button_height_type7) ? $settings->button_height_type7 : '40';
            $button_padding_type7 = (isset($settings->button_padding_type7) && $settings->button_padding_type7) ? $settings->button_padding_type7 : '0 30px 0 30px';
            $button_border_color_type7 = (isset($settings->button_border_color_type7) && $settings->button_border_color_type7) ? $settings->button_border_color_type7 : '#dbdbdb';
            $button_bg_color_type7 = (isset($settings->button_bg_color_type7) && $settings->button_bg_color_type7) ? $settings->button_bg_color_type7 : '#fff';
            $button_color_type7 = (isset($settings->button_color_type7) && $settings->button_color_type7) ? $settings->button_color_type7 : '#555';
            $button_margin_top_type7 = (isset($settings->button_margin_top_type7) && $settings->button_margin_top_type7) ? $settings->button_margin_top_type7 : '20';
            // 相关内容
            $about_size_type7 = (isset($settings->about_size_type7) && $settings->about_size_type7) ? $settings->about_size_type7 : '14';
            $about_color_type7 = (isset($settings->about_color_type7) && $settings->about_color_type7) ? $settings->about_color_type7 : '#666';
            $about_padding_type7 = (isset($settings->about_padding_type7) && $settings->about_padding_type7) ? $settings->about_padding_type7 : '30px 0 30px 0';
            $button_about_height_type7 = (isset($settings->button_about_height_type7) && $settings->button_about_height_type7) ? $settings->button_about_height_type7 : '26';
            $button_about_border_color_type7 = (isset($settings->button_about_border_color_type7) && $settings->button_about_border_color_type7) ? $settings->button_about_border_color_type7 : 'rgba(170,170,170,0.2)';
            $button_about_bg_color_type7 = (isset($settings->button_about_bg_color_type7) && $settings->button_about_bg_color_type7) ? $settings->button_about_bg_color_type7 : '#fff';
            $button_about_color_type7 = (isset($settings->button_about_color_type7) && $settings->button_about_color_type7) ? $settings->button_about_color_type7 : '#666';
            $button_about_margin_right_type7 = (isset($settings->button_about_margin_right_type7) && $settings->button_about_margin_right_type7) ? $settings->button_about_margin_right_type7 : '10';
            $button_about_padding_type7 = (isset($settings->button_about_padding_type7) && $settings->button_about_padding_type7) ? $settings->button_about_padding_type7 : '0 14px 0 14px';
            $button_about_margin_bottom_type7 = (isset($settings->button_about_margin_bottom_type7) && $settings->button_about_margin_bottom_type7) ? $settings->button_about_margin_bottom_type7 : '30';
            $about_list_col_type7 = (isset($settings->about_list_col_type7) && $settings->about_list_col_type7) ? $settings->about_list_col_type7 : '2';
            $about_list_col_margin_type7 = (isset($settings->about_list_col_margin_type7) && $settings->about_list_col_margin_type7) ? $settings->about_list_col_margin_type7 : '0 10px 20px 0';
            $btn_margin = explode(" ", $about_list_col_margin_type7);
            $btn_top = $btn_margin[0] ?? 0;
            $btn_right = $btn_margin[1] ?? 0;
            $btn_bottom = $btn_margin[2] ?? 0;
            $btn_left = $btn_margin[3] ?? 0;
            $about_list_col_border_color_type7 = (isset($settings->about_list_col_border_color_type7) && $settings->about_list_col_border_color_type7) ? $settings->about_list_col_border_color_type7 : 'rgba(170,170,170,0.2)';
            $about_list_title_padding_type7 = (isset($settings->about_list_title_padding_type7) && $settings->about_list_title_padding_type7) ? $settings->about_list_title_padding_type7 : '20px 5px 20px 5px';
            $about_list_title_color_type7 = (isset($settings->about_list_title_color_type7) && $settings->about_list_title_color_type7) ? $settings->about_list_title_color_type7 : '#666';
            $about_list_title_size_type7 = (isset($settings->about_list_title_size_type7) && $settings->about_list_title_size_type7) ? $settings->about_list_title_size_type7 : '12';
            // 小屏配置
            $type7_small_screen_box_margin = (isset($settings->type7_small_screen_box_margin) && $settings->type7_small_screen_box_margin) ? $settings->type7_small_screen_box_margin : '20px 2% 0 4%';
            $box_margin = explode(' ', $type7_small_screen_box_margin);
            $box_top = $box_margin[0] ?? 0;
            $box_right = $box_margin[1] ?? 0;
            $box_bottom = $box_margin[2] ?? 0;
            $box_left = $box_margin[3] ?? 0;
            // 标题
            $type7_small_screen_title_size = (isset($settings->type7_small_screen_title_size) && $settings->type7_small_screen_title_size) ? $settings->type7_small_screen_title_size : '16';
            $type7_small_screen_title_line_height = (isset($settings->type7_small_screen_title_line_height) && $settings->type7_small_screen_title_line_height) ? $settings->type7_small_screen_title_line_height : '20';
            $type7_small_screen_title_margin_top = (isset($settings->type7_small_screen_title_margin_top) && $settings->type7_small_screen_title_margin_top) ? $settings->type7_small_screen_title_margin_top : '48';
            $type7_small_screen_title_color = (isset($settings->type7_small_screen_title_color) && $settings->type7_small_screen_title_color) ? $settings->type7_small_screen_title_color : '#444';
            $about_list_subtitle_size_type7 = (isset($settings->about_list_subtitle_size_type7) && $settings->about_list_subtitle_size_type7) ? $settings->about_list_subtitle_size_type7 : '12';
            $about_list_subtitle_color_type7 = (isset($settings->about_list_subtitle_color_type7) && $settings->about_list_subtitle_color_type7) ? $settings->about_list_subtitle_color_type7 : '#666';
            $about_list_subtitle_margin_top_type7 = (isset($settings->about_list_subtitle_margin_top_type7) && $settings->about_list_subtitle_margin_top_type7) ? $settings->about_list_subtitle_margin_top_type7 : '';
            // 标签一
            $type7_small_screen_label1_size = (isset($settings->type7_small_screen_label1_size) && $settings->type7_small_screen_label1_size) ? $settings->type7_small_screen_label1_size : '12';
            $type7_small_screen_label1_color = (isset($settings->type7_small_screen_label1_color) && $settings->type7_small_screen_label1_color) ? $settings->type7_small_screen_label1_color : '#999';
            $type7_small_screen_label1_line_height = (isset($settings->type7_small_screen_label1_line_height) && $settings->type7_small_screen_label1_line_height) ? $settings->type7_small_screen_label1_line_height : '14';
            $type7_small_screen_label1_margin_top = (isset($settings->type7_small_screen_label1_margin_top) && $settings->type7_small_screen_label1_margin_top) ? $settings->type7_small_screen_label1_margin_top : '4';
            // 简介
            $type7_small_screen_des_font_size_cover = (isset($settings->type7_small_screen_des_font_size_cover) && ($settings->type7_small_screen_des_font_size_cover || $settings->type7_small_screen_des_font_size_cover == 0)) ? $settings->type7_small_screen_des_font_size_cover : '0';
            $type7_small_screen_des_font_size = (isset($settings->type7_small_screen_des_font_size) && $settings->type7_small_screen_des_font_size) ? $settings->type7_small_screen_des_font_size : '14';
            $type7_small_screen_desc_line_height = (isset($settings->type7_small_screen_desc_line_height) && $settings->type7_small_screen_desc_line_height) ? $settings->type7_small_screen_desc_line_height : '';
            $type7_small_screen_des_color = (isset($settings->type7_small_screen_des_color) && $settings->type7_small_screen_des_color) ? $settings->type7_small_screen_des_color : '#666';
            $type7_small_screen_desc_margin_top = (isset($settings->type7_small_screen_desc_margin_top) && $settings->type7_small_screen_desc_margin_top) ? $settings->type7_small_screen_desc_margin_top : '20';
            // 店铺跳转链接
            $type7_small_screen_button_height = (isset($settings->type7_small_screen_button_height) && $settings->type7_small_screen_button_height) ? $settings->type7_small_screen_button_height : '48';
            $type7_small_screen_button_padding = (isset($settings->type7_small_screen_button_padding) && $settings->type7_small_screen_button_padding) ? $settings->type7_small_screen_button_padding : '0 32px 0 32px';
            $type7_small_screen_button_border_color = (isset($settings->type7_small_screen_button_border_color) && $settings->type7_small_screen_button_border_color) ? $settings->type7_small_screen_button_border_color : '#d6d6d6';
            $type7_small_screen_button_bg_color = (isset($settings->type7_small_screen_button_bg_color) && $settings->type7_small_screen_button_bg_color) ? $settings->type7_small_screen_button_bg_color : '#fff';
            $type7_small_screen_button_color = (isset($settings->type7_small_screen_button_color) && $settings->type7_small_screen_button_color) ? $settings->type7_small_screen_button_color : '#666';
            $type7_small_screen_button_margin = (isset($settings->type7_small_screen_button_margin) && $settings->type7_small_screen_button_margin) ? $settings->type7_small_screen_button_margin : '16px 0 16px 0';
            // 正文
            $type7_small_screen_content_font_size = (isset($settings->type7_small_screen_content_font_size) && $settings->type7_small_screen_content_font_size) ? $settings->type7_small_screen_content_font_size : '13';
            $type7_small_screen_content_font_size_cover = (isset($settings->type7_small_screen_content_font_size_cover) && ($settings->type7_small_screen_content_font_size_cover || $settings->type7_small_screen_content_font_size_cover == 0)) ? $settings->type7_small_screen_content_font_size_cover : '0';
            $type7_small_screen_content_color = (isset($settings->type7_small_screen_content_color) && $settings->type7_small_screen_content_color) ? $settings->type7_small_screen_content_color : '#666';
            $type7_small_screen_content_line_height = (isset($settings->type7_small_screen_content_line_height) && $settings->type7_small_screen_content_line_height) ? $settings->type7_small_screen_content_line_height : '';
            // 相关内容
            $type7_small_screen_about_size = (isset($settings->type7_small_screen_about_size) && $settings->type7_small_screen_about_size) ? $settings->type7_small_screen_about_size : '14';
            $type7_small_screen_about_margin_top = (isset($settings->type7_small_screen_about_margin_top) && $settings->type7_small_screen_about_margin_top) ? $settings->type7_small_screen_about_margin_top : '20';
            $type7_small_screen_button_about_margin = (isset($settings->type7_small_screen_button_about_margin) && $settings->type7_small_screen_button_about_margin) ? $settings->type7_small_screen_button_about_margin : '32px 3px 32px 3px';
            $type7_small_screen_about_color = (isset($settings->type7_small_screen_about_color) && $settings->type7_small_screen_about_color) ? $settings->type7_small_screen_about_color : '#444';
            // 分类标签
            $type7_small_screen_button_about_border_color = (isset($settings->type7_small_screen_button_about_border_color) && $settings->type7_small_screen_button_about_border_color) ? $settings->type7_small_screen_button_about_border_color : '#f0f0f0';
            $type7_small_screen_button_about_bg_color = (isset($settings->type7_small_screen_button_about_bg_color) && $settings->type7_small_screen_button_about_bg_color) ? $settings->type7_small_screen_button_about_bg_color : '#f0f0f0';
            $type7_small_screen_button_about_color = (isset($settings->type7_small_screen_button_about_color) && $settings->type7_small_screen_button_about_color) ? $settings->type7_small_screen_button_about_color : '#666';
            $type7_small_screen_button_about_padding = (isset($settings->type7_small_screen_button_about_padding) && $settings->type7_small_screen_button_about_padding) ? $settings->type7_small_screen_button_about_padding : '4px 12px 4px 12px';
            // 列表
            $type7_small_screen_about_list_col = (isset($settings->type7_small_screen_about_list_col) && $settings->type7_small_screen_about_list_col) ? $settings->type7_small_screen_about_list_col : '2';
            $type7_small_screen_about_list_col_margin = (isset($settings->type7_small_screen_about_list_col_margin) && $settings->type7_small_screen_about_list_col_margin) ? $settings->type7_small_screen_about_list_col_margin : '1% 1% 1% 1%';
            $list_margin = explode(" ", $type7_small_screen_about_list_col_margin);
            $list_top = $list_margin[0] ?? 0;
            $list_right = $list_margin[1] ?? 0;
            $list_bottom = $list_margin[2] ?? 0;
            $list_left = $list_margin[3] ?? 0;
            $type7_small_screen_about_list_col_border_color = (isset($settings->type7_small_screen_about_list_col_border_color) && $settings->type7_small_screen_about_list_col_border_color) ? $settings->type7_small_screen_about_list_col_border_color : 'rgba(170,170,170,0.2)';
            // 标题
            $type7_small_screen_about_list_title_padding = (isset($settings->type7_small_screen_about_list_title_padding) && $settings->type7_small_screen_about_list_title_padding) ? $settings->type7_small_screen_about_list_title_padding : '20px 20px 20px 20px';
            $type7_small_screen_about_list_title_color = (isset($settings->type7_small_screen_about_list_title_color) && $settings->type7_small_screen_about_list_title_color) ? $settings->type7_small_screen_about_list_title_color : '#666';
            $type7_small_screen_about_list_title_size = (isset($settings->type7_small_screen_about_list_title_size) && $settings->type7_small_screen_about_list_title_size) ? $settings->type7_small_screen_about_list_title_size : '12';
            // 副标题
            $type7_small_screen_about_list_subtitle_size = (isset($settings->type7_small_screen_about_list_subtitle_size) && $settings->type7_small_screen_about_list_subtitle_size) ? $settings->type7_small_screen_about_list_subtitle_size : '12';
            $type7_small_screen_about_list_subtitle_color = (isset($settings->type7_small_screen_about_list_subtitle_color) && $settings->type7_small_screen_about_list_subtitle_color) ? $settings->type7_small_screen_about_list_subtitle_color : '#666';
            $type7_small_screen_about_list_subtitle_margin_top = (isset($settings->type7_small_screen_about_list_subtitle_margin_top) && $settings->type7_small_screen_about_list_subtitle_margin_top) ? $settings->type7_small_screen_about_list_subtitle_margin_top : '';

            $output .= ' <style>';
            $output .= $addonId . ' html,body{font:12px Arial,"微软雅黑";color:#666;width:100%;height:100%;-webkit-font-smoothing:antialiased;-webkit-text-size-adjust:none;-webkit-tap-highlight-color:transparent;-webkit-overflow-scrolling:touch;overflow-scrolling:touch}';
            $output .= $addonId . ' .ff_pageTarget .container_target a:last-child{color:' . $type7_theme_color . ';text-decoration:none}';
            $output .= $addonId . ' .ff_pageTarget .fa-angle-right{padding:0 10px}';
            $output .= $addonId . ' .fa-angle-right:before{content:">"}';

            $output .= $addonId . ' body{overflow-x:hidden}';
            $output .= $addonId . ' .postbody p img{display:inline-block;}';
            $output .= $addonId . ' a{color:#666;text-decoration:none}';
            $output .= $addonId . ' .container_target a{color:'.$nav_font_color_type07.';text-decoration:none}';
            $output .= $addonId . ' a.active{color:#333}';
            $output .= $addonId . ' img{max-width:100%}';
            $output .= $addonId . ' .clear{clear:both}';
            $output .= $addonId . ' .sitecontent #pageTarget.module .module_container{position:relative;padding:'.$type7_nav_padding.';font-size: '.$nav_font_size_type07.'px;color: '.$nav_font_color_type07.'}';
            $output .= $addonId . ' .sitecontent #pageTarget.module .module_container:before{content:"";border-bottom:1px solid #f2f2f2;width:100%;height:1px;position:absolute;margin:0 auto;left:0;top:54px}';
            $output .= $addonId . ' .sitecontent #postLeft{width:'.$type7_left_width.'%;float:left}';
            $output .= $addonId . ' .sitecontent #postLeft #postSlider{width:100%;height:'.($img_height_type7 + $img_padding_bottom_type7).'px;}';
            $output .= $addonId . ' *{margin:0;padding:0;list-style-type:none}';
            if($img_fill_mode_type7 === "auto"){
                $output .= $addonId . ' #owl-demo img{border:0 none;width:100%;height:auto}';
            }else{
                $output .= $addonId . ' #owl-demo img{border:0 none;width:100%;height:100%;object-fit: '.$img_fill_mode_type7.'}';
            }
            $output .= $addonId . ' #owl-demo{position:relative;width:'.$img_width_type7.'px;height:'.$img_height_type7.'px;padding-bottom:'.$img_padding_bottom_type7.'px;box-sizing: content-box;max-width: 100%; margin: '.$img_margin_type7.';}';
            $output .= $addonId . ' #owl-demo .li1{width:'.$img_width_type7.'px;height:'.$img_height_type7.'px;}';
            $output .= $addonId . ' #owl-demo .li1 .txt{bottom:0}';
            $output .= $addonId . ' #owl-demo .li1 h3{padding:0 25px;font-size:28px}';
            $output .= $addonId . ' #owl-demo p{margin-top:4px;padding:0 25px 5px}';
            $output .= $addonId . ' #owl-demo a{color:#fff;text-decoration:none}';
            $output .= $addonId . ' #owl-demo li:hover .txt{bottom:0}';
            $output .= $addonId . ' .owl-pagination{position:absolute;left:0;bottom:0;width:100%;height:80px;text-align:center}';
            $output .= $addonId . ' .owl-page{position:relative;display:inline-block;width:100px;height:60px;margin:0 5px;vertical-align:middle;overflow:hidden}';
            $output .= $addonId . ' .owl-page img{width:100%;height:100%}';
            $output .= $addonId . ' .owl-pagination .active{width:100px;height:60px}';
            $output .= $addonId . ' .owl-pagination span{position:absolute;left:0;top:0;width:100px;height:60px;_background-image:none}';
            $output .= $addonId . ' .owl-pagination .active span:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;border:1px solid ' . $type7_theme_color . ';box-sizing:border-box}';
            $output .= $addonId . ' .owl-buttons div{position:absolute;top:50%;width:50px;height:50px;margin-top:-90px;text-indent:19px;background-color:#CCCCCC;transition:background-position 0.2s ease-out 0s;color:#FFFFFF;line-height:50px;font-size:20px;font-weight:600}';
            $output .= $addonId . ' .owl-prev{left:-60px;background-position:0 0}';
            $output .= $addonId . ' .owl-next{right:-60px;background-position:right 0}';
            $output .= $addonId . ' .owl-prev:hover{background-position:-53px 0}';
            $output .= $addonId . ' .owl-next:hover{background-position:-122px 0}';
            $output .= $addonId . ' .sitecontent #postLeft .postbody{width:100%;padding-top:'.$content_padding_top_type07.'px;position:relative;z-index:0;margin:'.$content_margin_type07.';color: '.$content_font_color_type07.';line-height: '.$content_line_height_type07.'px}';
            if($content_font_size_cover_type07){
                $output .= $addonId . ' .sitecontent #postLeft .postbody *{font-size: '.$content_font_size_type07.'px!important;}';
            }
            $output .= $addonId . ' .sitecontent #postRight{width:'.$type7_right_width.'%;float:right}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo{padding-top:40px}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .title{color:'.$title_font_color_type07.';font-size:'.$title_font_size_type07.'px;transition:all .3s ease-out 0s;line-height:'.$title_line_height_type07.'px;';
            if($title_bold)
            {
                $output .= 'font-weight: bold;';
            }
            $output .= '}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .subtitle{color:'.$title_line_height_type07.';font-size:'.$label1_font_size_type07.'px;margin-top:'.$label1_margin_top_type07.'px;transition:all .3s ease-out 0s;line-height:'.$label1_line_height_type07.'px}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .description{margin:'.$box_margin_type07.';font-size:'.$des_font_size_type07.'px'.($des_font_size_cover_type07 ? '!important' : '').';line-height:'.$desc_line_height_type07.'px;color:'.$desc_font_color_type07.'}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .usetdate{font-size:'.$time_font_size_type07.'px;line-height:'.$time_line_height_type07.'px; color: '.$time_font_color_type07.'; margin-top: '.$time_margin_top_type07.'}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .description a{transition:all 0.6s cubic-bezier(0.215,0.61,0.355,1) 0s;border:1px solid '.$button_border_color_type7.';background-color:'.$button_bg_color_type7.';color:'.$button_color_type7.';height:'.$button_height_type7.'px;padding:'.$button_padding_type7.';line-height:'.$button_height_type7.'px;text-align:center;display:inline-block;margin-top:'.$button_margin_top_type7.'px;}';
            $output .= $addonId . ' .sitecontent #postRight .postInfo .description a:hover{background-color:' . $type7_theme_color . ';border-color:' . $type7_theme_color . ';color:#fff}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post h3{font-size:'.$about_size_type7.'px;padding:'.$about_padding_type7.';font-weight: bold;color: '.$about_color_type7.'}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post > .item_tags{clear:both;height:auto;padding:0;margin-bottom:'.$button_about_margin_bottom_type7.'px;border-top:none}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post > .item_tags::after{clear:both;display: block;content: ""}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post > .item_tags > a{float:left;margin-right:'.$button_about_margin_right_type7.'px;padding:'.$button_about_padding_type7.';line-height:'.$button_about_height_type7.'px;border:1px solid '.$button_about_border_color_type7.';background: '.$button_about_bg_color_type7.';color: '.$button_about_color_type7.';}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post > .item_tags>a.active,.sitecontent #postRight .listContent_post > .item_tags>a:hover{border-color:' . $type7_theme_color . ';background-color:' . $type7_theme_color . ';color:#fff}';
            $output .= $addonId . ' .sitecontent #postRight .listContent_post .item_block{transition:all 0.36s ease;width:calc(100% / '.$about_list_col_type7.' - '.$btn_right.' - '.$btn_left.');height:auto;float:left;margin:'.$about_list_col_margin_type7.';position:relative;box-shadow:0 0 0px rgba(0,0,0,0.15)}';
            $output .= $addonId . ' .sitecontent #postRight .mlist:not(.imagelink) .content_list .item_block:hover .item_box .item_img img{transform:scale(1.08);}';
            $output .= $addonId . ' .item_img{overflow:hidden;}';
            $output .= $addonId . ' .sitecontent #postRight .mlist:not(.imagelink) .content_list .item_block{border:1px solid '.$about_list_col_border_color_type7.';background-color:transparent;box-sizing:border-box;overflow: hidden}';
            $output .= $addonId . ' .sitecontent #postRight .content_list .item_block:hover .item_box .item_wrapper{background-color:' . $type7_theme_color . ';color:#FFFFFF}';
            $output .= $addonId . ' .sitecontent #postRight.content_list .item_block:hover .item_box .item_wrapper{background:' . $type7_theme_color . ';}';
            $output .= $addonId . ' .sitecontent #postRight .content_list .item_block .item_box .item_wrapper{padding:'.$about_list_title_padding_type7.';color: '.$about_list_title_color_type7.';font-size: '.$about_list_title_size_type7.'px}';
            $output .= $addonId . ' .sitecontent #postRight .content_list .item_block .item_box .item_wrapper .subtitle{color: '.$about_list_subtitle_color_type7.';font-size: '.$about_list_subtitle_size_type7.'px;margin-top: '.$about_list_subtitle_margin_top_type7.'}';
            $output .= $addonId . ' #postWrapper{display:none}';
            $output .= $addonId . ' #postWrapper #postSlider{display:none}';
            $output .= ' @media only screen and (max-width:1920px) and (min-width:1600px){';
            $output .= ' .sitecontent{display:block}';
            $output .= $addonId . ' #postWrapper #postSlider{display:none}';
            $output .= $addonId . ' #postInfo{display:none}';
            $output .= $addonId . ' #postContent{display:none}';
            $output .= ' }';
            $output .= ' @media only screen and (max-width:1023px){';
            $output .= $addonId . ' .sitecontent{display:none}';
            $output .= $addonId . ' .sitecontent #postLeft .postbody{display:none}';
            $output .= $addonId . ' #postWrapper{display:block}';
            $output .= $addonId . ' #postWrapper #postSlider{display:block}';
            $output .= $addonId . ' #postInfo{display:block}';
            $output .= $addonId . ' #postContent{display:block}';
            $output .= $addonId . ' #postInfo,#postNav,#postContent{width:calc(100% - '.$box_left.' - '.$box_right.');margin:'.$type7_small_screen_box_margin.';position:relative;text-align:left}';
            $output .= $addonId . ' #postInfo .title{font-size:'.$type7_small_screen_title_size.'px;transition:all .3s ease-out 0s;line-height:'.$type7_small_screen_title_line_height.'px;margin-top: '.$type7_small_screen_title_margin_top.'px;color: '.$type7_small_screen_title_color.'}';
            $output .= $addonId . ' #postInfo .subtitle{color:'.$type7_small_screen_label1_color.';font-size:'.$type7_small_screen_label1_size.'px;margin-top:'.$type7_small_screen_label1_margin_top.'px;transition:all .3s ease-out 0s;line-height:'.$type7_small_screen_label1_line_height.'px;}';
            $output .= $addonId . ' #postInfo .description{margin-top:'.$type7_small_screen_desc_margin_top.'px;display:block;font-size:'.$type7_small_screen_des_font_size.'px'.($type7_small_screen_des_font_size_cover ? '!important' : '').';color: '.$type7_small_screen_des_color.';line-height: '.$type7_small_screen_desc_line_height.'px}';
            $output .= $addonId . ' #postInfo .description .subbtn{height:'.$type7_small_screen_button_height.'px;line-height:'.$type7_small_screen_button_height.'px;border:1px solid '.$type7_small_screen_button_border_color.';text-align:center;display:inline-block;color:'.$type7_small_screen_button_color.';background: '.$type7_small_screen_button_bg_color.';text-decoration:none;padding:'.$type7_small_screen_button_padding.';margin:'.$type7_small_screen_button_margin.'}';
            $output .= $addonId . ' #postInfo .description .subbtn:hover{background:' . $type7_theme_color . ';color:#FFFFFF}';
            $output .= $addonId . ' #postContent .postbody p img{width:100%;align-items:center}';
            $output .= $addonId . ' .bx-wrapper .bx-wrapper {margin: 0 auto;}';
            $output .= $addonId . ' .bx-wrapper .bx-viewport{box-shadow:none;border:0;left:0}';
            $output .= $addonId . ' .bx-wrapper .bx-pager.bx-default-pager a{background:#ddd;display:block;width:8px;height:8px;margin:0 5px;outline:0;border-radius:5px}';
            $output .= $addonId . ' .bx-wrapper .bx-pager.bx-default-pager a.active{border:#a5a5a5 1px solid;background-color:#a5a5a5}';
            $output .= $addonId . ' .bx-wrapper .bx-pager{padding:0}';
            $output .= $addonId . ' .postbody{position:relative;z-index:0;color: '.$type7_small_screen_content_color.';line-height: '.$type7_small_screen_content_line_height.'px}';
            $output .= $addonId . ' .postbody *{font-size: '.$type7_small_screen_content_font_size.'px'.($type7_small_screen_content_font_size_cover ? '!important' : '').';}';
            $output .= $addonId . ' #listContent h3{color:'.$type7_small_screen_about_color.';font-size:'.$type7_small_screen_about_size.'px;margin-top:'.$type7_small_screen_about_margin_top.'px}';
            $output .= $addonId . ' #postWrapper .item_tags{text-align:left;display:block}';
            $output .= $addonId . ' #postWrapper .item_tags a{display:inline-block;padding:'.$type7_small_screen_button_about_padding.';border:'.$type7_small_screen_button_about_border_color.' 1px solid;background-color:'.$type7_small_screen_button_about_bg_color.';margin:'.$type7_small_screen_button_about_margin.';color: '.$type7_small_screen_button_about_color.'}';
            $output .= $addonId . ' #postWrapper #listContent .mlist.project{height:280px}';
            $output .= $addonId . ' #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block:hover .item_box .item_img img{transform:scale(1.08)}';
            $output .= $addonId . ' #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block{border:1px solid '.$type7_small_screen_about_list_col_border_color.';background-color:transparent;box-sizing:border-box}';
            $output .= $addonId . ' #postWrapper #listContent  .mlist:not(.imagelink)  .content_list .item_block:hover .item_box .item_wrapper{background-color:' . $type7_theme_color . ';color:#fff}';
            $output .= $addonId . ' #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper{padding:'.$type7_small_screen_about_list_title_padding.'}';
            $output .= $addonId . ' #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper .text_wrap p{font-size:'.$type7_small_screen_about_list_title_size.'px;color: '.$type7_small_screen_about_list_title_color.'}';
            $output .= $addonId . ' #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper .text_wrap p.subtitle{font-size:'.$type7_small_screen_about_list_subtitle_size.'px;color: '.$type7_small_screen_about_list_subtitle_color.';margin-top: '.$type7_small_screen_about_list_subtitle_margin_top.'px}';
            $output .= $addonId . ' #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block{width:calc(100% / '.$type7_small_screen_about_list_col.' - '.$list_left.' - '.$list_right.');margin:'.$type7_small_screen_about_list_col_margin.';float:left;transition:all .2s ease-out;-webkit-transition:all .2s ease-out;position:relative;overflow:hidden}';
            $output .= ' }';
            $output .= $addonId . ' .sitecontent  .tabBtn .post-prev .details:before{content:"<";font-family:"FontAwesome";speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;font-size:14px;line-height:1;color:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;position:absolute;top:50%;left:50%;font-size:24px;color:#999;transform:translate(-50%,-50%)}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next .details:before{content:">";font-family:"FontAwesome";speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;font-size:14px;line-height:1;color:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;position:absolute;top:50%;left:50%;font-size:24px;color:#999;transform:translate(-50%,-50%)}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev{width:42px;height:100px;position:fixed;left:0px;z-index:20;top:50%;transform:translateY(-50%);transition:opacity 0.36s ease-out,visibility 0.36s ease-out}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next{width:42px;height:100px;position:fixed;right:0px;z-index:20;top:50%;transform:translateY(-50%);transition:opacity 0.36s ease-out,visibility 0.36s ease-out}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .details,' . $addonId . ' .sitecontent .tabBtn .post-next .details{position:absolute;right:0;height:100%;width:42px;background:#fff;border:1px solid #ececec;box-sizing:border-box;transition:all 0.3s ease-out 0s}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev:hover .details,' . $addonId . ' .sitecontent .tabBtn .post-next:hover .details{border-color:' . $type7_theme_color . ';background-color:' . $type7_theme_color . ';color:#fff}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .img-wrap,' . $addonId . ' .sitecontent .tabBtn .post-next .img-wrap{position:absolute;box-sizing:border-box;z-index:10;height:100%;border:3px solid ' . $type7_theme_color . ';width:170px;opacity:0;visibility:hidden;border-left:0;transition:all 0.36s ease}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev  .img-wrap{left:100%;border-right:0}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next .img-wrap{right:100%;border-right:0}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .img-wrap img,' . $addonId . ' .sitecontent .tabBtn .post-next .img-wrap img{display:none}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .img-wrap:after,' . $addonId . ' .sitecontent .tabBtn .post-next .img-wrap:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.4)}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .img-wrap div,' . $addonId . ' .sitecontent .tabBtn .post-next .img-wrap div{width:165px;height:100%;background-position:center center;background-size:cover}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev  .infor-wrap,' . $addonId . ' .sitecontent .tabBtn .post-next .infor-wrap{position:absolute;top:50%;z-index:10;transform:translateY(-50%);box-sizing:border-box;width:170px;opacity:0;visibility:hidden;padding-left:20px}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev  .infor-wrap{left:100%;border-right:0}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next .infor-wrap{right:100%;border-right:0}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev .title{width:80%;display:block;font-size:16px;line-height:20px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next .infor-wrap .title{width:80%;display:block;font-size:16px;line-height:20px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev  .infor-wrap .subtitle,' . $addonId . ' .sitecontent .tabBtn .post-next .infor-wrap .subtitle{width:80%;display:block;line-height:18px;font-size:14px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}';
            $output .= '.button-box{display: flex; justify-content: space-between; flex-wrap: wrap;}';
            $output .= ' @media screen and (min-width:1440px){';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev:hover .img-wrap,' . $addonId . ' .sitecontent .tabBtn .post-next:hover .img-wrap{opacity:1;visibility:visible}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev:hover .infor-wrap,' . $addonId . ' .sitecontent .tabBtn .post-next:hover .infor-wrap{opacity:1;visibility:visible}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-prev:hover .details,' . $addonId . ' .sitecontent .tabBtn  .post-next:hover .details{border-color:' . $type7_theme_color . ';background:' . $type7_theme_color . ';}';
            $output .= $addonId . ' .project.mlistpost .post-prev:hover .details:before,.team.mlistpost .post-prev:hover .details:before,.project.mlistpost .post-next:hover .details:before,.team.mlistpost .post-next:hover .details:before{color:#fff}';
            $output .= $addonId . ' .sitecontent  .tabBtn .post-prev:hover  .details:before{color:#fff}';
            $output .= $addonId . ' .sitecontent .tabBtn .post-next:hover  .details:before{color:#fff}';
            $output .= ' }';

            $output .= ' </style>';

            $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/css/owl.carousel.css" type="text/css" />';
            $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/css/jquery.bxslider.css" type="text/css" />';
            $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/jquery-1.8.3.min.js"></script>';
            $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/owl.carousel.js"></script>';
            $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/jquery.bxslider.min.js"></script>';
            $output .= ' <script type="text/javascript">
              $(document).ready(function(){
                setTimeout(function(){
                    $("' . $addonId . ' #owl-demo").owlCarousel({
                        items: 1,
                        nav: true,
                        navigation: true,
                        navigationText: ["<",">"],
                        autoPlay: false,
                        navRewind: true,
                        stopOnHover: true,
                        afterInit: function(){

                        }
                    });
                    var $t = $("' . $addonId . ' .owl-pagination span");
                    var arry=[';
                        if (!empty($article->many_img)) {
                            $articleimgs = explode(";", $article->many_img);
                            foreach ($articleimgs as $keys => $vals) {
                                $output .= ' \'<img src="' . $vals . '">\',';
                            }
                        } else {
                            $output .= ' \'<img src="' . $article->image_intro . '">\',';
                        }
                    $output .= '     ];
                    $t.each(function(k){
                        $(this).before(arry[k]);
                    });
                    $("' . $addonId . ' .slider").bxSlider({
                        auto:true,
                        slideWidth: 600,
                        controls:false
                    });
                }, 600)
            });
            </script>';
            $output .= ' <div class="sitecontent">';
            $output .= '    <div id="pageTarget" class="ff_pageTarget module">';
            $output .= '      <div class="module_container">';
            $output .= '        <div class="container_target wow" style="visibility: visible; animation-name: fadeInUp;">您的位置：<a href="#">首頁</a><i class="fa fa-angle-right"></i><a href="#">' . $article->cat_title . '</a><i class="fa fa-angle-right"></i><a href="#">' . $article->title . '</a></div>';
            $output .= '        <div class="clear"></div>';
            $output .= '      </div>';
            $output .= '    </div>';
            $output .= '    <!-- 左侧 开始-->';
            $output .= '    <div id="postLeft"> ';
            $output .= '      <!--  幻灯片开始-->';
            $output .= '      <div id="postSlider" class="postSlider mlist">';
            $output .= '        <div id="owl-demo" class="owl-carousel owl-theme">';
            if (!empty($article->many_img)) {
                $articleimg = explode(";", $article->many_img);
                foreach ($articleimg as $key => $val) {
                    $output .= '          <div class="itme">';
                    $output .= '            <div class="li1"> <img src="' . $val . '" alt=""> </div>';
                    $output .= '          </div>';
                }
            } else {
                $output .= '          <div class="itme">';
                $output .= '            <div class="li1"> <img src="' . $article->image_intro . '" alt=""> </div>';
                $output .= '          </div>';
            }

            $output .= '        </div>';
            $output .= '          <div class="owl-controls clickable">';
            $output .= '            <div class="owl-pagination"> </div>';
            $output .= '            <div class="owl-buttons"> </div>';
            $output .= '          </div>';
            $output .= '      </div>';
            $output .= '      <!--  幻灯片结束--> ';
            $output .= '      <!--  内容开始-->';
            $output .= '      <div class="postbody">';
            $output .= '        <p>';
            $output .= $article->fulltext;
            $output .= '        </p>';
            $output .= '      </div>';
            $output .= '      <!--  内容结束--> ';
            $output .= '    </div>';
            $output .= '    ';
            $output .= '    <!-- 左侧 结束-->';
            $output .= '    ';
            $output .= '    <div id="postRight">';
            $output .= '      <div class="postInfo">';
            $output .= '        <p class="title">' . $article->title . ' </p>';
            $output .= '        <p class="subtitle">' . $article->label1 . ' </p>';
            $output .= '        <p class="usetdate">'.date('Y-m-d',strtotime($article->created)).'</p>';
            $output .= '        <div class="description">';
            $output .= $article->introtext;
            $output .= '          <div class="button-box">';
            if ($type7_taobao_button == 'yes') {
                $output .= '        <a href="' . $type7_taobao_button_url . '" target="'.$open_way_type7.'">淘宝购买</a>';
            }
            if ($type7_weidian_button == 'yes') {
                $output .= '<a href="' . $type7_weidian_button_url . '" target="'.$open_way_type7.'">微店购买</a>';
            }
            if(count($button_item_type7)){
                foreach ($button_item_type7 as $keys => $vals) {
                    $output .= '<a href="'.$vals -> link.'" target="'.$open_way_type7.'">' . $vals->btn_txt . '</a>';
                }
            }
            $output .= '</div>';
            $output .= '          <p><br>';
            $output .= '          </p>';
            $output .= '          <p><br>';
            $output .= '          </p>';
            $output .= '        </div>';
            $output .= '      </div>';
            $output .= '      <div id="listContent" class="listContent_post">';
            $output .= '        <h3>相关内容</h3>';
            $output .= '        <div class="item_tags">';
            if (!empty($article->right_categories_list)) {
                $rightcategorieslist = $article->right_categories_list;
                $output .= '<a href="#" target="_blank">' . $rightcategorieslist->title . '</a>';
            } else {
                $output .= '        <a href="#" target="_blank">' . $article->cat_title . '</a>';
            }
            $output .= '        </div>';
            $rightcontentlist = $article->right_content_list;
            $right_img_obj = $rightcontentlist -> images ? Json_decode($rightcontentlist -> images) : (object) array();
            if(isset($right_img_obj->image_intro)){
                $right_img = $right_img_obj->image_intro;
            }else{
                $right_img = "";
            }
            $output .= '        <div class="mlist type04">';
            $output .= '          <div class="content_wrapper">';
            $output .= '            <div class="content_list clearfix">';
            $output .= '              <div id="item_block_0" class="item_block_0 item_block wow" style="animation-delay: 0s; visibility: visible; animation-name: fadeInUp;"> <a href="#" class="item_box">';
            // $output .= '                <div href="#" class="item_img" target="_blank"> <img src="' . $rightcontentlist->image_intro . '">';
            $output .= '                <div href="#" class="item_img" target="_blank"> <img src="' . $right_img . '">';
            $output .= '                  <div class="item_mask"></div>';
            $output .= '                </div>';
            $output .= '                <div class="item_wrapper clearfix">';
            $output .= '                  <div class="item_info clearfix">';
            $output .= '                    <p class="title ellipsis">' . $rightcontentlist->title . '</p>';
            $output .= '                    <p class="subtitle ellipsis">' . $rightcontentlist->label2 . '</p>';
            $output .= '                  </div>';
            $output .= '                </div>';
            $output .= '              </div>';
            $output .= '            </div>';
            $output .= '          </div>';
            $output .= '          <div class="clear"></div>';
            $output .= '        </div>';
            $output .= '      </div>';
            $output .= '    </div>';
            $output .= '<div class="conTabBtn tabBtn"> ';
            if ($page_button == 0) {
                if (!empty($article2)) {
                    $output .= '<a href="' . $on . ' " class="post-prev">';
                    $output .= '  <div class="img-wrap">';
                    $output .= '    <div style="background-image: url(' . $article2->image_intro . ' )"></div>';
                    $output .= '    <img src="' . $article2->image_intro . '" alt=""> </div>';
                    $output .= '  <div class="infor-wrap"> <span class="title">' . $article2->title . ' </span> <span class="subtitle">' . $article2->label1 . '</span> </div>';
                    $output .= '  <i class="details"></i>';
                    $output .= '  <div class="tabMask"></div>';
                    $output .= '  </a> ';
                }
                if (!empty($article3)) {
                    $output .= '  <a href="' . $down . ' " class="post-next">';
                    $output .= '  <div class="img-wrap">';
                    $output .= '    <div style="background-image: url(' . $article3->image_intro . ' )"></div>';
                    $output .= '    <img src="' . $article2->image_intro . '" alt=""> </div>';
                    $output .= '  <div class="infor-wrap"> <span class="title">' . $article3->title . ' </span> <span class="subtitle">' . $article3->label1 . '</span> </div>';
                    $output .= '  <i class="details"></i>';
                    $output .= '  <div class="tabMask"></div>';
                    $output .= '  </a> ';
                }
            }
            $output .= '</div>';
            $output .= '  </div>';
            $output .= '  <!--  PC结束-->';
            $output .= '  <div id="postWrapper">';
                $output .= '    <div id="postSlider">';
                    $output .= '      <div class="bx-wrapper">';
                        $output .= '        <ul class="content_list slider" >';
                            if (!empty($article->many_img)) {
                                $articleimgs = explode(";", $article->many_img);
                                foreach ($articleimgs as $keys => $vals) {
                                    $output .= '          <li class="sItem_block bx-clone" >';
                                    $output .= '            <div class="wrapper">';
                                    $output .= '              <div class="item_img"> <img src="' . $vals . '"> </div>';
                                    $output .= '            </div>';
                                    $output .= '          </li>';
                                }
                            } else {
                                $output .= '          <li class="sItem_block bx-clone" >';
                                $output .= '            <div class="wrapper">';
                                $output .= '              <div class="item_img"> <img src="' . $article->image_intro . '"> </div>';
                                $output .= '            </div>';
                                $output .= '          </li>';
                            }
                        $output .= '        </ul>';
                    $output .= '      </div>';
                $output .= '    </div>';
                $output .= '    <div id="postInfo">';
                    $output .= '      <div class="wrapper" data-sf-top="20">';
                        $output .= '        <p class="title">' . $article->title . ' </p>';
                        $output .= '        <p class="subtitle">' . $article->label1 . '</p>';
                        $output .= '        <div class="description">';
                            $output .= $article->introtext;
                            $output .= '<div class="button-box">';
                                if ($type7_taobao_button == 'yes') {
                                    $output .= '<a class="subbtn" href="' . $type7_taobao_button_url . '" target="'.$open_way_type7.'">淘宝购买</a>';
                                }
                                if ($type7_weidian_button == 'yes') {
                                    $output .= '<a class="subbtn" href="' . $type7_weidian_button_url . '" target="'.$open_way_type7.'">微店购买</a>';
                                }
                                if(count($button_item_type7)){
                                    foreach ($button_item_type7 as $keys => $vals) {
                                        $output .= '<a class="subbtn" href="'.$vals -> link.'" target="'.$open_way_type7.'">' . $vals->btn_txt . '</a>';
                                    }
                                }
                            $output .= '</div>';
                        $output .= '        </div>';
                    $output .= '      </div>';
                $output .= '    </div>';
            $output .= '    <div id="postContent">';
            $output .= '      <div class="postbody">';
            $output .= '        <p>';
            $output .= $article->fulltext;
            $output .= '        </p>';
            $output .= '      </div>';
            $output .= '      <div id="listContent">';
            $output .= '        <h3>相关内容</h3>';
            $output .= '        <div class="item_tags"><i class="fa fa-tags"></i>';
            if (!empty($article->right_categories_list)) {
                $rightcategorieslist = $article->right_categories_list;
                $output .= '<a href="#" target="_blank">' . $rightcategorieslist->title . '</a>';
            } else {
                $output .= '        <a href="#" target="_blank">' . $article->cat_title . '</a>';
            }
            $output .= '        </div>';
            $output .= '        <div class="mlist project">';
            $output .= '          <div class="content_wrapper">';
            $output .= '            <div class="content_list clearfix">';
            $output .= '              <div class="item_block wow" > <a href="#" class="item_box">';
            // $output .= '                <div class="item_img" target="_blank"> <img src="' . $rightcontentlist->image_intro . '">';
            $output .= '                <div class="item_img" target="_blank"> <img src="' . $right_img . '">';
            $output .= '                  <div class="item_mask"></div>';
            $output .= '                </div>';
            $output .= '                <div class="item_wrapper clearfix">';
            $output .= '                  <div class="item_info clearfix">';
            $output .= '                    <div class="text_wrap">';
            $output .= '                      <p class="title ellipsis">' . $rightcontentlist->title . '</p>';
            $output .= '                      <p class="subtitle ellipsis">' . $rightcontentlist->label2 . '</p>';
            $output .= '                    </div>';
            $output .= '                  </div>';
            $output .= '                </div>';
            $output .= '                </a> </div>';
            $output .= '            </div>';
            $output .= '          </div>';
            $output .= '        </div>';
            $output .= '        <div class="clear"></div>';
            $output .= '      </div>';
            $output .= '    </div>';
            $output .= '    <div class="clear"></div>';
            $output .= '  </div>';
            $output .= '  </div>';

        }
        if($art_type_selector_proinfo == 'type8'){
            $type8_banopen           = (isset($settings->type8_banopen) && $settings->type8_banopen) ? $settings->type8_banopen : '0';
            $type8_banner_bili           = (isset($settings->type8_banner_bili) && $settings->type8_banner_bili) ? $settings->type8_banner_bili : '0';
            $type8_ban_img           = (isset($settings->type8_ban_img) && $settings->type8_ban_img) ? $settings->type8_ban_img : '2/1';

            $type8_price_size           = (isset($settings->type8_price_size) && $settings->type8_price_size) ? $settings->type8_price_size : '24';
            $type8_price_color           = (isset($settings->type8_price_color) && $settings->type8_price_color) ? $settings->type8_price_color : '#fa230a';
            $type8_title_size           = (isset($settings->type8_title_size) && $settings->type8_title_size) ? $settings->type8_title_size : '16';
            $type8_title_color           = (isset($settings->type8_title_color) && $settings->type8_title_color) ? $settings->type8_title_color : '#252525';

            if (isset($settings->type8_padding)) {
                if (is_object($settings->type8_padding)) {
                    $type8_padding_md = $settings->type8_padding->md;
                    $type8_padding_sm = $settings->type8_padding->sm;
                    $type8_padding_xs = $settings->type8_padding->xs;
                } else {
                    $type8_padding_md = $settings->type8_padding;
                    $type8_padding_sm = $settings->type8_padding_sm;
                    $type8_padding_xs = $settings->type8_padding_xs;
                }
            } else {
                $type8_padding_md = '18px 18px 18px 18px';
                $type8_padding_sm = '18px 18px 18px 18px';
                $type8_padding_xs = '18px 18px 18px 18px';
            }


            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);

            if($biaoqian_type == 'yes')
            {
                if($biaoqian_peizhi)
                {
                    $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                    foreach ($biaoqian_peizhi as $k => $v) {
                        $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                    }
                    if($biaoqian_title_type=='yes')
                    {
                        $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                    }
                    $output .= '</div>';
                }

            }
            $output .= '<style type="text/css">
                ' . $addon_id . ' p,a,li,ul,span,h1,h2,h3,h4,h5,h6,div,em,i,img,video,iframe,object,select,textarea,input{
                    margin: 0;
                    padding: 0;
                }
                ' . $addon_id . ' .detail-img p img{display:inline-block;}
                ' . $addon_id . ' .swiper-wrapper {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    z-index: 1;
                    display: flex;
                    transition-property: transform;
                    box-sizing: content-box;
                }

                ' . $addon_id . ' .swiper-pagination {
                    position: absolute;
                    text-align: center;
                    transition: .3s opacity;
                    transform: translate3d(0,0,0);
                    z-index: 10;
                    width:100%;
                }

                ' . $addon_id . ' .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
                    bottom: 10px;
                    left: 0;
                    width: 100%;
                }
                ' . $addon_id . ' .swiper-pagination-bullet {
                    width: var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));
                    height: var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));
                    display: inline-block;
                    border-radius: 50%;
                    background: var(--swiper-pagination-bullet-inactive-color,#000);
                    opacity: var(--swiper-pagination-bullet-inactive-opacity, .2);
                }

                ' . $addon_id . ' .swiper-slide {
                    flex-shrink: 0;
                    width: 100%;
                    height: 100%;
                    position: relative;
                    transition-property: transform;
                }

                ' . $addon_id . ' .type8.mySwiper{
                    width:100%;overflow:hidden;position:relative;
                }
                ' . $addon_id . ' .type8.mySwiper img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                ' . $addon_id . ' .self-pagination{
                    position: absolute;
                    bottom: 19px;
                    right: 0;
                    width: 55px;
                    height: 24px;
                    border-radius: 4px;
                    background: rgba(0,0,0,.3);
                    z-index: 9;
                    border-radius: 12px 0 0 12px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #fff;
                    font-size: 12px;
                    letter-spacing: 2px;
                }
                ' . $addon_id . ' .cur-page{
                    font-size: 16px;
                }
                ' . $addon_id . ' .swiper-pagination{
                    bottom: 6px;
                }
                ' . $addon_id . ' .swiper-pagination-bullet{
                    width: 8px;
                    height: 8px;
                    background: rgba(0,0,0,.2);
                    opacity: 1;
                    margin:0px 3px;
                }
                ' . $addon_id . ' .swiper-pagination-bullet-active{
                    background: rgba(0,0,0,.8);
                }
                ' . $addon_id . ' .info{
                    padding:'.$type8_padding_md.';
                    border-radius: 0px 0px 10px 10px;
                    background: #fff;
                }


                @media screen and (max-width: 997px) {
                    ' . $addon_id . ' .info{
                        padding:'.$type8_padding_sm.';
                        border-radius: 0px 0px 10px 10px;
                        background: #fff;
                    }
                }
                @media screen and (max-width: 768px) {
                    ' . $addon_id . ' .info{
                        padding:'.$type8_padding_xs.';
                        border-radius: 0px 0px 10px 10px;
                        background: #fff;
                    }
                }

                ' . $addon_id . ' .price{
                    color: '.$type8_price_color.';
                    font-size: 14px;
                    font-weight: bold;
                }
                ' . $addon_id . ' .price .int{
                    font-size: '.$type8_price_size.'px;
                }
                ' . $addon_id . ' .info .title{
                    font-size: '.$type8_title_size.'px;
                    color:'.$type8_title_color.';
                    font-weight: bold;
                    line-height: 1.5;
                    margin-top: 14px;
                }
                ' . $addon_id . ' .radius{
                    border-radius: 10px;
                    background: #fff;
                    margin-top: 11px;
                }
                ' . $addon_id . ' .model{
                    padding: 23px 18px;
                }
                ' . $addon_id . ' .model .item{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-weight: bold;
                    font-weight: 16px;
                }
                ' . $addon_id . ' .detail{
                    border-radius: 10px 10px 0 0;
                }
                ' . $addon_id . ' .detail .tips{
                    color: #cccccc;
                    font-size: 14px;
                    text-align: center;
                }
                ' . $addon_id . ' .detail .tips span:first-child {
                    margin-right: 5px;
                }
                ' . $addon_id . ' .detail .tips span:last-child{
                    display: inline-block;
                    transform: scaleY(0.8);
                }
                ' . $addon_id . ' .detail .tips i{
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-left: 2px solid #ccc;
                    border-top: 2px solid #ccc;
                    border-bottom: 2px solid transparent;
                    border-right: 2px solid transparent;
                    transform: rotate(45deg) translateY(50%);
                }
                ' . $addon_id . ' .detail-label{
                    font-size: 14px;
                    color: #000;
                    margin: 12px 24px;
                    padding-left: 13px;
                    font-weight: bold;
                    position: relative;
                }
                ' . $addon_id . ' .detail-label::before{
                    content: "";
                    display: block;
                    position: absolute;
                    left: 0;
                    width: 3px;
                    height: 70%;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    background-image: linear-gradient(to bottom, #f2270c, #fff);
                }';
                if ($article->on) {
                    $output .= '    ' . $addon_id . ' .btnBox{  display:flex;justify-content:space-between;padding:20px; }';

                } else {
                    $output .= '    ' . $addon_id . ' .btnBox{  display:flex;justify-content:flex-end;padding:20px; }';
                }
                $output .= '    ' . $addon_id . ' .btnBox a{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColor . ';color: ' . $pageColor . ';font-size: 14px;background:' . $pageBgColor . ';text-align: center;line-height: 40px;text-decoration: none;}';
                $output .= '    ' . $addon_id . ' .btnBox a:hover{     width: 120px; height: 40px; border: 1px solid ' . $pageBorderColorhover . ';color: ' . $pageColorhover . ';font-size: 14px;background:' . $pageBgColorhover . ';text-align: center;line-height: 40px;text-decoration: none;}';

            $output .= '</style>';

            $output .= '<div class="type8">';
                if($type8_banopen!=1){
                    $output .= '<div class="swiper mySwiper type8">
                        <div class="swiper-wrapper">';

                            if (!empty($article->many_img)) {
                                $articleimgs = explode(";", $article->many_img);
                                $nums=0;
                                $nums=count($articleimgs);
                                foreach ($articleimgs as $keys => $vals) {

                                    $output .= '<div class="swiper-slide">
                                        <img src="' . $vals . '">
                                    </div>';

                                }
                            }

                        $output .= '</div>
                        <div class="swiper-pagination"></div>
                        <div class="self-pagination">
                            <span class="cur-page"></span>
                            <span class="split">/</span>
                            <span class="total-page"></span>
                        </div>
                    </div>';
                }

                $output .= '<div class="info">';
                    if($article->label1){
                        $output .= '<p class="price">
                            ￥<span class="int">' . $article->label1 . '</span>
                        </p>';
                    }

                    $output .= '<p class="title">
                        ' . $article->title . '
                    </p>
                </div>

                <!--详情-->
                <div class="radius detail">
                    <div class="detail-label">
                        详情
                    </div>
                    <div class="detail-img">
                        ' . $article->fulltext . '
                    </div>
                </div>
            </div>';

            //翻页

            if ($page_button == 0) {
                if ($page_dttitle == 1) {
                    $output .= '    <div class="btnBoxt">';

                    if ($article->ontitle) {
                        $output .= '<a href="' . $on . '">' . $up_page_text . '：' . $article->ontitle . '</a>';
                    }
                    if ($article->downtitle) {
                        $output .= '<a href="' . $down . '">' . $next_page_text . '：' . $article->downtitle . '</a>';
                    }

                    $output .= '    </div>';
                } else {
                    $output .= '    <div class="btnBox">';
                    if ($page_button == 0) {

                        if ($article->on) {
                            $output .= '<a href="' . $on . '">' . $up_page_text . '</a>';
                        }
                        if ($article->down) {
                            $output .= '<a href="' . $down . '">' . $next_page_text . '</a>';
                        }
                    }
                    $output .= '    </div>';
                }
            }


            $output.='<script>';
                if($type8_banner_bili){
                    $output.='
                        var wid=$("'.$addon_id.' .swiper-wrapper").width();
                        var heit=wid/('.$type8_ban_img.');
                        $("'.$addon_id.' .swiper-wrapper").css({"height":heit+"px"});
                    ';
                }
                if($type8_banopen!=1){

                    $output.='var swiper = new Swiper("'.$addon_id.' .mySwiper.type8", {
                        autoplay:true,
                        loop: true,
                        pagination: {
                            el: "'.$addon_id.' .swiper-pagination",
                            clickable: true,
                        },
                        on: {
                            init(){
                                $("'.$addon_id.' .cur-page").text(this.realIndex +1);
                                var lens='.$nums.';
                                $("'.$addon_id.' .total-page").text(lens);
                            },
                            transitionEnd(){
                                $("'.$addon_id.' .cur-page").text(this.realIndex + 1);
                            }
                        }
                    });';
                }

            $output.='</script>';
        }
        if($art_type_selector_proinfo === 'type9'){
            $show_banner_type9 = isset($settings->show_banner_type9) ? $settings->show_banner_type9 : '1';
            if (isset($settings->img_width_type9) && $settings->img_width_type9) {
              if (is_object($settings->img_width_type9)) {
                $img_width_type9 = $settings->img_width_type9->md;
                $img_width_type9_sm = $settings->img_width_type9->sm;
                $img_width_type9_xs = $settings->img_width_type9->xs;
              } else {
                $img_width_type9 = $settings->img_width_type9;
                $img_width_type9_sm = $settings->img_width_type9_sm;
                $img_width_type9_xs = $settings->img_width_type9_xs;
              }
            } else {
              $img_width_type9 = '100';
              $img_width_type9_sm = '100';
              $img_width_type9_xs = '100';
            }
            $set_img_height_type9 = isset($settings->set_img_height_type9) ? $settings->set_img_height_type9 : '0';
            if (isset($settings->img_height_type9) && $settings->img_height_type9) {
              if (is_object($settings->img_height_type9)) {
                  $img_height_type9 = $settings->img_height_type9->md;
                  $img_height_type9_sm = $settings->img_height_type9->sm;
                  $img_height_type9_xs = $settings->img_height_type9->xs;
              } else {
                  $img_height_type9 = $settings->img_height_type9;
                  $img_height_type9_sm = $settings->img_height_type9_sm;
                  $img_height_type9_xs = $settings->img_height_type9_xs;
              }
            } else {
                $img_height_type9 = '';
                $img_height_type9_sm = '';
                $img_height_type9_xs = '';
            }
            $img_fit_type9 = (isset($settings->img_fit_type9) && $settings->img_fit_type9) ? $settings->img_fit_type9 : 'cover';
            if (isset($settings->detail_top_type9) && $settings->detail_top_type9) {
              if (is_object($settings->detail_top_type9)) {
                  $detail_top_type9 = $settings->detail_top_type9->md;
                  $detail_top_type9_sm = $settings->detail_top_type9->sm;
                  $detail_top_type9_xs = $settings->detail_top_type9->xs;
              } else {
                  $detail_top_type9 = $settings->detail_top_type9;
                  $detail_top_type9_sm = $settings->detail_top_type9_sm;
                  $detail_top_type9_xs = $settings->detail_top_type9_xs;
              }
            } else {
                $detail_top_type9 = '0';
                $detail_top_type9_sm = '0';
                $detail_top_type9_xs = '0';
            }
            if (isset($settings->detail_width_type9) && $settings->detail_width_type9) {
              if (is_object($settings->detail_width_type9)) {
                  $detail_width_type9 = $settings->detail_width_type9->md;
                  $detail_width_type9_sm = $settings->detail_width_type9->sm;
                  $detail_width_type9_xs = $settings->detail_width_type9->xs;
              } else {
                  $detail_width_type9 = $settings->detail_width_type9;
                  $detail_width_type9_sm = $settings->detail_width_type9_sm;
                  $detail_width_type9_xs = $settings->detail_width_type9_xs;
              }
            } else {
                $detail_width_type9 = '63';
                $detail_width_type9_sm = '63';
                $detail_width_type9_xs = '63';
            }

            $company_id = $_GET['company_id'] ?? '';
            $site_id    = $_GET['site_id'] ?? '';
            $catid_id   = $_GET['catid_id'] ?? '';
            $layout_id  = $_GET['layout_id'] ?? '';
            //获取产品详情的数据源

            $app   = JFactory::getApplication();
            $input = $app->input;

            $article_id = $input->get('detail');
            $detail_id  = base64_decode($input->get('id'));
            if($ordering_select!='')
            {
                $article    = JwPageFactoryBase::getGoodsByIdnew($article_id, $catid_id,$ordering_select);
            }
            else
            {
                $article    = JwPageFactoryBase::getGoodsById($article_id, $catid_id);
            }
            $on         = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0" . '&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);
            $down       = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . '&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&catid_id=' . $catid_id . '&company_id=' . $company_id, $absolute = true);

            // 翻页样式
            $pageTopType9 = (isset($settings->pageTopType9) && $settings->pageTopType9) ? $settings->pageTopType9 : '50';
            $pageColumnType9 = isset($settings->pageColumnType9) ? $settings->pageColumnType9 : '1';
            $pageMarginType9 = isset($settings->pageMarginType9) ? $settings->pageMarginType9 : '25';

            $output.= '<style>
              '.$addon_id.' .type9-detail{
                width: 100%;
              }
              '.$addon_id.' .banner{
                display: block;
                width: '.$img_width_type9.'%;
                margin: auto;';
                if(!$set_img_height_type9){
                  $output.='height: auto;';
                }else{
                  $output.='height: '.$img_height_type9.'px;';
                }
                $output.='object-fit: '.$img_fit_type9.';
              }
              '.$addon_id.' .banner.pc{
                display: block;
              }
              '.$addon_id.' .banner.phone,
              '.$addon_id.' .banner.pad{
                display: none;
              }
              '.$addon_id.' .content{
                overflow: hidden;
                margin-top: '.$detail_top_type9.'px;
                width: '.$detail_width_type9.'%;
                margin-left: auto;
                margin-right: auto;
              }
              '.$addon_id.' .content>div{
                overflow: hidden;
              }
              '.$addon_id.' .btnBoxt {
                margin-top: '.$pageTopType9.'px;
              }
              '.$addon_id.' .btnBoxt a {
                color: '.$pageColor.';';
                if($pageColumnType9){
                    $output.='display: block';
                }
              $output.='}
              '.$addon_id.' .btnBoxt a:hover {
                color: '.$pageColorhover.';
              }
              '.$addon_id.' .btnBoxt a + a {
                margin-top: '.$pageMarginType9.'px;
              }

              @media screen and (max-width: 992px) and (min-width: 767px){
                '.$addon_id.' .banner{
                  width: '.$img_width_type9_sm.'%;';
                  if(!$set_img_height_type9){
                    $output.='height: auto;';
                  }else{
                    $output.='height: '.$img_height_type9_sm.'px;';
                  }
                $output.='}
                '.$addon_id.' .banner.pad{
                    display: block;
                }
                '.$addon_id.' .banner.phone,
                '.$addon_id.' .banner.pc{
                    display: none;
                }
                '.$addon_id.'  .content{
                  margin-top: '.$detail_top_type9_sm.'px;
                  width: '.$detail_width_type9_sm.'%;
                }
              }

              @media screen and (max-width: 768px){
                '.$addon_id.' .banner{
                  width: '.$img_width_type9_xs.'%;';
                  if(!$set_img_height_type9){
                    $output.='height: auto;';
                  }else{
                    $output.='height: '.$img_height_type9_xs.'px;';
                  }
                $output.='}
                '.$addon_id.' .banner.phone{
                    display: block;
                }
                '.$addon_id.' .banner.pc,
                '.$addon_id.' .banner.pad{
                    display: none;
                }
                '.$addon_id.'  .content{
                  margin-top: '.$detail_top_type9_xs.'px;
                  width: '.$detail_width_type9_xs.'%;
                }
              }
            </style>';

            if (!empty($article->many_img)) {
                $articleimgs = explode(";", $article->many_img);
                if(count($articleimgs) > 0){
                    $articlePcimg = $articleimgs[0] ?? $article->image_intro;
                    $articlePhoneimg = $articleimgs[1] ?? $article->image_intro;
                    $articlePadimg = $articleimgs[2] ?? $article->image_intro;
                }else{
                    $articlePcimg = $article->image_intro;
                    $articlePhoneimg = $article->image_intro;
                    $articlePadimg = $article->image_intro;
                }
            }else{
                $articlePcimg = $article->image_intro;
                $articlePhoneimg = $article->image_intro;
                $articlePadimg = $article->image_intro;
            }

            $output.='<div class="type9-detail">';
                if($show_banner_type9){
                    $output.='<img class="banner pc" src="'.$articlePcimg.'">';
                    $output.='<img class="banner phone" src="'.$articlePhoneimg.'">';
                    $output.='<img class="banner pad" src="'.$articlePadimg.'">';
                }

                $output.='<div class="content">
                    <div>
                        ' . $article->fulltext . '
                    </div>';
                    //翻页
                    if ($page_button == 0) {
                        if ($page_dttitle == 1) {
                            $output .= '    <div class="btnBoxt">';

                            if ($article->ontitle) {
                                $output .= '<a href="' . $on . '">' . $up_page_text . '：' . $article->ontitle . '</a>';
                            }
                            if ($article->downtitle) {
                                $output .= '<a href="' . $down . '">' . $next_page_text . '：' . $article->downtitle . '</a>';
                            }

                            $output .= '    </div>';
                        } else {
                            $output .= '    <div class="btnBox">';
                            if ($page_button == 0) {

                                if ($article->on) {
                                    $output .= '<a href="' . $on . '">' . $up_page_text . '</a>';
                                }
                                if ($article->down) {
                                    $output .= '<a href="' . $down . '">' . $next_page_text . '</a>';
                                }
                            }
                            $output .= '    </div>';
                        }
                    }
                $output.='</div>';
          }

        return $output;
    }

    //在预览页面中使用
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(
            //JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.countdown.min.js'
            // JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',

            //JURI::base(true) . '/components/com_jwpagefactory/assets/',
            //JURI::base(true) . '/components/com_jwpagefactory/assets/js/presentation.js',
            //JURI::base(true) . '/components/com_jwpagefactory/assets/js/presentation.js',
            //JURI::base(true) . '/components/com_jwpagefactory/assets/js/presentation.js',
        );


        return $scripts;
    }

    public function js()
    {
        $app        = JFactory::getApplication();
        $input      = $app->input;
        $article_id = $input->get('detail');

        $js = "
        $(document).ready(function () {
            $.ajax({
                url: 'https://zhjzt.china9.cn/api/Goods/addGoodsfw',
                type: 'post',
                async: false,
                dataType: 'json',
                data: {id:'" . $article_id . "'},
                success: function (data) {
                    //console.log(data)
                }
            })
        })";
        return $js;
    }

    //在预览页面中使用的css样式
    public function stylesheets()
    {
        $css = array(
            //  JURI::base(true) . '/components/com_jwpagefactory/assets/js/presentation.css',
            //            JURI::base(true) . '/components/com_jwpagefactory/assets/js/presentation.scss',
        );
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {

        $output = '
            <#
                var addonId = "jwpf-addon-"+data.id;
                var theme = data.art_type_selector_proinfo?data.art_type_selector_proinfo:"type1";
                var biaoqian_type = data.biaoqian_type ? data.biaoqian_type : "no";
            #>
            <# if(biaoqian_type=="yes") { #>
                <div>
                    <span>首页 / </span>
                    <span>产品中心</span>
                </div>
            <# } #>
            <# if(theme=="type1") { #>
                <style type="text/css">
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT { color:{{{ data.color1615452944411 }}}; }
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2{
                        <# if( data.title_bold) { #>
                            font-weight: bold;
                        <# } #>
                    }
                    #{{ addonId }} .btnBox { display:flex;justify-content:space-between;padding:20px;}
                    #{{ addonId }} .btnBox a { width: 120px; height: 40px; border: 1px solid {{data.pageBorderColor}};color: {{data.pageColor}};font-size: 14px;background:{{data.pageBgColor}}; text-align: center;line-height: 40px;text-decoration: none;}
                    #{{ addonId }} .btnBox a:hover {     width: 120px; height: 40px; border: 1px solid {{data.pageBorderColorhover}};color: {{data.pageColorhover}};font-size: 14px;background:{{data.pageBgColorhover}};text-align: center;line-height: 40px;text-decoration: none;}

                    #{{ addonId }} .btnBoxt {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBoxt a {
                        width: 50%;
                        height: 40px;
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: left;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:hover {
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:nth-child(2){  text-align:right; }


                </style>
                <div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">
                    <h2 style="text-align:{{{ data.select1615452416171 }}};font-size: {{data.font_size}}px;">示例产品标题</h2>
                    <hr />
                    <p style="text-align:right; display:block; padding-right:10px; color:{{{ data.color1615453171634 }}};font-size: {{data.font_size_date}}px">编辑：2021年3月11日 16:59</p>
                    <div style="color:{{{ data.color1615443385649 }}};margin-top:{{{ data.content_top }}}px">
                        示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文。
                    </div>
                    <# if(data.page_button==0) { #>
                        <# if(data.page_dttitle==1) { #>
                            <div class="btnBoxt">
                                <a href="#">{{data.up_page_text}}:标题名称标题标题</a>
                                <a href="#">{{data.next_page_text}}:标题名称标题标题</a>
                            </div>
                        <# }else{ #>
                            <div class="btnBox">
                                <a href="#">{{data.up_page_text}}</a>
                                <a href="#">{{data.next_page_text}}</a>
                            </div>
                        <# } #>

                    <# } #>

                </div>
            <# } #>
            <# if(theme=="type2") { #>
                <#
                    var img_mode_type2 = data.img_mode_type2 || "cover";
                    var tit_cle_line_color = data.tit_cle_line_color || "#fecf41";
                    var pro_vice_title_line_color = data.pro_vice_title_line_color || "#fee800";
                    var pro_vice_title_color = data.pro_vice_title_color || "#fed561";
                    var pro_vice_title_padding = data.pro_vice_title_padding || "0 0 0 10px";
                    var tit_cle_line_margin = data.tit_cle_line_margin || "25px 0 0 0";

                #>
                <style>
                    #{{ addonId }} .container1{
                        margin: 0 auto;
                    }
                    #{{ addonId }}  .person-introduce {
                        padding-bottom: 23px;
                        border-bottom: 1px solid #f4f4f4;
                        overflow: visible;
                    }
                    #{{ addonId }}  .person-introduce::after {
                        content: "";
                        display: block;
                        clear: both;
                    }
                    #{{ addonId }}  .person-introduce .introduce .left_img {
                        width: {{{ data.img_w }}}px;
                        height: {{{ data.img_h }}}px;
                        object-fit: {{img_mode_type2}};
                        margin-right: 20px;
                        float: left;
                    }
                    #{{ addonId }}  .person-introduce .introduce {
                        width: 100%;
                        float: right;
                        padding-top: 10px;
                    }
                    #{{ addonId }}  .person-introduce .introduce .name {
                        color: {{{ data.tit_cle }}};
                        font-size: {{{ data.tit_size }}}px;
                        margin-bottom: 8px;
                        <# if( data.title_bold) { #>
                            font-weight: bold;
                        <# } #>
                    }
                    #{{ addonId }}  .person-introduce .introduce .position {
                        font-size: {{{ data.tit_cle_f_size }}}px;
                        color: {{{ data.tit_cle_f }}};
                    }
                    #{{ addonId }}  .person-introduce .introduce .split {
                        margin-top: 25px;
                        margin: {{tit_cle_line_margin}};
                        width: 24px;
                        height: 5px;
                        background: {{tit_cle_line_color}};
                    }
                    #{{ addonId }}  .person-introduce .introduce ul {
                        color: {{{ data.text_cle_j }}};
                        float: left;
                        margin-top: 20px;
                        padding-left: 0!important;
                    }
                    #{{ addonId }}  .person-introduce .introduce ul li {
                        position: relative;
                        font-size: {{{ data.jj_size }}}px;
                        color: {{{ data.text_cle_j }}};
                        line-height: 2;
                    }
                    /* #{{ addonId }}  .jianjie {
                        font-size: {{{ data.jj_size }}}px;
                        color: {{{ data.text_cle_j }}};
                    } */
                    #{{ addonId }}  .person-introduce .introduce ul li::before {
                        content: "";
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        background: #fdcf3f;
                        transform: rotate(45deg);
                        position: absolute;
                        left: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                    #{{ addonId }}  .person-introduce .introduce ul li:nth-of-type(-n+5) {
                        padding-left: 20px;
                        list-style: none;
                    }

                    #{{ addonId }}{
                        width: 100%;
                        margin-top: 34px;
                    }
                    #{{ addonId }} .module-title {
                        position: relative;
                        padding-left: 10px;
                        padding: {{pro_vice_title_padding}};
                        font-size: 16px;
                    }
                    #{{ addonId }} .module-title span {
                        text-transform: uppercase;
                        font-size: 10px;
                        color: {{pro_vice_title_color}};
                        vertical-align: 1px;
                    }
                    #{{ addonId }} .module-title::before {
                        content: "";
                        display: inline-block;
                        width: 2px;
                        height: 18px;
                        background: {{pro_vice_title_line_color}};
                        position: absolute;
                        left: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                    #{{ addonId }} .swiper-box {
                        position: relative;
                    }
                    #{{ addonId }} .swiper-box -swiper {
                        margin-top: 20px;
                        width: 100%;
                        height: 321px;
                    }
                    #{{ addonId }} .swiper-box -swiper .swiper-slide {
                        width: 228px;
                        height: 100%;
                    }
                    <# if(data.nav_back_col == 0) { #>
                        #{{ addonId }}   .swiper-box -swiper .swiper-slide img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            object-position: top;
                        }
                    <# } else{ #>
                        #{{ addonId }}   .swiper-box -swiper .swiper-slide img {
                            width: {{{ data.img_carousel_w }}}%;
                            height: {{{ data.img_carousel_h }}}%;
                            object-fit: cover;
                            object-position: top;
                        }
                    <# } #>
                    #{{ addonId }} .swiper-box .swiper-button-next,   .swiper-box .swiper-button-prev {
                        width: 42px;
                        height: 42px;
                        background: #e0e0e0;
                        border-radius: 50%;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                    #{{ addonId }} .swiper-box .swiper-button-next:hover,   .swiper-box .swiper-button-prev:hover {
                        background: #ffecb2;
                    }
                    #{{ addonId }} .swiper-box .swiper-button-next:hover::after,   .swiper-box .swiper-button-prev:hover::after {
                        color: #f8c114;
                    }
                    #{{ addonId }} .swiper-box .swiper-button-next::after,   .swiper-box .swiper-button-prev::after {
                        font-size: 16px;
                        color: #161616;
                    }
                    #{{ addonId }} .swiper-box .swiper-button-next {
                        right: -60px;
                    }
                    #{{ addonId }} .swiper-box .swiper-button-prev {
                        left: -60px;
                    }
                    @media screen and (max-width: 768px) {
                        #{{ addonId }} .container {
                            width: 100%;
                        }
                        #{{ addonId }} .person-introduce .introduce {
                            height: auto;
                            padding-right: 20px;
                            box-sizing: border-box;
                            padding-top: 0;
                        }
                        #{{ addonId }} .person-introduce .introduce img {
                            width: calc({{{ data.img_w }}}px - 15px);
                            height: {{{ data.img_h }}}px;
                            object-fit: cover;
                            margin-right: 20px;
                            float: left;
                        }
                        #{{ addonId }} .person-introduce .introduce .name {
                            margin-bottom: 14px;
                        }
                        #{{ addonId }} .person-introduce .introduce .position {
                            margin-bottom: 14px;
                        }
                        #{{ addonId }} .person-introduce .introduce ul {
                            float: left;
                            margin-top: 30px;
                            padding-left: 0!important;
                        }
                        #{{ addonId }} .person-introduce .introduce ul li {
                            padding-left: 20px;
                            list-style: none;
                            line-height: 1.5;
                            margin-bottom: 10px;
                        }
                        #{{ addonId }} .person-introduce .introduce ul li::before {
                            top: 6px;
                            margin: 0;
                        }
                        #{{ addonId }} .person-introduce .introduce ul li:nth-of-type(n+4)::before {
                        }
                        #{{ addonId }} .swiper-box .swiper-button-next,   .swiper-box .swiper-button-prev {
                            display: none;
                        }
                        #{{ addonId }} .swiper-box .presentation-swiper {
                            width: 90%;
                            margin-left:0;
                            height: 380px;
                        }
                    }
                </style>
                <div class="presentation container1">
                    <div class="person-introduce">
                        <div class="introduce">
                            <img class="left_img" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
                            <div style="float: left;width:calc(100% - {{{ data.img_w }}}px - 20px)">
                                <p class="name">示例产品标题</p>
                                <div class="position">示例产品标题1</div>
                                <div class="split"></div>
                                <span class="jianjie">示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文。</span>
                            </div>
                        </div>
                    </div>
                    <div class="presentation-style">
                        <div class="module-title">{{data.pro_title}}/ <span>{{data.pro_vice_title}}</span></div>
                        <div class="swiper-box">
                            <div class="presentation-swiper swiper-container">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <a href="javascript:">
                                            <img style="width:{{{data.img_carousel_w}}}px;height:{{{data.img_carousel_h}}}px" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <!-- Add Arrows -->
                            <div class=\"swiper-button-next\"></div>
                            <div class=\"swiper-button-prev\"></div>
                        </div>
                    </div>
                </div>
            <# } #>
            <# if(theme=="type3") { #>
                <#
                    // 小屏的列表展示个数
                    var type3_small_img_carousel_w = data.type3_small_img_carousel_w || 3;
                    // 外边距
                    var type3_small_img_carousel_margin_right = data.type3_small_img_carousel_margin_right || 0;
                    var type3_small_img_carousel_margin_bottom = data.type3_small_img_carousel_margin_bottom || 0;
                    // 选中列表背景色
                    var type3_list_bg_col_select = data.type3_list_bg_col_select || "#cdb7a3";
                #>
                <style>
                    #{{ addonId }} .pro_list{
                        width: 200px;
                        margin: 0;
                        float:left;
                    }
                    #{{ addonId }} .list_ttile{
                        width: 100%;
                        height: 100px;
                        text-align: center;
                        background-color: {{data.type3_bg_col}};
                        color: {{data.type3_text_col}};
                        border-top-right-radius:25px;
                    }
                    #{{ addonId }} .list_ttile h2{
                        line-height: 55px;
                        margin: 0;
                    }
                    #{{ addonId }} .list_ttile h3{
                        margin: 0;
                    }
                    #{{ addonId }} .list ul{
                        padding: 0;
                        margin: 0;
                    }
                    #{{ addonId }} .list ul li{
                        width: {{data.type3_img_carousel_w}}%;
                        text-align: center;
                        background-color: {{data.type3_list_bg_col}};
                        list-style-type: none;
                        color:{{data.type3_list_text_col}};
                        margin-top: 15px;
                        line-height: {{data.type3_img_carousel_h}}px;
                        font-size:{{data.type3_text_size}}px;
                        cursor: pointer;
                    }
                    #{{ addonId }} .list ul li.active{
                        background-color: {{type3_list_bg_col_select}};
                    }
                    #{{ addonId }} .details_content{
                        float: left;
                        margin-left: 50px;
                        width: calc(100% - 200px - 50px);
                        display: none;
                        position: relative;
                        top: {$type3_vertical_location}px;
                        left: {$type3_left_location}px;
                    }
                    #{{ addonId }} .details_content.active{
                        display: block;
                    }
                    @media (max-width: 1024px) {
                        #{{ addonId }} .pro_list{
                            width: 100%;
                            float: none;
                        }
                        #{{ addonId }} .list ul{
                            overflow: hidden;
                        }
                        #{{ addonId }} .list ul li {
                            width: calc(calc(100% / {{type3_small_img_carousel_w}} - {{type3_small_img_carousel_margin_right}}px + {{type3_small_img_carousel_margin_right}}px / 3));
                            margin-right: {{type3_small_img_carousel_margin_right}}px;
                            margin-bottom: {{type3_small_img_carousel_margin_bottom}}px;
                            float: left;
                        }
                        #{{ addonId }} .list ul li:nth-child({{type3_small_img_carousel_w}}n){
                            margin-right: 0!important;
                        }
                        #{{ addonId }} .details_content{
                            float: none!important;
                            width: 100%!important;
                            left: 0!important;
                            margin-left: 0!important;
                        }
                    }
                </style>

                <div class="center">
                    <!--列表-->
                    <div class="pro_list">

                        <div class="list_ttile">
                            <h2>{{data.type3_class_title}}</h2>
                            <h3>{{data.type3_class_vice_title}}</h3>
                        </div>
                        <div class="list">
                            <ul>
                                <li class="active">全部产品</li>
                                <li>产品案例1</li>
                                <li>产品案例2</li>
                                <li>产品案例3</li>
                                <li>产品案例4</li>
                                <li>产品案例5</li>
                                <li>产品案例6</li>
                            </ul>
                        </div>
                    </div>
                    <!--详情内容-->
                    <div class="details_content active">
                        2020年3月2日早，公司微信公众平台推送的《公司党委、纪委约谈焦化厂党政主要领导及纪检负责人》一文，收到“漳水河边”的一条留言。 这条留言，跨越长钢74年历史，跨越焦化发展三个历史阶段，将焦化发展置于长钢发展的宏大叙事之下，以不同时期赋予焦化不同的使命为期许，激励焦化人将首钢精神和长钢精神发扬光大。 初读这份留言，小编在感慨“漳水河边”历史视角、情怀担当的同时，想到更多的是，应当把这份情怀担当，传递给焦化厂所有干部职工。让所有焦化职工感受到长钢对焦化的期盼，承担起发展焦化的责任，不负老兵工留下的荣光与辉煌。 枣臻焦化 1948年2月，为配套故县铁厂一号高炉生产，陆达、耿震等人筹备（建）枣臻焦化（中国共产党建立的第一座焦化厂，距故县铁厂20km）。创业初期，老一辈焦化人夙兴夜寐，宵衣旰食，“为了解放全中国，我们立志在此创业。创业就等于吃苦，不吃苦算什么创业”。从一无所有到建起亨塞尔曼焦炉（主体设备），从屡获冶金工业部肯定，到1978年获得全国冶金工业学大庆红旗单位，枣臻焦化几十年风雨，创业多艰、奋斗以成。2005年12月9日，在长钢本部建成2座4.3米复热式捣固焦炉以后，因规模小、产能低、运输成本高等原因，枣臻焦化退出生产序列。 瑞达焦化 这是被称为“大焦化”的瑞达焦化。2002年，为配套两座1080m 3 高炉（长钢工艺装备大型化现代化的主要项目）用焦炭，两座4.3米复热式捣固焦炉开始建设，2003年4月投入运行。 这是当时国内第一座60万吨复热式捣 固焦炉，被喻为长钢调整产业结构、产品结构的“生命工程”、做大做强的“基础工程”。 刚投产时，焦炭质量、厂区环境和绿化都走在了全国的前列。按正常寿命，这两座焦炉可以服役到2030年以后。 遗憾的是： 一方面，由于国家环保排放指标越来越严苛。 另一方面，由于人员构成（技能相对较弱、缺乏焦化相关工作经验和培训，导致专业技能缺失）、管理团队（厂领导及基层管理人员缺乏专业素养）以及基础管理水平与焦炉建设者当初对焦炉的定位严重不匹配等，这两座焦炉只运行了短短13年，于2016年8月16日，退出历史舞台。
                    </div>
                </div>
            <# } #>
            <# if(theme=="type4") { #>
                <#
                    var type4_list_padding = data.type4_list_padding || "0 0 0 40px";
                    type4_list_padding = type4_list_padding.split(" ");
                    var top_padding = type4_list_padding[0];
                    var right_padding = type4_list_padding[1];
                    var bottom_padding = type4_list_padding[2];
                    var left_padding = type4_list_padding[3];
                    var type4_list_bg_color = data.type4_list_bg_color || "#f8f8f8";
                    var type4_list_light_bg_color = data.type4_list_light_bg_color || "#e2e3e4";
                    var right_title_size = data.right_title_size || 25;
                    var right_desc_size = data.right_desc_size || 12;
                    var right_img_title_size = data.right_img_title_size || 16;
                    var right_img_title_height = data.right_img_title_height || 35;
                    var right_img_bg_color = data.right_img_bg_color || "#798ca4";
                    var right_img_title_color = data.right_img_title_color || "#fff";
                    var right_arrow_color = data.right_arrow_color || "#f7941d";
                    var right_img_width, right_img_width_sm, right_img_width_xs;
                    if(data.right_img_width){
                        if(Object.prototype.toString.call(data.right_img_width)){
                            right_img_width = data.right_img_width.md;
                            right_img_width_sm = data.right_img_width.sm;
                            right_img_width_xs = data.right_img_width.xs;
                        }else{
                            right_img_width = data.right_img_width || 30;
                            right_img_width_sm = data.right_img_width_sm || 100;
                            right_img_width_xs = data.right_img_width_xs || 100;
                        }
                    }else{
                        right_img_width = 30;
                        right_img_width_sm = 100;
                        right_img_width_xs = 100;
                    }
                    var type4_bg_color = data.type4_bg_color || "#f2f5fa";
                    var mr_title_color_gl = data.mr_title_color_gl || "#0a4a9b";
                #>
                <style>
                    #{{ addonId }} .product {background: {{type4_bg_color}};position: relative;}
                    {$addon_id} .product-lei {width: 270px;position: absolute;top: 0;left: 0;background: {{type4_bg_color}};transition: all 1s;z-index: 9;padding-bottom: {{bottom_padding}};}
                    #{{ addonId }} .product-lei ul {width: auto;overflow: hidden;background: {{type4_list_bg_color}};padding: {{top_padding}} 0 0 0;}
                    #{{ addonId }} .product-lei-on {overflow: visible!important;background: {{type4_bg_color}}!important;height: auto!important;padding-bottom: 2%!important;}
                    {$addon_id} .product-lei li {overflow: hidden;vertical-align: bottom;height: 44px;line-height: 44px;transition: all 1s;padding-left: {{left_padding}};padding-right: {{right_padding}};list-style: none;}
                    #{{ addonId }} .product-lei li span {color: {{data.mr_title_color}};font-size: 16px;line-height: 44px;padding-left: 20px;cursor: pointer;}
                    @media (min-width: 1200px){#{{ addonId }} .container {width: 1170px;}}
                    #{{ addonId }} .container {padding-right: 15px;padding-left: 285px;margin-right: auto;margin-left: auto;}
                    #{{ addonId }} .product-leixq {padding-top: 30px;display: none;}
                    #{{ addonId }} .lei-pic {width: 473px;float: left;margin-right: 40px;overflow: hidden;position: relative;transition: all 1s ease 0s;}
                    #{{ addonId }} .lei-pic img {transition: all 1s ease 0s;width: 100%;}
                    #{{ addonId }} .lei-con {width: 600px;float: right;}
                    #{{ addonId }} .lei-title {color: {{mr_title_color_gl}};font-size: 35px;font-weight: bold;padding: 30px 0 20px 0;}
                    #{{ addonId }} .lei-content {color: #8193aa;font-size: 15px;line-height: 32px;}
                    #{{ addonId }} .ui-box {text-decoration: none;position: relative;vertical-align: baseline;}
                    #{{ addonId }} .more {color: {{mr_title_color_gl}};font-size: 15px;display: block;width: 128px;height: 38px;text-align: center;line-height: 38px;border: 1px solid #00306d;margin-top: 35px;}
                    #{{ addonId }} .clear {clear: both;}
                    #{{ addonId }} .product-bg2 span {color: {{data.mr_title_color_gl}}!important;}
                    #{{ addonId }} .pro-san {width: 1120px;padding-top: 45px;}
                    #{{ addonId }} .lei2-title {color: {{data.right_title_color}};font-size: {{right_title_size}}px;padding-bottom: 25px;
                        <# if( data.title_bold) { #>
                            font-weight: bold;
                        <# } #>
                    }
                    #{{ addonId }} .lei2-txt {line-height: 35px;color: {{data.right_desc_color}};font-size: {{right_desc_size}}px;padding-bottom: 50px;}
                    #{{ addonId }} .col-md-4 {width: {{right_img_width}}%;flex: 0 0 {{right_img_width}}%;max-width: {{right_img_width}}%;float: left;position: relative;min-height: 1px;padding-right: 15px;padding-left: 15px;}
                    #{{ addonId }} .lei3-li a {transition: all 1s ease 0s;}
                    #{{ addonId }} .lei3-pic img {width: 100%;}
                    #{{ addonId }} .hbtn {position: relative;box-sizing: border-box;display: inline-block;overflow: hidden;text-align: center;text-decoration: none;white-space: nowrap;z-index: 0;}
                    #{{ addonId }} .lei3-t {width: 100%;height: {{right_img_title_height}}px;line-height: {{right_img_title_height}}px;background: {{right_img_bg_color}};}
                    #{{ addonId }} .lei3-t .span {display: block;padding-left: 18px;color: {{right_img_title_color}};font-size: {{right_img_title_size}}px;}
                    #{{ addonId }} .jiantou {width: 57px;height: 100%;line-height: 35px;background: {{right_arrow_color}};position: absolute;right: 0;bottom: 0;text-align: center;}
                    #{{ addonId }} .jiantou img {animation: 3s ease 0s normal none infinite running mymove;position: absolute;left: 0;top: 0;bottom: 0;right: 0;margin: auto;}
                    #{{ addonId }} .lei3-li:hover .hbtn.hb-fill-right:before {width: 100%;height: 100%;opacity: 1;}
                    #{{ addonId }} .hbtn.hb-fill-right:before {position: absolute;background: {{right_arrow_color}};transition-duration: .3s;z-index: -1;top: 0;right: auto;bottom: auto;left: 0;width: 0;height: 100%;opacity: 1;}
                    #{{ addonId }} .product-bg2 {background: {{type4_list_light_bg_color}};color: #0a4a9b;}
                    #{{ addonId }} .product-bg2 span{color: {{data.mr_title_color_gl}}!important;font-weight:bold;}
                    #{{ addonId }} .product-lei li span:hover {background: url("https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/lei2-jian.png") no-repeat center left;color: #0a4a9b;}
                    @media (max-width: 991px) and (min-width: 768px) {
                        {$addon_id} .col-md-4 {
                            width: {{right_img_width_sm}}%;
                            float: none;
                            max-width: 100%;
                        }
                    }
                    @media (max-width: 767px){
                        {$addon_id} .product-lei {
                            width: 100%;
                            position: static;
                        }
                        {$addon_id} .container{
                            width: 100%;
                            padding-left: 15px;
                        }
                        {$addon_id} .col-md-4 {
                            width: {{right_img_width_xs}}%;
                            float: none;
                        }
                    }
                </style>

                <div class="product ">
                    <div class="product-lei " data-scroll-reveal="enter bottom over 1s and move 100px" data-scroll-reveal-id="10" style="-webkit-transform: translatey(0);transform: translatey(0);opacity: 1;-webkit-transition: -webkit-transform 1s ease-in-out 0s,  opacity 1s ease-in-out 0s;transition: transform 1s ease-in-out 0s, opacity 1s ease-in-out 0s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">
                        <ul class>
                            <li data-cid="31" class="product-bg1"><span>单线循环脱挂抱索器索道</span></li>
                            <li data-cid="30" class="product-bg1"><span>往复索道</span></li>
                            <li data-cid="29" class="product-bg1"><span>单线循环脉动索道</span></li>
                            <li data-cid="28" class="product-bg1"><span>单线循环固定抱索器索道</span></li>
                            <li data-cid="26" class="product-bg1"><span>地面缆车</span></li>
                            <li data-cid="25" class="product-bg1"><span>其他索牵引设备</span></li>
                            <li data-cid="70" class="product-bg1"><span>救援设备</span></li>
                            <li data-cid="32" class="product-bg2"><span>旅游索道</span></li>
                            <li data-cid="34" class="product-bg2"><span>滑雪索道</span></li>
                            <li data-cid="46" class="product-bg2"><span>城市交通</span></li>
                        </ul>
                </div>
                <div class="product-info container " data-scroll-reveal="enter right after 0.5s" data-scroll-reveal-id="11" style="-webkit-transform: translatex(0);transform: translatex(0);opacity: 1;-webkit-transition: -webkit-transform 0.66s ease-in-out 0.5s,  opacity 0.66s ease-in-out 0.5s;transition: transform 0.66s ease-in-out 0.5s, opacity 0.66s ease-in-out 0.5s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">
                    <div class="product-leixq right g31" style="display: block;">
                        <div class="lei2-title">单线循环脱挂抱索器索道</div>
                        <div class="lei2-txt">
                            <p style="text-indent:2em;">
                                <span style="line-height:2;">单线循环脱挂抱索器索道（简称脱挂索道），有别于传统的固定抱索器索道，在站内通过特殊装置可使吊具与钢绳脱开或挂结，从而实现吊具在站内低速运行，并在线路上高速运行。</span>
                            </p>
                            <p style="text-indent:2em;">
                                <span style="line-height:2;">脱挂索道上下车方便，自动化水平高，适应地形能力强，安全快速，运输能力大，是目前国内外索道的主流设备型式。</span>
                            </p>
                            <p style="text-indent:2em;">
                                <span style="line-height:2;">北起院成功开发研制的新型脱挂索道，适应国内旅游和滑雪市场快速发展的需求，技术成熟可靠，性价比高，已经在国内多个风景区和滑雪场得到应用。</span>
                            </p>
                            <p style="text-indent:2em;">
                                <span style="line-height:2;">目前，我们可以提供八人吊厢、六人吊厢式脱挂索道和四人、六人吊椅式脱挂索道。最高运输能力<span style="line-height:2;">单向</span>可达3200人/小时。</span>
                            </p>
                            <p style="text-indent:2em;">
                                <br>
                            </p>
                        </div>
                        <div class="row">
                              <div class="col-md-4 lei3-li " data-scroll-reveal="enter bottom after 0.5s" data-scroll-reveal-id="16" style="-webkit-transform: translatey(0);transform: translatey(0);opacity: 1;-webkit-transition: -webkit-transform 0.66s ease-in-out 0.5s,  opacity 0.66s ease-in-out 0.5s;transition: transform 0.66s ease-in-out 0.5s, opacity 0.66s ease-in-out 0.5s;-webkit-perspective: 1000;-webkit-backface-visibility: hidden;" data-scroll-reveal-initialized="true">
                                    <a href="?p=products_list2&amp;c_id=40&amp;lanmu=2" style="display: block;">
                                        <div class="lei3-pic"><img src="https://ijzt.china9.cn/images/blog/8.jpg" alt="六人吊椅脱挂索道" titel="六人吊椅脱挂索道" style="height: 202.006px;"><br></div>
                                        <div class="lei3-t  hbtn hb-fill-right">
                                            <span class="span">六人吊椅脱挂索道</span>
                                            <div class="jiantou"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/jiantou.png" alt=""></div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
            <# } #>
            <# if(theme=="type5") {
                var content_text_w = Number(data.type5_content_img_w) + Number(data.type5_content_text_m);

                // 页面导航是否和日期等同在一行
                var type5_home_title_inline = "type5_home_title_inline" in data ? data.type5_home_title_inline == 0 ? data.type5_home_title_inline : 1 : 1;
                var type5_position = type5_home_title_inline ? "absolute" : "static";
                // 导航标题字数限制
                var type5_home_title_limit_open = "type5_home_title_limit_open" in data ? data.type5_home_title_limit_open == 0 ? data.type5_home_title_limit_open : 1 : 0;
                var type5_home_title_limit = data.type5_home_title_limit || 10;
                #>
                <style type="text/css">
                    #{{ addonId }} * {
                        margin: 0;
                    }
                    #{{ addonId }} .content-box hr {
                        border-top-color: {{data.type5_titleL_color}};
                    }
                    #{{ addonId }} .content-box .title {
                        text-align:{{ data.type5_title_align }};
                        font-size: {{data.type5_font_size}}px;
                        color: {{ data.type5_title_color }};
                        margin-bottom: 20px;
                        <# if( data.title_bold) { #>
                            font-weight: bold;
                        <# } #>
                    }
                    #{{ addonId }} .content-box .title .line {
                        display: inline-block;
                        width: {{ data.type5_title_l_w }}px;
                        height: {{ data.type5_title_l_h }}px;
                        background-color: {{ data.type5_title_color }};
                        margin: 0 {{ data.type5_title_l_m }}px;
                        vertical-align: middle;
                    }
                    #{{ addonId }} .content-box .time-box {
                        position: relative;
                        margin-bottom: 20px;
                    }
                    #{{ addonId }} .content-box .time-info {
                        text-align: {{ data.type5_date_align }};
                        display: block;
                        padding-right: 10px;
                        color: {{ data.type5_date_color }};
                        font-size: {{data.type5_font_size_date}}px;
                    }
                    #{{ addonId }} .content-box .home-menu {
                        position:{{type5_position}};
                        {{ data.type5_home_align }}: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                        display: flex;
                        align-items: center;
                        color: {{ data.type5_date_color }};
                        font-size: {{data.type5_font_size_date}}px;
                    }
                    #{{ addonId }} .content-box .home-menu .icon {
                        margin-right: 5px;
                        width: {{data.type5_font_size_date}}px;
                    }
                    #{{ addonId }} .content-box .home-menu span {
                        color: {{ data.type5_home_color }};
                    }
                    #{{ addonId }} .content-box .content-info {
                        margin-top: {{ data.type5_content_top }}px;
                        display: flex;
                        justify-content: space-between;
                    }
                    #{{ addonId }} .content-box .content-info .content-img {
                        width: {{ data.type5_content_img_w }}px;
                    }
                    #{{ addonId }} .content-box .content-info .content-img img {
                        width: 100%;
                    }
                    #{{ addonId }} .content-box .content-info .content-text {
                        <# if(data.type5_content_img == 1) { #>
                            width: 100%;
                        <# } else { #>
                            width: calc(100% - {{ content_text_w }}px);
                        <# } #>
                    }
                    /*翻页 布局01*/
                    #{{ addonId }} .btnBox {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBox a {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColor}};
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBox a:hover {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBoxt a {
                        width: 50%;
                        height: 40px;
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: left;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:hover {
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:nth-child(2){  text-align:right; }

                    /*翻页 布局02*/
                    #{{ addonId }} .page02-btnBox {
                        margin-top: {{ data.type5_page_top }}px;
                    }
                    #{{ addonId }} .page02-btnBox a {
                        color: {{ data.pageColor }};
                        font-size: {{ data.type5_page_font_size }}px;
                        line-height: {{ data.type5_page_lineHeight }}px;
                        display: flex;
                        align-items: center;
                    }
                    #{{ addonId }} .page02-btnBox a img {
                        margin-right: 5px;
                        display: none;
                        width: {{ data.type5_page_font_size }}px;
                    }
                    #{{ addonId }} .page02-btnBox a .normal {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox a:hover {
                        color: {{ data.pageColorhover }};
                    }
                    #{{ addonId }} .page02-btnBox a:hover .normal {
                        display: none;
                    }
                    #{{ addonId }} .page02-btnBox a:hover .hover {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox .page-line {
                        width: 100%;
                        border-top: 1px {{ data.type5_page_line }} {{ data.type5_page_line_color }};
                    }
                    @media (max-width: 991px) {
                        #{{ addonId }} .content-box .home-menu {
                            position: inherit;
                            margin-bottom: 10px;
                        }
                        #{{ addonId }} .content-box .content-info {
                            flex-direction: column;
                            align-items: center;
                        }
                        #{{ addonId }} .content-box .content-info .content-img {
                            max-width: 100%;
                        }
                        #{{ addonId }} .content-box .content-info .content-text {
                            width: 100%;
                            margin-top: {{ data.type5_content_text_m }}px;
                        }
                    }
                </style>
                <div class="content-box">
                    <h2 class="title">
                        <# if(data.type5_title_l != 1) { #>
                            <span class="line"></span>
                        <# } #>
                        示例产品标题
                        <# if(data.type5_title_l != 1) { #>
                            <span class="line"></span>
                        <# } #>
                    </h2>
                    <div class="time-box">
                        <p class="time-info">发布时间：2021-03-11&nbsp;&nbsp;&nbsp;浏览量：42&nbsp;&nbsp;&nbsp;作者：admin</p>
                        <# if (data.type5_home != 1) { #>
                            <div class="home-menu"><img src=\'{{ data.type5_home_icon }}\' class="icon" /> <p> 首页 > {{data.type5_home_text}} >
                            <# if(type5_home_title_limit_open){ #>
                                <# var title = "产品标题"; title = title.slice(0, type5_home_title_limit) #>
                                <span>{{title}}...</span>
                            <# }else{ #>
                            <span>产品标题</span>
                            <# } #>
                            </p></div>
                        <# } #>
                    </div>
                    <hr />
                    <div class="content-info">
                        <#if (data.type5_content_img != 1) { #>
                            <div class="content-img">
                                <img src="https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg" />
                            </div>
                        <# } #>
                        <div class="content-text">
                            示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文，示例产品正文。
                        </div>
                    </div>
                    <# if(data.page_button==0) { #>
                        <# if(data.type5_page_style == "page02") { #>
                            <div class="page02-btnBox">
                                <a href="#">
                                    <img src=\'{{data.page_prev}}\' class="normal" />
                                    <img src=\'{{data.page_prev_hover}}\' class="hover" />
                                    {{data.up_page_text}}上一条产品标题
                                </a>
                                <div class="page-line"></div>
                                <a href="#">
                                    <img src=\'{{data.page_next}}\' class="normal" />
                                    <img src=\'{{data.page_next_hover}}\' class="hover" />
                                    {{data.next_page_text}}下一条产品标题
                                </a>
                            </div>
                        <# } else { #>

                                <# if(data.page_dttitle==1) { #>
                                    <div class="btnBoxt">
                                        <a href="#">{{data.up_page_text}}:标题名称标题标题</a>
                                        <a href="#">{{data.next_page_text}}:标题名称标题标题</a>
                                    </div>
                                <# }else{ #>
                                    <div class="btnBox">
                                        <a href="#">{{data.up_page_text}}</a>
                                        <a href="#">{{data.next_page_text}}</a>
                                    </div>
                                <# } #>

                        <# } #>
                    <# } #>
                </div>
            <# } #>
            <# if(theme=="type6") {
                    var footer_title_font_size = data.footer_title_font_size || 16;
                    var footer_title_color = data.footer_title_color || "";
                    var footer_label_font_size = "", footer_label_font_size_sm = "14", footer_label_font_size_xs = "14";
                    if(data.footer_label_font_size){
                        if(Object.prototype.toString.call(data.footer_label_font_size) === "[object Object]"){
                            footer_label_font_size = data.footer_label_font_size.md;
                            footer_label_font_size_sm = data.footer_label_font_size.sm;
                            footer_label_font_size_xs = data.footer_label_font_size.xs;
                        }else{
                            footer_label_font_size = data.footer_label_font_size_md;
                            footer_label_font_size_sm = data.footer_label_font_size_sm;
                            footer_label_font_size_xs = data.footer_label_font_size_xs;
                        }
                    }
                    var footer_label_color = data.footer_label_color || "";
                #>
                <style type="text/css">

                    #{{ addonId }} .fa{font-family: "Font Awesome 5 Free";}
                    #{{ addonId }} .container_content {
                        clear: both;
                    }
                    #{{ addonId }} .postbody{ color:{{{ data.color1615443385649 }}};}

                    #{{ addonId }} p {
                        margin:0px;
                    }
                    #{{ addonId }} .fa-angle-right:before {
                        content: "\f105";
                    }
                    #{{ addonId }} .fa-angle-left:before {
                        content: "\f104";
                    }
                    #{{ addonId }} .clear {
                        clear: both;
                    }
                    #{{ addonId }} .postContent {
                        margin-bottom: 40px;
                        padding-top: 40px;
                        max-width: 1200px;
                        margin:0 auto;
                    }
                    #{{ addonId }} .team.mlistpost .listWrap {
                        display: none;
                    }
                    #{{ addonId }} #postWrapper::after {
                        content: "";
                        display: block;
                        clear: both;
                    }
                    #{{ addonId }} .postInfo .title {

                        color: {{{ data.color1615452944411 }}};
                        font-size: {{{ data.font_size }}}px;
                        transition: all .3s ease-out 0s;
                        text-align:{{{data.select1615452416171}}};
                        <# if( data.title_bold) { #>
                            font-weight: bold;
                        <# } #>
                    }

                    #{{ addonId }} .postInfo .subtitle {
                        color: {{{ data.intro_color06 }}};
                        font-size: {{{ data.intro_fontsize06 }}}px;
                        margin-top: 4px;
                        transition: all .3s ease-out 0s;
                        padding-top: 4px;
                        text-align:{{{data.select1615452416171}}};

                    }
                    #{{ addonId }} .team .postInfo .usetdate{
                        display: none;
                    }
                    #{{ addonId }} .postInfo .usetdate {
                        font-size: 13px;
                        line-height: 24px;
                    }
                    #{{ addonId }} .postInfo .description {
                        margin-top: 20px;
                        font-size: 13px;
                        line-height: 24px;
                        margin-bottom: 24px;
                        color: #888;
                    }
                    #{{ addonId }} .post .postbody {
                        position: relative;
                        z-index: 0;
                    }
                    #{{ addonId }} .postbody hr {
                        margin: 20px 0;
                        border: none;
                        border-top: 1px dotted rgba(170, 170, 170, 0.2);
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev {
                        left: 0px;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev, #{{ addonId }} .team.mlistpost .tabBtn .post-next {
                        width: 42px;
                        height: 100px;
                        position: fixed;
                        z-index: 20;
                        top: 50%;
                        transform: translateY(-50%);
                        transition: opacity 0.36s ease-out, visibility 0.36s ease-out;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap {
                        border-color: #909744;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap {
                        overflow: hidden;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap {
                        border-left: 0;
                        left: 100%;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap,#{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap {
                        position: absolute;
                        box-sizing: border-box;
                        z-index: 10;
                        height: 100%;
                        border: 3px solid #909744;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap,#{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap {
                        width: 170px;
                        opacity: 0;
                        visibility: hidden;
                    }
                    #{{ addonId }} .item_tags > a, .container_category > a, .imagelink .owl-nav .owl-prev, .postSlider .owl-nav .owl-prev, .mlist .owl-nav .owl-prev, .postSlider .owl-nav .owl-next, .mlist .owl-nav .owl-next,.postSlider .owl-nav .owl-prev:hover .iconfont, .mlist .owl-nav .owl-prev:hover .iconfont, .imagelink .owl-nav .owl-next:hover .iconfont, .postSlider .owl-nav .owl-next:hover .iconfont, .mlist .owl-nav .owl-next:hover .iconfont,.team .content_list .item_block .item_box .item_wrapper, .news.mlistpost .content_list .item_block .item_box .item_wrapper,.team.mlistpost .tabBtn .post-prev .img-wrap, .team.mlistpost .tabBtn .post-next .img-wrap,.team.mlistpost .tabBtn .post-prev .infor-wrap,.team.mlistpost .tabBtn .post-next .infor-wrap, .postSlider .tab_button .item_img img, body #header, body #headTop #logo img, .imagelink .content_list .item_block .item_box {
                            transition: all 0.36s ease;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap {
                        padding-left: 20px;
                        left: 100%;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap {
                        position: absolute;
                        top: 50%;
                        z-index: 10;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap div,#{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap div {
                        padding-top: 70px;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap div,#{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap div {
                        height: 100%;
                        background-position: center center;
                        background-size: cover;
                        box-sizing:unset!important;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap img,#{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap img {
                        display: none;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap:after, #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap:after {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap .title,#{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap .title {
                        width: 70%;
                        display: block;
                        font-size: 16px;
                        line-height: 20px;
                        color: #fff;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap .title, #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, #{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap .title, #{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,#{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        width: 80%;
                        display: block;
                        line-height: 18px;
                        font-size: 14px;
                        color: #fff;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .details {
                        left: 0;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .details,#{{ addonId }} .team.mlistpost .tabBtn .post-next .details {
                        position: absolute;
                        height: 100%;
                        width: 42px;
                        background: {{{ data.pageBgColor }}};
                        border: 1px solid {{{ data.pageBorderColor }}};
                        box-sizing: border-box;
                        transition: all 0.3s ease-out 0s;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .details:before {
                        content: "\f104";
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .details:before,#{{ addonId }} .team.mlistpost .tabBtn .post-next .details:before {

                        font-family: "Font Awesome 5 Free";
                        font-variant: normal;
                        text-transform: none;
                        font-size: 14px;
                        line-height: 1;
                        color: inherit;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        font-size: 24px;
                        color: {{{ data.pageColor }}};
                        transform: translate(-50%, -50%);
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next {
                        right: 0px;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev,#{{ addonId }} .team.mlistpost .tabBtn .post-next {
                        width: 42px;
                        height: 100px;
                        position: fixed;
                        z-index: 20;
                        top: 50%;
                        transform: translateY(-50%);
                        transition: opacity 0.36s ease-out, visibility 0.36s ease-out;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap {
                        overflow: hidden;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap {
                        right: 100%;
                        border-right: 0;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap, #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap {
                        position: absolute;
                        box-sizing: border-box;
                        z-index: 10;
                        height: 100%;
                        border: 3px solid {{{ data.pageBorderColorhover }}};
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .img-wrap:after, #{{ addonId }} .team.mlistpost .tabBtn .post-next .img-wrap:after {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap {
                        right: 100%;
                        padding-left: 20px;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev .infor-wrap,#{{ addonId }} .team.mlistpost .tabBtn .post-next .infor-wrap {
                        position: absolute;
                        top: 50%;
                        z-index: 10;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .details {
                        right: 0;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next .details:before {
                        content: "\f105";
                    }

                    #{{ addonId }} .team.mlistpost .post-prev:hover .infor-wrap, #{{ addonId }} .team.mlistpost .post-prev:hover .img-wrap,#{{ addonId }} .team.mlistpost .post-next:hover .infor-wrap,#{{ addonId }} .team.mlistpost .post-next:hover .img-wrap {
                        opacity: 1;
                        visibility: visible;
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-prev:hover .details:before,#{{ addonId }} .team.mlistpost .tabBtn .post-next:hover .details:before {

                        color: {{{ data.pageColorhover }}};
                    }
                    #{{ addonId }} .team.mlistpost .tabBtn .post-next:hover .details, #{{ addonId }} .team.mlistpost .tabBtn .post-prev:hover .details {
                        border-color: {{{ data.pageBorderColorhover }}};
                        background-color: {{{ data.pageBgColorhover }}};
                    }

                    #{{ addonId }} .team.mlistpost .post-prev:hover .details,#{{ addonId }} .team.mlistpost .post-next:hover .details {
                        border-color: {{{ data.pageBorderColorhover }}};
                        background: {{{ data.pageBgColorhover }}};
                    }

                    #{{ addonId }} .fl {
                        float: left;
                    }
                    #{{ addonId }} .fr {
                        float: right;
                    }
                    #{{ addonId }} p {
                        margin:0;
                    }

                    #{{ addonId }} .timg {
                        width:{{{ data.timg_width06 }}}px;
                    }
                    #{{ addonId }} .sytext {
                        width:calc( 100% - {{{data.timg_width06}}}px - 20px );
                    }
                    #{{ addonId }} .xlsb{
                        font-size:{{footer_label_font_size}}px;
                        color: {{footer_label_color}};
                    }

                     @media (max-width: 991px) {
                        #{{ addonId }} .timg {
                            width:100%;
                        }
                        #{{ addonId }} .sytext {
                            width:100%;
                        }
                        #{{ addonId }} .conTabBtn{display:none;}
                        #{{ addonId }} .xlsb{font-size:{{footer_label_font_size_sm}}px;}
                    }
                    @meida (max-width: 767px){
                        #{{ addonId }} .xlsb{font-size:{{footer_label_font_size_xs}}px;}
                    }
                </style>
                <div class="content">
                    <div class="mlistpost team module" data-thumb="">
                        <div class="module_container">
                            <div class="container_content">
                                <div class="content_wrapper">
                                    <div id="postWrapper">
                                        <div class="postContent">
                                            <div class="postInfo">
                                                <div class="mainInfor">
                                                    <p class="title">鈴木 幸典</p>
                                                                <p class="subtitle">中国普洱茶终身成就大师 </p>
                                                                <p class="usetdate">
                                                        <span class="year">2017</span><i class="time-connect">-</i><span class="m">03</span><i class="time-connect">-</i><span class="d">07</span>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="postbody">
                                                <hr><p><br></p>

                                                <div class="firstRow">
                                                    <div class="timg fl" >
                                                        <p><img src="//resources.jsmo.xin/templates/upload/5124/201711/1509945280760.png"></p><p><br></p>
                                                        <p><strong><span style="font-size: {{footer_title_font_size}}px;color: {{footer_title_color}}">鈴木 幸典</span></strong></p>
                                                        <p class="xlsb">武野绍鸥为茶坛名人之一，（1502年-1555年）</p><p><br></p>
                                                    </div>
                                                    <div class="sytext fr">
                                                        普洱茶是一种阳光之茶，是一种有生命的茶是一种不断变化的茶，我做了大半辈子的茶，还有许多不懂的地方需要学习。</p><p>并不是我有多大的本事，而是天时地利，云南大叶种本身茶质好，内含物非常丰富，喝同样一口茶，喝到嘴里的东西就多。</p><p>普洱茶养胃、防‘三高’等功效，要让不同的人群知道，让越来越多的人喜欢上普洱茶。</p><p><br></p><p>这是一个奇迹的时代，一个艺术的时代，一个挥金如土的时代，也是一个充满嘲讽的时代。</p><p>狩猎帽子起源于14世纪的英国北部和意大利南部的部分地区，之后又被英国殖民者带去了美国。</p><p><br></p><p>武野绍鸥为茶坛名人之一，（1502年-1555年）。</p><p><br></p><p>饮茶鼻祖村田珠光曾对武野先生有过很大的影响。武野绍鸥既是千利休的老师，也是日本茶道创始人之一。</p><p><br></p><p>即使说活跃在安土桃山时代的茶人都是他的弟子的话也决不会过言。
                                                    </div>
                                                    <div class="clear"></div>
                                                </div>

                                            </div>
                                            <# if(data.page_button==0) { #>
                                                <div class="conTabBtn tabBtn">
                                                    <a href="//mo005-5124.mo5.line1.uemo.net/list/post/242399/" class="post-prev">
                                                        <div class="img-wrap">
                                                            <div style="background-image: url(//resources.jsmo.xin//templates/upload/5124/201711/1509959417339.jpg)"></div>
                                                            <img src="//resources.jsmo.xin//templates/upload/5124/201711/1509959417339.jpg" alt="">
                                                        </div>
                                                        <div class="infor-wrap">
                                                            <span class="title">高碰来</span>
                                                            <span class="subtitle">安溪乌龙茶制作技艺代表性传承人</span>
                                                        </div>
                                                        <i class="details fa fa-angle-left"></i>
                                                        <div class="tabMask"></div>
                                                    </a>
                                                    <a href="//mo005-5124.mo5.line1.uemo.net/list/post/232719/" class="post-next">
                                                        <div class="img-wrap">
                                                            <div style="background-image: url(//resources.jsmo.xin//templates/upload/5124/201711/1509934202759.png)"></div>
                                                            <img src="//resources.jsmo.xin//templates/upload/5124/201711/1509934202759.png" alt="">
                                                        </div>
                                                        <div class="infor-wrap">
                                                            <span class="title">武野绍鸥</span>
                                                            <span class="subtitle">日本茶道创始人之一</span>
                                                        </div>
                                                        <i class="details fa fa-angle-right"></i>
                                                        <div class="tabMask"></div>
                                                    </a>
                                                </div>
                                            <# } #>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            <# } #>
            <# if(theme=="type7") {
                // 右侧宽度
                var type7_right_width = data.type7_right_width || 25;
                // 左侧封面图
                var img_width_type7 = data.img_width_type7 || 612;
                var img_height_type7 = data.img_height_type7 || 410;
                var img_padding_bottom_type7 = img_padding_bottom_type7 || 100;
                var img_margin_type7 = img_margin_type7 || "40px auto 0 auto";
                var img_fill_mode_type7 = data.img_fill_mode_type7 || "auto";
                // 正文
                var content_font_size_cover_type07 = data.content_font_size_cover_type07 || 0;
                var content_font_size_type07 = data.content_font_size_type07 || 12;
                var content_font_color_type07 = data.content_font_color_type07 || "#666";
                var content_line_height_type07 = data.content_line_height_type07 || 24;
                var content_padding_top_type07 = data.content_padding_top_type07 || 10;
                var content_margin_type07 = data.content_margin_type07 || "0 4% 0 4%";
                // 左侧宽度
                var type7_left_width = data.type7_left_width || 66
                // 标题
                var title_font_size_type07 = data.title_font_size_type07 || 16;
                var title_font_color_type07 = data.title_font_color_type07 || "#444"
                var title_line_height_type07 = data.title_line_height_type07 || 20;
                var label1_font_size_type07 = data.label1_font_size_type07 || 12;
                // 标签一
                var label1_font_size_type07 = data.label1_font_size_type07 || 12;
                var label1_font_color_type07 = data.label1_font_color_type07 || "#999";
                var label1_line_height_type07 = data.label1_line_height_type07 || 14;
                var label1_margin_top_type07 = data.label1_margin_top_type07 || 4;
                // 时间
                var time_font_size_type07 = data.time_font_size_type07 || 13;
                var time_font_color_type07 = data.time_font_color_type07 || "#666";
                var time_line_height_type07 = data.time_line_height_type07 || 24;
                var time_margin_top_type07 = data.time_margin_top_type07 || 0;
                // 简介
                var des_font_size_cover_type07 = data.des_font_size_cover_type07 || 0;
                var des_font_size_type07 = data.des_font_size_type07 || 13;
                var desc_font_color_type07 = data.desc_font_color_type07 || "#888";
                var desc_line_height_type07 = data.desc_line_height_type07 || 24;
                var box_margin_type07 = data.box_margin_type07 || "20px 0 24px 0";
                // 导航
                var type7_nav_padding = data.type7_nav_padding || "20px 20px 20px 20px";
                var nav_font_size_type07 = data.nav_font_size_type07 || 12;
                var nav_font_color_type07 = data.nav_font_color_type07 || "#666";
                // 店铺按钮
                var button_item_type7 = data.button_item_type7 || [];
                var open_way_type7 = data.open_way_type7 || "_blank";
                var button_height_type7 = data.button_height_type7 || "40";
                var button_padding_type7 = data.button_padding_type7 || "0 30px 0 30px";
                var button_border_color_type7 = data.button_border_color_type7 || "#dbdbdb";
                var button_bg_color_type7 = data.button_bg_color_type7 || "#fff";
                var button_color_type7 = data.button_color_type7 || "#555";
                var button_margin_top_type7 = data.button_margin_top_type7 || 20;
                // 相关内容
                var about_size_type7 = data.about_size_type7 || 14;
                var about_color_type7 = data.about_color_type7 || "#666";
                var about_padding_type7 = data.about_padding_type7 || "30px 0 30px 0";
                var button_about_height_type7 = data.button_about_height_type7 || "26";
                var button_about_border_color_type7 = data.button_about_border_color_type7 || "rgba(170,170,170,0.2)";
                var button_about_bg_color_type7 = data.button_about_bg_color_type7 || "#fff";
                var button_about_color_type7 = data.button_about_color_type7 || "#666";
                var button_about_margin_right_type7 = data.button_about_margin_right_type7 || 10;
                var button_about_padding_type7 = data.button_about_padding_type7 || "0 14px 0 14px";
                var button_about_margin_bottom_type7 = data.button_about_margin_bottom_type7 || 30;
                var about_list_col_type7 = data.about_list_col_type7 || 2;
                var about_list_col_margin_type7 = data.about_list_col_margin_type7 || "0 10px 20px 0";
                var btn_margin = about_list_col_margin_type7.split(" ");
                var btn_top = btn_margin[0] || 0;
                var btn_right = btn_margin[1] || 0;
                var btn_bottom = btn_margin[2] || 0;
                var btn_left = btn_margin[3] || 0;
                var about_list_col_border_color_type7 = data.about_list_col_border_color_type7 || "rgba(170,170,170,0.2)";
                var about_list_title_padding_type7 = data.about_list_title_padding_type7 || "20px 5px 20px 5px";
                var about_list_title_color_type7 = data.about_list_title_color_type7 || "#666"
                var about_list_title_size_type7 = data.about_list_title_size_type7 || 12;
                // 小屏配置
                var type7_small_screen_box_margin = data.type7_small_screen_box_margin || "20px 2% 0 4%";
                var box_margin = type7_small_screen_box_margin.split(" ");
                var box_top = box_margin[0] || 0;
                var box_right = box_margin[1] || 0;
                var box_bottom = box_margin[2] || 0;
                var box_left = box_margin[3] || 0;
                // 标题
                var type7_small_screen_title_size = type7_small_screen_title_size || 16;
                var type7_small_screen_title_line_height = type7_small_screen_title_line_height || 20;
                var type7_small_screen_title_margin_top = type7_small_screen_title_margin_top || 48;
                var type7_small_screen_title_color = type7_small_screen_title_color || "#444";
                var about_list_subtitle_size_type7 = about_list_subtitle_size_type7 || 12;
                var about_list_subtitle_color_type7 = about_list_subtitle_color_type7 || "#666";
                var about_list_subtitle_margin_top_type7 = about_list_subtitle_margin_top_type7 || "";
                // 标签一
                var type7_small_screen_label1_size = data.type7_small_screen_label1_size || 12;
                var type7_small_screen_label1_color = data.type7_small_screen_label1_color || "#999";
                var type7_small_screen_label1_line_height = data.type7_small_screen_label1_line_height || "14";
                var type7_small_screen_label1_margin_top = data.type7_small_screen_label1_margin_top || "4";
                // 简介
                var type7_small_screen_des_font_size_cover = data.type7_small_screen_des_font_size_cover || "0";
                var type7_small_screen_des_font_size = data.type7_small_screen_des_font_size || 14;
                var type7_small_screen_desc_line_height = data.type7_small_screen_desc_line_height || "";
                var type7_small_screen_des_color = data.type7_small_screen_des_color || "#666";
                var type7_small_screen_desc_margin_top = data.type7_small_screen_desc_margin_top || 20;
                // 店铺跳转链接
                var type7_small_screen_button_height = data.type7_small_screen_button_height || 48;
                var type7_small_screen_button_padding = data.type7_small_screen_button_padding || "0 32px 0 32px";
                var type7_small_screen_button_border_color = data.type7_small_screen_button_border_color || "#d6d6d6";
                var type7_small_screen_button_bg_color = data.type7_small_screen_button_bg_color || "#fff";
                var type7_small_screen_button_color = data.type7_small_screen_button_color || "#666";
                var type7_small_screen_button_margin = data.type7_small_screen_button_margin || "16px 0 16px 0";
                // 正文
                var type7_small_screen_content_font_size = data.type7_small_screen_content_font_size || 13;
                var type7_small_screen_content_font_size_cover = data.type7_small_screen_content_font_size_cover || 0;
                var type7_small_screen_content_color = data.type7_small_screen_content_color || "#666";
                var type7_small_screen_content_line_height = data.type7_small_screen_content_line_height || "";
                var type7_small_screen_content_line_height = data.type7_small_screen_content_line_height || "";
                // 相关内容
                var type7_small_screen_about_size = data.type7_small_screen_about_size || 14;
                var type7_small_screen_about_margin_top = data.type7_small_screen_about_margin_top || 20;
                var type7_small_screen_button_about_margin = data.type7_small_screen_button_about_margin || "32px 3px 32px 3px";
                var type7_small_screen_about_color = data.type7_small_screen_about_color || "#444";
                // 分类标签
                var type7_small_screen_button_about_border_color = data.type7_small_screen_button_about_border_color || "#f0f0f0";
                var type7_small_screen_button_about_bg_color = data.type7_small_screen_button_about_bg_color || "#f0f0f0";
                var type7_small_screen_button_about_color = data.type7_small_screen_button_about_color || "#666";
                var type7_small_screen_button_about_padding = data.type7_small_screen_button_about_padding || "4px 12px 4px 12px";
                // 列表
                var type7_small_screen_about_list_col = data.type7_small_screen_about_list_col || 2;
                var type7_small_screen_about_list_col_margin = data.type7_small_screen_about_list_col_margin || "1% 1% 1% 1%";
                var list_margin = type7_small_screen_about_list_col_margin.split(" ");
                var list_top = list_margin[0] || 0;
                var list_right = list_margin[1] || 0;
                var list_bottom = list_margin[2] || 0;
                var list_left = list_margin[3] || 0;
                var type7_small_screen_about_list_col_border_color = data.type7_small_screen_about_list_col_border_color || "rgba(170,170,170,0.2)";
                // 标题
                var type7_small_screen_about_list_title_padding = data.type7_small_screen_about_list_title_padding || "20px 20px 20px 20px";
                var type7_small_screen_about_list_title_color = data.type7_small_screen_about_list_title_color || "#666";
                var type7_small_screen_about_list_title_size = data.type7_small_screen_about_list_title_size || 12;
                // 副标题
                var type7_small_screen_about_list_subtitle_size = data.type7_small_screen_about_list_subtitle_size || 12;
                var type7_small_screen_about_list_subtitle_color = data.type7_small_screen_about_list_subtitle_color || "#666";
                var type7_small_screen_about_list_subtitle_margin_top = data.type7_small_screen_about_list_subtitle_margin_top || "";
                #>
                <style>
                #{{ addonId }} html,body{font:12px Arial,"微软雅黑";color:#666;width:100%;height:100%;-webkit-font-smoothing:antialiased;-webkit-text-size-adjust:none;-webkit-tap-highlight-color:transparent;-webkit-overflow-scrolling:touch;overflow-scrolling:touch}
                #{{ addonId }} .ff_pageTarget .container_target a:last-child{color:#909744;text-decoration:none}
                #{{ addonId }} .ff_pageTarget .fa-angle-right{padding:0 10px}
                #{{ addonId }} .fa-angle-right:before{content:">"}
                #{{ addonId }} body{overflow-x:hidden}
                #{{ addonId }} a{color:#666;text-decoration:none}
                #{{ addonId }} a.active{color:#333}
                #{{ addonId }} img{max-width:100%}
                #{{ addonId }} .clear{clear:both}
                #{{ addonId }} .sitecontent #pageTarget.module .module_container{position:relative;padding:{{type7_nav_padding}}px;font-size: {{nav_font_size_type07}}px;color: {{nav_font_color_type07}}}
                #{{ addonId }} .sitecontent #pageTarget.module .module_container:before{content:"";border-bottom:1px solid #f2f2f2;width:100%;height:1px;position:absolute;margin:0 auto;left:0;top:54px}
                #{{ addonId }} .sitecontent #postLeft{width:66%;float:left}
                #{{ addonId }} .sitecontent #postLeft #postSlider{width:100%;height:0;}
                #{{ addonId }} *{margin:0;padding:0;list-style-type:none}
                <# if(img_fill_mode_type7 === "auto"){ #>
                    #{{addonId}} #owl-demo img{border:0 none;width:100%;height:auto}
                <# } else { #>
                    #owl-demo img{border:0 none;width:100%;height:100%;object-fit: {{img_fill_mode_type7}};}
                <# } #>
                #{{ addonId }} #owl-demo{position:relative;width:{{img_width_type7}}px;height:{{img_height_type7}}px;padding-bottom:{{img_padding_bottom_type7}}px;box-sizing: content-box;max-width: 100%;margin:{{img_margin_type7}};}
                #{{ addonId }} #owl-demo .li1{width:{{img_width_type7}}px;height:{{img_height_type7}}px;}
                #{{ addonId }} #owl-demo .li1 .txt{bottom:0}
                #{{ addonId }} #owl-demo .li1 h3{padding:0 25px;font-size:28px}
                #{{ addonId }} #owl-demo p{margin-top:4px;padding:0 25px 5px}
                #{{ addonId }} #owl-demo a{color:#fff;text-decoration:none}
                #{{ addonId }} #owl-demo li:hover .txt{bottom:0}
                #{{ addonId }} .owl-pagination{position:absolute;left:0;bottom:0;width:100%;height:80px;text-align:center}
                #{{ addonId }} .owl-page{position:relative;display:inline-block;width:100px;height:60px;margin:0 5px;vertical-align:middle;overflow:hidden}
                #{{ addonId }} .owl-page img{width:100%;height:100%}
                #{{ addonId }} .owl-pagination .active{width:100px;height:60px}
                #{{ addonId }} .owl-pagination span{position:absolute;left:0;top:0;width:100px;height:60px;_background-image:none}
                #{{ addonId }} .owl-pagination .active span:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;border:1px solid {{{ data.type7_theme_color }}};box-sizing:border-box}
                #{{ addonId }} .owl-buttons div{position:absolute;top:50%;width:50px;height:50px;margin-top:-90px;text-indent:19px;background-color:#CCCCCC;transition:background-position 0.2s ease-out 0s;color:#FFFFFF;line-height:50px;font-size:20px;font-weight:600}
                #{{ addonId }} .owl-prev{left:-60px;background-position:0 0}
                #{{ addonId }} .owl-next{right:-60px;background-position:right 0}
                #{{ addonId }} .owl-prev:hover{background-position:-53px 0}
                #{{ addonId }} .owl-next:hover{background-position:-122px 0}
                #{{ addonId }} .sitecontent #postLeft .postbody{width:100%;padding-top:{{content_padding_top_type07}}px;position:relative;z-index:0;margin: {{content_margin_type07}};color: {{content_font_color_type07}};line-height: {{content_line_height_type07}}px;}
                <# if(content_font_size_cover_type07){ #>
                    #{{addonId}} .sitecontent #postLeft .postbody *{
                        font-size: {{content_font_size_type07}}px!important;
                    }
                <# } #>
                #{{ addonId }} .sitecontent #postRight{width:{{type7_right_width}}%;float:right}
                #{{ addonId }} .sitecontent #postRight .postInfo{padding-top:40px}
                #{{ addonId }} .sitecontent #postRight .postInfo .title{color:{{title_font_color_type07}};font-size:{{title_font_size_type07}}px;transition:all .3s ease-out 0s;line-height:{{title_line_height_type07}}px;
                    <# if( data.title_bold) { #>
                        font-weight: bold;
                    <# } #>
                }
                #{{ addonId }} .sitecontent #postRight .postInfo .subtitle{color:{{title_line_height_type07}};font-size:{{label1_font_size_type07}}px;margin-top:{{label1_margin_top_type07}}px;transition:all .3s ease-out 0s;line-height:{{label1_line_height_type07}}px;}
                #{{ addonId }} .sitecontent #postRight .postInfo .description{margin:{{box_margin_type07}}px;font-size:{{des_font_size_type07}}px{{des_font_size_cover_type07 ? "!important" : ""}};line-height:{{desc_line_height_type07}}px;margin-bottom:24px;color:{{desc_font_color_type07}};}
                #{{ addonId }} .sitecontent #postRight .postInfo .usetdate{font-size:{{time_font_size_type07}}px;line-height:{{time_line_height_type07}}px; color: {{time_font_color_type07}}; margin-top: {{time_margin_top_type07}};}
                #{{ addonId }} .sitecontent #postRight .postInfo .description a{transition:all 0.6s cubic-bezier(0.215,0.61,0.355,1) 0s;border:1px solid {{button_border_color_type7}};background-color:{{button_bg_color_type7}};color:{{button_color_type7}};height:{{button_height_type7}}px;padding:{{button_padding_type7}};line-height:{{button_height_type7}}px;text-align:center;display:inline-block;margin-top:{{button_margin_top_type7}}px;}
                #{{ addonId }} .sitecontent #postRight .postInfo .description a:hover{background-color:{{{ data.type7_theme_color }}};border-color:{{{ data.type7_theme_color }}};color:#fff}
                #{{ addonId }} .sitecontent #postRight .listContent_post h3{font-size:{{about_size_type7}}px;padding:{{about_padding_type7}};font-weight: bold;color: {{about_color_type7}};}
                #{{ addonId }} .sitecontent #postRight .listContent_post > .item_tags{clear:both;height:28px;padding:0;margin-bottom:{{button_about_margin_bottom_type7}}px;border-top:none;}
                #{{ addonId }} .sitecontent #postRight .listContent_post > .item_tags > a{float:left;margin-right:{{button_about_margin_right_type7}}px;padding:{{button_about_padding_type7}};line-height:{{button_about_height_type7}}px;border:1px solid {{button_about_border_color_type7}};background: {{button_about_bg_color_type7}};color: {{button_about_color_type7}};}
                #{{ addonId }} .sitecontent #postRight .listContent_post > .item_tags>a.active,.sitecontent #postRight .listContent_post > .item_tags>a:hover{border-color:{{{ data.type7_theme_color }}};background-color:{{{ data.type7_theme_color }}};color:#fff}
                #{{ addonId }} .sitecontent #postRight .listContent_post .item_block{transition:all 0.36s ease;width:calc(100% / {{about_list_col_type7}} - {{btn_right}}px - {{btn_left}}px);height:auto;float:left;margin: {{about_list_col_margin_type7}};position:relative;box-shadow:0 0 0px rgba(0,0,0,0.15)}
                #{{addonId}} .sitecontent #postRight .content_list .item_block .item_box .item_wrapper .subtitle{
                    color: {{about_list_subtitle_color_type7}};
                    font-size: {{about_list_subtitle_size_type7}}px;
                    margin-top: {{about_list_subtitle_margin_top_type7}};
                }
                #{{ addonId }} .sitecontent #postRight .mlist:not(.imagelink) .content_list .item_block:hover .item_box .item_img img{transform:scale(1.08)}
                #{{ addonId }} .sitecontent #postRight .mlist:not(.imagelink) .content_list .item_block{border:1px solid {{about_list_col_border_color_type7}};background-color:transparent;box-sizing:border-box}
                #{{ addonId }} .sitecontent #postRight .content_list .item_block:hover .item_box .item_wrapper{background-color:{{{ data.type7_theme_color }}};color:#FFFFFF}
                #{{ addonId }} .sitecontent #postRight.content_list .item_block:hover .item_box .item_wrapper{background:{{{ data.type7_theme_color }}}}
                #{{ addonId }} .sitecontent #postRight .content_list .item_block .item_box .item_wrapper{padding:{{about_list_title_padding_type7}}px;color: {{about_list_title_color_type7}};font-size: {{about_list_title_size_type7}}px;}
                #{{ addonId }} .sitecontent{display:block}
                #{{ addonId }} #postWrapper #postSlider{display:none}
                #{{ addonId }} #postInfo{display:none}
                #{{ addonId }} #postContent{display:none}
                @media only screen and (max-width:1920px) and (min-width:1600px){}
                @media only screen and (max-width:1023px){
                #{{ addonId }} .sitecontent{display:none}
                #{{ addonId }} #postWrapper #postSlider{display:block}
                #{{ addonId }} #postInfo{display:block}
                #{{ addonId }} #postContent{display:none}
                #{{ addonId }} #postInfo,#postNav,#postContent{width:calc(100% - {{box_left}} - {{box_right}});margin:{{type7_small_screen_box_margin}};position:relative;text-align:left}
                #{{ addonId }} #postInfo .title{font-size:{{type7_small_screen_title_size}}px;transition:all .3s ease-out 0s;line-height:{{type7_small_screen_title_line_height}}px;margin-top: {{type7_small_screen_title_margin_top}}px;color: {{type7_small_screen_title_color}};}
                #{{ addonId }} #postInfo .subtitle{color:{{type7_small_screen_label1_color}};font-size:{{type7_small_screen_label1_size}}px;margin-top:{{type7_small_screen_label1_margin_top}}px;transition:all .3s ease-out 0s;line-height:{{type7_small_screen_label1_line_height}}px;}
                #{{ addonId }} #postInfo .description{margin-top:{{type7_small_screen_desc_margin_top}}px;display:block;font-size:{{type7_small_screen_des_font_size}}px{{type7_small_screen_des_font_size_cover ? "!important" : ""}};color: {{type7_small_screen_des_color}};line-height: {{type7_small_screen_desc_line_height}}px}
                #{{ addonId }} #postInfo .description .subbtn{height:{{type7_small_screen_button_height}}px;line-height:{{type7_small_screen_button_height}}px;border:1px solid {{type7_small_screen_button_border_color}};text-align:center;display:inline-block;color:{{type7_small_screen_button_color}};background: {{type7_small_screen_button_bg_color}};text-decoration:none;padding:{{type7_small_screen_button_padding}};margin:{{type7_small_screen_button_margin}};}
                #{{ addonId }} #postInfo .description .subbtn:hover{background:{{{ data.type7_theme_color }}};color:#FFFFFF}
                #{{ addonId }} #postContent .postbody p img{width:100%;align-items:center}
                #{{ addonId }} .bx-wrapper .bx-viewport{box-shadow:none;border:0;left:0}
                #{{ addonId }} .bx-wrapper .bx-pager.bx-default-pager a{background:#ddd;display:block;width:8px;height:8px;margin:0 5px;outline:0;border-radius:5px}
                #{{ addonId }} .bx-wrapper .bx-pager.bx-default-pager a.active{border:#a5a5a5 1px solid;background-color:#a5a5a5}
                #{{ addonId }} .bx-wrapper .bx-pager{padding:0}
                #{{ addonId }} .postbody{position:relative;z-index:0;color: {{type7_small_screen_content_color}};line-height: {{type7_small_screen_content_line_height}}px;}
                #{{addonId}} .postbody *{
                    font-size: {{type7_small_screen_content_font_size}}px{{type7_small_screen_content_font_size_cover ? "!important" : ""}};
                }
                #{{ addonId }} #listContent h3{color:{{type7_small_screen_about_color}};font-size:{{type7_small_screen_about_size}}px;margin-top:{{type7_small_screen_about_margin_top}}px}
                #{{ addonId }} #postWrapper .item_tags{text-align:left;display:block}
                #{{ addonId }} #postWrapper .item_tags a{display:inline-block;padding:{{type7_small_screen_button_about_padding}};border:{{type7_small_screen_button_about_border_color}} 1px solid;background-color:{{type7_small_screen_button_about_bg_color}};margin:{{type7_small_screen_button_about_margin}};color: {{type7_small_screen_button_about_color}};}
                #{{ addonId }} #postWrapper #listContent .mlist.project{height:280px}
                #{{ addonId }} #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block:hover .item_box .item_img img{transform:scale(1.08)}
                #{{ addonId }} #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block{border:1px solid {{type7_small_screen_about_list_col_border_color}};background-color:transparent;box-sizing:border-box}
                #{{ addonId }} #postWrapper #listContent  .mlist:not(.imagelink)  .content_list .item_block:hover .item_box .item_wrapper{background-color:{{{ data.type7_theme_color }}};color:#fff}
                #{{ addonId }} #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper{padding:{{type7_small_screen_about_list_title_padding}}}
                #{{ addonId }} #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper .text_wrap p{
                    font-size: {{type7_small_screen_about_list_title_size}}px;
                    color: {{type7_small_screen_about_list_title_color}};
                }
                #{{ addonId }} #postWrapper #listContent  .mlist:not(.imagelink) .content_list .item_block .item_box .item_wrapper .text_wrap p.subtitle{
                    font-size: {{type7_small_screen_about_list_subtitle_size}}px;
                    color: {{type7_small_screen_about_list_subtitle_color}};
                    margin-top: {{type7_small_screen_about_list_subtitle_margin_top}}px;
                }
                #{{ addonId }} #postWrapper #listContent .mlist:not(.imagelink) .content_list .item_block{width:calc(100% / {{type7_small_screen_about_list_col}} - {{list_left}} - {{list_right}});margin:{{type7_small_screen_about_list_col_margin}};float:left;transition:all .2s ease-out;-webkit-transition:all .2s ease-out;position:relative;overflow:hidden}
                }
                #{{ addonId }} .sitecontent  .tabBtn .post-prev .details:before{content:"<";font-family:"FontAwesome";speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;font-size:14px;line-height:1;color:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;position:absolute;top:50%;left:50%;font-size:24px;color:#999;transform:translate(-50%,-50%)}
                #{{ addonId }} .sitecontent .tabBtn .post-next .details:before{content:">";font-family:"FontAwesome";speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;font-size:14px;line-height:1;color:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;position:absolute;top:50%;left:50%;font-size:24px;color:#999;transform:translate(-50%,-50%)}
                #{{ addonId }} .sitecontent .tabBtn .post-prev{width:42px;height:100px;position:fixed;left:0px;z-index:20;top:50%;transform:translateY(-50%);transition:opacity 0.36s ease-out,visibility 0.36s ease-out}
                #{{ addonId }} .sitecontent .tabBtn .post-next{width:42px;height:100px;position:fixed;right:0px;z-index:20;top:50%;transform:translateY(-50%);transition:opacity 0.36s ease-out,visibility 0.36s ease-out}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .details,#{{ addonId }} .sitecontent .tabBtn .post-next .details{position:absolute;right:0;height:100%;width:42px;background:#fff;border:1px solid #ececec;box-sizing:border-box;transition:all 0.3s ease-out 0s}
                #{{ addonId }} .sitecontent .tabBtn .post-prev:hover .details,#{{ addonId }} .sitecontent .tabBtn .post-next:hover .details{border-color:#909744;background-color:#909744;color:#fff}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .img-wrap,#{{ addonId }} .sitecontent .tabBtn .post-next .img-wrap{position:absolute;box-sizing:border-box;z-index:10;height:100%;border:3px solid #909744;width:170px;opacity:0;visibility:hidden;border-left:0;transition:all 0.36s ease}
                #{{ addonId }} .sitecontent .tabBtn .post-prev  .img-wrap{left:100%;border-right:0}
                #{{ addonId }} .sitecontent .tabBtn .post-next .img-wrap{right:100%;border-right:0}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .img-wrap img,#{{ addonId }} .sitecontent .tabBtn .post-next .img-wrap img{display:none}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .img-wrap:after,#{{ addonId }} .sitecontent .tabBtn .post-next .img-wrap:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.4)}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .img-wrap div,#{{ addonId }} .sitecontent .tabBtn .post-next .img-wrap div{width:165px;height:100%;background-position:center center;background-size:cover}
                #{{ addonId }} .sitecontent .tabBtn .post-prev  .infor-wrap,#{{ addonId }} .sitecontent .tabBtn .post-next .infor-wrap{position:absolute;top:50%;z-index:10;transform:translateY(-50%);box-sizing:border-box;width:170px;opacity:0;visibility:hidden;padding-left:20px}
                #{{ addonId }} .sitecontent .tabBtn .post-prev  .infor-wrap{left:100%;border-right:0}
                #{{ addonId }} .sitecontent .tabBtn .post-next .infor-wrap{right:100%;border-right:0}
                #{{ addonId }} .sitecontent .tabBtn .post-prev .title,#{{ addonId }} .sitecontent .tabBtn .post-next .infor-wrap .title{width:70%;display:block;font-size:16px;line-height:20px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
                #{{ addonId }} .sitecontent .tabBtn .post-prev  .infor-wrap .subtitle,#{{ addonId }} .sitecontent .tabBtn .post-next .infor-wrap .subtitle{width:80%;display:block;line-height:18px;font-size:14px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
                @media screen and (min-width:1440px){
                #{{ addonId }} .sitecontent .tabBtn .post-prev:hover .img-wrap,#{{ addonId }} .sitecontent .tabBtn .post-next:hover .img-wrap{opacity:1;visibility:visible}
                #{{ addonId }} .sitecontent .tabBtn .post-prev:hover .infor-wrap,#{{ addonId }} .sitecontent .tabBtn .post-next:hover .infor-wrap{opacity:1;visibility:visible}
                #{{ addonId }}  .sitecontent .tabBtn .post-prev:hover .details,#{{ addonId }} .sitecontent .tabBtn  .post-next:hover .details{border-color:#909744;background:#909744}
                #{{ addonId }} .project.mlistpost .post-prev:hover .details:before,#{{ addonId }} .team.mlistpost .post-prev:hover .details:before,#{{ addonId }} .project.mlistpost .post-next:hover .details:before,.team.mlistpost .post-next:hover .details:before{color:#fff}
                #{{ addonId }} .sitecontent  .tabBtn .post-prev:hover  .details:before{color:#fff}
                #{{ addonId }} .sitecontent .tabBtn .post-next:hover  .details:before{color:#fff}
                }
                </style>

                    <div class="sitecontent">
                    <div id="pageTarget" class="ff_pageTarget module">
                      <div class="module_container">
                        <div class="container_target wow" style="visibility: visible; animation-name: fadeInUp;">您的位置：<a href="#">首頁</a><i class="fa fa-angle-right"></i><a href="#">茶葉種類</a><i class="fa fa-angle-right"></i><a href="#">黑茶</a></div>
                        <div class="clear"></div>
                      </div>
                    </div>
                    <!-- 左侧 开始-->
                    <div id="postLeft">
                      <!--  幻灯片开始-->
                      <div id="postSlider" class="postSlider mlist">
                        <div id="owl-demo" class="owl-carousel owl-theme">
                          <div class="itme">
                            <div class="li1"> <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a1.jpg" alt=""> </div>
                          </div>
                          <div class="itme">
                            <div class="li1"> <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a2.jpg" alt=""> </div>
                          </div>
                          <div class="itme">
                            <div class="li1"> <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a3.jpg" alt=""> </div>
                          </div>
                          <div class="itme">
                            <div class="li1"> <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a4.jpg" alt=""> </div>
                          </div>
                          <div class="owl-controls clickable">
                            <div class="owl-pagination"> </div>
                            <div class="owl-buttons"> </div>
                          </div>
                        </div>
                      </div>
                      <!--  幻灯片结束-->
                      <!--  内容开始-->
                      <div class="postbody">
                        <p>普洱熟茶源自西双版纳核心产区，精选古树毛茶原料，采用邹炳良先生独特的渥堆发酵技术，经筛分、<br>
                          拼配等工序精制而成。成品金毫满披，含芽过半；冲泡时茶汤红浓明艳、似陈年红酒色，陈香高显；入口粘稠、顺滑。</p>
                        <p><br>
                        </p>
                        <table>
                          <tbody>
                            <tr class="firstRow">
                              <td style="word-break: break-all;" width="auto" valign="top"><p><img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1509608948907.jpg" title="undefined" alt="undefined"></p>
                                <p><br>
                                </p>
                                <p>看外形<br>
                                  条索紧细，金毫披芽，<br>
                                  含芽率高，无异、杂味。<br>
                                  <br>
                                  <br>
                                </p></td>
                              <td width="15" valign="top"><br></td>
                              <td style="word-break: break-all;" width="auto" valign="top"><p><img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1509608690235.jpg" title="undefined" alt="undefined"></p>
                                <p><br>
                                </p>
                                <p>观汤色<br>
                                  红浓明艳，似陈年红酒色。<br>
                                  <br>
                                </p></td>
                            </tr>
                          </tbody>
                        </table>
                        <p><br>
                        </p>
                      </div>
                      <!--  内容结束-->
                    </div>

                    <!-- 左侧 结束-->

                    <div id="postRight">
                      <div class="postInfo">
                        <p class="title">普洱熟茶  三人装 </p>
                        <p class="subtitle">邹炳良大师作品</p>
                        <p class="usetdate"> <span class="year">2016</span><i class="time-connect">-</i><span class="m">12</span><i class="time-connect">-</i><span class="d">14</span> </p>
                        <div class="description">
                          <p>茶类：黑茶&nbsp;&nbsp;&nbsp; <br>
                          </p>
                          <p>原料：云南大叶种晒青毛茶&nbsp;&nbsp; <br>
                          </p>
                          <p>&nbsp;产地：云南省安宁市&nbsp;&nbsp;&nbsp; <br>
                          </p>
                          <p>等级：特级<span style="color: rgb(255, 0, 0);"></span></p>
                          <p><br>
                          </p>
                          <p>
                          <# if(data.type7_taobao_button=="yes") { #>

                                  <a href="https://www.taobao.com" target="_blank">淘宝购买</a>
                         <# } #>
                         <# if(data.type7_weidian_button=="yes") { #>
                                  &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" target="_blank">微店购买</a>
                         <# } #>
                          </p>
                          <p><br>
                          </p>
                          <p><br>
                          </p>
                        </div>
                      </div>
                      <div id="listContent" class="listContent_post">
                        <h3>相关内容</h3>
                        <div class="item_tags"><a href="#" target="_blank">黑茶</a><a href="#" target="_blank">邹炳良</a></div>
                        <div class="mlist type04">
                          <div class="content_wrapper">
                            <div class="content_list clearfix">
                              <div id="item_block_0" class="item_block_0 item_block wow" style="animation-delay: 0s; visibility: visible; animation-name: fadeInUp;"> <a href="#" class="item_box">
                                <div href="#" class="item_img" target="_blank"> <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a1.jpg">
                                  <div class="item_mask"></div>
                                </div>
                                <div class="item_wrapper clearfix">
                                  <div class="item_info clearfix">
                                    <p class="title ellipsis">烏龍茶  豪華精裝</p>
                                    <p class="subtitle ellipsis">￥5000</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="clear"></div>
                        </div>
                      </div>
                    </div>

                    </div>
                      <!--  PC结束-->
                      <div id="postWrapper">
                        <div id="postSlider">
                          <div class="bx-wrapper">
                            <ul class="content_list slider" >
                              <li id="pptc_item_block_3" class="sItem_block bx-clone" >
                                <div class="wrapper">
                                  <div class="item_img"> <img src="img/a1.jpg"> </div>
                                </div>
                              </li>
                              <li id="pptc_item_block_0" class="sItem_block">
                                <div class="wrapper">
                                  <div class="item_img"> <img src="img/a2.jpg"> </div>
                                </div>
                              </li>
                              <li id="pptc_item_block_1" class="sItem_block">
                                <div class="wrapper">
                                  <div class="item_img"> <img src="img/a3.jpg"> </div>
                                </div>
                              </li>
                              <li id="pptc_item_block_2" class="sItem_block">
                                <div class="wrapper">
                                  <div class="item_img"> <img src="img/a4.jpg"> </div>
                                </div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div id="postInfo">
                          <div class="wrapper" data-sf-top="20">
                            <p class="title">普洱熟茶  三人装 </p>
                            <p class="subtitle">邹炳良大师作品</p>
                            <div class="description">
                              <p>茶类：黑茶&nbsp;&nbsp;&nbsp; <br>
                              </p>
                              <p>原料：云南大叶种晒青毛茶&nbsp;&nbsp; <br>
                              </p>
                              <p>&nbsp;产地：云南省安宁市&nbsp;&nbsp;&nbsp; <br>
                              </p>
                              <p>等级：特级<span style="color: rgb(255, 0, 0);"></span></p>
                              <p><br>
                              </p>
                              <p>
                              <# if(data.type7_taobao_button=="yes") { #>
                                   <a href="https://www.taobao.com" target="_blank">淘宝购买</a>
                              <# } #>
                              <# if(data.type7_weidian_button=="yes") { #>
                                      &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" target="_blank">微店购买</a>
                              <# } #>
                              </p>
                              <p><br>
                              </p>
                              <p><br>
                              </p>
                            </div>
                          </div>
                        </div>
                        <div id="postContent">
                          <div class="postbody">
                            <p>普洱熟茶源自西双版纳核心产区，精选古树毛茶原料，采用邹炳良先生独特的渥堆发酵技术，经筛分、<br>
                              拼配等工序精制而成。成品金毫满披，含芽过半；冲泡时茶汤红浓明艳、似陈年红酒色，陈香高显；入口粘稠、顺滑。</p>
                            <p><br>
                            </p>
                            <p><img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1509608948907.jpg" title="undefined" alt="undefined"></p>
                            <p><br>
                            </p>
                            <p>看外形<br>
                              条索紧细，金毫披芽，<br>
                              含芽率高，无异、杂味。<br>
                              <br>
                              <br>
                            </p>
                            <br>
                            <p><img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1509608690235.jpg" title="undefined" alt="undefined"></p>
                            <p><br>
                            </p>
                            <p>观汤色<br>
                              红浓明艳，似陈年红酒色。<br>
                              <br>
                            </p>
                            <p><br>
                            </p>
                          </div>
                          <div id="listContent">
                            <h3>相关内容</h3>
                            <div class="item_tags"><i class="fa fa-tags"></i><a href="#" target="_blank">黑茶</a><a href="#" target="_blank">邹炳良</a></div>
                            <div class="mlist project">
                              <div class="content_wrapper">
                                <div class="content_list clearfix">
                                  <div class="item_block wow" > <a href="#" class="item_box">
                                    <div class="item_img" target="_blank"> <img src="img/a1.jpg">
                                      <div class="item_mask"></div>
                                    </div>
                                    <div class="item_wrapper clearfix">
                                      <div class="item_info clearfix">
                                        <div class="text_wrap">
                                          <p class="title ellipsis">烏龍茶  豪華精裝</p>
                                          <p class="subtitle ellipsis">￥5000</p>
                                        </div>
                                      </div>
                                    </div>
                                    </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="clear"></div>
                            </div>
                          </div>
                          <div class="clear"></div>
                        </div>
                         <div class="conTabBtn tabBtn">
                     <# if(data.page_button == 0) { #>

                            <a href="#" class="post-prev">
                                <div class="img-wrap">
                                  <div style="background-image: url(/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1a1.jpg)"></div>
                                        <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a11.jpg" alt="">
                                                </div>
                                <div class="infor-wrap">
                                    <span class="title">普洱熟茶  三人装 </span>
                                    <span class="subtitle">邹炳良大师作品</span>
                                </div>
                                    <i class="details"></i>
                                    <div class="tabMask"></div>
                            </a>
                            <a href="#" class="post-next">
                                <div class="img-wrap">
                                                <div style="background-image: url(/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/1a2.jpg)"></div>
                                    <img src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/img/a11.jpg" alt="">
                                            </div>
                                <div class="infor-wrap">
                                    <span class="title">普洱熟茶  三人装 </span>
                                    <span class="subtitle">邹炳良大师作品</span>
                                </div>
                                <i class="details"></i>
                                    <div class="tabMask"></div>
                            </a>
                     <# } #>

                </div>
                </div>
            <# } #>
            <# if(theme=="type8") {

                var type8_banopen = data.type8_banopen?data.type8_banopen:"0";
                var type8_banner_bili = data.type8_banner_bili?data.type8_banner_bili:"0";
                var type8_ban_img = data.type8_ban_img?data.type8_ban_img:"2/1";
                var type8_banopen = data.type8_banopen?data.type8_banopen:"0";

                var type8_price_size = data.type8_price_size?data.type8_price_size:"24";
                var type8_price_color = data.type8_price_color?data.type8_price_color:"#fa230a";
                var type8_title_size = data.type8_title_size?data.type8_title_size:"16";
                var type8_title_color = data.type8_title_color?data.type8_title_color:"#252525";


                #>
                <style type="text/css">
                    #{{ addonId }} p,a,li,ul,span,h1,h2,h3,h4,h5,h6,div,em,i,img,video,iframe,object,select,textarea,input{
                        margin: 0;
                        padding: 0;
                    }
                    #{{ addonId }} .type8.mySwiper{
                        height: 415px;
                        overflow:hidden;
                    }
                    #{{ addonId }} .type8.mySwiper img{
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                    #{{ addonId }} .self-pagination{
                        position: absolute;
                        bottom: 19px;
                        right: 0;
                        width: 55px;
                        height: 24px;
                        border-radius: 4px;
                        background: rgba(0,0,0,.3);
                        z-index: 9;
                        border-radius: 12px 0 0 12px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        font-size: 12px;
                        letter-spacing: 2px;
                    }
                    #{{ addonId }} .cur-page{
                        font-size: 16px;
                    }
                    #{{ addonId }} .swiper-pagination{
                        bottom: 6px;
                    }
                    #{{ addonId }} .swiper-pagination-bullet{
                        width: 8px;
                        height: 8px;
                        background: rgba(0,0,0,.2);
                        opacity: 1;
                    }
                    #{{ addonId }} .swiper-pagination-bullet-active{
                        background: rgba(0,0,0,.8);
                    }
                    #{{ addonId }} .info{

                        <# if(_.isObject(data.type8_padding)){ #>
                            padding: {{ data.type8_padding.md }};
                        <# } else { #>
                            padding: {{ data.type8_padding }};
                        <# } #>

                        border-radius: 0px 0px 10px 10px;
                        background: #fff;
                    }
                    #{{ addonId }} .price{
                        color: {{type8_price_color}};
                        font-size: 14px;
                        font-weight: bold;
                    }
                    #{{ addonId }} .price .int{
                        font-size: {{type8_price_size}}px;
                    }
                    #{{ addonId }} .info .title{
                        font-size: {{type8_title_size}}px;
                        color:{{type8_title_color}};
                        font-weight: bold;
                        line-height: 1.5;
                        margin-top: 14px;
                    }
                    #{{ addonId }} .radius{
                        border-radius: 10px;
                        background: #fff;
                        margin-top: 11px;
                    }
                    #{{ addonId }} .model{
                        padding: 23px 18px;
                    }
                    #{{ addonId }} .model .item{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        font-weight: bold;
                        font-weight: 16px;
                    }
                    #{{ addonId }} .detail{
                        border-radius: 10px 10px 0 0;
                    }
                    #{{ addonId }} .detail .tips{
                        color: #cccccc;
                        font-size: 14px;
                        text-align: center;
                    }
                    #{{ addonId }} .detail .tips span:first-child {
                        margin-right: 5px;
                    }
                    #{{ addonId }} .detail .tips span:last-child{
                        display: inline-block;
                        transform: scaleY(0.8);
                    }
                    #{{ addonId }} .detail .tips i{
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        border-left: 2px solid #ccc;
                        border-top: 2px solid #ccc;
                        border-bottom: 2px solid transparent;
                        border-right: 2px solid transparent;
                        transform: rotate(45deg) translateY(50%);
                    }
                    #{{ addonId }} .detail-label{
                        font-size: 14px;
                        color: #000;
                        margin: 12px 24px;
                        padding-left: 13px;
                        font-weight: bold;
                        position: relative;
                    }
                    #{{ addonId }} .detail-label::before{
                        content: "";
                        display: block;
                        position: absolute;
                        left: 0;
                        width: 3px;
                        height: 70%;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                        background-image: linear-gradient(to bottom, #f2270c, #fff);
                    }

                    @media (max-width: 991px) and (min-width: 768px) {
                        #{{ addonId }} .type8.mySwiper{
                            height: 415px;
                        }
                    }
                    @media (max-width: 768px) {
                        #{{ addonId }} .type8.mySwiper{
                            height: 415px;
                        }
                    }

                    /*翻页 布局01*/
                    #{{ addonId }} .btnBox {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBox a {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColor}};
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBox a:hover {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBoxt a {
                        width: 50%;
                        height: 40px;
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: left;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:hover {
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:nth-child(2){  text-align:right; }

                    /*翻页 布局02*/
                    #{{ addonId }} .page02-btnBox {
                        margin-top: {{ data.type5_page_top }}px;
                    }
                    #{{ addonId }} .page02-btnBox a {
                        color: {{ data.pageColor }};
                        font-size: {{ data.type5_page_font_size }}px;
                        line-height: {{ data.type5_page_lineHeight }}px;
                        display: flex;
                        align-items: center;
                    }
                    #{{ addonId }} .page02-btnBox a img {
                        margin-right: 5px;
                        display: none;
                        width: {{ data.type5_page_font_size }}px;
                    }
                    #{{ addonId }} .page02-btnBox a .normal {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox a:hover {
                        color: {{ data.pageColorhover }};
                    }
                    #{{ addonId }} .page02-btnBox a:hover .normal {
                        display: none;
                    }
                    #{{ addonId }} .page02-btnBox a:hover .hover {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox .page-line {
                        width: 100%;
                        border-top: 1px {{ data.type5_page_line }} {{ data.type5_page_line_color }};
                    }
                </style>

                <div class="type8">
                <# if(type8_banopen!=1){ #>
                    <div class="swiper mySwiper type8">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220809/7dbd5b6e8ef2c7022539096f163a3718.jpeg">
                            </div>
                            <div class="swiper-slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220809/e47359134c53f7c481d69697264a0936.jpeg">
                            </div>
                            <div class="swiper-slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220809/a0e7a0fee810dd5db5e79e0ccdafecb6.jpeg">
                            </div>
                            <div class="swiper-slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220809/5bf84b5d047dd5917202b4f977338724.jpeg">
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="self-pagination">
                            <span class="cur-page"></span>
                            <span class="split">/</span>
                            <span class="total-page"></span>
                        </div>
                    </div>
                <# } #>
                    <div class="info">
                        <p class="price">
                            ￥<span class="int">66</span>
                        </p>
                        <p class="title">
                            海尔(Haier\")空调一级能效家用冷暖变频壁挂式
                            空调自清洁线下同款企业采购静悦KFR
                            26GW/02KBB83U1
                        </p>
                    </div>

                    <!--详情-->
                    <div class="radius detail">

                        <div class="detail-label">
                            详情
                        </div>
                        <div class="detail-img">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220720/ec5d06f0e56ca01787bbfdf8312d9226.jpeg">
                        </div>
                    </div>
                </div>

                <# if(data.page_button==0) { #>
                    <# if(data.type5_page_style == "page02") { #>
                        <div class="page02-btnBox">
                            <a href="#">
                                <img src=\'{{data.page_prev}}\' class="normal" />
                                <img src=\'{{data.page_prev_hover}}\' class="hover" />
                                {{data.up_page_text}}上一条产品标题
                            </a>
                            <div class="page-line"></div>
                            <a href="#">
                                <img src=\'{{data.page_next}}\' class="normal" />
                                <img src=\'{{data.page_next_hover}}\' class="hover" />
                                {{data.next_page_text}}下一条产品标题
                            </a>
                        </div>
                    <# } else { #>
                            <# if(data.page_dttitle==1) { #>
                                <div class="btnBoxt">
                                    <a href="#">{{data.up_page_text}}:标题名称标题标题</a>
                                    <a href="#">{{data.next_page_text}}:标题名称标题标题</a>
                                </div>
                            <# }else{ #>
                                <div class="btnBox">
                                    <a href="#">{{data.up_page_text}}</a>
                                    <a href="#">{{data.next_page_text}}</a>
                                </div>
                            <# } #>
                    <# } #>
                <# } #>

            <# } #>
            <# if(theme==="type9") { #>
                <#
                    var show_banner_type9 = data.show_banner_type9 || 0;
                    var img_width_type9 = 100, img_width_type9_sm = 100, img_width_type9_xs = 100;
                    if(Object.prototype.toString.call(data.img_width_type9) === "[object Object]"){
                        img_width_type9=data.img_width_type9.md || 100;
                        img_width_type9_sm=data.img_width_type9.sm || 100;
                        img_width_type9_xs=data.img_width_type9.xs || 100;
                    }else{
                        img_width_type9=data.img_width_type9||100
                        img_width_type9_sm=data.img_width_type9||100
                        img_width_type9_xs=data.img_width_type9||100
                    }
                    var set_img_height_type9 = data.set_img_height_type9 || 0;
                    var img_height_type9 = "auto", img_height_type9_sm = "auto", img_height_type9_xs = "auto";
                    if(Object.prototype.toString.call(data.img_height_type9) === "[object Object]"){
                        img_height_type9=data.img_height_type9.md || "auto";
                        img_height_type9_sm=data.img_height_type9.sm || "auto";
                        img_height_type9_xs=data.img_height_type9.xs || "auto";
                    }else{
                        img_height_type9=data.img_height_type9||"auto"
                        img_height_type9_sm=data.img_height_type9||"auto"
                        img_height_type9_xs=data.img_height_type9||"auto"
                    }
                    var img_fit_type9 = data.img_fit_type9 || "cover";
                    var detail_top_type9 = 24, detail_top_type9_sm = 24, detail_top_type9_xs = 24;
                    if(Object.prototype.toString.call(data.detail_top_type9) === "[object Object]"){
                        detail_top_type9=data.detail_top_type9.md || 24;
                        detail_top_type9_sm=data.detail_top_type9.sm || 24;
                        detail_top_type9_xs=data.detail_top_type9.xs || 24;
                    }else{
                        detail_top_type9=data.detail_top_type9||24
                        detail_top_type9_sm=data.detail_top_type9||24
                        detail_top_type9_xs=data.detail_top_type9||24
                    }
                    var detail_width_type9 = 63, detail_width_type9_sm = 63, detail_width_type9_xs = 63;
                    if(Object.prototype.toString.call(data.detail_width_type9) === "[object Object]"){
                        detail_width_type9=data.detail_width_type9.md || 63;
                        detail_width_type9_sm=data.detail_width_type9.sm || 63;
                        detail_width_type9_xs=data.detail_width_type9.xs || 63;
                    }else{
                        detail_width_type9=data.detail_width_type9||63
                        detail_width_type9_sm=data.detail_width_type9||63
                        detail_width_type9_xs=data.detail_width_type9||63
                    }
                    var pageTopType9 = data.pageTopType9 || 50;
                    var pageColor = data.pageColor || "#1237ce";
                    var pageColumnType9 = data.pageColumnType9 || "1";
                    var pageColorhover = data.pageColorhover || "#1237ce";
                #>
                <style>
                    #{{ addonId }} .type9-detail{
                        width: 100%;
                    }
                    #{{ addonId }} .banner{
                        display: block;
                        width: {{img_width_type9}}%;
                        margin: auto;
                        <# if(!set_img_height_type9){ #>
                            height: auto;
                        <# }else{ #>
                            height: {{img_height_type9}}px;
                        <# } #>
                        object-fit: {{img_fit_type9}};
                    }
                    #{{ addonId }}  .content{
                        overflow: hidden;
                        margin-top: {{detail_top_type9}}px;
                        width: {{detail_width_type9}}%;
                        margin-left: auto;
                        margin-right: auto;
                    }
                    #{{ addonId }}  .content>div{
                        overflow: hidden;
                    }
                    @media and (max-width: 992px) and (min-width: 767px){
                        #{{ addonId }} .banner{
                            width: {{img_width_type9_sm}}%;
                            <# if(!set_img_height_type9){ #>
                            height: auto;
                            <# }else{ #>
                            height: {{img_height_type9_sm}}px;
                            <# } #>
                        }
                        #{{ addonId }}  .content{
                            margin-top: {{detail_top_type9_sm}}px;
                            width: {{detail_width_type9_sm}}%;
                        }
                    }

                    @media and (max-width: 768px){
                        #{{ addonId }} .banner{
                            width: {{img_width_type9_xs}}%;
                            <# if(!set_img_height_type9){ #>
                                height: auto;
                            <# }else{ #>
                                height: {{img_height_type9_xs}}px;
                            <# } #>
                        }
                        #{{ addonId }}  .content{
                            margin-top: {{detail_top_type9_xs}}px;
                            width: {{detail_width_type9_xs}}%;
                        }
                    }

                    /*翻页 布局01*/
                    #{{ addonId }} .btnBox {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBox a {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColor}};
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBox a:hover {
                        width: 120px;
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        text-align: center;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt {
                        display: flex;
                        justify-content: space-between;
                        padding: 20px;
                    }
                    #{{ addonId }} .btnBoxt a {
                        width: 50%;
                        height: 40px;
                        color: {{data.pageColor}};
                        font-size: 14px;background:{{data.pageBgColor}};
                        text-align: left;
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:hover {
                        height: 40px;
                        border: 1px solid {{data.pageBorderColorhover}};
                        color: {{data.pageColorhover}};
                        font-size: 14px;
                        background:{{data.pageBgColorhover}};
                        line-height: 40px;
                        text-decoration: none;
                    }
                    #{{ addonId }} .btnBoxt a:nth-child(2){  text-align:right; }

                    /*翻页 布局02*/
                    #{{ addonId }} .page02-btnBox {
                        margin-top: {{ data.type5_page_top }}px;
                    }
                    #{{ addonId }} .page02-btnBox a {
                        color: {{ data.pageColor }};
                        font-size: {{ data.type5_page_font_size }}px;
                        line-height: {{ data.type5_page_lineHeight }}px;
                        display: flex;
                        align-items: center;
                    }
                    #{{ addonId }} .page02-btnBox a img {
                        margin-right: 5px;
                        display: none;
                        width: {{ data.type5_page_font_size }}px;
                    }
                    #{{ addonId }} .page02-btnBox a .normal {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox a:hover {
                        color: {{ data.pageColorhover }};
                    }
                    #{{ addonId }} .page02-btnBox a:hover .normal {
                        display: none;
                    }
                    #{{ addonId }} .page02-btnBox a:hover .hover {
                        display: block;
                    }
                    #{{ addonId }} .page02-btnBox .page-line {
                        width: 100%;
                        border-top: 1px {{ data.type5_page_line }} {{ data.type5_page_line_color }};
                    }

                    #{{ addonId }} .btnBoxt {
                        margin-top: {{pageTopType9}}px;
                    }
                    #{{ addonId }} .btnBoxt a {
                        color: {{pageColor}};
                        <# if(pageColumnType9){ #>
                            display: block;
                        <# } #>
                    }
                    #{{ addonId }} .btnBoxt a:hover {
                        color: {{pageColorhover}};
                    }
                </style>

                <div class="type9-detail">
                    <# if(show_banner_type9){ #>
                        <img class="banner" src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20230704/f478324d29f4bcd5254e8c43e40bec60.jpeg">
                    <# } #>

                <div class="content">
                    <div>
                        <p id="name" style="text-align: left;font-size: 32px;color: #8e765a;margin-top: 24px;line-height: 1">律师介绍</p>
                        <div id="xian" style="width: 80px;background: #8e765a;height: 2px;margin-top: 10px;"></div>
                        <div class="content-left" style="float: left;width: 800px;">
                        <p class="MsoNormal" style="text-indent:2em;">
                            <br>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">创始合伙人、名誉主任</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">太原市新长征突击手</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">太原市首届优秀青年律师</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">山西省劳动竞赛委员会一等功获得者</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">中国政法大学优秀校友</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:24px;line-height:2.5;color:#64451D;"><strong>教育背景</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">中国政法大学，法律系，法律专业，本科</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">山西财经大学，金融学专业，研究生</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:24px;line-height:2.5;color:#64451D;"><strong>工作经历</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">1989</span><span style="font-size:16px;line-height:2.5;">年</span><span style="font-size:16px;line-height:2.5;">7</span><span style="font-size:16px;line-height:2.5;">月至</span><span style="font-size:16px;line-height:2.5;">1992</span><span style="font-size:16px;line-height:2.5;">年</span><span style="font-size:16px;line-height:2.5;">12</span><span style="font-size:16px;line-height:2.5;">月，山西省人民检察院工作；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">1993</span><span style="font-size:16px;line-height:2.5;">年起至今，从事律师工作</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:24px;line-height:2.5;color:#64451D;"><strong>业务领域</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">高端民商事诉讼代理、企业破产重整、职务犯罪刑事辩护，专长于利用和整合律师事务所团队优势，组织协调开展重大法律事务</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:24px;line-height:2.5;color:#64451D;"><strong>代表业绩</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;"><strong>（一）民商事类</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">1.</span><span style="font-size:16px;line-height:2.5;">担任山西证券股份有限公司代理人，在“</span><span style="font-size:16px;line-height:2.5;">7</span><span style="font-size:16px;line-height:2.5;">·</span><span style="font-size:16px;line-height:2.5;">28</span><span style="font-size:16px;line-height:2.5;">”特大金融案中，与中国农业银行、中国建设银行及交通银行票据纠纷案全部获胜并全部执行，为山西证券挽回经济损失逾</span><span style="font-size:16px;line-height:2.5;">10</span><span style="font-size:16px;line-height:2.5;">亿元；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">2.</span><span style="font-size:16px;line-height:2.5;">担任中煤平朔集团有限公司代理人，在与某矿山企业侵权损害赔偿纠纷案中，历经一审、重一审、二审、重二审、再审，最终完全胜诉，为平朔集团避免经济损失超过</span><span style="font-size:16px;line-height:2.5;">1</span><span style="font-size:16px;line-height:2.5;">亿元；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">3.</span><span style="font-size:16px;line-height:2.5;">担任中煤平朔集团有限公司代理人，在与池某、王某矿权纠纷两案中，为平朔集团避免直接经济损失</span><span style="font-size:16px;line-height:2.5;">1.5</span><span style="font-size:16px;line-height:2.5;">亿元，并为中煤及地方政府处理类似百余家非法矿山企业关停取缔提供法律及案例支持；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">4.</span><span style="font-size:16px;line-height:2.5;">担任中国中煤能源集团公司代理人，在与某焦化公司财产损害赔偿案中，为中煤集团避免经济损失</span><span style="font-size:16px;line-height:2.5;">1.81</span><span style="font-size:16px;line-height:2.5;">亿元；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">5.</span><span style="font-size:16px;line-height:2.5;">担任太原钢铁</span><span style="font-size:16px;line-height:2.5;">(</span><span style="font-size:16px;line-height:2.5;">集团</span><span style="font-size:16px;line-height:2.5;">)</span><span style="font-size:16px;line-height:2.5;">有限公司代理人，在与某钢铁公司股权纠纷中，成功为太钢集团追回股权款</span><span style="font-size:16px;line-height:2.5;">7000</span><span style="font-size:16px;line-height:2.5;">余万元；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">6.</span><span style="font-size:16px;line-height:2.5;">接受省内多家煤炭企业及投资人委托，办理多起股权及资产转让纠纷案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">7.</span><span style="font-size:16px;line-height:2.5;">接受山西省农村信用合作联社系统内下属多家机构委托，办理多起借贷、票据及资金业务案，累计金额数十亿元。</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;"><strong>（二）刑事辩护类</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">1.</span><span style="font-size:16px;line-height:2.5;">原临汾市人民政府副市长苗某受贿案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">2.</span><span style="font-size:16px;line-height:2.5;">原临汾市人民政府副市长周某受贿案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">3.</span><span style="font-size:16px;line-height:2.5;">原临汾市蒲县煤管局局长郝某贪污、挪用公款案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">4.</span><span style="font-size:16px;line-height:2.5;">某煤炭企业向原省国土资源厅厅长单位行贿</span><span style="font-size:16px;line-height:2.5;">3000</span><span style="font-size:16px;line-height:2.5;">万元案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">5.</span><span style="font-size:16px;line-height:2.5;">某钢铁企业单位行贿</span><span style="font-size:16px;line-height:2.5;">1.2</span><span style="font-size:16px;line-height:2.5;">亿元案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">6.</span><span style="font-size:16px;line-height:2.5;">某钢铁集团尖山矿难案中所涉企业高管玩忽职守案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">7.</span><span style="font-size:16px;line-height:2.5;">某煤电集团屯兰矿难案中所涉企业高管玩忽职守案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">8.</span><span style="font-size:16px;line-height:2.5;">温州商人山西临汾矿难系列犯罪案；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">9.</span><span style="font-size:16px;line-height:2.5;">山西农信社、阳泉银行、晋中银行“</span><span style="font-size:16px;line-height:2.5;">6.03</span><span style="font-size:16px;line-height:2.5;">”专案中，多名高管系列受贿案。</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;"><strong>（三）公司类非诉讼业务</strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">1.</span><span style="font-size:16px;line-height:2.5;">曾长期担任中国石油山西分公司法律顾问，为其收购数百座加油站及油库提供法律意见；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">2.</span><span style="font-size:16px;line-height:2.5;">曾长期担任中科院山西煤化所法律顾问，为国家重点项目煤基合成油、灰熔聚煤炭气化等成果转化提供法律服务；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">3.</span><span style="font-size:16px;line-height:2.5;">为山西证券股份公司收购大华期货公司及设立网点提供法律服务；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">4.</span><span style="font-size:16px;line-height:2.5;">在山西省煤炭资源整合中，为西山煤电集团、晋煤集团、山煤国际收购整合煤炭企业提供法律服务；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">5.</span><span style="font-size:16px;line-height:2.5;">在联盛集团系列破产案中，担任债委会主席单位法律顾问及互保企业联合体法律顾问；</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">6.</span><span style="font-size:16px;line-height:2.5;">在多家省属企业破产案中担任管理人。</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:24px;line-height:2.5;color:#64451D;"><strong>社会<b><span style="line-height:2.5;font-size:24px;color:#64451D;">兼职</span></b></strong></span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">曾担任山西省政协社会法制委委员</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <span style="font-size:16px;line-height:2.5;">中国政法大学山西校友会秘书长</span>
                        </p>
                        <p class="MsoNormal" style="text-indent:2em;">
                            <br>
                        </p>
                        </div>
                                <div class="content-right" style="float: right;width:360px"><p style="text-indent:2em;">
                            <span style="font-size:24px;color:#64451D;line-height:2.5;"><strong>联系方式</strong></span>
                        </p>
                        <p style="text-indent:2em;">
                            <span style="line-height:2.5;">手机：13903407869</span>
                        </p>
                        <p style="text-indent:2em;">
                            <span style="line-height:2.5;">座机：0351-7526622</span>
                        </p>
                        <p style="text-indent:2em;">
                            <span style="line-height:2.5;">电子邮箱：<EMAIL></span>
                        </p>
                        <p>
                            <br>
                        </p>
                        </div>
                    </div>
                    <# if(data.page_button==0) { #>
                        <# if(data.type5_page_style == "page02") { #>
                            <div class="page02-btnBox">
                                <a href="#">
                                    <img src=\'{{data.page_prev}}\' class="normal" />
                                    <img src=\'{{data.page_prev_hover}}\' class="hover" />
                                    {{data.up_page_text}}上一条产品标题
                                </a>
                                <div class="page-line"></div>
                                <a href="#">
                                    <img src=\'{{data.page_next}}\' class="normal" />
                                    <img src=\'{{data.page_next_hover}}\' class="hover" />
                                    {{data.next_page_text}}下一条产品标题
                                </a>
                            </div>
                        <# } else { #>
                                <# if(data.page_dttitle==1) { #>
                                    <div class="btnBoxt">
                                        <a href="#">{{data.up_page_text}}:标题名称标题标题</a>
                                        <a href="#">{{data.next_page_text}}:标题名称标题标题</a>
                                    </div>
                                <# }else{ #>
                                    <div class="btnBox">
                                        <a href="#">{{data.up_page_text}}</a>
                                        <a href="#">{{data.next_page_text}}</a>
                                    </div>
                                <# } #>
                        <# } #>
                    <# } #>
                </div>
            </div>
        <# } #>
        ';
         // $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/css/owl.carousel.css" type="text/css" />';
         //    $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/css/jquery.bxslider.css" type="text/css" />';
         //    $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/jquery-1.8.3.min.js"></script>';
         //    $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/owl.carousel.js"></script>';
         //    $output .= ' <script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8_productInfo/assets/js/jquery.bxslider.min.js"></script>';
        return $output;
    }

}
