<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWor<PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonHonor extends JwpagefactoryAddons
{

    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        
		$adv_typeid = (isset($settings->adv_typeid) && $settings->adv_typeid) ? $settings->adv_typeid : '0';
		$bgimage = (isset($settings->bgimage) && $settings->bgimage) ? $settings->bgimage : 'https://oss.lc<PERSON>b01.cn/joomla/20220518/96529d63c154443df192811e57e6578b.jpg';
		$link = (isset($settings->link) && $settings->link) ? $settings->link : 'javascript:;';
		$openjs = (isset($settings->openjs) && $settings->openjs) ? $settings->openjs : '1';

		$tz_page_type = (isset($settings->tz_page_type) && $settings->tz_page_type) ? $settings->tz_page_type : 'external_links';
		$detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : '';
		$media_target = (isset($settings->media_target) && $settings->media_target) ? $settings->media_target : '';

		$button_width = (isset($settings->button_width) && $settings->button_width) ? $settings->button_width : '160';
		$button_height = (isset($settings->button_height) && $settings->button_height) ? $settings->button_height : '55';
		$button_cont = (isset($settings->button_cont) && $settings->button_cont) ? $settings->button_cont : '了解更多';
		$button_size = (isset($settings->button_size) && $settings->button_size) ? $settings->button_size : '16';
		$button_color = (isset($settings->button_color) && $settings->button_color) ? $settings->button_color : '#000';
		$button_colorhv = (isset($settings->button_colorhv) && $settings->button_colorhv) ? $settings->button_colorhv : '#fff';
		$button_bg = (isset($settings->button_bg) && $settings->button_bg) ? $settings->button_bg : '#d0111b';
		
		$button_url ='';
		if($tz_page_type=='Internal_pages'){
			$thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&layout_id=' . $layout_id;
            $button_url = $thisUrl;
		}else{
			$button_url =$link;
		}

		$output = '';
		$output .= '
			<style>
			
			    '.$addon_id.' .f_more {
					font-size: '.$button_size.'px;
					width: '.$button_width.'px;
					height: '.$button_height.'px;
					border: solid 1px #949494;
					border-radius: 26px;
					text-align: center;
					line-height: calc('.$button_height.'px - 5px);
					display: block;
					position: relative;
					overflow: hidden;
					color:'.$button_color.';
				}
				'.$addon_id.' .f_more:before {
					content: "";
					width: 0;
					height: 100%;
					background: '.$button_bg.';
					position: absolute;
					right: 0;
					bottom: 0;
					transition: 0.5s;
					z-index: -1;
				}
				'.$addon_id.' .f_more:after {
					position: absolute;
					left: 0;
					bottom: 0;
					z-index: -2;
					content: "";
					box-sizing: border-box;
					width: 100%;
					height: 100%;
					border-radius: 28px;
				}
				'.$addon_id.' .f_more:hover,
				'.$addon_id.' .f_more.active {
					color: '.$button_colorhv.';
					border-color: '.$button_bg.';
					transition-delay: 0.1s;
				}
				'.$addon_id.' .f_more:hover:before,
				'.$addon_id.' .f_more.active:before {
					width: 100%;
					right: auto;
					left: 0;
				}
				@media only screen and (min-width: 1480px) {
					'.$addon_id.' .about3-a1 {
						width: 100%;
						height: 640px;
						position: relative;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a2 {
						width: 50%;
						height: 640px;
						position: relative;
						margin-left: 50%;
					}
					'.$addon_id.' .about3-a2 img {
						width: 100%;
						min-height: 100%;
					}
					'.$addon_id.' .about3-a3 {
						width: 100%;
						position: absolute;
						top: 50px;
						left: 50px;
					}
					'.$addon_id.' .about3-a3 .about0-b1 {
						margin-bottom: 40px;
						text-align: left;
					}
					'.$addon_id.' .about3-a4 {
						width: 90%;
						height: 518px;
						position: relative;
					}
					'.$addon_id.' .about3-a4:after {
						content: "";
						display: block;
						clear: both;
					}
					'.$addon_id.' .about3-a5 {
						width: 40%;
						height: 100%;
						position: relative;
						float: left;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a6 {
						width: 100%;
						height: calc(52px * 8);
						overflow: hidden;
						margin-bottom: 45px;
					}
					'.$addon_id.' .about3-a6:hover {
						overflow-y: auto;
					}
					'.$addon_id.' .about3-a6::-webkit-scrollbar {
						width: 2px;
					}
					'.$addon_id.' .about3-a6>div {
						font-size: 24px;
						line-height: 52px;
						color: #454545;
						transition: 0.5s;
						cursor: pointer;
						white-space: nowrap;
						overflow: hidden;
						width: 97%;
						text-overflow: ellipsis;
					}
					'.$addon_id.' .about3-a6>div.on1 {
						font-weight: bolder;
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a7 {
						width: 50%;
						height: 392px;
						position: relative;
						float: left;
						top: 90px;
					}
					'.$addon_id.' .about3-a7>div {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: none;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a7>div img {
						width:auto;
						max-width: 100%;
						height: 100%;
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a7>div:hover img {
						transform: scale(1.08);
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a8 {
						position: absolute;
						bottom: 42px;
						right: 0;
					}
					'.$addon_id.' .about3-a8>span:first-child {
						font-size: 28px;
						line-height: 28px;
						color: #404040;
					}
					'.$addon_id.' .about3-a8>span:last-child {
						font-size: 18px;
						line-height: 28px;
						color: #747474;
					}
					'.$addon_id.' .about3-b1 {
						display: none;
					}
				}
				@media only screen and (max-width: 1479px) and (min-width: 1024px) {
					'.$addon_id.' .about3-a1 {
						width: 100%;
						height: 460px;
						position: relative;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a2 {
						width: 50%;
						height: 460px;
						position: relative;
						margin-left: 50%;
					}
					'.$addon_id.' .about3-a2 img {
						width: 100%;
						min-height: 100%;
					}
					'.$addon_id.' .about3-a3 {
						width: 960px;
						position: absolute;
						top: 50px;
						left: calc(50% - 960px / 2);
						height: 100%;
					}
					'.$addon_id.' .about3-a3 .about0-b1 {
						margin-bottom: 40px;
						text-align: left;
					}
					'.$addon_id.' .about3-a4 {
						width: 100%;
						height: 518px;
						position: relative;
					}
					'.$addon_id.' .about3-a4:after {
						content: "";
						display: block;
						clear: both;
					}
					'.$addon_id.' .about3-a5 {
						width: 436px;
						height: 100%;
						position: relative;
						float: left;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a6 {
						width: 100%;
						height: calc(34px * 8);
						overflow: hidden;
						margin-bottom: 75px;
					}
					'.$addon_id.' .about3-a6:hover {
						overflow-y: auto;
					}
					'.$addon_id.' .about3-a6::-webkit-scrollbar {
						width: 2px;
					}
					'.$addon_id.' .about3-a6>div {
						font-size: 18px;
						line-height: 34px;
						color: #454545;
						transition: 0.5s;
						cursor: pointer;
						white-space: nowrap;
						overflow: hidden;
						width: 97%;
						text-overflow: ellipsis;
					}
					'.$addon_id.' .about3-a6>div.on1 {
						font-weight: bolder;
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a7 {
						width: 390px;
						height: 270px;
						position: relative;
						float: left;
						top: 36px;
					}
					'.$addon_id.' .about3-a7>div {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: none;
						overflow: hidden;
					}
					'.$addon_id.' .about3-a7>div img {
						width:auto;
						max-width: 100%;
						height: 100%;
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a7>div:hover img {
						transform: scale(1.08);
						transition: 0.5s;
					}
					'.$addon_id.' .about3-a8 {
						position: absolute;
						bottom: 200px;
						right: 0;
					}
					'.$addon_id.' .about3-a8>span:first-child {
						font-size: 24px;
						line-height: 24px;
						color: #404040;
					}
					'.$addon_id.' .about3-a8>span:last-child {
						font-size: 16px;
						line-height: 24px;
						color: #747474;
					}
					'.$addon_id.' .about3-b1 {
						display: none;
					}
				}
				@media only screen and (min-width: 1024px) {
					'.$addon_id.' .zzj-pc {
						display: block;
					}
					'.$addon_id.' .zmr-phone {
						display: none;
					}
				}
				@media only screen and (max-width: 1023px) {
					'.$addon_id.' .zzj-pc {
						display: none;
					}
					'.$addon_id.' .zmr-phone {
						display: block;
					}
					
					'.$addon_id.' .z-into-about {
						width: 100%;
						position: relative;
						overflow: hidden;
						z-index: 1;
						padding-bottom:  calc(0.8 * 44px);
					}
					'.$addon_id.' .about3-b2 {
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
					}
					'.$addon_id.' .about3-b2 img {
						width: 100%;
					}
					'.$addon_id.' .about3-b3 {
						width: 100%;
						height:  calc(6.5 * 44px);
						position: relative;
						overflow: hidden;
						margin-top:  calc(0.8 * 44px);
					}
					'.$addon_id.' .about311 {
						width: 100% !important;
						height: 100% !important;
						position: relative;
					}
					'.$addon_id.' .about311 .swiper-slide {
						width: 100% !important;
						height: 100% !important;
						position: relative;
					}
					'.$addon_id.' .about3-b4 {
						width: 100%;
						padding: 0 20px;
						position: relative;
					}
					'.$addon_id.' .about3-b4 img {
						width: 100%;
						max-height: 100%;
					}
					'.$addon_id.' .about3-b5 {
						width: 100%;
						height:  calc(4.32 * 44px);
						position: relative;
						margin-bottom:  calc(0.42 * 44px);
					}
					'.$addon_id.' .about3-b6 {
						width: 100%;
						font-size: 14px;
						color: #676767;
						text-align: center;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					'.$addon_id.' .about3-b7 {
						width: 100%;
						position: absolute;
						bottom:  calc(0.2 * 44px);
						left: 0;
						text-align: center;
					}
					'.$addon_id.' .about3-b7>span:first-child {
						font-size: calc(0.48 * 44px);
						line-height:calc(0.44 * 44px);
						color: #404040;
					}
					'.$addon_id.' .about3-b7>span:last-child {
						font-size:  calc(0.3 * 44px);
						line-height: calc(0.44 * 44px);
						color: #747474;
					}
					'.$addon_id.' .z-into-more {
						background: '.$button_bg.';
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 26px;
						margin: 17px auto 28.5px;
						font-size: '.$button_size.'px;
						width: '.$button_width.'px;
						height: '.$button_height.'px;
						line-height: calc('.$button_height.'px - 5px);
						color:'.$button_colorhv.';

					}
					'.$addon_id.' .z-into-more img {
						width: calc(0.16 * 44px);
						margin: 0 0 0 5px;
					}
			
					'.$addon_id.' .a1 {
						position: relative;
					}
			
					'.$addon_id.' .a1>a {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: block;
					}
				}
				
			</style>
		';

        $hzhb_list = JwPageFactoryBase::getAdvLists($site_id, $company_id,$adv_typeid);
		
        if(!count($hzhb_list)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            return $output;
        }

        if($hzhb_list){
			
        	$output .= '
			<div class="about3-a1 wow fadeInUp zzj-pc animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
				<div class="about3-a2"><img src="'.$bgimage.'" oncontextmenu="return false;"></div>
				<div class="about3-a3">
		
					<div class="about3-a4">
						<div class="about3-a5">
							<div class="about3-a6">';
								foreach ($hzhb_list as $key => $value) {
									$output .= '
										<div class="'; if($key==0){ $output .= 'on1';} $output .= ' ">'.$value->title.'</div>
									';
								}
							$output .= '</div>
							<a href="'.$button_url.'" target="'.$media_target.'" class="f_more">'.$button_cont.'</a>
						</div>
		
						<div class="about3-a7">';
							foreach ($hzhb_list as $key => $value) {
								$output .= '
									<div style="display: none;"><img src="'.$value->images.'" oncontextmenu="return false;"></div>
								';
							}
							$output .= '</div>
						<div class="about3-a8"><span>07</span>/<span>10</span></div>
					</div>
				</div>
			</div>
		
			<div class="z-into-about zmr-phone wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
				<div class="about3-b2"><img src="'.$bgimage.'" oncontextmenu="return false;"></div>
				<div class="about3-b3">

					<div class="swiper-container swiper-container1 about311 swiper-container-horizontal">

						<div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-420px, 0px, 0px);">';
							foreach ($hzhb_list as $key => $value) {
								$output .= '
									<div class="swiper-slide swiper-slide-duplicate" data-swiper-slide-index="'.$key.'" style="width: 210px;">
										<div class="about3-b4">
											<div class="about3-b5"><img src="'.$value->images.'" alt="" oncontextmenu="return false;">
											</div>
											<div class="about3-b6">'.$value->title.'</div>
										</div>
									</div>

								';
							}

						$output .= '</div><div class="about3-b7 swiper-pagination"></div>
						
					</div>

				</div>
				<div class="z-into-more a1">
					<a href="'.$button_url.'" target="'.$media_target.'"></a>'.$button_cont.' 
				</div>
			</div>';
			if($openjs==1){
				$output .= '<script src="https://ijzt.china9.cn/components/com_jwpagefactory/assets/js/swiper-bundle.min.js"></script>';
			}
	
			$output .= '<script type="text/javascript">

				$("'.$addon_id.' .about3-a6>div").eq(0).addClass("on1");
				$("'.$addon_id.' .about3-a7>div").eq(0).show();
				if($("'.$addon_id.' .about3-a6>div").length < 10) {
					$("'.$addon_id.' .about3-a8>span").last().html("0" + $("'.$addon_id.' .about3-a6>div").length);
				} else {
					$("'.$addon_id.' .about3-a8>span").last().html($("'.$addon_id.' .about3-a6>div").length);
				}
				$("'.$addon_id.' .about3-a8>span").first().html("01");
				$("'.$addon_id.' .about3-a6>div").click(function() {
					$("'.$addon_id.' .about3-a6>div").removeClass("on1").eq($(this).index()).addClass("on1");
					$("'.$addon_id.' .about3-a7>div").hide().eq($(this).index()).show();
					if(($(this).index() + 1) < 10) {
						$("'.$addon_id.' .about3-a8>span").first().html("0" + ($(this).index() + 1));
					} else {
						$("'.$addon_id.' .about3-a8>span").first().html($(this).index() + 1);
					}
				})
		
				//手机
				if ($(window).width() < 1024) {
					var about311 = new Swiper("'.$addon_id.' .about311", {
						normalizeSlideIndex: true,
						autoplay: {
							delay: 5000,
							disableOnInteraction: false,
						},
						pagination: {
							el: ".swiper-pagination",
							type: "fraction"
						}
					})
				} 
				
			</script> 
			';

        }

        return $output;
    }

    public function scripts()
    {

    }

    public  function js()
	{
       
    }

    public function css()
    {
        
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
			var addonId = "#jwpf-addon-"+data.id;
			var img_zt = data.img_zt || "qx";
			
			var button_width = data.button_width || "160";
			var button_height = data.button_height || "55";

			var button_cont = data.button_cont || "了解更多";
			var button_size = data.button_size || "16";
			var button_color = data.button_color || "#000";
			var button_colorhv = data.button_colorhv || "#fff";
			var button_bg = data.button_bg || "#d0111b";
		
		#>
        	<style>
			
				{{addonId}} .ind6-a4:nth-child(1) {
			        animation: ind666 linear 4s infinite;
			        animation-delay: -1.583629850744975s;
			    }
			    {{addonId}} .f_more {
					font-size: {{button_size}}px;
					width: {{button_width}}px;
					height: {{button_height}}px;
					border: solid 1px #949494;
					border-radius: 26px;
					text-align: center;
					line-height: calc( {{button_height}}px - 5px );
					display: block;
					position: relative;
					overflow: hidden;
					color:{{button_color}};
				}
				{{addonId}} .f_more:before {
					content: "";
					width: 0;
					height: 100%;
					background: {{button_bg}};
					position: absolute;
					right: 0;
					bottom: 0;
					transition: 0.5s;
					z-index: -1;
				}
				{{addonId}} .f_more:after {
					position: absolute;
					left: 0;
					bottom: 0;
					z-index: -2;
					content: "";
					box-sizing: border-box;
					width: 100%;
					height: 100%;
					border-radius: 28px;
				}
				{{addonId}} .f_more:hover,
				{{addonId}} .f_more.active {
					color: {{button_colorhv}};
					border-color: {{button_bg}};
					transition-delay: 0.1s;
				}
				{{addonId}} .f_more:hover:before,
				{{addonId}} .f_more.active:before {
					width: 100%;
					right: auto;
					left: 0;
				}
				@media only screen and (min-width: 1480px) {
					{{addonId}} .about3-a1 {
						width: 100%;
						height: 640px;
						position: relative;
						overflow: hidden;
					}
					{{addonId}} .about3-a2 {
						width: 960px;
						height: 640px;
						position: relative;
						margin-left: 50%;
					}
					{{addonId}} .about3-a2 img {
						width: 100%;
						min-height: 100%;
					}
					{{addonId}} .about3-a3 {
						width: 1440px;
						position: absolute;
						top: 50px;
						left: calc(50% - 1440px / 2);
					}
					{{addonId}} .about3-a3 .about0-b1 {
						margin-bottom: 40px;
						text-align: left;
					}
					{{addonId}} .about3-a4 {
						width: 100%;
						height: 518px;
						position: relative;
					}
					{{addonId}} .about3-a4:after {
						content: "";
						display: block;
						clear: both;
					}
					{{addonId}} .about3-a5 {
						width: 630px;
						height: 100%;
						position: relative;
						float: left;
						overflow: hidden;
					}
					{{addonId}} .about3-a6 {
						width: 100%;
						height: calc(52px * 8);
						overflow: hidden;
						margin-bottom: 45px;
					}
					{{addonId}} .about3-a6:hover {
						overflow-y: auto;
					}
					{{addonId}} .about3-a6::-webkit-scrollbar {
						width: 2px;
					}
					{{addonId}} .about3-a6>div {
						font-size: 24px;
						line-height: 52px;
						color: #454545;
						transition: 0.5s;
						cursor: pointer;
						white-space: nowrap;
						overflow: hidden;
						width: 97%;
						text-overflow: ellipsis;
					}
					{{addonId}} .about3-a6>div.on1 {
						font-weight: bolder;
						transition: 0.5s;
					}
					{{addonId}} .about3-a7 {
						width: 602px;
						height: 392px;
						position: relative;
						float: left;
						top: 90px;
					}
					{{addonId}} .about3-a7>div {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: none;
						overflow: hidden;
					}
					{{addonId}} .about3-a7>div img {
						width: auto;
						max-width:100%;
						height: 100%;
						transition: 0.5s;
					}
					{{addonId}} .about3-a7>div:hover img {
						transform: scale(1.08);
						transition: 0.5s;
					}
					{{addonId}} .about3-a8 {
						position: absolute;
						bottom: 42px;
						right: 0;
					}
					{{addonId}} .about3-a8>span:first-child {
						font-size: 28px;
						line-height: 28px;
						color: #404040;
					}
					{{addonId}} .about3-a8>span:last-child {
						font-size: 18px;
						line-height: 28px;
						color: #747474;
					}
					{{addonId}} .about3-b1 {
						display: none;
					}
				}
				@media only screen and (max-width: 1479px) and (min-width: 1024px) {
					{{addonId}} .about3-a1 {
						width: 100%;
						height: 460px;
						position: relative;
						overflow: hidden;
					}
					{{addonId}} .about3-a2 {
						width: 50%;
						height: 460px;
						position: relative;
						margin-left: 50%;
					}
					{{addonId}} .about3-a2 img {
						width: 100%;
						min-height: 100%;
					}
					{{addonId}} .about3-a3 {
						width: 960px;
						position: absolute;
						top: 50px;
						left: calc(50% - 960px / 2);
						height: 100%;
					}
					{{addonId}} .about3-a3 .about0-b1 {
						margin-bottom: 40px;
						text-align: left;
					}
					{{addonId}} .about3-a4 {
						width: 100%;
						height: 518px;
						position: relative;
					}
					{{addonId}} .about3-a4:after {
						content: "";
						display: block;
						clear: both;
					}
					{{addonId}} .about3-a5 {
						width: 436px;
						height: 100%;
						position: relative;
						float: left;
						overflow: hidden;
					}
					{{addonId}} .about3-a6 {
						width: 100%;
						height: calc(34px * 8);
						overflow: hidden;
						margin-bottom: 75px;
					}
					{{addonId}} .about3-a6:hover {
						overflow-y: auto;
					}
					{{addonId}} .about3-a6::-webkit-scrollbar {
						width: 2px;
					}
					{{addonId}} .about3-a6>div {
						font-size: 18px;
						line-height: 34px;
						color: #454545;
						transition: 0.5s;
						cursor: pointer;
						white-space: nowrap;
						overflow: hidden;
						width: 97%;
						text-overflow: ellipsis;
					}
					{{addonId}} .about3-a6>div.on1 {
						font-weight: bolder;
						transition: 0.5s;
					}
					{{addonId}} .about3-a7 {
						width: 390px;
						height: 270px;
						position: relative;
						float: left;
						top: 36px;
					}
					{{addonId}} .about3-a7>div {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: none;
						overflow: hidden;
					}
					{{addonId}} .about3-a7>div img {
						width:auto;
						max-width: 100%;
						height: 100%;
						transition: 0.5s;
					}
					{{addonId}} .about3-a7>div:hover img {
						transform: scale(1.08);
						transition: 0.5s;
					}
					{{addonId}} .about3-a8 {
						position: absolute;
						bottom: 200px;
						right: 0;
					}
					{{addonId}} .about3-a8>span:first-child {
						font-size: 24px;
						line-height: 24px;
						color: #404040;
					}
					{{addonId}} .about3-a8>span:last-child {
						font-size: 16px;
						line-height: 24px;
						color: #747474;
					}
					{{addonId}} .about3-b1 {
						display: none;
					}
				}
				@media only screen and (min-width: 1024px) {
					{{addonId}} .zzj-pc {
						display: block;
					}
					{{addonId}} .zmr-phone {
						display: none;
					}
				}
				@media only screen and (max-width: 1023px) {
					{{addonId}} .zzj-pc {
						display: none;
					}
					{{addonId}} .zmr-phone {
						display: block;
					}
					
					{{addonId}} .z-into-about {
						width: 100%;
						position: relative;
						overflow: hidden;
						z-index: 1;
						padding-bottom:  calc(0.8 * 44px);
					}
					{{addonId}} .about3-b2 {
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
					}
					{{addonId}} .about3-b2 img {
						width: 100%;
					}
					{{addonId}} .about3-b3 {
						width: 100%;
						height:  calc(6.5 * 44px);
						position: relative;
						overflow: hidden;
						margin-top:  calc(0.8 * 44px);
					}
					{{addonId}} .about311 {
						width: 100% !important;
						height: 100% !important;
						position: relative;
					}
					{{addonId}} .about311 .swiper-slide {
						width: 100% !important;
						height: 100% !important;
						position: relative;
					}
					{{addonId}} .about3-b4 {
						width: 100%;
						padding: 0 20px;
						position: relative;
					}
					{{addonId}} .about3-b4 img {
						width: 100%;
						max-height: 100%;
					}
					{{addonId}} .about3-b5 {
						width: 100%;
						height:  calc(4.32 * 44px);
						position: relative;
						margin-bottom:  calc(0.42 * 44px);
					}
					{{addonId}} .about3-b6 {
						width: 100%;
						font-size:  calc(0.3 * 44px);
						color: #676767;
						text-align: center;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					{{addonId}} .about3-b7 {
						width: 100%;
						position: absolute;
						bottom:  calc(0.2 * 44px);
						left: 0;
						text-align: center;
					}
					{{addonId}} .about3-b7>span:first-child {
						font-size: calc(0.48 * 44px);
						line-height:calc(0.44 * 44px);
						color: #404040;
					}
					{{addonId}} .about3-b7>span:last-child {
						font-size:  calc(0.3 * 44px);
						line-height: calc(0.44 * 44px);
						color: #747474;
					}
					{{addonId}} .z-into-more {
						width: {{button_width}}px;
						height: {{button_height}}px;
						background: {{button_bg}};
						color: {{button_colorhv}};
						font-size: {{button_size}}px;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 26px;
						margin: 17px auto 28.5px;
					}
					{{addonId}} .z-into-more img {
						width: calc(0.16 * 44px);
						margin: 0 0 0 5px;
					}
			
					{{addonId}} .a1 {
						position: relative;
					}
			
					{{addonId}} .a1>a {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						display: block;
					}
				}
				
			</style>
			
			<div class="about3-a1 wow fadeInUp zzj-pc animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
				<div class="about3-a2"><img src="https://oss.lcweb01.cn/joomla/20220608/189a32de3f933413e6648aa2bbd31e04.jpg" oncontextmenu="return false;"></div>
				<div class="about3-a3">

					<div class="about3-a4">

						<div class="about3-a5">

							<div class="about3-a6">

								<div class="on1">信息技术服务管理体系认证</div>
								
								<div class="">信息安全管理体系认证</div>
								
								<div class="">涉密信息系统集成资质</div>

								<div class="">CNNIC-IP地址分配联盟成员证书</div>
								
								<div class="">高新技术企业认证</div>
								
								<div class="">软件企业认定证书</div>
								
								<div class="">软件产品登记证书</div>

								<div class="">中国互联网协会理事单位证书</div>

								<div class="">国家信息系统安全等级保护三级</div>

								<div class="">增值电信业务经营许可</div>
								
							</div>

							<a href="" class="f_more">{{button_cont}}</a>

						</div>

						<div class="about3-a7">

							
							<div style="display:block;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							<div style="display: none;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/96cc0a7002dffa0a2cdd5d616040d401.jpeg" oncontextmenu="return false;"></div>
							
						</div>

						<div class="about3-a8"><span>07</span>/<span>10</span></div>

					</div>
				</div>
			</div>
    
    		<div class="z-into-about zmr-phone wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">

				<div class="about3-b2"><img src="https://oss.lcweb01.cn/joomla/20220608/189a32de3f933413e6648aa2bbd31e04.jpg" oncontextmenu="return false;"></div>

				<div class="about3-b3">

					<div class="swiper-container swiper-container1 about311 swiper-container-horizontal">

						<div class="swiper-wrapper" >
						<div class="swiper-slide swiper-slide-duplicate" data-swiper-slide-index="8" style="width: 210px;">

								<div class="about3-b4">

									<div class="about3-b5"><img src="https://oss.lcweb01.cn/joomla/20220608/45f5f2031c5f1bff86c3e930cc6656ab.jpg" alt="" oncontextmenu="return false;">

									</div>

									<div class="about3-b6">国家信息系统安全等级保护三级</div>

								</div>
						</div>
						<div class="swiper-slide swiper-slide-duplicate swiper-slide-prev" data-swiper-slide-index="9" style="width: 210px;">

								<div class="about3-b4">

									<div class="about3-b5"><img src="https://oss.lcweb01.cn/joomla/20220608/45f5f2031c5f1bff86c3e930cc6656ab.jpg" alt="" oncontextmenu="return false;">

									</div>

									<div class="about3-b6">增值电信业务经营许可</div>

								</div>
						</div>
							
						<div class="swiper-slide swiper-slide-active" data-swiper-slide-index="0" style="width: 210px;">

								<div class="about3-b4">

									<div class="about3-b5"><img src="https://oss.lcweb01.cn/joomla/20220608/45f5f2031c5f1bff86c3e930cc6656ab.jpg" alt="" oncontextmenu="return false;">

									</div>

									<div class="about3-b6">信息技术服务管理体系认证</div>

								</div>

						</div>

						<div class="swiper-slide swiper-slide-next" data-swiper-slide-index="1" style="width: 210px;">

								<div class="about3-b4">

									<div class="about3-b5"><img src="https://oss.lcweb01.cn/joomla/20220608/45f5f2031c5f1bff86c3e930cc6656ab.jpg" alt="" oncontextmenu="return false;">

									</div>

									<div class="about3-b6">信息安全管理体系认证</div>

								</div>

						</div>

						<div class="swiper-slide swiper-slide-duplicate-prev" data-swiper-slide-index="9" style="width: 210px;">

							<div class="about3-b4">

								<div class="about3-b5"><img src="https://oss.lcweb01.cn/joomla/20220608/45f5f2031c5f1bff86c3e930cc6656ab.jpg" alt="" oncontextmenu="return false;">

								</div>

								<div class="about3-b6">增值电信业务经营许可</div>

							</div>

						</div>

					</div>

					<div class="about3-b7 swiper-pagination"></div>

					</div>

				</div>

				<div class="z-into-more a1">

					<a href=""></a>{{button_cont}} <img src="https://oss.lcweb01.cn/joomla/20220608/c6c7536213d714e529bc473be6958904.png" alt="" oncontextmenu="return false;">

				</div>
			</div>

		';

        return $output;
    }
}
