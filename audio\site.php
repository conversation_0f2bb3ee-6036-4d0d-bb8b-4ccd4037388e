<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonAudio extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$style = (isset($settings->style) && $settings->style) ? $settings->style : 'panel-default';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		// Addon options
		$mp3_link = (isset($settings->mp3_link) && $settings->mp3_link) ? $settings->mp3_link : '';
		$ogg_link = (isset($settings->ogg_link) && $settings->ogg_link) ? $settings->ogg_link : '';
		$autoplay = (isset($settings->autoplay) && $settings->autoplay) ? $settings->autoplay : 0;
		$repeat = (isset($settings->repeat) && $settings->repeat) ? $settings->repeat : 0;

		//样式2
		$music_list = (isset($settings->music_list) && $settings->music_list) ? $settings->music_list : 'type1';
		$music_img = (isset($settings->music_img) && $settings->music_img) ? $settings->music_img : 'https://oss.lcweb01.cn/joomla/20230531/6c2010f5c6dc8de7d3e5effa92535cef.png';
		$music_title = (isset($settings->music_title) && $settings->music_title) ? $settings->music_title : '协会会歌';
		$music_author = (isset($settings->music_author) && $settings->music_author) ? $settings->music_author : 'CHQA';
		$music_ypimg = (isset($settings->music_ypimg) && $settings->music_ypimg) ? $settings->music_ypimg : 'https://oss.lcweb01.cn/joomla/20230531/2b6e7358c32be79a37e033d51d8e5f23.png';
		$music_type2_width = (isset($settings->music_type2_width) && $settings->music_type2_width) ? $settings->music_type2_width : '1000';
		$music_type2_bg = (isset($settings->music_type2_bg) && $settings->music_type2_bg) ? $settings->music_type2_bg : '#F5F5F5';
		
		//
		$output = '';
		if($music_list=='type2'){
			$output .= '<style>
				'.$addon_id.' .demo {
				    width: '.$music_type2_width.'px;
				    margin: 0px auto;
				    border-radius: 10px;
				}

				'.$addon_id.' .demo p {
				    padding: 10px 0;
				    position: relative;
				    left: 16px;
				    opacity: 0.5;
				}
				@font-face{font-family:aplayer-fontello;src:url(' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/font/aplayer-fontello.eot?72550380);
				    src:url(' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/font/aplayer-fontello.eot?72550380#iefix) 
				    format("embedded-opentype"),
				    url(' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/font/aplayer-fontello.woff?72550380) 
				    format("woff"),url(' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/font/aplayer-fontello.ttf?72550380) 
				    format("truetype"),url(' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/font/aplayer-fontello.svg?72550380#fontello) 
				    format("svg");
				    font-weight:400;font-style:normal}
				    '.$addon_id.' .aplayer-narrow{width:66px}
				    '.$addon_id.' .aplayer-narrow .aplayer-info{display:none}
				    '.$addon_id.' .aplayer-withlrc.aplayer-narrow{width:106px}
				    '.$addon_id.'.aplayer-withlrc.aplayer .aplayer-pic{height:100%;width:96px}
				     aplayer-withlrc.aplayer .aplayer-info{margin-left:106px;height:106px}
				    '.$addon_id.' .aplayer-withlrc.aplayer .aplayer-lrc{display:none}
				    '.$addon_id.' .aplayer{
				        font-family: Arial,Helvetica,sans-serif;
				        padding: 5px;   
				        -webkit-user-select: none;
				        -moz-user-select: none;
				        -ms-user-select: none;
				        user-select: none;
				        background-color: '.$music_type2_bg.';
				        height:106px;
				    }
				    '.$addon_id.' .aplayer [class*=" aplayer-icon-"]:before,
				    '.$addon_id.' .aplayer [class^=aplayer-icon-]:before{font-family:aplayer-fontello;font-style:normal;font-weight:400;display:inline-block;text-decoration:inherit;width:1em;text-align:center;font-variant:normal;text-transform:none;line-height:1em}
				    '.$addon_id.' .aplayer .aplayer-lrc-content,.aplayer .aplayer-time .aplayer-hide{display:none}
				    '.$addon_id.' .aplayer .aplayer-icon-weibo:before{content:"\e805"}
				    '.$addon_id.' .aplayer .aplayer-icon-play:before{content:"\e806"}
				    '.$addon_id.' .aplayer .aplayer-icon-pause:before{content:"\e807"}
				    '.$addon_id.' .aplayer .aplayer-icon-to-start:before{content:"\e808"}
				    '.$addon_id.' .aplayer .aplayer-icon-to-end:before{content:"\e809"}
				    '.$addon_id.' .aplayer .aplayer-icon-list:before{content:"\e80a"}
				    '.$addon_id.' .aplayer .aplayer-icon-menu:before{content:"\e80b"}
				    '.$addon_id.' .aplayer .aplayer-icon-volume-off:before{content:"\e800"}
				    '.$addon_id.' .aplayer .aplayer-icon-volume-down:before{content:"\e801"}
				    '.$addon_id.' .aplayer .aplayer-icon-volume-up:before{content:"\e802"}
				    '.$addon_id.' .aplayer span{cursor:default!important}
				    '.$addon_id.' .aplayer .aplayer-pic{position:relative;float:left;}
				    '.$addon_id.' .aplayer .aplayer-pic img{
				        height: 100%;
				        width: 96px;
				    }

				    '.$addon_id.' .blx{
				        margin: 0 120px 0 5px;position: absolute;z-index:22;
				    }
				    '.$addon_id.' .blx img{width:100%;}
				    '.$addon_id.' .tims{float:left;margin-top:-10px;}
				    '.$addon_id.' .aplayer-ptime{color:#333;font-size:13px;display:inline-block;}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-button{position:absolute;color:#fff;-webkit-border-radius:50%;border-radius:50%;opacity:.8;cursor:pointer;text-shadow:0 1px 1px rgba(0,0,0,.2);-webkit-box-shadow:0 1px 1px rgba(0,0,0,.2);box-shadow:0 1px 1px rgba(0,0,0,.2);background:#336CFB}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-button:hover{opacity:1}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-play{width:26px;height:26px;border:2px solid #fff;top:50%;right:30px;margin:-15px 0 0 -15px}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-play .aplayer-icon-play{position:absolute;top:-1px;left:5px;font-size:20px;line-height:23px}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-pause{width:26px;height:26px;border:2px solid #fff;top:50%;right:30px;margin:-15px 0 0 -15px}
				    '.$addon_id.' .aplayer .aplayer-time .aplayer-pause .aplayer-icon-pause{position:absolute;top:4px;left:4px;font-size:12px;line-height:14px}
				    '.$addon_id.' .aplayer .aplayer-volume-wrap{width:26px;height:26px;border:2px solid #fff;top:50%;right:20px;margin:-15px 0 0 -15px}
				    '.$addon_id.' .aplayer .aplayer-pause i{position: absolute;top:1px;left:4px;}
				    '.$addon_id.' .aplayer .tist{float:left;width:105px;padding:20px 10px;height:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;}
				    '.$addon_id.' .aplayer .tist div{line-height:30px;}

				    '.$addon_id.' .aplayer .aplayer-info{margin-left:201px;padding:10px 7px 0 0px;height:106px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-music{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin-bottom:17px;display: none;}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-music .aplayer-title{font-size:14px}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-music .aplayer-author{font-size:12px;color:#666}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller{position:relative;margin-top:20px;}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap{margin:0 120px 0 5px;position: absolute;
				        height: 36px;
				        width: calc(100% - 125px);
				        top: 0px;
				    }
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar{position:relative;height:100%;width:100%;background:#D4D3D3;cursor:pointer!important}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded{width:100%;position:absolute;left:0;top:0;bottom:0;background:#D4D3D3;height:100%;-webkit-transition:all .5s ease;transition:all .5s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played{position:absolute;left:0;top:0;bottom:0;background:#336CFB;height:100%}

				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb:hover{background:#336CFB}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-time{position:absolute;right:0;top:8px;height:20px;color:#999;font-size:11px;width:100px;}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-time i{color:#fff;font-size:15px;}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap{display:inline-block;cursor:pointer!important;position: absolute;right:0px;}

				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap i{position: absolute;top:0px;left:4px;}


				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap:hover .aplayer-volume-bar-wrap{display:block}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap{display:none;position:absolute;bottom:20px;right:0px;width:25px;height:40px;z-index:99}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar{position:absolute;bottom:0;right:10px;width:5px;height:35px;background:#aaa}
				    '.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar .aplayer-volume{position:absolute;bottom:0;right:0;width:5px;background:#336CFB}
				    '.$addon_id.' .aplayer .aplayer-lrc{display:none;position:relative;height:40px;background:#fff;text-align:center;overflow:hidden;margin:-10px 0 10px}
				    '.$addon_id.' .aplayer .aplayer-lrc:after,.aplayer .aplayer-lrc:before{position:absolute;z-index:1;display:block;overflow:hidden;content:"";width:100%}
				    '.$addon_id.' .aplayer .aplayer-lrc:before{top:0;height:10%;background:-webkit-linear-gradient(top,#fff 0,rgba(255,255,255,0) 100%);background:-webkit-gradient(linear,left top,left bottom,from(white),to(rgba(255,255,255,0)));background:linear-gradient(to bottom,#fff 0,rgba(255,255,255,0) 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#00ffffff", GradientType=0 )}
				    '.$addon_id.' .aplayer .aplayer-lrc:after{bottom:0;height:33%;background:-webkit-linear-gradient(bottom,#fff 0,rgba(255,255,255,0) 100%);background:-webkit-gradient(linear,left bottom,left top,from(white),to(rgba(255,255,255,0)));background:linear-gradient(to top,#fff 0,rgba(255,255,255,0) 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr="#00ffffff", endColorstr="#ffffff", GradientType=0 )}
				    '.$addon_id.' .aplayer .aplayer-lrc p{font-size:12px;color:#666;line-height:20px;padding:0;margin:0;-webkit-transition:all .5s ease-out;transition:all .5s ease-out;opacity:.4}
				    '.$addon_id.' .aplayer .aplayer-lrc p.aplayer-lrc-current{opacity:1}
				    '.$addon_id.' .aplayer .aplayer-lrc .aplayer-lrc-contents{width:100%;-webkit-transition:all .5s ease-out;transition:all .5s ease-out}

				    @media (max-width: 1200px) {
						'.$addon_id.' .demo{
							width:100%;
						}
				    }
				    @media (max-width: 768px) {
				    	'.$addon_id.' .aplayer{
				    		height:76px;
				    	}
						'.$addon_id.' .aplayer-withlrc.aplayer .aplayer-pic{width:66px;}
						'.$addon_id.' .aplayer .tist{width:85px;padding:10px 5px;}
						'.$addon_id.' .aplayer .tist div{line-height:25px;}
						'.$addon_id.' .aplayer .aplayer-info{height:66px;margin-left:140px;padding: 15px 6px 0 0px;}
						'.$addon_id.' .aplayer .aplayer-info .aplayer-controller{margin-top:5px;}
						'.$addon_id.' .tist div:first-child{font-size:14px!important;}
						'.$addon_id.' .tist div:second-child{font-size:12px!important;}
						'.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-time{width:95px;}
						'.$addon_id.' .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap{height:26px;width:calc(100% - 115px)!important;}
						'.$addon_id.' .blx{height:26px!important;width:calc(100% - 115px)!important;}
				    }
			</style>';
			$output .= '
				<div id="header">
				</div>
				<div id="main">
					<div class="demo">
				        <div id="player3" class="aplayer">
				            <pre class="aplayer-lrc-content">
				                [00:00.00]Jar Of Love
				                [00:00.08]演唱：曲婉婷
				                [00:00.22]
				                [00:01.00]Another sunrise, another sunset
				                [00:05.10]Soon it"ll all be yesterday
				                [00:08.33]Another good day, another bad day,
				                [00:12.77]What did you do today?
				                [00:15.95]Why do we choose to chase what we"ll lose?
				                [00:19.77]What you want isn"t what you have.
				                [00:23.34]What you have may not be yours, to keep.
				                [00:31.29]If I could find love, at a stop, in a park with open arms,
				            </pre>
				        </div>
					</div>
				</div>
                <script src="' . JURI::base(true) . '/components/com_jwpagefactory/addons/audio/assets/js/APlayer.min.js"></script>
				<script>
				        
			        var ap3 = new APlayer({
			            element: document.getElementById("player3"),//样式1
			            narrow: false,
			            autoplay: false,
			            showlrc: true,
			            music: {
			                title: "'.$music_title.'",
			                author: "'.$music_author.'",
			                url: "'.$mp3_link.'",
			                pic: "'.$music_img.'",
			                yppic: "'.$music_ypimg.'"
			            }
			        });
			        ap3.init();
			        
				</script>
			';
		}else{

			$output .= '<div class="jwpf-addon jwpf-addon-audio ' . $class . '">';

			if ($title) {
				$output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
			}

			$output .= '<div class="jwpf-addon-content">';
			$output .= '<audio controls ' . $autoplay . ' ' . $repeat . '>';
			$output .= '<source src="' . $mp3_link . '" type="audio/mp3">';
			$output .= '<source src="' . $ogg_link . '" type="audio/ogg">';
			$output .= 'Your browser does not support the audio element.';
			$output .= '</audio>';
			$output .= '</div>';

			$output .= '</div>';
		}

		return $output;

	}

	public static function getTemplate()
	{
		$output = '
		<#
			var addonId = "#jwpf-addon-" + data.id;
			var music_list = data.music_list || "type1";
		#>

			<# if(music_list=="type2"){ #>
				<img src="https://oss.lcweb01.cn/joomla/20230601/d70cbbd9e78b2d4c4d51967188d6d7f2.png">
			<# }else{ #>
				<div class="jwpf-addon jwpf-addon-audio {{ data.class }}">
					<# if( !_.isEmpty( data.title ) ){ #><{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{ data.title }}</{{ data.heading_selector }}><# } #>
					<audio controls {{ data.autoplay }} {{ data.repeat }}>
						<source src=\'{{ data.mp3_link }}\' type="audio/mp3">
						<source src=\'{{ data.ogg_link }}\' type="audio/ogg">
					</audio>
				</div>
			<# } #>
		';
		return $output;
	}
}
