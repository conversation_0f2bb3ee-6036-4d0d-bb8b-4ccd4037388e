<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonTal_button extends JwpagefactoryAddons
{
	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$layout_id = $_GET['layout_id'] ?? 0;
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$config = new JConfig();
        $search_type = (isset($settings->search_type)) ? $settings->search_type : 'type1';
        $desc_text = (isset($settings->desc_text)) ? $settings->desc_text : '添加微信好友, 详细了解产品';
        $title_text = (isset($settings->title_text)) ? $settings->title_text : '微信号';
        $search_side = (isset($settings->search_side)) ? $settings->search_side : 'top';
        $div_bg1 = (isset($settings->div_bg1)) ? $settings->div_bg1 : '#522f2f';
        $button_bg2 = (isset($settings->button_bg2)) ? $settings->button_bg2 : '#f84d6a';
        $div_color1 = (isset($settings->div_color1)) ? $settings->div_color1 : '#fff';
        $button_color2 = (isset($settings->button_color2)) ? $settings->button_color2 : '#fff';
        $num_text = (isset($settings->num_text)) ? $settings->num_text : '000000';

		$output = '';
      $output .= "
      <!-- <script src='/components/com_jwpagefactory/addons/tal_button/assets/js/jquery.min.js'></script> -->
                <style>
                     {$addon_id} .box_serch{
                            position:absolute;
                            width: 256.53px;
                            height: 178.02px;
                            background-color: #fff;
                            left: 0;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                            text-align: center;
                        }
                    
                        {$addon_id} .box_serch img{
                            width: 39.84px;
                            height: 39.84px;
                            display: inline-block;
                        }
                        {$addon_id} .box_serch .span1{
                            font-weight: bold;
                            font-size: 18px;
                            line-height: 25px;
                        }
                    
                    
                        {$addon_id}  .white_content { 
                            display: none;  
                            position: fixed;  
                            width:100%;
                            height: 100%;
                            background-color:rgba(0,0,0,0.3);
                            z-index:1002;  
                            overflow: auto;
                            top:0;
                            left:0;   
                        } 
                    
                        {$addon_id} .white_content_div { 
                            position: fixed;  
                            left: 0;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                            z-index:1003;  
                            overflow: auto;  
                            width: 300px;
                            height: 221.48px;
                            background-color: white;
                            border-radius:20px;
                        } 
                    
                        {$addon_id} .div-close { 
                            position: absolute;  
                            right: 0;
                            top: 0;  
                        }
                    
                        {$addon_id} .title{
                          display: inline-block;
                          width: 67%;
                          height: 48px;
                          background-color: {$div_bg1};
                          {$search_side}:0;
                          position: fixed;
                          left: 0;
                          color: {$div_color1};
                          font-size: 13px;
                          line-height: 48px;
                          padding-left: 5%;
                        }
                    
                        {$addon_id} .div-btn{
                          width: 33%;
                          height: 48px;
                          background-color: {$button_bg2};
                          position: fixed;
                          {$search_side}:0;
                          right: 0;
                          font-size: 15px;
                          line-height: 35px;
                          text-align: center;
                          color: {$button_color2};
                          /* vertical-align: middle; */
                          border-radius:0;
                        }
                    
                        {$addon_id} .div-btn img{
                          width: 21px;
                          height: 21px;
                          /* align-items: center; */
                          vertical-align: middle;
                          display: inline-block;
                        }
                    
                        {$addon_id} a{
                          text-decoration: none;
                        }
                    
                        {$addon_id} .box_serch_div{
                          color: #939393;
                          font-size: 15px;
                          height: 40px;
                          line-height: 25px;
                        }
                    
                        {$addon_id} .box_serch .three_fang{
                          position: absolute;
                          bottom: 0;
                          left: 25%;
                          background-color: #1aad17;
                          font-size: 15px;
                          width: 130.42px;
                          height: 40.75px;
                          line-height: 40.75px;
                          border-radius:7px;
                          color: white;
                          font-size: 16px;
                        }
                    
                        {$addon_id} .div-close img{
                          width: 16px;
                          height: 16px;
                          display: inline-block;
                        }  
                    </style>";
                    $output .= '<div id="cardList">
                    <input type="hidden" id="text" value="'.$num_text.'">
                    <input readOnly="true" style="outline: none;border: 0px; color: rgba(0,0,0,0.0);position: absolute;left:-200px; background-color: transparent" id="biao1" value="'.$num_text.'"/>
                    <div id="biaoios" style="position: absolute;left:-200px; color: rgba(0,0,0,0);background-color: transparent" >'.$num_text.'</div>
                    <div class="title">长按复制'.$title_text.'</div>
                    <a class="a_icon div-btn btn" href="javascript:void(0)"><img src="/components/com_jwpagefactory/addons/tal_button/assets/images/wx.png" alt=""> ';
                    if($search_type=='type1')
                    {
                        $output.='复制微信';
                    }
                    else
                    {
                        $output.='复制QQ';
                    }
        $output .=  '</a>
                                   <div id="light" class="white_content">
                                    <div class="white_content_div">
                                        <div class="box_serch">
                                            <img src="/components/com_jwpagefactory/addons/tal_button/assets/images/wechat_success_icon.png" alt=""><br>
                                            <span class="span1">复制成功</span>
                                            <div class="box_serch_div">
                                            '.$title_text.'：<span>'.$num_text.'</span><br>
                                              <span>'.$desc_text.'</span>
                                            </div>';
                        if($search_type=='type1')
                        { 
                            $output.='<div class="three_fang" onclick="openWx()">打开微信</div>';
                        }
                        else
                        {
                            $output.='<div class="three_fang" onclick="location.href=\'http://wpa.qq.com/msgrd?v=3&uin='.$num_text.'&site=qq&menu=yes\'">打开QQ</div>';
                        }
                                            
                    $output .= '      
                                            <a href="javascript:void(0)"  class="div-close"><img src="/components/com_jwpagefactory/addons/tal_button/assets/images/close.png" alt=""></a>
                                        </div>
                                        
                                    </div>
                                    
                                </div>
                    </div>';
                $output.='
                <!-- <script src="/components/com_jwpagefactory/addons/tal_button/assets/js/jquery.min.js"></script> -->
                <script>
                 jQuery(" .div-btn").click(function(){
                        if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
                          var text = document.getElementById(\'biaoios\');
                          //获取隐藏的input，并写入text内容，在进行复制
                          var input = document.getElementById("biao1");
                          input.value = text.innerHTML;
                          input.select();
                          input.setSelectionRange(0, input.value.length);   //兼容ios
                          document.execCommand("Copy");
                          input.blur();
                      }else{
                          var Url2=document.getElementById("biao1");//要复制文字的节点
                            Url2.select(); // 选择对象
                            //document.execCommand("Copy"); // 执行浏览器复制命令
                            $("#biao1").blur();
                            if(document.execCommand(\'copy\', false, null)){
                            var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
                            }
                      }
                     jQuery(" .white_content").fadeIn(500);
                 })
                 jQuery(".div-close").click(function(){
                     jQuery(".white_content").fadeOut(500);
                 });
                 var browser = {
                  versions: function () {
                      var u = navigator.userAgent,
                              app = navigator.appVersion;
                      return {
                          trident: u.indexOf(\'Trident\') > -1, /*IE内核*/
                          presto: u.indexOf(\'Presto\') > -1, /*opera内核*/
                          webKit: u.indexOf(\'AppleWebKit\') > -1, /*苹果、谷歌内核*/
                          gecko: u.indexOf(\'Gecko\') > -1 && u.indexOf(\'KHTML\') == -1, /*火狐内核*/
                          mobile: !!u.match(/AppleWebKit.*Mobile.*/), /*是否为移动终端*/
                          ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), /*ios终端*/
                          android: u.indexOf(\'Android\') > -1 || u.indexOf(\'Linux\') > -1, /*android终端或者uc浏览器*/
                          iPhone: u.indexOf(\'iPhone\') > -1, /*是否为iPhone或者QQHD浏览器*/
                          iPad: u.indexOf(\'iPad\') > -1, /*是否iPad*/
                          webApp: u.indexOf(\'Safari\') == -1, /*是否web应该程序，没有头部与底部*/
                          souyue: u.indexOf(\'souyue\') > -1,
                          superapp: u.indexOf(\'superapp\') > -1,
                          weixin: u.toLowerCase().indexOf(\'micromessenger\') > -1,
                          Safari: u.indexOf(\'Safari\') > -1
                      };
                  }(),
                  language: (navigator.browserLanguage || navigator.language).toLowerCase()
              };
          
                 function openWx(url){
             
                  window.location.href = "weixin://";
                }
                   
          </script>
                ';
		return $output;
	}

	public function css()
	{

	}

	//用于设计器中显示
	public static function getTemplate()
	{
		$output = '';
            $output .= '<style>
            <#
            var addonId = "jwpf-addon-"+data.id;
#>

                #{{ addonId }} .box_serch{
                    position:absolute;
                    width: 256.53px;
                    height: 178.02px;
                    background-color: #fff;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    text-align: center;
                }
            
                #{{ addonId }} .box_serch img{
                    width: 39.84px;
                    height: 39.84px;
                    display: inline-block;
                }
                #{{ addonId }} .box_serch .span1{
                    font-weight: bold;
                    font-size: 18px;
                    line-height: 25px;
                }
            
            
                #{{ addonId }}  .white_content { 
                    display: none;  
                    position: fixed;  
                    width:100%;
                    height: 100%;
                    background-color:rgba(0,0,0,0.5);
                    z-index:1002;  
                    overflow: auto;
                    top:0;
                    left:0;   
                } 
            
                #{{ addonId }} .white_content_div { 
                    position: fixed;  
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    z-index:1003;  
                    overflow: auto;  
                    width: 300px;
                    height: 221.48px;
                    background-color: white;
                    border-radius:20px;
                } 
            
                #{{ addonId }} .div-close { 
                    position: absolute;  
                    right: 0;
                    top: 0;  
                }
            
                #{{ addonId }} .title{
                  display: inline-block;
                  width: 67%;
                  height: 48px;
                  background-color: {{data.div_bg1}};
                  {{data.search_side}}:0;
                  position: fixed;
                  left: 0;
                  color: {{data.div_color1}};
                  font-size: 13px;
                  line-height: 48px;
                  padding-left: 5%;
                }
            
                #{{ addonId }} .div-btn{
                  width: 33%;
                  height: 48px;
                  background-color: {{data.button_bg2}};
                  position: fixed;
                  {{data.search_side}}:0;
                  right: 0;
                  font-size: 15px;
                  line-height: 35px;
                  text-align: center;
                  color: {{data.button_color2}};
                  /* vertical-align: middle; */
                  border-radius:0;
                }
            
                #{{ addonId }} .div-btn img{
                  width: 21px;
                  height: 21px;
                  /* align-items: center; */
                  vertical-align: middle;
                  display: inline-block;
                }
            
                #{{ addonId }} a{
                  text-decoration: none;
                }
            
                #{{ addonId }} .box_serch_div{
                  color: #939393;
                  font-size: 15px;
                  height: 40px;
                  line-height: 25px;
                }
            
                #{{ addonId }} .box_serch .three_fang{
                  position: absolute;
                  bottom: 0;
                  left: 25%;
                  background-color: #1aad17;
                  font-size: 15px;
                  width: 130.42px;
                  height: 40.75px;
                  line-height: 40.75px;
                  border-radius:7px;
                  color: white;
                  font-size: 16px;
                }
            
                #{{ addonId }} .div-close img{
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                } 
            
        </style>';
        $output .= '<div id="cardList">
                    <input type="hidden" id="text" value="{{data.num_text}}">
                    <div class="title">长按复制{{data.title_text}}</div>
                    <a class="a_icon div-btn btn" href="javascript:void(0)"><img src="/components/com_jwpagefactory/addons/tal_button/assets/images/wx.png" alt=""> ';
                    if($search_type=='type1')
                    {
                        $output.='复制微信';
                    }
                    else
                    {
                        $output.='复制QQ';
                    }
        $output .=            '</a>
                                   <div id="light" class="white_content">
                                    <div class="white_content_div">
                                        <div class="box_serch">
                                            <img src="/components/com_jwpagefactory/addons/tal_button/assets/images/wechat_success_icon.png" alt=""><br>
                                            <span class="span1">复制成功</span>
                                            <div class="box_serch_div">
                                            {{data.title_text}}：<span>{{data.num_text}}</span><br>
                                              <span>{{data.desc_text}}</span>
                                            </div>';
                        if($search_type=='type1')
                        {
                            $output.='<div class="three_fang" onclick="openWx()">打开微信</div>';
                        }
                        else
                        {
                            $output.='<div class="three_fang" onclick="location.href=\'http://wpa.qq.com/msgrd?v=3&uin={{data.num_text}}&site=qq&menu=yes\'">打开QQ</div>';
                        }
                                           
                 $output .= ' <a href="javascript:void(0)"  class="div-close"><img src="/components/com_jwpagefactory/addons/tal_button/assets/images/close.png" alt=""></a>
                                        </div>
                                        
                                    </div>
                                    
                                </div>
                    </div>';
                $output.='
                <script>
                function copy(){
                    var str = $("#text").val();
             　　　　var save = function (e){
             　　　　　　e.clipboardData.setData("text/plain",str);//下面会说到clipboardData对象
             　　　　　　e.preventDefault();//阻止默认行为
             　　　　}
             　　　　document.addEventListener("copy",save);
             　　　　document.execCommand("copy");//使文档处于可编辑状态，否则无效
             　　}
                                 
             　　document.getElementById("cardList").addEventListener("click",function(ev){
             　　　　copy(ev.target.innerText)
             
             　　})
                 jQuery(" .div-btn").click(function(){
                     jQuery(" .white_content").fadeIn(500);
                 })
                 jQuery(".div-close").click(function(){
                     jQuery(".white_content").fadeOut(500);
                 });
                 function openWx(){
             
                   locatUrl = "weixin://";
             
                   if(/ipad|iphone|mac/i.test(navigator.userAgent)) {
             
                     var ifr =document.createElement("iframe");
             
                     ifr.src = locatUrl;
             
                     ifr.style.display = "none";
             
                     document.body.appendChild(ifr);
             
                   }else{
             
                     window.location.href = locatUrl;
             
                   }
             
                   }
                   
          </script>
                ';
		return $output;
	}
}