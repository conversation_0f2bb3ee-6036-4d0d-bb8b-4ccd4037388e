<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_countdown',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_DESC'),
		'category' => '其他',
		'attr' => array(
			'general' => array(

				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'max' => 500,
					'responsive' => true,
					'depends' => array(array('title', '!=', '')),
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'max' => 500,
					'responsive' => true,
					'depends' => array(array('title', '!=', '')),
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_fontweight' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_WEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_WEIGHT_DESC'),
					'std' => '',
					'depends' => array(
						array('title', '!=', ''),
						array('time_type', '!=', 'type2')
					),
				),

				'div_bj_color' => array(
					'type' => 'color',
					'title' => '倒计时背景颜色',
					'desc' => '',
					'std' => '#ffffff',
				),

				'box_content' => array(
					'type' => 'text',
					'title' => '左侧标题内容设置',
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_WEIGHT_DESC'),
					'std' => '',
					'depends' => array(
						array('time_type', '!=', 'type1')
					),
				),
				'box_content_color' => array(
					'type' => 'color',
					'title' => '左侧标题内容颜色设置',
					'desc' => '',
					'std' => '#000000',
				),

				'time_type' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'type1' => '样式1',
						'type2' => '样式2',
					),
					'std' => 'type1',
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', ''),array('time_type', '!=', 'type1')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'max' => 500,
					'responsive' => true,
					'depends' => array(array('title', '!=', ''),array('time_type', '!=', 'type1')),
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'max' => 500,
					'responsive' => true,
					'depends' => array(array('title', '!=', ''),array('time_type', '!=', 'type1')),
				),

				'separator1' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_OPTIONS'),
				),

				'date' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_DATE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_DATE_DESC'),
					'placeholder' => '2019/12/25',
					'std' => '2019/12/25'
				),

				'time' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_TIME'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_TIME_DESC'),
					'placeholder' => '20:23',
					'std' => '20:23',
				),

				'finish_text' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_FINISHED_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_FINISHED_TEXT_DESC'),
					'placeholder' => 'Finally we are here',
					'std' => 'Finally we are here',
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_HEIGHT'),
					'placeholder' => '',
					'max' => 500,
					'responsive' => true,
					'std' => array('md' => 80),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_WIDTH'),
					'placeholder' => '',
					'max' => 500,
					'responsive' => true,
					'std' => array('md' => 80),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_FONT_SIZE'),
					'std' => array('md' => 36),
					'max' => 500,
					'responsive' => true,
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_TEXT_COLOR'),
					'std' => '#FFFFFF',
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_text_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_TEXT_FONT_FAMILY'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-countdown-number { font-family: "{{ VALUE }}"; }',
					),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_text_font_weight' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_TEXT_FONT_WEIGHT'),
					'values' => array(
						100 => 100,
						200 => 200,
						300 => 300,
						400 => 400,
						500 => 500,
						600 => 600,
						700 => 700,
						800 => 800,
						900 => 900,
					),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_background_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_BACKGROUND_COLOR'),
					'std' => '#0089e6',
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_user_border' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_USE_BORDER'),
					'std' => 0,
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'counter_border_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_WIDTH'),
					'std' => array('md' => 1),
					'depends' => array('counter_user_border' => 1),
					'max' => 500,
					'responsive' => true,
				),

				'counter_border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR'),
					'std' => '#E5E5E5',
					'depends' => array('counter_user_border' => 1),
				),

				'counter_border_style' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE'),
					'values' => array(
						'none' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_NONE'),
						'solid' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_SOLID'),
						'double' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOUBLE'),
						'dotted' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOTTED'),
						'dashed' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DASHED'),
					),
					'std' => 'solid',
					'depends' => array('counter_user_border' => 1)
				),

				'counter_border_radius' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS'),
					'std' => array('md' => 4),
					'max' => 500,
					'responsive' => true,
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'label_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_LABEL_FONT_SIZE'),
					'std' => array('md' => 14),
					'max' => 500,
					'responsive' => true,
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'label_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_LABEL_COLOR'),
					'std' => '',
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'label_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_LABEL_FONTFAMILY'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-countdown-text { font-family: "{{ VALUE }}"; }',
					),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'label_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_LABEL_FONTSTYLE'),
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'label_margin' => array(
					'type' => 'margin',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_COUTNDOWN_COUNTER_LABEL_MARGIN'),
					'responsive' => true,
					'depends' => array(
						array('time_type', '!=', 'type2')
					),
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
