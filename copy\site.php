<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */

//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonCopy extends JwpagefactoryAddons
{

	public function render()
	{

		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $id = $this->addon->id;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

		$bgimage = (isset($settings->bgimage) && $settings->bgimage) ? $settings->bgimage : 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg';
		$ewmimage1 = (isset($settings->ewmimage1) && $settings->ewmimage1) ? $settings->ewmimage1 : 'https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png';
		$ewmimage2 = (isset($settings->ewmimage2) && $settings->ewmimage2) ? $settings->ewmimage2 : 'https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png';
		$copy_cont1 = (isset($settings->copy_cont1) && $settings->copy_cont1) ? $settings->copy_cont1 : '';
		$copy_cont2 = (isset($settings->copy_cont2) && $settings->copy_cont2) ? $settings->copy_cont2 : '';
		$ewm_background = (isset($settings->ewm_background) && $settings->ewm_background) ? $settings->ewm_background : '#fff';
		$cpy_color = (isset($settings->cpy_color) && $settings->cpy_color) ? $settings->cpy_color : '#333';

		if (isset($settings->ewmtop)) {
		    if (is_object($settings->ewmtop)) {
		        $ewmtop_md = $settings->ewmtop->md;
		        $ewmtop_sm = $settings->ewmtop->sm;
		        $ewmtop_xs = $settings->ewmtop->xs;
		    } else {
		        $ewmtop_md = $settings->ewmtop;
		        $ewmtop_sm = $settings->ewmtop_sm;
		        $ewmtop_xs = $settings->ewmtop_xs;
		    }
		} else {
		    $ewmtop_md = '150px 20px 50px 20px';
		    $ewmtop_sm = '130px 20px 30px 20px';
		    $ewmtop_xs = '200px 20px 50px 20px';
		}

		if (isset($settings->ewmwidth)) {
		    if (is_object($settings->ewmwidth)) {
		        $ewmwidth_md = $settings->ewmwidth->md;
		        $ewmwidth_sm = $settings->ewmwidth->sm;
		        $ewmwidth_xs = $settings->ewmwidth->xs;
		    } else {
		        $ewmwidth_md = $settings->ewmwidth;
		        $ewmwidth_sm = $settings->ewmwidth_sm;
		        $ewmwidth_xs = $settings->ewmwidth_xs;
		    }
		} else {
		    $ewmwidth_md = '220';
		    $ewmwidth_sm = '150';
		    $ewmwidth_xs = '130';
		}

		if (isset($settings->ewmheight)) {
		    if (is_object($settings->ewmheight)) {
		        $ewmheight_md = $settings->ewmheight->md;
		        $ewmheight_sm = $settings->ewmheight->sm;
		        $ewmheight_xs = $settings->ewmheight->xs;
		    } else {
		        $ewmheight_md = $settings->ewmheight;
		        $ewmheight_sm = $settings->ewmheight_sm;
		        $ewmheight_xs = $settings->ewmheight_xs;
		    }
		} else {
		    $ewmheight_md = '250';
		    $ewmheight_sm = '180';
		    $ewmheight_xs = '130';
		}

		if (isset($settings->ewmmargin)) {
		    if (is_object($settings->ewmmargin)) {
		        $ewmmargin_md = $settings->ewmmargin->md;
		        $ewmmargin_sm = $settings->ewmmargin->sm;
		        $ewmmargin_xs = $settings->ewmmargin->xs;
		    } else {
		        $ewmmargin_md = $settings->ewmmargin;
		        $ewmmargin_sm = $settings->ewmmargin_sm;
		        $ewmmargin_xs = $settings->ewmmargin_xs;
		    }
		} else {
		    $ewmmargin_md = '50';
		    $ewmmargin_sm = '10';
		    $ewmmargin_xs = '10';
		}

		$output='';
		$output .='
			<style>
				'.$addon_id.' *{padding:0px;margin:0px}
				'.$addon_id.' .coya{width:100%;height:100%;background:url('.$bgimage.') no-repeat center center/100% 100%;}
				'.$addon_id.' .coya{
					padding:'.$ewmtop_md.';
				}
				'.$addon_id.' .cycg{
					width:20%;position:absolute;top:100px;left:40%;background:#e7faf0;color:#13ce66;font-size:14px;text-align:center;padding:12px;border-radius:5px;display:none;
				}
				'.$addon_id.' .copyb{
					display: flex;
			    	justify-content: center;
				}
				'.$addon_id.' li{ list-style: none;}
				'.$addon_id.' .copyb li{margin:0px '.$ewmmargin_md.'px;width:'.$ewmwidth_md.'px;padding:10px 10px 0px 10px;background:'.$ewm_background.';border-radius:3px;}
				'.$addon_id.' .copyb li img{
					width:100%;
					height:'.$ewmheight_md.'px;
				}
				'.$addon_id.' .clikcpy{
					font-size:14px;color:'.$cpy_color.';text-align:center;line-height:40px;cursor:pointer;
				}

				@media (min-width: 768px) and (max-width: 991px) {
					'.$addon_id.' .coya{
						padding:'.$ewmtop_sm.';
					}
					'.$addon_id.' .copyb li{margin:0px '.$ewmmargin_sm.'px;width:'.$ewmwidth_sm.'px;padding:10px 10px 0px 10px;background:'.$ewm_background.';border-radius:3px;}
					'.$addon_id.' .copyb li img{
						width:100%;
						height:'.$ewmheight_sm.'px;
					}
				}
				@media (max-width: 767px) {
					'.$addon_id.' .copyb{
						display: flex;
				    	justify-content: center;
					}
					'.$addon_id.' .coya{
						padding:'.$ewmtop_xs.';
					}
					'.$addon_id.' .copyb li{margin:0px '.$ewmmargin_xs.'px;width:'.$ewmwidth_xs.'px;padding:10px 10px 0px 10px;background:'.$ewm_background.';border-radius:3px;}
					'.$addon_id.' .copyb li img{
						width:100%;
						height:'.$ewmheight_xs.'px;
					}
					'.$addon_id.' .cycg{
						width:40%;left:30%;top:50px;
					}
				}
			</style>
		'; 
		$output .='
			<div class="coya">
				<div class="posd" style="position:relative;">
					<div class="cycg" style="">复制成功</div>
					<ul class="copyb">
						<li class="fl">
							<div class="coyimg">
								<img src="'.$ewmimage1.'">
							</div>
							<div class="clikcpy" onclick="myFun(\''.$copy_cont1.'\')" >
								一键复制
							</div>
						</li>
						<li class="fl">
							<div class="coyimg">
								<img src="'.$ewmimage2.'">
							</div>
							<div class="clikcpy" onclick="myFun(\''.$copy_cont2.'\')">
								一键复制
							</div>
						</li>

					</ul>
					
				</div>
			</div>
			<script>
				
					/**
					 * 返回当前元素的文本内容
					 * @parm {DOM} element 当前DOM元素
					 */
					function selectText(element){
					    return element.innerText;
					}

					function myFun(text){
					    var textValue = document.createElement("textarea");
					    textValue.setAttribute("readonly", "readonly"); //设置只读属性防止手机上弹出软键盘
					    textValue.value = text;
					    document.body.appendChild(textValue); //将textarea添加为body子元素
					    textValue.select();
					    var res = document.execCommand("copy");
					    document.body.removeChild(textValue);//移除DOM元素
					    console.log("复制成功");
					    $(".cycg").show(100);
					    setTimeout(function(){$(".cycg").hide()},2000);
					    return res;
					}
				
			
			</script>
		';
		return $output;
	}

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
	public static function getTemplate()
	{
		$output = '
		<#
			var addonId = "#jwpf-addon-" + data.id;
            var bgimage = (typeof data.bgimage !== "undefined" && data.bgimage) ? data.bgimage : "https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg";
            var ewmimage1 = (typeof data.ewmimage1 !== "undefined" && data.ewmimage1) ? data.ewmimage1 : "https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png";
 			var ewmimage2 = (typeof data.ewmimage2 !== "undefined" && data.ewmimage2) ? data.ewmimage2 : "https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png";
			var ewm_background = (typeof data.ewm_background !== "undefined" && data.ewm_background) ? data.ewm_background : "#fff";
			var cpy_color = (typeof data.cpy_color !== "undefined" && data.cpy_color) ? data.cpy_color : "#333";
	
		#>
		<style type="text/css">
			#jwpf-addon-{{ data.id }} *{padding:0px;margin:0px}
			#jwpf-addon-{{ data.id }} .coya{width:100%;height:100%;background:url({{bgimage}}) no-repeat center center/100% 100%;}
			#jwpf-addon-{{ data.id }} li{ list-style: none;}
			#jwpf-addon-{{ data.id }} .coya{
				<# if(_.isObject(data.ewmtop)) { #>
				    <# if(data.ewmtop.md){ #>
				        padding: {{data.ewmtop.md}};
				    <# }else{ #>
				        padding: 150px 20px 50px 20px;
				    <# } #> 
				<# } #>

			}
	
			#jwpf-addon-{{ data.id }} .copyb{
				display: flex;
		    	justify-content: center;
			}

			#jwpf-addon-{{ data.id }} .copyb li{
				
				<# if(_.isObject(data.ewmmargin)) { #>
				    <# if(data.ewmmargin.md){ #>
				        margin:0px {{data.ewmmargin.md}}px;
				    <# }else{ #>
				        margin: 0px 50px;
				    <# } #> 
				<# } #>

				<# if(_.isObject(data.ewmwidth)) { #>
				    <# if(data.ewmwidth.md){ #>
				        width: {{data.ewmwidth.md}}px;
				    <# }else{ #>
				        width: 220px;
				    <# } #> 
				<# } #>
				padding:10px 10px 0px 10px;
				background:{{ewm_background}};
				border-radius:3px;
			}
			#jwpf-addon-{{ data.id }} .copyb li img{
				width:100%;
				<# if(_.isObject(data.ewmheight)) { #>
				    <# if(data.ewmheight.md){ #>
				        height: {{data.ewmheight.md}}px;
				    <# }else{ #>
				        height: 250px;
				    <# } #> 
				<# } #>
			}
			#jwpf-addon-{{ data.id }} .clikcpy{
				font-size:14px;color:{{cpy_color}};text-align:center;line-height:40px;cursor:pointer;
			}
			
			@media (min-width: 768px) and (max-width: 991px) {
				#jwpf-addon-{{ data.id }} .coya{
					<# if(_.isObject(data.ewmtop)) { #>
					    <# if(data.ewmtop.sm){ #>
					        padding: {{data.ewmtop.sm}};
					    <# }else{ #>
					        padding: 130px 20px 30px 20px;
					    <# } #> 
					<# } #>

				}
				#jwpf-addon-{{ data.id }} .copyb li{
				
					<# if(_.isObject(data.ewmmargin)) { #>
					    <# if(data.ewmmargin.sm){ #>
					        margin:0px {{data.ewmmargin.sm}}px;
					    <# }else{ #>
					        margin: 0px 10px;
					    <# } #> 
					<# } #>

					<# if(_.isObject(data.ewmwidth)) { #>
					    <# if(data.ewmwidth.sm){ #>
					        width: {{data.ewmwidth.sm}}px;
					    <# }else{ #>
					        width: 150px;
					    <# } #> 
					<# } #>
					padding:10px;
					background:{{ewm_background}};
					border-radius:3px;
				}
				#jwpf-addon-{{ data.id }} .copyb li img{
					width:100%;
					<# if(_.isObject(data.ewmheight)) { #>
					    <# if(data.ewmheight.sm){ #>
					        height: {{data.ewmheight.sm}}px;
					    <# }else{ #>
					        height: 180px;
					    <# } #> 
					<# } #>
				}
			}
			@media (max-width: 767px) {
				#jwpf-addon-{{ data.id }} .coya{
					<# if(_.isObject(data.ewmtop)) { #>
					    <# if(data.ewmtop.xs){ #>
					        padding: {{data.ewmtop.xs}};
					    <# }else{ #>
					        padding: 200px 20px 50px 20px;
					    <# } #> 
					<# } #>

				}
				#jwpf-addon-{{ data.id }} .copyb li{
				
					<# if(_.isObject(data.ewmmargin)) { #>
					    <# if(data.ewmmargin.xs){ #>
					        margin:0px {{data.ewmmargin.xs}}px;
					    <# }else{ #>
					        margin: 0px 10px;
					    <# } #> 
					<# } #>

					<# if(_.isObject(data.ewmwidth)) { #>
					    <# if(data.ewmwidth.xs){ #>
					        width: {{data.ewmwidth.xs}}px;
					    <# }else{ #>
					        width: 130px;
					    <# } #> 
					<# } #>
				}
				#jwpf-addon-{{ data.id }} .copyb li img{
					width:100%;
					<# if(_.isObject(data.ewmheight)) { #>
					    <# if(data.ewmheight.xs){ #>
					        height: {{data.ewmheight.xs}}px;
					    <# }else{ #>
					        height: 120px;
					    <# } #> 
					<# } #>
				}
			}
		</style>

		<div class="coya {{ data.class }}">
			<div class="posd">
				<ul class="copyb">
					<li class="fl">
						<div class="coyimg">
							<img src=\'{{ewmimage1}}\'>
						</div>
						<div class="clikcpy"  >
							一键复制
						</div>
					</li>
					<li class="fl">
						<div class="coyimg">
							<img src=\'{{ewmimage2}}\'>
						</div>
						<div class="clikcpy">
							一键复制
						</div>
					</li>
					<div class="cl"></div>
				</ul>
			</div>
		</div>		
		';

		return $output;
	}
}
