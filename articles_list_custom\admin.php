<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'articles_list_custom',
        'title' => '自定义文章列表',
        'desc' => '自定义文章列表',
        'category' => '文章',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'data_related_config' => array(
                    'type' => 'separator',
                    'title' => '数据相关配置'
                ),
                'data_source' => array(
                    'type' => 'select',
                    'title' => '数据源',
                    'desc' => '选择数据源',
                    'values' => array(
                        'articles' => '文章',
                        'products' => '产品',
                        'nav' => '导航',
                    ),
                    'std' => 'articles',
                ),
                'catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => '选择要显示的分类',
                    'multiple' => true,
                    'depends' => array(
                        array('data_source', '=', 'articles'),
                    ),
                ),
                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'multiple' => true,
                    'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                    'depends' => array(
                        array('data_source', '=', 'products'),
                    ),
                ),
                // 是否显示选项卡
                'show_tabs' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示选项卡',
                    'desc' => '是否显示选项卡',
                    'std' => 0,
                ),
                'include_subcat' => array(
                    'type' => 'select',
                    'title' => '是否包含子分类',
                    'desc' => '是否包含子分类',
                    'values' => array(
                        1 => '是',
                        0 => '否',
                    ),
                    'std' => 1,
                ),
                'ordering' => array(
                    'type' => 'select',
                    'title' => '排序方式',
                    'desc' => '排序方式',
                    'values' => array(

                        'latest' => '最新的',
                        'oldest' => '最旧的',

                        'timedesc' => '添加时间降序',
                        'timeasc' => '添加时间升序',

                        'sortdesc' => '排序id倒序',
                        'sortasc' => '排序id正序',

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'sortdesc',
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => 3,
                ),
                'separator_options' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS')
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),
                // 详情页跳转方式
                'detail_page_jump' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页面',
                        '_blank' => '新页面',
                        'no' => '禁止跳转',
                    ),
                    'std' => '_self',
                ),

                // 文章列表相关配置
                'article_list_config' => array(
                    'type' => 'separator',
                    'title' => '文章列表相关配置'
                ),
                // 是否开启大图模式
                'big_img_status' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启大图模式',
                    'std' => 0,
                ),
                'big_img_settings_area' => array(
                    'type' => 'separator',
                    'title' => '大图模式相关配置',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                    )
                ),
                // 大图模式下大图显示项配置选项卡
                'big_img_settings_tabs' => array(
                    'type' => 'buttons',
                    'title' => '大图模式下大图显示项配置选项卡',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '列表项',
                            'value' => 'big_img_item',
                        ),
                        array(
                            'label' => '大图内容',
                            'value' => 'big_img_content',
                        ),
                    ),
                    'std' => 'big_img_item',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                    )
                ),
                // 大图显示位置
                'big_img_position' => array(
                    'type' => 'select',
                    'title' => '大图显示位置',
                    'values' => array(
                        'top' => '顶部',
                        'left' => '左侧',
                    ),
                    'std' => 'top',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 大图显示个数
                'big_img_number' => array(
                    'type' => 'slider',
                    'title' => '大图显示个数',
                    // 'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'std' => 2,
                    'max' => 10,
                    // 'responsive' => true
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                'item_number_big' => array(
                    'type' => 'slider',
                    'title' => '大图列表项个数',
                    'std' => array('md' => 2, 'sm' => 2, 'xs' => 1),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 宽度
                'item_width_big' => array(
                    'type' => 'slider',
                    'title' => '大图显示项宽度（%）',
                    'std' => array('md' => 50, 'sm' => 100, 'xs' => 100),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_position', '=', 'left'),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 列表项排版
                'item_layout_big' => array(
                    'type' => 'select',
                    'title' => '列表项排版',
                    'values' => array(
                        'column' => '上图下文',
                    ),
                    'std' => 'column',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                'item_padding_big' => array(
                    'type' => 'padding',
                    'title' => '列表项内边距',
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                'item_margin_big' => array(
                    'type' => 'padding',
                    'title' => '列表项外边距',
                    'std' => array('md' => '0 80px 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_position', '=', 'left'),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 列表项边框颜色
                'item_border_color_big' => array(
                    'type' => 'color',
                    'title' => '列表项边框颜色',
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    ),
                ),
                // 列表边框宽度
                'item_border_width_big' => array(
                    'type' => 'padding',
                    'title' => '列表项边框宽度',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                'item_content_padding_big' => array(
                    'type' => 'padding',
                    'title' => '列表项内容内边距',
                    'std' => array('md' => '20px 20px 20px 20px', 'sm' => '20px 20px 20px 20px', 'xs' => '20px 20px 20px 20px'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 状态选项卡
                'item_style_settings_tabs_big' => array(
                    'type' => 'buttons',
                    'title' => '大图列表项样式配置',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover',
                        ),
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                    )
                ),
                // 是否开启投影
                'item_box_shadow_big' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启投影(如果发现阴影被遮挡，请调整列表项内边距和列表项外边距)',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 投影颜色
                'item_box_shadow_color_big' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'std' => 'rgba(0, 0, 0, 0.1)',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_box_shadow_big', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 投影大小
                'item_box_shadow_size_big' => array(
                    'type' => 'slider',
                    'title' => '投影大小',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_box_shadow_big', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 投影扩散大小
                'item_box_shadow_spread_big' => array(
                    'type' => 'slider',
                    'title' => '投影扩散大小',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_box_shadow_big', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 投影水平位置
                'item_box_shadow_horizontal_big' => array(
                    'type' => 'slider',
                    'title' => '投影水平位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_box_shadow_big', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 投影垂直位置
                'item_box_shadow_vertical_big' => array(
                    'type' => 'slider',
                    'title' => '投影垂直位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_box_shadow_big', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'normal'),
                    )
                ),
                // 鼠标移入是否开启投影
                'hover_item_box_shadow_big_status' => array(
                    'type' => 'checkbox',
                    'title' => '鼠标移入是否开启投影(如果发现阴影被遮挡，请调整列表项内边距和列表项外边距)',
                    'std' => 1,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 鼠标移入投影颜色
                'hover_item_box_shadow_color_big' => array(
                    'type' => 'color',
                    'title' => '鼠标移入投影颜色',
                    'std' => '',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('hover_item_box_shadow_big_status', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 鼠标移入投影大小
                'hover_item_box_shadow_blur_big' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影大小',
                    'std' => 15,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('hover_item_box_shadow_big_status', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 鼠标移入投影扩散大小
                'hover_item_box_shadow_spread_big' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影扩散大小',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('hover_item_box_shadow_big_status', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 鼠标移入投影水平位置
                'hover_item_box_shadow_horizontal_big' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影水平位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('hover_item_box_shadow_big_status', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 鼠标移入投影垂直位置
                'hover_item_box_shadow_vertical_big' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影垂直位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_item'),
                        array('hover_item_box_shadow_big_status', '=', 1),
                        array('item_style_settings_tabs_big', '=', 'hover'),
                    )
                ),
                // 列表项样式配置选项卡
                'big_item_style_settings_tabs' => array(
                    'type' => 'buttons',
                    'title' => '大图列表项样式配置',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover',
                        ),
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                    )
                ),
                // 是否开启遮罩
                'big_img_mask_status' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启遮罩',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    )
                ),
                // 列表项内容背景色
                'item_content_bg_color_big' => array(
                    'type' => 'color',
                    'title' => '列表项内容背景色',
                    'std' => 'rgba(0, 0, 0, 0.2)',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    )
                ),
                // 列表项内容背景色设置项选项卡
                'item_bg_color_big_mask_settings' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '单色',
                            'value' => 'color',
                        ),
                        array(
                            'label' => '渐变',
                            'value' => 'gradient',
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'image',
                        )
                    ),
                    'std' => 'color',
                    'title' => '列表项背景色设置项',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                        array('big_img_mask_status', '=', 1),
                    )
                ),
                'item_bg_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '列表项背景色',
                    'std' => 'rgba(rgba(180, 145, 90, 0.6)',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                        array('big_img_mask_status', '=', 1),
                        array('item_bg_color_big_mask_settings', '=', 'color'),
                    )
                ),
                'item_bg_gradient_big_mask' => array(
                    'type' => 'gradient',
					'title' => JText::_('列表项背景色'),
					'std' => array(
						"color" => "rgba(180, 145, 90, 0.6)",
						"color2" => "rgba(0, 0, 0, 0.3)",
						"deg" => "180",
						"type" => "linear"
					),
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                        array('big_img_mask_status', '=', 1),
                        array('item_bg_color_big_mask_settings', '=', 'gradient'),
                    )
                ),
                'item_bg_image_big_mask' => array(
                    'type' => 'media',
                    'title' => '列表项背景图片',
                    'std' => '',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                        array('big_img_mask_status', '=', 1),
                        array('item_bg_color_big_mask_settings', '=', 'image'),
                    )
                ),
                // 内容设置选项卡
                'big_img_title_settings_tabs' => array(
                    'type' => 'buttons',
                    'title' => '大图内容设置选项卡',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '标题',
                            'value' => 'big_img_title',
                        ),
                        array(
                            'label' => '简介',
                            'value' => 'big_img_intro',
                        ),
                        array(
                            'label' => '日期',
                            'value' => 'big_img_date',
                        ),
                    ),
                    'std' => 'big_img_title',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                    )
                ),
                // 大图标题是否覆盖在图片上
                'big_img_title_cover' => array(
                    'type' => 'checkbox',
                    'title' => '大图标题是否覆盖在图片上',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    )
                ),
                // 标题颜色
                'item_title_color_big' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    )
                ),
                // 标题颜色
                'item_title_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    )
                ),
                // 标题字体大小
                'item_title_font_size_big' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'std' => array('md' => 18, 'sm' => 16, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 标题字体大小
                'item_title_font_size_big_mask' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'std' => array('md' => 22, 'sm' => 16, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 标题粗细
                'item_title_fontweight_big' => array(
                    'type' => 'select',
                    'title' => '标题粗细',
                    'std' => 'normal',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 标题粗细
                'item_title_fontweight_big_mask' => array(
                    'type' => 'select',
                    'title' => '标题粗细',
                    'std' => 'bold',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 标题外边距
                'item_title_margin_big' => array(
                    'type' => 'margin',
                    'title' => '标题外边距',
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 标题外边距
                'item_title_margin_big_mask' => array(
                    'type' => 'margin',
                    'title' => '标题外边距',
                    'std' => array('md' => '0 0 15px 0', 'sm' => '0 0 15px 0', 'xs' => '0 0 15px 0'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 标题显示行数
                'item_title_line_big' => array(
                    'type' => 'slider',
                    'title' => '标题显示行数',
                    'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 标题显示行数
                'item_title_line_big_mask' => array(
                    'type' => 'slider',
                    'title' => '标题显示行数',
                    'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 标题下方下划线颜色
                'item_title_underline_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '标题下方下划线颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    )
                ),
                // 标题下方下划线边距
                'item_title_underline_margin_big_mask' => array(
                    'type' => 'margin',
                    'title' => '标题下方下划线边距',
                    'std' => array('md' => '30px 0 30px 0', 'sm' => '30px 0 30px 0', 'xs' => '30px 0 30px 0'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_title'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 是否显示简介
                'item_intro_show_big' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示简介',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    )
                ),
                // 是否显示简介
                'item_intro_show_big_mask' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示简介',
                    'std' => 1,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    )
                ),
                // 简介颜色
                'item_intro_color_big' => array(
                    'type' => 'color',
                    'title' => '简介颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                        array('item_intro_show_big', '=', '1'),
                    )
                ),
                // 简介颜色
                'item_intro_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '简介颜色',
                    'std' => 'rgba(255, 255, 255, 0.9)',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                        array('item_intro_show_big_mask', '=', '1'),
                    )
                ),
                // 简介字体大小
                'item_intro_font_size_big' => array(
                    'type' => 'slider',
                    'title' => '简介字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_intro_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 简介字体大小
                'item_intro_font_size_big_mask' => array(
                    'type' => 'slider',
                    'title' => '简介字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_intro_show_big_mask', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 简介显示行数
                'item_intro_line_big' => array(
                    'type' => 'slider',
                    'title' => '简介显示行数',
                    'std' => array('md' => 2, 'sm' => 2, 'xs' => 2),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_intro_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 简介显示行数
                'item_intro_line_big_mask' => array(
                    'type' => 'slider',
                    'title' => '简介显示行数',
                    'std' => array('md' => 3, 'sm' => 3, 'xs' => 3),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_intro_show_big_mask', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_intro'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 是否显示日期
                'item_date_show_big' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示日期',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    )
                ),
                // 是否显示日期
                'item_date_show_big_mask' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示日期',
                    'std' => 0,
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    )
                ),
                // 日期显示位置
                'item_date_position_big' => array(
                    'type' => 'select',
                    'title' => '日期显示位置',
                    'values' => array(
                        'image_bottom_title_top' => '标题上方',
                        'title_bottom' => '标题下方',
                        'intro_bottom' => '简介下方',
                    ),
                    'std' => 'title_bottom',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期显示位置
                'item_date_position_big_mask' => array(
                    'type' => 'select',
                    'title' => '日期显示位置',
                    'values' => array(
                        'image_bottom_title_top' => '标题上方',
                        'title_bottom' => '标题下方',
                        'intro_bottom' => '简介下方',
                    ),
                    'std' => 'image_bottom_title_top',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期外边距
                'item_date_margin_big' => array(
                    'type' => 'margin',
                    'title' => '日期外边距',
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期外边距
                'item_date_margin_big_mask' => array(
                    'type' => 'margin',
                    'title' => '日期外边距',
                    'std' => array('md' => '0 0 15px 0', 'sm' => '0 0 15px 0', 'xs' => '0 0 15px 0'),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期内边距
                'item_date_padding_big' => array(
                    'type' => 'padding',
                    'title' => '日期内边距',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期内边距
                'item_date_padding_big_mask' => array(
                    'type' => 'padding',
                    'title' => '日期内边距',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期背景颜色
                'item_date_background_color_big' => array(
                    'type' => 'color',
                    'title' => '日期背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                    ),
                ),
                // 日期背景颜色
                'item_date_background_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '日期背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                'item_date_format_big' => array(
                    'type' => 'select',
                    'title' => '日期格式',
                    'desc' => '如开启日期某个部分单独显示，选择的格式需不包含特殊显示部分',
                    'values' => array(
                        'd F Y' => '22 April 2020',
                        'Y-m-d H:i:s' => '2020-04-22 12:00:00',
                        'Y-m-d' => '2020-04-22',
                        'm-d' => '04-22',
                        'Y-m' => '2020-04',
                        'm-d Y' => '04-22 2020',
                        'Y/m/d' => '2020/04/22',
                        'm/d' => '04/22',
                        'Y/m' => '2020/04',
                        'Y.m.d' => '2020.04.22',
                        'm.d' => '04.22',
                        'Y.m' => '2020.04',
                    ),
                    'std' => 'd F Y',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                'item_date_format_big_mask' => array(
                    'type' => 'select',
                    'title' => '日期格式',
                    'desc' => '如开启日期某个部分单独显示，选择的格式需不包含特殊显示部分',
                    'values' => array(
                        'd F Y' => '22 April 2020',
                        'Y-m-d H:i:s' => '2020-04-22 12:00:00',
                        'Y-m-d' => '2020-04-22',
                        'm-d' => '04-22',
                        'Y-m' => '2020-04',
                        'm-d Y' => '04-22 2020',
                        'Y/m/d' => '2020/04/22',
                        'm/d' => '04/22',
                        'Y/m' => '2020/04',
                        'Y.m.d' => '2020.04.22',
                        'm.d' => '04.22',
                        'Y.m' => '2020.04',
                    ),
                    'std' => 'd F Y',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 开启日期某个部分特殊显示
                'item_date_specific_show_big' => array(
                    'type' => 'checkbox',
                    'title' => '开启日期某个部分特殊显示',
                    'desc' => '其余需显示的部分请选择上面的日期格式',
                    'std' => 0,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 开启日期某个部分特殊显示
                'item_date_specific_show_big_mask' => array(
                    'type' => 'checkbox',
                    'title' => '开启日期某个部分特殊显示',
                    'desc' => '其余需显示的部分请选择上面的日期格式',
                    'std' => 0,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期特殊显示部分
                'item_date_specific_format_show_big' => array(
                    'type' => 'select',
                    'title' => '特殊显示的部分',
                    'values' => array(
                        'Y' => '年',
                        'd' => '日',
                    ),
                    'std' => 'Y',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('item_date_specific_show_big', '=', 1),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期特殊显示部分
                'item_date_specific_format_show_big_mask' => array(
                    'type' => 'select',
                    'title' => '特殊显示的部分',
                    'values' => array(
                        'Y' => '年',
                        'd' => '日',
                    ),
                    'std' => 'Y',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('item_date_specific_show_big_mask', '=', 1),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期特殊显示部分与普通日期对齐方式
                'item_date_specific_align_big' => array(
                    'type' => 'select',
                    'title' => '特殊显示部分与普通日期对齐方式',
                    'values' => array(
                        'start' => '左对齐',
                        'center' => '居中对齐',
                        'end' => '右对齐',
                    ),
                    'std' => 'start',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('item_date_specific_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期特殊显示部分与普通日期对齐方式
                'item_date_specific_align_big_mask' => array(
                    'type' => 'select',
                    'title' => '特殊显示部分与普通日期对齐方式',
                    'values' => array(
                        'start' => '左对齐',
                        'center' => '居中对齐',
                        'end' => '右对齐',
                    ),
                    'std' => 'start',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('item_date_specific_show_big_mask', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期颜色
                'item_date_color_big' => array(
                    'type' => 'color',
                    'title' => '日期文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期颜色
                'item_date_color_big_mask' => array(
                    'type' => 'color',
                    'title' => '日期文字颜色',
                    'std' => 'rgba(255, 255, 255, 0.8)',
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                // 日期字体大小
                'item_date_font_size_big' => array(
                    'type' => 'slider',
                    'title' => '日期字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'normal'),
                    ),
                ),
                // 日期字体大小
                'item_date_font_size_big_mask' => array(
                    'type' => 'slider',
                    'title' => '日期字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_show_big', '=', 1),
                        array('big_img_settings_tabs', '=', 'big_img_content'),
                        array('big_img_title_settings_tabs', '=', 'big_img_date'),
                        array('big_item_style_settings_tabs', '=', 'hover'),
                    ),
                ),
                'big_img_settings_area_end' => array(
                    'type' => 'separator',
                    'title' => '大图模式相关配置结束',
                    'depends' => array(
                        array('big_img_status', '=', 1),
                    )
                ),
                'item_number' => array(
                    'type' => 'slider',
                    'title' => '列表项个数',
                    'std' => array('md' => 3, 'sm' => 2, 'xs' => 1),
                    'max' => 100,
                    'responsive' => true
                ),
                'item_column_gap' => array(
                    'type' => 'slider',
                    'title' => '列表项水平间距',
                    'std' => array('md' => 20, 'sm' => 20, 'xs' => 20),
                    'max' => 1000,
                    'responsive' => true
                ),
                'item_row_gap' => array(
                    'type' => 'slider',
                    'title' => '列表项垂直间距',
                    'std' => array('md' => 20, 'sm' => 20, 'xs' => 20),
                    'max' => 1000,
                    'responsive' => true
                ),
                'item_padding' => array(
                    'type' => 'padding',
                    'title' => '列表项内边距',
                    'std' => array('md' => '20px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true
                ),
                'item_content_padding' => array(
                    'type' => 'padding',
                    'title' => '列表项内容内边距',
                    'std' => array('md' => '20px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                ),
                // 列表项高度
                'item_height' => array(
                    'type' => 'slider',
                    'title' => '列表项高度',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                ),
                // 列表项排版
                'item_layout' => array(
                    'type' => 'select',
                    'title' => '列表项排版',
                    'values' => array(
                        'column' => '上图下文',
                        'column-reverse' => '上文下图',
                        'row' => '左图右文',
                        'row-reverse' => '左文右图',
                    ),
                    'std' => 'column',
                ),
                // 标题简介与日期/更多间距
                'item_title_intro_gap' => array(
                    'type' => 'slider',
                    'title' => '标题简介与日期/更多间距',
                    'desc' => '日期或更多与标题简介显示一行生效',
                    'std' => array('md' => 20, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                ),
                // 切换相关配置
                'swiper_config' => array(
                    'type' => 'separator',
                    'title' => '轮播切换相关配置'
                ),
                // 是否开启切换
                'swiper_status' => array(
                    'type' => 'checkbox',
                    'title' => '开启轮播切换',
                    'std' => 0,
                ),
                // 切换选项
                'swiper_options' => array(
                    'type' => 'buttons',
                    'title' => '切换选项',
                    'values' => array(
                        array(
                            'label' => '轮播项',
                            'value' => 'swiper_item',
                        ),
                        array(
                            'label' => '切换按钮',
                            'value' => 'swiper_navigation',
                        ),
                    ),
                    'std' => 'swiper_item',
                    'depends' => array(
                        array('swiper_status', '=', 1),
                    ),
                ),
                // 开启自动切换
                'swiper_auto_play' => array(
                    'type' => 'checkbox',
                    'title' => '开启自动切换',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_options', '=', 'swiper_item'),
                    ),
                ),
                // 自动切换间隔
                'swiper_auto_play_interval' => array(
                    'type' => 'slider',
                    'title' => '自动切换间隔（毫秒）',
                    'std' => 3000,
                    'max' => 10000,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_auto_play', '=', 1),
                        array('swiper_options', '=', 'swiper_item'),
                    ),
                ),
                // 循环切换
                'swiper_loop' => array(
                    'type' => 'checkbox',
                    'title' => '开启循环切换',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_options', '=', 'swiper_item'),
                    ),
                ),
                // 显示切换按钮
                'swiper_navigation' => array(
                    'type' => 'checkbox',
                    'title' => '显示切换按钮',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 切换按钮宽度
                'swiper_navigation_width' => array(
                    'type' => 'slider',
                    'title' => '切换按钮宽度',
                    'desc' => '切换按钮宽度，单位px',
                    'responsive' => true,
                    'std' => array('md' => 24, 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 切换按钮高度
                'swiper_navigation_height' => array(
                    'type' => 'slider',
                    'title' => '切换按钮高度',
                    'desc' => '切换按钮高度，单位px',
                    'responsive' => true,
                    'std' => array('md' => 24, 'sm' => '', 'xs' => ''),
                    'max' => 100,
                ),
                // 切换按钮上一页图片
                'swiper_navigation_prev_img' => array(
                    'type' => 'media',
                    'title' => '切换按钮上一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png',
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 切换按钮下一页图片
                'swiper_navigation_next_img' => array(
                    'type' => 'media',
                    'title' => '切换按钮下一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png',
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 移入切换按钮上一页图片
                'swiper_navigation_prev_img_hover' => array(
                    'type' => 'media',
                    'title' => '移入切换按钮上一页图片',
                    'std' => '',
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 移入切换按钮下一页图片
                'swiper_navigation_next_img_hover' => array(
                    'type' => 'media',
                    'title' => '移入切换按钮下一页图片',
                    'std' => '',
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 切换按钮顶部距离
                'swiper_navigation_top' => array(
                    'type' => 'slider',
                    'title' => '切换按钮顶部距离(%)',
                    'std' => array('md' => 30, 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                // 切换按钮两侧边距
                'swiper_navigation_margin' => array(
                    'type' => 'slider',
                    'title' => '切换按钮两侧距离(px)',
                    'std' => array('md' => 10, 'sm' => '', 'xs' => ''),
                    'max' => 800,
                    'min' => -150,
                    'responsive' => true,
                    'depends' => array(
                        array('swiper_status', '=', 1),
                        array('swiper_navigation', '=', 1),
                        array('swiper_options', '=', 'swiper_navigation'),
                    ),
                ),
                'item_html_status_all' => array(
                    'type' => 'buttons',
                    'title' => '列表项元素配置',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'image'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '简介',
                            'value' => 'intro'
                        ),
                        array(
                            'label' => '日期',
                            'value' => 'date'
                        ),
                        array(
                            'label' => '分类',
                            'value' => 'category'
                        ),
                        array(
                            'label' => '更多',
                            'value' => 'more'
                        )
                    ),
                    'std' => 'image'
                ),
                'item_image_hidden' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏列表项封面图片',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'image'),
                    ),
                ),
                // 图片高度
                'item_image_height' => array(
                    'type' => 'slider',
                    'title' => '图片高度/宽度(水平布局时为宽度)',
                    'std' => array('md' => 260, 'sm' => 200, 'xs' => 320),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'image'),
                    ),
                ),
                // 图片填充方式
                'item_image_object_fit' => array(
                    'type' => 'select',
                    'title' => '图片填充方式',
                    'values' => array(
                        'cover' => '占满切割',
                        'contain' => '保持图片宽高比不切割显示',
                        'fill' => '占满拉伸',
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('item_html_status_all', '=', 'image'),
                    ),
                ),
                // 标题显示行数
                'item_title_line' => array(
                    'type' => 'slider',
                    'title' => '标题显示行数',
                    'std' => array('md' => 1, 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'title'),
                    ),
                ),
                // 简介显示行数
                'item_intro_line' => array(
                    'type' => 'slider',
                    'title' => '简介显示行数',
                    'std' => array('md' => 3, 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'intro'),
                    ),
                ),
                // 简介
                'item_intro_hidden' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏列表项简介',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'intro'),
                    ),
                ),
                // 简介
                'item_date_hidden' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏列表项日期',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 日期外边距
                'item_date_margin' => array(
                    'type' => 'margin',
                    'title' => '日期外边距',
                    'std' => array('md' => '0 0 20px 0', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 日期外边距
                'item_date_padding' => array(
                    'type' => 'padding',
                    'title' => '日期内边距',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                'item_date_format' => array(
                    'type' => 'select',
                    'title' => '日期格式',
                    'desc' => '如开启日期某个部分单独显示，选择的格式需不包含特殊显示部分',
                    'values' => array(
                        'd F Y' => '22 April 2020',
                        'Y-m-d H:i:s' => '2020-04-22 12:00:00',
                        'Y-m-d' => '2020-04-22',
                        'm-d' => '04-22',
                        'Y-m' => '2020-04',
                        'm-d Y' => '04-22 2020',
                        'Y/m/d' => '2020/04/22',
                        'm/d' => '04/22',
                        'Y/m' => '2020/04',
                        'Y.m.d' => '2020.04.22',
                        'm.d' => '04.22',
                        'Y.m' => '2020.04',
                    ),
                    'std' => 'd F Y',
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 开启日期某个部分特殊显示
                'item_date_specific_show' => array(
                    'type' => 'checkbox',
                    'title' => '开启日期某个部分特殊显示',
                    'desc' => '其余需显示的部分请选择上面的日期格式',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 日期特殊显示部分
                'item_date_specific_format_show' => array(
                    'type' => 'select',
                    'title' => '特殊显示的部分',
                    'values' => array(
                        'Y' => '年',
                        'd' => '日',
                    ),
                    'std' => 'Y',
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                        array('item_date_specific_show', '=', 1),
                    ),
                ),
                // 日期特殊显示部分与普通日期对齐方式
                'item_date_specific_align' => array(
                    'type' => 'select',
                    'title' => '特殊显示部分与普通日期对齐方式',
                    'values' => array(
                        'start' => '左对齐',
                        'center' => '居中对齐',
                        'end' => '右对齐',
                    ),
                    'std' => 'start',
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                        array('item_date_specific_show', '=', 1),
                    ),
                ),
                // 日期显示位置
                'item_date_position' => array(
                    'type' => 'select',
                    'title' => '日期显示位置',
                    'values' => array(
                        'image_top' => '图片上方',
                        'image_bottom_title_top' => '标题上方',
                        'title_bottom' => '标题下方',
                        'intro_bottom' => '简介下方',
                        'title_intro_right' => '标题简介右侧',
                        'title_intro_left' => '标题简介左侧',
                        'cover_image' => '悬浮于列表项'
                    ),
                    'std' => 'image_top',
                    'depends' => array(
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 日期距列表项左距离
                'item_date_margin_left' => array(
                    'type' => 'slider',
                    'title' => '日期距列表项左侧距离',
                    'std' => array('md' => 20, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_position', '=', 'cover_image'),
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 日期距列表项上距离
                'item_date_margin_top' => array(
                    'type' => 'slider',
                    'title' => '日期距列表项顶部距离',
                    'std' => array('md' => 20, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_position', '=', 'cover_image'),
                        array('item_html_status_all', '=', 'date'),
                    ),
                ),
                // 分类
                'item_category_hidden' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏列表项分类',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'category'),
                    ),
                ),
                // 更多
                'item_more_show' => array(
                    'type' => 'checkbox',
                    'title' => '显示列表项更多',
                    'std' => 0,
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                    ),
                ),
                // 更多显示位置
                'item_more_position' => array(
                    'type' => 'select',
                    'title' => '更多显示位置',
                    'values' => array(
                        'bottom' => '简介底部',
                        'title_intro_right' => '标题简介右侧',
                    ),
                    'std' => 'bottom',
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                        array('item_more_show', '=', 1),
                    ),
                ),
                // 更多文字
                'item_more_text' => array(
                    'type' => 'text',
                    'title' => '更多文字',
                    'std' => '了解更多',
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                        array('item_more_show', '=', 1),
                    ),
                ),
                // 更多按钮内边距
                'item_more_padding' => array(
                    'type' => 'padding',
                    'title' => '更多按钮内边距',
                    'std' => array('md' => '20px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                        array('item_more_show', '=', 1),
                    ),
                ),
                // 更多按钮外边距
                'item_more_margin' => array(
                    'type' => 'margin',
                    'title' => '更多按钮外边距',
                    'std' => array('md' => '20px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                        array('item_more_show', '=', 1),
                    ),
                ),
                // 更多按钮文字与图标对齐方式
                'item_more_text_align' => array(
                    'type' => 'select',
                    'title' => '更多按钮文字与图标对齐方式',
                    'values' => array(
                        'space-between' => '两端对齐',
                        'flex-start' => '左侧对齐',
                    ),
                    'std' => 'space-between',
                    'depends' => array(
                        array('item_html_status_all', '=', 'more'),
                        array('item_more_show', '=', 1),
                    ),
                ),
                'list_item_status' => array(
                    'type' => 'buttons',
                    'title' => '列表项状态',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'std' => 'normal'
                ),
                // 容器内边距
                'item_box_shadow_padding' => array(
                    'type' => 'padding',
                    'title' => '容器内边距',
                    'std' => array('md' => '10px 0 10px 0', 'sm' => '10px 0 10px 0', 'xs' => '10px 0 10px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 容器外边距
                'item_box_shadow_margin' => array(
                    'type' => 'margin',
                    'title' => '容器外边距',
                    'std' => array('md' => '-10px 0 -10px 0', 'sm' => '-10px 0 -10px 0', 'xs' => '-10px 0 -10px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 是否开启投影
                'item_box_shadow_status' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启投影（如果发现阴影被遮挡，请调整容器内边距和容器外边距）',
                    'std' => 0,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 投影颜色
                'item_box_shadow_color' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'std' => 'rgba(0, 0, 0, 0.1)',
                    'depends' => array(
                        array('item_box_shadow_status', '=', 1),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 投影大小
                'item_box_shadow_blur' => array(
                    'type' => 'slider',
                    'title' => '投影大小',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_box_shadow_status', '=', 1),
                    ),
                ),
                // 投影扩散距离
                'item_box_shadow_spread' => array(
                    'type' => 'slider',
                    'title' => '投影扩散距离',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('item_box_shadow_status', '=', 1),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 投影水平位置
                'item_box_shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => '投影水平位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('item_box_shadow_status', '=', 1),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 投影垂直位置
                'item_box_shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => '投影垂直位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('item_box_shadow_status', '=', 1),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 鼠标移入是否显示投影
                'hover_item_box_shadow_status' => array(
                    'type' => 'checkbox',
                    'title' => '鼠标移入是否显示投影',
                    'std' => 0,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                // 鼠标移入投影颜色
                'hover_item_box_shadow_color' => array(
                    'type' => 'color',
                    'title' => '鼠标移入投影颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('hover_item_box_shadow_status', '=', 1),
                    ),
                ),
                // 鼠标移入投影大小
                'hover_item_box_shadow_blur' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影大小',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('hover_item_box_shadow_status', '=', 1),
                    ),
                ),
                // 鼠标移入投影扩散距离
                'hover_item_box_shadow_spread' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影扩散距离',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('hover_item_box_shadow_status', '=', 1),
                    ),
                ),
                // 鼠标移入投影水平位置
                'hover_item_box_shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影水平位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('hover_item_box_shadow_status', '=', 1),
                    ),
                ),
                // 鼠标移入投影垂直位置
                'hover_item_box_shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入投影垂直位置',
                    'std' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('hover_item_box_shadow_status', '=', 1),
                    ),
                ),
                // 列表边框宽度
                'item_border_width' => array(
                    'type' => 'padding',
                    'title' => '列表项边框宽度',
                    'std' => array('md' => '3px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    )
                ),
                'hover_item_border_width' => array(
                    'type' => 'padding',
                    'title' => '列表项移入边框宽度',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                // 列表项边框颜色
                'item_border_color' => array(
                    'type' => 'color',
                    'title' => '列表项边框颜色',
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                'hover_item_border_color' => array(
                    'type' => 'color',
                    'title' => '列表项移入边框颜色',
                    'std' => '#0062CA',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                // 列表项圆角
                'item_border_radius' => array(
                    'type' => 'padding',
                    'title' => '列表项圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                'hover_item_border_radius' => array(
                    'type' => 'padding',
                    'title' => '列表项移入圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                // 列表项背景颜色
                'item_bg_color' => array(
                    'type' => 'color',
                    'title' => '列表项背景颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                'hover_item_bg_color' => array(
                    'type' => 'color',
                    'title' => '列表项移入背景颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                'item_html_status' => array(
                    'type' => 'buttons',
                    'title' => '列表项元素配置',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'image'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '简介',
                            'value' => 'intro'
                        ),
                        array(
                            'label' => '日期',
                            'value' => 'date'
                        ),
                        array(
                            'label' => '分类',
                            'value' => 'category'
                        ),
                        array(
                            'label' => '更多',
                            'value' => 'more'
                        ),
                    ),
                    'std' => 'image'
                ),
                // 图片圆角
                'item_img_border_radius' => array(
                    'type' => 'padding',
                    'title' => '图片圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'image'),
                    ),
                ),
                'hover_item_img_border_radius' => array(
                    'type' => 'padding',
                    'title' => '移入图片圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'image'),
                    ),
                ),
                // 标题颜色
                'item_title_color' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#1e1e1e',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'title'),
                    ),
                ),
                'hover_item_title_color' => array(
                    'type' => 'color',
                    'title' => '移入标题颜色',
                    'std' => '#0062CA',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'title'),
                    ),
                ),
                // 标题字体大小
                'item_title_font_size' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'std' => array('md' => 18, 'sm' => 16, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'title'),
                    ),
                ),
                // 标题粗细
                'item_title_fontweight' => array(
                    'type' => 'select',
                    'title' => '标题粗细',
                    'std' => 'normal',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'title'),
                    ),
                ),
                // 简介颜色
                'item_intro_color' => array(
                    'type' => 'color',
                    'title' => '简介颜色',
                    'std' => '#666666',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'intro'),
                    ),
                ),
                'hover_item_intro_color' => array(
                    'type' => 'color',
                    'title' => '移入简介颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'intro'),
                    ),
                ),
                // 简介字体大小
                'item_intro_font_size' => array(
                    'type' => 'slider',
                    'title' => '简介字体大小',
                    'std' => array('md' => 14, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'intro'),
                    ),
                ),
                // 日期背景颜色
                'item_date_background_color' => array(
                    'type' => 'color',
                    'title' => '日期背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期移入背景颜色
                'hover_item_date_background_color' => array(
                    'type' => 'color',
                    'title' => '移入日期背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期边框宽度
                'item_date_border_width' => array(
                    'type' => 'padding',
                    'title' => '日期边框宽度',
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 移入日期边框宽度
                'hover_item_date_border_width' => array(
                    'type' => 'padding',
                    'title' => '移入日期边框宽度',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期边框颜色
                'item_date_border_color' => array(
                    'type' => 'color',
                    'title' => '日期边框颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 移入日期边框颜色
                'hover_item_date_border_color' => array(
                    'type' => 'color',
                    'title' => '移入日期边框颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期圆角
                'item_date_border_radius' => array(
                    'type' => 'padding',
                    'title' => '日期圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 移入日期圆角
                'hover_item_date_border_radius' => array(
                    'type' => 'padding',
                    'title' => '移入日期圆角',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期颜色
                'item_date_color' => array(
                    'type' => 'color',
                    'title' => '日期文字颜色',
                    'std' => '#000000',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期移入颜色
                'hover_item_date_color' => array(
                    'type' => 'color',
                    'title' => '移入日期文字颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期字体大小
                'item_date_font_size' => array(
                    'type' => 'slider',
                    'title' => '日期字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 日期字体粗细
                'item_date_fontweight' => array(
                    'type' => 'select',
                    'title' => '日期字体粗细',
                    'std' => 'normal',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 特殊显示的日期颜色
                'item_date_color_special' => array(
                    'type' => 'color',
                    'title' => '特殊显示的日期颜色',
                    'std' => '',
                    'depends' => array(
                        array('item_date_specific_show', '=', 1),
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 移入特殊显示的日期颜色
                'hover_item_date_color_special' => array(
                    'type' => 'color',
                    'title' => '移入特殊显示的日期颜色',
                    'std' => '',
                    'depends' => array(
                        array('item_date_specific_show', '=', 1),
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 特殊显示的日期字体大小
                'item_date_font_size_special' => array(
                    'type' => 'slider',
                    'title' => '特殊显示的日期字体大小',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_date_specific_show', '=', 1),
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),
                // 特殊显示的日期字体粗细
                'item_date_fontweight_special' => array(
                    'type' => 'select',
                    'title' => '特殊显示的日期字体粗细',
                    'std' => '',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'depends' => array(
                        array('item_date_specific_show', '=', 1),
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'date'),
                    ),
                ),

                // 分类颜色
                'item_category_color' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#999999',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'category'),
                    ),
                ),
                // 分类移入颜色
                'hover_item_category_color' => array(
                    'type' => 'color',
                    'title' => '移入分类文字颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'category'),
                    ),
                ),
                // 分类字体大小
                'item_category_font_size' => array(
                    'type' => 'slider',
                    'title' => '分类字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'category'),
                    ),
                ),
                // 更多按钮边框宽度
                'item_more_border_width' => array(
                    'type' => 'padding',
                    'title' => '更多按钮边框宽度',
                    'std' => array('md' => '1px 0 0 0', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多按钮移入边框宽度
                'hover_item_more_border_width' => array(
                    'type' => 'padding',
                    'title' => '移入更多按钮边框宽度',
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多按钮背景颜色
                'item_more_border_color' => array(
                    'type' => 'color',
                    'title' => '更多按钮边框颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多按钮移入背景颜色
                'hover_item_more_border_color' => array(
                    'type' => 'color',
                    'title' => '移入更多边框颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多文字颜色
                'item_more_color' => array(
                    'type' => 'color',
                    'title' => '更多文字颜色',
                    'std' => '#666',
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多文字移入颜色
                'hover_item_more_color' => array(
                    'type' => 'color',
                    'title' => '移入更多文字颜色',
                    'std' => '',
                    'depends' => array(
                        array('list_item_status', '=', 'hover'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),
                // 更多文字大小
                'item_more_font_size' => array(
                    'type' => 'slider',
                    'title' => '更多文字大小',
                    'std' => array('md' => 14, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('item_html_status', '=', 'more'),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 更多图标
                'item_more_icon' => array(
                    'type' => 'media',
                    'title' => '更多图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250728/2286a4457863c70fe52656938f2d8e26.png',
                    'depends' => array(
                        array('item_html_status', '=', 'more'),
                        array('list_item_status', '=', 'normal'),
                    ),
                ),
                // 更多按钮移入图标
                'item_more_hover_icon' => array(
                    'type' => 'media',
                    'title' => '移入更多按钮图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250728/41177de7b4fbe49175b0a710ad659303.png',
                    'depends' => array(
                        array('item_html_status', '=', 'more'),
                        array('list_item_status', '=', 'hover'),
                    ),
                ),
                // 更多按钮宽度
                'item_more_icon_width' => array(
                    'type' => 'slider',
                    'title' => '更多按钮图标高度',
                    'std' => array('md' => 20, 'sm' => '', 'xs' => ''),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('list_item_status', '=', 'normal'),
                        array('item_html_status', '=', 'more'),
                    ),
                ),


                'page_config' => array(
                    'type' => 'separator',
                    'title' => '翻页码相关配置'
                ),
                 'show_page' => array(
                    'type' => 'checkbox',
                    'title' => '显示翻页码',
                    'desc' => '是否显示翻页码',
                    'std' => '0',
                 ),
                 'page_style_selector' => array(
                    'type' => 'select',
                    'title' => '翻页样式',
                    'values' => array(
                        'page01' => '翻页样式一',
                        'page02' => '翻页样式二',
                        'page03' => '翻页样式三',
                        'page04' => '翻页样式四',

                    ),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => 'page01'
                ),
                'page1_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#1e1e1e',
                ),
                'page1_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#ffffff',
                ),
                'page1_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#2a68a7',
                ),
                'page1_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                    ),
                    'std' => '#2a68a7',
                ),
            ),
        ),
    )
);