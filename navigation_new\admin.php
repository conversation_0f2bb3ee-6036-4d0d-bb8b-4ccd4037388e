<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$nav = !empty($layout_id) ? JwPageFactoryBase::getNavigationList($layout_id) : array();
$firstNav = array();
// 遍历导航数组，把id和title组成新数组
foreach ($nav as $key => $value) {
    $firstNav[$value->id] = $value->title;
}

JwAddonsConfig::addonConfig(
    array(
        'type' => 'general',
        'addon_name' => 'navigation_new',
        'title' => "导航",
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_DESC'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'theme' => array(
                    'type' => 'select',
                    'title' => '选择主题（编辑页面的导航为默认展示，可以去预览页面查看实际录入的导航）',
                    'values' => array(
                        'site01' => '方形导航',
                        'site02' => '圆形导航',
                        'site03' => '下划线导航',
                        'site04' => '导航4',
                        'site05' => '导航5',
                        'site06' => '导航6',
                        'site07' => '导航7',
                        'site08' => '图片导航',
                        'site09' => '导航9',
                        'site10' => '导航10',
                        'site11' => '导航11',
                        'site12' => '导航12',
                        'site13' => '导航13',
                        'site14' => '导航14',
                        'site15' => '导航15（请先添加背景色）',
                        'site16' => '导航16',
                        'site17' => '导航17（导航所在的区块位置请选择>顶端固定）',
                        'site18' => '导航18（请先添加背景色）',
                        'site19' => '导航19',
                        'site20' => '导航20（请先添加背景色）',
                        'site21' => '导航21',
                        'site22' => '导航22',
                        'site23' => '导航23',
                        'site24' => '导航24',
                        'site25' => '导航25',
                    ),
                    'std' => 'site01'
                ),
                'jw_link_list_item' => array(
                    'title' => JText::_('链接项目(仅用于编辑页展示,不可删除)'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('链接标题（从后台添加）'),
                            'std' => 'Hom11e',
                        ),
                        'url' => array(
                            'type' => 'media',
                            'format' => 'attachment',
                            'hide_preview' => true,
                            'title' => JText::_('链接地址（从后台添加）'),
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                        ),
                        'icon' => array(
                            'type' => 'icon',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ICON'),
                        ),
                        'active' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ENABLE_ACTIVE'),
                            'std' => 0
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        'class' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                            'std' => '',
                        ),
                    ),
                    'depends' => array(
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                'item_style' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ITEM_STYLE'),
                ),
                'equipment' => array(
                    'type' => 'buttons',
                    'title' => '导航项设置',
                    //                    'desc' => '',
                    'std' => 'pc',
                    'values' => array(
                        array(
                            'label' => 'pc',
                            'value' => 'pc'
                        ),
                        array(
                            'label' => '手机',
                            'value' => 'mobile'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'item_style_00' => array(
                    'type' => 'separator',
                    'title' => JText::_('该导航样式暂不支持'),
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site01'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),


                // 布局23 国气联
                // 整体设置
                'nav_width_23' => array(
                    'type' => 'slider',
                    'title' => '导航宽度（%）',
                    'std' => '100',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                    )
                ),
                'nav_height_23' => array(
                    'type' => 'slider',
                    'title' => '导航高度（px）',
                    'std' => '50',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                    )
                ),
                'nav_margin_23' => array(
                    'type' => 'margin',
                    'title' => '导航外边距',
                    'std' => '20px auto 20px auto',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                    )
                ),
                'nav_padding_23' => array(
                    'type' => 'padding',
                    'title' => '导航内边距',
                    'std' => '0 6% 0 6%',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                    )
                ),
                'nav_background_23' => array(
                    'type' => 'color',
                    'title' => '导航背景色',
                    'std' => '#0169bf',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                    )
                ),

                // 样式24开始
                'first_nav_bg_24' => array(
                    'type' => 'color',
                    'title' => '导航背景',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc')
                    )
                ),
                // 样式24结束

                'nav_level' => array(
                    'type' => 'buttons',
                    'title' => '导航等级设置',
                    'std' => 'nav01',
                    'values' => array(
                        array(
                            'label' => '一级导航',
                            'value' => 'nav01'
                        ),
                        array(
                            'label' => '二级导航',
                            'value' => 'nav02'
                        ),
                        array(
                            'label' => '三级导航',
                            'value' => 'nav03'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'item_style_01' => array(
                    'type' => 'separator',
                    'title' => JText::_('该导航样式暂不支持'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '!=', 'site01'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        //                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        //                        array('theme', '!=', 'site10'),
                        //                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),
                'item_style_02' => array(
                    'type' => 'separator',
                    'title' => JText::_('该导航样式暂不支持'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '!=', 'site01'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                        //                        array('theme', '!=', 'site02'),
                        //                        array('theme', '!=', 'site03'),
                        //                        array('theme', '!=', 'site04'),
                        //                        array('theme', '!=', 'site05'),
                        //                        array('theme', '!=', 'site06'),
                        //                        array('theme', '!=', 'site07'),
                        //                        array('theme', '!=', 'site08'),
                        //                        array('theme', '!=', 'site09'),
                        //                        array('theme', '!=', 'site10'),
                        //                        array('theme', '!=', 'site11'),
                    ),
                ),
                /*
                 *  一级导航配置
                 * */
                /* 中间logo */
                'openCenterLogo' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启中间logo'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),
                'logoWidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('中间logo宽度'),
                    'std' => 100,
                    'max' => 600,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'logoHeight' => array(
                    'type' => 'slider',
                    'title' => JText::_('中间logo高度'),
                    'std' => 50,
                    'max' => 600,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'logo_mid_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('logo间距 (格式：数字px)'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                ),
                //2021.8.21添加默认logo图
                'logoImage' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210821/234b1ec5895320e7cc56c305f75e791d.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site12'),
                    ),
                ),
                //logo跳转链接
                'logoImageHref' => array(
                    'type' => 'text',
                    'title' => JText::_('logo跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                //logo  art10
                'tz_page_type_art10' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(
                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                        'none_links' => JText::_('不跳转'),
                    ),
                    'std' => 'none_links',
                    'depends' => array(
                        array('openCenterLogo', '=', '1'),
                        array('theme', '=', 'site10'),
                    ),
                ),
                'detail_page_id_art10' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('tz_page_type_art10', '=', 'Internal_pages'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '=', 'site10'),
                    ),
                ),
                'detail_page_art10' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(
                        array('tz_page_type_art10', '=', 'external_links'),
                        array('openCenterLogo', '=', '1'),
                        array('theme', '=', 'site10'),
                    ),
                ),
                'target_art10' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                    ),
                    'depends' => array(
                        array('openCenterLogo', '=', '1'),
                        array('theme', '=', 'site10'),
                    ),
                ),
                /* 中间logo end */
                /* 一级导航前面加点 */
                'nav_sub_item' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('一级导航前面加点'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),
                'nav_sub_item_color' => array(
                    'type' => 'color',
                    'title' => JText::_('点颜色'),
                    'std' => '#eeeeee',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_sub_item', '=', 1),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'nav_sub_item_fontSize' => array(
                    'type' => 'slider',
                    'title' => JText::_('点大小'),
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_sub_item', '=', 1),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'nav_sub_item_tb' => array(
                    'type' => 'slider',
                    'title' => JText::_('点上下距离'),
                    'std' => 8,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_sub_item', '=', 1),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'nav_sub_item_lr' => array(
                    'type' => 'slider',
                    'title' => JText::_('点左距离'),
                    'std' => 0,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_sub_item', '=', 1),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                /* 一级导航前面加点 end */

                /*导航14添加一级导航设置*/
                'nav_sub_item_width_site14' => array(
                    'title' => '一级导航宽度',
                    'type' => 'slider',
                    'std' => 400,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                    )
                ),
                'nav_sub_item_padding_top_site14' => array(
                    'title' => '一级导航容器上内边距',
                    'type' => 'slider',
                    'std' => 100,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                    )
                ),
                'nav_sub_item_top_color_site14' => array(
                    'title' => '一级导航上边框颜色',
                    'type' => 'color',
                    'std' => '#F5F5F5',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                    )
                ),
                /*导航14添加一级导航设置end*/

                /*导航18添加一级导航设置*/
                'nav18_first_nav_height' => array(
                    'title' => '一级导航高度',
                    'type' => 'slider',
                    'std' => 77,
                    'max' => 500,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    )
                ),

                // 2021.10.19 方形导航添加背景图片效果
                'opennavbg' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                    ),
                ),

                // 2022.05.06 方形导航添加开启边框效果
                'open_border' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启边框'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                    ),
                ),
                'bordercolor_site01' => array(
                    'title' => '边框颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('open_border', '=', '1'),

                    )
                ),
                'bordercolor_site01_hover' => array(
                    'title' => '划过边框颜色',
                    'type' => 'color',
                    'std' => '#73a91c',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('open_border', '=', '1'),
                    )
                ),

                /*纵向排列*/
                'nav_direction' => array(
                    'title' => '导航排列方式',
                    'type' => 'select',
                    'std' => '',
                    'values' => array(
                        'row' => '水平排列',
                        'column' => '纵向排列'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                    )
                ),
                'nav_bgwidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航宽度'),
                    'std' => '100',
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('opennavbg', '=', '1'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),

                'nav_bgheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航高度'),
                    'std' => '50',
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('opennavbg', '=', '1'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),


                'nav_bgwidth_column' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航容器宽度'),
                    'std' => '200',
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('nav_direction', '=', 'column'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'nav_bgheight_column' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航容器高度'),
                    'std' => '595',
                    'max' => 2000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('nav_direction', '=', 'column'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'nav_padding_column' => array(
                    'type' => 'padding',
                    'title' => JText::_('导航容器内边距'),
                    'std' => '30px 0px 32px 0px',
                    'max' => 2000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('opennavbg', '=', '1'),
                        array('nav_direction', '=', 'column'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'nav_bg_column' => array(
                    'type' => 'color',
                    'title' => JText::_('导航容器背景色'),
                    'std' => 'rgba(255,255,255,0.7)',
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('nav_direction', '=', 'column'),
                    )
                ),
                'navbgImage' => array(
                    'type' => 'media',
                    'title' => JText::_('导航背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211019/75002a21fb4c1f935a59cacbc091160a.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('opennavbg', '=', '1'),
                        array('theme', '=', 'site01'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbghover' => array(
                    'type' => 'media',
                    'title' => JText::_('划过背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211019/f0e52d12cf57910ca2b22f192a6b2929.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('opennavbg', '=', '1'),
                        array('theme', '=', 'site01'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                //背景图end
                // 20211104导航6添加使用背景图
                'opennavbg6' => array(
                    'type' => 'checkbox',
                    'desc' => '开启使用划过背景图样式后，设置的划过背景色将不生效',
                    'title' => JText::_('划过使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbgImage6' => array(
                    'type' => 'media',
                    'title' => JText::_('添加划过显示的背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211104/3245aac773ddc4907182dc49cf190ade.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('opennavbg6', '=', '1'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'nav_bgwidth6' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航宽度'),
                    'std' => '128',
                    'max' => 300,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        // array('opennavbg6', '=', '1'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                //end
                //导航22
                'nav22_tbimg' => array(
                    'type' => 'media',
                    'title' => JText::_('菜单图标'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220730/83685688b7da189e1a19614a555dbed8.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site22'),
                    )
                ),
                'nav22_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#03594b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site22'),
                    )
                ),
                'nav22_bgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220730/add275f743dfd09e120082214473bd8d.jpg',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site22'),
                    )
                ),
                'nav22_gbutton' => array(
                    'type' => 'media',
                    'title' => JText::_('关闭按钮'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220730/cf2bee715657c9cef2bc889b36f748d5.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site22'),
                    )
                ),
                //
                // 方形导航 导航宽度
                'nav_width_01' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航宽度'),
                    'std' => '128',
                    'max' => 300,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'link_border_radius_active' => array(
                    'type' => 'slider',
                    'desc' => '即当前页面对应的导航栏目的边框半径',
                    'title' => JText::_('当前页一级导航边框半径'),
                    'std' => 1,
                    'max' => 600,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('opennavbg', '!=', '1'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                // 分界线-导航设置
                'nav_settings_site25' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航设置'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 导航和logo间隔
                'nav_logo_space_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航和logo间隔'),
                    'std' => 100,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 一级导航字体颜色
                'first_link_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#1A1A1A',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                'first_link_hover_site25' => array(
                    'title' => '鼠标移入一级导航',
                    'type' => 'color',
                    'std' => '#0256FF',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                'first_link_now_site25' => array(
                    'title' => '当前页一级导航颜色',
                    'type' => 'color',
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 一级导航内边距
                'first_link_padding_site25' => array(
                    'type' => 'padding',
                    'title' => JText::_('一级导航内边距'),
                    'std' => '0 21px 0 21px',
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 导航25一级结束
                'link_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航字体颜色'),
                    'std' => '#22b8f0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'link_color_hover_active' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航鼠标移入字体颜色'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),
                'link_color_column' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site01'),
                        array('nav_direction', '=', 'column'),
                    ),
                ),
                'link_color_column_site03' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                    ),
                ),
                'link_color_column_site16' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site16'),
                    ),
                ),
                'r1_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('opennavbg', '!=', '1'),
                        array('opennavbg6', '!=', '1'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'link_bg' => array(
                    'type' => 'color',
                    'desc' => '导航背景色',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('opennavbg', '!=', '1'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),

                'link_bg_hover' => array(
                    'type' => 'color',
                    'desc' => '鼠标划过时导航的背景色，如需修改划过导航的字体色请点击头部的样式 > 设置 链接悬停颜色',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
                    'std' => 'rgba(9, 29, 236, 0.95)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('opennavbg', '!=', '1'),
                        array('opennavbg6', '!=', '1'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),
                'link_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_BORDER_RADIUS'),
                    'std' => 0,
                    'max' => 600,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('opennavbg', '!=', '1'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                ),

                'link_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-link-list-wrap ul li a { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'link_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => 16,
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'link_fontsize_site16' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'std' => 16,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site16'),
                    ),
                ),
                'link_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '35',
                    'max' => 400,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    )
                ),

                'link_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    )
                ),

                'link_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0' => '默认',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                'them01_wenzi_wz' => array(
                    'type' => 'select',
                    'title' => JText::_('文字位置'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                    'values' => array(
                        'center' => '居中',
                        'left' => '居左',
                        'right' => '居右',
                    ),
                    'std' => 'center',

                ),
                'link_text_transform' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TEXT_TRANSFORM'),
                    'desc' => JText::_('只适用于英文导航'),
                    'values' => array(
                        'none' => '无',
                        'capitalize' => '首字母大写',
                        'uppercase' => '大写',
                        'lowercase' => '小写',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                'link_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                'link_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
                    'std' => array('md' => '7px 17px 7px 17px', 'sm' => '7px 17px 7px 17px', 'xs' => '7px 17px 7px 17px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    )
                ),
                'nav_position' => array(
                    'type' => 'select',
                    'title' => JText::_('导航位置'),
                    'values' => array(
                        'flex-start' => JText::_('左'),
                        'center' => JText::_('中'),
                        'flex-end' => JText::_('右'),
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        //                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    )
                ),

                // 导航3 配置
                'them03_border_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('主题3边框颜色'),
                    'std' => 'rgba(1 ,1 ,1, 1)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site20'),
                    ),
                ),
                'them03_border_nav_ani' => array(
                    'type' => 'checkbox',
                    'desc' => '开启边框动画后，设置的悬停背景色则不生效',
                    'title' => JText::_('是否开启导航边框线动画'),
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site20'),
                    ),
                ),
                'them03_border_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('划过二级关闭一级下划线'),
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('them03_border_nav_ani', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site20'),
                    ),
                ),
                'them03_border_location' => array(
                    'type' => 'select',
                    'title' => '主题3边框位置',
                    'values' => array(
                        'location' => '上',
                        'location2' => '下',
                    ),
                    'std' => 'location2',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site20'),
                    ),
                ),

                // 下划线导航使用背景图
                'site03_use_border_img_level1' => array(
                    'type' => 'checkbox',
                    'title' => '下划线导航边框是否使用图片',
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                    )
                ),
                'site03_border_img_level1' => array(
                    'type' => 'media',
                    'title' => JText::_('下划线导航边框图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220816/368a370ddfa29b66f200a0ceba92e595.jpg',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('site03_use_border_img_level1', '=', 1),
                    )
                ),
                'site03_now_page_need_img' => array(
                    'type' => 'checkbox',
                    'title' => '当前页是否添加图片边框',
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('site03_use_border_img_level1', '=', 1),
                    )
                ),
                'site03_border_img_level1_bottom' => array(
                    'type' => 'slider',
                    'title' => '下划线导航边框图片底边距（%）',
                    'std' => 68,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site03'),
                        array('site03_use_border_img_level1', '=', 1),
                    )
                ),


                // 导航4 配置
                'site04_fitstwidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航宽度'),
                    'std' => '150',
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site04'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'r4_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site04'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'is_site04_img' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('一级导航悬停是否使用图片'),
                    'desc' => JText::_('一级导航悬停是否使用图片'),
                    'values' => array(
                        1 => JText::_('JYES'),
                        0 => JText::_('JNO'),
                    ),
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site04'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site04_btn_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('一级导航悬停背景图'),
                    'desc' => JText::_('一级导航悬停背景图'),
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210821/4cbed3d669354d7ef037a0f98f046a34.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('is_site04_img', '=', '1'),
                        array('theme', '=', 'site04'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_bg_site04_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航悬停的背景颜色'),
                    'std' => 'rgba(9, 29, 236, 0.95)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site04'),
                        array('is_site04_img', '!=', '1'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_bg_site04_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航悬停的字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site04'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                // 导航5 配置
                'site05_fw_style' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('首字设置'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site05_fw_border_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site05_fw_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'std' => '#f71b1b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site05_fw_border_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标进入边框颜色'),
                    'std' => '#f71b1b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),

                    ),
                ),
                'site05_fw_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框圆角'),
                    'std' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site05_fw_border_padding' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框内边距'),
                    'std' => 4,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site05_fw_border_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框外边距'),
                    'std' => 5,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site05'),
                        array('site05_fw_style', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                // 导航6 配置
                'links_fontsize_site06' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航副标题文字大小'),
                    'std' => 12,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'links_top_site06' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航副标题上间距'),
                    'std' => 5,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'links_color_site06' => array(
                    'type' => 'color',
                    'title' => JText::_('导航副标题颜色'),
                    'std' => '#aaa',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_color_hover_site06' => array(
                    'type' => 'color',
                    'title' => JText::_('导航副标题划过颜色'),
                    'std' => '#aaa',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_line_site06' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航标题下划线',
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_line_position_site06' => array(
                    'type' => 'select',
                    'title' => '下划线位置',
                    'values' => array(
                        'left' => '左划线',
                        'right' => '右划线',
                        'top' => '上划线',
                        'bottom' => '下划线',
                    ),
                    'std' => 'bottom',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('links_line_site06', '=', '1'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_line_color_site06' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('links_line_site06', '=', '1'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_line_color_hover_site06' => array(
                    'type' => 'color',
                    'title' => '划过下划线颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('links_line_site06', '=', '1'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'links_column_site06' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航文字竖排显示',
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site06'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                // 导航7 配置
                'link_color_hover_site07' => array(
                    'type' => 'color',
                    'title' => JText::_('悬停的字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site07'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_line_height_site07' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航7行高'),
                    'std' => 80,
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site07'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'background_width_site07' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航7背景宽度'),
                    'std' => 60,
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site07'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'link_padding_site07' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
                    'std' => array('md' => '7px 58px 7px 58px', 'sm' => '7px 58px 7px 58px', 'xs' => '7px 58px 7px 58px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site07'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                // 导航8 配置
                'first_nav_flex8' => array(
                    'type' => 'select',
                    'title' => '选择一级导航划过样式',
                    'values' => array(
                        'site01' => '下横线',
                        'site02' => '左横线'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                    'std' => 'site01'
                ),
                'xhxys_site08' => array(
                    'type' => 'color',
                    'title' => JText::_('横线颜色'),
                    'std' => '#b90c12',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'xhxgd_site08' => array(
                    'type' => 'slider',
                    'title' => JText::_('横线粗细'),
                    'std' => 4,
                    'max' => 20,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('first_nav_flex8', '=', 'site01'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'xhxcd_site08' => array(
                    'type' => 'slider',
                    'title' => JText::_('横线长度'),
                    'std' => 15,
                    'max' => 30,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('first_nav_flex8', '=', 'site02'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'link_color_hover_site08' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航悬停字体颜色'),
                    'std' => '#b90c12',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_fontsize8' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => array('md' => '16', 'sm' => '14', 'xs' => '14'),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_lineheight8' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => array('md' => '60', 'sm' => '50', 'xs' => '40'),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'hide_border_8' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭一级导航右侧竖线'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('first_nav_flex8', '=', 'site01'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_padding08' => array(
                    'type' => 'padding',
                    'title' => JText::_('导航项内边距'),
                    'std' => array('md' => '0px 15px 0px 15px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'a_padding08' => array(
                    'type' => 'padding',
                    'title' => JText::_('文字内边距'),
                    'std' => '0px 0px 0px 0px',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site08'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                // 导航9 配置
                'link_bg9' => array(
                    'type' => 'color',
                    'title' => JText::_('导航9背景色'),
                    'std' => '#2B499F',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_color9' => array(
                    'type' => 'color',
                    'title' => JText::_('导航9一级导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_color_hover_site09' => array(
                    'type' => 'color',
                    'title' => JText::_('导航9悬停的字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_fontsize9' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航9字号'),
                    'std' => array('md' => '16', 'sm' => '14', 'xs' => '14'),
                    'max' => 50,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'link_padding09' => array(
                    'type' => 'padding',
                    'title' => JText::_('导航9内边距'),
                    'std' => array('md' => '7px 30px 7px 30px', 'sm' => '7px 30px 7px 30px', 'xs' => '7px 30px 7px 30px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'ul_width9' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航9容器宽度'),
                    'std' => 1200,
                    'max' => 2000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'ul_padding09' => array(
                    'type' => 'padding',
                    'title' => JText::_('导航9容器内边距'),
                    'std' => array('md' => '20px 30px 20px 30px', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site09'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                // 导航10 配置
                'top_imgwz' => array(
                    'type' => 'select',
                    'title' => JText::_('图标位置'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    ),
                    'values' => array(
                        'ding' => '顶部',
                        'di' => '底部',
                    ),
                    'std' => 'ding',

                ),
                'top_img' => array(
                    'type' => 'media',
                    'title' => JText::_('顶部图标'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),

                    ),
                ),
                'navbar_color_site10' => array(
                    'type' => 'color',
                    'title' => JText::_('导航颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),

                    ),
                ),
                'navbar_hover_color_site10' => array(
                    'type' => 'color',
                    'title' => JText::_('导航滑过颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbar_text' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'std' => 30,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbar_icon' => array(
                    'type' => 'slider',
                    'title' => JText::_('图标大小'),
                    'std' => 30,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbar_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航右间距'),
                    'std' => 30,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'navbar_margin_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航左间距'),
                    'std' => 0,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                // 导航11 配置
                'firstborder11' => array(
                    'type' => 'color',
                    'title' => JText::_('首字边框色'),
                    'std' => '#b63332',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'first11_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航高度'),
                    'std' => '117',
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'first11_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航宽度'),
                    'std' => '110',
                    'max' => 300,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site11'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                /* 导航12 */
                'site12_01' => array(
                    'type' => 'separator',
                    'title' => '导航整体配置',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site12_nav_width' => array(
                    'type' => 'slider',
                    'title' => '导航整体宽度',
                    'std' => '300',
                    'max' => 2000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_nav_bgColor' => array(
                    'type' => 'color',
                    'title' => '导航整体背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_02' => array(
                    'type' => 'separator',
                    'title' => '导航logo配置',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site12_logoImage' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210821/234b1ec5895320e7cc56c305f75e791d.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site12_logo_height' => array(
                    'type' => 'slider',
                    'title' => 'logo高度',
                    'std' => '44',
                    'max' => 500,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_03' => array(
                    'type' => 'separator',
                    'title' => '一级导航项配置',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    ),
                ),
                'site12_nav_link_height' => array(
                    'type' => 'slider',
                    'title' => '一级导航行高',
                    'std' => '52',
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_nav_link_indent' => array(
                    'type' => 'slider',
                    'title' => '一级导航左侧内边距',
                    'std' => '80',
                    'max' => 500,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_nav_link_color' => array(
                    'type' => 'color',
                    'title' => '一级导航文字颜色',
                    'std' => '#949494',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_nav_link_color_active' => array(
                    'type' => 'color',
                    'title' => '一级导航选中文字颜色',
                    'std' => '#111',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_link_before_width' => array(
                    'type' => 'slider',
                    'title' => '一级导航选中/划过 线条宽度',
                    'std' => '30',
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_link_before_color' => array(
                    'type' => 'color',
                    'title' => '一级导航选中/划过 线条颜色',
                    'std' => '#6b6b6b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                        array('theme', '!=', 'site21'),
                    )
                ),
                'site12_04' => array(
                    'type' => 'separator',
                    'title' => '导航底部信息配置',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                    ),
                ),
                'site12_footer_tel' => array(
                    'type' => 'text',
                    'title' => '导航底部电话',
                    'std' => '0351-88888888',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                    ),
                ),
                'site12_footer_email' => array(
                    'type' => 'text',
                    'title' => '导航底部邮箱',
                    'std' => '<EMAIL>',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site12'),
                    ),
                ),
                //                导航14
                'link_bg_site14' => array(
                    'type' => 'color',
                    'desc' => '导航背景色',
                    'std' => '#fff',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                        array('opennavbg', '!=', '1'),

                    ),
                ),
                'first_color_site14' => array(
                    'type' => 'color',
                    'std' => '#666',
                    'title' => JText::_('导航字体颜色'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                        array('opennavbg', '!=', '1'),

                    ),
                ),
                'link_bg_hover_site14' => array(
                    'type' => 'color',
                    'desc' => '鼠标划过时导航的背景色，如需修改划过导航的字体色请点击头部的样式 > 设置 链接悬停颜色',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
                    'std' => '#f8f8f8',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                        array('opennavbg', '!=', '1'),
                        array('opennavbg6', '!=', '1'),

                    ),
                ),
                'link_color_hover_site14' => array(
                    'type' => 'color',
                    'std' => '#000',
                    'title' => JText::_('鼠标划过时导航字体颜色'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                        array('opennavbg', '!=', '1'),

                    ),
                ),
                'link_fontsize_site14' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => 14,
                    'max' => 50,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'link_lineheight_site14' => array(
                    'type' => 'slider',
                    'title' => '行高',
                    'std' => '60',
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),
                    )
                ),

                'link_letterspace_site14' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0' => '默认',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '2px',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site14'),

                    )
                ),
                'link_padding_site14' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
                    'std' => array('md' => '0px 50px 0px 50px', 'sm' => '0px 50px 0px 50px', 'xs' => '0px 50px 0px 50px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    )
                ),
                //                导航15
                'link_width_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('当前页一级导航宽度'),
                    'std' => 200,
                    'max' => 2000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'link_color_active_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'r1_color_active_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航背景颜色'),
                    'std' => 'rgb(0, 85, 60)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'link_bg_hover_site15' => array(
                    'type' => 'color',
                    'desc' => '鼠标划过时导航的背景色，如需修改划过导航的字体色请点击头部的样式 > 设置 链接悬停颜色',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
                    'std' => 'rgb(0, 85, 60)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'link_color_site15' => array(
                    'type' => 'color',
                    'desc' => '字体颜色',
                    'title' => '字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'link_color_hover_site15' => array(
                    'type' => 'color',
                    'desc' => '鼠标悬停字体颜色',
                    'title' => '鼠标悬停字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'link_img_width_site15' => array(
                    'type' => 'slider',
                    'desc' => '一级导航图片宽度',
                    'title' => '一级导航图片宽度',
                    'std' => 40,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'link_img_height_site15' => array(
                    'type' => 'slider',
                    'desc' => '一级导航图片高度',
                    'title' => '一级导航图片高度',
                    'std' => 40,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'link_img_margin_right_site15' => array(
                    'type' => 'slider',
                    'desc' => '一级导航图片右边距',
                    'title' => '一级导航图片右边距',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'link_lineheight_site15' => array(
                    'type' => 'slider',
                    'title' => '一级导航行高',
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site15'),
                    )
                ),

                // 导航17
                'link_padding_site17' => array(
                    'type' => 'slider',
                    'title' => JText::_('整体导航上下间距'),
                    'std' => '10',
                    'max-width' => '100',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                    ),
                ),
                'link_color_site17' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                    ),
                ),
                'link_hvcolor_site17' => array(
                    'type' => 'color',
                    'title' => JText::_('划过字体颜色'),
                    'std' => '#f58505',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                    ),
                ),
                'logo17_img' => array(
                    'type' => 'media',
                    'title' => JText::_('左侧logo图片'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220326/5b02eae2a53829a4e2a53965fedd9420.png',
                ),
                'nav_button_settings_style17' => array(
                    'title' => '导航17设置',
                    'type' => 'buttons',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '普通',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '下滑',
                            'value' => 'scroll'
                        ),
                        array(
                            'label' => '展开',
                            'value' => 'open'
                        )
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                    )
                ),
                'nav_button_color_style17' => array(
                    'title' => '正常导航按钮颜色',
                    'type' => 'color',
                    'std' => '#979797',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'normal'),
                    )
                ),
                'nav_bg_color_style17' => array(
                    'title' => '正常导航条背景颜色',
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'normal'),
                    )
                ),
                'nav_button_scroll_color_style17' => array(
                    'title' => '下滑导航按钮颜色',
                    'type' => 'color',
                    'std' => '#979797',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'scroll'),
                    )
                ),
                'nav_bg_scroll_color_style17' => array(
                    'title' => '下滑导航条背景颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'scroll'),
                    )
                ),
                'nav_button_open_color_style17' => array(
                    'title' => '导航关闭按钮颜色',
                    'type' => 'color',
                    'std' => '#979797',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'open'),
                    )
                ),
                'nav_bg_open_color_style17' => array(
                    'title' => '展开导航背景颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site17'),
                        array('nav_button_settings_style17', '=', 'open'),
                    )
                ),

                //                导航18
                'link_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'ulhover_link_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入一级导航容器字体颜色'),
                    'std' => '#152f76',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'link_color_active_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页一级导航字体颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'link_color_hover_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入一级导航字体颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                // 导航18关联产品是否使用一级分类id
                'link_product_category18' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航18关联产品是否使用一级分类id'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                // 导航25选择哪些页面需要展示特殊样式
                'special_page_25' => array(
                    'type' => 'select',
                    'title' => JText::_('需要展示特殊样式的页面'),
                    'values' => $firstNav,
                    'std' => '',
                    'multiple' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    ),
                ),

                // 公共 未知效果 配置
                'scroll_to' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ENABLE_SCROLL_TO'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    )
                ),
                'scroll_to_offset' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ENABLE_SCROLL_TO_OFFSET'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('scroll_to', '=', 1),
                        array('theme', '!=', 'site10'),

                    ),
                    'max' => 2000,
                    'min' => -2000,
                ),
                // 启用禁止
                'sticky_menu' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ENABLE_STICKY'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    )
                ),

                /*
                 *  二级导航配置
                 * */
                'second_nav' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级导航'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site21'),

                    ),
                ),
                'second_nav_ani' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级导航动画'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site01'),
                        array('second_nav', '=', '1'),
                        array('theme', '!=', 'site10'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site03'),
                    ),
                ),
                'second_nav_flex' => array(
                    'type' => 'select',
                    'title' => '选择二级导航布局',
                    'values' => array(
                        'site01' => '布局1',
                        'site02' => '布局2',
                        'site03' => '布局3（方形导航）',
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                    ),
                    'std' => 'site01'
                ),

                //二级导航布局2展示选中项
                'second_layout2_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启选中当前页的二级导航'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('second_nav_flex', '=', 'site02'),

                    ),
                ),

                // 二级导航 布局3
                'second_03_small_icon' => array(
                    'type' => 'checkbox',
                    'title' => '关闭二级/三级导航图标展示',
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_box_top' => array(
                    'type' => 'slider',
                    'title' => '二级导航距离页面顶端距离',
                    'desc' => '此项数值为一级导航底部距离页面顶端距离',
                    'std' => '68',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_box_pad_top' => array(
                    'type' => 'slider',
                    'title' => '二级导航外层盒子上内边距',
                    'desc' => '如果二级导航需要与一级导航有间距需要使用该项调整',
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_main_bg' => array(
                    'type' => 'color',
                    'title' => '二级导航主内容背景色',
                    'desc' => '',
                    'std' => '#F3F5F7',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more' => array(
                    'type' => 'text',
                    'title' => '二级导航主内容更多按钮文字',
                    'desc' => '',
                    'std' => '了解更多',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_bg' => array(
                    'type' => 'color',
                    'title' => '二级导航主内容更多按钮背景颜色',
                    'desc' => '',
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_color' => array(
                    'type' => 'color',
                    'title' => '二级导航主内容更多按钮文字颜色',
                    'desc' => '',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_f' => array(
                    'type' => 'slider',
                    'title' => '二级导航主内容更多按钮文字大小',
                    'desc' => '',
                    'std' => '14',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_w' => array(
                    'type' => 'slider',
                    'title' => '二级导航主内容更多按钮宽度',
                    'desc' => '',
                    'std' => '144',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_h' => array(
                    'type' => 'slider',
                    'title' => '二级导航主内容更多按钮高度',
                    'desc' => '',
                    'std' => '32',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_b' => array(
                    'type' => 'slider',
                    'title' => '二级导航主内容更多按钮圆角',
                    'desc' => '',
                    'std' => '32',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_more_bottom' => array(
                    'type' => 'slider',
                    'title' => '二级导航主内容更多按钮图片底部距离',
                    'desc' => '',
                    'std' => '-14',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('second_03_more', '!=', ''),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_03_content' => array(
                    'type' => 'editor',
                    'title' => '二级导航右侧内容',
                    'std' => '<dl>
                        <dt>需要服务支持？</dt>
                        <dd><a>联系我们</a></dd>
                        <dd><a>服务预约&amp;查询</a></dd>
                        <dd>客服电话：<b>400 119 7707</b></dd>
                    </dl>',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                //2022.08.02
                'open_sechond_radius' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级圆角'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site01'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'open_sechond_onebz' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启一级下拉标识'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site01'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'open_sechond_bsimg' => array(
                    'type' => 'media',
                    'title' => JText::_('下拉图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220802/69d13bb71d7999b651e3dd819402aa83.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site01'),
                        array('open_sechond_onebz', '=', '1'),
                    ),
                ),
                'open_bsimg_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('下拉图片宽'),
                    'std' => '10',
                    'max' => '50',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site01'),
                        array('open_sechond_onebz', '=', '1'),
                    ),
                ),
                'open_bsimg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('下拉图片高'),
                    'std' => '6',
                    'max' => '20',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site01'),
                        array('open_sechond_onebz', '=', '1'),
                    ),
                ),
                //
                // 2022.05.17
                'open_sechond_xia' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级下划线'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ), //
                'open_sechond_xhx' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('二级划过开启下划线'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                //2022.05.06  开启二级箭头
                'open_sechond_jiant' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启箭头样式'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'second_jiant_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#73a91c',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('open_sechond_jiant', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_jiant_weizhi' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头位置%'),
                    'min' => '0',
                    'max' => '100',
                    'std' => '50',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('open_sechond_jiant', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_jiant_style' => array(
                    'type' => 'select',
                    'title' => JText::_('箭头样式'),
                    'values' => array(
                        'style1' => '样式一',
                        'style2' => '样式二',
                    ),
                    'std' => 'style1',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('open_sechond_jiant', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                //二级导航定位
                'sechond_position_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级导航定位'),
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'sechond_position' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航左侧定位'),
                    'std' => '0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('sechond_position_open', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_flex', '!=', 'site02'),

                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                // 25 二级导航
                // 分界线-导航25二级菜单容器设置
                'second_settings_site25' => array(
                    'type' => 'separator',
                    'title' => JText::_('二级菜单容器设置（如果没有三级，二级导航将会使用三级样式）'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 容器上边距
                // 容器上边框
                'second_top_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('容器上边距'),
                    'std' => 81,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 容器背景色
                'second_bg_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('容器背景色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 容器上边框
                'second_border_top_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('容器上边框'),
                    'std' => 1,
                    'max' => 10,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 容器上边框颜色
                'second_border_top_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('容器上边框颜色'),
                    'std' => '#EDEDED',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 是否开启阴影
                'second_shadow_site25' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启阴影'),
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 阴影颜色
                'second_shadow_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('阴影颜色'),
                    'std' => 'rgba(0, 0, 0, 0.1)',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_shadow_site25', '=', 1),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 阴影横向偏移量
                'second_shadow_offset_x_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影横向偏移量'),
                    'std' => 0,
                    'max' => 20,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_shadow_site25', '=', 1),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 阴影纵向偏移量
                'second_shadow_offset_y_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影纵向偏移量'),
                    'std' => 8,
                    'max' => 20,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_shadow_site25', '=', 1),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 阴影模糊半径
                'second_shadow_blur_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影模糊半径'),
                    'std' => 10,
                    'max' => 20,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_shadow_site25', '=', 1),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 分界线-左侧背景图设置
                'second_bg_settings_site25' => array(
                    'type' => 'separator',
                    'title' => JText::_('左侧背景图设置'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧背景图
                'second_bg_img_site25' => array(
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250409/a83972d02cbd6328c91dedea6c1f625f.png',
                    'title' => JText::_('左侧背景图'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧背景图宽度
                'second_bg_width_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧背景图宽度（%）'),
                    'std' => 31.3,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧背景图高度
                'second_bg_height_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧背景图高度（px）'),
                    'std' => '',
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧内边距
                'second_bg_padding_site25' => array(
                    'type' => 'padding',
                    'title' => JText::_('左侧内边距'),
                    'std' => array('md' => '41px 4.12% 41px 8.9%', 'sm' => '41px 4.12% 41px 8.9%', 'xs' => '41px 4.12% 41px 8.9%'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧标题字号
                'second_bg_title_size_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧标题字号（px）'),
                    'std' => 24,
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧标题颜色
                'second_bg_title_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧标题颜色'),
                    'std' => '#282938',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧简介外边距
                'second_bg_intro_margin_site25' => array(
                    'type' => 'margin',
                    'title' => JText::_('左侧简介外边距'),
                    'responsive' => true,
                    'std' => array('md' => '24px 0px 0px 0px', 'sm' => '24px 0px 0px 0px', 'xs' => '24px 0px 0px 0px'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧简介颜色
                'second_bg_intro_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧简介颜色'),
                    'std' => '#757575',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧简介字号
                'second_bg_intro_size_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧简介字号（px）'),
                    'std' => 16,
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 左侧简介行高
                'second_bg_intro_line_height_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧简介行高（px）'),
                    'std' => 28,
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 右侧导航内边距
                'second_bg_nav_padding_site25' => array(
                    'type' => 'padding',
                    'title' => JText::_('右侧导航内边距'),
                    'std' => array('md' => '32px 81px 32px 81px', 'sm' => '32px 81px 32px 81px', 'xs' => '32px 81px 32px 81px'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 一行显示几列
                'second_bg_nav_column_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('一行显示几列'),
                    'std' => 3,
                    'max' => 4,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 二级导航右间距
                'second_bg_nav_right_margin_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右间距'),
                    'std' => 74,
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 二级导航内边距
                'second_container_nav_padding_site25' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航内边距'),
                    'std' => array('md' => '0 0 18px 0', 'sm' => '0 0 18px 0', 'xs' => '0 0 18px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 二级导航边框
                'second_container_nav_border_site25' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航边框'),
                    'std' => 1,
                    'max' => 10,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 二级导航边框颜色
                'second_container_nav_border_color_site25' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航边框颜色'),
                    'std' => '#E4E4E4',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_container_nav_border_site25', '!=', 0),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 25 二级导航结束
                //
                'sechond_align' => array(
                    'type' => 'select',
                    'title' => JText::_('二级导航字体位置'),
                    'values' => array(
                        'left' => JText::_('左'),
                        'center' => JText::_('中'),
                        'right' => JText::_('右'),
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                // 导航25特殊样式二级导航字体位置
                'sechond_align_spect_25' => array(
                    'type' =>'select',
                    'title' => JText::_('特殊样式二级导航字体位置'),
                    'values' => array(
                        'left' => JText::_('左'),
                        'center' => JText::_('中'),
                        'right' => JText::_('右'),
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=','site25'),
                    ),
                ),
                // 导航25特殊样式图片宽度
                'second_nav_spec_img_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('特殊样式图片宽度'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=','site25'),
                    ),
                ),
                'second_nav_spec_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('特殊样式图片高度'),
                    'std' => '',
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 特殊样式二级导航图片展示方式
                'second_nav_spec_img_show' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'std' => 'contain',
                    'values' => array(
                        'cover' => JText::_('填充-超出裁剪'),
                        'contain' => JText::_('适应-不裁剪'),
                        'fit' => JText::_('裁剪-不超出'),
                        'fill' => JText::_('填充-不超出'),
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 特殊样式图片内边距
                'second_nav_spec_img_padding_25' => array(
                    'type' => 'padding',
                    'title' => JText::_('特殊样式图片内边距'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 间距
                'second_link_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('二级导航间距'),
                    'responsive' => true,
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'second_nav_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航宽度'),
                    'std' => 120,
                    'max' => 300,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                //导航22
                'second22_tb' => array(
                    'type' => 'media',
                    'title' => JText::_('二级导航划过前图标'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220730/83685688b7da189e1a19614a555dbed8.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '=', 'site22'),

                    ),
                ),
                //
                'second_link_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 14,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'second_link_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航行高'),
                    'std' => '35',
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'second_nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#0e0101',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                    ),
                ),
                'second_nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#5fe8e0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'second_nav_color_active' => array(
                    'type' => 'color',
                    'desc' => '鼠标划过二级导航显示的颜色',
                    'title' => JText::_('二级导航选中字体颜色'),
                    'std' => '#1818c7',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'second_nav_bg_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中背景颜色'),
                    'std' => '#e1f18b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site21'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                // 导航8 二级导航 配置
                'second_8_part04' => array(
                    'type' => 'separator',
                    'title' => JText::_('布局选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_li_num08' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航一列显示的个数'),
                    'std' => 5,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_nav_flex08' => array(
                    'type' => 'select',
                    'title' => '选择二级导航布局',
                    'values' => array(
                        'site01' => '左图右文',
                        'site02' => '左文右图'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                    'std' => 'site01'
                ),
                'second_nav_left08' => array(
                    'type' => 'select',
                    'title' => '选择二级导航对齐方式',
                    'values' => array(
                        'left' => '相对一级导航左对齐',
                        'right' => '相对一级导航右对齐'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                    'std' => 'site01'
                ),
                'second_8_part00' => array(
                    'type' => 'separator',
                    'title' => JText::_('背景选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_navbg_top8' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航背景距离一级导航间距'),
                    'std' => 0,
                    'max' => 1800,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_navbg_width8' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航背景宽度'),
                    'std' => 680,
                    'max' => 1800,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_backimg' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210802/1f7a00ffe53f79ef5677029dd4fbabef.jpg',
                ),
                'second_back' => array(
                    'type' => 'color',
                    'title' => JText::_('背景颜色'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_8_part01' => array(
                    'type' => 'separator',
                    'title' => JText::_('背景边框选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_border08' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级导航背景边框'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site08'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_border08' => array(
                    'type' => 'select',
                    'title' => '选择二级导航背景边框位置',
                    'values' => array(
                        'top' => '上边框',
                        'right' => '右边框',
                        'bottom' => '下边框',
                        'left' => '左边框',
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_border08', '=', 1)
                    ),
                    'std' => 'top'
                ),
                'second_nav_border_style08' => array(
                    'type' => 'select',
                    'title' => '选择二级导航背景边框样式',
                    'values' => array(
                        'none' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_NONE'),
                        'solid' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_SOLID'),
                        'double' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOUBLE'),
                        'dotted' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOTTED'),
                        'dashed' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DASHED'),
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_border08', '=', 1)
                    ),
                    'std' => 'solid'
                ),
                'second_nav_border_color08' => array(
                    'type' => 'color',
                    'title' => '二级导航背景边框颜色',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_border08', '=', 1)
                    ),
                    'std' => '#0a4a9b'
                ),
                'second_nav_border_width08' => array(
                    'type' => 'slider',
                    'title' => '二级导航背景边框宽度',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_border08', '=', 1)
                    ),
                    'std' => 2,
                    'max' => 100,
                ),
                'second_8_part02' => array(
                    'type' => 'separator',
                    'title' => JText::_('图片选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_navimg_width8' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航图片宽度'),
                    'std' => 280,
                    'max' => 1200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_8_part05' => array(
                    'type' => 'separator',
                    'title' => JText::_('二级导航文字下滑线选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_link_border08' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启二级导航文字下滑线'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site08'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_link_border_style08' => array(
                    'type' => 'select',
                    'title' => '选择二级导航文字下滑线样式',
                    'values' => array(
                        'none' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_NONE'),
                        'solid' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_SOLID'),
                        'double' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOUBLE'),
                        'dotted' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOTTED'),
                        'dashed' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DASHED'),
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_link_border08', '=', 1)
                    ),
                    'std' => 'solid'
                ),
                'second_link_border_color08' => array(
                    'type' => 'color',
                    'title' => '二级导航文字下滑线颜色',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_link_border08', '=', 1)
                    ),
                    'std' => '#0a4a9b'
                ),
                'second_link_border_width08' => array(
                    'type' => 'slider',
                    'title' => '二级导航文字下滑线高度',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_link_border08', '=', 1)
                    ),
                    'std' => 2,
                    'max' => 100,
                ),
                'second_link_backimg' => array(
                    'type' => 'media',
                    'title' => JText::_('二级导航文字前小图标'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210926/dd020c3e7d97fbbb135bfc372a3632ae.jpg',
                ),
                'second_8_part03' => array(
                    'type' => 'separator',
                    'title' => JText::_('二级导航文字选项'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_nav_width8' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航名称宽度'),
                    'std' => 140,
                    'max' => 300,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_nav_color8' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_nav_color_active8' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                    ),
                ),
                'second_nav_s_title08' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航图片下方副标题文字大小'),
                    'std' => 15,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_nav_flex08', '=', 'site02'),
                    ),
                ),
                'second_nav_s_title_color08' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航图片下方副标题文字颜色'),
                    'std' => '#001862',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_nav_flex08', '=', 'site02'),
                    ),
                ),
                'second_nav_jian08' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航图片下方简介文字大小'),
                    'std' => 12,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_nav_flex08', '=', 'site02'),
                    ),
                ),
                'second_nav_jian_color08' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航图片下方简介文字颜色'),
                    'std' => '#727272',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site08'),
                        array('second_nav_flex08', '=', 'site02'),
                    ),
                ),
                // 导航9 二级导航 配置
                'navbar_offset09' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航9二级导航偏移量'),
                    'std' => 110,
                    'max' => 1000,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site09')
                    ),
                ),
                // 导航12 二级导航 配置
                'site12_second_nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航整体背景颜色'),
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site12'),
                    ),
                ),
                //                导航14二级导航配置
                'second_link_padding_site14' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航内边距'),
                    'responsive' => true,
                    'std' => array('md' => '14px 30px 14px 30px', 'sm' => '14px 30px 14px 30px', 'xs' => '14px 30px 14px 30px'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_nav_width_site14' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航宽度'),
                    'std' => 210,
                    'max' => 600,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_link_fontsize_site14' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 12,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_link_lineheight_site14' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航行高'),
                    'std' => '32',
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_nav_color_site14' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_nav_bg_color_site14' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_nav_color_active_site14' => array(
                    'type' => 'color',
                    'desc' => '鼠标划过二级导航显示的颜色',
                    'title' => JText::_('二级导航选中字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                'second_nav_bg_color_active_site14' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中背景颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site14'),
                    ),
                ),
                //                导航15二级导航配置
                'second_box_max_width_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航容器最大宽度'),
                    'responsive' => true,
                    'std' => 1200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site25'),

                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),

                    ),
                ),
                'second_box_padding_site15' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航容器内边距'),
                    'responsive' => true,
                    'std' => '30px 20px 30px 20px',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),

                    ),
                ),
                'second_link_margin_right_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右间距'),
                    'responsive' => true,
                    'std' => 30,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'second_link_lineheight_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航行高'),
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'second_nav_color_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#444',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'second_nav_margin_top_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航上边距'),
                    'std' => 6,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site19'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),

                    ),
                ),
                'second_nav_bg_color_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'second_nav_color_active_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中字体颜色'),
                    'std' => '#2396B3',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'second_nav_bg_color_active_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中背景颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site15'),
                    ),
                ),

                // 导航17 二级导航配置
                'sechond17_image' => array(
                    'type' => 'media',
                    'title' => JText::_('二级下拉箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220326/89217f968d33f66fa713fa1d727364da.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site17'),
                    ),
                ),
                'sechond17_image_hover' => array(
                    'type' => 'media',
                    'title' => JText::_('划过二级下拉箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220326/39953b6055bb75a378f47176bbb394b1.png',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site17'),
                    ),
                ),

                //                导航18二级导航配置
                'is_full_width_site18' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否全宽'),
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'second_left_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航左边距（%）'),
                    'std' => 50,
                    'min' => -100,
                    'max' => 100,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'second_box_bg_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航容器背景颜色'),
                    'std' => '#fdfdfd',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'second_nav_color_active_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中字体颜色和鼠标移入字体颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'second_nav_setting' => array(
                    'title' => '二级导航设置',
                    'type' => 'buttons',
                    'values' => array(
                        array(
                            'label' => '关联产品',
                            'value' => 'pro'
                        ),
                        array(
                            'label' => '不关联产品',
                            'value' => 'normal'
                        ),
                    ),
                    'std' => 'pro',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'second_pro_setting' => array(
                    'title' => '关联产品分类的二级导航设置',
                    'type' => 'buttons',
                    'values' => array(
                        array(
                            'label' => '左侧',
                            'value' => 'left'
                        ),
                        array(
                            'label' => '右侧',
                            'value' => 'right'
                        ),
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                //                产品分类左侧
                'second_pro_left_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('关联产品分类列表的二级导航左侧宽度（%）'),
                    'std' => 14,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'second_link_fontsize_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航左侧一级分类字体大小'),
                    'std' => 15,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'second_link_lineheight_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航左侧一级分类行高'),
                    'std' => 30,
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'second_box_padding_site18' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航左侧一级分类内边距'),
                    'responsive' => true,
                    'std' => '5px 30px 5px 30px',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site20'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),
                        array('theme', '!=', 'site25'),
                        array('second_nav_flex', '!=', 'site02'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'second_nav_font_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航一级分类字体颜色'),
                    'std' => '#222',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'second_nav_bg_active_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('选中二级导航一级分类背景颜色和鼠标移入背景颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                //              产品分类右侧
                'second_pro_right_padding_site18' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航右侧容器内边距'),
                    'std' => '30px 60px 100px 60px',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right_lineheight_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右侧二级分类行高'),
                    'std' => 30,
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right_fontsize_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右侧二级分类字号'),
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航右侧二级分类字体颜色'),
                    'std' => '#111',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right2_lineheight_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右侧三级分类行高'),
                    'std' => 24,
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right2_fontsize_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航右侧三级分类字号'),
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_pro_right2_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航右侧三级分类字体颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'pro'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                //                普通导航左侧
                'second_normal_left_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('普通的二级导航左侧宽度（%）'),
                    'std' => 24,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'left'),
                    ),
                ),
                'img_style_site18' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'scale-down',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'left'),
                    )
                ),
                'second_normal_right_title_fontsize_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('普通二级导航中一级导航字体大小'),
                    'std' => 24,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_normal_right_title_lineheight_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('普通二级导航中一级导航字体行高'),
                    'std' => 40,
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_normal_right_title_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('普通二级导航中一级导航字体颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_normal_right_p_fontsize_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('普通二级导航字号'),
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_normal_right_p_lineheight_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('普通二级导航行高'),
                    'std' => 24,
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),
                'second_normal_right_p_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('普通二级导航字体颜色'),
                    'std' => '#222',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav02'),
                        array('second_nav', '=', '1'),
                        array('theme', '=', 'site18'),
                        array('second_nav_setting', '=', 'normal'),
                        array('second_pro_setting', '=', 'right'),
                    ),
                ),

                /*
                 *  三级导航配置
                 * */
                'three_nav' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启三级导航'),
                    'std' => 0,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site01'),
                        array('second_nav_flex', '!=', 'site03'),
                    ),
                ),
                'three_nav_flex' => array(
                    'type' => 'select',
                    'title' => '选择三级导航布局',
                    'values' => array(
                        'site01' => '布局1',
                        // 'site02' => '布局2'
                    ),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                    'std' => 'site01'
                ),
                // 间距
                'three_link_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('三级导航间距'),
                    'responsive' => true,
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('三级导航字体颜色'),
                    'std' => '#0e0101',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_nav_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('三级导航选中字体颜色'),
                    'std' => '#1818c7',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('三级导航背景颜色'),
                    'std' => '#5fe8e0',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_nav_bg_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('三级导航选中背景颜色'),
                    'std' => '#e1f18b',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_link_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('三级导航字体大小'),
                    'std' => 14,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),
                'three_link_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('三级导航行高'),
                    'std' => '35',
                    'max' => 400,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('three_nav', '=', '1'),
                        array('second_nav_flex', '!=', 'site03'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                        array('theme', '!=', 'site05'),
                        array('theme', '!=', 'site06'),
                        array('theme', '!=', 'site07'),
                        array('theme', '!=', 'site08'),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site10'),
                        array('theme', '!=', 'site11'),
                    ),
                ),

                /*
                 *  手机导航配置
                 * */
                'responsive_menu' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_ENABLE_RESPONSIVE'),
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'nav_list_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航列表顶部距离'),
                    'std' => '50',
                    'max' => '2000',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),

                    ),
                ),
                'nav_m_fixed' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启导航固定'),
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                    'std' => 0
                ),

                'nav_m_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否展示导航列表'),
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site14'),
                        array('theme', '!=', 'site13'),
                        array('theme', '!=', 'site17'),
                        array('theme', '!=', 'site16'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                    'std' => 0
                ),
                'nav_m_img' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('按钮是否使用图片'),
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                    'std' => 0
                ),
                'nav_img_but' => array(
                    'type' => 'media',
                    'title' => JText::_('导航按钮图片'),
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220606/2a17c5bf71960c34d87c3c3b95c3822a.png',
                    'depends' => array(
                        array('nav_m_img', '=', '1'),
                    ),
                ),
                'nav_img_but_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮图片宽'),
                    'std' => '40',
                    'max' => '100',
                    'depends' => array(
                        array('nav_m_img', '=', '1'),
                    ),
                ),
                'nav_img_but_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮图片高'),
                    'std' => '35',
                    'max' => '50',
                    'depends' => array(
                        array('nav_m_img', '=', '1'),
                    ),
                ),

                'responsive_bar_style' => array(
                    'type' => 'separator',
                    'title' => JText::_('手机导航样式设置'),
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                    ),
                ),
                //22图标下文字
                'phone22_tbwz' => array(
                    'type' => 'text',
                    'title' => JText::_('菜单图标下文字'),
                    'std' => '导航',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site22'),
                        array('nav_m_img', '!=', '1'),
                    ),
                ),
                'navbar22_color_r1' => array(
                    'type' => 'color',
                    'title' => JText::_('文字字体颜色'),
                    'std' => '#b3a36b',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site22'),
                        array('nav_m_img', '!=', '1'),
                    ),
                ),
                //2022.06.09
                'responsive_bar_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机导航宽度%'),
                    'std' => '100',
                    'max' => '100',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),
                    ),
                ),
                'responsive_bar_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('左侧距离%'),
                    'std' => '0',
                    'max' => '100',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),
                    ),
                ), //
                'responsive_bar_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景颜色'),
                    'std' => 'rgba(0, 0, 0, .1)',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),

                    ),
                ),
                'responsive_bar_bg_active' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景点击颜色'),
                    'std' => '#22b8f0',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),

                    ),
                ),
                'responsive_bar_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮横线颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),

                    ),
                ),
                'responsive_bar_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮横线点击颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                        array('nav_m_img', '!=', '1'),

                    ),
                ),
                'mobile_nav_bg_head' => array(
                    'type' => 'color',
                    'title' => JText::_('导航头部背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('nav_m_fixed', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'mobile_nav_bg_head_botton_border' => array(
                    'type' => 'color',
                    'title' => JText::_('导航头部下边线颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('nav_m_fixed', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'mobile_nav_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('导航内容背景颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'navbar_color_r1' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'navbar_fontSize_r1' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'std' => 16,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),
                        array('responsive_menu', '=', 1),

                    ),
                ),
                'navbar_btcolor_r1' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航下边线颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),
                        array('responsive_menu', '=', 1),

                    ),
                ),
                'navbar_fontSize_r1_line' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航高度'),
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),
                        array('responsive_menu', '=', 1),
                    ),
                ),

                'mobile_nav_bg_r2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'navbar_color_r2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site23'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'navbar_fontSize_r2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 14,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),
                        array('responsive_menu', '=', 1),

                    ),
                ),

                'navbar_color_r2_arrow' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航箭头颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '!=', 'site12'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site22'),
                        array('theme', '!=', 'site24'),

                    ),
                ),
                'navbar_fontSize_r2_line' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航高度'),
                    'std' => 30,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '!=', 'site09'),
                        array('theme', '!=', 'site12'),
                        array('theme', '!=', 'site18'),
                        array('theme', '!=', 'site15'),
                        array('theme', '!=', 'site24'),
                    ),
                ),

                //              导航15手机端配置
                'mobile_nav_bg_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('导航内容背景颜色'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'navbar_color_r1_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'navbar_fontSize_r1_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'std' => 16,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),
                        array('responsive_menu', '=', 1),

                    ),
                ),
                'navbar_fontSize_r1_line_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航高度'),
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),
                        array('responsive_menu', '=', 1),
                    ),
                ),
                'mobile_nav_bg_r2_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'mobile_nav_bg_r1_hover_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('移入一级导航背景颜色'),
                    'std' => 'rgb(0, 85, 60)',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'navbar_color_r2_site15' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#444',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('responsive_menu', '=', 1),
                        array('theme', '=', 'site15'),
                    ),
                ),
                'navbar_fontSize_r2_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 16,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'navbar_fontSize_r2_line_site15' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航高度'),
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),
                    ),
                ),

                //                导航18手机端配置
                'responsive_bar_bg_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景颜色'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'responsive_bar_bg_active_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景点击颜色'),
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'responsive_bar_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮横线颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'responsive_bar_color_active_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮横线点击颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'navbar_color_r1_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),

                    ),
                ),
                'navbar_fontSize_r1_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'std' => 14,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'navbar_r1_line_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航高度'),
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'navbar_color_hover_r1_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入一级导航字体颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),

                    ),
                ),
                'navbar_btcolor_r1_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航下边线颜色'),
                    'std' => 'rgba(255,255,255,0.3)',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),

                    ),
                ),
                'navbar_arrow_color_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航箭头颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),
                'navbar_color_r2_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site15'),

                    ),
                ),
                'navbar_fontSize_r2_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 12,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),

                    ),
                ),
                'navbar_color_hover_r2_site18' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入二级导航字体颜色'),
                    'std' => '#e5ad58',
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),

                    ),
                ),
                'navbar_fontSize_r2_line_site18' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航高度'),
                    'std' => 30,
                    'max' => 60,
                    'depends' => array(
                        array('equipment', '=', 'mobile'),
                        array('theme', '=', 'site18'),
                    ),
                ),

                // 全局 css 配置
                'global_class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => '',
                ),

                //                鼠标移入区块变色
                'hover_section_change_color' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启区块移入变色',
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site03'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'section_id' => array(
                    'title' => '区块章节id',
                    'type' => 'text',
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('hover_section_change_color', '=', 1),
                        array('theme', '=', 'site03'),
                    )
                ),
                'hover_section_bg' => array(
                    'title' => '鼠标移入区块背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('hover_section_change_color', '=', 1),
                        array('theme', '=', 'site03'),
                    )
                ),
                'hover_section_color' => array(
                    'title' => '鼠标移入区块字体颜色',
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('hover_section_change_color', '=', 1),
                        array('theme', '=', 'site03'),
                    )
                ),
                'site19_logo_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('导航栏logo图片'),
                    'desc' => JText::_('导航栏logo图片'),
                    'format' => 'image',
                    'std' => '/components/com_jwpagefactory/addons/navigation_new/assets/images/navlogo.png',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_logo_url' => array(
                    'type' => 'text',
                    'title' => JText::_('导航栏logo图片跳转地址'),
                    'desc' => JText::_('导航栏logo图片跳转地址'),
                    'std' => '#',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav1_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级栏目背景颜色'),
                    'std' => '#ececec',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav1_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级栏目字体颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav1_hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级栏目鼠标移入区块字体颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav1_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级栏目字体大小'),
                    'std' => 13,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav2_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级栏目背景颜色'),
                    'std' => '#ececec',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav2_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级栏目字体颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav2_hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级栏目鼠标移入区块字体颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav2_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级栏目字体大小'),
                    'std' => 13,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),
                'site19_nav2_indent_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级栏目字体移动大小(特效)'),
                    'std' => 10,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site19'),
                    ),
                ),

                // 导航下滑变色
                'nav_decline_settings' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航下滑变色配置项'),
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site01'),
                    ),
                ),
                'nav_decline' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启导航下滑变色'),
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site01'),
                    ),
                ),
                'nav_decline_normal' => array(
                    'type' => 'text',
                    'title' => JText::_('未下滑时显示的导航区块id'),
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site01'),
                        array('nav_decline', '=', 1)
                    )
                ),
                'section_id_decline' => array(
                    'type' => 'text',
                    'title' => JText::_('下滑后显示的导航区块id'),
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site01'),
                        array('nav_decline', '=', 1)
                    )
                ),

                /* 'nav_decline20' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启导航下滑变色'),
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site01'),
                    ),
                ),
                'nav_decline_normal20' => array(
                    'type' => 'text',
                    'title' => JText::_('未下滑时显示的导航区块id'),
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site01'),
                        array('nav_decline','=',1)
                    )
                ),
                'section_id_decline20' => array(
                    'type' => 'text',
                    'title' => JText::_('下滑后显示的导航区块id'),
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site01'),
                        array('nav_decline','=',1)
                    )
                ), */

                // 导航20
                'settings_site20' => array(
                    'title' => '导航20设置项',
                    'type' => 'buttons',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '内容配置',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '样式配置',
                            'value' => 'style'
                        )
                    ),
                    'std' => 'content',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                    ),
                ),
                'need_home' => array(
                    'title' => JText::_('是否展示第一个导航'),
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'nav_icon_site20' => array(
                    'title' => JText::_('导航图标'),
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/a24c480042aff8f9da67e589c5acbf19.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'logo_site20' => array(
                    'title' => JText::_('pc端logo'),
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220602/c0e8d5a49a8effade605e1684baf971f.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'logo_phone_site20' => array(
                    'title' => JText::_('移动端logo'),
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220602/e225b837984966da66200977e538bd35.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'close_button_site20' => array(
                    'title' => JText::_('关闭按钮'),
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220602/ac7d8225f6a0a0bfbe9e21ad822f18bd.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'bg_site20' => array(
                    'title' => JText::_('背景'),
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220602/7dfdfedee9f165ace1b5cef7edc07798.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'meida_text_site20' => array(
                    'title' => JText::_('官方社交媒体占位字符'),
                    'type' => 'text',
                    'std' => '官方社交媒体',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'jw_tab_item_site20' => array(
                    'title' => JText::_('官方社交媒体'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => '微博'
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => JText::_('图标'),
                            'std' => 'Subtitle',
                        ),
                        'qr_code' => array(
                            'type' => 'media',
                            'title' => JText::_('二维码'),
                            'std' => '',
                        ),
                        'link' => array(
                            'type' => 'text',
                            'title' => JText::_('链接'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '微博',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220602/10f2e6f1e252ddd3f7059c79e47fe0db.png',
                            'qr_code' => 'https://oss.lcweb01.cn/joomla/20220602/e2d6ebe297940b31f5332071ceff274e.jpg',
                            'link' => 'https://weibo.com/p/1006062488214857/home?from=page_100606&mod=TAB#place'
                        ),
                        array(
                            'title' => '微信',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220602/486bc968086cd8f39c0c024e773c8dbd.png',
                            'qr_code' => 'https://oss.lcweb01.cn/joomla/20220602/9eb31b515c23cfce02a01630d0183ee6.jpg',
                            'link' => ''
                        ),
                        array(
                            'title' => '抖音',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220602/ba0ec8271a9c3d253e6d43b0b38e53bb.png',
                            'qr_code' => 'https://oss.lcweb01.cn/joomla/20220602/6f96c087b5dda1c46c5fed9d2e16a9e4.jpg',
                            'link' => ''
                        ),
                        array(
                            'title' => '百家号',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220602/6991427b06e59e99ecd3a168a060d102.png',
                            'qr_code' => 'https://oss.lcweb01.cn/joomla/20220602/5b8672d9f5d4109246ce0ad02c3d7aa1.jpg',
                            'link' => ''
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'site20_arrow' => array(
                    'type' => 'media',
                    'title' => JText::_('箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220706/b9a3feea7f038fa14bacda1192c22d50.png',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'content'),
                    ),
                ),
                'style_settings' => array(
                    'title' => JText::_('样式设置'),
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'btn',
                    'values' => array(
                        array(
                            'label' => '导航按钮',
                            'value' => 'btn',
                        ),
                        array(
                            'label' => 'logo',
                            'value' => 'logo',
                        ),
                        array(
                            'label' => '导航列表',
                            'value' => 'list',
                        ),
                        array(
                            'label' => '关闭按钮',
                            'value' => 'close',
                        ),
                        array(
                            'label' => '底部列表',
                            'value' => 'footer',
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'site20_nav_button_height' => array(
                    'title' => JText::_('导航按钮高度（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 24, 'sm' => 3, 'xs' => 5.9),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'btn'),
                    ),
                    'responsive' => true
                ),
                'site20_nav_button_margin' => array(
                    'title' => JText::_('导航按钮外边距'),
                    'type' => 'margin',
                    'std' => '0 30px 0 0',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'btn'),
                    ),
                ),
                'site20_bg_settings' => array(
                    'title' => '背景设置',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '纯色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变',
                            'value' => 'gradient'
                        ),
                        array(
                            'label' => '背景图',
                            'value' => 'image'
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                    ),
                ),
                'site20_bg' => array(
                    'title' => '背景色',
                    'type' => 'color',
                    'std' => '#d9012a',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'color'),
                    ),
                ),
                'site20_bg_gradient' => array(
                    'type' => 'gradient',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
                    'std' => array(
                        "color" => "",
                        "color2" => "",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'gradient'),
                    )
                ),
                'site20_bg_img' => array(
                    'title' => '背景图',
                    'type' => 'media',
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'image'),
                    )
                ),
                'site20_bg_repeat' => array(
                    'title' => '背景图是否重复',
                    'type' => 'select',
                    'std' => 'no-repeat',
                    'values' => array(
                        'no-repeat' => '不重复',
                        'repeat' => '重复'
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'image'),
                    )
                ),
                'site20_bg_img_width' => array(
                    'title' => '背景图宽度（%）',
                    'type' => 'slider',
                    'std' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'image'),
                    )
                ),
                'site20_bg_img_height' => array(
                    'title' => '背景图高度（%）',
                    'type' => 'slider',
                    'std' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('site20_bg_settings', '=', 'image'),
                    )
                ),
                'need_home' => array(
                    'title' => JText::_('是否展示第一个导航'),
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('settings_site20', '=', 'style'),
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'list'),
                    ),
                ),
                'site20_align' => array(
                    'title' => '导航对齐方式',
                    'type' => 'select',
                    'std' => 'center',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中对齐',
                        'right' => '右对齐'
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                    ),
                ),
                'nav_phone_padding_site20' => array(
                    'title' => JText::_('移动端导航列表内边距'),
                    'type' => 'padding',
                    'std' => '0 4vw 0 4vw',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                    ),
                ),
                'nav_list_settings_site20' => array(
                    'title' => JText::_('导航列表配置项'),
                    'type' => 'buttons',
                    'std' => 'img',
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '导航',
                            'value' => 'list'
                        )
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                    ),
                ),
                'nav_img_size_site20' => array(
                    'title' => JText::_('导航列表图片大小，平板手机端单位为vw'),
                    'type' => 'slider',
                    'std' => array('md' => 90, 'sm' => 9.6, 'xs' => 9.6),
                    'max' => '200',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                    'responsive' => true
                ),
                'nav_img_max_site20' => array(
                    'title' => JText::_('导航列表图片最大高度（单位：%，平板、移动端单位：px）'),
                    'type' => 'slider',
                    'std' => array(array('md' => 70, 'sm' => 70, 'xs' => 70)),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                    'responsive' => true
                ),
                'site20_img_left' => array(
                    'title' => '导航列表图片左边距（%）',
                    'type' => 'slider',
                    'std' => array('md' => 0, 'sm' => 0, 'xs' => 0),
                    'min' => '-100',
                    'max' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                ),
                'first_img_right_site20' => array(
                    'title' => JText::_('一级导航图片右边距（vw，pc无效）'),
                    'type' => 'slider',
                    'std' => array('md' => 0, 'sm' => 4, 'xs' => 4),
                    'max' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                ),
                'nav_img_top_offset_site20' => array(
                    'title' => JText::_('导航列表图片上下偏移'),
                    'type' => 'slider',
                    'std' => '-214',
                    'min' => '-500',
                    'max' => '500',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                ),
                'nav_arrow_size_site20' => array(
                    'title' => JText::_('导航列表图片上下偏移'),
                    'type' => 'slider',
                    'std' => '5.5',
                    'max' => '10',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'img'),
                    ),
                ),
                'nav_level_settings_site20' => array(
                    'title' => JText::_('导航列表级别配置项'),
                    'type' => 'buttons',
                    'std' => 'level1',
                    'values' => array(
                        array(
                            'label' => '一级',
                            'value' => 'level1'
                        ),
                        array(
                            'label' => '二级',
                            'value' => 'level2'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                    ),
                ),
                'first_nav_font_size_site20' => array(
                    'title' => JText::_('一级导航字号（手机单位：vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 4.7),
                    'max' => '50',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level1'),
                    ),
                    'responsive' => true,
                ),
                'first_nav_line_height_site20' => array(
                    'title' => JText::_('一级导航行高'),
                    'type' => 'slider',
                    'std' => 36,
                    'max' => '50',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level1'),
                    ),
                ),
                'first_nav_offset_site20' => array(
                    'title' => JText::_('一级导航字号上下偏移'),
                    'type' => 'slider',
                    'std' => -115,
                    'min' => '-500',
                    'max' => '500',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level1'),
                    ),
                ),
                'first_nav_bottom_site20' => array(
                    'title' => JText::_('一级导航下边距（vw，pc无效）'),
                    'type' => 'slider',
                    'std' => array('md' => 0, 'sm' => 6.8, 'xs' => 6.8),
                    'max' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level1'),
                    ),
                ),
                'first_nav_color_site20' => array(
                    'title' => JText::_('一级导航字体颜色'),
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level1'),
                    ),
                ),
                'second_nav_offset_site20' => array(
                    'title' => JText::_('二级导航字号上下偏移'),
                    'type' => 'slider',
                    'std' => -60,
                    'min' => '-500',
                    'max' => '500',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_size_site20' => array(
                    'title' => JText::_('二级导航字号（平板、手机单位：vw）'),
                    'type' => 'slider',
                    'std' => array('sm' => 18, 'sm' => 3.9, 'xs' => 3.9),
                    'max' => '50',
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_line_height_site20' => array(
                    'title' => JText::_('二级导航行高'),
                    'type' => 'slider',
                    'std' => 48,
                    'max' => '100',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_color_site20' => array(
                    'title' => JText::_('二级导航字体颜色'),
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_max_height_site20' => array(
                    'title' => JText::_('二级导航最大高度，仅PC端有效'),
                    'type' => 'slider',
                    'std' => '',
                    'max' => '5000',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_overflow_site20' => array(
                    'title' => JText::_('二级导航内容超出时展示方式，仅在二级导航最大高度设置数值时有效'),
                    'type' => 'select',
                    'std' => 'auto',
                    'values' => array(
                        'auto' => '自动',
                        'scroll' => '滚动',
                        'hidden' => '超出隐藏'
                    ),
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_padding_site20' => array(
                    'title' => JText::_('二级导航内边距（vw,pc无效）'),
                    'type' => 'slider',
                    'std' => '5.5vw 0 0 13.7vw',
                    'max' => '20',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'second_nav_bottom_site20' => array(
                    'title' => JText::_('二级导航下边距（vw,pc无效）'),
                    'type' => 'slider',
                    'std' => '5.5',
                    'max' => '20',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'list'),
                        array('nav_list_settings_site20', '=', 'list'),
                        array('nav_level_settings_site20', '=', 'level2'),
                        array('second_nav', '=', '1'),
                    ),
                ),
                'logo_height_site20' => array(
                    'title' => JText::_('logo高度（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 60, 'sm' => 3, 'xs' => 9.3),
                    'max' => '100',
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'logo'),
                    ),
                ),
                'logo_left_site20' => array(
                    'title' => JText::_('logo左边距（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 40, 'sm' => 0, 'xs' => 0),
                    'max' => '100',
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('settings_site20', '=', 'style'),
                        array('style_settings', '=', 'logo'),
                    ),
                ),
                'logo_top_site20' => array(
                    'title' => JText::_('logo上边距（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 30, 'sm' => 0, 'xs' => 0),
                    'max' => '100',
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'logo'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'logo_bottom_site20' => array(
                    'title' => JText::_('移动端logo下边距（vw。pc端无效）'),
                    'type' => 'slider',
                    'std' => array('md' => 0, 'sm' => 4, 'xs' => 5.3),
                    'max' => 110,
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'logo'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'close_size_site20' => array(
                    'title' => JText::_('关闭按钮大小（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 56, 'sm' => 9.2, 'xs' => 9.2),
                    'max' => '30',
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'close'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'close_top_site20' => array(
                    'title' => JText::_('关闭按钮上边距（平板、手机端单位vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 24, 'sm' => 0, 'xs' => 0),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'close'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'close_right_site20' => array(
                    'title' => JText::_('关闭按钮右边距'),
                    'type' => 'slider',
                    'std' => array('md' => 40, 'sm' => 0, 'xs' => 0),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'close'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_bottom_site20' => array(
                    'title' => JText::_('pc端底部列表下边距'),
                    'type' => 'slider',
                    'std' => 40,
                    'responsive' => true,
                    'max' => 2000,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_left_site20' => array(
                    'title' => JText::_('pc端底部列表左边距'),
                    'type' => 'slider',
                    'std' => 30,
                    'responsive' => true,
                    'max' => 2000,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_title_size_site20' => array(
                    'title' => JText::_('底部列表字号(移动端单位为vw)'),
                    'type' => 'slider',
                    'std' => array('md' => '18', 'sm' => '28', 'xs' => '3.4'),
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_title_line_height_site20' => array(
                    'title' => JText::_('底部列表行高'),
                    'type' => 'slider',
                    'std' => 20,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_title_color_site20' => array(
                    'title' => JText::_('底部列表字体颜色'),
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_title_right_site20' => array(
                    'title' => JText::_('底部列表标题右边距，仅移动端有效'),
                    'type' => 'slider',
                    'std' => '40',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_list_title_bottom_site20' => array(
                    'title' => JText::_('底部列表标题下边距，仅移动端有效(vw)'),
                    'type' => 'slider',
                    'std' => '4',
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_img_width_site20' => array(
                    'title' => JText::_('底部列表图片宽度（平板、手机单位：%）'),
                    'type' => 'slider',
                    'std' => array('md' => 25, 'sm' => 15, 'xs' => 15),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_img_height_site20' => array(
                    'title' => JText::_('底部列表图片高度（平板、手机单位：vw）'),
                    'type' => 'slider',
                    'std' => array('md' => 25, 'sm' => 5, 'xs' => 5),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_img_max_height_site20' => array(
                    'title' => JText::_('底部列表图片最大高度'),
                    'type' => 'slider',
                    'std' => 40,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_img_margin_site20' => array(
                    'title' => JText::_('底部列表图片外边距'),
                    'type' => 'margin',
                    'std' => "0 20px 0 20px",
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                ),
                'media_erwei_size_site20' => array(
                    'title' => JText::_('底部列表二维码图片大小，移动端和平板单位为vw'),
                    'type' => 'slider',
                    'std' => array('md' => 120, 'sm' => 32, 'xs' => 32),
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                    'resopnsive' => true
                ),
                'media_erwei_bottom_site20' => array(
                    'title' => JText::_('底部列表二维码图片底边距(%)'),
                    'type' => 'slider',
                    'std' => array('md' => 110, 'sm' => 120, 'xs' => 120),
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site20'),
                        array('style_settings', '=', 'footer'),
                        array('settings_site20', '=', 'style'),
                    ),
                    'resopnsive' => true
                ),

                //site21
                'site21_logo' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'std' => '/components/com_jwpagefactory/addons/navigation_new/assets/img/logo.png',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_bottom_logo' => array(
                    'type' => 'media',
                    'title' => JText::_('logo下方图片'),
                    'std' => '/components/com_jwpagefactory/addons/navigation_new/assets/img/hk.png',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_logoHref' => array(
                    'type' => 'text',
                    'title' => JText::_('logo跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_lianxiwmHref_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('启用右上角联系我们展示'),
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_lianxiwmHref' => array(
                    'type' => 'text',
                    'title' => JText::_('右上角联系我们跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                        array('site21_lianxiwmHref_show', '=', '1'),
                    ),
                ),
                'site21_cnHref_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('启用右上角简展示'),
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_cnHref' => array(
                    'type' => 'text',
                    'title' => JText::_('右上角简跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                        array('site21_cnHref_show', '=', '1'),
                    ),
                ),
                'site21_fanHref_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('启用右上角繁展示'),
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_fanHref' => array(
                    'type' => 'text',
                    'title' => JText::_('右上角繁跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                        array('site21_fanHref_show', '=', '1'),
                    ),
                ),
                'site21_usHref_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('启用右上角EN展示'),
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_usHref' => array(
                    'type' => 'text',
                    'title' => JText::_('右上角EN跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                        array('site21_usHref_show', '=', '1'),
                    ),
                ),
                'site21_sousuoHref_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('启用右上角放大镜展示'),
                    'std' => 1,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_sousuoHref' => array(
                    'type' => 'text',
                    'title' => JText::_('右上角放大镜跳转链接'),
                    'std' => 'javascript:;',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                        array('site21_sousuoHref_show', '=', '1'),
                    ),
                ),
                'site21_font_color1' => array(
                    'type' => 'color',
                    'title' => '一级导航字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_font_size1' => array(
                    'type' => 'slider',
                    'title' => '一级导航字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_width_size1' => array(
                    'type' => 'slider',
                    'title' => '一级导航标题宽度大小',
                    'std' => '90',
                    'min' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_width_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('一级导航标题内边距'),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                    'std' => array('md' => '23px 10px 20px 10px', 'sm' => '23px 10px 20px 10px', 'xs' => '0px 0px 0px 0px'),
                ),
                'site21_font_hover_color1' => array(
                    'type' => 'color',
                    'title' => '一级导航悬停标题下划线颜色',
                    'std' => '#db9f5d',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_font_hover_size1' => array(
                    'type' => 'slider',
                    'title' => '一级导航悬停标题下划线大小',
                    'std' => '4',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_font_size2' => array(
                    'type' => 'slider',
                    'title' => '二级导航字体大小',
                    'std' => '6',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_font_color2' => array(
                    'type' => 'color',
                    'title' => '二级导航字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_font_hover_color2' => array(
                    'type' => 'color',
                    'title' => '二级导航悬停字体颜色',
                    'std' => '#c79556',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_width_size2' => array(
                    'type' => 'slider',
                    'title' => '二级导航标题宽度大小',
                    'std' => '90',
                    'min' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),
                'site21_width_padding2' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航标题内边距'),
                    'responsive' => true,
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                    'std' => array('md' => '6px 0px 6px 0px', 'sm' => '6px 0px 6px 0px', 'xs' => '0px 0px 0px 0px'),
                ),
                'site21_bg_color2' => array(
                    'type' => 'color',
                    'title' => '二级导航背景颜色',
                    'std' => '#272624',
                    'depends' => array(
                        array('theme', '=', 'site21'),
                    ),
                ),


                // 导航23 国气联 一级导航
                'nav_item_settins_23' => array(
                    'title' => '一级导航设置',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'select'
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                    ),
                ),
                'nav_item_width_unit_23' => array(
                    'title' => '一级导航宽度单位',
                    'type' => 'select',
                    'std' => 'px',
                    'values' => array(
                        'px' => '像素',
                        '%' => '百分比',
                    ),
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'normal'),
                    ),
                ),
                'nav_item_width_23' => array(
                    'title' => '导航项宽度（px）',
                    'type' => 'slider',
                    'std' => 150,
                    'max' => 500,
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'normal'),
                    ),
                ),
                'nav_item_background_23' => array(
                    'title' => '一级导航背景色',
                    'type' => 'color',
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'normal'),
                    ),
                ),
                'nav_item_active_background_setting_23' => array(
                    'title' => '一级导航选中背景色设置',
                    'type' => 'select',
                    'std' => 'gradient',
                    'values' => array(
                        'color' => '纯色',
                        'gradient' => '渐变'
                    ),
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                    ),
                ),
                'nav_item_active_background_23' => array(
                    'title' => '一级导航选中背景色1',
                    'type' => 'color',
                    'std' => '#0169bf',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                    ),
                ),
                'nav_item_active_background_2_23' => array(
                    'title' => '一级导航选中背景色2',
                    'type' => 'color',
                    'std' => '#057acb',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                        array('nav_item_active_background_setting_23', '=', 'gradient'),
                    ),
                ),
                'nav_item_active_background_position_23' => array(
                    'title' => '渐变方向',
                    'type' => 'select',
                    'std' => 'top',
                    'values' => array(
                        'top' => '上',
                        'bottom' => '下',
                        'left' => '左',
                        'right' => '右'
                    ),
                    'std' => 'top',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                        array('nav_item_active_background_setting_23', '=', 'gradient'),
                    ),
                ),
                'nav_item_active_top_23' => array(
                    'title' => '上端突出位置（px）',
                    'type' => 'slider',
                    'std' => '-5',
                    'min' => '-20',
                    'max' => '20',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                    ),
                ),
                'nav_item_color_23' => array(
                    'title' => '字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'normal'),
                    ),
                ),
                'nav_item_active_color_23' => array(
                    'title' => '字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                    ),
                ),
                'nav_item_img_width_23' => array(
                    'title' => '图片宽度',
                    'type' => 'slider',
                    'std' => '16',
                    'max' => '50',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'select'),
                    ),
                ),
                'nav_item_img_right_23' => array(
                    'title' => '图片外边距',
                    'type' => 'margin',
                    'std' => '0 5px 0 0',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav01'),
                        array('nav_item_settins_23', '=', 'normal'),
                    ),
                ),

                // 导航23 国气联 二级导航
                'nav_second_box_img_padding_23' => array(
                    'title' => '二级导航容器内边距',
                    'type' => 'padding',
                    'std' => '60px 10px 20px 10px',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                    ),
                ),
                'nav_second_box_background_setting_23' => array(
                    'title' => '二级导航背景色设置',
                    'type' => 'select',
                    'std' => 'color',
                    'values' => array(
                        'color' => '纯色',
                        'gradient' => '渐变'
                    ),
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                    ),
                ),
                'nav_second_box_background_position_23' => array(
                    'title' => '渐变方向',
                    'type' => 'select',
                    'std' => 'top',
                    'values' => array(
                        'top' => '上',
                        'bottom' => '下',
                        'left' => '左',
                        'right' => '右'
                    ),
                    'std' => 'top',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_box_background_setting_23', '=', 'gradient'),
                    ),
                ),
                'nav_second_box_background_23' => array(
                    'title' => '二级导航背景色1',
                    'type' => 'color',
                    'std' => '#0169bf',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                    ),
                ),
                'nav_second_box_background_2_23' => array(
                    'title' => '二级导航背景色2',
                    'type' => 'color',
                    'std' => '#057acb',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                    ),
                ),
                'nav_second_item_settings_23' => array(
                    'title' => '二级导航设置',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'select'
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                    ),
                ),
                'nav_second_item_background_23' => array(
                    'title' => '二级导航项背景色',
                    'type' => 'color',
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_border_top_23' => array(
                    'title' => '二级导航项边框色',
                    'type' => 'color',
                    'std' => '#1085d9',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_padding_23' => array(
                    'title' => '二级导航内边距',
                    'type' => 'padding',
                    'std' => '5px 0 5px 0',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_height_23' => array(
                    'title' => '二级导航高度',
                    'type' => 'slider',
                    'std' => '46',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_radius_23' => array(
                    'title' => '二级导航圆角',
                    'type' => 'slider',
                    'std' => '4',
                    'max' => '10',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_color_23' => array(
                    'title' => '二级导航字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'normal'),
                    ),
                ),
                'nav_second_item_active_background_23' => array(
                    'title' => '二级导航背景色',
                    'type' => 'color',
                    'std' => '#039dff',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'select'),
                    ),
                ),
                'nav_second_item_active_color_23' => array(
                    'title' => '二级导航字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site23'),
                        array('nav_level', '=', 'nav02'),
                        array('nav_second_item_settings_23', '=', 'select'),
                    ),
                ),

                // 样式24开始
                // 一级导航
                'first_nav_settings_24' => array(
                    'type' => 'buttons',
                    'tab' => true,
                    'std' => 'normal',
                    'values' => array(
                        array('label' => '正常', 'value' => 'normal'),
                        array('label' => '鼠标移入', 'value' => 'hover'),
                    ),
                    'depends' => array(
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_height_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航高度',
                    'std' => 90,
                    'max' => 200,
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'normal'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航字号',
                    'std' => 18,
                    'max' => 50,
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'normal'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_color_24' => array(
                    'type' => 'color',
                    'title' => '一级导航字体颜色',
                    'std' => '#595959',
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'normal'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_hover_color_24' => array(
                    'type' => 'color',
                    'title' => '一级导航鼠标移入字体颜色',
                    'std' => '#1B5A92',
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'hover'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_hover_line_color_24' => array(
                    'type' => 'color',
                    'title' => '一级导航鼠标移入下划线颜色',
                    'std' => '#1B5A92',
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'hover'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'first_nav_hover_line_height_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航鼠标移入下划线高度',
                    'std' => 2,
                    'max' => 100,
                    'depends' => array(
                        array('first_nav_settings_24', '=', 'hover'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                // 一级导航结束
                // 二级导航
                'second_nav_settings_24' => array(
                    'type' => 'buttons',
                    'tab' => true,
                    'title' => '二级导航设置',
                    'std' => 'box',
                    'values' => array(
                        array('label' => '容器', 'value' => 'box'),
                        array('label' => '列表', 'value' => 'list'),
                    ),
                    'depends' => array(
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_full_width_24' => array(
                    'type' => 'checkbox',
                    'title' => '二级导航是否全宽',
                    'std' => 1,
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'box'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_padding_24' => array(
                    'type' => 'padding',
                    'title' => '二级导航内边距',
                    'std' => '30px 15%',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'box'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_bg_24' => array(
                    'type' => 'color',
                    'title' => '二级导航背景色',
                    'std' => '#fff',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'box'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_border_24' => array(
                    'type' => 'color',
                    'title' => '二级导航背景边框色',
                    'std' => '#ddd',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'box'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_width_24' => array(
                    'type' => 'slider',
                    'title' => '二级导航列表宽度（%）',
                    'std' => 30,
                    'max' => 100,
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_padding_24' => array(
                    'type' => 'padding',
                    'title' => '二级导航列表内边距',
                    'std' => '0 30px',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '二级导航字号',
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_padding2_24' => array(
                    'type' => 'padding',
                    'title' => '二级导航项内边距',
                    'std' => '0 0 0 20px',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_margin2_24' => array(
                    'type' => 'margin',
                    'title' => '二级导航项外边距',
                    'std' => '0 0 15px 0',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_line_color_24' => array(
                    'type' => 'color',
                    'title' => '二级导航项左侧短线颜色',
                    'std' => '#1B5A92',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'second_nav_item_color_24' => array(
                    'type' => 'color',
                    'title' => '二级导航项字体颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('second_nav_settings_24', '=', 'list'),
                        array('second_nav', '=', '1'),
                        array('nav_level', '=', 'nav02'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                // 二级导航结束
                // 三级导航
                'third_nav_24' => array(
                    'type' => 'checkbox',
                    'title' => '开启三级导航',
                    'std' => 0,
                    'depends' => array(
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_settings_24' => array(
                    'type' => 'buttons',
                    'tab' => true,
                    'title' => '三级导航设置',
                    'std' => 'box',
                    'values' => array(
                        array('label' => '容器', 'value' => 'box'),
                        array('label' => '列表', 'value' => 'list'),
                    ),
                    'depends' => array(
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('third_nav_24', '=', '1'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_padding_24' => array(
                    'type' => 'padding',
                    'title' => '三级导航容器内边距',
                    'std' => '15px',
                    'depends' => array(
                        array('third_nav_settings_24', '=', 'box'),
                        array('third_nav_24', '=', '1'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_item_padding_24' => array(
                    'type' => 'margin',
                    'title' => '三级导航项内边距',
                    'std' => '3px 0',
                    'depends' => array(
                        array('third_nav_settings_24', '=', 'list'),
                        array('third_nav_24', '=', '1'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_item_margin_24' => array(
                    'type' => 'margin',
                    'title' => '三级导航项外边距',
                    'std' => '0 0 10px 0',
                    'depends' => array(
                        array('third_nav_settings_24', '=', 'list'),
                        array('third_nav_24', '=', '1'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_item_color_24' => array(
                    'type' => 'color',
                    'title' => '三级导航项字体颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('third_nav_settings_24', '=', 'list'),
                        array('third_nav_24', '=', '1'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                'third_nav_item_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '三级导航项字号',
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('third_nav_settings_24', '=', 'list'),
                        array('third_nav_24', '=', '1'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'pc'),
                    )
                ),
                // 三级导航结束
                // 手机端导航
                'mobile_settings_24' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'title' => '手机端设置',
                    'std' => 'btn',
                    'values' => array(
                        array('label' => '按钮', 'value' => 'btn'),
                        array('label' => '导航容器', 'value' => 'box'),
                        array('label' => '一级导航', 'value' => 'first'),
                        array('label' => '二级导航', 'value' => 'second'),
                        array('label' => '三级导航', 'value' => 'third'),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                    )
                ),
                // 按钮
                'btn_right_24' => array(
                    'type' => 'text',
                    'title' => '按钮右边距',
                    'std' => 0,
                    'max' => 500,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_top_24' => array(
                    'type' => 'text',
                    'title' => '按钮上边距',
                    'std' => 0,
                    'max' => 500,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_width_24' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'std' => 25,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_height_24' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'std' => 25,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_bg_24' => array(
                    'type' => 'color',
                    'title' => '按钮背景色',
                    'std' => '#31363a',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_img_settings_24' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'title' => '按钮设置',
                    'std' => 'nav',
                    'values' => array(
                        array('label' => '导航', 'value' => 'nav'),
                        array('label' => '关闭', 'value' => 'close'),
                    ),
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                    )
                ),
                'btn_img1_24' => array(
                    'type' => 'media',
                    'title' => '导航按钮图片',
                    'std' => 'https://oss.lcweb01.cn/jzt/255/image/20240330/a490ebeb0d17de8772ec2422e41412d2.png',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'nav'),
                    )
                ),
                'btn_img1_position_24' => array(
                    'type' => 'text',
                    'title' => '导航按钮图片位置（x y）',
                    'std' => '50% 50%',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'nav'),
                    )
                ),
                'btn_img1_size_24' => array(
                    'type' => 'text',
                    'title' => '导航按钮图片大小（宽 高）',
                    'std' => '40% 40%',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'nav'),
                    )
                ),
                'btn_img2_24' => array(
                    'type' => 'media',
                    'title' => '关闭按钮图片',
                    'std' => 'https://oss.lcweb01.cn/jzt/255/image/20240330/cc6d0b006de04624f1d49771f7c2ad4f.png',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'close'),
                    )
                ),
                'btn_img2_position_24' => array(
                    'type' => 'text',
                    'title' => '关闭按钮图片位置（x y）',
                    'std' => '50% 50%',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'close'),
                    )
                ),
                'btn_img2_size_24' => array(
                    'type' => 'text',
                    'title' => '关闭按钮图片大小（宽 高）',
                    'std' => '40% 40%',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'btn'),
                        array('btn_img_settings_24', '=', 'close'),
                    )
                ),
                // 按钮结束
                // 容器
                'mobile_nav_box_bg_24' => array(
                    'type' => 'color',
                    'title' => '背景色',
                    'std' => '#161719',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'box'),
                    )
                ),
                'mobile_nav_box_padding_24' => array(
                    'type' => 'padding',
                    'title' => '内边距',
                    'std' => '90px 0 80px 0',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'box'),
                    )
                ),
                // 容器结束
                // 一级导航
                'mobile_first_height_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航高度',
                    'std' => 50,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_padding_24' => array(
                    'type' => 'padding',
                    'title' => '一级导航内边距',
                    'std' => '0 20px',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_border_color_24' => array(
                    'type' => 'color',
                    'title' => '一级导航下划线颜色',
                    'std' => '#4D4D4F',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_bg_24' => array(
                    'type' => 'color',
                    'title' => '一级导航背景色',
                    'std' => '#161719',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_color_24' => array(
                    'type' => 'color',
                    'title' => '一级导航字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航字号',
                    'std' => 18,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_arrow_24' => array(
                    'type' => 'media',
                    'title' => '一级导航下拉箭头',
                    'std' => 'https://oss.lcweb01.cn/jzt/255/image/20240330/aa9a4de5fa855e523d5e3dc7a7361c71.png',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_arrow_width_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航下拉箭头宽度',
                    'std' => 25,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                'mobile_first_arrow_height_24' => array(
                    'type' => 'slider',
                    'title' => '一级导航下拉箭头高度',
                    'std' => 25,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'first'),
                    )
                ),
                // 一级导航结束
                // 二级导航
                'mobile_second_padding_24' => array(
                    'type' => 'padding',
                    'title' => '二级导航容器内边距',
                    'std' => '5px 0 10px',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_bg_24' => array(
                    'type' => 'color',
                    'title' => '二级导航背景色',
                    'std' => '#202123',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_padding2_24' => array(
                    'type' => 'padding',
                    'title' => '二级导航项内边距',
                    'std' => '10px 20px',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_font_left_24' => array(
                    'type' => 'slider',
                    'title' => '二级导航项文字左边距',
                    'std' => 20,
                    'max' => 200,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '二级导航字号',
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_color_24' => array(
                    'type' => 'color',
                    'title' => '二级导航字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                'mobile_second_nav_line_color_24' => array(
                    'type' => 'color',
                    'title' => '二级导航项左侧短线颜色',
                    'std' => '#1B5A92',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'second'),
                    )
                ),
                // 二级导航结束
                // 三级导航
                'mobile_third_padding_24' => array(
                    'type' => 'padding',
                    'title' => '三级导航容器内边距',
                    'std' => '0 20px 0 40px',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'third'),
                    )
                ),
                'mobile_third_padding2_24' => array(
                    'type' => 'padding',
                    'title' => '三级导航项内边距',
                    'std' => '0 0 10px 0',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'third'),
                    )
                ),
                'mobile_third_width_24' => array(
                    'type' => 'slider',
                    'title' => '三级导航项宽度（%）',
                    'std' => 25,
                    'max' => 100,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'third'),
                    )
                ),
                'mobile_third_font_size_24' => array(
                    'type' => 'slider',
                    'title' => '三级导航字号',
                    'std' => 12,
                    'max' => 50,
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'third'),
                    )
                ),
                'mobile_third_color_24' => array(
                    'type' => 'color',
                    'title' => '三级导航字体颜色',
                    'std' => '#cacaca',
                    'depends' => array(
                        array('theme', '=', 'site24'),
                        array('equipment', '=', 'mobile'),
                        array('mobile_settings_24', '=', 'third'),
                    )
                ),
                // 三级导航结束
                // 手机端导航结束
                // 样式24结束

                // 导航25
                // 导航25开始
                // 一级导航
                'logo_site25' => array(
                    'type' => 'media',
                    'title' => 'logo',
                    'std' => '',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav01'),
                        array('theme', '=', 'site25'),
                    ),
                ),
                // 一级导航结束
                // 三级导航
                'show_third_nav_25' => array(
                    'type' => 'checkbox',
                    'title' => '显示三级导航',
                    'std' => 1,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site25'),
                    )
                ),
                // 三级导航字体颜色
                'third_nav_color_site25' => array(
                    'type' => 'color',
                    'title' => '三级导航字体颜色',
                   'std' => '#1A1A1A',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=','site25'),
                        array('show_third_nav_25', '=', 1),
                    )
                ),
                // 导航25鼠标移入三级导航字体颜色
                'third_nav_hover_color_site25' => array(
                    'type' => 'color',
                    'title' => '鼠标移入三级导航字体颜色',
                    'std' => '#0256FF',
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=','site25'),
                        array('show_third_nav_25', '=', 1),
                    )
                ),
                // 三级导航内边距
                'third_nav_padding_site25' => array(
                    'type' => 'padding',
                    'title' => '三级导航容器内边距',
                    'std' => '24px 0 0 0',
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site25'),
                        array('show_third_nav_25', '=', 1),
                    )
                ),
                // 三级导航项内边距
                'third_nav_item_padding_site25' => array(
                    'type' => 'margin',
                    'title' => '三级导航项内边距',
                    'std' => '0 0 23px 0',
                    'responsive' => true,
                    'depends' => array(
                        array('equipment', '=', 'pc'),
                        array('nav_level', '=', 'nav03'),
                        array('theme', '=', 'site25'),
                        array('show_third_nav_25', '=', 1),
                    )
                ),
                // 导航25结束
            )
        )
    )
);
