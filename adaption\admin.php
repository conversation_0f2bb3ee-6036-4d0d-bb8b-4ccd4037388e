<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 11:00:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id= $input->get('site_id',0);
 $company_id= $input->get('company_id',0);

 JwAddonsConfig::addonConfig(
 	array(
 		'type' => 'content',
 		'addon_name' => 'adaption',
 		'title' => JText::_('PC显示手机样式'),
 		'desc' => JText::_('PC上显示手机样式'),
 		'category' => '其他',
 		'attr' => array(
 			'general' => array(
 				'ada' => array(

                    'type' => 'select',
                    'title' => JText::_('选择当前网页需求'),
                    'values' => array(
                        'phone' => '手机站显示在PC上',
                        // 'pc' => 'PC站显示在手机上',
                    ),
                    'std' => 'phone',

                ),

 			),
 		),
 	)
 );
