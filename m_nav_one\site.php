<?php
defined('_JEXEC') or die ('resticted access');
class JwpagefactoryAddonM_nav_one extends JwpagefactoryAddons
{
    public function getList()
    {
        $app = JFactory::getApplication();
        $input = $app->input;
        return JwPageFactoryBase::getNavigationList($input->get('layout_id', 0));
    }
    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $app = JUri::getInstance();
        $current_page_id = $app->getVar('id');
        $settings = $this->addon->settings;
        $links = $this->getList();
        $ids = $_GET['ids'] ?? 0;
        $logoImage =  (isset($settings->logoImage)) ? $settings->logoImage : '';
        $logoWidth =  (isset($settings->logoWidth)) ? $settings->logoWidth : 50;
        $logoHeight =  (isset($settings->logoHeight)) ? $settings->logoHeight : 30;
        $nav_color_bg_xz =  (isset($settings->nav_color_bg_xz)) ? $settings->nav_color_bg_xz : '';
        $nav_color_font_xz_1 =  (isset($settings->nav_color_font_xz)) ? $settings->nav_color_font_xz : $settings->nav_color_font_color;
        $nav_color_font_xz_2 =  (isset($settings->nav_color_font_xz)) ? $settings->nav_color_font_xz : $settings->nav2_color_font_color;
        $nav_bg_color =  (isset($settings->nav_bg_color)) ? $settings->nav_bg_color : '#ffffff';
        $nav_arrow_img =  (isset($settings->nav_arrow_img)) ? $settings->nav_arrow_img : ''; // 一级导航箭头图片
        $dh_style =  (isset($settings->dh_style)) ? $settings->dh_style : 'type1';
        if($dh_style=='type1')
        {
            $output = '
                    <section id="menu-hd">
                        <div class="LCS-HLJ-FOTTER00001-navcontent" style="background: none;height: ' . $settings->nav_height . 'px">
                            <div class="LCS-HLJ-FOTTER00001-navcontent-cl">
                            ';
                            if($logoImage){
                                $output .= ' <img style="width: ' . $logoWidth . 'px;height:' . $logoHeight . 'px;" src="' . $logoImage . '">';
                            }

            $output .= ' </div>
                            <div class="LCS-HLJ-FOTTER00001-navcontent-cr">
                                <div class="LCS-HLJ-FOTTER00001-navcontent-menu LCS-HLJ-FOTTER00001-navcontent-menu-mIconClose">
                                    <span class="LCS-HLJ-FOTTER00001-navcontent-menu-bar"></span>
                                    <span class="LCS-HLJ-FOTTER00001-navcontent-menu-bar"></span>
                                    <span class="LCS-HLJ-FOTTER00001-navcontent-menu-bar"></span>
                                </div>
                            </div>
                        </div>
                        <!-- 导航 -->
                        <div class="LCS-HLJ-FOTTER00001-navcontent-sel" style="background-color: ' . $nav_bg_color . '">
                            <ul>
                            ';
                                if (count((array)$links)) {
                                    foreach ($links as $key => $link) {
                                        // 标题
                                        $title = (isset($link->title) && $link->title) ? $link->title : '';
                                        $url = (isset($link->url) ? $link->url : '');
                                        if ($url) {
                                            $urlt = '?view=page&ids='.$link->id;
                                            $url =str_replace("?view=page",$urlt,$url);
                                            if ($link->type == 1) {

                                                $sits=strpos($url,'site_id');
                                                if(!$sits){
                                                    $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                                }
                                            }
                                        }
                                        $target = isset($link->target) ? 'target="' . $link->target . '"' . ($link->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';

                                        $link_text = '';
                                        if($ids!=0 && $ids==$link->id)
                                        {
                                            $output .= '<li style="color:blue;background:'.$nav_color_bg_xz.';">';
                                        }
                                        else
                                        {
                                            $output .= '<li style="color:#3f3f3f">';
                                        }
                                        // 二级导航为空
                                        if (!count((array)$link->data)) {

                                            if($ids!=0 && $ids==$link->id)
                                            {
                                                $output .= '<a style="color: '.$nav_color_font_xz_1.' !important;" href="' . $url . '" ' . $target . ' class="first-wrap">
                                                            <div style="display: flex;align-items: center;">
                                                                <span></span>' . $title . '
                                                            </div>
                                                        </a>';
                                            }
                                            else
                                            {
                                                $output .= '<a href="' . $url . '" ' . $target . ' class="first-wrap">
                                                            <div style="display: flex;align-items: center;">
                                                                <span></span>' . $title . '
                                                            </div>
                                                        </a>';
                                            }

                                        }else{
                                            // 有二级导航
                                            if($ids!=0 && $ids==$link->id)
                                            {
                                                // $output .= '<a style="color: '.$nav_color_font_xz_1.' !important;" ' . $target . '>' . $title . '</a>';
                                                $output .= '<div class="first-wrap">
                                                    <div style="display: flex;align-items: center;">
                                                        <span></span>
                                                        <a style="color: '.$nav_color_font_xz_1.' !important;" ' . $target . '>' . $title . '</a>
                                                    </div>';
                                            }
                                            else
                                            {
                                                // $output .= '<a href="' . $url . '" ' . $target . '>' . $title . '</a>';
                                                $output .= '<div class="first-wrap">
                                                            <div style="display: flex;align-items: center;">
                                                                <span></span>
                                                                <a href="' . $url . '" ' . $target . '>' . $title . '</a>
                                                            </div>
                                                            ';
                                            }
                                                            if($nav_arrow_img){
                                                                $output .= '<img class="LCS-HLJ-FOTTER00001-navcontent-sel-icon" src="' . $nav_arrow_img . '">';
                                                            }
                                            $output .= '</div>';
                                            $output .= '<div class="children LCS-HLJ-FOTTER00001-navcontent-sel-dl">';
                                                foreach ($link->data as $keysub => $linkindex) {
                                                    $url1 = $linkindex->url;
                                                    if ($url1) {
                                                        $urlt = '?view=page&ids='.$link->id;
                                                        $url1 =str_replace("?view=page",$urlt,$url1);
                                                        // $url1 .= '&ids='.$linkindex->id;
                                                        if ($linkindex->type == 1) {//2024.4.18日修改 解决一级导航设置为不跳转后  二级导航链接没有拼接上公司id和站点id问题
                                                            $sits=strpos($url1,'site_id');
                                                            if(!$sits){
                                                                $url1 .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                                            }
                                                        }
                                                    }

                                                    if($ids!=0 && $ids==$linkindex->id)
                                                    {
                                                        $output .= '<dl style="color:blue;background:'.$nav_color_bg_xz.';">';
                                                    }
                                                    else
                                                    {
                                                        $output .= '<dl>';
                                                    }

                                                        $output .= '<dt>';
                                                    if($ids!=0 && $ids==$linkindex->id)
                                                    {
                                                        $output .= '<a style="color: '.$nav_color_font_xz_2.' !important;" class="nav_t_sub" href="' . $url1 . '" ' . $linkindex->target . '>' . $linkindex->title . '</a>';
                                                    }
                                                    else
                                                    {
                                                        $output .= '<a class="nav_t_sub" href="' . $url1 . '" ' . $linkindex->target . '>' . $linkindex->title . '</a>';
                                                    }

                                                        $output .= '</dt>';
                                                    $output .= '</dl>';
                                                }
                                            $output .= '</div>';
                                        }
                                    }

                                }
                                $output .= '</li>
                            </ul>
                        </div>
                    </section>
                  ';
        }
        elseif($dh_style=='type2')
        {
            $logoImage_type2 =  (isset($settings->logoImage_type2)) ? $settings->logoImage_type2 : 'https://oss.lcweb01.cn/joomla/20220805/1c123aeb08d348bba8857580c151e5bf.png';
            $bottonImage_type2 =  (isset($settings->bottonImage_type2)) ? $settings->bottonImage_type2 : 'https://oss.lcweb01.cn/jzt/255/image/20240330/a490ebeb0d17de8772ec2422e41412d2.png';
            $nav_color_font_type2 =  (isset($settings->nav_color_font_type2)) ? $settings->nav_color_font_type2 : '#fff';
            $output .= '<style>
                '.$addon_id.' .top {
                    width: 100%;
                    height: 50px;
                    background: rgba(163, 163, 163, 0.17);
                }
                '.$addon_id.' body, '.$addon_id.' div, '.$addon_id.' ul, '.$addon_id.' li, '.$addon_id.' ol, '.$addon_id.' a, '.$addon_id.' img {
                    margin: 0;
                    padding: 0;
                }
                '.$addon_id.' .logo {
                    margin-top: 12px;
                    margin-left: 11px;
                    width: 102px;
                    float: left;
                }
                '.$addon_id.' .top img {
                    width: 100%;
                    height: auto;
                    border: none;
                }
                '.$addon_id.' .menu {
                    float: right;
                    width: 24px;
                    margin-right: 15px;
                    margin-top: 14px;
                }
                '.$addon_id.' .daohang {
                    width: 140px;
                    background-color: rgba(47, 47, 47, 0.6);
                    text-align: center;
                    color: #fff;
                    float: right;
                    right: 15px;
                    display: none;
                    position: absolute;
                    z-index: 888;
                }
                '.$addon_id.' .clear {
                    clear: both;
                }
                '.$addon_id.' ul, ol {
                    list-style: none;
                }
                '.$addon_id.' .daohang a {
                    color: '.$nav_color_font_type2.';
                }
                '.$addon_id.' a {
                    text-decoration: none;
                    color: #969696;
                    font-size: 12px;
                    font-family: "Microsoft YaHei",Tahoma,Arial,sans-serif;
                }
                '.$addon_id.' .daohang ul li {
                    height: 30px;
                    line-height: 30px;
                    border-bottom: 1px solid rgba(47, 47, 47, 0.7);
                }
                '.$addon_id.' .daohang .ejcd {
                    width: 140px;
                    background-color: rgba(47, 47, 47, 0.6);
                    text-align: center;
                    color: #fff;
                    margin-left: -140px;
                    margin-top: -30px;
                    display: none;
                }
                </style>
            ';
            $output .= '<div class="top">
                <div class="logo">
                    <img src="'.$logoImage_type2.'" alt="logo">
                </div>
                <div class="menu">
                    <img src="'.$bottonImage_type2.'" alt="menu">
                </div>
                <div class="clear"></div>
                <div class="daohang" style="display: none;">
                    <ul>
            ';
            if (count((array)$links))
            {
                foreach ($links as $key => $link)
                {
                    $title = (isset($link->title) && $link->title) ? $link->title : '';
                    $url = (isset($link->url) ? $link->url : '');
                    if ($url) {
                        if ($link->type == 1) {
                            $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                        }
                    }
                    $target = isset($link->target) ? 'target="' . $link->target . '"' . ($link->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';

                    $link_text = '';
                    // 二级导航为空
                    if (!count((array)$link->data))
                    {
                        $output .= '<a href="'.$url.'" ' . $target . '>
                            <li>'.$title.'</li>
                        </a>
                        ';
                    }
                    else
                    {
                        $output .= '<li>
                        <a class="yjcd ty" onclick="dd('.$link->id.')">'.$title.'</a>
                        <div class="ejcd ty2" id="dddd'.$link->id.'">
                            <ul>
                        ';
                        foreach ($link->data as $keysub => $linkindex)
                        {
                            $url1 = $linkindex->url;
                            if ($url1) {

                                // if ($link->type == 1) {//2024.4.18之前使用
                                //     $url1 .= ((strpos($url1, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                // }
                                if ($linkindex->type == 1) {//2024.4.18日修改 解决一级导航设置为不跳转后  二级导航链接没有拼接上公司id和站点id问题
                                    $url1 .= '&ids='.$linkindex->id;
                                    $url1 .= ((strpos($url1, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                }
                            }
                            $output .= '<a href="' . $url1 . '"  ' . $linkindex->target . '>
                                    <li>' . $linkindex->title . '</li>
                                </a>
                            ';
                        }
                        $output .= '</ul>
                                </div>
                            </li>
                        ';
                    }
                }
                $output .= '</ul>
                        </div>
                    </div>
                ';
                $output .="
                <script>
                    $('".$addon_id." .menu').on('click', function () {
                        var state = $('".$addon_id." .daohang').css('display');
                        if (state == 'none') {
                            $('".$addon_id." .daohang').css('display', 'block')
                        } else {
                            $('".$addon_id." .daohang').css('display', 'none')
                        }
                    })
                    function dd(index)
                    {
                        $('".$addon_id." .ejcd').css('display', 'none')
                        $('".$addon_id." #dddd'+index).css('display', 'block')
                    }
                </script>
                ";
            }
        }
        elseif($dh_style === 'type3'){
            $nav3_fixed =  (isset($settings->nav3_fixed)) ? $settings->nav3_fixed : 1;
            $nav3_bg =  (isset($settings->nav3_bg)) ? $settings->nav3_bg : '#fff';
            $nav3_logo =  (isset($settings->nav3_logo)) ? $settings->nav3_logo : '/components/com_jwpagefactory/addons/m_nav_one/assets/img/logo_nav3.png';
            $nav3_height =  (isset($settings->nav3_height)) ? $settings->nav3_height : 60;
            $nav3_padding =  (isset($settings->nav3_padding)) ? $settings->nav3_padding : '13px 4.5%';
            $nav3_button_color =  (isset($settings->nav3_button_color)) ? $settings->nav3_button_color : '#333';
            $nav3_nav_width =  (isset($settings->nav3_nav_width)) ? $settings->nav3_nav_width : 80;
            $nav3_nav_bg =  (isset($settings->nav3_nav_bg)) ? $settings->nav3_nav_bg : '#222';
            $nav3_nav_button_color =  (isset($settings->nav3_nav_button_color)) ? $settings->nav3_nav_button_color : '#fff';
            $nav3_nav_item_height =  (isset($settings->nav3_nav_item_height)) ? $settings->nav3_nav_item_height : 56;
            $nav3_nav_item_font_size =  (isset($settings->nav3_nav_item_font_size)) ? $settings->nav3_nav_item_font_size : 24;
            $nav3_nav_item_color =  (isset($settings->nav3_nav_item_color)) ? $settings->nav3_nav_item_color : '#fff';
            $nav3_tel_title =  (isset($settings->nav3_tel_title)) ? $settings->nav3_tel_title : '服务热线';
            $nav3_tel =  (isset($settings->nav3_tel)) ? $settings->nav3_tel : '************';
            $nav3_tel_color =  (isset($settings->nav3_tel_color)) ? $settings->nav3_tel_color : '#fff';
            $nav3_tel_title_font_size =  (isset($settings->nav3_tel_title_font_size)) ? $settings->nav3_tel_title_font_size : 14;
            $nav3_tel_font_size =  (isset($settings->nav3_tel_font_size)) ? $settings->nav3_tel_font_size : 18;
            $nav3_tel_show =  (isset($settings->nav3_tel_show)) ? $settings->nav3_tel_show : 1;
            $nav3_second_nav_item_font_size =  (isset($settings->nav3_second_nav_item_font_size)) ? $settings->nav3_second_nav_item_font_size : 20;
            if (isset($settings->nav3_nav_item_width_layout2) && $settings->nav3_nav_item_width_layout2) {
                if (is_object($settings->nav3_nav_item_width_layout2)) {
                    $nav3_nav_item_width = $settings->nav3_nav_item_width_layout2->md;
                    $nav3_nav_item_width_sm = $settings->nav3_nav_item_width_layout2->sm;
                    $nav3_nav_item_width_xs = $settings->nav3_nav_item_width_layout2->xs;
                } else {
                    $nav3_nav_item_width = $settings->nav3_nav_item_width_layout2;
                    $nav3_nav_item_width_sm = $settings->nav3_nav_item_width_layout2_sm;
                    $nav3_nav_item_width_xs = $settings->nav3_nav_item_width_layout2_xs;
                }
            } else {
                $nav3_nav_item_width = 58;
                $nav3_nav_item_width_sm = 78;
                $nav3_nav_item_width_xs = 78;
            }
            $output='<style>
                '.$addon_id.' .navBar {
                    transform: translateX(0px);
                    top: 0;
                    margin-top: auto;
                    width: 100%;
                    height: '.$nav3_height.'px;
                    background-color: '.$nav3_bg.';
                    z-index: 999;
                    position: '.($nav3_fixed==1?'fixed':'static').';
                    left: 0;
                    cursor: pointer;
                    overflow: hidden;
                    padding: '.$nav3_padding.';
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                '.$addon_id.' .navBar .bar-logo {
                    width: 100%;
                    height: 100%;
                }
                '.$addon_id.' a{
                    text-decoration: none;
                }
                '.$addon_id.' .navBar .bar-logo img:last-child {
                    display: block;
                    width: auto;
                    height: 100%;
                }
                '.$addon_id.' .navBar .bar-menu {
                    width: '.$nav3_height.'px;
                    height: '.$nav3_height.'px;
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    position: relative;
                }
                '.$addon_id.' .navBar .bar-menu i{
                    display: block;
                    width: 100%;
                    height: 2px;
                    width: 22px;
                }
                '.$addon_id.' .navBar .bar-menu i:not(:last-child){
                    margin-bottom: 5px;
                }
                '.$addon_id.' .fixed-menu .close-bar i {
                    position: absolute;
                    left: 18px;
                    display: block;
                    width: 22px;
                    height: 2px;
                    overflow: hidden;
                }
                '.$addon_id.' .navBar .bar-menu i span,
                '.$addon_id.' .fixed-menu .close-bar i span {
                    display: block;
                    width: 100%;
                    height: 100%;
                    background: '.$nav3_nav_button_color.';
                    transition: all .5s ease 0s;
                    transform-origin: center right;
                    transform: scaleX(0);
                }
                '.$addon_id.' .navBar.active .bar-menu i.bar-top span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-cen span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-bom span {
                    transform: scaleX(1);
                    background: '.$nav3_button_color.';
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .fixed-menu .close-bar i.bar-left {
                    transform: rotateZ(45deg);
                }
                '.$addon_id.' .navBar .bar-menu i.bar-right,
                '.$addon_id.' .fixed-menu .close-bar i.bar-right {
                    transform: rotateZ(-45deg);
                }
                '.$addon_id.' .navBar .bar-menu i.bar-cen,
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .navBar .bar-menu i.bar-right {
                    bottom: 30px;
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .navBar .bar-menu i.bar-right{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                }
                '.$addon_id.' .fixed-menu {
                    position: fixed;
                    top: 0;
                    height: 100%;
                    z-index: 999;
                    left: auto;
                    right: 0;
                    width: '.$nav3_nav_width.'%;
                    transform: translateX(100%);
                    transition: all .8s cubic-bezier(0.3, 0.8, 0.3, 1) 0.3s;
                }
                '.$addon_id.' .fixed-menu.active {
                    transform: matrix(1,0,0,1,0,0) translateX(0px);
                }
                '.$addon_id.' .fixed-menu .close-bar {
                    z-index: 1001;
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    display: block;
                    width: 60px;
                    height: 60px;
                    cursor: pointer;
                }
                '.$addon_id.' .fixed-menu .close-bar i.bar-right,
                '.$addon_id.' .fixed-menu .close-bar i.bar-left {
                    left: 15px;
                    bottom: 30px;
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left span,
                '.$addon_id.' .navBar .bar-menu i.bar-right span,
                '.$addon_id.' .fixed-menu.active .close-bar span {
                    transform: scaleX(1);
                }
                '.$addon_id.' .navBar.active .bar-menu i.bar-left span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-right span{
                    transform: scaleX(0);
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: '.$nav3_nav_bg.';
                    box-sizing: border-box;
                    transition: all .5s cubic-bezier(0.66, 0.53, 0.65, 1) .1s;
                    padding: 0 30px;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul {
                    position: absolute;
                    width: 100%;
                    transform: translateY(-50%);
                    top: 45%;
                    padding: 0 30px;
                }
                '.$addon_id.' nav li, .index-banner li,
                '.$addon_id.' .item-list li, .inner-tab li {
                    list-style-type: none;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul li {
                    display: table;
                    width: 100%;
                    position: relative;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul li a {
                    float: right;
                    overflow: hidden;
                    display: block;
                    width: '.$nav3_nav_item_width.'%;
                    height: '.$nav3_nav_item_height.'px;
                    line-height: '.$nav3_nav_item_height.'px;
                    position: relative;
                    opacity: 1;
                    transform: translateY(50px);
                    color: '.$nav3_nav_item_color.';
                    font-size: '.$nav3_nav_item_font_size.'px;
                }
                '.$addon_id.' .fixed-menu.active .sub-menu-phone ul li a {
                    transform: translateY(0px);
                    opacity: 1;
                }';
                if (count((array)$links)) {
                    foreach ($links as $key => $link) {
                        $output.=$addon_id.' .fixed-menu .sub-menu-phone ul li:nth-of-type('.($key+1).') a {
                            transition: all 0.3s ease-in-out '.(0.4 + $key * 0.1).'s;
                        }';
                    }
                }
                $output.=$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap {
                    position: absolute;
                    bottom: 60px;
                    padding-left: 30px;
                }

                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap .label {
                    display: block;
                    font-size: '.$nav3_tel_title_font_size.'px;
                    color: '.$nav3_tel_color.';
                    text-transform: uppercase;
                    opacity: .5;
                    background-color: transparent;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap .phone a {
                    font: '.$nav3_tel_font_size.'px/1.5 "Gotham-Book";
                    color: '.$nav3_tel_color.';
                }
                '.$addon_id.' .navBar.active .bar-menu i span {
                    background: transparent;
                }
                '.$addon_id.' .navBar .bar-menu i span {
                    background: '.$nav3_button_color.';
                }
                '.$addon_id.' .second-nav{
                    float: right;
                    width: 100%!important;
                    display: none;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul .second-nav ul{
                    position: static;
                    transform: translate(0);
                    padding: 0;
                    margin: 0;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul .second-nav ul a{
                    transition: all 0s;
                    font-size: '.$nav3_second_nav_item_font_size.'px;
                }
                '.$addon_id.' .fa-caret-down{
                    margin-left: 10px;
                    transition: transform 0.3s;
                }
                '.$addon_id.' .fa-caret-down.active{
                    transform: rotate(-180deg);
                }
                @media (max-width: 991px) and (min-width: 768px){
                    '.$addon_id.' .fixed-menu .sub-menu-phone ul li a {
                        width: '.$nav3_nav_item_width_sm.'%;
                    }
                }
                @media (max-width: 767px) {
                    '.$addon_id.' .fixed-menu .sub-menu-phone ul li a {
                        width: '.$nav3_nav_item_width_xs.'%;
                    }
                }
            </style>';
            $output.='<div class="navBarWrap">
                <div class="navBar active">
                    <div class="bar-logo">
                        <a href="#" rel="nofollow">
                            <img src=\''.$nav3_logo.'\'>
                        </a>
                    </div>
                    <div class="bar-menu" onClick="toggleNavClass()">
                        <i class="bar-top"><span></span></i>
                        <i class="bar-cen"><span></span></i>
                        <i class="bar-bom"><span></span></i>
                        <i class="bar-left"><span></span></i>
                        <i class="bar-right"><span></span></i>
                    </div>
                </div>
                <section class="fixed-menu">
                    <div class="close-bar" onClick="toggleNavClass()">
                        <i class="bar-left"><span></span></i>
                        <i class="bar-right"><span></span></i>
                    </div>
                    <nav class="sub-menu-phone">
                        <ul>';
                            if (count((array)$links)) {
                                foreach ($links as $key => $link) {
                                    $title = (isset($link->title) && $link->title) ? $link->title : '';
                                    $url = (isset($link->url) ? $link->url : '');
                                    if ($url) {
                                        if ($link->type == 1) {
                                            $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                        }
                                    }
                                    $target = isset($link->target) ? 'target="' . $link->target . '"' . ($link->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                                    // 一级导航
                                    $output.='<li>
                                        <a href="'.$url.'" rel="nofollow" '.$target.'>
                                            <font>'.$title.'</font>';
                                            if (count((array)$link->data)) {
                                                $output.='<span style="margin-left: 10px;" onClick="toggleSecondNavClass(this);return false;">
                                                    <i class="fa fa-caret-down"></i>
                                                </span>';
                                            }
                                        $output.='</a>';
                                        // 二级导航
                                        if (count((array)$link->data)) {
                                            $output.='<div class="second-nav" style="display: none;">
                                                <ul>';
                                                    foreach ($link->data as $keysub => $linkindex) {
                                                        $title1 = (isset($linkindex->title) && $linkindex->title) ? $linkindex->title : '';
                                                        $url1 = $linkindex->url;
                                                        if ($url1) {
                                                            if ($linkindex->type == 1) {
                                                                $url1 .= '&ids='.$linkindex->id;

                                                                $url1 .= ((strpos($url1, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                                            }
                                                        }
                                                        $target1 = isset($linkindex->target) ? 'target="' . $linkindex->target . '"' . ($linkindex->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                                                        $output.='<li>
                                                            <a href="'.$url1.'" rel="nofollow" '.$target1.'>
                                                                <font>'.$title1.'</font>
                                                            </a>
                                                        </li>';
                                                    }
                                                $output.='</ul>
                                            </div>';
                                        }
                                    $output.='</li>';
                                }
                            }
                            $tel =  (isset($nav3_tel)) ? $nav3_tel : '';
                            $tel = str_replace("-","",$tel);
                        $output.='</ul>';
                        if($nav3_tel_show){
                            $output.='<div class="phone-wrap">
                                <span class="label">'.$nav3_tel_title.'</span>
                                <strong class="phone"><a href="tel:'.$tel.'">'.$nav3_tel.'</a></strong>
                            </div>';
                        }
                    $output.='</nav>
                </section>
            </div>';

            $output.='<script>
                function toggleNavClass(){
                    $("'.$addon_id.' .navBar").toggleClass("active");
                    $("'.$addon_id.' .fixed-menu").toggleClass("active");
                }
                function toggleSecondNavClass(that){
                    $(that).parent().siblings(".second-nav").toggle("display");
                    $(that).find(".fa").toggleClass("active");
                }
            </script>';
        }
        elseif($dh_style === 'type4'){
            $logoImage_type4 =  (isset($settings->logoImage_type4)) ? $settings->logoImage_type4 : 'https://oss.lcweb01.cn/joomla/20220803/f8a5437faf1133e6947a0274ea343aaf.png';
            $bg_type4 =  (isset($settings->bg_type4)) ? $settings->bg_type4 : '#ffa';
            $radius_type4 =  (isset($settings->radius_type4)) ? $settings->radius_type4 : '10';
            $bgcolor_tc =  (isset($settings->bgcolor_tc)) ? $settings->bgcolor_tc : '#f00';
            $bglogo_tc =  (isset($settings->bglogo_tc)) ? $settings->bglogo_tc : 'https://oss.lcweb01.cn/joomla/20220803/f8a5437faf1133e6947a0274ea343aaf.png';
            $dhcolor =  (isset($settings->dhcolor)) ? $settings->dhcolor : '#fff';

            $dhcolor_xhx =  (isset($settings->dhcolor_xhx)) ? $settings->dhcolor_xhx : '#fff';
            $select_jt =  (isset($settings->select_jt)) ? $settings->select_jt : 'https://oss.lcweb01.cn/joomla/20220802/69d13bb71d7999b651e3dd819402aa83.png';
            $tel_type4 =  (isset($settings->tel_type4)) ? $settings->tel_type4 : '************';

            $fixed_type4 =  (isset($settings->fixed_type4)) ? $settings->fixed_type4 : '0';

            $jw_image_type4 = isset($settings->jw_image_type4) && $settings->jw_image_type4 ? $settings->jw_image_type4 : array(

                array(
                    'title4' => '抖音',
                    'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/2cc2e5e752f4a6ce30d73f9c8aa1f50c.png',
                    'fanshi' => 'chu1',
                    'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                    'target' => '',
                    'detail_page_id' => 0,
                ),
                array(
                    'title4' => '视频号',
                    'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/ac716e2bf4e719d8ba42873863886012.png',
                    'fanshi' => 'chu1',
                    'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                    'target' => '',
                    'detail_page_id' => 0,
                ),
                array(
                    'title4' => '微信',
                    'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/0dec44bb71131cb1c2fab5ea8836049e.png',
                    'fanshi' => 'chu1',
                    'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                    'target' => '',
                    'detail_page_id' => 0,
                ),

            );

            $output='<style>

                '.$addon_id.' * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                '.$addon_id.' .cl {
                    clear: both;
                }
                '.$addon_id.' li {
                    list-style: none;
                }
                '.$addon_id.' img{
                    display:inline-block;
                }
                '.$addon_id.' a{
                    color: inherit;
                    text-decoration: none;
                }
                '.$addon_id.' .navBar {
                    transform: translateX(0px);
                    top: 0;
                    margin-top: auto;
                    width: 100%;
                    height: 50px;
                    background-color: '.$bg_type4.';
                    z-index: 999;';
                    if($fixed_type4==1){
                    }else{
                        $output.='position: fixed;';
                    }
                    $output.='left: 0;
                    cursor: pointer;
                    overflow: hidden;
                    padding: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-radius: '.$radius_type4.'px;

                }
                '.$addon_id.' .navBar .bar-logo {
                    width: 100%;
                    height: 100%;
                }

                '.$addon_id.' .navBar .bar-logo img:last-child {
                    display: block;
                    width: auto;
                    height: 100%;
                }
                '.$addon_id.' .navBar .bar-menu {
                    width: 50px;
                    height: 40px;
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    position: absolute;
                    right: 0px;
                    top: 7px;
                }
                '.$addon_id.' .navBar .bar-menu i{
                    display: block;
                    width: 100%;
                    height: 2px;
                    width: 22px;
                }
                '.$addon_id.' .navBar .bar-menu i:not(:last-child){
                    margin-bottom: 5px;
                }
                '.$addon_id.' .fixed-menu .close-bar i {
                    position: absolute;
                    left: 18px;
                    display: block;
                    width: 22px;
                    height: 2px;
                    overflow: hidden;
                }
                '.$addon_id.' .navBar .bar-menu i span,
                '.$addon_id.' .fixed-menu .close-bar i span {
                    display: block;
                    width: 100%;
                    height: 100%;
                    background: #fff;
                    transition: all .5s ease 0s;
                    transform-origin: center right;
                    transform: scaleX(0);
                }
                '.$addon_id.' .navBar.active .bar-menu i.bar-top span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-cen span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-bom span {
                    transform: scaleX(1);
                    background: #333;
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .fixed-menu .close-bar i.bar-left {
                    transform: rotateZ(45deg);
                }
                '.$addon_id.' .navBar .bar-menu i.bar-right,
                '.$addon_id.' .fixed-menu .close-bar i.bar-right {
                    transform: rotateZ(-45deg);
                }
                '.$addon_id.' .navBar .bar-menu i.bar-cen,
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .navBar .bar-menu i.bar-right {
                    bottom: 30px;
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left,
                '.$addon_id.' .navBar .bar-menu i.bar-right{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                }
                '.$addon_id.' .fixed-menu {
                    position: fixed;
                    top: 0;
                    height: 100%;
                    z-index: 999;
                    left: auto;
                    right: 0;
                    width: 100%;
                    background:rgba(0,0,0,0.5);
                    transform: translateX(100%);
                    transition: all .8s cubic-bezier(0.3, 0.8, 0.3, 1) 0.3s;
                }
                '.$addon_id.' .fixed-menu.active {
                    transform: matrix(1,0,0,1,0,0) translateX(0px);
                }
                '.$addon_id.' .fixed-menu .close-bar {
                    z-index: 1001;
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    display: block;
                    width: 50px;
                    height: 50px;
                    cursor: pointer;
                }
                '.$addon_id.' .fixed-menu .close-bar i.bar-right,
                '.$addon_id.' .fixed-menu .close-bar i.bar-left {
                    left: 15px;
                    bottom: 30px;
                }
                '.$addon_id.' .navBar .bar-menu i.bar-left span,
                '.$addon_id.' .navBar .bar-menu i.bar-right span,
                '.$addon_id.' .fixed-menu.active .close-bar span {
                    transform: scaleX(1);
                }
                '.$addon_id.' .navBar.active .bar-menu i.bar-left span,
                '.$addon_id.' .navBar.active .bar-menu i.bar-right span{
                    transform: scaleX(0);
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 80%;
                    height: 100%;
                    background-color: '.$bgcolor_tc.';
                    box-sizing: border-box;
                    transition: all .5s cubic-bezier(0.66, 0.53, 0.65, 1) .1s;
                    padding: 0 30px;
                    overflow-y:auto;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul {
                    width: 100%;
                }
                '.$addon_id.'  nav li, .index-banner li,
                '.$addon_id.' .item-list li, .inner-tab li {
                    list-style-type: none;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul li {
                    display: table;
                    width: 100%;
                    position: relative;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul li a {
                    overflow: hidden;
                    display: block;
                    width: 100%;
                    height: 50px;
                    line-height: 50px;
                    position: relative;
                    opacity: 1;
                    color: '.$dhcolor.';
                    font-size: 16px;
                }
                '.$addon_id.' .fixed-menu.active .sub-menu-phone ul li a {
                    transform: translateY(0px);
                    opacity: 1;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul li:nth-of-type(1) a {
                    transition: all 0.3s ease-in-out 0.4s;

                }
                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap {
                    background:rgba(0,0,0,0.3);text-align: center;color:#fff;
                    height:40px;line-height: 40px;border-radius: 30px;overflow: hidden;
                    margin:30px 0px;
                    display: flex!important;
                    align-items: center;
                    justify-content: center;
                }
                '.$addon_id.' .per .ldi{
                  display: flex!important;
                  align-items: center;
                  justify-content: center;
                }

                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap>img{
                        margin-right: 10px;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap .label {
                    display: block;
                    font-size: 14px;
                    color: #ffa;
                    text-transform: uppercase;
                    opacity: .5;
                    background-color: transparent;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone .phone-wrap .phone a {
                    font:14px/1.5 "Gotham-Book";
                    color: #fff;
                }
                '.$addon_id.' .navBar.active .bar-menu i span {
                    background: transparent;
                }
                '.$addon_id.' .navBar .bar-menu i span {
                    background: #333;
                }
                '.$addon_id.' .second-nav{
                    float: right;
                    width: 100%!important;
                    display: none;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul .second-nav ul{
                    position: static;
                    transform: translate(0);
                    padding: 0;
                    margin: 0;
                }
                '.$addon_id.' .fixed-menu .sub-menu-phone ul .second-nav ul a{
                    transition: all 0s;
                    font-size: 16px;
                    padding:0px 10px;
                }
                '.$addon_id.' .fa-caret-down{
                    margin-left: 10px;
                    transition: transform 0.3s;
                }
                '.$addon_id.' .fa-caret-down.active{
                    transform: rotate(-180deg);
                }
                '.$addon_id.' .dib{padding-bottom:20px;}
                '.$addon_id.' .lisc>a{border-bottom:1px solid '.$dhcolor_xhx.';}
                '.$addon_id.' .erwei{display: none;}
                @media (max-width: 991px) and (min-width: 768px){
                    '.$addon_id.' .fixed-menu .sub-menu-phone ul li a {
                        width: 100%;
                    }
                }

            </style>';

            $output.='<div class="navBarWrap">
                <div class="navBar active">
                    <div class="bar-logo">
                        <a href="#" rel="nofollow">
                            <img src=\''.$logoImage_type4.'\'>
                        </a>
                    </div>
                    <div class="bar-menu" onClick="toggleNavClass()">
                        <i class="bar-top"><span></span></i>
                        <i class="bar-cen"><span></span></i>
                        <i class="bar-bom"><span></span></i>
                        <i class="bar-left"><span></span></i>
                        <i class="bar-right"><span></span></i>
                    </div>
                </div>
                <section class="fixed-menu">
                    <div class="close-bar" onClick="toggleNavClass()">
                        <i class="bar-left"><span></span></i>
                        <i class="bar-right"><span></span></i>
                    </div>

                    <nav class="sub-menu-phone">
                        <div style="margin:60px 0px 20px 0px;text-align:right;">
                            <img style="display: inline;" src="'.$bglogo_tc.'" alt="">
                        </div>
                        <ul>';
                            if (count((array)$links)) {
                                foreach ($links as $key => $link) {
                                    $title = (isset($link->title) && $link->title) ? $link->title : '';
                                    $url = (isset($link->url) ? $link->url : '');
                                    if ($url) {
                                        if ($link->type == 1) {
                                            $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                        }
                                    }
                                    $target = isset($link->target) ? 'target="' . $link->target . '"' . ($link->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                                    // 一级导航
                                        $output.='<li class="lisc">';
                                            if (count((array)$link->data)) {
                                                $output.='<a href="javascript:;" onClick="toggleSecondNavClass1(this);return false;">';
                                            }else{
                                                $output.='<a href="'.$url.'" rel="nofollow" '.$target.'>';
                                            }
                                                $output.='<font>'.$title.'</font>';
                                                if (count((array)$link->data)) {
                                                    $output.='<span style="position:absolute;right:0px;" onClick="toggleSecondNavClass(this);return false;">
                                                       <img style="width:12px;height:6px;" src=\''.$select_jt.'\' alt="">
                                                    </span>';
                                                }

                                        $output.='</a>';


                                        // 二级导航
                                        if (count((array)$link->data)) {
                                            $output.='<div class="second-nav" style="display: none;">
                                                <ul>';
                                                    foreach ($link->data as $keysub => $linkindex) {
                                                        $title1 = (isset($linkindex->title) && $linkindex->title) ? $linkindex->title : '';
                                                        $url1 = $linkindex->url;
                                                        if ($url1) {
                                                            if ($linkindex->type == 1) {
                                                                $url1 .= '&ids='.$linkindex->id;
                                                                $url1 .= ((strpos($url1, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                                            }
                                                        }
                                                        $target1 = isset($linkindex->target) ? 'target="' . $linkindex->target . '"' . ($linkindex->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                                                        $output.='<li>
                                                            <a href="'.$url1.'" rel="nofollow" '.$target1.'>
                                                                <font>'.$title1.'</font>
                                                            </a>
                                                        </li>';
                                                    }
                                                $output.='</ul>
                                            </div>';

                                        }
                                    $output.='</li>';
                                }
                            }

                        $output.='
                        </ul>
                        <div class="phone-wrap">
                            <img style="display: inline;height:22px;vertical-align: middle;" src="https://oss.lcweb01.cn/joomla/20220802/16beacb85269e81a3c4802238884f6a1.png" alt="">
                            <strong class="phone"><a href="tel:'.$tel_type4.'">'.$tel_type4.'</a></strong>
                        </div>
                        <div class="dib" id="xtbr"  bstyle="width:80%;margin:0 auto;" >';

                            foreach ($jw_image_type4 as $key => $item) {
                                $output .= '
                                <div class="swiper-slide">
                                    <div class="sausage1-b1 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.2s">';
                                        if($item->image_carousel_img) {
                                            $output .= '<img src="' . $item->image_carousel_img . '">';
                                        }
                                    $output .= '
                                    </div>
                                    <div class="sausage1-b2 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.3s">
                                        <div>
                                            <div>
                                                <div class="sausage1-b3">' . $item->title_sm . '</div>
                                                <div class="sausage1-b4">' . $item->desc_sm . '</div>
                                                <div class="sausage1-b5">' . $item->content_sm . '</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div style="width:33%;float: left;text-align: center;">
                                    <div class="per" dat="'.$key.'" style="position: relative;width:45px;display: inline-block;">';
                                        if($item->fanshi=='chu2'){
                                            if($item->detail_page_id){
                                                $id=base64_encode($item->detail_page_id);
                                                $link = 'component/jwpagefactory/?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                            }else{
                                                $link ="javascript:;";
                                            }
                                            if($item->tz_page_type=="Internal_pages") {

                                                $output .= '<a href="'.$link.'" target="'.$item->target.'">';
                                            }elseif($item->tz_page_type=="external_links"){

                                                $output .= '<a href="'.$item->detail_page.'" target="'.$item->target.'">';
                                            }else{

                                                $output .= '<a href="javascript:;">';
                                            }

                                        }

                                        $output .= '<div class="ldi" dat="'.$key.'" style="width:45px;height:45px;background:rgba(0,0,0,0.3);border-radius: 30px;text-align:center;line-height: 45px;">
                                            <img class="limg " dat="'.$key.'" style="width:20px;vertical-align: middle;" src="' . $item->logo4 . '" alt="">
                                        </div>';

                                        if($item->fanshi=='chu2'){
                                            $output .= '</a>';
                                        }
                                        if($item->fanshi=='chu1'){
                                            $output .= '<div class="erwei" style="position: absolute;left:-54%;bottom:50px;width:90px;height:auto;background:#fff;padding:5px;">
                                                <img style="position: absolute;left:41%;max-width:20px;height:13px;bottom:-9px;" src="https://oss.lcweb01.cn/joomla/20220802/575b87782b5b1340a996c0f4ec721981.png" alt="">
                                                <img style="width:100%;" src=\' ' . $item->image_xtb . ' \' alt="">
                                            </div>';
                                        }

                                    $output .= '</div>
                                </div>

                                ';
                            }



                            $output .= '<div class="cl"></div>
                        </div>
                    </nav>
                </section>
            </div>';

            $output.='<script>
                function toggleNavClass(){
                    $("'.$addon_id.' .navBar").toggleClass("active");
                    $("'.$addon_id.' .fixed-menu").toggleClass("active");
                }
                function toggleSecondNavClass(that){
                    $(that).parent().siblings(".second-nav").toggle("display");
                    $(that).find(".fa").toggleClass("active");
                }
                function toggleSecondNavClass1(that){
                    $(that).siblings(".second-nav").toggle("display");
                    $(that).find(".fa").toggleClass("active");
                }
                $("'.$addon_id.' .ldi").click(function(e){
                    $(this).next(".erwei").slideToggle(100);
                })

                $("body,html").click(function(e) {

                    if (!$(e.target).hasClass("ldi") && !$(e.target).hasClass("limg")) {
                        $("'.$addon_id.' .erwei").hide();
                    }else{

                        $("'.$addon_id.' .dib .ldi").each(function(aa){
                            var cc=$(e.target).attr("dat");
                            var bb = $(this).attr("dat");
                            if(cc!=bb){
                                $(this).next(".erwei").fadeOut(100);
                            }
                        })

                    }

                })


            </script>';
        }
        elseif($dh_style === 'type5'){
            $nav5_button_size = (isset($settings->nav5_button_size) && $settings -> nav5_button_size) ? $settings -> nav5_button_size : 50;
            $nav5_button_line_width = (isset($settings->nav5_button_line_width) && $settings -> nav5_button_line_width) ?$settings -> nav5_button_line_width : 25;
            $nav5_button_line_height = (isset($settings->nav5_button_line_height) && $settings -> nav5_button_line_height) ?$settings -> nav5_button_line_height : 3;
            $nav5_button_line_color = (isset($settings->nav5_button_line_color) && $settings -> nav5_button_line_color) ?$settings -> nav5_button_line_color : '#000';
            $nav5_button_bg = (isset($settings->nav5_button_bg) && $settings -> nav5_button_bg) ?$settings -> nav5_button_bg : 'transparent';
            $nav5_list_padding = (isset($settings->nav5_list_padding) && $settings -> nav5_list_padding) ?$settings -> nav5_list_padding : '50px 5% 50px 5%';
            $nav5_list_bg_img = (isset($settings->nav5_list_bg_img) && $settings -> nav5_list_bg_img) ?$settings -> nav5_list_bg_img : 'https://oss.lcweb01.cn/joomla/20220816/33ebe3002194ca278fd0e003b09fbb8e.png';
            $nav5_list_bg_color = (isset($settings->nav5_list_bg_color) && $settings -> nav5_list_bg_color) ?$settings -> nav5_list_bg_color : '#fcf5ee';
            $nav5_item_color = (isset($settings->nav5_item_color) && $settings -> nav5_item_color) ?$settings -> nav5_item_color : '#000';
            $nav5_item_size = (isset($settings->nav5_item_size) && $settings -> nav5_item_size) ?$settings -> nav5_item_size : 16;
            $nav5_button_right = (isset($settings->nav5_button_right) && $settings -> nav5_button_right) ?$settings -> nav5_button_right : 0;
            $nav5_button_top = (isset($settings->nav5_button_top) && $settings -> nav5_button_top) ?$settings -> nav5_button_top : 0;
            $nav5_item_margin = (isset($settings->nav5_item_margin) && $settings -> nav5_item_margin) ?$settings -> nav5_item_margin : '1.6em 0 1.6em 0';
            $nav5_item_padding = (isset($settings->nav5_item_padding) && $settings -> nav5_item_padding) ?$settings -> nav5_item_padding : '0 0 0 0';
            $nav5_item_align = (isset($settings->nav5_item_align) && $settings -> nav5_item_align) ?$settings -> nav5_item_align : 'left';
            $nav5_item_second_margin = (isset($settings->nav5_item_second_margin) && $settings -> nav5_item_second_margin) ?$settings -> nav5_item_second_margin : '10px 0 10px 0';
            $nav5_second_item_size = (isset($settings->nav5_second_item_size) && $settings -> nav5_second_item_size) ?$settings -> nav5_second_item_size : 14;
            $nav5_second_item_margin = (isset($settings->nav5_second_item_margin) && $settings -> nav5_second_item_margin) ?$settings -> nav5_second_item_margin : '1.4em 0 1.4em 0';
            $nav5_second_item_padding = (isset($settings->nav5_second_item_padding) && $settings -> nav5_second_item_padding) ?$settings -> nav5_second_item_padding : '0 0 0 0';

            $output='<style>
                '.$addon_id.', '.$addon_id.' *{
                    margin: 0;
                    padding: 0;
                    text-decoration: none;
                    list-style: none;
                }
                '.$addon_id.' ul{
                    padding: 0;
                }
                '.$addon_id.' .top {
                    width: 100%;
                    position: fixed;
                    left: 0;
                    top: 0;
                    background: white;
                    z-index: 9999;
                }
                '.$addon_id.' .cd-nav-trigger {
                    position: fixed;
                    display: inline-block;
                    cursor: pointer;
                }
                '.$addon_id.' .cd-nav-trigger {
                    top: '.$nav5_button_top.'%;
                    right: '.$nav5_button_right.'%;
                    height: '.$nav5_button_size.'px;
                    width: '.$nav5_button_size.'px;
                    z-index: 99999;
                    overflow: hidden;
                    text-indent: 100%;
                    white-space: nowrap;
                    background: '.$nav5_button_bg.';
                }
                '.$addon_id.' .cd-nav-trigger .cd-icon {
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    bottom: auto;
                    right: auto;
                    transform: translateX(-50%) translateY(-50%);
                    display: inline-block;
                    width: '.$nav5_button_line_width.'px;
                    height: '.$nav5_button_line_height.'px;
                    background-color: '.$nav5_button_line_color.';
                    z-index: 10;
                }
                '.$addon_id.' .cd-nav-trigger .cd-icon::before, '.$addon_id.' .cd-nav-trigger .cd-icon:after {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    background-color: '.$nav5_button_line_color.';
                    -webkit-transform: translateZ(0);
                    -moz-transform: translateZ(0);
                    -ms-transform: translateZ(0);
                    -o-transform: translateZ(0);
                    transform: translateZ(0);
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;
                    -webkit-transition: -webkit-transform .2s;
                    -moz-transition: -moz-transform .2s;
                    transition: transform .2s;
                    -webkit-box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    box-sizing: border-box;
                    content: "";
                }
                '.$addon_id.' .cd-nav-trigger .cd-icon::before {
                    -webkit-transform: translateY(-6px) rotate(0deg);
                    -moz-transform: translateY(-6px) rotate(0deg);
                    -ms-transform: translateY(-6px) rotate(0deg);
                    -o-transform: translateY(-6px) rotate(0deg);
                    transform: translateY(-6px) rotate(0deg);
                }
                '.$addon_id.' .cd-nav-trigger .cd-icon::after {
                    -webkit-transform: translateY(6px) rotate(0deg);
                    -moz-transform: translateY(6px) rotate(0deg);
                    -ms-transform: translateY(6px) rotate(0deg);
                    -o-transform: translateY(6px) rotate(0deg);
                    transform: translateY(6px) rotate(0deg);
                }
                '.$addon_id.' .cd-primary-nav {
                    position: fixed;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    padding: '.$nav5_list_padding.';
                    margin: 0;
                    z-index: 3;
                    background: url(\''.$nav5_list_bg_img.'\');
                    overflow: auto;
                    -webkit-overflow-scrolling: touch;
                    visibility: hidden;
                    opacity: 0;
                    -webkit-transition: visibility 0s, opacity 0.2s;
                    -moz-transition: visibility 0s, opacity 0.2s;
                    transition: visibility 0s, opacity 0.2s;
                }
                '.$addon_id.' .cd-primary-nav li {
                    margin: '.$nav5_item_margin.';
                    padding: '.$nav5_item_padding.';
                    text-align: '.$nav5_item_align.';
                    text-transform: capitalize;
                    position: relative;
                }
                '.$addon_id.' .cd-primary-nav a {
                    font-size: '.$nav5_item_size.'px;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    -webkit-transition: color 0.2s;
                    -moz-transition: color 0.2s;
                    transition: color 0.2s;
                    color: '.$nav5_item_color.';
                }
                '.$addon_id.' .cd-primary-nav a span{
                    top: 50%;
                    transform: translateY(-50%);
                }
                '.$addon_id.' .cd-primary-nav.fade-in {
                    visibility: visible;
                    opacity: 1;
                }
                '.$addon_id.' .cd-overlay-nav, .cd-overlay-content {
                    position: fixed;
                    top: 18px;
                    right: 5%;
                    height: 4px;
                    width: 4px;
                    -webkit-transform: translateX(-20px) translateY(20px);
                    -moz-transform: translateX(-20px) translateY(20px);
                    -ms-transform: translateX(-20px) translateY(20px);
                    -o-transform: translateX(-20px) translateY(20px);
                    transform: translateX(-20px) translateY(20px);
                    z-index: 2;
                }
                '.$addon_id.' .cd-overlay-nav span, .cd-overlay-content span {
                    display: inline-block;
                    position: absolute;
                    border-radius: 50%;
                    -webkit-transform: translateZ(0);
                    -moz-transform: translateZ(0);
                    -ms-transform: translateZ(0);
                    -o-transform: translateZ(0);
                    transform: translateZ(0);
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;
                    will-change: transform;
                    -webkit-transform-origin: 50% 50%;
                    -moz-transform-origin: 50% 50%;
                    -ms-transform-origin: 50% 50%;
                    -o-transform-origin: 50% 50%;
                    transform-origin: 50% 50%;
                    -webkit-transform: scale(0);
                    -moz-transform: scale(0);
                    -ms-transform: scale(0);
                    -o-transform: scale(0);
                    transform: scale(0);
                }
                '.$addon_id.' .cd-overlay-nav span {
                    background: '.$nav5_list_bg_color.';
                }
                '.$addon_id.' .cd-overlay-nav, .cd-overlay-content {
                    position: fixed;
                    top: 18px;
                    right: 5%;
                    height: 4px;
                    width: 4px;
                    -webkit-transform: translateX(-20px) translateY(20px);
                    -moz-transform: translateX(-20px) translateY(20px);
                    -ms-transform: translateX(-20px) translateY(20px);
                    -o-transform: translateX(-20px) translateY(20px);
                    transform: translateX(-20px) translateY(20px);
                    z-index: 4;
                }
                '.$addon_id.' .cd-overlay-nav {
                    z-index: 2;
                }
                '.$addon_id.' .second-nav {
                    margin: '.$nav5_item_second_margin.';
                }
                '.$addon_id.' .second-nav li{
                    margin: '.$nav5_second_item_margin.';
                    padding: '.$nav5_second_item_padding.';
                }
                '.$addon_id.' .second-nav li > a{
                    font-size: '.$nav5_second_item_size.'px;
                }
            </style>';
            $select_jt =  (isset($settings->select_jt)) ? $settings->select_jt : 'https://oss.lcweb01.cn/joomla/20220802/69d13bb71d7999b651e3dd819402aa83.png';
            $output.='<div class="top">
                <div class="cd-nav-trigger">菜单<span class="cd-icon"></span></div>
                <nav>
                    <ul class="cd-primary-nav">';
                    if (count((array)$links)) {
                        foreach ($links as $key => $link) {
                            $title = (isset($link->title) && $link->title) ? $link->title : '';
                            $url = (isset($link->url) ? $link->url : '');
                            if ($url) {
                                if ($link->type == 1) {
                                    $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                }
                            }
                            $target = isset($link->target) ? 'target="' . $link->target . '"' . ($link->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                            // 一级导航
                                $output.='<li>
                                    <a href="'.$url.'" rel="nofollow" '.$target.'>
                                        <font>'.$title.'</font>';
                                        if (count((array)$link->data)) {
                                            $output.='<span style="position:absolute;right:0px;" onClick="toggleSecondNavClass(this);return false;">
                                               <img style="width:12px;height:6px;" src=\''.$select_jt.'\' alt="">
                                            </span>';
                                        }

                                $output.='</a>';


                                // 二级导航
                                if (count((array)$link->data)) {
                                    $output.='<div class="second-nav" style="display: none;">
                                        <ul>';
                                            foreach ($link->data as $keysub => $linkindex) {
                                                $title1 = (isset($linkindex->title) && $linkindex->title) ? $linkindex->title : '';
                                                $url1 = $linkindex->url;
                                                if ($url1) {
                                                    if ($linkindex->type == 1) {
                                                        $url1 .= '&ids='.$linkindex->id;
                                                        $url1 .= ((strpos($url1, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
                                                    }
                                                }
                                                $target1 = isset($linkindex->target) ? 'target="' . $linkindex->target . '"' . ($linkindex->target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
                                                $output.='<li>
                                                    <a href="'.$url1.'" rel="nofollow" '.$target1.'>
                                                        <font>'.$title1.'</font>
                                                    </a>
                                                </li>';
                                            }
                                        $output.='</ul>
                                    </div>';

                                }
                            $output.='</li>';
                        }
                    }
                        $output.='
                    </ul>
                </nav>
                <div class="cd-overlay-nav">
                    <span class="" style="transform: scaleX(0) scaleY(0); height: 1859.5px; width: 1859.5px; top: -929.751px; left: -929.751px;"></span>
                </div>
                <div class="cd-overlay-content">
                    <span class="" style="transform: scaleX(0) scaleY(0); height: 1859.5px; width: 1859.5px; top: -929.751px; left: -929.751px;"></span>
                </div>
            </div>
            <script type="text/javascript">
                function toggleSecondNavClass(that){
                    $(that).parent().siblings(".second-nav").toggle("display");
                }
            </script>';
        }
        return $output;
    }
    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;
        $nav_color_border =  (isset($settings->nav_color_border)) ? $settings->nav_color_border : '#333333'; // 导航导航按钮颜色
        $nav_color_border_left =  (isset($settings->nav_color_border_left)) ? $settings->nav_color_border_left : '#cc0000'; //一级导航竖线颜色
        $nav_color_border_br =  (isset($settings->nav_color_border_br)) ? $settings->nav_color_border_br : '#3f3f3f'; // 一级导航分割线颜色
        $nav_color_font_color =  (isset($settings->nav_color_font_color)) ? $settings->nav_color_font_color : '#cc0000'; // 一级字体颜色
        $nav_color_font_color_hover =  (isset($settings->nav_color_font_color_hover)) ? $settings->nav_color_font_color_hover : '#e81748'; // 一级字体颜色
        $nav_font_size =  (isset($settings->nav_font_size)) ? $settings->nav_font_size : 14; // 一级字体大小
        $nav_border_height =  (isset($settings->nav_border_height)) ? $settings->nav_border_height : 16; // 一级导航竖线高度
        $nav_border_width =  (isset($settings->nav_border_width)) ? $settings->nav_border_width : 2; // 一级导航竖线宽度
        $nav_border_marginRight =  (isset($settings->nav_border_marginRight)) ? $settings->nav_border_marginRight : 10; // 一级导航竖右边距
        $nav_font_w =  (isset($settings->nav_font_w)) ? $settings->nav_font_w : 0; // 一级字体是否加粗
        $nav_arrow =  (isset($settings->nav_arrow)) ? $settings->nav_arrow : 6; // 一级箭头大小
        $nav2_bg_color =  (isset($settings->nav2_bg_color)) ? $settings->nav2_bg_color : '#eeeeee'; // 二级导航背景
        $nav2_color_font_color =  (isset($settings->nav2_color_font_color)) ? $settings->nav2_color_font_color : '#cc0000'; // 二级导航字体颜色
        $nav2_font_size =  (isset($settings->nav2_font_size)) ? $settings->nav2_font_size : 14; // 二级字体大小
        $nav2_font_w =  (isset($settings->nav2_font_w)) ? $settings->nav2_font_w : 0; // 二级字体是否加粗
        $nav2_lineHeight =  (isset($settings->nav2_lineHeight)) ? $settings->nav2_lineHeight : 34; // 二级导航行高
        $nav_padding =  (isset($settings->nav_padding)) ? $settings->nav_padding : ''; // 导航栏内边距
        $nav_s_padding =  (isset($settings->nav_s_padding)) ? $settings->nav_s_padding : ''; // 导航栏内边距

        $css = "
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu {display: inline-block;width: 33px;height: 42px;padding: 3px 10px;vertical-align: middle;padding-right: 0;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu-bar {display: block;width: 22px;height: 2px;margin-top: 8px;margin-bottom: 8px;transition: all .4s ease;background: {$nav_color_border};}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(1) {animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate1;-webkit-animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate1;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(2) {opacity: 0;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(3) {animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate2;-webkit-animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate2;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-menu-mIconClose .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(1) {animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate3;-webkit-animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate3;}
            $addon_id .mIconClose .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(2) {opacity: 1;}
            $addon_id .mIconClose .LCS-HLJ-FOTTER00001-navcontent-menu-bar:nth-child(3) {animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate4;-webkit-animation: 0.5s ease-out forwards LCS-HLJ-FOTTER00001-navcontent-animate4;}
            @-webkit-keyframes LCS-HLJ-FOTTER00001-navcontent-animate1 {
                0% {
                    -webkit-transform: rotate(0deg) translateY(0px);
                }

                100% {
                    -webkit-transform: rotate(45deg) translateY(8px) translateX(8px);
                }
            }

            @keyframes LCS-HLJ-FOTTER00001-navcontent-animate1 {
                0% {
                    transform: rotate(0deg) translateY(0px);
                }

                100% {
                    transform: rotate(45deg) translateY(8px) translateX(8px);
                }
            }

            @-webkit-keyframes LCS-HLJ-FOTTER00001-navcontent-animate2 {
                0% {
                    -webkit-transform: rotate(0deg) translateY(0px);
                }

                100% {
                    -webkit-transform: rotate(-45deg) translateY(-7px) translateX(6px);
                }
            }

            @keyframes LCS-HLJ-FOTTER00001-navcontent-animate2 {
                0% {
                    transform: rotate(0deg) translateY(0px);
                }

                100% {
                    transform: rotate(-45deg) translateY(-7px) translateX(6px);
                }
            }

            @-webkit-keyframes LCS-HLJ-FOTTER00001-navcontent-animate3 {
                0% {
                    -webkit-transform: rotate(45deg) translateY(8px) translateX(8px);
                }

                100% {
                    -webkit-transform: rotate(0deg) translateY(0px);
                }
            }

            @keyframes LCS-HLJ-FOTTER00001-navcontent-animate3 {
                0% {
                    transform: rotate(45deg) translateY(8px) translateX(8px);
                }

                100% {
                    transform: rotate(0deg) translateY(0px);
                }
            }

            @-webkit-keyframes LCS-HLJ-FOTTER00001-navcontent-animate4 {
                0% {
                    -webkit-transform: rotate(-45deg) translateY(-8px) translateX(8px);
                }

                100% {
                    -webkit-transform: rotate(0deg) translateY(0px);
                }
            }

            @keyframes LCS-HLJ-FOTTER00001-navcontent-animate4 {
                0% {
                    transform: rotate(-45deg) translateY(-8px) translateX(8px);
                }

                100% {
                    transform: rotate(0deg) translateY(0px);
                }
            }
            $addon_id .LCS-HLJ-FOTTER00001-navcontent{padding: {$nav_padding};overflow: hidden;background: #ffffff;position: relative;line-height: 42px;display: flex;align-items: center;justify-content: space-between;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-cl{float: left; width: 80%;overflow: hidden}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-cr{display: inline-block;float: right;line-height: initial;text-align: right;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel{width: 100%;left: 100%;background: #ffffff;position: fixed;z-index: 3;transition: .6s all 0s;overflow: auto;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel ul{margin: 0;padding: 0;list-style: none;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap{display: flex;align-items: center;justify-content: space-between;padding: {$nav_s_padding};border-bottom: 1px solid {$nav_color_border_br};font-size: {$nav_font_size}px;color: {$nav_color_font_color} !important;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap:hover{color:{$nav_color_font_color_hover} !important;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel li>.first-wrap>div>span{width: {$nav_border_width}px;height: {$nav_border_height}px;display: inline-block;background: {$nav_color_border_left};margin-right: {$nav_border_marginRight}px;vertical-align: middle;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap img{width: {$nav_arrow}px;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap a{color: inherit;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl{display: none;padding: 10px 0;background: {$nav2_bg_color};}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl>dl{margin: 0 !important;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl>dl>dt{line-height: {$nav2_lineHeight}px;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl>dl>dt>a{display: inline-block;color: {$nav2_color_font_color} !important;width: 100%;padding-left: 26px;font-size: {$nav2_font_size}px;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-active{left: 0;transition: .6s all 0s;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl-active{color: #cc0000!important;}
            $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-icon-active{transform: rotate(90deg);}
             body.LCS-HLJ-FOTTER00001-navcontent-sel-body-active{overflow: hidden;}
        ";
        //        一级导航开启加粗
        if($nav_font_w){
            $css.="$addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap{
                font-weight: bold;
            }";
        }else{
            $css.="$addon_id .LCS-HLJ-FOTTER00001-navcontent-sel>ul>li>.first-wrap{
                font-weight: 1;
            }";
        }
        //        二级导航开启加粗
        if($nav2_font_w){
            $css.=" $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl>dl>dt>a{
                font-weight: bold;
            }";
        }else{
            $css.=" $addon_id .LCS-HLJ-FOTTER00001-navcontent-sel-dl>dl>dt>a{
                font-weight: 1;
            }";
        }
        return $css;
    }
    public function js()
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $nav_height =  (isset($settings->nav_height)) ? $settings->nav_height : ''; // 导航栏高度
        $dh_style =  (isset($settings->dh_style)) ? $settings->dh_style : 'type1';
        if($dh_style === 'type1'){
            $js = '
                jQuery(function($){
                           let bar_id=$(".LCS-HLJ-FOTTER00001-navcontent-menu").parents(".jwpf-section")[jQuery(".LCS-HLJ-FOTTER00001-navcontent-menu").parents(".jwpf-section").length-1].id;
                          $(".LCS-HLJ-FOTTER00001-navcontent-menu").click(function () {
                        if ($(this).hasClass("LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive")) {
                            $(this).removeClass("LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive").addClass("LCS-HLJ-FOTTER00001-navcontent-menu-mIconClose");
                            $(".LCS-HLJ-FOTTER00001-navcontent-sel").removeClass("LCS-HLJ-FOTTER00001-navcontent-sel-active")
                            $("body").removeClass("LCS-HLJ-FOTTER00001-navcontent-sel-body-active");
                        } else {
                             $("#"+bar_id).css("cssText","z-index:1000 !important;height:' . $settings->nav_height . 'px");
                            $(this).removeClass("LCS-HLJ-FOTTER00001-navcontent-menu-mIconClose").addClass("LCS-HLJ-FOTTER00001-navcontent-menu-mIconActive");
                            $("body").addClass("LCS-HLJ-FOTTER00001-navcontent-sel-body-active");
                            $(".LCS-HLJ-FOTTER00001-navcontent-sel").addClass("LCS-HLJ-FOTTER00001-navcontent-sel-active")
                        }

                    });
                     $(".LCS-HLJ-FOTTER00001-navcontent-sel ul li>.first-wrap").click(function (e) {
                        if(e.target.tagName !== "A" || $(e.target).attr("href") === ""){
                            if($(this).hasClass("LCS-HLJ-FOTTER00001-navcontent-sel-dl-active")){
                                $(this).removeClass("LCS-HLJ-FOTTER00001-navcontent-sel-dl-active")
                                $(this).find(".LCS-HLJ-FOTTER00001-navcontent-sel-icon").removeClass("LCS-HLJ-FOTTER00001-navcontent-sel-icon-active")
                                $(this).siblings(".LCS-HLJ-FOTTER00001-navcontent-sel-dl").slideUp(500)
                            }else{
                                $(this).addClass("LCS-HLJ-FOTTER00001-navcontent-sel-dl-active");
                                $(this).find(".LCS-HLJ-FOTTER00001-navcontent-sel-icon").addClass("LCS-HLJ-FOTTER00001-navcontent-sel-icon-active");
                                $(this).siblings(".LCS-HLJ-FOTTER00001-navcontent-sel-dl").slideDown(500)
                            }
                        }

                        });
                        let sheightow=$(window).height();
                         $("#"+bar_id).css("height","' . $settings->nav_height . 'px");

                         let sheightoh=$("#"+bar_id).outerHeight(true);
                          console.log("sheightoh")
                          console.log($("#"+bar_id).css("height"))
                          console.log(sheightoh)
                          console.log("' . $settings->nav_height . 'px")
                        $("#"+bar_id).find(".LCS-HLJ-FOTTER00001-navcontent-sel").css("height",sheightow-sheightoh+"px");
                        $("#"+bar_id).find(".LCS-HLJ-FOTTER00001-navcontent-sel").css("top",sheightoh+"px");

                })
              ';
        }
        return $js;
    }
    public function scripts()
    {
        $settings = $this->addon->settings;
        $dh_style =  (isset($settings->dh_style)) ? $settings->dh_style : 'type1';
        $scripts = [];
        if($dh_style === 'type5')
        {
            $scripts = array(
                $dh_style === 'type5' ? JURI::base(true) . '/components/com_jwpagefactory/addons/m_nav_one/assets/js/velocity.js' : '',
                $dh_style === 'type5' ? JURI::base(true) . '/components/com_jwpagefactory/addons/m_nav_one/assets/js/main.js' : '',
            );
        }
        return $scripts;
    }
}