<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'swiper_navtab',
        'title' => JText::_('多用途轮播组件'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => '',
                ),
                'jw_snt_tab_item' => array(
                    'title' => '内容列表',
                    'std' => array(
                        array(
                            'types' => 'text',
                            'title' => '连续六年蝉联',
                            'title_s' => '中国互联网百强',
                            'title_ss' => '龙采科技集团',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220513/d1dc8b73e761c063b3debab6df9ae58b.jpg',
                        ),
                        array(
                            'types' => 'video',
                            'video' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/img/video.mp4',
                        ),
                    ),
                    'attr' => array(
                        'types' => array(
                            'title' => '内容类型',
                            'type' => 'buttons',
                            'tabs' => true,
                            'values' => array(
                                array(
                                    'label' => '图文',
                                    'value' => 'text',
                                ),
                                array(
                                    'label' => '视频',
                                    'value' => 'video',
                                ),
                            ),
                            'std' => 'text',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '投资创新的未来',
                            'depends' => array(
                                array('types', '=', 'text'),
                            ),
                        ),
                        'title_s' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '中国互联网百强',
                            'depends' => array(
                                array('types', '=', 'text'),
                            ),
                        ),
                        'title_ss' => array(
                            'type' => 'text',
                            'title' => '副标题2',
                            'std' => '龙采科技集团',
                            'depends' => array(
                                array('types', '=', 'text'),
                            ),
                        ),
                        'bg_img' => array(
                            'type' => 'media',
                            'title' => '背景图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220513/d1dc8b73e761c063b3debab6df9ae58b.jpg',
                            'depends' => array(
                                array('types', '=', 'text'),
                            ),
                        ),
                        'video' => array(
                            'type' => 'media',
                            'title' => '视频',
                            'format' => 'video',
                            'std' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/img/video.mp4',
                            'depends' => array(
                                array('types', '=', 'video'),
                            ),
                        ),
                    ),
                ),
            ),
        ),
    )
);
