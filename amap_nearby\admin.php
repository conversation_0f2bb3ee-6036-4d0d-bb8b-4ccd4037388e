<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'amap_nearby',
		'title' => '高德地图搜索附近',
		'desc' => '',
		'category' => '地图',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),

				'content_style' => array(
					'type' => 'buttons',
					'title' => '样式选项',
					'std' => 'tab_style',
					'values' => array(
						array(
							'label' => '选项卡',
							'value' => 'tab_style'
						),
						array(
							'label' => '地图',
							'value' => 'map_style'
						),
					),
					'tabs' => true,
				),
				//选项卡
				'section_tab_bgColor' => array(
					'type' => 'color',
					'title' => '选项卡背景色',
					'std' => '#F0F0F0',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_color' => array(
					'type' => 'color',
					'title' => '选项卡文字颜色',
					'std' => '#333',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_fontsize' => array(
					'type' => 'slider',
					'title' => '选项卡文字大小',
					'std' => array(
						'md' => 16,
						'sm' => 16,
						'xs' => 14,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_active_fontsize' => array(
					'type' => 'slider',
					'title' => '选项卡选中文字大小',
					'std' => array(
						'md' => 16,
						'sm' => 16,
						'xs' => 14,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_active_bgColor' => array(
					'type' => 'color',
					'title' => '选项卡选中背景色',
					'std' => '#730B0C',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_active_color' => array(
					'type' => 'color',
					'title' => '选项卡选中文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_num' => array(
					'type' => 'slider',
					'title' => '一行显示选项卡的个数',
					'std' => array(
						'md' => 8,
						'sm' => 6,
						'xs' => 3,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 10,
				),
				'section_tab_H' => array(
					'type' => 'slider',
					'title' => '选项卡高度',
					'std' => array(
						'md' => 60,
						'sm' => 52,
						'xs' => 52,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_mgB' => array(
					'type' => 'slider',
					'title' => '选项卡外下边距',
					'std' => array(
						'md' => 20,
						'sm' => 20,
						'xs' => 10,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 1000,
				),
				'section_tab_active_time' => array(
					'type' => 'slider',
					'title' => '选项卡动画效果时间',
					'desc' => '',
					'min' => 100,
					'max' => 500,
					'std' => 200,
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_item' => array(
					'title' => '选项卡列表',
					'attr' => array(
						'name' => array(
							'type' => 'text',
							'title' => '选项卡名称',
							'desc' => '当前选项卡需要搜索的关键字',
							'std' => '公交',
						),
						'icon' => array(
							'type' => 'media',
							'title' => '选卡未选中图标',
							'desc' => '',
							'std' => 'https://oss.lcweb01.cn/joomla/20210918/074a58d63df2da121f382695134f4c6b.png',
						),
						'iconA' => array(
							'type' => 'media',
							'title' => '选项卡选中图标',
							'desc' => '',
							'std' => 'https://oss.lcweb01.cn/joomla/20210918/f2ffeca1b3a329711ca70919ec78d7a9.png'
						),
						'locationIcon' => array(
							'type' => 'media',
							'title' => '地图上散点图标',
							'desc' => '',
							'std' => 'https://oss.lcweb01.cn/joomla/20210918/72aaf431624d9ed239e0ade487ed37cd.png'
						),
					),
					'std' => array(
						array(
							'name'=> '公交',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/074a58d63df2da121f382695134f4c6b.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/f2ffeca1b3a329711ca70919ec78d7a9.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/72aaf431624d9ed239e0ade487ed37cd.png',
						),
						array(
							'name'=> '学校',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/4fa8186d2a507d17f3ce660c7769a151.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/fd6c755b2a934cb87c29ee545845c4f4.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/57968ffb0daf771d9b47ec106da113ea.png',
						),
						array(
							'name'=> '医院',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/a17adb57bafea90ebfe58288d5247404.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/b15c97fd22767f3b5e10c4e5e6c8233e.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/27c4ce9da18a13709ef685015e4c647e.png',
						),
						array(
							'name'=> '宾馆',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/e7e70c682cc06b101dbaa0cdd7ad13b7.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/3d807068767378da1e35e50d36e96c3e.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/be4b223dc5258d0898c853b2654c6dfb.png',
						),
						array(
							'name'=> '餐饮',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/70ef49ddc1d6395536467fc5657a811f.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/fa6dcec98f3662609993eed367b06b6d.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/23dbb3f6ee74a7726f58e50bb11c95c9.png',
						),
						array(
							'name'=> '娱乐',
							'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/c071b60e8cbd6a67fdd0b89dc635d49b.png',
							'iconA'=> 'https://oss.lcweb01.cn/joomla/20210918/88ef38eaee341ada2fb09654a3852ab1.png',
							'locationIcon'=> 'https://oss.lcweb01.cn/joomla/20210918/f6cc98ba70ef8316b27b58d8c5a45149.png',
						),
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				//地图选项卡
				'section_map_name' => array(
					'type' => 'text',
					'title' => '地图中心点名称',
					'desc' => '',
					'std' => '中华巴洛克',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_map_city' => array(
					'type' => 'text',
					'title' => '地图中心点所在城市',
					'desc' => '地图中心点城市需要与填写的地图中心点经纬度一致',
					'std' => '太原',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_map_lng' => array(
					'type' => 'text',
					'title' => '地图中心点经度',
					'desc' => '',
					'std' => '112.54716',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_map_lat' => array(
					'type' => 'text',
					'title' => '地图中心点纬度',
					'desc' => '',
					'std' => '37.7945',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_map_centerIcon' => array(
					'type' => 'media',
					'title' => '地图中心点图标',
					'std' => 'https://oss.lcweb01.cn/joomla/20210918/ed63a6697794aa7fe19b0ab9bd889463.png',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_map_centerIcon_w' => array(
					'type' => 'slider',
					'title' => '地图中心点图标宽度',
					'desc' => '数值需要与中心点图标的宽度一致',
					'std' => 50,
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'max' => 200,
				),
				'section_map_centerIcon_h' => array(
					'type' => 'slider',
					'title' => '地图中心点图标高度',
					'desc' => '数值需要与中心点图标的高度一致',
					'std' => 50,
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'max' => 200,
				),
				'section_map_icon_w' => array(
					'type' => 'slider',
					'title' => '地图散点图标宽度',
					'desc' => '数值需要与散点图标的宽度一致',
					'std' => 28,
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'max' => 50,
				),
				'section_map_icon_h' => array(
					'type' => 'slider',
					'title' => '地图散点图标高度',
					'desc' => '数值需要与散点图标的高度一致',
					'std' => 36,
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'max' => 50,
				),
				'section_map_map_w' => array(
					'type' => 'slider',
					'title' => '左侧地图pc宽度',
					'std' => 500,
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'max' => 1000,
				),
				'section_map_map_h' => array(
					'type' => 'slider',
					'title' => '左侧地图高度',
					'desc' => '',
					'std' => array(
						'md' => 400,
						'sm' => 400,
						'xs' => 400,
					),
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'responsive' => true,
                    'min' => 200,
					'max' => 2000,
				),

				'section_map_content_h' => array(
					'type' => 'slider',
					'title' => '右侧列表高度',
					'desc' => '只在平板和手机起作用',
					'std' => array(
						'md' => 400,
						'sm' => 400,
						'xs' => 500,
					),
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'responsive' => true,
					'min' => 200,
					'max' => 2000,
				),
				'section_content_title_fontsize' => array(
					'type' => 'slider',
					'title' => '右侧列表标题文字大小',
					'std' => array(
						'md' => 16,
						'sm' => 16,
						'xs' => 14,
					),
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'responsive' => true,
					'max' => 40,
				),
				'section_content_title_color' => array(
					'type' => 'color',
					'title' => '右侧列表标题文字颜色',
					'std' => '#333',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
				'section_content_title_mgB' => array(
					'type' => 'slider',
					'title' => '右侧列表标题下边距',
					'std' => array(
						'md' => 8,
						'sm' => 8,
						'xs' => 6,
					),
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'section_content_sTitle_fontsize' => array(
					'type' => 'slider',
					'title' => '右侧列表地址文字大小',
					'std' => array(
						'md' => 14,
						'sm' => 14,
						'xs' => 12,
					),
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
					'responsive' => true,
					'max' => 40,
				),
				'section_content_sTitle_color' => array(
					'type' => 'color',
					'title' => '右侧列表地址文字颜色',
					'std' => '#666',
					'depends' => array(
						array('content_style', '=', 'map_style'),
					),
				),
                'icon_beat' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启图标跳动'),
                    'std' => 0,
                ),
			),
		),
	)
);
