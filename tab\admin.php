<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'jw_tab',
        'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
        'category' => '选项卡',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
                    )
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'style' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DESC'),
                    'values' => array(
                        'modern' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_MODERN'),
                        'tabs' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DEFAULT'),
                        'pills' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_PILLS'),
                        'lines' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_LINES'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_CUSTOM'),
                        'type06' => '布局06',
                    ),
                    'std' => 'tabs'
                ),

                'select_tuwen' => array(
                    'type' => 'select',
                    'title' => JText::_('选项下面的信息'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DESC'),
                    'values' => array(
                        'no' => '不展示',
                        'yes' => '展示',
                    ),
                    'std' => 'no',
                    'depends' => array(array('style', '=', 'custom')),
                ),
                'tuwen_font_content' => array(
                    'type' => 'text',
                    'title' => JText::_('信息内容'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                ),
                'tuwen_font_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('信息宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'max' => 1000,
                    'min' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => 200,
                ),
                'tuwen_font_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('信息高度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'max' => 1000,
                    'min' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => 50,
                ),
                'tuwen_font_side' => array(
                    'type' => 'select',
                    'title' => JText::_('信息位置'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DESC'),
                    'values' => array(
                        'center' => '居中',
                        'left' => '居左',
                        'right' => '居右',
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                ),
                'tuwen_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('信息背景颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => '#ff0000',
                ),
                'tuwen_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('信息字体颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => '#ffffff',
                ),

                'tuwen_desc_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('信息内容背景颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => 'none',
                ),
                'tuwen_desc_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('信息内容边框颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => '#ccc',
                ),
                'tuwen_desc_content' => array(
                    'type' => 'editor',
                    'title' => JText::_('信息内容详细'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                ),
                'tuwen_desc_bjnum' => array(
                    'type' => 'slider',
                    'title' => JText::_('信息上边距'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => 10,
                ),
                'tuwen_desc_bjnum2' => array(
                    'type' => 'slider',
                    'title' => JText::_('信息下边距'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('select_tuwen', '=', 'yes'),
                    ),
                    'std' => 10,
                ),
                // Repeatable Item 插件
                'jw_tab_item' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEMS'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE_DESC'),
                            'std' => '点我'
                        ),
                        'subtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE_DESC'),
                            'std' => ' ',
                        ),
                        'image_or_icon' => array(
                            'type' => 'buttons',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE'),
                            'std' => 'icon',
                            'values' => array(
                                array(
                                    'label' => '图标',
                                    'value' => 'icon'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'image'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'icon' => array(
                            'type' => 'icon',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_DESC'),
                            'std' => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'icon'),
                            )
                        ),

                        'image' => array(
                            'type' => 'media',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE_DESC'),
                            'std' => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'image'),
                            )
                        ),
                        'active_bg_image' => array(
                            'type' => 'media',
                            'title' => '选中选项卡背景图标（请勿与选中统一背景图同时使用）',
                            'std' => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'image'),
                            )
                        ),
                        // 2021.8.24关联分类
                        'aboutcase' => array(
                            'type' => 'checkbox',
                            'title' => '关联分类',
                            'desc' => '如果标签内容选择的插件为产品列表或文章列表，请选择对应关联的分类名称，其他插件请勿开启',
                            'std' => 0,
                        ),
                        'resource' => array(
                            'type' => 'select',
                            'title' => '选择资源',
                            'values' => array(
                                'product' => '产品资源',
                                'article' => '文章资源',
                            ),
                            'std' => '',
                            'depends' => array(
                                array('aboutcase', '=', '1'),
                            ),
                        ),

                        'goods_catid' => array(
                            'type' => 'select',
                            'title' => JText::_('选择产品分类'),
                            'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                            'depends' => array(
                                array('aboutcase', '=', '1'),
                                array('resource', '=', 'product'),
                            ),
                        ),
                        'catid' => array(
                            'type' => 'category',
                            'title' => JText::_('选择文章分类'),
                            'depends' => array(
                                array('aboutcase', '=', '1'),
                                array('resource', '=', 'article'),
                            ),
                        ),
                        ///

                        'content' => array(
                            'type' => 'builder',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT_DESC'),
                            'std' => '我们的高级生活谴责特里·理查森和鱿鱼。3狼月办公室，非丘比特滑板dolor早午餐。食品车藜麦nesciunt laborum eiusmod。早午餐3狼月tempor, sunt aliqua把一只鸟在它鱿鱼单一起源咖啡nulla假设enda shoreditch等。'
                        ),
                        'closeClick' => array(
                            'type' => 'checkbox',
                            'title' => '关闭点击事件',
                            'std' => 0
                        )
                    ),
                ),
                // min--
                // 非自定义导航块位置
                'nav_all_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'img_cent' => array(
                    'type' => 'checkbox',
                    'title' => '图片居中',
                    'std' => 0,
                ),
                'wrap' => array(
                    'type' => 'checkbox',
                    'title' => '副标题是否换行',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_stuffing' => array(
                    'type' => 'checkbox',
                    'title' => '导航间填充',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_stuffing_c' => array(
                    'type' => 'color',
                    'title' => '填充物颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_stuffing_m' => array(
                    'type' => 'margin',
                    'title' => '填充物左右间距',
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                    'std' => '0 5 0 5',
                ),
                'nav_stuffing_w' => array(
                    'type' => 'slider',
                    'title' => '填充物宽度',
                    'desc' => '填充物宽度',
                    'max' => 100,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_stuffing_h' => array(
                    'type' => 'slider',
                    'title' => '填充物高度',
                    'desc' => '填充物高度',
                    'max' => 100,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),

                //新修改


                'nav_font_position' => array(
                    'type' => 'select',
                    'title' => '导航字体居中设置',
                    'std' => 'flex-start',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右',
                    ),
                    'depends' => array(
                        array('style', '=', 'custom')
                    ),
                ),
                'nav_text_align' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_TEXT_POSITION'),
                    'desc' => JText::_('这个选项是调整导航文本和图标的居中，靠左，靠右'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'left',
                ),
                'nav_all_bg_color' => array(
                    'type' => 'color',
                    'title' => '导航条背景颜色',
                    'std' => '#000',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                // 自定义导航块
                'nav_block_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '=', 'custom'), array('nav_position', '=', 'nav-top')),
                ),
                // 20211104添加使用导航条背景图片
                'opennavbg' => array(
                    'type' => 'checkbox',
                    'desc' => '开启导航条背景图',
                    'title' => JText::_('开启使用导航条背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'navbgImage' => array(
                    'type' => 'media',
                    'title' => JText::_('添加导航条背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211104/2b2cecb35a36294c2301732801fc7662.jpg',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('opennavbg', '=', '1'),

                    ),
                ),
                // end
                //Custom Tab Style
                'custom_tab_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_OPTIONS'),
                    'std' => 'navigation',
                    'values' => array(
                        array(
                            'label' => '导航',
                            'value' => 'navigation'
                        ),
                        array(
                            'label' => '导航图标或者图片',
                            'value' => 'icon_image'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom')
                    ),
                ),

                'tab_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_SEPERATOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'nav-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'nav-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'nav-top' => JText::_('上')
                    ),
                ),
                //在导航和内容之间的空间
                'nav_gutter' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER_DESC'),
                    'max' => 100,
                    'std' => array('md' => 15),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_open_num' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航前数字显示',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_open_arrow' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航后箭头图标',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //N导航宽度
                'nav_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'max' => 100,
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                // 箭头显示
                'tab_arrow' => array(
                    'type' => 'media',
                    'title' => '箭头图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220311/a04b3bf3970b3c66d921f16dc05ee040.png',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('nav_open_arrow', '=', '1'),
                    ),
                ),
                // 20211104添加使用背景图片
                'openbg' => array(
                    'type' => 'checkbox',
                    'desc' => '开启使用导航背景图样式',
                    'title' => JText::_('使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'bgImage' => array(
                    'type' => 'media',
                    'title' => JText::_('添加导航背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211104/4c74cc44c34854cd4cf1e6050f3a0025.png',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg', '=', '1'),
                    ),
                ),
                // end
                'nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BG_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg', '=', '0'),
                    ),
                ),
                'nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_STYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_sub_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航副标题字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_sub_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航副标题文字大小'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => 16),
                ),
                'nav_sub_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航副标题文字行高'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_num_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航数字字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('nav_open_num', '=', '1'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_num_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航数字文字大小'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('nav_open_num', '=', '1'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => 16),
                ),
                'nav_border' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_MARGIN'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '0px 0px 5px 0px',
                ),
                'nav_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_PADDING'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                // 20211104使用背景图添加
                'bgmargin' => array(
                    'type' => 'margin',
                    'title' => JText::_('导航外边距'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg', '=', '1'),

                    ),
                    'std' => '0px 0px 0px 0px',
                ),


                //Hover Nav Style
                // 20211104添加使用背景图片
                'openbg_hover' => array(
                    'type' => 'checkbox',
                    'desc' => '开启使用导航划过背景图样式',
                    'title' => JText::_('划过使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'bgImage_hover' => array(
                    'type' => 'media',
                    'title' => JText::_('添加划过背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg_hover', '=', '1'),
                    ),
                ),
                //end
                'hover_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停背景色1'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg_hover', '=', '0'),

                    ),
                ),
                'hover_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停文字颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_nav_sub_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停副标题字体颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),

                // 龙采官网下划线选项卡鼠标移入动画
                'open_line_animate' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启下划线动画'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                
                //Active Nav Style 选中导航样式
                // 箭头显示
                'active_tab_arrow' => array(
                    'type' => 'media',
                    'title' => '箭头图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220311/a24e384e7b1e1c9c3dfff4b6b65f5518.png',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('nav_open_arrow', '=', '1'),
                    ),
                ),
                // 20211104添加使用背景图片
                'openbg_active' => array(
                    'type' => 'checkbox',
                    'desc' => '开启使用导航选中背景图样式',
                    'title' => JText::_('选中使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'bgImage_active' => array(
                    'type' => 'media',
                    'title' => JText::_('添加选中背景图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg_active', '=', '1'),
                    ),
                ),
                //end
                'active_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('openbg_active', '=', '0'),

                    ),
                ),
                'active_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_COLOR'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'tab_color_repair' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'show_subtitle_always' => array(
                    'type' => 'checkbox',
                    'title' => '是否一直显示副标题',
                    'std' => '0',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'custom_border_settings' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启自定义边框',
                    'std' => '0',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'custom_border_styles' => array(
                    'type' => 'select',
                    'title' => '自定义边框样式',
                    'values' => array(
                        'style1' => '样式一',
                    ),
                    'std' => 'style1',
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('custom_border_settings', '=', '1'),
                    ),
                ),
                'custom_border_height' => array(
                    'type' => 'slider',
                    'title' => '边框高度（%）',
                    'std' => 70,
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('custom_border_settings', '=', '1'),
                    ),
                ),
                'custom_border_width' => array(
                    'type' => 'slider',
                    'title' => '边框宽度（px）',
                    'std' => 2,
                    'max' => 10,
                    'depends' => array(
                        array('style', '!=', 'modern'),
                        array('style', '!=', 'tabs'),
                        array('style', '!=', 'type06'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                        array('custom_border_settings', '=', '1'),
                    ),
                ),
                'active_nav_sub_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航选中副标题字体颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_show_boxshadow' => array(
                    'type' => 'checkbox',
                    'title' => '开启投影',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('active_show_boxshadow', '=', 1),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_HORIZONTAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('active_show_boxshadow', '=', 1),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_VERTICAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('active_show_boxshadow', '=', 1),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_shadow_blur' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_BLUR'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('active_show_boxshadow', '=', 1),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_shadow_spread' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_SPREAD'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('active_show_boxshadow', '=', 1),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_radius' => array(
                    'type' => 'margin',
                    'title' => JText::_('选中导航圆角'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Icon or Image style
                'image_or_icon_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE_STYLE'),
                    'std' => 'icon_style',
                    'values' => array(
                        array(
                            'label' => '图标样式',
                            'value' => 'icon_style'
                        ),
                        array(
                            'label' => '图片样式',
                            'value' => 'image_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                    ),
                ),
                //Icon Style
                'icon_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ICON_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'nav_icon_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'icon_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_SIZE'),
                    'max' => 400,
                    'std' => array('md' => 16),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_HOVER'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_ACTIVE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_MARGIN'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'std' => '',
                ),
                //Image Style
                'image_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_IMG_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'nav_image_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_ALIGNMENT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'image_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_HEIGHT'),
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_WIDTH'),
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'std' => '',
                ),
                //Content Style
                'content_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_backround' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'content_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'content_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-tab-custom-content > div{ font-family: {{ VALUE }}; }'
                    )
                ),
                'content_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONTSTYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER'),
                    'std' => 1,
                    'max' => 20,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTEN_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'show_boxshadow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW_DESC'),
                    'std' => 1,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_HORIZONTAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_VERTICAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_blur' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_BLUR'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_spread' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_SPREAD'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_MARGIN'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '',
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_PADDING'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                // 布局6
                'detail_page_id' => array(
                    'type'    => 'select',
                    'title'   => JText::_('跳转内页'),
                    'desc'   => JText::_('请设置为文章详情页或产品详情页'),
                    'std'     => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'target' => array(
                    'type'   => 'select',
                    'title'  => '打开方式',
                    'values' => array(
                        '_self' => '默认',
                        '_blank' => '新窗口',
                    ),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'section_id_type6' => array(
                    'type'   => 'text',
                    'title'  => '需要连接的章节区块id',
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'active_color_type6' => array(
                    'type'   => 'color',
                    'title'  => '选中颜色',
                    'std' => '#d9012a',
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'tab_width_type6' => array(
                    'type'   => 'slider',
                    'title'  => '选项卡所占宽度(%)',
                    'std' => 46,
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'tab_margin_top_type6' => array(
                    'type'   => 'slider',
                    'title'  => '选项卡上边距',
                    'std' => 166,
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'scroll_type6' => array(
                    'type'   => 'checkbox',
                    'title'  => '是否开启拖动',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                'item_margin_right_type6' => array(
                    'type'   => 'slider',
                    'title'  => '选项卡右边距',
                    'std' => 10,
                    'max' => 600,
                    'depends' => array(
                        array('style', '=', 'type06'),
                    )
                ),
                // 布局六结束
                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);
