<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonCarousel extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';

        $lbys = (isset($settings->lbys) && $settings->lbys) ? $settings->lbys : "lb1"; //布局
        $lb2_title = (isset($settings->lb2_title) && $settings->lb2_title) ? $settings->lb2_title : "WELCOME TO EVOLUTION DESIGN"; //标题
        $lb2_fontsize = (isset($settings->lb2_fontsize) && $settings->lb2_fontsize) ? $settings->lb2_fontsize : "38"; //字体大小
        $lb2_topheight = (isset($settings->lb2_topheight) && $settings->lb2_topheight) ? $settings->lb2_topheight : "0"; //头部距离
        $output = "";
        if ($lbys == "lb2") {
            $output .= '
                <div class="containera rotator-full">
                    <div class="rowa">
                        <div class="rotator" id="rotator2094259376">';
            if (isset($settings->jw_carousel_item2) && count((array)$settings->jw_carousel_item2)) {
                $nus = 0;
                foreach ($settings->jw_carousel_item2 as $key => $value) {
                    // 轮播来源
                    $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                    //选择轮播类型
                    $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                    $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;

                    if ($text_from == 0 && $key == 0) {
                        $nus = 1;
                    }
                    if ($nus == 1) {

                        if ($value->media_url_show) {
                            if ($value->tz_page_type == 'Internal_pages') {
                                $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '" style="display:inline-block;">'; //20210630 添加设置链接
                            } else {
                                $output .= '<a href="' . $value->media_url . '" style="display:inline-block;" target="' . $value->media_target . '">'; //20210630 添加设置链接
                            }
                        }
                        $output .= '<div class="rotator-image" data-image="' . $value->bg . '" data-caption-text="" data-label-text=""></div>';
                        if ($value->media_url_show) {
                            $output .= '</a>';
                        }
                    } else {
                        if ($hqtype == 'zu' && $key == 0 && $banner_typeid) {
                            $nus = 2;
                        }
                        if ($nus == 2) {

                            if ($key == 0) {
                                $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);
                                if ($bantype) {
                                    foreach ($bantype as $keyd => $valued) {

                                        if ($valued['image_intro']) {
                                            $valued['image_intro'] = $valued['image_intro'];
                                        } else {
                                            $valued['image_intro'] = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                        }

                                        if ($valued['link']) {
                                            $output .= '<a href="' . $valued['link'] . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                        }
                                        $output .= '<div class="rotator-image" data-image="' . $valued['image_intro'] . '" data-caption-text="" data-label-text=""></div>';

                                        if ($valued['link']) {
                                            $output .= '</a>';
                                        }
                                    }
                                } else {
                                    $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                                }
                            }
                        } else {

                            // 轮播内容
                            $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
                            // 2021.09.16
                            if ($value->text_from && $value->text_from == 1 && $value->text_id) {
                                $infos = JwPageFactoryBase::getBannerById($text_id);
                                if ($infos) {

                                    if ($infos->image_intro) {
                                        $value->bg = $infos->image_intro;
                                    } else {
                                        $value->bg = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                    }
                                    $value->title = $infos->banner_title;
                                    $value->content = $infos->introtext;
                                }
                            }

                            if ($value->tz_page_type == 'Internal_pages') {
                                $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id2) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                $button_url = $thisUrl;
                            } else {
                                $button_url = (isset($value->button_url) && $value->button_url) ? $value->button_url : 'javascript:;';
                            }


                            if ($value->media_url_show) {
                                if ($value->tz_page_type == 'Internal_pages') {
                                    $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                    $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                } else {
                                    $output .= '<a href="' . $value->media_url . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                }
                            }
                            $output .= '<div class="rotator-image" data-image="' . $value->bg . '" data-caption-text="" data-label-text=""></div>';

                            if ($value->media_url_show) {
                                $output .= '</a>';
                            }
                        }
                    }
                }

                foreach ($settings->jw_carousel_item2 as $key => $value) {
                    // 轮播来源
                    $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                    //选择轮播类型
                    $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                    $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;

                    if ($text_from == 1) {
                        if ($hqtype == 'zu' && $banner_typeid) {

                            if ($key == 0) {
                                $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);
                                if ($bantype) {
                                    foreach ($bantype as $keyd => $valued) {

                                        if ($valued['image_intro']) {
                                            $valued['image_intro'] = $valued['image_intro'];
                                        } else {
                                            $valued['image_intro'] = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                        }

                                        if ($keyd == 0) {
                                            $output .= '<div class="rotator-screen" style="background-image: url(' . $valued['image_intro'] . ');">
                                                                <div class="cover">
                                                                    <p>' . $lb2_title . '</p>
                                                                </div>
                                                            </div>';
                                        }
                                    }
                                } else {
                                    $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                                }
                            }
                        } else {

                            // 轮播内容
                            $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
                            // 2021.09.16
                            if ($value->text_from && $value->text_from == 1 && $value->text_id) {
                                $infos = JwPageFactoryBase::getBannerById($text_id);
                                if ($infos) {

                                    if ($infos->image_intro) {
                                        $value->bg = $infos->image_intro;
                                    } else {
                                        $value->bg = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                    }
                                    $value->title = $infos->banner_title;
                                    $value->content = $infos->introtext;
                                }
                            }
                            if ($key == 0) {
                                if ($keyd == 0) {
                                    $output .= '<div class="rotator-screen" style="background-image: url(' . $value->bg . ');">
                                                        <div class="cover">
                                                            <p>' . $lb2_title . '</p>
                                                        </div>
                                                    </div>';
                                }
                            }
                        }
                    } else {
                        if ($key == 0) {
                            $output .= '<div class="rotator-screen" style="background-image: url(' . $value->bg . ');">
                                                <div class="cover">
                                                    <p>' . $lb2_title . '</p>
                                                </div>
                                            </div>';
                        }
                    }
                }
            }

            $output .= '
                            
                            <div class="rotatorPrev rotatorNav">PREV</div>
                            <div class="rotatorNext rotatorNav">NEXT</div>
                            <div class="rotatorBullets bullets" id="rotator2094259376-bullets"></div>
                            <div class="rotator-down"></div>
                        </div>
                    </div>
                </div>

                <script>

                    jQuery(document).ready(function($){
                        
                        var gao=$(window).height(); 
                        var last=gao-165;
                        $("' . $addon_id . ' .rotator-screen").css({"height":gao+"px"});
                        $("' . $addon_id . ' .rotator-full").css({"height":gao+"px"});

                        
                        $("' . $addon_id . ' .rotator-screen").delay(4000).fadeOut(1000);
                        $("' . $addon_id . ' .rotator-screen h1").delay(800).fadeIn(800);
                        $("' . $addon_id . ' .rotator-screen p").delay(1600).fadeIn(800);
                        $("' . $addon_id . ' .rotator .rotatorBullets").delay(4000).fadeIn(1000);
                        if (!$("' . $addon_id . ' .rotator-full").hasClass("short")) {
                            $("' . $addon_id . ' .rotator-full").delay(4000).animate({height:last}, 3000, "swing").addClass("short");
                        }
            
                        setTimeout(fnRotateNow, 3000);
                        function fnRotateNow() {
                            var bannerRotator = new kingslandLinassi.Rotator();
                            bannerRotator.initialise(
                                {rotatorId: "rotator2094259376",
                                debugId: "full",
                                bulletId: "rotator2094259376-bullets"}
                            );
                        };
            
                    });
                </script>
            ';
        } elseif ($lbys == "lb3") {
            $lb3_zsq = (isset($settings->lb3_zsq) && $settings->lb3_zsq) ? $settings->lb3_zsq : "ysang1"; //指示器样式
            $qx_height = (isset($settings->qx_height) && $settings->qx_height) ? $settings->qx_height : "0"; //高度自适应

            $output = '
                <div class="ind1-a1">
                    <div class="swiper-container ind111 swiper-container-fade swiper-container-horizontal">
                        <div class="swiper-wrapper" style="transition-duration: 0ms;">';

            $jw_carousel_item3 = (isset($settings->jw_carousel_item3) && $settings->jw_carousel_item3) ? $settings->jw_carousel_item3 : array(
                (Object)array(
                    'bg' => 'https://oss.lcweb01.cn/joomla/20220711/56fa646cff29309488d6602246694d27.jpg',
                    'phone_bg' => 'https://oss.lcweb01.cn/joomla/20220711/0b472b1eb9db57d7557bc97b093b0b70.jpg',
                    'media_url_show' => '0',
                    'text_from' => '0',
                )
            );

            if (isset($jw_carousel_item3) && count((array)$jw_carousel_item3)) {
                foreach ($jw_carousel_item3 as $key => $value) {

                    // 2021.09.16
                    // 轮播来源
                    $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                    //选择轮播类型
                    $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                    $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;

                    if ($hqtype == 'zu' && $banner_typeid) {

                        if ($key == 0) {
                            $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);

                            if ($bantype) {
                                foreach ($bantype as $keyd => $valued) {

                                    if ($valued['image_intro']) {
                                        $valued['bg'] = $valued['image_intro'];
                                    } else {
                                        $valued['bg'] = 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg';
                                    }
                                    if ($valued['qrcode']) {
                                        $valued['phone_bg'] = $valued['qrcode'];
                                    } else {
                                        $valued['phone_bg'] = 'https://oss.lcweb01.cn/joomla/20220711/0b472b1eb9db57d7557bc97b093b0b70.jpg';
                                    }

                                    $staring = $key * (-1480);

                                    $output .= '
                                                        <div class="swiper-slide " data-swiper-slide-index="4" style="width: 1480px; transition-duration: 0ms; opacity: 1; transform: translate3d(' . $staring . 'px, 0px, 0px);">';

                                    if ($valued['link']) {
                                        $output .= '<a href="' . $valued['link'] . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                    }

                                    $output .= '<div class="ind1-a2 i100">
                                                    <img src="' . $valued['bg'] . '">
                                                </div>
                                                <div class="ind1-a3 i100">
                                                    <img src="' . $valued['phone_bg'] . '">
                                                </div>';

                                    if ($valued['link']) {
                                        $output .= '</a>';
                                    }
                                    $output .= '</div>';
                                }
                            } else {
                                $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                            }
                        }
                    } else {

                        // 轮播内容
                        $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
                        // 2021.09.16
                        if ($value->text_from && $value->text_from == 1 && $value->text_id) {
                            $infos = JwPageFactoryBase::getBannerById($text_id);
                            if ($infos) {

                                if ($infos->image_intro) {
                                    $value->bg = $infos->image_intro;
                                } else {
                                    $value->bg = 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg';
                                }
                                if ($infos->qrcode) {
                                    $value->phone_bg = $infos->qrcode;
                                } else {
                                    $value->phone_bg = 'https://oss.lcweb01.cn/joomla/20220711/0b472b1eb9db57d7557bc97b093b0b70.jpg';
                                }
                            }
                        }

                        if ($value->tz_page_type == 'Internal_pages') {
                            $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id2) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        } else {
                            $thisUrl = (isset($value->button_url) && $value->button_url) ? $value->button_url : 'javascript:;';
                        }

                        $staring = $key * (-1480);

                        $output .= '
                                            <div class="swiper-slide " data-swiper-slide-index="4" style="width: 1480px; transition-duration: 0ms; opacity: 1; transform: translate3d(' . $staring . 'px, 0px, 0px);">';
                        if ($value->media_url_show) {
                            if ($value->tz_page_type == 'Internal_pages') {
                                $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                            } else {
                                $output .= '<a href="' . $value->media_url . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                            }
                        }
                        $output .= '<div class="ind1-a2 i100">
                                                                <img src="' . $value->bg . '">
                                                            </div>
                                                            <div class="ind1-a3 i100">
                                                                <img src="' . $value->phone_bg . '">
                                                            </div>
                                                        
                                        ';

                        if ($value->media_url_show) {
                            $output .= '</a>';
                        }
                        $output .= '</div>';
                    }
                }
            }

            $output .= '</div>
                    </div>
                    <div class="swiper-pagination ind112 swiper-pagination-clickable swiper-pagination-bullets ';
            if ($lb3_zsq == "ysang2") {
                $output .= 'clust';
            }
            $output .= '">';
            if (isset($jw_carousel_item3) && count((array)$jw_carousel_item3)) {
                foreach ($jw_carousel_item3 as $key => $value) {
                    $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                    //选择轮播类型
                    $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                    $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;

                    if ($hqtype == 'zu' && $banner_typeid) {
                        $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);

                        if ($bantype) {
                            if ($key == 0) {
                                foreach ($bantype as $keyd => $valued) {
                                    $output .= '<span class="swiper-pagination-bullet"></span>';
                                }
                            }
                        }
                    } else {
                        $output .= '<span class="swiper-pagination-bullet"></span>';
                    }
                }
            }
            $output .= '</div>
                </div>
                
                <script type="text/javascript">
                    var mySwiper = new Swiper ("' . $addon_id . ' .ind111", {   
                            loop: true,';
            if ($lb3_zsq == 'ysang2') {
                $output .= 'pagination: {
                                  el: "' . $addon_id . ' .swiper-pagination",
                                  clickable: true,
                                  renderBullet: function (index, className) {
                                    return "<span class=" + className + ">0" + (index + 1) + "</span>";
                                  },
                                },';
            } else {
                $output .= 'pagination: {
                                    el: "' . $addon_id . ' .ind112",
                                    clickable :true,
                                },';
            }

            $output .= 'autoplay:{
                                delay: 10000, 
                                disableOnInteraction: false, 
                            },
                            speed: 800,
                            effect : "fade",
                            fadeEffect: {
                                crossFade: true,
                            },
                            on:{
                                init: function(){
                                },
                            },
                    });
                </script>
            ';
        } elseif ($lbys == "lb4") {
            $autoplay = isset($settings->autoplay) ? $settings->autoplay : 1;
            if ($autoplay == 1) {
                $autoplay = 'true';
            } else {
                $autoplay = 'false';
            }
            // 切换间隔
            $interval = (isset($settings->interval) && $settings->interval) ? ((int)$settings->interval * 1000) : 3000;
            // 切换速度
            $speed = (isset($settings->speed) && $settings->speed) ? $settings->speed : 1100;
            
            $jw_carousel_item04 = (isset($settings->jw_carousel_item04) && $settings->jw_carousel_item04) ? $settings->jw_carousel_item04 : array(
                (Object)array(
                    'text_from' => '0',
                    'bg' => 'https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg',
                    'media_url_show' => '0',
                )
            );
            
            $output .= '
            <div class="os-slider">
                <ul class="os-slider-main">';
                    foreach ($jw_carousel_item04 as $key => $value) {
                        // print_r('-----------<br/>');
                        // print_r($value);
                        // print_r('<br/>');
                        // 轮播来源  是否后台获取
                        $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                        // 选择轮播类型
                        $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                        // 轮播组 分类id
                        $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;
                        // 单个轮播图id
                        $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;

                        if ($value->text_from && $value->text_from == 1) {
                            if ($hqtype == 'zu' && $banner_typeid) {
                                // 获取轮播组列表
                                $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);
                                if ($bantype) {
                                    foreach ($bantype as $keyd => $valued) {
                                        if ($valued['image_intro']) {
                                            $valued['bg'] = $valued['image_intro'];
                                        } else {
                                            $valued['bg'] = 'https://oss.lcweb01.cn/joomla/20230508/0e8c3b59abaafa61c3810bb3f981b2f3.jpg';
                                        }
                                        $output .= '<li>';
                                        if ($valued['link'] && $value->media_url_show) {
                                            $output .= '<a href="' . $valued['link'] . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                        }
                                        $output .= '<img src="' . $valued['bg'] . '">';
                                        if ($valued['link'] && $value->media_url_show) {
                                            $output .= '</a>';
                                        }
                                        $output .= '</li>';
                                    }
                                } else {
                                    $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                                }
                            }
                            else if($value->text_id) {
                                $infos = JwPageFactoryBase::getBannerById($text_id);
                                if ($infos) {
                                    if ($infos->image_intro) {
                                        $value->bg = $infos->image_intro;
                                    }
                                }
                                $output .= $this->banner_item($value);
                            }
                        } else {
                            $output .= $this->banner_item($value);
                        }
                    }
                    $output .= '
                </ul>
            </div>
            <script type="text/javascript">
                var slider' . $id . ' = new osSlider({ //开始创建效果
                    pNode: "' . $addon_id . ' .os-slider", //容器的选择器 必填
                    cNode: ".os-slider-main li", //轮播体的选择器 必填
                    speed: ' . $speed . ', //速度 默认3000 可不填写
                    interval: ' . $interval . ',  //切换间隔 默认1100 可不填写
                    autoPlay: ' . $autoplay . ' //是否自动播放 默认true 可不填写
                });
            </script>
            ';
        } elseif($lbys == "lb5") {
            $autoplay = isset($settings->autoplay) ? $settings->autoplay : 1;
            // 切换间隔
            $interval = (isset($settings->interval) && $settings->interval) ? ((int)$settings->interval * 1000) : 3000;
            // 切换速度
            $speed = (isset($settings->speed) && $settings->speed) ? $settings->speed : 1100;
            // 切换动画
            $animate_dh = (isset($settings->animate_dh) && $settings->animate_dh) ? $settings->animate_dh : 'fade';
            
            
            $jw_carousel_item05 = (isset($settings->jw_carousel_item05) && $settings->jw_carousel_item05) ? $settings->jw_carousel_item05 : array(
                (Object)array(
                    'text_from' => '0',
                    'bg' => 'https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg',
                    'title' => '这里是标题',
                    'media_url_show' => '0',
                )
            );
            $output .= '
                <div class="swiper-container">
                    <div class="swiper-wrapper">';
                        foreach ($jw_carousel_item05 as $key => $value) {
                            // print_r('-----------<br/>');
                            // print_r($value);
                            // print_r('<br/>');
                            // 轮播来源  是否后台获取
                            $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                            // 选择轮播类型
                            $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                            // 轮播组 分类id
                            $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;
                            // 单个轮播图id
                            $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
    
                            if ($value->text_from && $value->text_from == 1) {
                                if ($hqtype == 'zu' && $banner_typeid) {
                                    // 获取轮播组列表
                                    $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);
                                    if ($bantype) {
                                        foreach ($bantype as $keyd => $valued) {
                                            if ($valued['image_intro']) {
                                                $valued['bg'] = $valued['image_intro'];
                                            } else {
                                                $valued['bg'] = 'https://oss.lcweb01.cn/joomla/20230508/0e8c3b59abaafa61c3810bb3f981b2f3.jpg';
                                            }
                                            $output .= '<div class="swiper-slide">';
                                            if ($valued['link'] && $value->media_url_show) {
                                                $output .= '<a href="' . $valued['link'] . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                                            }
                                            $output .= '
                                            <div class="img-box">
                                                <img src="' . $valued['bg'] . '">
                                            </div>
                                            <p class="title">' . $valued['title'] . '</p>
                                            ';
                                            if ($valued['link'] && $value->media_url_show) {
                                                $output .= '</a>';
                                            }
                                            $output .= '</div>';
                                        }
                                    } else {
                                        $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                                    }
                                }
                                else if($value->text_id) {
                                    $infos = JwPageFactoryBase::getBannerById($text_id);
                                    if ($infos) {
                                        if ($infos->image_intro) {
                                            $value->bg = $infos->image_intro;
                                        }
                                    }
                                    $output .= $this->banner_item($value);
                                }
                            } else {
                                $output .= $this->banner_item($value);
                            }
                        }
                        $output .= '
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            ';
            $output .= '
            <script type="text/javascript">
                var mySwiper' . $id . ' = new Swiper ("' . $addon_id . ' .swiper-container", {
                    loop: true,
                    pagination: {
                        el: "' . $addon_id . ' .swiper-pagination",
                        clickable :true,
                    },';
                    if ($autoplay == 1) {
                        $output .= '
                        autoplay: {
                            delay: ' . $interval . ', 
                            disableOnInteraction: false, 
                        },
                        ';
                    }
                    $output .= 'speed: ' . $speed . ',
                    effect : "' . $animate_dh . '",
                    fadeEffect: {
                        crossFade: true,
                    },
                    on: {
                        init: function(){
                        },
                    },
                });
            </script>
            ';
        }
        else {

            //Addons option
            $autoplay = (isset($settings->autoplay) && $settings->autoplay) ? 1 : 0;
            $controllers = (isset($settings->controllers) && $settings->controllers) ? $settings->controllers : 0;

            //气泡效果开启
            $show_bubble = (isset($settings->show_bubble) && $settings->show_bubble) ? $settings->show_bubble : 0;

            // $tz_page_type = (isset($settings->tz_page_type) && $settings->tz_page_type) ? $settings->tz_page_type : 'external_links';
            // $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;

            $arrows = (isset($settings->arrows) && $settings->arrows) ? $settings->arrows : 0;
            $alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : 0;
            $interval = (isset($settings->interval) && $settings->interval) ? ((int)$settings->interval * 1000) : 5000;
            $carousel_autoplay = ($autoplay) ? ' data-jwpf-ride="jwpf-carousel"' : '';
            if ($autoplay == 0) {
                $interval = 'false';
            }
            $output = '<div id="jwpf-carousel-' . $this->addon->id . '" data-interval="' . $interval . '" class="jwpf-carousel jwpf-slide' . $class . '"' . $carousel_autoplay . '>';

            if ($controllers) {
                $output .= '<ol class="jwpf-carousel-indicators jwpf-carousel-indicators_preview">';
                foreach ($settings->jw_carousel_item as $key1 => $value) {
                    $output .= '<li data-jwpf-target="#jwpf-carousel-' . $this->addon->id . '" ' . (($key1 == 0) ? ' class="active"' : '') . '  data-jwpf-slide-to="' . $key1 . '"></li>' . "\n";
                }
                $output .= '</ol>';
            }

            if ($show_bubble) {
                $bubble_color_set = (isset($settings->bubble_color_set) && $settings->bubble_color_set) ? $settings->bubble_color_set : 'custom';
                $bubble_color = (isset($settings->bubble_color) && $settings->bubble_color) ? $settings->bubble_color : 'rgba(255,255,255, .4)';
                if ($bubble_color_set == 'random') {
                    $bubble_color = 'random';
                }
                $output .= '<div class="canvas-area" id="' . $id . '" data-id="' . $id . '" data-bubble-color="' . $bubble_color . '"></div>';
            }

            $output .= '<div class="jwpf-carousel-inner ' . $alignment . '">';

            if (isset($settings->jw_carousel_item) && count((array)$settings->jw_carousel_item)) {
                foreach ($settings->jw_carousel_item as $key => $value) {

                    // 2021.09.16
                    // 轮播来源
                    $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                    //选择轮播类型
                    $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                    $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;


                    if ($hqtype == 'zu' && $banner_typeid && $text_from == 1) {

                        if ($key == 0) {
                            $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id, $banner_typeid);
                            if ($bantype) {
                                foreach ($bantype as $keyd => $valued) {

                                    if ($valued['image_intro']) {
                                        $valued['image_intro'] = $valued['image_intro'];
                                    } else {
                                        $valued['image_intro'] = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                    }


                                    $output .= '<div class="jwpf-item jwpf-item-' . $this->addon->id . $keyd . ' ' . ((isset($valued['image_intro']) && $valued['image_intro']) ? ' jwpf-item-has-bg' : '') . (($keyd == 0) ? ' active' : '') . '">';

                                    $alt_text = $valued['banner_title'];

                                    // 2021.10.12 轮播视频
                                    if ($valued['video_link']) {
                                        $output .= '<video loop autoplay width="100%" muted >
                                            <source src="' . $valued['video_link'] . '" type="video/mp4">
                                        </video>';
                                    } else {
                                        $output .= '<img src="' . $valued['image_intro'] . '" alt="' . $alt_text . '">';
                                    }

                                    if ($valued['link']) {

                                        $output .= '<a href="' . $valued['link'] . '" target="' . $value->media_target . '">'; //20210630 添加设置链接

                                    }
                                    $output .= '<div class="jwpf-carousel-item-inner">';
                                    $output .= '<div class="jwpf-carousel-caption">';
                                    $output .= '<div class="jwpf-carousel-text">';

                                    if ($valued['banner_title'] || $valued['introtext']) {
                                        $output .= (isset($valued['banner_title']) && $valued['banner_title']) ? '<h2>' . $valued['banner_title'] . '</h2>' : '';
                                        $output .= (isset($valued['introtext']) && $valued['introtext']) ? '<div class="jwpf-carousel-content">' . $valued['introtext'] . '</div>' : '';
                                        if (isset($value->button_text) && $value->button_text) {
                                            $button_class = (isset($value->button_type) && $value->button_type) ? ' jwpf-btn-' . $value->button_type : ' jwpf-btn-default';
                                            $button_class .= (isset($value->button_size) && $value->button_size) ? ' jwpf-btn-' . $value->button_size : '';
                                            $button_class .= (isset($value->button_shape) && $value->button_shape) ? ' jwpf-btn-' . $value->button_shape : ' jwpf-btn-rounded';
                                            $button_class .= (isset($value->button_appearance) && $value->button_appearance) ? ' jwpf-btn-' . $value->button_appearance : '';
                                            $button_class .= (isset($value->button_block) && $value->button_block) ? ' ' . $value->button_block : '';
                                            $button_icon = (isset($value->button_icon) && $value->button_icon) ? $value->button_icon : '';
                                            $button_icon_position = (isset($value->button_icon_position) && $value->button_icon_position) ? $value->button_icon_position : 'left';
                                            $button_target = (isset($value->button_target) && $value->button_target) ? $value->button_target : '_self';

                                            $icon_arr = array_filter(explode(' ', $button_icon));
                                            if (count($icon_arr) === 1) {
                                                $button_icon = 'fa ' . $button_icon;
                                            }

                                            if ($button_icon_position == 'left') {
                                                $value->button_text = ($button_icon) ? '<i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i> ' . $value->button_text : $value->button_text;
                                            } else {
                                                $value->button_text = ($button_icon) ? $value->button_text . ' <i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i>' : $value->button_text;
                                            }



                                            $output .= '<a href="' . $valued['link'] . '" target="' . $button_target . '" ' . ($button_target === '_blank' ? 'rel="noopener noreferrer"' : '') . ' id="btn-' . ($this->addon->id + $keyd) . '" class="jwpf-btn' . $button_class . '">' . $value->button_text . '</a>';
                                        }
                                    }

                                    $output .= '</div>';
                                    $output .= '</div>';
                                    $output .= '</div>';

                                    if ($valued['link']) {
                                        $output .= '</a>';
                                    }
                                    $output .= '</div>';
                                }
                            } else {
                                $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                            }
                        }
                    } else {

                        // 轮播内容
                        $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
                        // 2021.09.16
                        if ($value->text_from && $value->text_from == 1 && $value->text_id) {
                            $infos = JwPageFactoryBase::getBannerById($text_id);
                            if ($infos) {

                                if ($infos->image_intro) {
                                    $value->bg = $infos->image_intro;
                                } else {
                                    $value->bg = 'https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                }
                                $value->title = $infos->banner_title;
                                $value->content = $infos->introtext;
                            }
                        }

                        if ($value->tz_page_type2 == 'Internal_pages') {
                            $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id2) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                            $button_url = $thisUrl;
                        } else {
                            $button_url = (isset($value->button_url) && $value->button_url) ? $value->button_url : 'javascript:;';
                        }

                        $output .= '<div class="jwpf-item jwpf-item-' . $this->addon->id . $key . ' ' . ((isset($value->bg) && $value->bg) ? ' jwpf-item-has-bg' : '') . (($key == 0) ? ' active' : '') . '">';

                        $alt_text = isset($value->title) ? $value->title : '';

                        // 2021.10.12 轮播视频
                        if ($value->media_video && $value->media_video == 1) {
                            $output .= (isset($value->slider_video) && $value->slider_video) ? '<video loop autoplay width="100%" muted >
                                <source src="' . $value->slider_video . '" type="video/mp4">
                            </video>' : '';
                        } else {
                            $output .= (isset($value->bg) && $value->bg) ? '<img src="' . $value->bg . '" alt="' . $alt_text . '">' : '';
                        }

                        if ($value->media_url_show) {
                            if ($value->tz_page_type == 'Internal_pages') {
                                $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                                $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                            } else {
                                $output .= '<a href="' . $value->media_url . '" target="' . $value->media_target . '">'; //20210630 添加设置链接
                            }
                        }
                        $output .= '<div class="jwpf-carousel-item-inner">';
                        $output .= '<div class="jwpf-carousel-caption">';
                        $output .= '<div class="jwpf-carousel-text">';

                        if ((isset($value->title) && $value->title) || (isset($value->content) && $value->content)) {
                            $output .= (isset($value->title) && $value->title) ? '<h2>' . $value->title . '</h2>' : '';
                            $output .= (isset($value->content) && $value->content) ? '<div class="jwpf-carousel-content">' . $value->content . '</div>' : '';
                            if (isset($value->button_text) && $value->button_text) {
                                $button_class = (isset($value->button_type) && $value->button_type) ? ' jwpf-btn-' . $value->button_type : ' jwpf-btn-default';
                                $button_class .= (isset($value->button_size) && $value->button_size) ? ' jwpf-btn-' . $value->button_size : '';
                                $button_class .= (isset($value->button_shape) && $value->button_shape) ? ' jwpf-btn-' . $value->button_shape : ' jwpf-btn-rounded';
                                $button_class .= (isset($value->button_appearance) && $value->button_appearance) ? ' jwpf-btn-' . $value->button_appearance : '';
                                $button_class .= (isset($value->button_block) && $value->button_block) ? ' ' . $value->button_block : '';
                                $button_icon = (isset($value->button_icon) && $value->button_icon) ? $value->button_icon : '';
                                $button_icon_position = (isset($value->button_icon_position) && $value->button_icon_position) ? $value->button_icon_position : 'left';
                                $button_target = (isset($value->button_target) && $value->button_target) ? $value->button_target : '_self';

                                $icon_arr = array_filter(explode(' ', $button_icon));
                                if (count($icon_arr) === 1) {
                                    $button_icon = 'fa ' . $button_icon;
                                }

                                if ($button_icon_position == 'left') {
                                    $value->button_text = ($button_icon) ? '<i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i> ' . $value->button_text : $value->button_text;
                                } else {
                                    $value->button_text = ($button_icon) ? $value->button_text . ' <i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i>' : $value->button_text;
                                }



                                $output .= '<a href="' . $button_url . '" target="' . $button_target . '" ' . ($button_target === '_blank' ? 'rel="noopener noreferrer"' : '') . ' id="btn-' . ($this->addon->id + $key) . '" class="jwpf-btn' . $button_class . '">' . $value->button_text . '</a>';
                            }
                        }

                        $output .= '</div>';
                        $output .= '</div>';
                        $output .= '</div>';


                        if ($value->media_url_show) {
                            $output .= '</a>';
                        }
                        $output .= '</div>';
                    }
                }
            }


            $output .= '</div>';

            if ($arrows) {
                $arrows_site = (isset($settings->arrows_site) && $settings->arrows_site) ? $settings->arrows_site : 'center';
                if ($arrows_site == 'left') {
                    $output .= "
                    <style>
                    $addon_id .lefts {
                        position: absolute;
                        top: 74%;
                        left: 5%;
                        bottom: 0;
                        width: 40px;
                        opacity: .8;
                        filter: alpha(opacity=80);
                        font-size: 24px;
                        color: #fff;
                        text-align: center;
                        text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                    }
                    $addon_id .rights {
                        position: absolute;
                        top: 74%;
                        left: 10%;
                        bottom: 0;
                        width: 40px;
                        opacity: .8;
                        filter: alpha(opacity=80);
                        font-size: 24px;
                        color: #fff;
                        text-align: center;
                        text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                    }
                    </style>
                    ";
                }
                if ($arrows_site == 'right') {
                    $output .= "
                    <style>
                    $addon_id .lefts {
                        position: absolute;
                        top: 74%;
                        left: 85%;
                        bottom: 0;
                        width: 40px;
                        opacity: .8;
                        filter: alpha(opacity=80);
                        font-size: 24px;
                        color: #fff;
                        text-align: center;
                        text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                    }
                    $addon_id .rights {
                        position: absolute;
                        top: 74%;
                        left: 90%;
                        bottom: 0;
                        width: 40px;
                        opacity: .8;
                        filter: alpha(opacity=80);
                        font-size: 24px;
                        color: #fff;
                        text-align: center;
                        text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                    }
                    </style>
                    ";
                }
                $output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow left lefts jwpf-carousel-control" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-chevron-left" aria-hidden="true"></i></a>';
                $output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow right rights jwpf-carousel-control" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-chevron-right" aria-hidden="true"></i></a>';
            }

            $output .= '</div>';
        }

        return $output;
    }

    
    public function banner_item($value) 
    {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $lbys = (isset($settings->lbys) && $settings->lbys) ? $settings->lbys : "lb1"; //布局

        $output = '';
        if($lbys == 'lb4') {
            $output .= '<li>';
            if ($value->media_url_show) {
                // 是否启用链接跳转
                if ($value->tz_page_type == 'Internal_pages') {
                    // 跳转 站内链接
                    $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                } else {
                    $thisUrl = $value->media_url;
                }
                if ($thisUrl) {
                    $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '">';
                }
            }
            $output .= '<img src="' . $value->bg . '">';

            if ($value->media_url_show && $thisUrl) {
                $output .= '</a>';
            }
            $output .= '</li>';
        }
        if($lbys == 'lb5') {
            $output .= '<div class="swiper-slide">';
            if ($value->media_url_show) {
                // 是否启用链接跳转
                if ($value->tz_page_type == 'Internal_pages') {
                    // 跳转 站内链接
                    $thisUrl = "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                } else {
                    $thisUrl = $value->media_url;
                }
                if ($thisUrl) {
                    $output .= '<a href="' . $thisUrl . '" target="' . $value->media_target . '">';
                }
            }
            $output .= '
            <div class="img-box">
                <img src="' . $value->bg . '">
            </div>
            <p class="title">' . $value->title . '</p>
            ';

            if ($value->media_url_show && $thisUrl) {
                $output .= '</a>';
            }
            $output .= '</div>';
        }
        
        return $output;
    }

    public function css()
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css = '';

        $lbys = (isset($settings->lbys) && $settings->lbys) ? $settings->lbys : "lb1"; //布局
        $qx_height = (isset($settings->qx_height) && $settings->qx_height) ? $settings->qx_height : "0"; //高度自适应
        $lb2_fontsize = (isset($settings->lb2_fontsize) && $settings->lb2_fontsize) ? $settings->lb2_fontsize : "38"; //
        $lb2_topheight = isset($settings->lb2_topheight)  ? $settings->lb2_topheight : "0"; //
        $lb2_image_fit = isset($settings->lb2_image_fit)  ? $settings->lb2_image_fit : "cover"; //
        if ($lb2_image_fit == 'fill') {
            $lb2_image_fit = "100% 100%";
        }
        if ($lbys == "lb2") {
            $css .= ''
                . $addon_id . ' body {margin: 0;}
                ' . $addon_id . ' html, body {width: 100%;height: 100%;}
                ' . $addon_id . ' p {
                    font-size: 13px;
                    line-height: 19px;
                    letter-spacing: 0px;
                }
                
                ' . $addon_id . ' img {
                    width: auto;
                    height: auto;
                }

                ' . $addon_id . ' .rotator-full {
                    height: 100%;
                    font-size: 0px;
                    z-index: 5;
                }
                
                ' . $addon_id . ' .rotator-standard {
                    padding:30px 0;background:#ebebeb;
                    width: 100%;
                    font-size: 0;
                }
                ' . $addon_id . ' .rotator-standard .rotator-image{
                    background-size:100% 100%;
                }
                
                ' . $addon_id . ' .rotator-full .rowa {
                    max-width: 100%;
                    width: 100%;
                    height: 100%;
                }
                
                ' . $addon_id . ' .rotator-full-short .rowa {
                    max-width: 100%;
                    height: 100%;
                    width: 100%;
                }
                
                ' . $addon_id . ' .rotator-standard .rowa {
                    height: 100%;
                    width: 100%;
                }
                
                ' . $addon_id . ' .rotator {
                    height: 100%;
                    display: inherit;
                    position: relative;
                    z-index: 1;
                }
                
                ' . $addon_id . ' .rotator .rotatorText {
                    display: none;
                }
                
                ' . $addon_id . ' .rotator .rotatorBullets {
                    position: absolute;
                    left: 0px;
                    right: 0px;
                    bottom: 55px;
                    z-index: 100;
                    display: none;
                    height: 9px;
                    margin: 0px auto;
                    text-align: center;
                }
                
                ' . $addon_id . ' .rotator-standard .rotator .rotatorBullets {
                    bottom: 30px;
                }
                
                ' . $addon_id . ' .rotator .rotatorBullets ul {
                    padding: 0px;
                    margin: 0px;
                }
                
                ' . $addon_id . ' .rotator .rotatorBullets ul li {
                    width: 14px;
                    height: 14px;
                    background: url(https://oss.lcweb01.cn/joomla/20220601/cb7a60739b8ac3770f2e166b64e6cf19.png) no-repeat;
                    display: inline-block;
                    margin-right: 8px;
                    padding: 0;
                    cursor: pointer;
                    opacity: 1;
                }
                ' . $addon_id . ' .rotator .rotatorBullets ul li:first-child {
                    margin-left: 10px;
                }
                ' . $addon_id . ' .rotator .rotatorBullets ul li.selected {
                    background: url(https://oss.lcweb01.cn/joomla/20220601/236879530f085c9faba2c2f8270e853a.png);
                    opacity: 1;
                }
                
                ' . $addon_id . ' .rotatorNav {
                    position: absolute;
                    z-index: 90;
                    color: #FFF;
                    cursor: pointer;
                    display: none;
                }
                
                ' . $addon_id . ' .rotatorPrev {
                    top: 45%;
                    left: 40px;
                    text-indent: -9999px;
                    width: 23px;
                    height: 33px;
                }
                
                ' . $addon_id . ' .rotatorNext {
                    top: 45%;
                    right: 40px;
                    text-indent: -9999px;
                    width: 23px;
                    height: 33px;
                }
                
                ' . $addon_id . ' .rotator-image {
                    background: #381809;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    z-index: 5;
                    background-size: ' . $lb2_image_fit . ';
                    background-position: 50% 50%;
                    background-repeat: no-repeat;
                    display: none;
                    top: 0px;
                    left: 0px;
                }
                
                ' . $addon_id . ' .rotator-full .rotator-image {
                    height: calc(100% - 0px);

                }
                ' . $addon_id . ' .rotator-full .rotator-screen {
                    background: #381809;
                    position: absolute;
                    width: 100%;
                    overflow: hidden;
                    z-index: 8;
                    background-size: ' . $lb2_image_fit . ';
                    background-position: 50% 50%;
                    background-repeat: no-repeat;
                    left: 0px;
                    height: calc(100% - ' . $lb2_topheight . 'px);
 
                }
                ' . $addon_id . ' .rotator-full .rotator-screen .cover {
                    background: rgba(0, 0, 0, 0.7);
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0px;
                    left: 0px;
                }
                ' . $addon_id . ' .rotator-full .rotator-screen .cover h1, ' . $addon_id . ' .rotator-full .rotator-screen .cover p {
                    display: none;
                    position: absolute;
                    width: 100%;
                    left: 0%;
                    top: 45%;
                    text-align: center;
                    text-transform: uppercase;
                }
                ' . $addon_id . ' .rotator-full .rotator-screen .cover h1 {
                    font-family: ScalaWebPro, serif;
                    font-weight: 400;
                    font-size: 18px;
                    line-height: 24px;
                    color: #f9f8f8;
                    opacity: 0.7;
                    margin-top: -40px;
                    letter-spacing: 3px;
                }
                ' . $addon_id . ' .rotator-full .rotator-screen .cover p {
                    font-weight: 300;
                    font-size: ' . $lb2_fontsize . 'px;
                    line-height: 44px;
                    color: white;
                    letter-spacing: 5px;
                }
            
            ';
        } elseif ($lbys == "lb3") {
            $lb3_zsq = (isset($settings->lb3_zsq) && $settings->lb3_zsq) ? $settings->lb3_zsq : "ysang1"; //指示器样式
            $qx_height = (isset($settings->qx_height) && $settings->qx_height) ? $settings->qx_height : "0"; //取消固定高度
            $img_lunb3 = (isset($settings->img_lunb3) && $settings->img_lunb3) ? $settings->img_lunb3 : "fill"; //图片填充方式

            if (isset($settings->qx_height3)) {
                if (is_object($settings->qx_height3)) {
                    $qx_height3_md = $settings->qx_height3->md;
                    $qx_height3_sm = $settings->qx_height3->sm;
                    $qx_height3_xs = $settings->qx_height3->xs;
                } else {
                    $qx_height3_md = $settings->qx_height3;
                    $qx_height3_sm = $settings->qx_height3_sm;
                    $qx_height3_xs = $settings->qx_height3_xs;
                }
            } else {
                $qx_height3_md = '930';
                $qx_height3_sm = '1326';
                $qx_height3_xs = '720';
            }

            $css .= '
                ' . $addon_id . ' .i100>img {
                    width: 100%;
                }
                ' . $addon_id . ' .i100 {
                    overflow: hidden;
                }
                ' . $addon_id . ' .swiper-container-fade .swiper-slide {

                    -webkit-transition-property: opacity;
                    -o-transition-property: opacity;
                    transition-property: opacity;
                }
                ' . $addon_id . ' .swiper-slide {
                    -webkit-flex-shrink: 0;
                    -ms-flex-negative: 0;
                    flex-shrink: 0;
                    width: 100%;
                    height: 100%;
                    position: relative;
                    -webkit-transition-property: -webkit-transform;
                    transition-property: -webkit-transform;
                    -o-transition-property: transform;
                    transition-property: transform;
                    transition-property: transform,-webkit-transform;
                }
                ' . $addon_id . ' .swiper-container-android .swiper-slide, .swiper-wrapper {
                    -webkit-transform: translate3d(0,0,0);
                    transform: translate3d(0,0,0);
                }
                ' . $addon_id . ' .swiper-wrapper {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    z-index: 1;
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-transition-property: -webkit-transform;
                    transition-property: -webkit-transform;
                    -o-transition-property: transform;
                    transition-property: transform;
                    transition-property: transform,-webkit-transform;
                    -webkit-box-sizing: content-box;
                    box-sizing: content-box;
                }
                ' . $addon_id . ' .ind1-a2 img,' . $addon_id . ' .ind1-a3 img{
                    object-fit:' . $img_lunb3 . ';
                }
                /*ind1*/
                @media only screen and (min-width: 1480px) {';
            if ($qx_height) {
                $css .= '
                            ' . $addon_id . ' .ind1-a1{width: 100%;height: auto;position: relative;overflow: hidden;z-index: 1;}
                        ';
            } else {
                $css .= '
                           ' . $addon_id . ' .ind1-a1{width: 100%;height: ' . $qx_height3_md . 'px;position: relative;overflow: hidden;z-index: 1;} 
                        ';
            }

            $css .= '' . $addon_id . ' .ind111{width: 1920px!important;height: 100%!important;position: relative;margin-left: calc(50% - 1920px/2);}
                    ' . $addon_id . ' .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                    ' . $addon_id . ' .ind1-a2{width: 100%;height: 100%;position: relative;}
                    ' . $addon_id . ' .ind1-a2 img{max-height: 100%;transform: scale(1.2);transition: 10s;}
                    ' . $addon_id . ' .ind111 .swiper-slide-active .ind1-a2 img{transform: scale(1);transition: 10s;}
                    ' . $addon_id . ' .ind1-a3{display: none;}
                    ' . $addon_id . ' .ind1-a4{width: 480px;position: absolute;top: 230px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a5{position: absolute;top: 354px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a6{position: absolute;top: 512px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a7{position: absolute;top: 614px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a8{width: 800px;position: absolute;top: 196px;left: calc(50% + 30px);}
                    ' . $addon_id . ' .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}';
            if ($lb3_zsq != "ysang2") {
                $css .= '' . $addon_id . ' .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        ' . $addon_id . ' .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}';
            }
            $css .= '}
                @media only screen and (max-width: 1479px) and (min-width: 1024px) {';
            if ($qx_height) {
                $css .= '
                            ' . $addon_id . ' .ind1-a1{width: 100%;height: auto;position: relative;overflow: hidden;z-index: 1;}
                        ';
            } else {
                $css .= '
                           ' . $addon_id . ' .ind1-a1{width: 100%;height: 700px;position: relative;overflow: hidden;z-index: 1;} 
                        ';
            }

            $css .= '' . $addon_id . ' .ind111{width: 1480px!important;height: 100%!important;position: relative;margin-left: calc(50% - 1480px/2);}
                    ' . $addon_id . ' .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                    ' . $addon_id . ' .ind1-a2{width: 100%;height: 100%;position: relative;}
                    ' . $addon_id . ' .ind1-a2 img{height: 100%;transform: scale(1.2);transition: 10s;}
                    ' . $addon_id . ' .ind111 .swiper-slide-active .ind1-a2 img{transform: scale(1);transition: 10s;}
                    ' . $addon_id . ' .ind1-a3{display: none;}
                    ' . $addon_id . ' .ind1-a4{width: 360px;position: absolute;top: 180px;left: calc(50% - 960px/2);}
                    ' . $addon_id . ' .ind1-a5{font-size: 90px;line-height: 90px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a5{position: absolute;top: 270px;left: calc(50% - 960px/2);}
                    ' . $addon_id . ' .ind1-a6{font-size: 54px;line-height: 54px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a6{position: absolute;top: 400px;left: calc(50% - 960px/2);}
                    ' . $addon_id . ' .ind1-a7{font-size: 44px;line-height: 44px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a7{position: absolute;top: 500px;left: calc(50% - 960px/2);}
                    ' . $addon_id . ' .ind1-a8{width: 500px;position: absolute;top: 180px;left: calc(50% + 30px);}
                    ' . $addon_id . ' .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}';
            if ($lb3_zsq != "ysang2") {
                $css .= '
                        ' . $addon_id . ' .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        ' . $addon_id . ' .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}';
            }
            $css .= '}

                @media only screen and (max-width: 1023px) {';
            if ($qx_height) {
                $css .= '
                            ' . $addon_id . ' .ind1-a1{width: 100%;height: auto;position: relative;overflow: hidden;z-index: 1;}
                        ';
            } else {
                $css .= '
                           ' . $addon_id . ' .ind1-a1{width: 100%;height: ' . $qx_height3_sm . 'px;position: relative;overflow: hidden;z-index: 1;}
                        ';
            }

            $css .= '

                    ' . $addon_id . ' .ind111{width: 100%!important;height: 100%!important;position: relative}
                    ' . $addon_id . ' .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                    ' . $addon_id . ' .ind1-a2{display: none;}
                    ' . $addon_id . ' .ind1-a3{width: 100%;height: 100%;position: relative;}
                    ' . $addon_id . ' .ind1-a3 img{height: 100%;transform: scale(1.2);transition: 10s;}
                    ' . $addon_id . ' .ind111 .swiper-slide-active .ind1-a3 img{transform: scale(1);transition: 10s;}
                    ' . $addon_id . ' .ind1-a4{width: 480px;position: absolute;top: 190px;left: 56px;}
                    ' . $addon_id . ' .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a5{position: absolute;top: 310px;left: 48px;}
                    ' . $addon_id . ' .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a6{position: absolute;top: 470px;left: 50px;}
                    ' . $addon_id . ' .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a7{position: absolute;top: 580px;left: 40px;}
                    ' . $addon_id . ' .ind1-a8{width: 670px;position: absolute;top: 680px;left: calc(50% - 670px/2);}
                    ' . $addon_id . ' .ind112{width: 100%;position: absolute;bottom: 56px!important;left: 0;}';
            if ($lb3_zsq != "ysang2") {
                $css .= '
                            ' . $addon_id . ' .ind112 .swiper-pagination-bullet{width: 18px;height: 18px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 14px;opacity: 0.5;transition: 0.5s;}
                            ' . $addon_id . ' .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}';
            }
            $css .= '}
                @media only screen and (max-width: 720px) {';
            if ($qx_height) {
                $css .= '
                            ' . $addon_id . ' .ind1-a1{width: 100%;height: auto;position: relative;overflow: hidden;z-index: 1;}
                        ';
            } else {
                $css .= '
                           ' . $addon_id . ' .ind1-a1{width: 100%;height: ' . $qx_height3_xs . 'px;position: relative;overflow: hidden;z-index: 1;}
                        ';
            }

            $css .= '
                    ' . $addon_id . ' .ind111{width: 100%!important;height: 100%!important;position: relative}
                    ' . $addon_id . ' .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                    ' . $addon_id . ' .ind1-a2{display: none;}
                    ' . $addon_id . ' .ind1-a3{width: 100%;height: 100%;position: relative;}
                    ' . $addon_id . ' .ind1-a3 img{height: 100%;transform: scale(1.2);transition: 10s;}
                    ' . $addon_id . ' .ind111 .swiper-slide-active .ind1-a3 img{transform: scale(1);transition: 10s;}
                    ' . $addon_id . ' .ind1-a4{width: 345px;position: absolute;top: 136px;left: 40px;}
                    ' . $addon_id . ' .ind1-a5{font-size: 86px;line-height: 86px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a5{position: absolute;top: 223px;left: 34px;}
                    ' . $addon_id . ' .ind1-a6{font-size: 51px;line-height: 51px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a6{position: absolute;top: 338px;left: 36px;}
                    ' . $addon_id . ' .ind1-a7{font-size: 41px;line-height: 41px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a7{position: absolute;top: 417px;left: 28px;}
                    ' . $addon_id . ' .ind1-a8{width: 482px;position: absolute;top: 489px;left: calc(50% - 482px/2);}
                    ' . $addon_id . ' .ind112{width: 100%;position: absolute;bottom: 30px!important;left: 0;}';
            if ($lb3_zsq != "ysang2") {
                $css .= '
                            ' . $addon_id . ' .ind112 .swiper-pagination-bullet{width: 10px;height: 10px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                            ' . $addon_id . ' .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}';
            }
            $css .= '}
                @media only screen and (min-width: 1921px) {';

            if ($qx_height) {
                $css .= '
                            ' . $addon_id . ' .ind1-a1{width: 100%;height: auto;position: relative;overflow: hidden;z-index: 1;}
                        ';
            } else {
                $css .= '
                           ' . $addon_id . ' .ind1-a1{width: 100%;height: ' . $qx_height3_md . 'px;position: relative;overflow: hidden;z-index: 1;}
                        ';
            }

            $css .= '
                    
                    
                    ' . $addon_id . ' .ind111{width: 2560px!important;height: 100%!important;position: relative;margin-left: calc(50% - 2560px/2);}
                    ' . $addon_id . ' .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                    ' . $addon_id . ' .ind1-a2{width: 100%;height: 100%;position: relative;}
                    ' . $addon_id . ' .ind1-a2 img{max-height: 100%;height: auto;position: absolute;bottom: -150px;left: 0;}
                    ' . $addon_id . ' .ind1-a3{display: none;}
                    ' . $addon_id . ' .ind1-a4{width: 480px;position: absolute;top: 230px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a5{position: absolute;top: 354px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a6{position: absolute;top: 512px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;}
                    ' . $addon_id . ' .ind1-a7{position: absolute;top: 614px;left: calc(50% - 1440px/2);}
                    ' . $addon_id . ' .ind1-a8{width: 800px;position: absolute;top: 196px;left: calc(50% + 30px);}
                    ' . $addon_id . ' .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}';
            if ($lb3_zsq != "ysang2") {
                $css .= '
                        ' . $addon_id . ' .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        ' . $addon_id . ' .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}';
            }
            $css .= '}
            ';
            if ($lb3_zsq == "ysang2") {
                $css .= '
                ' . $addon_id . ' .swiper-pagination-bullet{background:transparent;width:18px;height:auto;}
                ' . $addon_id . ' .clust{
                    position: absolute;
                    bottom:8%;
                    left: 50%;
                    padding-bottom: 5px;
                    border-bottom: 1px solid rgba(255,255,255,0.2);
                    display: table;
                    padding: 0 20px;
                    width: auto;
                    text-align: center;
                    transform: translateX(-50%);
                    -webkit-transform: translateX(-50%);
                    height: 32px;
                }
                ' . $addon_id . ' .clust span{
                    font-family: "Myriad Pro","Microsoft YaHei";
                    font-size: 14px;
                    color: #fff;
                    margin-right:100px;
                    display: inline-block;
                    cursor: pointer;
                    position: relative;
                    transition: all 500ms ease;
                    -webkit-transition:all 500ms ease;
                }
                ' . $addon_id . ' .clust span:last-child{
                    margin-right: 0;
                }
                ' . $addon_id . ' .clust span:after{
                    content: "";
                    display: inline-block;
                    width: 0;
                    height: 2px;
                    background: #fff;
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    -webkit-transform: translateX(-50%);
                    bottom:-9px;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 500ms ease;
                    -webkit-transition:all 500ms ease;
                    margin-top: 5px;
                }
                ' . $addon_id . ' .clust span.swiper-pagination-bullet-active:after{
                    width: 100%;
                    opacity: 1;
                    visibility: visible;
                }
                ' . $addon_id . ' .clust span.active:after {
                    width: 100%;
                    opacity: 1;
                    visibility: visible;
                }';
            }
        } elseif($lbys == "lb4") {
            if (isset($settings->img_height04)) {
                if (is_object($settings->img_height04)) {
                    $img_height04_md = $settings->img_height04->md;
                    $img_height04_sm = $settings->img_height04->sm;
                    $img_height04_xs = $settings->img_height04->xs;
                } else {
                    $img_height04_md = $settings->img_height04;
                    $img_height04_sm = $settings->img_height04_sm;
                    $img_height04_xs = $settings->img_height04_xs;
                }
            } else {
                $img_height04_md = '602';
                $img_height04_sm = '334';
                $img_height04_xs = '186';
            }
            // 图片填充方式
            $img_fit_04 = isset($settings->img_fit_04) ? $settings->img_fit_04 : 'cover';

            // 是否显示翻页按钮
            $is_swiper_button = isset($settings->is_swiper_button) ? $settings->is_swiper_button : 1;
            // 上翻页按钮
            $swiper_button_prev = isset($settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png';
            // 下翻页按钮
            $swiper_button_next = isset($settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png';
            // 移入上翻页按钮
            $swiper_button_prev_hover = isset($settings->swiper_button_prev_hover) ? $settings->swiper_button_prev_hover : '';
            // 移入下翻页按钮
            $swiper_button_next_hover = isset($settings->swiper_button_next_hover) ? $settings->swiper_button_next_hover : '';
            // 切换按钮宽度
            $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 40;
            $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : '';
            $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : '';
            // 切换按钮高度
            $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 40;
            $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : '';
            $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : '';
            // 切换按钮上边距（百分比）
            $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
            $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : '';
            $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : '';
            // 切换按钮 上边距单位
            $swiper_button_top_unit = (isset($settings->is_swiper_button_top_px) && $settings->is_swiper_button_top_px == 1) ? 'px' : '%';
            // 切换按钮两侧边距（px）
            $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
            $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : '';
            $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : '';

            /* 轮播点样式 */
            // 是否显示翻页按钮
            $is_swiper_pagination = isset($settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 1;
            // 轮播点底部距离
            $swiper_pagination_bottom_md = (isset($settings->swiper_pagination_bottom) && $settings->swiper_pagination_bottom) ? $settings->swiper_pagination_bottom : 20;
            $swiper_pagination_bottom_sm = (isset($settings->swiper_pagination_bottom_sm) && $settings->swiper_pagination_bottom_sm) ? $settings->swiper_pagination_bottom_sm : '';
            $swiper_pagination_bottom_xs = (isset($settings->swiper_pagination_bottom_xs) && $settings->swiper_pagination_bottom_xs) ? $settings->swiper_pagination_bottom_xs : '';
            //正常
            // 轮播点宽度
            $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 23;
            $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : '';
            $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : '';
            // 轮播点高度
            $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 5;
            $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : '';
            $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : '';
            // 轮播点间距
            $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? ($settings->swiper_p_margin / 2) : 5;
            $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? ($settings->swiper_p_margin_sm / 2) : '';
            $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? ($settings->swiper_p_margin_xs / 2) : '';
            // 轮播点颜色
            $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#fff';

            //选中
            // 轮播点宽度
            $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : '';
            $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : '';
            $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : '';
            // 轮播点高度
            $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : '';
            $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : '';
            $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : '';
            // 轮播点颜色
            $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';


            $css .= "
                {$addon_id} ul,
                {$addon_id} li,
                {$addon_id} ol {
                    list-style: none;
                }
                {$addon_id} .cvNode>img {
                    max-width: initial !important;
                    object-fit: {$img_fit_04};
                }
                {$addon_id} .os-slider {
                    width: 100%;
                    height: {$img_height04_md}px;
                }
                {$addon_id} .os-slider-main,
                {$addon_id} .os-slider-main li {
                    padding: 0;
                    margin: 0;
                    width: 100%;
                    height: 100%;
                }
                {$addon_id} .os-slider-main li>a>img,
                {$addon_id} .os-slider-main li>img {
                    width: 100%;
                    height: 100%;
                    object-fit: {$img_fit_04};
                }
                /* 轮播点 样式 */
                {$addon_id} .osSlider-main .slider-nav {
                    position: absolute;
                    width: 100%;
                    bottom: {$swiper_pagination_bottom_md}px;
                    text-align: center;
                    z-index: 25;
                    padding: 0;
                    margin: 0;";
                    if($is_swiper_pagination != 1) {
                        $css .= "display: none;";
                    }
                $css .= "
                }
                {$addon_id} .osSlider-main .slider-nav li {
                    display: inline-block;
                    width: {$swiper_p_width_md}px;
                    height: {$swiper_p_height_md}px;
                    border-radius: 50px;
                    cursor: pointer;
                    background: {$swiper_p_color};
                    box-sizing: border-box;
                    vertical-align: middle;
                    text-indent: -9999px;
                    overflow: hidden;
                    margin: 0 {$swiper_p_margin_md}px;
                }
                {$addon_id} .osSlider-main .slider-nav li.active {
                    width: {$swiper_p_width_a_md}px;
                    height: {$swiper_p_height_a_md}px;
                    background: {$swiper_p_color_a};
                }
                /* 切换按钮 */
                {$addon_id} .osSlider-main .slider-btn-prev,
                {$addon_id} .osSlider-main .slider-btn-next {
                    position: absolute;
                    cursor: pointer;
                    z-index: 30;
                    top: {$swiper_button_top_md}{$swiper_button_top_unit};
                    width: {$swiper_button_width_md}px;
                    height: {$swiper_button_height_md}px;
                    text-indent: -9999px;
                    transition: all .5s linear;";
                    if($is_swiper_button != 1) {
                        $css .= "display: none;";
                    }
                $css .= "
                }
                /* 上一个 */
                {$addon_id} .osSlider-main .slider-btn-prev {
                    left: -45px;
                    background: url({$swiper_button_prev}) no-repeat;
                    background-size: contain;
                }
                {$addon_id} .osSlider-main:hover .slider-btn-prev {
                    left: {$swiper_button_left_md}px;
                }
                {$addon_id} .osSlider-main .slider-btn-prev:hover {";
                    if($swiper_button_prev_hover) {
                        $css .= "background-image: url({$swiper_button_prev_hover});";
                    }
                $css .= "
                }
                /* 下一个 */
                {$addon_id} .osSlider-main .slider-btn-next {
                    right: -45px;
                    background: url({$swiper_button_next}) no-repeat;
                    background-size: contain;
                }
                {$addon_id} .osSlider-main:hover .slider-btn-next {
                    right: {$swiper_button_left_md}px;
                }
                {$addon_id} .osSlider-main .slider-btn-next:hover {";
                    if($swiper_button_next_hover) {
                        $css .= "background-image: url({$swiper_button_next_hover});";
                    }
                $css .= "
                }

                @media (min-width: 768px) and (max-width: 991px) {
                    {$addon_id} .os-slider {
                        height: {$img_height04_sm}px;
                    }
                    /* 轮播点 样式 */
                    {$addon_id} .osSlider-main .slider-nav {
                        bottom: {$swiper_pagination_bottom_sm}px;
                    }
                    {$addon_id} .osSlider-main .slider-nav li {
                        width: {$swiper_p_width_sm}px;
                        height: {$swiper_p_height_sm}px;
                        margin: 0 {$swiper_p_margin_sm}px;
                    }
                    {$addon_id} .osSlider-main .slider-nav li.active {
                        width: {$swiper_p_width_a_sm}px;
                        height: {$swiper_p_height_a_sm}px;
                    }
                    /* 切换按钮 */
                    {$addon_id} .osSlider-main .slider-btn-prev,
                    {$addon_id} .osSlider-main .slider-btn-next {
                        top: {$swiper_button_top_sm}{$swiper_button_top_unit};
                        width: {$swiper_button_width_sm}px;
                        height: {$swiper_button_height_sm}px;
                    }
                    /* 上一个 */
                    {$addon_id} .osSlider-main:hover .slider-btn-prev {
                        left: {$swiper_button_left_sm}px;
                    }
                    /* 下一个 */
                    {$addon_id} .osSlider-main:hover .slider-btn-next {
                        right: {$swiper_button_left_sm}px;
                    }
                }
                @media (max-width: 767px) {
                    {$addon_id} .os-slider {
                        height: {$img_height04_xs}px;
                    }
                    /* 轮播点 样式 */
                    {$addon_id} .osSlider-main .slider-nav {
                        bottom: {$swiper_pagination_bottom_xs}px;
                    }
                    {$addon_id} .osSlider-main .slider-nav li {
                        width: {$swiper_p_width_xs}px;
                        height: {$swiper_p_height_xs}px;
                        margin: 0 {$swiper_p_margin_xs}px;
                    }
                    {$addon_id} .osSlider-main .slider-nav li.active {
                        width: {$swiper_p_width_a_xs}px;
                        height: {$swiper_p_height_a_xs}px;
                    }
                    /* 切换按钮 */
                    {$addon_id} .osSlider-main .slider-btn-prev,
                    {$addon_id} .osSlider-main .slider-btn-next {
                        top: {$swiper_button_top_xs}{$swiper_button_top_unit};
                        width: {$swiper_button_width_xs}px;
                        height: {$swiper_button_height_xs}px;
                    }
                    /* 上一个 */
                    {$addon_id} .osSlider-main:hover .slider-btn-prev {
                        left: {$swiper_button_left_xs}px;
                    }
                    /* 下一个 */
                    {$addon_id} .osSlider-main:hover .slider-btn-next {
                        right: {$swiper_button_left_xs}px;
                    }
                }
            ";
        } elseif($lbys == "lb5") {
            // 开启固定宽高
            $show_zsy = (isset($settings->show_zsy) && $settings->show_zsy) ? $settings->show_zsy : 0;
            // 轮播高度
            $img_gd_height = isset($settings->img_gd_height) ? $settings->img_gd_height : '500';
            $img_gd_height_sm = isset($settings->img_gd_height_sm) ? $settings->img_gd_height_sm : '';
            $img_gd_height_xs = isset($settings->img_gd_height_xs) ? $settings->img_gd_height_xs : '';
            // 图片填充方式
            $img_style_type7 = (isset($settings->img_style_type7) && $settings->img_style_type7) ? $settings->img_style_type7 : 'fill';
            
            /* 标题配置 */
            // 标题背景颜色
            $title_bgcolor05 = isset($settings->title_bgcolor05) ? $settings->title_bgcolor05 : 'rgba(0, 0, 0, 0.64)';
            // 标题文字大小
            $title_fontsize05_md = (isset($settings->title_fontsize05) && $settings->title_fontsize05) ? $settings->title_fontsize05 : 18;
            $title_fontsize05_sm = (isset($settings->title_fontsize05_sm) && $settings->title_fontsize05_sm) ? $settings->title_fontsize05_sm : '';
            $title_fontsize05_xs = (isset($settings->title_fontsize05_xs) && $settings->title_fontsize05_xs) ? $settings->title_fontsize05_xs : '';
            // 标题文字行高
            $title_lineHeight05_md = (isset($settings->title_lineHeight05) && $settings->title_lineHeight05) ? $settings->title_lineHeight05 : 52;
            $title_lineHeight05_sm = (isset($settings->title_lineHeight05_sm) && $settings->title_lineHeight05_sm) ? $settings->title_lineHeight05_sm : '';
            $title_lineHeight05_xs = (isset($settings->title_lineHeight05_xs) && $settings->title_lineHeight05_xs) ? $settings->title_lineHeight05_xs : '';
            // 标题文字颜色
            $title_color05 = isset($settings->title_color05) ? $settings->title_color05 : '#FFFFFF';
            // 标题文字内边距
            $title_padding05_md = (isset($settings->title_padding05) && $settings->title_padding05) ? $settings->title_padding05 : '0 200px 0 20px';
            $title_padding05_sm = (isset($settings->title_padding05_sm) && $settings->title_padding05_sm) ? $settings->title_padding05_sm : '';
            $title_padding05_xs = (isset($settings->title_padding05_xs) && $settings->title_padding05_xs) ? $settings->title_padding05_xs : '';
            
            // 是否显示翻页按钮
            $is_swiper_button05 = isset($settings->is_swiper_button05) ? $settings->is_swiper_button05 : 1;
            // 上翻页按钮
            $swiper_button_prev05 = isset($settings->swiper_button_prev05) ? $settings->swiper_button_prev05 : 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png';
            // 下翻页按钮
            $swiper_button_next05 = isset($settings->swiper_button_next05) ? $settings->swiper_button_next05 : 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png';
            // 移入上翻页按钮
            $swiper_button_prev_hover05 = isset($settings->swiper_button_prev_hover05) ? $settings->swiper_button_prev_hover05 : '';
            // 移入下翻页按钮
            $swiper_button_next_hover05 = isset($settings->swiper_button_next_hover05) ? $settings->swiper_button_next_hover05 : '';
            // 切换按钮宽度
            $swiper_button_width05_md = (isset($settings->swiper_button_width05) && $settings->swiper_button_width05) ? $settings->swiper_button_width05 : 40;
            $swiper_button_width05_sm = (isset($settings->swiper_button_width05_sm) && $settings->swiper_button_width05_sm) ? $settings->swiper_button_width05_sm : '';
            $swiper_button_width05_xs = (isset($settings->swiper_button_width05_xs) && $settings->swiper_button_width05_xs) ? $settings->swiper_button_width05_xs : '';
            // 切换按钮高度
            $swiper_button_height05_md = (isset($settings->swiper_button_height05) && $settings->swiper_button_height05) ? $settings->swiper_button_height05 : 40;
            $swiper_button_height05_sm = (isset($settings->swiper_button_height05_sm) && $settings->swiper_button_height05_sm) ? $settings->swiper_button_height05_sm : '';
            $swiper_button_height05_xs = (isset($settings->swiper_button_height05_xs) && $settings->swiper_button_height05_xs) ? $settings->swiper_button_height05_xs : '';
            
            /* 轮播点样式 */
            // 是否显示翻页按钮
            $is_swiper_pagination05 = isset($settings->is_swiper_pagination05) ? $settings->is_swiper_pagination05 : 1;
            //正常
            // 轮播点宽度
            $swiper_p_width05_md = (isset($settings->swiper_p_width05) && $settings->swiper_p_width05) ? $settings->swiper_p_width05 : 18;
            $swiper_p_width05_sm = (isset($settings->swiper_p_width05_sm) && $settings->swiper_p_width05_sm) ? $settings->swiper_p_width05_sm : '';
            $swiper_p_width05_xs = (isset($settings->swiper_p_width05_xs) && $settings->swiper_p_width05_xs) ? $settings->swiper_p_width05_xs : '';
            // 轮播点高度
            $swiper_p_height05_md = (isset($settings->swiper_p_height05) && $settings->swiper_p_height05) ? $settings->swiper_p_height05 : 7;
            $swiper_p_height05_sm = (isset($settings->swiper_p_height05_sm) && $settings->swiper_p_height05_sm) ? $settings->swiper_p_height05_sm : '';
            $swiper_p_height05_xs = (isset($settings->swiper_p_height05_xs) && $settings->swiper_p_height05_xs) ? $settings->swiper_p_height05_xs : '';
            // 轮播点间距
            $swiper_p_margin05_md = (isset($settings->swiper_p_margin05) && $settings->swiper_p_margin05) ? ($settings->swiper_p_margin05 / 2) : 2.5;
            $swiper_p_margin05_sm = (isset($settings->swiper_p_margin05_sm) && $settings->swiper_p_margin05_sm) ? ($settings->swiper_p_margin05_sm / 2) : '';
            $swiper_p_margin05_xs = (isset($settings->swiper_p_margin05_xs) && $settings->swiper_p_margin05_xs) ? ($settings->swiper_p_margin05_xs / 2) : '';
            // 轮播点圆角
            $swiper_p_border05_md = isset($settings->swiper_p_border05) ? $settings->swiper_p_border05 : 1;
            $swiper_p_border05_sm = isset($settings->swiper_p_border05_sm) ? $settings->swiper_p_border05_sm : '';
            $swiper_p_border05_xs = isset($settings->swiper_p_border05_xs) ? $settings->swiper_p_border05_xs : '';
            // 轮播点颜色
            $swiper_p_color05 = (isset($settings->swiper_p_color05) && $settings->swiper_p_color05) ? $settings->swiper_p_color05 : 'rgba(255,255,255,0.52)';

            //选中
            // 轮播点宽度
            $swiper_p_width_a05_md = (isset($settings->swiper_p_width_a05) && $settings->swiper_p_width_a05) ? $settings->swiper_p_width_a05 : '';
            $swiper_p_width_a05_sm = (isset($settings->swiper_p_width_a05_sm) && $settings->swiper_p_width_a05_sm) ? $settings->swiper_p_width_a05_sm : '';
            $swiper_p_width_a05_xs = (isset($settings->swiper_p_width_a05_xs) && $settings->swiper_p_width_a05_xs) ? $settings->swiper_p_width_a05_xs : '';
            // 轮播点高度
            $swiper_p_height_a05_md = (isset($settings->swiper_p_height_a05) && $settings->swiper_p_height_a05) ? $settings->swiper_p_height_a05 : '';
            $swiper_p_height_a05_sm = (isset($settings->swiper_p_height_a05_sm) && $settings->swiper_p_height_a05_sm) ? $settings->swiper_p_height_a05_sm : '';
            $swiper_p_height_a05_xs = (isset($settings->swiper_p_height_a05_xs) && $settings->swiper_p_height_a05_xs) ? $settings->swiper_p_height_a05_xs : '';
            // 轮播点颜色
            $swiper_p_color_a05 = (isset($settings->swiper_p_color_a05) && $settings->swiper_p_color_a05) ? $settings->swiper_p_color_a05 : '#FFFFFF';

            if ($show_zsy == 1) {
                $css .= "
                    {$addon_id} .swiper-container {
                        height: {$img_gd_height}px;
                    }
                    {$addon_id} .swiper-container .swiper-slide .img-box {
                        width: 100%;
                        height: 100%;
                    }
                    {$addon_id} .swiper-container .swiper-slide .img-box img {
                        width: 100%;
                        height: 100%;
                        object-fit: {$img_style_type7};
                    }
                ";
            }
            $css .= "
                {$addon_id} .swiper-slide .title {
                    position: absolute;
                    width: 100%;
                    height: {$title_lineHeight05_md}px;
                    line-height: {$title_lineHeight05_md}px;
                    padding: {$title_padding05_md};
                    box-sizing: border-box;
                    background-color: {$title_bgcolor05};
                    bottom: 0;
                    left: 0;
                    color: {$title_color05};
                    font-size: {$title_fontsize05_md}px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin: 0;
                }
                /* 轮播点 */
                {$addon_id} .swiper-container .swiper-pagination {
                    width: max-content;
                    right: 20px;
                    left: inherit;
                    height: {$title_lineHeight05_md}px;
                    bottom: 0;
                    display: flex;
                    align-items: center;";
                    if ($is_swiper_pagination05 != 1) {
                        $css .= "display: none;";
                    }
                    $css .= "
                }
                {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                    width: {$swiper_p_width05_md}px;
                    height: {$swiper_p_height05_md}px;
                    border-radius: {$swiper_p_border05_md}px;
                    background: {$swiper_p_color05};
                    margin: 0 {$swiper_p_margin05_md}px;
                    opacity: 1;
                }
                {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                    width: {$swiper_p_width_a05_md}px;
                    height: {$swiper_p_height_a05_md}px;
                    background: {$swiper_p_color_a05};
                }
                @media (min-width: 768px) and (max-width: 991px) {";
                    if ($show_zsy == 1) {
                        $css .= "
                            {$addon_id} .swiper-container {
                                height: {$img_gd_height_sm}px;
                            }
                        ";
                    }
                    $css .= "
                    {$addon_id} .swiper-slide .title {
                        height: {$title_lineHeight05_sm}px;
                        line-height: {$title_lineHeight05_sm}px;
                        padding: {$title_padding05_sm};
                        font-size: {$title_fontsize05_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination {
                        height: {$title_lineHeight05_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                        width: {$swiper_p_width05_sm}px;
                        height: {$swiper_p_height05_sm}px;
                        border-radius: {$swiper_p_border05_sm}px;
                        margin: 0 {$swiper_p_margin05_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                        width: {$swiper_p_width_a05_sm}px;
                        height: {$swiper_p_height_a05_sm}px;
                    }
                }
                @media (max-width: 767px) {";
                    if ($show_zsy == 1) {
                        $css .= "
                            {$addon_id} .swiper-container {
                                height: {$img_gd_height_xs}px;
                            }
                        ";
                    }
                    $css .= "
                    {$addon_id} .swiper-slide .title {
                        height: {$title_lineHeight05_xs}px;
                        line-height: {$title_lineHeight05_xs}px;
                        padding: {$title_padding05_xs};
                        font-size: {$title_fontsize05_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination {
                        height: {$title_lineHeight05_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                        width: {$swiper_p_width05_xs}px;
                        height: {$swiper_p_height05_xs}px;
                        border-radius: {$swiper_p_border05_xs}px;
                        margin: 0 {$swiper_p_margin05_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                        width: {$swiper_p_width_a05_xs}px;
                        height: {$swiper_p_height_a05_xs}px;
                    }
                }
            ";
        } else {
            //气泡效果开启
            $show_bubble = (isset($settings->show_bubble) && $settings->show_bubble) ? $settings->show_bubble : 0;
            if ($show_bubble) {
                $css .= $addon_id . ' .canvas-area {
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 10;
                    width: 100%;
                    height: 100%;
                }
                ' . $addon_id . ' .jwpf-item.jwpf-item-has-bg .jwpf-carousel-item-inner {
                    z-index: 12;
                }';
            }

            // Buttons style
            foreach ($settings->jw_carousel_item as $key => $value) {

                if (isset($value->button_text)) {
                    $css_path = new JLayoutFile('addon.css.button', $layout_path);
                    $css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $value, 'id' => 'btn-' . ($this->addon->id + $key)));
                }

                $title_css = '';
                $title_css .= (isset($value->title_fontsize) && $value->title_fontsize) ? 'font-size:' . $value->title_fontsize . 'px;' : '';
                $title_css .= (isset($value->title_lineheight) && $value->title_lineheight) ? 'line-height:' . $value->title_lineheight . 'px;' : '';
                $title_css .= (isset($value->title_color) && !empty($value->title_color)) ? 'color:' . $value->title_color . ';' : '';

                if (isset($value->title_font_family) && $value->title_font_family) {
                    $font_path = new JLayoutFile('addon.css.fontfamily', $layout_path);
                    $font_path->render(array('font' => $value->title_font_family));
                    $title_css .= 'font-family: ' . $value->title_font_family . ';';
                }

                if (isset($value->title_padding) && $value->title_padding) {
                    if (trim($value->title_padding) != "") {
                        $title_padding_md = '';
                        $title_paddings_md = explode(' ', $value->title_padding);
                        foreach ($title_paddings_md as $padding_md) {
                            $padding_md = trim($padding_md);
                            if (empty($padding_md)) {
                                $title_padding_md .= ' 0';
                            } else {
                                $title_padding_md .= ' ' . $padding_md;
                            }
                        }
                        $title_css .= "padding: " . $title_padding_md . ";\n";
                    }
                }

                if (isset($value->title_margin) && $value->title_margin) {
                    if (trim($value->title_margin) != "") {
                        $title_margin_md = '';
                        $title_margins_md = explode(' ', $value->title_margin);
                        foreach ($title_margins_md as $margin_md) {
                            $margin_md = trim($margin_md);
                            if (empty($margin_md)) {
                                $title_margin_md .= ' 0';
                            } else {
                                $title_margin_md .= ' ' . $margin_md;
                            }
                        }
                        $title_css .= "margin: " . $title_margin_md . ";\n";
                    }
                }

                if (!empty($title_css)) {
                    $css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2 {';
                    $css .= $title_css;
                    $css .= '}';
                }

                $content_css = '';
                $content_css .= (isset($value->content_fontsize) && $value->content_fontsize) ? 'font-size:' . $value->content_fontsize . 'px;' : '';
                $content_css .= (isset($value->content_lineheight) && $value->content_lineheight) ? 'line-height:' . $value->content_lineheight . 'px;' : '';
                $content_css .= (isset($value->content_color) && $value->content_color) ? 'color:' . $value->content_color . ';' : '';

                if (isset($value->content_font_family) && $value->content_font_family) {
                    $font_path = new JLayoutFile('addon.css.fontfamily', $layout_path);
                    $font_path->render(array('font' => $value->content_font_family));
                    $content_css .= 'font-family: ' . $value->content_font_family . ';';
                }

                if (isset($value->content_padding) && $value->content_padding) {
                    if (trim($value->content_padding) != "") {
                        $content_padding_md = '';
                        $content_paddings_md = explode(' ', $value->content_padding);
                        foreach ($content_paddings_md as $padding_md) {
                            $padding_md = trim($padding_md);
                            if (empty($padding_md)) {
                                $content_padding_md .= ' 0';
                            } else {
                                $content_padding_md .= ' ' . $padding_md;
                            }
                        }
                        $content_css .= "padding: " . $content_padding_md . ";\n";
                    }
                }

                if (isset($value->content_margin) && $value->content_margin) {
                    if (trim($value->content_margin) != "") {
                        $content_margin_md = '';
                        $content_margins_md = explode(' ', $value->content_margin);
                        foreach ($content_margins_md as $margin_md) {
                            $margin_md = trim($margin_md);
                            if (empty($margin_md)) {
                                $content_margin_md .= ' 0';
                            } else {
                                $content_margin_md .= ' ' . $margin_md;
                            }
                        }
                        $content_css .= "margin: " . $content_margin_md . ";\n";
                    }
                }

                if (!empty($content_css)) {
                    $css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content{';
                    $css .= $content_css;
                    $css .= '}';
                }

                $selector_css = new JLayoutFile('addon.css.selector', $layout_path);
                $css .= $selector_css->render(
                    array(
                        'options' => $value,
                        'addon_id' => $addon_id,
                        'selector' => '#jwpf-item-' . ($this->addon->id . $key)
                    )
                );

                // echo $css;
                // die();

                // Tablet CSS

                $tablet_css = '';
                $title_css = '';
                $title_css .= (isset($value->title_fontsize_sm) && $value->title_fontsize_sm) ? 'font-size:' . $value->title_fontsize_sm . 'px;' : '';
                $title_css .= (isset($value->title_lineheight_sm) && $value->title_lineheight_sm) ? 'line-height:' . $value->title_lineheight_sm . 'px;' : '';

                if (isset($value->title_padding_sm) && $value->title_padding_sm) {
                    if (trim($value->title_padding_sm) != "") {
                        $title_padding_sm = '';
                        $title_paddings_sm = explode(' ', $value->title_padding_sm);
                        foreach ($title_paddings_sm as $padding_sm) {
                            $padding_sm = trim($padding_sm);
                            if (empty($padding_sm)) {
                                $title_padding_sm .= ' 0';
                            } else {
                                $title_padding_sm .= ' ' . $padding_sm;
                            }
                        }
                        $title_css .= "padding: " . $title_padding_sm . ";\n";
                    }
                }

                if (isset($value->title_padding_sm) && $value->title_margin_sm) {
                    if (trim($value->title_margin_sm) != "") {
                        $title_margin_sm = '';
                        $title_margins_sm = explode(' ', $value->title_margin_sm);
                        foreach ($title_margins_sm as $margin_sm) {
                            $margin_sm = trim($margin_sm);
                            if (empty($margin_sm)) {
                                $title_margin_sm .= ' 0';
                            } else {
                                $title_margin_sm .= ' ' . $margin_sm;
                            }
                        }
                        $title_css .= "margin: " . $title_margin_sm . ";\n";
                    }
                }

                if (!empty($title_css)) {
                    $tablet_css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2 {';
                    $tablet_css .= $title_css;
                    $tablet_css .= '}';
                }

                $content_css = '';
                $content_css .= (isset($value->content_fontsize_sm) && $value->content_fontsize_sm) ? 'font-size:' . $value->content_fontsize_sm . 'px;' : '';
                $content_css .= (isset($value->content_lineheight_sm) && $value->content_lineheight_sm) ? 'line-height:' . $value->content_lineheight_sm . 'px;' : '';

                if (isset($value->content_padding_sm) && $value->content_padding_sm) {
                    if (trim($value->content_padding_sm) != "") {
                        $content_padding_sm = '';
                        $content_paddings_sm = explode(' ', $value->content_padding_sm);
                        foreach ($content_paddings_sm as $padding_sm) {
                            $padding_sm = trim($padding_sm);
                            if (empty($padding_sm)) {
                                $content_padding_sm .= ' 0';
                            } else {
                                $content_padding_sm .= ' ' . $padding_sm;
                            }
                        }
                        $content_css .= "padding: " . $content_padding_sm . ";\n";
                    }
                }

                if (isset($value->content_margin_sm) && $value->content_margin_sm) {
                    if (trim($value->content_margin_sm) != "") {
                        $content_margin_sm = '';
                        $content_margins_sm = explode(' ', $value->content_margin_sm);
                        foreach ($content_margins_sm as $margin_sm) {
                            $margin_sm = trim($margin_sm);
                            if (empty($margin_sm)) {
                                $content_margin_sm .= ' 0';
                            } else {
                                $content_margin_sm .= ' ' . $margin_sm;
                            }
                        }
                        $content_css .= "margin: " . $content_margin_sm . ";\n";
                    }
                }

                if (!empty($content_css)) {
                    $tablet_css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content{';
                    $tablet_css .= $content_css;
                    $tablet_css .= '}';
                }

                if (!empty($tablet_css)) {
                    $css .= '@media (min-width: 768px) and (max-width: 991px) {';
                    $css .= $tablet_css;
                    $css .= '}';
                }

                // Mobile CSS
                $mobile_css = '';
                $title_css = '';
                $title_css .= (isset($value->title_fontsize_xs) && $value->title_fontsize_xs) ? 'font-size:' . $value->title_fontsize_xs . 'px;' : '';
                $title_css .= (isset($value->title_lineheight_xs) && $value->title_lineheight_xs) ? 'line-height:' . $value->title_lineheight_xs . 'px;' : '';

                if (isset($value->title_padding_xs) && $value->title_padding_xs) {
                    if (trim($value->title_padding_xs) != "") {
                        $title_padding_xs = '';
                        $title_paddings_xs = explode(' ', $value->title_padding_xs);
                        foreach ($title_paddings_xs as $padding_xs) {
                            $padding_xs = trim($padding_xs);
                            if (empty($padding_xs)) {
                                $title_padding_xs .= ' 0';
                            } else {
                                $title_padding_xs .= ' ' . $padding_xs;
                            }
                        }
                        $title_css .= "padding: " . $title_padding_xs . ";\n";
                    }
                }

                if (isset($value->title_margin_xs) && $value->title_margin_xs) {
                    if (trim($value->title_margin_xs) != "") {
                        $title_margin_xs = '';
                        $title_margins_xs = explode(' ', $value->title_margin_xs);
                        foreach ($title_margins_xs as $margin_xs) {
                            $margin_xs = trim($margin_xs);
                            if (empty($margin_xs)) {
                                $title_margin_xs .= ' 0';
                            } else {
                                $title_margin_xs .= ' ' . $margin_xs;
                            }
                        }
                        $title_css .= "margin: " . $title_margin_xs . ";\n";
                    }
                }

                if (!empty($title_css)) {
                    $mobile_css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2{';
                    $mobile_css .= $title_css;
                    $mobile_css .= '}';
                }

                $content_css = '';
                $content_css .= (isset($value->content_fontsize_xs) && $value->content_fontsize_xs) ? 'font-size:' . $value->content_fontsize_xs . 'px;' : '';
                $content_css .= (isset($value->content_lineheight_xs) && $value->content_lineheight_xs) ? 'line-height:' . $value->content_lineheight_xs . 'px;' : '';

                if (isset($value->content_padding_xs) && $value->content_padding_xs) {
                    if (trim($value->content_padding_xs) != "") {
                        $content_padding_xs = '';
                        $content_paddings_xs = explode(' ', $value->content_padding_xs);
                        foreach ($content_paddings_xs as $padding_xs) {
                            $padding_xs = trim($padding_xs);
                            if (empty($padding_xs)) {
                                $content_padding_xs .= ' 0';
                            } else {
                                $content_padding_xs .= ' ' . $padding_xs;
                            }
                        }
                        $content_css .= "padding: " . $content_padding_xs . ";\n";
                    }
                }

                if (isset($value->content_margin_xs) && $value->content_margin_xs) {
                    if (trim($value->content_margin_xs) != "") {
                        $content_margin_xs = '';
                        $content_margins_xs = explode(' ', $value->content_margin_xs);
                        foreach ($content_margins_xs as $margin_xs) {
                            $margin_xs = trim($margin_xs);
                            if (empty($margin_xs)) {
                                $content_margin_xs .= ' 0';
                            } else {
                                $content_margin_xs .= ' ' . $margin_xs;
                            }
                        }
                        $content_css .= "margin: " . $content_margin_xs . ";\n";
                    }
                }

                if (!empty($content_css)) {
                    $mobile_css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content{';
                    $mobile_css .= $content_css;
                    $mobile_css .= '}';
                }

                if (!empty($mobile_css)) {
                    $css .= '@media (max-width: 767px) {';
                    $css .= $mobile_css;
                    $css .= '}';
                }
            }

            $speed = (isset($settings->speed) && $settings->speed) ? $settings->speed : 600;

            $css .= $addon_id . ' .jwpf-carousel-inner > .jwpf-item{-webkit-transition-duration: ' . $speed . 'ms; transition-duration: ' . $speed . 'ms;}';

            $show_zsy = (isset($settings->show_zsy) && $settings->show_zsy) ? $settings->show_zsy : 0;
            $img_gd_height = (isset($settings->img_gd_height) && $settings->img_gd_height) ? $settings->img_gd_height : '500';
            $img_gd_height_sm = (isset($settings->img_gd_height_sm) && $settings->img_gd_height_sm) ? $settings->img_gd_height_sm : '500';
            $img_gd_height_xs = (isset($settings->img_gd_height_xs) && $settings->img_gd_height_xs) ? $settings->img_gd_height_xs : '500';

            if ($show_zsy) {

                $img_style_type7 = (isset($settings->img_style_type7) && $settings->img_style_type7) ? $settings->img_style_type7 : 'fill';
                $css .= $addon_id . ' {height:' . $img_gd_height . 'px;overflow:hidden;}';
                $css .= $addon_id . ' .jwpf-item-has-bg img{height:' . $img_gd_height . 'px;object-fit:' . $img_style_type7 . ';}';
                $css .= $addon_id . ' .jwpf-item-has-bg video{height:' . $img_gd_height . 'px;object-fit:' . $img_style_type7 . ';}';

                $css .= '@media (max-width: 991px) {';
                $css .= $addon_id . ' {height:' . $img_gd_height_sm . 'px;overflow:hidden;}';
                $css .= $addon_id . ' .jwpf-item-has-bg img{height:' . $img_gd_height_sm . 'px;object-fit:' . $img_style_type7 . ';}';
                $css .= $addon_id . ' .jwpf-item-has-bg video{height:' . $img_gd_height_sm . 'px;object-fit:' . $img_style_type7 . ';}';
                $css .= '}';

                $css .= '@media (max-width: 767px) {';
                $css .= $addon_id . ' {height:' . $img_gd_height_xs . 'px;overflow:hidden;}';
                $css .= $addon_id . ' .jwpf-item-has-bg img{height:' . $img_gd_height_xs . 'px;object-fit:' . $img_style_type7 . ';}';
                $css .= $addon_id . ' .jwpf-item-has-bg video{height:' . $img_gd_height_xs . 'px;object-fit:' . $img_style_type7 . ';}';
                $css .= '}';
            }
        }

        return $css;
    }


    public function stylesheets()
    {
        $settings = $this->addon->settings;
        $lbys = (isset($settings->lbys) && $settings->lbys) ? $settings->lbys : "lb1"; //布局
        $style_sheet = "";
        if ($lbys == "lb3" || $lbys == "lb5") {
            $style_sheet = array(
                JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css'
            );
        }

        return $style_sheet;
    }
    public function scripts()
    {
        $settings = $this->addon->settings;
        $lbys = (isset($settings->lbys) && $settings->lbys) ? $settings->lbys : "lb1"; //布局
        if ($lbys == "lb2") {
            $js = array(
                JURI::base(true) . '/components/com_jwpagefactory/addons/carousel/assets/js/kl-rotator.js',
            );
        } elseif ($lbys == "lb3" || $lbys == "lb5") {
            $js = array(
                JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            );
        } elseif ($lbys == "lb4") {
            $js = array(
                JURI::base(true) . '/components/com_jwpagefactory/addons/carousel/assets/js/osSlider.js',
            );
        } else {
            $js = array(
                JURI::base(true) . '/components/com_jwpagefactory/addons/carousel/assets/js/bubble-canvas.js'
            );
        }

        return $js;
    }

    public static function getTemplate()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $infoss = JwPageFactoryBase::getBannerLists($site_id, $company_id);
        $atts = json_encode($infoss);

        $output = '
        <#
            var infoss = ' . $atts . ';
            var interval = data.interval ? parseInt(data.interval) * 1000 : 5000;
            var addonId = "#jwpf-addon-" + data.id;
            
            if(data.autoplay==0){
                interval = "false";
            }
            var autoplay = data.autoplay ? \'data-jwpf-ride="jwpf-carousel"\' : "";
            
            var show_zsy = (typeof data.show_zsy !== "undefined" && data.show_zsy) ? data.show_zsy : "0";
            var img_style_type7 = (typeof data.img_style_type7 !== "undefined" && data.img_style_type7) ? data.img_style_type7 : "fill";
            var img_lunb3 = (typeof data.img_lunb3 !== "undefined" && data.img_lunb3) ? data.img_lunb3 : "fill";
            
        #>
            <# if(data.lbys=="lb3"){ #>
                <style>

                    {{addonId}} .i100>img {
                        width: 100%;
                    }
                    {{addonId}} .i100 {
                        overflow: hidden;
                    }
                    {{addonId}} .ind1-a2 img,{{addonId}} .ind1-a3 img{
                        object-fit:{{img_lunb3}};
                    }
                    {{addonId}} .swiper-container-fade .swiper-slide {
           
                        -webkit-transition-property: opacity;
                        -o-transition-property: opacity;
                        transition-property: opacity;
                    }
                    {{addonId}} .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                    }
                    {{addonId}} .swiper-container-android .swiper-slide, .swiper-wrapper {
                        -webkit-transform: translate3d(0,0,0);
                        transform: translate3d(0,0,0);
                    }
                    {{addonId}} .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }

                    /*ind1*/
                    @media only screen and (min-width: 1480px) {
                        {{addonId}} .ind1-a1{
                            width: 100%;
                            <# if(_.isObject(data.qx_height3)) { #>
                                <# if(data.qx_height3.md){ #>
                                    height:{{data.qx_height3.xs}}px;
                                <# }else{ #>
                                    height:930px;
                                <# } #> 
                            <# } #>
                            
                            position: relative;
                            overflow: hidden;
                            z-index: 1;
                        }
                        {{addonId}} .ind111{width: 1920px!important;height: 100%!important;position: relative;margin-left: calc(50% - 1920px/2);}
                        {{addonId}} .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                        {{addonId}} .ind1-a2{width: 100%;height: 100%;position: relative;}
                        {{addonId}} .ind1-a2 img{max-height: 100%;transform: scale(1);transition: 10s;}
                        {{addonId}} .ind111 .swiper-slide-active .ind1-a2 img{transform: scale(1);transition: 10s;}
                        {{addonId}} .ind1-a3{display: none;}
                        {{addonId}} .ind1-a4{width: 480px;position: absolute;top: 230px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;font-family: "SourceHanSansCN-Bold-Alphabetic";}
                        {{addonId}} .ind1-a5{position: absolute;top: 354px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;font-family: "SourceHanSansCN-ExtraLight-Alphabetic";}
                        {{addonId}} .ind1-a6{position: absolute;top: 512px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a6 span{font-family: "SourceHanSansCN-Normal-Alphabetic";}
                        {{addonId}} .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;font-family: "Herr Von Muellerhoff";}
                        {{addonId}} .ind1-a7{position: absolute;top: 614px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a8{width: 800px;position: absolute;top: 196px;left: calc(50% + 30px);}
                        {{addonId}} .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}
                        {{addonId}} .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        {{addonId}} .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}
                    }
                    @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                        {{addonId}} .ind1-a1{width: 100%;
          
                            <# if(_.isObject(data.qx_height3)) { #>
                                <# if(data.qx_height3.md){ #>
                                    height:{{data.qx_height3.md}}px;
                                <# }else{ #>
                                    height:930px;
                                <# } #> 
                            <# } #>

                            position: relative;
                            overflow: hidden;z-index: 1;}
                        {{addonId}} .ind111{width: 1480px!important;height: 100%!important;position: relative;margin-left: calc(50% - 1480px/2);}
                        {{addonId}} .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                        {{addonId}} .ind1-a2{width: 100%;height: 100%;position: relative;}
                        {{addonId}} .ind1-a2 img{height: 100%;transform: scale(1);transition: 10s;}
                        {{addonId}} .ind111 .swiper-slide-active .ind1-a2 img{transform: scale(1);transition: 10s;}
                        {{addonId}} .ind1-a3{display: none;}
                        {{addonId}} .ind1-a4{width: 360px;position: absolute;top: 180px;left: calc(50% - 960px/2);}
                        {{addonId}} .ind1-a5{font-size: 90px;line-height: 90px;color: #cba67c;font-family: "SourceHanSansCN-Bold-Alphabetic";}
                        {{addonId}} .ind1-a5{position: absolute;top: 270px;left: calc(50% - 960px/2);}
                        {{addonId}} .ind1-a6{font-size: 54px;line-height: 54px;color: #cba67c;font-family: "SourceHanSansCN-ExtraLight-Alphabetic";}
                        {{addonId}} .ind1-a6{position: absolute;top: 400px;left: calc(50% - 960px/2);}
                        {{addonId}} .ind1-a6 span{font-family: "SourceHanSansCN-Normal-Alphabetic";}
                        {{addonId}} .ind1-a7{font-size: 44px;line-height: 44px;color: #cba67c;font-family: "Herr Von Muellerhoff";}
                        {{addonId}} .ind1-a7{position: absolute;top: 500px;left: calc(50% - 960px/2);}
                        {{addonId}} .ind1-a8{width: 500px;position: absolute;top: 180px;left: calc(50% + 30px);}
                        {{addonId}} .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}
                        {{addonId}} .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        {{addonId}} .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}
                    }

                    @media only screen and (max-width: 1023px) {
                        {{addonId}} .ind1-a1{
                            width: 100%;
                            <# if(_.isObject(data.qx_height3)) { #>
                                <# if(data.qx_height3.sm){ #>
                                    height:{{data.qx_height3.sm}}px;
                                <# }else{ #>
                                    height:1326px;
                                <# } #> 
                            <# } #>

                            position: relative;
                            overflow: hidden;z-index: 1;
                        }
                        {{addonId}} .ind111{width: 100%!important;height: 100%!important;position: relative}
                        {{addonId}} .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                        {{addonId}} .ind1-a2{display: none;}
                        {{addonId}} .ind1-a3{width: 100%;height: 100%;position: relative;}
                        {{addonId}} .ind1-a3 img{height: 100%;transform: scale(1.2);transition: 10s;}
                        {{addonId}} .ind111 .swiper-slide-active .ind1-a3 img{transform: scale(1);transition: 10s;}
                        {{addonId}} .ind1-a4{width: 480px;position: absolute;top: 190px;left: 56px;}
                        {{addonId}} .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;font-family: "SourceHanSansCN-Bold-Alphabetic";}
                        {{addonId}} .ind1-a5{position: absolute;top: 310px;left: 48px;}
                        {{addonId}} .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;font-family: "SourceHanSansCN-ExtraLight-Alphabetic";}
                        {{addonId}} .ind1-a6{position: absolute;top: 470px;left: 50px;}
                        {{addonId}} .ind1-a6 span{font-family: "SourceHanSansCN-Normal-Alphabetic";}
                        {{addonId}} .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;font-family: "Herr Von Muellerhoff";}
                        {{addonId}} .ind1-a7{position: absolute;top: 580px;left: 40px;}
                        {{addonId}} .ind1-a8{width: 670px;position: absolute;top: 680px;left: calc(50% - 670px/2);}
                        {{addonId}} .ind112{width: 100%;position: absolute;bottom: 56px!important;left: 0;}
                        {{addonId}} .ind112 .swiper-pagination-bullet{width: 18px;height: 18px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 14px;opacity: 0.5;transition: 0.5s;}
                        {{addonId}} .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}
                    }
                    @media only screen and (max-width: 720px) {
                        {{addonId}} .ind1-a1{
                            width: 100%;
                            <# if(_.isObject(data.qx_height3)) { #>
                                <# if(data.qx_height3.xs){ #>
                                    height:{{data.qx_height3.xs}}px;
                                <# }else{ #>
                                    height:720px;
                                <# } #> 
                            <# } #>

                            position: relative;
                            overflow: hidden;
                            z-index: 1;
                        }
                        {{addonId}} .ind111{width: 100%!important;height: 100%!important;position: relative}
                        {{addonId}} .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                        {{addonId}} .ind1-a2{display: none;}
                        {{addonId}} .ind1-a3{width: 100%;height: 100%;position: relative;}
                        {{addonId}} .ind1-a3 img{height: 100%;transform: scale(1.2);transition: 10s;}
                        {{addonId}} .ind111 .swiper-slide-active .ind1-a3 img{transform: scale(1);transition: 10s;}
                        {{addonId}} .ind1-a4{width: 345px;position: absolute;top: 136px;left: 40px;}
                        {{addonId}} .ind1-a5{font-size: 86px;line-height: 86px;color: #cba67c;font-family: "SourceHanSansCN-Bold-Alphabetic";}
                        {{addonId}} .ind1-a5{position: absolute;top: 223px;left: 34px;}
                        {{addonId}} .ind1-a6{font-size: 51px;line-height: 51px;color: #cba67c;font-family: "SourceHanSansCN-ExtraLight-Alphabetic";}
                        {{addonId}} .ind1-a6{position: absolute;top: 338px;left: 36px;}
                        {{addonId}} .ind1-a6 span{font-family: "SourceHanSansCN-Normal-Alphabetic";}
                        {{addonId}} .ind1-a7{font-size: 41px;line-height: 41px;color: #cba67c;font-family: "Herr Von Muellerhoff";}
                        {{addonId}} .ind1-a7{position: absolute;top: 417px;left: 28px;}
                        {{addonId}} .ind1-a8{width: 482px;position: absolute;top: 489px;left: calc(50% - 482px/2);}
                        {{addonId}} .ind112{width: 100%;position: absolute;bottom: 30px!important;left: 0;}
                        {{addonId}} .ind112 .swiper-pagination-bullet{width: 10px;height: 10px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        {{addonId}} .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}
                    }
                    @media only screen and (min-width: 1921px) {
                        {{addonId}} .ind1-a1{
                            width: 100%;
                            <# if(_.isObject(data.qx_height3)) { #>
                                <# if(data.qx_height3.md){ #>
                                    height:{{data.qx_height3.md}}px;
                                <# }else{ #>
                                    height:930px;
                                <# } #> 
                            <# } #>

                            position: relative;
                            overflow: hidden;
                            z-index: 1;
                        }
                        {{addonId}} .ind111{width: 2560px!important;height: 100%!important;position: relative;margin-left: calc(50% - 2560px/2);}
                        {{addonId}} .ind111 .swiper-slide{width: 100%!important;height: 100%!important;position: relative;}
                        {{addonId}} .ind1-a2{width: 100%;height: 100%;position: relative;}
                        {{addonId}} .ind1-a2 img{max-height: 100%;height: auto;position: absolute;bottom: -150px;left: 0;}
                        {{addonId}} .ind1-a3{display: none;}
                        {{addonId}} .ind1-a4{width: 480px;position: absolute;top: 230px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a5{font-size: 120px;line-height: 120px;color: #cba67c;font-family: "SourceHanSansCN-Bold-Alphabetic";}
                        {{addonId}} .ind1-a5{position: absolute;top: 354px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a6{font-size: 72px;line-height: 72px;color: #cba67c;font-family: "SourceHanSansCN-ExtraLight-Alphabetic";}
                        {{addonId}} .ind1-a6{position: absolute;top: 512px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a6 span{font-family: "SourceHanSansCN-Normal-Alphabetic";}
                        {{addonId}} .ind1-a7{font-size: 58px;line-height: 58px;color: #cba67c;font-family: "Herr Von Muellerhoff";}
                        {{addonId}} .ind1-a7{position: absolute;top: 614px;left: calc(50% - 1440px/2);}
                        {{addonId}} .ind1-a8{width: 800px;position: absolute;top: 196px;left: calc(50% + 30px);}
                        {{addonId}} .ind112{width: 100%;position: absolute;bottom: 40px!important;left: 0;}
                        {{addonId}} .ind112 .swiper-pagination-bullet{width: 16px;height: 16px;background: #b3a36b;position: relative;border-radius: 50%;margin: 0 10px;opacity: 0.5;transition: 0.5s;}
                        {{addonId}} .ind112 .swiper-pagination-bullet-active{opacity: 1;transition: 0.5s;}
                    }
                </style>
                <div class="ind1-a1">
                    <div class="swiper-container ind111 swiper-container-fade swiper-container-horizontal">
                        <div class="swiper-wrapper" style="transition-duration: 0ms;">
                            <# _.each(data.jw_carousel_item3, function (carousel_item, key){ #>
                                <div class="swiper-slide swiper-slide-duplicate swiper-slide-duplicate-next" data-swiper-slide-index="4" style="width: 1480px; transition-duration: 0ms; opacity: 1; transform: translate3d(0px, 0px, 0px);">
                                    <div class="ind1-a2 i100"><img src=\'{{carousel_item.bg}}\'></div>
                                    <div class="ind1-a3 i100"><img src=\'{{carousel_item.phone_bg}}\'></div>
                                </div>
                            <# }); #>
                        </div>
                    </div>
                    <div class="swiper-pagination ind112 swiper-pagination-clickable swiper-pagination-bullets">
                        <span class="swiper-pagination-bullet"></span>
                        <span class="swiper-pagination-bullet"></span>
                        <span class="swiper-pagination-bullet"></span>
                        <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                        <span class="swiper-pagination-bullet"></span>
                    </div>
                </div>

            <# }else if(data.lbys=="lb2"){ #>
                <style>
                    {{addonId}} body {margin: 0;}
                    {{addonId}} html, body {width: 100%;height: 100%;}
                    {{addonId}} p {
                        font-size: 13px;
                        line-height: 19px;
                        letter-spacing: 0px;
                    }
                    
                    {{addonId}} img {
                        width: 100%;
                        height: auto;
                    }

                    {{addonId}} .rotator-full {
                        height: 100%;
                        font-size: 0px;
                        z-index: 5;
                    }
                    
                    {{addonId}} .rotator-standard {
                        padding:30px 0;background:#ebebeb;
                        width: 100%;
                        font-size: 0;
                    }
                    {{addonId}} .rotator-standard .rotator-image{
                        background-size:100% 100%;
                    }
                    
                    {{addonId}} .rotator-full .rowa {
                        max-width: 100%;
                        width: 100%;
                        height: 100%;
                    }
                    
                    {{addonId}} .rotator-full-short .rowa {
                        max-width: 100%;
                        height: 100%;
                        width: 100%;
                    }
                    
                    {{addonId}} .rotator-standard .rowa {
                        height: 100%;
                        width: 100%;
                    }
                    
                    {{addonId}} .rotator {
                        height: 100%;
                        display: inherit;
                        position: relative;
                        z-index: 1;
                    }
                    
                    {{addonId}} .rotator .rotatorText {
                        display: none;
                    }
                    
                    {{addonId}} .rotator .rotatorBullets {
                        position: absolute;
                        left: 0px;
                        right: 0px;
                        bottom: 55px;
                        z-index: 100;
                        display: none;
                        height: 9px;
                        margin: 0px auto;
                        text-align: center;
                    }
                    
                    {{addonId}} .rotator-standard .rotator .rotatorBullets {
                        bottom: 30px;
                    }
                    
                    {{addonId}} .rotator .rotatorBullets ul {
                        padding: 0px;
                        margin: 0px;
                    }
                    
                    {{addonId}} .rotator .rotatorBullets ul li {
                        width: 14px;
                        height: 14px;
                        background: url(https://oss.lcweb01.cn/joomla/20220601/cb7a60739b8ac3770f2e166b64e6cf19.png) no-repeat;
                        display: inline-block;
                        margin-right: 8px;
                        padding: 0;
                        cursor: pointer;
                        opacity: 1;
                    }
                    {{addonId}} .rotator .rotatorBullets ul li:first-child {
                        margin-left: 10px;
                    }
                    {{addonId}} .rotator .rotatorBullets ul li.selected {
                        background: url(https://oss.lcweb01.cn/joomla/20220601/236879530f085c9faba2c2f8270e853a.png);
                        opacity: 1;
                    }
                    
                    {{addonId}} .rotatorNav {
                        position: absolute;
                        z-index: 90;
                        color: #FFF;
                        cursor: pointer;
                        display: none;
                    }
                    
                    {{addonId}} .rotatorPrev {
                        top: 45%;
                        left: 40px;
                        text-indent: -9999px;
                        width: 23px;
                        height: 33px;
                    }
                    
                    {{addonId}} .rotatorNext {
                        top: 45%;
                        right: 40px;
                        text-indent: -9999px;
                        width: 23px;
                        height: 33px;
                    }
                    
                    {{addonId}} .rotator-image {
                        background: #381809;
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                        z-index: 5;
                        background-size: cover;
                        background-position: 50% 50%;
                        background-repeat: no-repeat;
                        display: none;
                        top: 0px;
                        left: 0px;
                    }
                    
                    {{addonId}} .rotator-full .rotator-image {
                        height: calc(100% - {{data.lb2_topheight}}px);
                        top: {{data.lb2_topheight}}px;
                    }
                    {{addonId}} .rotator-full .rotator-screen {
                        background: #381809;
                        width: 100%;
                        overflow: hidden;
                        z-index: 8;
                        background-size: cover;
                        background-position: 50% 50%;
                        background-repeat: no-repeat;
                        left: 0px;
                        height: calc(100% - {{data.lb2_topheight}}px);
                        top: {{data.lb2_topheight}}px;
                    }
                    {{addonId}} .rotator-full .rotator-screen .cover {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        top: 0px;
                        left: 0px;
                    }
                    {{addonId}} .rotator-full .rotator-screen .cover h1, {{addonId}} .rotator-full .rotator-screen .cover p {
       
                        position: absolute;
                        width: 100%;
                        left: 0%;
                        top: 45%;
                        text-align: center;
                        text-transform: uppercase;
                    }
                    {{addonId}} .rotator-full .rotator-screen .cover h1 {
                        font-family: ScalaWebPro, serif;
                        font-weight: 400;
                        font-size: 18px;
                        line-height: 24px;
                        color: #f9f8f8;
                        opacity: 0.7;
                        margin-top: -40px;
                        letter-spacing: 3px;
                    }
                    {{addonId}} .rotator-full .rotator-screen .cover p {
                        font-weight: 300;
                        font-size: {{data.lb2_fontsize}}px;
                        line-height: 44px;
                        color: white;
                        letter-spacing: 5px;
                    }
                </style>
                <div class="containera rotator-full">
                    <div class="rowa">
                        <div class="rotator" id="rotator2094259376">
                        
                            <# _.each(data.jw_carousel_item2, function (carousel_item, key){ #>
                                <# if(key==0){ #>

                                    <# if(carousel_item.text_from && carousel_item.text_from == 1 && carousel_item.text_id) {
                                        let newArr = infoss.filter(item => item.id == carousel_item.text_id)
                                        // console.log(newArr)
                                        var item_ = ""
                                        if(newArr.length > 0) {
                                            item_ = newArr[0]
                                            carousel_item.title = item_.banner_title
                                            carousel_item.content = item_.introtext

                                            if(item_.image_intro){
                                                carousel_item.bg = item_.image_intro
                                            }else{
                                                carousel_item.bg ="https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg";
                                            }
                                        }
                                        
                                    } #>

                                    <# if(carousel_item.bg && carousel_item.bg.indexOf("http://") == -1 && carousel_item.bg.indexOf("https://") == -1){ #>
                                 
                                        <div class="rotator-screen">
                                            <img src=\'{{ pagefactory_base + carousel_item.bg }}\' >
                                            <div class="cover">
                                                <p>{{data.lb2_title}}</p>
                                            </div>
                                        </div>             
                                    <# } else if(carousel_item.bg){ #>
                                    
                                        <div class="rotator-screen" style="background-image: url(\'{{carousel_item.bg}}\');">
                                            <img src=\'{{ carousel_item.bg }}\' >
                                            <div class="cover">
                                                <p>{{data.lb2_title}}</p>
                                            </div>
                                        </div>                     
                                    <# } #>
                                <# } #>

                            <# }); #>

                            
                            <div class="rotatorPrev rotatorNav">PREV</div>
                            <div class="rotatorNext rotatorNav">NEXT</div>
                            <div class="rotatorBullets bullets" id="rotator2094259376-bullets"></div>
                            <div class="rotator-down"></div>
                        </div>
                    </div>
                </div>
            <# }else if(data.lbys=="lb4"){
                // 轮播项
                var jw_carousel_item04 = data.jw_carousel_item04 || [
                    {
                        text_form: 0,
                        bg: "https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg",
                        media_url_show: 0
                    }
                ];
                // 轮播高度
                var img_height04_md = toChangeNum("img_height04").md || "602", 
                    img_height04_sm = toChangeNum("img_height04").sm || "334", 
                    img_height04_xs = toChangeNum("img_height04").xs || "186";
                // 图片填充方式
                var img_fit_04 = data.img_fit_04 || "cover";
                
                // 是否开启切换按钮
                var is_swiper_button = data.is_swiper_button;
                console.log(is_swiper_button, "is_swiper_button")
                // 上翻页按钮
                var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png";
                // 下翻页按钮
                var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png";
                // 切换按钮宽度
                var swiper_button_width_md = toChangeNum("swiper_button_width").md || "40", 
                    swiper_button_width_sm = toChangeNum("swiper_button_width").sm, 
                    swiper_button_width_xs = toChangeNum("swiper_button_width").xs;
                // 切换按钮高度
                var swiper_button_height_md = toChangeNum("swiper_button_height").md || "40", 
                    swiper_button_height_sm = toChangeNum("swiper_button_height").sm, 
                    swiper_button_height_xs = toChangeNum("swiper_button_height").xs;
                // 切换按钮上边距（百分比）
                var swiper_button_top_md = toChangeNum("swiper_button_top").md || "48", 
                    swiper_button_top_sm = toChangeNum("swiper_button_top").sm, 
                    swiper_button_top_xs = toChangeNum("swiper_button_top").xs;
                // 切换按钮 上边距单位
                var swiper_button_top_unit = data.is_swiper_button_top_px && data.is_swiper_button_top_px == 1 ? "px" : "%";
                // 切换按钮两侧边距（px）
                var swiper_button_left_md = toChangeNum("swiper_button_left").md || "10", 
                    swiper_button_left_sm = toChangeNum("swiper_button_left").sm, 
                    swiper_button_left_xs = toChangeNum("swiper_button_left").xs;

                // 是否开启轮播点
                var is_swiper_pagination = data.is_swiper_pagination;
                console.log(is_swiper_pagination, "is_swiper_pagination")
                // 轮播点底部间距
                var swiper_pagination_bottom_md = toChangeNum("swiper_pagination_bottom").md || "10", 
                    swiper_pagination_bottom_sm = toChangeNum("swiper_pagination_bottom").sm, 
                    swiper_pagination_bottom_xs = toChangeNum("swiper_pagination_bottom").xs;
                // 轮播点宽度
                var swiper_p_width_md = toChangeNum("swiper_p_width").md || "23", 
                    swiper_p_width_sm = toChangeNum("swiper_p_width").sm, 
                    swiper_p_width_xs = toChangeNum("swiper_p_width").xs;
                // 轮播点高度
                var swiper_p_height_md = toChangeNum("swiper_p_height").md || "5", 
                    swiper_p_height_sm = toChangeNum("swiper_p_height").sm, 
                    swiper_p_height_xs = toChangeNum("swiper_p_height").xs;
                // 轮播点间距
                var swiper_p_margin_md = toChangeNum("swiper_p_margin").md, 
                    swiper_p_margin_sm = toChangeNum("swiper_p_margin").sm, 
                    swiper_p_margin_xs = toChangeNum("swiper_p_margin").xs;
                swiper_p_margin_md = swiper_p_margin_md ? (swiper_p_margin_md / 2) : "5";
                swiper_p_margin_sm = swiper_p_margin_sm ? (swiper_p_margin_sm / 2) : "";
                swiper_p_margin_xs = swiper_p_margin_xs ? (swiper_p_margin_xs / 2) : "";
                // 轮播点颜色
                var swiper_p_color = data.swiper_p_color || "#fff";
                // 选中轮播点宽度
                var swiper_p_width_a_md = toChangeNum("swiper_p_width_a").md, 
                    swiper_p_width_a_sm = toChangeNum("swiper_p_width_a").sm, 
                    swiper_p_width_a_xs = toChangeNum("swiper_p_width_a").xs;
                // 选中轮播点高度
                var swiper_p_height_a_md = toChangeNum("swiper_p_height_a").md, 
                    swiper_p_height_a_sm = toChangeNum("swiper_p_height_a").sm, 
                    swiper_p_height_a_xs = toChangeNum("swiper_p_height_a").xs;
                // 选中轮播点颜色
                var swiper_p_color_a = data.swiper_p_color_a || "#007aff";

                // 处理适配
                function toChangeNum(key) {
                    var md = "", sm = "", xs = "";
                    if(_.isObject(data[key])){
                        md = data[key].md || ""; sm = data[key].sm || ""; xs = data[key].xs || "";
                    }
                    return { md: md, sm: sm, xs: xs }
                }
            #>
                <style type="text/css">
                    {{addonId}} ul,
                    {{addonId}} li,
                    {{addonId}} ol {
                        list-style: none;
                    }
                    {{addonId}} .cvNode>img {
                        max-width: initial !important;
                        object-fit: {{img_fit_04}};
                    }
                    {{addonId}} .os-slider {
                        width: 100%;
                        height: {{img_height04_md}}px;
                    }
                    {{addonId}} .os-slider-main,
                    {{addonId}} .os-slider-main li {
                        padding: 0;
                        margin: 0;
                        width: 100%;
                        height: 100%;
                    }
                    {{addonId}} .os-slider-main li>a>img,
                    {{addonId}} .os-slider-main li>img {
                        width: 100%;
                        height: 100%;
                        object-fit: {{img_fit_04}};
                    }
                    /* 轮播点 样式 */
                    {{addonId}} .osSlider-main .slider-nav {
                        position: absolute;
                        width: 100%;
                        bottom: {{swiper_pagination_bottom_md}}px;
                        text-align: center;
                        z-index: 25;
                        padding: 0;
                        margin: 0;
                        <#if(is_swiper_pagination != 1) {#>
                            display: none;
                        <#}#>
                    }
                    {{addonId}} .osSlider-main .slider-nav li {
                        display: inline-block;
                        width: {{swiper_p_width_md}}px;
                        height: {{swiper_p_height_md}}px;
                        border-radius: 50px;
                        cursor: pointer;
                        background: {{swiper_p_color}};
                        box-sizing: border-box;
                        vertical-align: middle;
                        text-indent: -9999px;
                        overflow: hidden;
                        margin: 0 {{swiper_p_margin_md}}px;
                    }
                    {{addonId}} .osSlider-main .slider-nav li.active {
                        width: {{swiper_p_width_a_md}}px;
                        height: {{swiper_p_height_a_md}}px;
                        background: {{swiper_p_color_a}};
                    }
                    /* 切换按钮 */
                    {{addonId}} .osSlider-main .slider-btn-prev,
                    {{addonId}} .osSlider-main .slider-btn-next {
                        position: absolute;
                        cursor: pointer;
                        z-index: 30;
                        top: {{swiper_button_top_md}}{{swiper_button_top_unit}};
                        width: {{swiper_button_width_md}}px;
                        height: {{swiper_button_height_md}}px;
                        text-indent: -9999px;
                        transition: all .5s linear;
                        <# if (is_swiper_button != 1) { #>
                            display: none;
                        <# } #>
                    }
                    /* 上一个 */
                    {{addonId}} .osSlider-main .slider-btn-prev {
                        left: {{swiper_button_left_md}}px;
                        background: url({{swiper_button_prev}}) no-repeat;
                        background-size: contain;
                    }
                    {{addonId}} .osSlider-main .slider-btn-prev:hover {
                        <#if(data.swiper_button_prev_hover != 1) {#>
                            background-image: url({{data.swiper_button_prev_hover}});
                        <#}#>
                    }
                    /* 下一个 */
                    {{addonId}} .osSlider-main .slider-btn-next {
                        right: {{swiper_button_left_md}}px;
                        background: url({{swiper_button_next}}) no-repeat;
                        background-size: contain;
                    }
                    {{addonId}} .osSlider-main .slider-btn-next:hover {
                        <#if(data.swiper_button_next_hover != 1) {#>
                            background-image: url({{data.swiper_button_next_hover}});
                        <#}#>
                    }

                    @media (min-width: 768px) and (max-width: 991px) {
                        {{addonId}} .os-slider {
                            height: {{img_height04_sm}}px;
                        }
                        /* 轮播点 样式 */
                        {{addonId}} .osSlider-main .slider-nav {
                            bottom: {{swiper_pagination_bottom_sm}}px;
                        }
                        {{addonId}} .osSlider-main .slider-nav li {
                            width: {{swiper_p_width_sm}}px;
                            height: {{swiper_p_height_sm}}px;
                            margin: 0 {{swiper_p_margin_sm}}px;
                        }
                        {{addonId}} .osSlider-main .slider-nav li.active {
                            width: {{swiper_p_width_a_sm}}px;
                            height: {{swiper_p_height_a_sm}}px;
                        }
                        /* 切换按钮 */
                        {{addonId}} .osSlider-main .slider-btn-prev,
                        {{addonId}} .osSlider-main .slider-btn-next {
                            top: {{swiper_button_top_sm}}{{swiper_button_top_unit}};
                            width: {{swiper_button_width_sm}}px;
                            height: {{swiper_button_height_sm}}px;
                        }
                        /* 上一个 */
                        {{addonId}} .osSlider-main:hover .slider-btn-prev {
                            left: {{swiper_button_left_sm}}px;
                        }
                        /* 下一个 */
                        {{addonId}} .osSlider-main:hover .slider-btn-next {
                            right: {{swiper_button_left_sm}}px;
                        }
                    }
                    @media (max-width: 767px) {
                        {{addonId}} .os-slider {
                            height: {{img_height04_xs}}}px;
                        }
                        /* 轮播点 样式 */
                        {{addonId}} .osSlider-main .slider-nav {
                            bottom: {{swiper_pagination_bottom_xs}}px;
                        }
                        {{addonId}} .osSlider-main .slider-nav li {
                            width: {{swiper_p_width_xs}}px;
                            height: {{swiper_p_height_xs}}px;
                            margin: 0 {{swiper_p_margin_xs}}px;
                        }
                        {{addonId}} .osSlider-main .slider-nav li.active {
                            width: {{swiper_p_width_a_xs}}px;
                            height: {{swiper_p_height_a_xs}}px;
                        }
                        /* 切换按钮 */
                        {{addonId}} .osSlider-main .slider-btn-prev,
                        {{addonId}} .osSlider-main .slider-btn-next {
                            top: {{swiper_button_top_xs}}{{swiper_button_top_unit}};
                            width: {{swiper_button_width_xs}}px;
                            height: {{swiper_button_height_xs}}px;
                        }
                        /* 上一个 */
                        {{addonId}} .osSlider-main:hover .slider-btn-prev {
                            left: {{swiper_button_left_xs}}px;
                        }
                        /* 下一个 */
                        {{addonId}} .osSlider-main:hover .slider-btn-next {
                            right: {{swiper_button_left_xs}}px;
                        }
                    }
                </style>
                <p class="alert alert-warning" style="margin-bottom: 10px;">编辑页仅为布局样式，请在预览页面中查看该插件切换效果</p>
                <div class="os-slider osSlider-main">
                    <ul class="os-slider-main">
                        <# _.each(jw_carousel_item04, function (carousel_item, key){ 
                            if(!carousel_item.bg) carousel_item.bg = "https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg";
                        #>
                            <# if(key == 0) { #>
                            <li><img src=\'{{carousel_item.bg}}\'></li>
                            <# } #>
                        <# }) #>
                    </ul>
                    <ul class="slider-btn">
                        <li class="slider-btn-prev">prev</li>
                        <li class="slider-btn-next">next</li>
                    </ul>
                    <ul class="slider-nav">
                        <li class="active"">1</li>
                        <li class="">2</li>
                        <li class="">3</li>
                    </ul>
                </div>
            <# }else if(data.lbys=="lb5"){
                // 轮播项
                var jw_carousel_item05 = data.jw_carousel_item05 || [
                    {
                        text_form: 0,
                        bg: "https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg",
                        title: "这里是标题",
                        media_url_show: 0
                    }
                ];
                // 开启固定宽高
                var show_zsy = data.show_zsy || 0;
                // 轮播高度
                var img_gd_height = toChangeNum("img_gd_height").md || "500",
                    img_gd_height_sm = toChangeNum("img_gd_height").sm || "",
                    img_gd_height_xs = toChangeNum("img_gd_height").xs || "";
                // 图片填充方式
                var img_style_type7 = data.img_style_type7 || "fill";
                
                /* 标题配置 */
                // 标题背景颜色
                var title_bgcolor05 = data.title_bgcolor05 || "rgba(0, 0, 0, 0.64)";
                // 标题文字大小
                var title_fontsize05_md = toChangeNum("title_fontsize05").md || "18",
                    title_fontsize05_sm = toChangeNum("title_fontsize05").sm,
                    title_fontsize05_xs = toChangeNum("title_fontsize05").xs;
                // 标题文字行高
                var title_lineHeight05_md = toChangeNum("title_lineHeight05").md || "52",
                    title_lineHeight05_sm = toChangeNum("title_lineHeight05").sm,
                    title_lineHeight05_xs = toChangeNum("title_lineHeight05").xs;
                // 标题文字颜色
                var title_color05 = data.title_color05 || "#FFFFFF";
                // 标题文字内边距
                var title_padding05_md = toChangeNum("title_padding05").md || "0 200px 0 20px",
                    title_padding05_sm = toChangeNum("title_padding05").sm,
                    title_padding05_xs = toChangeNum("title_padding05").xs;
                
                /* 轮播点样式 */
                // 是否开启轮播点
                var is_swiper_pagination05 = data.is_swiper_pagination05;
                // 轮播点宽度
                var swiper_p_width05_md = toChangeNum("swiper_p_width05").md || "18", 
                    swiper_p_width05_sm = toChangeNum("swiper_p_width05").sm, 
                    swiper_p_width05_xs = toChangeNum("swiper_p_width05").xs;
                // 轮播点高度
                var swiper_p_height05_md = toChangeNum("swiper_p_height05").md || "7", 
                    swiper_p_height05_sm = toChangeNum("swiper_p_height05").sm, 
                    swiper_p_height05_xs = toChangeNum("swiper_p_height05").xs;
                // 轮播点间距
                var swiper_p_margin05_md = toChangeNum("swiper_p_margin05").md, 
                    swiper_p_margin05_sm = toChangeNum("swiper_p_margin05").sm, 
                    swiper_p_margin05_xs = toChangeNum("swiper_p_margin05").xs;
                swiper_p_margin05_md = swiper_p_margin05_md ? (swiper_p_margin05_md / 2) : "5";
                swiper_p_margin05_sm = swiper_p_margin05_sm ? (swiper_p_margin05_sm / 2) : "";
                swiper_p_margin05_xs = swiper_p_margin05_xs ? (swiper_p_margin05_xs / 2) : "";
                // 轮播点圆角
                var swiper_p_border05_md = toChangeNum("swiper_p_border05").md || "1", 
                    swiper_p_border05_sm = toChangeNum("swiper_p_border05").sm, 
                    swiper_p_border05_xs = toChangeNum("swiper_p_border05").xs;
                // 轮播点颜色
                var swiper_p_color05 = data.swiper_p_color05 || "rgba(255,255,255,0.52)";
                // 选中轮播点宽度
                var swiper_p_width_a05_md = toChangeNum("swiper_p_width_a05").md, 
                    swiper_p_width_a05_sm = toChangeNum("swiper_p_width_a05").sm, 
                    swiper_p_width_a05_xs = toChangeNum("swiper_p_width_a05").xs;
                // 选中轮播点高度
                var swiper_p_height_a05_md = toChangeNum("swiper_p_height_a05").md, 
                    swiper_p_height_a05_sm = toChangeNum("swiper_p_height_a05").sm, 
                    swiper_p_height_a05_xs = toChangeNum("swiper_p_height_a05").xs;
                // 选中轮播点颜色
                var swiper_p_color_a05 = data.swiper_p_color_a05 || "#FFFFFF";

                // 处理适配
                function toChangeNum(key) {
                    var md = "", sm = "", xs = "";
                    if(_.isObject(data[key])){
                        md = data[key].md || ""; sm = data[key].sm || ""; xs = data[key].xs || "";
                    }
                    return { md: md, sm: sm, xs: xs }
                }
            #>
                <style>
                    <# if (show_zsy == 1) { #>
                        {{addonId}} .swiper-container {
                            height: {{img_gd_height}}px;
                        }
                        {{addonId}} .swiper-container .swiper-slide .img-box {
                            width: 100%;
                            height: 100%;
                        }
                        {{addonId}} .swiper-container .swiper-slide .img-box img {
                            width: 100%;
                            height: 100%;
                            object-fit: {{img_style_type7}};
                        }
                    <# } #>
                    {{addonId}} .swiper-slide .title {
                        position: absolute;
                        width: 100%;
                        height: {{title_lineHeight05_md}}px;
                        line-height: {{title_lineHeight05_md}}px;
                        padding: {{title_padding05_md}};
                        box-sizing: border-box;
                        background-color: {{title_bgcolor05}};
                        bottom: 0;
                        left: 0;
                        color: {{title_color05}};
                        font-size: {{title_fontsize05_md}}px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin: 0;
                    }
                    /* 轮播点 */
                    {{addonId}} .swiper-container .swiper-pagination {
                        width: max-content;
                        right: 20px;
                        left: inherit;
                        height: {{title_lineHeight05_md}}px;
                        bottom: 0;
                        display: flex;
                        align-items: center;
                        <# if (is_swiper_pagination05 != 1) { #>
                        display: none;
                        <# } #>
                    }
                    {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                        width: {{swiper_p_width05_md}}px;
                        height: {{swiper_p_height05_md}}px;
                        border-radius: {{swiper_p_border05_md}}px;
                        background: {{swiper_p_color05}};
                        margin: 0 {{swiper_p_margin05_md}}px;
                        opacity: 1;
                    }
                    {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                        width: {{swiper_p_width_a05_md}}px;
                        height: {{swiper_p_height_a05_md}}px;
                        background: {{swiper_p_color_a05}};
                    }
                    @media (min-width: 768px) and (max-width: 991px) {
                        <# if (show_zsy == 1) { #>
                            {{addonId}} .swiper-container {
                                height: {{img_gd_height_sm}}px;
                            }
                        <# } #>
                        {{addonId}} .swiper-slide .title {
                            height: {{title_lineHeight05_sm}}px;
                            line-height: {{title_lineHeight05_sm}}px;
                            padding: {{title_padding05_sm}};
                            font-size: {{title_fontsize05_sm}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination {
                            height: {{title_lineHeight05_sm}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                            width: {{swiper_p_width05_sm}}px;
                            height: {{swiper_p_height05_sm}}px;
                            border-radius: {{swiper_p_border05_sm}}px;
                            margin: 0 {{swiper_p_margin05_sm}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                            width: {{swiper_p_width_a05_sm}}px;
                            height: {{swiper_p_height_a05_sm}}px;
                        }
                    }
                    @media (max-width: 767px) {";
                        <# if (show_zsy == 1) { #>
                            {{addonId}} .swiper-container {
                                height: {{img_gd_height_xs}}px;
                            }
                        <# } #>
                        {{addonId}} .swiper-slide .title {
                            height: {{title_lineHeight05_xs}}px;
                            line-height: {{title_lineHeight05_xs}}px;
                            padding: {{title_padding05_xs}};
                            font-size: {{title_fontsize05_xs}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination {
                            height: {{title_lineHeight05_xs}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                            width: {{swiper_p_width05_xs}}px;
                            height: {{swiper_p_height05_xs}}px;
                            border-radius: {{swiper_p_border05_xs}}px;
                            margin: 0 {{swiper_p_margin05_xs}}px;
                        }
                        {{addonId}} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                            width: {{swiper_p_width_a05_xs}}px;
                            height: {{swiper_p_height_a05_xs}}px;
                        }
                    }
                </style>
                <p class="alert alert-warning" style="margin-bottom: 10px;">编辑页仅为布局样式，请在预览页面中查看该插件切换效果</p>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <# _.each(jw_carousel_item05, function (carousel_item, key){ 
                            if(!carousel_item.bg) carousel_item.bg = "https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg";
                        #>
                            <# if(key == 0) { #>
                            <div class="swiper-slide">
                                <div class="img-box">
                                    <img src=\'{{carousel_item.bg}}\'>
                                </div>
                                <p class="title">{{carousel_item.title}}</p>
                            </div>
                            <# } #>
                        <# }) #>
                    </div>
                    <div class="swiper-pagination">
                        <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                        <span class="swiper-pagination-bullet"></span>
                        <span class="swiper-pagination-bullet"></span>
                    </div>
                </div>
            <# }else{ #> 

                <style type="text/css">
                    #jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
                        -webkit-transition-duration: {{ data.speed }}ms;
                        transition-duration: {{ data.speed }}ms;
                    }
                    <# if(data.show_zsy) { #>
                        <# if(_.isObject(data.img_gd_height)){ #>
                            <# if(data.img_gd_height){ #>
                                {{ addonId }} {height:{{data.img_gd_height.md}}px;overflow:hidden;}
                                {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height.md}}px;object-fit:{{img_style_type7}};}
                                {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height.md}}px;object-fit:{{img_style_type7}};}

                            <# }else{ #>
                                {{ addonId }} {height:500px;overflow:hidden;}
                                {{ addonId }} .jwpf-item-has-bg img{height:500px;object-fit:{{img_style_type7}};}
                                {{ addonId }} .jwpf-item-has-bg video{height:500px;object-fit:{{img_style_type7}};}

                            <# } #> 
                        <# } else { #>
                            {{ addonId }} {height:{{data.img_gd_height}}px;overflow:hidden;}
                            {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}
                            {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}

                        <# } #>
                        
                        @media (max-width: 991px) {
                            <# if(_.isObject(data.img_gd_height)){ #>
                                <# if(data.img_gd_height.sm){ #>
                                    {{ addonId }} {height:{{data.img_gd_height.sm}}px;overflow:hidden;}
                                    {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height.sm}}px;object-fit:{{img_style_type7}};}
                                    {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height.sm}}px;object-fit:{{img_style_type7}};}

                                <# }else{ #>
                                    {{ addonId }} {height:500px;overflow:hidden;}
                                    {{ addonId }} .jwpf-item-has-bg img{height:500px;object-fit:{{img_style_type7}};}
                                    {{ addonId }} .jwpf-item-has-bg video{height:500px;object-fit:{{img_style_type7}};}

                                <# } #> 
                                
                            <# } else { #>
                                {{ addonId }} {height:{{data.img_gd_height}}px;overflow:hidden;}
                                {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}
                                {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}

                            <# } #>
                        }

                        @media (max-width: 767px) {
                            <# if(_.isObject(data.img_gd_height)){ #>
                                <# if(data.img_gd_height.xs){ #>
                                    {{ addonId }} {height:{{data.img_gd_height.xs}}px;overflow:hidden;}
                                    {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height.xs}}px;object-fit:{{img_style_type7}};}
                                    {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height.xs}}px;object-fit:{{img_style_type7}};}

                                <# }else{ #>
                                    {{ addonId }} {height:500px;overflow:hidden;}
                                    {{ addonId }} .jwpf-item-has-bg img{height:500px;object-fit:{{img_style_type7}};}
                                    {{ addonId }} .jwpf-item-has-bg video{height:500px;object-fit:{{img_style_type7}};}
                                <# } #> 
                                
                            <# } else { #>
                                {{ addonId }} {height:{{data.img_gd_height}}px;overflow:hidden;}
                                {{ addonId }} .jwpf-item-has-bg img{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}
                                {{ addonId }} .jwpf-item-has-bg video{height:{{data.img_gd_height}}px;object-fit:{{img_style_type7}};}

                            <# } #>
                        }
                        
                    <# } #>  
                    <# if(data.show_bubble) { #>
                        {{ addonId }} .canvas-area {
                            position: absolute;
                            top: 0;
                            left: 0;
                            z-index: 10;
                            width: 100%;
                            height: 100%;
                        }
                        {{ addonId }} .jwpf-item.jwpf-item-has-bg .jwpf-carousel-item-inner {
                            z-index: 12;
                        }
                    <# } #>

                    <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>
                        <# var button_fontstyle = carousel_item.button_font_style || ""; 

                        #>
                        #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }} {
                            letter-spacing: {{ carousel_item.button_letterspace }};

                                <# if(button_fontstyle.underline){ #>
                                    text-decoration: underline;
                                <# } #>
                                <# if(button_fontstyle.uppercase){ #>
                                    text-transform: uppercase;
                                <# } #>
                                <# if(button_fontstyle.italic){ #>
                                    font-style: italic;
                                <# } #>
                                <# if(button_fontstyle.lighter){ #>
                                    font-weight: lighter;
                                <# } else if(button_fontstyle.normal){#>
                                    font-weight: normal;
                                <# } else if(button_fontstyle.bold){#>
                                    font-weight: bold;
                                <# } else if(button_fontstyle.bolder){#>
                                    font-weight: bolder;
                                <# } #>
           
                        }

                        <# if(carousel_item.button_type == "custom"){ #>
                            #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom{
                                color: {{ carousel_item.button_color }};
                                <# if(carousel_item.button_appearance == "outline"){ #>
                                    border-color: {{ carousel_item.button_background_color }}
                                <# } else if(carousel_item.button_appearance == "3d"){ #>
                                    border-bottom-color: {{ carousel_item.button_background_color_hover }};
                                    background-color: {{ carousel_item.button_background_color }};
                                <# } else if(carousel_item.button_appearance == "gradient"){ #>
                                    border: none;
                                    <# if(typeof carousel_item.button_background_gradient.type !== "undefined" && carousel_item.button_background_gradient.type == "radial"){ #>
                                        background-image: radial-gradient(at {{ carousel_item.button_background_gradient.radialPos || "center center"}}, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
                                    <# } else { #>
                                        background-image: linear-gradient({{ carousel_item.button_background_gradient.deg || 0}}deg, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
                                    <# } #>
                                <# } else { #>
                                    background-color: {{ carousel_item.button_background_color }};
                                <# } #>
                            }

                            #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom:hover{
                                color: {{ carousel_item.button_color_hover }};
                                background-color: {{ carousel_item.button_background_color_hover }};
                                <# if(carousel_item.button_appearance == "outline"){ #>
                                    border-color: {{ carousel_item.button_background_color_hover }};
                                <# } else if(carousel_item.button_appearance == "gradient"){ #>
                                    <# if(typeof carousel_item.button_background_gradient_hover.type !== "undefined" && carousel_item.button_background_gradient_hover.type == "radial"){ #>
                                        background-image: radial-gradient(at {{ carousel_item.button_background_gradient_hover.radialPos || "center center"}}, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
                                    <# } else { #>
                                        background-image: linear-gradient({{ carousel_item.button_background_gradient_hover.deg || 0}}deg, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
                                    <# } #>
                                <# } #>
                            }

                        <# } #>
                        <#
                            var padding = "";
                            var padding_sm = "";
                            var padding_xs = "";
                            if(carousel_item.title_padding){
                                if(_.isObject(carousel_item.title_padding)){
                                    if(carousel_item.title_padding.md.trim() !== ""){
                                        padding = carousel_item.title_padding.md.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.title_padding.sm.trim() !== ""){
                                        padding_sm = carousel_item.title_padding.sm.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.title_padding.xs.trim() !== ""){
                                        padding_xs = carousel_item.title_padding.xs.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }
                                }

                            }

                            var margin = "";
                            var margin_sm = "";
                            var margin_xs = "";
                            if(carousel_item.title_margin){
                                if(_.isObject(carousel_item.title_margin)){
                                    if(carousel_item.title_margin.md.trim() !== ""){
                                        margin = carousel_item.title_margin.md.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.title_margin.sm.trim() !== ""){
                                        margin_sm = carousel_item.title_margin.sm.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.title_margin.xs.trim() !== ""){
                                        margin_xs = carousel_item.title_margin.xs.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }
                                }

                            }


                            var content_padding = "";
                            var content_padding_sm = "";
                            var content_padding_xs = "";
                            if(carousel_item.content_padding){
                                if(_.isObject(carousel_item.content_padding)){
                                    if(carousel_item.content_padding.md.trim() !== ""){
                                        content_padding = carousel_item.content_padding.md.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.content_padding.sm.trim() !== ""){
                                        content_padding_sm = carousel_item.content_padding.sm.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.content_padding.xs.trim() !== ""){
                                        content_padding_xs = carousel_item.content_padding.xs.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }
                                }

                            }

                            var content_margin = "";
                            var content_margin_sm = "";
                            var content_margin_xs = "";
                            if(carousel_item.content_margin){
                                if(_.isObject(carousel_item.content_margin)){
                                    if(carousel_item.content_margin.md.trim() !== ""){
                                        content_margin = carousel_item.content_margin.md.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.content_margin.sm.trim() !== ""){
                                        content_margin_sm = carousel_item.content_margin.sm.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }

                                    if(carousel_item.content_margin.xs.trim() !== ""){
                                        content_margin_xs = carousel_item.content_margin.xs.split(" ").map(item => {
                                            if(_.isEmpty(item)){
                                                return "0";
                                            }
                                            return item;
                                        }).join(" ")
                                    }
                                }

                            }
                        #>

                        #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                            <# if(_.isObject(carousel_item.title_fontsize)){ #>
                                font-size: {{ carousel_item.title_fontsize.md }}px;
                            <# } else { #>
                                font-size: {{ carousel_item.title_fontsize }}px;
                            <# } #>
                            <# if(_.isObject(carousel_item.title_lineheight)){ #>
                                line-height: {{ carousel_item.title_lineheight.md }}px;
                            <# } else { #>
                                line-height: {{ carousel_item.title_lineheight }}px;
                            <# } #>
                            color: {{ carousel_item.title_color }};
                            padding: {{ padding }};
                            margin: {{ margin }};
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                            <# if(_.isObject(carousel_item.content_fontsize)){ #>
                                font-size: {{ carousel_item.content_fontsize.md }}px;
                            <# } else { #>
                                font-size: {{ carousel_item.content_fontsize }}px;
                            <# } #>
                            <# if(_.isObject(carousel_item.content_lineheight)){ #>
                                line-height: {{ carousel_item.content_lineheight.md }}px;
                            <# } else { #>
                                line-height: {{ carousel_item.content_lineheight }}px;
                            <# } #>
                            color: {{ carousel_item.content_color }};
                            padding: {{ content_padding }};
                            margin: {{ content_margin }};
                        }
                        @media (min-width: 768px) and (max-width: 991px) {
                            #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                                <# if(_.isObject(carousel_item.title_fontsize)){ #>
                                    font-size: {{ carousel_item.title_fontsize.sm }}px;
                                <# } #>
                                <# if(_.isObject(carousel_item.title_lineheight)){ #>
                                    line-height: {{ carousel_item.title_lineheight.sm }}px;
                                <# } #>
                                padding: {{ padding_sm }};
                                margin: {{ margin_sm }};
                            }
                            #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                                <# if(_.isObject(carousel_item.content_fontsize)){ #>
                                    font-size: {{ carousel_item.content_fontsize.sm }}px;
                                <# } #>
                                <# if(_.isObject(carousel_item.content_lineheight)){ #>
                                    line-height: {{ carousel_item.content_lineheight.sm }}px;
                                <# } #>
                                padding: {{ content_padding_sm }};
                                margin: {{ content_margin_sm }};
                            }
                        }

                        @media (max-width: 767px) {
                            #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                                <# if(_.isObject(carousel_item.title_fontsize)){ #>
                                    font-size: {{ carousel_item.title_fontsize.xs }}px;
                                <# } #>
                                <# if(_.isObject(carousel_item.title_lineheight)){ #>
                                    line-height: {{ carousel_item.title_lineheight.xs }}px;
                                <# } #>
                                padding: {{ padding_xs }};
                                margin: {{ margin_xs }};
                            }
                            #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                                <# if(_.isObject(carousel_item.content_fontsize)){ #>
                                    font-size: {{ carousel_item.content_fontsize.xs }}px;
                                <# } #>
                                <# if(_.isObject(carousel_item.content_lineheight)){ #>
                                    line-height: {{ carousel_item.content_lineheight.xs }}px;
                                <# } #>
                                padding: {{ content_padding_xs }};
                                margin: {{ content_margin_xs }};
                            }
                        }
                    <# }); #>
                </style>
                <div class="jwpf-carousel jwpf-slide {{ data.class }}" id="jwpf-carousel-{{ data.id }}" data-interval="{{ interval }}" {{{ autoplay }}}>
                    <# if(data.controllers){ #>
                        <ol class="jwpf-carousel-indicators  jwpf-carousel-indicators_preview">
                        <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>

                            <# var active = (key == 0) ? "active" : ""; #>
                            <li data-jwpf-target="#jwpf-carousel-{{ data.id }}"  class="{{ active }}"  data-jwpf-slide-to="{{ key }}"></li>
                        <# }); #>
                        </ol>
                    <# } #>
                    <# if(data.show_bubble){ 
                        var bubble_color_set = data.bubble_color_set || "custom";
                        var bubble_color = data.bubble_color || "rgba(255,255,255, .4)";
                        if(bubble_color_set == "random") {
                            bubble_color = "random"
                        }
                    #>
                        <div class="canvas-area" id="{{ data.id }}" data-id="{{ data.id }}" data-bubble-color="{{ bubble_color }}"></div>
                    <# } #>
                    <div class="jwpf-carousel-inner {{ data.alignment }}">
                        <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>
                            
                            <#
                                var classNames = (key == 0) ? "active" : "";
                                classNames += (carousel_item.bg) ? " jwpf-item-has-bg" : "";
                                classNames += " jwpf-item-"+data.id+""+key;

                                var media_video = (typeof carousel_item.media_video !== "undefined" && carousel_item.media_video) ? carousel_item.media_video : "0";
                                var slider_video = (typeof carousel_item.slider_video !== "undefined" && carousel_item.slider_video) ? carousel_item.slider_video : "";

                                var srcs = "src=" + slider_video;
                                
                                if(_.isObject(carousel_item.video_height)){
                                    var heights= "height=" + carousel_item.video_height.md + "px";
                                } else { 
                                    var heights= "height=" + carousel_item.video_height + "px";
                                } 

                            #>
                            <# if(carousel_item.text_from && carousel_item.text_from == 1 && carousel_item.text_id) {
                                let newArr = infoss.filter(item => item.id == carousel_item.text_id)
                                // console.log(newArr)
                                var item_ = ""
                                if(newArr.length > 0) {
                                    item_ = newArr[0]
                                    carousel_item.title = item_.banner_title
                                    carousel_item.content = item_.introtext

                                    if(item_.image_intro){
                                        carousel_item.bg = item_.image_intro
                                    }else{
                                        carousel_item.bg ="https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg";
                                    }
                                }
                                
                            } #>
                 
                            <div class="jwpf-item {{ classNames }} cb">
                                <# if(media_video == 1 && slider_video ){
                                    if(slider_video){
                                        if(slider_video.indexOf("http://") == 0 || slider_video.indexOf("https://") == 0){ #>
                                            <video loop autoplay width="100%" heights >
                                                <source {{srcs}} type="video/mp4">
                                            </video>
                                        <# }
                                    }
                                }else{ #>
                                    <# if(carousel_item.bg && carousel_item.bg.indexOf("http://") == -1 && carousel_item.bg.indexOf("https://") == -1){ #>
                                        <img src=\'{{ pagefactory_base + carousel_item.bg }}\' alt="{{ carousel_item.title }}">
                                    <# } else if(carousel_item.bg){ #>
                                        <img src=\'{{ carousel_item.bg }}\' alt="{{ carousel_item.title }}">
                                    <# } #>
                                <# } #>


                                <# if(carousel_item.media_url_show){ #>
                                   <a href="{{carousel_item.media_url}}" target="{{carousel_item.media_target}}">   
                                <# } #>
                                <div class="jwpf-carousel-item-inner">
                                    <div class="jwpf-carousel-caption">
                                        <div class="jwpf-carousel-text">
                                            <# if(carousel_item.title || carousel_item.content) { #>

                                                <# if(carousel_item.title) { #>
                                                    <h2 class="jw-editable-content" id="addon-title-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carousel_item-{{key}}-title">{{ carousel_item.title }}</h2>
                                                <# } #>
                                                <div class="jwpf-carousel-content jw-editable-content" id="addon-content-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carousel_item-{{key}}-content">{{{ carousel_item.content }}}</div>
                                                <# if(carousel_item.button_text) { #>
                                                    <#
                                                        var btnClass = "";
                                                        btnClass += carousel_item.button_type ? " jwpf-btn-"+carousel_item.button_type : " jwpf-btn-default" ;
                                                        btnClass += carousel_item.button_size ? " jwpf-btn-"+carousel_item.button_size : "" ;
                                                        btnClass += carousel_item.button_shape ? " jwpf-btn-"+carousel_item.button_shape : " jwpf-btn-rounded" ;
                                                        btnClass += carousel_item.button_appearance ? " jwpf-btn-"+carousel_item.button_appearance : "" ;
                                                        btnClass += carousel_item.button_block ? " "+carousel_item.button_block : "" ;
                                                        var button_text = carousel_item.button_text;

                                                        let icon_arr = (typeof carousel_item.button_icon !== "undefined" && carousel_item.button_icon) ? carousel_item.button_icon.split(" ") : "";
                                                        let icon_name = icon_arr.length === 1 ? "fa "+carousel_item.button_icon : carousel_item.button_icon;

                                                        if(carousel_item.button_icon_position == "left"){
                                                            button_text = (carousel_item.button_icon) ? \'<i class="\'+icon_name+\'"></i> \'+carousel_item.button_text : carousel_item.button_text ;
                                                        }else{
                                                            button_text = (carousel_item.button_icon) ? carousel_item.button_text+\' <i class="\'+icon_name+\'"></i>\' : carousel_item.button_text ;
                                                        }
                                                    #>
                                                    <a href=\'{{ carousel_item.button_url }}\' target="{{ carousel_item.button_target }}" id="btn-{{ data.id + "" + key}}" class="jwpf-btn{{ btnClass }}">{{{ button_text }}}</a>
                                                <# } #>
                                            <# } #>
                                        </div>
                                    </div>
                                </div>
                                <# if(carousel_item.media_url_show){ #>
                                  </a>
                                <# } #>
                            </div>

                        <# }); #>
                
                    </div>
                    <# if(data.arrows) { 

                        var arrows_site = data.arrows_site ? data.arrows_site : "center";
                        if(arrows_site=="left")
                        { #>
                            <style>
                                #jwpf-addon-{{ data.id }} .lefts {
                                    position: absolute;
                                    top: 74%;
                                    left: 5%;
                                    bottom: 0;
                                    width: 40px;
                                    opacity: .8;
                                    filter: alpha(opacity=80);
                                    font-size: 24px;
                                    color: #fff;
                                    text-align: center;
                                    text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                                }
                                #jwpf-addon-{{ data.id }} .rights {
                                    position: absolute;
                                    top: 74%;
                                    left: 10%;
                                    bottom: 0;
                                    width: 40px;
                                    opacity: .8;
                                    filter: alpha(opacity=80);
                                    font-size: 24px;
                                    color: #fff;
                                    text-align: center;
                                    text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                                }
                            </style>
                        <# }
                        if(arrows_site=="right")
                        { #> 
                            <style>
                                #jwpf-addon-{{ data.id }} .lefts {
                                    position: absolute;
                                    top: 74%;
                                    left: 85%;
                                    bottom: 0;
                                    width: 40px;
                                    opacity: .8;
                                    filter: alpha(opacity=80);
                                    font-size: 24px;
                                    color: #fff;
                                    text-align: center;
                                    text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                                }
                                #jwpf-addon-{{ data.id }} .rights {
                                    position: absolute;
                                    top: 74%;
                                    left: 90%;
                                    bottom: 0;
                                    width: 40px;
                                    opacity: .8;
                                    filter: alpha(opacity=80);
                                    font-size: 24px;
                                    color: #fff;
                                    text-align: center;
                                    text-shadow: 0 1px 2px rgb(0 0 0 / 60%);
                                }
                            </style>
                        <# } #>

                        <a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow left lefts jwpf-carousel-control" data-slide="prev"><i class="fa fa-chevron-left"></i></a>
                        <a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow right rights jwpf-carousel-control" data-slide="next"><i class="fa fa-chevron-right"></i></a>
                    <# } #>
                </div>

            <# } #>

        ';

        return $output;
    }
}
