<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
error_reporting(0);
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonService_tab_list_carousel extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id    = $_GET['site_id'] ?? 0;
        $layout_id  = $_GET['layout_id'] ?? 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;


        $settings = $this->addon->settings;
        //分类显示,选择几级分类
        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
        //从第n个分类开始显示(导航)
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : 1;
        //显示n个分类(导航)
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : 4;
        //指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        //指定每个分类下展示几条
        $limit = (isset($settings->limit)) ? $settings->limit : 4;
        //是否详情页
        $isLinkDetail = (isset($settings->isLinkDetail)) ? $settings->isLinkDetail : 0;

        $service_tab_item_number = (isset($settings->service_tab_item_number_md) && $settings->service_tab_item_number_md) ? $settings->service_tab_item_number_md : '8';
        $service_tab_item_number_sm = (isset($settings->service_tab_item_number_sm) && $settings->service_tab_item_number_sm) ? $settings->service_tab_item_number_sm : '6';
        $service_tab_item_number_xs = (isset($settings->service_tab_item_number_xs) && $settings->service_tab_item_number_xs) ? $settings->service_tab_item_number_xs : '3';
        $service_tab_bgColor = (isset($settings->service_tab_bgColor) && $settings->service_tab_bgColor) ? $settings->service_tab_bgColor : '#fff';
        $service_tab_line = (isset($settings->service_tab_line) && $settings->service_tab_line) ? $settings->service_tab_line : '#f0f0f0';
        $service_tab_item_icon_width = (isset($settings->service_tab_item_icon_width) && $settings->service_tab_item_icon_width) ? $settings->service_tab_item_icon_width : '60';
        $service_tab_item_icon_height = (isset($settings->service_tab_item_icon_height) && $settings->service_tab_item_icon_height) ? $settings->service_tab_item_icon_height : '60';
        $service_tab_item_fontsize = (isset($settings->service_tab_item_fontsize) && $settings->service_tab_item_fontsize) ? $settings->service_tab_item_fontsize : '16';
        $service_tab_item_lineHeight = (isset($settings->service_tab_item_lineHeight) && $settings->service_tab_item_lineHeight) ? $settings->service_tab_item_lineHeight : '32';
        $service_tab_item_style = (isset($settings->service_tab_item_style) && $settings->service_tab_item_style) ? $settings->service_tab_item_style : '';
        $service_tab_item_letterSpace = (isset($settings->service_tab_item_letterSpace) && $settings->service_tab_item_letterSpace) ? $settings->service_tab_item_letterSpace : '0';
        $service_tab_item_color = (isset($settings->service_tab_item_color) && $settings->service_tab_item_color) ? $settings->service_tab_item_color : '#444';
        $service_tab_item_textAlign = (isset($settings->service_tab_item_textAlign) && $settings->service_tab_item_textAlign) ? $settings->service_tab_item_textAlign : 'center';
        $service_tab_item_margin = (isset($settings->service_tab_item_margin) && $settings->service_tab_item_margin) ? $settings->service_tab_item_margin : '10px 0 0 0';

        $service_carousel_bgColor = (isset($settings->service_carousel_bgColor) && $settings->service_carousel_bgColor) ? $settings->service_carousel_bgColor : '#fff';
        $service_carousel_mgB = (isset($settings->service_carousel_mgB) && $settings->service_carousel_mgB) ? $settings->service_carousel_mgB : '20';
        $service_carousel_item_imgH = (isset($settings->service_carousel_item_imgH) && $settings->service_carousel_item_imgH) ? $settings->service_carousel_item_imgH : '200';
        $service_carousel_item_imgTime = (isset($settings->service_carousel_item_imgTime) && $settings->service_carousel_item_imgTime) ? $settings->service_carousel_item_imgTime : '200';
        $service_carousel_item_fontsize = (isset($settings->service_carousel_item_fontsize) && $settings->service_carousel_item_fontsize) ? $settings->service_carousel_item_fontsize : '16';
        $service_carousel_textBgColor = (isset($settings->service_carousel_textBgColor) && $settings->service_carousel_textBgColor) ? $settings->service_carousel_textBgColor : '#2164d9';
        $service_carousel_textColor = (isset($settings->service_carousel_textColor) && $settings->service_carousel_textColor) ? $settings->service_carousel_textColor : '#fff';
        $service_carousel_item_textH = (isset($settings->service_carousel_item_textH) && $settings->service_carousel_item_textH) ? $settings->service_carousel_item_textH : '80';
        $service_carousel_textIconBgColor = (isset($settings->service_carousel_textIconBgColor) && $settings->service_carousel_textIconBgColor) ? $settings->service_carousel_textIconBgColor : '#e29728';
        $service_carousel_textIcon = (isset($settings->service_carousel_textIcon) && $settings->service_carousel_textIcon) ? $settings->service_carousel_textIcon : 'https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png';
        $service_carousel_item_fontsizeHover = (isset($settings->service_carousel_item_fontsizeHover) && $settings->service_carousel_item_fontsizeHover) ? $settings->service_carousel_item_fontsizeHover : '16';
        $service_carousel_textHoverBgColor = (isset($settings->service_carousel_textHoverBgColor) && $settings->service_carousel_textHoverBgColor) ? $settings->service_carousel_textHoverBgColor : '#0143b7';
        $service_carousel_textHoverColor = (isset($settings->service_carousel_textHoverColor) && $settings->service_carousel_textHoverColor) ? $settings->service_carousel_textHoverColor : '#e29728';
        $service_carousel_item_textTime = (isset($settings->service_carousel_item_textTime) && $settings->service_carousel_item_textTime) ? $settings->service_carousel_item_textTime : '200';

        $service_tab_item_hover_bg = (isset($settings->service_tab_item_hover_bg) && $settings->service_tab_item_hover_bg) ? $settings->service_tab_item_hover_bg : '#2164d9';
        $service_tab_item_hover_color = (isset($settings->service_tab_item_hover_color) && $settings->service_tab_item_hover_color) ? $settings->service_tab_item_hover_color : '#fff';
        $service_carousel_item_number = (isset($settings->service_carousel_item_number) && $settings->service_carousel_item_number) ? $settings->service_carousel_item_number : 4;

        $output = '
        <style>
        ';
        $output.=
            $addon_id .' .service-tab {
				width: 100%;
				margin: 30px auto;
			}
			' . $addon_id . ' .service-tab .tab-box {
				display: flex;
				flex-wrap: wrap;
				background-color: ' . $service_tab_bgColor . ';
				box-shadow: #eaeaea 0px 0px 6px 2px;
				padding: 0 20px;
				z-index: 5;
				position: relative;
			}
			' . $addon_id . ' .service-tab .tab-box .item {
				width: calc(100% / ' . $service_tab_item_number . ');
				text-align: center;
				padding: 30px 0;
				position: relative;
				transition: all linear 0.2s;
			}
			' . $addon_id . ' .service-tab .tab-box .item::after {
				content: "";
				width: 1px;
				height: 50px;
				background-color: ' . $service_tab_line . ';
				position: absolute;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}
			' . $addon_id . ' .service-tab .tab-box .item:last-child::after {
				width: 0;
			}
			' . $addon_id . ' .service-tab .tab-box .item .icon,.service-tab .tab-box .item .icon-hover {
				display: inline-block;
				width: ' . $service_tab_item_icon_width . 'px;
				height: ' . $service_tab_item_icon_height . 'px;
				background: url() no-repeat center;
				background-size: contain;
			}
			' . $addon_id . ' .service-tab .tab-box .item .icon-hover {
				display: none;
			}
			' . $addon_id . ' .service-tab .tab-box .item p {
				font-size: ' . $service_tab_item_fontsize . 'px;
				line-height: ' . $service_tab_item_lineHeight . 'px;
				letter-spacing: ' . $service_tab_item_letterSpace . 'px;
				color: ' . $service_tab_item_color . ';
				text-align: ' . $service_tab_item_textAlign . ';
				margin: ' . $service_tab_item_margin . ';';
        if(isset($service_tab_item_style->underline) && $service_tab_item_style->underline){
            $output .= 'text-decoration: underline;';
        }
        if(isset($service_tab_item_style->italic) && $service_tab_item_style->italic){
            $output .= 'font-style: italic;';
        }
        if(isset($service_tab_item_style->uppercase) && $service_tab_item_style->uppercase){
            $output .= 'text-transform: uppercase;';
        }
        if(isset($service_tab_item_style->weight) && $service_tab_item_style->weight){
            $output .= 'font-weight:' . $service_tab_item_style->weight . ';';
        }
        $output .= '}
			' . $addon_id . ' .service-tab .tab-box .item:hover {
				cursor: pointer;
				background-color: ' . $service_tab_item_hover_bg . ';
			}
			' . $addon_id . ' .service-tab .tab-box .item:hover::after {
				width: 0;
			}
			' . $addon_id . ' .service-tab .tab-box .item:hover .icon {
				display: none;
			}
			' . $addon_id . ' .service-tab .tab-box .item:hover .icon-hover {
				display: inline-block;
			}
			' . $addon_id . ' .service-tab .tab-box .item:hover p {
				color: ' . $service_tab_item_hover_color . ';
			}
			' . $addon_id . ' .service-tab .service-list {
				background-color: ' .$service_carousel_bgColor . ';
				padding: 80px 20px 30px;
				width: calc(100% - 20px);
				margin: -40px auto 0;
				box-shadow: #eaeaea 0px 0px 6px 2px;
				z-index: 2;
			}
			' . $addon_id . ' .service-tab .service-list .list-box {
				display: none;
			}
			' . $addon_id . ' .service-tab .list-box .item {
				text-align: center;
				padding: 0 10px;
				transition: all linear 0.2s;
				cursor: pointer;
			}
			' . $addon_id . ' .service-tab .list-box .item .img-box {
				width: 100%;
				height: ' . $service_carousel_item_imgH . 'px;
				overflow: hidden;
			}
			' . $addon_id . ' .service-tab .list-box .item img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				display: block;
				transition: all linear ' . $service_carousel_item_imgTime . 'ms;
			}
			' . $addon_id . ' .service-tab .list-box .item .img-box:hover img {
				transform: scale(1.5);
			}
			' . $addon_id . ' .service-tab .list-box .item p {
				height: ' . $service_carousel_item_textH . 'px;
				width:240px;
				line-height: ' . $service_carousel_item_textH . 'px;
				padding-bottom: 20px;
				color: ' . $service_carousel_textColor . ';
				font-size: ' . $service_carousel_item_fontsize . 'px;
				background-color: ' . $service_carousel_textBgColor . ';
				position: relative;
				transition: all linear ' . $service_carousel_item_textTime . 'ms;
			}
			' . $addon_id . ' .service-tab .list-box .item p:hover {
				color: ' . $service_carousel_textHoverColor . ';
				font-size: ' . $service_carousel_item_fontsizeHover . 'px;
				background-color: ' . $service_carousel_textHoverBgColor . ';
			}
			' . $addon_id . ' .service-tab .list-box .item p::before {
				content: "";
				width: 26px;
				height: 26px;
				background: url("' . $service_carousel_textIcon . '") no-repeat center;
				background-size: 54%;
				position: absolute;
				left: 0;
				right: 0;
				bottom: -15px;
				margin: auto;
				z-index: 9;
			}
			' . $addon_id . ' .service-tab .list-box .item p::after {
				content: "";
				position: absolute;
				left: 0;
				right: 0;
				bottom: -16px;
				margin: auto;
				transform: rotateZ(-45deg);
				width: 26px;
				height: 26px;
				background: ' . $service_carousel_textIconBgColor . ';
				border: #fff solid 3px;
				transition: all linear 0.2s;
			}
			' . $addon_id . ' .service-tab .list-box .item p:hover::after {
				transform: rotateZ(45deg);
			}
			' . $addon_id . ' .owl-carousel .owl-wrapper-outer {
				padding-bottom: ' . $service_carousel_mgB . 'px;
			}
			' . $addon_id . ' .owl-theme .owl-controls .owl-pagination {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			' . $addon_id . ' .owl-theme .owl-controls .owl-page span {
				width: 14px;
				height: 14px;
				background-color: #fff;
				border: #fff solid 2px;
				opacity: 1;
				position: relative;
				transition: all ease-in-out 0.2s;
			}
			' . $addon_id . ' .owl-theme .owl-controls .owl-page span::after {
				content: "";
				width: 4px;
				height: 4px;
				border-radius: 2px;
				background-color: #869791;
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}
			' . $addon_id . ' .owl-theme .owl-controls .owl-page.active span, .owl-theme .owl-controls.clickable .owl-page:hover span {
				opacity: 0.5;
				border-color: #869791;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				' . $addon_id . ' .service-tab .tab-box .item {
					width: calc(100% / ' . $service_tab_item_number_sm . ');
				}
				' . $addon_id . ' .service-tab .tab-box .item:nth-child(' . $service_tab_item_number_sm . 'n)::after {
					width: 0;
				}
			}
			@media (max-width: 767px) {
				' . $addon_id . ' .service-tab .tab-box .item {
					width: calc(100% / ' . $service_tab_item_number_xs . ');
				}
				' . $addon_id . ' .service-tab .tab-box .item:nth-child(' . $service_tab_item_number_xs . 'n)::after {
					width: 0;
				}
			}
			</style>
	    ';


        //取出该公司数据
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $item = JwpagefactoryHelperCategories::GetCategoriesthree('com_goods', $site_id, $detail_page_id, $layout_id, $company_id);

        //数据转为多维数组
        $item = $this->subTree($item);
        //对各级数据进行分类

        //获取下面列表的数据
        $ordering       = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $goods_catid    = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type      = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $tagids         = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $page           = $_GET['page'] ?? 1;

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
        require_once $article_helper;
        $goods_arr = [];

        //二级数据
        $two_arr = [];
        foreach ($item as $k => $v)
        {
            if (!empty($v['sub']))
            {
                foreach ($v['sub'] as $kk => $vv)
                {
                    array_push($two_arr, $vv);
                }
                unset($v['sub']);
            }
            $item[$k] = $v;
        }
        //三级数据
        $three_arr = [];
        foreach ($two_arr as $key => $val)
        {
            if (!empty($val['sub']))
            {
                foreach ($val['sub'] as $key2 => $val2)
                {
                    array_push($three_arr, $val2);
                }
                unset($val['sub']);
            }
        }

        //一级数据处理从第几条开始
        foreach ($item as $ke1 => $va1)
        {
            if ($ke1 < $type_start - 1)
            {
                unset($item[$ke1]);
            }
        }
        $item = array_merge($item);

        //二级数据处理从第几条开始
        foreach ($two_arr as $ke2 => $va2)
        {
            if ($ke2 < $type_start - 1)
            {
                unset($two_arr[$ke2]);
            }
        }
        $two_arr = array_merge($two_arr);

        //三级数据处理从第几条开始
        foreach ($three_arr as $ke3 => $va3)
        {
            if ($ke3 < $type_start - 1)
            {
                unset($three_arr[$ke3]);
            }
        }
        $three_arr = array_merge($three_arr);

        //一级数据处理显示几条
        foreach ($item as $ke1 => $va1)
        {
            if ($ke1 > $type_num - 1)
            {
                unset($item[$ke1]);
            }
        }
        $item = array_merge($item);
        //二级数据处理显示几条
        foreach ($two_arr as $ke2 => $va2)
        {
            if ($ke2 > $type_num - 1)
            {
                unset($two_arr[$ke2]);
            }
        }
        $two_arr = array_merge($two_arr);

        //三级数据处理显示几条
        foreach ($three_arr as $ke3 => $va3)
        {
            if ($ke3 > $type_num - 1)
            {
                unset($three_arr[$ke3]);
            }
        }
        $three_arr = array_merge($three_arr);



        $output .= '<div class="service-tab">';
        //上面最大的div
        $output .= '	<div class="tab-box">';
        /*上边导航从这里循环*/
        switch ($type_parent)
        {
            case 'type1':
                foreach ($item as $k1 => $v1)
                {
                    $output .='
                            <style>
                            ' . $addon_id . ' .service-tab .service-list .list-box'.$k1.' {
                        display: none;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item {
                        text-align: center;
                        padding: 0 10px;
                        transition: all linear 0.2s;
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box {
                        width: 100%;
                        height: ' . $service_carousel_item_imgH . 'px;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        display: block;
                        transition: all linear ' . $service_carousel_item_imgTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box:hover img {
                        transform: scale(1.5);
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p {
                        height: ' . $service_carousel_item_textH . 'px;
                        width:240px;
                        line-height: ' . $service_carousel_item_textH . 'px;
                        padding-bottom: 20px;
                        color: ' . $service_carousel_textColor . ';
                        font-size: ' . $service_carousel_item_fontsize . 'px;
                        background-color: ' . $service_carousel_textBgColor . ';
                        position: relative;
                        transition: all linear ' . $service_carousel_item_textTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover {
                        color: ' . $service_carousel_textHoverColor . ';
                        font-size: ' . $service_carousel_item_fontsizeHover . 'px;
                        background-color: ' . $service_carousel_textHoverBgColor . ';
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::before {
                        content: "";
                        width: 26px;
                        height: 26px;
                        background: url("' . $service_carousel_textIcon . '") no-repeat center;
                        background-size: 54%;
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -15px;
                        margin: auto;
                        z-index: 9;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::after {
                        content: "";
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -16px;
                        margin: auto;
                        transform: rotateZ(-45deg);
                        width: 26px;
                        height: 26px;
                        background: ' . $service_carousel_textIconBgColor . ';
                        border: #fff solid 3px;
                        transition: all linear 0.2s;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover::after {
                        transform: rotateZ(45deg);
                    }
                    
                    </style>
                    ';

                    $output   .= '	
                    	<div class="item" data-id="ct-'.$k1.'">';
                    $output   .= '			<i class="icon" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<i class="icon-hover" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<p style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">' . mb_substr($v1['title'], 0, 4, "UTF8") . '</p>';
                    $output   .= '		</div>';
                    $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v1['tag_id'], $limit);
                    foreach ($Don_item as $kk1 => $vv1)
                    {
                        array_push($goods_arr, $vv1);
                    }
                }
                break;
            case 'type2':

                foreach ($two_arr as $k1 => $v1)
                {
                    $output .='
                            <style>
                            ' . $addon_id . ' .service-tab .service-list .list-box'.$k1.' {
                        display: none;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item {
                        text-align: center;
                        padding: 0 10px;
                        transition: all linear 0.2s;
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box {
                        width: 100%;
                        height: ' . $service_carousel_item_imgH . 'px;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        display: block;
                        transition: all linear ' . $service_carousel_item_imgTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box:hover img {
                        transform: scale(1.5);
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p {
                        height: ' . $service_carousel_item_textH . 'px;
                        width:240px;
                        line-height: ' . $service_carousel_item_textH . 'px;
                        padding-bottom: 20px;
                        color: ' . $service_carousel_textColor . ';
                        font-size: ' . $service_carousel_item_fontsize . 'px;
                        background-color: ' . $service_carousel_textBgColor . ';
                        position: relative;
                        transition: all linear ' . $service_carousel_item_textTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover {
                        color: ' . $service_carousel_textHoverColor . ';
                        font-size: ' . $service_carousel_item_fontsizeHover . 'px;
                        background-color: ' . $service_carousel_textHoverBgColor . ';
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::before {
                        content: "";
                        width: 26px;
                        height: 26px;
                        background: url("' . $service_carousel_textIcon . '") no-repeat center;
                        background-size: 54%;
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -15px;
                        margin: auto;
                        z-index: 9;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::after {
                        content: "";
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -16px;
                        margin: auto;
                        transform: rotateZ(-45deg);
                        width: 26px;
                        height: 26px;
                        background: ' . $service_carousel_textIconBgColor . ';
                        border: #fff solid 3px;
                        transition: all linear 0.2s;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover::after {
                        transform: rotateZ(45deg);
                    }
                    
                    </style>
                    ';
                    $output   .= '		<div class="item" data-id="ct-'.$k1.'">';
                    $output   .= '			<i class="icon" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<i class="icon-hover" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<p>' . mb_substr($v1['title'], 0, 4, "UTF8") . '</p>';
                    $output   .= '		</div>';
                    $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v1['tag_id'], $limit);
                    foreach ($Don_item as $kk2 => $vv2)
                    {
                        array_push($goods_arr, $vv2);
                    }
                }
                break;
            case 'type3':
                foreach ($three_arr as $k1 => $v1)
                {
                    $output .='
                            <style>
                            ' . $addon_id . ' .service-tab .service-list .list-box'.$k1.' {
                        display: none;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item {
                        text-align: center;
                        padding: 0 10px;
                        transition: all linear 0.2s;
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box {
                        width: 100%;
                        height: ' . $service_carousel_item_imgH . 'px;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        display: block;
                        transition: all linear ' . $service_carousel_item_imgTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item .img-box:hover img {
                        transform: scale(1.5);
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p {
                        height: ' . $service_carousel_item_textH . 'px;
                        width:240px;
                        line-height: ' . $service_carousel_item_textH . 'px;
                        padding-bottom: 20px;
                        color: ' . $service_carousel_textColor . ';
                        font-size: ' . $service_carousel_item_fontsize . 'px;
                        background-color: ' . $service_carousel_textBgColor . ';
                        position: relative;
                        transition: all linear ' . $service_carousel_item_textTime . 'ms;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover {
                        color: ' . $service_carousel_textHoverColor . ';
                        font-size: ' . $service_carousel_item_fontsizeHover . 'px;
                        background-color: ' . $service_carousel_textHoverBgColor . ';
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::before {
                        content: "";
                        width: 26px;
                        height: 26px;
                        background: url("' . $service_carousel_textIcon . '") no-repeat center;
                        background-size: 54%;
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -15px;
                        margin: auto;
                        z-index: 9;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p::after {
                        content: "";
                        position: absolute;
                        left: 0;
                        right: 0;
                        bottom: -16px;
                        margin: auto;
                        transform: rotateZ(-45deg);
                        width: 26px;
                        height: 26px;
                        background: ' . $service_carousel_textIconBgColor . ';
                        border: #fff solid 3px;
                        transition: all linear 0.2s;
                    }
                    ' . $addon_id . ' .service-tab .list-box'.$k1.' .item p:hover::after {
                        transform: rotateZ(45deg);
                    }
                    
                    </style>
                    ';
                    $output   .= '		<div class="item" data-id="ct-'.$k1.'" >';
                    $output   .= '			<i class="icon" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<i class="icon-hover" style="background-image: url(' . $v1['Lv_three_icon'] . ');"></i>';
                    $output   .= '			<p>' . mb_substr($v1['title'], 0, 4, "UTF8") . '</p>';
                    $output   .= '		</div>';
                    $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v1['tag_id'], $limit);
                    foreach ($Don_item as $kk3 => $vv3)
                    {
                        array_push($goods_arr, $vv3);
                    }
                }
                break;
        }
        /**上边导航从这里循环结束*/
        $output .= '	</div>';
        //上面最大的div的关闭

        //一级数据处理
        foreach ($item as $k1 => $v1)
        {
            foreach ($goods_arr as $k => $v)
            {
                if ($v1['tag_id'] == $v['pid'])
                {
                    $item[$k1]['sub'][] = $v;
                }
            }
        }

        //二级数据处理
        foreach ($two_arr as $k1 => $v1)
        {
            foreach ($goods_arr as $k => $v)
            {
                if ($v1['tag_id'] == $v['pid'])
                {
                    $two_arr[$k1]['sub'][] = $v;
                }
            }
        }

        //三级数据处理
        foreach ($three_arr as $k1 => $v1)
        {
            foreach ($goods_arr as $k => $v)
            {
                if ($v1['tag_id'] == $v['pid'])
                {
                    $three_arr[$k1]['sub'][] = $v;
                }
            }
        }

        //下面最大的div
        $output .= '	<div class="service-list">';
        /*下面的列表循环这里开始*/
        $arr_i=[];
        switch ($type_parent)
        {
            case 'type1':
                foreach (@$item as $k1 => $v1)
                {
                    $output .= "
                        <style>
                        {$addon_id}  #ct-{$k1}.active{
                            display: flex;
                        }
                        {$addon_id} .list-box{$k1}{
                            overflow: hidden;
                        }
                        </style>";
                    $output .= '		<div class="list-box'.$k1.' ' . (($k1 == 0) ? "active" : "") . ' " id="ct-' . $k1 . '" style="transition-property: none; padding-bottom: inherit; position: relative;">';
                    $output .= '<div class="swiper-wrapper" style="transform: none!important;">';
                    foreach (@$v1['sub'] as $k2 => $v2)
                    {

                        if ($v2['pid'] == $v1['tag_id'])
                        {
                            $output .= '			<div class="item swiper-slide" style="transition-property: none;">';
                            $output .= '				<div class="img-box">';
                            if ($isLinkDetail == 1)
                            {
                                $output .= '                 <a href="' . $v2['link'] . '" class="jwpf-cate-right-list-item">';
                            }
                            else
                            {
                                $output .= '                 <a class="jwpf-cate-right-list-item">';
                            }
                            $output .= '					<img src="' . $v2['list_img'] . '" >';
                            $output .= '                 </a>';
                            $output .= '				</div>';
                            $output .= '				<p style="width: 100%">' . mb_substr($v2['list_title'], 0, 4, "UTF8") . '</p>';
                            $output .= '			</div>';
                        }
                    }
                    $output .= '			</div>';
                    $output .= '<div class="swiper-pagination'.$k1.'" style="position: absolute;text-align: center;"></div>';
                    $output .= '	    </div>';
                }

                break;
            case 'type2':
                foreach ($two_arr as $k1 => $v1)
                {
                    $output .= "
                        <style>
                        {$addon_id}  #ct-{$k1}.active{
                            display: flex;
                        }
                        {$addon_id} .list-box{$k1}{
                            overflow: hidden;
                        }
                        </style>";
                    $output .= '		<div class="list-box'.$k1.' ' . (($k1 == 0) ? "active" : "") . ' " id="ct-' . $k1 . '" style="transition-property: none; padding-bottom: inherit; position: relative;">';
                    $output .= '<div class="swiper-wrapper" style="transform: none!important;">';
                    foreach ($v1['sub'] as $k2 => $v2)
                    {

                        if ($v2['pid'] == $v1['tag_id'])
                        {
                            $output .= '			<div class="item swiper-slide" style="transition-property: none;">';
                            $output .= '				<div class="img-box">';
                            if ($isLinkDetail == 1)
                            {
                                $output .= '                 <a href="' . $v2['link'] . '" class="jwpf-cate-right-list-item">';
                            }
                            else
                            {
                                $output .= '                 <a class="jwpf-cate-right-list-item">';
                            }
                            $output .= '					<img src="' . $v2['list_img'] . '" >';
                            $output .= '                 </a>';
                            $output .= '				</div>';
                            $output .= '				<p style="width: 100%">' . mb_substr($v2['list_title'], 0, 4, "UTF8") . '</p>';
                            $output .= '			</div>';
                        }
                    }
                    $output .= '			</div>';
                    $output .= '<div class="swiper-pagination'.$k1.'" style="position: absolute;text-align: center;"></div>';
                    $output .= '			</div>';
                }
                break;
            case 'type3':
                foreach ($three_arr as $k1 => $v1)
                {
                    $output .= "
                        <style>
                        {$addon_id}  #ct-{$k1}.active{
                            display: flex;
                        }
                        {$addon_id} .list-box{$k1}{
                            overflow: hidden;
                        }
                        </style>";
                    $output .= '		<div class="list-box'.$k1.' ' . (($k1 == 0) ? "active" : "") . ' " id="ct-' . $k1 . '" style="transition-property: none; padding-bottom: inherit; position: relative;">';
                    $output .= '<div class="swiper-wrapper" style="transform: none!important;">';
                    foreach ($v1['sub'] as $k2 => $v2)
                    {
                        if ($v2['pid'] == $v1['tag_id'])
                        {

                            $output .= '			<div class="item swiper-slide" style="transition-property: none;">';
                            $output .= '				<div class="img-box">';
                            if ($isLinkDetail == 1)
                            {
                                $output .= '                 <a href="' . $v2['link'] . '" class="jwpf-cate-right-list-item">';
                            }
                            else
                            {
                                $output .= '                 <a class="jwpf-cate-right-list-item">';
                            }
                            $output .= '					<img src="' . $v2['list_img'] . '" >';
                            $output .= '                 </a>';
                            $output .= '				</div>';
                            $output .= '				<p style="width: 100%">' . mb_substr($v2['list_title'], 0, 4, "UTF8") . '</p>';
                            $output .= '			</div>';
                        }
                    }
                    $output .= '			</div>';
                    $output .= '<div class="swiper-pagination'.$k1.'" style="position: absolute;text-align: center;"></div>';
                    $output .= '		</div>';
                }
                break;
        }
        /*下面的列表循环这里结束*/
        $output .= '	</div>';
        //下面最大的div的关闭
        $output .= '</div>';

        switch ($type_parent) {
            case 'type1':
                //js控制
                $output .= "
                <script>
                window.onload=function(){
                    jQuery('{$addon_id} .item').hover(function (){
                            var id =jQuery(this).attr('data-id');
                            jQuery('{$addon_id} #'+id).addClass('active').siblings().removeClass('active');
                    });
                ";
                foreach (@$item as $k1 => $v1) {
                    $output .= "
                    //轮播图
                        jQuery(document).ready(function($){
                            new Swiper ('.list-box{$k1}', {
                                init: function(){
                                    swiperAnimateCache(this); //隐藏动画元素
                                },
                                  slidesPerView: {$service_carousel_item_number},   //每页几个
                                  spaceBetween: 11,   //边距
                                    observer: true,   //多轮播需要写这个
                                    observeParents:true,//多轮播需要写这个
                                  pagination: {
                                    el: '.swiper-pagination{$k1}',//轮播下面的小图标
                                    clickable: true,
                                  },
                            })    
                        }); 
                ";
                }

                $output .= "}</script>";
                break;

            case 'type2':
                //js控制
                $output .= "
                <script>
                window.onload=function(){
                    jQuery('{$addon_id} .item').hover(function (){
                            var id =jQuery(this).attr('data-id');
                            jQuery('{$addon_id} #'+id).addClass('active').siblings().removeClass('active');
                    });
                    
                
                ";
                foreach (@$two_arr as $k1 => $v1) {
                    $output .= "
                    //轮播图
                        jQuery(document).ready(function($){
                            new Swiper ('.list-box{$k1}', {
                                init: function(){
                                    swiperAnimateCache(this); //隐藏动画元素
                                },
                                  slidesPerView: {$service_carousel_item_number},   //每页几个
                                  spaceBetween: 11,   //边距
                                    observer: true,   //多轮播需要写这个
                                    observeParents:true,//多轮播需要写这个
                                  pagination: {
                                    el: '.swiper-pagination{$k1}',//轮播下面的小图标
                                    clickable: true,
                                  },
                            })    
                        }); 
                ";
                }

                $output .= "}</script>";
                break;
            case 'type3':
                //js控制
                $output .= "
                <script>
                window.onload=function(){
                    jQuery('{$addon_id} .item').hover(function (){
                            var id =jQuery(this).attr('data-id');
                            jQuery('{$addon_id} #'+id).addClass('active').siblings().removeClass('active');
                    });
                    
                
                ";
                foreach (@$three_arr as $k1 => $v1) {
                    $output .= "
                    //轮播图
                        jQuery(document).ready(function($){
                            new Swiper ('.list-box{$k1}', {
                                init: function(){
                                    swiperAnimateCache(this); //隐藏动画元素
                                },
                                  slidesPerView: {$service_carousel_item_number},   //每页几个
                                  spaceBetween: 11,   //边距
                                    observer: true,   //多轮播需要写这个
                                    observeParents:true,//多轮播需要写这个
                                  pagination: {
                                    el: '.swiper-pagination{$k1}',//轮播下面的小图标
                                    clickable: true,
                                  },
                            })    
                        }); 
                ";
                }

                $output .= "}</script>";
                break;
        }
        return $output;

    }

    //    引用css文件
    public function stylesheets()
    {
        //		$style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css');
        return array(
            'https://www.dowebok.com/demo/2014/93/css/owl.carousel.css',
            'https://www.dowebok.com/demo/2014/93/css/owl.theme.css',
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
        );
    }

    //    引用js文件
    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');

        return $js;
    }
    public function js(){
        $script="";
        $script.="
            
        ";

        return $script;
    }
    public static function getTemplate()
    {

        $output = '
		<#
		var addonId = "#jwpf-addon-"+data.id;
		//上方选项卡
//		var service_tab_item = (!_.isEmpty(data.service_tab_item) && data.service_tab_item) ? data.service_tab_item : "";

		// 指定文章详情页ID
		var detail_page_id = (!_.isEmpty(data.detail_page_id) && data.detail_page_id) ? data.detail_page_id : "";
		#>

		<style>
            {{addonId}} .service-tab {
				width: 100%;
				margin: 30px auto;
			}
			{{addonId}} .service-tab .tab-box {
				display: flex;
				background-color: #FFFFFF;
				box-shadow: #eaeaea 0px 0px 6px 2px;
				padding: 0 20px;
				z-index: 5;
				position: relative;
			}
			{{addonId}} .service-tab .tab-box .item {
				width: calc(100% / 8);
				text-align: center;
				padding: 30px 0;
				position: relative;
				transition: all linear 0.2s;
			}
			{{addonId}} .service-tab .tab-box .item::after {
				content: "";
				width: 1px;
				height: 50px;
				background-color: #f0f0f0;
				position: absolute;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}
			{{addonId}} .service-tab .tab-box .item:last-child::after {
				width: 0;
			}
			{{addonId}} .service-tab .tab-box .item .icon,.service-tab .tab-box .item .icon-hover {
				display: inline-block;
				width: 60px;
				height: 60px;
				background: url() no-repeat center;
				background-size: contain;
			}
			{{addonId}} .service-tab .tab-box .item .icon-hover {
				display: none;
			}
			{{addonId}} .service-tab .tab-box .item p {
				font-size: 16px;
				margin-top: 10px;
				line-height: 32px;
				margin-bottom: 0;
			}
			{{addonId}} .service-tab .tab-box .item:hover {
				cursor: pointer;
				background-color: #2164d9;
			}
			{{addonId}} .service-tab .tab-box .item:hover::after {
				width: 0;
			}
			{{addonId}} .service-tab .tab-box .item:hover .icon {
				display: none;
			}
			{{addonId}} .service-tab .tab-box .item:hover .icon-hover {
				display: inline-block;
			}
			{{addonId}} .service-tab .tab-box .item:hover p {
				color: #fff;
			}
			{{addonId}} .service-tab .service-list {
				background-color: #FFFFFF;
				padding: 80px 20px 30px;
				width: calc(100% - 20px);
				margin: -40px auto 0;
				box-shadow: #eaeaea 0px 0px 6px 2px;
				z-index: 2;
			}
			{{addonId}} .service-tab .service-list .list-box {
				display: flex;
			}
			{{addonId}} .service-tab .list-box .item {
				/* width: calc(100% / 3); */
				text-align: center;
				padding: 0 10px;
			}
			{{addonId}} .service-tab .list-box .item img {
				width: 100%;
				display: block;
			}
			{{addonId}} .service-tab .list-box .item p {
				height: 80px;
				width: 240px;
				line-height: 80px;
				padding-bottom: 20px;
				color: #FFFFFF;
				font-size: 16px;
				background-color: #2164d9;
				position: relative;
			}
			{{addonId}} .service-tab .list-box .item p::before {
				content: "";
				width: 26px;
				height: 26px;
				background: url("https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png") no-repeat center;
				background-size: 54%;
				position: absolute;
				left: 0;       
				right: 0;
				bottom: -15px;
				margin: auto;
				z-index: 9;
			}
			{{addonId}} .service-tab .list-box .item p::after {
				content: "";
				position: absolute;
				left: 0;
				right: 0;
				bottom: -16px;
				margin: auto;
				transform: rotateZ(-45deg);
				width: 26px;
				height: 26px;
				background: #e29728;
				border: #fff solid 3px;
			}
			{{addonId}} .owl-carousel .owl-wrapper-outer {
				padding-bottom: 30px;
			}
			{{addonId}} .owl-theme .owl-controls .owl-pagination {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			{{addonId}} .owl-theme .owl-controls .owl-page span {
				width: 14px;
				height: 14px;
				background-color: #fff;
				border: #fff solid 2px;
				opacity: 1;
				position: relative;
				transition: all ease-in-out 0.2s;
			}
			{{addonId}} .owl-theme .owl-controls .owl-page span::after {
				content: "";
				width: 4px;
				height: 4px;
				border-radius: 2px;
				background-color: #869791;
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}
			{{addonId}} .owl-theme .owl-controls .owl-page.active span, .owl-theme .owl-controls.clickable .owl-page:hover span {
				opacity: 0.5;
				border-color: #869791;
			}
		</style>



		<div class="service-tab">
			<div class="tab-box">
				<div class="item">
					<i class="icon" style="background-image: url(https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg);"></i>
					<i class="icon-hover" style="background-image: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg);"></i>
					<p>岗前培训</p>
				</div>
				<div class="item">
					<i class="icon" style="background-image: url(https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg);"></i>
					<i class="icon-hover" style="background-image: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg);"></i>
					<p>月嫂服务</p>
				</div>
				<div class="item">
					<i class="icon" style="background-image: url(https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg);"></i>
					<i class="icon-hover" style="background-image: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg);"></i>
					<p>护工服务</p>
				</div>
				<div class="item">
					<i class="icon" style="background-image: url(https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg);"></i>
					<i class="icon-hover" style="background-image: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg);"></i>
					<p>家装服务</p>
				</div>
			</div>
			<div class="service-list">
				<div class="list-box" id="owl-demo">
					<div class="item">
						<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" >
						<p>清理地面卫生</p>
					</div>
					<div class="item">
						<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" >
						<p>清理地面卫生</p>
					</div>
					<div class="item">
						<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" >
						<p>清理地面卫生</p>
					</div>
					<div class="item">
						<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" >
						<p>清理地面卫生</p>
					</div>
				</div>
			</div>
		</div>';

        return $output;
    }


    //数组多层级 递归
    public function subTree($data, $pid = 1, $deep = 0)
    {   //用来存放数据
        $arr = [];
        //遍历数据库中的字段
        foreach ($data as $val)
        {
            //判断字段pid相等时放行
            if ($pid == $val['parent_id'])
            {
                //不同的层级
                $val['deep'] = $deep;
                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
                $val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
                //如果遇到pid==本条id时，将其存入数组
                $arr[] = $val;
            }
        }

        //返回数组
        return $arr;
    }
}
