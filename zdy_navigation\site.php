<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonZdy_Navigation extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-div-' . $this->addon->id;

        $output = '<div class="section-10">';
            $output .= '<div class="menu-btn"><div class="menu-icon"></div></div>';  ///<img src="' . $settings->daohang_bg . '" class="menu-icon" />
            $output .= '<div class="menu-cover">';
                $output .= '<div class="close"></div>';
                $output .= '<div class="content">';
                    foreach ($settings->zdy_navigation_item as $key => $value) {
                        $output .= '  <div class="m-item">';
                        $link = '';
                        if($value->detail_page_id){
                            $id = base64_encode($value->detail_page_id);
                            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                        if($link != ''){
                            $output .= '<div class="m-title"><a href="' . $link . '">' . $value->title . '</a></div>' . "\n";
                        }else {
                            $output .= '<div class="m-title">      <a>' . $value->title . '</a></div>' . "\n";
                        }
                        if ($value->isstd) {
                            $output .= '      <div class="child">';
                            if ($value->istudiao) {
                                $output .= '          <img src="' . $settings->btn_bg . '" class="icon" alt="">';
                            }
                            $output .= '          <div class="c-box">';
                            foreach ($settings->zdy_navigation_std_item as $k => $val) {
                                if ($value->title_std == $val->std_title_std) {
                                    if($link != ''){
                                        $output .= '      <a class="item" href="' . $link . '#' . $val->std_title_id . '">' . $val->std_title . '</a>' . "\n";
                                    }else {
                                        $output .= '      <a class="item" href="#' . $val->std_title_id . '">' . $val->std_title . '</a>' . "\n";
                                    }
                                }
                            }
                            $output .= '          </div>';
                            $output .= '      </div>';
                        }
                        $output .= '  </div>';
                    }
                $output .= '</div>';
            $output .= '</div>';
        $output .= '</div>';
        return $output;
    }
    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $a_css    = '';
        $title_fontsize_css .= (isset($settings->title_fontsize) && $settings->title_fontsize) ? 'font-size:' . $settings->title_fontsize . 'px;' : 'font-size:26px;';
        $title_lineheight_css .= (isset($settings->title_lineheight) && $settings->title_lineheight) ? 'line-height:' . $settings->title_lineheight . 'px;' : 'line-height:60px;';
        $title_color_css .= (isset($settings->title_color) && !empty($settings->title_color)) ? 'color:' . $settings->title_color . ';' : 'color:#FFF;';
        $title_active_color_css .= (isset($settings->title_active_color) && !empty($settings->title_active_color)) ? 'color:' . $settings->title_active_color . ';' : 'color:#FFF;';

        $title_std_fontsize_css .= (isset($settings->title_std_fontsize) && $settings->title_std_fontsize) ? 'font-size:' . $settings->title_std_fontsize . 'px;' : 'font-size:20px;';
        $title_std_lineheight_css .= (isset($settings->title_std_lineheight) && $settings->title_std_lineheight) ? 'line-height:' . $settings->title_std_lineheight . 'px;' : 'line-height:48px;';
        $title_std_color_css .= (isset($settings->title_std_color) && !empty($settings->title_std_color)) ? 'color:' . $settings->title_std_color . ';' : 'color:#FFF;';
        $title_std_active_color_css .= (isset($settings->title_std_active_color) && !empty($settings->title_std_active_color)) ? 'color:' . $settings->title_std_active_color . ';' : 'color:#FFF;';

        // 导航展开背景图
        $daohang_cover_bg = isset($settings->daohang_cover_bg) && $settings->daohang_cover_bg ? $settings->daohang_cover_bg : 'https://oss.lcweb01.cn/joomla/20210705/8a067bd046566dbdd4f234a9f8ad2f34.png';

        $css = '';
        // $css .= '<style type="text/css">';
        $css .= $addon_id . ' .section-10 {';
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'position: relative;';
//            $css .= 'background: #aaa;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-btn {';
//            $css .= 'width: 100%;';
//            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'align-items: center;';
            $css .= 'justify-content: center;';
            $css .= 'cursor: pointer;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-btn .menu-icon {';
            $css .= 'width: 45px;';
            $css .= 'height: 45px;';
            $css .= 'object-fit: contain;';
            $css .= 'background: url("'. $settings->daohang_bg .'") no-repeat center;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover {';
            $css .= 'position: fixed;';
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'left: 0;';
            $css .= 'top: 0;';
            $css .= 'background: url("' . $daohang_cover_bg . '") no-repeat;';
            $css .= 'background-size: cover;';
            $css .= 'display: none;    z-index: 999;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .close {';
            $css .= 'position: absolute;';
            $css .= 'top: 54px;';
            $css .= 'right: 57px;';
            $css .= 'width: 24px;';
            $css .= 'height: 24px;';
            $css .= 'background: url("' . $settings->close_bg . '") no-repeat;';
            $css .= 'background-size: 100%;';
            $css .= 'cursor: pointer;';
            $css .= 'opacity: 1;';
            $css .= 'z-index: 9999;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content {';
            $css .= 'width: 1100px;';
            $css .= 'height: -moz-fit-content;;';
            $css .= 'height: -webkit-fit-content;';
            $css .= 'height: fit-content;';
            $css .= 'display: flex;';
            $css .= 'justify-content: space-around;';
            $css .= 'text-align: center;';
            $css .= 'position: absolute;';
            $css .= 'top: 0;';
            $css .= 'right: 0;';
            $css .= 'bottom: 0;';
            $css .= 'left: 0;';
            $css .= 'margin: auto;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content a {';
            $css .= 'display: block;';
            $css .= 'color: #fff;';
            $css .= 'cursor: pointer;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content .m-title {';
            $css .= $title_fontsize_css;
            $css .= $title_lineheight_css;
            $css .= $title_color_css;
            $css .= 'font-family: Microsoft YaHei;';
            $css .= 'font-weight: 400;';
            $css .= 'color: #FFFFFF;';
            $css .= 'line-height: 60px;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content .m-title a {
            display: inline-block;
        }';
        $css .= $addon_id . ' .section-10  .menu-cover .content .m-title:hover {';
            $css .= $title_active_color_css;
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content .child .icon {';
            $css .= 'display: inline-block;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content .child .item {';
            $css .= 'font-family: Microsoft YaHei;';
            $css .= 'font-weight: 400;';
            $css .= $title_std_fontsize_css;
            $css .= $title_std_lineheight_css;
            $css .= $title_std_color_css;
//            $css .= 'opacity: 0.7;';
        $css .= '}';
        $css .= $addon_id . ' .section-10 .menu-cover .content .child .item:hover {';
            $css .= $title_std_active_color_css;
        $css .= '}';

        //适配一级导航文字 大小
        $title_fontsize_css_sm .= (isset($settings->title_fontsize_sm) && $settings->title_fontsize_sm) ? 'font-size:' . $settings->title_fontsize_sm . 'px;' : 'font-size:24px;';
        $title_fontsize_css_xs .= (isset($settings->title_fontsize_xs) && $settings->title_fontsize_xs) ? 'font-size:' . $settings->title_fontsize_xs . 'px;' : 'font-size:18px;';

        //适配一级导航文字 行高
        $title_lineheight_css_sm .= (isset($settings->title_lineheight_sm) && $settings->title_lineheight_sm) ? 'line-height:' . $settings->title_lineheight_sm . 'px;' : 'line-height:120px;';
        $title_lineheight_css_xs .= (isset($settings->title_lineheight_xs) && $settings->title_lineheight_xs) ? 'line-height:' . $settings->title_lineheight_xs . 'px;' : 'line-height:80px;';

        //适配二级导航文字 大小
        $title_std_fontsize_css_sm .= (isset($settings->title_std_fontsize_sm) && $settings->title_std_fontsize_sm) ? $settings->title_std_fontsize_sm : 20;
        $title_std_fontsize_css_xs .= (isset($settings->title_std_fontsize_xs) && $settings->title_std_fontsize_xs) ? $settings->title_std_fontsize_xs : 14;

        //适配二级导航文字 行高
        $title_std_lineheight_css_sm .= (isset($settings->title_std_lineheight_sm) && $settings->title_std_lineheight_sm) ? 'line-height:' . $settings->title_std_lineheight_sm . 'px;' : 'line-height:48px;';
        $title_std_lineheight_css_xs .= (isset($settings->title_std_lineheight_xs) && $settings->title_std_lineheight_xs) ? 'line-height:' . $settings->title_std_lineheight_xs . 'px;' : 'line-height:48px;';


        $css .= '
            @media (min-width: 768px) and (max-width: 991px) {
                ' . $addon_id . ' .section-10 .menu-cover .close {
                    top: 62px;
                    right: 45px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content {
                    width: 100%;
                    height: 100%;
                    display: block;
                    text-align: left;
                    overflow-y: auto;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item { 
                    border-bottom: solid #873F3C 1px;
                    padding: 0 35px;
                    overflow: hidden;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item:first-child {
                    margin-top: 100px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item:last-child {
                    border-bottom: none;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-title { 
                    ' . $title_lineheight_css_sm . '
                    font-weight: bold;
                    position: relative;
                    ' . $title_fontsize_css_sm . '
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-title:after {
                    content: "";
                    width: 30px;
                    height: 30px;
                    background: url("' . $settings->btn_bg . '") no-repeat center;
                    background-size: contain;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    right: 0;
                    margin: auto;
                    transform: rotateZ(-90deg);
				    transition: all ease-in-out 500ms;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child {
                    height: 0px;
				    transition: all ease-in-out 500ms;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .icon {
                    display: none;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item {
                    font-size: ' . $title_std_fontsize_css_sm . 'px;
                    ' .$title_std_lineheight_css_sm . '
                    /*margin-bottom: 38px;*/
                    padding-left: 30px;
                    position: relative;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .c-box {
                    /*margin-bottom: 38px;*/
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item:last-child {
                    margin-bottom: 0px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item:before {
                    content: "";
                    width: 15px;
                    height: 15px;
                    background: url("/components/com_jwpagefactory/addons/zdy_navigation/assets/images/more.png") no-repeat center;
                    background-size: contain;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    margin: auto;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item.active .m-title:after {
                    transform: rotateZ(0deg);
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item.active .child {
                    /*height: 300px;*/
                }
            }
            @media (max-width:768px) {

 
                  '.$addon_id . ' .section-10 .menu-btn .menu-icon {'
                 .'width: 45px;'
                 . 'height: 45px;'
                 . 'object-fit: contain;'
                 .'background: url("'.$settings->daohang_wap_bg.'") no-repeat center !important;'
                 . '}


                ' . $addon_id . ' .section-10 .menu-cover .close {
                    top: 24px;
                    right: 24px;
                    width: 18px;
                    height: 18px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content {
                    width: 100%;
                    height: 100%;
                    display: block;
                    text-align: left;
                    overflow-y: auto;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item:first-child {
                    margin-top: 70px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item { 
                    border-bottom: solid #873F3C 1px;
                    padding: 0 24px;
                    overflow: hidden;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item:last-child {
                    border-bottom: none;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-title { 
                    ' . $title_lineheight_css_xs . '
                    font-weight: bold;
                    position: relative;
                    ' . $title_fontsize_css_xs . '
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-title:after {
                    content: "";
                    width: 20px;
                    height: 20px;
                    background: url("' . $settings->btn_bg . '") no-repeat center;
                    background-size: contain;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    right: 0;
                    margin: auto;
                    transform: rotateZ(-90deg);
				    transition: all ease-in-out 500ms;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child {
                    height: 0px;
				    transition: all ease-in-out 500ms;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .icon {
                    display: none;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item {
                    font-size: ' . $title_std_fontsize_css_xs . 'px;                    
                    ' .$title_std_lineheight_css_xs . '
                    /*margin-bottom: 38px;*/
                    padding-left: 30px;
                    position: relative;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .c-box {
                    /*margin-bottom: 38px;*/
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item:last-child {
                    margin-bottom: 0px;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .child .item:before {
                    content: "";
                    width: 15px;
                    height: 15px;
                    background: url("/components/com_jwpagefactory/addons/zdy_navigation/assets/images/more.png") no-repeat center;
                    background-size: contain;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    margin: auto;
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item.active .m-title:after {
                    transform: rotateZ(0deg);
                }
                ' . $addon_id . ' .section-10 .menu-cover .content .m-item.active .child {
                    /*height: 300px;*/
                }
            }
        ';
        return $css;

    }

    public function scripts() {

    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $js = 'jQuery(function($){
            $("' . $addonId . ' .section-10 .menu-btn").click(function(){
                $("' . $addonId . ' .section-10 .menu-cover").fadeIn(500);
            })
            $("' . $addonId . ' .section-10 .menu-cover .close").click(function(){
                $("' . $addonId . ' .section-10 .menu-cover").fadeOut(500);
            });
            $("' . $addonId . ' .menu-cover .content .m-item").click(function(){
                let documentW = $(document).width();
                if(documentW <= 991){
                    let height = $(this).find(".c-box").outerHeight(true);
                    if($(this).hasClass("active")){
                        $(this).find(".child").css("height", 0);
                        $(this).removeClass("active");
                    }else {
                        $(this).find(".child").css("height", height);
                        $(this).addClass("active");
                    }
                }                
            }) 
        })';
        return $js;
    }
    public static function getTemplate()
    {
        $output ='
        <#
            var addonId = "#jwpf-addon-" + data.id;
            var btn_bg = data.btn_bg;
            var daohang_bg = data.daohang_bg;
        #>
        <style type="text/css">
            {{ addonId }} .section-10 {
                width: 100%;
                height: 100%;
                position: relative;
            }
            {{ addonId }} .section-10 .menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            {{ addonId }} .section-10 .menu-btn .menu-icon {
                width: 45px;
                height: 45px;
                object-fit: contain;
            }
            {{ addonId }} .section-10 .menu-cover {
                position: fixed;
                width: 100%;
                height: 100%;
                left: 0;
                top: 0;
                background: url("https://oss.lcweb01.cn/joomla/20210705/8a067bd046566dbdd4f234a9f8ad2f34.png") no-repeat;
                background-size: cover;
                display: none;
            }
            {{ addonId }} .section-10 .menu-cover .close {
                position: absolute;
                top: 54px;
                right: 57px;
                width: 24px;
                height: 24px;
                background: url("{{data.close_bg}}") no-repeat;
                background-size: 100%;
                cursor: pointer;
                opacity: 1;
            }
            {{ addonId }} .section-10 .menu-cover .content {
               width: 1100px;
               height: max-content;
               display: flex;
               justify-content: space-around;
               text-align: center;
               position: absolute;
               top: 0;
               right: 0;
               bottom: 0;
               left: 0;
               margin: auto;
            }
            {{ addonId }} .section-10 .menu-cover .content a {
               display: block;
               color: #fff;
               cursor: pointer;
            }
            {{ addonId }} .section-10 .menu-cover .content .m-title {
               font-size: {{data.title_fontsize}}px;
               color: {{data.title_color}};
               line-height:{{data.title_lineheight}}px;
               font-family: Microsoft YaHei;
               font-weight: 400;
            }
            {{ addonId }} .section-10  .menu-cover .content .m-title:hover {
                color: {{data.title_active_color}};
            }
            {{ addonId }} .section-10 .menu-cover .content .child .icon {
                display: inline-block;
            }
            {{ addonId }} .section-10 .menu-cover .content .child .item {
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: {{data.title_std_fontsize}}px;
                color: {{data.title_std_color}};
                line-height:{{data.title_std_lineheight}}px;
                /* opacity: 0.7; */
            }
            {{ addonId }} .section-10 .menu-cover .content .child .item:hover {
                color: {{data.title_std_active_color}};
            }
        </style>
        <div class="section-10">
            <div class="menu-btn"><img src=\'{{ daohang_bg }}\' class="menu-icon" alt="" /></div>
            <div class="menu-cover">
                <div class="close"></div>
                <div class="content">
                    <# _.each(data.zdy_navigation_item, function (value, key){ #>
                    <div class="m-item">
                        <a class="m-title" href="#{{ value.title_id }}">{{ value.title }}</a>
                        <# if(value.isstd) { #>
                            <div class="child">   
                                <# if(value.istudiao) { #>
                                    <img   src=\'{{ btn_bg }}\'  class="icon" alt="">
                                <# } #>  
                                <div class="c-box"> 
                                    <# _.each(data.zdy_navigation_std_item, function (val, k){ #>
                                        <# if(value.title_std===val.std_title_std) { #>
                                            <a class="item" href="#{{val.std_title_id}}">{{val.std_title}}</a>
                                        <# } #>  
                                    <# }); #>  
                                </div>
                           </div>
                        <# } #>
                    </div>
                    <# }); #>
                </div>
            </div>
        </div>
        ';

        return $output;
    }
}
