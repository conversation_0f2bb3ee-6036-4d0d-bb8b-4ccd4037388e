<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'support_window',
        'title' => JText::_('客服浮窗'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '龙采官网插件',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'type_select' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'values' => array(
                        'type01' => '客服',
                        'type02' => '分享',
                    ),
                    'std' => 'type01'
                ),
                'title_desc' => array(
                    'type' => 'text',
                    'title' => JText::_('滑过显示内容'),
                    'desc' => JText::_('滑过显示内容'),
                    'std' => 'Hi，我是你的客服龙龙，有需要随时找我哦~',
                    'depends' => array(
                        array('type_select', '=', 'type01'),
                    ),
                ),
                'img_cw' => array(
                    'type' => 'media',
                    'title' => JText::_('悬浮图片'),
                    'desc' => JText::_('悬浮图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/b881f40c559a7f021cd56f1e1cf2f7ff.gif',
                ),
                'tz_page_type' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(
                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                    ),
                    'std' => 'external_links',
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                ),
                'detail_page' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(array('tz_page_type', '=', 'external_links')),
                    'std' => '',
                ),
                'target' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                    ),
                ),
                'img_ewm' => array(
                    'type' => 'media',
                    'title' => JText::_('微信二维码'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220607/9071976a458df5e8fce7651c26a07907.jpg',
                    'depends' => array(
                        array('type_select', '=', 'type02'),
                    ),
                ),
                'weibo_url' => array(
                    'type' => 'text',
                    'title' => '微博链接',
                    'std' => 'https://weibo.com/p/1006062488214857/home?from=page_100606&mod=TAB#place',
                    'depends' => array(
                        array('type_select', '=', 'type02'),
                    ),
                ),
                'type2_top' => array(
                    'type' => 'slider',
                    'title' => '头部距离',
                    'std' => '200',
                    'max' => '1000',
                    'depends' => array(
                        array('type_select', '=', 'type02'),
                    ),
                ),
            ),
        ),
    )
);
