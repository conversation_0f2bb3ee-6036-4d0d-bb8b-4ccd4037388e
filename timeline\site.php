<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonTimeline extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;

		$addon_id = '#jwpf-addon-' . $this->addon->id;

		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';

		$output = '';
        if($style == 'style1'){
            $output .= '<div class="jwpf-addon jwpf-addon-timeline pc ' . $class . '">';
            $output .= '<div class="jwpf-addon-timeline-text-wrap">';
            $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
            $output .= '</div>'; //.jwpf-addon-instagram-text-wrap

            $output .= '<div class="jwpf-addon-timeline-wrapper">';

            foreach ($settings->jw_timeline_items as $key => $timeline) {
                $oddeven = (round($key % 2) == 0) ? 'even' : 'odd';
                $output .= '<div class="jwpf-row timeline-movement ' . $oddeven . '">';
                $output .= '<div class="timeline-badge"></div>';
                if ($oddeven == 'odd') {
                    $output .= '<div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">';
                    if (isset($timeline->date) && $timeline->date) {
                        $output .= '<p class="timeline-date text-right">' . $timeline->date . '</p>';
                    }
                    $output .= '</div>';
                    $output .= '<div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">';
                    $output .= '<div class="timeline-panel">';

                    $clsphoto = '';
                    if (isset($timeline->photo) && $timeline->photo) {
                        $output .= '<div class="photo"><img src="' . $timeline->photo . '" /></div>';
                        $clsphoto = 'hasphoto';
                    }
                    if (isset($timeline->title) && $timeline->title) {
                        $output .= '<p class="title ' . $clsphoto . '">' . $timeline->title . '</p>';
                    }
                    if (isset($timeline->content) && $timeline->content) {
                        $output .= '<div class="details ' . $clsphoto . '">' . $timeline->content . '</div>';
                    }
                    $output .= '</div>';
                    $output .= '</div>';
                } elseif ($oddeven == 'even') {
                    $output .= '<div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item mobile-block">';
                    if (isset($timeline->date) && $timeline->date) {
                        $output .= '<p class="timeline-date text-left">' . $timeline->date . '</p>';
                    }
                    $output .= '</div>';
                    $output .= '<div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">';
                    $output .= '<div class="timeline-panel left-part">';

                    $clsphoto = '';
                    if (isset($timeline->photo) && $timeline->photo) {
                        $output .= '<div class="photo"><img src="' . $timeline->photo . '" /></div>';
                        $clsphoto = 'hasphoto';
                    }
                    if (isset($timeline->title) && $timeline->title) {
                        $output .= '<p class="title ' . $clsphoto . '">' . $timeline->title . '</p>';
                    }
                    if (isset($timeline->content) && $timeline->content) {
                        $output .= '<div class="details ' . $clsphoto . '">' . $timeline->content . '</div>';
                    }
                    $output .= '</div>';
                    $output .= '</div>';
                    $output .= '<div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item mobile-hidden">';
                    if (isset($timeline->date) && $timeline->date) {
                        $output .= '<p class="timeline-date text-left">' . $timeline->date . '</p>';
                    }
                    $output .= '</div>';
                }
                $output .= '</div>'; //.timeline-movement
            } // foreach timelines

            $output .= '</div>'; //.Timeline

            $output .= '</div>'; //.jwpf-addon-instagram-gallery

            //        是否需要附加样式
            $need_additional = (isset($settings->need_additional) && ($settings->need_additional || $settings->need_additional == 0)) ? $settings->need_additional : 0;
            $additional = (isset($settings->additional) && $settings->additional) ? $settings->additional : '';
            $dot_sincere_style1 = (isset($settings->dot_sincere_style1) && ($settings->dot_sincere_style1 || $settings->dot_sincere_style1 == 0)) ? $settings->dot_sincere_style1 : 0;
            if (isset($settings->content_text_margin_top_style1) && $settings->content_text_margin_top_style1) {
                if (is_object($settings->content_text_margin_top_style1)) {
                    $content_text_margin_top_style1 = $settings->content_text_margin_top_style1->md;
                    $content_text_margin_top_style1_sm = $settings->content_text_margin_top_style1->sm;
                    $content_text_margin_top_style1_xs = $settings->content_text_margin_top_style1->xs;
                } else {
                    $content_text_margin_top_style1 = $settings->content_text_margin_top_style1;
                    $content_text_margin_top_style1_sm = $settings->content_text_margin_top_style1_sm;
                    $content_text_margin_top_style1_xs = $settings->content_text_margin_top_style1_xs;
                }
            } else {
                $content_text_margin_top_style1 = 10;
                $content_text_margin_top_style1_sm = 10;
                $content_text_margin_top_style1_xs = 10;
            }
            if (isset($settings->content_title_margin_top_style1) && $settings->content_title_margin_top_style1) {
                if (is_object($settings->content_title_margin_top_style1)) {
                    $content_title_margin_top_style1 = $settings->content_title_margin_top_style1->md;
                    $content_title_margin_top_style1_sm = $settings->content_title_margin_top_style1->sm;
                    $content_title_margin_top_style1_xs = $settings->content_title_margin_top_style1->xs;
                } else {
                    $content_title_margin_top_style1 = $settings->content_title_margin_top_style1;
                    $content_title_margin_top_style1_sm = $settings->content_title_margin_top_style1_sm;
                    $content_title_margin_top_style1_xs = $settings->content_title_margin_top_style1_xs;
                }
            } else {
                $content_title_margin_top_style1 = 20;
                $content_title_margin_top_style1_sm = 20;
                $content_title_margin_top_style1_xs = 20;
            }

            if ($need_additional == 1) {
                if ($additional === 'style1') {
                    $output.='<div class="content mobile">';
                    foreach ($settings->jw_timeline_items as $key => $timeline){
                        $oddeven = (round($key % 2) == 0) ? 'y1' : 'y2';
                        $oddevenDot = (round($key % 2) == 0) ? 'dian1' : 'dian2';
                        $output.='<div class="'.$oddeven.'">
                                    <div class="'.$oddevenDot.'"></div>
                                    <div class="year">';
                        if (isset($timeline->date) && $timeline->date) {
                            $output.='<div class="y">'.$timeline->date.'</div>';
                        }
                        if (isset($timeline->photo) && $timeline->photo) {
                            $output.='<div class="p"><img src="'.$timeline->photo.'" alt=""></div>';
                        }
                        $output.='<div class="clear"></div>
                                    </div>';
                        if (isset($timeline->title) && $timeline->title) {
                            $output.='<div class="title">'.$timeline->title.'</div>';
                        }
                        if (isset($timeline->date) && $timeline->date) {
                            $output .= '<div class="text">' . $timeline->content . '</div>';
                        }
                        $output.='</div>';
                    }
                    $output.='</div>';
                    $output.='<script>
                        let contentTop='.$content_text_margin_top_style1.';
                        let titleTop='.$content_title_margin_top_style1.';
                        if($(window).width()<992 && $(window).width()>768){
                            contentTop='.$content_text_margin_top_style1_sm.';
                            titleTop='.$content_title_margin_top_style1_sm.';
                        }
                        $("'.$addon_id.' .pc .timeline-item .timeline-panel").each(function(i,v){
                            let detail = $(v).find(".details");
                            let photo = $(v).find(".photo");
                            let title = $(v).find(".title");
                            photo.find("img").on("load",function (){
                                console.log(detail.innerHeight() -  contentTop - 60,photo.height());
                                if(detail.innerHeight() -  contentTop - 60> photo.height()) {
                                    photo.css({
                                        "width":"100%",
                                        "float":"none"
                                    });
                                    detail.css({
                                        "max-width":"none",
                                        "margin-top":contentTop+"px"
                                    });
                                    title.css({
                                        "margin-top":titleTop+"px"
                                    })
                                }
                            })
                        })
                    </script>';
                }
            }
        }
        elseif($style == 'style2'){
            $timeline_items_style2 = (isset($settings->timeline_items_style2) && $settings->timeline_items_style2) ? $settings->timeline_items_style2 : array();

            $output .= '<div class="jwpf-addon jwpf-addon-timeline pc ' . $class . '">';
                $output .= '<div class="jwpf-addon-timeline-text-wrap">';
                $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
                $output .= '</div>'; 
                $output .= '<!--公司简介时间轴-->'; 
                $output .= '<div class="rongyu_b">
                    <ul class="bd">';
                        foreach ( $timeline_items_style2 as $key => $value ) { 
                            $output .= '<li class="'.($key === 0 ? 'on' : '').'">'.$value -> date.'</li>';
                        }
                    $output .= '</ul>
                    <span class="prev prevStop"></span>
                    <span class="next"></span>
                </div>';
                foreach ( $timeline_items_style2 as $key => $value ) { 
                    $output .= '<div class="rongyu_wrap '.($key === 0 ? 'active' : '').'">'; 
                        $output .= '<div class="rongyu_c">';
                            $output .= '<div class="rongyu_list" style="display: block;">';
                                foreach ( $value -> list_title as $list_key => $list_value ) {
                                    $output .= '<div>';
                                        $output .= '<div class="rongyu_title">';
                                        $output .= '<h2>'.$list_value -> title.'</h2>';
                                        $output .= '</div>';
                                        $output .= '<div class="rongyu_content">';
                                            $output .= $list_value -> content;
                                        $output .= '</div>';
                                    $output .= '</div>';
                                }
                            $output .= '</div>
                        </div>';
                    $output .= '</div>'; 
                }
            $output .= '</div>'; 
            $output .= '<script src="'.JURI::base(true) . '/components/com_jwpagefactory/addons/timeline/assets/js/jquery.SuperSlide.min.js"></script>';
            $output .= '<script>
                let visibleNum = 6;
                if(window.innerWidth > 768 && window.innerWidth < 992){
                    visibleNum = 4;
                }else if(window.innerWidth <= 768){
                    visibleNum = 2;
                }
                $(".rongyu_b").slide({titCell:".hd ul",mainCell:".bd",autoPage:true,effect:"left",vis:visibleNum,trigger:"click",pnLoop:false,
                    startFun:function(i){
                        $(".tempWrap").css("width","100%");
                        $(".rongyu_b .bd").css("padding-right","100px");
                    }
                });
                $(".rongyu_b ul li").hover(function(){
                    $(this).addClass("on").siblings().removeClass("on");
                    $(".rongyu_wrap").eq($(this).index()).show().siblings(".rongyu_wrap").hide();
                })
                $(".rongyu_b ul li").first().addClass("on");
                $(".rongyu_c ul").first().show();
            </script>';
        }

		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';

		$bar_color = (isset($settings->bar_color) && $settings->bar_color) ? $settings->bar_color : '#0095eb';

		$css = '';

        if($style == 'style1'){
            if ($bar_color) {
                $css .= $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before, ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:after, ' . $addon_id . ' .jwpf-addon-timeline .timeline-movement.even:before{';
                $css .= 'background-color: ' . $bar_color . ';';
                $css .= '}';

                $css .= $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:before, ' . $addon_id . ' .jwpf-addon-timeline .timeline-movement.even:after{';
                $css .= 'border-color: ' . $bar_color . ';';
                $css .= '}';
            }

            //        附加样式
            $need_additional = (isset($settings->need_additional) && ($settings->need_additional || $settings->need_additional == 0)) ? $settings->need_additional : 0;
            $additional = (isset($settings->additional) && $settings->additional) ? $settings->additional : '';
            $dot_sincere_style1 = (isset($settings->dot_sincere_style1) && ($settings->dot_sincere_style1 || $settings->dot_sincere_style1 == 0)) ? $settings->dot_sincere_style1 : 0;
            $dot_size_style1 = (isset($settings->dot_size_style1) && ($settings->dot_size_style1 || $settings->dot_size_style1 == 0)) ? $settings->dot_size_style1 : 11;
            $line_width_style1 = (isset($settings->line_width_style1) && ($settings->line_width_style1 || $settings->line_width_style1 == 0)) ? $settings->line_width_style1 : 1;
            if (isset($settings->time_font_size_style1) && $settings->time_font_size_style1) {
                if (is_object($settings->time_font_size_style1)) {
                    $time_font_size_style1 = $settings->time_font_size_style1->md;
                    $time_font_size_style1_sm = $settings->time_font_size_style1->sm;
                    $time_font_size_style1_xs = $settings->time_font_size_style1->xs;
                } else {
                    $time_font_size_style1 = $settings->time_font_size_style1;
                    $time_font_size_style1_sm = $settings->time_font_size_style1_sm;
                    $time_font_size_style1_xs = $settings->time_font_size_style1_xs;
                }
            } else {
                $time_font_size_style1 = 32;
                $time_font_size_style1_sm = 32;
                $time_font_size_style1_xs = 28;
            }
            $time_color_style1 = (isset($settings->time_color_style1) && $settings->time_color_style1) ? $settings->time_color_style1 : '#0095eb';
            $time_margin_left_style1 = (isset($settings->time_margin_left_style1) && $settings->time_margin_left_style1) ? $settings->time_margin_left_style1 : 7;
            if (isset($settings->content_width_style1) && $settings->content_width_style1) {
                if (is_object($settings->content_width_style1)) {
                    $content_width_style1 = $settings->content_width_style1->md;
                    $content_width_style1_sm = $settings->content_width_style1->sm;
                    $content_width_style1_xs = $settings->content_width_style1->xs;
                } else {
                    $content_width_style1 = $settings->content_width_style1;
                    $content_width_style1_sm = $settings->content_width_style1_sm;
                    $content_width_style1_xs = $settings->content_width_style1_xs;
                }
            } else {
                $content_width_style1 = 80;
                $content_width_style1_sm = 80;
                $content_width_style1_xs = 100;
            }
            if (isset($settings->img_max_width_style1) && $settings->img_max_width_style1) {
                if (is_object($settings->img_max_width_style1)) {
                    $img_max_width_style1 = $settings->img_max_width_style1->md;
                    $img_max_width_style1_sm = $settings->img_max_width_style1->sm;
                    $img_max_width_style1_xs = $settings->img_max_width_style1->xs;
                } else {
                    $img_max_width_style1 = $settings->img_max_width_style1;
                    $img_max_width_style1_sm = $settings->img_max_width_style1_sm;
                    $img_max_width_style1_xs = $settings->img_max_width_style1_xs;
                }
            } else {
                $img_max_width_style1 = 131;
                $img_max_width_style1_sm = 131;
                $img_max_width_style1_xs = 138;
            }
            $content_text_margin_left_style1 = (isset($settings->content_text_margin_left_style1) && $settings->content_text_margin_left_style1) ? $settings->content_text_margin_left_style1 : 28;
            if (isset($settings->content_text_font_size_style1) && $settings->content_text_font_size_style1) {
                if (is_object($settings->content_text_font_size_style1)) {
                    $content_text_font_size_style1 = $settings->content_text_font_size_style1->md;
                    $content_text_font_size_style1_sm = $settings->content_text_font_size_style1->sm;
                    $content_text_font_size_style1_xs = $settings->content_text_font_size_style1->xs;
                } else {
                    $content_text_font_size_style1 = $settings->content_text_font_size_style1;
                    $content_text_font_size_style1_sm = $settings->content_text_font_size_style1_sm;
                    $content_text_font_size_style1_xs = $settings->content_text_font_size_style1_xs;
                }
            } else {
                $content_text_font_size_style1 = 16;
                $content_text_font_size_style1_sm = 16;
                $content_text_font_size_style1_xs = 14;
            }
            if (isset($settings->content_text_line_height_style1) && $settings->content_text_line_height_style1) {
                if (is_object($settings->content_text_line_height_style1)) {
                    $content_text_line_height_style1 = $settings->content_text_line_height_style1->md;
                    $content_text_line_height_style1_sm = $settings->content_text_line_height_style1->sm;
                    $content_text_line_height_style1_xs = $settings->content_text_line_height_style1->xs;
                } else {
                    $content_text_line_height_style1 = $settings->content_text_line_height_style1;
                    $content_text_line_height_style1_sm = $settings->content_text_line_height_style1_sm;
                    $content_text_line_height_style1_xs = $settings->content_text_line_height_style1_xs;
                }
            } else {
                $content_text_line_height_style1 = 37;
                $content_text_line_height_style1_sm = 37;
                $content_text_line_height_style1_xs = 24;
            }
            $content_text_color_style1 = (isset($settings->content_text_color_style1) && $settings->content_text_color_style1) ? $settings->content_text_color_style1 : '#444';
            $img_max_height_style1 = (isset($settings->img_max_height_style1) && $settings->img_max_height_style1) ? $settings->img_max_height_style1 : 100;
            $year_text_margin_left_style1 = (isset($settings->year_text_margin_left_style1) && $settings->year_text_margin_left_style1) ? $settings->year_text_margin_left_style1 : 10;
            if (isset($settings->content_text_margin_top_style1) && $settings->content_text_margin_top_style1) {
                if (is_object($settings->content_text_margin_top_style1)) {
                    $content_text_margin_top_style1 = $settings->content_text_margin_top_style1->md;
                    $content_text_margin_top_style1_sm = $settings->content_text_margin_top_style1->sm;
                    $content_text_margin_top_style1_xs = $settings->content_text_margin_top_style1->xs;
                } else {
                    $content_text_margin_top_style1 = $settings->content_text_margin_top_style1;
                    $content_text_margin_top_style1_sm = $settings->content_text_margin_top_style1_sm;
                    $content_text_margin_top_style1_xs = $settings->content_text_margin_top_style1_xs;
                }
            } else {
                $content_text_margin_top_style1 = 10;
                $content_text_margin_top_style1_sm = 10;
                $content_text_margin_top_style1_xs = 10;
            }
            $img_float_style1 = (isset($settings->img_float_style1) && ($settings->img_float_style1 || $settings->img_float_style1 == 0)) ? $settings->img_float_style1 : 1;
            if (isset($settings->content_title_font_size_style1) && $settings->content_title_font_size_style1) {
                if (is_object($settings->content_title_font_size_style1)) {
                    $content_title_font_size_style1 = $settings->content_title_font_size_style1->md;
                    $content_title_font_size_style1_sm = $settings->content_title_font_size_style1->sm;
                    $content_title_font_size_style1_xs = $settings->content_title_font_size_style1->xs;
                } else {
                    $content_title_font_size_style1 = $settings->content_title_font_size_style1;
                    $content_title_font_size_style1_sm = $settings->content_title_font_size_style1_sm;
                    $content_title_font_size_style1_xs = $settings->content_title_font_size_style1_xs;
                }
            } else {
                $content_title_font_size_style1 = 16;
                $content_title_font_size_style1_sm = 16;
                $content_title_font_size_style1_xs = 16;
            }
            $content_title_color_style1 = (isset($settings->content_title_color_style1) && $settings->content_title_color_style1) ? $settings->content_title_color_style1 : '#444';


            if ($need_additional == 1) {
                if ($additional === 'style1') {
                    if ($dot_sincere_style1 == 1) {
                        $css .= $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:before{
                            background-color: ' . $bar_color . ';
                            width: ' . $dot_size_style1 . 'px;
                            height: ' . $dot_size_style1 . 'px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:after{
                            display: none;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before{
                            width: ' . $line_width_style1 . 'px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-date{
                            font-size: ' . $time_font_size_style1 . 'px;
                            color: ' . $time_color_style1 . ';
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement.even .timeline-item .timeline-date{
                            margin-left: ' . $time_margin_left_style1 . 'px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement.odd .timeline-item .timeline-date{
                            margin-right: ' . $time_margin_left_style1 . 'px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel{
                            border: none;
                            width: ' . $content_width_style1 . '%;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel::after{
                            content: "";
                            display: block;
                            clear: both;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel.left-part:before,
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel:before{
                            display: none;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel.left-part .photo{
                            float: left;
                            margin-right: '.$content_text_margin_left_style1.'px;
                            margin-left: 0;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                            float: right;
                            max-width: '.$img_max_width_style1.'px;
                            margin-left: '.$content_text_margin_left_style1.'px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .details.hasphoto{
                            font-size: '.$content_text_font_size_style1.'px;
                            line-height: '.$content_text_line_height_style1.'px;
                            color: '.$content_text_color_style1.';
                            float: left;
                            max-width: calc(100% - '.($img_max_width_style1 + $content_text_margin_left_style1).'px);
                            text-align: left;
                        }
                        ' . $addon_id . ' .pc{
                            display: block;
                        }
                        ' . $addon_id . ' .mobile{
                            display: none;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before{
                            top: 33px;
                        }
                        ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .title{
                            float: left;
                            font-size: '.$content_title_font_size_style1.'px;
                            color: '.$content_title_color_style1.';
                        }
                        @media (max-width:992px) and (min-width:768px){
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel{
                                border: none;
                                width: ' . $content_width_style1_sm . '%;
                            }
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                                max-width: '.$img_max_width_style1_sm.'px;
                            }
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-date{
                                font-size: ' . $time_font_size_style1_sm . 'px;
                            }                        
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .details.hasphoto{
                                font-size: '.$content_text_font_size_style1_sm.'px;
                                line-height: '.$content_text_line_height_style1_sm.'px;
                            }
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .title{
                                font-size: '.$content_title_font_size_style1_sm.'px;
                            }
                        }
                        @media (max-width:767px){
                            ' . $addon_id . ' .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                                max-width: '.$img_max_width_style1_xs.'px;
                            }
                            ' . $addon_id . ' .pc{
                                display: none;
                            }
                            ' . $addon_id . ' .mobile{
                                display: block;
                            }
                            ' . $addon_id . ' .content {
                                width: 100%;
                                padding-top: 20px;
                                padding-bottom: 20px;
                            }
                            ' . $addon_id . ' .y1 {
                                width: 80%;
                                margin-top: -2px;
                                margin-left: 50px;
                                border: 2px solid '.$bar_color.';
                                border-radius: 0 25px 25px 0;
                                border-left: none;
                                padding-top: 20px;
                                padding-bottom: 20px;
                            }
                            ' . $addon_id . ' .dian1 {
                                width: 10px;
                                height: 10px;
                                background-color: '.$bar_color.';
                                border-radius: 50%;
                                margin-top: -26px;
                            }
                            ' . $addon_id . ' .content .year::after{
                                content: "";
                                display: block;
                                clear: both;
                            }
                            ' . $addon_id . ' .content .year .y {
                                font-size: '.$time_font_size_style1_xs.'px;
                                font-family: PingFangSC-Semibold, PingFang SC;
                                font-weight: 600;
                                color: '.$time_color_style1.';
                                letter-spacing: 1px;
                                height: '.($img_float_style1==1?$img_max_height_style1."px":"auto").';
                                line-height: '.($img_float_style1==1?$img_max_height_style1."px":"2").';
                                float: '.($img_float_style1==1?'left':'none').';
                            }
                            ' . $addon_id . ' .content .year .p {
                                width: '.$img_max_width_style1_xs.'px;
                                height: '.$img_max_height_style1.'px;
                                margin-left: '.($img_float_style1==1?$year_text_margin_left_style1:'0').'px;
                                float: '.($img_float_style1==1?'left':'none').';
                                position: relative;
                            }
                            ' . $addon_id . ' .content img {
                                max-width: 100%;
                                max-height: 100%;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                            }
                            ' . $addon_id . ' .content .text {
                                width: 87%;
                                font-size: '.$content_text_font_size_style1_xs.'px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: '.$content_text_color_style1.';
                                line-height: '.$content_text_line_height_style1_xs.'px;
                                margin-top: '.$content_text_margin_top_style1_xs.'px;
                            }
                            ' . $addon_id . ' .y2 {
                                width: 80%;
                                margin-top: -2px;
                                margin-left: 22px;
                                border: 2px solid '.$bar_color.';
                                border-radius: 25px 0 0 25px;
                                border-right: none;
                                padding-top: 20px;
                                padding-bottom: 20px;
                            }
                            ' . $addon_id . ' .dian2 {
                                width: 10px;
                                height: 10px;
                                background-color: '.$bar_color.';
                                border-radius: 50%;
                                margin-top: -26px;
                                margin-left: 26px;
                            }
                            ' . $addon_id . ' .content .y2 > .year{
                                width: 100%;
                            }
                            ' . $addon_id . ' .content .y2 > .year, .y2 > .text,' . $addon_id . ' .content .y2 .title {
                                margin-left: 25px;
                            }
                            ' . $addon_id . ' .content .y2 .title{
                                width: 87%;
                            }
                            ' . $addon_id . ' .content .title{
                                font-size: '.$content_title_font_size_style1_xs.'px;
                            }
                        }';
                    }
                }
            }
        }
        elseif($style == 'style2'){
            $timeline_padding_style2 = (isset($settings->timeline_padding_style2) && $settings->timeline_padding_style2) ? $settings->timeline_padding_style2 : '38px 0 0 0';
            $timeline_bg_img_style2 = (isset($settings->timeline_bg_img_style2) && $settings->timeline_bg_img_style2) ? $settings->timeline_bg_img_style2 : 'https://oss.lcweb01.cn/joomla/20230111/587dc2ade0a77d464f2ef2366501ceeb.jpg';
            $timeline_bg_img_offset_x_style2 = (isset($settings->timeline_bg_img_offset_x_style2) && $settings->timeline_bg_img_offset_x_style2) ? $settings->timeline_bg_img_offset_x_style2 : '0';
            $timeline_bg_img_offset_y_style2 = (isset($settings->timeline_bg_img_offset_y_style2) && ($settings->timeline_bg_img_offset_y_style2 || $settings->timeline_bg_img_offset_y_style2 == 0)) ? $settings->timeline_bg_img_offset_y_style2 : '8';
            $timeline_height_style2 = (isset($settings->timeline_height_style2) && $settings->timeline_height_style2) ? $settings->timeline_height_style2 : '70';
            $timeline_date_item_margin_style2 = (isset($settings->timeline_date_item_margin_style2) && $settings->timeline_date_item_margin_style2) ? $settings->timeline_date_item_margin_style2 : '0 27px 0 86px';
            $timeline_date_item_padding_style2 = (isset($settings->timeline_date_item_padding_style2) && $settings->timeline_date_item_padding_style2) ? $settings->timeline_date_item_padding_style2 : '25px 0 0 0';
            $timeline_date_item_width_style2 = (isset($settings->timeline_date_item_width_style2) && $settings->timeline_date_item_width_style2) ? $settings->timeline_date_item_width_style2 : '50';
            $timeline_date_on_img_style2 = (isset($settings->timeline_date_on_img_style2) && $settings->timeline_date_on_img_style2) ? $settings->timeline_date_on_img_style2 : 'https://oss.lcweb01.cn/joomla/20230111/5981e24c87ebd4f060188fd6807c7dd8.jpg';
            $timeline_date_on_offset_x_style2 = (isset($settings->timeline_date_on_offset_x_style2) && $settings->timeline_date_on_offset_x_style2) ? $settings->timeline_date_on_offset_x_style2 : '0';
            $timeline_date_on_offset_y_style2 = (isset($settings->timeline_date_on_offset_y_style2) && ($settings->timeline_date_on_offset_y_style2 || $settings->timeline_date_on_offset_y_style2 == 0)) ? $settings->timeline_date_on_offset_y_style2 : '9';
            $timeline_button_left_style2 = (isset($settings->timeline_button_left_style2) && $settings->timeline_button_left_style2) ? $settings->timeline_button_left_style2 : 'https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg';
            $timeline_button_offset_left_x_style2 = (isset($settings->timeline_button_offset_left_x_style2) && $settings->timeline_button_offset_left_x_style2) ? $settings->timeline_button_offset_left_x_style2 : '0';
            $timeline_button_offset_left_y_style2 = (isset($settings->timeline_button_offset_left_y_style2) && $settings->timeline_button_offset_left_y_style2) ? $settings->timeline_button_offset_left_y_style2 : '0';
            $timeline_button_right_style2 = (isset($settings->timeline_button_right_style2) && $settings->timeline_button_right_style2) ? $settings->timeline_button_right_style2 : 'https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg';
            $timeline_button_offset_right_x_style2 = (isset($settings->timeline_button_offset_right_x_style2) && ($settings->timeline_button_offset_right_x_style2 || $settings->timeline_button_offset_right_x_style2 == 0)) ? $settings->timeline_button_offset_right_x_style2 : '-56';
            $timeline_button_offset_right_y_style2 = (isset($settings->timeline_button_offset_right_y_style2) && $settings->timeline_button_offset_right_y_style2) ? $settings->timeline_button_offset_right_y_style2 : '0';
            $timeline_button_width_style2 = (isset($settings->timeline_button_width_style2) && $settings->timeline_button_width_style2) ? $settings->timeline_button_width_style2 : '24';
            $timeline_button_height_style2 = (isset($settings->timeline_button_height_style2) && $settings->timeline_button_height_style2) ? $settings->timeline_button_height_style2 : '24';
            $timeline_button_offset_x_style2 = isset($settings->timeline_button_offset_x_style2) ? $settings->timeline_button_offset_x_style2 : '-30';
            $timeline_button_offset_y_style2 = isset($settings->timeline_button_offset_y_style2) ? $settings->timeline_button_offset_y_style2 : '38';
            $list_title_style2 = isset($settings->list_title_style2) ? $settings->list_title_style2 : '24';
            $list_title_padding_style2 = isset($settings->list_title_padding_style2) ? $settings->list_title_padding_style2 : '10px 0 25px 30px';
            $list_title_color_style2 = isset($settings->list_title_color_style2) ? $settings->list_title_color_style2 : '#666';
            $list_content_size_style2 = isset($settings->list_content_size_style2) ? $settings->list_content_size_style2 : '13';
            $list_content_padding_style2 = isset($settings->list_content_padding_style2) ? $settings->list_content_padding_style2 : '0 0 0 30px';
            $list_content_column = isset($settings->list_content_column) ? $settings->list_content_column : '2';
            $list_padding = isset($settings->list_padding) ? $settings->list_padding : '0 0 0 0';
            $list_hidden_style2 = isset($settings->list_hidden_style2) ? $settings->list_hidden_style2 : '1';
            $list_content_color_style2 = isset($settings->list_content_color_style2) ? $settings->list_content_color_style2 : '#666';
            $list_line_height_style2 = isset($settings->list_line_height_style2) ? $settings->list_line_height_style2 : '35';

            $css .= $addon_id . ' *{
                margin: 0;
                padding: 0;
                list-style: none;
            }
            '. $addon_id . ' .rongyu_b, '. $addon_id . ' .licheng_a {
                position: relative;
                padding: '.$timeline_padding_style2.';
            }
            '. $addon_id . ' .rongyu_b .bd, '. $addon_id . ' .licheng_a .bd {
                background: url("'.$timeline_bg_img_style2.'") repeat-x '.$timeline_bg_img_offset_x_style2.'px '.$timeline_bg_img_offset_y_style2.'px;
                height: '.$timeline_height_style2.'px;
                box-sizing: content-box;
            }
            '. $addon_id . ' .rongyu_b .bd li, '. $addon_id . ' .licheng_a .bd li {
                float: left;
                margin: '.$timeline_date_item_margin_style2.';
                padding: '.$timeline_date_item_padding_style2.';
                font-weight: 700;
                cursor: pointer;
                width: '.$timeline_date_item_width_style2.'px;
            }
            '. $addon_id . ' .rongyu_b .bd .on, '. $addon_id . ' .licheng_a .bd .on {
                background: url("'.$timeline_date_on_img_style2.'") no-repeat '.$timeline_date_on_offset_x_style2.'px '.$timeline_date_on_offset_y_style2.'px;
            }
            '. $addon_id . ' .rongyu_b .prev, '. $addon_id . ' .licheng_a .prev {
                background: url("'.$timeline_button_left_style2.'") no-repeat '.$timeline_button_offset_left_x_style2.'px '.$timeline_button_offset_left_y_style2.'px;
                display: inline-block;
                width: '.$timeline_button_width_style2.'px;
                height: '.$timeline_button_height_style2.'px;
                position: absolute;
                left: '.$timeline_button_offset_x_style2.'px;
                cursor: pointer;
                top: '.$timeline_button_offset_y_style2.'px;
            }
            '. $addon_id . ' .rongyu_b .next, '. $addon_id . ' .licheng_a .next {
                background: url("'.$timeline_button_right_style2.'") no-repeat '.$timeline_button_offset_right_x_style2.'px '.$timeline_button_offset_right_y_style2.'px;
                display: inline-block;
                width: '.$timeline_button_width_style2.'px;
                height: '.$timeline_button_height_style2.'px;
                position: absolute;
                right: '.$timeline_button_offset_x_style2.'px;
                cursor: pointer;
                top: '.$timeline_button_offset_y_style2.'px;
            }
            '. $addon_id . ' .rongyu_title {
                zoom: 1;
                overflow: hidden;
            }
            '. $addon_id . ' .rongyu_title h2 {
                font-size: '.$list_title_style2.'px;
                font-weight: 700;
                padding: '.$list_title_padding_style2.';
                color: '.$list_title_color_style2.';
            }
            '. $addon_id . ' .rongyu_c {
                zoom: 1;
                overflow: hidden;
            }
            '.$addon_id.' .rongyu_content{
                line-height: '.$list_line_height_style2.'px;
                color: '.$list_content_color_style2.';
                font-size: '.$list_content_size_style2.'px;
            }
            '. $addon_id . ' .rongyu_c li {
                width: 100%;
                padding: '.$list_content_padding_style2.';
                line-height: '.$list_line_height_style2.'px;
                color: '.$list_content_color_style2.';
                font-size: '.$list_content_size_style2.'px;';
                if($list_hidden_style2){
                    $css .= 'overflow:hidden;
                    text-overflow:ellipsis;
                    white-space:nowrap';
                }
            $css .= '}';
            $css .= $addon_id . ' .rongyu_c .rongyu_list > div{
                float: left;
                width: calc(100% / '.$list_content_column.');
                padding: '.$list_padding.';
            }
            '. $addon_id . ' .rongyu_wrap{
                display: none;
            }
            '. $addon_id . ' .rongyu_wrap.active{
                display: block;
            }';
        }

		return $css;
	}

	public static function getTemplate()
	{
		$output = '
            <# let style = data.style || "style1"; #>
            <# if(style == "style1"){ #>
                <#
                    let bar_color = data.bar_color || "#0095eb";
                    if(bar_color){
                #>
                <style type="text/css">
                    #jwpf-addon-{{data.id}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before,
                    #jwpf-addon-{{data.id}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:after,
                    #jwpf-addon-{{data.id}} .jwpf-addon-timeline .timeline-movement.even:before {
                        background-color: {{bar_color}};
                    }
        
                    #jwpf-addon-{{data.id}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:before,
                    #jwpf-addon-{{data.id}} .jwpf-addon-timeline .timeline-movement.even:after {
                        border-color: {{bar_color}};
                    }
                </style>
        
                <# } #>
                
                <!--附加样式-->
                <#  
                    var addonId="#jwpf-addon-"+data.id;
                    let need_additional = data.need_additional || data.need_additional == 0 ? data.need_additional : 0;
                    let dot_sincere_style1 = data.dot_sincere_style1 || data.dot_sincere_style1 == 0 ? data.dot_sincere_style1 : 0;
                    let dot_size_style1 = data.dot_size_style1 || data.dot_size_style1 == 0 ? data.dot_size_style1 : 11;
                    let line_width_style1 = data.line_width_style1 || data.line_width_style1 == 0 ? data.line_width_style1 : 1;
                    let time_font_size_style1, time_font_size_style1_sm, time_font_size_style1_xs;
                    if(Object.prototype.toString.call(data.time_font_size_style1) === "[object Object]"){
                        time_font_size_style1 = data.time_font_size_style1.md || 32;
                        time_font_size_style1_sm = data.time_font_size_style1.sm || 32;
                        time_font_size_style1_xs = data.time_font_size_style1.xs || 28;
                    }else{
                        time_font_size_style1 = data.time_font_size_style1 || 32;
                        time_font_size_style1_sm = data.time_font_size_style1 || 32;
                        time_font_size_style1_xs = data.time_font_size_style1 || 28;
                    }
                    let time_color_style1 = data.time_color_style1 || "#0095eb";
                    let time_margin_left_style1 = data.time_margin_left_style1 || 7;
                    let content_width_style1, content_width_style1_sm, content_width_style1_xs;
                    if(Object.prototype.toString.call(data.content_width_style1) === "[object Object]"){
                        content_width_style1 = data.content_width_style1.md || 80;
                        content_width_style1_sm = data.content_width_style1.sm || 80;
                        content_width_style1_xs = data.content_width_style1.xs || 100;
                    }else{
                        content_width_style1 = data.content_width_style1 || 80;
                        content_width_style1_sm = data.content_width_style1 || 80;
                        content_width_style1_xs = data.content_width_style1 || 100;
                    }
                    let img_max_width_style1, img_max_width_style1_sm, img_max_width_style1_xs;
                    if(Object.prototype.toString.call(data.img_max_width_style1) === "[object Object]"){
                        img_max_width_style1 = data.img_max_width_style1.md || 131;
                        img_max_width_style1_sm = data.img_max_width_style1.sm || 131;
                        img_max_width_style1_xs = data.img_max_width_style1.xs || 138;
                    }else{
                        img_max_width_style1 = data.img_max_width_style1 || 131;
                        img_max_width_style1_sm = data.img_max_width_style1 || 131;
                        img_max_width_style1_xs = data.img_max_width_style1 || 138;
                    }
                    let content_text_margin_left_style1 = data.content_text_margin_left_style1 || 28;
                    let content_text_font_size_style1, content_text_font_size_style1_sm, content_text_font_size_style1_xs;
                    if(Object.prototype.toString.call(data.content_text_font_size_style1) === "[object Object]"){
                        content_text_font_size_style1 = data.content_text_font_size_style1.md || 16;
                        content_text_font_size_style1_sm = data.content_text_font_size_style1.sm || 16;
                        content_text_font_size_style1_xs = data.content_text_font_size_style1.xs || 14;
                    }else{
                        content_text_font_size_style1 = data.content_text_font_size_style1 || 16;
                        content_text_font_size_style1_sm = data.content_text_font_size_style1 || 16;
                        content_text_font_size_style1_xs = data.content_text_font_size_style1 || 14;
                    }
                    let content_text_line_height_style1, content_text_line_height_style1_sm, content_text_line_height_style1_xs;
                    if(Object.prototype.toString.call(data.content_text_line_height_style1) === "[object Object]"){
                        content_text_line_height_style1 = data.content_text_line_height_style1.md || 37;
                        content_text_line_height_style1_sm = data.content_text_line_height_style1.sm || 37;
                        content_text_line_height_style1_xs = data.content_text_line_height_style1.xs || 24;
                    }else{
                        content_text_line_height_style1 = data.content_text_line_height_style1 || 37;
                        content_text_line_height_style1_sm = data.content_text_line_height_style1 || 37;
                        content_text_line_height_style1_xs = data.content_text_line_height_style1 || 24;
                    }
                    let content_text_color_style1 = data.content_text_color_style1 || "#444";
                    let img_max_height_style1 = data.img_max_height_style1 || 100;
                    let year_text_margin_left_style1 = data.year_text_margin_left_style1 || 10;
                    let content_text_margin_top_style1, content_text_margin_top_style1_sm, content_text_margin_top_style1_xs;
                    if(Object.prototype.toString.call(data.content_text_margin_top_style1) === "[object Object]"){
                        content_text_margin_top_style1 = data.content_text_margin_top_style1.md || 10;
                        content_text_margin_top_style1_sm = data.content_text_margin_top_style1.sm || 10;
                        content_text_margin_top_style1_xs = data.content_text_margin_top_style1.xs || 10;
                    }else{
                        content_text_margin_top_style1 = data.content_text_margin_top_style1 || 10;
                        content_text_margin_top_style1_sm = data.content_text_margin_top_style1 || 10;
                        content_text_margin_top_style1_xs = data.content_text_margin_top_style1 || 10;
                    }
                    let additional = data.additional || "";
                    let img_float_style1 = data.img_float_style1 || data.img_float_style1 == 0 ? data.img_float_style1 : 0;
                    let content_title_font_size_style1, content_title_font_size_style1_sm, content_title_font_size_style1_xs;
                    if(Object.prototype.toString.call(data.content_title_font_size_style1) === "[object Object]"){
                        content_title_font_size_style1 = data.content_title_font_size_style1.md || 16;
                        content_title_font_size_style1_sm = data.content_title_font_size_style1.sm || 16;
                        content_title_font_size_style1_xs = data.content_title_font_size_style1.xs || 16;
                    }else{
                        content_title_font_size_style1 = data.content_title_font_size_style1 || 16;
                        content_title_font_size_style1_sm = data.content_title_font_size_style1 || 16;
                        content_title_font_size_style1_xs = data.content_title_font_size_style1 || 16;
                    }
                    let content_title_color_style1 = data.content_title_color_style1 || "#444";
                #>
                <# 
                    if (need_additional == 1) {
                        if(additional === "style1"){
                            if(dot_sincere_style1 == 1){ #>
                                <style>
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:before{
                                        background-color: {{bar_color}};
                                        width: {{dot_size_style1}}px;
                                        height: {{dot_size_style1}}px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-badge:after{
                                        display: none;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before{
                                        width: {{line_width_style1}}px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-date{
                                        font-size: {{time_font_size_style1}}px;
                                        color: {{time_color_style1}};
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement.even .timeline-item .timeline-date{
                                        margin-left: {{time_margin_left_style1}}px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement.odd .timeline-item .timeline-date{
                                        margin-right: {{time_margin_left_style1}}px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel{
                                        border: none;
                                        width: {{content_width_style1}}%;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel::after{
                                        content: "";
                                        display: block;
                                        clear: both;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel.left-part:before,
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel:before{
                                        display: none;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel.left-part .photo{
                                        float: left;
                                        margin-right: {{content_text_margin_left_style1}}px;
                                        margin-left: 0;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                                        float: right;
                                        max-width: {{img_max_width_style1}}px;
                                        margin-left: {{content_text_margin_left_style1}}px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .details.hasphoto{
                                        font-size: {{content_text_font_size_style1}}px;
                                        line-height: {{content_text_line_height_style1}}px;
                                        color: {{content_text_color_style1}};
                                        float: left;
                                        max-width: calc(100% - {{img_max_width_style1 + content_text_margin_left_style1}}px);
                                        text-align: left;
                                    }
                                    {{addonId}} .pc{
                                        display: block;
                                    }
                                    {{addonId}} .mobile{
                                        display: none;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper:before{
                                        top: 33px;
                                    }
                                    {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .title{
                                        float: left;
                                        font-size: {{content_title_font_size_style1}}px;
                                        color: {{content_title_color_style1}};
                                    }
                                    @media (max-width:992px) and (min-width:768px){
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel{
                                            border: none;
                                            width: {{content_width_style1_sm}}%;
                                        }
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                                            max-width: {{img_max_width_style1_sm}}px;
                                        }
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-date{
                                            font-size: {{time_font_size_style1_sm}}px;
                                        }                        
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .details.hasphoto{
                                            font-size: {{content_text_font_size_style1_sm}}px;
                                            line-height: {{content_text_line_height_style1_sm}}px;
                                        }
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .title{
                                            font-size: {{content_title_font_size_style1_sm}}px;
                                        }
                                    }
                                    @media (max-width:767px){
                                        {{addonId}} .jwpf-addon-timeline .jwpf-addon-timeline-wrapper .timeline-movement .timeline-item .timeline-panel .photo{
                                            max-width: {{img_max_width_style1_xs}}px;
                                        }
                                        {{addonId}} .pc{
                                            display: none;
                                        }
                                        {{addonId}} .mobile{
                                            display: block;
                                        }
                                        {{addonId}} .content {
                                            width: 100%;
                                            padding-top: 20px;
                                            padding-bottom: 20px;
                                        }
                                        {{addonId}} .y1 {
                                            width: 80%;
                                            margin-top: -2px;
                                            margin-left: 50px;
                                            border: 2px solid {{bar_color}};
                                            border-radius: 0 25px 25px 0;
                                            border-left: none;
                                            padding-top: 20px;
                                            padding-bottom: 20px;
                                        }
                                        {{addonId}} .dian1 {
                                            width: 10px;
                                            height: 10px;
                                            background-color: {{bar_color}};
                                            border-radius: 50%;
                                            margin-top: -26px;
                                        }
                                        {{addonId}} .content .year::after{
                                            content: "";
                                            display: block;
                                            clear: both;
                                        }
                                        {{addonId}} .content .year .y {
                                            font-size: {{time_font_size_style1_xs}}px;
                                            font-family: PingFangSC-Semibold, PingFang SC;
                                            font-weight: 600;
                                            color: {{time_color_style1}};
                                            letter-spacing: 1px;
                                            <# if(img_float_style1 == 1){ #>
                                                float: left;
                                                height: {{img_max_height_style1}}px;
                                                line-height: {{img_max_height_style1}}px;
                                            <# }else{ #>
                                                float: none;
                                                height: auto;
                                                line-height: 2;
                                            <# } #>
                                        }
                                        {{addonId}} .content .year .p {
                                            width: {{img_max_width_style1_xs}}px;
                                            height: {{img_max_height_style1}}px;
                                            position: relative;
                                            <# if(img_float_style1 == 1){ #>
                                                float: left;
                                                line-height: {{img_max_height_style1}}px;
                                                margin-left: {{year_text_margin_left_style1}}px;
                                            <# }else{ #>
                                                float: none;
                                            <# } #>
                                        }
                                        {{addonId}} .content img {
                                            max-width: 100%;
                                            max-height: 100%;
                                            position: absolute;
                                            top: 50%;
                                            transform: translateY(-50%);
                                        }
                                        {{addonId}} .content .text {
                                            width: 87%;
                                            font-size: {{content_text_font_size_style1_xs}}px;
                                            font-family: PingFangSC-Regular, PingFang SC;
                                            font-weight: 400;
                                            color: {{content_text_color_style1}};
                                            line-height: {{content_text_line_height_style1_xs}}px;
                                            margin-top: {{content_text_margin_top_style1_xs}}px;
                                        }
                                        {{addonId}} .y2 {
                                            width: 80%;
                                            margin-top: -2px;
                                            margin-left: 26px;
                                            border: 2px solid {{bar_color}};
                                            border-radius: 25px 0 0 25px;
                                            border-right: none;
                                            padding-top: 20px;
                                            padding-bottom: 20px;
                                        }
                                        {{addonId}} .dian2 {
                                            width: 10px;
                                            height: 10px;
                                            background-color: {{bar_color}};
                                            border-radius: 50%;
                                            margin-top: -26px;
                                            margin-left: 25px;
                                        }
                                        {{addonId}} .content .y2 > .year, .y2 > .text,{{addonId}} .content .y2 .title {
                                            margin-left: 25px;
                                        }
                                        {{addonId}} .content .y2 .title{
                                            width: 87%;
                                        }
                                        {{addonId}} .content .title{
                                            font-size: {{content_title_font_size_style1_xs}}px;
                                        }
                                    }
                                </style>
                            <# }
                        }
                    }
                #>
                
                <div class="jwpf-addon jwpf-addon-timeline pc {{ data.class }}">
                    <div class="jwpf-addon-timeline-text-wrap">
                    <# if( !_.isEmpty( data.title ) ){ #><{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{ data.title }}</{{ data.heading_selector }}><# } #>
                    </div>
        
                    <div class="jwpf-addon-timeline-wrapper">
                    <#
                        _.each(data.jw_timeline_items, function(timeline_item, key){
                        let oddeven = ((key%2) == 0 ) ? "even":"odd";
                    #>
                        <div class="jwpf-row timeline-movement {{oddeven}}">
                            <div class="timeline-badge"></div>
                            <#
                                if(oddeven == "odd") {
                            #>
                            <div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">
                                <p class="timeline-date text-right">{{ timeline_item.date }}</p>
                            </div>
                            <div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">
                                <div class="timeline-panel">
                                    <p class="title jw-editable-content" id="addon-title-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-title">{{ timeline_item.title }}</p>
                                    <div class="details jw-editable-content" id="addon-content-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-content">{{{ timeline_item.content }}}</div>
                                    <div class="details jw-editable-content" id="addon-photo-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-photo">{{{ timeline_item.photo }}}</div>
                                </div>
                            </div>
        
                            <# } else { #>
        
                            <div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item mobile-block">
                                <p class="timeline-date text-left">{{ timeline_item.date }}</p>
                            </div>
                            <div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item">
                                <div class="timeline-panel left-part">
                                    <p class="title jw-editable-content" id="addon-title-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-title">{{ timeline_item.title }}</p>
                                    <div class="details jw-editable-content" id="addon-content-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-content">{{{ timeline_item.content }}}</div>
                                    <div class="details jw-editable-content" id="addon-photo-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_timeline_items-{{key}}-photo">{{{ timeline_item.photo }}}</div>
                                </div>
                            </div>
                            <div class="jwpf-col-xs-12 jwpf-col-sm-6 timeline-item mobile-hidden">
                                <p class="timeline-date text-left">{{ timeline_item.date }}</p>
                            </div>
                            <# } #>
                        </div>
                    <# }) #>
                    </div>
                </div>
                
                <!--附加样式-->
                <# 
                    if (need_additional == 1) {
                        if (additional === "style1") {
                            if (dot_sincere_style1 == 1) { #>
                                <div class="content mobile">
                                    <#
                                        _.each(data.jw_timeline_items, function(timeline_item, key){
                                            let oddeven = ((key%2) == 0 ) ? "y1":"y2";
                                            let oddevenDot = ((key%2) == 0 ) ? "dian1":"dian2";
                                    #>
                                        <div class="{{oddeven}}">
                                            <div class="{{oddevenDot}}"></div>
                                                <div class="year">
                                                    <div class="y">{{timeline_item.date}}</div>
                                                        <# if (timeline_item.photo) { #>
                                                            <div class="p"><img src=\'{{timeline_item.photo}}\' alt=""></div>
                                                        <# } #>
                                                    <div class="clear"></div>
                                                </div>
                                            <div class="title">{{timeline_item.title}}</div>
                                            <div class="text">{{timeline_item.content}}</div>
                                        </div>
                                    <# }) #>
                                </div>
                            <# }
                        }
                    }
                #>
            <# }else if(style == "style2"){
                var timeline_items_style2 = data.timeline_items_style2 || [];
                var timeline_padding_style2 = data.timeline_padding_style2 || "38px 0 0 0";
                var timeline_bg_img_style2 = data.timeline_bg_img_style2 || "https://oss.lcweb01.cn/joomla/20230111/587dc2ade0a77d464f2ef2366501ceeb.jpg";
                var timeline_bg_img_offset_x_style2 = data.timeline_bg_img_offset_x_style2 || "0";
                var timeline_bg_img_offset_y_style2 = "timeline_bg_img_offset_y_style2" in data ? data.timeline_bg_img_offset_y_style2 : "8";
                var timeline_height_style2 = data.timeline_height_style2 || "70";
                var timeline_date_item_margin_style2 = data.timeline_date_item_margin_style2 || "0 27px 0 86px";
                var timeline_date_item_padding_style2 = data.timeline_date_item_padding_style2 || "25px 0 0 0";
                var timeline_date_item_width_style2 = data.timeline_date_item_width_style2 || "50";
                var timeline_date_on_img_style2 = data.timeline_date_on_img_style2 || "https://oss.lcweb01.cn/joomla/20230111/5981e24c87ebd4f060188fd6807c7dd8.jpg";
                var timeline_date_on_offset_x_style2 = data.timeline_date_on_offset_x_style2 || "0";
                var timeline_date_on_offset_y_style2 = "timeline_date_on_offset_y_style2" in data ? data.timeline_date_on_offset_y_style2 : "9";
                var timeline_button_left_style2 = data.timeline_button_left_style2 || "https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg";
                var timeline_button_offset_left_x_style2 = data.timeline_button_offset_left_x_style2 || "0";
                var timeline_button_offset_left_y_style2 = data.timeline_button_offset_left_y_style2 || "0";
                var timeline_button_right_style2 = data.timeline_button_right_style2 || "https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg";
                var timeline_button_offset_right_x_style2 = "timeline_button_offset_right_x_style2" in data ? data.timeline_button_offset_right_x_style2 : "-56";
                var timeline_button_offset_right_y_style2 = data.timeline_button_offset_right_y_style2 || "0";
                var timeline_button_width_style2 = data.timeline_button_width_style2 || "24";
                var timeline_button_height_style2 = data.timeline_button_height_style2 || "24";
                var timeline_button_offset_x_style2 = data.timeline_button_offset_x_style2 || "-30";
                var timeline_button_offset_y_style2 = data.timeline_button_offset_y_style2 || "38";
                var list_title_style2 = data.list_title_style2 || "24";
                var list_title_padding_style2 = data.list_title_padding_style2 || "10px 0 25px 30px";
                var list_title_color_style2 = data.list_title_color_style2 || "#666";
                var list_content_size_style2 = data.list_content_size_style2 || "13";
                var list_content_padding_style2 = data.list_content_padding_style2 || "0 0 0 30px";
                var list_content_column = data.list_content_column || "2";
                var list_padding = data.list_padding || "0 0 0 0";
                var list_hidden_style2 = "list_hidden_style2" in data ? data.list_hidden_style2 : "1";
                var list_content_color_style2 = data.list_content_color_style2 || "#666";
                var list_line_height_style2 = data.list_line_height_style2 || "35";
                var addon_id = "#jwpf-addon-" + data.id;
                #>
                <style>
                    {{addon_id}} *{
                        margin: 0;
                        padding: 0;
                        list-style: none;
                    }
                    {{addon_id}} .rongyu_b, {{addon_id}} .licheng_a {
                        position: relative;
                        padding: {{timeline_padding_style2}};
                    }
                    {{addon_id}} .rongyu_b .bd, {{addon_id}} .licheng_a .bd {
                        background: url("{{timeline_bg_img_style2}}") repeat-x {{timeline_bg_img_offset_x_style2}}px {{timeline_bg_img_offset_y_style2}}px;
                        height: {{timeline_height_style2}}px;
                        box-sizing: content-box;
                    }
                    {{addon_id}} .rongyu_b .bd li, {{addon_id}} .licheng_a .bd li {
                        float: left;
                        margin: {{timeline_date_item_margin_style2}};
                        padding: {{timeline_date_item_padding_style2}};
                        font-weight: 700;
                        cursor: pointer;
                        width: {{timeline_date_item_width_style2}}px;
                    }
                    {{addon_id}} .rongyu_b .bd .on, {{addon_id}} .licheng_a .bd .on {
                        background: url("{{timeline_date_on_img_style2}}") no-repeat {{timeline_date_on_offset_x_style2}}px {{timeline_date_on_offset_y_style2}}px;
                    }
                    {{addon_id}} .rongyu_b .prev, {{addon_id}} .licheng_a .prev {
                        background: url("{{timeline_button_left_style2}}") no-repeat {{timeline_button_offset_left_x_style2}}px {{timeline_button_offset_left_y_style2}}px;
                        display: inline-block;
                        width: {{timeline_button_width_style2}}px;
                        height: {{timeline_button_height_style2}}px;
                        position: absolute;
                        left: {{timeline_button_offset_x_style2}}px;
                        cursor: pointer;
                        top: {{timeline_button_offset_y_style2}}px;
                    }
                    {{addon_id}} .rongyu_b .next, {{addon_id}} .licheng_a .next {
                        background: url("{{timeline_button_right_style2}}") no-repeat {{timeline_button_offset_right_x_style2}}px {{timeline_button_offset_right_y_style2}}px;
                        display: inline-block;
                        width: {{timeline_button_width_style2}}px;
                        height: {{timeline_button_height_style2}}px;
                        position: absolute;
                        right: {{timeline_button_offset_x_style2}}px;
                        cursor: pointer;
                        top: {{timeline_button_offset_y_style2}}px;
                    }
                    {{addon_id}} .rongyu_title {
                        zoom: 1;
                        overflow: hidden;
                    }
                    {{addon_id}} .rongyu_title h2 {
                        font-size: {{list_title_style2}}px;
                        font-weight: 700;
                        padding: {{list_title_padding_style2}};
                        color: {{list_title_color_style2}};
                    }
                    {{addon_id}} .rongyu_c {
                        zoom: 1;
                        overflow: hidden;
                    }
                    {{addon_id}} .rongyu_content{
                        line-height: {{list_line_height_style2}}px;
                        color: {{list_content_color_style2}};
                        font-size: {{list_content_size_style2}}px;
                    }
                    {{addon_id}} .rongyu_c li {
                        width: 100%;
                        padding: {{list_content_padding_style2}};
                        line-height: {{list_line_height_style2}}px;
                        color: {{list_content_color_style2}};
                        font-size: {{list_content_size_style2}}px;
                        <# if(list_hidden_style2){ #>
                            overflow:hidden;
                            text-overflow:ellipsis;
                            white-space:nowrap;
                        <# } #>
                    }
                    {{addon_id}} .rongyu_c .rongyu_list > div{
                        float: left;
                        width: calc(100% / {{list_content_column}});
                        padding: {{list_padding}};
                    }
                    {{addon_id}} .rongyu_wrap{
                        display: none;
                    }
                    {{addon_id}} .rongyu_wrap.active{
                        display: block;
                    }
                </style>
                <div class="jwpf-addon jwpf-addon-timeline pc {{ data.class }}">
                    <div class="jwpf-addon-timeline-text-wrap">
                        <# if( !_.isEmpty( data.title ) ){ #>
                            <{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">
                                {{ data.title }}
                            </{{ data.heading_selector }}>
                        <# } #>
                    </div>
                    <!--公司简介时间轴-->
                    <div class="rongyu_b">
                        <ul class="bd">
                            <# timeline_items_style2.forEach(function(value, key){
                                    let dateClass = key === 0 ? "on" : "";
                                #>
                                <li class="{{dateClass}}">{{value.date}}</li>
                            <# }) #>
                        </ul>
                        <span class="prev prevStop"></span>
                        <span class="next"></span>
                    </div>
                    <# timeline_items_style2.forEach(function(value, key){ 
                        let listClass = key === 0 ? "active" : "";
                        #>
                        <div class="rongyu_wrap {{listClass}}"> 
                            <div class="rongyu_c">
                                <div class="rongyu_list" style="display: block;">
                                    <# value.list_title.forEach(function(list_value, list_key){ #>
                                        <div>
                                            <div class="rongyu_title">
                                                <h2>{{list_value.title}}</h2>
                                            </div>
                                            <div class="rongyu_content">
                                                {{{list_value.content}}}
                                            </div>
                                        </div>
                                    <# }) #>
                                </div>
                            </div>
                        </div>
                    <# }) #>
                </div>
            <# } #>
		';

		return $output;
	}

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/addons/timeline/assets/js/jquery.SuperSlide.min.js');
        return $js;
    }
}
