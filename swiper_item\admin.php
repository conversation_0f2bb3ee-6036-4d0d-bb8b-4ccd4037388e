<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'swiper_item',
		'title' => JText::_('轮播组件'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
		'category' => '轮播',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				// 轮播项目
				'jw_swiper_item_item' => array(
					'title' => JText::_('轮播项'),
					'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE_DESC'),
                            'std' => ''
                        ),
						'content' => array(
							'type' => 'builder',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT_DESC'),
							'std' => 'Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et.'
						),
					),
					'std' => array(
                        array(
                            'content' => 'Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et.'
                        ),
                        array(
                            'content' => 'Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et.'
                        ),
                        array(
                            'content' => 'Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et.'
                        )
                    )
				),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '轮播配置'
                ),
                'swiper_style' => array(
                    'type' => 'buttons',
                    'title' => '切换选项状态',
                    'std' => 'item_s',
                    'values' => array(
                        array(
                            'label' => '轮播项配置',
                            'value' => 'item_s'
                        ),
                        array(
                            'label' => '翻页配置',
                            'value' => 'button_s'
                        ),
                        array(
                            'label' => '轮播点配置',
                            'value' => 'pagination_s'
                        )
                    ),
                    'tabs' => true,
                ),
                // 轮播项配置
                'swiper_speed' => array(
                    'type' => 'number',
                    'title' => '轮播项切换速度（单位ms）',
                    'std' => 300,
                    'depends'=>array(
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                'is_swiper_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启自动切换',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                'swiper_autoplay_delay' => array(
                    'type' => 'number',
                    'title' => '轮播项自动切换的时间间隔（单位ms）',
                    'std' => 3000,
                    'depends'=>array(
                        array('swiper_style', '=', 'item_s'),
                        array('is_swiper_autoplay', '=', '1'),
                    ),
                ),
                'is_xunhuan' => array(
                    'type' => 'checkbox',
                    'title' => '开启循环播放',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                'animate_dh' => array(
                    'type' => 'select',
                    'title' => '切换动画',
                    'values' => array(
                        'slide' => '默认',
                        'fade' => '淡入',
                        'cube' => '方块',
                        'coverflow' => '3d流',
                        'flip' => '3d翻转',
                        'narrowIn' => '缩小进入',
                        //'cards' => '卡片式',  // Swiper 6.6.2 不支持
                        //'creative' => '创意性',  // Swiper 6.6.2 不支持
                    ),
                    'std' => 'slide',
                    'depends' => array(
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                // 翻页按钮配置
                'is_swiper_button' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启切换按钮',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                    ),
                ),
                'swiper_button_prev' => array(
                    'type' => 'media',
                    'title' => '上一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png',
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_next' => array(
                    'type' => 'media',
                    'title' => '下一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png',
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                //是否开启划过切换图片
                'is_hoverimg' => array(
                    'type' => 'checkbox',
                    'title' => '开启划过按钮显示图片',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),

                    ),
                ),
                'hv_button_prev' => array(
                    'type' => 'media',
                    'title' => '划过上一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/c50b0b3cfdfe92ba84bebbaf1df690c2.png',
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                        array('is_hoverimg', '=', 1),

                    ),
                ),
                'hv_button_next' => array(
                    'type' => 'media',
                    'title' => '划过下一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/f2634a62cdd9d537f21e041c96a81a73.png',
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                        array('is_hoverimg', '=', 1),

                    ),
                ),

                //
                'swiper_button_width' => array(
                    'type' => 'slider',
                    'title' => '切换按钮宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 24
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_height' => array(
                    'type' => 'slider',
                    'title' => '切换按钮高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 24
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_top' => array(
                    'type' => 'slider',
                    'title' => '切换按钮上边距（百分比）',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 48,
                        'sm' => 48,
                        'xs' => 48
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_left' => array(
                    'type' => 'slider',
                    'title' => '切换按钮两侧边距（px）',
                    'max' => 800,
                    'min' => -150,
                    'responsive' => true,
                    'std' => array(
                        'md' => 10,
                        'sm' => 10,
                        'xs' => 10
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                // 轮播点配置
                'is_swiper_pagination' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启轮播点',
                    'std' => 0,
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                    ),
                ),
                'swiper_pagination_bottom' => array(
                    'type' => 'slider',
                    'title' => '轮播点底部距离（px）',
                    'responsive' => true,
                    'std' => array(
                        'md' => 10,
                        'sm' => 10,
                        'xs' => 10
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'pagination_style' => array(
                    'type' => 'buttons',
                    'title' => '轮播点状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                    'tabs' => true,
                ),
                'swiper_p_width' => array(
                    'type' => 'slider',
                    'title' => '轮播点宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 8,
                        'sm' => 8,
                        'xs' => 8
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'normal'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'swiper_p_height' => array(
                    'type' => 'slider',
                    'title' => '轮播点高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 8,
                        'sm' => 8,
                        'xs' => 8
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'normal'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'swiper_p_margin' => array(
                    'type' => 'slider',
                    'title' => '轮播点间距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 5,
                        'sm' => 5,
                        'xs' => 5
                    ),
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'normal'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'swiper_p_color' => array(
                    'type' => 'color',
                    'title' => '轮播点颜色',
                    'std' => '#f0f0f0',
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'normal'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                //轮播点选中
                'swiper_p_width_a' => array(
                    'type' => 'slider',
                    'title' => '轮播点宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'active'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'swiper_p_height_a' => array(
                    'type' => 'slider',
                    'title' => '轮播点高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'active'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),
                'swiper_p_color_a' => array(
                    'type' => 'color',
                    'title' => '轮播点颜色',
                    'std' => '#007aff',
                    'depends' => array(
                        array('swiper_style', '=', 'pagination_s'),
                        array('pagination_style', '=', 'active'),
                        array('is_swiper_pagination', '=', 1),
                    ),
                ),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
