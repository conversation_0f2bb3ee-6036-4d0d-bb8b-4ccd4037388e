<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2021-06-26 11:52:59
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-06-20 16:01:14
 * @FilePath: \addons\image_swiper\admin.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'image_swiper',
        'title' => JText::_('混动轮播'),
        'desc' => JText::_('混动轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'image_carousel_layout' => array(
                    'type' => 'thumbnail',
                    'title' => JText::_('布局方式'),
                    'desc' => JText::_('布局方式'),
                    'values' => array(
                        'layout1' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout1.png', //布局一
                        'layout2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout2.png', // 布局二
                        'layout3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout3.png', // 布局三,
                        'layout4' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout4.png', // 官网轮播1,
                        'layout5' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout5.png', // 官网轮播2,
                        'layout6' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout6.png', // 官网轮播3,
                        'layout7' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout7.png', // 官网轮播4,
                        'layout8' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout8.png', // 官网轮播5,
                        'layout9' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout9.png', // 官网轮播6,
                        'layout10' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout10.png', // 官网轮播7,
                        'layout11' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout11.png', // 官网轮播8,
                        'layout12' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout12.jpg', // 官网轮播8,
                        'layout13' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout13.jpg', // 轮播13,
                        'layout14' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout14.png', // 轮播14,
                        'layout15' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout15.png', // 轮播15,
                        'layout16' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout16.png', // 轮播16,
                        'layout17' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout17.png', // 轮播17,
                        'layout18' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout18.png', // 轮播18,
                        'layout19' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout19.png', // 轮播19,
                        'layout20' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout20.png', // 轮播20,
                        'layout21' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout21.png', // 轮播21,
                        'layout22' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout22.jpg', // 轮播22,
                        'layout23' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout23.jpg', // 轮播23,
                        'layout24' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout24.png', // 轮播24,
                        'layout25' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout25.png', // 轮播25,
                    ),
                    'std' => 'layout1',
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                            'depends' => array(
                                array('image_carousel_layout', '!=', 'layout12')
                            ),
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                            'depends' => array(
                                array('image_carousel_layout', '!=', 'layout12')
                            ),
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('image_carousel_layout', '!=', 'layout12')
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(
                                array('image_carousel_img_link', '!=', '')
                            ),
                        ),
                        'mask_content'=>array(
                            'type'=>'text',
                            'title' => JText::_('遮罩内容'),
                            'std' => '营销内容',
                            'depends' => array(
                                array('image_carousel_layout', '=', 'layout3')
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout9'),
                        array('image_carousel_layout','!=','layout10'),
                        array('image_carousel_layout','!=','layout11'),
                        array('image_carousel_layout','!=','layout12'),
                        array('image_carousel_layout','!=','layout13'),
                        array('image_carousel_layout','!=','layout14'),
                        array('image_carousel_layout','!=','layout15'),
                        array('image_carousel_layout','!=','layout16'),
                        array('image_carousel_layout','!=','layout17'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout20'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout22'),
                        array('image_carousel_layout','!=','layout23'),
                        array('image_carousel_layout','!=','layout24'),
                        array('image_carousel_layout','!=','layout25'),
                    ),
                ),

                'jw_image_carousel_item_layout9' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => '轮播项标签名',
                        ),
                        //轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端轮播管理获取'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0
						),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                            'depends' => array(
								array('text_from', '=', '0'),
							),
                        ),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
							),
						),
						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'dan'),
							),
						),
						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'zu'),
							),
						),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('text_from', '=', 0),
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(
                                array('is_link', '=', 1),
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),

                    ),
                ),

                'jw_image_carousel_item_12' => array(
                    'title' => '轮播项',
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220402/9453ec2586fc8e33f5e9b038e8aa21fa.png',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220402/03fd80629ae4bfc183bcc587a229bf4d.png',
                        ),
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220402/9453ec2586fc8e33f5e9b038e8aa21fa.png',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout12'),
                    ),
                ),

                // 布局25配置选项卡
                'carousel_settings_25' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'values'=>array(
                        array(
                            'label'=>'轮播项',
                            'value'=>'elements'
                        ),
                        array(
                            'label'=>'样式',
                            'value'=>'styles'
                        ),
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                    ),
                ),
                'jw_image_carousel_item_25' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img_labtop' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img_phone' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title' => '产品解决方案',
                            'title_en' => 'Product Solutions',
                            'content' => '我们的产品解决方案是为客户提供一站式的解决方案，包括产品设计、产品开发、产品营销、产品售后等。',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img_labtop' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img_phone' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title' => '产品解决方案',
                            'title_en' => 'Product Solutions',
                            'content' => '我们的产品解决方案是为客户提供一站式的解决方案，包括产品设计、产品开发、产品营销、产品售后等。',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'std' => '产品解决方案',
                        ),
                        'title_en' => array(
                            'type' => 'text',
                            'title' => '轮播项标题英文',
                            'std' => 'Product Solutions',
                        ),
                        'content' => array(
                            'type' => 'text',
                            'title' => '轮播项内容',
                            'std' => '我们的产品解决方案是为客户提供一站式的解决方案，包括产品设计、产品开发、产品营销、产品售后等。',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'image_carousel_img_labtop' => array(
                            'type' => 'media',
                            'title' => '平板图片',
                            'desc' => '为空获取pc端图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'image_carousel_img_phone' => array(
                            'type' => 'media',
                            'title' => '移动端图片',
                            'desc' => '为空获取pc端图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(
                                array('image_carousel_img_link', '!=', '')
                            ),
                        ),
                    ),
                    'depends' => array(
                        array('carousel_settings_25', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout25'),
                    )
                ),

                'title_type4' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'desc' => '标题',
                    'std' => 'Carousel Item Tittle',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout4')
                    ),
                ),

                'title_type4_color' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'desc' => '标题颜色',
                    'std' => '#252525',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout4')
                    ),
                ),

                'title_type4_color_sm' => array(
                    'type' => 'color',
                    'title' => '小标题颜色',
                    'desc' => '小标题颜色',
                    'std' => '#252525',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout4')
                    ),
                ),

                'jw_image_carousel_item_type4' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题1',
                            'desc' => '标题1',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片1',
                            'desc' => '轮播项图片1',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'title1' => array(
                            'type' => 'text',
                            'title' => '标题2',
                            'desc' => '标题2',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img1' => array(
                            'type' => 'media',
                            'title' => '轮播项图片2',
                            'desc' => '轮播项图片2',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout4')
                    ),
                ),

                'jw_image_carousel_item_type5' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        )
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片1',
                            'desc' => '轮播项图片1',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout5')
                    ),
                ),

                'need_data'=>array(
                    'type'=>'checkbox',
                    'title'=>'是否启用后台数据',
                    'std'=>0,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout1'),
                        array('image_carousel_layout','!=','layout2'),
                        array('image_carousel_layout','!=','layout3'),
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout9'),
                        array('image_carousel_layout','!=','layout10'),
                        array('image_carousel_layout','!=','layout12'),
                        array('image_carousel_layout','!=','layout13'),
                        array('image_carousel_layout','!=','layout14'),
                        array('image_carousel_layout','!=','layout15'),
                        array('image_carousel_layout','!=','layout16'),
                        array('image_carousel_layout','!=','layout17'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout20'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout22'),
                        array('image_carousel_layout','!=','layout23'),
                        array('image_carousel_layout','!=','layout24'),
                        array('image_carousel_layout','!=','layout25'),

                    )
                ),

                'site_id' => array(
                    'std' => $site_id,
                    'depends'=>array(
                        array('need_data','=',1)
                    )
                ),

                'company_id' => array(
                    'std' => $company_id,
                    'depends'=>array(
                        array('need_data','=',1)
                    )
                ),

                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_goods')['list'],
                    'depends'=>array(
                        array('need_data','=',1),
                        array('image_carousel_layout','!=','layout9')
                    )
                ),

                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('排序'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(

                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),

                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',

                    'depends'=>array(
                        array('need_data','=',1),
                        array('image_carousel_layout','!=','layout9')
                    )
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('显示几条'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10',
                    'depends'=>array(
                        array('need_data','=',1),
                        array('image_carousel_layout','!=','layout9')
                    )
                ),

                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends'=>array(
                        array('need_data','=',1),
                        array('image_carousel_layout','!=','layout9')
                    )
                ),

                'jw_image_carousel_item_type8' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'subtitle'=>'汉阳市政宣传片<城市筑梦者>',
                            'title_settings'=>'title',
                            'need_title'=>0,
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_settings'=>'title',
                            'need_title'=>1,
                            'isImg'=>1,
                            'title'=>'厚德善建 实干兴邦',
                            'subtitle'=>'月亮湾城市阳台工程',
                            'intro'=>'该工程西起三环线孟家铺立交,东与二环线龙阳大道(琴台大道—墨水湖南路)改造工程龙阳立交主线之一的墨水湖北路东段相接,线路全长3.986KM,为双向6车道,建设内容包括桥梁、道路、排水、绿化、照明、交通工程等建设内容.',
                            'title_img'=> str_replace('administrator/', '', JURI::base()) .'/components/com_jwpagefactory/addons/image_swiper/assets/images/banTxt.png'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_settings'=>'title',
                            'need_title'=>1,
                            'isImg'=>1,
                            'title'=>'厚德善建 实干兴邦',
                            'subtitle'=>'墨水湖北路工程',
                            'intro'=>'该工程西起三环线孟家铺立交,东与二环线龙阳大道(琴台大道—墨水湖南路)改造工程龙阳立交主线之一的墨水湖北路东段相接,线路全长3.986KM,为双向6车道,建设内容包括桥梁、道路、排水、绿化、照明、交通工程等建设内容.',
                            'title_img'=> str_replace('administrator/', '', JURI::base()) .'/components/com_jwpagefactory/addons/image_swiper/assets/images/banTxt.png'
                        ),
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                        'title_settings' => array(
                            'type' => 'buttons',
                            'title' => JText::_('标题内容设置'),
                            'std' => 'title',
                            'values' => array(
                                array(
                                    'label' => '标题',
                                    'value' => 'title'
                                ),
                                array(
                                    'label' => '副标题',
                                    'value' => 'subtitle'
                                ),
                                array(
                                    'label' => '按钮',
                                    'value' => 'button'
                                ),
                                array(
                                    'label' => '简介',
                                    'value' => 'intro'
                                ),
                            ),
                        ),
                        'need_title'=>array(
                            'type'=>'checkbox',
                            'title' => JText::_('是否需要标题'),
                            'std' => 1,
                            'depends'=>array(
                                array('title_settings','=','title')
                            )
                        ),
                        'isImg'=>array(
                            'type'=>'checkbox',
                            'title' => JText::_('是否使用图片代替标题'),
                            'std' => 1,
                            'depends'=>array(
                                array('need_title','=',1),
                                array('title_settings','=','title')
                            )
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'desc' => '轮播项标题',
                            'std' => '厚德善建 实干兴邦',
                            'depends'=>array(
                                array('isImg', '=', 0),
                                array('title_settings', '=', 'title'),
                                array('need_title','=',1)
                            ),
                        ),
                        'title_img' => array(
                            'type' => 'media',
                            'title' => '轮播项标题图片',
                            'desc' => '轮播项标题图片',
                            'std' => str_replace('administrator/', '', JURI::base()) .'/components/com_jwpagefactory/addons/image_swiper/assets/images/banTxt.png',
                            'depends'=>array(
                                array('isImg', '=', 1),
                                array('need_title','=',1),
                                array('title_settings','=','title'),
                            ),
                        ),
                        'subtitle' => array(
                            'type' => 'text',
                            'title' => '轮播项副标题',
                            'desc' => '轮播项副标题',
                            'std' => '汉阳市政宣传片<城市筑梦者>',
                            'depends'=>array(
                                array('title_settings', '=', 'subtitle'),
                            ),
                        ),
                        'button' => array(
                            'type' => 'text',
                            'title' => '按钮内容',
                            'desc' => '按钮内容',
                            'std' => '了解我们',
                            'depends'=>array(
                                array('title_settings', '=', 'button'),
                            ),
                        ),
                        'intro' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'desc' => '简介',
                            'std' => '',
                            'depends'=>array(
                                array('title_settings', '=', 'intro'),
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('need_data','=',0),
                    ),
                ),
                'jw_image_carousel_item_type13' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title' => '教务管理',
                            'intro'=>'【学员管理】批量化进行学员分班、调班操作,数字化记录学员个人信息数据,精细化为学员排课、结班.减轻教务人员工作量,极大提升工作效率!',
                            'title_img'=>'https://oss.lcweb01.cn/joomla/20220407/2a2a7be239ead41c590d7a52f43c0b99.png',
                            'title_imgs'=>'https://oss.lcweb01.cn/joomla/20220408/8c4eabf384c6a6409af912a9dfc3ce14.png',

                        ),
                        array(
                            'title' => '课后服务',
                            'intro'=>'【学员管理】批量化进行学员分班、调班操作,数字化记录学员个人信息数据,精细化为学员排课、结班.减轻教务人员工作量,极大提升工作效率!',
                            'title_img'=>'https://oss.lcweb01.cn/joomla/20220407/2a2a7be239ead41c590d7a52f43c0b99.png',
                            'title_imgs'=>'https://oss.lcweb01.cn/joomla/20220408/8c4eabf384c6a6409af912a9dfc3ce14.png',

                        ),
                        array(
                            'title' => '体测管理',
                            'intro'=>'【学员管理】批量化进行学员分班、调班操作,数字化记录学员个人信息数据,精细化为学员排课、结班.减轻教务人员工作量,极大提升工作效率!',
                            'title_img'=>'https://oss.lcweb01.cn/joomla/20220407/2a2a7be239ead41c590d7a52f43c0b99.png',
                            'title_imgs'=>'https://oss.lcweb01.cn/joomla/20220408/8c4eabf384c6a6409af912a9dfc3ce14.png',

                        ),
                    ),
                    'attr' => array(

                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'desc' => '轮播项标题',
                            'std' => '教务管理',

                        ),
                        'intro' => array(
                            'type' => 'editor',
                            'title' => '轮播项简介',
                            'desc' => '简介',
                            'std' => '',

                        ),
                        'title_img' => array(
                            'type' => 'media',
                            'title' => '左侧图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220407/2a2a7be239ead41c590d7a52f43c0b99.png',

                        ),
                        'title_imgs' => array(
                            'type' => 'media',
                            'title' => '标题左侧图标',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220408/8c4eabf384c6a6409af912a9dfc3ce14.png',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    ),
                ),

                'jw_image_carousel_item_type14' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-img1.png',
                            'image_carousel_img_2' => '',
                            'title' => '培训入驻管理系统',
                            'icon_img_setting'=>'normal',
                            'icon_img'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon1.png',
                            'icon_img_hover'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon2.png',
                            'intro'=>'帮助B端体教培机构开展学员体测管理、招生引流试训、教务教案管理、私域数据管理、体育作业课视频分发及回课等服务.五大工具帮助学校、机构提效增收,让教练更加专业,使学员更具粘性,易于监控管理!',
                            'is_phone'=>0
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-img2.png',
                            'image_carousel_img_2' => '',
                            'title' => '赛事入驻管理系统',
                            'icon_img_setting'=>'normal',
                            'icon_img'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon3.png',
                            'icon_img_hover'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon4.png',
                            'intro'=>'帮助B端赛事主办方系统化管理赛事,组织策划→设计宣传→球员报名→赛程编排→秩序册制定→球友圈语音技术统计→球友圈直播→成绩册汇编→证书打印,一条龙高速服务,效率翻倍!',
                            'is_phone'=>0
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-img3.png',
                            'image_carousel_img_2' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-img4.png',
                            'title' => '篮球技术统计系统(语音版)APP',
                            'icon_img_setting'=>'normal',
                            'icon_img'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon5.png',
                            'icon_img_hover'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon6.png',
                            'intro'=>'自主研发,以语音代替手写并实时准确记录赛事各项数据,帮助B端赛事主办方降低裁判工时工费,同时彻底解决绝大多数业余篮球赛事无数据、无排行的窘境.',
                            'is_phone'=>1
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-img1.png',
                            'image_carousel_img_2' => '',
                            'title' => '球友助手APP',
                            'icon_img_setting'=>'normal',
                            'icon_img'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon1.png',
                            'icon_img_hover'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout14-icon2.png',
                            'intro'=>'帮助B端赛事主办方随时随地开启高清直播,支持手机、摄像机等多种常规设备.直播不仅能在APP中观看,还支持自动保存、回放以及一键分享至微信&朋友圈等功能.',
                            'is_phone'=>1
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                            'depends' => array(
                                array('image_carousel_layout', '!=', 'layout12')
                            ),
                        ),
                        'icon_img_setting'=>array(
                            'type'=>'buttons',
                            'title'=>'图标设置',
                            'tabs'=>true,
                            'values'=>array(
                                array(
                                    'label'=>'正常',
                                    'value'=>'normal'
                                ),
                                array(
                                    'label'=>'激活',
                                    'value'=>'hover'
                                )
                            ),
                            'std'=>'normal'
                        ),
                        'icon_img' => array(
                            'type' => 'media',
                            'title' => '图标',
                            'desc' => '图标',
                            'std' => 'https://www.qiuyouzone.com/img/scene_icon_02.png',
                            'depends'=>array(
                                array('icon_img_setting','=','normal')
                            )
                        ),
                        'icon_img_hover' => array(
                            'type' => 'media',
                            'title' => '图标',
                            'desc' => '图标',
                            'std' => 'https://www.qiuyouzone.com/img/scene_icon_01.png',
                            'depends'=>array(
                                array('icon_img_setting','=','hover')
                            )
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片(pc和手机正放的图片)',
                            'desc' => '轮播项图片',
                            'std' => 'https://www.qiuyouzone.com/img/pc_01.png',
                        ),
                        'is_phone'=>array(
                            'type' => 'checkbox',
                            'title' => '是否是手机图片',
                            'desc' => '是否是手机图片',
                            'std' => 0,
                        ),
                        'image_carousel_img_2' => array(
                            'type' => 'media',
                            'title' => '手机倾斜图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://www.qiuyouzone.com/img/phone2_03.png',
                            'depends'=>array(
                                array('is_phone','=',1)
                            )
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('image_carousel_layout', '!=', 'layout12')
                            )
                        ),
                        'intro'=>array(
                            'title'=>'描述',
                            'type'=>'textarea',
                            'std'=>'帮助B端体教培机构开展学员体测管理、招生引流试训、教务教案管理、私域数据管理、体育作业课视频分发及回课等服务.五大工具帮助学校、机构提效增收,让教练更加专业,使学员更具粘性,易于监控管理!'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout14'),

                    ),
                ),

                'jw_image_carousel_item_type15' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                            'image_carousel_bg' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                            'title' => '认证系统独家资源',
                            'intro'=>'独家级段位认证系统帮助场馆建立差异化优势,形成竞争壁垒,打通培训考试认证链条,让独有资源助力场馆经营成功'
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                            'image_carousel_bg' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                            'title' => '信息化运营更高效',
                            'intro'=>'相比传统场馆,馆家提供全流程、全场景信息化管理 体系,促进场馆组织在线、沟通在线、协同在线、业务 在线、生态在线,运营更高效'
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                            'image_carousel_bg' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                            'title' => '高频操作更便捷',
                            'intro'=>'深刻理解使用场景,在会员管理、销售管理、课程管理等高频使用环节简化操作.实现快速添加会员、多条件搜索、多标签 管理、批量导出会员信息等功能'
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                            'image_carousel_bg' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                            'title' => '丰厚分润收入有保障',
                            'intro'=>'场馆可获得会员在海健身商城消费分润;场 馆优质内容可在海健身平台销售,迅速在全网海量用户中展示,人气、盈利双丰收'
                        ),
                        array(
                            'image_carousel_img' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                            'image_carousel_bg' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                            'title' => '招聘求职好帮手',
                            'intro'=>'场馆发布招聘信息,直达海健身运动达人、认证教练,帮助场馆迅速招揽专业人才,破除用人烦恼'
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'std' => '',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图标',
                            'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-icon.png',
                        ),
                        'image_carousel_bg' => array(
                            'type' => 'media',
                            'title' => '轮播项背景图',
                            'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-item-bg.png',
                        ),
                        'intro' => array(
                            'type' => 'textarea',
                            'title' => '轮播项简介',
                            'std' => '',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                            'depends' => array(
                                array('image_carousel_layout', '!=', 'layout12')
                            ),
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '轮播项链接',
                            'desc' => '轮播项链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('image_carousel_layout', '!=', 'layout12')
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(
                                array('image_carousel_img_link', '!=', '')
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout15'),

                    ),
                ),

                // 布局16配置项
                'jw_image_carousel_item_type16' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'pc_bg' => 'https://oss.lcweb01.cn/joomla/20220602/f3812387bf4934c111de04c03d587a68.jpg',
                            'phone_bg' => 'https://oss.lcweb01.cn/joomla/20220602/e01f96d4367fe491608dc8aa6cfbae4a.jpg',
                            'title' => '东运优宜酷爱车家',
                            'intro' => '辽宁东运优宜酷爱车家汽车服务有限公司经营范围包括:汽车租赁,汽车零配件批发,新能源汽车整车销售,新能源汽车换电设施销售,汽车新车销售,汽车旧车销售,汽车零配件零售,汽车装饰用品销售等.'
                        ),
                        array(
                            'pc_bg' => 'https://oss.lcweb01.cn/joomla/20220602/e76fc2ec82096e16730f0ec6543fc44d.jpg',
                            'phone_bg' => 'https://oss.lcweb01.cn/joomla/20220602/e76fc2ec82096e16730f0ec6543fc44d.jpg',
                            'title' => '如祭',
                            'intro' => '如祭APP平台是专门给那些需要祭奠亲人先辈朋友却不能实现的小伙伴而准备的,我国各地政府都在深化移风易俗,大力推动文明祭祀模式,鼓励民众采取鲜花祭扫、网络祭扫、"云祭祀"、居家追思等.'
                        ),
                    ),
                    'attr' => array(
                        'pc_bg' => array(
                            'type' => 'media',
                            'title' => 'pc端图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220602/f3812387bf4934c111de04c03d587a68.jpg',
                        ),
                        'phone_bg' => array(
                            'type' => 'media',
                            'title' => '移动端图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220602/e01f96d4367fe491608dc8aa6cfbae4a.jpg',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '东运优宜酷爱车家',
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => '介绍',
                            'std' => '辽宁东运优宜酷爱车家汽车服务有限公司经营范围包括:汽车租赁,汽车零配件批发,新能源汽车整车销售,新能源汽车换电设施销售,汽车新车销售,汽车旧车销售,汽车零配件零售,汽车装饰用品销售等.',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    ),
                ),
                'jw_image_carousel_item_type17' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(

                        array(
                            'image_carousel_bg' => 'https://oss.lcweb01.cn/joomla/20220517/85b2d0a339475e7b0654cabd0973732c.jpg',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220517/4b8fc114ce8288652cba164ddd39f809.jpg',
                            'title' => '艺星整形',
                            'intro'=>'基于客户实际情况,进行账户全托管+线索API的投放优化, 构建搜索oCPC+ 信息流oCPC+基木鱼落地页整体投放链条,大幅提升有效线索和转化效率.',
                            'is_link' => '1',
                            'image_carousel_img_link' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',

                        ),
                        array(
                            'image_carousel_bg' => 'https://oss.lcweb01.cn/joomla/20220517/28f0fb3b2ccf41e7af7768e8af7fa31b.jpg',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220517/6b77a3fabc5b196f6f25973259ca6219.jpg',
                            'title' => '森鹰门窗',
                            'intro'=>'基于客户实际情况,进行账户全托管+线索API的投放优化,构建搜索oCPC+信息流oCPC+基木鱼落地页整体投放链条,大幅提升有效线索和转化效率.',
                            'is_link' => '1',
                            'image_carousel_img_link' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',

                        ),

                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'std' => '',
                        ),
                        'intro' => array(
                            'type' => 'textarea',
                            'title' => '轮播项简介',
                            'std' => '',
                        ),
                        'image_carousel_bg' => array(
                            'type' => 'media',
                            'title' => 'PC轮播图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220517/85b2d0a339475e7b0654cabd0973732c.jpg',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '手机轮播图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220517/4b8fc114ce8288652cba164ddd39f809.jpg',
                        ),

                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                            'depends' => array(
                                array('image_carousel_layout', '!=', 'layout12')
                            ),
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '轮播项链接',
                            'desc' => '轮播项链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('image_carousel_layout', '!=', 'layout12')
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(
                                array('image_carousel_img_link', '!=', '')
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout17'),

                    ),
                ),

                'jw_image_carousel_item_type18' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220602/1a91cb42e7bf6da4b0cf11124668d83f.png',
                            'title'=>'由设计师量身定制',
                            'intro' => '行业个性化定制,提升企业形象,彰显实力',
                            'is_link' => 0,
                            'image_carousel_img_link' => '',
                            'link_open_new_window' => ''
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220602/8f3b1fbc5a90c42bbe0ddb2c282c179c.png',
                            'title'=>'兼容手机 ipad等',
                            'intro' => '有助于接触更多手机和平板电脑用户',
                            'is_link' => 0,
                            'image_carousel_img_link' => '',
                            'link_open_new_window' => ''
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220602/07de9484250227a11755d01ca2b22b4e.png',
                            'title'=>'显示效果炫酷',
                            'intro' => '动画效果生动炫酷,吸引客户眼球',
                            'is_link' => 0,
                            'image_carousel_img_link' => '',
                            'link_open_new_window' => ''
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => '显示效果炫酷',
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => '介绍',
                            'desc' => '介绍',
                            'std' => '',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://pagefactory.joomla.work/images/pagefactory/addons/js_slideshow/slideshow-default-bg.jpg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout18'),
                    ),
                ),
                'jw_image_carousel_item_type19' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'logo' => 'https://oss.lcweb01.cn/joomla/20220602/ff6df994e033630132e2448dd3162a2b.png',
                            'title'=>'北京乾正隆文化交流有限公司',
                            'admin'=>'HR行政经理&nbsp;&nbsp;马静静',
                            'intro' => '现在,我们的员工每天用手机打卡、请假、查看工资条和公司通知等,不仅很方便,而且非常符合年轻职场人的使用习惯.而HR也从繁琐细碎的日常工作中解脱出来,投入到更有价值的人才管理和挖掘工作中.'
                        ),
                        array(
                            'logo' => 'https://oss.lcweb01.cn/joomla/20220602/d8840c29be814649acdf6c662fe19a66.png',
                            'title'=>'山西普智商贸有限公司',
                            'admin'=>'HR行政经理&nbsp;&nbsp;王琳',
                            'intro' => '使用了资海云人资圈之后,再也不用为了保存一些纸请假条和简历档案烦恼了,后台的记录是永远不会丢失的,不仅节约了工作时间,还节约了公司的办公资源,为我们的工作减轻了很多负担.'
                        ),
                        array(
                            'logo' => 'https://oss.lcweb01.cn/joomla/20220602/de20188645aaefc2682003a99710aecc.png',
                            'title'=>'北京球友圈网络科技有限责任公司',
                            'admin'=>'HR行政经理&nbsp;&nbsp;郝明',
                            'intro' => '之前每月为了统计员工的考勤和工资都搞得我们很忙,现在工作轻松了很多,每月不仅可以直接导出考勤报表,就连工资统计报表也可以直接导出,并且所有数据都一目了然,终于告别了繁琐的统计工作.'
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'std' => '北京乾正隆文化交流有限公司',
                        ),
                        'admin' => array(
                            'type' => 'text',
                            'title' => '负责人',
                            'std' => 'HR行政经理&nbsp;&nbsp;马静静',
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => '介绍',
                            'std' => '现在,我们的员工每天用手机打卡、请假、查看工资条和公司通知等,不仅很方便,而且非常符合年轻职场人的使用习惯.而HR也从繁琐细碎的日常工作中解脱出来,投入到更有价值的人才管理和挖掘工作中.',
                        ),
                        'logo' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout19-logo1.png',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout19'),
                    ),
                ),
                'jw_image_carousel_item_type20' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220602/bf28a6b0e2cc0acb38d70938d2debaac.jpg',
                            'title'=>'资海云人资圈',
                            'subtitle'=>'行政人资一站式服务平台',
                            'intro' => '',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/b8359d5752bfb964207487938765b929.png',
                            'img2' => 'https://oss.lcweb01.cn/joomla/20220602/c2e9f0fd057a5d87e7e8b199bb331413.png',
                            'bg_phone1' => 'https://oss.lcweb01.cn/joomla/20220602/6e11428921c0652fd628e5787e46b4cd.jpg',
                            'bg_phone2' => '',
                            'bg_phone3' => '',
                        ),
                        array(
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220602/e9433737ec7f91472c84dd4767d7640d.jpg',
                            'title'=>'',
                            'subtitle'=>'',
                            'intro' => '',
                            'img' => '',
                            'img2' => '',
                            'bg_phone1' => 'https://oss.lcweb01.cn/joomla/20220602/08028b149d33257b688f3081809cf747.jpg',
                            'bg_phone2' => 'https://oss.lcweb01.cn/joomla/20220602/ff53029619db650584fc5ac1ed2aba18.jpg',
                            'bg_phone3' => 'https://oss.lcweb01.cn/joomla/20220602/bb8bdbf5faed4f57f6f3e08a9a8d7ae9.jpg',
                        ),
                        array(
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220602/6bdb28feba1ecb197b531e900ca8ab5b.jpg',
                            'title'=>'无感知考勤系统',
                            'subtitle'=>'开启考勤智能新时代',
                            'intro' => '<ul>
                                <li class="item2_3 ani" swiper-animate-effect="lightSpeedInLeft" swiper-animate-duration="1s" swiper-animate-delay="0s">超清人脸摄像头检查机器学习</li>
                                <li class="item2_4 ani" swiper-animate-effect="lightSpeedInLeft" swiper-animate-duration="1s" swiper-animate-delay="0s">AI智能技术高精准度进行智能捕捉,抓拍对比</li>
                                <li class="item2_5 ani" swiper-animate-effect="lightSpeedInLeft" swiper-animate-duration="1s" swiper-animate-delay="0s">迅速响应,无需驻足等待,只需路过即可考勤</li>
                            </ul>',
                            'img' => '',
                            'img2' => '',
                            'bg_phone1' => 'https://oss.lcweb01.cn/joomla/20220602/6bdb28feba1ecb197b531e900ca8ab5b.jpg',
                            'bg_phone2' => '',
                            'bg_phone3' => '',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'desc' => '第二个轮播项无效',
                            'title' => '轮播项标签名',
                            'std' => '北京乾正隆文化交流有限公司',
                        ),
                        'subtitle' => array(
                            'type' => 'text',
                            'desc' => '第二个轮播项无效',
                            'title' => '负责人',
                            'std' => 'HR行政经理&nbsp;&nbsp;马静静',
                        ),
                        'intro' => array(
                            'type' => 'editor',
                            'desc' => '第一、二个轮播项无效',
                            'title' => '介绍',
                            'std' => '',
                        ),
                        'bg' => array(
                            'type' => 'media',
                            'title' => 'pc端轮播项背景图',
                            'std' => '',
                        ),
                        'bg_phone1' => array(
                            'type' => 'media',
                            'title' => '移动端轮播项背景图1',
                            'std' => '',
                        ),
                        'bg_phone2' => array(
                            'type' => 'media',
                            'desc' => '第一、三个轮播项无效',
                            'title' => '移动端轮播项背景图2',
                            'std' => '',
                        ),
                        'bg_phone3' => array(
                            'type' => 'media',
                            'desc' => '第一、三个轮播项无效',
                            'title' => '移动端轮播项背景图3',
                            'std' => '',
                        ),
                        'img' => array(
                            'type' => 'media',
                            'desc' => '第二、三个轮播项无效',
                            'title' => '轮播项图片',
                            'std' => '',
                        ),
                        'img2' => array(
                            'type' => 'media',
                            'desc' => '仅第一个轮播项移动端有效',
                            'title' => '轮播项图片2',
                            'std' => '',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout20'),
                    ),
                ),

                'type5_title_left' => array(
                    'type' => 'text',
                    'title' => '左标题',
                    'desc' => '标题',
                    'std' => 'H5',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout5')
                    ),
                ),

                'type5_title_right' => array(
                    'type' => 'text',
                    'title' => '右标题',
                    'desc' => '标题',
                    'std' => '案例',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout5')
                    ),
                ),

                'type5_desc' => array(
                    'type' => 'text',
                    'title' => '介绍',
                    'desc' => '介绍',
                    'std' => '真实联网项目',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout5')
                    ),
                ),

                'type5_bg_img' => array(
                    'type' => 'media',
                    'title' => '背景图片',
                    'desc' => '背景图片',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/z01.jpg',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout5')
                    ),
                ),

                'type6_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'desc' => '标题',
                    'std' => '小程序案例',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout6')
                    ),
                ),

                'type6_desc' => array(
                    'type' => 'text',
                    'title' => '介绍',
                    'desc' => '介绍',
                    'std' => '咨询热线:400-622-8811',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout6')
                    ),
                ),


                'jw_image_carousel_item_type6' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title_sm' => '美食类小程序展示',
                            'desc_sm' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                            'content_sm' => '编号:wx_00741',
                        )
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片1',
                            'desc' => '轮播项图片1',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'title_sm' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'desc' => '标题',
                            'std' => '美食类小程序展示',
                        ),
                        'desc_sm' => array(
                            'type' => 'text',
                            'title' => '介绍',
                            'desc' => '介绍',
                            'std' => '烧烤｜牛肉｜生鲜水产｜餐饮美食',
                        ),
                        'content_sm' => array(
                            'type' => 'text',
                            'title' => '内容',
                            'desc' => '内容',
                            'std' => '编号:wx_00741',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout6')
                    ),
                ),

                'jw_image_carousel_item_type7' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title_type7' => '小程序',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img2' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img3' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img4' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'title_type7' => 'PC端直播',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img2' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img3' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'image_carousel_img4' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'title_type7' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'desc' => '标题',
                            'std' => '小程序',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片中间',
                            'desc' => '轮播项图片1',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        'image_carousel_img1' => array(
                            'type' => 'media',
                            'title' => '轮播项图片1',
                            'desc' => '轮播项图片1',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        'image_carousel_img2' => array(
                            'type' => 'media',
                            'title' => '轮播项图片2',
                            'desc' => '轮播项图片2',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        'image_carousel_img3' => array(
                            'type' => 'media',
                            'title' => '轮播项图片3',
                            'desc' => '轮播项图片3',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        'image_carousel_img4' => array(
                            'type' => 'media',
                            'title' => '轮播项图片4',
                            'desc' => '轮播项图片4',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout7')
                    ),
                ),
                'jw_image_carousel_item_type21' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title_type21' => '认证系统独家资源',
                            'content_type21' => '独家级段位认证系统帮助场馆建立差异化优势,形成竞争壁垒,打通培训考试认证链条,让独有资源助力场馆经营成功.',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'image_carousel_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/272495a038b394eba58f2a2173391f61.png',
                            'image_carousel_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/52638f58dc7df972be07ef344ff04c96.png',
                        ),
                        array(
                            'title_type21' => '信息化运营更高效',
                            'content_type21' => '相比传统场馆,馆家提供全流程、全场景信息化管理体系,促进场馆组织在线、沟通在线、协同在线、业务在线、生态在线,运营更高效.',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'image_carousel_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/62fc686501ea2f8ff716e1eb6a5f79fb.png',
                            'image_carousel_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/46017bbad4dfaa697023bb3bfbec8ce5.png',
                        ),
                        array(
                            'title_type21' => '丰厚分润收入有保障',
                            'content_type21' => '场馆可获得会员在海健身商城消费分润;场馆优质内容可在海健身平台销售,迅速在全网海量用户中展示,人气、盈利双丰收.',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'image_carousel_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/b1b10e1a5a0d0ee4a2aa006b889b0ea1.png',
                            'image_carousel_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/2a3c0ba006a7a90a834d77aa9b51fb17.png',
                        ),
                        array(
                            'title_type21' => '高频操作更便捷',
                            'content_type21' => '深刻理解使用场景,在会员管理、销售管理、课程管理等高频使用环节简化操作.实现快速添加会员、多条件搜索、多标签管理、批量导出会员信息等功能.',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'image_carousel_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/d62a46fd40bcba2b81889b05c5146595.png',
                            'image_carousel_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/bdcf6f2f2c982447076ac116cd50adba.png',
                        ),
                        array(
                            'title_type21' => '招聘求职',
                            'content_type21' => '场馆发布招聘好帮手信息,直达海健身运动达人、认证教练,帮助场馆迅速招揽专业人才,破除用人烦恼.',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'image_carousel_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/e1ebd2c7407f9cf1ff057dad89e6b869.png',
                            'image_carousel_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220601/5940797a9cdea0760671090757b15b25.png',
                        ),
                    ),
                    'attr' => array(
                        'title_type21' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'desc' => '标题',
                            'std' => '标题',
                        ),
                        'content_type21' => array(
                            'type' => 'text',
                            'title' => '简介',
                            'desc' => '简介',
                            'std' => '简介',
                        ),
                        'image_carousel_img_pc' => array(
                            'type' => 'media',
                            'title' => 'pc图片',
                            'desc' => 'pc图片',
                            'std' => '',
                        ),
                        'image_carousel_img_sj' => array(
                            'type' => 'media',
                            'title' => '手机图片',
                            'desc' => '手机图片',
                            'std' => '',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout21')
                    ),
                ),
                'jw_type7_bg_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '整体背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220518/96529d63c154443df192811e57e6578b.jpg',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout7')
                    ),
                ),
                'jw_type7_prev_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '上一张切换按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout7')
                    ),
                ),
                'jw_type7_next_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '下一张切换按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout7')
                    ),
                ),

                'jw_image_carousel_item_type10' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'title'=>'计算机软件著作权登记证书'
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => '计算机软件著作权登记证书'
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                        'mask_content' => array(
                            'type' => 'text',
                            'title' => JText::_('遮罩内容'),
                            'std' => '营销内容',
                            'depends' => array(array('image_carousel_layout', '=', 'layout3')),
                        ),
                    ),
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                ),

                'jw_image_carousel_item_type11' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img1.png',
                            'title'=>'黑龙江省哈尔滨市中华巴洛克街区',
                            'title_en'=>'Zhonghua Baroque block, Harbin City, Heilongjiang Province',
                            'intro'=>'中华巴洛克街区是位于黑龙江省哈尔滨市的景区,也是中国国内现存面积最大、保存最完整的中华巴洛克建筑群,有着独特的历史建筑,特色院落、特色胡同等物质文化资源.<br>
                                需要针对该景区的文化底蕴进行专门的网站制作,而该景区并没有专门的技术人员和成本去进行制作,市面上的建站平台也没有可以契合网站复古文化主题的样式供其选择.',
                            'content'=>'<div class="title">解决方案及价值体现</div>
                                <p>利用建站通产品为中华巴洛克街区制作了创意型网站.网站样式符合客户对于文化底蕴主题的需求,且视觉观感大气简约,网站同步适配PC端、手机端与多平台小程序端,利用多角度为客户达到利用网站进行文化宣传的需求,有效解决了客户的问题.</p>
                                <p>建站通不需要客户投入大量人力与技术成本,仅需将网站制作的需求提出,产品就可提供海量的样式供其进行选择,且搭建速度快,还可PC、手机、小程序多端适配,满足客户对于网站样式需求的同时,也可满足不同端口网站的制作,为客户的工作降低了成本,增加了效率.</p>
                                <div class="img">
                                  <img src="'.JUri::base().'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img2.png" alt="">
                                </div>'
                        ),
                        array(
                            'image_carousel_img' => 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img1.png',
                            'title'=>'黑龙江省哈尔滨市中华巴洛克街区',
                            'title_en'=>'Zhonghua Baroque block, Harbin City, Heilongjiang Province',
                            'intro'=>'中华巴洛克街区是位于黑龙江省哈尔滨市的景区,也是中国国内现存面积最大、保存最完整的中华巴洛克建筑群,有着独特的历史建筑,特色院落、特色胡同等物质文化资源.<br>
                                需要针对该景区的文化底蕴进行专门的网站制作,而该景区并没有专门的技术人员和成本去进行制作,市面上的建站平台也没有可以契合网站复古文化主题的样式供其选择.',
                            'content'=>'<div class="title">解决方案及价值体现</div>
                                <p>利用建站通产品为中华巴洛克街区制作了创意型网站.网站样式符合客户对于文化底蕴主题的需求,且视觉观感大气简约,网站同步适配PC端、手机端与多平台小程序端,利用多角度为客户达到利用网站进行文化宣传的需求,有效解决了客户的问题.</p>
                                <p>建站通不需要客户投入大量人力与技术成本,仅需将网站制作的需求提出,产品就可提供海量的样式供其进行选择,且搭建速度快,还可PC、手机、小程序多端适配,满足客户对于网站样式需求的同时,也可满足不同端口网站的制作,为客户的工作降低了成本,增加了效率.</p>
                                <div class="img">
                                  <img src="'.JUri::base().'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img2.png" alt="">
                                </div>'
                        ),
                        array(
                            'image_carousel_img' => 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img1.png',
                            'title'=>'黑龙江省哈尔滨市中华巴洛克街区',
                            'title_en'=>'Zhonghua Baroque block, Harbin City, Heilongjiang Province',
                            'intro'=>'中华巴洛克街区是位于黑龙江省哈尔滨市的景区,也是中国国内现存面积最大、保存最完整的中华巴洛克建筑群,有着独特的历史建筑,特色院落、特色胡同等物质文化资源.<br>
                                需要针对该景区的文化底蕴进行专门的网站制作,而该景区并没有专门的技术人员和成本去进行制作,市面上的建站平台也没有可以契合网站复古文化主题的样式供其选择.',
                            'content'=>'<div class="title">解决方案及价值体现</div>
                                <p>利用建站通产品为中华巴洛克街区制作了创意型网站.网站样式符合客户对于文化底蕴主题的需求,且视觉观感大气简约,网站同步适配PC端、手机端与多平台小程序端,利用多角度为客户达到利用网站进行文化宣传的需求,有效解决了客户的问题.</p>
                                <p>建站通不需要客户投入大量人力与技术成本,仅需将网站制作的需求提出,产品就可提供海量的样式供其进行选择,且搭建速度快,还可PC、手机、小程序多端适配,满足客户对于网站样式需求的同时,也可满足不同端口网站的制作,为客户的工作降低了成本,增加了效率.</p>
                                <div class="img">
                                  <img src="'.JUri::base().'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img2.png" alt="">
                                </div>'
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'std' => '黑龙江省哈尔滨市中华巴洛克街区'
                        ),
                        'title_en' => array(
                            'type' => 'text',
                            'title' => '轮播项标签英文名',
                            'std' => 'Zhonghua Baroque block, Harbin City, Heilongjiang Province'
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项左侧封面图',
                            'std' => 'components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img1.png',
                        ),
                        'intro' => array(
                            'type' => 'textarea',
                            'title' => '简介',
                            'std' => '中华巴洛克街区是位于黑龙江省哈尔滨市的景区,也是中国国内现存面积最大、保存最完整的中华巴洛克建筑群,有着独特的历史建筑,特色院落、特色胡同等物质文化资源.<br>
                                需要针对该景区的文化底蕴进行专门的网站制作,而该景区并没有专门的技术人员和成本去进行制作,市面上的建站平台也没有可以契合网站复古文化主题的样式供其选择.'
                        ),
                        'content' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'desc'=>'标题请切换至"Source Code"处添加"title"类名(class="title").图片请套一层div,并添加类名img(&lt;div class=&quot;img&quot;&gt;&lt;img src=&quot;&quot;&gt;&lt;/div&gt;)',
                            'std' => '<div class="title">解决方案及价值体现</div>
                                <p>利用建站通产品为中华巴洛克街区制作了创意型网站.网站样式符合客户对于文化底蕴主题的需求,且视觉观感大气简约,网站同步适配PC端、手机端与多平台小程序端,利用多角度为客户达到利用网站进行文化宣传的需求,有效解决了客户的问题.</p>
                                <p>建站通不需要客户投入大量人力与技术成本,仅需将网站制作的需求提出,产品就可提供海量的样式供其进行选择,且搭建速度快,还可PC、手机、小程序多端适配,满足客户对于网站样式需求的同时,也可满足不同端口网站的制作,为客户的工作降低了成本,增加了效率.</p>
                                <div class="img">
                                  <img src="'.JUri::base().'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_img2.png" alt="">
                                </div>'
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                    ),
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout11'),
                        array('need_data', '=', '0'),
                    ),
                ),

                /*'carousel_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播容器左边距'),
                    'desc' => JText::_('轮播容器左边距'),
                    'min' => 0,
                    'max' => 1000,
                    'std'=>0,
                    'responsive' => true,
                ),*/

                'carousel_item' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项'),
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout12'),
                        array('image_carousel_layout','!=','layout13'),
                        array('image_carousel_layout','!=','layout21'),
                    ),
                ),
                'carousel_item_layout8' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项'),
                    'desc' => JText::_('轮播项'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                    ),
                ),
                'carousel_layout8_settings'=>array(
                    'type'=>'buttons',
                    'values'=>array(
                        array(
                            'label'=>'轮播项',
                            'value'=>'item'
                        ),
                        array(
                            'label'=>'内容',
                            'value'=>'content'
                        ),
                    ),
                    'std'=>'item',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                    )
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'min' => 0,
                    'max' => 2000,
                    'std' => 220,
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1'),
                    ),
                ),
                'carousel_height_layout2' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 160,
                        'sm' => 490,
                        'xs' => 240
                    ),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2'),
                    ),
                ),
                'carousel_height_layout3' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 160,
                        'sm' => 158,
                        'xs' => 100
                    ),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3'),
                    ),
                ),
                'carousel_height_layout13' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 530,
                        'sm' => 430,
                        'xs' => 300
                    ),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    ),
                ),

                'carousel_padding_layout13'=>array(
                    'type'=>'padding',
                    'title'=>'轮播内间距',
                    'std' => array('md' => '20px 20px 20px 20px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 0px 0px 0px'),
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_left_width_layout13'=>array(
                    'type'=>'slider',
                    'title'=>'图片宽度(%)',
                    'min' => 0,
                    'max' => 100,
                    'std' => array(
                        'md' => 43,
                        'sm' => 40,
                        'xs' => 100
                    ),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                // 右侧内容背景色
                'carousel_content_bg_layout13'=>array(
                    'type'=>'color',
                    'title'=>'右侧整体背景色',
                    'std' => '#ffffff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_content_padding_layout13'=>array(
                    'type'=>'padding',
                    'title'=>'右侧整体内间距',
                    'std' => array('md' => '20px 20px 20px 20px', 'sm' => '10px 10px 10px 10px', 'xs' => '10px 10px 10px 10px'),
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_left_cont_layout13'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容左侧距离',
                    'min' => 0,
                    'max' => 100,
                    'std' => 20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_title_layout13'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体大小',
                    'std' => 20,
                    'min' => 12,
                    'max' => 50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_cont_layout13'=>array(
                    'type'=>'color',
                    'title'=>'标题字体颜色',
                    'color' => '#333',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout13'),
                    )
                ),
                'carousel_control_arrow_layout13' => array(
                    'type' => 'separator',
                    'title' => '箭头控制器',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout13'),
                    )
                ),
                'carousel_arrow_left_layout13' => array(
                    'type' => 'media',
                    'title' => '左箭头',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220407/bf37f99e719038d36ad2dd0cca222e35.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout13'),
                    )
                ),
                'carousel_arrow_right_layout13' => array(
                    'type' => 'media',
                    'title' => '右箭头',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220407/f610f1c2128c963bb4c2bcdef1999711.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout13'),
                    )
                ),
                'carousel_zsq_layout13' => array(
                    'type' => 'color',
                    'title' => '指示器的选中色',
                    'std' => '#E0BE68',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout13'),
                    )
                ),
                'carousel_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域占容器的百分比'),
                    'desc' => JText::_('轮播区域占容器的百分比'),
                    'min' => 0,
                    'max' => 100,
                    'std' =>80,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    )
                ),
                'carousel_item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                    'std' => 4
                ),
                'carousel_item_number_layout2' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    ),
                    'std' =>3
                ),
                'carousel_item_number_layout3' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    ),
                    'std' =>3,
                ),
                'carousel_margin' => array(
                    'type' => 'number',
                    'title' => JText::_('slide之间的距离'),
                    'desc' => JText::_('slide之间的距离'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                    'std' => 15,
                ),
                'carousel_margin_layout2' => array(
                    'type' => 'number',
                    'title' => JText::_('slide之间的距离'),
                    'desc' => JText::_('slide之间的距离'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    ),
                    'std' => 23,
                ),
                'carousel_height_layout8' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度(占一屏的百分比)'),
                    'desc' => JText::_('单位:%'),
                    'min' => 0,
                    'max' => 100,
                    'std' => array('md'=>100,'sm'=>80,'xs'=>80),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_use_aspect_ratio_layout9' => array(
                    'type' => 'checkbox',
                    'title' => '轮播区域是否启用宽高比',
                    'std' => 0,
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                    ),
                ),
                'carousel_aspect_ratio_layout9' => array(
                    'type' => 'text',
                    'title' => '轮播区域宽高比（宽/高）',
                    'std' => '16/9',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_use_aspect_ratio_layout9','=','1'),
                    ),
                ),
                'carousel_height_layout9' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度(px)'),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array('md'=>600,'sm'=>400,'xs'=>300),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_use_aspect_ratio_layout9','!=','1'),
                    ),
                ),

//                样式9
                'carousel_pagination_setting_layout9'=>array(
                    'type'=>'buttons',
                    'title'=>'分页设置',
                    'std'=>'dot',
                    'values'=>array(
                        array(
                            'label'=>'圆点',
                            'value'=>'dot'
                        ),
                        array(
                            'label'=>'数字',
                            'value'=>'num'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                    ),
                ),
                'carousel_pagination_bottom_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'分页下边距',
                    'std'=>20,
                    'max'=>300,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                    ),
                ),
                'carousel_pagination_color_layout9'=>array(
                    'type'=>'color',
                    'title'=>'分页颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                    ),
                ),
                'carousel_pagination_margin_layout9'=>array(
                    'type'=>'margin',
                    'title'=>'圆点边距',
                    'std'=>'0px 10px 0px 10px',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                    ),
                ),
                'carousel_pagination_status_setting_layout9'=>array(
                    'type'=>'buttons',
                    'title'=>'分页状态',
                    'std'=>'normal',
                    'values'=>array(
                        array(
                            'label'=>'正常',
                            'value'=>'normal'
                        ),
                        array(
                            'label'=>'选中',
                            'value'=>'selected'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                    ),
                ),
                'carousel_pagination_size_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'分页大小',
                    'desc'=>'如果分页大小设置为奇(偶)数,选中状态下的分页大小也设置为奇(偶)数',
                    'std'=>4,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                        array('carousel_pagination_status_setting_layout9','=','normal'),
                    ),
                ),
                'carousel_pagination_size_selected_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'选中分页大小',
                    'desc'=>'如果选中状态下的分页大小设置为奇(偶)数,正常状态下的分页大小也设置为奇(偶)数',
                    'std'=>16,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','dot'),
                        array('carousel_pagination_status_setting_layout9','=','selected'),
                    ),
                ),
                'carousel_line_fixed_layout9'=>array(
                    'type'=>'checkbox',
                    'title'=>'数字分页部分是否固定',
                    'std'=>0,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_line_width_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'线宽',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_line_height_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'线长',
                    'std'=>100,
                    'max'=>800,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_line_color_layout9'=>array(
                    'type'=>'color',
                    'title'=>'线条颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_nums_top_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'数字分页上边距',
                    'std'=>100,
                    'max'=>500,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_nums_color_layout9'=>array(
                    'type'=>'color',
                    'title'=>'数字分页颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_nums_now_size_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'当前页字号',
                    'std'=>24,
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_nums_now_weight_layout9'=>array(
                    'type' => 'select',
                    'title'=>'当前页字体粗细',
                    'values'=>array(
                        '300'=>'300',
                        '400'=>'400',
                        '500'=>'500',
                        '600'=>'600',
                        '700'=>'700',
                        '800'=>'800',
                        '900'=>'900',
                    ),
                    'std'=>'600',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_nums_font_size_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'"/"和总页数分页字号',
                    'std'=>16,
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_split_top_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'"/"和当前页的错位距离',
                    'std'=>2,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),
                'carousel_total_top_layout9'=>array(
                    'type'=>'slider',
                    'title'=>'总页数和当前页的错位距离',
                    'std'=>4,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout9'),
                        array('carousel_pagination_setting_layout9','=','num'),
                    ),
                ),

//                样式10
                'carousel_item_setting_layout10' => array(
                    'type' => 'buttons',
                    'title' => '设置',
                    'values' => array(
                        array(
                            'label' => '轮播项',
                            'value' => 'item'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        )
                    ),
                    'std' => 'item',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_nums_layout10' => array(
                    'type' => 'slider',
                    'title' => '展示个数',
                    'std' => array('md'=>3,'sm'=>2,'xs'=>1),
                    'max' => 3,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    ),
                    'responsive' => true
                ),
                'carousel_item_margin_layout10' => array(
                    'type' => 'slider',
                    'title' => '展示项间隔',
                    'std' => array('md'=>-10,'sm'=>0,'xs'=>0),
                    'max' => 20,
                    'min'=>-20,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    ),
                    'responsive' => true
                ),
                'carousel_width_layout10' => array(
                    'type' => 'slider',
                    'title' => '轮播宽度(%)',
                    'std' => 100,
                    'max' => 100,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    ),
                ),
                'carousel_height_layout10' => array(
                    'type' => 'slider',
                    'title' => '轮播高度',
                    'std' => array('md' => 260, 'sm' => 260, 'xs' => 260),
                    'max' => 1000,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    ),
                    'responsive' => true
                ),
                'carousel_padding_layout10' => array(
                    'type' => 'padding',
                    'title' => '轮播项内边距',
                    'std' => '0px 30px 30px 30px',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    ),
                ),
                'carousel_content_left_layout10' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容左边距'),
                    'std' => '20',
                    'max' => '200',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                    )
                ),
                'carousel_content_top_layout10' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容上边距(%)'),
                    'std' => '24',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                    )
                ),
                'carousel_content_setting_layout10' => array(
                    'type' => 'buttons',
                    'title' => '内容设置',
                    'values' => array(
                        array(
                            'label' => '序号',
                            'value' => 'index'
                        ),
                        array(
                            'label' => '圆点',
                            'value' => 'dot'
                        ),
                        array(
                            'label' => '图标',
                            'value' => 'icon'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        )
                    ),
                    'std' => 'index',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                    )
                ),
                'carousel_index_font_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '"/"和总页数字号',
                    'std' => '14',
                    'max' => '40',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_now_font_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '序号字号',
                    'std' => '24',
                    'max' => '40',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_now_translate_layout10' => array(
                    'type' => 'slider',
                    'title' => '序号向上平移距离',
                    'std' => '-3',
                    'max' => '10',
                    'min' => '-10',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_dot_top_layout10' => array(
                    'type' => 'slider',
                    'title' => '圆点上边距',
                    'std' => '0',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'dot'),
                    )
                ),
                'carousel_dot_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '圆点大小',
                    'std' => '4',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'dot'),
                    )
                ),
                'carousel_dot_right_layout10' => array(
                    'type' => 'slider',
                    'title' => '圆点右边距',
                    'std' => '4',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'dot'),
                    )
                ),
                'carousel_icon_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '图标大小',
                    'std' => '30',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                    )
                ),
                'carousel_icon_top_layout10' => array(
                    'type' => 'slider',
                    'title' => '图标上边距',
                    'std' => '20',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                    )
                ),
                'carousel_icon_img_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '图标图片大小(%)',
                    'std' => '60',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                    )
                ),
                'carousel_title_font_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std' => '14',
                    'max'=>'40',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'title'),
                    )
                ),
                'carousel_title_top_layout10' => array(
                    'type' => 'slider',
                    'title' => '标题上边距',
                    'std' => '16',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'title'),
                    )
                ),
                'carousel_setting_layout10' => array(
                    'type' => 'buttons',
                    'title' => '轮播项设置',
                    'values' => array(
                        array(
                            'label' => '未选中',
                            'value' => 'unselected'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'selected'
                        )
                    ),
                    'std' => 'unselected',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '!=', 'img'),
                    )
                ),
                'carousel_unselected_scale_layout10' => array(
                    'type' => 'text',
                    'title' => '未选中项目缩小倍数',
                    'std' => 0.8,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_unselected_bg_layout10' => array(
                    'type' => 'media',
                    'title' => '未选中项目背景图',
                    'std' => '',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_unselected_bg_color_layout10' => array(
                    'type' => 'color',
                    'title' => '未选中项目背景色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_unselected_top_layout10' => array(
                    'type' => 'slider',
                    'title' => '项目背景上边距(%)',
                    'std' => '20',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_unselected_radius_layout10' => array(
                    'type' => 'slider',
                    'title' => '未选中项目背景圆角',
                    'std' => '10',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_selected_bg_layout10' => array(
                    'type' => 'media',
                    'title' => '选中项目背景图',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout10_active.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'selected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_selected_bg_color_layout10' => array(
                    'type' => 'color',
                    'title' => '选中项目背景色',
                    'std' => '#b71b28',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_setting_layout10', '=', 'selected'),
                        array('carousel_item_setting_layout10', '=', 'item'),
                    )
                ),
                'carousel_img_width_layout10' => array(
                    'type' => 'slider',
                    'title' => '图片宽度(%)',
                    'std' => '40',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_img_height_layout10' => array(
                    'type' => 'slider',
                    'title' => '图片高度(%)',
                    'std' => '100',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_img_translate_layout10' => array(
                    'type' => 'slider',
                    'title' => '图片向上平移距离',
                    'std' => '0',
                    'max' => 200,
                    'min' => -200,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_img_padding_layout10' => array(
                    'type' => 'padding',
                    'title' => '图片内边距',
                    'std' => '3px 3px 3px 3px',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_border_width_layout10' => array(
                    'type' => 'slider',
                    'title' => '图片边框宽度',
                    'std' => '1',
                    'max' => '20',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_border_color_layout10' => array(
                    'type' => 'color',
                    'title' => '图片边框颜色',
                    'std' => '#ccc',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_border_selected_layout10' => array(
                    'type' => 'color',
                    'title' => '选中图片边框颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'img_style_layout10' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'scale-down',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'img'),
                    )
                ),
                'carousel_un_index_color_layout10' => array(
                    'type' => 'color',
                    'title' => '"/"和总页数字体颜色',
                    'std' => '#ccc',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_index_color_layout10' => array(
                    'type' => 'color',
                    'title' => '"/"和总页数字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_setting_layout10', '=', 'selected'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_un_now_color_layout10' => array(
                    'type' => 'color',
                    'title' => '序号颜色',
                    'std' => '#666',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_now_color_layout10' => array(
                    'type' => 'color',
                    'title' => '序号颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_setting_layout10', '=', 'selected'),
                        array('carousel_content_setting_layout10', '=', 'index'),
                    )
                ),
                'carousel_un_dot_color_layout10' => array(
                    'type' => 'color',
                    'title' => '圆点颜色',
                    'std' => '#ccc',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'dot'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                    )
                ),
                'carousel_dot_color_layout10' => array(
                    'type' => 'color',
                    'title' => '圆点颜色',
                    'std' => '#ffffff94',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'dot'),
                        array('carousel_setting_layout10', '=', 'selected'),
                    )
                ),
                'carousel_un_icon_layout10' => array(
                    'type' => 'media',
                    'title' => '图标',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout10_icon.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                    )
                ),
                'carousel_icon_layout10' => array(
                    'type' => 'media',
                    'title' => '图标',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout10_icon1.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                        array('carousel_setting_layout10', '=', 'selected'),
                    )
                ),
                'carousel_un_icon_color_layout10' => array(
                    'type' => 'color',
                    'title' => '图标背景颜色',
                    'std' => '#f8e8e9',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                    )
                ),
                'carousel_icon_color_layout10' => array(
                    'type' => 'color',
                    'title' => '图标背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'icon'),
                        array('carousel_setting_layout10', '=', 'selected'),
                    )
                ),
                'carousel_un_font_color_layout10' => array(
                    'type' => 'color',
                    'title' => '未选中标题字体颜色',
                    'std' => '#666',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'title'),
                        array('carousel_setting_layout10', '=', 'unselected'),
                    )
                ),
                'carousel_font_color_layout10' => array(
                    'type' => 'color',
                    'title' => '选中标题字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                        array('carousel_item_setting_layout10', '=', 'content'),
                        array('carousel_content_setting_layout10', '=', 'title'),
                        array('carousel_setting_layout10', '=', 'selected'),
                    )
                ),

                'carousel_height_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'轮播高度',
                    'std'=>array('md'=>700,'sm'=>600,'xs'=>500),
                    'max'=>2000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    ),
                    'responsive'=>true
                ),
                'carousel_content_settings_layout11'=>array(
                    'type'=>'buttons',
                    'tab'=>true,
                    'title'=>'内容设置',
                    'values'=>array(
                        array(
                            'label'=>'左侧',
                            'value'=>'left'
                        ),
                        array(
                            'label'=>'右侧',
                            'value'=>'right'
                        )
                    ),
                    'std'=>'left',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'carousel_left_width_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧宽度(%)',
                    'std'=>'34',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_bg_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'左侧背景颜色',
                    'std'=>'#b71b28',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_bg_img_layout11'=>array(
                    'type'=>'media',
                    'title'=>'左侧背景图片',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_bg1.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_border_radius_layout11'=>array(
                    'type'=>'margin',
                    'title'=>'左侧圆角',
                    'std'=>'4px 0px 0px 4px',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_padding_layout11'=>array(
                    'type'=>'padding',
                    'title'=>'左侧内边距',
                    'std'=>'85px 38px 85px 38px',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_content_settings_layout11'=>array(
                    'type'=>'buttons',
                    'tab'=>true,
                    'title'=>'左侧内容设置',
                    'values'=>array(
                        array(
                            'label'=>'序号',
                            'value'=>'index'
                        ),
                        array(
                            'label'=>'标题',
                            'value'=>'title'
                        ),
                        array(
                            'label'=>'图片',
                            'value'=>'img'
                        ),
                        array(
                            'label'=>'内容',
                            'value'=>'content'
                        )
                    ),
                    'std'=>'index',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                    )
                ),
                'carousel_left_index_stroke_width_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧序号描边宽度',
                    'std'=>2,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','index'),
                    )
                ),
                'carousel_left_index_stroke_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'左侧序号描边颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','index'),
                    )
                ),
                'carousel_left_index_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'左侧序号颜色',
                    'std'=>'#b71b28',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','index'),
                    )
                ),
                'carousel_left_index_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧序号字号',
                    'std'=>100,
                    'max'=>200,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','index'),
                    )
                ),
                'carousel_left_title_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧标题字号',
                    'std'=>28,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','title'),
                    )
                ),
                'carousel_left_title_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'左侧标题字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','title'),
                    )
                ),
                'carousel_left_title_top_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧标题上边距',
                    'std'=>24,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','title'),
                    )
                ),
                'carousel_left_title_en_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧英文标题字号',
                    'std'=>14,
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','title'),
                    )
                ),
                'carousel_left_title_en_top_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧英文标题上边距',
                    'std'=>7,
                    'max'=> 50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','title'),
                    )
                ),
                'carousel_left_img_top_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧封面图上边距',
                    'std'=>34,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','img'),
                    )
                ),
                'carousel_left_content_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧内容字号',
                    'std'=>14,
                    'max'=>40,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','content'),
                    )
                ),
                'carousel_left_content_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'左侧内容字体颜色',
                    'std'=>'rgba(255,255,255,0.5)',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','content'),
                    )
                ),
                'carousel_left_content_line_height_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧内容行高(px)',
                    'std'=>24,
                    'max'=>100,
                    'min'=> 12,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','content'),
                    )
                ),
                'carousel_left_content_top_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'左侧内容上边距',
                    'std'=>82,
                    'max'=>300,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','left'),
                        array('carousel_left_content_settings_layout11','=','content'),
                    )
                ),
                'carousel_right_padding_layout11'=>array(
                    'type'=>'padding',
                    'title'=>'右侧内容内边距',
                    'std'=>array('md'=>'55px 0px 55px 73px','sm'=>'55px 0px 55px 73px','xs'=>'40px 40px 40px 40px'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                    ),
                    'responsive'=>true
                ),
                'carousel_right_content_settings_layout11'=>array(
                    'type'=>'buttons',
                    'tab'=>true,
                    'title'=>'右侧内容设置',
                    'values'=>array(
                        array(
                            'label'=>'线条',
                            'value'=>'line'
                        ),
                        array(
                            'label'=>'标题',
                            'value'=>'title'
                        ),
                        array(
                            'label'=>'图片',
                            'value'=>'img'
                        ),
                        array(
                            'label'=>'内容',
                            'value'=>'content'
                        )
                    ),
                    'std'=>'line',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                    )
                ),
                'carousel_right_line_width_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容上方线条宽度',
                    'std'=>68,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','line'),
                    ),
                ),
                'carousel_right_line_height_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容上方线条高度',
                    'std'=>2,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','line'),
                    ),
                ),
                'carousel_right_line_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'右侧内容上方线条颜色',
                    'std'=>'#B71B28',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','line'),
                    ),
                ),
                'carousel_right_title_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧标题字号',
                    'std'=>20,
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','title'),
                    ),
                ),
                'carousel_right_title_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'右侧标题颜色',
                    'std'=>'#B8202C',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','title'),
                    ),
                ),
                'carousel_right_title_weight_layout11'=>array(
                    'type'=>'select',
                    'title'=>'右侧标题粗细',
                    'std'=>'600',
                    'values'=>array(
                        '300'=>'300',
                        '400'=>'400',
                        '500'=>'500',
                        '600'=>'600',
                        '700'=>'700',
                        '800'=>'800',
                        '900'=>'900',
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','title'),
                    ),
                ),
                'carousel_right_title_bottom_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧标题下边距',
                    'std'=>54,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','title'),
                    ),
                ),
                'carousel_right_title_top_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧标题上边距',
                    'std'=>20,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','title'),
                    ),
                ),
                'carousel_right_content_font_size_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容字号',
                    'std'=>16,
                    'max'=>40,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','content'),
                    ),
                ),
                'carousel_right_content_color_layout11'=>array(
                    'type'=>'color',
                    'title'=>'右侧内容颜色',
                    'std'=>'#979A9F',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','content'),
                    ),
                ),
                'carousel_right_content_line_height_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容行高',
                    'std'=>24,
                    'max'=>100,
                    'min'=>12,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','content'),
                    ),
                ),
                'carousel_right_content_p_bottom_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧内容段落间隔',
                    'std'=>20,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','content'),
                    ),
                ),
                'carousel_right_content_img_layout11'=>array(
                    'type'=>'slider',
                    'title'=>'右侧图片上边距',
                    'std'=>35,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                        array('carousel_content_settings_layout11','=','right'),
                        array('carousel_right_content_settings_layout11','=','img'),
                    ),
                ),


//                动画
                'animate_keep' => array(
                    'type' => 'number',
                    'title' => JText::_('动画持续时间(秒)'),
                    'desc' => JText::_('动画持续时间(秒)'),
                    'std' => 2,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2'),
                    ),
                ),
                'animate_keep_layout3' => array(
                    'type' => 'number',
                    'title' => JText::_('动画持续时间(秒)'),
                    'desc' => JText::_('动画持续时间(秒)'),
                    'std' => 0.8,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3'),
                    ),
                ),
                'animate_turn'=> array(
                    'type' => 'slider',
                    'title' => JText::_('翻转圈数'),
                    'desc' => JText::_('翻转圈数'),
                    'std' => 2,
                    'max'=>5,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    ),
                ),
                // 布局12 配置项
                'layout12_title' => array(
                    'type' => 'text',
                    'title' => '遮罩层标题',
                    'std' => '球友圈 一个全新的体育+互联网平台',
                    'depends' => array(
                        array('image_carousel_layout','=','layout12')
                    ),
                ),
                'layout12_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '遮罩层标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 60,
                        'sm' => 34,
                        'xs' => 18
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout12'),
                        array('layout12_title','!=',''),
                    ),
                ),
                'layout12_lbd' => array(
                    'type' => 'select',
                    'title' => '轮播点样式',
                    'values'=>array(
                        '1'=>'长方形',
                        '2'=>'圆形',
                    ),
                    'std' => '1',
                    'depends' => array(
                        array('image_carousel_layout','=','layout12')
                    ),
                ),
                'layout12_page_active' => array(
                    'type' => 'color',
                    'title' => '轮播点选中颜色',
                    'std' => '#e0be68',
                    'depends' => array(
                        array('image_carousel_layout','=','layout12')
                    ),
                ),
                'layout12_page_width' => array(
                    'type' => 'slider',
                    'title' => '轮播点宽度',
                    'max' => 300,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 60,
                        'sm' => 34,
                        'xs' => 18
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout12'),
                        array('layout12_title','!=',''),
                    ),
                ),


                //              样式14
                'is_position_layout14'=>array(
                    'title'=>'是否启用定位',
                    'type'=>'checkbox',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                    )
                ),
                'show_layout14_title'=>array(
                    'title'=>'是否展示名称',
                    'type'=>'checkbox',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                    )
                ),
                'title_en_layout14'=>array(
                    'title'=>'英文名称',
                    'type'=>'text',
                    'std'=>'THE CORE PRODUCT',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'title_cn_layout14'=>array(
                    'title'=>'中文名称',
                    'type'=>'text',
                    'std'=>'球友圈核心产品简介',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'equipment_settings_layout14'=>array(
                    'title'=>'设备配置项',
                    'type'=>'buttons',
                    'tabs'=>true,
                    'std'=>'pc',
                    'values'=>array(
                        array(
                            'label'=>'pc端',
                            'value'=>'pc'
                        ),
                        array(
                            'label'=>'手机端',
                            'value'=>'mobile'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                    )
                ),
                'title_en_font_size_layout14'=>array(
                    'title'=>'英文标题字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>1.8,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'title_cn_font_size_layout14'=>array(
                    'title'=>'中文标题字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>1.5,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'title_arrow_layout14'=>array(
                    'title'=>'标题箭头',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/title-arrow-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'font_color_layout14'=>array(
                    'title'=>'字体颜色',
                    'type'=>'color',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                    )
                ),
                'padding_bottom_layout14'=>array(
                    'title'=>'插件下内边距(vw)',
                    'type'=>'slider',
                    'std'=>'4',
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                    )
                ),
                'height_layout14'=>array(
                    'title'=>'插件高度(px)',
                    'type'=>'slider',
                    'std'=>570,
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                    )
                ),
                'padding_top_layout14'=>array(
                    'title'=>'插件上边距(vw)',
                    'type'=>'text',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                    )
                ),
                'pc_content_settings'=>array(
                    'title'=>'pc内容配置',
                    'type'=>'buttons',
                    'tabs'=>true,
                    'values'=>array(
                        array(
                            'label'=>'左侧',
                            'value'=>'left'
                        ),
                        array(
                            'label'=>'右侧',
                            'value'=>'right'
                        ),
                    ),
                    'std'=>'left',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                    )
                ),
                'content_width_layout14'=>array(
                    'title'=>'左侧文字部分宽度(%)',
                    'type'=>'slider',
                    'std'=>39,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'content_info_layout14'=>array(
                    'title'=>'左侧简介部分高度(vw)',
                    'type'=>'slider',
                    'std'=>11,
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'content_info_margin_top_layout14'=>array(
                    'title'=>'左侧简介部分上边距(vw)',
                    'type'=>'slider',
                    'std'=>2,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'content_info_title_size_layout14'=>array(
                    'title'=>'左侧简介部分标题字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.9,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'content_info_title_padding_bottom_layout14'=>array(
                    'title'=>'左侧简介标题部分下边距(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.8,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'content_info_intro_size_layout14'=>array(
                    'title'=>'左侧简介部分字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.8,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_margin_bottom_layout14'=>array(
                    'title'=>'左侧菜单部分下边距(vw)',
                    'type'=>'slider',
                    'std'=>3,
                    'max'=>10,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_icon_width_layout14'=>array(
                    'title'=>'左侧菜单部分图标大小(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>2.3,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_name_font_size_layout14'=>array(
                    'title'=>'左侧菜单部分字体大小(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.9,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_name_margin_layout14'=>array(
                    'title'=>'左侧菜单名称外边距(vw,可以是小数)',
                    'type'=>'margin',
                    'std'=>'0.73vw 0 0.6vw 0',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_bar_width_layout14'=>array(
                    'title'=>'左侧菜单进度条宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>3.4,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_bar_height_layout14'=>array(
                    'title'=>'左侧菜单进度条宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.2,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_bar_background_layout14'=>array(
                    'title'=>'左侧菜单进度条背景色',
                    'type'=>'color',
                    'std'=>'#404040',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'menu_bar_color_layout14'=>array(
                    'title'=>'左侧菜单进度条颜色',
                    'type'=>'color',
                    'std'=>'rgba(255,255,255,0.3)',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'left'),
                    )
                ),
                'img_border_color_layout14'=>array(
                    'title'=>'右侧图片边框色',
                    'type'=>'color',
                    'std'=>'#000',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_border_width_layout14'=>array(
                    'title'=>'右侧图片边框宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.36,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_border_radius_layout14'=>array(
                    'title'=>'右侧图片边框圆角(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>1.7,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_phone_right_layout14'=>array(
                    'title'=>'右侧手机图片右边距(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>2.5,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_phone_width_layout14'=>array(
                    'title'=>'右侧正放手机图片宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>18.4,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_phone_height_layout14'=>array(
                    'title'=>'右侧正放手机图片高度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>39,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_phone1_width_layout14'=>array(
                    'title'=>'右侧斜放手机图片宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>15.3,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'img_phone1_height_layout14'=>array(
                    'title'=>'右侧斜放手机图片高度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>31.93,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'pc'),
                        array('pc_content_settings', '=', 'right'),
                    )
                ),
                'title_en_font_size_mobile_layout14'=>array(
                    'title'=>'英文标题字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>4.5,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'title_cn_font_size_mobile_layout14'=>array(
                    'title'=>'中文标题字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>3.9,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'title_arrow_phone_size_layout14'=>array(
                    'title'=>'标题箭头',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/title-arrow-phone-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'font_color_phone_layout14'=>array(
                    'title'=>'字体颜色',
                    'type'=>'color',
                    'std'=>'#333',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('show_layout14_title', '=', 1),
                    )
                ),
                'phone_padding_layout14'=>array(
                    'title'=>'轮播项内边距(vw,可以是小数)',
                    'type'=>'padding',
                    'std'=>'0vw 4vw 0 4vw',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'phone_border_width_layout14'=>array(
                    'title'=>'图片边框宽度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>0.5,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'img_border_radius_phone_layout14'=>array(
                    'title'=>'图片边框圆角(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>1.7,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'img_border_color_phone_layout14'=>array(
                    'title'=>'图片边框色',
                    'type'=>'color',
                    'std'=>'#000',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'page_button_top_layout14'=>array(
                    'title'=>'pc图片翻页按钮上边距(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>7,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'page_button_size_layout14'=>array(
                    'title'=>'翻页按钮大小(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>11.22,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'page_button_settings_layout14'=>array(
                    'title'=>'翻页按钮设置',
                    'type'=>'buttons',
                    'tabs'=>true,
                    'values'=>array(
                        array(
                            'label'=>'左',
                            'value'=>'left'
                        ),
                        array(
                            'label'=>'右',
                            'value'=>'right'
                        ),
                    ),
                    'std'=>'left',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'page_button_left_layout14'=>array(
                    'title'=>'左翻页正常背景图',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/toleft-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('page_button_settings_layout14', '=', 'left'),
                    )
                ),
                'page_button_left_disabled_layout14'=>array(
                    'title'=>'左翻页禁用背景图',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/left-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('page_button_settings_layout14', '=', 'left'),
                    )
                ),
                'page_button_right_layout14'=>array(
                    'title'=>'右翻页正常背景图',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/tonext-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('page_button_settings_layout14', '=', 'right'),
                    )
                ),
                'page_button_right_disabled_layout14'=>array(
                    'title'=>'左翻页禁用背景图',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/next-layout14.png',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                        array('page_button_settings_layout14', '=', 'right'),
                    )
                ),
                'info_bg_phone_layout14'=>array(
                    'title'=>'内容介绍背景色',
                    'type'=>'color',
                    'std'=>'#F2F2F2',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'info_height_phone_layout14'=>array(
                    'title'=>'内容介绍高度(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>62.4,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'info_bottom_phone_layout14'=>array(
                    'title'=>'内容介绍文字下边距(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>9,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'info_font_size_phone_layout14'=>array(
                    'title'=>'内容介绍文字字号(vw,请输入数字,可以是小数)',
                    'type'=>'text',
                    'std'=>3.9,
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),
                'info_padding_phone_layout14'=>array(
                    'title'=>'内容介绍内边距(vw,可以是小数)',
                    'type'=>'padding',
                    'std'=>'0 2vw 0 2vw',
                    'depends'=>array(
                        array('image_carousel_layout', '=', 'layout14'),
                        array('equipment_settings_layout14', '=', 'mobile'),
                    )
                ),

                // 布局15
                'items_num_layout15'=>array(
                    'title' => '一屏展示几个',
                    'type' => 'slider',
                    'std' => 3,
                    'max' => 5,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'items_bottom_layout15'=>array(
                    'title' => '展示项间距',
                    'type' => 'slider',
                    'std' => 21,
                    'max' => 200,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'height_layout15'=>array(
                    'title' => '高度',
                    'type' => 'slider',
                    'std' => array('md'=>669,'sm' =>669,'xs'=> 669),
                    'max' => 1000,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                    'responsive' => true,
                ),
                'color_layout15' => array(
                    'title' => '字体颜色',
                    'type' => 'color',
                    'std'=>'#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'carousel_padding_layout15' => array(
                    'title' => '轮播项内边距',
                    'type'=>'padding',
                    'std'=>'26px 34px 26px 34px',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'item_active_border_color_layout15' => array(
                    'title' => '鼠标移入轮播项左竖线颜色',
                    'type'=>'color',
                    'std'=>'#2F76F2',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'title_border_bottom_color_layout15' => array(
                    'title' => '标题下方横线颜色',
                    'type'=>'color',
                    'std'=>'#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'title_padding_bottom_color_layout15' => array(
                    'title' => '标题下边距',
                    'type'=>'slider',
                    'std'=>17,
                    'max' => 200,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'title_font_size_layout15' => array(
                    'title' => '标题字号',
                    'type'=>'slider',
                    'std'=>18,
                    'max' => 50,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'icon_size_layout15' => array(
                    'title' => '图标宽度',
                    'type'=>'slider',
                    'std'=>25,
                    'max' => 300,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'icon_right_layout15' => array(
                    'title' => '图标右边距',
                    'type'=>'slider',
                    'std'=>25,
                    'max' => 300,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'intro_font_size_layout15' => array(
                    'title' => '简介字号',
                    'type'=>'slider',
                    'std'=>14,
                    'max' => 50,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'intro_padding_layout15' => array(
                    'title' => '简介内边距',
                    'type'=>'padding',
                    'std'=>'16px 0px 16px 0px',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'intro_max_height_layout15' => array(
                    'title' => '简介最大高度',
                    'type'=>'slider',
                    'std'=>82,
                    'max' => 600,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'intro_hide_row_layout15' => array(
                    'title' => '简介隐藏行数',
                    'type'=>'slider',
                    'std'=>3,
                    'max' => 10,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'next_button_url_layout15' => array(
                    'title' => '翻页按钮图片',
                    'type'=>'media',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/layout15-bottom-arrow.png',
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'next_button_top_layout15' => array(
                    'title' => '翻页按钮上边距',
                    'type'=>'slider',
                    'std'=>21,
                    'max' => 500,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                'next_button_width_layout15' => array(
                    'title' => '翻页按钮图片宽度',
                    'type'=>'slider',
                    'std'=>51,
                    'max' => 500,
                    'depends' => array(
                        array('image_carousel_layout','=','layout15')
                    ),
                ),
                // 布局22 轮播项
                'jw_image_carousel_item_type22' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220711/630e0e10542015377814fff5fbb00e68.jpg',
                            'title_sm' => '红肠',
                            'desc_sm' => '马迭尔红肠采用正宗俄式工艺',
                            'content_sm' => '马迭尔红肠采用正宗俄式工艺 &nbsp; 精选猪瘦肉为主原料<br>经腌制、制馅、灌制和烤、煮、熏等欧式传统工艺制作<br>百年的历史 &nbsp; 孕育食品厚重的历史文化特色',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220708/d9d79d10b4324742720b8b2140786e1b.jpg',
                            'title_sm' => '干肠',
                            'desc_sm' => '',
                            'content_sm' => '马迭尔红肠采用正宗俄式工艺&nbsp;&nbsp;&nbsp;精选猪瘦肉为主原料<br/>经腌制、制馅、灌制和烤、煮、熏等欧式传统工艺制作<br/>百年的历史&nbsp;&nbsp;&nbsp;孕育食品厚重的历史文化特色'
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220711/630e0e10542015377814fff5fbb00e68.jpg',
                            'title_sm' => '红肠',
                            'desc_sm' => '马迭尔红肠采用正宗俄式工艺',
                            'content_sm' => '马迭尔红肠采用正宗俄式工艺 &nbsp; 精选猪瘦肉为主原料<br>经腌制、制馅、灌制和烤、煮、熏等欧式传统工艺制作<br>百年的历史 &nbsp; 孕育食品厚重的历史文化特色',
                        ),
                    ),
                    'attr' => array(
                        // 添加视频
                        'media_video' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否启用视频'),
                            'desc' => JText::_('开启即可上传banner视频,不会再显示banner图片'),
                            'values' => array(
                                1 => JText::_('JYES'),
                                0 => JText::_('JNO'),
                            ),
                            'std' => 0,

                        ),
                        'slider_video' => array(
                            'type' => 'media',
                            'format' => 'video',
                            'title' => JText::_('上传banner视频'),
                            'desc' => JText::_('上传banner视频'),
                            'depends' => array(
                                array('media_video', '=', 1),
                            ),
                        ),

                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220711/630e0e10542015377814fff5fbb00e68.jpg',
                            'depends' => array(
                                array('media_video', '!=', 1),
                            ),
                        ),
                        'title_sm' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '红肠',
                        ),
                        'desc_sm' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '马迭尔红肠采用正宗俄式工艺',
                        ),
                        'content_sm' => array(
                            'type' => 'text',
                            'title' => '简介',
                            'std' => '马迭尔红肠采用正宗俄式工艺 &nbsp; 精选猪瘦肉为主原料<br>经腌制、制馅、灌制和烤、煮、熏等欧式传统工艺制作<br>百年的历史 &nbsp; 孕育食品厚重的历史文化特色',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22')
                    ),
                ),
                'carousel_arrow_setting_22'=>array(
                    'type'=>'buttons',
                    'title'=>'翻页设置',
                    'std'=>'normal',
                    'values'=>array(
                        array(
                            'label'=>'正常',
                            'value'=>'normal'
                        ),
                        array(
                            'label'=>'移入',
                            'value'=>'hover'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22'),
                    ),
                ),
                'carousel_arrow_prev_22'=>array(
                    'type'=>'media',
                    'title'=>'上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/joomla/20220708/f18062292754d3cf6c662d8c38a8a389.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22'),
                        array('carousel_arrow_setting_22','=','normal'),
                    ),
                ),
                'carousel_arrow_next_22'=>array(
                    'type'=>'media',
                    'title'=>'下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/joomla/20220708/8975b6b2a024f7a0bf11b7182adcc3bf.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22'),
                        array('carousel_arrow_setting_22','=','normal'),
                    ),
                ),
                'carousel_arrow_prev_22_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/joomla/20220708/6481dfc2f7a5c3c20144e910bc1a05ef.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22'),
                        array('carousel_arrow_setting_22','=','hover'),
                    ),
                ),
                'carousel_arrow_next_22_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/joomla/20220708/b4e0a3d2f72e8223c622d8b223121212.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout22'),
                        array('carousel_arrow_setting_22','=','hover'),
                    ),
                ),
                // 样式配置
                'carousel_style_settings_25' => array(
                    'title' => '样式配置',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'box',
                    'values' => array(
                        array(
                            'label' => '容器',
                            'value' => 'box'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '翻页控制器',
                            'value' => 'page'
                        ),
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                    ),
                ),
                // 布局25高度
                'carousel_height_25' => array(
                    'type' => 'slider',
                    'title' => '高度',
                    'std' => array('md' => 600, 'sm' => '600', 'xs' => 600),
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','box'),
                    ),
                ),
                // 内边距
                'carousel_padding_25' => array(
                    'type' => 'padding',
                    'title' => '内边距',
                    'std' => array('md' => '60px 100px 60px 100px', 'sm' => '60px 100px 60px 100px', 'xs' => '40px 20px 40px 20px'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','box'),
                    ),
                ),
                // 内容样式配置选项卡
                'carousel_content_settings_25' => array(
                    'title' => '内容样式配置选项卡',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'title',
                    'values' => array(
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '英文标题',
                            'value' => 'title_en'
                        ),
                        array(
                            'label' => '简介',
                            'value' => 'intro'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '按钮',
                            'value' => 'button'
                        ),
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                    ),
                ),
                // 内容水平对齐
                'carousel_content_justify_25' => array(
                    'type' => 'select',
                    'title' => '内容水平对齐',
                    'std' => 'flex-start',
                    'values' => array(
                        'center' => '居中',
                        'flex-start' => '左对齐',
                        'flex-end' => '右对齐',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                    ),
                ),
                // 内容垂直对齐
                'carousel_content_align_25' => array(
                    'type' => 'select',
                    'title' => '内容垂直对齐',
                    'std' => 'flex-start',
                    'values' => array(
                        'center' => '居中',
                        'flex-start' => '上对齐',
                        'flex-end' => '下对齐',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                    ),
                ),
                // 标题外边距
                'carousel_title_margin_25' => array(
                    'type' => 'padding',
                    'title' => '标题外边距',
                    'std' => array('md' => '0 0 20px 0', 'sm' => '0 0 20px 0', 'xs' => '0 0 20px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 标题颜色
                'carousel_title_color_25' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 标题字号
                'carousel_title_font_size_25' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std' => array('md' => 24, 'sm' => 24, 'xs' => 24),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 标题字体样式
                'carousel_title_font_style_25' => array(
                    'type' => 'fontstyle',
                    'title' => '标题字体样式',
                    'std' => array(
                        'weight' => 'bold'
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 标题字体
                'carousel_title_font_family_25' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
                    )
                ),
                // 标题显示行数
                'carousel_title_line_clamp_25' => array(
                    'type' => 'slider',
                    'title' => '标题显示行数',
                    'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 标题行高
                'carousel_title_line_height_25' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title'),
                    ),
                ),
                // 是否显示英文标题
                'carousel_title_en_show_25' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示英文标题',
                    'std' => 1,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                    ),
                ),
                // 英文标题显示位置
                'carousel_title_en_position_25' => array(
                    'type' => 'select',
                    'title' => '英文标题显示位置',
                    'std' => 'top',
                    'values' => array(
                        'top' => '中文标题上方',
                        'bottom' => '中文标题下方',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文标题外边距
                'carousel_title_en_margin_25' => array(
                    'type' => 'padding',
                    'title' => '英文标题外边距',
                    'std' => array('md' => '0 0 20px 0', 'sm' => '0 0 20px 0', 'xs' => '0 0 20px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文标题颜色
                'carousel_title_en_color_25' => array(
                    'type' => 'color',
                    'title' => '英文标题颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文标题字号
                'carousel_title_en_font_size_25' => array(
                    'type' => 'slider',
                    'title' => '英文标题字号',
                    'std' => array('md' => 24, 'sm' => 24, 'xs' => 24),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文字体样式
                'carousel_title_en_font_style_25' => array(
                    'type' => 'fontstyle',
                    'title' => '英文标题字体样式',
                    'std' => array(
                        'weight' => 'bold',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文标题字体
                'carousel_title_en_font_family_25' => array(
                    'type' => 'font',
                    'title' => '英文标题字体',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文标题显示行数
                'carousel_title_en_line_clamp_25' => array(
                    'type' => 'slider',
                    'title' => '英文标题显示行数',
                    'std' => array('md' => 1, 'ms' => 1, 'xs' => 1),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 英文字体行高
                'carousel_title_en_line_height_25' => array(
                    'type' => 'slider',
                    'title' => '英文标题行高',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','title_en'),
                        array('carousel_title_en_show_25','=','1'),
                    ),
                ),
                // 简介显示行数
                'carousel_intro_line_clamp_25' => array(
                    'type' => 'slider',
                    'title' => '内容显示行数',
                    'std' => array('md' => 2, 'ms' => 2, 'xs' => 2),
                    'max' => 10,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 简介外边距
                'carousel_intro_margin_25' => array(
                    'type' => 'padding',
                    'title' => '简介外边距',
                    'std' => array('md' => '0 0 20px 0', 'sm' => '0 0 20px 0', 'xs' => '0 0 20px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 简介文字颜色
                'carousel_intro_color_25' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 简介字号
                'carousel_intro_font_size_25' => array(
                    'type' => 'slider',
                    'title' => '简介字号',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 简介文字字体
                'carousel_intro_font_family_25' => array(
                    'type' => 'font',
                    'title' => '简介文字字体',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                        array('carousel_title_show_25','=','1'),
                    ),
                ),
                // 简介文字字体样式
                'carousel_intro_fontstyle_25' => array(
                    'type' => 'fontstyle',
                    'title' => '简介文字字体样式',
                    'std' => array(
                        'weight' => 'normal',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 简介行高
                'carousel_intro_line_height_25' => array(
                    'type' => 'slider',
                    'title' => '简介行高',
                    'std' => array('md' => 21, 'sm' => 21, 'xs' => 21),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','intro'),
                    ),
                ),
                // 图片展示方式
                'carousel_img_show_25' => array(
                    'type' => 'select',
                    'title' => '图片展示方式',
                    'std' => 'cover',
                    'values' => array(
                        'cover' => '占满裁剪',
                        'contain' => '保持比例留白',
                        'fill' => '占满拉伸',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','img'),
                    ),
                ),
                // 按钮设置选项卡
                'carousel_btn_settings_25' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'title' => '按钮设置',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '悬停',
                            'value' => 'hover'
                        ),
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                    ),
                ),
                // 是否使用图片
                'carousel_btn_img_show_25' => array(
                    'type' => 'checkbox',
                    'title' => '是否使用图片',
                    'std' => 0,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                    ),
                ),
                // 按钮图片
                'carousel_btn_img_25' => array(
                    'type' => 'media',
                    'title' => '按钮图片',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','1'),
                    ),
                ),
                // 悬停按钮图片
                'carousel_btn_img_hover_25' => array(
                    'type' => 'media',
                    'title' => '悬停按钮图片',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','hover'),
                        array('carousel_btn_img_show_25','=','1'),
                    ),
                ),
                // 按钮文字
                'carousel_btn_text_25' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'std' => 'Read more',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮背景色
                'carousel_btn_bg_color_25' => array(
                    'type' => 'color',
                    'title' => '按钮背景色',
                    'std' => '#2e6dde',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮文字颜色
                'carousel_btn_text_color_25' => array(
                    'type' => 'color',
                    'title' => '按钮文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮字号
                'carousel_btn_font_size_25' => array(
                    'type' => 'slider',
                    'title' => '按钮字号',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮字体样式
                'carousel_btn_fontstyle_25' => array(
                    'type' => 'fontstyle',
                    'title' => '按钮字体样式',
                    'std' => array(
                        'weight' => 'normal',
                    ),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮字体
                'carousel_btn_font_family_25' => array(
                    'type' => 'font',
                    'title' => '按钮字体',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮内边距
                'carousel_btn_padding_25' => array(
                    'type' => 'padding',
                    'title' => '按钮内边距',
                    'std' => array('md' => '10px 20px 10px 20px', 'sm' => '10px 20px 10px 20px', 'xs' => '10px 20px 10px 20px'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮外边距
                'carousel_btn_margin_25' => array(
                    'type' => 'padding',
                    'title' => '按钮外边距',
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮边框颜色
                'carousel_btn_border_color_25' => array(
                    'type' => 'color',
                    'title' => '按钮边框颜色',
                    'std' => '#2e6dde',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮圆角
                'carousel_btn_border_radius_25' => array(
                    'type' => 'slider',
                    'title' => '按钮圆角',
                    'std' => 50,
                    'max' => 100,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮图标
                'carousel_btn_icon_25' => array(
                    'type' => 'icon',
                    'title' => '按钮图标',
                    'std' => 'fa-arrow-right',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮右边距
                'carousel_btn_margin_right_25' => array(
                    'type' => 'slider',
                    'title' => '按钮右边距',
                    'std' => 6,
                    'max' => 100,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','normal'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮悬停背景色
                'carousel_btn_bg_color_hover_25' => array(
                    'type' => 'color',
                    'title' => '按钮悬停背景色',
                    'std' => '#f0f0f0',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','hover'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 按钮悬停文字颜色
                'carousel_btn_text_color_hover_25' => array(
                    'type' => 'color',
                    'title' => '按钮悬停文字颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','content'),
                        array('carousel_content_settings_25','=','button'),
                        array('carousel_btn_settings_25','=','hover'),
                        array('carousel_btn_img_show_25','=','0'),
                    ),
                ),
                // 翻页控制器配置项
                'carousel_page_settings_25' => array(
                    'type' => 'buttons',
                    'title' => '翻页控制器',
                    'values' => array(
                        array(
                            'label' => '左右箭头',
                            'value' => 'arrow'
                        ),
                        array(
                            'label' => '翻页器',
                            'value' => 'page'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                    ),
                ),
                // 控制器样式配置项
                'carousel_page_style_settings_25' => array(
                    'type' => 'buttons',
                    'title' => '样式配置',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '悬停',
                            'value' => 'hover'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                    ),
                ),
                // 是否使用图片
                'carousel_page_img_show_25' => array(
                    'type' => 'checkbox',
                    'title' => '是否使用图片',
                    'std' => 0,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头图片
                'carousel_page_img_25' => array(
                    'type' => 'media',
                    'title' => '箭头图片',
                    'desc' => '请上传图片（右箭头自动翻转）',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','1'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 悬停箭头图片
                'carousel_page_img_hover_25' => array(
                    'type' => 'media',
                    'title' => '悬停箭头图片',
                    'desc' => '请上传图片（右箭头自动翻转）',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','1'),
                        array('carousel_page_style_settings_25','=','hover'),
                    ),
                ),
                // 箭头图标左
                'carousel_page_icon_left_25' => array(
                    'type' => 'icon',
                    'title' => '箭头图标左',
                    'std' => 'fa-chevron-left',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头图标右
                'carousel_page_icon_right_25' => array(
                    'type' => 'icon',
                    'title' => '箭头图标右',
                    'std' => 'fa-chevron-right',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头颜色
                'carousel_page_icon_color_25' => array(
                    'type' => 'color',
                    'title' => '箭头颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头字号
                'carousel_page_icon_size_25' => array(
                    'type' => 'slider',
                    'title' => '箭头字号',
                    'responsive' => true,
                    'std' => array('md' => 14, 'md' => 14, 'xs' => 14),
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头按钮背景色
                'carousel_page_btn_bg_color_25' => array(
                    'type' => 'color',
                    'title' => '箭头按钮背景色',
                    'std' => 'transparent',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头按钮边框色
                'carousel_page_btn_border_color_25' => array(
                    'type' => 'color',
                    'title' => '箭头按钮边框色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 箭头按钮圆角
                'carousel_page_btn_border_radius_25' => array(
                    'type' => 'slider',
                    'title' => '箭头按钮圆角',
                    'std' => 20,
                    'max' => 400,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 悬停箭头颜色
                'carousel_page_btn_border_color_hover_25' => array(
                    'type' => 'color',
                    'title' => '悬停箭头颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','hover'),
                    ),
                ),
                // 悬停背景色
                'carousel_page_btn_bg_color_hover_25' => array(
                    'type' => 'color',
                    'title' => '悬停背景色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','hover'),
                    ),
                ),
                // 按钮宽度
                'carousel_page_btn_width_25' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'std' => array('md' => 40, 'sm' => 40, 'xs' => 40),
                    'responsive' => true,
                    'max' => 400,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 按钮高度
                'carousel_page_btn_height_25' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'std' => array('md' => 40, 'sm' => 40, 'xs' => 40),
                    'responsive' => true,
                    'max' => 400,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 按钮距离容器的距离
                'carousel_page_btn_margin_right_25' => array(
                    'type' => 'slider',
                    'title' => '按钮距离容器的距离',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 0),
                    'min' => -200,
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_page_settings_25','=','arrow'),
                        array('carousel_page_img_show_25','=','0'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器右边距
                'carousel_pagination_margin_right_25' => array(
                    'type' => 'slider',
                    'title' => '分页器右边距',
                    'std' => array('md' => 120, 'sm' => 120, 'xs' => 50),
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器宽度
                'carousel_pagination_width_25' => array(
                    'type' => 'slider',
                    'title' => '分页器宽度',
                    'std' => array('md' => 200, 'sm' => 200, 'xs' => 200),
                    'max' => 400,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器左侧线条颜色
                'carousel_pagination_line_color_25' => array(
                    'type' => 'color',
                    'title' => '分页器左侧线条颜色',
                    'std' => 'rgba(255, 255, 255, 0.2)',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器每项内边距
                'carousel_pagination_padding_25' => array(
                    'type' => 'slider',
                    'title' => '分页器每项内边距',
                    'std' => array('md' => '20px 10px 20px 10px', 'sm' => '20px 10px 20px 10px', 'xs' => '20px 10px 20px 10px'),
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器文字颜色
                'carousel_pagination_text_color_25' => array(
                    'type' => 'color',
                    'title' => '分页器文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器文字字体大小
                'carousel_pagination_text_font_size_25' => array(
                    'type' => 'slider',
                    'title' => '分页器文字字体大小',
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 14),
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器悬停字体颜色
                'carousel_pagination_text_hover_color_25' => array(
                    'type' => 'color',
                    'title' => '分页器悬停字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','hover'),
                    ),
                ),
                // 分页器文字前小方块圆角
                'carousel_pagination_dot_border_radius_25' => array(
                    'type' => 'slider',
                    'title' => '分页器文字前小方块圆角',
                    'std' => 5,
                    'max' => 20,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器文字前小方块宽度
                'carousel_pagination_dot_width_25' => array(
                    'type' => 'slider',
                    'title' => '分页器文字前小方块宽度',
                    'std' => 3,
                    'max' => 20,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 分页器文字前小方块高度
                'carousel_pagination_dot_height_25' => array(
                    'type' => 'slider',
                    'title' => '分页器文字前小方块高度',
                    'std' => 3,
                    'max' => 20,
                    'depends' => array(
                        array('image_carousel_layout','=','layout25'),
                        array('carousel_settings_25','=','styles'),
                        array('carousel_style_settings_25','=','page'),
                        array('carousel_page_settings_25','=','page'),
                        array('carousel_page_style_settings_25','=','normal'),
                    ),
                ),
                // 轮播效果配置
                'carousel_play' => array(
                    'title' => '轮播效果',
                    'type' => 'separator',
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout15'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout23'),

                    ),
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'std' => 1,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout15'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout23'),

                    ),
                ),

                'carousel_autoplay_layout8' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'std' => 1,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                    ),
                ),
                'carousel_loop' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否循环'),
                    'std' => 1,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout1'),
                        array('image_carousel_layout','!=','layout2'),
                        array('image_carousel_layout','!=','layout3'),
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout12'),
                        array('image_carousel_layout','!=','layout13'),
                        array('image_carousel_layout','!=','layout14'),
                        array('image_carousel_layout','!=','layout15'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout20'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout24'),

                    ),
                ),

                'carousel_loop_layout8' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否循环'),
                    'std' => 0,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                    ),
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'std' => 2500,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout16'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout21'),
                    ),
                ),
                'carousel_speed_type21' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'std' => 9000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout21'),
                    ),
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'std' => 3000,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout16'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout21'),
                    ),
                ),
                'single_style' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否单图轮播'),
                    'desc' => JText::_('是否单图轮播'),
                    'std' => 0,
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout4'),
                        array('image_carousel_layout','!=','layout5'),
                        array('image_carousel_layout','!=','layout6'),
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout8'),
                        array('image_carousel_layout','!=','layout9'),
                        array('image_carousel_layout','!=','layout10'),
                        array('image_carousel_layout','!=','layout11'),
                        array('image_carousel_layout','!=','layout12'),
                        array('image_carousel_layout','!=','layout13'),
                        array('image_carousel_layout','!=','layout14'),
                        array('image_carousel_layout','!=','layout16'),
                        array('image_carousel_layout','!=','layout17'),
                        array('image_carousel_layout','!=','layout18'),
                        array('image_carousel_layout','!=','layout19'),
                        array('image_carousel_layout','!=','layout21'),
                        array('image_carousel_layout','!=','layout22'),
                        array('image_carousel_layout','!=','layout23'),
                        array('image_carousel_layout','!=','layout24'),

                    ),
                ),
                'carousel_image_scale'=>array(
                    'type'=>'number',
                    'title'=>'图片放大倍数',
                    'std'=>'1.1',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    ),
                ),

                // 布局23 轮播项
                'jw_image_carousel_item_type23' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/d8812dad8b6d1d8972378a99f6440464.jpeg',
                            'title_sm' => '马迭尔档口店',
                            'desc_sm' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                            'content_sm' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/bbf98e08c7d8f7c7624d768a95bc8b33.jpeg',
                            'title_sm' => '马迭尔档口店',
                            'desc_sm' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                            'content_sm' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/5c7e822e1e74a0662b43ec521db8b640.jpeg',
                            'title_sm' => '马迭尔档口店',
                            'desc_sm' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                            'content_sm' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/80083a7f80204503d33e28716a92c785.jpeg',
                            'title_sm' => '马迭尔档口店',
                            'desc_sm' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                            'content_sm' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/a1509ff5d719ccccfdb01f892b3c611d.jpeg',
                            'title_sm' => '马迭尔档口店',
                            'desc_sm' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                            'content_sm' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/d8812dad8b6d1d8972378a99f6440464.jpeg',
                        ),
                        'title_sm' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '马迭尔档口店',
                        ),
                        'desc_sm' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '以销售马迭尔预包装系列产品为主的线下零售门店',
                        ),
                        'content_sm' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'std' => '平均经营面积在20平米,环境主体为复古、浪漫、时尚、爱情<br>强化VI标准色的视觉印象与视觉记忆<br>马迭尔档口店是预包装产品销售的重要渠道和提升马迭尔产品形象的重要阵地',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),

                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23')
                    ),
                ),
                'carousel_arrow_setting_23'=>array(
                    'type'=>'buttons',
                    'title'=>'翻页设置',
                    'std'=>'normal',
                    'values'=>array(
                        array(
                            'label'=>'正常',
                            'value'=>'normal'
                        ),
                        array(
                            'label'=>'移入',
                            'value'=>'hover'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23'),
                    ),
                ),
                'carousel_arrow_prev_23'=>array(
                    'type'=>'media',
                    'title'=>'上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/d05a40ae1ac527e66c06e63f5a41550d.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23'),
                        array('carousel_arrow_setting_23','=','normal'),
                    ),
                ),
                'carousel_arrow_next_23'=>array(
                    'type'=>'media',
                    'title'=>'下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f9e89c9f7f1a82beac36b9e92504b24a.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23'),
                        array('carousel_arrow_setting_23','=','normal'),
                    ),
                ),
                'carousel_arrow_prev_23_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f9474763148d5645f0a19a3f4c30c782.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23'),
                        array('carousel_arrow_setting_23','=','hover'),
                    ),
                ),
                'carousel_arrow_next_23_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f6bb93bfb9a48c8e83277fae7b155fe2.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout23'),
                        array('carousel_arrow_setting_23','=','hover'),
                    ),
                ),

                // 布局24
                'jw_image_carousel_item_type24' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查1',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查1',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查2',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查2',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查3',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查3',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查4',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查4',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查5',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查5',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        array(
                            'small_img' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                            'title' => '地球物理与地球化学勘查6',
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                            'title_sm' => '地球物理与地球化学勘查6',
                            'content_sm' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                    ),
                    'attr' => array(
                        'small_img' => array(
                            'type' => 'media',
                            'title' => '小图标',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220827/68aee01ca45f0aeb210ac5fd2ea84750.png',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => '小标题',
                            'std' => '地球物理与地球化学勘查',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '图片',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220826/fcebe90c903db1d3f36a028945372ca8.png',
                        ),
                        'title_sm' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '地球物理与地球化学勘查',
                        ),

                        'content_sm' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'std' => '大地测量、工程测量、轨道交通测量、地下管线探测、规划测量、不动产测量、摄影测量与遥感、航空摄影测量及地理信息系统',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),

                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24')
                    ),
                ),
                'titbox_width'=>array(
                    'type'=>'slider',
                    'title'=>'标题区域宽度',
                    'std'=>'1200',
                    'max'=>'2000',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'titbox_geshu'=>array(
                    'type'=>'slider',
                    'title'=>'标题模块展示数量',
                    'std'=>'7',
                    'max'=>'30',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'xtb_width'=>array(
                    'type'=>'slider',
                    'title'=>'小图标宽高',
                    'std'=>'70',
                    'max'=>'300',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'titbox_fontsize'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体大小',
                    'std'=>'14',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),

                'titbox_color'=>array(
                    'type'=>'color',
                    'title'=>'标题字体颜色',
                    'std'=>'#333',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'titbox_actcolor'=>array(
                    'type'=>'color',
                    'title'=>'标题选中字体颜色',
                    'std'=>'#2447A5',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'lrimg_top'=>array(
                    'type'=>'slider',
                    'title'=>'左右轮播上间距',
                    'std'=>'50',
                    'max'=>'200',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'lrimg_center'=>array(
                    'type'=>'slider',
                    'title'=>'轮播间距',
                    'std'=>'50',
                    'max'=>'200',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'contbox_height'=>array(
                    'type'=>'slider',
                    'title'=>'内容区域高度',
                    'std'=>'390',
                    'max'=>'1000',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'contbox_back'=>array(
                    'type'=>'color',
                    'title'=>'选中内容区域背景色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'contbox_padding'=>array(
                    'type'=>'slider',
                    'title'=>'选中区域内间距',
                    'std'=>'20',
                    'max'=>'200',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'img_style_layout24' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout24'),
                    )
                ),
                'cbox_fontsize'=>array(
                    'type'=>'slider',
                    'title'=>'内容标题字体大小',
                    'std'=>'18',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'titbox_lineheight'=>array(
                    'type'=>'slider',
                    'title'=>'内容标题行高',
                    'std'=>'50',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'cbox_color'=>array(
                    'type'=>'color',
                    'title'=>'内容标题字体颜色',
                    'std'=>'#333',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'xt_color'=>array(
                    'type'=>'color',
                    'title'=>'内容标题下线条颜色',
                    'std'=>'#2447A5',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'xt_width'=>array(
                    'type'=>'slider',
                    'title'=>'线条宽度',
                    'std'=>'20',
                    'max'=>'200',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'xt_height'=>array(
                    'type'=>'slider',
                    'title'=>'线条高度',
                    'std'=>'2',
                    'max'=>'20',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'cboxjj_fontsize'=>array(
                    'type'=>'slider',
                    'title'=>'简介字体大小',
                    'std'=>'14',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'cboxjj_lineheight'=>array(
                    'type'=>'slider',
                    'title'=>'简介行高',
                    'std'=>'30',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'cboxjj_color'=>array(
                    'type'=>'color',
                    'title'=>'简介字体颜色',
                    'std'=>'#333',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'button24_color'=>array(
                    'type'=>'color',
                    'title'=>'按钮颜色',
                    'std'=>'#DC2422',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'carousel_arrow_setting_24'=>array(
                    'type'=>'buttons',
                    'title'=>'翻页设置',
                    'std'=>'normal',
                    'values'=>array(
                        array(
                            'label'=>'正常',
                            'value'=>'normal'
                        ),
                        array(
                            'label'=>'移入',
                            'value'=>'hover'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                    ),
                ),
                'carousel_arrow_width24'=>array(
                    'type'=>'slider',
                    'title'=>'翻页箭头宽',
                    'std'=>'61',
                    'max'=>'200',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_height24'=>array(
                    'type'=>'slider',
                    'title'=>'翻页箭头高',
                    'std'=>'108',
                    'max'=>'300',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_top'=>array(
                    'type'=>'slider',
                    'title'=>'箭头顶部距离(%)',
                    'std'=>'45',
                    'max'=>'100',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_relet'=>array(
                    'type'=>'slider',
                    'title'=>'翻页箭头左右距离(%)',
                    'std'=>'15',
                    'max'=>'100',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_prev_24'=>array(
                    'type'=>'media',
                    'title'=>'上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/d05a40ae1ac527e66c06e63f5a41550d.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_next_24'=>array(
                    'type'=>'media',
                    'title'=>'下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f9e89c9f7f1a82beac36b9e92504b24a.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','normal'),
                    ),
                ),
                'carousel_arrow_prev_24_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入上翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f9474763148d5645f0a19a3f4c30c782.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','hover'),
                    ),
                ),
                'carousel_arrow_next_24_hover'=>array(
                    'type'=>'media',
                    'title'=>'移入下翻页箭头',
                    'std'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220716/f6bb93bfb9a48c8e83277fae7b155fe2.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout24'),
                        array('carousel_arrow_setting_24','=','hover'),
                    ),
                ),

//                样式8
                'carousel_arrow'=>array(
                    'type'=>'separator',
                    'title'=>'翻页箭头',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_arrow_setting'=>array(
                    'type'=>'buttons',
                    'title'=>'翻页设置',
                    'std'=>'normal',
                    'values'=>array(
                        array(
                            'label'=>'正常',
                            'value'=>'normal'
                        ),
                        array(
                            'label'=>'移入',
                            'value'=>'hover'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_arrow_img'=>array(
                    'type'=>'media',
                    'title'=>'翻页箭头',
                    'desc'=>'请把正常和移入样式的图片放在一张图上,正常样式在上,移入样式在下',
                    'std'=>'/components/com_jwpagefactory/addons/image_swiper/assets/images/arrows.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                        array('carousel_arrow_setting','=','normal'),
                    ),
                ),
                'carousel_arrow_bg'=>array(
                    'type'=>'color',
                    'title'=>'翻页箭头背景',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                        array('carousel_arrow_setting','=','normal'),
                    ),
                ),
                'carousel_arrow_bg_hover'=>array(
                    'type'=>'color',
                    'title'=>'鼠标移入翻页箭头背景',
                    'std'=>'#c2221f',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                        array('carousel_arrow_setting','=','hover'),
                    ),
                ),
                'carousel_arrow_width'=>array(
                    'type'=>'slider',
                    'title'=>'翻页箭头宽度',
                    'std'=>'34',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                        array('carousel_arrow_setting','=','normal'),
                    ),
                ),
                'carousel_arrow_height'=>array(
                    'type'=>'slider',
                    'title'=>'翻页箭头高度',
                    'std'=>'34',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                        array('carousel_arrow_setting','=','normal'),
                    ),
                ),
                'paging'=>array(
                    'type'=>'separator',
                    'title'=>'翻页',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_paging_margin'=>array(
                    'type'=>'margin',
                    'title'=>'翻页外边距',
                    'std'=>'0px 30px 0px 30px',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_paging_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'翻页字号',
                    'std'=>'20',
                    'max'=>'50',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_paging_color'=>array(
                    'type'=>'color',
                    'title'=>'翻页字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_content_width'=>array(
                    'type'=>'slider',
                    'title'=>'内容部分宽度',
                    'desc'=>'包括副标题和简介',
                    'std'=>80,
                    'max'=>100,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                    ),
                ),
                'carousel_content_bottom'=>array(
                    'type'=>'slider',
                    'title'=>'内容部分底边距',
                    'desc'=>'包括副标题和简介',
                    'std'=>44,
                    'max'=>500,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                    ),
                ),
                'carousel_content_setting'=>array(
                    'type'=>'buttons',
                    'title'=>'内容设置',
                    'desc'=>'如果使用图片代替标题,标题和按钮设置可跳过',
                    'std'=>'title',
                    'values'=>array(
                        array(
                            'label'=>'标题',
                            'value'=>'title'
                        ),
                        array(
                            'label'=>'按钮',
                            'value'=>'button'
                        ),
                        array(
                            'label'=>'副标题',
                            'value'=>'subtitle'
                        ),
                        array(
                            'label'=>'简介',
                            'value'=>'intro'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                    ),
                ),

                'carousel_title'=>array(
                    'type'=>'text',
                    'title'=>'标题名称',
                    'std'=>'厚德善建 实干兴邦',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','title'),
                        array('need_data','=',1),
                    ),
                ),
                'carousel_title_top'=>array(
                    'type'=>'slider',
                    'title'=>'标题上边距(%)',
                    'std'=>'20',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_setting'=>array(
                    'type'=>'buttons',
                    'title'=>'标题设置',
                    'std'=>'img',
                    'values'=>array(
                        array(
                            'label'=>'图片',
                            'value'=>'img'
                        ),
                        array(
                            'label'=>'字体',
                            'value'=>'font'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_img_width'=>array(
                    'type'=>'slider',
                    'title'=>'图片宽度(%)',
                    'std'=>array('md'=>'','sm'=>'49','xs'=>'49'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','img'),
                        array('carousel_content_setting','=','title'),
                    ),
                    'responsive'=>true
                ),
                'carousel_title_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体大小',
                    'std'=>array('md'=>'50','sm'=>'40','xs'=>'28'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                    'responsive'=>true
                ),
                'carousel_title_color'=>array(
                    'type'=>'color',
                    'title'=>'标题字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_letter_spacing'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体间距',
                    'std'=>3,
                    'max'=>6,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_shadow_x'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体阴影横向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_shadow_y'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体阴影纵向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_shadow_range'=>array(
                    'type'=>'slider',
                    'title'=>'标题字体阴影范围',
                    'std'=>2,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_title_shadow_color'=>array(
                    'type'=>'color',
                    'title'=>'标题字体阴影颜色',
                    'std'=>'#000',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','title'),
                    ),
                ),
                'carousel_button_margin_top'=>array(
                    'type'=>'slider',
                    'title'=>'按钮上边距(px)',
                    'std'=>20,
                    'max'=>500,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),

                'carousel_button_content'=>array(
                    'type'=>'text',
                    'title'=>'按钮内容',
                    'std'=>'了解我们',
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                        array('need_data','=',1),
                    ),
                    'responsive'=>true
                ),
                'carousel_button_width'=>array(
                    'type'=>'slider',
                    'title'=>'按钮宽度(px)',
                    'std'=>200,
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                    'responsive'=>true
                ),
                'carousel_button_height'=>array(
                    'type'=>'slider',
                    'title'=>'按钮高度(px)',
                    'std'=>40,
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                    'responsive'=>true
                ),
                'carousel_button_radius'=>array(
                    'type'=>'slider',
                    'title'=>'按钮圆角(px)',
                    'std'=>40,
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),
                'carousel_button_bg'=>array(
                    'type'=>'color',
                    'title'=>'按钮背景色',
                    'std'=>'#c2221f',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),
                'carousel_button_color'=>array(
                    'type'=>'color',
                    'title'=>'字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),
                'carousel_button_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'按钮字号',
                    'std'=>array('md'=>16,'sm'=>14,'xs'=>12),
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                    'responsive'=>true
                ),
                'carousel_button_bg_hover'=>array(
                    'type'=>'color',
                    'title'=>'鼠标移入按钮背景色',
                    'std'=>'#c2221f',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),
                'carousel_button_color_hover'=>array(
                    'type'=>'color',
                    'title'=>'鼠标移入按钮字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','button'),
                    ),
                ),

                'carousel_subtitle_setting'=>array(
                    'type'=>'buttons',
                    'title'=>'副标题设置',
                    'std'=>'font',
                    'values'=>array(
                        array(
                            'label'=>'字体',
                            'value'=>'font'
                        ),
                        array(
                            'label'=>'线条',
                            'value'=>'line'
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                    ),
                ),
                'carousel_subtitle_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字号',
                    'std'=>array('md'=>20,'sm'=>20,'xs'=>15),
                    'max'=>50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                    'responsive'=>true
                ),
                'carousel_subtitle_color'=>array(
                    'type'=>'color',
                    'title'=>'副标题字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_subtitle_shadow_x'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字体阴影横向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_subtitle_shadow_y'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字体阴影纵向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_subtitle_shadow_range'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字体阴影范围',
                    'std'=>2,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_subtitle_shadow_color'=>array(
                    'type'=>'color',
                    'title'=>'副标题字体阴影颜色',
                    'std'=>'#000',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_title_setting','=','font'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_progress_height'=>array(
                    'type'=>'slider',
                    'title'=>'进度条高度',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','line'),
                    ),
                ),
                'carousel_progress_color'=>array(
                    'type'=>'color',
                    'title'=>'进度条颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','line'),
                    ),
                ),
                'carousel_progress_margin'=>array(
                    'type'=>'margin',
                    'title'=>'进度条外边距',
                    'std'=>'0px 40px 0px 40px',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','line'),
                    ),
                ),
                'carousel_intro_margin'=>array(
                    'type'=>'slider',
                    'title'=>'简介上边距',
                    'std'=>'30',
                    'max'=>500,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_limit'=>array(
                    'type'=>'slider',
                    'title'=>'简介字数',
                    'std'=>'100',
                    'max'=>500,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                        array('need_data','=',1),
                    ),
                ),
                'carousel_intro_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'简介字号',
                    'std'=>'15',
                    'max'=>40,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_color'=>array(
                    'type'=>'color',
                    'title'=>'简介字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_height'=>array(
                    'type'=>'slider',
                    'title'=>'简介高度',
                    'std'=>'52',
                    'max'=>'600',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_line_height'=>array(
                    'type'=>'slider',
                    'title'=>'简介行高',
                    'std'=>'26',
                    'max'=>'50',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_shadow_x'=>array(
                    'type'=>'slider',
                    'title'=>'简介字体阴影横向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_shadow_y'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字体阴影纵向偏移',
                    'std'=>1,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_shadow_range'=>array(
                    'type'=>'slider',
                    'title'=>'副标题字体阴影范围',
                    'std'=>2,
                    'max'=>20,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_shadow_color'=>array(
                    'type'=>'color',
                    'title'=>'副标题字体阴影颜色',
                    'std'=>'#000',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),

//              样式10
                'carousel_decoration_layout10' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播两侧装饰'),
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                ),
                'carousel_decoration_height_layout10' => array(
                    'type' => 'slider',
                    'title' => '轮播项两侧装饰高度',
                    'std' => 176,
                    'max' => 1000,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                ),
                'carousel_decoration_options_layout10' => array(
                    'title' => '装饰背景设置',
                    'type' => 'buttons',
                    'std' => 'gradient_bg',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                    'values' => array(
                        array(
                            'label' => '纯色',
                            'value' => 'color_bg'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient_bg'
                        ),
                    ),
                    'tabs' => true,
                ),
                'carousel_decoration_gradient_bg' => array(
                    'type' => 'gradient',
                    'std' => array(
                        "color" => "#fff",
                        "color2" => "rgba(255,255,255,0.1)",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'title' => JText::_('渐变色'),
                    'depends' => array(
                        array('carousel_decoration_options_layout10', '=', 'gradient_bg'),
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                ),
                'carousel_decoration_color_bg' => array(
                    'type' => 'color',
                    'std' => '#fff',
                    'title' => JText::_('轮播两侧装饰颜色'),
                    'depends' => array(
                        array('carousel_decoration_options_layout10', '=', 'color_bg'),
                        array('image_carousel_layout', '=', 'layout10'),
                    ),
                ),
                'carousel_decoration_width_layout10' => array(
                    'type' => 'slider',
                    'title' => '装饰宽度(%)',
                    'std' => '16',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_decoration_border_radius_layout10' => array(
                    'type' => 'slider',
                    'title' => '装饰圆角',
                    'std' => '10',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_decoration_offset_layout10' => array(
                    'type' => 'slider',
                    'title' => '装饰偏移(%)',
                    'std' => '8',
                    'max' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_control_arrow_layout10' => array(
                    'type' => 'separator',
                    'title' => '箭头控制器',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_arrow_left_layout10' => array(
                    'type' => 'media',
                    'title' => '左箭头',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout10_arrow_left.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_arrow_right_layout10' => array(
                    'type' => 'media',
                    'title' => '右箭头',
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/layout10_arrow_right.png',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),
                'carousel_arrow_size_layout10' => array(
                    'type' => 'slider',
                    'title' => '箭头大小',
                    'std' => '40',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout10'),
                    )
                ),

                // 样式16
                'height_layout16' => array(
                    'title'=>'轮播高度',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 810,
                        'sm' => 1030,
                        'xs' => 406
                    ),
                    'max' => 2000,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'content_padding' => array(
                    'title'=>'内容内边距',
                    'type'=>'padding',
                    'std'=>array(
                        'md' => '234px 0 0 50px',
                        'sm' => '11% 0 0 45px',
                        'xs' => '60px 0 0 35px'
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'title_font_size_layout16' => array(
                    'title'=>'标题字号',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 36,
                        'sm' => 40,
                        'xs' => 20
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'title_font_color_layout16' => array(
                    'title'=>'标题字体颜色',
                    'type'=>'color',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'text_font_size_layout16' => array(
                    'title'=>'内容字号',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 16,
                        'sm' => 30,
                        'xs' => 14
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'text_font_color_layout16' => array(
                    'title'=>'内容字体颜色',
                    'type'=>'color',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'pagination_font_size_layout16' => array(
                    'title'=>'分页字号',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 30,
                        'sm' => 50,
                        'xs' => 16
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'now_pagination_font_size_layout16' => array(
                    'title'=>'当前页分页字号',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 48,
                        'sm' => 75,
                        'xs' => 24
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'pagination_color_layout16' => array(
                    'title'=>'分页颜色',
                    'type'=>'color',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'pagination_left_layout16' => array(
                    'title'=>'分页左边距',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 30,
                        'sm' => 50,
                        'xs' => 34
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                'pagination_bottom_layout16' => array(
                    'title'=>'分页下边距',
                    'type'=>'slider',
                    'std'=>array(
                        'md' => 220,
                        'sm' => 67,
                        'xs' => 24
                    ),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16')
                    )
                ),
                // 样式16适配
                'adaptation_layout16' => array(
                    'title' => JText::_('响应式配置'),
                        'std' => array(
                            array(
                                'max_width' => '1500',
                                'height' => '47.2',
                                'height_unit' => 'vw',
                                'content_padding' => '12vw 0 0 50px',
                                'pagination_left' => '3',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '10.7',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '1400',
                                'height' => '46',
                                'height_unit' => 'vw',
                                'content_padding' => '8.6vw 0 0 50px',
                                'pagination_left' => '4',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '7',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '1300',
                                'height' => '46.2',
                                'height_unit' => 'vw',
                                'content_padding' => '8.6vw 0 0 50px',
                                'pagination_left' => '3.7',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '7',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '1200',
                                'height' => '45',
                                'height_unit' => 'vw',
                                'content_padding' => '10vw 0 0 50px',
                                'pagination_left' => '4',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '5',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '1100',
                                'height' => '47.3',
                                'height_unit' => 'vw',
                                'content_padding' => '10vw 0 0 50px',
                                'pagination_left' => '4',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '5',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '1023',
                                'height' => '82',
                                'height_unit' => 'vw',
                                'content_padding' => '11% 0 0 50px',
                                'pagination_left' => '3.7',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '6',
                                'pagination_bottom_unit' => 'vw',
                            ),
                            array(
                                'max_width' => '767',
                                'height' => '105',
                                'height_unit' => 'vw',
                                'content_padding' => '60px 0 0 35px',
                                'pagination_left' => '9',
                                'pagination_left_unit' => 'vw',
                                'pagination_bottom' => '3',
                                'pagination_bottom_unit' => 'vw',
                            )
                        ),
                        'attr' => array(
                            'max_width' => array(
                                'type' => 'slider',
                                'title' => '屏幕适配',
                                'std' => '1500',
                                'max'=>'3000'
                            ),
                            'height' => array(
                                'type' => 'text',
                                'title' => '轮播高度',
                                'std' => '47.2',
                            ),
                            'height_unit' => array(
                                'type' => 'select',
                                'title' => '轮播高度屏幕适配单位',
                                'std' => 'vw',
                                'values' => array(
                                    'px' => 'px',
                                    'vw' => 'vw',
                                    '%' => '%',
                                    'rem' => 'rem',
                                )
                            ),
                            'pagination_left' => array(
                                'type' => 'text',
                                'title' => '分页左边距',
                                'std' => '3',
                            ),
                            'pagination_left_unit' => array(
                                'type' => 'select',
                                'title' => '屏幕适配单位',
                                'std' => 'vw',
                                'values' => array(
                                    'px' => 'px',
                                    'vw' => 'vw',
                                    '%' => '%',
                                    'rem' => 'rem',
                                )
                            ),
                            'pagination_bottom' => array(
                                'type' => 'text',
                                'title' => '分页左边距',
                                'std' => '10.7',
                            ),
                            'pagination_bottomt_unit' => array(
                                'type' => 'select',
                                'title' => '屏幕适配单位',
                                'std' => 'vw',
                                'values' => array(
                                    'px' => 'px',
                                    'vw' => 'vw',
                                    '%' => '%',
                                    'rem' => 'rem',
                                )
                            ),
                            'content_padding' => array(
                                'type' => 'padding',
                                'title' => '内容内边距',
                                'std' => '',
                            ),
                        ),
                        'depends'=>array(
                            array('image_carousel_layout','=','layout16'),
                        ),
                ),

                //  样式18配置项
                'padding_layout18' => array(
                    'type' => 'padding',
                    'title' => '内边距',
                    'std'=> '0 0 0 0',
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'content_bottom_layout18' => array(
                    'type' => 'slider',
                    'title' => '文字底边距(%)',
                    'std'=> 5,
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'title_font_size_layout18' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std'=> array('md'=>24,'sm' =>24,'xs' =>24),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'title_line_height_layout18' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'std'=> array('md'=>1.2,'sm' =>1.2,'xs' =>2),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'cont_title_font_size_layout18' => array(
                    'type' => 'slider',
                    'title' => '内容字号',
                    'std'=> array('md'=>18,'sm' =>18,'xs' =>18),
                    'max' => 50,
                    'responsive' => true,
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'cont_line_height_layout18' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'std'=> array('md'=>1.2,'sm' =>1.2,'xs' =>2),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'content_top_layout18' => array(
                    'type' => 'slider',
                    'title' => '内容上边距(%)',
                    'std'=> '2',
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                'color_layout18' => array(
                    'type' => 'color',
                    'title' => '字体颜色',
                    'std'=> '#fff',
                    'depends'=> array(
                        array('image_carousel_layout','=','layout18')
                    )
                ),
                // 样式18适配
                'adaptation_layout18' => array(
                    'title' => JText::_('响应式配置'),
                        'std' => array(
                            array(
                                'max_width' => '1023',
                                'title_font_size' => '3.4',
                                'title_font_size_unit' => 'vw',
                                'title_line_height' => '1.2',
                                'cont_font_size' => '2',
                                'cont_font_size_unit' => 'vw',
                                'cont_line_height' => '1.2',
                                'content_margin_top' => '2',
                                'content_margin_top_unit' => '%',
                            ),
                            array(
                                'max_width' => '499',
                                'title_font_size' => '6',
                                'title_font_size_unit' => 'vw',
                                'title_line_height' => '2',
                                'cont_font_size' => '4',
                                'cont_font_size_unit' => 'vw',
                                'cont_line_height' => '2',
                                'content_margin_top' => '2',
                                'content_margin_top_unit' => '%',
                            ),
                        ),
                        'attr' => array(
                            'max_width' => array(
                                'type' => 'slider',
                                'title' => '屏幕适配',
                                'std' => '1023',
                                'max'=>'3000'
                            ),
                            'title_font_size' => array(
                                'type' => 'text',
                                'title' => '标题字号',
                                'std' => '3.4',
                            ),
                            'title_font_size_unit' => array(
                                'type' => 'select',
                                'title' => '标题字号屏幕适配单位',
                                'std' => 'vw',
                                'values' => array(
                                    'px' => 'px',
                                    'vw' => 'vw',
                                    '%' => '%',
                                    'rem' => 'rem',
                                )
                            ),
                            'title_line_height' => array(
                                'type' => 'text',
                                'title' => '标题行高',
                                'std' => '1.2',
                            ),
                            'cont_font_size' => array(
                                'type' => 'text',
                                'title' => '简介字号',
                                'std' => '3.4',
                            ),
                            'cont_font_size_unit' => array(
                                'type' => 'select',
                                'title' => '简介字号屏幕适配单位',
                                'std' => 'vw',
                                'values' => array(
                                    'px' => 'px',
                                    'vw' => 'vw',
                                    '%' => '%',
                                    'rem' => 'rem',
                                )
                            ),
                            'cont_line_height' => array(
                                'type' => 'text',
                                'title' => '简介行高',
                                'std' => '1.2',
                            ),
                        ),
                        'depends'=>array(
                            array('image_carousel_layout','=','layout18'),
                        ),
                ),

                //                样式8动画
                'carousel_item_animate_layout8' => array(
                    'type' => 'separator',
                    'title' => JText::_('动画'),
                    'depends'=>array(
                        array('image_carousel_layout','!=','layout7'),
                        array('image_carousel_layout','!=','layout9'),
                        array('image_carousel_layout','!=','layout11'),
                        array('image_carousel_layout','!=','layout12'),
                    ),
                ),
                'carousel_image_scale_layout8'=>array(
                    'type'=>'text',
                    'title'=>'切换时图片放大倍数',
                    'std'=>'2',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_image_animate_time'=>array(
                    'type'=>'text',
                    'title'=>'切换时图片动画执行时间(秒)',
                    'std'=>'2',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_arrow_animate_time'=>array(
                    'type'=>'text',
                    'title'=>'翻页按钮动画执行时间(秒)',
                    'std'=>'0.3',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','item'),
                    ),
                ),
                'carousel_subtitle_animate_time'=>array(
                    'type'=>'text',
                    'title'=>'副标题动画执行时间',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_subtitle_delay_time'=>array(
                    'type'=>'text',
                    'title'=>'副标题动画延迟时间',
                    'std'=>1.2,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','font'),
                    ),
                ),
                'carousel_progress_animate_time'=>array(
                    'type'=>'text',
                    'title'=>'进度条动画执行时间',
                    'std'=>2,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','line'),
                    ),
                ),
                'carousel_progress_delay_time'=>array(
                    'type'=>'text',
                    'title'=>'进度条动画延迟时间',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','subtitle'),
                        array('carousel_subtitle_setting','=','line'),
                    ),
                ),
                'carousel_intro_animate_time'=>array(
                    'type'=>'text',
                    'title'=>'简介动画执行时间',
                    'std'=>1,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),
                'carousel_intro_delay_time'=>array(
                    'type'=>'text',
                    'title'=>'简介动画延迟时间',
                    'std'=>1.2,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout8'),
                        array('carousel_layout8_settings','=','content'),
                        array('carousel_content_setting','=','intro'),
                    ),
                ),


                'controller' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器'),
                    'desc' => JText::_('控制器'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    )
                ),
                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    )
                ),
                'arrow_margin_right' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮右边距'),
                    'desc' => JText::_('翻页按钮右边距'),
                    'min' => 0,
                    'max' => 100,
                    'std' => array(
                        'md' => 10,
                        'sm' => 10,
                        'xs' => 10
                    ),
                    'responsive'=>true,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    ),
                    'std' => 39,
                ),
                'arrow_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    ),
                    'std' => 39,
                ),
                'arrow_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => 14,
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                //Arrow hover
                'arrow_hover_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#d01920',
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','hover_arrow'),
                    )
                ),
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('image_carousel_layout','=','layout2'),
                        array('arrow_style','=','hover_arrow'),
                    )
                ),

                //              样式11控制器
                'controller_layout11' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器'),
                    'desc' => JText::_('控制器'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11'),
                    ),
                ),
                'arrow_bottom_layout11' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮下边距'),
                    'std' => 55,
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_left_layout11' => array(
                    'type' => 'media',
                    'title' => JText::_('上一页'),
                    'std' => "/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_arrow1.png",
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_right_layout11' => array(
                    'type' => 'media',
                    'title' => JText::_('下一页'),
                    'std' => "/components/com_jwpagefactory/addons/image_swiper/assets/images/layout11_arrow2.png",
                    'max'=>1000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_size_layout11' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮大小'),
                    'std' => 50,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_next_color_layout11' => array(
                    'type' => 'color',
                    'desc'=>'开启循环播放后为下一页按钮颜色设置,不开启循环播放为可用按钮颜色设置',
                    'title' => JText::_('下一页或可用状态按钮颜色'),
                    'std' => '#B71B28',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_prev_color_layout11' => array(
                    'type' => 'color',
                    'desc'=>'开启循环播放后为上一页按钮颜色设置,不开启循环播放为禁用按钮颜色设置',
                    'title' => JText::_('上一页或可用状态按钮颜色'),
                    'std' => '#c2c2c2',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),
                'arrow_margin_left_layout11' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮间隔'),
                    'std' => 10,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout11')
                    )
                ),

                'mask' => array(
                    'type' => 'separator',
                    'title' => JText::_('遮罩'),
                    'desc' => JText::_('遮罩'),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    )
                ),
                'mask_background' => array(
                    'type' => 'color',
                    'title' => JText::_('遮罩背景色'),
                    'desc' => JText::_('遮罩背景色'),
                    'std'=>'rgba(0,0,0,0.67)',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    )
                ),
                'mask_font_size'=>array(
                    'type'=>'slider',
                    'title'=>'遮罩内容字号',
                    'std'=>'',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    )
                ),
                'mask_font_color'=>array(
                    'type'=>'color',
                    'title'=>'遮罩内容字体颜色',
                    'std'=>'#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout3')
                    )
                ),

                'pagination_top_type19'=>array(
                    'type'=>'slider',
                    'title'=>'翻页按钮上边距',
                    'std'=>145,
                    'max'=>2000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout19')
                    )
                ),
                // 样式16动画
                'carousel_interval_layout16' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'std' => 4000,
                    'depends'=>array(
                        array('image_carousel_layout','=','layout16'),
                    ),
                ),
            ),
        ),
    )
);
