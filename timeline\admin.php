<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_timeline',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TIMELINE'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TIMELINE_DESC'),
		'category' => 'Content',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'style' => array(
					'title' => '布局',
					'type' => 'select',
					'values' => array(
						'style1' => JText::_('布局一'),
						'style2' => JText::_('布局二'),
					),
					'std' => 'style1'
				),

				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(
						array('title', '!=', ''),
					),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'bar_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TIMELINE_BAR_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TIMELINE_BAR_COLOR_DESC'),
					'std' => '#0095eb',
					'depends' => array(
						array('style', '=', 'style1')
					),
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),

				// Repeatable Items
                'jw_timeline_items' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_REOEATABLE_ITEMS'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TITLE_DESC'),
                            'std' => '时间轴项目',
                        ),
                        'content' => array(
                            'type' => 'editor',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CONTENT'),
                            'std' => '我为你祈祷，为你祈祷，为你祈祷，为你祈祷。',
                        ),
                        'photo' => array(
                            'type' => 'media',
                            'title' => JText::_('IMAGE'),
                            'show_input' => true,
                            'std' => '',
                        ),
                        'date' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TIMELINE_DATE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TIMELINE_DATE_DESC'),
                            'std' => '2013 5月 8日',
                        ),
                    ),
                    'depends' => array(
                        array('style', '=', 'style1')
                    )
                ), //Repeatable Items

                //                附加样式
                'need_additional' => array(
                    'title' => '是否需要附加样式',
                    'type' => 'checkbox',
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'style1')
                    )
                ),
                'additional' => array(
                    'title' => '附加样式',
                    'type' => 'select',
                    'std' => '',
                    'values' => array(
                        'style1' => '样式1'
                    ),
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('style', '=', 'style1')
                    )
                ),
                'style1_settings' => array(
                    'title' => '附加样式一设置',
                    'type' => 'buttons',
                    'values' => array(
                        array(
                            'label' => '时间轴',
                            'value' => 'line'
                        ),
                        array(
                            'label' => '时间',
                            'value' => 'time'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        )
                    ),
                    'std' => 'line',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style', '=', 'style1')
                    )
                ),
                'dot_sincere_style1' => array(
                    'title' => '时间轴节点是否实心',
                    'type' => 'checkbox',
                    'std' => 0,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    )
                ),
                'dot_size_style1' => array(
                    'title' => '时间轴节点大小',
                    'type' => 'slider',
                    'std' => array('md'=>11,'sm'=>11,'xs'=>10),
                    'max' => 50,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    ),
                    'responsive'=>true
                ),
                'dot_margin_even_style1' => array(
                    'title' => '奇数时间轴节点外边距（仅移动端有效）',
                    'type' => 'margin',
                    'std' => '-26px 0 0 0',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    ),
                ),
                'dot_margin_odd_style1' => array(
                    'title' => '偶数时间轴节点外边距（仅移动端有效）',
                    'type' => 'margin',
                    'std' => '-26px 0 0 26px',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    ),
                ),
                'line_width_style1' => array(
                    'title' => '时间轴线宽',
                    'type' => 'slider',
                    'std' => array('md'=>1,'sm'=>1,'xs'=>2),
                    'max' => 5,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true
                ),
                'line_offset_style1' => array(
                    'title' => '时间轴上边距（仅移动端有效）',
                    'type' => 'slider',
                    'std' => -2,
                    'min'=> -50,
                    'max' => 50,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'line'),
                        array('style', '=', 'style1')
                    ),
                ),
                'time_font_size_style1' => array(
                    'title' => '时间字号',
                    'type' => 'slider',
                    'std' => array('md' => 32, 'sm' => 32, 'xs' => 28),
                    'max' => 60,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'time'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true
                ),
                'time_line_height_style1' => array(
                    'title' => '时间行高（仅在移动端有效）',
                    'desc' => '仅在移动端有效',
                    'type' => 'slider',
                    'std' => 100,
                    'max' => 600,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'time'),
                        array('style', '=', 'style1')
                    ),
                ),
                'time_color_style1' => array(
                    'title' => '时间字体颜色',
                    'type' => 'color',
                    'std' => '#0095eb',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'time'),
                        array('style', '=', 'style1')
                    )
                ),
                'time_margin_left_style1' => array(
                    'title' => '时间左边距',
                    'type' => 'slider',
                    'std' => 7,
                    'max' => 300,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'time'),
                        array('style', '=', 'style1')
                    )
                ),
                'content_width_style1' => array(
                    'title' => '内容区宽度（%）',
                    'desc' => '移动端无效',
                    'type' => 'slider',
                    'std' => array('md' => 80, 'sm' => 80, 'xs' => 100),
                    'max' => 100,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true
                ),
                'style1_content_settings' => array(
                    'title' => '附加样式一内容设置',
                    'type' => 'buttons',
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        )
                    ),
                    'std' => 'img',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    )
                ),
                'img_max_width_style1' => array(
                    'title' => '内容区图片最大宽度',
                    'type' => 'slider',
                    'std' => array('md' => 131, 'sm' => 131, 'xs' => 138),
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'img'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true
                ),
                'img_max_height_style1' => array(
                    'title' => '内容区图片最大高度（仅移动端有效）',
                    'type' => 'slider',
                    'std' => 100,
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'img'),
                        array('style', '=', 'style1')
                    )
                ),
                'content_text_margin_left_style1' => array(
                    'title' => '内容区文字距图片的距离（左右，移动端无效）',
                    'desc' => '移动端无效',
                    'type' => 'slider',
                    'std' => 28,
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'img'),
                        array('style', '=', 'style1')
                    )
                ),
                'img_float_style1' => array(
                    'title' => '内容区图片是否浮动',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'img'),
                        array('style', '=', 'style1')
                    )
                ),
                'year_text_margin_left_style1' => array(
                    'title' => '日期距图片的距离（左右，仅移动端有效）',
                    'desc' => '仅移动端有效',
                    'type' => 'slider',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('img_float_style1', '=', 1),
                        array('style1_content_settings', '=', 'img'),
                        array('style', '=', 'style1')
                    )
                ),
                'content_title_margin_top_style1' => array(
                    'title' => '内容区标题距图片的距离（上下）',
                    'type' => 'slider',
                    'std' => array('md' => 20, 'sm' => 20, 'xs' => 20),
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'title'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true,
                ),
                'content_title_font_size_style1' => array(
                    'title' => '内容区标题大小',
                    'type' => 'slider',
                    'std' => array('md' => 16, 'sm' => 16, 'xs' => 16),
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'title'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true,
                ),
                'content_title_color_style1' => array(
                    'title' => '内容区标题颜色',
                    'type' => 'color',
                    'std' => '#444',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'title'),
                    ),
                ),
                'content_text_margin_top_style1' => array(
                    'title' => '内容区文字上边距',
                    'type' => 'slider',
                    'std' => array('md' => 10, 'sm' => 10, 'xs' => 10),
                    'max' => 800,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true,
                ),
                'content_text_font_size_style1' => array(
                    'title' => '内容区文字大小',
                    'type' => 'slider',
                    'std' => array('md' => 16, 'sm' => 16, 'sx' => 14),
                    'max' => 50,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true,
                ),
                'content_text_line_height_style1' => array(
                    'title' => '内容区文字行高（px）',
                    'type' => 'slider',
                    'std' => array('md' => 37, 'sm' => 37, 'xs' => 24),
                    'max' => 100,
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    ),
                    'responsive' => true,
                ),
                'content_text_color_style1' => array(
                    'title' => '内容区文字颜色',
                    'type' => 'color',
                    'std' => '#444',
                    'depends' => array(
                        array('need_additional', '=', 1),
                        array('additional', '=', 'style1'),
                        array('style1_settings', '=', 'content'),
                        array('style1_content_settings', '=', 'content'),
                        array('style', '=', 'style1')
                    )
                ),

				// 样式二
				'timeline_items_style2' => array(
					'title' => '时间项',
					'std' => array(
						array(
							'date' => '2022',
							'list_title' => array(
								array(
									'title' => '荣誉',
									'content' => '<ul>
										<li class="wt">
										2022-1 荣获“2021年度卓越品牌示范单位”<br>
										</li>
										<li class="wt">
										2022-3 荣获“高质量发展百佳企业”<br>
										</li>
										<li class="wt">
										2022-3 荣获“广东省科技进步奖二等奖”<br>
										</li>
										<li class="wt">
										2022-3 荣获“纳税突出贡献百佳企业”<br>
										</li>
										<li class="wt">
										2022-4 荣获“佛山国家高新区2021年度知识产权密集型企业”<br>
										</li>
										<li class="wt">
										2022-4 荣获“优秀会员单位”<br>
										</li>
										<li class="wt">
										2022-6 荣获“佛山市无偿献血促进奖”<br>
										</li>
										<li class="wt">
										2022-8 荣获“2021年度中国LED行业知识产权50强企业”<br>
										</li>
										<li class="wt">
										2022-8 荣获“2021年度中国LED行业营收50强企业”<br>
										</li>
										<li class="wt">
										2022-9 荣获“佛山企业100强”<br>
										</li>
										<li class="wt">
										2022-12 荣获“2021年广东省电子信息制造业综合实力百强企业”<br>
										</li>
									</ul>'
								),
								array(
									'title' => '评比/授予单位',
									'content' => '<ul>
										<li class="wt">
										深圳市照明与显示工程行业协会
										</li>
										<li class="wt">
										中共佛山市委、佛山市人民政府
										</li>
										<li class="wt">
										广东省人民政府
										</li>
										<li class="wt">
										中共佛山市委、佛山市人民政府
										</li>
										<li class="wt">
										佛山高新技术产业开发区管理委员会
										</li>
										<li class="wt">
										中国光学光电子行业协会
										</li>
										<li class="wt">
										佛山市卫生健康局、佛山市红十字会
										</li>
										<li class="wt">
										中国LED首创大会组委会
										</li>
										<li class="wt">
										中国LED首创大会组委会
										</li>
										<li class="wt">
										佛山市企业联合会、佛山市企业家协会
										</li>
										<li class="wt">
										广东省电子信息行业协会
										</li>
									</ul>'
								)
							)
						),
						array(
							'date' => '2021',
							'list_title' => array(
								array(
									'title' => '荣誉',
									'content' => '<ul>
										<li class="wt">
										2020-12 荣获国家级高新技术企业认定<br>
										</li>
										<li class="wt">
										2021-1 荣获“科技创新优势企业”<br>
										</li>
										<li class="wt">
										2021-1 荣获“优秀成果奖”<br>
										</li>
										<li class="wt">
										2021-4 荣获“佛山市禅城区先进集体”<br>
										</li>
										<li class="wt">
										2021-5 荣获“广东省科技进步奖”一等奖<br>
										</li>
										<li class="wt">
										2021-5 荣获“佛山市工业互联网示范标杆项目”<br>
										</li>
										<li class="wt">
										2021-6 荣获“中国专利优秀奖”<br>
										</li>
										<li class="wt">
										2021-11 荣获“佛山企业100强”<br>
										</li>
										<li class="wt">
										2021-12 荣获“广东省电子信息制造业综合实力百强企业”<br>
										</li>
										<li class="wt">
										2021-12 荣获行家极光奖“「Mini LED背光模组」十大供应链之星”<br>
										</li>
										<li class="wt">
										2021-12 荣获行家极光奖“「RGB LED封装」十大供应链之星”<br>
										</li>
										<li class="wt">
										2021-12 荣获行家极光奖“「LED显示供应链创新」年度产品奖”<br>
										</li>
									</ul>'
								),
								array(
									'title' => '评比/授予单位',
									'content' => '<ul>
										<li class="wt">
										广东省科学技术厅/广东省财政厅/国家税务总局广东省税务局
										</li>
										<li class="wt">
										佛山市高新技术产业协会
										</li>
										<li class="wt">
										佛山市高新技术产业协会
										</li>
										<li class="wt">
										中共佛山市禅城区委员会/佛山市禅城区人民政府
										</li>
										<li class="wt">
										广东省人民政府
										</li>
										<li class="wt">
										佛山市工业和信息化局
										</li>
										<li class="wt">
										国家知识产权局
										</li>
										<li class="wt">
										佛山市企业联合会、佛山市企业家协会
										</li>
										<li class="wt">
										广东省电子信息行业协会
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
									</ul>'
								)
							)
						),
						array(
							'date' => '2020',
							'list_title' => array(
								array(
									'title' => '荣誉',
									'content' => '<ul>
										<li class="wt">
										2019-12 荣获“国家科学技术进步奖一等奖”<br>
										</li>
										<li class="wt">
										2020-1 荣获中国光电行业“影响力企业”<br>
										</li>
										<li class="wt">
										2020-2 荣获“广东省技术发明奖一等奖”<br>
										</li>
										<li class="wt">
										2020-2 荣获“广东省科技进步二等奖”<br>
										</li>
										<li class="wt">
										2020-8 荣获2020年佛山企业100强<br>
										</li>
										<li class="wt">
										2020-12 荣获2020年度佛山高新区智能工厂<br>
										</li>
										<li class="wt">
										2020-12 荣获2019~2020年度国内LED知名品牌<br>
										</li>
										<li class="wt">
										2020-12 荣获2020年广东省电子信息制造业综合实力“百强企业”<br>
										</li>
										<li class="wt">
										2020-12 荣获行业发展突出贡献奖<br>
										</li>
										<li class="wt">
										2020-12 荣获科技创新优秀企业奖<br>
										</li>
										<li class="wt">
										2020-12 荣获基业长青企业奖<br>
										</li>
										<li class="wt">
										2020-12 荣获高工金球奖<br>
										</li>
										<li class="wt">
										2020-12 荣获2020年度LED产业TOP 50<br>
										</li>
										<li class="wt">
										2020-12 2020年度最佳品质奖<br>
										</li>
										<li class="wt">
										2020-12 荣获2020行家极光奖“「RGB供应链」TOP3 极光之星奖”<br>
										</li>
										<li class="wt">
										2020-12 荣获2020行家极光奖“「Mini背光供应链」十佳供应商”<br>
										</li>
										<li class="wt">
										2020-12 荣获2020行家极光奖“「UVLED封装」十佳供应商”<br>
										</li>
										<li class="wt">
										2020-12 荣获2020行家极光奖“「IMD」产业推动贡献奖”<br>
										</li>
									</ul>'
								),
								array(
									'title' => '评比/授予单位',
									'content' => '<ul>
										<li class="wt">
										中华人民共和国国务院
										</li>
										<li class="wt">
										中国光学光电子行业协会
										</li>
										<li class="wt">
										广东省人民政府
										</li>
										<li class="wt">
										广东省人民政府
										</li>
										<li class="wt">
										佛山市企业联合会/佛山市企业家协会
										</li>
										<li class="wt">
										佛山高新技术产业开发区管理委员会
										</li>
										<li class="wt">
										佛中国光学光电子行业协会
										</li>
										<li class="wt">
										广东省电子信息行业协会
										</li>
										<li class="wt">
										佛山市电子信息行业协会
										</li>
										<li class="wt">
										佛山市电子信息行业协会
										</li>
										<li class="wt">
										佛山市电子信息行业协会
										</li>
										<li class="wt">
										高工产业研究院（GGII）
										</li>
										<li class="wt">
										高工产业研究院（GGII）
										</li>
										<li class="wt">
										冠捷显示科技（厦门）有限公司
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
										<li class="wt">
										行家说产业研究中心
										</li>
									</ul>'
								)
							)
						)
					),
					'attr' => array(
						'date' => array(
							'type' => 'text',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TIMELINE_DATE'),
							'std' => '2022',
						),
						'list_title' => array(
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ITEM_TITLE'),
							'std' => array(
								array(
									'list_title' => '荣誉',
									'content' => ''
								),
								array(
									'list_title' => '评比/授予单位',
									'content' => ''
								)
							),
							'attr' => array(
								'title' => array(
									'type' => 'text',
									'title' => '列表标题',
									'std' => '荣誉'
								),
								'content' => array(
									'type' => 'editor',
									'title' => '列表内容',
									'std' => ''
								)
							)
						)
					), //attr
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				), 
				'timeline_items_style2_settings' => array(
					'title' => '时间轴布局2设置',
					'type'=>'buttons',
					'tab' => true,
					'values' => array(
						array(
							'label' => '时间轴',
							'value' => 'line'
						),
						array(
							'label' => '内容',
							'value' => 'content'
						)
					),
					'std' => 'line',
					'depends' => array(
						array('style', '=', 'style2')
					),
				),
				'timeline_part_style2' => array(
					'title' => '时间轴配置项',
					'type' => 'separator',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_height_style2' => array(
					'title' => '时间轴高度',
					'type' => 'slider',
					'std' => '70',
					'max' => '200',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_padding_style2' => array(
					'title' => '时间轴内边距',
					'type' => 'padding',
					'std' => '38px 0 0 0',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_bg_img_style2' => array(
					'title' => '时间轴背景图',
					'type' => 'media',
					'std' => 'https://oss.lcweb01.cn/joomla/20230111/587dc2ade0a77d464f2ef2366501ceeb.jpg',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_bg_img_offset_x_style2' => array(
					'title' => '时间轴背景图横向偏移',
					'type' => 'slider',
					'std' => 0,
					'min' => '-200',
					'max' => '200',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_bg_img_offset_y_style2' => array(
					'title' => '时间轴背景图纵向偏移',
					'type' => 'slider',
					'std' => 8,
					'min' => '-200',
					'max' => '200',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_item_margin_style2' => array(
					'title' => '时间轴日期外边距',
					'type' => 'margin',
					'std' => '0 27px 0 86px',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_item_padding_style2' => array(
					'title' => '时间轴日期内边距',
					'type' => 'padding',
					'std' => '25px 0 0 0',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_item_width_style2' => array(
					'title' => '时间轴日期宽度',
					'type' => 'slider',
					'std' => '50',
					'max' => 200,
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_on_img_style2' => array(
					'title' => '时间轴选中日期背景图',
					'type' => 'media',
					'std' => 'https://oss.lcweb01.cn/joomla/20230111/5981e24c87ebd4f060188fd6807c7dd8.jpg',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_on_offset_x_style2' => array(
					'title' => '时间轴选中日期背景图横向偏移',
					'type' => 'slider',
					'std' => 0,
					'min' => '-200',
					'max' => '200',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_date_on_offset_y_style2' => array(
					'title' => '时间轴选中日期背景图纵向偏移',
					'type' => 'slider',
					'std' => 8,
					'min' => '-200',
					'max' => '200',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_width_style2' => array(
					'title' => '时间轴翻页按钮宽度',
					'type' => 'slider',
					'std' => '24',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_height_style2' => array(
					'title' => '时间轴翻页按钮高度',
					'type' => 'slider',
					'std' => '24',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_offset_x_style2' => array(
					'title' => '时间轴翻页按钮距离时间轴距离',
					'type' => 'slider',
					'std' => '-30',
					'min' => '-100',
					'max' => '100',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_offset_y_style2' => array(
					'title' => '时间轴翻页按钮上边距',
					'type' => 'slider',
					'std' => '38',
					'min' => '-100',
					'max' => '100',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_settings' => array(
					'title' => '翻页按钮设置',
					'type' => 'buttons',
					'tab' => true,
					'values' => array(
						array(
							'label' => '左',
							'value' => 'left'
						),
						array(
							'label' => '右',
							'value' => 'right'
						),
					),
					'std' => 'left',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
					),
				),
				'timeline_button_left_style2' => array(
					'title' => '时间轴翻页左侧按钮图片',
					'type' => 'media',
					'std' => 'https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'left'),
					),
				),
				'timeline_button_offset_left_x_style2' => array(
					'title' => '时间轴翻页按钮图片横向偏移',
					'type' => 'slider',
					'std' => '0',
					'min' => "-200",
					'max' => "200",
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'left'),
					),
				),
				'timeline_button_offset_left_y_style2' => array(
					'title' => '时间轴翻页按钮图片纵向偏移',
					'type' => 'slider',
					'std' => '0',
					'min' => "-200",
					'max' => "200",
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'left'),
					),
				),
				'timeline_button_right_style2' => array(
					'title' => '时间轴翻页右侧按钮图片',
					'type' => 'media',
					'std' => 'https://oss.lcweb01.cn/joomla/20230111/2daf7e23d2cfca6d43cb1639a075f247.jpg',
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'right'),
					),
				),
				'timeline_button_offset_right_x_style2' => array(
					'title' => '时间轴翻页按钮图片横向偏移',
					'type' => 'slider',
					'std' => '-56',
					'min' => "-200",
					'max' => "200",
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'right'),
					),
				),
				'timeline_button_offset_right_y_style2' => array(
					'title' => '时间轴翻页按钮图片纵向偏移',
					'type' => 'slider',
					'std' => '0',
					'min' => "-200",
					'max' => "200",
					'depends' => array(
						array('style', '=', 'style2'),
						array('timeline_items_style2_settings', '=', 'line'),
						array('timeline_button_settings', '=', 'right'),
					),
				),
				'content_title_style2' => array(
					'title' => '列表标题设置项',
					'type' => 'separator',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_title_style2' => array(
					'title' => '标题字号',
					'type' => 'slider',
					'std' => 24,
					'max' => 50,
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_title_padding_style2' => array(
					'title' => '标题内边距',
					'type' => 'padding',
					'std' => '10px 0 25px 30px',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_title_color_style2' => array(
					'title' => '标题字体颜色',
					'type' => 'color',
					'std' => '#666',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'content_style2' => array(
					'title' => '列表内容设置项',
					'type' => 'separator',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_content_column' => array(
					'title' => '内容列数',
					'type' => 'slider',
					'max' => 5,
					'std' => 2,
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_padding' => array(
					'title' => '内容内边距',
					'type' => 'padding',
					'std' => '0 0 0 0',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_content_size_style2' => array(
					'title' => '内容字号',
					'type' => 'slider',
					'std' => 13,
					'max' => 50,
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_content_padding_style2' => array(
					'title' => '列表内边距',
					'type' => 'padding',
					'std' => '0 0 0 30px',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_hidden_style2' => array(
					'title' => '列表是否超出隐藏',
					'type' => 'checkbox',
					'std' => '1',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_content_color_style2' => array(
					'title' => '内容字体颜色',
					'type' => 'color',
					'std' => '#666',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
				'list_line_height_style2' => array(
					'title' => '内容行高',
					'type' => 'slider',
					'std' => '35',
					'depends' => array(
						array('timeline_items_style2_settings', '=', 'content'),
					)
				),
			),
		)
	)
);
