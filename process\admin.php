<?php

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'process',
         'title' => '操作流程',
         'desc' => '',
         'category' => '龙采官网插件',
         'attr' => array(
             'general' => array(
                 'site_id' => array(
                     'std' => $site_id,
                 ),
                 'company_id' => array(
                     'std' => $company_id,
                 ),

                'bgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('PC背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/1f9a981cf4b250d1167d9929a443aa5a.jpg',
                    
                ),
                'sjbgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('手机背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/64c10e2a9efaf3ba5f4c983466fb5d63.jpg',
                    
                ),
                'biaoti' => array(
                    'type' => 'text',
                    'title' => JText::_('标题'),
                    'std' => '快捷的操作流程',
                    
                ),
                'biaoti_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => '60',
                    'responsive' => true,
                    'std' => array('md' => '48', 'sm' => '30', 'xs' => '26'),
                ),
                'biaoti_color' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#fff',
                ),
                'jianjie' => array(
                    'type' => 'text',
                    'title' => JText::_('简介'),
                    'std' => 'WEB工作台+手机端+智能硬件',
                ),
                'jianjie_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'max' => '60',
                    'responsive' => true,
                    'std' => array('md' => '24', 'sm' => '18', 'xs' => '14'),
                ),
                'jianjie_color' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体颜色'),
                    'std' => '#fff',
                ),
                'mk_bgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('模块背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png',
                    
                ),
                'hv_bgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('划过背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220530/0b3b00f9e65f2c5aee19ac367b118c3b.png',

                ),
                'mk_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('模块字体大小'),
                    'max' => '60',
                    'responsive' => true,
                    'std' => array('md' => '24', 'sm' => '18', 'xs' => '12'),
                ),
                'jw_tab_item' => array(
                    'title' => JText::_('模块组'),
                    'std' => array(
                        array(
                            'xuhao' => '01',
                            'title' => '合适位置安装位置',
                        ),
                        array(
                            'xuhao' => '02',
                            'title' => '后台录入人脸信息',
                        ),
                        array(
                            'xuhao' => '03',
                            'title' => '摄像头人脸采集',
                        ),
                        array(
                            'xuhao' => '04',
                            'title' => '后台分析识别',
                        ),
                        array(
                            'xuhao' => '05',
                            'title' => '移动考勤通知后台记录轨迹',
                        )
                    ),
                    'attr' => array(
                        'xuhao' => array(
                            'type' => 'text',
                            'title' => JText::_('序号'),
                            'std' => '',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => ''
                        ),
                    ),
                ),

             ),
         ),
     )
 );
