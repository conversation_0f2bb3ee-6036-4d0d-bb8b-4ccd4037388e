<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type'       => 'repeatable',
        'addon_name' => 'jw_tiyuyunguan_zuoyou_swiper',
        'category'   => '体育云馆家官网',
        'title'      => '左右内容翻页',
        'desc'       => '体育云馆家官网插件2-左右内容翻页',
        'attr'       => array(
            'general' => array(
                'jw_tiyuyunguan_zuoyou_item'        => array(
                    'title' => JText::_('内容列表'),
                    'attr'  => array(

                        'title'                            => array(
                            'type'  => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CA<PERSON>USEL_ITEM_TITLE'),
                            'std'   => '填写文字',
                        ),

                        'title_font_family'                => array(
                            'type'     => 'fonts',
                            'title'    => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_FONT_FAMILY'),
                            'depends'  => array(array('title', '!=', '')),
                            'selector' => array(
                                'type' => 'font',
                                'font' => '{{ VALUE }}',
                                'css'  => ' h2 { font-family: "{{ VALUE }}"; }',
                            ),
                        ),

                        'title_fontsize'                   => array(
                            'type'       => 'slider',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_FONTSIZE'),
                            'max'        => 100,
                            'std'        => array('md' => 46, 'sm' => 36, 'xs' => 16),
                            'responsive' => true,
                        ),

                        'title_lineheight'                 => array(
                            'type'       => 'slider',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_LINEHEIGHT'),
                            'max'        => 100,
                            'std'        => array('md' => 56, 'sm' => 46, 'xs' => 20),
                            'responsive' => true,
                        ),

                        'title_color'                      => array(
                            'type'  => 'color',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_COLOR'),
                            'std'   => '#fff',
                        ),

                        'title_padding'                    => array(
                            'type'       => 'padding',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_PADDING'),
                            'std'        => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                            'responsive' => true,
                        ),

                        'title_margin'                     => array(
                            'type'       => 'margin',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_MARGIN'),
                            'std'        => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                            'responsive' => true,
                        ),

                        'content'                          => array(
                            'type'  => 'editor',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT'),
                            'desc'  =>'填写内容',
                            'std'   => '构建全新智能化服务体系。软硬件结合，大数据智能分析、精准营销，场馆运营更高效。',
                        ),

                        'content_font_family'              => array(
                            'type'     => 'fonts',
                            'title'    => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
                            'depends'  => array(array('content', '!=', '')),
                            'selector' => array(
                                'type' => 'font',
                                'font' => '{{ VALUE }}',
                                'css'  => ' .jwpf-carousel-pro-text .jwpf-carousel-content { font-family: "{{ VALUE }}"; }',
                            ),
                        ),

                        'content_fontsize'                 => array(
                            'type'       => 'slider',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_FONTSIZE'),
                            'max'        => 100,
                            'std'        => array('md' => 16, 'sm' => 14, 'xs' => 12),
                            'responsive' => true,
                        ),

                        'content_lineheight'               => array(
                            'type'       => 'slider',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_LINEHEIGHT'),
                            'max'        => 100,
                            'std'        => array('md' => 24, 'sm' => 22, 'xs' => 16),
                            'responsive' => true,
                        ),

                        'content_color'                    => array(
                            'type'  => 'color',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_COLOR'),
                            'std'   => '#fff',
                        ),

                        'content_padding'                  => array(
                            'type'       => 'padding',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_PADDING'),
                            'std'        => array('md' => '20px 0px 30px 0px', 'sm' => '15px 0px 20px 0px', 'xs' => '10px 0px 10px 0px'),
                            'responsive' => true,
                        ),

                        'content_margin'                   => array(
                            'type'       => 'margin',
                            'title'      => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_MARGIN'),
                            'std'        => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                            'responsive' => true,
                        ),

                        'bg'                               => array(
                            'type'   => 'media',
                            'title'  => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
                            'desc'   => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                            'format' => 'image',
                            'std'    => '/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/bg1.png',
                        ),
                        'wapbg'                               => array(
                            'type'   => 'media',
                            'title'  => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
                            'desc'   => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                            'format' => 'image',
                            'std'    => '/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/bg1-2.png',
                        ),

                        //Button
                        'button_text'                      => array(
                            'type'  => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT'),
                            'desc'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT_DESC'),
                            'std'   => '按钮',
                        ),

                        'button_font_family'               => array(
                            'type'     => 'fonts',
                            'title'    => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_BUTTON_FONT_FAMILY'),
                            'depends'  => array(array('button_text', '!=', '')),
                            'selector' => array(
                                'type' => 'font',
                                'font' => '{{ VALUE }}',
                                'css'  => '.jwpf-carousel-pro-text .jwpf-btn { font-family: "{{ VALUE }}"; }',
                            ),
                        ),

                        'button_font_style'                => array(
                            'type'    => 'fontstyle',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_letterspace'               => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
                            'values'  => array(
                                '0'    => 'Default',
                                '1px'  => '1px',
                                '2px'  => '2px',
                                '3px'  => '3px',
                                '4px'  => '4px',
                                '5px'  => '5px',
                                '6px'  => '6px',
                                '7px'  => '7px',
                                '8px'  => '8px',
                                '9px'  => '9px',
                                '10px' => '10px',
                            ),
                            'std'     => '0',
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_url'                       => array(
                            'type'         => 'media',
                            'format'       => 'attachment',
                            'title'        => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL'),
                            'desc'         => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL_DESC'),
                            'placeholder'  => 'http://',
                            'hide_preview' => true,
                            'depends'      => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_target'                    => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values'  => array(
                                ''       => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_type'                      => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
                            'values'  => array(
                                'default'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
                                'primary'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
                                'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
                                'success'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
                                'info'      => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
                                'warning'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
                                'danger'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
                                'dark'      => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
                                'link'      => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
                                'custom'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
                            ),
                            'std'     => 'success',
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_appearance'                => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
                            'values'  => array(
                                ''         => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
                                'gradient' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_GRADIENT'),
                                'outline'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
                                '3d'       => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
                            ),
                            'std'     => 'flat',
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_status'                    => array(
                            'type'    => 'buttons',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_ENABLE_BACKGROUND_OPTIONS'),
                            'std'     => 'normal',
                            'values'  => array(
                                array(
                                    'label' => 'Normal',
                                    'value' => 'normal',
                                ),
                                array(
                                    'label' => 'Hover',
                                    'value' => 'hover',
                                ),
                            ),
                            'tabs'    => true,
                            'depends' => array(
                                array('button_type', '=', 'custom'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_background_color'          => array(
                            'type'    => 'color',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_DESC'),
                            'std'     => '#444444',
                            'depends' => array(
                                array('button_appearance', '!=', 'gradient'),
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'normal'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_background_gradient'       => array(
                            'type'    => 'gradient',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
                            'std'     => array(
                                "color"  => "#B4EC51",
                                "color2" => "#429321",
                                "deg"    => "45",
                                "type"   => "linear",
                            ),
                            'depends' => array(
                                array('button_appearance', '=', 'gradient'),
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'normal'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_color'                     => array(
                            'type'    => 'color',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_DESC'),
                            'std'     => '#fff',
                            'depends' => array(
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'normal'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_background_color_hover'    => array(
                            'type'    => 'color',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER_DESC'),
                            'std'     => '#222',
                            'depends' => array(
                                array('button_appearance', '!=', 'gradient'),
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'hover'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_background_gradient_hover' => array(
                            'type'    => 'gradient',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
                            'std'     => array(
                                "color"  => "#429321",
                                "color2" => "#B4EC51",
                                "deg"    => "45",
                                "type"   => "linear",
                            ),
                            'depends' => array(
                                array('button_appearance', '=', 'gradient'),
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'hover'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_color_hover'               => array(
                            'type'    => 'color',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER_DESC'),
                            'std'     => '#fff',
                            'depends' => array(
                                array('button_type', '=', 'custom'),
                                array('button_status', '=', 'hover'),
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_size'                      => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
                            'values'  => array(
                                ''    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
                                'lg'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
                                'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
                                'sm'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
                                'xs'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
                            ),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_shape'                     => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_DESC'),
                            'values'  => array(
                                'rounded' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUNDED'),
                                'square'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_SQUARE'),
                                'round'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUND'),
                            ),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_block'                     => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
                            'values'  => array(
                                ''               => JText::_('JNO'),
                                'jwpf-btn-block' => JText::_('JYES'),
                            ),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_icon'                      => array(
                            'type'    => 'icon',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                        'button_icon_position'             => array(
                            'type'    => 'select',
                            'title'   => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
                            'values'  => array(
                                'left'  => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                                'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                            ),
                            'depends' => array(
                                array('button_text', '!=', ''),
                            ),
                        ),

                    ),
                ),
            ),
        ),
    )
);