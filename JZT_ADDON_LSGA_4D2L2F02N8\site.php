<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonJZT_ADDON_LSGA_4D2L2F02N8 extends JwpagefactoryAddons
{
    // 上一页 下一页 公共方法
    public function articlePage()
    {

        $company_id = $_GET['company_id'] ?? '';
        $site_id = $_GET['site_id'] ?? '';
        $catid_id = $_GET['catid_id'] ?? '';
        $layout_id = $_GET['layout_id'] ?? '';

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        //翻页按钮是否开启
        $page_button=(isset($settings->page_button) && $settings->page_button) ? $settings->page_button : 0;
        // 翻页开启动态标题
        $page_dttitle = (isset($settings->page_dttitle) && $settings->page_dttitle) ? $settings->page_dttitle : 0;
        $next_page_text = isset($settings->next_page_text) && $settings->next_page_text ? $settings->next_page_text : '下一页';
        $up_page_text = isset($settings->up_page_text) && $settings->up_page_text ? $settings->up_page_text : '上一页';
        $ordering_select = isset($settings->ordering_select) && $settings->ordering_select ? $settings->ordering_select : '';
        
        //获取文章详情的数据源
        $app = JFactory::getApplication();
        $input = $app->input;
        $article_id = $input->get('detail');
        $detail_id = base64_decode($input->get('id'));
        //读取文章数据
        if($ordering_select!='')
        {
            $article = JwPageFactoryBase::getArticleById2new($article_id, $catid_id,$ordering_select);
        }
        else
        {
            $article = JwPageFactoryBase::getArticleById2($article_id, $catid_id);
        }
        if ($catid_id && is_array($catid_id)) {
            $catid_id = implode(',', $catid_id);
        }

        $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->on."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);
        $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->down.'&Itemid=0&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);

        // 样式部分变量
        $pageColor = (isset($settings->pageColor) && $settings->pageColor) ? $settings->pageColor : '';
        $pageBgColor = (isset($settings->pageBgColor) && $settings->pageBgColor) ? $settings->pageBgColor : '';
        $pageBorderColor = (isset($settings->pageBorderColor) && $settings->pageBorderColor) ? $settings->pageBorderColor : '';
        $pageBorderColorhover = (isset($settings->pageBorderColorhover) && $settings->pageBorderColorhover) ? $settings->pageBorderColorhover : '';
        $pageBgColorhover = (isset($settings->pageBgColorhover) && $settings->pageBgColorhover) ? $settings->pageBgColorhover : '';
        $pageColorhover = (isset($settings->pageColorhover) && $settings->pageColorhover) ? $settings->pageColorhover : '';
        $page_a_line = isset($settings->page_a_line) && $settings->page_a_line ? 'underline' : 'none';

        $page_a_font = isset($settings->page_a_font) && $settings->page_a_font ? $settings->page_a_font : '14';

        // 开启返回列表
        $page_button_back = isset($settings->page_button_back) && $settings->page_button_back ? $settings->page_button_back : 0;

        $page_a_icon_show = isset($settings->page_a_icon_show) && $settings->page_a_icon_show ? $settings->page_a_icon_show : 0;
        $page_a_icon_prev = isset($settings->page_a_icon_prev) && $settings->page_a_icon_prev ? $settings->page_a_icon_prev : '';
        $page_a_icon_next = isset($settings->page_a_icon_next) && $settings->page_a_icon_next ? $settings->page_a_icon_next : '';
        $page_a_icon_prev_hover = isset($settings->page_a_icon_prev_hover) && $settings->page_a_icon_prev_hover ? $settings->page_a_icon_prev_hover : '';
        $page_a_icon_next_hover = isset($settings->page_a_icon_next_hover) && $settings->page_a_icon_next_hover ? $settings->page_a_icon_next_hover : '';

        // 富文本p标签上边距
        $content_p_top = isset($settings->content_p_top) ? $settings->content_p_top : 0;
        // 富文本p标签下边距
        $content_p_bot = isset($settings->content_p_bot) ? $settings->content_p_bot : 16;

        // 翻页按钮宽度
        $page_width = isset($settings->page_width) ? $settings->page_width : 48;
        // 翻页按钮高度
        $page_height = isset($settings->page_height) ? $settings->page_height : 40;

        $output = '<style>';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .article-content p {
                margin-top: ' . $content_p_top . 'px;
                margin-bottom: ' . $content_p_bot . 'px;
            }';
            if ($article->on) {
                $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox,
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                }';
            } else {
                $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox,
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt {
                    display: flex;
                    justify-content: flex-end;
                    padding: 20px;
                }';
            }
            $output .= ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .article-content table p { margin: 0 }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .article-content {
                height: auto;
                overflow: hidden;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a {
                width: 120px;
                height: 40px;
                border: 1px solid ' . $pageBorderColor . ';
                color: ' . $pageColor . ';
                font-size: ' . $page_a_font . 'px;
                background:' . $pageBgColor . ';
                text-align: center;
                line-height: 40px;
                text-decoration: none;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a:hover {
                border: 1px solid ' . $pageBorderColorhover . ';
                color: ' . $pageColorhover . ';
                background:' . $pageBgColorhover . ';
                text-decoration: ' . $page_a_line . ';
            }';
            // 翻页动态标题样式
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a {
                width: ' . $page_width . '%;
                text-align:left;
                height: ' . $page_height . 'px;
                color: ' . $pageColor . ';
                font-size: ' . $page_a_font . 'px;
                background:' . $pageBgColor . ';
                line-height: ' . $page_height . 'px;
                text-decoration: none;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:hover {
                color: ' . $pageColorhover . ';
                background:' . $pageBgColorhover . ';
                text-decoration: ' . $page_a_line . ';
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:nth-child(2) {
                text-align:right;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a .a-text {
                width: calc(100% - 30px);
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: middle;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:hover .a-text {
                text-decoration: ' . $page_a_line . ';
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a .a-icon-box {
                width: 20px;
                height: 20px;
                position: relative;
                margin-right: 10px;
                display: inline-block;
                vertical-align: middle;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:nth-child(2) .a-icon-box {
                margin-left: 10px;
                margin-right: 0px;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a .a-icon-box .a-icon {
                width: 100%;
                height: 100% !important;
                object-fit: scale-down;
                opacity: 1;
                transition: 0.5s;
                position: absolute;
                top: 0;
                left: 0;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a .a-icon-box .a-icon.a-icon-hover {
                opacity: 0;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:hover .a-icon-box .a-icon {
                opacity: 0;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBoxt a:hover .a-icon-box .a-icon.a-icon-hover {
                opacity: 1;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .page-back-box {
                font-size: 16px;
                line-height: 16px;
                color: #4e4e4e;
                text-align: right;
                padding: 84px 0 22px;
                border-bottom: 1px solid #dcdfdd;
                margin-bottom: 36px;
            }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .page-back {
                color: #4e4e4e;
            }';
        $output .= '</style>';
        if($page_button == 0) {
            if($page_button_back == 1) {
                $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
                $back_link = '';
                if($detail_page_id){
                    $id = base64_encode($detail_page_id);
                    $back_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                }
                $output .= '
                <div class="page-back-box">
                    <a href="' . $back_link . '" class="page-back">返回列表</a>
                </div>
                ';
            }
            if ($page_dttitle == 1) {
                $on_title = '';
                if ($article->ontitle) {
                    if($up_page_text) {
                        $on_title .= $up_page_text;
                    }
                    $on_title .= $article->ontitle;
                }
                $down_title = '';
                if ($article->downtitle) {
                    if($next_page_text) {
                        $down_title .= $next_page_text;
                    }
                    $down_title .= $article->downtitle;
                }
                $output .= '<div class="btnBoxt">';
                $on_style = '';
                if(!$on_title) {
                    $on_style = ' style="width: 0;"';
                }
                $output .= '<a href="' . $on . '"' . $on_style .'>';
                if($page_a_icon_show == 1 && $on_title) {
                    $output .= '<span class="a-icon-box">';
                    if($page_a_icon_prev) {
                        $output .= '<img class="a-icon" src="' . $page_a_icon_prev . '" />' ;
                    }
                    if($page_a_icon_prev_hover) {
                        $output .= '<img class="a-icon a-icon-hover" src="' . $page_a_icon_prev_hover . '" />' ;
                    }
                    $output .= '</span>';
                    $output .= '<span class="a-text">';
                    $output .= $on_title;
                    $output .= '</span>';
                }else {
                    $output .= $on_title;
                }
                $output .='</a>';
                $down_style = '';
                if(!$down_title) {
                    $down_style = ' style="width: 0;"';
                }
                $output .= '<a href="' . $down . '"' . $down_style .'>';
                if($page_a_icon_show == 1 && $down_title) {
                    $output .= '<span class="a-text">';
                    $output .= $down_title;
                    $output .= '</span>';
                    $output .= '<span class="a-icon-box">';
                    if($page_a_icon_next) {
                        $output .= '<img class="a-icon" src="' . $page_a_icon_next . '" />' ;
                    }
                    if($page_a_icon_next_hover) {
                        $output .= '<img class="a-icon a-icon-hover" src="' . $page_a_icon_next_hover . '" />' ;
                    }
                    $output .= '</span>';
                }else {
                    $output .= $down_title;
                }
                $output .='</a>';
                $output .= '</div>';
            } else {
                $output .= '	<div class="btnBox">';
                if ($article->on) {
                    $output .= '<a href="' . $on . '">'.$up_page_text.'</a>';
                }
                if ($article->down) {
                    $output .= '<a href="' . $down . '">'.$next_page_text.'</a>';
                }
                $output .= '</div>';
            }
        }
        return $output;
    }

    //在预览页面中渲染
    public function render()
    {
        $settings = $this->addon->settings;

        //此处载入所有配置项变量
        //$class = (isset($settings->class) && $settings->class) ? $settings->class : '';

        //配置项变量
        $color1615443385649 = (isset($settings->color1615443385649) && $settings->color1615443385649) ? $settings->color1615443385649 : '';
        $select1615452416171 = (isset($settings->select1615452416171) && $settings->select1615452416171) ? $settings->select1615452416171 : '';
        $font_style_type1 = (isset($settings->font_style_type1) && $settings->font_style_type1) ? $settings->font_style_type1 : '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol"';
        $color1615452944411 = (isset($settings->color1615452944411) && $settings->color1615452944411) ? $settings->color1615452944411 : '';
        $color1615453171634 = (isset($settings->color1615453171634) && $settings->color1615453171634) ? $settings->color1615453171634 : '';
        $font_size = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '';
        $font_size_date = (isset($settings->font_size_date) && $settings->font_size_date) ? $settings->font_size_date : '';
        //        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : '';
        $pageColor = (isset($settings->pageColor) && $settings->pageColor) ? $settings->pageColor : '';
        $ordering_select = (isset($settings->ordering_select) && $settings->ordering_select) ? $settings->ordering_select : '';
        $pageBorderColor = (isset($settings->pageBorderColor) && $settings->pageBorderColor) ? $settings->pageBorderColor : '';
        $pageBgColor = (isset($settings->pageBgColor) && $settings->pageBgColor) ? $settings->pageBgColor : '';
        $theme = (isset($settings->theme) && $settings->theme) ? $settings->theme : 'site01'; // 布局
        $pageColorhover = (isset($settings->pageColorhover) && $settings->pageColorhover) ? $settings->pageColorhover : '';
        $select1646020218 = (isset($settings->select1646020218) && $settings->select1646020218) ? $settings->select1646020218 : 'right';
        $pageBorderColorhover = (isset($settings->pageBorderColorhover) && $settings->pageBorderColorhover) ? $settings->pageBorderColorhover : '';

        $biaoqian_type = (isset($settings->biaoqian_type) && $settings->biaoqian_type) ? $settings->biaoqian_type : 'no';
        $biaoqian_peizhi = (isset($settings->biaoqian_peizhi) && $settings->biaoqian_peizhi) ? $settings->biaoqian_peizhi : '';
        $biaoqian_bg_color = (isset($settings->biaoqian_bg_color) && $settings->biaoqian_bg_color) ? $settings->biaoqian_bg_color : '';
        $biaoqian_line_height = (isset($settings->biaoqian_line_height) && $settings->biaoqian_line_height) ? $settings->biaoqian_line_height : 0;
        $biaoqian_title_type = (isset($settings->biaoqian_title_type) && $settings->biaoqian_title_type) ? $settings->biaoqian_title_type : 'no';
        $biaoqian_title_color = (isset($settings->biaoqian_title_color) && $settings->biaoqian_title_color) ? $settings->biaoqian_title_color : '';
        $biaoqian_title_size = (isset($settings->biaoqian_title_size) && $settings->biaoqian_title_size) ? $settings->biaoqian_title_size : 14;

        $pageBgColorhover = (isset($settings->pageBgColorhover) && $settings->pageBgColorhover) ? $settings->pageBgColorhover : '';
        $font_size_fl = (isset($settings->font_size_fl) && $settings->font_size_fl) ? $settings->font_size_fl : '14'; // 分类字体大小
        $color1615453171634fl = (isset($settings->color1615453171634fl) && $settings->color1615453171634fl) ? $settings->color1615453171634fl : '#888888'; // 分类颜色
        $d_font_posi = (isset($settings->d_font_posi) && $settings->d_font_posi) ? $settings->d_font_posi : 'flex-start'; // 日期分类位置
        $d_margin_right = (isset($settings->d_margin_right) && $settings->d_margin_right) ? $settings->d_margin_right : '20'; // 日期分类间距
        $d_mtop = (isset($settings->d_mtop) && $settings->d_mtop) ? $settings->d_mtop : '10'; // 日期分类上间距
        $d_mbottom = (isset($settings->d_mbottom) && $settings->d_mbottom) ? $settings->d_mbottom : '10'; // 日期分类下间距

        $font_size_content=(isset($settings->font_size_content) && $settings->font_size_content) ? $settings->font_size_content : '14';
        $font_size_content_sm=(isset($settings->font_size_content_sm) && $settings->font_size_content_sm) ? $settings->font_size_content_sm : '14';
        $font_size_content_xs=(isset($settings->font_size_content_xs) && $settings->font_size_content_xs) ? $settings->font_size_content_xs : '14';
        $tdk_button=(isset($settings->tdk_button) && $settings->tdk_button) ? $settings->tdk_button :'no';
        $title_h2_button=(isset($settings->title_h2_button) && $settings->title_h2_button) ? $settings->title_h2_button :'yes';
        $keywords_h2_button=(isset($settings->keywords_h2_button) && $settings->keywords_h2_button) ? $settings->keywords_h2_button :'no';
        $description_h2_button=(isset($settings->description_h2_button) && $settings->description_h2_button) ? $settings->description_h2_button :'no';

        $next_page_text=(isset($settings->next_page_text) && $settings->next_page_text) ? $settings->next_page_text : '下一页';
        $up_page_text=(isset($settings->up_page_text) && $settings->up_page_text) ? $settings->up_page_text :'上一页';

//        文字环绕图片布局的图片位置
        $img_position=(isset($settings->img_position) && $settings->img_position) ? $settings->img_position : 'left';
//        标题离正文的距离
        $content_top=(isset($settings->content_top) && $settings->content_top) ? $settings->content_top : '15';
        //翻页按钮是否开启
        $page_button=(isset($settings->page_button) && $settings->page_button) ? $settings->page_button : 0;
        $color1615453171634f=(isset($settings->color1615453171634f) && $settings->color1615453171634f) ? $settings->color1615453171634f : '#888888';
        $color1615453171634f2=(isset($settings->color1615453171634f2) && $settings->color1615453171634f2) ? $settings->color1615453171634f2 : '#888888';


        //03正文高度
        $content_height03=(isset($settings->content_height03) && $settings->content_height03) ? $settings->content_height03 : '500';
        $dianzan03_color=(isset($settings->dianzan03_color) && $settings->dianzan03_color) ? $settings->dianzan03_color : '#0051ca';
        $font_size03_dz=(isset($settings->font_size03_dz) && $settings->font_size03_dz) ? $settings->font_size03_dz : '24';
        $dianzan03_autor=(isset($settings->dianzan03_autor) && $settings->dianzan03_autor) ? $settings->dianzan03_autor : '';


        //04简介配置项
        $intro_fontsize04 = (isset($settings->intro_fontsize04) && $settings->intro_fontsize04) ? $settings->intro_fontsize04 : '13'; // 简介字体大小
        $intro_color04 = (isset($settings->intro_color04) && $settings->intro_color04) ? $settings->intro_color04 : '#888'; // 简介字体颜色
        $site04_fy_height = (isset($settings->site04_fy_height) && $settings->site04_fy_height) ? $settings->site04_fy_height : '120'; // 翻页高度

        $page_dttitle = (isset($settings->page_dttitle) && $settings->page_dttitle) ? $settings->page_dttitle : 0;

        //关闭日期显示
        $date1_hide = (isset($settings->date1_hide) && $settings->date1_hide) ? $settings->date1_hide : 0;

        $company_id = $_GET['company_id'] ?? '';
        $site_id = $_GET['site_id'] ?? '';
        $catid_id = $_GET['catid_id'] ?? '';
        $layout_id = $_GET['layout_id'] ?? '';

        //获取文章详情的数据源
        $app = JFactory::getApplication();
        $input = $app->input;
        $article_id = $input->get('detail');
        $detail_id = base64_decode($input->get('id'));
        //浏览次数增加
        JwPageFactoryBase::setrticleLookNum($article_id, $catid_id);

        //读取文章数据
        if($ordering_select!='')
        {
            $article = JwPageFactoryBase::getArticleById2new($article_id, $catid_id,$ordering_select);
        }
        else
        {
            $article = JwPageFactoryBase::getArticleById2($article_id, $catid_id);
        }
        if ($catid_id && is_array($catid_id)) {
            $catid_id = implode(',', $catid_id);
        }
        // $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid_id . , $absolute = true);
        $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->on."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);
        //	    $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid_id . '&catid=' . $catid_id, $absolute = true);
        $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->down.'&Itemid=0&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);
        $strict_editor = (isset($settings->strict_editor) && $settings->strict_editor) ? $settings->strict_editor : 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $output = '';

        // 标题是否加粗
        $title_font_weight = isset($settings->title_font_weight) && $settings->title_font_weight ? $settings->title_font_weight : 0;
        $title_css = '';
        if($title_font_weight == 1) {
            $title_css .= 'font-weight: bold;';
        }
        $output .= '
        <style type="text/css">
            @font-face {
                font-family: "Founder_small_label_Song_Simplified";
                src: url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.woff") format("woff"),
                    url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.ttf") format("truetype"),
                    url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.svg") format("svg");
            }
            @font-face {
                font-family: "imitate_song";
                src: url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.woff") format("woff"),
                    url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.ttf") format("truetype"),
                    url("' . JURI::base(true) .'/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.svg") format("svg");
            }
            ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2,
            ' . $addon_id . ' .dytit,
            ' . $addon_id . ' .postInfo .title {
                ' . $title_css . '
            }
            ' . $addon_id . ' img, ' . $addon_id . ' video { max-width: 100% !important }
            ' . $addon_id . ' img { height: auto !important }
        </style>
        ';
        if($biaoqian_type == 'yes')
        {
            if($biaoqian_peizhi)
            {
                $output .= '<div style="line-height:'.$biaoqian_line_height.'px;background-color:'.$biaoqian_bg_color.';">';
                foreach ($biaoqian_peizhi as $k => $v) {
                    $output .= '<span style="color:'.$v->title_color.';font-size:'.$biaoqian_title_size.'px;">'.$v->title.'</span>';
                }
                if($biaoqian_title_type=='yes')
                {
                    $output .= '<span style="color:'.$biaoqian_title_color.';font-size:'.$biaoqian_title_size.'px;">'.$article->title.'</span>';

                }
                $output .= '</div>';
            }

        }

        if ($theme == 'site01') {
            $date_text = $settings->date_text ?? '编辑：';
            $output .= '<style>'
                . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT {
                    color: ' . $color1615452944411 . ';
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                    display: inline-block '.($strict_editor ? '' : '!important').';
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2 {
                    font-size: ' . $font_size . 'px;
                    font-family: '. $font_style_type1 . ' !important;
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT p {
                    font-size:' . $font_size_date . 'px;
                }';
            $output .= '
                @media (max-width: 991px) {
                     ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                        display: inline-block '.($strict_editor ? '' : '!important').';
                        max-width: 100% !important;
                        height: 100% !important;
                     }
                }
                @media (max-width: 470px) {
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                        display: inline-block '.($strict_editor ? '' : '!important').';
                        max-width: 100% !important;
                        height: auto !important;
                    }
                }';
            $output .= '</style>';
            $output .= '<div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">';
            $output .= '	<h2 style="text-align:' . $select1615452416171 . '">' . $article->title . '</h2>';
            $output .= '   <hr />';

            if($date1_hide!=1){
                $output .= '	<p style="text-align:'.$select1646020218.'; display:block; padding-right:10px; color:' . $color1615453171634 . '">' . $date_text . $article->modified . '</p>';
            }

            $output .= '	<div class="article-content" style="color:' . $color1615443385649 . ';margin-top:'.$content_top.'px">';
            $output .= '    ' . $article->fulltext . '';
            $output .= '	</div>';
            // 文章详情 翻页部分
            $output .= $this->articlePage();
            $output .= '</div>';
            $output .= '<script>';
            $output .= '';
            $output .= '</script>';
        }
        if ($theme == 'site02') {
            $date_text = $settings->date_text ?? '日期：';
            $output .= '<style>';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT {
                    color:' . $color1615452944411 . ';
                }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                    display: inline-block '.($strict_editor ? '' : '!important').';
                }';
            $output .= $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2 {
                    font-size:' . $font_size . 'px;
                    font-family: '. $font_style_type1.' !important;
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .time-date {
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    color:' . $color1615453171634 . ';
                    font-size: ' . $font_size_date . 'px;
                    margin-right: ' . $d_margin_right . 'px;
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .time-type {
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    color: ' . $color1615453171634fl . ';
                    font-size: ' . $font_size_fl . 'px;
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .time-read {
                    color: ' . $color1615453171634f2 . ';
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    font-size: ' . $font_size_fl . 'px;
                    margin-left: ' . $d_margin_right . 'px;
                }
                ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .time-read span {
                    color: ' . $color1615453171634f . ';
                }
                @media (max-width: 992px) {
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                        display: inline-block '.($strict_editor ? '' : '!important').';
                        max-width: 100% !important;
                        height: 100% !important;
                    }
                }
                @media (max-width: 470px) {
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img {
                        display: inline-block '.($strict_editor ? '' : '!important').';
                        max-width: 100% !important;
                        height: auto !important;
                    }
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .p-box {
                        flex-direction: column;
                    }
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .p-box .time-date,
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .p-box .time-type,
                    ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .p-box .time-read
                     {
                        display: flex;
                        margin: 0;
                        justify-content: ' . $d_font_posi . ';
                    }
                }';
            $output .= '</style>';
            $output .= '<div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">';
            $output .= '	<h2 style="text-align:' . $select1615452416171 . ';">' . $article->title . '</h2>';
            $output .= '<div class="p-box" style="display: flex; margin-bottom:0px; justify-content: ' . $d_font_posi . ';margin-top: ' . $d_mtop . 'px;margin-bottom: ' . $d_mbottom . 'px;">';
            if($date1_hide!=1){
                $output .= '<p class="time-date">' . $date_text . $article->modified . '</p>';
            }
            if($settings->theme02_type_hide != 1) {
                $output .= '<p class="time-type">分类：' . $article->categories_title . '</p>';
            }
            if($settings->theme02_read_hide != 1) {
                $output .= '<p class="time-read">阅读：<span>' . $article->Look_number . '</span>次</p>';
            }
            $output .= '</div>';
            $output .= '   <hr style="margin-top: 0; "/>';
            $output .= '	<div class="article-content" style="color:' . $color1615443385649 . '">';
            $output .= '    ' . $article->fulltext . '';
            $output .= '	</div>';
            $output .= $this->articlePage();
            $output .= '</div>';
        }
        if ($theme == 'site03') {
            $date_text = $settings->date_text ?? '发布时间：';
            $output.='
                <style>
                    ' . $addon_id . ' .newscon p img {
                        display:inline-block;
                    }
                    ' . $addon_id . ' a {
                      text-decoration: none;
                      cursor: pointer;
                    }

                    ' . $addon_id . ' .tjxwtime {
                      color: '.$color1615453171634.';
                      font-size: '.$font_size_date.'px;
                      line-height: 32px;
                    }
                    ' . $addon_id . ' .tjxwcon {
                      color: #4b4b4b;
                      line-height: 21px;
                      font-size: 14px;
                      height: 45px;
                    }

                    ' . $addon_id . ' .dytit {
                      color: '.$color1615452944411.';
                      font-size: '.$font_size.'px;
                      line-height: 60px;
                      text-align: '.$select1615452416171.';
                    }
                    ' . $addon_id . ' .newscon {
                      color: '.$color1615443385649.';
                    }
                    ' . $addon_id . ' .xwlin {
                      border-bottom: 1px dotted #2f2f2f;
                      height: 14px;
                      line-height: 14px;
                    }
                    ' . $addon_id . ' .ssinput {
                      border: 1px solid #1171fc;
                      height: 36px;
                      width: 366px;
                      background: #ffffff;
                      color: #000000;
                      text-indent: 5px;
                      line-height: 36px;
                    }
                    ' . $addon_id . ' .ssbtn {
                      background: #1171fc;
                      border: 0px;
                      height: 38px;
                      color: #ffffff;
                      text-align: center;
                      width: 77px;
                      cursor: pointer;
                    }
                    ' . $addon_id . ' .xwlast {
                      color: #3d3d3d;
                      font-size: 14px;
                      line-height: 34px;
                      text-align: left;
                      width:50%;display:inline-block;
                      height:34px;overflow:hidden;
                    }
                    ' . $addon_id . ' .xwlast a {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwlast a:link {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwlast a:visited {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwlast a:active {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwlast a:hover {
                      color: #e40012;
                    }
                    ' . $addon_id . ' .xwnext {
                      color: #3d3d3d;
                      font-size: 14px;
                      line-height: 34px;
                      text-align: right;
                      width:50%;display:inline-block;height:34px;overflow:hidden;
                    }
                    ' . $addon_id . ' .xwnext a {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwnext a:link {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwnext a:visited {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwnext a:active {
                      color: #3d3d3d;
                    }
                    ' . $addon_id . ' .xwnext a:hover {
                      color: #e40012;
                    }
                    ' . $addon_id . ' .kzan #zan{ float:left; padding:9px; background:'.$dianzan03_color.';}
                    ' . $addon_id . ' .kzan #zanshu{ float:left; width:130px; height:47px; border:1px solid '.$dianzan03_color.'; line-height:47px; text-align:center; color:'.$dianzan03_color.'; font-size:'.$font_size03_dz.'px; cursor:pointer;}
                    ' . $addon_id . ' .kzan #zanshu span{ color:#fa1d1d; font-size:14px;}
                    ' . $addon_id . ' .nzan #zan{ background:#CCD3E4; float:left; padding:9px;}
                    ' . $addon_id . ' .nzan #zanshu{ float:left; width:130px; height:47px; border:1px solid #CCD3E4; line-height:47px; text-align:center; color:#000000; font-size:24px; cursor:pointer;}
                    ' . $addon_id . ' .nzan #zanshu span{ color:#fa1d1d; font-size:14px;}
                    ' . $addon_id . ' .dzts{ color:#333333; text-align:center; line-height:48px; font-size:14px; background:url(https://oss.lcweb01.cn/joomla/20220115/7d82f57bb118769856d2a9c9ba35e342.png) no-repeat center left; width:205px;}
                    ' . $addon_id . ' .dzts a{ color:#3b5998;}
                    ' . $addon_id . ' .tjxwtime img{display:inline-block;}

                    /****滚动条****/
                    ' . $addon_id . ' .compright{  width:100%; height:calc('.$content_height03.'px + 10px);  position:relative;margin:0 auto; }
                    ' . $addon_id . ' .compright .Container {
                      position: absolute;
                      width: 100%;
                      height:'.$content_height03.'px;

                    }
                    ' . $addon_id . ' .compright #Scroller-1 {
                      position: absolute;
                      overflow: hidden;
                      width: 100%;
                      height: '.$content_height03.'px;
                      left: 2px;
                      top: 5px;
                    }
                    ' . $addon_id . ' .compright #Scroller-1 p {
                      margin: 0; padding: 0px 0px;
                    }
                    ' . $addon_id . ' .compright .Scroller-Container {
                      position: absolute;
                      top: 0px;
                      left: 0px;
                      width:100%;
                    }
                    ' . $addon_id . ' .compright #Scrollbar-Container {
                      position: absolute;
                      top:-5px; right:0px;

                    }
                    ' . $addon_id . ' .compright .Scrollbar-Up {
                      cursor: pointer;
                      position: absolute;
                    }
                    ' . $addon_id . ' .compright .Scrollbar-Track {
                      width:14px; height: '.$content_height03.'px;
                      position: absolute;
                      top: 10px; left:0px;
                      background: transparent url(https://oss.lcweb01.cn/joomla/20220115/a0c64abe06aad21757a97534882ceff4.jpg) repeat-y center center;
                    }
                    ' . $addon_id . ' .compright .Scrollbar-Handle {
                      position: absolute;
                      width:14px; height: 14px;
                        padding-bottom:15px;
                      background:url(https://oss.lcweb01.cn/joomla/20220115/adcbc5bd407d3ea39f3c92156c26f7eb.png) no-repeat;
                      top:15px; left:0px;
                    }
                    ' . $addon_id . ' .compright .Scrollbar-Down {
                      cursor: pointer;
                      position: absolute;
                      top: 10px;
                    }
                    @media (max-width: 470px) {
                        ' . $addon_id . ' .kzan #zanshu{ width:100px;}
                    }

                </style>
            ';
            $output .= '
                <div style="width:100%;margin:0 auto;" >
                    <div class="dytit" style="margin-top:20px;font-family: '. $font_style_type1.'; ">' . $article->title . '</div>
                    <div class="tjxwtime">'; if($date1_hide!=1){ $output .=''.$date_text . $article->modified.'&nbsp;&nbsp;&nbsp;&nbsp;'; } $output .= '作者：'.$dianzan03_autor.'&nbsp;&nbsp;话题：&nbsp;&nbsp;<img src="https://oss.lcweb01.cn/joomla/20220115/3c9b416a061f3d915d6c672d11b0ea67.png" style="vertical-align:middle" />&nbsp;<span class="dznum">'.$article->fabulous.'</span></div>

                    <div class="tjxwtime">声明：'.$article->introtext.'</div>

                    <div class="compright" style="margin-top:10px;">
                        <div class="Container">
                            <div id="Scroller-1">
                                <div class="Scroller-Container">

                                    <div>
                                        <div class="newscon article-content">
                                            ' . $article->fulltext . '
                                        </div>
                                        <div class="xwlin"></div>
                                        <div class="dba" style="width:450px;margin: 30px auto;display:block;height:50px;">
                                            <table  border="0" cellspacing="0" cellpadding="0" align="left" >
                                                <tr>
                                                  <td ><div  class="kzan" onclick="kzan()"  id="kz">
                                                    <div id="zan"><img src="https://oss.lcweb01.cn/joomla/20220115/b53cf218169357838b97d096366c9748.png" /></div>
                                                    <div id="zanshu"><font>
                                                      点 赞                    </font><span>（<font id="pra">'.$article->fabulous.'</font>）</span></div>
                                                    </div></td>
                                                  <td  id="dzts" class="dzts">点赞，为作者喝彩&nbsp;<a onclick="dzts()">我知道了</a></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div id="Scrollbar-Container">
                            <div class="Scrollbar-Track">
                                <div class="Scrollbar-Handle">
                                </div>
                            </div>
                        </div>
                    </div>';
            if($page_button==0){
                $output .= '<div style="width:100%;margin:15px auto;">';
                if ($article->on) {
                    $article_on = JwPageFactoryBase::getArticleById2($article->on, $catid_id);
                    $output .= '<a class="xwlast" href="' . $on . '">'.$up_page_text.':'.$article_on->title.'</a>';
                }else{
                    $output .= '<a class="xwlast" href="' . $on . '">'.$up_page_text.':暂无</a>';
                }
                if ($article->down) {
                    $article_down = JwPageFactoryBase::getArticleById2($article->down, $catid_id);
                    $output .= '<a class="xwnext" href="' . $down . '">'.$next_page_text.':'.$article_down->title.'</a>';
                }else{
                    $output .= '<a class="xwnext" href="' . $down . '">'.$next_page_text.':暂无</a>';
                }
                $output .= '</div>';

            }
            $output .= '</div>';
            $config = new JConfig();
            $urlpath = $config->jzt_url;
            $output .= '
                    <script src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/js/jsscroller.js"></script>
                    <script src="/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/js/jsscrollbar.js"></script>
                    <script type="text/javascript" language="javascript">
                        var scroller  = null;
                        var scrollbar = null;
                        $(function () {
                            scroller  = new jsScroller(document.getElementById("Scroller-1"), 400, 200);
                            scrollbar = new jsScrollbar (document.getElementById("Scrollbar-Container"), scroller, false);
                        })
                        function kzan(){
                            jQuery.ajax({
                                type: "POST",
                                url: "' . $urlpath . '/api/Message/fabulous",
                                dataType: "json",
                                data: {
                                    "id" :' . $article_id . ',
                                    "pri" :1,
                                },
                                success: function (res) {
                                    console.log(res.data);
                                    if(res.data>0){
                                        $("' . $addon_id . ' #kz").removeClass();
                                        $("' . $addon_id . ' #kz").addClass("nzan");
                                        $("' . $addon_id . ' #kz").attr("onclick","nzan()");
                                        $("' . $addon_id . ' #zanshu font").html("取消");
                                        var pr=$("' . $addon_id . ' #pra").html();
                                        $("' . $addon_id . ' #pra").html(res.data);
                                        $("' . $addon_id . ' .dznum").html(res.data);
                                    }
                                }
                            });
                        }
                        function nzan(){
                            jQuery.ajax({
                                type: "POST",
                                url: "' . $urlpath . '/api/Message/fabulous",
                                dataType: "json",
                                data: {
                                    "id" :' . $article_id . ',
                                    "pri" :-1,
                                },
                                success: function (res) {
                                    console.log(res.data);
                                    if(res.data>0){
                                          $("' . $addon_id . ' #kz").removeClass();
                                          $("' . $addon_id . ' #kz").addClass("kzan");
                                          $("' . $addon_id . ' #kz").attr("onclick","kzan()");
                                          $("' . $addon_id . ' #zanshu > font").html("点赞");
                                          var pr=$("' . $addon_id . ' #pra").html();
                                          $("' . $addon_id . ' #pra").html(res.data);
                                          $("' . $addon_id . ' .dznum").html(res.data);

                                    }
                                }
                            });
                        }
                        function dzts(){
                           $("' . $addon_id . ' #dzts").hide();
                        }
                    </script>
                ';
        }

        if ($theme == 'site04') {
            $output.='
                <style>
                    ' . $addon_id . ' .fa{font-family: "Font Awesome 5 Free";}
                    ' . $addon_id . ' .container_content {
                        clear: both;
                    }
                    ' . $addon_id . '.postContent {
                        padding-top: 40px;
                    }
                    ' . $addon_id . ' .news.mlistpost .content_wrapper .postInfo {
                        text-align: center;
                    }
                    ' . $addon_id . ' .postInfo .title {
                        color: '.$color1615452944411.';
                        font-size: '.$font_size.'px;
                        transition: all .3s ease-out 0s;
                        text-align:'.$select1615452416171.';
                    }
                    ' . $addon_id . ' .postInfo .usetdate {
                        font-size: '.$font_size_date.'px;
                        color:'.$color1615453171634.';
                        line-height: 24px;
                    }
                    ' . $addon_id . ' .postInfo .description {
                        margin-top: 20px;
                        font-size: '.$intro_fontsize04.'px;
                        line-height: 24px;
                        margin-bottom: 24px;
                        color: '.$intro_color04.';
                    }
                    ' . $addon_id . ' .post .postbody {
                        position: relative;
                        z-index: 0;
                    }
                    ' . $addon_id . ' .postbody {
                        margin-bottom: 60px;
                    }
                    ' . $addon_id . ' .postbody {
                        color: '.$color1615443385649.';
                    }
                    ' . $addon_id . ' .postbody hr {
                        margin: 20px 0;
                        border: none;
                        border-top: 1px dotted rgba(170, 170, 170, 0.2);
                    }
                    ' . $addon_id . ' .postbody p {
                        line-height: 24px;
                    }
                    ' . $addon_id . ' .postbody img {
                        display: inline-block;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev {
                        float: left;
                        padding-left: 85px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next {
                        transition: all 0.36s ease;
                        position: relative;
                        width: 50%;
                        height: '.$site04_fy_height.'px;
                        overflow: hidden;
                        float: left;
                        padding: 10px;
                        box-sizing: border-box;
                        background: '.$pageBgColor.';
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap {
                        right: 15px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap {
                        transition: all 0.36s ease;
                        opacity: 0;
                        width: 80px;
                        height: auto;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap div, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap div {
                        display: none;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap img,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap img {
                        width: 100%;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap {
                        float: left;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap {
                        width: calc( 100% - 100px );
                        position: relative;
                        top: 0;
                        top: 50%;
                        transform: translateY(-50%);
                    }

                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        display: none;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        height: 30px;
                        font-size: 13px;
                        line-height: 30px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .title, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        transition: all 0.36s ease;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: block;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details {
                        border-right: 1px solid rgba(170, 170, 170, 0.2);
                        left: 22px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .details {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 40px;
                        height: 16px;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details:before {
                        right: 25px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details:before,' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details:after {

                        transition: all 0.36s ease;
                        color: #999;
                        position: absolute;
                        top: 50%;
                        font-size: 16px;
                        transform: translateY(-50%);
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .details:after {
                        right: -30px;
                        opacity: 0;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap {
                        left: 15px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap {
                        transition: all 0.36s ease;
                        opacity: 0;
                        width: 80px;
                        height: auto;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap div, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap div {
                        display: none;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .img-wrap img,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .img-wrap img {
                        width: 100%;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap {
                        float: right;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        text-align: right;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .title {
                        font-size: 16px;
                        line-height: 20px;
                        color: '.$pageColor.';
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,' . $addon_id . ' news.mlistpost .tabBtn .post-next .infor-wrap .title, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        transition: all 0.36s ease;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: block;
                    }

                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .title, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        text-align: right;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        height: 30px;
                        font-size: 13px;
                        line-height: 30px;
                        display: none;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .title, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                        transition: all 0.36s ease;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: block;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn::after {
                        content: "";
                        display: block;
                        clear: both;
                    }

                    ' . $addon_id . ' .listContent_post h3 {
                        font-size: 14px;
                        padding: 30px 0;
                    }
                    ' . $addon_id . ' .listContent_post > .item_tags {
                        padding: 0;
                        margin-bottom: 30px;
                        border-top: none;
                    }
                    ' . $addon_id . ' .item_tags {
                        clear: both;
                        height: 28px;
                        border-top: 1px solid rgba(170, 170, 170, 0.2);
                    }
                    ' . $addon_id . ' .item_tags > a {
                        float: left;
                        margin-right: 10px;
                        padding: 0 14px;
                        line-height: 26px;
                        border: 1px solid rgba(170, 170, 170, 0.2);
                    }
                    ' . $addon_id . ' .item_tags > a, .container_category > a {
                        border: 1px solid rgba(170, 170, 170, 0.2);
                        color: #828282;
                        background-color: transparent;
                    }
                    ' . $addon_id . ' .item_tags > a, .container_category > a, .imagelink .owl-nav .owl-prev, .postSlider .owl-nav .owl-prev, .mlist .owl-nav .owl-prev, .imagelink .owl-nav .owl-next, .postSlider .owl-nav .owl-next, .mlist .owl-nav .owl-next, .imagelink .owl-nav .owl-prev:hover .iconfont, .postSlider .owl-nav .owl-prev:hover .iconfont, .mlist .owl-nav .owl-prev:hover .iconfont,  .imagelink .owl-nav .owl-next:hover .iconfont, .postSlider .owl-nav .owl-next:hover .iconfont, .mlist .owl-nav .owl-next:hover .iconfont, .team .content_list .item_block .item_box .item_wrapper,.news.mlistpost .content_list .item_block .item_box .item_wrapper, .team.mlistpost .tabBtn .post-prev .img-wrap,.team.mlistpost .tabBtn .post-prev .infor-wrap,.team.mlistpost .tabBtn .post-next .infor-wrap, .postSlider .tab_button .item_img img, body #header, body #headTop #logo img, .ff_indexPage .mlist .content_wrapper .more, .imagelink .content_list .item_block .item_box {
                            transition: all 0.36s ease;
                        }
                    ' . $addon_id . ' .item_tags::after {
                        content: "";
                        display: block;
                        clear: both;
                    }
                    ' . $addon_id . ' .news.mlistpost .listContent_post .content_wrapper {
                        position: relative;
                        margin-right: -15px;
                    }
                    ' . $addon_id . ' #sitecontent .content .content_list {
                        position: relative;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .news.mlistpost .listContent_post .content_list::after {
                        content: "";
                        display: block;
                        clear: both;
                    }

                    ' . $addon_id . ' .fa-angle-right, .fa-angle-down , .fa-angle-left{
                        padding-left: 10px;
                    }

                    ' . $addon_id . ' .clear {
                        clear: both;
                    }
                    ' . $addon_id . ' #postWrapper::after {
                        content: "";
                        display: block;
                        clear: both;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next .details {
                        right: 22px;
                        border-left: 1px solid rgba(170, 170, 170, 0.2);
                        color:#999;
                        text-align:right;
                    }

                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev:not(.empty):hover, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next:not(.empty):hover {
                        background: '.$pageBgColorhover.';
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev:not(.empty):hover .img-wrap,' . $addon_id . ' .news.mlistpost .tabBtn .post-next:not(.empty):hover .img-wrap {
                        opacity: 1;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev:hover .infor-wrap .title,' . $addon_id . ' .news.mlistpost .tabBtn .post-next:hover .infor-wrap .title {
                        color: '.$pageColorhover.';
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev {
                        float: left;
                        padding-left: 85px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next {
                        float: right;
                        padding-right: 85px;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev:hover .details:after{
                        color: #fff!important;
                    }
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-next:hover .details:after {
                        color: #fff!important;
                    }

                    ' . $addon_id . ' .conTabBtn .post-prev:hover .details:before{color:#fff!important;}
                    ' . $addon_id . ' .conTabBtn .post-next:hover .details{color:#fff!important;}
                    ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev:hover .details, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next:hover .details {
                        border-color: rgba(170, 170, 170, 0.2);
                    }

                    ' . $addon_id . ' p{margin:0;}
                    @media (max-width: 670px) {
                        ' . $addon_id . ' .news.mlistpost .tabBtn .post-prev, ' . $addon_id . ' .news.mlistpost .tabBtn .post-next{width:100%!important;height:60px!important;}
                        ' . $addon_id . ' .postInfo .title{
                            font-size:16px!important;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="content">
                    <div class="mlistpost news module" data-thumb="">
                        <div class="module_container">
                            <div class="container_content">
                                <div class="content_wrapper">
                                    <div id="postWrapper">
                                        <div class="postContent">
                                            <div class="postInfo">
                                                <div class="mainInfor">
                                                    <p class="title" style="font-family: '. $font_style_type1.'; ">' . $article->title . '</p>';
                                                    if($date1_hide!=1){
                                                        $output .= '<p class="usetdate">
                                                        ' . $article->modified . '
                                                    </p>';}
                                                $output .= '</div>
                                                <div class="description">
                                                    <p>'.$article->introtext.'</p>
                                                </div>
                                            </div>
                                            <div class="postbody article-content">
                                                <hr>
                                                ' . $article->fulltext . '
                                            </div>';
            if($page_button==0){
                $output .= '<div class="conTabBtn tabBtn">';
                if ($article->on) {
                    $article_on = JwPageFactoryBase::getArticleById2($article->on, $catid_id);
                    $img=json_decode($article_on->images);

                    $output .= '
                                                        <a href="' . $on . '" class="post-prev">
                                                            <div class="img-wrap">
                                                                <img src="'.$img->image_intro.'" alt="">
                                                            </div>
                                                            <div class="infor-wrap">
                                                                <span class="title">'.$article_on->title.'</span>
                                                            </div>
                                                            <i class="fa fa-angle-left details"></i>
                                                            <div class="tabMask"></div>
                                                        </a>
                                                    ';
                }else{
                    $output .= '<a href="javascript:;" class="post-prev">
                                                            <div class="img-wrap">
                                                            </div>

                                                            <div class="infor-wrap">
                                                                <span class="title"></span>
                                                            </div>
                                                            <i class="fa fa-angle-left details"></i>
                                                            <div class="tabMask"></div>
                                                        </a>';
                }

                if ($article->down) {
                    $article_down = JwPageFactoryBase::getArticleById2($article->down, $catid_id);
                    $img=json_decode($article_down->images);

                    $output .= '
                                                        <a href="' . $down . '" class="post-next">
                                                            <div class="img-wrap">
                                                                <img src="'.$img->image_intro.'" alt="">
                                                            </div>
                                                            <div class="infor-wrap">
                                                                <span class="title">'.$article_down->title.'</span>
                                                            </div>
                                                            <i class="fa fa-angle-right details"></i>
                                                            <div class="tabMask"></div>
                                                        </a>
                                                    ';
                }else{
                    $output .= '
                                                        <a href="javascript:;" class="post-next">
                                                            <div class="img-wrap">
                                                            </div>
                                                            <div class="infor-wrap">
                                                                <span class="title"></span>
                                                            </div>
                                                            <i class="fa fa-angle-right details"></i>
                                                            <div class="tabMask"></div>
                                                        </a>
                                                    ';
                }

                $output .= '
                                                </div>';
            }
            $output .= '</div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            ';
        }
        $output .= '<script language="javascript">';
        if($tdk_button == 'yes')
        {
            $output .= ' if ($(\'meta[name="keywords"]\').length === 0) {
                $("head").append(\'<meta name="keywords" content="1">\');
            }';
            $output .= ' if ($(\'meta[name="description"]\').length === 0) {
                $("head").append(\'<meta name="description" content="1">\');
            }
            ';
            // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';

            if($article->title_name)
            {
                $output .= '$(\'title\').text("' . mb_substr(strip_tags($article->title_name), 0, 80, 'UTF-8') . '");';
            }
            else
            {
                $output .= '$(\'title\').text("' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';
            }
            if($article->keywords)
            {
                $output .= '$(\'meta[name="keywords"]\').attr("content","' . mb_substr(strip_tags($article->keywords), 0, 100, 'UTF-8') . '");';
            }
            else
            {
                $kk = mb_substr(strip_tags($article->introtext), 0, 100, 'UTF-8');
                if($kk)
                {
                    $output .= '$(\'meta[name="keywords"]\').attr("content","' . $kk . '");';
                }
                else
                {
                    $output .= '$(\'meta[name="keywords"]\').attr("content","' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';
                }
            }
            if($article->description)
            {
                $output .= '$(\'meta[name="description"]\').attr("content","' . mb_substr(strip_tags($article->description), 0, 200, 'UTF-8') . '");';
            }
            else
            {
                $dd = mb_substr(trim(strip_tags(str_replace("&nbsp;","",$article->fulltext))), 0, 200, 'UTF-8');
                if($dd)
                {
                    $output .= '$(\'meta[name="description"]\').attr("content","' . $dd . '");';
                }
                else
                {
                    $output .= '$(\'meta[name="description"]\').attr("content","' . mb_substr(strip_tags($article->title), 0, 80, 'UTF-8') . '");';
                }
            }

        }
        $output .= '</script>';

        return $output;
    }

    //引用js文件，同时作用于编辑器和预览页
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.countdown.min.js'

        );
        return $scripts;
    }

    //引用css文件，同时作用于编辑器和预览页
    public function stylesheets()
    {
        $style_sheet = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css'

        );
        return $style_sheet;
    }

    //在预览页面中使用的JS脚本
    public function js()
    {
        $js = '';
        return $js;
    }

    //在预览页面中使用的css样式
    public function css()
    {
        $css = '';
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "jwpf-addon-"+data.id;
        var theme = data.theme ? data.theme : "site01";
        var biaoqian_type = data.biaoqian_type ? data.biaoqian_type : "no";
        var title_font_weight = data.title_font_weight || 0;
        var date1_hide = data.date1_hide || 0;
        // 富文本p标签上边距
        var content_p_top = data.content_p_top || 0;
        // 富文本p标签下边距
        var content_p_bot = data.content_p_bot || 16;
        #>
        <style type="text/css">
            @font-face {
                font-family: "Founder_small_label_Song_Simplified";
                src: url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.woff") format("woff"),
                     url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.ttf") format("truetype"),
                     url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/5cbc27d3823fa.svg") format("svg");
            }
            @font-face {
                font-family: "imitate_song";
                src: url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.woff") format("woff"),
                     url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.ttf") format("truetype"),
                     url("/components/com_jwpagefactory/addons/JZT_ADDON_LSGA_4D2L2F02N8/assets/css/1653883165.svg") format("svg");
            }
            #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2,
            #{{ addonId }} .dytit,
            #{{ addonId }} .postInfo .title {
                <# if (title_font_weight == 1) { #>
                font-weight: bold;
                <# } #>
            }
            #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .article-content p {
                margin-top: {{content_p_top}}px;
                margin-bottom: {{content_p_bot}}px;
            }
        </style>
        <# // 上一页 下一页 公共方法
        function articlePage() { 
            // 翻页按钮宽度
            var page_width = data.page_width || 48;
            // 翻页按钮高度
            var page_height = data.page_height || 40;
        #>
            <style type="text/css">
                #{{ addonId }} .btnBox,
                #{{ addonId }} .btnBoxt {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                }
                #{{ addonId }} .btnBox a {
                    width: 120px;
                    height: 40px;
                    border: 1px solid {{data.pageBorderColor}};
                    color: {{data.pageColor}};
                    font-size: {{page_a_font}}px;
                    background:{{data.pageBgColor}};
                    text-align: center;
                    line-height: 40px;
                    text-decoration: none;
                }
                #{{ addonId }} .btnBox a:hover {
                    border: 1px solid {{data.pageBorderColorhover}};
                    color: {{data.pageColorhover}};
                    background:{{data.pageBgColorhover}};
                    <# if(data.page_a_line == 1) { #>
                    text-decoration: underline;
                    <# } else { #>
                    text-decoration: none;
                    <# } #>
                }
                #{{ addonId }} .btnBoxt a {
                    width: {{page_width}}%;
                    height: {{page_height}}px;
                    color: {{data.pageColor}};
                    font-size: {{page_a_font}}px;
                    background:{{data.pageBgColor}};
                    text-align: left;
                    line-height: {{page_height}}px;
                    text-decoration: none;
                }
                #{{ addonId }} .btnBoxt a:hover {
                    color: {{data.pageColorhover}};
                    font-size: {{page_a_font}}px;
                    background:{{data.pageBgColorhover}};
                    <# if(data.page_a_line == 1) { #>
                    text-decoration: underline;
                    <# } else { #>
                    text-decoration: none;
                    <# } #>
                }
                #{{ addonId }} .btnBoxt a:nth-child(2) {
                    text-align:right;
                }
                /* 开启翻页左右箭头 样式 */
                #{{ addonId }} .btnBoxt a .a-text {
                    width: calc(100% - 35px);
                    display: inline-block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    vertical-align: middle;
                }
                #{{ addonId }} .btnBoxt a:hover .a-text {
                    <# if(data.page_a_line == 1) { #>
                    text-decoration: underline;
                    <# } #>
                }
                #{{ addonId }} .btnBoxt a .a-icon-box {
                    width: 20px;
                    height: 20px;
                    position: relative;
                    margin-right: 10px;
                    display: inline-block;
                    vertical-align: middle;
                }
                #{{ addonId }} .btnBoxt a:nth-child(2) .a-icon-box {
                    margin-left: 10px;
                    margin-right: 0px;
                }
                #{{ addonId }} .btnBoxt a .a-icon-box .a-icon {
                    width: 100%;
                    height: 100% !important;
                    object-fit: scale-down;
                    opacity: 1;
                    transition: 0.5s;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                #{{ addonId }} .btnBoxt a .a-icon-box .a-icon.a-icon-hover {
                    opacity: 0;
                }
                #{{ addonId }} .btnBoxt a:hover .a-icon-box .a-icon {
                    opacity: 0;
                }
                #{{ addonId }} .btnBoxt a:hover .a-icon-box .a-icon.a-icon-hover {
                    opacity: 1;
                }
                /* 返回列表样式 */
                #{{ addonId }} .page-back-box {
                    font-size: 16px;
                    line-height: 16px;
                    color: #4e4e4e;
                    text-align: right;
                    padding: 84px 0 22px;
                    border-bottom: 1px solid #dcdfdd;
                    margin-bottom: 36px;
                }
                #{{ addonId }} .page-back {
                    color: #4e4e4e;
                }
            </style>
            <# if(data.page_button == 0) { #>
                <# if(data.page_button_back == 1) { #>
                    <div class="page-back-box">
                        <a href="#" class="page-back">返回列表</a>
                    </div>
                <# } #>
                <# if(data.page_dttitle == 1) { #>
                    <div class="btnBoxt">
                        <a href="#">
                            <# if(data.page_a_icon_show == 1) { #>
                            <span class="a-icon-box">
                                <# if(data.page_a_icon_prev) { #>
                                <img class="a-icon" src=\'{{ data.page_a_icon_prev }}\' />
                                <# } #>
                                <# if(data.page_a_icon_prev_hover) { #>
                                <img class="a-icon a-icon-hover" src=\'{{ data.page_a_icon_prev_hover }}\' />
                                <# } #>
                            </span>
                            <span class="a-text">{{data.up_page_text}}标题名称标题标题</span>
                            <# } else { #>
                            {{data.up_page_text}}标题名称标题标题
                            <# } #>
                        </a>
                        <a href="#">
                            <# if(data.page_a_icon_show == 1) { #>
                            <span class="a-text">{{data.next_page_text}}标题名称标题标题</span>
                            <span class="a-icon-box">
                                <# if(data.page_a_icon_next) { #>
                                <img class="a-icon" src=\'{{ data.page_a_icon_next }}\' />
                                <# } #>
                                <# if(data.page_a_icon_next_hover) { #>
                                <img class="a-icon a-icon-hover" src=\'{{ data.page_a_icon_next_hover }}\' />
                                <# } #>
                            </span>
                            <# } else { #>
                            {{data.next_page_text}}标题名称标题标题
                            <# } #>
                        </a>
                    </div>
                <# }else{ #>
                    <div class="btnBox">
                        <a href="#">{{data.up_page_text}}</a>
                        <a href="#">{{data.next_page_text}}</a>
                    </div>
                <# } #>
            <# } #>
        <# } #>
        <# if(biaoqian_type=="yes") { #>
            <div>
                <span>首页 / </span>
                <span>产品中心</span>
            </div>
        <# } #>
        <# if(theme=="site01") {
            var date_text = data.date_text || "编辑：";
            var page_a_font = data.page_a_font || 14;

        #>
            <style type="text/css">
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT {
                    color:{{{ data.color1615452944411 }}};
                }
            </style>
            <div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">
                <h2 style="text-align:{{{ data.select1615452416171 }}};font-size: {{data.font_size}}px;font-family: {{data.font_style_type1}};">示例文章标题</h2>
                <hr />
                <# if(date1_hide!=1){ #>
                <p style="text-align:{{data.select1646020218}}; display:block; padding-right:10px; color:{{{ data.color1615453171634 }}};font-size: {{data.font_size_date}}px">{{{date_text}}}2021年3月11日 16:59</p>
                <# } #>
                <div style="color:{{{ data.color1615443385649 }}};margin-top:{{{ data.content_top }}}px">
                    示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文。
                </div>
                <# articlePage(); #>
            </div>
        <# } #>
        <# if(theme=="site02") {
            var date_text = data.date_text || "日期：";
            var page_a_font = data.page_a_font || 14;
        #>
            <style type="text/css">
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT {
                    color:{{{ data.color1615452944411 }}};
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2 {
                    text-align:{{{ data.select1615452416171 }}};
                    font-size: {{data.font_size}}px;
                    font-family: {{data.font_style_type1}};
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl {
                    display: flex;
                    justify-content: {{{data.d_font_posi}}};
                    margin-top: {{{data.d_mtop}}}px;
                    margin-bottom: {{{data.d_mbottom}}}px;
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-date {
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    color: {{{ data.color1615453171634 }}};
                    font-size: {{data.font_size_date}}px;
                    margin-right: {{{data.d_margin_right}}}px;
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-type {
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    color: {{{ data.color1615453171634fl }}};
                    font-size: {{data.font_size_fl}}px;
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-read {
                    color: {{{data.color1615453171634f2}}};
                    text-align: right;
                    display: block;
                    padding-right: 10px;
                    font-size: {{data.font_size_fl}}px;
                    margin-left: {{{data.d_margin_right}}}px;
                }
                #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-read span {
                    color: {{{data.color1615453171634f}}};
                }
                @media (max-width: 480px) {
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl {
                        flex-direction: column;
                    }
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-date,
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-type,
                    #{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT .title_fl .time-read
                    {
                        display: flex;
                        margin: 0;
                        justify-content: {{{data.d_font_posi}}};
                    }
                }
            </style>
            <div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">
                <h2 style="">示例文章标题</h2>
                <div class="title_fl" style="">
                <# if(date1_hide!=1){ #>
                    <p class="time-date" style="">{{{date_text}}}2020-12-16</p>
                <# } #>
                    <# if(data.theme02_type_hide != 1) { #>
                    <p class="time-type" style="">分类：高端官网及营销类网站案例</p>
                    <# } #>
                    <# if(data.theme02_read_hide != 1) { #>
                    <p class="time-read" style="">阅读：<span style ="">0</span>次</p>
                    <# } #>
                </div>
                <hr style="margin-top: 0px; " />
                <div style=" margin-top: 0px; color:{{{ data.color1615443385649 }}}">
                    示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文。
                </div>
                <# articlePage(); #>
            </div>
        <# } #>


        <# if(theme=="site03") {
            var date_text = data.date_text || "发布时间：";
        #>
            <style type="text/css">
                #{{ addonId }} .tjxwtime {
                    color: {{{ data.color1615453171634 }}};
                    font-size: {{{ data.font_size_date }}}px;
                    line-height: 32px;
                }
                #{{ addonId }} .tjxwcon {
                    color: #4b4b4b;
                    line-height: 21px;
                    font-size: 14px;
                    height: 45px;
                }

                #{{ addonId }} .dytit {
                    color: {{{ data.color1615452944411 }}};
                    font-size: {{{ data.font_size }}}px;
                    line-height: 30px;
                    text-align: {{{ data.select1615452416171 }}};
                }
                #{{ addonId }} .newscon {
                    color: #2f2f2f;
                    font-size: 14px;
                    line-height: 34px;
                }
                #{{ addonId }} .xwlin {
                    border-bottom: 1px dotted #2f2f2f;
                    height: 14px;
                    line-height: 14px;
                }
                #{{ addonId }} .ssinput {
                    border: 1px solid #1171fc;
                    height: 36px;
                    width: 366px;
                    background: #ffffff;
                    color: #000000;
                    text-indent: 5px;
                    line-height: 36px;
                }
                #{{ addonId }} .ssbtn {
                    background: #1171fc;
                    border: 0px;
                    height: 38px;
                    color: #ffffff;
                    text-align: center;
                    width: 77px;
                    cursor: pointer;
                }
                #{{ addonId }} .xwlast {
                    color: #3d3d3d;
                    font-size: 14px;
                    line-height: 34px;
                    text-align: left;
                    width: 48%;
                }
                #{{ addonId }} .xwlast a {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwlast a:link {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwlast a:visited {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwlast a:active {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwlast a:hover {
                    color: #e40012;
                }
                #{{ addonId }} .xwnext {
                    color: #3d3d3d;
                    font-size: 14px;
                    line-height: 34px;
                    text-align: right;
                    width: 48%;
                }
                #{{ addonId }} .xwnext a {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwnext a:link {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwnext a:visited {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwnext a:active {
                    color: #3d3d3d;
                }
                #{{ addonId }} .xwnext a:hover {
                    color: #e40012;
                }
                #{{ addonId }} .kzan #zan{ float:left; padding:9px; background:#0051ca;}
                #{{ addonId }} .kzan #zanshu{ float:left; width:130px; height:47px; border:1px solid #0051ca; line-height:47px; text-align:center; color:#0342a1; font-size:24px; font-family:"微软雅黑"; cursor:pointer;}
                #{{ addonId }} .kzan #zanshu span{ color:#fa1d1d; font-size:14px;}
                #{{ addonId }} .nzan #zan{ background:#CCD3E4; float:left; padding:9px;}
                #{{ addonId }} .nzan #zanshu{ float:left; width:130px; height:47px; border:1px solid #CCD3E4; line-height:47px; text-align:center; color:#000000; font-size:24px; font-family:"微软雅黑"; cursor:pointer;}
                #{{ addonId }} .nzan #zanshu span{ color:#fa1d1d; font-size:14px;}
                #{{ addonId }} .dzts{ color:#333333; text-align:center; line-height:48px; font-size:14px; background:url(https://oss.lcweb01.cn/joomla/20220115/7d82f57bb118769856d2a9c9ba35e342.png) no-repeat center left; width:205px;}
                #{{ addonId }} .dzts a{ color:#3b5998;}
                #{{ addonId }} .tjxwtime img{display:inline-block;}

                /****滚动条****/
                #{{ addonId }} .compright{  width:100%; height:360px;  position:relative;margin:0 auto; }
                #{{ addonId }} .compright .Container {
                  position: absolute;
                  width: 100%;
                  height:350px;

                }
                #{{ addonId }} .compright #Scroller-1 {
                  position: absolute;
                  overflow: hidden;
                  width: 100%;
                  height: 350px;
                  left: 2px;
                  top: 5px;
                }
                #{{ addonId }} .compright #Scroller-1 p {
                  margin: 0; padding: 0px 0px;
                }
                #{{ addonId }} .compright .Scroller-Container {
                  position: absolute;
                  top: 0px;
                  left: 0px;


                }
                #{{ addonId }} .compright #Scrollbar-Container {
                  position: absolute;
                  top:-5px; right:0px;

                }
                #{{ addonId }} .compright .Scrollbar-Up {
                  cursor: pointer;
                  position: absolute;
                }
                #{{ addonId }} .compright .Scrollbar-Track {
                  width:14px; height: 410px;
                  position: absolute;
                  top: 10px; left:0px;
                  background: transparent url(https://oss.lcweb01.cn/joomla/20220115/a0c64abe06aad21757a97534882ceff4.jpg) repeat-y center center;
                }
                #{{ addonId }} .compright .Scrollbar-Handle {
                  position: absolute;
                  width:14px; height: 14px;
                 padding-bottom:15px;
                  background:url(https://oss.lcweb01.cn/joomla/20220115/adcbc5bd407d3ea39f3c92156c26f7eb.png) no-repeat;
                  top:15px; left:0px;
                }
                #{{ addonId }} .compright .Scrollbar-Down {
                  cursor: pointer;
                  position: absolute;
                  top: 10px;
                }
            </style>
            <div style="width:100%;margin:0 auto;" >
                <div class="dytit" style="font-family: {{data.font_style_type1}};">示例文章标题</div>

                <div class="tjxwtime">
                    <# if(date1_hide!=1){ #>{{{date_text}}}2016-04-24&nbsp;&nbsp;&nbsp;&nbsp;<# } #>
                作者：&nbsp;&nbsp;话题：&nbsp;&nbsp;<img src="https://oss.lcweb01.cn/joomla/20220115/3c9b416a061f3d915d6c672d11b0ea67.png" style="vertical-align:middle" />&nbsp;3</div>

                <div class="tjxwtime">声明：个人观点，喜分享···</div>

                <div class="compright" style="margin-top:10px;">
                    <div class="Container">
                        <div id="Scroller-1">
                            <div class="Scroller-Container">

                                <div>
                                    <div class="newscon" style="color:{{{ data.color1615443385649 }}}">
                                      示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文。
                                      示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文。
                                    </div>
                                    <div class="xwlin"></div>
                                    <div style="width:450px;margin: 30px auto;display:block;height:50px;">
                                        <table  border="0" cellspacing="0" cellpadding="0" align="left" >
                                            <tr>
                                              <td ><div  class="kzan" onclick="kzan()"  id="kz">
                                                <div id="zan"><img src="https://oss.lcweb01.cn/joomla/20220115/b53cf218169357838b97d096366c9748.png" /></div>
                                                <div id="zanshu"><font>
                                                  点 赞                    </font><span>（<font id="pra">3</font>）</span></div>
                                                </div></td>
                                              <td  id="dzts" class="dzts">点赞，为作者喝彩&nbsp;<a onclick="dzts()">我知道了</a></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="Scrollbar-Container">
                        <div class="Scrollbar-Track">
                            <div class="Scrollbar-Handle">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <# if(data.page_button==0) { #>
                <div style="width:100%;margin:10px auto;display: flex;justify-content: space-between;">
                    <div class="xwlast">上一篇：暂无</div>
                    <div class="xwnext">下一篇：<a href="#">测试测试</a>
                    </div>
                </div>
            <# } #>

        <# } #>

        <# if(theme=="site04") { #>
            <style type="text/css">
                #{{ addonId }} .fa{font-family: "Font Awesome 5 Free";}
                #{{ addonId }} .container_content {
                    clear: both;
                }
                #{{ addonId }} .postContent {
                    padding-top: 40px;
                }
                #{{ addonId }} .news.mlistpost .content_wrapper .postInfo {
                    text-align: center;
                }
                #{{ addonId }} .postInfo .title {
                    color: {{{ data.color1615452944411 }}};
                    font-size: {{{ data.font_size }}}px;
                    transition: all .3s ease-out 0s;
                    /*line-height: 20px;*/
                    text-align:{{{data.select1615452416171}}};
                }
                #{{ addonId }} .postInfo .usetdate {
                    font-size: {{{ data.font_size_date }}}px;
                    color:{{{data.color1615453171634}}};
                    line-height: 24px;
                }
                #{{ addonId }} .postInfo .description {
                    margin-top: 20px;
                    font-size: {{{data.intro_fontsize04}}}px;
                    line-height: 24px;
                    margin-bottom: 24px;
                    color: {{{data.intro_color04}}};
                }
                #{{ addonId }} .post .postbody {
                    position: relative;
                    z-index: 0;
                }
                #{{ addonId }} .postbody {
                    margin-bottom: 60px;
                }
                #{{ addonId }} .postbody {
                    font-size: 13px;
                    color:{{{data.color1615443385649}}};
                }
                #{{ addonId }} .postbody hr {
                    margin: 20px 0;
                    border: none;
                    border-top: 1px dotted rgba(170, 170, 170, 0.2);
                }
                #{{ addonId }} .postbody p {
                    line-height: 24px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev {
                    float: left;
                    padding-left: 85px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev, #{{ addonId }} .news.mlistpost .tabBtn .post-next {
                    transition: all 0.36s ease;
                    position: relative;
                    width: 50%;
                    height: {{{data.site04_fy_height}}}px;
                    overflow: hidden;
                    float: left;
                    padding: 10px;
                    box-sizing: border-box;
                    background: {{{data.pageBgColor}}};
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap {
                    right: 15px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap {
                    transition: all 0.36s ease;
                    opacity: 0;
                    width: 80px;
                    height: auto;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap div, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap div {
                    display: none;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap img, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap img {
                    width: 100%;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap {
                    float: left;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap {
                    width: 250px;
                    position: relative;
                    top: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title {
                    font-size: 16px;
                    line-height: 20px;
                    color: {{{data.pageColor}}};
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,#{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title,#{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    transition: all 0.36s ease;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: block;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,#{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    display: none;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle,#{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    height: 30px;
                    font-size: 13px;
                    line-height: 30px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    transition: all 0.36s ease;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: block;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details {
                    border-right: 1px solid rgba(170, 170, 170, 0.2);
                    left: 22px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details, #{{ addonId }} .news.mlistpost .tabBtn .post-next .details {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 40px;
                    height: 16px;
                    overflow: hidden;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details:before {
                    right: 25px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details:before, #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details:after {
                    transition: all 0.36s ease;
                    color: #999;
                    position: absolute;
                    top: 50%;
                    font-size: 16px;
                    transform: translateY(-50%);
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details:after {
                    right: -30px;
                    opacity: 0;
                }

                #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap {
                    left: 15px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap {
                    transition: all 0.36s ease;
                    opacity: 0;
                    width: 80px;
                    height: auto;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap div, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap div {
                    display: none;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap img, #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap img {
                    width: 100%;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap {
                    float: right;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    text-align: right;
                }

                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    display: none;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    text-align: right;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    height: 30px;
                    font-size: 13px;
                    line-height: 30px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-prev .infor-wrap .subtitle, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next .infor-wrap .subtitle {
                    transition: all 0.36s ease;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: block;
                }
                #{{ addonId }} .news.mlistpost .tabBtn::after {
                    content: "";
                    display: block;
                    clear: both;
                }

                #{{ addonId }} .listContent_post h3 {
                    font-size: 14px;
                    padding: 30px 0;
                }
                #{{ addonId }} .listContent_post > .item_tags {
                    padding: 0;
                    margin-bottom: 30px;
                    border-top: none;
                }
                #{{ addonId }} .item_tags {
                    clear: both;
                    height: 28px;
                    border-top: 1px solid rgba(170, 170, 170, 0.2);
                }
                #{{ addonId }} .item_tags > a {
                    float: left;
                    margin-right: 10px;
                    padding: 0 14px;
                    line-height: 26px;
                    border: 1px solid rgba(170, 170, 170, 0.2);
                }
                #{{ addonId }} .item_tags > a, .container_category > a {
                    border: 1px solid rgba(170, 170, 170, 0.2);
                    color: #828282;
                    background-color: transparent;
                }
                #{{ addonId }} .item_tags > a, .container_category > a, .imagelink .owl-nav .owl-prev, .postSlider .owl-nav .owl-prev, .mlist .owl-nav .owl-prev, .ff_topSlider .owl-nav .owl-prev, .imagelink .owl-nav .owl-next, .postSlider .owl-nav .owl-next, .mlist .owl-nav .owl-next, .ff_topSlider .owl-nav .owl-next, .imagelink .owl-nav .owl-prev:hover .iconfont, .postSlider .owl-nav .owl-prev:hover .iconfont, .mlist .owl-nav .owl-prev:hover .iconfont, .ff_topSlider .owl-nav .owl-prev:hover .iconfont, .imagelink .owl-nav .owl-next:hover .iconfont, .postSlider .owl-nav .owl-next:hover .iconfont, .mlist .owl-nav .owl-next:hover .iconfont, .ff_topSlider .owl-nav .owl-next:hover .iconfont, .project.mlist .content_list .item_block .item_box .item_wrapper, .project.mlistpost .content_list .item_block .item_box .item_wrapper, .team .content_list .item_block .item_box .item_wrapper, .ff_indexPage .news.ff_slider .content_list .item_block .item_box .item_wrapper, .ff_indexPage .news.ff_noSlider .content_list .item_block .item_box .item_wrapper, .ff_pageList .news .content_list .item_block .item_box .item_wrapper, .news.mlistpost .content_list .item_block .item_box .item_wrapper, .ff_indexPage .ad01.mlist .content_list .item_block .item_box .item_wrapper, .project.mlistpost .tabBtn .post-prev .img-wrap, .team.mlistpost .tabBtn .post-prev .img-wrap, .project.mlistpost .tabBtn .post-next .img-wrap, .team.mlistpost .tabBtn .post-next .img-wrap, .project.mlistpost .tabBtn .post-prev .infor-wrap, .team.mlistpost .tabBtn .post-prev .infor-wrap, .project.mlistpost .tabBtn .post-next .infor-wrap, .team.mlistpost .tabBtn .post-next .infor-wrap, .postSlider .tab_button .item_img img, body #header, body #headTop #logo img, .ff_indexPage .mlist .content_wrapper .more, .imagelink .content_list .item_block .item_box {
                        transition: all 0.36s ease;
                    }
                #{{ addonId }} .item_tags::after {
                    content: "";
                    display: block;
                    clear: both;
                }
                #{{ addonId }} .news.mlistpost .listContent_post .content_wrapper {
                    position: relative;
                    margin-right: -15px;
                }
                #{{ addonId }} #sitecontent .content .content_list {
                    position: relative;
                    overflow: hidden;
                }
                #{{ addonId }} .news.mlistpost .listContent_post .content_list::after {
                    content: "";
                    display: block;
                    clear: both;
                }

                #{{ addonId }} .fa-angle-right, .fa-angle-down , .fa-angle-left{
                    padding-left: 10px;
                }

                #{{ addonId }} .clear {
                    clear: both;
                }
                #{{ addonId }} #postWrapper::after {
                    content: "";
                    display: block;
                    clear: both;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next .details {
                    right: 22px;
                    border-left: 1px solid rgba(170, 170, 170, 0.2);
                    color:#999;
                    text-align:right;
                }

                #{{ addonId }} .news.mlistpost .tabBtn .post-prev:not(.empty):hover, #{{ addonId }} .news.mlistpost .tabBtn .post-next:not(.empty):hover {
                    background: {{{data.pageBgColorhover}}};
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev:not(.empty):hover .img-wrap, #{{ addonId }} .news.mlistpost .tabBtn .post-next:not(.empty):hover .img-wrap {
                    opacity: 1;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev:hover .infor-wrap .title, #{{ addonId }} .news.mlistpost .tabBtn .post-next:hover .infor-wrap .title {
                    color: {{{data.pageColorhover}}};
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev {
                    float: left;
                    padding-left: 85px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next {
                    float: right;
                    padding-right: 85px;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev:hover .details:after{
                    color: {{{data.pageColorhover}}}!important;
                }
                #{{ addonId }} .news.mlistpost .tabBtn .post-next:hover .details:after {
                    color: {{{data.pageColorhover}}}!important;
                }

                #{{ addonId }} .conTabBtn .post-prev:hover .details:before{color:#fff!important;}
                #{{ addonId }} .conTabBtn .post-next:hover .details{color:#fff!important;}
                #{{ addonId }} .news.mlistpost .tabBtn .post-prev:hover .details, #{{ addonId }} .news.mlistpost .tabBtn .post-next:hover .details {
                    border-color: rgba(170, 170, 170, 0.2);
                }
                @media (max-width: 670px) {
                    #{{ addonId }} .postInfo .title {
                        font-size: 16px;
                    }
                    #{{ addonId }} .news.mlistpost .tabBtn .post-prev {
                        padding-left: 34px;
                    }
                    #{{ addonId }} .news.mlistpost .tabBtn .post-prev .details {
                        left: 0;
                    }
                    #{{ addonId }} .news.mlistpost .tabBtn .post-next {
                        padding-right: 34px;
                    }
                    #{{ addonId }} .news.mlistpost .tabBtn .post-next .details {
                        right: 0;
                    }
                    #{{ addonId }} .news.mlistpost .tabBtn .post-prev .img-wrap,
                    #{{ addonId }} .news.mlistpost .tabBtn .post-next .img-wrap {
                        display: none;
                    }
                }
            </style>

            <div class="content">
                <div class="mlistpost news module" data-thumb="https://oss.lcweb01.cn/joomla/20221207/c4bda4b59e5dbbaad3994652cb531372.jpg">
                    <div class="module_container">
                        <div class="container_content">
                            <div class="content_wrapper">
                                <div id="postWrapper">
                                    <div class="postContent">
                                        <div class="postInfo">
                                            <div class="mainInfor">
                                                <p class="title" style="font-family: {{data.font_style_type1}};">国宝级中国茶，献礼风尚与财富——中国茶受邀成为环球网和《第一财经》......</p>
                                                <# if(date1_hide!=1){ #>
                                                <p class="usetdate">
                                                    <span class="year">2016</span><i class="time-connect">-</i><span class="m">12</span><i class="time-connect">-</i><span class="d">06</span>
                                                </p><# } #>
                                            </div>
                                            <div class="description">
                                                <p>因为所秉有的高贵品质，不同于以往茶品的创新思维，使它一举赢得了众多媒体的青睐和认可，更成为环球网和《第一财经》两家知名媒体......</p>
                                            </div>
                                        </div>
                                        <div class="postbody">
                                            <hr><p><br></p><p style="text-align: center;">“志于道、据于德、依于仁、游于艺”，中国文化向来讲究文以载道，而茶极大地在生活中滋养人们，形成一种独特的生活方式。自汉武帝开启丝绸之路，茶的角色愈加重要，成为经济贸易的卓越贡献者，随着经济的发展，中国茶传至日本、韩国、乃至欧洲和美洲，“东方树叶”成为中国的代名词。</p>
                                        </div>
                                        <# if(data.page_button==0) { #>
                                            <div class="conTabBtn tabBtn">
                                                <a href="" class="post-prev">
                                                    <div class="img-wrap">
                                                        <div style="background-image: url(https://oss.lcweb01.cn/joomla/20221207/a537585cdc1f79e18979e53e6a1274f5.jpg)"></div>
                                                        <img src="https://oss.lcweb01.cn/joomla/20221207/a537585cdc1f79e18979e53e6a1274f5.jpg" alt="">
                                                    </div>
                                                    <div class="infor-wrap">
                                                        <span class="title">陈文波：是波普艺术在当代中国的品牌代表</span>
                                                    </div>
                                                    <i class="fa fa-angle-left details"></i>
                                                    <div class="tabMask"></div>
                                                </a>
                                                <a href="" class="post-next">
                                                    <div class="img-wrap">
                                                        <div style="background-image: url(https://oss.lcweb01.cn/joomla/20221207/5335b83cb821ff693437eb1212328a65.jpg)"></div>
                                                        <img src="https://oss.lcweb01.cn/joomla/20221207/5335b83cb821ff693437eb1212328a65.jpg" alt="">
                                                    </div>
                                                    <div class="infor-wrap">
                                                        <span class="title">传统与现代碰撞，打造中国式高品质生活</span>
                                                    </div>
                                                    <i class="fa fa-angle-right details"></i>

                                                    <div class="tabMask"></div>
                                                </a>
                                            </div>
                                        <# } #>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
            </div>
        <# } #>


        ';

        return $output;
    }

}