<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'zihai_show',
        'title' => JText::_('官网服务'),
        'desc' => JText::_('官网服务'),
        'category' => '常用插件',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'image_carousel_layout' => array(
                    'type' => 'select',
                    'title' => JText::_('布局方式'),
                    'desc' => JText::_('布局方式'),
                    'values' => array(
                        'layout1' => '布局一',
                        'layout2' => '布局二',
                    ),
                    'std' => 'layout1',
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('右侧选项内容设置'),
                    'std' => array(
                        array(
                            'title' => '显示效果炫酷',
                            'content' => '动画效果生动炫酷，吸引客户眼球',
                        ),
                        array(
                            'title' => '由设计师量身定制',
                            'content' => '行业个性化定制，提升企业形象，彰显实力',
                        ),
                        array(
                            'title' => '兼容手机、pad等',
                            'content' => '有助于接触更多手机和平板电脑用户',
                        ),
                        array(
                            'title' => '适用不同终端',
                            'content' => '同时支持电脑端。移动端、效果不受设备影响',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'desc' => '标题',
                            'std' => '显示效果炫酷',
                        ),
                        'content'=>array(
                            'type'=>'text',
                            'title' => '介绍',
                            'desc' => '介绍',
                            'std' => 'html5 您所不知道的四大优势',
                        )
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1'),
                    ),
                ),

                'title1_type1' => array(
                    'type' => 'text',
                    'title' => '右侧标题1',
                    'desc' => '标题',
                    'std' => '龙采H5官网',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'title2_type1' => array(
                    'type' => 'text',
                    'title' => '右侧标题2',
                    'desc' => '标题',
                    'std' => '创造品牌动力',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'content_type1' => array(
                    'type' => 'text',
                    'title' => '右侧简介',
                    'desc' => '右侧简介',
                    'std' => 'html5 您所不知道的四大优势',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'image_bg1_type1' => array(
                    'type' => 'media',
                    'title' => '背景图pc端',
                    'desc' => '背景图pc端',
                    'std' => 'https://oss.lcweb01.cn/joomla/20221021/1b0b18e24ca9bb45999a185d3b888df1.jpg',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'image_bg2_type1' => array(
                    'type' => 'media',
                    'title' => '背景图移动端',
                    'desc' => '背景图移动端',
                    'std' => '',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'image1_type1' => array(
                    'type' => 'media',
                    'title' => '左侧大图',
                    'desc' => '左侧大图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20221021/3e7c8d525ca85c81dfb42f5655f7b8df.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),
                'image2_type1' => array(
                    'type' => 'media',
                    'title' => '左侧小图',
                    'desc' => '左侧小图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20221021/3e7c8d525ca85c81dfb42f5655f7b8df.png',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout1')
                    ),
                ),

                //type2
                'jw_image_carousel_item2' => array(
                    'title' => JText::_('右侧选项内容设置'),
                    'std' => array(
                        array(
                            'title' => '售后服务',
                            'content' => '我公司不仅以先进可靠的计算机系统和应用软件确保客户业务连续有效地运行，还向客户提供全方位的优良服务。公司本着用户利益至上的原则，设立专门的客户服务受理中心，该中心配备具有丰富应用开发和实施经验的软件工程师，和系统支持工程师，做到专人负责，及时响应用户在使用时所遇到的问题，保证在短的时间内响应用户。用户就是上帝，想用户所想，急用户所急，提供全方位的支持是我们一贯的所为。',
                            'bg_img' => 'https://www.longcai.com/templates/default/img/v2.png',
                            'bg_img_m1' => 'https://www.longcai.com/templates/default/img/t1.png',
                            'bg_img_m2' => 'https://www.longcai.com/templates/default/img/t2.png',
                        ),
                        array(
                            'title' => '服务宗旨',
                            'content' => '保证客户的计算机系统、网络以及应用软件能正常、可靠地运行，保证该项目的业务不停顿地运转。<br>众所周知，优质的售后服务是一个项目的承包商必须做出的承诺。但是，如何根据用户的实际情况（人员素质、计算机应用水平、系统的要求等），做出切合实际的项目售后服务计划书，才是用户关注的问题。优质的售后服务也一直是我们公司在经营活动中基本的原则。无论是在系统的安装调试过程中还是在系统投入运行之后，无论发生任何问题用户都可以得到迅速的响应。',
                            'bg_img' => 'https://www.longcai.com/templates/default/img/v2.png',
                            'bg_img_m1' => 'https://www.longcai.com/templates/default/img/t3.png',
                            'bg_img_m2' => 'https://www.longcai.com/templates/default/img/t4.png',
                        ),
                        array(
                            'title' => '服务内容',
                            'content' => '应用系统提供一年的免费维护并且免费升级，终身技术支持及代码错误修改；<br>系统使用过程中遇到的基本问题，提供技术上的解释和解决方案；<br>系统出现异常情况，能够迅速做出反应，提供解决方案，保证系统正常运行。',
                            'bg_img' => 'https://www.longcai.com/templates/default/img/v2.png',
                            'bg_img_m1' => 'https://www.longcai.com/templates/default/img/t5.png',
                            'bg_img_m2' => 'https://www.longcai.com/templates/default/img/t6.png',
                        ),
                        array(
                            'title' => '服务类型分类',
                            'content' => '免费服务：从系统正式运行后一年内；<br>付费服务：免费服务期满后；<br>优先级服务：当发生故障时，您可以自己根据故障等级确定故障的<br>优先级和支持服务中心的响应时间，从而得到不同的响应速度。',
                            'bg_img' => 'https://www.longcai.com/templates/default/img/v2.png',
                            'bg_img_m1' => 'https://www.longcai.com/templates/default/img/t7.png',
                            'bg_img_m2' => 'https://www.longcai.com/templates/default/img/t8.png',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'desc' => '标题',
                            'std' => '售后服务',
                        ),
                        'content'=>array(
                            'type'=>'textarea',
                            'title' => '介绍',
                            'desc' => '介绍',
                            'std' => '我公司不仅以先进可靠的计算机系统和应用软件确保客户业务连续有效地运行，还向客户提供全方位的优良服务。公司本着用户利益至上的原则，设立专门的客户服务受理中心，该中心配备具有丰富应用开发和实施经验的软件工程师，和系统支持工程师，做到专人负责，及时响应用户在使用时所遇到的问题，保证在短的时间内响应用户。用户就是上帝，想用户所想，急用户所急，提供全方位的支持是我们一贯的所为。',
                        ),
                        'bg_img' => array(
                            'type' => 'media',
                            'title' => '背景图',
                            'desc' => '背景图',
                            'std' => 'https://www.longcai.com/templates/default/img/v2.png',
                        ),
                        'bg_img_m1' => array(
                            'type' => 'media',
                            'title' => '小背景图',
                            'desc' => '小背景图',
                            'std' => 'https://www.longcai.com/templates/default/img/t1.png',
                        ),
                        'bg_img_m2' => array(
                            'type' => 'media',
                            'title' => '背景图',
                            'desc' => '背景图',
                            'std' => 'https://www.longcai.com/templates/default/img/t2.png',
                        ),
                    ),
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2'),
                    ),
                ),
                'bg_color_type2' => array(
                    'type' => 'color',
                    'title' => '滑入背景颜色',
                    'desc' => '滑入背景颜色',
                    'std' => '#d51920',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    ),
                ),
                'text_color_type2' => array(
                    'type' => 'color',
                    'title' => '滑入字体颜色',
                    'desc' => '滑入字体颜色',
                    'std' => '#fff',
                    'depends'=>array(
                        array('image_carousel_layout','=','layout2')
                    ),
                ),


                
            ),
        ),
    )
);
