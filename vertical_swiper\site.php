<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonVertical_swiper extends JwpagefactoryAddons
{
    public function render()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $items = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';

        $carousel_arrow_type = (isset($settings->carousel_arrow_type) && $settings->carousel_arrow_type) ? $settings->carousel_arrow_type : 'icon';
        $arrow_img = (isset($settings->arrow_img) && $settings->arrow_img) ? $settings->arrow_img : '/components/com_jwpagefactory/addons/image_swiper/assets/images/normal_left.png';
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';


        $output = '';
        $output .= '<div class="swiper-box" id="swiper_' . $addonId . '">
  <div class="swiper-container">
    <div class="swiper-wrapper">';
        foreach ($items as $key => $item) {
            $output .= '<div class="swiper-slide" data-id="' . $key . '">';
            if ($item->is_link === 1) {
                $target = $item->link_open_new_window === 1 ? '_blank' : '';
                $output .= '<a href="' . $item->image_carousel_img_link . '" target="' . $target . '">';
            }
            if (strstr($item->image_carousel_img, 'http://')||strstr($item->image_carousel_img,'https://')) {
                $output .= '<img src=\' ' . $item->image_carousel_img . ' \' alt="">';
            }else{
                $output .= '<img src=\' ' . JURI::base(true) . $item->image_carousel_img . ' \' alt="">';
            }
            if ($item->item_content_type === 'font') {
                $output .= '<div class="swiper-content fadeInDown">
                    <div class="first-title">' . $item->item_title . '</div>
                    <div class="subtitle">' . $item->item_subtitle . '</div>
                    <div class="description">' . $item->item_description . '</div>
                </div>';
            } else {
                $output .= '<div class="swiper-content fadeInDown">';
                if ($item->carousel_item_img_width) {
                    $output .= '<div style="width:' . $item->carousel_item_img_width . '%;">';
                } else {
                    $output .= '<div style="width: 100%;text-align: ' . $item_content_hori_align . '">';
                }
                if ($item->carousel_item_img) {
                    if (strstr($item->carousel_item_img, 'http://')||strstr($item->carousel_item_img,'https://')) {
                        $output .= '<img class="content-img" src="' . $item->carousel_item_img . '" alt="">';
                    }else{
                        $output .= '<img class="content-img" src=\' ' . JURI::base(true) . $item->carousel_item_img . ' \' alt="">';
                    }
                }

                $output .= '</div>';
                $output .= '</div>';
            }

            if ($item->is_link === 1) {
                $output .= '</a>';
            }
            $output .= '</div>';
        }
        $output .= '</div>
  </div>';
        $output .= '<!-- Add Pagination -->
            <div class="swiper-pagination"></div>';
        $output .= '</div>';


        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;


//        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;

        if ($carousel_autoplay === 1) {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        } else {
            $autoplay = 'false';
        }

//        分页器
        $pagination = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;
        if ($pagination === 1) {
            $pagination = '{
                    el: "#swiper_' . $addonId . ' .swiper-pagination",
                    clickable: true,
            }';
        } else {
            $pagination = '{}';
        }

        $script = 'jQuery(document).ready(function($){
//        初始化swiper配置项
            function initSwiper(needNavigation){
                let settings={
                    direction: "vertical",
                    loop: true,
                    observer: true,
                    observeParents:true,
                    loopFillGroupWithBlank: true,
                    pagination: ' . $pagination . ',
                    autoplay: ' . $autoplay . ',                  
                    speed: ' . $carousel_speed . ',
                }
                let swiper = new Swiper("#jwpf-addon-' . $addonId . ' .swiper-container", settings);
                return swiper;
            }
//            根据屏幕初始化swiper
            initSwiper();
        })';
        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;
//        轮播
        $addonId = '#swiper_' . $this->addon->id;


//        外部容器
        $carousel_height = '';
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
            } else {
                $carousel_height = $settings->carousel_height;
            }
        } else {
            $carousel_height = '';
        }
        $carousel_height_sm='';
        if (isset($settings->carousel_height_sm) && $settings->carousel_height_sm) {
            if (is_object($settings->carousel_height_sm)) {
                $carousel_height_sm = $settings->carousel_height_sm->sm;
            } else {
                $carousel_height_sm = $settings->carousel_height_sm;
            }
        } else {
            $carousel_height_sm = '';
        }
        $carousel_height_xs='';
        if (isset($settings->carousel_height_xs) && $settings->carousel_height_xs) {
            if (is_object($settings->carousel_height_xs)) {
                $carousel_height_xs = $settings->carousel_height_xs->xs;
            } else {
                $carousel_height_xs = $settings->carousel_height_xs;
            }
        } else {
            $carousel_height_xs = '';
        }


//        分页器
        $pagination = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;
        $show_pagination=$pagination===1?'block':'none';


        $pagination_right = '';
        if (isset($settings->pagination_right) && $settings->pagination_right) {
            if (is_object($settings->pagination_right)) {
                $pagination_right = $settings->pagination_right->md;
            } else {
                $pagination_right = $settings->pagination_right;
            }
        } else {
            $pagination_right = '';
        }

        $pagination_right_sm='';
        if (isset($settings->pagination_right) && $settings->pagination_right) {
            if (is_object($settings->pagination_right)) {
                $pagination_right_sm = $settings->pagination_right->sm;
            } else {
                $pagination_right_sm = $settings->pagination_right_sm;
            }
        } else {
            $pagination_right_sm = '';
        }

        $pagination_right_xs='';
        if (isset($settings->pagination_right) && $settings->pagination_right) {
            if (is_object($settings->pagination_right)) {
                $pagination_right_xs = $settings->pagination_right->xs;
            } else {
                $pagination_right_xs = $settings->pagination_right_xs;
            }
        } else {
            $pagination_right_xs = '';
        }
        $bullet_height = (isset($settings->bullet_height) && $settings->bullet_height) ? $settings->bullet_height : 8;
        $bullet_width = (isset($settings->bullet_width) && $settings->bullet_width) ? $settings->bullet_width : 8;
        $bullet_background = (isset($settings->bullet_background) && $settings->bullet_background) ? $settings->bullet_background : '#000';
        $bullet_border_width = (isset($settings->bullet_border_width) && $settings->bullet_border_width) ? $settings->bullet_border_width : 0;
        $bullet_border_color = (isset($settings->bullet_border_color) && $settings->bullet_border_color) ? $settings->bullet_border_color : '';
        $bullet_border_radius = (isset($settings->bullet_border_radius) && $settings->bullet_border_radius) ? $settings->bullet_border_radius : '50';
        $bullet_margin = (isset($settings->bullet_margin) && $settings->bullet_margin) ? $settings->bullet_margin : 8;

//        鼠标移入
        $bullet_active_height = (isset($settings->bullet_active_height) && $settings->bullet_active_height) ? $settings->bullet_active_height : 8;
        $bullet_active_width = (isset($settings->bullet_active_width) && $settings->bullet_active_width) ? $settings->bullet_active_width : 8;
        $bullet_active_background = (isset($settings->bullet_active_background) && $settings->bullet_active_background) ? $settings->bullet_active_background : '#007aff';
        $bullet_active_border_width = (isset($settings->bullet_active_border_width) && $settings->bullet_active_border_width) ? $settings->bullet_active_border_width : 0;
        $bullet_active_border_color = (isset($settings->bullet_active_border_color) && $settings->bullet_active_border_color) ? $settings->bullet_active_border_color : '';
        $bullet_active_border_radius = (isset($settings->bullet_active_border_radius) && $settings->bullet_active_border_radius) ? $settings->bullet_active_border_radius : '50';
        $bullet_opacity = (isset($settings->bullet_active_opacity) && $settings->bullet_active_opacity) ? $settings->bullet_active_opacity / 100 : '.2';

//      翻页按钮
        $carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        $show_arrow = $carousel_arrow === 1 ? 'block' : 'none';

        $arrow_position_verti='';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti = $settings->arrow_position_verti->md;
            } else {
                $arrow_position_verti = $settings->arrow_position_verti;
            }
        } else {
            $arrow_position_verti = '';
        }

        $arrow_position_verti_sm='';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti_sm = $settings->arrow_position_verti->sm;
            } else {
                $arrow_position_verti_sm = $settings->arrow_position_verti_sm;
            }
        } else {
            $arrow_position_verti_sm = '';
        }

        $arrow_position_verti_xs='';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti_xs = $settings->arrow_position_verti->xs;
            } else {
                $arrow_position_verti_xs = $settings->arrow_position_verti_xs;
            }
        } else {
            $arrow_position_verti_xs = '';
        }

        $arrow_position_hori='';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori = $settings->arrow_position_hori->md;
            } else {
                $arrow_position_hori = $settings->arrow_position_hori;
            }
        } else {
            $arrow_position_hori = '';
        }

        $arrow_position_hori_sm='';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori_sm = $settings->arrow_position_hori->sm;
            } else {
                $arrow_position_hori_sm = $settings->arrow_position_hori_sm;
            }
        } else {
            $arrow_position_hori_sm = '';
        }

        $arrow_position_hori_xs='';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori_xs = $settings->arrow_position_hori->xs;
            } else {
                $arrow_position_hori_xs = $settings->arrow_position_hori_xs;
            }
        } else {
            $arrow_position_hori_xs = '';
        }

//        轮播项内容
//        对齐
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';
        $item_content_verti_align = (isset($settings->item_content_verti_align) && $settings->item_content_verti_align) ? $settings->item_content_verti_align : 'center';
//          标题字体大小
        $content_title_fontsize='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize = $settings->content_title_fontsize->md;
            } else {
                $content_title_fontsize = $settings->content_title_fontsize;
            }
        } else {
            $content_title_fontsize = '';
        }

        $content_title_fontsize_sm='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_sm = $settings->content_title_fontsize->sm;
            } else {
                $content_title_fontsize_sm = $settings->content_title_fontsize_sm;
            }
        } else {
            $content_title_fontsize_sm = '';
        }
        $content_title_fontsize_xs='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_xs = $settings->content_title_fontsize->xs;
            } else {
                $content_title_fontsize_xs = $settings->content_title_fontsize_xs;
            }
        } else {
            $content_title_fontsize_xs = '';
        }
//        行高
        $content_title_lineheight = (isset($settings->content_title_lineheight) && $settings->content_title_lineheight) ? $settings->content_title_lineheight : '60';
        $content_title_font_family = (isset($settings->content_title_font_family) && $settings->content_title_font_family) ? $settings->content_title_font_family : '';
        $title_style = '';
        $content_title_font_style = (isset($settings->content_title_font_style) && $settings->content_title_font_style) ? $settings->content_title_font_style : '';
        if (isset($content_title_font_style->underline) && $content_title_font_style->underline) {
            $title_style .= 'text-decoration:underline;';
        }
        if (isset($content_title_font_style->italic) && $content_title_font_style->italic) {
            $title_style .= 'font-style:italic;';
        }
        if (isset($content_title_font_style->uppercase) && $content_title_font_style->uppercase) {
            $title_style .= 'text-transform:uppercase;';
        }
        if (isset($content_title_font_style->weight) && $content_title_font_style->weight) {
            $title_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }
        $content_title_letterspace = (isset($settings->content_title_letterspace) && $settings->content_title_letterspace) ? $settings->content_title_letterspace : '';
        $content_title_text_color = (isset($settings->content_title_text_color) && $settings->content_title_text_color) ? $settings->content_title_text_color : '#333';

        $content_title_margin='';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin = $settings->content_title_margin->md;
            } else {
                $content_title_margin = $settings->content_title_margin;
            }
        } else {
            $content_title_margin = '';
        }
        $content_title_margin_sm='';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_sm = $settings->content_title_margin->sm;
            } else {
                $content_title_margin_sm = $settings->content_title_margin_sm;
            }
        } else {
            $content_title_margin_sm = '';
        }
        $content_title_margin_xs='';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_xs = $settings->content_title_margin->xs;
            } else {
                $content_title_margin_xs = $settings->content_title_margin_xs;
            }
        } else {
            $content_title_margin_xs = '';
        }

//      副标题
        $content_subtitle_fontsize='';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize->md;
            } else {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize;
            }
        } else {
            $content_subtitle_fontsize = '';
        }
        $content_subtitle_fontsize_sm='';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize->sm;
            } else {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize_sm;
            }
        } else {
            $content_subtitle_fontsize_sm = '';
        }
        $content_subtitle_fontsize_xs='';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize->xs;
            } else {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize_xs;
            }
        } else {
            $content_subtitle_fontsize_xs = '';
        }
        $content_subtitle_letterspace = (isset($settings->content_subtitle_letterspace) && $settings->content_subtitle_letterspace) ? $settings->content_subtitle_letterspace : '';
        $content_subtitle_lineheight = (isset($settings->content_subtitle_lineheight) && $settings->content_subtitle_lineheight) ? $settings->content_subtitle_lineheight : '';
        $content_subtitle_font_family = (isset($settings->content_subtitle_font_family) && $settings->content_subtitle_font_family) ? $settings->content_subtitle_font_family : '';
        $content_subtitle_text_color = (isset($settings->content_subtitle_text_color) && $settings->content_subtitle_text_color) ? $settings->content_subtitle_text_color : '';
        $subtitle_style = '';
//        描述
        $description_fontsize='';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->md;
            } else {
                $description_fontsize = $settings->description_fontsize;
            }
        } else {
            $description_fontsize = '';
        }

        $description_fontsize_sm='';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->sm;
            } else {
                $description_fontsize = $settings->description_fontsize_sm;
            }
        } else {
            $description_fontsize_sm = '';
        }
        $description_fontsize_xs='';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize_xs = $settings->description_fontsize->xs;
            } else {
                $description_fontsize_xs = $settings->description_fontsize_xs;
            }
        } else {
            $description_fontsize_xs = '';
        }
        $description_letterspace = (isset($settings->description_letterspace) && $settings->description_letterspace) ? $settings->description_letterspace : '';
        $description_lineheight = (isset($settings->description_lineheight) && $settings->description_lineheight) ? $settings->description_lineheight : '';
        $description_font_family = (isset($settings->description_font_family) && $settings->description_font_family) ? $settings->description_font_family : '';
        $description_text_color = (isset($settings->description_text_color) && $settings->description_text_color) ? $settings->description_text_color : '';
        $description_style = '';

        $description_font_style = (isset($settings->description_font_style) && $settings->description_font_style) ? $settings->description_font_style : '';
        if (isset($description_font_style->underline) && $description_font_style->underline) {
            $description_style .= 'text-decoration:underline;';
        }
        if (isset($description_font_style->italic) && $description_font_style->italic) {
            $description_style .= 'font-style:italic;';
        }
        if (isset($description_font_style->uppercase) && $description_font_style->uppercase) {
            $description_style .= 'text-transform:uppercase;';
        }
        if (isset($description_font_style->weight) && $description_font_style->weight) {
            $description_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }

        $description_margin='';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin = $settings->description_margin->md;
            } else {
                $description_margin = $settings->description_margin;
            }
        } else {
            $description_margin = '';
        }

        $description_margin_sm='';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin_sm = $settings->description_margin->sm;
            } else {
                $description_margin_sm = $settings->description_margin_sm;
            }
        } else {
            $description_margin_sm = '';
        }

        $description_margin_xs='';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin_xs = $settings->description_margin->xs;
            } else {
                $description_margin_xs = $settings->description_margin_xs;
            }
        } else {
            $description_margin_xs = '';
        }


//      轮播项内容图是图片的时候
        $content_img_margin='';
        if (isset($settings->content_img_margin) && trim($settings->content_img_margin)) {
            if (is_object($settings->description_margin)) {
                $content_img_margin = $settings->content_img_margin->md;
            } else {
                $content_img_margin = $settings->content_img_margin;
            }
        } else {
            $content_img_margin = '';
        }

        $content_img_margin_sm='';
        if (isset($settings->content_img_margin) && trim($settings->content_img_margin)) {
            if (is_object($settings->description_margin)) {
                $content_img_margin_sm = $settings->content_img_margin->sm;
            } else {
                $content_img_margin_sm = $settings->content_img_margin_sm;
            }
        } else {
            $content_img_margin_sm = '';
        }
        $content_img_margin_xs='';
        if (isset($settings->content_img_margin) && trim($settings->content_img_margin)) {
            if (is_object($settings->description_margin)) {
                $content_img_margin_xs = $settings->content_img_margin->xs;
            } else {
                $content_img_margin_xs = $settings->content_img_margin_xs;
            }
        } else {
            $content_img_margin_xs = '';
        }



        $output = '
            ' . $addonId . '{
                position: relative;
                width: 100%;
                height: ' . $carousel_height . 'px;
                overflow: hidden;
            }
            ' . $addonId . ' a{
                text-decoration: none;
            }
            ' . $addonId . ' .swiper-container{
                width: 100%;
                height: 100%;
            }
            ' . $addonId . ' .swiper-slide{
                position: relative
            }
            ' . $addonId . ' .swiper-pagination{
                display: '.$show_pagination.';
                right: '.$pagination_right.'px!important;
                outline: none;
            }
            ' . $addonId . ' .swiper-pagination-bullet{
                width: ' . $bullet_width . 'px;
                height: ' . $bullet_height . 'px;
                background: ' . $bullet_background . ';
                border: ' . $bullet_border_width . 'px solid ' . $bullet_border_color . ';
                border-radius: ' . $bullet_border_radius . 'px;
                opacity: ' . $bullet_opacity . ';
                margin-bottom:' . $bullet_margin . 'px;
                outline: none;
            }
            ' . $addonId . ' .swiper-pagination-bullet:hover,
            ' . $addonId . ' .swiper-pagination-bullet.swiper-pagination-bullet-active{
                width: ' . $bullet_active_width . 'px;
                height: ' . $bullet_active_height . 'px;
                background: ' . $bullet_active_background . ';
                border: ' . $bullet_active_border_width . 'px solid ' . $bullet_active_border_color . ';
                border-radius: ' . $bullet_active_border_radius . 'px;
                opacity: 1;
            }
            ' . $addonId . ' .swiper-button-next,
            ' . $addonId . ' .swiper-button-prev{
                display:none;
                background-image: none;
                outline:none;
            }
            ' . $addonId . ' .swiper-slide img{
                width: 100%;
                height: 100%;
                display: block;
                object-fit: cover;
            }
            ' . $addonId . ' .swiper-content .first-title{
                font-size: ' . $content_title_fontsize . 'px;
                font-family: ' . $content_title_font_family . ';
                color: ' . $content_title_text_color . ';
                line-height: ' . $content_title_lineheight . 'px;
                text-align: ' . $item_content_hori_align . ';
                ' . $title_style . ';
                letter-spacing: ' . $content_title_letterspace . ';
                ' . $content_title_margin . '
            }
            
            ' . $addonId . ' .swiper-content{
                position:absolute;
                left:0;
                right:0;
                margin:auto;
                display: flex;
                flex-direction: column;
                text-align:center;
                justify-content: center;';
        if ($item_content_hori_align==='center'){
            $output.='align-items: center;';
        }elseif ($item_content_hori_align==='left'){
            $output.='align-items: flex-start;';
        }else{
            $output.='align-items: flex-end;';
        }
        if ($item_content_verti_align === 'middle') {
            $output .= '
                top: 0;
                bottom: 0;
            }';
        } else if ($item_content_verti_align === 'bottom') {
            $output .= 'bottom: 0;}';
        } else {
            $output .= 'top:0;}';
        }
        $output .= '
            ' . $addonId . ' .subtitle{
                font-size: ' . $content_subtitle_fontsize . 'px;
                line-height: ' . $content_subtitle_lineheight . 'px;
                font-family: ' . $content_subtitle_font_family . 'px;
                ' . $subtitle_style . ';
                letter-spacing: ' . $content_subtitle_letterspace . ';
                text-align: ' . $item_content_hori_align . ';
                color: ' . $content_subtitle_text_color . ';
            }
            ' . $addonId . ' .description{
                font-size: ' . $description_fontsize . 'px;
                font-family: ' . $description_font_family . ';
                color: ' . $description_text_color . ';
                line-height: ' . $description_lineheight . 'px;
                text-align: ' . $item_content_hori_align . ';
                ' . $description_style . ';
                letter-spacing: ' . $description_letterspace . ';
                ' . $description_margin . '
            }
            ' . $addonId . ' .content-img{
                width:auto!important;
                height:auto!important;
                display: inline-block!important;
                margin: ' . $content_img_margin . ';
            }
            @media (min-width: 768px) and (max-width: 991px){
                ' . $addonId . '{
                    height: ' . $carousel_height_sm . 'px;
                }
                ' . $addonId . ' .first-title{
                    font-size: ' . $content_title_fontsize_sm . 'px;
                    ' . $content_title_margin_sm . '
                }
                ' . $addonId . ' .subtitle{
                    font-size: ' . $content_subtitle_fontsize_sm . 'px;
                }
                ' . $addonId . ' .description{
                    font-size: ' . $description_fontsize_sm . 'px;
                    ' . $description_margin_sm . '
                }
                ' . $addonId . ' .content-img{
                    margin: ' . $content_img_margin_sm . ';
                }
                
                ' . $addonId . ' .swiper-pagination{
                    right: '.$pagination_right_sm.'px!important;
                }
            }
            @media (max-width: 767px){
                ' . $addonId . '{
                    height: ' . $carousel_height_xs . 'px;
                }
                ' . $addonId . ' .first-title{
                    font-size: ' . $content_title_fontsize_xs . 'px;
                    ' . $content_title_margin_xs . '
                }               
                ' . $addonId . ' .subtitle{
                    font-size: ' . $content_subtitle_fontsize_xs . 'px;
                }
                ' . $addonId . ' .description{
                    font-size: ' . $description_fontsize_xs . 'px;
                    ' . $description_margin_xs . '
                }
                ' . $addonId . ' .content-img{
                    margin: ' . $content_img_margin_xs . ';
                }
                ' . $addonId . ' .swiper-pagination{
                    right: '.$pagination_right_xs.'px!important;
                }
            }
            ' . $addonId . ' .swiper-pagination{
                top:50%;
                right: 6px;
                margin:auto;
                transform:translateY(-43%);
                width:' . $bullet_width . 'px;
                height: auto;
            }';

        return $output;
    }

    static function verticalSwiperCss(){
        $output='
        <#
            let vertical_swiper_id="#swiper_"+data.id;
            let carousel_height=_.isObject(data.carousel_height)&&data.carousel_height.md?data.carousel_height.md:data.carousel_height;
            let carousel_height_sm=_.isObject(data.carousel_height)&&data.carousel_height.sm?data.carousel_height.sm:data.carousel_height;
            let carousel_height_xs=_.isObject(data.carousel_height)&&data.carousel_height.xs?data.carousel_height.xs:data.carousel_height;
            let content_verti_align=data.item_content_verti_align?data.item_content_verti_align:"middle";
            let content_hori_align_css=data.item_content_hori_align?data.item_content_hori_align:"center";
            let content_subtitle_fontsize=_.isObject(data.content_subtitle_fontsize)&&data.content_subtitle_fontsize.md?data.content_subtitle_fontsize.md:data.content_subtitle_fontsize;
            let content_subtitle_fontsize_sm=_.isObject(data.content_subtitle_fontsize)&&data.content_subtitle_fontsize.sm?data.content_subtitle_fontsize.sm:data.content_subtitle_fontsize;
            let content_subtitle_fontsize_xs=_.isObject(data.content_subtitle_fontsize)&&data.content_subtitle_fontsize.xs?data.content_subtitle_fontsize.xs:data.content_subtitle_fontsize;
            let content_subtitle_lineheight=data.content_subtitle_lineheight?data.content_subtitle_lineheight:"";
            let content_subtitle_font_family=data.content_subtitle_font_family?data.content_subtitle_font_family:"";
            let content_subtitle_letterspace=data.content_subtitle_letterspace?data.content_subtitle_letterspace:"";
            let content_subtitle_text_color=data.content_subtitle_text_color?data.content_subtitle_text_color:"red";
            let content_title_fontsize=_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.md?data.content_title_fontsize.md:data.content_title_fontsize;
            let content_title_fontsize_sm=_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.sm?data.content_title_fontsize.sm:data.content_title_fontsize;
            let content_title_fontsize_xs=_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.xs?data.content_title_fontsize.xs:data.content_title_fontsize;
            let content_title_font_family=data.content_title_font_family?data.content_title_font_family:"";
            let content_title_text_color=data.content_title_text_color?data.content_title_text_color:"red";
            let content_title_lineheight=data.content_title_lineheight?data.content_title_lineheight:"";
            let description_fontsize_xs=_.isObject(data.description_fontsize)&&data.description_fontsize.xs?data.description_fontsize.xs:data.description_fontsize;
            let description_fontsize_sm=_.isObject(data.description_fontsize)&&data.description_fontsize.sm?data.description_fontsize.sm:data.description_fontsize;
            let description_fontsize=_.isObject(data.description_fontsize)&&data.description_fontsize.md?data.description_fontsize.md:data.description_fontsize;
            let title_style="";
            let content_title_font_style = data.content_title_font_style ? data.content_title_font_style : "";
            if(content_title_font_style.underline){
                title_style+="text-decoration:underline;";
            }
            if (content_title_font_style.italic) {
                $title_style += "font-style:italic;";
            }
            if (content_title_font_style.uppercase) {
                $title_style += "text-transform:uppercase;";
            }
            if (content_title_font_style.weight) {
                $title_style += "font-weight:" + content_title_font_style.weight + ";";
            }
            let content_title_letterspace=data.content_title_letterspace?data.content_title_letterspace:"";
            let content_title_margin=_.isObject(data.content_title_margin)&&data.content_title_margin.md?data.content_title_margin.md:data.content_title_margin;
            let content_title_margin_xs=_.isObject(data.content_title_margin)&&data.content_title_margin.xs?data.content_title_margin.xs:data.content_title_margin;
            let content_title_margin_sm=_.isObject(data.content_title_margin)&&data.content_title_margin.sm?data.content_title_margin.sm:data.content_title_margin;
            let description_font_family=data.description_font_family?data.description_font_family:"";
            let description_text_color=data.description_text_color?data.description_text_color:"";
            let description_lineheight=data.description_lineheight?data.description_lineheight:"";
            let description_style = "";
            let description_font_style = data.description_font_style ? data.description_font_style : "";
            if (description_font_style.underline) {
                description_style += "text-decoration:underline;";
            }
            if (description_font_style.italic) {
                description_style += "font-style:italic;";
            }
            if (description_font_style.uppercase) {
                description_style += "text-transform:uppercase;";
            }
            if (description_font_style.weight) {
                description_style += "font-weight: "+content_title_font_style.weight+";";
            }
            let description_letterspace=data.description_letterspace?data.description_letterspace:"";
            let description_margin=_.isObject(data.description_margin)&&data.description_margin.md?data.description_margin.md:data.description_margin;
            let description_margin_sm=_.isObject(data.description_margin)&&data.description_margin.sm?data.description_margin.sm:data.description_margin;
            let description_margin_xs=_.isObject(data.description_margin)&&data.description_margin.xs?data.description_margin.xs:data.description_margin;
            let content_img_margin_md=_.isObject(data.content_img_margin)&&data.content_img_margin.md?data.content_img_margin.md:data.content_img_margin;
            let content_img_margin_sm=_.isObject(data.content_img_margin)&&data.content_img_margin.sm?data.content_img_margin.sm:data.content_img_margin;
            let content_img_margin_xs=_.isObject(data.content_img_margin)&&data.content_img_margin.xs?data.content_img_margin.xs:data.content_img_margin;
            let pagination_right_xs=_.isObject(data.pagination_right)&&data.pagination_right.xs?data.pagination_right.xs:data.pagination_right;
            let pagination_right_sm=_.isObject(data.pagination_right)&&data.pagination_right.sm?data.pagination_right.sm:data.pagination_right;
            let pagination_right=_.isObject(data.pagination_right)&&data.pagination_right.md?data.pagination_right.md:data.pagination_right;
            let bullet_width=data.bullet_width?data.bullet_width:"18";
            let bullet_height=data.bullet_height?data.bullet_height:"3";
            let bullet_background=data.bullet_background?data.bullet_background:"#fff";
            let bullet_border_radius=data.bullet_border_radius?data.bullet_border_radius:"";
            let bullet_border_width=data.bullet_border_width?data.bullet_border_width:"0";
            let bullet_border_color=data.bullet_border_color?data.bullet_border_color:"#fff";
            let bullet_opacity=data.bullet_opacity?data.bullet_opacity:"0.8";
            let bullet_margin=data.bullet_margin?data.bullet_margin:"15";
            let bullet_active_width=data.bullet_active_width?data.bullet_active_width:"18";
            let bullet_active_height=data.bullet_active_height?data.bullet_active_height:"3";
            let bullet_active_background=data.bullet_active_background?data.bullet_active_background:"#fff";
            let bullet_active_border_width=data.bullet_active_border_width?data.bullet_active_border_width:"0";
            let bullet_active_border_color=data.bullet_active_border_color?data.bullet_active_border_color:"";
            let bullet_active_border_radius=data.bullet_active_border_radius?data.bullet_active_border_radius:"0";
        #>
        <style>
            {{vertical_swiper_id}} {
                height: {{carousel_height}}px;
                overflow: hidden;
                position: relative;
            }
            {{vertical_swiper_id}} img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            {{vertical_swiper_id}} .swiper-content{
                position:absolute;
                left:0;
                right:0;
                margin:auto;
                display: flex;
                flex-direction: column;
                text-align:center;
                justify-content: center;
                <# if (content_hori_align_css==="center"){ #>
                    align-items: center;
                <# } #>
                <# if (content_hori_align_css==="left"){ #>
                    align-items: flex-start;
                <# } #>
                <# if(content_hori_align_css==="right") { #>
                    align-items: flex-end;
                <# } #>
                <# if (content_verti_align==="top") { #>
                        top: 0;
                        bottom:auto;
                <# } #>
                <# if(content_verti_align === "middle"||content_verti_align === "bottom") { #>
                        bottom:0;
                <# } #>
            }
            {{vertical_swiper_id}} .swiper-content .first-title{
                font-size: {{content_title_fontsize}}px;
                font-family: {{content_title_font_family}};
                color: {{content_title_text_color}};
                line-height: {{content_title_lineheight}}px;
                text-align: {{content_hori_align_css}};
                {{title_style}}
                letter-spacing: {{content_title_letterspace}};
                margin: {{content_title_margin}};
            }
            {{vertical_swiper_id}} .subtitle{
                font-size: {{content_subtitle_fontsize}}px;
                line-height: {{content_subtitle_lineheight}}px;
                font-family: {{content_subtitle_font_family}}px;
                letter-spacing: {{content_subtitle_letterspace}};
                text-align: {{content_hori_align_css}};
                color: {{content_subtitle_text_color}};
            }
            {{vertical_swiper_id}} .description{
                font-size: {{description_fontsize}}px;
                font-family: {{description_font_family}};
                color: {{description_text_color}};
                line-height: {{description_lineheight}}px;
                text-align: {{content_hori_align_css}};
                {{description_style}}
                letter-spacing: {{description_letterspace}};
                {{description_margin}}
            }
            {{vertical_swiper_id}} .content-img{
                width:auto!important;
                height:auto!important;
                display: inline-block!important;
                margin: {{description_margin}};
            }
            {{vertical_swiper_id}} .pagination{
                position: absolute;
                top: 50%;
                right: {{pagination_right}}px;
                margin:auto;
                width:{{bullet_width}}px;
                height: auto;
                z-index: 2;
                transform: translateY(-50%);
                display: block;
            }
            {{vertical_swiper_id}} .paging{
                width:{{bullet_width}}px;
                height: {{bullet_height}}px;
                background: {{bullet_background}};
                border-radius: {{bullet_border_radius}}px;
                border: {{bullet_border_width}}px solid {{bullet_border_color}};
                opacity: {{bullet_opacity}};
            }
            {{vertical_swiper_id}} .paging:not(:last-child){
                margin-bottom: {{bullet_margin}}px;
            }
            {{vertical_swiper_id}} .paging:first-child{
                width:{{bullet_active_width}}px;
                height: {{bullet_active_height}}px;
                background: {{bullet_active_background}};
                border: {{bullet_active_border_width}}px solid {{bullet_active_border_color}};
                opacity: 1;
                border-radius: {{bullet_active_border_radius}}px;
            }
            @media (min-width: 768px) and (max-width: 991px){
                {{vertical_swiper_id}}{
                    height: {{carousel_height_sm}}px;
                }
                {{vertical_swiper_id}} .first-title{
                    font-size: {{content_title_fontsize_sm}}px;
                    margin: {{content_title_margin_sm}};
                }
                {{vertical_swiper_id}} .subtitle{
                    font-size: {{content_subtitle_fontsize_sm}}px;
                }
                {{vertical_swiper_id}} .description{
                    font-size: {{description_fontsize_sm}}px;
                    margin: {{description_margin_sm}};
                }
                {{vertical_swiper_id}} .content-img{
                    margin: {{content_img_margin_sm}};
                }

                {{vertical_swiper_id}} .pagination{
                    right: {{pagination_right_sm}}px;
                }
            }
            @media (max-width: 767px){
                {{vertical_swiper_id}}{
                    height: {{carousel_height_xs}}px;
                }
                {{vertical_swiper_id}} .first-title{
                    font-size: {{content_title_fontsize_xs}}px;
                    margin: {{content_title_margin_xs}};
                }
                {{vertical_swiper_id}} .subtitle{
                    font-size: {{content_subtitle_fontsize_xs}}px;
                }
                {{vertical_swiper_id}} .description{
                    font-size: {{description_fontsize_xs}}px;
                    margin: {{description_margin_xs}};
                }
                {{vertical_swiper_id}} .content-img{
                    margin: {{content_img_margin_xs}};
                }
                {{vertical_swiper_id}} .pagination{
                    right: {{pagination_right_xs}}px;
                }
            }
        </style>
        ';
        return $output;
    }

    static function getTemplate(){
        $output='
        <#
            let verticalSwiperId="swiper_"+data.id;
            let img=data.jw_image_carousel_item[0].image_carousel_img?data.jw_image_carousel_item[0].image_carousel_img:"https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg";
            let type=data.jw_image_carousel_item[0].item_content_type?data.jw_image_carousel_item[0].item_content_type:"font";
            let title=data.jw_image_carousel_item[0].item_title?data.jw_image_carousel_item[0].item_title:"";
            let subtitle=data.jw_image_carousel_item[0].item_subtitle?data.jw_image_carousel_item[0].item_subtitle:"";
            let description=data.jw_image_carousel_item[0].item_description?data.jw_image_carousel_item[0].item_description:"";
            let img_width=data.jw_image_carousel_item[0].carousel_item_img_width?data.jw_image_carousel_item[0].carousel_item_img_width:"";
            let content_hori_align=data.item_content_hori_align?data.item_content_hori_align:"center";
            let content_img=data.jw_image_carousel_item[0].carousel_item_img?data.jw_image_carousel_item[0].carousel_item_img:"";
            let items=data.jw_image_carousel_item;
        #>
        <p class="alert alert-warning">本图片仅为布局样式，请在预览页面中查看该插件切换效果</p>
        <div id="{{verticalSwiperId}}">
            <# if(img.includes("http://")||img.includes("https://")){ #>
                <img src=\'{{img}}\' alt="">
            <# }else{ #>
                <img src=\''.JURI::base(true).'{{img}}\' alt="">
            <# } #>
            <#　if (type === "font") { #>
                <div class="swiper-content fadeInDown">
                    <div class="first-title">{{title}}</div>
                    <div class="subtitle">{{subtitle}}</div>
                    <div class="description">{{description}}</div>
                </div>
            <# } else { #>
                <div class="swiper-content fadeInDown">
                    <# if (img_width) { #>
                        <div style="width: {{img_width}}%;">
                    <# } else { #>
                        <div style="width: 100%;text-align: {{content_hori_align}}">
                    <# }
                        if (content_img) {
                            if(img.includes("http://")||img.includes("https://")){ #>
                                <img class="content-img" src=\'{{content_img}}\' alt="">
                            <# }else{ #>
                                <img class="content-img" src=\''.JURI::base(true).'{{content_img}}\' alt="">
                            <# }
                        }#>
                    </div>
                </div>
            <# } #>
            <div class="pagination">
                <# _.each(items,function(item,index){ #>
                    <div class="paging"></div>
                <# }) #>
            </div>
        </div>
        ';
        return self::verticalSwiperCss().$output;
    }
}
