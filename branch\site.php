<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-14 13:51:34
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-01 17:19:33
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\branch\site.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonBranch extends JwpagefactoryAdd<PERSON>
{

    public function render()
    {
        $isk2installed = self::isComponentInstalled('branch');

        if ($isk2installed === 0) {
            return '<div>出错了</div>';
        }

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        
            
        $output='';
        
        //Output
        if($style === 'style1') {
            $placeholder = (isset($settings->placeholder) && $settings->placeholder) ? $settings->placeholder : '分公司';
            $placeholder_icon = (isset($settings->placeholder_icon) && $settings->placeholder_icon) ? $settings->placeholder_icon : 'https://oss.lcweb01.cn/joomla/20220602/901e0a593113c81639812c9e8de7d0c4.png';
            $placeholder_active_icon = (isset($settings->placeholder_active_icon) && $settings->placeholder_active_icon) ? $settings->placeholder_active_icon : 'https://oss.lcweb01.cn/joomla/20220602/b98d4fac54bf7101f618597b85547e6d.png';
            $title = (isset($settings->title) && $settings->title) ? $settings->title : '龙采分公司';
            $intro = (isset($settings->intro) && $settings->intro) ? $settings->intro : '连续六年蝉联中国互联网百强，集团下设73家分公司，遍布全国16个省46个城市';
            $link_text = (isset($settings->link_text) && $settings->link_text) ? $settings->link_text : '联系我们';
            $link_page_select = (isset($settings->link_page_select) && $settings->link_page_select) ? $settings->link_page_select : 'inner';
            $target = (isset($settings->target) && $settings->target) ? $settings->target : '';
            $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
            $detail_page_url = (isset($settings->detail_page_url) && $settings->detail_page_url) ? $settings->detail_page_url : '';
            $jw_tab_item_style1 = (isset($settings->jw_tab_item_style1) && $settings->jw_tab_item_style1) ? $settings->jw_tab_item_style1 : array();

            $output .= '<div class="hd1-a66">
                <div class="hd1-a67">
                    <img src=\''.$placeholder_icon.'\'>
                    <img src=\''.$placeholder_active_icon.'\'>
                    '.$placeholder.'
                </div>
                <div class="header-dropdown-market J-drop-common header-backdrop-filter" data-name="market" bi_parent_name="market">
                    <div class="dropdown-common-container" data-define="dropdown-container">
                        <div class="dropdown-common-wrapper">
                            <div class="wrapper-container dropdown-common-wrapper-inner">
                                <div class="dropdown-common-left-wrapper">
                                    <div>
                                        <i class="dropdown-common-icon"></i>
                                        <p class="dropdown-common-title" data-link="true">
                                            <a href="">'.$title.'</a>
                                        </p>
                                        <p class="dropdown-common-desc">'.$intro.'</p>';
                                            if($link_page_select == 'outer'){
                                                $output.='<a class="advertising-main" href="'.$detail_page_url.'" target="'.$target.'">';
                                            }else{
                                                $url = '/index.php/component/jwpagefactory/?view=page';
                                                $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0).'&id='.base64_encode($detail_page_id);
                                                
                                                $output.='<a class="dropdown-common-link" href=\''.$url.'\' target="'.$target.'">';
                                            }
                                            $output.= $link_text.'<i class="header-icon-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="dropdown-common-right-wrapper">
                                    <div class="dropdown-common-right-middle">
                                        <div class="dropdown-common-rightl">
                                            <div class="dropdown-common-module">
                                                <div class="dropdown-common-module-wrapper">';
                                                    foreach ($jw_tab_item_style1 as $key => $tab) {
                                                            $output.='<a target="_blank" href="'.$tab->link.'" class="dropdown-common-item">
                                                            <p class="dropdown-common-item-title">'.$tab->title.'</p>
                                                            <p class="dropdown-common-item-desc">'.$tab->link.'</p>
                                                        </a>';
                                                    }
                                                $output.='</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
        elseif($style === 'style2') {
            $background_img_type2_sj = (isset($settings->background_img_type2_sj) && $settings->background_img_type2_sj) ? $settings->background_img_type2_sj : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/54d72dc7232745dba6ca00c3964cba88.png';
            $output .= '
                <div class="ind7-a1">
                    <div class="ind7-a2 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                        style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                        <div class="ind7-a3 i100"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/65b0f22e5e5f7357dd885f73b5a6aa7c.png"></div>
                        <div class="ind7-a4 i100"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/48515ecc1a367cae46798322efad92ec.png">
                            <div>赤尾屿</div>
                            <div>黄尾屿</div>
                            <div>钓鱼岛</div>
                        </div>
                        <div class="ind7-a5">
                            <div class="ind7-a6 i100"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/621a19c8de0a7dbcd5fbf60648e184f7.png"></div>
                            <div class="ind7-a7">
                                <div>黑龙江</div>
                                <div>吉林</div>
                                <div>辽宁</div>
                                <div>内蒙古</div>
                                <div>山西</div>
                                <div>河北</div>
                                <div>山东</div>
                                <div>江苏</div>
                                <div>安徽</div>
                                <div>河南</div>
                                <div>宁夏</div>
                                <div>甘肃</div>
                                <div>青海</div>
                                <div>四川</div>
                                <div>西藏</div>
                                <div>云南</div>
                                <div>海南</div>
                                <div>广西</div>
                                <div>澳门</div>
                                <div>贵州</div>
                                <div>重庆</div>
                                <div>湖南</div>
                                <div>广东</div>
                                <div>香港</div>
                                <div>台湾</div>
                                <div>福建</div>
                                <div>江西</div>
                                <div>湖北</div>
                                <div>陕西</div>
                                <div>浙江</div>
                                <div>新疆</div>
                            </div>
                            <div class="ind7-a8">
                                <div class="ind7-a9">
                                    <div class="ind7-a10">哈尔滨</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">牡丹江</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <!-- <div class="ind7-a9">
                                    <div class="ind7-a10">鸡西</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div> -->
                                <div class="ind7-a9">
                                    <div class="ind7-a10">双鸭山</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">佳木斯</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">齐齐哈尔</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">大庆</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">绥化</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">鹤岗</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">伊春</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">黑河</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">长春</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">沈阳</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <!-- <div class="ind7-a9">
                                    <div class="ind7-a10">辽阳</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div> -->
                                <!-- <div class="ind7-a9">
                                    <div class="ind7-a10">阜新</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div> -->
                                <div class="ind7-a9">
                                    <div class="ind7-a10">盘锦</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">营口</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">鞍山</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">大连</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">北京</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">天津</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">朔州</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">大同</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">阳泉</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">晋中</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">忻州</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">太原</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">吕梁</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">临汾</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">运城</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">晋城</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">长治</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">青岛</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">烟台</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">济南</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">西安</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">成都</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">武汉</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <!-- <div class="ind7-a9">
                                    <div class="ind7-a10">上海</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div> -->
                                <div class="ind7-a9">
                                    <div class="ind7-a10">南昌</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">福州</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">泉州</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">厦门</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">深圳</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">南京</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">重庆</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                                <div class="ind7-a9">
                                    <div class="ind7-a10">湘潭</div>
                                    <div class="ind7-a11">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ind7-b1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                        style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                        <div class="ind7-b2">足迹遍布<span>16</span>个省<br><span>46</span>个市<br><span>73</span>家分公司</div>
                        <div class="ind7-b3"></div>
                        <div class="ind7-b4">中国互联网综合实力企业第38位<br>中国互联云计算战略合作伙伴<br>百度黑龙江.山西.大连战略合作伙伴</div>
                    </div>
                </div>
                <div class="p-index-a6">
                    <i class="p-index-a6-img i300"><img src="'.$background_img_type2_sj.'" alt=""></i>
                    <div class="p-index-a6-box">
                        <div class="p-index-a6-a1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                            style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                            <div>足迹遍布 <span>16</span>个省 <br> <span>46</span> 个市 <br> <span>73</span>家分公司</div>
                        </div>
                        <div class="p-index-a6-a2 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                            style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                            <div class="p-index-a6-a3 clear"><span class="fL">分布与实力</span><img class="fR"
                                    src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/f5d7cb7449f6765fa6c1c0c6e926c4a9.png" alt="">
                            </div>
                            <ul class="p-index-a6-a4">
                                <li><a href="http://www.longcai.com">龙采科技集团有限责任公司（黑龙江总部）</a></li>
                                <li><a href="http://www.longcai0452.com/">龙采科技集团有限责任公司（齐齐哈尔）</a></li>
                                <li><a href="http://www.longcai0453.com/">龙采科技集团有限责任公司（牡丹江）</a></li>
                                <li><a href="http://www.longcai0454.com">龙采科技集团有限责任公司（佳木斯）</a></li>
                                <li><a href="http://www.longcai0455.com/">龙采科技集团有限责任公司（绥化）</a></li>
                                <!-- <li><a href="http://www.longcai0467.com/">龙采科技集团有限责任公司（鸡西）</a></li> -->
                                <li><a href="http://www.longcai0459.com/">龙采科技集团有限责任公司（大庆）</a></li>
                                <li><a href="http://www.longcai0411.com/">龙采科技集团有限责任公司（大连总部）</a></li>
                                <li><a href="http://www.longcai0417.com/">龙采科技集团有限责任公司（营口）</a></li>
                                <li><a href="http://www.longcai0427.com/">龙采科技集团有限责任公司（盘锦）</a></li>
                                <li><a href="http://www.longcai0412.com/">龙采科技集团有限责任公司（鞍山）</a></li>
                                <!-- <li><a href="http://www.longcai0419.com/">龙采科技集团有限责任公司（辽阳）</a></li> -->
                                <!-- <li><a href="http://www.longcai0418.com/">龙采科技集团有限责任公司（阜新）</a></li> -->
                                <li><a href="http://www.longcai0351.com">龙采科技集团有限责任公司（山西总部）</a></li>
                                <li><a href="http://www.longcai0359.com/">龙采科技集团有限责任公司（运城）</a></li>
                                <li><a href="http://www.longcai0354.com/">龙采科技集团有限责任公司（晋中）</a></li>
                                <li><a href="http://www.longcai0353.com/">龙采科技集团有限责任公司（阳泉）</a></li>
                                <li><a href="http://www.longcai0352.com/">龙采科技集团有限责任公司（大同）</a></li>
                                <li><a href="http://www.longcai0350.com/">龙采科技集团有限责任公司（忻州）</a></li>
                                <li><a href="http://www.longcai0355.com/">龙采科技集团有限责任公司（长治）</a></li>
                                <li><a href="http://www.longcai0357.com/">龙采科技集团有限责任公司（临汾）</a></li>
                                <li><a href="http://www.longcai0356.com/">龙采科技集团有限责任公司（晋城）</a></li>
                                <li><a href="http://www.longcai0349.com/">龙采科技集团有限责任公司（朔州）</a></li>
                                <li><a href="http://www.longcai0358.com/">龙采科技集团有限责任公司（吕梁）</a></li>
                                <li><a href="http://www.longcai024.com/">龙采科技集团有限责任公司（沈阳）</a></li>
                                <li><a href="http://www.cclongcai.com/">龙采科技集团有限责任公司（长春）</a></li>
                                <li><a href="http://www.longcai0311.com/">龙采科技集团有限责任公司（石家庄）</a></li>
                                <li><a href="http://www.qdlongcai.cn/">龙采科技集团有限责任公司（青岛）</a></li>
                                <li><a href="http://www.longcai0531.com">龙采科技集团有限责任公司（山东）</a></li>
                                <li><a href="http://www.longcai027.com">龙采科技集团有限责任公司（武汉）</a></li>
                                <li><a href="http://www.longcai0791.com">龙采科技集团有限责任公司（南昌）</a></li>
                                <!-- <li><a href="http://www.longcai021.cn">龙采科技集团有限责任公司（上海）</a></li> -->
                                <li><a href="http://www.longcai025.com">龙采科技集团有限责任公司（南京）</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <script>
                    $("'.$addon_id.' .p-index-a6-a3").click(function () {
                        if (!$(this).hasClass("quest-active")) {
                            $("'.$addon_id.' .p-index-a6-a3").removeClass("quest-active")
                            $(this).addClass("quest-active")
                            $("'.$addon_id.' .p-index-a6-a3").next(".p-index-a6-a4").hide()
                            $(this).next(".p-index-a6-a4").slideDown()
                        } else {
                            $("'.$addon_id.' .p-index-a6-a3").removeClass("quest-active")
                            $("'.$addon_id.' .p-index-a6-a3").next(".p-index-a6-a4").slideUp()
                        }
                    });
                </script>
            ';
        }

        return $output;
    }

    public function css(){
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        $output = '';
        if($style === 'style1') {
            $jw_tab_items_left_style1 = (isset($settings->jw_tab_items_left_style1) && $settings->jw_tab_items_left_style1) ? $settings->jw_tab_items_left_style1 : -21;
            $placeholder_color = (isset($settings->placeholder_color) && $settings->placeholder_color) ? $settings->placeholder_color : '#fff';
            $placeholder_color_hover = (isset($settings->placeholder_color_hover) && $settings->placeholder_color_hover) ? $settings->placeholder_color_hover : '#333';
            $contact_img = (isset($settings->contact_img) && $settings->contact_img) ? $settings->contact_img : 'https://oss.lcweb01.cn/joomla/20220602/41671e6ee31a849ccc3c591bf887a9af.png';
            $placeholder_font_size = (isset($settings->placeholder_font_size) && $settings->placeholder_font_size) ? $settings->placeholder_font_size : 16;
            $placeholder_line_height = (isset($settings->placeholder_line_height) && $settings->placeholder_line_height) ? $settings->placeholder_line_height : 100;
            $placeholder_icon_size = (isset($settings->placeholder_icon_size) && $settings->placeholder_icon_size) ? $settings->placeholder_icon_size : 16;
            $placeholder_margin = (isset($settings->placeholder_margin) && $settings->placeholder_margin) ? $settings->placeholder_margin : '-2px 6px 0 0';
            $list_background_settings = (isset($settings->list_background_settings) && $settings->list_background_settings) ? $settings->list_background_settings : 'color';
            $list_color = (isset($settings->list_color) && $settings->list_color) ? $settings->list_color : '#fff';
            $list_bg_img = (isset($settings->list_bg_img) && $settings->list_bg_img) ? $settings->list_bg_img : '';
            $list_bg_img_repeat = (isset($settings->list_bg_img_repeat) && $settings->list_bg_img_repeat) ? $settings->list_bg_img_repeat : 0;
            $list_bg_img_position = (isset($settings->list_bg_img_position) && $settings->list_bg_img_position) ? $settings->list_bg_img_position : '0 0';
            $list_height = (isset($settings->list_height) && $settings->list_height) ? $settings->list_height : 500;
            $list_inner_height = (isset($settings->list_inner_height) && $settings->list_inner_height) ? $settings->list_inner_height : 440;
            $list_inner_left_color = (isset($settings->list_inner_left_color) && $settings->list_inner_left_color) ? $settings->list_inner_left_color : '#eee';
            $list_inner_padding = (isset($settings->list_inner_padding) && $settings->list_inner_padding) ? $settings->list_inner_padding : '32px 24px 32px 0';
            $list_width = (isset($settings->list_width) && $settings->list_width) ? $settings->list_width : 1560;
            $list_inner_left_icon_width = (isset($settings->list_inner_left_icon_width) && $settings->list_inner_left_icon_width) ? $settings->list_inner_left_icon_width : 110;
            $list_inner_left_icon_height = (isset($settings->list_inner_left_icon_height) && $settings->list_inner_left_icon_height) ? $settings->list_inner_left_icon_height : 90;
            $list_inner_title_size = (isset($settings->list_inner_title_size) && $settings->list_inner_title_size) ? $settings->list_inner_title_size : 24;
            $list_inner_title_line_height = (isset($settings->list_inner_title_line_height) && $settings->list_inner_title_line_height) ? $settings->list_inner_title_line_height : 30;
            $list_inner_title_margin_top = (isset($settings->list_inner_title_margin_top) && $settings->list_inner_title_margin_top) ? $settings->list_inner_title_margin_top : 24;
            $list_inner_title_color = (isset($settings->list_inner_title_color) && $settings->list_inner_title_color) ? $settings->list_inner_title_color : '#252b3a';
            $list_inner_intro_size = (isset($settings->list_inner_intro_size) && $settings->list_inner_intro_size) ? $settings->list_inner_intro_size : 16;
            $list_inner_intro_line_height = (isset($settings->list_inner_intro_line_height) && $settings->list_inner_intro_line_height) ? $settings->list_inner_intro_line_height : 32;
            $list_inner_intro_margin_top = (isset($settings->list_inner_intro_margin_top) && $settings->list_inner_intro_margin_top) ? $settings->list_inner_intro_margin_top : 12;
            $list_inner_intro_color = (isset($settings->list_inner_intro_color) && $settings->list_inner_intro_color) ? $settings->list_inner_intro_color : '#575d6c';
            $list_inner_contact_size = (isset($settings->list_inner_contact_size) && $settings->list_inner_contact_size) ? $settings->list_inner_contact_size : 16;
            $list_inner_contact_line_height = (isset($settings->list_inner_contact_line_height) && $settings->list_inner_contact_line_height) ? $settings->list_inner_contact_line_height : 18;
            $list_inner_contact_margin_top = (isset($settings->list_inner_contact_margin_top) && $settings->list_inner_contact_margin_top) ? $settings->list_inner_contact_margin_top : 30;
            $list_inner_contact_color = (isset($settings->list_inner_contact_color) && $settings->list_inner_contact_color) ? $settings->list_inner_contact_color : '#252b3a';
            $list_inner_arrow_size = (isset($settings->list_inner_arrow_size) && $settings->list_inner_arrow_size) ? $settings->list_inner_arrow_size : 6;
            $list_inner_arrow_width = (isset($settings->list_inner_arrow_width) && $settings->list_inner_arrow_width) ? $settings->list_inner_arrow_width : 1;
            $list_inner_arrow_left = (isset($settings->list_inner_arrow_left) && $settings->list_inner_arrow_left) ? $settings->list_inner_arrow_left : 8;
            $list_box_padding_right = (isset($settings->list_box_padding_right) && $settings->list_box_padding_right) ? $settings->list_box_padding_right : '0 0 0 12px';
            $list_mask_height_right = (isset($settings->list_mask_height_right) && $settings->list_mask_height_right) ? $settings->list_mask_height_right : 45;
            $list_mask_color_right = (isset($settings->list_mask_color_right) && $settings->list_mask_color_right) ? $settings->list_mask_color_right : '#fff';
            $list_mask_color2_right = (isset($settings->list_mask_color2_right) && $settings->list_mask_color2_right) ? $settings->list_mask_color2_right : 'rgba(255, 255, 255, 0)';
            $list_list_padding_bottom_right = (isset($settings->list_list_padding_bottom_right) && $settings->list_list_padding_bottom_right) ? $settings->list_list_padding_bottom_right : 32;
            $list_list_top_right = (isset($settings->list_list_top_right) && ($settings->list_list_top_right || $settings->list_list_top_right == 0)) ? $settings->list_list_top_right : -4;
            $list_item_margin_right = (isset($settings->list_item_margin_right) && $settings->list_item_margin_right) ? $settings->list_item_margin_right : '16px 16px 0 0';
            $list_item_width_right = (isset($settings->list_item_width_right) && $settings->list_item_width_right) ? $settings->list_item_width_right : 3;
            $list_item_max_width_right = (isset($settings->list_item_max_width_right) && $settings->list_item_max_width_right) ? $settings->list_item_max_width_right : 31;
            $list_item_padding_right = (isset($settings->list_item_padding_right) && $settings->list_item_padding_right) ? $settings->list_item_padding_right : '18px 18px 18px 18px';
            $list_item_border_color_right = (isset($settings->list_item_border_color_right) && $settings->list_item_border_color_right) ? $settings->list_item_border_color_right : '#eee';
            $list_item_border_hover_color_right = (isset($settings->list_item_border_hover_color_right) && $settings->list_item_border_hover_color_right) ? $settings->list_item_border_hover_color_right : '#eee';
            $list_item_title_color_right = (isset($settings->list_item_title_color_right) && $settings->list_item_title_color_right) ? $settings->list_item_title_color_right : '#252b3a';
            $list_item_title_size = (isset($settings->list_item_title_size) && $settings->list_item_title_size) ? $settings->list_item_title_size : 16;
            $list_item_title_line_height_size = (isset($settings->list_item_title_line_height_size) && $settings->list_item_title_line_height_size) ? $settings->list_item_title_line_height_size : 20;
            $list_item_title_hover_color_right = (isset($settings->list_item_title_hover_color_right) && $settings->list_item_title_hover_color_right) ? $settings->list_item_title_hover_color_right : '#c7000b';
            $list_item_desc_color_right = (isset($settings->list_item_desc_color_right) && $settings->list_item_desc_color_right) ? $settings->list_item_desc_color_right : '#8a8e99';
            $list_item_desc_hover_color_right = (isset($settings->list_item_desc_hover_color_right) && $settings->list_item_desc_hover_color_right) ? $settings->list_item_desc_hover_color_right : '#8a8e99';
            $list_item_desc_size_right = (isset($settings->list_item_desc_size_right) && $settings->list_item_desc_size_right) ? $settings->list_item_desc_size_right : 14;
            $list_item_desc_line_height_size_right = (isset($settings->list_item_desc_line_height_size_right) && $settings->list_item_desc_line_height_size_right) ? $settings->list_item_desc_line_height_size_right : 22;
            $list_item_desc_top_size_right = (isset($settings->list_item_desc_top_size_right) && $settings->list_item_desc_top_size_right) ? $settings->list_item_desc_top_size_right : 4;

            $output = $addon_id.' * {
                margin: 0;
                padding: 0;
                color: inherit;
                font-size: inherit;
                font-weight: inherit;
            }
            '.$addon_id.' a:hover{
                text-decoration: none;
            }
            '.$addon_id.' .hd1-a66 {
                font-size: 16px;
                line-height: 100px;
                color: #fff;
                font-weight: bold;
                position: relative;
                height: 100%;
                left: 0px;
                cursor: pointer;
            }
            '.$addon_id.' .hd1-a66:hover .header-dropdown-market{
                display: block;
            }
            '.$addon_id.' .hd1-a67 {
                font-size: '.$placeholder_font_size.'px;
                line-height: '.$placeholder_line_height.'px;
                color: '.$placeholder_color.';
                font-weight: bold;
                white-space: nowrap;
            }
            '.$addon_id.' .hd1-a66:hover .hd1-a67 {
                color: '.$placeholder_color_hover.';
            }
            '.$addon_id.' .hd1-a67 img {
                width: '.$placeholder_icon_size.'px;
                height: '.$placeholder_icon_size.'px;
                display: none;
                vertical-align: middle;
                position: relative;
                margin: '.$placeholder_margin.';
            }
            '.$addon_id.' .hd1-a67 img:nth-child(1){
                display: inline-block;
            }
            '.$addon_id.' .hd1-a66:hover .hd1-a67 img:nth-child(1){
                display: none;
            }
            '.$addon_id.' .hd1-a66:hover .hd1-a67 img:nth-child(2){
                display: inline-block;
            }
            '.$addon_id.' .header-dropdown-market {';
                if($list_background_settings === 'color'){
                    $output .='background-color: '.$list_color.';';
                }else{
                    $output .='background-image: url(\''.$list_bg_img.'\');';
                    if($list_bg_img_repeat){
                        $output .='background-repeat: repeat;';
                    }else{
                        $output .='background-repeat: no-repeat;';
                    }
                    $output.='background-position: '.$list_bg_img_position.';';
                }
                $output.='
                width: 200vw;
                position: absolute;
                top: '.$placeholder_line_height.'px;
                left: '.$jw_tab_items_left_style1.'vw;
                height: '.$list_height.'px;
                z-index: 99;
                display: none;
            }
            '.$addon_id.' .dropdown-common-container {
                width: 100vw;
                height: calc(100% - 60px);
                position: relative;
                overflow: hidden;
            }
            '.$addon_id.' .dropdown-common-wrapper {
                height: calc(100% - 20px);
                padding: 0;
                max-height: 100%;
            }
            '.$addon_id.' .dropdown-common-container::after {
                content: "";
                display: block;
                width: 100%;
                height: 50px;
                position: absolute;
                bottom: 0;
            }
            '.$addon_id.' .wrapper-container {
                max-width: '.$list_width.'px;
                margin-left: auto;
                margin-right: auto;
            }
            '.$addon_id.' .dropdown-common-wrapper-inner {
                padding: 0;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                max-height: 100%;
            }
            '.$addon_id.' .wrapper-container {
                height: '.$list_inner_height.'px;
                max-height: 100%;
                overflow: hidden;
            }
            '.$addon_id.' .dropdown-common-left-wrapper {
                border-right: 1px solid '.$list_inner_left_color.';
                padding: '.$list_inner_padding.';
                -webkit-box-flex: 5;
                -ms-flex: 5;
                flex: 5;
                z-index: 100;
                max-height: 100%;
            }
            '.$addon_id.' .dropdown-common-left-wrapper > div {
                overflow: auto;
                padding-right: 16px;
                height: calc(100% - 50px);
            }
            '.$addon_id.' .dropdown-common-left-wrapper > div {
                height: 100%;
            }
            '.$addon_id.' .dropdown-common-icon {
                z-index: 99;
                display: block;
                padding-top: 0px;
                margin-top: 0px;
                padding-bottom: 0px;
                margin-bottom: 0px;
                background:url('.$contact_img.');
                background-size: 100% 100%;
                width: '.$list_inner_left_icon_width.'px;
                height: '.$list_inner_left_icon_height.'px;
            }
            '.$addon_id.' .dropdown-common-title {
                font-size: '.$list_inner_title_size.'px;
                line-height: '.$list_inner_title_line_height.'px;
                color: '.$list_inner_title_color.';
                margin-top: '.$list_inner_title_margin_top.'px;
                -webkit-transition: all .2s;
                transition: all .2s;
            }
            '.$addon_id.' .dropdown-common-desc {
                font-size: '.$list_inner_intro_size.'px;
                line-height: '.$list_inner_intro_line_height.'px;
                color: '.$list_inner_intro_color.';
                margin-top: '.$list_inner_intro_margin_top.'px;
                font-weight: initial;
            }
            '.$addon_id.' .dropdown-common-link {
                display: block;
                font-size: '.$list_inner_contact_size.'px;
                line-height: '.$list_inner_contact_line_height.'px;
                color: '.$list_inner_contact_color.';
                margin-top: '.$list_inner_contact_margin_top.'px;
                -webkit-transition: all .2s;
                transition: all .2s;
                cursor: pointer;
                font-weight: 600;
            }
            '.$addon_id.' .header-icon-arrow-right {
                display: inline-block;
                vertical-align: middle;
                width: '.$list_inner_arrow_size.'px;
                height: '.$list_inner_arrow_size.'px;
                border-top: '.$list_inner_arrow_width.'px solid;
                border-right: '.$list_inner_arrow_width.'px solid;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
                -webkit-transform-origin: 60%;
                transform-origin: 60%;
                -webkit-transition: all .2s;
                transition: all .2s;
                position: relative;
                left: 0;
            }
            '.$addon_id.' .dropdown-common-link i {
                margin-left: '.$list_inner_arrow_left.'px;
            }
            '.$addon_id.' .dropdown-common-right-wrapper {
                margin-bottom: 0;
                padding: '.$list_box_padding_right.';
                -webkit-box-orient: horizontal;
                -webkit-box-direction: normal;
                -ms-flex-flow: row wrap;
                flex-flow: row wrap;
                -webkit-box-flex: 18;
                -ms-flex: 18;
                flex: 18;
                overflow: auto;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-align: start;
                -ms-flex-align: start;
                align-items: flex-start;
                position: relative;
                max-height: 100%;
            }
            '.$addon_id.' .dropdown-common-right-wrapper {
                position: relative;
            }
            '.$addon_id.' .dropdown-common-right-middle {
                height: 100%;
                overflow: hidden;
                overflow-y: scroll;
            }
            '.$addon_id.' .dropdown-common-right-wrapper::after {
                width: 100%;
                height: '.$list_mask_height_right.'%;
                position: absolute;
                right: 0;
                bottom: 0;
                content: "";
                background: linear-gradient(0deg, '.$list_mask_color_right.', '.$list_mask_color2_right.');
                z-index: 1;
                pointer-events: none;
            }
            '.$addon_id.' .dropdown-common-rightl {
                -webkit-box-flex: 12;
                -ms-flex: 12;
                flex: 12;
                padding-bottom: '.$list_list_padding_bottom_right.'px;
                display: block;
            }
            '.$addon_id.' .dropdown-common-rightl::after {
                content: "";
                display: table;
                clear: both;
            }
            '.$addon_id.' .dropdown-common-module-wrapper {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                margin-top: '.$list_list_top_right.'px;
            }
            '.$addon_id.' .dropdown-common-item {
                display: block;
                cursor: pointer;
                margin-top: 20px;
            }
            '.$addon_id.' .dropdown-common-module-wrapper .dropdown-common-item {
                -webkit-box-flex: 0;
                -ms-flex: 0 0 '.(100 / $list_item_width_right).'%;
                flex: 0 0 '.(100 / $list_item_width_right).'%;
                max-width: '.$list_item_max_width_right.'%;
                margin: '.$list_item_margin_right.';
                border: 1px solid '.$list_item_border_color_right.';
                padding: '.$list_item_padding_right.';
                box-shadow: 0 0 10px rgba(225,225,225,.3);
            }
            '.$addon_id.' .dropdown-common-module-wrapper .dropdown-common-item:hover{
                border: 1px solid '.$list_item_border_hover_color_right.';
            }
            '.$addon_id.' .dropdown-common-module-wrapper .dropdown-common-item:hover .dropdown-common-item-title{
                color: '.$list_item_title_hover_color_right.';
            }
            '.$addon_id.' .dropdown-common-item-title {
                font-size: '.$list_item_title_size.'px;
                line-height: '.$list_item_title_line_height_size.'px;
                font-weight: 700;
                color: '.$list_item_title_color_right.';
                -webkit-transition: all .2s;
                transition: all .2s;
            }
            '.$addon_id.' .dropdown-common-item-desc {
                width: 100%;
                font-size: '.$list_item_desc_size_right.'px;
                line-height: '.$list_item_desc_line_height_size_right.'px;
                color: '.$list_item_desc_color_right.';
                margin-top: '.$list_item_desc_top_size_right.'px;
                font-weight: 400;
            }
            /*    滚动条*/
            '.$addon_id.' ::-webkit-scrollbar {
                width: 13px;
            }
            '.$addon_id.' ::-webkit-scrollbar-thumb {
                border: 3px solid transparent;
                background-clip: padding-box;
                border-radius: 7px;
                min-height: 84px;
                background-color: rgba(187, 187, 187, 0.88);
            }
            '.$addon_id.' ::-webkit-scrollbar-track {
                background-color: transparent;
            }';
        }
        if($style === 'style2') {
            $background_img_type2_pc = (isset($settings->background_img_type2_pc) && $settings->background_img_type2_pc) ? $settings->background_img_type2_pc : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/205277a59818c3a7178ef975f0bd91fa.jpeg';
            $output = $addon_id.' * {
                padding: 0;
                margin: 0;
                box-sizing: border-box;
            }
        
            @-webkit-keyframes fadeInUp {
                from {
                    opacity: 0;
                    -webkit-transform: translate3d(0, 100%, 0);
                    transform: translate3d(0, 100%, 0);
                }
        
                to {
                    opacity: 1;
                    -webkit-transform: none;
                    transform: none;
                }
            }
        
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    -webkit-transform: translate3d(0, 100%, 0);
                    transform: translate3d(0, 100%, 0);
                }
        
                to {
                    opacity: 1;
                    -webkit-transform: none;
                    transform: none;
                }
            }
        
            @keyframes maplighting1 {
                0% {
                    transform: scale(0.5);
                    opacity: 1;
                }
        
                100% {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        
            '.$addon_id.' .fadeInUp {
                -webkit-animation-name: fadeInUp;
                animation-name: fadeInUp;
            }
        
            '.$addon_id.' .i100 {
                overflow: hidden;
            }
        
            '.$addon_id.' .i100>img {
                width: 100%;
            }
        
            '.$addon_id.' .p-index-a6 {
                display: none;
            }
        
            '.$addon_id.' .i300>img {
                width: 100%;
                height: 100%;
            }
        
            '.$addon_id.' a {
                text-decoration: none;
                margin: 0;
                padding: 0;
                color: inherit;
                font-size: inherit;
                font-weight: inherit;
            }
        
            @media only screen and (min-width: 1600px) {
                '.$addon_id.' .ind7-a1 {
                    background-image: url('.$background_img_type2_pc.');
                    background-size: 1920px 100%;
                    background-position: center center;
                    background-repeat: no-repeat;
                    background-color: #000;
                }
        
                '.$addon_id.' .ind7-a1 {
                    width: 100%;
                    height: 900px;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a2 {
                    width: 912px;
                    position: absolute;
                    top: 72px;
                    left: calc(50% - 1560px/2);
                }
        
                '.$addon_id.' .ind7-a3 {
                    width: calc(123/912*100%);
                    position: absolute;
                    top: calc(575/753*100%);
                    left: calc(867/912*100%);
                }
        
                '.$addon_id.' .ind7-a4 {
                    width: calc(37/912*100%);
                    position: absolute;
                    top: calc(542/753*100%);
                    left: calc(844/912*100%);
                    overflow: visible !important;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(2) {
                    top: -18px;
                    left: 30px;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(3) {
                    top: -14px;
                    left: -25px;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(4) {
                    top: 10px;
                    left: -40px;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a5 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a6 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a7 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a7>div {
                    font-size: 13px;
                    line-height: 13px;
                    color: #960a0f;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a11 {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: #fff;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a8 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a11>div {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 1);
                    position: absolute;
                    top: -5px;
                    left: -5px;
                    animation: maplighting1 2s linear infinite;
                }
        
                '.$addon_id.' .ind7-a9 {
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a10 {
                    font-size: 13px;
                    line-height: 13px;
                    color: #fff;
                    white-space: nowrap;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-b1 {
                    position: absolute;
                    top: 255px;
                    right: calc(50% - 1560px/2);
                    text-align: right;
                }
        
                '.$addon_id.' .ind7-b2 {
                    font-size: 60px;
                    line-height: 90px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                    margin-bottom: 32px;
                }
        
                '.$addon_id.' .ind7-b2 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .ind7-b3 {
                    width: 250px;
                    height: 1px;
                    background: #fff;
                    display: inline-block;
                    margin-bottom: 24px;
                }
        
                '.$addon_id.' .ind7-b4 {
                    font-size: 20px;
                    line-height: 48px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                }
            }
        
            @media only screen and (max-width: 1599px) and (min-width: 1400px) {
                '.$addon_id.' .ind7-a1 {
                    background-image: url('.$background_img_type2_pc.');
                    background-size: 1920px 100%;
                    background-position: center center;
                    background-repeat: no-repeat;
                    background-color: #000;
                }
        
                '.$addon_id.' .ind7-a1 {
                    width: 100%;
                    height: 900px;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a2 {
                    width: 912px;
                    position: absolute;
                    top: 72px;
                    left: calc(50% - 1360px/2);
                }
        
                '.$addon_id.' .ind7-a3 {
                    width: calc(123/912*100%);
                    position: absolute;
                    top: calc(575/753*100%);
                    left: calc(867/912*100%);
                }
        
                '.$addon_id.' .ind7-a4 {
                    width: calc(37/912*100%);
                    position: absolute;
                    top: calc(542/753*100%);
                    left: calc(844/912*100%);
                    overflow: visible !important;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(2) {
                    top: -18px;
                    left: 30px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(3) {
                    top: -14px;
                    left: -25px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(4) {
                    top: 10px;
                    left: -40px;
                }
        
                '.$addon_id.' .ind7-a5 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a6 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a7 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a7>div {
                    font-size: 13px;
                    line-height: 13px;
                    color: #960a0f;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a8 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a9 {
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a10 {
                    font-size: 13px;
                    line-height: 13px;
                    color: #fff;
                    white-space: nowrap;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a11 {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: #fff;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a11>div {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 1);
                    position: absolute;
                    top: -5px;
                    left: -5px;
                    animation: maplighting1 2s linear infinite;
                }
        
                '.$addon_id.' .ind7-b1 {
                    position: absolute;
                    top: 255px;
                    right: calc(50% - 1360px/2);
                    text-align: right;
                }
        
                '.$addon_id.' .ind7-b2 {
                    font-size: 60px;
                    line-height: 90px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                    margin-bottom: 32px;
                }
        
                '.$addon_id.' .ind7-b2 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .ind7-b3 {
                    width: 250px;
                    height: 1px;
                    background: #fff;
                    display: inline-block;
                    margin-bottom: 24px;
                }
        
                '.$addon_id.' .ind7-b4 {
                    font-size: 20px;
                    line-height: 48px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                }
            }
        
            @media only screen and (max-width: 1399px) and (min-width: 1200px) {
                '.$addon_id.' .ind7-a1 {
                    background-image: url('.$background_img_type2_pc.');
                    background-size: 1440px 100%;
                    background-position: center center;
                    background-repeat: no-repeat;
                    background-color: #000;
                }
        
                '.$addon_id.' .ind7-a1 {
                    width: 100%;
                    height: 675px;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a2 {
                    width: 684px;
                    position: absolute;
                    top: 60px;
                    left: calc(50% - 1160px/2);
                }
        
                '.$addon_id.' .ind7-a3 {
                    width: calc(123/912*100%);
                    position: absolute;
                    top: calc(575/753*100%);
                    left: calc(867/912*100%);
                }
        
                '.$addon_id.' .ind7-a4 {
                    width: calc(37/912*100%);
                    position: absolute;
                    top: calc(542/753*100%);
                    left: calc(844/912*100%);
                    overflow: visible !important;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(2) {
                    top: -18px;
                    left: 30px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(3) {
                    top: -14px;
                    left: -25px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(4) {
                    top: 10px;
                    left: -40px;
                }
        
                '.$addon_id.' .ind7-a5 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a6 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a7 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a7>div {
                    font-size: 13px;
                    line-height: 13px;
                    color: #960a0f;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a8 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a9 {
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a10 {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    white-space: nowrap;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a11 {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: #fff;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a11>div {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 1);
                    position: absolute;
                    top: -5px;
                    left: -5px;
                    animation: maplighting1 2s linear infinite;
                }
        
                '.$addon_id.' .ind7-b1 {
                    position: absolute;
                    top: 160px;
                    right: calc(50% - 1160px/2);
                    text-align: right;
                }
        
                '.$addon_id.' .ind7-b2 {
                    font-size: 44px;
                    line-height: 64px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                    margin-bottom: 24px;
                }
        
                '.$addon_id.' .ind7-b2 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .ind7-b3 {
                    width: 180px;
                    height: 1px;
                    background: #fff;
                    display: inline-block;
                    margin-bottom: 18px;
                }
        
                '.$addon_id.' .ind7-b4 {
                    font-size: 16px;
                    line-height: 36px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                }
            }
        
            @media only screen and (max-width: 1199px) and (min-width: 1024px) {
                '.$addon_id.' .ind7-a1 {
                    background-image: url('.$background_img_type2_pc.');
                    background-size: 1440px 100%;
                    background-position: center center;
                    background-repeat: no-repeat;
                    background-color: #000;
                }
        
                '.$addon_id.' .ind7-a1 {
                    width: 100%;
                    height: 675px;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a2 {
                    width: 684px;
                    position: absolute;
                    top: 60px;
                    left: calc(50% - 960px/2);
                }
        
                '.$addon_id.' .ind7-a3 {
                    width: calc(123/912*100%);
                    position: absolute;
                    top: calc(575/753*100%);
                    left: calc(767/912*100%);
                    transform: scale(0.9);
                    transform-origin: 0 50%;
                }
        
                '.$addon_id.' .ind7-a4 {
                    width: calc(37/912*100%);
                    position: absolute;
                    top: calc(542/753*100%);
                    left: calc(804/912*100%);
                    overflow: visible !important;
                    transform: scale(0.9);
                    transform-origin: 0 50%;
                }
        
                '.$addon_id.' .ind7-a4>div {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(2) {
                    top: -18px;
                    left: 30px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(3) {
                    top: -14px;
                    left: -25px;
                }
        
                '.$addon_id.' .ind7-a4>div:nth-child(4) {
                    top: 10px;
                    left: -40px;
                }
        
                '.$addon_id.' .ind7-a5 {
                    width: 100%;
                    position: relative;
                    transform: scale(0.9);
                    transform-origin: 0 50%;
                }
        
                '.$addon_id.' .ind7-a6 {
                    width: 100%;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a7 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a7>div {
                    font-size: 13px;
                    line-height: 13px;
                    color: #960a0f;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a8 {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
        
                '.$addon_id.' .ind7-a9 {
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a10 {
                    font-size: 12px;
                    line-height: 12px;
                    color: #fff;
                    white-space: nowrap;
                    position: absolute;
                }
        
                '.$addon_id.' .ind7-a11 {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: #fff;
                    position: relative;
                }
        
                '.$addon_id.' .ind7-a11>div {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 1);
                    position: absolute;
                    top: -5px;
                    left: -5px;
                    animation: maplighting1 2s linear infinite;
                }
        
                '.$addon_id.' .ind7-b1 {
                    position: absolute;
                    top: 160px;
                    right: calc(50% - 960px/2);
                    text-align: right;
                }
        
                '.$addon_id.' .ind7-b2 {
                    font-size: 44px;
                    line-height: 64px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                    margin-bottom: 24px;
                }
        
                '.$addon_id.' .ind7-b2 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .ind7-b3 {
                    width: 180px;
                    height: 1px;
                    background: #fff;
                    display: inline-block;
                    margin-bottom: 18px;
                }
        
                '.$addon_id.' .ind7-b4 {
                    font-size: 16px;
                    line-height: 36px;
                    color: #fff;
                    font-weight: bold;
                    text-align: right;
                }
            }
        
            @media only screen and (max-width: 1023px) and (min-width: 660px) {
                '.$addon_id.' .p-index-a6 {
                    display: block;
                }
        
                '.$addon_id.' .ind7-a1 {
                    display: none;
                }
        
                '.$addon_id.' .p-index-a6 {
                    width: 100%;
                    height: 1100px;
                    overflow: hidden;
                    position: relative;
                }
        
                '.$addon_id.' .p-index-a6-box {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding-top: 280px;
                    z-index: 3;
                }
        
                '.$addon_id.' .p-index-a6-a1 {
                    text-align: center;
                    color: #fff;
                    font-size: 60px;
                    font-weight: bolder;
                    margin-bottom: 180px;
                }
        
                '.$addon_id.' .p-index-a6-a1 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .p-index-a6-a2 {
                    width: 600px;
                    margin: 0 auto;
                }
        
                '.$addon_id.' .p-index-a6-a3 {
                    width: 600px;
                    height: 100px;
                    background: #fff;
                    padding: 0 30px;
                    line-height: 100px;
                }
        
                '.$addon_id.' .p-index-a6-a3>span {
                    font-size: 32px;
                    color: #d9012a;
                    font-weight: bolder;
                }
        
                '.$addon_id.' .fL {
                    float: left;
                }
        
                '.$addon_id.' .fR {
                    float: right;
                }
        
                '.$addon_id.' .p-index-a6-a3>img {
                    display: inline-block;
                    padding-top: 42px;
                    transition: .3s;
                }
        
                '.$addon_id.' .p-index-a6-a4 {
                    background: #fff;
                    color: #454545;
                    padding: 0 30px;
                    height: 200px;
                    overflow-y: auto;
                    display: none;
                    list-style: none;
                    font: normal normal 28px/1.5 sans-serif;
                }
        
                '.$addon_id.' .p-index-a6-a4 li {
                    line-height: 60px;
                }
        
                '.$addon_id.' .p-index-a6-a3.quest-active>img {
                    transform: rotate(90deg);
                    transition: .3s;
                    margin-top: 20px;
                }
            }
        
            @media only screen and (max-width: 659px) and (min-width: 500px) {
                '.$addon_id.' .p-index-a6 {
                    display: block;
                }
        
                '.$addon_id.' .ind7-a1 {
                    display: none;
                }
        
                '.$addon_id.' .p-index-a6 {
                    width: 100%;
                    height: 880px;
                    overflow: hidden;
                    position: relative;
                }
        
                '.$addon_id.' .p-index-a6-box {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding-top: 224px;
                    z-index: 3;
                }
        
                '.$addon_id.' .p-index-a6-a1 {
                    text-align: center;
                    color: #fff;
                    font-size: 48px;
                    font-weight: bolder;
                    margin-bottom: 144px;
                }
        
                '.$addon_id.' .p-index-a6-a1 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .p-index-a6-a2 {
                    width: 480px;
                    margin: 0 auto;
                }
        
                '.$addon_id.' .p-index-a6-a3 {
                    width: 480px;
                    height: 80px;
                    background: #fff;
                    padding: 0 24px;
                    line-height: 80px;
                }
        
                '.$addon_id.' .p-index-a6-a3>span {
                    font-size: 25.6px;
                    color: #d9012a;
                    font-weight: bolder;
                }
        
                '.$addon_id.' .fL {
                    float: left;
                }
        
                '.$addon_id.' .fR {
                    float: right;
                }
        
                '.$addon_id.' .p-index-a6-a3>img {
                    display: inline-block;
                    padding-top: 33.6px;
                    transition: .3s;
                }
        
                '.$addon_id.' .p-index-a6-a4 {
                    background: #fff;
                    color: #454545;
                    padding: 0 24px;
                    height: 160px;
                    overflow-y: auto;
                    display: none;
                    list-style: none;
                    font: normal normal 22.4px/1.5 sans-serif;
                }
        
                '.$addon_id.' .p-index-a6-a4 li {
                    line-height: 48px;
                }
        
                '.$addon_id.' .p-index-a6-a3.quest-active>img {
                    transform: rotate(90deg);
                    transition: .3s;
                    margin-top: 16px;
                }
            }
        
            @media only screen and (max-width: 499px) {
                '.$addon_id.' .p-index-a6 {
                    display: block;
                }
        
                '.$addon_id.' .ind7-a1 {
                    display: none;
                }
        
                '.$addon_id.' .p-index-a6 {
                    width: 100%;
                    height: 550px;
                    overflow: hidden;
                    position: relative;
                }
        
                '.$addon_id.' .p-index-a6-box {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding-top: 140px;
                    z-index: 3;
                }
        
                '.$addon_id.' .p-index-a6-a1 {
                    text-align: center;
                    color: #fff;
                    font-size: 30px;
                    font-weight: bolder;
                    margin-bottom: 90px;
                }
        
                '.$addon_id.' .p-index-a6-a1 span {
                    color: #d9012a;
                }
        
                '.$addon_id.' .p-index-a6-a2 {
                    width: 300px;
                    margin: 0 auto;
                }
        
                '.$addon_id.' .p-index-a6-a3 {
                    width: 300px;
                    height: 50px;
                    background: #fff;
                    padding: 0 15px;
                    line-height: 50px;
                }
        
                '.$addon_id.' .p-index-a6-a3>span {
                    font-size: 16px;
                    color: #d9012a;
                    font-weight: bolder;
                }
        
                '.$addon_id.' .fL {
                    float: left;
                }
        
                '.$addon_id.' .fR {
                    float: right;
                }
        
                '.$addon_id.' .p-index-a6-a3>img {
                    display: inline-block;
                    padding-top: 19px;
                    transition: .3s;
                }
        
                '.$addon_id.' .p-index-a6-a4 {
                    background: #fff;
                    color: #454545;
                    padding: 0 15px;
                    height: 100px;
                    overflow-y: auto;
                    display: none;
                    list-style: none;
                    font: normal normal 14px/1.5 sans-serif;
                }
        
                '.$addon_id.' .p-index-a6-a4 li {
                    line-height: 30px;
                }
        
                '.$addon_id.' .p-index-a6-a3.quest-active>img {
                    transform: rotate(90deg);
                    transition: .3s;
                    margin-top: 10px;
                }
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(1) {
                top: calc(75/753*100%);
                left: calc(785/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(2) {
                top: calc(195/753*100%);
                left: calc(830/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(3) {
                top: calc(245/753*100%);
                left: calc(780/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(4) {
                top: calc(260/753*100%);
                left: calc(530/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(5) {
                top: calc(300/753*100%);
                left: calc(595/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(6) {
                top: calc(310/753*100%);
                left: calc(660/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(7) {
                top: calc(365/753*100%);
                left: calc(715/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(8) {
                top: calc(410/753*100%);
                left: calc(735/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(9) {
                top: calc(440/753*100%);
                left: calc(695/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(10) {
                top: calc(420/753*100%);
                left: calc(625/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(11) {
                top: calc(340/753*100%);
                left: calc(490/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(12) {
                top: calc(290/753*100%);
                left: calc(390/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(13) {
                top: calc(370/753*100%);
                left: calc(350/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(14) {
                top: calc(510/753*100%);
                left: calc(430/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(15) {
                top: calc(470/753*100%);
                left: calc(190/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(16) {
                top: calc(630/753*100%);
                left: calc(410/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(17) {
                top: calc(725/753*100%);
                left: calc(570/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(18) {
                top: calc(630/753*100%);
                left: calc(550/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(19) {
                top: calc(685/753*100%);
                left: calc(635/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(20) {
                top: calc(570/753*100%);
                left: calc(510/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(21) {
                top: calc(490/753*100%);
                left: calc(535/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(22) {
                top: calc(545/753*100%);
                left: calc(610/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(23) {
                top: calc(615/753*100%);
                left: calc(645/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(24) {
                top: calc(645/753*100%);
                left: calc(685/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(25) {
                top: calc(635/753*100%);
                left: calc(770/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(26) {
                top: calc(580/753*100%);
                left: calc(700/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(27) {
                top: calc(550/753*100%);
                left: calc(675/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(28) {
                top: calc(465/753*100%);
                left: calc(595/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(29) {
                top: calc(440/753*100%);
                left: calc(535/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(30) {
                top: calc(500/753*100%);
                left: calc(755/912*100%);
            }
        
            '.$addon_id.' .ind7-a7>div:nth-child(31) {
                top: calc(245/753*100%);
                left: calc(190/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(1) {
                top: calc(138/753*100%);
                left: calc(810/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(2) {
                top: calc(140/753*100%);
                left: calc(835/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(3) {
                top: calc(160/753*100%);
                left: calc(850/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(4) {
                top: calc(145/753*100%);
                left: calc(865/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(5) {
                top: calc(125/753*100%);
                left: calc(850/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(6) {
                top: calc(85/753*100%);
                left: calc(775/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(7) {
                top: calc(115/753*100%);
                left: calc(765/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(8) {
                top: calc(105/753*100%);
                left: calc(790/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(9) {
                top: calc(95/753*100%);
                left: calc(850/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(10) {
                top: calc(90/753*100%);
                left: calc(820/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(11) {
                top: calc(60/753*100%);
                left: calc(785/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(12) {
                top: calc(180/753*100%);
                left: calc(805/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(13) {
                top: calc(215/753*100%);
                left: calc(775/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(14) {
                top: calc(235/753*100%);
                left: calc(770/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(15) {
                top: calc(225/753*100%);
                left: calc(760/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(16) {
                top: calc(245/753*100%);
                left: calc(740/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(17) {
                top: calc(265/753*100%);
                left: calc(770/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(18) {
                top: calc(260/753*100%);
                left: calc(795/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(19) {
                top: calc(285/753*100%);
                left: calc(765/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(20) {
                top: calc(250/753*100%);
                left: calc(690/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(21) {
                top: calc(280/753*100%);
                left: calc(715/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(22) {
                top: calc(285/724*100%); 
                left: calc(615/921*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(23) {
                top: calc(300/777*100%);
                left: calc(635/918*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(24) {
                top: calc(340/753*100%);
                left: calc(635/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(25) {
                top: calc(360/753*100%);
                left: calc(610/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(26) {
                top: calc(325/753*100%);
                left: calc(620/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(27) {
                top: calc(340/753*100%);
                left: calc(610/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(28) {
                top: calc(350/753*100%);
                left: calc(590/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(29) {
                top: calc(370/753*100%);
                left: calc(595/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(30) {
                top: calc(390/753*100%);
                left: calc(590/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(31) {
                top: calc(385/753*100%);
                left: calc(610/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(32) {
                top: calc(370/753*100%);
                left: calc(625/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(33) {
                top: calc(350/753*100%);
                left: calc(745/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(34) {
                top: calc(325/753*100%);
                left: calc(760/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(35) {
                top: calc(365/753*100%);
                left: calc(700/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(36) {
                top: calc(420/753*100%);
                left: calc(550/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(37) {
                top: calc(485/753*100%);
                left: calc(440/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(38) {
                top: calc(475/753*100%);
                left: calc(655/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(39) {
                top: calc(455/753*100%);
                left: calc(765/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(40) {
                top: calc(525/753*100%);
                left: calc(690/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(41) {
                top: calc(550/753*100%);
                left: calc(750/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(42) {
                top: calc(585/753*100%);
                left: calc(750/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(43) {
                top: calc(605/753*100%);
                left: calc(730/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(44) {
                top: calc(640/753*100%);
                left: calc(665/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(45) {
                top: calc(420/753*100%);
                left: calc(750/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(46) {
                top: calc(500/753*100%);
                left: calc(540/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(47) {
                top: calc(560/753*100%);
                left: calc(620/912*100%);
            }
        
            '.$addon_id.' .ind7-a9:nth-child(1) .ind7-a10 {
                top: -2px;
                left: -60px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(2) .ind7-a10 {
                top: 15px;
                left: -38px;
        
            }
        
            '.$addon_id.' .ind7-a9:nth-child(3) .ind7-a10 {
                top: 0px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(4) .ind7-a10 {
                top: 0px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(5) .ind7-a10 {
                top: -4px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(6) .ind7-a10 {
                top: 0px;
                left: -78px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(7) .ind7-a10 {
                top: -2px;
                left: -44px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(8) .ind7-a10 {
                top: 0px;
                left: 18px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(9) .ind7-a10 {
                top: -2px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(10) .ind7-a10 {
                top: -20px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(11) .ind7-a10 {
                top: -30px;
                left: -10px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(12) .ind7-a10 {
                top: -2px;
                left: -38px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(13) .ind7-a10 {
                top: -1px;
                left: 18px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(14) .ind7-a10 {
                top: -1px;
                left: 18px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(15) .ind7-a10 {
                top: -6px;
                left: -36px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(16) .ind7-a10 {
                top: -5px;
                left: -34px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(17) .ind7-a10 {
                top: -2px;
                left: -35px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(18) .ind7-a10 {
                top: -1px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(19) .ind7-a10 {
                top: -1px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(20) .ind7-a10 {
                top: -8px;
                left: -38px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(21) .ind7-a10 {
                top: -2px;
                left: -35px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(22) .ind7-a10 {
                top: -1px;
                left: -36px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(23) .ind7-a10 {
                top: -5px;
                left: 21px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(24) .ind7-a10 {
                top: -2px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(25) .ind7-a10 {
                top: -5px;
                left: 21px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(26) .ind7-a10 {
                top: -9px;
                left: -36px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(27) .ind7-a10 {
                top: -8px;
                left: -38px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(28) .ind7-a10 {
                top: -4px;
                left: -36px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(29) .ind7-a10 {
                top: -4px;
                left: -38px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(30) .ind7-a10 {
                top: 20px;
                left: -12px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(31) .ind7-a10 {
                top: 6px;
                left: 20px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(32) .ind7-a10 {
                top: 0px;
                left: 22px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(33) .ind7-a10 {
                top: -2px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(34) .ind7-a10 {
                top: -1px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(35) .ind7-a10 {
                top: 17px;
                left: -12px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(36) .ind7-a10 {
                top: -4px;
                left: -38px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(37) .ind7-a10 {
                top: 0px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(38) .ind7-a10 {
                top: -1px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(39) .ind7-a10 {
                top: -1px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(40) .ind7-a10 {
                top: -1px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(41) .ind7-a10 {
                top: -10px;
                left: 14px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(42) .ind7-a10 {
                top: -1px;
                left: 32px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(43) .ind7-a10 {
                top: 1px;
                left: 28px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(44) .ind7-a10 {
                top: 22px;
                left: 22px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(45) .ind7-a10 {
                top: 2px;
                left: 22px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(46) .ind7-a10 {
                top: 2px;
                left: 22px;
            }
        
            '.$addon_id.' .ind7-a9:nth-child(47) .ind7-a10 {
                top: 2px;
                left: 22px;
            }';
        }
        return $output;
    }

    public function js(){
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        $nav_section_id_hide = (isset($settings->nav_section_id_hide) && $settings->nav_section_id_hide) ? $settings->nav_section_id_hide : '';
        $nav_section_id_show = (isset($settings->nav_section_id_show) && $settings->nav_section_id_show) ? $settings->nav_section_id_show : '';
        $output = '';
        if($style === 'style1') {
            $output.='jQuery(document).ready(function($){
                $(".hd1-a66").each(function(i,v){
                    $(v).on("mouseenter",function(){
                        $(this).addClass("active");
                        $("#'.$nav_section_id_hide.'").hide();
                        $("#'.$nav_section_id_show.'").show();
                    }).on("mouseleave",function(){
                        $(this).removeClass("active");
                        if ($(window).scrollTop() <= 0) {
                            $("#'.$nav_section_id_hide.'").show();
                            $("#'.$nav_section_id_show.'").hide();
                        }
                    })
                })
            })';
        }
        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }

}