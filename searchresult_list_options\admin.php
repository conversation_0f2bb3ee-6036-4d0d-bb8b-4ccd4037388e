<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'searchresult_list_options',
        'title' => JText::_('搜索结果列表(检索版)'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '搜索',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),

                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => '',
                ),

                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
                    )
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'separator_options' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS')
                ),
                'options_show_values' => array(
                     'type' => 'select',
                     'title' => '选择搜索方式',
                     'desc' => '',
                     'values' => array(
                         'type0' => '模糊查询',
                         'type1' => '检索查询',
                     ),
                     'std' => 'type1'
                 ),


                'news_close' => array(
                    'type' => 'checkbox',
                    'title' => '关闭文章搜索',
                    'desc' => '关闭则不搜索文章列表',
                    'std' => 0
                ),
                'news_catid' => array(
                    'type' => 'category',
                    'title' => '选择要搜索的文章分类',
                    'multiple' => true,
                    'depends' => array(
                        array('news_close', '!=', '1'),
                    ),
                ),

                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '文章详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('news_close', '!=', '1'),
                    ),
                ),

                'pros_close' => array(
                    'type' => 'checkbox',
                    'title' => '关闭产品搜索',
                    'desc' => '关闭则不搜索产品列表',
                    'std' => 0
                ),

                'type_parent' => array(
                    'type' => 'select',
                    'title' => '选择要搜索的产品分类',
                    'values' => array(
                        'type1' => '一级分类',
                        'type2' => '二级分类',
                    ),
                    'std' => 'type1',
                    'depends' =>array(
                        array('pros_close', '!=', '1'),
                    ),
                ),
                'catordering' => array(
                    'type' => 'select',
                    'title' => JText::_('分类排序'),
                    'desc' => JText::_('从分类中选择排序的方式'),
                    'values' => array(
                        'sortasc' => JText::_('按排序id正序'),
                        'sortdesc' => JText::_('按排序id倒序'),
                    ),
                    'std' => 'sortasc',
                    'depends' =>array(
                        array('pros_close', '!=', '1'),
                    ),
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始搜索',
                    'std' => '1',
                    'depends' =>array(
                        array('pros_close', '!=', '1'),
                    ),
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '搜索n条分类(0为不限)',
                    'std' => '10',
                    'depends' =>array(
                        array('pros_close', '!=', '1'),
                    ),
                ),

                'product_page_id' => array(
                    'type' => 'select',
                    'title' => '产品详情页模版',
                    'desc' => '显示产品详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pros_close', '!=', '1'),
                    ),
                ),
                'art_type_selector' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'values' => array(
                        'art01' => '上图下文',
                        //  'art02' => '左图右文',
                        //  'art03' => '头文带图',
                        'art04' => '去掉图片',
                        'art05' => '布局5',
                        'art06' => '布局6',
                    ),
                    'std' => 'art01'
                ),
                'art73_label'=> array(
                    'type' => 'text',
                    'title' => JText::_('标签名称'),
                    'std' => '企业资讯',
                    'depends' => array(
                        array('art_type_selector', '=', 'art06'),
                    ),
                ),
                'padding_art73' => array(
                    'type' => 'padding',
                    'title' => JText::_('插件内边距'),
                    'std' => '0 0 40px 0',
                    'depends' => array(
                        array('art_type_selector', '=', 'art06'),
                    ),
                ),
                'li_width_art73' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项宽度（%）'),
                    'std' => 25,
                    'max' => 100,
                    'depends' => array(
                        array('art_type_selector', '=', 'art06'),
                    ),
                ),
                'li_bottom_art73' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项下边距'),
                    'std' => 30,
                    'max' => 200,
                    'depends' => array(
                        array('art_type_selector', '=', 'art06'),
                    ),
                ),
                'li_padding_art73' => array(
                    'type' => 'padding',
                    'title' => JText::_('列表项内边距'),
                    'std' => '0 10px 0 10px',
                    'depends' => array(
                        array('art_type_selector', '=', 'art06'),
                    ),
                ),

                'art_data_selector' => array(
                    'type' => 'select',
                    'title' => '日期布局',
                    'values' => array(
                        'left' => '左',
                        'right' => '右',
                    ),
                    'std' => 'left'
                ),
                'art_title_selector' => array(
                    'type' => 'select',
                    'title' => '标题布局',
                    'values' => array(
                        'left' => '左',
                        'right' => '右',
                        'center' => '中',
                    ),
                    'std' => 'left'
                ),

                'art_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => 100,
                    'min' => 0
                ),
                'art_date_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('日期字体大小'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 0
                ),
                'art_desc_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'max' => 100,
                    'min' => 0
                ),
                'art_desc_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'std' => '#000000',
                ),
                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10'
                ),

                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                ),

                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '',
                ),
                'intro_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT_DESC'),
                    'std' => '200',
                    'depends' => array('show_intro' => '1')
                ),
                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                ),
                'link_catid' => array(
                    'type' => 'category',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'depends' => array(
                        array('resource', '=', 'article'),
                        array('link_articles', '=', '1')
                    )
                ),

                'link_k2catid' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID_DESC'),
                    'depends' => array(
                        array('resource', '=', 'k2'),
                        array('link_articles', '=', '1')
                    ),
                    'values' => JwPageFactoryBase::k2CatList(),
                ),

                'all_articles_btn_text' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ALL_ARTICLES_BUTTON_TEXT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ALL_ARTICLES_BUTTON_TEXT_DESC'),
                    'std' => 'See all posts',
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_FAMILY'),
                    'depends' => array('link_articles' => '1'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-btn { font-family: "{{ VALUE }}"; }'
                    )
                ),

                'all_articles_btn_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
                    'values' => array(
                        'default' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
                        'primary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
                        'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
                        'success' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
                        'info' => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
                        'warning' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
                        'danger' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
                        'dark' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
                        'link' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
                    ),
                    'std' => 'default',
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_appearance' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
                        'outline' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
                        '3d' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
                    ),
                    'std' => 'flat',
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_background_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_DESC'),
                    'std' => '#444444',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom')
                    ),
                ),

                'all_articles_btn_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'std' => '#fff',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom')
                    ),
                ),

                'all_articles_btn_background_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER_DESC'),
                    'std' => '#222',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom')
                    ),
                ),

                'all_articles_btn_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER_DESC'),
                    'std' => '#fff',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom')
                    ),
                ),

                'all_articles_btn_size' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
                        'lg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
                        'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
                        'sm' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
                        'xs' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
                    ),
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_icon' => array(
                    'type' => 'icon',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_icon_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
                    'values' => array(
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'depends' => array('link_articles' => '1')
                ),

                'all_articles_btn_block' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
                    'values' => array(
                        '' => JText::_('JNO'),
                        'jwpf-btn-block' => JText::_('JYES'),
                    ),
                    'depends' => array('link_articles' => '1')
                ),

                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),

            ),
        ),
    )
);
