<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonCommodity_info extends JwpagefactoryAddons
{
    //在预览页面中渲染
    public function render()
    {


        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        //此处载入所有配置项变量
        //$class = (isset($settings->class) && $settings->class) ? $settings->class : '';

        //配置项变量
        $title_position = (isset($settings->title_position) && $settings->title_position) ? $settings->title_position : 'left';
        $title_color = (isset($settings->title_color) && $settings->title_color) ? $settings->title_color : '#000';
        $title_font_size = (isset($settings->title_font_size) && $settings->title_font_size) ? $settings->title_font_size : '20'; 
        $title_font_size_sm = (isset($settings->title_font_size_sm) && $settings->title_font_size_sm) ? $settings->title_font_size_sm : '20'; 
        $title_font_size_xs = (isset($settings->title_font_size_xs) && $settings->title_font_size_xs) ? $settings->title_font_size_xs : '20'; 
        $price_margin_top =  (isset($settings->price_margin_top) && $settings->price_margin_top) ? $settings->price_margin_top : '10'; 
        $price_margin_top_sm =  (isset($settings->price_margin_top_sm) && $settings->price_margin_top_sm) ? $settings->price_margin_top_sm : '10'; 
        $price_margin_top_xs =  (isset($settings->price_margin_top_xs) && $settings->price_margin_top_xs) ? $settings->price_margin_top_xs : '10'; 
        $price_margin_bottom =  (isset($settings->price_margin_bottom) && $settings->price_margin_bottom) ? $settings->price_margin_bottom : '10'; 
        $price_margin_bottom_sm =  (isset($settings->price_margin_bottom_sm) && $settings->price_margin_bottom_sm) ? $settings->price_margin_bottom_sm : '10'; 
        $price_margin_bottom_xs =  (isset($settings->price_margin_bottom_xs) && $settings->price_margin_bottom_xs) ? $settings->price_margin_bottom_xs : '10'; 
        $now_price_size = (isset($settings->now_price_size) && $settings->now_price_size) ? $settings->now_price_size : '14';
        $now_price_color = (isset($settings->now_price_color) && $settings->now_price_color) ? $settings->now_price_color : '#000';
        $old_price_size = (isset($settings->old_price_size) && $settings->old_price_size) ? $settings->old_price_size : '12';
        $old_price_color = (isset($settings->old_price_color) && $settings->old_price_color) ? $settings->old_price_color : '#000';
        $price_box_bgcolor = (isset($settings->price_box_bgcolor) && $settings->price_box_bgcolor) ? $settings->price_box_bgcolor : 'yellow';
        $price_margin_left = (isset($settings->price_margin_left) && $settings->price_margin_left) ? $settings->price_margin_left : '10';
        $old_now_margin = (isset($settings->old_now_margin) && $settings->old_now_margin) ? $settings->old_now_margin : '10'; 
        $show_btn = (isset($settings->show_btn) && $settings->show_btn) ? $settings->show_btn : 0; 
        $show_desc_btn = (isset($settings->show_desc_btn) && $settings->show_desc_btn) ? $settings->show_desc_btn : 0;
        $show_content_btn = (isset($settings->show_content_btn) && $settings->show_content_btn) ? $settings->show_content_btn : 0;
        $btn_bgcolor = (isset($settings->btn_bgcolor) && $settings->btn_bgcolor) ? $settings->btn_bgcolor : '#fd8516';
        $btn_color = (isset($settings->btn_color) && $settings->btn_color) ? $settings->btn_color : '#fff';
        $btn_border_radius = (isset($settings->btn_border_radius) && $settings->btn_border_radius) ? $settings->btn_border_radius : '14';
        $img_margin_right = (isset($settings->img_margin_right) && $settings->img_margin_right) ? $settings->img_margin_right : '10';  
        $title_h2_button = (isset($settings->title_h2_button) && $settings->title_h2_button) ? $settings->title_h2_button : 'yes';

        $show_desc_color = (isset($settings->show_desc_color) && $settings->show_desc_color) ? $settings->show_desc_color : '#000'; 
        $show_desc_fontsize = (isset($settings->show_desc_fontsize) && $settings->show_desc_fontsize) ? $settings->show_desc_fontsize : 14; 
        $show_desc_top = (isset($settings->show_desc_top) && $settings->show_desc_top) ? $settings->show_desc_top : 0; 
        $show_desc_bottom = (isset($settings->show_desc_bottom) && $settings->show_desc_bottom) ? $settings->show_desc_bottom : 0;

        $content_top = (isset($settings->content_top) && $settings->content_top) ? $settings->content_top : '';
        $content_title_color = (isset($settings->content_title_color) && $settings->content_title_color) ? $settings->content_title_color : '#333';
        $content_title_fontsize = (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) ? $settings->content_title_fontsize : 18;

        // 按钮文字内容
        $btn_text = (isset($settings->btn_text) && $settings->btn_text) ? $settings->btn_text : '马上咨询';
        // 一键电话
        $show_phonebtn = (isset($settings->show_phonebtn) && $settings->show_phonebtn) ? $settings->show_phonebtn : 0; 
        $phonebtn_text = (isset($settings->phonebtn_text) && $settings->phonebtn_text) ? $settings->phonebtn_text : '一键电话';
        $phonebtn_bgcolor = (isset($settings->phonebtn_bgcolor) && $settings->phonebtn_bgcolor) ? $settings->phonebtn_bgcolor : '#fd8516';
        $phonebtn_color = (isset($settings->phonebtn_color) && $settings->phonebtn_color) ? $settings->phonebtn_color : '#fff';
        $phonebtn_border_radius = (isset($settings->phonebtn_border_radius) && $settings->phonebtn_border_radius) ? $settings->phonebtn_border_radius : '14';
        $phonebtn_left = (isset($settings->phonebtn_left) && $settings->phonebtn_left) ? $settings->phonebtn_left : 0; 

        //获取文章详情的数据源
        $app = JFactory::getApplication();
        $input = $app->input;

        $article_id = $input->get('detail');

        $article = JwPageFactoryBase::getShopsById($article_id);

        // print_r($article);

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $output = '';
        $output .= "
        <style>
        ".$addon_id." .commodity_info {
            display: flex;
        }
        ".$addon_id." .commodity_info img {
            margin-right: 10px;
            width: 50%;
            height:400px;
            margin-right:${img_margin_right}
        }
        ".$addon_id." .infoBox {
            width: 50%;
            display:flex;
            flex-direction:column;
        }
        ".$addon_id." .commodity_info_title {
            text-align:${title_position};
            color:${title_color};
            font-size:${title_font_size}px;
            white-space: pre-wrap;
        }
        ".$addon_id." .commodity_info_introtext {
            color:${show_desc_color};
            margin-top:${show_desc_top}px;
            margin-bottom:${show_desc_bottom}px;
            font-size:${show_desc_fontsize}px;
            padding-bottom:10px;
        }
        ".$addon_id." .priceBox {
            display:flex;
            align-items:center;
            margin-top:${price_margin_top}px;
            margin-bottom:${price_margin_bottom}px;
            height: 50px;
            background-color: ${price_box_bgcolor};
        }
        ".$addon_id." .now_price {
            font-size:${now_price_size}px;
            color:${now_price_color};
            margin-left:${price_margin_left}px;
            margin-right:${old_now_margin}px;
        }
        ".$addon_id." .old_price {
            text-decoration:line-through;
            font-size:${old_price_size}px;
            color:${old_price_color};
        }
        ".$addon_id." .btnBox {
            display:flex;
        }

        ".$addon_id." .btn {
            border-radius: ${btn_border_radius}px;
            padding:10px 20px;
            background-color: ${btn_bgcolor};
            color:  ${btn_color};
            cursor: pointer;
        }

        ".$addon_id." .btn1 {
            border-radius: ${phonebtn_border_radius}px;
            padding:10px 20px;
            background-color: ${phonebtn_bgcolor};
            color:  ${phonebtn_color};
            cursor: pointer;
            margin-left:${phonebtn_left}px;
        }
        ".$addon_id." .btn2 {display:none;}

        
        ".$addon_id." .showbox {
            height: 100vh;
            width: 100%;
            position: fixed;
            z-index: 100000000;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }
        ".$addon_id." .imgerBox {
            width: 440px;
            height: 440px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 2px 2px 11px #4d4b4b;
            border-radius: 5px;
        }
        ".$addon_id." .showbox1 {
            height: 100vh;
            width: 100%;
            position: fixed;
            z-index: 100000000;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }
        ".$addon_id." .imgerBox1 {
            width: 440px;
            height: 140px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 2px 2px 11px #4d4b4b;
            border-radius: 5px;
        }
        ".$addon_id." #close1 {
            position: absolute;
            right: 10px;
            top: 10px;
            height: 20px;
            width: 20px;
            line-height: 20px;
            text-align: center;
        }
        ".$addon_id." .imgerBox img {
            height: 265px;
            width: 265px;
        }
        ".$addon_id." .title {
            color: #333;
            margin-top: 30px;
        }  
        ".$addon_id." .show {
            display: block;
        }
        ".$addon_id." #close {
            position: absolute;
            right: 10px;
            top: 10px;
            height: 20px;
            width: 20px;
            line-height: 20px;
            text-align: center;
        }
        ".$addon_id." .content-title {
            font-size: ${content_title_fontsize}px;
            color: ${content_title_color};
            font-weight: bold;
            margin-top: ${content_top}px;
        }

        @media (min-width: 768px) and (max-width: 991px)  {
            ".$addon_id." .commodity_info_title {
                font-size:${title_font_size_sm}px;
            }
            ".$addon_id." .priceBox {
                margin-top:${price_margin_top_sm}px;
                margin-bottom:${price_margin_bottom_sm}px;
            }
        }
        @media (max-width: 767px) {
            ".$addon_id." .commodity_info_title {
                font-size:${title_font_size_xs}px;
            }
            ".$addon_id." .priceBox {
                margin-top:${price_margin_top_xs}px;
                margin-bottom:${price_margin_bottom_xs}px;
            }

            ".$addon_id." .btnBox {
                display:block;
            }

            ".$addon_id." .btn {
                border-radius: ${btn_border_radius}px;
                padding:10px 10px;
                background-color: ${btn_bgcolor};
                color:  ${btn_color};
                cursor: pointer;
                width:100px;
            }
            ".$addon_id." .btn1 {display:none;}
            ".$addon_id." .btn2 {
                display:block;
                border-radius: ${phonebtn_border_radius}px;
                padding:10px 10px;
                background-color: ${phonebtn_bgcolor};
                color: ${phonebtn_color};
                cursor: pointer;
                margin-left:0px;
                margin-top:10px;
                width:100px;
            }
            ".$addon_id." .btn2 a{
                display:block;
                color: ${phonebtn_color};
            }
            ".$addon_id." .imgerBox {
                width: 240px;
                height: 240px;
            }
            ".$addon_id." .imgerBox img {
                height: 150px;
                width: 150px;
            }
        }
        
        </style>";

          $output .= "    <div class='commodity_info'>";
          $output .= "    <img src='{$article->image_intro}' alt=''>";
          $output .= "     <div class='infoBox'>";
          $output .= "        <div class='commodity_info_title'>{$article->title}</div>";
          $output .= "        <div class='priceBox'>";
          $output .= "           <span class='now_price'>￥{$article->sale_price}</span>";
          $output .= "             <span class='old_price'>￥{$article->original_price}</span>";
          $output .= "         </div>";
          if($show_desc_btn)
          {
            $output .= "        <div class='commodity_info_introtext'>{$article->introtext}</div>";
          }
          
        if($show_btn || $show_phonebtn) {
            $output .= "         <div class='btnBox'>";
            if($show_btn){
                $output .= "           <div class='btn'>";
                $output .= "               ".$btn_text."";
                $output .= "           </div>";
            }
            if($show_phonebtn){
                $output .= "           <div class='btn1'>";
                $output .= "               ".$phonebtn_text."";
                $output .= "           </div>";

                $output .= "           <div class='btn2'><a href='tel:{$article->tel}'>";
                $output .= "               ".$phonebtn_text."";
                $output .= "           </a></div>";
            }
            $output .= "        </div>";
        }

          $output .= "    </div>";
          $output .= "     </div>";
          if($show_content_btn)
          {
            $output .= "        <div>
                <p class='content-title'>商品详情</p>
                {$article->fulltext}
            </div>";
          }
            $output .="  <div class='showbox' id='showbox'>
                <div class='imgerBox'>
                    <img src='{$article->qrcode}' alt=''>
                    <div class='title'>请使用手机微信扫描二维码</div>
                    <div id='close'>
                        x
                    </div>
                </div>
            </div>";
            $output .="  <div class='showbox1' id='showbox1'>
                <div class='imgerBox1'>
                    <div>{$article->tel}</div>
                    <div id='close1'>
                        x
                    </div>
                </div>
            </div>
            <script>
                jQuery(function(e){
                    e(document).on('click','#close1',function(o) {
                        o.preventDefault(),
                        e(this).parents('.showbox1').hide()
                    })
                    e(document).on('click','.btn1',function(o) {
                        o.preventDefault(),
                        e('.showbox1').show()
                    })
                })
            </script>
            ";
        if($title_h2_button == 'yes')
        {
            $output .= '
            <!-- <script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script> -->
            <script language="javascript">
                var title = $(\'.commodity_info_title\').text()
                $(\'title\').text(title)
            </script>';
        }

 
        return $output;
    }
 
    //引用js文件，同时作用于编辑器和预览页
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/commodity_info.js'
        );
        return $scripts;
    }

    //引用css文件，同时作用于编辑器和预览页
    public function stylesheets()
    {
        $style_sheet = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css'

        );
        return $style_sheet;
    }

    //在预览页面中使用的JS脚本
    public function js()
    {
        $js = '';
        return $js;
    }

    //在预览页面中使用的css样式
    public function css()
    {
        $css = '';
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $app = JFactory::getApplication();
        $input = $app->input;

        $article_id = $input->get('detail');

        $article = JwPageFactoryBase::getShopsById($article_id);
        $output = "";
        $output .= '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        let price_margin_top = (!_.isEmpty(data.price_margin_top) && data.price_margin_top.md) ? data.price_margin_top.md : 10;
        let price_margin_top_sm = (!_.isEmpty(data.price_margin_top) && data.price_margin_top.sm) ? data.price_margin_top.sm : 10;
        let price_margin_top_xs = (!_.isEmpty(data.price_margin_top) && data.price_margin_top.xs) ? data.price_margin_top.xs : 10;
        let price_margin_bottom = (!_.isEmpty(data.price_margin_bottom) && data.price_margin_bottom.md) ? data.price_margin_bottom.md : 10;
        let price_margin_bottom_sm = (!_.isEmpty(data.price_margin_bottom) && data.price_margin_bottom.sm) ? data.price_margin_bottom.sm : 10;
        let price_margin_bottom_xs = (!_.isEmpty(data.price_margin_bottom) && data.price_margin_bottom.xs) ? data.price_margin_bottom.xs : 10;
        let now_price_size = (!_.isEmpty(data.now_price_size) && data.now_price_size) ? data.now_price_size : 14; 
        let now_price_color = (!_.isEmpty(data.now_price_color) && data.now_price_color) ? data.now_price_color : "#000"; 
        let old_price_size = (!_.isEmpty(data.old_price_size) && data.old_price_size) ? data.old_price_size : 14; 
        let old_price_color = (!_.isEmpty(data.old_price_color) && data.old_price_color) ? data.old_price_color : "#000"; 
        let price_box_bgcolor = (!_.isEmpty(data.price_box_bgcolor) && data.price_box_bgcolor) ? data.price_box_bgcolor : "yellow"; 
        let price_margin_left = (!_.isEmpty(data.price_margin_left) && data.price_margin_left) ? data.price_margin_left : "10";  
        // let price_margin_right = (!_.isEmpty(data.price_margin_right) && data.price_margin_right) ? data.price_margin_right : "10"; 
        let old_now_margin = (!_.isEmpty(data.old_now_margin) && data.old_now_margin) ? data.old_now_margin : "10";  
        let show_btn = (!_.isEmpty(data.show_btn) && data.show_btn) ? data.show_btn : 0;  
        let btn_bgcolor = (!_.isEmpty(data.btn_bgcolor) && data.btn_bgcolor) ? data.btn_bgcolor : "#fd8516";
        let btn_color = (!_.isEmpty(data.btn_color) && data.btn_color) ? data.btn_color : "#fff";
        let btn_border_radius = (!_.isEmpty(data.btn_border_radius) && data.btn_border_radius) ? data.btn_border_radius : "10"; 
        let img_margin_right = (!_.isEmpty(data.img_margin_right) && data.img_margin_right) ? data.img_margin_right : "10"; 
        
        var content_top = data.content_top;
        var content_title_color = data.content_title_color || "#333";
        var content_title_fontsize = data.content_title_fontsize || 18;
        
        var btn_text = data.btn_text || "马上咨询";
        let show_phonebtn = (!_.isEmpty(data.show_phonebtn) && data.show_phonebtn) ? data.show_phonebtn : 0;  
        let phonebtn_text = data.phonebtn_text || "一键电话";
        let phonebtn_bgcolor = (!_.isEmpty(data.phonebtn_bgcolor) && data.phonebtn_bgcolor) ? data.phonebtn_bgcolor : "#fd8516";
        let phonebtn_color = (!_.isEmpty(data.phonebtn_color) && data.phonebtn_color) ? data.phonebtn_color : "#fff";
        let phonebtn_border_radius = (!_.isEmpty(data.phonebtn_border_radius) && data.phonebtn_border_radius) ? data.phonebtn_border_radius : "10"; 
        let phonebtn_left = (!_.isEmpty(data.phonebtn_left) && data.phonebtn_left) ? data.phonebtn_left : "0";

        #>';
        $output .= "

        <style>

        {{ addonId }} .commodity_info {
            display: flex;
        }
        {{ addonId }} .commodity_info img {
            margin-right: 10px;
            width: 50%;
            height:400px;
            margin-right:{{img_margin_right}}px;
        }
        {{ addonId }} .infoBox {
            width: 50%;
            display:flex;
            flex-direction:column;
        }
        {{ addonId }} .commodity_info_title {
            text-align:{{data.title_position}};
            color:{{data.title_color}};
            font-size:{{data.title_font_size.md}}px;
            white-space: pre-wrap;
        }
        {{ addonId }} .priceBox {
            display:flex;
            align-items:center;
            height: 50px;
            background-color: {{price_box_bgcolor}};
            margin-top:{{price_margin_top}}px;
            margin-bottom:{{price_margin_bottom}}px;
        }
        {{ addonId }} .now_price {
            font-size:{{now_price_size}}px;
            color:{{now_price_color}};
            margin-left:{{price_margin_left}}px;
            margin-right:{{old_now_margin}}px;
        }
        {{ addonId }} .commodity_info_introtext {
            color:{{data.show_desc_color}};
            margin-top:{{data.show_desc_top}}px;
            margin-bottom:{{data.show_desc_bottom}}px;
            font-size:{{data.show_desc_fontsize}}px;
            padding-bottom:10px;
            
        }
        {{ addonId }} .old_price {
            text-decoration:line-through;
            font-size:{{old_price_size}}px;
            color:{{old_price_color}};
        }
        {{ addonId }} .btnBox {
            display:flex;
        }
        {{ addonId }} .btn {
            border-radius: {{btn_border_radius}}px;
            padding:10px 20px;
            background-color: {{btn_bgcolor}};
            color: {{btn_color}};
            cursor: pointer;
        }
        {{ addonId }} .btn1 {
            border-radius: {{phonebtn_border_radius}}px;
            padding:10px 20px;
            background-color: {{phonebtn_bgcolor}};
            color: {{phonebtn_color}};
            cursor: pointer;
            margin-left:{{phonebtn_left}}px;
        }
        @media (min-width: 768px) and (max-width: 991px)  {
            {{ addonId }} .commodity_info_title {
                font-size:{{data.title_font_size.sm}}px;
            }
            {{ addonId }} .priceBox {
                margin-top:{{price_margin_top_sm}}px;
                margin-bottom:{{price_margin_bottom_sm}}px;
            }
        }
        @media (max-width: 767px) {
            {{ addonId }} .commodity_info_title {
                font-size:{{data.title_font_size.xs}}px;
            }
            {{ addonId }} .priceBox {
                margin-top:{{price_margin_top_xs}}px;
                margin-bottom:{{price_margin_bottom_xs}}px;
            }
        }
        {{ addonId }} .showbox {
            height: 100vh;
            width: 100%;
            position: fixed;
            z-index: 100000000;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }
        {{ addonId }} .imgerBox {
            width: 440px;
            height: 440px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 2px 2px 11px #4d4b4b;
            border-radius: 5px;
        }
        {{ addonId }} .showbox1 {
            height: 100vh;
            width: 100%;
            position: fixed;
            z-index: 100000000;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }
        {{ addonId }} .imgerBox1 {
            width: 440px;
            height: 140px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 2px 2px 11px #4d4b4b;
            border-radius: 5px;
        }
        {{ addonId }} #close1 {
            position: absolute;
            right: 10px;
            top: 10px;
            height: 20px;
            width: 20px;
            line-height: 20px;
            text-align: center;
        }
        {{ addonId }} .imgerBox img {
            height: 265px;
            width: 265px;
        }
        {{ addonId }} .title {
            color: #333;
            margin-top: 30px;
        }
        {{ addonId }} .show {
            display: block;
        }
        {{ addonId }} #close {
            position: absolute;
            right: 10px;
            top: 10px;
            height: 20px;
            width: 20px;
            line-height: 20px;
            text-align: center;
        }
        {{ addonId }} .content-title {
            font-size: {{ content_title_fontsize }}px;
            color: {{ content_title_color }};
            font-weight: bold;
            margin-top: {{ content_top }}px;
        }
        </style>
  
     
        <div class='commodity_info'>
        <img src='https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg' alt=''>
        <div class='infoBox'>
            <div class='commodity_info_title'>这里是商品标题</div>
            <div class='priceBox'>
                <span class='now_price'>￥69.99</span>
                <span class='old_price'>￥120.88</span>
            </div>
            <# if (data.show_desc_btn) { #>
                <div class='commodity_info_introtext'>
                这里是商品商品简介</div>
                <# } #>
            <# if (data.show_btn || data.show_phonebtn) { #>
                <div class='btnBox'>
                    <# if (data.show_btn) { #>
                        <span class='btn'>
                            {{btn_text}}
                        </span>
                    <# } #>
                    <# if (data.show_phonebtn) { #>
                        <span class='btn1'>
                            {{phonebtn_text}}
                        </span>
                    <# } #>
                </div>
            <# } #>
            
        </div>
        </div>
        <# if (data.show_content_btn) { #>
            <div>
                <p class='content-title'>商品详情</p>
                这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情这里是商品详情
            </div>
        <# } #>
            <div class='showbox' id='showbox'>
                <div class='imgerBox'>
                    <img src='https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg' alt=''>
                    <div class='title'>请使用手机微信扫描二维码</div>
                    <div id='close'>
                        x
                    </div>
                </div>
            </div>
            <div class='showbox1' id='showbox1'>
                <div class='imgerBox1'>
                    <div>188XXXXXXXX</div>
                    <div id='close1'>
                        x
                    </div>
                </div>
            </div>
        ";


        return $output;
    }

}