<?php
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonArticles_list02 extends JwpagefactoryAddons
{
	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$page = $_GET['page'] ?? 1;
		$zcatid = $_GET['catid'] ?? 0;


		$layout_id = $_GET['layout_id'] ?? 0;
		if (!is_numeric($_GET['page'])) {
			$page = 1;
		}

		$zcpcatid = $_GET['zcpcatid'] ?? 1000000000000;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$page_view_name = isset($_GET['view']);
		$settings = $this->addon->settings;

		// 文章列表 布局 从80开始
		$art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : 'art80';

		/* 数据相关 */
		// 分类id
		$catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
		if ($zcatid) {
			$catid = $zcatid;
		}
		// 包含子分类
		$include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
		// 文章类型
		$post_type = '';
		$tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
		// 排序
		$ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'sortdesc';
		// 每页条数
		$limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
		// 列数
		$columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
		// 手机端列数
		$columns_xs = (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : 2;
		// 指定文章详情页ID
		$detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
		// 详情页跳转方式
		$detail_target = (isset($settings->detail_target) && $settings->detail_target) ? $settings->detail_target : '_self';

		/* 文章列表 标题 */
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		$output = '';

		/* 公共部分  获取列表数据 */
		$article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
		require_once $article_helper;
		$items = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
		$items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $catid, $include_subcat, $company_id, $site_id, $post_type, $tagids);
		// print_r($items);die;
		// 列表数据为空 占位提示
		if (!count($items)) {
			$output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
			return $output;
		}
		if (count((array)$items)) {
			// 公共样式
			$output .= "
			<style>
				{$addon_id} .jwpf-addon-article {
					width: calc(100% / {$columns});
					padding: 0 15px;
					box-sizing: border-box;
				}
				@media (max-width:768px) {
					{$addon_id} .jwpf-addon-article {
						width: calc(100% / {$columns_xs});
					}
				}
			</style>
			";

			if($art_type_selector === 'art80'){
				$output .= '<div class="art_list_id jwpf-addon-articles">';
				if ($title) {
					$output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
				}
				$output .= $this->itemClass();  // 文章样式文件
				$output .= '<div class="jwpf-addon-content">';
				$output .= '<div class="jwpf-row">';
				foreach ($items as $key => $item) {
					$output .= '<div class="jwpf-addon-article">';
					$output .= '<a href="' . $item->link  . '" target="' . $detail_target . '">';
					$output .= $this->artListItem($item, 0);  // 文章列表项
					$output .= '</a>';
					$output .= '</div>'; // .jwpf-addon-article
				}
				$output .= '</div>'; // .jwpf-row
				$output .= '</div>'; // .jwpf-addon-content
				$output .= $this->pagination($items_count, $limit);  // 翻页
				$output .= '</div>'; // .jwpf-addon-articles
			}elseif($art_type_selector === 'art81'){
				// 变量
				$id = $this->addon->id;
				$autoplay_art81 = isset($settings->autoplay_art81) ? $settings->autoplay_art81 : 1;
				// 切换间隔
				$interval_art81 = (isset($settings->interval_art81) && $settings->interval_art81) ? ((int)$settings->interval_art81 * 1000) : 3000;
				// 切换速度
				$speed_art81 = (isset($settings->speed_art81) && $settings->speed_art81) ? $settings->speed_art81 : 1100;
				// 切换动画
				$animate_dh_art81 = (isset($settings->animate_dh_art81) && $settings->animate_dh_art81) ? $settings->animate_dh_art81 : 'fade';
				// 箭头翻页
				$is_swiper_button_art81 = (isset($settings->is_swiper_button_art81) && $settings->is_swiper_button_art81) ? $settings->is_swiper_button_art81 : '0';
				// 轮播点
				$is_swiper_pagination_art81 = isset($settings->is_swiper_pagination_art81) ? $settings->is_swiper_pagination_art81 : 1;
				// 上翻页按钮
				$swiper_button_prev_art81 = isset($settings->swiper_button_prev_art81) ? $settings->swiper_button_prev_art81 : 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png';
				// 下翻页按钮
				$swiper_button_next_art81 = isset($settings->swiper_button_next_art81) ? $settings->swiper_button_next_art81 : 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png';
				// 移入上翻页按钮
				$swiper_button_prev_hover_art81 = isset($settings->swiper_button_prev_hover_art81) ? $settings->swiper_button_prev_hover_art81 : $swiper_button_prev_art81;
				// 移入下翻页按钮
				$swiper_button_next_hover_art81 = isset($settings->swiper_button_next_hover_art81) ? $settings->swiper_button_next_hover_art81 : $swiper_button_next_art81;

				$output .= $this->itemClass();  // 文章样式文件

				$output.='<div class="swiper-container">';
					$output.='<div class="swiper-wrapper">';
						foreach ($items as $key => $item) {
							$output .= '<div class="swiper-slide">';
								// 跳转链接
								$output .= '<a href="' . $item->link  . '" target="' . $detail_target . '">';
									$output .= $this -> artListItem($item, 0);
								$output .= '</a>';
							$output .= '</div>'; // .swiper-slide
						}
					$output.='</div>'; // .swiper-wrapper
					// 翻页
					$output.='<div class="swiper-pagination"></div>';
					// 箭头翻页
					if($is_swiper_button_art81){
						$output.='<div class="swiper-button-prev">
							<img src="'.$swiper_button_prev_art81.'"></img>
						</div>';
						$output.='<div class="swiper-button-next">
							<img src="'.$swiper_button_next_art81.'"></img>
						</div>';
					}
				$output.='</div>'; // .swiper-container
				// 轮播js
				$output .= '<script type="text/javascript">
					var mySwiper' . $id . ' = new Swiper ("' . $addon_id . ' .swiper-container", {
						loop: true,';
						if($is_swiper_pagination_art81){
							$output.='pagination: {
								el: "' . $addon_id . ' .swiper-pagination",
								clickable :true,
							},';
						}
						if ($is_swiper_button_art81){
							$output.='navigation: {
								nextEl: "' . $addon_id . ' .swiper-button-next",
								prevEl: "' . $addon_id . ' .swiper-button-prev",
						  	},';
						}
						if ($autoplay_art81 == 1) {
							$output .= '
							autoplay: {
								delay: ' . $interval_art81 . ',
								disableOnInteraction: false,
							},
							';
						}
						$output .= 'speed: ' . $speed_art81 . ',
						effect : "' . $animate_dh_art81 . '",
						fadeEffect: {
							crossFade: true,
						},
					});
					// 鼠标移入翻页按钮更换图片
					';
					if($is_swiper_button_art81){
						$output.= '$("' . $addon_id . ' .swiper-button-prev").unbind().on("mouseenter", function(){
							$(this).find("img").attr("src", "'.($swiper_button_prev_hover_art81 ?? $swiper_button_prev_art81).'");
						}).on("mouseleave", function(){
							$(this).find("img").attr("src", "'.$swiper_button_prev_art81.'");
						})
						$("' . $addon_id . ' .swiper-button-next").unbind().on("mouseenter", function(){
							$(this).find("img").attr("src", "'.($swiper_button_next_hover_art81 ?? $swiper_button_next_art81).'")
						}).on("mouseleave", function(){
							$(this).find("img").attr("src", "'.$swiper_button_next_art81.'");
						})';
					}
				$output.='</script>';
			}else if($art_type_selector == 'art82'){
				$show_btn_82 = ($settings->show_btn_82) ? $settings->show_btn_82 : 1;

				$output .= $this->itemClass();  // 文章样式文件
				$output .= '<div class="art_82_wrap '.$class.'">';
				foreach ($items as $key => $item) {
					$output .= '<div class="art_82_item">';
						if(!$show_btn_82){
							$output .= '<a href="'.$item -> link.'" class="art_82_link"  target="' . $detail_target . '"></a>';
						}
						$output .= $this->artListItem($item, 0);  // 文章列表项
					$output .= '</div>';
				}
				$output .= '</div>';
			}
			elseif ($art_type_selector == 'art83')
			{
				$output .= $this->itemClass();  // 文章样式文件
				$output .= '
				<div class="container">
					<div class="swiper-container">
						<div class="swiper-wrapper">
				';
					foreach ($items as $key => $item) {
						$output .= $this->artListItem($item, 0);
					}
				$output .= '
						</div>
					</div>
					<!-- 导航按钮 -->
					<div class="swiper-button-prev"></div>
					<div class="swiper-button-next"></div>
					<div class="content-display-area">';
						foreach ($items as $key => $item) {
							$active = '';
							if($key == 0){
								$active = ' active';
							}
							$output .= '<a class="slide-content oneline'.$active.'" '.$this->handleLink($item) . '>' . strip_tags($item->introtext) . '</a>';
						}
						$output .= '
					</div>
				</div>
				';
				$output .= $this->art83Js();
			}
		}
		return $output;
	}

	// 处理跳转
	public function handleLink($item)
	{
		$settings = $this->addon->settings;
		$detail_target = (isset($settings->detail_target) && $settings->detail_target) ? $settings->detail_target : '_self';
		$target = '';
		$link = '';
		if($detail_target == '_blank' ){
			$target = ' target="_blank"';
		}
		if($detail_target == 'none' ){
			$link = 'javascript:void(0);';
		} else {
			$link = $item->link;
		}
		return 'href="' . $link . '"' . $target;
	}

	// 文章列表被循环的部分 is_large 是否大图模式
	public function artListItem($item, $is_large)
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		// 布局样式
		$art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : 'art80';
		// 是否显示简介
		$show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
		// 简介字数
		$intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
		// 是否启用详情当做简介
		$use_detail_instead_intro = (isset($settings->use_detail_instead_intro) && $settings->use_detail_instead_intro) ? $settings->use_detail_instead_intro : '0';

		$image = $item->image_thumbnail ? $item->image_thumbnail : '';

		// 图片占位
		$img_placeholder = 'https://oss.lcweb01.cn/joomla/20230614/214c4ca925d12e21678c9e3fd4a85f10.png';

		$output = '';
		if ($art_type_selector == 'art80') {
			$output .= '
				<div class="list-item">
					<div class="img-box">';
					$output.='<img src="' . $image . '" alt="" onerror="this.src = \''.$img_placeholder.'\';this.style.opacity = 0;">';
					$output.='</div>
					<div class="right-info">
						<div class="title-box">
							<h3 class="title">' . $item->title . '</h3>
							<span class="time">[' . date('Y-m-d', strtotime($item->created)) . ']</span>
						</div>';
			if ($show_intro == 1) {
				if($use_detail_instead_intro){
					$output .= '<p class="intro">' . mb_substr(strip_tags($item->fulltext), 0, $intro_limit, 'UTF-8') . '...</p>';
				}else{
					$output .= '<p class="intro">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</p>';
				}
			}
			$output .= '
					</div>
				</div>
			';
		}elseif($art_type_selector === 'art81'){
			// 封面图
			$output.='<div class="img-box">';
			$output.='<img src="' . $image . '" onerror="this.src = \''.$img_placeholder.'\';this.style.opacity = 0;">';
			$output.='</div>';

			// 标题
			$output.='<p class="title">' . $item->title . '</p>';
		}elseif($art_type_selector === 'art82'){
			$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
			$detail_target = (isset($settings->detail_target) && $settings->detail_target) ? $settings->detail_target : '_self';
			$icon_before_title = isset($settings->icon_before_title) ? $settings->icon_before_title : 0;
			$show_time_82 = isset($settings->show_time_82) ? $settings->show_time_82 : 1;
			$time_format_82 = isset($settings->time_format_82) ? $settings->time_format_82 : 'Y-m-d';
			$show_btn_82 = ($settings->show_btn_82) ? $settings->show_btn_82 : 1;
			$btn_text_82 = ($settings->btn_text_82) ? $settings->btn_text_82 : '查看详情 >>';
			$output .= '<div class="art_82_item_content">
				<div class="art_82_item_title">
					<div class="art_82_title_left">
						'.($icon_before_title ? '<i class="fa fa-genderless"></i>' : '').'
						<' . $heading_selector . ' class="art_82_title class="jwpf-addon-title"">' . $item->title . '</' . $heading_selector . '>
					</div>';
					if($show_time_82){
						$output .= '<time datetime="'.date($time_format_82, strtotime($item->created)).'" class="art_82_time">'.date($time_format_82, strtotime($item->created)).'</time>';
					}
				$output .= '</div>';
				if ($show_intro == 1) {
					if($use_detail_instead_intro){
						$output .= '<div class="art_82_item_text">' . mb_substr(strip_tags($item->fulltext), 0, $intro_limit, 'UTF-8') . '...</div>';
					}else{
						$output .= '<div class="art_82_item_text">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</div>';
					}
				}
				if($show_btn_82){
					$output .= '<div class="art_82_item_btn">
						<a href="'.$item -> link.'" class="art_82_btn" target="' . $detail_target . '">'.$btn_text_82.'</a>
					</div>';
				}
			$output .= '</div>';
		}
		elseif ($art_type_selector === 'art83')
		{
			$output .= '
				<a class="swiper-slide" '.$this->handleLink($item) . '>
					<div class="image-item">
						<img class="slide-image" src="' . ($image ? $image : $img_placeholder) .'" alt="" />
						<div class="blue-info-card">
							<div class="card-title oneline">' . $item->title . '</div>
							<div class="card-subtitle oneline">' . $item->label1 . '</div>
						</div>
					</div>
				</a>';
		}
		return $output;
	}

	// 文章列表样式
	public function itemClass()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		// 布局样式
		$art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : 'art80';

		$css = '
		<style>';
		if ($art_type_selector == 'art80') {
			// 布局方式
			$art80_layout = (isset($settings->art80_layout) && $settings->art80_layout) ? $settings->art80_layout : 'row';
			// 图片填充方式
			$art80_img_style = (isset($settings->art80_img_style) && $settings->art80_img_style) ? $settings->art80_img_style : 'cover';
			// 图片宽度
			$art80_img_w = $this->toFit('art80_img_w')->md ?: '186';
			$art80_img_w_sm = $this->toFit('art80_img_w')->sm;
			$art80_img_w_xs = $this->toFit('art80_img_w')->xs;
			// 图片高度
			$art80_img_h = $this->toFit('art80_img_h')->md ?: '124';
			$art80_img_h_sm = $this->toFit('art80_img_h')->sm;
			$art80_img_h_xs = $this->toFit('art80_img_h')->xs;
			// 背景颜色
			$art80_item_bgcolor = $settings->art80_item_bgcolor ?? '';
			// 标题文字大小
			$art80_title_fontsize = $this->toFit('art80_title_fontsize')->md ?: '16';
			$art80_title_fontsize_sm = $this->toFit('art80_title_fontsize')->sm;
			$art80_title_fontsize_xs = $this->toFit('art80_title_fontsize')->xs;
			// 标题文字行高
			$art80_title_line_h = $this->toFit('art80_title_line_h')->md ?: '22';
			$art80_title_line_h_sm = $this->toFit('art80_title_line_h')->sm;
			$art80_title_line_h_xs = $this->toFit('art80_title_line_h')->xs;
			// 标题文字颜色
			$art80_title_color = $settings->art80_title_color ?? '#0B5EA0';
			// 时间文字大小
			$art80_time_fontsize = $this->toFit('art80_time_fontsize')->md ?: '14';
			$art80_time_fontsize_sm = $this->toFit('art80_time_fontsize')->sm;
			$art80_time_fontsize_xs = $this->toFit('art80_time_fontsize')->xs;
			// 时间文字颜色
			$art80_time_color = $settings->art80_time_color ?? '#9E9E9E';
			// 简介文字大小
			$art80_intro_fontsize = $this->toFit('art80_intro_fontsize')->md ?: '14';
			$art80_intro_fontsize_sm = $this->toFit('art80_intro_fontsize')->sm;
			$art80_intro_fontsize_xs = $this->toFit('art80_intro_fontsize')->xs;
			// 简介文字行高
			$art80_intro_line_h = $this->toFit('art80_intro_line_h')->md ?: '26';
			$art80_intro_line_h_sm = $this->toFit('art80_intro_line_h')->sm;
			$art80_intro_line_h_xs = $this->toFit('art80_intro_line_h')->xs;
			// 简介文字颜色
			$art80_intro_color = $settings->art80_intro_color ?? '#333333';
			/* 移入 */
			// 移入背景颜色
			$art80_item_bgcolor_hover = $settings->art80_item_bgcolor_hover ?? '#E9F9FF';

			$css .= "
			{$addon_id} .jwpf-addon-articles .jwpf-addon-article {
				margin-bottom: 16px;
			}
			{$addon_id} .list-item {
				display: flex;
				flex-direction: {$art80_layout};
				padding: 14px 9px;
				transition: all ease-in-out 0.3s;
				background: {$art80_item_bgcolor};
			}
			{$addon_id} .list-item:hover {
				background: {$art80_item_bgcolor_hover};
			}
			{$addon_id} .list-item .img-box {
				width: {$art80_img_w}px;
				height: {$art80_img_h}px;";
			if ($art80_layout == 'no-img') {
				$css .= "display: none;";
			}
			if ($art80_layout == 'column' || $art80_layout == 'column-reverse') {
				$css .= "width: 100%;";
			}
			if ($art80_layout == 'row') {
				$css .= "margin-right: 20px;";
			}
			if ($art80_layout == 'row-reverse') {
				$css .= "margin-left: 20px;";
			}
			if ($art80_layout == 'column') {
				$css .= "margin-bottom: 20px;";
			}
			if ($art80_layout == 'column-reverse') {
				$css .= "margin-top: 20px;";
			}
			$css .= "
			}
			{$addon_id} .list-item .img-box img {
				width: 100%;
				height: 100%;
				object-fit: {$art80_img_style};
			}
			{$addon_id} .list-item .right-info {
				width: calc(100% - {$art80_img_w}px - 20px);";
			if ($art80_layout == 'column' || $art80_layout == 'column-reverse' || $art80_layout == 'no-img') {
				$css .= "width: 100%;";
			}
			$css .= "
			}
			{$addon_id} .list-item .title-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			{$addon_id} .list-item .title {
				font-size: {$art80_title_fontsize}px;
				line-height: {$art80_title_line_h}px;
				color: {$art80_title_color};
				width: calc(100% - 120px);
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
			{$addon_id} .list-item .time {
				font-size: {$art80_time_fontsize}px;
				color: {$art80_time_color};
			}
			{$addon_id} .list-item .intro {
				font-size: {$art80_intro_fontsize}px;
				color: {$art80_intro_color};
				line-height: {$art80_intro_line_h}px;
				margin-top: 11px;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				{$addon_id} .list-item .img-box {";
			if ($art80_layout == 'row' || $art80_layout == 'row-reverse') {
				$css .= "width: {$art80_img_w_sm}px;";
			}
			$css .= "
					height: {$art80_img_h_sm}px;
				}
				{$addon_id} .list-item .title {
					font-size: {$art80_title_fontsize_sm}px;
					line-height: {$art80_title_line_h_sm}px;
				}
				{$addon_id} .list-item .time {
					font-size: {$art80_time_fontsize_sm}px;
				}
				{$addon_id} .list-item .intro {
					font-size: {$art80_intro_fontsize_sm}px;
					line-height: {$art80_intro_line_h_sm}px;
				}
			}
			@media (max-width: 767px) {
				{$addon_id} .list-item .img-box {";
			if ($art80_layout == 'row' || $art80_layout == 'row-reverse') {
				$css .= "width: {$art80_img_w_xs}px;";
			}
			$css .= "
					height: {$art80_img_h_xs}px;
				}
				{$addon_id} .list-item .title {
					font-size: {$art80_title_fontsize_xs}px;
					line-height: {$art80_title_line_h_xs}px;
				}
				{$addon_id} .list-item .time {
					font-size: {$art80_time_fontsize_xs}px;
				}
				{$addon_id} .list-item .intro {
					font-size: {$art80_intro_fontsize_xs}px;
					line-height: {$art80_intro_line_h_xs}px;
				}
			}
			";
		}elseif($art_type_selector === 'art81'){
			// 开启固定宽高
            $show_zsy_art81 = (isset($settings->show_zsy_art81) && $settings->show_zsy_art81) ? $settings->show_zsy_art81 : 0;

            // 轮播高度
			$img_gd_height_art81 = $this->toFit('img_gd_height_art81')->md ?: '500';
			$img_gd_height_art81_sm = $this->toFit('img_gd_height_art81')->sm ?: '';
			$img_gd_height_art81_xs = $this->toFit('img_gd_height_art81')->xs ?: '';

            // 图片填充方式
            $img_style_art81 = (isset($settings->img_style_art81) && $settings->img_style_art81) ? $settings->img_style_art81 : 'fill';

            /* 标题配置 */
            // 标题背景颜色
            $title_bgcolor_art81 = isset($settings->title_bgcolor_art81) ? $settings->title_bgcolor_art81 : 'rgba(0, 0, 0, 0.64)';
            // 标题文字大小
			$title_fontsize_art81_md = $this->toFit('title_fontsize_art81')->md ?: '18';
            $title_fontsize_art81_sm = $this->toFit('title_fontsize_art81')->sm ?: '';
            $title_fontsize_art81_xs = $this->toFit('title_fontsize_art81')->xs ?: '';
            // 标题文字行高
			$title_lineHeight_art81_md = $this->toFit('title_lineHeight_art81')->md ?: '52';
            $title_lineHeight_art81_sm = $this->toFit('title_lineHeight_art81')->sm ?: '';
            $title_lineHeight_art81_xs = $this->toFit('title_lineHeight_art81')->xs ?: '';
            // 标题文字颜色
            $title_color_art81 = isset($settings->title_color_art81) ? $settings->title_color_art81 : '#FFFFFF';
            // 标题文字内边距
            $title_padding_art81_md = $this->toFit('title_padding_art81')->md ?: '0 200px 0 20px';
            $title_padding_art81_sm = $this->toFit('title_padding_art81')->sm ?: '';
            $title_padding_art81_xs = $this->toFit('title_padding_art81')->xs ?: '';

            // 是否显示翻页按钮
            $is_swiper_button_art81 = isset($settings->is_swiper_button_art81) ? $settings->is_swiper_button_art81 : 0;
            // 上翻页按钮
            $swiper_button_prev_art81 = isset($settings->swiper_button_prev_art81) ? $settings->swiper_button_prev_art81 : 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png';
            // 下翻页按钮
            $swiper_button_next_art81 = isset($settings->swiper_button_next_art81) ? $settings->swiper_button_next_art81 : 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png';
            // 移入上翻页按钮
            $swiper_button_prev_hover_art81 = isset($settings->swiper_button_prev_hover_art81) ? $settings->swiper_button_prev_hover_art81 : '';
            // 移入下翻页按钮
            $swiper_button_next_hover_art81 = isset($settings->swiper_button_next_hover_art81) ? $settings->swiper_button_next_hover_art81 : '';
            // 切换按钮宽度
            $swiper_button_width_art81_md = $this->toFit('swiper_button_width_art81')->md ?: 40;
            $swiper_button_width_art81_sm = $this->toFit('swiper_button_width_art81')->sm ?: '';
            $swiper_button_width_art81_xs = $this->toFit('swiper_button_width_art81')->xs ?: '';
            // 切换按钮高度
            $swiper_button_height_art81_md = $this->toFit('swiper_button_height_art81')->md ?: 40;
            $swiper_button_height_art81_sm = $this->toFit('swiper_button_height_art81')->sm ?: '';
            $swiper_button_height_art81_xs = $this->toFit('swiper_button_height_art81')->xs ?: '';

            /* 轮播点样式 */
            // 是否显示翻页按钮
            $is_swiper_pagination_art81 = isset($settings->is_swiper_pagination_art81) ? $settings->is_swiper_pagination_art81 : 1;
            //正常
            // 轮播点宽度
            $swiper_p_width_art81_md = $this->toFit('swiper_p_width_art81')->md ?: 18;
            $swiper_p_width_art81_sm = $this->toFit('swiper_p_width_art81')->sm ?: '';
            $swiper_p_width_art81_xs = $this->toFit('swiper_p_width_art81')->xs ?: '';
            // 轮播点高度
            $swiper_p_height_art81_md = $this->toFit('swiper_p_height_art81')->md ?: 7;
            $swiper_p_height_art81_sm = $this->toFit('swiper_p_height_art81')->sm ?: '';
            $swiper_p_height_art81_xs = $this->toFit('swiper_p_height_art81')->xs ?: '';
            // 轮播点间距
            $swiper_p_margin_art81_md = $this->toFit('swiper_p_margin_art81')->md / 2 ?: 2.5;
            $swiper_p_margin_art81_sm = $this->toFit('swiper_p_margin_art81')->sm / 2 ?: '';
            $swiper_p_margin_art81_xs = $this->toFit('swiper_p_margin_art81')->xs / 2 ?: '';
            // 轮播点圆角
            $swiper_p_border_art81_md = $this->toFit('swiper_p_border_art81')->md ?: 1;
            $swiper_p_border_art81_sm = $this->toFit('swiper_p_border_art81')->sm ?: '';
            $swiper_p_border_art81_xs = $this->toFit('swiper_p_border_art81')->xs ?: '';
            // 轮播点颜色
            $swiper_p_color_art81 = (isset($settings->swiper_p_color_art81) && $settings->swiper_p_color_art81) ? $settings->swiper_p_color_art81 : 'rgba(255,255,255,0.52)';

            //选中
            // 轮播点宽度
            $swiper_p_width_hover_art81_md = $this->toFit('swiper_p_width_hover_art81')->md ?: '';
            $swiper_p_width_hover_art81_sm = $this->toFit('swiper_p_width_hover_art81')->sm ?: '';
            $swiper_p_width_hover_art81_xs = $this->toFit('swiper_p_width_hover_art81')->xs ?: '';
            // 轮播点高度
            $swiper_p_height_hover_art81_md = $this->toFit('swiper_p_height_hover_art81')->md ?: '';
            $swiper_p_height_hover_art81_sm = $this->toFit('swiper_p_height_hover_art81')->sm ?: '';
            $swiper_p_height_hover_art81_xs = $this->toFit('swiper_p_height_hover_art81')->xs ?: '';
            // 轮播点颜色
            $swiper_p_color_hover_art81 = (isset($settings->swiper_p_color_hover_art81) && $settings->swiper_p_color_hover_art81) ? $settings->swiper_p_color_hover_art81 : '#FFFFFF';

            if ($show_zsy_art81 == 1) {
                $css .= "
                    {$addon_id} .swiper-container {
                        height: {$img_gd_height_art81}px;
                    }
                    {$addon_id} .swiper-container .swiper-slide .img-box {
                        width: 100%;
                        height: 100%;
                    }
                    {$addon_id} .swiper-container .swiper-slide .img-box img {
                        width: 100%;
                        height: 100%;
                        object-fit: {$img_style_art81};
                    }
                ";
            }
            $css .= "
                {$addon_id} .swiper-slide .title {
                    position: absolute;
                    width: 100%;
                    height: {$title_lineHeight_art81_md}px;
                    line-height: {$title_lineHeight_art81_md}px;
                    padding: {$title_padding_art81_md};
                    box-sizing: border-box;
                    background-color: {$title_bgcolor_art81};
                    bottom: 0;
                    left: 0;
                    color: {$title_color_art81};
                    font-size: {$title_fontsize_art81_md}px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin: 0;
                }
                /* 轮播点 */
                {$addon_id} .swiper-container .swiper-pagination {
                    width: max-content;
                    right: 20px;
                    left: inherit;
                    height: {$title_lineHeight_art81_md}px;
                    bottom: 0;
                    display: flex;
                    align-items: center;";
                    if ($is_swiper_pagination_art81 != 1) {
                        $css .= "display: none;";
                    }
                    $css .= "
                }
                {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                    width: {$swiper_p_width_art81_md}px;
                    height: {$swiper_p_height_art81_md}px;
                    border-radius: {$swiper_p_border_art81_md}px;
                    background: {$swiper_p_color_art81};
                    margin: 0 {$swiper_p_margin_art81_md}px;
                    opacity: 1;
                }
                {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                    width: {$swiper_p_width_hover_art81_md}px;
                    height: {$swiper_p_height_hover_art81_md}px;
                    background: {$swiper_p_color_hover_art81};
                }
				/*翻页按钮*/
				{$addon_id} .swiper-container .swiper-button-next, {$addon_id} .swiper-container .swiper-button-prev{
					width: {$swiper_button_width_art81_md}px;
					height: {$swiper_button_height_art81_md}px;
				}
				{$addon_id} .swiper-button-next::after, {$addon_id} .swiper-button-prev::after{
					display: none;
				}
                @media (min-width: 768px) and (max-width: 991px) {";
                    if ($show_zsy_art81 == 1) {
                        $css .= "
                            {$addon_id} .swiper-container {
                                height: {$img_gd_height_art81_sm}px;
                            }
                        ";
                    }
                    $css .= "
                    {$addon_id} .swiper-slide .title {
                        height: {$title_lineHeight_art81_sm}px;
                        line-height: {$title_lineHeight_art81_sm}px;
                        padding: {$title_padding_art81_sm};
                        font-size: {$title_fontsize_art81_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination {
                        height: {$title_lineHeight_art81_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                        width: {$swiper_p_width_art81_sm}px;
                        height: {$swiper_p_height_art81_sm}px;
                        border-radius: {$swiper_p_border_art81_sm}px;
                        margin: 0 {$swiper_p_margin_art81_sm}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                        width: {$swiper_p_width_hover_art81_sm}px;
                        height: {$swiper_p_height_hover_art81_sm}px;
                    }
					/*翻页按钮*/
					{$addon_id} .swiper-container .swiper-button-next, {$addon_id} .swiper-container .swiper-button-prev{
						width: {$swiper_button_width_art81_sm}px;
						height: {$swiper_button_height_art81_sm}px;
					}
					{$addon_id} .swiper-container .swiper-button-next img, {$addon_id} .swiper-container .swiper-button-prev img{
						width: 100%;
						height: 100%;
					}
                }
                @media (max-width: 767px) {";
                    if ($show_zsy_art81 == 1) {
                        $css .= "
                            {$addon_id} .swiper-container {
                                height: {$img_gd_height_art81_xs}px;
                            }
                        ";
                    }
                    $css .= "
                    {$addon_id} .swiper-slide .title {
                        height: {$title_lineHeight_art81_xs}px;
                        line-height: {$title_lineHeight_art81_xs}px;
                        padding: {$title_padding_art81_xs};
                        font-size: {$title_fontsize_art81_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination {
                        height: {$title_lineHeight_art81_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet {
                        width: {$swiper_p_width_art81_xs}px;
                        height: {$swiper_p_height_art81_xs}px;
                        border-radius: {$swiper_p_border_art81_xs}px;
                        margin: 0 {$swiper_p_margin_art81_xs}px;
                    }
                    {$addon_id} .swiper-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
                        width: {$swiper_p_width_hover_art81_xs}px;
                        height: {$swiper_p_height_hover_art81_xs}px;
                    }
					/*翻页按钮*/
					{$addon_id} .swiper-container .swiper-button-next, {$addon_id} .swiper-container .swiper-button-prev{
						width: {$swiper_button_width_art81_xs}px;
						height: {$swiper_button_height_art81_xs}px;
					}
                }
            ";
		}elseif($art_type_selector === 'art82'){
			if (isset($settings->col_num_82)) {
				if (is_object($settings->col_num_82)) {
						$col_num_82_md = $settings->col_num_82->md;
						$col_num_82_sm = $settings->col_num_82->sm;
						$col_num_82_xs = $settings->col_num_82->xs;
				} else {
						$col_num_82_md = $settings->col_num_82;
						$col_num_82_sm = $settings->col_num_82_sm;
						$col_num_82_xs = $settings->col_num_82_xs;
				}
			} else {
					$col_num_82_md = '2';
					$col_num_82_sm = '2';
					$col_num_82_xs = '1';
			}
			if (isset($settings->col_gap_82)) {
				if (is_object($settings->col_gap_82)) {
						$col_gap_82_md = $settings->col_gap_82->md;
						$col_gap_82_sm = $settings->col_gap_82->sm;
						$col_gap_82_xs = $settings->col_gap_82->xs;
				} else {
						$col_gap_82_md = $settings->col_gap_82;
						$col_gap_82_sm = $settings->col_gap_82_sm;
						$col_gap_82_xs = $settings->col_gap_82_xs;
				}
			} else {
					$col_gap_82_md = '9';
					$col_gap_82_sm = '9';
					$col_gap_82_xs = '9';
			}
			if (isset($settings->row_gap_82)) {
				if (is_object($settings->row_gap_82)) {
						$row_gap_82_md = $settings->row_gap_82->md;
						$row_gap_82_sm = $settings->row_gap_82->sm;
						$row_gap_82_xs = $settings->row_gap_82->xs;
				} else {
						$row_gap_82_md = $settings->row_gap_82;
						$row_gap_82_sm = $settings->row_gap_82_sm;
						$row_gap_82_xs = $settings->row_gap_82_xs;
				}
			} else {
					$row_gap_82_md = '30';
					$row_gap_82_sm = '30';
					$row_gap_82_xs = '30';
			}
			$border_width_82 = isset($settings->border_width_82) ? $settings->border_width_82 : '3px 0 0 0';
			$theme_color_82 = isset($settings->theme_color_82) ? $settings->theme_color_82 : '#C7000B';
			$border_style_82 = isset($settings->border_style_82) ? $settings->border_style_82 : 'solid';
			$bg_color_82 = isset($settings->bg_color_82) ? $settings->bg_color_82 : '#FAFAFA';
			if (isset($settings->padding_82)) {
				if (is_object($settings->padding_82)) {
						$padding_82_md = $settings->padding_82->md;
						$padding_82_sm = $settings->padding_82->sm;
						$padding_82_xs = $settings->padding_82->xs;
				} else {
						$padding_82_md = $settings->padding_82;
						$padding_82_sm = $settings->padding_82_sm;
						$padding_82_xs = $settings->padding_82_xs;
				}
			} else {
					$padding_82_md = '34px 24px 22px 24px';
					$padding_82_sm = '24px 22px 22px 24px';
					$padding_82_xs = '20px 18px 18px 20px';
			}
			if (isset($settings->icon_size_82)) {
				if (is_object($settings->icon_size_82)) {
						$icon_size_82_md = $settings->icon_size_82->md;
						$icon_size_82_sm = $settings->icon_size_82->sm;
						$icon_size_82_xs = $settings->icon_size_82->xs;
				} else {
						$icon_size_82_md = $settings->icon_size_82;
						$icon_size_82_sm = $settings->icon_size_82_sm;
						$icon_size_82_xs = $settings->icon_size_82_xs;
				}
			} else {
					$icon_size_82_md = '28';
					$icon_size_82_sm = '28';
					$icon_size_82_xs = '28';
			}
			if (isset($settings->icon_margin_82)) {
				if (is_object($settings->icon_margin_82)) {
						$icon_margin_82_md = $settings->icon_margin_82->md;
						$icon_margin_82_sm = $settings->icon_margin_82->sm;
						$icon_margin_82_xs = $settings->icon_margin_82->xs;
				} else {
						$icon_margin_82_md = $settings->icon_margin_82;
						$icon_margin_82_sm = $settings->icon_margin_82_sm;
						$icon_margin_82_xs = $settings->icon_margin_82_xs;
				}
			} else {
					$icon_margin_82_md = '16';
					$icon_margin_82_sm = '16';
					$icon_margin_82_xs = '16';
			}
			if (isset($settings->title_font_size_82)) {
				if (is_object($settings->title_font_size_82)) {
						$title_font_size_82_md = $settings->title_font_size_82->md;
						$title_font_size_82_sm = $settings->title_font_size_82->sm;
						$title_font_size_82_xs = $settings->title_font_size_82->xs;
				} else {
						$title_font_size_82_md = $settings->title_font_size_82;
						$title_font_size_82_sm = $settings->title_font_size_82_sm;
						$title_font_size_82_xs = $settings->title_font_size_82_xs;
				}
			} else {
					$title_font_size_82_md = '18';
					$title_font_size_82_sm = '18';
					$title_font_size_82_xs = '18';
			}
			$title_color_82 = isset($settings->title_color_82) ? $settings->title_color_82 : '#333';
			$title_font_weight_82 = isset($settings->title_font_weight_82) ? $settings->title_font_weight_82 : '600';
			$title_font_family_82 = isset($settings->title_font_family_82) ? $settings->title_font_family_82 : 'inherit';
			if (isset($settings->title_margin_82)) {
				if (is_object($settings->title_margin_82)) {
						$title_margin_82_md = $settings->title_margin_82->md;
						$title_margin_82_sm = $settings->title_margin_82->sm;
						$title_margin_82_xs = $settings->title_margin_82->xs;
				} else {
						$title_margin_82_md = $settings->title_margin_82;
						$title_margin_82_sm = $settings->title_margin_82_sm;
						$title_margin_82_xs = $settings->title_margin_82_xs;
				}
			} else {
					$title_margin_82_md = '14';
					$title_margin_82_sm = '14';
					$title_margin_82_xs = '14';
			}
			if (isset($settings->time_font_size_82)) {
				if (is_object($settings->time_font_size_82)) {
						$time_font_size_82_md = $settings->time_font_size_82->md;
						$time_font_size_82_sm = $settings->time_font_size_82->sm;
						$time_font_size_82_xs = $settings->time_font_size_82->xs;
				} else {
						$time_font_size_82_md = $settings->time_font_size_82;
						$time_font_size_82_sm = $settings->time_font_size_82_sm;
						$time_font_size_82_xs = $settings->time_font_size_82_xs;
				}
			} else {
					$time_font_size_82_md = '14';
					$time_font_size_82_sm = '14';
					$time_font_size_82_xs = '14';
			}
			$time_color_82 = isset($settings->time_color_82) ? $settings->time_color_82 : '#7F7F7F';
			$time_font_weight_82 = isset($settings->time_font_weight_82) ? $settings->time_font_weight_82 : '';
			$time_font_family_82 = isset($settings->time_font_family_82) ? $settings->time_font_family_82 : 'inherit';
			if (isset($settings->intro_font_size_82)) {
				if (is_object($settings->intro_font_size_82)) {
						$intro_font_size_82_md = $settings->intro_font_size_82->md;
						$intro_font_size_82_sm = $settings->intro_font_size_82->sm;
						$intro_font_size_82_xs = $settings->intro_font_size_82->xs;
				} else {
						$intro_font_size_82_md = $settings->intro_font_size_82;
						$intro_font_size_82_sm = $settings->intro_font_size_82_sm;
						$intro_font_size_82_xs = $settings->intro_font_size_82_xs;
				}
			} else {
					$intro_font_size_82_md = '16';
					$intro_font_size_82_sm = '16';
					$intro_font_size_82_xs = '16';
			}
			if (isset($settings->title_margin_bottom_82)) {
				if (is_object($settings->title_margin_bottom_82)) {
						$title_margin_bottom_82_md = $settings->title_margin_bottom_82->md;
						$title_margin_bottom_82_sm = $settings->title_margin_bottom_82->sm;
						$title_margin_bottom_82_xs = $settings->title_margin_bottom_82->xs;
				} else {
						$title_margin_bottom_82_md = $settings->title_margin_bottom_82;
						$title_margin_bottom_82_sm = $settings->title_margin_bottom_82_sm;
						$title_margin_bottom_82_xs = $settings->title_margin_bottom_82_xs;
				}
			} else {
					$title_margin_bottom_82_md = '24';
					$title_margin_bottom_82_sm = '24';
					$title_margin_bottom_82_xs = '24';
			}
			$intro_font_family_82 = (isset($settings->intro_font_family_82)) ? $settings->intro_font_family_82 : 'inherit';
			$intro_color_82 = (isset($settings->intro_color_82)) ? $settings->intro_color_82 : '#333';
			$intro_font_weight_82 = (isset($settings->intro_font_weight_82)) ? $settings->intro_font_weight_82 : '';
			if (isset($settings->intro_line_height_82)) {
				if (is_object($settings->intro_line_height_82)) {
						$intro_line_height_82_md = $settings->intro_line_height_82->md;
						$intro_line_height_82_sm = $settings->intro_line_height_82->sm;
						$intro_line_height_82_xs = $settings->intro_line_height_82->xs;
				} else {
						$intro_line_height_82_md = $settings->intro_line_height_82;
						$intro_line_height_82_sm = $settings->intro_line_height_82_sm;
						$intro_line_height_82_xs = $settings->intro_line_height_82_xs;
				}
			} else {
					$intro_line_height_82_md = '34';
					$intro_line_height_82_sm = '34';
					$intro_line_height_82_xs = '34';
			}
			if (isset($settings->item_height_82)) {
				if (is_object($settings->item_height_82)) {
						$item_height_82_md = $settings->item_height_82->md;
						$item_height_82_sm = $settings->item_height_82->sm;
						$item_height_82_xs = $settings->item_height_82->xs;
				} else {
						$item_height_82_md = $settings->item_height_82;
						$item_height_82_sm = $settings->item_height_82_sm;
						$item_height_82_xs = $settings->item_height_82_xs;
				}
			} else {
					$item_height_82_md = '221';
					$item_height_82_sm = '300';
					$item_height_82_xs = '300';
			}
			if (isset($settings->btn_width_82)) {
				if (is_object($settings->btn_width_82)) {
						$btn_width_82_md = $settings->btn_width_82->md;
						$btn_width_82_sm = $settings->btn_width_82->sm;
						$btn_width_82_xs = $settings->btn_width_82->xs;
				} else {
						$btn_width_82_md = $settings->btn_width_82;
						$btn_width_82_sm = $settings->btn_width_82_sm;
						$btn_width_82_xs = $settings->btn_width_82_xs;
				}
			} else {
					$btn_width_82_md = '';
					$btn_width_82_sm = '';
					$btn_width_82_xs = '';
			}
			if (isset($settings->btn_height_82)) {
				if (is_object($settings->btn_height_82)) {
						$btn_height_82_md = $settings->btn_height_82->md;
						$btn_height_82_sm = $settings->btn_height_82->sm;
						$btn_height_82_xs = $settings->btn_height_82->xs;
				} else {
						$btn_height_82_md = $settings->btn_height_82;
						$btn_height_82_sm = $settings->btn_height_82_sm;
						$btn_height_82_xs = $settings->btn_height_82_xs;
				}
			} else {
					$btn_height_82_md = '';
					$btn_height_82_sm = '';
					$btn_height_82_xs = '';
			}
			if (isset($settings->btn_padding_82)) {
				if (is_object($settings->btn_padding_82)) {
						$btn_padding_82_md = $settings->btn_padding_82->md;
						$btn_padding_82_sm = $settings->btn_padding_82->sm;
						$btn_padding_82_xs = $settings->btn_padding_82->xs;
				} else {
						$btn_padding_82_md = $settings->btn_padding_82;
						$btn_padding_82_sm = $settings->btn_padding_82_sm;
						$btn_padding_82_xs = $settings->btn_padding_82_xs;
				}
			} else {
					$btn_padding_82_md = '';
					$btn_padding_82_sm = '';
					$btn_padding_82_xs = '';
			}
			$btn_border_radius_82 = isset($settings->btn_border_radius_82) ? $settings->btn_border_radius_82 : '0 0 0 0';
			$btn_bg_color_82 = isset($settings->btn_bg_color_82) ? $settings->btn_bg_color_82 : '';
			$btn_color_82 = isset($settings->btn_color_82) ? $settings->btn_color_82 : $theme_color_82;
			if (isset($settings->btn_font_size_82)) {
				if (is_object($settings->btn_font_size_82)) {
						$btn_font_size_82_md = $settings->btn_font_size_82->md;
						$btn_font_size_82_sm = $settings->btn_font_size_82->sm;
						$btn_font_size_82_xs = $settings->btn_font_size_82->xs;
				} else {
						$btn_font_size_82_md = $settings->btn_font_size_82;
						$btn_font_size_82_sm = $settings->btn_font_size_82_sm;
						$btn_font_size_82_xs = $settings->btn_font_size_82_xs;
				}
			} else {
					$btn_font_size_82_md = '';
					$btn_font_size_82_sm = '';
					$btn_font_size_82_xs = '';
			}
			if (isset($settings->btn_font_weight_82)) {
				if (is_object($settings->btn_font_weight_82)) {
						$btn_font_weight_82_md = $settings->btn_font_weight_82->md;
						$btn_font_weight_82_sm = $settings->btn_font_weight_82->sm;
						$btn_font_weight_82_xs = $settings->btn_font_weight_82->xs;
				} else {
						$btn_font_weight_82_md = $settings->btn_font_weight_82;
						$btn_font_weight_82_sm = $settings->btn_font_weight_82_sm;
						$btn_font_weight_82_xs = $settings->btn_font_weight_82_xs;
				}
			} else {
					$btn_font_weight_82_md = '';
					$btn_font_weight_82_sm = '';
					$btn_font_weight_82_xs = '';
			}
			$btn_bg_hover_color_82 = isset($settings->btn_bg_hover_color_82) ? $settings->btn_bg_hover_color_82 : '';
			$btn_hover_color_82 = isset($settings->btn_hover_color_82) ? $settings->btn_hover_color_82 : '';
			if (isset($settings->btn_margin_82)) {
				if (is_object($settings->btn_margin_82)) {
						$btn_margin_82_md = $settings->btn_margin_82->md;
						$btn_margin_82_sm = $settings->btn_margin_82->sm;
						$btn_margin_82_xs = $settings->btn_margin_82->xs;
				} else {
						$btn_margin_82_md = $settings->btn_margin_82;
						$btn_margin_82_sm = $settings->btn_margin_82_sm;
						$btn_margin_82_xs = $settings->btn_margin_82_xs;
				}
			} else {
					$btn_margin_82_md = '18px 0 0 0';
					$btn_margin_82_sm = '18px 0 0 0';
					$btn_margin_82_xs = '18px 0 0 0';
			}
			$btn_font_family_82 = isset($settings->btn_font_family_82) ? $settings->btn_font_family_82 : '';
			$intro_first_line_indent_82 = $settings->intro_first_line_indent_82 == 1 ? 'text-indent: 2em;' : 'text-indent: 0em';

			$css .= $addon_id.' .art_82_wrap{
					margin: 0 -calc('.$col_gap_82_md.'px / 2);
				}
				'.$addon_id.' .art_82_wrap::after{
					display: block;
					content: "";
					clear: both;
				}
				'.$addon_id.' .art_82_item{
					float: left;
					padding: 0 calc('.$col_gap_82_md.'px / 2);
					width: calc(100% / '.$col_num_82_md.');
					margin-bottom: '.$row_gap_82_md.'px;
					position: relative;
				}
				'.$addon_id.' .art_82_link{
					position: absolute;
					top: 0;
					bottom: 0;
					left: calc('.$col_gap_82_md.'px / 2);
					right: calc('.$col_gap_82_md.'px / 2);
				}
				'.$addon_id.' .art_82_item_content{
					border-width: '.$border_width_82.';
					border-color: '.$theme_color_82.';
					border-style: '.$border_style_82.';
					background-color: '.$bg_color_82.';
					padding: '.$padding_82_md.';
					height: '.$item_height_82_md.'px;
					display: flex;
					flex-direction: column;
				}
				'.$addon_id.' .art_82_item_title{
					margin: 0;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: '.$title_margin_bottom_82_md.'px;
				}
				'.$addon_id.' .art_82_item_title .art_82_title_left{
					display: flex;
					align-items: center;
					flex: 1;
					padding-right: '.$title_margin_82_md.'px;
					max-width: 80%;
				}
				'.$addon_id.' .art_82_item_title .art_82_title_left .art_82_title{
					max-width: calc(100% - '.$icon_size_82_md.'px - '.$icon_margin_82_md.'px);
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-size: '.$title_font_size_82_md.'px;
					color: '.$title_color_82.';
					margin: 0;
					font-weight: '.$title_font_weight_82.';
					font-family: '.$title_font_family_82.';
				}
				'.$addon_id.' .art_82_item_title .art_82_title_left .fa-genderless{
					display: block;
					color: '.$theme_color_82.';
					font-size: '.$icon_size_82_md.'px;
					margin-right: '.$icon_margin_82_md.'px;
				}
				'.$addon_id.' .art_82_item_title .art_82_title_left + time{
					font-size: '.$time_font_size_82_md.'px;
					color: '.$time_color_82.';
					font-weight: '.$time_font_weight_82.';
					font-family: '.$time_font_family_82.';
					flex-shrink: 0;
				}
				'.$addon_id.' .art_82_item_text{
					font-size: '.$intro_font_size_82_md.'px;
					font-family: '.$intro_font_family_82.';
					color: '.$intro_color_82.';
					font-weight: '.$intro_font_weight_82.';
					line-height: '.$intro_line_height_82_md.'px;
					flex: 1;
					overflow: hidden;
					'.$intro_first_line_indent_82.';
				}
				'.$addon_id.' .art_82_item_btn{
					text-align: right;
				}
				'.$addon_id.' .art_82_item_btn a{
					display: inline-flex;
					justify-content: center;
					align-items: center;
					width: '.$btn_width_82_md.'px;
					height: '.$btn_height_82_md.'px;
					padding: '.$btn_padding_82_md.';
					border-radius: '.$btn_border_radius_82.';
					background-color: '.$btn_bg_color_82.';
					color: '.$btn_color_82.';
					font-size: '.$btn_font_size_82_md.'px;
					font-family: '.$btn_font_family_82.';
					font-weight: '.$btn_font_weight_82_md.';
					text-decoration: none;
					margin: '.$btn_margin_82_md.';
				}
				'.$addon_id.' .art_82_item_btn a:hover{
					background-color: '.$btn_bg_hover_color_82.';
					color: '.$btn_hover_color_82.';
					transition: all 0.3s;
				}
				@media screen and (max-width: 991px) {
					'.$addon_id.' .art_82_wrap{
						margin: 0 -calc('.$col_gap_82_sm.'px / 2);
					}
					'.$addon_id.' .art_82_item{
						padding: 0 calc('.$col_gap_82_sm.'px / 2);
						width: calc(100% / '.$col_num_82_sm.');
						margin-bottom: '.$row_gap_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_content{
						padding: '.$padding_82_sm.';
						height: '.$item_height_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_title{
						margin-bottom: '.$title_margin_bottom_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left{
						padding-right: '.$title_margin_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left .art_82_title{
						max-width: calc(100% - '.$icon_size_82_sm.'px - '.$icon_margin_82_sm.'px);
						font-size: '.$title_font_size_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left .fa-genderless{
						font-size: '.$icon_size_82_sm.'px;
						margin-right: '.$icon_margin_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left + time{
						font-size: '.$time_font_size_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_text{
						font-size: '.$intro_font_size_82_sm.'px;
						line-height: '.$intro_line_height_82_sm.'px;
					}
					'.$addon_id.' .art_82_item_btn a{
						width: '.$btn_width_82_sm.'px;
						height: '.$btn_height_82_sm.'px;
						padding: '.$btn_padding_82_sm.';
						font-size: '.$btn_font_size_82_sm.'px;
						font-weight: '.$btn_font_weight_82_sm.';
						margin: '.$btn_margin_82_sm.';
					}
				}
				@media screen and (max-width: 767px) {
					'.$addon_id.' .art_82_wrap{
						margin: 0 -calc('.$col_gap_82_xs.'px / 2);
					}
					'.$addon_id.' .art_82_item{
						display: block;
						padding: 0 calc('.$col_gap_82_xs.'px / 2);
						width: calc(100% / '.$col_num_82_xs.');
						margin-bottom: '.$row_gap_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_content{
						padding: '.$padding_82_xs.';
						height: '.$item_height_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_title{
						margin-bottom: '.$title_margin_bottom_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left{
						padding-right: '.$title_margin_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left .art_82_title{
						max-width: calc(100% - '.$icon_size_82_xs.'px - '.$icon_margin_82_xs.'px);
						font-size: '.$title_font_size_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left .fa-genderless{
						font-size: '.$icon_size_82_xs.'px;
						margin-right: '.$icon_margin_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_title .art_82_title_left + time{
						font-size: '.$time_font_size_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_text{
						font-size: '.$intro_font_size_82_xs.'px;
						line-height: '.$intro_line_height_82_xs.'px;
					}
					'.$addon_id.' .art_82_item_btn a{
						width: '.$btn_width_82_xs.'px;
						height: '.$btn_height_82_xs.'px;
						padding: '.$btn_padding_82_xs.';
						font-size: '.$btn_font_size_82_xs.'px;
						font-weight: '.$btn_font_weight_82_xs.';
						margin: '.$btn_margin_82_xs.';
					}
				}';
		}
		elseif ($art_type_selector == 'art83')
		{
			$img_height_art83 = $this->getResponsiveValues('img_height_art83', ['md' => 300, 'sm' => '', xs => '']);
			$card_bg_color_art83 = $this->safeGetProp('card_bg_color_art83', '#0078d4');
			$card_triangle_color_art83 = $this->safeGetProp('card_triangle_color_art83', '#FFFFFF');
			$swiper_button_prev_art83 = $this->safeGetProp('swiper_button_prev_art83', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMjQgMTJMMTYgMjBMMjQgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
			$swiper_button_next_art83 = $this->safeGetProp('swiper_button_next_art83', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMTYgMTJMMjQgMjBMMTYgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
			$swiper_button_prev_hover_art83 = $this->safeGetProp('swiper_button_prev_hover_art83', '');
			$swiper_button_next_hover_art83 = $this->safeGetProp('swiper_button_next_hover_art83', '');

			$css .= '
			' .$addon_id. ' * {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}
			' .$addon_id. ' .oneline {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

			' .$addon_id. ' .container {
			}

			' .$addon_id. ' .swiper-container {
				width: 100%;
				height: auto;
			}

			' .$addon_id. ' .swiper-slide {
				flex-shrink: 0;
				width: calc(100% / 3 - (20px * 2 / 3));
				margin-right: 20px;
			}

			' .$addon_id. ' .image-item {
				width: 100%;
				position: relative;
				cursor: pointer;
				transition: all 0.3s ease;
				overflow: hidden;
				height: '.$img_height_art83['md'].'px;
			}

			' .$addon_id. ' .image-item:hover .slide-image {
				transform: scale(1.2);
			}

			' .$addon_id. ' .slide-image {
				width: 100%;
				height: 100%;
				display: block;
				object-fit: cover;
				transition: all 0.3s ease-in-out;
			}

			/* 蓝色信息卡片 */
			' .$addon_id. ' .blue-info-card {
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: '.$card_bg_color_art83.';
				color: white;
				padding: 15px 20px;
				text-align: left;
				width: 100%;
				opacity: 0;
				transition: all 0.3s ease;
				z-index: 10;
			}

			' .$addon_id. ' .blue-info-card::after {
				content: "";
				position: absolute;
				bottom: 0px;
				left: 50%;
				transform: translateX(-50%);
				width: 0;
				height: 0;
				border-left: 8px solid transparent;
				border-right: 8px solid transparent;
				border-bottom: 8px solid '.$card_triangle_color_art83.';
			}

			' .$addon_id. ' .blue-info-card .card-title {
				font-size: 18px;
				font-weight: bold;
				margin-bottom: 5px;
			}

			' .$addon_id. ' .blue-info-card .card-subtitle {
				font-size: 14px;
				opacity: 0.9;
			}

			' .$addon_id. ' .image-item.active .blue-info-card {
				opacity: 1;
			}

			' .$addon_id. ' .content-display-area {
				margin-top: 20px;
				border-width: 1px 0 1px 0;
				border-style: solid;
				border-color: #e5e5e5;
				background: white;
				padding: 20px 0;
			}

			' .$addon_id. ' .content-display-area .slide-content {
				font-size: 14px;
				line-height: 1.6;
				height: calc(14px * 1.6 * 3);
				color: #333;
				display: none;
                -webkit-line-clamp: 3;
			}

			' .$addon_id. ' .content-display-area .slide-content.active {
				display: -webkit-box;
			}

			' .$addon_id. ' .title {
				text-align: center;
				margin-bottom: 30px;
				font-size: 24px;
				color: #333;
			}

			/* Swiper导航按钮 */
			' .$addon_id. ' .swiper-button-prev,
			' .$addon_id. ' .swiper-button-next {
				width: 40px;
				height: 40px;
				margin-top: -20px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
			}

			' .$addon_id. ' .swiper-button-prev {
				left: -60px;
				background-image: url("' . $swiper_button_prev_art83 . '");
			}

			' .$addon_id. ' .swiper-button-next {
				right: -60px;
				background-image: url("' . $swiper_button_next_art83 . '");
			}

			' .$addon_id. ' .swiper-button-prev:hover {
				'.($swiper_button_prev_hover_art83 ? 'background-image: url('.$swiper_button_prev_hover_art83.');' : '') . '
			}

			' .$addon_id. ' .swiper-button-next:hover {
				'.($swiper_button_next_hover_art83 ? 'background-image: url('.$swiper_button_next_hover_art83.');' : '') . '
			}

			/* 隐藏默认的箭头 */
			' .$addon_id. ' .swiper-button-prev::after,
			' .$addon_id. ' .swiper-button-next::after {
				display: none;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				' .$addon_id. ' .image-item {
					height: '.$img_height_art83['sm'].'px;
				}
			}
			@media (max-width: 767px) {
				' .$addon_id. ' .image-item {
					height: '.$img_height_art83['xs'].'px;
				}
			}
			';
		}
		$css .= '
		</style>
		';
		return $css;
	}

	// 文章列表83 js
	public function art83Js()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$js = '
		<script>
			const swiper = new Swiper("' . $addon_id . ' .swiper-container", {
				slidesPerView: 3,
				spaceBetween: 20,
				centeredSlides: true,
				loop: true,
				autoplay: {
					delay: 5000,
					disableOnInteraction: false,
				},
				navigation: {
					nextEl: "' . $addon_id . ' .swiper-button-next",
					prevEl: "' . $addon_id . ' .swiper-button-prev",
				},
				effect: "slide",
				speed: 600,
				slideToClickedSlide: true,
				on: {
					slideChange: function () {
						const realIndex = this.realIndex;

						// 移除所有active类 - 使用jQuery语法
						$("' . $addon_id . ' .image-item").removeClass("active");
						$("' . $addon_id . ' .slide-content").removeClass("active");

						// 添加active类到当前中心的slide - 使用jQuery语法
						const $activeSlide = $("' . $addon_id . ' .swiper-slide").eq(this.activeIndex);
						console.log($activeSlide);
						if ($activeSlide.length) {
							$activeSlide.find(".image-item").addClass("active");
						}

						// 显示对应的文字内容 - 使用jQuery语法
						$("' . $addon_id . ' .content-display-area .slide-content").eq(realIndex).addClass("active");
					}
				}
			});

			// 初始化时设置第一个为active - 使用jQuery语法
			setTimeout(function() {
				$("' . $addon_id . ' .swiper-slide-active .image-item").addClass("active");
				$("' . $addon_id . ' .content-display-area .slide-content").first().addClass("active");
			}, 100);
		</script>
		';
		return $js;
	}

	// 文章列表 翻页
	public function pagination($items_count, $limit)
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$page = $_GET['page'] ?? 1;
		$zcatid = $_GET['catid'] ?? 0;

		$layout_id = $_GET['layout_id'] ?? 0;
		if (!is_numeric($_GET['page'])) {
			$page = 1;
		}

		$zcpcatid = $_GET['zcpcatid'] ?? 1000000000000;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$id = $this->addon->id;
		$page_view_name = isset($_GET['view']);

		$settings = $this->addon->settings;
		$catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
		if ($zcatid) {
			$catid = $zcatid;
		}

		// 是否显示翻页
		$show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
		// 翻页样式
		$page_style_selector = (isset($settings->page_style_selector) && $settings->page_style_selector) ? $settings->page_style_selector : 'page01';

		$page1_fontcolor = (isset($settings->page1_fontcolor)) ? $settings->page1_fontcolor : '#ffffff';
		$page1_bordercolor = (isset($settings->page1_bordercolor)) ? $settings->page1_bordercolor : '#2a68a7';
		$page1_bgcolor = (isset($settings->page1_bgcolor)) ? $settings->page1_bgcolor : '#ffffff';
		$page1_cur_fontcolor = (isset($settings->page1_cur_fontcolor)) ? $settings->page1_cur_fontcolor : '#ffffff';
		$page1_cur_bordercolor = (isset($settings->page1_cur_bordercolor)) ? $settings->page1_cur_bordercolor : '#2a68a7';
		$page1_cur_bgcolor = (isset($settings->page1_cur_bgcolor)) ? $settings->page1_cur_bgcolor : '#ffffff';

		$output = "";
		if ($show_page) {
			$all_page = 1;
			if ($limit) {
				$all_page = ceil($items_count / $limit);
			}
			$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
			// 翻页样式一
			if ($page_style_selector == 'page01') {
				$output .= "
					<style>
						{$addon_id} .art_list_id .page_plug{
							width: 90%;
							margin: 5px auto;
							text-align: center;
							display: flex;
							flex-wrap: wrap;
							justify-content: center;
							align-items: center;
						}
						{$addon_id} .art_list_id .page_plug a{
							padding: 3px 8px;
							border: 1px solid {$page1_bordercolor};
							margin-right: 5px;
							margin-bottom: 5px;
							text-decoration: none;
							color: {$page1_fontcolor};
							background:{$page1_bgcolor};
						}
						{$addon_id} .art_list_id .page_plug .curPage {
							border: 1px solid {$page1_cur_bordercolor};
							color: {$page1_cur_fontcolor};
							background:{$page1_cur_bgcolor};
						}
					</style>
				";
				$output .= '<div class="page_plug">';
				// 判断是不是第一页
				if ($page && $page != 1) {
					$output .= '<a class="page_num" href="' . $url . '&page=' . ($page - 1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
				}
				for ($i = 1; $i <= $all_page; $i++) {
					if ($page == $i) {
						$output .= '<a class="curPage">' . $i . '</a>';
					} else {
						$output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '">' . $i . '</a>';
					}
				}
				// 判断是不是最后一页
				if ($page < $all_page) {
					$output .= '<a class="page_num"  href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
				}

				$output .= '</div>';
				// $output .= '<div class="page_plug">共' . $items_count . '条</div>';
			}
			// 翻页样式二
			if ($page_style_selector == 'page02') {
				/* 编辑器省略号分页 */
				$output .= '<div class="zxf_pagediv " id="false_page" style="width:100%;display:flex;align-items: center;justify-content: center">';
				if ($page != 1) {
					$output .= '    <a href="' . $url . '&page=' . ($page - 1) . '&zcpcatid=' . $catid[0] . '" class="prebtn">上一页</a>';
				}
				$output .= ' <div class="page">';
				for ($i = 1; $i <= $all_page; $i++) {
					if ($page == $i) {
						$output .= '<a class="current">' . $i . '</a>';
					} else {
						$output .= '<a class="zxfPagenum" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '">' . $i . '</a>';
					}
				}
				$output .= '</div>';
				// 判断是不是最后一页
				if ($page != $all_page) {
					$output .= '    <a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $catid[0] . '" class="nextbtn">下一页</a>';
				}
				$output .= '</div>';
				$output .= '<script>

              </script>';
			}
			// 翻页样式三
			if ($page_style_selector == 'page03') {
				/* 编辑器省略号分页 */
				$output .= '
				<style>
					' . $addon_id . ' .pagination {
						margin-bottom: 60px;
					}
					' . $addon_id . ' .pagination {
						text-align: center;
						display: block;
						width:100%;
					}
					' . $addon_id . ' .pagination ul,
					' . $addon_id . ' .pagination ol {
						list-style: none;
					}
					' . $addon_id . ' .pagination li {
						width: 50px;
						height: 50px;
						background: #F6F6F6;
						border-radius: 3px;
						font-size: 20px;
						display: inline-block;
						line-height: 50px;
						margin: 0 2px;
						text-align: center;
						color: #666666;
						vertical-align: middle;
					}
					' . $addon_id . ' .pagination li.active {
						color: #333;
						background: none;
						border: 1px solid #666;
					}
					' . $addon_id . ' .pagination a:link,
					' . $addon_id . ' .pagination a:active,
					' . $addon_id . ' .pagination a:visited,
					' . $addon_id . ' .pagination a:hover {
						background: none;
						-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
						-webkit-tap-highlight-color: transparent;
					}
					' . $addon_id . ' .pagination a,
					' . $addon_id . ' .pagination li,
					' . $addon_id . ' .pagination ul {
						margin: 0;
						padding: 0;
						color: inherit;
						font-size: inherit;
						font-weight: inherit;
					}
					' . $addon_id . ' .pagination a {
						text-decoration: none;
						color: inherit;
						font-family: "Microsoft YaHei", Tahoma, Arial, sans-serif;
					}
					' . $addon_id . ' .pagination li:hover {
						color: #fff;
						background: #CC1E04;
					}
					' . $addon_id . ' .pagination > .active > span {
						background:none !important;
					}
				</style>';
				$output .= '<ul class="pagination">';
				if ($page != 1) {
					$output .= '<li><a href="' . $url . '&page=' . ($page - 1) . '&zcpcatid=' . $catid[0] . '">«</a></li>';
				} else {
					$output .= '<li class="disabled"><span>«</span></li>';
				}

				for ($i = 1; $i <= $all_page; $i++) {
					if ($page == $i) {
						$output .= '<li class="active"><span>' . $i . '</span></li>';
					} else {
						$output .= '<li><a href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '">' . $i . '</a></li>';
					}
				}
				// 判断是不是最后一页
				if ($page != $all_page) {
					$output .= '<li><a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $catid[0] . '" >»</a></li>';
				} else {
					$output .= '<li class="disabled"><span>»</span></li>';
				}
				$output .= '</ul>';
			}
			// 翻页样式四
			if ($page_style_selector == 'page04') {
				$output .= '
				<style>
					' . $addon_id . ' .fant3{color:#ffa;font-size:14px;line-height:30px;text-align: center;width:100%;display:block;}
					' . $addon_id . ' .fant3 li{display: inline-block;margin:10px 3px;}
					' . $addon_id . ' .fant3 li a{color:' . $page1_fontcolor . ';text-decoration:none;display:inline-block;}
					' . $addon_id . ' .current{width:30px!important;height:30px!important;line-height:30px!important;background:' . $page1_cur_bgcolor . '!important;color:' . $page1_cur_fontcolor . '!important;}
					' . $addon_id . ' .zxfPagenum{margin:0px!important;}
				</style>
				';
				$output .= '
					<ul class="fant3">';
				$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
				$output .= '    <li><a href="' . $url . '&page=1&zcpcatid=' . $catid[0] . '" >首页</a></li>';

				if ($page != 1) {
					$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
					$output .= '    <li><a href="' . $url . '&page=' . ($page - 1) . '&zcpcatid=' . $catid[0] . '" >上一页</a></li>';
				}
				for ($i = 1; $i <= $all_page; $i++) {
					if ($page == $i) {
						$output .= '<li><a class="current zxfPagenum">' . $i . '</a></li>';
					} else {
						$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
						$output .= '<li><a class="zxfPagenum" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '">' . $i . '</a></li>';
					}
				}
				if ($page != $all_page) {
					$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
					$output .= '    <li><a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $catid[0] . '" >下一页</a></li>';
				}

				$url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
				$output .= '    <li><a href="' . $url . '&page=' . $all_page . '&zcpcatid=' . $catid[0] . '" >尾页</a></li>';

				$output .= '<li><a href="javascript:;">' . $page . '/' . $all_page . '页</a></li>
					</ul>
				';
			}
			// 翻页样式五
			if ($page_style_selector == 'page05') {
				$output .= "
				<style>
					{$addon_id} .pagination-box {
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 36px 0;
						border-top: #E1E9EC dashed 1px;
					}
					{$addon_id} .page-item-box {
						display: flex;
    				align-items: center;
					}
					{$addon_id} .page-item-box a,
					{$addon_id} .page-item-box .btn {
						min-width: 36px;
						height: 36px;
						line-height: 36px;
						background: {$page1_bgcolor};
						border-radius: 3px;
						border: 1px solid {$page1_bordercolor};
						display: inline-block;
						text-align: center;
						margin: 0 3px;
						padding: 0 10px;
						font-size: 14px;
						color: {$page1_fontcolor};
						cursor: pointer;
					}
					{$addon_id} .page-item-box a.active {
						background: {$page1_cur_bgcolor};
						border-color: {$page1_cur_bordercolor};
						color: {$page1_cur_fontcolor};
					}
					{$addon_id} .page-item-box span {
						font-size: 12px;
						color: {$page1_fontcolor};
					}
					{$addon_id} .page-item-box span.active {
						color: {$page1_cur_bgcolor};
						margin-left: 5px;
					}
					{$addon_id} .page-item-box input {
						width: 63px;
						height: 36px;
						line-height: 36px;
						background: none;
						border-radius: 3px;
						border: 1px solid {$page1_bordercolor};
						margin: 0 5px;
						text-align: center;
						font-size: 14px;
					}
					{$addon_id} .page-item-box .btn {
						width: 80px;
					}
				</style>
				";
				$page_arr = $this->generatePagination($page, $all_page, 5);
				$output .= '
					<div class="pagination-box">
						<div class="page-item-box">';
						if ($page != 1) {
							$output .= '<a href="' . $url . '&page=' . ($page - 1) . '&zcpcatid=' . $catid[0] . '">&lt;</a>';
						}
						for ($i = 0; $i < count($page_arr); $i++) {
							if($page_arr[$i] == $page) {
								$output .= '<a class="active">' . $page_arr[$i] . '</a>';
							}
							elseif($page_arr[$i] == '...') {
								$output .= '<span>...</span>';
							}
							else {
								$output .= '<a href="' . $url . '&page=' . $page_arr[$i] . '&zcpcatid=' . $catid[0] . '">' . $page_arr[$i] . '</a>';
							}
						}
						if ($page < $all_page) {
							$output .= '<a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $catid[0] . '">下一页 &gt;</a>';
						}
						if ($all_page > 3) {
							$output .= '
								<span class="active">' . $page . '</span>
								<span style="margin-right: 8px;"> / ' . $all_page . ' </span>
							';
							$output .= '
								<span>到第</span>
								<input type="text" id="current' . $id . '" />
								<span>页</span>
								<div class="btn" onclick="toPage' . $id . '()">确定</div>
							';
						}
						$output .= '
						</div>
					<div>
				';
				$output .= '
				<script>
					function toPage' . $id . '() {
						var page' . $id . ' = $("#current' . $id . '").val();
						if (!page' . $id . ') {
							alert("请输入要跳转的页码");
						}else if(!(/^[1-9]\d*$/.test(page' . $id . '))) {
							alert("请输入正确的页码");
						}else if(page' . $id . ' > ' . $all_page. ') {
							alert("当前输入的页码大于总页数");
						}else {
							window.location.href = "/' . $url . '&page=" + page' . $id . ' + "&zcpcatid=' . $catid[0] . '";
						}
					}
				</script>';
			}
		}
		return $output;
	}

	public function scripts()
    {
        $scripts = array(
			JURI::base(true) . '/components/com_jwpagefactory/assets/js/zxf_page.js',
			JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $scripts;
    }

	public function stylesheets()
    {
        $style_sheet = array(
			JURI::base(true) . '/components/com_jwpagefactory/assets/css/zxf_page.css',
			JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css'
		);

        $left_font_family_type70 = $this->safeGetProp('left_font_family_type70', '');

        if ($left_font_family_type70)
        {
            array_push($style_sheet, 'https://fonts.geekzu.org/css?family=' . $left_font_family_type70 . ':100,100italic,200,200italic,300,300italic,400,400italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic&display=swap');
        }
        return $style_sheet;
    }

	// 处理省略号分页
	public function generatePagination($currentPage, $totalPages) {
		$pagination = [];

		// 添加当前页和第一页
		$pagination[] = 1;

		// 添加省略号前的页码
		if ($currentPage - 2 > 1) {
			$pagination[] = '...';
		}
		if ($currentPage - 1 > 1) {
			$pagination[] = $currentPage - 1;
		}

		// 添加当前页及其后两页
		if ($currentPage > 1) {
			$pagination[] = $currentPage;
		}
		if ($currentPage + 1 < $totalPages) {
			$pagination[] = $currentPage + 1;
		}
		if ($currentPage + 2 < $totalPages) {
			$pagination[] = $currentPage + 2;
		}

		// 添加省略号后的页码
		if ($currentPage + 2 < $totalPages - 1) {
			$pagination[] = '...';
		}
		if ($currentPage + 2 < $totalPages) {
			$pagination[] = $totalPages - 1;
		}
		if ($currentPage < $totalPages) {
			$pagination[] = $totalPages;
		}

		return $pagination;
	}

	// 处理适配
	public function toFit($key)
	{
		$settings = $this->addon->settings;
		$arr = (array)$settings;

		$md = '';
		$sm = '';
		$xs = '';

		if ($arr[$key]) {
			if (is_object($arr[$key])) {
				$md = $arr[$key]->md;
				$sm = $arr[$key]->sm;
				$xs = $arr[$key]->xs;
			} else {
				$md = $arr[$key];
				$sm = $arr[$key . '_sm'];
				$xs = $arr[$key . '_xs'];
			}
		}
		return (object)array(
			'md' => $md, 'sm' => $sm, 'xs' => $xs
		);
	}

	//去掉url指定参数
	function removeqsvar($url, $var_names)
	{
		foreach ($var_names as $param) {
			$url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
			$url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
		}
		$url = trim($url, "?");
		$url = trim($url, "#");
		$url = trim($url, "&");
		$url = trim($url, "/");
		return $url;
	}

	// 处理适配的值
    public function getResponsiveValues($baseName, $defaults = null) {

        $settings = $this->addon->settings;

        // 设置默认值（支持完整覆盖或部分覆盖）
        $finalDefaults = array_merge([
            'md' => '',
            'sm' => '',
            'xs' => ''
        ], (array)$defaults);

        // 动态构建属性名
        $value = $baseName;
        $valueSm = $baseName . '_sm';
        $valueXs = $baseName . '_xs';

        // 检查主属性是否存在
        if (isset($settings->$value)) {
            $mainValue = $settings->$value;

            if ($mainValue && is_object($mainValue)) {
                // 对象处理：从对象属性获取值
                return [
                    'md' => $mainValue->md ?? $finalDefaults['md'],
                    'sm' => $mainValue->sm ?? $finalDefaults['sm'],
                    'xs' => $mainValue->xs ?? $finalDefaults['xs']
                ];
            } elseif ($mainValue) {
                // 标量值处理：从后缀属性获取响应值
                return [
                    'md' => $mainValue ?? $finalDefaults['md'],
                    'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
                    'xs' => $settings->$valueXs ?? $finalDefaults['xs']
                ];
            }
        }

        // 当主属性存在但为假值时（如0、空字符串等），返回默认值
        return $finalDefaults;
    }

    /**
     * 处理普通变量 安全获取对象属性值
     * @param object $obj 对象实例
     * @param string $prop 属性名称
     * @param mixed $default 默认值 (默认为'percent')
     * @param bool $strict 严格模式 (true: 0/false视为有效值; false: 0/false视为空值)
     * @return mixed 属性值或默认值
     */
    public function safeGetProp($prop, $default = '', $strict = false)
    {

        $settings = $this->addon->settings;
        // 检查属性是否存在
        if (!isset($settings->$prop)) {
            return $default;
        }
        $value = $settings->$prop;

        // 严格模式：0/false视为有效值
        if ($strict) {
            return $value;
        }
        // 非严格模式：空值检查
        return trim($value) ? $value : $default;
    }
}
