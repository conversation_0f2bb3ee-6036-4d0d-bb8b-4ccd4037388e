<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'friendly_link',
        'title' => '友情链接',
        'desc' => '',
        'category' => '咨询/友情链接',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                // 切换item
                'section_tab_item' => array(
                    'title' => '内容列表',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '名称',
                            'std' => '公司简介',
                        ),
                        'tz_link_style' => array(
                            'type' => 'select',
                            'title' => '链接方式',
                            'desc' => '链接方式',
                            'values' => array(
                                'nei' => '内部链接',
                                'wai' => '外部链接',
                            ),
                            'std' => 'nei',
                        ),
                        'link1' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('tz_link_style', '=', 'nei'),
                            ),
                        ),

                        'link2' => array(
                            'type' => 'text',
                            'title' => '外部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(                                                                                                                                                                                                                   
                                array('tz_link_style', '=', 'wai'),
                            ),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '中国炼焦行业协会',
                            'link1' => 'https://www.cnljxh.com/',
                            'link2' => 'https://www.cnljxh.com/',
                        ),
                        array(
                            'title' => '山西焦化集团',
                            'link1' => 'https://www.sxjh.com.cn/',
                            'link2' => 'https://www.sxjh.com.cn/',
                        ),
                        array(
                            'title' => '山西华鑫肥业股份有限公司',
                            'link1' => 'https://feiye.sxhuaxin.cn/',
                            'link2' => 'https://feiye.sxhuaxin.cn/',
                        ),
                        array(
                            'title' => '山西华鑫煤焦化实业集团有限公司',
                            'link1' => 'https://jiaohua.sxhuaxin.cn/',
                            'link2' => 'https://jiaohua.sxhuaxin.cn/',
                        ),
                    ),
                ),
                'tz_style' => array(
                    'type' => 'select',
                    'title' => '跳转方式',
                    'desc' => '跳转方式',
                    'values' => array(
                        '_blank' => '新窗口',
                        '_self' => '当前页面',
                    ),
                    'std' => '_blank',
                ),
                'tc_site' => array(
                    'type' => 'select',
                    'title' => '下拉方向',
                    'desc' => '下拉方向',
                    'values' => array(
                        'x' => '下',
                        's' => '上',
                    ),
                    'std' => 'x',
                ),
                'a_right_icon' => array(
                    'type' => 'media',
                    'title' => '右侧图标',
                    'desc' => '',
                    'std' => '/components/com_jwpagefactory/addons/friendly_link/assets/images/s-.jpg',
                    // 'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/friendly_link/assets/images/s-.jpg',
                ),
                'a_width' => array(
                    'type' => 'slider',
                    'title' => '整体边框宽度',
                    'std' => array(
                        'md' => 328
                    ),
                    'max' => 1000,
                    'responsive'=> true
                ),
                'a_height' => array(
                    'type' => 'slider',
                    'title' => '整体边框高度',
                    'std' => array(
                        'md' => 30
                    ),
                    'max' => 1000,
                    'responsive'=> true
                ),
                'a_bg_color' => array(
                    'type' => 'color',
                    'title' => '整体背景颜色',
                    'std' => '#fff',
                ),
                'a_title' => array(
                    'type' => 'text',
                    'title' => '提示标题',
                    'std' => '友情链接',
                ),
                'a_link_color' => array(
                    'type' => 'color',
                    'title' => '提示标题文字颜色',
                    'std' => '#b8b8b8',
                ),
                'a_link_fontsize' => array(
                    'type' => 'slider',
                    'title' => '提示标题文字大小',
                    'std' => '',
                    'max' => 50,
                    'responsive'=> true
                ),
                'a_border_color' => array(
                    'type' => 'color',
                    'title' => '描边边框颜色',
                    'std' => '#999999',
                ),
                'a_border_width' => array(
                    'type' => 'slider',
                    'title' => '描边边框粗细',
                    'std' => array(
                        'md' => 1
                    ),
                    'max' => 50,
                    'responsive'=> true
                ),
                'a_border_radius' => array(
                    'type' => 'slider',
                    'title' => '描边边框圆角',
                    'std' => '',
                    'max' => 100,
                    'responsive'=> true
                ),
            ),
        ),
    )
);
