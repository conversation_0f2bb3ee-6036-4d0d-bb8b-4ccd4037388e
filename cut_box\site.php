<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonCut_box extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $link = (isset($settings->link) && $settings->link) ? $settings->link : '';
        $second_con = (isset($settings->second_con) && $settings->second_con) ? $settings->second_con : 0;
        // 是否开启链接
        $show_link = (isset($settings->show_link)) ? $settings->show_link : 1;
        // 是否站内页面链接
        $link_type = (isset($settings->link_type)) ? $settings->link_type : 0;
        $open_type = (isset($settings->open_type) && $settings->open_type) ? $settings->open_type : '_blank';
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        // 模块内边距
        $model_padding = (isset($settings->model_padding) && $settings->model_padding) ? $settings->model_padding : '0px';
        // 副标题距离标题的距离
        $second_con_top = (isset($settings->second_con_top) && $settings->second_con_top) ? $settings->second_con_top : '0';
        // 是否开启动画
        $is_animation = (isset($settings->is_animation)) ? $settings->is_animation : 0;
        $title_top = (isset($settings->title_top)) ? $settings->title_top : 0;
        $animation = '';
        if($is_animation == 1) {
           $animation .= 'transition: all 0.3s ease-in-out;';
        }
        $output = '';
        $output .= '<style>                        
            ' . $addon_id . ' .box {
                height: ' . $settings->model_height . 'px;
                background:' . $settings->bg_color . ';
                display: flex;
                flex-flow: column;
                justify-content: center;
                padding: ' . $model_padding . ';
                border: 1px solid ' . $settings->bg_color_border . ';
                ' . $animation . '
            }
            ' . $addon_id . ' .box:hover {
                background:' . $settings->bg_color_hover . ';
                cursor: pointer;
                border:1px solid ' . $settings->bg_color_border_hover . ';
            } 
            ' . $addon_id . ' .box .box_img {
                background: url(' . $settings->image_t . ') no-repeat;
                background-size: auto ' . $settings->image_height . 'px;
                height: ' . $settings->image_height . 'px;
                background-position: center;
                width: 100%;
                ' . $animation . '
            }
            ' . $addon_id . ' .box .box_title {
                text-align: center;
                margin-top: '.$title_top.'px;
            }
            ' . $addon_id . ' .box .box_title span {
                color:' . $settings->font_color . ';
                font-size:' . $settings->font_size . 'px;
                ' . $animation . '  
            }
            ' . $addon_id . ' .second_con {
                font-size:  ' . $settings->second_con_font_size . 'px !important;
                color:' . $settings->second_con_font_color . ' ;
                text-align: center;
                margin-top: ' . $second_con_top . 'px;
                ' . $animation . '
            }
            ' . $addon_id . ' .box:hover .box_title span {
                color:' . $settings->font_color_hover . ';
                font-size:' . $settings->font_size . 'px;
            } 
            ' . $addon_id . ' .box:hover .box_img {
                background: url(' . $settings->image_t_hover . ') no-repeat;
                background-size: auto ' . $settings->image_height . 'px;
                height: ' . $settings->image_height . 'px;
                background-position: center;
                width: 100%;
            } 
            ' . $addon_id . ' .box:hover .second_con {
                color:' . $settings->second_con_font_color_hover . ';
            }
        </style>';
        if ($show_link == '1') {
            $link_ = '';
            if($link_type == 1) {
                $link_ .= 'href="';
                if($detail_page_id){
                    $id = base64_encode($detail_page_id);
                    $link_ .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                }
                $link_ .= '"';
            } else if ($link != '') {
                $link_ .= 'href="' . $link . '"';
            }
            $output .= '<a target="' . $open_type . '" ' . $link_ . ' class="link_box">';
        }
        $output .= '<div class="box">';
        $output .= '<div class="box_img">';
        $output .= '</div>';
        $output .= '<div class="box_title">';
        $output .= '<span>' . $settings->input_text . '</span>';
        $output .= '</div>';
        if ($second_con == 1) {
            $output .= '  <div class="second_con">' . $settings->second_con_text . '</div>';
        }
        $output .= '</div>';
        if ($show_link == '1') {
            $output .= '</a>';
        }
        return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = '';
        $output .= '
        <#
            let image_t = (!_.isEmpty(data.image_t) && data.image_t) ? data.image_t : "";
            let image_t_hover = (!_.isEmpty(data.image_t_hover) && data.image_t_hover) ? data.image_t_hover : "";
            var model_padding = data.model_padding || "0px";
            var second_con_top = data.second_con_top || 0;
            var is_animation = data.is_animation || 0;
            var animation = "";
            if(is_animation == 1) {
                animation = "transition: all 0.3s ease-in-out;";
            }
        #>
        <style type="text/css">
            <#
                var addonId = "#jwpf-addon-"+ data.id;
            #>
            {{addonId}} .box {
                height: {{data.model_height}}px;
                background:{{data.bg_color}};
                display: flex;
                flex-flow: column;
                justify-content: center;
                padding: {{ model_padding }};
                border: 1px solid {{data.bg_color_border}};
                {{ animation }}
            }
            {{addonId}} .box:hover {
                background: {{data.bg_color_hover}};
                cursor: pointer;
                border:1px solid {{data.bg_color_border_hover}}
            } 
            {{addonId}} .box .box_img {
                background:url({{image_t}}) no-repeat;
                background-size: auto {{data.image_height}}px;
                height:{{data.image_height}}px;
                background-position: center;
                width: 100%;
                {{ animation }}
            }
            {{addonId}} .box .box_title {
                text-align: center;
                margin-top: {{data.title_top}}px;
            }
            {{addonId}} .box .box_title span {
                color:{{data.font_color}};
                font-size:{{data.font_size}}px;
                {{ animation }}
            }
            {{addonId}}  .second_con {
                font-size: {{data.second_con_font_size}}px !important;
                color: {{data.second_con_font_color}};
                text-align: center;
                margin-top: {{ second_con_top }}px;
                {{ animation }}
            }
            {{addonId}} .box:hover .box_title span {
                color:{{data.font_color_hover}};
                font-size:{{data.font_size}}px;
            } 
            {{addonId}} .box:hover .box_img {
                background:url({{image_t_hover}}) no-repeat;
                background-size: auto {{data.image_height}}px;
                height:{{data.image_height}}px;
                background-position: center;
                width: 100%;
            } 
            {{addonId}} .box:hover .second_con {
                color:{{data.second_con_font_color_hover}};
            }
        </style>
        <div class="box">
            <div class="box_img">

            </div>
            <div class="box_title">
                <span>{{data.input_text}}</span>
            </div>
            <# if(data.second_con){ #>
                <div class="second_con">{{{data.second_con_text}}}</div>
            <# } #>
        </div>
    ';
        return $output;
    }

}