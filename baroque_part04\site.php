<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonBaroque_part04 extends JwpagefactoryAddons
{

    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        $bgImg = (isset($settings->bgImg) && $settings->bgImg)? $settings->bgImg : 'https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png';
        $content_text = (isset($settings->content_text)) ? $settings->content_text : '「中华巴洛克」是西洋建筑与中国建筑艺术在立面装饰技巧上的有机结合，既保留了巴洛克建筑的精美造型和装饰手法，强调追求新颖奇特、富丽堂皇的艺术效果。';

        $output = '<div class="section-9">
			<div class="img-box">
				<img src="' . $bgImg . '" alt="" class="img" />
				<div class="cover">
					<div class="content">
						<p class="text">' . $content_text . '</p>
					</div>
				</div>
			</div>
		</div>';

        return $output;
    }

    public function scripts()
    {

    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;

        $js = 'jQuery(function($){
		    
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        //图片高度
        $section_img_height_md = (isset($settings->section_img_height) && $settings->section_img_height) ? $settings->section_img_height : 720;
        $section_img_height_sm = (isset($settings->section_img_height_sm) && $settings->section_img_height_sm) ? $settings->section_img_height_sm : 430;
        $section_img_height_xs = (isset($settings->section_img_height_xs) && $settings->section_img_height_xs) ? $settings->section_img_height_xs : 250;
        //遮罩颜色
        $cover_bg_color = (isset($settings->cover_bg_color) && $settings->cover_bg_color) ? $settings->cover_bg_color : 'rgba(105, 23, 18, 0.9)';

        $css =
            $addonId . ' * {
				margin: 0;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-9 {
				width: 100%;
				height: ' . $section_img_height_md . 'px;
			}
			' . $addonId . ' .section-9 .img-box {
				width: 100%;
				height: 100%;
				overflow: hidden;
				position: relative;
			}
			' . $addonId . ' .section-9 .img-box .img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-9 .img-box .cover {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background: ' . $cover_bg_color .';
				padding: 87px 55px;
				box-sizing: border-box;
				opacity: 0;
				z-index: -1;
			}
			' . $addonId . ' .section-9 .img-box .cover .content {
				position: relative;
				background: url(https://oss.lcweb01.cn/joomla/20210918/1712387bf91c2461a0e5c997f2880b95.png) no-repeat center;
				background-size: contain;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20px;
				box-sizing: border-box;
			}
			' . $addonId . ' .section-9 .img-box .cover .content::before {
				content: "";
				width: 49px;
				height: 90px;
				background: url(https://oss.lcweb01.cn/joomla/20210918/f677288bf6ab7bc3bb32d1a157dc0d24.png) no-repeat;
				background-size: 100%;
				position: absolute;
				left: 0;
				top: 0;
				transform: translate3d(-100px, -100px, 0);
				transition: all ease-in-out 300ms;
				bottom
			}
			' . $addonId . ' .section-9 .img-box .cover .content::after {
				content: "";
				width: 49px;
				height: 90px;
				background: url(https://oss.lcweb01.cn/joomla/20210918/645bd5c1dc703a2f358f803bfa2288bc.png) no-repeat;
				background-size: contain;
				position: absolute;
				right: 0;
				bottom: 0;
				transform: translate3d(100px, 100px, 0);
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-9 .img-box .cover .content .text {
				font-size: 16px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36px;
				opacity: 0;
				transition-delay: 200ms;
                max-height: 100%;
                overflow: hidden;
			}
			' . $addonId . ' .section-9 .img-box:hover .img {
				transform: scale(1.2);
			}
			' . $addonId . ' .section-9 .img-box:hover .cover {
				opacity: 1;
				z-index: 2;
			}
			' . $addonId . ' .section-9 .img-box:hover .cover .content::before, ' . $addonId . ' .section-9 .img-box:hover .cover .content::after {
				transform: translate3d(0px, 0px, 0px);
			}
			' . $addonId . ' .section-9 .img-box:hover .cover .content .text {
				opacity: 1;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    ' . $addonId . ' .section-9 { 
			        height: ' . $section_img_height_sm . 'px;
			    }
			}
			@media (max-width: 767px) {
			    ' . $addonId . ' .section-9 { 
			        height: ' . $section_img_height_xs . 'px;
			    }
			    ' . $addonId . ' .section-9 .img-box .cover {
			        padding: 30px;
			    }
			}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		//背景图
		var bgImg = data.bgImg || "https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png";
		//遮罩文字
		var content_text = data.content_text || "「中华巴洛克」是西洋建筑与中国建筑艺术在立面装饰技巧上的有机结合，既保留了巴洛克建筑的精美造型和装饰手法，强调追求新颖奇特、富丽堂皇的艺术效果。";
		//图片高度
		var section_img_height = data.section_img_height || 720;
		//文字内容高度
		var cover_bg_color = data.cover_bg_color || "rgba(105, 23, 18, 0.9)";
		#>
        <style type="text/css">
			{{ addonId }} * {
				margin: 0;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .section-9 {
				width: 100%;
				height: {{section_img_height.md}}px;
			}
			{{ addonId }} .section-9 .img-box {
				width: 100%;
				height: 100%;
				overflow: hidden;
				position: relative;
			}
			{{ addonId }} .section-9 .img-box .img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .section-9 .img-box .cover {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background: {{cover_bg_color}};
				padding: 87px 55px;
				box-sizing: border-box;
				opacity: 0;
				z-index: -1;
			}
			{{ addonId }} .section-9 .img-box .cover .content {
				position: relative;
				background: url(https://oss.lcweb01.cn/joomla/20210918/1712387bf91c2461a0e5c997f2880b95.png) no-repeat center;
				background-size: contain;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20px;
				box-sizing: border-box;
			}
			{{ addonId }} .section-9 .img-box .cover .content::before {
				content: "";
				width: 49px;
				height: 90px;
				background: url(https://oss.lcweb01.cn/joomla/20210918/f677288bf6ab7bc3bb32d1a157dc0d24.png) no-repeat;
				background-size: 100%;
				position: absolute;
				left: 0;
				top: 0;
				transform: translate3d(-100px, -100px, 0);
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .section-9 .img-box .cover .content::after {
				content: "";
				width: 49px;
				height: 90px;
				background: url(https://oss.lcweb01.cn/joomla/20210918/645bd5c1dc703a2f358f803bfa2288bc.png) no-repeat;
				background-size: contain;
				position: absolute;
				right: 0;
				bottom: 0;
				transform: translate3d(100px, 100px, 0);
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .section-9 .img-box .cover .content .text {
				font-size: 16px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36px;
				opacity: 0;
				transition-delay: 200ms;
                max-height: 100%;
                overflow: hidden;
			}
			{{ addonId }} .section-9 .img-box:hover .img {
				transform: scale(1.2);
			}
			{{ addonId }} .section-9 .img-box:hover .cover {
				opacity: 1;
				z-index: 2;
			}
			{{ addonId }} .section-9 .img-box:hover .cover .content::before, {{ addonId }} .section-9 .img-box:hover .cover .content::after {
				transform: translate3d(0px, 0px, 0px);
			}
			{{ addonId }} .section-9 .img-box:hover .cover .content .text {
				opacity: 1;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    {{ addonId }} .section-9 { 
			        height: {{section_img_height.sm}}px;
			    }
			}
			@media (max-width: 767px) {
			    {{ addonId }} .section-9 { 
			        height: {{section_img_height.xs}}px;
			    }
			    {{ addonId }} .section-9 .img-box .cover { 
			        padding: 30px;
			    }
			}
		</style>
        <div class="section-9">
			<div class="img-box">
				<img src=\'{{bgImg}}\' alt="" class="img" />
				<div class="cover">
					<div class="content">
						<p class="text">{{content_text}}</p>
					</div>
				</div>
			</div>
		</div>
		';

        return $output;
    }
}
