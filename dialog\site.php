<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2024-02-27 11:07:17
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonDialog extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $output = '';

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 点击按钮出弹窗
        $floating_id = 'floating-' . $this->addon->id;

        $dialog_addon_id = (isset($settings->dialog_addon_id) && $settings->dialog_addon_id) ? 'dialog-' .$settings->dialog_addon_id : 'dialog-';

        $float_img = (isset($settings->float_img) && $settings->float_img) ? $settings->float_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240221/63de139cf8ba383e116f0d3100f07933.png';
        $phone_float_img = (isset($settings->phone_float_img) && $settings->phone_float_img) ? $settings->phone_float_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240221/63de139cf8ba383e116f0d3100f07933.png';

        $dialog_img = (isset($settings->dialog_img) && $settings->dialog_img) ? $settings->dialog_img : 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50';
        $close_img = (isset($settings->close_img) && $settings->close_img) ? $settings->close_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png';
        $mask_close = isset($settings->mask_close) ? $settings->mask_close : 1;
        $close_ids = $mask_close == 1 ? '#' . $dialog_addon_id . ' .mask, #' . $dialog_addon_id . ' .close' : '#' . $dialog_addon_id . ' .close';

        $dialog_level = (isset($settings->dialog_level) && $settings->dialog_level) ? $settings->dialog_level : '99999999999999999999999999999';
        $float_level = (isset($settings->float_level) && $settings->float_level) ? $settings->float_level : '99999999';
        $mask_bg = (isset($settings->mask_bg) && $settings->mask_bg) ? $settings->mask_bg : 'rgba(0,0,0,0.5)';
        $dialog_img_fill = (isset($settings->dialog_img_fill) && $settings->dialog_img_fill) ? $settings->dialog_img_fill : 'cover';
        $close_img_fill = (isset($settings->close_img_fill) && $settings->close_img_fill) ? $settings->close_img_fill : 'cover';
        $float_img_fill = (isset($settings->float_img_fill) && $settings->float_img_fill) ? $settings->float_img_fill : 'cover';

        if (isset($settings->dialog_width) && $settings->dialog_width) {
            if (is_object($settings->dialog_width)) {
                $dialog_width = $settings->dialog_width->md;
                $dialog_width_sm = $settings->dialog_width->sm;
                $dialog_width_xs = $settings->dialog_width->xs;
            } else {
                $dialog_width = $settings->dialog_width;
                $dialog_width_sm = $settings->dialog_width_sm;
                $dialog_width_xs = $settings->dialog_width_xs;
            }
        } else {
            $dialog_width = '600';
            $dialog_width_sm = '600';
            $dialog_width_xs = '600';
        }

        if (isset($settings->dialog_height) && $settings->dialog_height) {
            if (is_object($settings->dialog_height)) {
                $dialog_height = $settings->dialog_height->md;
                $dialog_height_sm = $settings->dialog_height->sm;
                $dialog_height_xs = $settings->dialog_height->xs;
            } else {
                $dialog_height = $settings->dialog_height;
                $dialog_height_sm = $settings->dialog_height_sm;
                $dialog_height_xs = $settings->dialog_height_xs;
            }
        } else {
            $dialog_height = '400';
            $dialog_height_sm = '400';
            $dialog_height_xs = '400';
        }

        if (isset($settings->close_width) && $settings->close_width) {
            if (is_object($settings->close_width)) {
                $close_width = $settings->close_width->md;
                $close_width_sm = $settings->close_width->sm;
                $close_width_xs = $settings->close_width->xs;
            } else {
                $close_width = $settings->close_width;
                $close_width_sm = $settings->close_width_sm;
                $close_width_xs = $settings->close_width_xs;
            }
        } else {
            $close_width = '40';
            $close_width_sm = '40';
            $close_width_xs = '40';
        }

        if (isset($settings->close_height) && $settings->close_height) {
            if (is_object($settings->close_height)) {
                $close_height = $settings->close_height->md;
                $close_height_sm = $settings->close_height->sm;
                $close_height_xs = $settings->close_height->xs;
            } else {
                $close_height = $settings->close_height;
                $close_height_sm = $settings->close_height_sm;
                $close_height_xs = $settings->close_height_xs;
            }
        } else {
            $close_height = '40';
            $close_height_sm = '40';
            $close_height_xs = '40';
        }

        if (isset($settings->close_position) && $settings->close_position) {
            if (is_object($settings->close_position)) {
                $close_position = $settings->close_position->md;
                $close_position_sm = $settings->close_position->sm;
                $close_position_xs = $settings->close_position->xs;
            } else {
                $close_position = $settings->close_position;
                $close_position_sm = $settings->close_position_sm;
                $close_position_xs = $settings->close_position_xs;
            }
        } else {
            $close_position = '0 0 auto auto';
            $close_position_sm = '0 0 auto auto';
            $close_position_xs = '0 0 auto auto';
        }

        $close_position_arr = explode(" ", $close_position);
        $close_position_arr_sm = explode(" ", $close_position_sm);
        $close_position_arr_xs = explode(" ", $close_position_xs);

        if (isset($settings->float_position) && $settings->float_position) {
            if (is_object($settings->float_position)) {
                $float_position = $settings->float_position->md;
                $float_position_sm = $settings->float_position->sm;
                $float_position_xs = $settings->float_position->xs;
            } else {
                $float_position = $settings->float_position;
                $float_position_sm = $settings->float_position_sm;
                $float_position_xs = $settings->float_position_xs;
            }
        } else {
            $float_position = '0 0 auto auto';
            $float_position_sm = '0 0 auto auto';
            $float_position_xs = '0 0 auto auto';
        }

        $float_position_arr = explode(" ", $float_position);
        $float_position_arr_sm = explode(" ", $float_position_sm);
        $float_position_arr_xs = explode(" ", $float_position_xs);

        if (isset($settings->float_width) && $settings->float_width) {
            if (is_object($settings->float_width)) {
                $float_width = $settings->float_width->md;
                $float_width_sm = $settings->float_width->sm;
                $float_width_xs = $settings->float_width->xs;
            } else {
                $float_width = $settings->float_width;
                $float_width_sm = $settings->float_width_sm;
                $float_width_xs = $settings->float_width_xs;
            }
        } else {
            $float_width = '270';
            $float_width_sm = '270';
            $float_width_xs = '65';
        }

        if (isset($settings->float_height) && $settings->float_height) {
            if (is_object($settings->float_height)) {
                $float_height = $settings->float_height->md;
                $float_height_sm = $settings->float_height->sm;
                $float_height_xs = $settings->float_height->xs;
            } else {
                $float_height = $settings->float_height;
                $float_height_sm = $settings->float_height_sm;
                $float_height_xs = $settings->float_height_xs;
            }
        } else {
            $float_height = '364';
            $float_height_sm = '364';
            $float_height_xs = '228';
        }

        $output .= '<style>
            #' . $dialog_addon_id . '{
                display: none;
                width: 100vw;
                height: 100vh;
                position: fixed;
                top: 0;
                left: 0;
                z-index: '. $dialog_level .';
            }
            #' . $dialog_addon_id . ' .mask{
                width: 100%;
                height: 100%;
                background-color: '. $mask_bg .';
            }
            #' . $dialog_addon_id . ' .dialog{
                position: absolute;
                inset: 0;
                margin: auto;
                width: ' . $dialog_width . 'px;
                height: ' . $dialog_height . 'px;
            }
            #' . $dialog_addon_id . ' .dialog-content{
                border-radius: 10px;
                overflow: hidden;
                width: 100%;
                height: 100%;
            }
            #' . $dialog_addon_id . ' .dialog img{
                width: 100%;
                height: 100%;
                object-fit: ' . $dialog_img_fill . ';
            }
            #' . $dialog_addon_id . ' .dialog .close{
                position: absolute;
                top: ' . $close_position_arr[0] . ';
                right: ' . $close_position_arr[1] . ';
                bottom: ' . $close_position_arr[2] . ';
                left: ' . $close_position_arr[3] . ';
                width: ' . $close_width . 'px;
                height: ' . $close_height . 'px;
            }
            #' . $dialog_addon_id . ' .dialog .close img{
                object-fit: ' . $close_img_fill . ';
            }
            @media (min-width: 768px) and (max-width: 991px) {
                #' . $floating_id . '{
                    width: ' . $float_width_sm . 'px;
                    height: ' . $float_height_sm . 'px;
                    top: ' . $float_position_arr_sm[0] . ';
                    right: ' . $float_position_arr_sm[1] . ';
                    bottom: ' . $float_position_arr_sm[2] . ';
                    left: ' . $float_position_arr_sm[3] . ';
                }
                #' . $dialog_addon_id . ' .dialog{
                    width: ' . $dialog_width_sm . 'px;
                    height: ' . $dialog_height_sm . 'px;
                }
                #' . $dialog_addon_id . ' .dialog .close{
                    top: ' . $close_position_arr_sm[0] . ';
                    right: ' . $close_position_arr_sm[1] . ';
                    bottom: ' . $close_position_arr_sm[2] . ';
                    left: ' . $close_position_arr_sm[3] . ';
                    width: ' . $close_width_sm . 'px;
                    height: ' . $close_height_sm . 'px;
                }
            }
            @media (max-width: 767px) {
                #' . $floating_id . '{
                    width: ' . $float_width_xs . 'px;
                    height: ' . $float_height_xs . 'px;
                    top: ' . $float_position_arr_xs[0] . ';
                    right: ' . $float_position_arr_xs[1] . ';
                    bottom: ' . $float_position_arr_xs[2] . ';
                    left: ' . $float_position_arr_xs[3] . ';
                }
                #' . $dialog_addon_id . ' .dialog{
                    width: ' . $dialog_width_xs . 'px;
                    height: ' . $dialog_height_xs . 'px;
                }
                #' . $dialog_addon_id . ' .dialog .close{
                    top: ' . $close_position_arr_xs[0] . ';
                    right: ' . $close_position_arr_xs[1] . ';
                    bottom: ' . $close_position_arr_xs[2] . ';
                    left: ' . $close_position_arr_xs[3] . ';
                    width: ' . $close_width_xs . 'px;
                    height: ' . $close_height_xs . 'px;
                }
                #' . $floating_id . ' img.phone{
                    display: block;
                }
                #' . $floating_id . ' img.pc{
                    display: none;
                }
            }
        </style>';

        $output.='<div id="'. $dialog_addon_id .'">
            <div class="mask"></div>
            <div class="dialog">
                <div class="dialog-content">
                    <img src="'. $dialog_img .'">
                </div>

                <div class="close">
                    <img src="'. $close_img .'">
                </div>
            </div>
        </div>';

        $output .= '<script>
            $("'. $close_ids .'").on("click", function(){
                $("#' . $dialog_addon_id . '").hide();
            })
        </script>';
        // 点击按钮出弹窗结束
        return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
    public static function getTemplate()
    {
        return '<div>本段文字用于编辑模式下占位，预览模式下不显示</div>';
    }

}