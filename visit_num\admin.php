<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 11:00:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id= $input->get('site_id',0);
 $company_id= $input->get('company_id',0);

 JwAddonsConfig::addonConfig(
 	array(
 		'type' => 'content',
 		'addon_name' => 'visit_num',
 		'title' => JText::_('访问量'),
 		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
 		'category' => '咨询/友情链接',
 		'attr' => array(
 			'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'kaiqi_xianshi'               => array(
                    'type'   => 'select',
                    'title'  => JText::_('开启文字显示'),
                    'desc'   => JText::_('开启文字显示'),
                    'values' => array(
                        'k' => JText::_('显示'),
                        'g'   => JText::_('不显示'),
                    ),
                    'std'    => 'k'
                ),
                'num_title'             => array(
                    'type'  => 'text',
                    'title' => JText::_('访问量名称'),
                    'desc'  => JText::_('访问量名称'),
                    'std'   => '访问量：',
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_color'             => array(
                    'type'  => 'color',
                    'title' => JText::_('访问量名称字体颜色'),
                    'desc'  => JText::_('访问量名称字体颜色'),
                    'std'   => '#000000',
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_color'             => array(
                    'type'  => 'color',
                    'title' => JText::_('访问量数字颜色'),
                    'desc'  => JText::_('访问量数字颜色'),
                    'std'   => '#ff0000',
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_bgcolor'             => array(
                    'type'  => 'color',
                    'title' => JText::_('背景颜色'),
                    'desc'  => JText::_('背景颜色'),
                    'std'   => '#ffffff',
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_font_size'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('访问量名称字体大小'),
                    'desc'  => JText::_('访问量名称字体大小'),
                    'max'  => 100,
                    'min' =>1,
                    'std'   => 18,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_font_size'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('访问量数字大小'),
                    'desc'  => JText::_('访问量数字大小'),
                    'max'  => 100,
                    'min' =>1,
                    'std'   => 18,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_bj'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('名称数字间距'),
                    'desc'  => JText::_('名称数字间距'),
                    'max'  => 200,
                    'min' =>0,
                    'std'   => 0,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_zbj'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('名称左间距'),
                    'desc'  => JText::_('名称左间距'),
                    'max'  => 200,
                    'min' =>0,
                    'std'   => 0,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_bg_width'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('背景宽度'),
                    'desc'  => JText::_('背景宽度'),
                    'max'  => 1000,
                    'min' =>100,
                    'std'   => 100,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
                'num_title_bg_border'             => array(
                    'type'  => 'slider',
                    'title' => JText::_('背景圆角'),
                    'desc'  => JText::_('背景圆角'),
                    'max'  => 100,
                    'min' =>0,
                    'std'   => 0,
                    'depends'     => array(array('kaiqi_xianshi', '=', 'k')),
                ),
 			),
 		),
 	)
 );
