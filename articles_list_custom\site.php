<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonArticles_list_custom extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $zcatid = $_GET['catid'] ?? 0;


        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $zcpcatid = $_GET['zcpcatid'] ?? 1000000000000;
        $settings = $this->addon->settings;

        $output = '';
        // 数据来源
        $data_source = $this->safeGetP<PERSON>('data_source', 'articles');
        if($data_source == 'products'){
            $items_all = $this->getProductCates();
        }elseif($data_source == 'nav'){
            $items_all = $this->getNavs();
        }else{
            $items_all = $this->getArticleCates();
        }

        // 是否显示选项卡
        $show_tabs = $this->safeGetProp('show_tabs', 0);

        if($show_tabs){
            if (!count($items_all)) {
                $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
                return $output;
            }
            $output .= $this->addonCateStyle();
            $output .= $this -> getCateHtml($items_all);
        }else{
            $items = $items_all['items'];
            if (!count($items)) {
                $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
                return $output;
            }
            $output.= $this -> getListContent($items);
        }

        return $output;
    }

    // 获取分类样式
    public function addonCateStyle(){
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $style = '<style>';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-wrap {
            width: 100%;
            margin-bottom: 20px;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-box {
            display: flex;
            overflow-x: auto;
            padding: 0 20px;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-list {
            display: flex;
            list-style: none;
            padding: 0;
            margin: 0;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-item {
            padding: 10px 20px;
            cursor: pointer;
            white-space: nowrap;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-item.active {
            background-color: #f0f0f0;
            border-bottom: 2px solid #007bff;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-content {
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }';
        $style .= '        ' . $addon_id . ' .article-list-custom-cate-item-content {
            display: none;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-item-content.active {
            display: block;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-cate-item-content p {
            margin: 0;
            padding: 0 10px;
        }';
        $style .= '</style>';

        return $style;
    }

    // 获取分类html
    public function getCateHtml($items_all){
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $output = '';
        if(count($items_all)){
            $output .= '<div class="article-list-custom-cate-wrap">';
            $output .= '<div class="article-list-custom-cate-box">';
            $output .= '<ul class="article-list-custom-cate-list">';
            foreach ($items_all as $key => $cate) {
                if($cate['nav_id']){
                    $active = $cate['cates']['tag_id'] == $cate['nav_id'] ? ' active' : '';
                }else{
                    if($key == 0){
                        $active = ' active';
                    }else{
                        $active = '';
                    }
                }
                $output .= '<li class="article-list-custom-cate-item' . $active . '" data-id="' . $cate['cates']['tag_id'] . '">' . $cate['cates']['title'] . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
            $output .= '</div>';

            $output .= '<div class="article-list-custom-cate-content">';
            foreach ($items_all as $key => $cate) {
                if($cate['nav_id']){
                    $active = $cate['cates']['tag_id'] == $cate['nav_id'] ? ' active' : '';
                }else{
                    if($key == 0){
                        $active = ' active';
                    }else{
                        $active = '';
                    }
                }
                $output .= '<div class="article-list-custom-cate-item-content' . $active . '" data-id="' . $cate['cates']['tag_id'] . '">';
                foreach ($cate['lists'] as $key => $value) {
                    if($key === 'items'){
                        if(!count($value)){
                            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
                        }
                        $output.=$this -> getListContent($value, ['items_count' => $cate['lists']['items_count'], 'page' => $cate['lists']['page'], 'limit' => $cate['lists']['limit'], 'catid' => [$cate['cates']['tag_id']]]);
                    }
                }
                $output .= '</div>';
            }
            $output .= '</div>';

            $output .= '<script>';
            $output .= '$("'.$addon_id.' .article-list-custom-cate-wrap .article-list-custom-cate-list li").on("click", function() {
                var cateId = $(this).data("id");
                $("'.$addon_id.' .article-list-custom-cate-wrap .article-list-custom-cate-list li").removeClass("active");
                $(this).addClass("active");
                $("'.$addon_id.' .article-list-custom-cate-content .article-list-custom-cate-item-content").removeClass("active");
                $("'.$addon_id.' .article-list-custom-cate-content .article-list-custom-cate-item-content[data-id=\'" + cateId + "\']").addClass("active");
            });';
            $output .= '</script>';
        }
        return $output;
    }

    // 获取文章列表
    public function getListContent($items = [], $pages = ['items_count' => 0, 'page' => 1, 'limit' => 10, 'catid' => []]){
        $output = '';
        $output .= $this->addonStyle();

        // 详情页跳转方式
        $detail_page_jump = $this->safeGetProp('detail_page_jump', '_self');
        // 隐藏列表项封面图片
        $item_image_hidden = $this->safeGetProp('item_image_hidden', 0);
        // 隐藏列表项简介
        $item_intro_hidden = $this->safeGetProp('item_intro_hidden', 0);
        // 日期显示位置
        $item_date_position = $this->safeGetProp('item_date_position', 'image_top');
        // 更多显示位置
        $item_more_position = $this->safeGetProp('item_more_position', 'bottom');
        // 是否开启大图模式
        $big_img_status = $this->safeGetProp('big_img_status', 0);
        // 大图显示个数
        $big_img_number = $this->safeGetProp('big_img_number', 2);

        // 是否显示选项卡
        $show_tabs = $this->safeGetProp('show_tabs', 0);

        $output.='<div class="article-list-custom-wrap">';
        if($big_img_status){
            $item_intro_show_big = $this->safeGetProp('item_intro_show_big', 0);
            $item_date_position_big = $this->safeGetProp('item_date_position_big', 'title_bottom');
            $item_date_specific_show_big = $this->safeGetProp('item_date_specific_show_big', 0);
            $item_date_specific_format_show_big = 'item_date_specific_format_show_big';
            $item_date_specific_format_show_big_mask = 'item_date_specific_format_show_big_mask';
            $big_img_mask_status = $this->safeGetProp('big_img_mask_status', '0');
            $item_date_show_big = $this->safeGetProp('item_date_show_big', '0');
            $item_date_show_big_mask = $this->safeGetProp('item_date_show_big_mask', '0');
            $item_date_position_big_mask = $this->safeGetProp('item_date_position_big_mask', 'image_bottom_title_top');
            $item_intro_show_big_mask = $this->safeGetProp('item_intro_show_big_mask', '1');
            $item_date_specific_show_big_mask = 'item_date_specific_show_big_mask';
            $item_date_format_big = 'item_date_format_big';
            $item_date_format_big_mask = 'item_date_format_big_mask';
            $item_date_show_big_mask_str = 'item_date_show_big_mask';
            $item_date_show_big_str = 'item_date_show_big';

            $output .= '<div class="wrap-big">';
                foreach ($items as $key => $item) {
                    if ($detail_page_jump == 'no') {
                        $link = 'javascript:void(0);';
                        $target = '';
                    } else {
                        $link = $item->link;
                        $target = ' target="'. $detail_page_jump . '"';
                    }
                    if ($item->image_thumbnail != '') {
                        $image = $item->image_thumbnail;
                    } else {
                        $image = 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/articles_list/assets/images/moren.png';
                    }

                    if($key < $big_img_number){
                        $output .= '
                        <a class="article-list-custom-item big-img k'.$key.' n'.$big_img_number.' big-img" href="' . $link . '"' . $target . '>
                            <div class="item-image-wrap">';
                                $output .= '
                                <div class="item-image-box">
                                    <img class="thumbnail" src="' . $image . '" alt="' . $item->title . '">
                                </div>';
                            $output .= '
                            </div>';
                            if ($item_date_show_big && $item_date_position_big == 'cover_image') {
                                $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str);
                            }
                            $output .= '
                            <div class="item-content-box">';
                                $output .= '
                                <div class="item-content">';
                                    if ($item_date_show_big && $item_date_position_big == 'image_bottom_title_top') {
                                        $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str, 'show');
                                    }
                                    $output .= '
                                    <h3 class="item-title">' . $item->title . '</h3>';
                                    if ($item_date_show_big && $item_date_position_big == 'title_bottom') {
                                        $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str, 'show');
                                    }
                                    if ($item_intro_show_big) {
                                        $output .= '
                                        <div class="item-intro">' . strip_tags($item->introtext) . '</div>';
                                    }
                                    if ($item_date_show_big && $item_date_position_big == 'intro_bottom') {
                                        $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str, 'show');
                                    }
                                    if ($item_more_position == 'bottom') {
                                        $output .= $this->processMoreHtml();
                                    }
                                $output .= '
                                </div>';
                                if ($item_date_position_big == 'title_intro_right' || $item_date_position_big == 'title_intro_left' || $item_more_position == 'title_intro_right') {
                                    $output .= '
                                    <div class="item-content-other">';
                                        if ($item_date_position_big == 'title_intro_right' || $item_date_position_big == 'title_intro_left') {
                                            $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str, 'show');
                                            $output .= $this->processDateHtml($item, $item_date_specific_show_big, $item_date_specific_format_show_big, $item_date_format_big, $item_date_show_big_str, 'show');
                                        }
                                        if ($item_more_position == 'title_intro_right') {
                                            $output .= $this->processMoreHtml();
                                        }
                                    $output .= '
                                    </div>
                                    ';
                                }
                            $output .= '
                            </div>';
                            if($big_img_mask_status){
                                // 遮罩
                                $output.='<div class="card-hover">
                                    <div class="info-wrap">';
                                        if ($item_date_show_big_mask && $item_date_position_big_mask === 'image_bottom_title_top') {
                                            $output.='<div class="hover-date">'.$this->processDateHtml($item, $item_date_specific_show_big_mask, $item_date_specific_format_show_big_mask, $item_date_format_big_mask, $item_date_show_big_mask_str, 'show').'</div>';
                                        }
                                        $output.='<div class="hover-title ellipsis-text">' . $item->title . '</div>';
                                        if ($item_date_show_big_mask && $item_date_position_big_mask === 'title_bottom') {
                                            $output.='<div class="hover-date">'.$this->processDateHtml($item, $item_date_specific_show_big_mask, $item_date_specific_format_show_big_mask, $item_date_format_big_mask, $item_date_show_big_mask_str, 'show').'</div>';
                                        }
                                        $output.='<div class="line"></div>';
                                        if($item_intro_show_big_mask){
                                            $output.='<div class="hover-content ellipsis-text">
                                                ' . strip_tags($item->introtext) . '
                                            </div>';
                                        }
                                        if ($item_date_show_big_mask && $item_date_position_big_mask === 'intro_bottom') {
                                            $output.='<div class="hover-date">'.$this->processDateHtml($item, $item_date_specific_show_big_mask, $item_date_specific_format_show_big_mask, $item_date_format_big_mask, $item_date_show_big_mask_str, 'show').'</div>';
                                        }
                                    $output.='</div>
                                    <div class="hover-arrow"></div>
                                </div>';
                            }
                        $output .= '
                        </a>';
                    }
                }
            $output .= '</div>';
        }
        $output .= '
        <div class="swiper-container">
            <div class="article-list-custom swiper-wrapper">
        ';
            foreach ($items as $key => $item) {
                if ($detail_page_jump == 'no') {
                    $link = 'javascript:void(0);';
                    $target = '';
                } else {
                    $link = $item->link;
                    $target = ' target="'. $detail_page_jump . '"';
                }
                if ($item->image_thumbnail != '') {
                    $image = $item->image_thumbnail;
                } else {
                    $image = 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/articles_list/assets/images/moren.png';
                }
                if($big_img_status && $key < $big_img_number){
                    continue;
                }

                $output .= '
                <a class="article-list-custom-item swiper-slide" href="' . $link . '"' . $target . '>
                    <div class="item-image-wrap">

                ';
                    $output .= $this->processCategoryHtml($item);
                    if ($item_date_position == 'image_top') {
                        $output .= $this->processDateHtml($item);
                    }
                    if ($item_image_hidden == 0) {
                        $output .= '
                        <div class="item-image-box">
                            <img class="thumbnail" src="' . $image . '" alt="' . $item->title . '">
                        </div>';
                    }
                $output .= '
                    </div>';
                if ($item_date_position == 'cover_image') {
                    $output .= $this->processDateHtml($item);
                }
                $output .= '
                    <div class="item-content-box">';
                        $output .= '
                        <div class="item-content">';
                            if ($item_date_position == 'image_bottom_title_top') {
                                $output .= $this->processDateHtml($item);
                            }
                            $output .= '
                            <h3 class="item-title">' . $item->title . '</h3>';
                            if ($item_date_position == 'title_bottom') {
                                $output .= $this->processDateHtml($item);
                            }
                            if ($item_intro_hidden == 0) {
                                $output .= '
                                <div class="item-intro">' . strip_tags($item->introtext) . '</div>';
                            }
                            if ($item_date_position == 'intro_bottom') {
                                $output .= $this->processDateHtml($item);
                            }
                            if ($item_more_position == 'bottom') {
                                $output .= $this->processMoreHtml();
                            }
                        $output .= '
                        </div>';
                    if ($item_date_position == 'title_intro_right' || $item_date_position == 'title_intro_left' || $item_more_position == 'title_intro_right') {
                        $output .= '
                        <div class="item-content-other">';
                            if ($item_date_position == 'title_intro_right' || $item_date_position == 'title_intro_left') {
                                $output .= $this->processDateHtml($item);
                            }
                            if ($item_more_position == 'title_intro_right') {
                                $output .= $this->processMoreHtml();
                            }
                        $output .= '
                        </div>
                        ';
                    }
                    $output .= '
                    </div>';
                $output .= '
                </a>';
            }
            $output .= '
            </div>';
        $output .= '
        </div>
        ';
        $output.='</div>';
        $output .= $this->pagination_html();
        $output .= $this->swiper_js();
        $output .= $this->getPageHtml($pages);

        return $output;
    }

    /**
     * 处理日期html避免重复
     * @param object $item 文章对象
     * @param string $item_date_specific_show_str 特殊显示日期的配置属性名
     * @param string $item_date_specific_format_show_str 特殊显示日期格式的配置属性名
     * @param string $formatType 日期格式类型
     * @param string $item_date_hidden_str 隐藏日期的配置属性名
     * @param string $hideType 隐藏类型（'hide'或'show'）
     * @return string 返回处理后的日期HTML
     */
    public function processDateHtml($item, $item_date_specific_show_str = 'item_date_specific_show', $item_date_specific_format_show_str = 'item_date_specific_format_show', $formatType = 'item_date_format', $item_date_hidden_str='item_date_hidden', $hideType = 'hide')
    {
        $item_date_specific_show = $this->safeGetProp($item_date_specific_show_str, 0);
        $item_date_specific_format_show = $this->safeGetProp($item_date_specific_format_show_str, 'Y');
        // 隐藏列表项日期
        $item_date_hidden = $this->safeGetProp($item_date_hidden_str, 0);

        if($hideType == 'hide'){
            if ($item_date_hidden == 1) {
                return '';
            }
        }else{
            if ($item_date_hidden == 0) {
                return '';
            }
        }

        $output = '';
        $output .= '
            <div class="item-meta">';
                $output .= '
                <div class="item-date">';
                    if ($item_date_specific_show == 1 && $item_date_specific_format_show) {
                    $output .= '
                    <span class="item-date-specific">' . date($item_date_specific_format_show, strtotime($item->created)) . '</span>';
                    }
                    $output.= '
                    <span>' . $this->formatDate($item->created, $formatType) . '</span>
                </div>';
                $output .= '
            </div>
        ';

        return $output;
    }

    // 处理分类html避免重复
    public function processCategoryHtml($item)
    {
        $item_category_hidden = $this->safeGetProp('item_category_hidden', 0);
        $output = '';
        if ($item_category_hidden == 0) {
            $output .= '
            <div class="item-meta">
                <span class="item-cat">' . $item->category . '</span>
            </div>';
        }
        return $output;
    }

    // 处理更多html避免重复
    public function processMoreHtml()
    {
        $item_more_show = $this->safeGetProp('item_more_show', 0);
        $item_more_text = $this->safeGetProp('item_more_text', '');
        $item_more_icon = $this->safeGetProp('item_more_icon', '');
        $item_more_hover_icon = $this->safeGetProp('item_more_hover_icon', '');

        $output = '';
        if ($item_more_show == 1) {
            $output .= '
            <div class="item-more">';
                if ($item_more_text) {
                    $output .= '
                    <span class="item-more-text">' . $item_more_text . '</span>';
                }
                if ($item_more_icon || $item_more_hover_icon) {
                    $output .= '
                    <span class="item-more-icon">';
                    if ($item_more_icon) {
                        $output .= '
                        <img class="icon-img" src="' . $item_more_icon . '" alt="' . $item_more_text . '">';
                    }
                    if ($item_more_hover_icon) {
                        $output .= '
                        <img class="icon-img hover" src="' . $item_more_hover_icon . '" alt="' . $item_more_text . '">';
                    }
                    $output .= '
                    </span>';
                }
                $output .= '
            </div>
            ';
        }
        return $output;
    }

    // 处理适配的值
    public function getResponsiveValues($baseName, $defaults = null)
    {

        $settings = $this->addon->settings;

        // 设置默认值（支持完整覆盖或部分覆盖）
        $finalDefaults = array_merge([
            'md' => '',
            'sm' => '',
            'xs' => ''
        ], (array)$defaults);

        // 动态构建属性名
        $value = $baseName;
        $valueSm = $baseName . '_sm';
        $valueXs = $baseName . '_xs';

        // 检查主属性是否存在
        if (isset($settings->$value)) {
            $mainValue = $settings->$value;

            if ($mainValue && is_object($mainValue)) {
                // 对象处理：从对象属性获取值
                return [
                    'md' => $mainValue->md ?? $finalDefaults['md'],
                    'sm' => $mainValue->sm ?? $finalDefaults['sm'],
                    'xs' => $mainValue->xs ?? $finalDefaults['xs']
                ];
            } elseif ($mainValue) {
                // 标量值处理：从后缀属性获取响应值
                return [
                    'md' => $mainValue,
                    'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
                    'xs' => $settings->$valueXs ?? $finalDefaults['xs']
                ];
            }
        }

        // 当主属性存在但为假值时（如0、空字符串等），返回默认值
        return $finalDefaults;
    }

    /**
     * 处理普通变量 安全获取对象属性值
     * @param object $obj 对象实例
     * @param string $prop 属性名称
     * @param mixed $default 默认值 (默认为'percent')
     * @param bool $strict 严格模式 (true: 0/false视为有效值; false: 0/false视为空值)
     * @return mixed 属性值或默认值
     */
    public function safeGetProp($prop, $default = '', $strict = false)
    {

        $settings = $this->addon->settings;
        // 检查属性是否存在
        if (!isset($settings->$prop)) {
            return $default;
        }
        $value = $settings->$prop;

        // 严格模式：0/false视为有效值
        if ($strict) {
            return $value;
        }
        // 非严格模式：空值检查
        return trim($value) ? $value : $default;
    }

    /**
     * 获取投影样式
     */
    public function getBoxShadow($item_box_shadow_status_str = 'item_box_shadow_status', $item_box_shadow_color_str = 'item_box_shadow_color', $item_box_shadow_blur_str = 'item_box_shadow_blur', $item_box_shadow_spread_str = 'item_box_shadow_spread', $item_box_shadow_horizontal_str = 'item_box_shadow_horizontal', $item_box_shadow_vertical_str = 'item_box_shadow_vertical'){
        $style = '';
        $item_box_shadow_status = $this->safeGetProp($item_box_shadow_status_str, 0);
        if($item_box_shadow_status){
            $item_box_shadow_color = $this->safeGetProp($item_box_shadow_color_str, 'rgba(0, 0, 0, 0.1)');
            $item_box_shadow_blur = $this->safeGetProp($item_box_shadow_blur_str, 10);
            $item_box_shadow_spread = $this->safeGetProp($item_box_shadow_spread_str, 0);
            $item_box_shadow_horizontal = $this->safeGetProp($item_box_shadow_horizontal_str, 0);
            $item_box_shadow_vertical = $this->safeGetProp($item_box_shadow_vertical_str, 0);
            $style .= 'box-shadow: ' . $item_box_shadow_horizontal . 'px ' . $item_box_shadow_vertical . 'px ' . $item_box_shadow_blur . 'px ' . $item_box_shadow_spread . 'px ' . $item_box_shadow_color . ';';
        }else{
            $style .= 'box-shadow: none;';
        }
        return $style;
    }
    /**
     * 自动设置投影后的内边距，容器内边距刚好可以放下投影，外边距为负的内边距
     * @param string $item_box_shadow_status_str 投影状态属性名
     * @param string $item_box_shadow_blur_str 投影模糊度属性名
     * @param string $item_box_shadow_spread_str 投影扩散度属性名
     * @param string $item_box_shadow_horizontal_str 投影水平偏移属性名
     * @param string $item_box_shadow_vertical_str 投影垂直偏移属性名
     * @return object['padding' => string, 'margin' => string] 返回内边距样式，按上右下左顺序
     */
    public function getBoxShadowPadding($item_box_shadow_status_str = 'item_box_shadow_status', $item_box_shadow_blur_str = 'item_box_shadow_blur', $item_box_shadow_spread_str = 'item_box_shadow_spread', $item_box_shadow_horizontal_str = 'item_box_shadow_horizontal', $item_box_shadow_vertical_str = 'item_box_shadow_vertical')
    {
        $settings = $this->addon->settings;
        $item_box_shadow_status = $this->safeGetProp($item_box_shadow_status_str, 0);
        if ($item_box_shadow_status) {
            $item_box_shadow_blur = $this->safeGetProp($item_box_shadow_blur_str, 10);
            $item_box_shadow_spread = $this->safeGetProp($item_box_shadow_spread_str, 0);
            $item_box_shadow_horizontal = $this->safeGetProp($item_box_shadow_horizontal_str, 0);
            $item_box_shadow_vertical = $this->safeGetProp($item_box_shadow_vertical_str, 0);

            // 内边距为投影模糊度和扩散度之和，外边距为负的内边距
            $padding = ($item_box_shadow_blur + abs($item_box_shadow_spread)) . 'px';
            $margin = '-' . ($item_box_shadow_blur + abs($item_box_shadow_spread)) . 'px';

            return (object)['padding' => $padding, 'margin' => $margin];
        } else {
            return (object)['padding' => '0', 'margin' => '0'];
        }
    }

    // 列表样式
    public function addonStyle()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        // 列表项排版
        $item_layout = $this->safeGetProp('item_layout', 'column');
        // 列表项高度
        $item_height = $this->getResponsiveValues('item_height', ['md' => '', 'sm' => '', 'xs' => '']);
        // 列表项数量
        $item_number = $this->getResponsiveValues('item_number', ['md' => 3, 'sm' => 2, 'xs' => 1]);
        // 列表项水平间距
        $item_column_gap = $this->getResponsiveValues('item_column_gap', ['md' => 20, 'sm' => '', 'xs' => '']);
        // 列表项垂直间距
        $item_row_gap = $this->getResponsiveValues('item_row_gap', ['md' => 20, 'sm' => '', 'xs' => '']);
        // 列表项内边距
        $item_padding = $this->getResponsiveValues('item_padding', ['md' => '20px 0 0 0', 'sm' => '', 'xs' => '']);
        // 图片高度
        $item_image_height = $this->getResponsiveValues('item_image_height', ['md' => 260, 'sm' => '', 'xs' => '']);
        // 图片填充方式
        $item_image_object_fit = $this->safeGetProp('item_image_object_fit', 'cover');
        // 列表项内容内边距
        $item_content_padding = $this->getResponsiveValues('item_content_padding', ['md' => '20px 0 0 0', 'sm' => '', 'xs' => '']);
        // 列表边框宽度
        $item_border_width = $this->getResponsiveValues('item_border_width', ['md' => '3px 0 0 0', 'sm' => '', 'xs' => '']);
        // 列表移入边框宽度
        $hover_item_border_width = $this->getResponsiveValues('hover_item_border_width', ['md' => '', 'sm' => '', 'xs' => '']);
        // 列表边框颜色
        $item_border_color = $this->safeGetProp('item_border_color', '#EAEAEA');
        // 列表移入边框颜色
        $hover_item_border_color = $this->safeGetProp('hover_item_border_color', '');
        // 列表项圆角
        $item_border_radius = $this->getResponsiveValues('item_border_radius', '');
        // 列表项移入圆角
        $hover_item_border_radius = $this->getResponsiveValues('hover_item_border_radius', '');
        // 列表项背景颜色
        $item_bg_color = $this->safeGetProp('item_bg_color', '#fff');
        // 列表项移入背景颜色
        $hover_item_bg_color = $this->safeGetProp('hover_item_bg_color', '');
        // 图片圆角
        $item_img_border_radius = $this->getResponsiveValues('item_img_border_radius', '');
        // 图片移入圆角
        $hover_item_img_border_radius = $this->getResponsiveValues('hover_item_img_border_radius', '');
        // 标题颜色
        $item_title_color = $this->safeGetProp('item_title_color', '#1e1e1e');
        // 标题移入颜色
        $hover_item_title_color = $this->safeGetProp('hover_item_title_color', '');
        // 标题字体大小
        $item_title_font_size = $this->getResponsiveValues('item_title_font_size', ['md' => 18, 'sm' => 16, 'xs' => 14]);
        // 标题粗细
        $item_title_fontweight = $this->safeGetProp('item_title_fontweight', 'normal');
        // 标题显示行数
        $item_title_line = $this->getResponsiveValues('item_title_line', ['md' => 1, 'sm' => '', 'xs' => '']);
        // 简介颜色
        $item_intro_color = $this->safeGetProp('item_intro_color', '#666666');
        // 简介移入颜色
        $hover_item_intro_color = $this->safeGetProp('hover_item_intro_color', '');
        // 简介字体大小
        $item_intro_font_size = $this->getResponsiveValues('item_intro_font_size', ['md' => 14, 'sm' => '', 'xs' => '']);
        // 简介显示行数
        $item_intro_line = $this->getResponsiveValues('item_intro_line', ['md' => 3, 'sm' => '', 'xs' => '']);
        // 日期颜色
        $item_date_color = $this->safeGetProp('item_date_color', '#000000');
        // 日期移入颜色
        $hover_item_date_color = $this->safeGetProp('hover_item_date_color', '');
        // 日期字体大小
        $item_date_font_size = $this->getResponsiveValues('item_date_font_size', ['md' => 14, 'sm' => '', 'xs' => '']);
        // 日期字体粗细
        $item_date_fontweight = $this->safeGetProp('item_date_fontweight', 'normal');
        // 日期外边距
        $item_date_margin = $this->getResponsiveValues('item_date_margin', ['md' => '0 0 20px 0', 'sm' => '', 'xs' => '']);
        // 日期开启特殊显示对齐方式
        $item_date_specific_align = $this->safeGetProp('item_date_specific_align', 'start');
        // 特殊显示日期文字颜色
        $item_date_color_special = $this->safeGetProp('item_date_color_special', '');
        // 移入特殊显示日期文字颜色
        $hover_item_date_color_special = $this->safeGetProp('hover_item_date_color_special', '');
        // 特殊显示的日期字体大小
        $item_date_font_size_special = $this->getResponsiveValues('item_date_font_size_special', ['md' => '', 'sm' => '', 'xs' => '']);
        // 特殊显示的日期字体粗细
        $item_date_fontweight_special = $this->safeGetProp('item_date_fontweight_special', '');
        // 分类颜色
        $item_category_color = $this->safeGetProp('item_category_color', '#999999');
        // 分类移入颜色
        $hover_item_category_color = $this->safeGetProp('hover_item_category_color', '');
        // 分类字体大小
        $item_category_font_size = $this->getResponsiveValues('item_category_font_size', ['md' => 14, 'sm' => '', 'xs' => '']);
        // 更多按钮内边距
        $item_more_padding = $this->getResponsiveValues('item_more_padding', ['md' => '20px 0 0 0', 'sm' => '', 'xs' => '']);
        // 更多按钮外边距
        $item_more_margin = $this->getResponsiveValues('item_more_margin', ['md' => '20px 0 0 0', 'sm' => '', 'xs' => '']);
        // 更多按钮边框宽度
        $item_more_border_width = $this->getResponsiveValues('item_more_border_width', ['md' => '1px 0 0 0', 'sm' => '', 'xs' => '']);
        // 更多按钮移入边框宽度
        $hover_item_more_border_width = $this->getResponsiveValues('hover_item_more_border_width', ['md' => '', 'sm' => '', 'xs' => '']);
        // 更多按钮边框颜色
        $item_more_border_color = $this->safeGetProp('item_more_border_color', '#666');
        // 更多按钮移入边框颜色
        $hover_item_more_border_color = $this->safeGetProp('hover_item_more_border_color', '');
        // 更多文字颜色
        $item_more_color = $this->safeGetProp('item_more_color', '#666');
        // 更多文字移入颜色
        $hover_item_more_color = $this->safeGetProp('hover_item_more_color', '');
        // 更多文字字体大小
        $item_more_font_size = $this->getResponsiveValues('item_more_font_size', ['md' => 14, 'sm' => '', 'xs' => '']);
        // 更多按钮宽度
        $item_more_icon_width = $this->getResponsiveValues('item_more_icon_width', ['md' => 20, 'sm' => '', 'xs' => '']);
        $item_more_hover_icon = $this->safeGetProp('item_more_hover_icon', '');
        // 更多按钮文字与图标对齐方式
        $item_more_text_align = $this->safeGetProp('item_more_text_align', 'space-between');
        // 标题简介与日期/更多间距
        $item_title_intro_gap = $this->getResponsiveValues('item_title_intro_gap', ['md' => 20, 'sm' => '', 'xs' => '']);
        // 日期背景颜色
        $item_date_background_color = $this->safeGetProp('item_date_background_color', '');
        // 日期移入背景颜色
        $hover_item_date_background_color = $this->safeGetProp('hover_item_date_background_color', '');
        // 日期位置
        $item_date_position = $this->safeGetProp('item_date_position', 'image_top');
        // 日期内边距
        $item_date_padding = $this->getResponsiveValues('item_date_padding', ['md' => '', 'sm' => '', 'xs' => '']);
        // 日期边框宽度
        $item_date_border_width = $this->getResponsiveValues('item_date_border_width', ['md' => '', 'sm' => '', 'xs' => '']);
        // 移入日期边框宽度
        $hover_item_date_border_width = $this->getResponsiveValues('hover_item_date_border_width', ['md' => '', 'sm' => '', 'xs' => '']);
        // 日期边框颜色
        $item_date_border_color = $this->safeGetProp('item_date_border_color', '');
        // 移入日期边框颜色
        $hover_item_date_border_color = $this->safeGetProp('hover_item_date_border_color', '');
        // 日期圆角
        $item_date_border_radius = $this->getResponsiveValues('item_date_border_radius', ['md' => '', 'sm' => '', 'xs' => '']);
        // 移入日期圆角
        $hover_item_date_border_radius = $this->getResponsiveValues('hover_item_date_border_radius', ['md' => '', 'sm' => '', 'xs' => '']);
        // 开启轮播
        $swiper_status = $this->safeGetProp('swiper_status', 0);
        // 日期距列表项左距离
        $item_date_margin_left = $this->getResponsiveValues('item_date_margin_left', ['md' => 20, 'sm' => '', 'xs' => '']);
        // 日期距列表项上距离
        $item_date_margin_top = $this->getResponsiveValues('item_date_margin_top', ['md' => 20, 'sm' => '', 'xs' => '']);
        $item_box_shadow_padding = $this->getResponsiveValues('item_box_shadow_padding', ['md' => '10px 0 10px 0', 'sm' => '10px 0 10px 0', 'xs' => '10px 0 10px 0']);
        $item_box_shadow_margin = $this->getResponsiveValues('item_box_shadow_margin', ['md' => '-10px 0 -10px 0', 'sm' => '-10px 0 -10px 0', 'xs' => '-10px 0 -10px 0']);
        $hover_item_box_shadow_status = $this->safeGetProp('hover_item_box_shadow_status', 0);
        $item_box_shadow_status = $this->safeGetProp('item_box_shadow_status', 0);

        $style = '<style>';
        $style .= '
        ' . $addon_id . ' * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            transition: all ease-in-out 0.3s;
        }';
        $style .= $addon_id . ' .swiper-container {';
            if($item_box_shadow_status){
                $style.='padding: ' . $this -> getBoxShadowPadding() -> padding . ';
                margin: ' . $this -> getBoxShadowPadding() -> margin . ';';
            }
            if($hover_item_box_shadow_status){
                $style.='padding: ' . $this -> getBoxShadowPadding('hover_item_box_shadow_status', 'hover_item_box_shadow_blur', 'hover_item_box_shadow_spread', 'hover_item_box_shadow_horizontal', 'hover_item_box_shadow_vertical') -> padding . ';
                margin: ' . $this -> getBoxShadowPadding('hover_item_box_shadow_status', 'hover_item_box_shadow_blur', 'hover_item_box_shadow_spread', 'hover_item_box_shadow_horizontal', 'hover_item_box_shadow_vertical') -> margin . ';';
            }
            $style.='}';
        $style.= $addon_id . ' .article-list-custom {
            display: flex;
            ' . ($swiper_status == 1 ? '' : 'flex-wrap: wrap;') . '
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item {
            display: flex;
            flex-direction: ' . $item_layout . ';
            height: ' . $item_height['md'] . 'px;
            width: calc(100% / ' . $item_number['md'] . ' - ' . ($item_column_gap['md'] * ($item_number['md'] - 1) / $item_number['md']) . 'px);
            margin-right: ' . $item_column_gap['md'] . 'px;
            margin-bottom: ' . $item_row_gap['md'] . 'px;
            border-width: ' . $item_border_width['md'] . ';
            border-color: ' . $item_border_color . ';
            border-style: solid;
            border-radius: ' . $item_border_radius['md'] . ';
            background-color: ' . $item_bg_color . ';
            padding: ' . $item_padding['md'] . ';
            overflow: hidden;
            position: relative;
            '. $this->getBoxShadow() . '
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover {
            border-width: ' . $hover_item_border_width['md'] . ';
            border-color: ' . $hover_item_border_color . ';
            border-radius: ' . $hover_item_border_radius['md'] . ';
            background-color: ' . $hover_item_bg_color . ';
            '. $this->getBoxShadow('hover_item_box_shadow_status', 'hover_item_box_shadow_color', 'hover_item_box_shadow_blur', 'hover_item_box_shadow_spread', 'hover_item_box_shadow_horizontal', 'hover_item_box_shadow_vertical') . '
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['md'] . 'n) {
            margin-right: 0;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-image-box {';
            if (strpos($item_layout, 'row') !== false) {
                $style .= '
                width: ' . $item_image_height['md'] . 'px;
                height: 100%;';
            } else {
                $style .= '
                width: 100%;
                height: ' . $item_image_height['md'] . 'px;';
            }
            $style .= '
            overflow: hidden;
            border-radius: ' . $item_img_border_radius['md'] . ';
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover .item-image-box {
            border-radius: ' . $hover_item_img_border_radius['md'] . ';
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-image-box img {
            width: 100%;
            height: 100%;
            object-fit: ' . $item_image_object_fit . ';
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover .item-image-box img {
            transform: scale(1.1);
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-content-box {
            padding: ' . $item_content_padding['md'] . ';
            display: flex;
            ' . ($item_date_position == 'title_intro_left' ? 'flex-direction: row-reverse;' : '') . '
            ' . (strpos($item_layout, 'row') !== false ? 'flex: 1;
            ' : '') . '
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-content {
            flex: 1;
        }
        ';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-title {
            font-size: ' . $item_title_font_size['md'] . 'px;
            font-weight: ' . $item_title_fontweight . ';
            color: ' . $item_title_color . ';
            margin-bottom: 10px;
            line-height: 1.4;
            height: ' . $item_title_font_size['md'] * $item_title_line['md'] * 1.4 . 'px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: ' . $item_title_line['md'] . ';
            -webkit-box-orient: vertical;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover .item-title {
            color: ' . $hover_item_title_color . ';
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-intro {
            font-size: ' . $item_intro_font_size['md'] . 'px;
            color: ' . $item_intro_color . ';
            line-height: 1.4;
            height: ' . $item_intro_font_size['md'] * $item_intro_line['md'] * 1.4 . 'px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: ' . $item_intro_line['md'] . ';
            -webkit-box-orient: vertical;
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover .item-intro {
            color: ' . $hover_item_intro_color . ';
        }';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-meta {
            display: flex;
            flex-direction: column;
        }
        ' . $addon_id . ' .article-list-custom-item .item-meta .item-cat {
            color: ' . $item_category_color . ';
            font-size: ' . $item_category_font_size['md'] . 'px;
        }
        ' . $addon_id . ' .article-list-custom-item .item-meta .item-date {
            color: ' . $item_date_color . ';
            font-size: ' . $item_date_font_size['md'] . 'px;
            font-weight: ' . $item_date_fontweight . ';
            margin: ' . $item_date_margin['md'] . ';
            padding: ' . $item_date_padding['md'] . ';
            display: flex;
            flex-direction: column;
            align-items: ' . $item_date_specific_align . ';
            background-color: ' . $item_date_background_color . ';
            border-width: ' . $item_date_border_width['md'] . ';
            border-color: ' . $item_date_border_color . ';
            ' . ($item_date_border_width['md'] ? 'border-style: solid;' : '') . '
            border-radius: ' . $item_date_border_radius['md'] . ';
            ' . ($item_date_position == 'cover_image' ? 'position: absolute;
            left: ' . $item_date_margin_left['md'] . 'px;
            top: ' . $item_date_margin_top['md'] . 'px;' : '') . '
        }
        ';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-date .item-date-specific {
            color: ' . $item_date_color_special . ';
            font-size: ' . $item_date_font_size_special['md'] . 'px;
            font-weight: ' . $item_date_fontweight_special . ';
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-date .item-date-specific {
            color: ' . $hover_item_date_color_special . ';
        }
        ';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item:hover .item-meta .item-cat {
            color: ' . $hover_item_category_color . ';
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-meta .item-date {
            color: ' . $hover_item_date_color . ';
            background-color: ' . $hover_item_date_background_color . ';
            border-width: ' . $hover_item_date_border_width['md'] . ';
            border-radius: ' . $hover_item_date_border_radius['md'] . ';
            border-color: ' . $hover_item_date_border_color . ';
        }
        ';
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-more {
            display: flex;
            align-items: center;
            justify-content: ' . $item_more_text_align . ';
            border-width: ' . $item_more_border_width['md'] . ';
            border-color: ' . $item_more_border_color . ';
            border-style: solid;
            padding: ' . $item_more_padding['md'] . ';
            margin: ' . $item_more_margin['md'] . ';
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-more {
            border-width: ' . $hover_item_more_border_width['md'] . ';
            border-color: ' . $hover_item_more_border_color . ';
        }
        ' . $addon_id . ' .article-list-custom-item .item-more-text {
            font-size: ' . $item_more_font_size['md'] . 'px;
            color: ' . $item_more_color . ';
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-more-text {
            color: ' . $hover_item_more_color . ';
        }
        ' . $addon_id . ' .article-list-custom-item .item-more-icon {
            width: ' . $item_more_icon_width['md'] . 'px;
            height: ' . $item_more_icon_width['md'] . 'px;
            position: relative;
        }
        ' . $addon_id . ' .article-list-custom-item .item-more-icon .icon-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            position: absolute;
            top: 0;
            left: 0;
        }';
        if ($item_more_hover_icon) {
        $style .='
        ' . $addon_id . ' .article-list-custom-item .item-more-icon .icon-img.hover {
            opacity: 0;
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-more-icon .icon-img {
            opacity: 0;
        }
        ' . $addon_id . ' .article-list-custom-item:hover .item-more-icon .icon-img.hover {
            opacity: 1;
        }
        ';
        }
        // 标题简介右侧显示的时间及更多样式
        $style .= '
        ' . $addon_id . ' .article-list-custom-item .item-content-other {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: space-between;
            margin-' . ($item_date_position == 'title_intro_left' ? 'right' : 'left') . ': ' . $item_title_intro_gap['md'] . 'px;
        }
        ';

        $style .= '
        @media (min-width: 768px) and (max-width: 991px) {
            ' . $addon_id . ' .swiper-container {            
                padding: ' . $item_box_shadow_padding['sm'] . ';
                margin: ' . $item_box_shadow_margin['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item {
                height: ' . $item_height['sm'] . 'px;
                width: calc(100% / ' . $item_number['sm'] . ' - ' . ($item_column_gap['sm'] * ($item_number['sm'] - 1) / $item_number['sm']) . 'px);
                margin-right: ' . $item_column_gap['sm'] . 'px;
                margin-bottom: ' . $item_row_gap['sm'] . 'px;
                border-width: ' . $item_border_width['sm'] . ';
                border-radius: ' . $item_border_radius['sm'] . ';
                padding: ' . $item_padding['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['sm'] . 'n) {
                margin-right: 0;
            }
            ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['md'] . 'n) {
                margin-right: ' . $item_column_gap['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-image-box {
                ' . (strpos($item_layout, 'row') !== false ? 'width' : 'height') . ': ' . $item_image_height['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-content-box {
                padding: ' . $item_content_padding['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-title {
                font-size: ' . $item_title_font_size['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-intro {
                font-size: ' . $item_intro_font_size['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-meta .item-cat {
                font-size: ' . $item_category_font_size['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-meta .item-date {
                font-size: ' . $item_date_font_size['sm'] . 'px;
                margin: ' . $item_date_margin['sm'] . ';
                padding: ' . $item_date_padding['sm'] . ';
                border-width: ' . $item_date_border_width['sm'] . ';
                border-radius: ' . $item_date_border_radius['sm'] . ';
                ' . ($item_date_position == 'cover_image' ? '
                left: ' . $item_date_margin_left['sm'] . 'px;
                top: ' . $item_date_margin_top['sm'] . 'px;' : '') . '
            }
            ' . $addon_id . ' .article-list-custom-item:hover .item-meta .item-date {
                border-width: ' . $hover_item_date_border_width['sm'] . ';
                border-radius: ' . $hover_item_date_border_radius['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-date .item-date-specific {
                font-size: ' . $item_date_font_size_special['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-more {
                padding: ' . $item_more_padding['sm'] . ';
                border-width: ' . $item_more_border_width['sm'] . ';
                margin: ' . $item_more_margin['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item:hover .item-more {
                border-width: ' . $hover_item_more_border_width['sm'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-more-text {
                font-size: ' . $item_more_font_size['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-more-icon {
                width: ' . $item_more_icon_width['sm'] . 'px;
                height: ' . $item_more_icon_width['sm'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-content-other {
                margin-left: ' . $item_title_intro_gap['sm'] . 'px;
            }
        }
        ';
        $style .= '
        @media screen and (max-width: 767px) {
            ' . $addon_id . ' .swiper-container {            
                padding: ' . $item_box_shadow_padding['xs'] . ';
                margin: ' . $item_box_shadow_margin['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item {
                height: ' . $item_height['xs'] . 'px;
                width: calc(100% / ' . $item_number['xs'] . ' - ' . ($item_column_gap['xs'] * ($item_number['xs'] - 1) / $item_number['xs']) . 'px);
                margin-right: ' . $item_column_gap['xs'] . 'px;
                margin-bottom: ' . $item_row_gap['xs'] . 'px;
                border-width: ' . $item_border_width['xs'] . ';
                border-radius: ' . $item_border_radius['xs'] . ';
                padding: ' . $item_padding['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['xs'] . 'n) {
                margin-right: 0;
            }
            ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['md'] . 'n),
            ' . $addon_id . ' .article-list-custom-item:nth-child(' . $item_number['sm'] . 'n) {
                margin-right: ' . $item_column_gap['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-image-box {
                ' . (strpos($item_layout, 'row') !== false ? 'width' : 'height') . ': ' . $item_image_height['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-content-box {
                padding: ' . $item_content_padding['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-title {
                font-size: ' . $item_title_font_size['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-intro {
                font-size: ' . $item_intro_font_size['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-meta .item-cat {
                font-size: ' . $item_category_font_size['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-meta .item-date {
                font-size: ' . $item_date_font_size['xs'] . 'px;
                margin: ' . $item_date_margin['xs'] . ';
                padding: ' . $item_date_padding['xs'] . ';
                border-width: ' . $item_date_border_width['xs'] . ';
                border-radius: ' . $item_date_border_radius['xs'] . ';
                ' . ($item_date_position == 'cover_image' ? '
                left: ' . $item_date_margin_left['xs'] . 'px;
                top: ' . $item_date_margin_top['xs'] . 'px;' : '') . '
            }
            ' . $addon_id . ' .article-list-custom-item:hover .item-meta .item-date {
                border-width: ' . $hover_item_date_border_width['xs'] . ';
                border-radius: ' . $hover_item_date_border_radius['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-date .item-date-specific {
                font-size: ' . $item_date_font_size_special['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-more {
                padding: ' . $item_more_padding['xs'] . ';
                border-width: ' . $item_more_border_width['xs'] . ';
                margin: ' . $item_more_margin['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item:hover .item-more {
                border-width: ' . $hover_item_more_border_width['xs'] . ';
            }
            ' . $addon_id . ' .article-list-custom-item .item-more-text {
                font-size: ' . $item_more_font_size['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-more-icon {
                width: ' . $item_more_icon_width['xs'] . 'px;
                height: ' . $item_more_icon_width['xs'] . 'px;
            }
            ' . $addon_id . ' .article-list-custom-item .item-content-other {
                margin-left: ' . $item_title_intro_gap['xs'] . 'px;
            }
        }
        ';

        // 是否开启大图模式
        $big_img_status = $this->safeGetProp('big_img_status', 0);
        $item_number_big = $this->getResponsiveValues('item_number_big', ['md' => 2, 'sm' => 2, 'xs' => 1]);
        $item_layout_big = $this->safeGetProp('item_layout_big', 'column');
        $big_img_title_cover = $this->safeGetProp('big_img_title_cover', 0);
        $item_content_padding_big = $this->getResponsiveValues('item_content_padding_big', ['md' => '20px 0 0 0', 'sm' => '', 'xs' => '']);
        $item_content_bg_color_big = $this->safeGetProp('item_content_bg_color_big', 'rgba(0, 0, 0, 0.2)');
        $item_title_color_big = $this->safeGetProp('item_title_color_big', '#fff');
        $item_intro_color_big = $this->safeGetProp('item_intro_color_big', '#fff');
        $item_title_font_size_big = $this->getResponsiveValues('item_title_font_size_big', ['md' => 18, 'sm' => 16, 'xs' => 14]);
        $item_date_margin_big = $this->getResponsiveValues('item_date_margin_big', ['md' => '0 0 0 0', 'sm' => '', 'xs' => '']);
        $item_date_padding_big = $this->getResponsiveValues('item_date_padding_big', ['md' => '0 0 0 0', 'sm' => '', 'xs' => '']);
        $item_date_background_color_big = $this->safeGetProp('item_date_background_color_big', '');
        $item_date_specific_align_big = $this->safeGetProp('item_date_specific_align_big', 'start');
        $item_date_color_big = $this->safeGetProp('item_date_color_big', '#fff');
        $item_intro_font_size_big = $this->getResponsiveValues('item_intro_font_size_big', ['md' => 14, 'sm' => 14, 'xs' => 14]);
        $item_padding_big = $this->getResponsiveValues('item_padding_big', ['md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0']);
        $item_date_font_size_big = $this->getResponsiveValues('item_date_font_size_big', ['md' => 14, 'sm' => 14, 'xs' => 14]);
        $big_img_position = $this->safeGetProp('big_img_position', 'top');
        $item_border_color_big = $this->safeGetProp('item_border_color_big', '');
        $item_border_width_big = $this->getResponsiveValues('item_border_width_big', ['md' => '', 'sm' => '', 'xs' => '']);
        $item_width_big = $this->getResponsiveValues('item_width_big', ['md' => '50', 'sm' => '100', 'xs' => '100']);
        $item_margin_big = $this->getResponsiveValues('item_margin_big', ['md' => '0 80px 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0']);
        $item_bg_color_big_mask_settings = $this->safeGetProp('item_bg_color_big_mask_settings', 'gradient');
        $settings = $this->addon->settings;
        $item_bg_gradient_big_mask = (isset($settings -> item_bg_gradient_big_mask) ? $settings -> item_bg_gradient_big_mask : (object)[
            "color" => "rgba(180, 145, 90, 0.6)",
            "color2" => "rgba(0, 0, 0, 0.3)",
            "deg" => "180",
            "type" => "linear"
        ]);
        $item_bg_color_big_mask = $this->safeGetProp('item_bg_color_big_mask', 'rgba(rgba(180, 145, 90, 0.6)');
        $item_bg_image_big_mask = $this->safeGetProp('item_bg_image_big_mask', '');
        $item_date_color_big_mask = $this->safeGetProp('item_date_color_big_mask', 'rgba(255, 255, 255, 0.8)');
        $item_date_font_size_big_mask = $this->getResponsiveValues('item_date_font_size_big_mask', ['md' => 14, 'sm' => 14, 'xs' => 14]);
        $item_date_margin_big_mask = $this->getResponsiveValues('item_date_margin_big_mask', ['md' => '0 0 15px 0', 'sm' => '0 0 15px 0', 'xs' => '0 0 15px 0']);
        $item_date_padding_big_mask = $this->getResponsiveValues('item_date_padding_big_mask', ['md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0']);
        $item_title_margin_big = $this->getResponsiveValues('item_title_margin_big', ['md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0']);
        $item_title_margin_big_mask = $this->getResponsiveValues('item_title_margin_big_mask', ['md' => '0 0 15px 0', 'sm' => '0 0 15px 0', 'xs' => '0 0 15px 0']);
        $item_title_color_big_mask = $this->safeGetProp('item_title_color_big_mask', '#fff');
        $item_title_font_size_big_mask = $this->getResponsiveValues('item_title_font_size_big_mask', array('md' => 22, 'sm' => 16, 'xs' => 14));
        $item_title_line_big = $this->getResponsiveValues('item_title_line_big', array('md' => 1, 'sm' => 1, 'xs' => 1));
        $item_title_line_big_mask = $this->getResponsiveValues('item_title_line_big_mask', array('md' => 1, 'sm' => 1, 'xs' => 1));
        $item_title_fontweight_big = $this->safeGetProp('item_title_fontweight_big', 'normal');
        $item_title_fontweight_big_mask = $this->safeGetProp('item_title_fontweight_big_mask', 'bold');
        $item_title_underline_color_big_mask = $this->safeGetProp('item_title_underline_color_big_mask', 'rgba(255, 255, 255, 0.6)');
        $item_title_underline_margin_big_mask = $this->getResponsiveValues('item_title_underline_margin_big_mask', array('md' => '30px 0 30px 0', 'sm' => '30px 0 30px 0', 'xs' => '30px 0 30px 0'));
        $item_intro_color_big_mask = $this->safeGetProp('item_intro_color_big_mask', 'rgba(255, 255, 255, 0.9)');
        $item_intro_font_size_big_mask = $this->getResponsiveValues('item_intro_font_size_big_mask', array('md' => 14, 'sm' => 14, 'xs' => 14));
        $item_intro_line_big = $this->getResponsiveValues('item_intro_line_big', array('md' => 2, 'sm' => 2, 'xs' => 2));
        $item_intro_line_big_mask = $this->getResponsiveValues('item_intro_line_big_mask', array('md' => 3, 'sm' => 2, 'xs' => 3));
        if($big_img_status){
            $style.= $addon_id.' .article-list-custom-wrap::after{
                content: "";
                display: block;
                clear: both;
            }';
            $style.= $addon_id.' .ellipsis-text{
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }';
            $style.= $addon_id.' .wrap-big{
                display: flex;
                flex-wrap: wrap;';
                $style.='margin: '.$item_margin_big['md'].';';
                if($big_img_position === 'left'){
                    $style.='float: left;
                    width: '.$item_width_big['md'].'%;';
                }else{
                    $style.='width: 100%;';
                }
            $style.='}';
            if($big_img_position === 'left'){
                $gapArr = explode(' ', $item_margin_big['md']);
                $bottom = isset($gapArr[2]) ? $gapArr[2] : 0;
                $right = isset($gapArr[1]) ? $gapArr[1] : 0;
                if($item_width_big['md'] === '100'){
                    $style.= $addon_id.' .swiper-container{
                        width: 100%;
                    }';
                }else{
                    $style.= $addon_id.' .swiper-container{
                        float: right;
                        width: calc(100% - '.$item_width_big['md'].'% - '.$right.');
                    }';
                }
            }else{
                $style.= $addon_id.' .swiper-container{
                    width: 100%;
                }';
            }
            $style.= $addon_id.' .article-list-custom-item.big-img:nth-child(1n){
                margin-right: ' . $item_column_gap['md'] . 'px;
            }';
            $style.= $addon_id.' .article-list-custom-item.big-img{
                border-color: '.$item_border_color_big.';
                border-width: '.$item_border_width_big['md'].';
                width: calc(100% / ' . $item_number_big['md'] . ' - ' . $item_column_gap['md'] . 'px + ' . $item_column_gap['md'] . 'px / ' . $item_number_big['md'] . ');';
                $style.= 'flex-direction: '.$item_layout_big.';';
                $style.='position: relative;';
                $style.='padding: '.$item_padding_big['md'].';';
                if($big_img_position === 'left'){
                    $style.='margin: '.$item_margin_big['md'].';';
                }
                $style .= $this -> getBoxShadow('item_box_shadow_big', 'item_box_shadow_color_big', 'item_box_shadow_size_big', 'item_box_shadow_spread_big', 'item_box_shadow_horizontal_big', 'item_box_shadow_vertical_big');
            $style.='}';
            $style.= $addon_id.' .article-list-custom-item.big-img:hover{
                '.$this -> getBoxShadow('hover_item_box_shadow_big_status', 'hover_item_box_shadow_color_big', 'hover_item_box_shadow_blur_big', 'hover_item_box_shadow_spread_big', 'hover_item_box_shadow_horizontal_big', 'hover_item_box_shadow_vertical_big').'
            }';
            $style.= $addon_id.' .article-list-custom-item.big-img:nth-of-type('.$item_number_big['md'].'n){
                margin-right: 0;
            }';
            if($item_layout_big === 'column'){
                $style.= $addon_id . ' .article-list-custom-item.big-img .item-image-box {
                    width: 100%;
                }';
            }
            $style.= $addon_id.' .article-list-custom-item.big-img + .article-list-custom-item.big-img{';
                if($item_number_big['md'] > 1){
                    $style.='margin-left: 0;';
                }else{
                    $style.='margin-left: 0;';
                    $style.='margin-top: ' . $item_column_gap['md'] . 'px;';
                }
            $style.='}';
            $style .= $addon_id.' .article-list-custom-item.big-img .item-content-box {
                padding: '.$item_content_padding_big['md'].';
                background: '.$item_content_bg_color_big.';
            }';
            if($big_img_title_cover){
                $style .= $addon_id.' .article-list-custom-item.big-img .item-content-box {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                }';
            }
            $style .= $addon_id. ' .article-list-custom-item.big-img .item-title{
                color: '.$item_title_color_big.';
                font-size: ' . $item_title_font_size_big['md'] . 'px;
                line-height: 1.4;
                margin: ' . $item_title_margin_big['md'] . ';
                -webkit-line-clamp: '.$item_title_line_big['md'].';
                height: ' . $item_title_font_size_big['md'] * $item_title_line_big['md'] * 1.4 . 'px;
                font-weight: '.$item_title_fontweight_big.';
            }';
            $style .= $addon_id. ' .article-list-custom-item.big-img .item-intro{
                color: '.$item_intro_color_big.';
                font-size: ' . $item_intro_font_size_big['md'] . 'px;
                line-height: 1.6;
                -webkit-line-clamp: '.$item_intro_line_big['md'].';
                height: ' . $item_intro_font_size_big['md'] * $item_intro_line_big['md'] * 1.6 . 'px;
            }';
            $style .= $addon_id. ' .article-list-custom-item.big-img .item-meta .item-date{
                margin: ' . $item_date_margin_big['md'] . ';
                padding: ' . $item_date_padding_big['md'] . ';
                background: '.$item_date_background_color_big.';
                align-items: '.$item_date_specific_align_big.';
                color: '.$item_date_color_big.';
                font-size: ' . $item_date_font_size_big['md'] . 'px;
            }';
            // 遮罩
            $style.= $addon_id.' .article-list-custom-item.big-img .card-hover{
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;';
                 if($item_bg_color_big_mask_settings == 'gradient' && $item_bg_gradient_big_mask){
                    $style .= 'background-image: ' . ($item_bg_gradient_big_mask->type ?: "linear") .'-gradient(';
                    if($item_bg_gradient_big_mask->type && $item_bg_gradient_big_mask->type == "radial") {
                        $style .= 'at ' . ($item_bg_gradient_big_mask->radialPos ?: "center center");
                    } else {
                        $style .= ($item_bg_gradient_big_mask -> deg ?: 0) . 'deg';
                    }
                    $style .= ',
                                        ' . $item_bg_gradient_big_mask -> color . ' ' . ($item_bg_gradient_big_mask -> pos ?: 0) .'%,';
                    $style .= $item_bg_gradient_big_mask -> color2 . ' ' . ($item_bg_gradient_big_mask -> pos2 ?: 100) .'%);';
                }elseif($item_bg_color_big_mask_settings === 'color' && $item_bg_color_big_mask){
                    $style.='background: '.$item_bg_color_big_mask.';';
                }elseif($item_bg_color_big_mask_settings === 'image' && $item_bg_img_big_mask){
                    $style.='background: url('.$item_bg_image_big_mask.') no-repat center/cover;';
                }
                $style.='color: white;
                padding: 30px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                opacity: 0;
                transform: translateX(100%) translateZ(0);
                transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                will-change: transform, opacity;
            }
            '. $addon_id.' .article-list-custom-item.big-img:hover .card-hover {
                opacity: 1;
                transform: translateX(0);
            }
            '. $addon_id.' .article-list-custom-item.big-img:hover .card-hover .line {
                width: 100%;
                height: 1px;
                background: '.$item_title_underline_color_big_mask.';
                margin: '.$item_title_underline_margin_big_mask['md'].';
            }
            '. $addon_id.' .article-list-custom-item.big-img .card-hover .item-meta .item-date {
                font-size: '.$item_date_font_size_big_mask['md'].'px;
                color: '.$item_date_color_big_mask.';
                margin: '.$item_date_margin_big_mask['md'].';
                padding: '.$item_date_padding_big_mask['md'].';
            }
            '. $addon_id.' .article-list-custom-item.big-img .hover-title {
                font-size: '.$item_title_font_size_big_mask['md'].'px;
                color: '.$item_title_color_big_mask.';
                line-height: 1.4;
                font-weight: '.$item_title_fontweight_big_mask.';
                margin: '.$item_title_margin_big_mask['md'].';
                -webkit-line-clamp: '.$item_title_line_big_mask['md'].';
                height: ' . $item_title_font_size_big_mask['md'] * $item_title_line_big_mask['md'] * 1.4 . 'px;
            }
            '. $addon_id.' .article-list-custom-item.big-img .hover-content {
                font-size: '.$item_intro_font_size_big_mask['md'].'px;
                color: '.$item_intro_color_big_mask.';
                line-height: 1.6;
                flex: 1;
                -webkit-line-clamp: '.$item_intro_line_big_mask['md'].';
                height: ' . $item_intro_font_size_big_mask['md'] * $item_intro_line_big_mask['md'] * 1.6 . 'px;
            }
            '. $addon_id.' .article-list-custom-item.big-img .hover-arrow {
                width: 40px;
                height: 40px;
                border: 1px solid rgba(255, 255, 255, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                transition: border-color 0.2s ease, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                transform: translateZ(0);
                will-change: transform;
                margin-top: 30px;
            }
            '. $addon_id.' .article-list-custom-item.big-img .hover-arrow::after {
                content: \'→\';
                font-size: 18px;
                color: white;
                transition: color 0.2s ease;
            }
            '. $addon_id.' .article-list-custom-item.big-img:hover .hover-arrow {
                border-color: white;
                transform: scale(1.05) translateZ(0);
            }';
            $style.= '@media (min-width: 768px) and (max-width: 991px) {
                '.$addon_id.' .wrap-big{
                    width: '.$item_width_big['sm'].'%;
                    margin: '.$item_margin_big['sm'].';
                }
                '. $addon_id.' .article-list-custom-item.big-img:nth-child(1n){
                    margin-right: ' . $item_column_gap['sm'] . 'px;
                }';
                if($big_img_position === 'left'){
                    $gapArr = explode(' ', $item_margin_big['sm']);
                    $bottom = isset($gapArr[2]) ? $gapArr[2] : 0;
                    $right = isset($gapArr[1]) ? $gapArr[1] : 0;
                    if($item_width_big['sm'] === '100'){
                        $style.= $addon_id.' .swiper-container{
                            width: 100%;
                        }';
                    }else{
                        $style.= $addon_id.' .swiper-container{
                            float: right;
                            width: calc(100% - '.$item_width_big['sm'].'% - '.$right.');
                        }';
                    }
                }else{
                    $style.= $addon_id.' .swiper-container{
                        width: 100%;
                    }';
                }
                $style.= $addon_id.' .article-list-custom-item.big-img{
                    border-width: '.$item_border_width_big['sm'].';
                    width: calc(100% / ' . $item_number_big['sm'] . ' - ' . $item_column_gap['sm'] . 'px + ' . $item_column_gap['sm'] . 'px / ' . $item_number_big['sm'] . ');';
                    $style.= 'flex-direction: '.$item_layout_big.';';
                    $style.='position: relative;';
                    $style.='padding: '.$item_padding_big['sm'].';';
                    if($big_img_position === 'left'){
                        $style.='margin: '.$item_margin_big['sm'].';';
                    }
                $style.='}';
                $style.= $addon_id.' .article-list-custom-item.big-img:nth-of-type('.$item_number_big['sm'].'n){
                    margin-right: 0;
                }';
                $style .= $addon_id.' .article-list-custom-item.big-img .item-content-box {
                    padding: '.$item_content_padding_big['sm'].';
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-title{
                    font-size: ' . $item_title_font_size_big['sm'] . 'px;
                    margin: ' . $item_title_margin_big['sm'] . ';
                    -webkit-line-clamp: '.$item_title_line_big['sm'].';
                    height: ' . $item_title_font_size_big['sm'] * $item_title_line_big['sm'] * 1.4 . 'px;
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-meta .item-date{
                    margin: ' . $item_date_margin_big['sm'] . ';
                    padding: ' . $item_date_padding_big['sm'] . ';
                    font-size: ' . $item_date_font_size_big['sm'] . 'px;
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-intro{
                    font-size: ' . $item_intro_font_size_big['sm'] . 'px;
                    -webkit-line-clamp: '.$item_intro_line_big['sm'].';
                    height: ' . $item_intro_font_size_big['sm'] * $item_intro_line_big['sm'] * 1.6 . 'px;
                }';
                $style.=$addon_id.' .article-list-custom-item.big-img .card-hover .item-meta .item-date {
                    font-size: '.$item_date_font_size_big_mask['sm'].'px;
                    margin: '.$item_date_margin_big_mask['sm'].';
                    padding: '.$item_date_padding_big_mask['sm'].';
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img .hover-title {
                    font-size: '.$item_title_font_size_big_mask['sm'].'px;
                    margin: '.$item_title_margin_big_mask['sm'].';
                    -webkit-line-clamp: '.$item_title_line_big_mask['sm'].';
                    height: ' . $item_title_font_size_big_mask['sm'] * $item_title_line_big_mask['sm'] * 1.4 . 'px;
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img:hover .card-hover .line {
                    margin: '.$item_title_underline_margin_big_mask['sm'].';
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img .hover-content {
                    font-size: '.$item_intro_font_size_big_mask['sm'].'px;
                    -webkit-line-clamp: '.$item_intro_line_big_mask['sm'].';
                    height: ' . $item_intro_font_size_big_mask['sm'] * $item_intro_line_big_mask['sm'] * 1.6 . 'px;
                }';
            $style.='}
            @media (max-width: 767px) {
                '.$addon_id.' .wrap-big{
                    width: '.$item_width_big['xs'].'%;
                    margin: '.$item_margin_big['xs'].';
                }
                '. $addon_id.' .article-list-custom-item.big-img:nth-child(1n){
                    margin-right: ' . $item_column_gap['xs'] . 'px;
                }';
                    if($big_img_position === 'left'){
                        $gapArr = explode(' ', $item_margin_big['xs']);
                        $bottom = isset($gapArr[2]) ? $gapArr[2] : 0;
                        $right = isset($gapArr[1]) ? $gapArr[1] : 0;
                    if($item_width_big['sm'] === '100'){
                        $style.= $addon_id.' .swiper-container{
                            width: 100%;
                        }';
                    }else{
                        $style.= $addon_id.' .swiper-container{
                            float: right;
                            width: calc(100% - '.$item_width_big['xs'].'% - '.$right.');
                        }';
                    }
                }else{
                    $style.= $addon_id.' .swiper-container{
                        width: 100%;
                    }';
                }
                $style.= $addon_id.' .article-list-custom-item.big-img{
                    border-width: '.$item_border_width_big['xs'].';
                    width: calc(100% / ' . $item_number_big['xs'] . ' - ' . $item_column_gap['sm'] . 'px + ' . $item_column_gap['xs'] . 'px / ' . $item_number_big['xs'] . ');';
                    $style.= 'flex-direction: '.$item_layout_big.';';
                    $style.='position: relative;';
                    $style.='padding: '.$item_padding_big['xs'].';';
                    if($big_img_position === 'left'){
                        $style.='margin: '.$item_margin_big['xs'].';';
                    }
                $style.='}';
                $style.= $addon_id.' .article-list-custom-item.big-img:nth-of-type('.$item_number_big['xs'].'n){
                    margin-right: 0;
                }';
                $style .= $addon_id.' .article-list-custom-item.big-img .item-content-box {
                    padding: '.$item_content_padding_big['xs'].';
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-title{
                    font-size: ' . $item_title_font_size_big['xs'] . 'px;
                    margin: ' . $item_title_margin_big['xs'] . ';
                    -webkit-line-clamp: '.$item_title_line_big['xs'].';
                    height: ' . $item_title_font_size_big['xs'] * $item_title_line_big['xs'] * 1.4 . 'px;
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-meta .item-date{
                    margin: ' . $item_date_margin_big['xs'] . ';
                    padding: ' . $item_date_padding_big['xs'] . ';
                    font-size: ' . $item_date_font_size_big['xs'] . 'px;
                }';
                $style .= $addon_id. ' .article-list-custom-item.big-img .item-intro{
                    font-size: ' . $item_intro_font_size_big['xs'] . 'px;
                    -webkit-line-clamp: '.$item_intro_line_big['xs'].';
                    height: ' . $item_intro_font_size_big['xs'] * $item_intro_line_big['xs'] * 1.6 . 'px;
                }';
                $style.=$addon_id.' .article-list-custom-item.big-img .card-hover .item-meta .item-date {
                    font-size: '.$item_date_font_size_big_mask['xs'].'px;
                    margin: '.$item_date_margin_big_mask['xs'].';
                    padding: '.$item_date_padding_big_mask['xs'].';
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img .hover-title {
                    font-size: '.$item_title_font_size_big_mask['xs'].'px;
                    margin: '.$item_title_margin_big_mask['xs'].';
                    -webkit-line-clamp: '.$item_title_line_big_mask['xs'].';
                    height: ' . $item_title_font_size_big_mask['xs'] * $item_title_line_big_mask['xs'] * 1.4 . 'px;
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img:hover .card-hover .line {
                    margin: '.$item_title_underline_margin_big_mask['xs'].';
                }';
                $style.= $addon_id.' .article-list-custom-item.big-img .hover-content {
                    font-size: '.$item_intro_font_size_big_mask['xs'].'px;
                    -webkit-line-clamp: '.$item_intro_line_big_mask['xs'].';
                    height: ' . $item_intro_font_size_big_mask['xs'] * $item_intro_line_big_mask['xs'] * 1.6 . 'px;
                }';
            $style.='}';
        }

        $style .= '</style>';
        return $style;
    }

    // 日期格式处理
    public function formatDate($date, $formatType = 'item_date_format')
    {

        $item_date_format = $this->safeGetProp($formatType, 'd F Y');

        $date = strtotime($date);

        $day = date('d', $date);
        $month = date('n', $date);
        $year = date('Y', $date);

        $english_months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];

        $month = date('n', $date);
        $month_name = $english_months[$month - 1];

        $result = date($item_date_format, $date);
        if ($item_date_format == 'd F Y') {
            $result = $day . ' ' . $month_name . ' ' . $year;
        }

        return $result;
    }

    public function getItems($cate_id = 0)
    {
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        $settings = $this->addon->settings;

        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $zcatid = $_GET['catid'] ?? 0;

        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        if($zcatid)
        {
           $catid = $zcatid;
        }

        if ($cate_id) {
            $catid = $cate_id;
            $zcpcatid = $_GET['zcpcatid'] ?? 0;
            $show_tabs = (isset($settings->show_tabs) && $settings->show_tabs) ? $settings->show_tabs : 0;
            if ($show_tabs) {
                $url_addon = $_GET['addon_id'] ?? '';

                if($url_addon && $this->addon->id != $url_addon){
                    $page = 1;
                }else {
                    if($cate_id != $zcpcatid){
                        $page = 1;
                    }
                }
            }
        }
        require_once $article_helper;
        $items = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        $items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $catid, $include_subcat, $company_id, $site_id, $post_type, $tagids);

        // 返回items、items_count
        return [
            'items' => $items,
            'items_count' => $items_count,
            'page' => $page,
            'limit' => $limit,
            'catid' => $catid,
        ];
    }

    // 获取文章分类
    public function getArticleCates()
    {
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $settings = $this->addon->settings;

        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $layout_id = $_GET['layout_id'] ?? 0;
        $show_tabs = (isset($settings->show_tabs) && $settings->show_tabs) ? $settings->show_tabs : 0;
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : (array)[];
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $cate_ids = array();
        $selected_cate_id = $_GET['zcpcatid'] ?? 0;

        if($catid === 0 || in_array('', $catid) || empty($catid)){
            $cate_ids = JwpagefactoryHelperCategories::getArticlefl2($company_id, $site_id);
        }else{
            foreach ($catid as $keyd => $valued) {
                $cate_ids[] = JwpagefactoryHelperCategories::GetCategorieidsssss('com_content', $site_id, '', $layout_id, $company_id, '', $valued);
            }
        }
        $url_addon = $_GET['addon_id'] ?? '';
        if($url_addon && $this->addon->id != $url_addon){
            $selected_cate_id = $cate_ids[0]['tag_id'] ?? 0;
        }else{
            $foundKey = array_search($selected_cate_id, array_column($cate_ids, 'tag_id'));

            if ($foundKey !== false) {
                $selected_cate_id = $cate_ids[$foundKey]['tag_id'];
            } else {
                $selected_cate_id = $cate_ids[0]['tag_id'] ?? 0;
            }
        }

        foreach ($cate_ids as $keyd => $valued) {
            $cates[] = [
                'cates' => $valued,
                'lists' => $this -> getItems($valued['tag_id']),
                'nav_id' => $selected_cate_id,
            ];
            $lists[] = $this -> getItems($valued['tag_id']);
        }
        if ($show_tabs) {
            return $cates;
        } else {
            return $lists[0];
        }
    }

    // 获取产品数据
    public function getProducts($cate_id = 0){
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
        require_once $article_helper;

        $settings = $this->addon->settings;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $zcatid = $_GET['catid'] ?? 0;

        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : '';
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        // 指定详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        //指定获得产品列表的pid
        $detail = $_GET['details'] ?? 0;
        $cpcatid = $_GET['zcpcatid'] ?? 0;
        $goods_catid = $cate_id ?? $_GET['goods_catid'] ?? 0;
        if ($goods_catid == 0) {
            $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;
        }

        $url_addon = $_GET['addon_id'] ?? '';
        if($url_addon && $this->addon->id != $url_addon){
            $page = 1;
        }else{
            if ($cpcatid != $goods_catid) {
                $page = 1;
            }
        }

        if (isset($detail) && $detail != 0) {
            $goods_catid = $detail;
            $page = $_GET['page'] ?? 1;
            $items = JwpagefactoryHelperGoods::getGoodsListThree($limit, $ordering, $goods_catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $detail);
            $items_count = JwpagefactoryHelperGoods::getGoodsCount($ordering, $goods_catid, 1, $company_id, $site_id);
        }else{
            $items = JwpagefactoryHelperGoods::getGoodsListProductData($limit, $ordering, $goods_catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
            $items_count = JwpagefactoryHelperGoods::getGoodsListProductCount($limit, $ordering, $goods_catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
        }
        // 返回items、items_count
        return [
            'items' => $items,
            'items_count' => $items_count,
            'page' => $page,
            'limit' => $limit,
            'catid' => array($goods_catid)
        ];
    }
    // 获取导航数据
    public function getNavs()
    {
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $settings = $this->addon->settings;

        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;
        $nav_pid = $_GET['nav_pid'] ?? 0;
        $nav_id = $_GET['zcpcatid'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $show_tabs = (isset($settings->show_tabs) && $settings->show_tabs) ? $settings->show_tabs : 0;

        $from = $_GET['from'] ?? '';
        $url_addon = $_GET['addon_id'] ?? '';

        $cates = array();
        $lists =array();
        // 通过$nav_pid获取对应的分类
        if($nav_pid){
            $nav_info = JwPageFactoryBase::getNavigationInfo($nav_pid);
            if($from == 'goods'){
                $cate_ids = isset($nav_info->goods_type) ? explode(',', $nav_info->goods_type) : array();

                if($url_addon && $addon_id === $url_addon){
                    $selected_cate_id = $nav_id;
                }else{
                    if(in_array($nav_id, $cate_ids)){
                        $selected_cate_id = $nav_id;
                    }else{
                        $selected_cate_id = $cate_ids[0] ?? 0;
                    }
                }

                foreach ($cate_ids as $keyd => $valued) {
                    $cates[] = [
                        'cates' => JwpagefactoryHelperCategories::GetCategorieidsssss('com_goods', $site_id, '', $layout_id, $company_id, '', $valued),
                        'lists' => $this -> getProducts($valued),
                        'nav_id' => $selected_cate_id,
                    ];
                    $lists[] = $this -> getProducts($valued);
                }
            }else{
                $cate_ids = isset($nav_info->article_type) ? explode(',', $nav_info->article_type) : array();
            }
        }else{
            if($from == 'goods'){
                $this -> getProducts();
            }else{
                $this -> getItems();
            }
        }

        if($show_tabs){
            return $cates;
        }else{
            return $lists[0];
        }
    }

    // 获取产品分类
    public function getProductCates()
    {
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $settings = $this->addon->settings;

        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : array();
        $layout_id = $_GET['layout_id'] ?? 0;
        $show_tabs = (isset($settings->show_tabs) && $settings->show_tabs) ? $settings->show_tabs : 0;
        $nav_id = $_GET['zcpcatid'] ?? 0;
        $addon_id = $this->addon->id;
        $url_addon = $_GET['addon_id'] ?? '';
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $detail = $_GET['details'] ?? 0;
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';

        $cates = array();
        $lists = array();

        if($show_tabs){
            if($goods_catid){
                $cate_ids = $goods_catid;

                if($url_addon && $addon_id === $url_addon){
                    $selected_cate_id = $nav_id;
                }else{
                    if(in_array($nav_id, $cate_ids)){
                        $selected_cate_id = $nav_id;
                    }else{
                        $selected_cate_id = $cate_ids[0] ?? 0;
                    }
                }

                foreach ($cate_ids as $keyd => $valued) {
                    $cates[] = [
                        'cates' => JwpagefactoryHelperCategories::GetCategorieidsssss('com_goods', $site_id, '', $layout_id, $company_id, '', $valued),
                        'lists' => $this -> getProducts($valued),
                        'nav_id' => $selected_cate_id,
                    ];
                    $lists[] = $this -> getProducts($valued);
                }
            }else{
                $type_parent = 'type1';
                if($include_subcat){
                    $type_parent = 'all';
                }
                $type_start = 1;
                $type_num = 0;
                $cate_all = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, $type_parent, $type_start, $type_num, $ordering);

                $foundKey = array_search($selected_cate_id, array_column($cate_all, 'tag_id'));

                if ($foundKey !== false) {
                    $selected_cate_id = $cate_ids[$foundKey]['tag_id'];
                } else {
                    $selected_cate_id = $cate_ids[0]['tag_id'] ?? 0;
                }

                foreach ($cate_all as $keyd => $valued) {
                    $cates[] = [
                        'cates' => $valued,
                        'lists' => $this -> getProducts($valued['tag_id']),
                        'nav_id' => $selected_cate_id,
                    ];
                    $lists[] = $this -> getProducts($valued['tag_id']);
                }
            }
            return $cates;
        }else{
            return $lists[0];
        }
    }

    public function getPageHtml($pages = ['items_count' => 0, 'page' => 1, 'limit' => 10, 'catid' => []])
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $addonId = $this->addon->id;

        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : 0;
        $page_style_selector = (isset($settings->page_style_selector)) ? $settings->page_style_selector : 'page01';

        $show_tabs = (isset($settings->show_tabs) && $settings->show_tabs) ? $settings->show_tabs : 0;

        if($show_tabs) {
            $items_all = $pages;
        } else {
            // 数据来源
            $data_source = $this->safeGetProp('data_source', 'articles');
            if($data_source == 'products'){
                $items_all = $this->getProducts();
            } elseif($data_source == 'nav'){
                $items_all = $this->getNavs();
            } else {
                $items_all = $this->getItems();
            }
        }

        $items_count = $items_all['items_count'];
        $page = $items_all['page'];
        $catid = $items_all['catid'];
        $limit = $items_all['limit'];

        $page1_fontcolor = (isset($settings->page1_fontcolor)) ? $settings->page1_fontcolor : '#ffffff';
        $page1_bordercolor = (isset($settings->page1_bordercolor)) ? $settings->page1_bordercolor : '#2a68a7';
        $page1_bgcolor = (isset($settings->page1_bgcolor)) ? $settings->page1_bgcolor : '#ffffff';
        $page1_cur_fontcolor = (isset($settings->page1_cur_fontcolor)) ? $settings->page1_cur_fontcolor : '#ffffff';
        $page1_cur_bordercolor = (isset($settings->page1_cur_bordercolor)) ? $settings->page1_cur_bordercolor : '#2a68a7';
        $page1_cur_bgcolor = (isset($settings->page1_cur_bgcolor)) ? $settings->page1_cur_bgcolor : '#ffffff';

        $zcpcatid = $_GET['zcpcatid'] ?? 0;
        $nav_pid = $_GET['nav_pid'] ?? 0;
        if($nav_pid){
            $zcpcatid = $nav_pid;
        }

        $output = '';

        if ($show_page) {
            $all_page = 1;
            if ($limit) {
                $all_page = ceil($items_count / $limit);
            }
            if ($page_style_selector == 'page01') {
                $output .= '
                <style>
                    ' . $addon_id . ' .page_plug {
                        width: 90%;
                        margin: 5px auto;
                        text-align: center;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: center;
                        align-items: center;
                    }
                    ' . $addon_id . ' .page_plug a{
                        padding: 3px 8px;
                        border: 1px solid ' . $page1_bordercolor . ';
                        margin-right: 5px;
                        margin-bottom: 5px;
                        text-decoration: none;
                        color: ' . $page1_fontcolor . ';
                        background:' . $page1_bgcolor . ';
                    }
                    ' . $addon_id . ' .page_plug .curPage {
                        border: 1px solid ' . $page1_cur_bordercolor . ';
                        color: ' . $page1_cur_fontcolor . ';
                        background:' . $page1_cur_bgcolor . ';
                    }
                </style>
                ';
                $output .= '<div class="page_plug">';
                // 判断是不是第一页
                if($page && $page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '<a class="page_num" href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'"> << </a>';
                }
                for ($i = 1; $i <= $all_page; $i++) {
                    if ($page == $i) {
                        $output .= '<a class="curPage">' . $i . '</a>';
                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                        $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'">' . $i . '</a>';
                    }
                }
                // 判断是不是最后一页
                if($page < $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'"> >> </a>';
                }

                $output .= '</div>';
                // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
            }
            if ($page_style_selector == 'page02') {

                $output .= "
                <style>
                    {$addon_id} div.zxf_pagediv{
                        margin: 0 auto;
                    }
                    {$addon_id} div.zxf_pagediv a.current {
                        background: {$page1_cur_bgcolor};
                        color: {$page1_cur_fontcolor};
                        border: 1px solid {$page1_cur_bordercolor};
                        width: 30px;
                        height: 30px;
                        line-height: 30px;
                    }
                    {$addon_id} .zxfPagenum {
                        color:{$page1_fontcolor};
                        border: 1px solid {$page1_bordercolor};
                        width: 30px;
                        height: 30px;
                        line-height: 30px;
                        background:{$page1_bgcolor};
                    }
                    {$addon_id} .nextbtn, .prebtn, span.disabled{
                        color:{$page1_fontcolor};
                    }
                    {$addon_id} div.zxf_pagediv span{
                        color:{$page1_fontcolor};
                    }
                </style>";

                /* 编辑器省略号分页 */
                $output .= '<div class="zxf_pagediv " id="false_page" style="width:100%;display:flex;align-items: center;justify-content: center">';
                if($page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '    <a href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" class="prebtn">上一页</a>';
                }

                $output .= ' <div class="page">';
                for ($i = 1; $i <= $all_page; $i++) {
                    if ($page == $i) {
                        $output .= '<a class="current zxfPagenum">' . $i . '</a>';

                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                        $output .= '<a class="zxfPagenum" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'">' . $i . '</a>';

                    }
                }
                $output .= '</div>';
                // 判断是不是最后一页
                if($page != $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '    <a href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" class="nextbtn">下一页</a>';
                }


                $output .= '</div>';
                $output .= '<script>

                    </script>';
            }
            if ($page_style_selector == 'page03') {

                /* 编辑器省略号分页 */
                $output .= '<style>
                '.$addon_id.' .pagination {
                    margin-bottom: 60px;
                }
                '.$addon_id.' .pagination {
                    text-align: center;
                    display: block;
                    width:100%;
                }
                '.$addon_id.' ul, '.$addon_id.' ol {
                    list-style: none;
                }
                '.$addon_id.' .pagination li {
                    width: 50px;
                    height: 50px;
                    background: #F6F6F6;
                    border-radius: 3px;
                    font-size: 20px;
                    display: inline-block;
                    line-height: 50px;
                    margin: 0 2px;
                    text-align: center;
                    color: #666666;
                    vertical-align: middle;
                }
                '.$addon_id.' .pagination li.active {
                    color: #333;
                    background: none;
                    border: 1px solid #666;
                }
                '.$addon_id.' a:link, '.$addon_id.' a:active, '.$addon_id.' a:visited, '.$addon_id.' a:hover {
                    background: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    -webkit-tap-highlight-color: transparent;
                }
                '.$addon_id.' a, '.$addon_id.' li, '.$addon_id.' ul {
                    margin: 0;
                    padding: 0;
                    color: inherit;
                    font-size: inherit;
                    font-weight: inherit;
                }
                '.$addon_id.' a {
                    text-decoration: none;
                    color: inherit;
                    font-family: "Microsoft YaHei", Tahoma, Arial, sans-serif;
                }
                '.$addon_id.' .pagination li:hover {
                    color: #fff;
                    background: #CC1E04;
                }
                '.$addon_id.' .pagination > .active > span{
                    background:none !important;
                }
                </style>';
                $output .= '<ul class="pagination">';
                if($page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '<li><a href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'">«</a></li>';
                }
                else
                {
                    $output .='<li class="disabled"><span>«</span></li>';
                }

                for ($i = 1; $i <= $all_page; $i++) {
                    if ($page == $i) {
                        $output .= '<li class="active"><span>' . $i . '</span></li>';

                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                        $output .= '<li><a href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'">' . $i . '</a></li>';

                    }
                }
                // 判断是不是最后一页
                if($page != $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    $output .= '<li><a href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" >»</a></li>';
                }
                else
                {
                    $output .='<li class="disabled"><span>»</span></li>';
                }

                $output .= '</ul>';
            }
            if ($page_style_selector == 'page04') {
                $output .= '<style>
                    '.$addon_id.' .fant3{color:#ffa;font-size:14px;line-height:30px;text-align: center;width:100%;display:block;}
                    '.$addon_id.' .fant3 li{display: inline-block;margin:10px 3px;}
                    '.$addon_id.' .fant3 li a{color:'.$page1_fontcolor.';text-decoration:none;display:inline-block;}
                    '.$addon_id.' .current{width:30px!important;height:30px!important;line-height:30px!important;background:'.$page1_cur_bgcolor.'!important;color:'.$page1_cur_fontcolor.'!important;}
                    '.$addon_id.' .zxfPagenum{margin:0px!important;}
                    </style>
                ';

                $output .= '
                    <ul class="fant3">';

                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                        $output .= '    <li><a href="' . $url . '&page=1&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" >首页</a></li>';

                        if($page != 1) {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                            $output .= '    <li><a href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" >上一页</a></li>';
                        }
                        for ($i = 1; $i <= $all_page; $i++) {
                            if ($page == $i) {
                                $output .= '<li><a class="current zxfPagenum">' . $i . '</a></li>';

                            } else {
                                $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                                $output .= '<li><a class="zxfPagenum" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'">' . $i . '</a></li>';

                            }
                        }
                        if($page != $all_page) {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                            $output .= '    <li><a href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" >下一页</a></li>';
                        }

                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                        $output .= '    <li><a href="' . $url . '&page=' . $all_page . '&zcpcatid=' . $catid[0] . '&addon_id='.$addonId.'" >尾页</a></li>';

                        $output .= '
                        <li><a href="javascript:;">'.$page.'/'.$all_page.'页</a></li>
                    </ul>
                ';
            }
        }

        return $output;
    }

    // 切换html 翻页按钮
    public function pagination_html()
    {
        $swiper_status = $this->safeGetProp('swiper_status', 0);
        if($swiper_status != 1) {
            return '';
        }

        // 显示切换按钮
        $swiper_navigation = $this->safeGetProp('swiper_navigation', 0);

        $output = '';
        if ($swiper_navigation == 1) {
            $output .= '
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            ';
        }

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 切换按钮宽度
        $swiper_navigation_width = $this->getResponsiveValues('swiper_navigation_width', ['md' => 24, 'sm' => '', 'xs' => '']);
        // 切换按钮高度
        $swiper_navigation_height = $this->getResponsiveValues('swiper_navigation_height', ['md' => 24, 'sm' => '', 'xs' => '']);
        // 切换按钮上一页图片
        $swiper_navigation_prev_img = $this->safeGetProp('swiper_navigation_prev_img', 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png');
        // 切换按钮下一页图片
        $swiper_navigation_next_img = $this->safeGetProp('swiper_navigation_next_img', 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png');
        // 移入切换按钮上一页图片
        $swiper_navigation_prev_img_hover = $this->safeGetProp('swiper_navigation_prev_img_hover', '');
        // 移入切换按钮下一页图片
        $swiper_navigation_next_img_hover = $this->safeGetProp('swiper_navigation_next_img_hover', '');
        // 切换按钮顶部距离
        $swiper_navigation_top = $this->getResponsiveValues('swiper_navigation_top', ['md' => 30, 'sm' => '', 'xs' => '']);
        // 切换按钮两侧距离
        $swiper_navigation_margin = $this->getResponsiveValues('swiper_navigation_margin', ['md' => 10, 'sm' => '', 'xs' => '']);

        $output .= '
        <style>
            '.$addon_id.' {
                --swiper-navigation-width: '.$swiper_navigation_width['md'].'px;
                --swiper-navigation-height: '.$swiper_navigation_height['md'].'px;
                --swiper-navigation-top: '.$swiper_navigation_top['md'].'%;
                --swiper-navigation-margin: '.$swiper_navigation_margin['md'].'px;
            }
           @media (min-width: 768px) and (max-width: 991px) {
                '.$addon_id.' {
                    --swiper-navigation-width: '.$swiper_navigation_width['sm'].'px;
                    --swiper-navigation-height: '.$swiper_navigation_height['sm'].'px;
                    --swiper-navigation-top: '.$swiper_navigation_top['sm'].'%;
                    --swiper-navigation-margin: '.$swiper_navigation_margin['sm'].'px;
                }
            }
             @media screen and (max-width: 767px) {
                '.$addon_id.' {
                    --swiper-navigation-width: '.$swiper_navigation_width['xs'].'px;
                    --swiper-navigation-height: '.$swiper_navigation_height['xs'].'px;
                    --swiper-navigation-top: '.$swiper_navigation_top['xs'].'%;
                    --swiper-navigation-margin: '.$swiper_navigation_margin['xs'].'px;
                }
            }
            '.$addon_id.' .swiper-button-prev,
            '.$addon_id.' .swiper-button-next {
                top: var(--swiper-navigation-top);
                width: var(--swiper-navigation-width);
                height: var(--swiper-navigation-height);
            }
            '.$addon_id.' .swiper-button-prev {
                left: var(--swiper-navigation-margin);
            }
            '.$addon_id.' .swiper-button-next {
                right: var(--swiper-navigation-margin);
            }
            '.$addon_id.' .swiper-button-prev:after,
            '.$addon_id.' .swiper-button-next:after {
                content: "";
                background: url() no-repeat center;
                background-size: contain;
                width: 100%;
                height: 100%;
            }
            '.$addon_id.' .swiper-button-prev:after {
                background-image: url('.$swiper_navigation_prev_img.');
            }
            '.$addon_id.' .swiper-button-next:after {
                background-image: url('.$swiper_navigation_next_img.');
            }
            '.$addon_id.' .swiper-button-prev:hover:after {
                ' . ($swiper_navigation_prev_img_hover ? 'background-image: url('.$swiper_navigation_prev_img_hover.');' : '') . '
            }
            '.$addon_id.' .swiper-button-next:hover:after {
                ' . ($swiper_navigation_next_img_hover ? 'background-image: url('.$swiper_navigation_next_img_hover.');' : '') . '
            }
        </style>
        ';

        return $output;
    }

    // 切换js
    public function swiper_js()
    {
        $swiper_status = $this->safeGetProp('swiper_status', 0);

        if($swiper_status != 1) {
            return '';
        }

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 列表项数量
        $item_number = $this->getResponsiveValues('item_number', ['md' => 3, 'sm' => 2, 'xs' => 1]);
        // 列表项水平间距
        $item_column_gap = $this->getResponsiveValues('item_column_gap', ['md' => 20, 'sm' => 20, 'xs' => 20]);
        // 自动切换
        $swiper_auto_play = $this->safeGetProp('swiper_auto_play', 0);
        // 自动切换间隔
        $swiper_auto_play_delay = $this->safeGetProp('swiper_auto_play_delay', 3000);
        // 开启循环切换
        $swiper_loop = $this->safeGetProp('swiper_loop', 0);

        $output = '
        <script>
            var swiper = new Swiper("'.$addon_id.' .swiper-container", {
                slidesPerView: '.$item_number['md'].',
                pagination: {
                    el: "'.$addon_id.' .swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: "'.$addon_id.' .swiper-button-next",
                    prevEl: "'.$addon_id.' .swiper-button-prev",
                },
                ';
                if ($swiper_auto_play == 1) {
                    $output .= '
                    autoplay: {
                        delay: '.$swiper_auto_play_delay.',
                        disableOnInteraction: false,
                    },';
                }
                if ($swiper_loop == 1) {
                    $output .= '
                    loop: true,';
                }
                $output .= '
                breakpoints: {
                    0: {
                        slidesPerView: '.$item_number['xs'].',
                        spaceBetween: '.$item_column_gap['xs'].',
                    },
                    767: {
                        slidesPerView: '.$item_number['sm'].',
                        spaceBetween: '.$item_column_gap['sm'].',
                    },
                    992: {
                        slidesPerView: '.$item_number['md'].',
                        spaceBetween: '.$item_column_gap['md'].',
                    },
                },
            });
        </script>
        ';
        return $output;
    }

    public function stylesheets()
    {
        $swiper_status = $this->safeGetProp('swiper_status', 0);
        if($swiper_status != 1) {
            return '';
        }
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $swiper_status = $this->safeGetProp('swiper_status', 0);
        if($swiper_status != 1) {
            return '';
        }
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
