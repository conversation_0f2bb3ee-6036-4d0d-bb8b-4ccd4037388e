<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'general',
        'addon_name' => 'zdy_navigation',
        'title' => "按钮导航",
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_LINK_LIST_DESC'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'mian-nav' => array(
                    'type' => 'separator',
                    'title' => JText::_('全局设置'),
                ),
                'daohang_cover_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('导航展开背景图'),
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210705/8a067bd046566dbdd4f234a9f8ad2f34.png',
                ),
                'daohang_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('PC导航图标'),
                    'desc' => JText::_('PC导航图标'),
                    'format' => 'image',
                    'std' => '/components/com_jwpagefactory/addons/zdy_navigation/assets/images/daohang.png',
                ),
                'daohang_wap_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('手机导航图标'),
                    'desc' => JText::_('手机导航图标'),
                    'format' => 'image',
                    'std' => '/components/com_jwpagefactory/addons/zdy_navigation/assets/images/daohang.png',
                ),
                'btn_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('特效背景图'),
                    'desc' => JText::_('特效背景图'),
                    'format' => 'image',
                    'std' => '/components/com_jwpagefactory/addons/zdy_navigation/assets/images/icon-5.png',
                ),
                'close_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('关闭图标'),
                    'desc' => JText::_('关闭图标'),
                    'format' => 'image',
                    'std' => '/components/com_jwpagefactory/addons/zdy_navigation/assets/images/close.png',
                ),
                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'max' => 100,
                    'std' => array('md' => 26, 'sm' => 16, 'xs' => 10),
                    'responsive' => true,
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体行高'),
                    'max' => 100,
                    'std' => array('md' => 60, 'sm' => 46, 'xs' => 20),
                    'responsive' => true,
                ),
                'title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#FFFFFF',
                ),
                'title_active_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体选中颜色'),
                    'std' => '#f58505',
                ),

                'mian-c' => array(
                    'type' => 'separator',
                    'title' => JText::_('内容添加'),
                ),
                'zdy_navigation_item' => array(
                    'title' => JText::_('导航一级标题'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题名称'),
                            'std' => '这里可以填写一个导航名称',
                        ),
                        'title_id' => array(
                            'type' => 'text',
                            'title' => JText::_('标题跳转ID（容器章节ID）'),
                            'std' => '这里可以填写跳转ID',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                        'istudiao' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否开启图标'),
                            'desc' => JText::_('是否开启图标'),
                            'values' => array(
                                1 => JText::_('是'),
                                0 => JText::_('否'),
                            ),
                            'std' => 0,
                        ),
                        'isstd' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否开启二级栏目'),
                            'desc' => JText::_('是否开启二级栏目'),
                            'values' => array(
                                1 => JText::_('是'),
                                0 => JText::_('否'),
                            ),
                            'std' => 0,
                        ),
                        'title_std' => array(
                            'type' => 'text',
                            'title' => JText::_('章节(父id)'),
                            'std' => '这里可以填写章节(父id)',
                        ),

                    ),
                ),

                'zdy_navigation_std_item' => array(
                    'title' => JText::_('导航二级标题'),
                    'attr' => array(
                        'std_title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题名称'),
                            'std' => '这里可以填写一个导航名称',
                        ),
                        'std_title_id' => array(
                            'type' => 'text',
                            'title' => JText::_('标题跳转ID（容器章节ID）'),
                            'std' => '这里可以填写跳转ID',
                        ),
                        'std_title_std' => array(
                            'type' => 'text',
                            'title' => JText::_('章节(父id)'),
                            'std' => '这里可以填写章节(ID',
                        ),

                    ),
                ), 'mian-s' => array(
                    'type' => 'separator',
                    'title' => JText::_('二级导航设置'),
                    'depends' => array(array(array('std_title', '!=', ''))),
                ),
                'title_std_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'max' => 100,
                    'std' => array('md' => 20, 'sm' => 15, 'xs' => 10),
                    'responsive' => true,
                    'depends' => array(array(array('std_title', '!=', ''))),
                ),
                'title_std_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体行高'),
                    'max' => 100,
                    'std' => array('md' => 48, 'sm' => 24, 'xs' => 10),
                    'responsive' => true,
                    'depends' => array(array(array('std_title', '!=', ''))),
                ),
                'title_std_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends' => array(array(array('std_title', '!=', ''))),
                ),
                'title_std_active_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体选中颜色'),
                    'std' => '#f58505',
                    'depends' => array(array(array('std_title', '!=', ''))),
                ),
            ),
        ),
    )
);
