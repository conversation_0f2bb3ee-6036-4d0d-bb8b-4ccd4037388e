<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonContent_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;
        $items   = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';

        $output = '<div class="swiper-box" id="swiper_' . $addonId . '">';

        $output .= '<div class="content-swiper-container">
                <div class="swiper-wrapper">';
        foreach ($items as $key => $item){
            $output .= '<div class="swiper-slide" data-id="' . $key . '">';
            if ($item->is_link === 1) {
                $target = $item->link_open_new_window === 1 ? '_blank' : '';
                $output .= '<a href="' . $item->image_carousel_img_link . '" target="' . $target . '">';
            }
            if ($item->image_carousel_img){
                if (strstr($item->image_carousel_img, 'http://')||strstr($item->image_carousel_img,'https://')){
                    $output .= '<img src=\'' . $item->image_carousel_img . '\' alt="">';
                }else{
                    $output .= '<img src=\'' . JURI::base(true). $item->image_carousel_img . '\' alt="">';
                }
            }


            $output .= '<div class="swiper-content ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.5s">
            
                                    <div style="position: relative;width: 100%;height: 100%">';

            $output.='<div class="content-img">';
                if ($item->carousel_item_img){
                    if (strstr($item->carousel_item_img, 'http://')||strstr($item->carousel_item_img,'https://')){
                        $output.='<img src=\'' . $item->carousel_item_img . '\' alt="">';
                    }else{
                        $output .= '<img src=\'' . JURI::base(true). $item->carousel_item_img . '\' alt="">';
                    }
                }
            $output.='</div>';

            if ($item->item_description){
                $output.='<div class="content-text">
                                                        ' . $item->item_description . '
                                                    </div>';
            }else{
                $output.='<div class="content-text">
                                                        这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。
                                                    </div>';
            }
            $output.='<div class="swiper-pagination button-box">
                                                      <div class="swiper-button-next"></div>
                                                      <div class="swiper-button-prev"></div>
                                        </div>                        
                                    </div>
                                </div>';


            if ($item->is_link === 1)
            {
                $output .= '</a>';
            }
            $output .= '</div>';
        }
        $output .= '
                </div>
            </div>
        </div>';


        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');

        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js');

        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed        = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval     = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;

//        切换方式
        $carousel_direction = (isset($settings->carousel_direction) && $settings->carousel_direction) ? $settings->carousel_direction : 'horizontal';


//        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;

        if ($carousel_autoplay === 1)
        {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        }
        else
        {
            $autoplay = 'false';
        }

        $carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        if ($carousel_arrow === 1)
        {
            $navigation = '{
                nextEl: "#swiper_' . $addonId . ' .swiper-button-next",
                prevEl: "#swiper_' . $addonId . ' .swiper-button-prev",
            }';
        }
        else
        {
            $navigation = '{}';
        }

        $script = 'jQuery(document).ready(function($){
//        初始化swiper配置项
            function initSwiper(){
                let navigation={};
                if(innerWidth>767){
                    navigation='.$navigation.';
                }
                let settings={
                    direction: "' . $carousel_direction . '",
                    loop: true,
                    loopFillGroupWithBlank: true,
                    autoplay: ' . $autoplay . ',                  
                    speed: ' . $carousel_speed . ',
                    slidesPerView: 1,
                    navigation: navigation,
                    on:{
                      init: function(){
                        swiperAnimateCache(this); //隐藏动画元素 
                        swiperAnimate(this); //初始化完成开始动画
                      }, 
                      slideChangeTransitionEnd: function(){ 
                        swiperAnimate(this); //每个slide切换结束时也运行当前slide动画
                        //this.slides.eq(this.activeIndex).find(".ani").removeClass("ani"); 动画只展现一次，去除ani类名
                      } 
                    }
                }
                
                
                let swiper = new Swiper("#swiper_' . $addonId . ' .content-swiper-container", settings);
                return swiper;
            }
//            根据屏幕初始化swiper
            initSwiper()
//            屏幕改变大小再初始化一次
            window.onresize=function (){
                initSwiper();
            } 
        })';

        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;

//        轮播
        $addonId = '#swiper_' . $this->addon->id;


//        外部容器
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
                $carousel_height_sm = $settings->carousel_height->sm;
                $carousel_height_xs = $settings->carousel_height->xs;
            } else {
                $carousel_height = $settings->carousel_height;
                $carousel_height_sm = $settings->carousel_height_sm;
                $carousel_height_xs = $settings->carousel_height_xs;
            }
        } else {
            $carousel_height = '';
            $carousel_height_sm ='';
            $carousel_height_xs ='';
        }


//          背景
        if (isset($settings->background_width) && $settings->background_width) {
            if (is_object($settings->carousel_height)) {
                $background_width = $settings->background_width->md;
                $background_width_sm = $settings->background_width->sm;
                $background_width_xs = $settings->background_width->xs;
            } else {
                $background_width = $settings->background_width;
                $background_width_sm = $settings->background_width_sm;
                $background_width_xs = $settings->background_width_xs;
            }
        } else {
            $background_width = '';
            $background_width_sm ='';
            $background_width_xs ='';
        }
        if (isset($settings->background_width) && $settings->background_width) {
            if (is_object($settings->carousel_height)) {
                $background_height = $settings->background_height->md;
                $background_height_sm = $settings->background_height->sm;
                $background_height_xs = $settings->background_height->xs;
            } else {
                $background_height = $settings->background_height;
                $background_height_sm = $settings->background_height_sm;
                $background_height_xs = $settings->background_height_xs;
            }
        } else {
            $background_height = '';
            $background_height_sm ='';
            $background_height_xs ='';
        }
        $background_color  = (isset($settings->background_color) && $settings->background_color) ? $settings->background_color : '#000000AB';
        if (isset($settings->background_left) && $settings->background_left) {
            if (is_object($settings->background_left)) {
                $background_left = $settings->background_left->md;
                $background_left_sm = $settings->background_left->sm;
                $background_left_xs = $settings->background_left->xs;
            } else {
                $background_left = $settings->background_left;
                $background_left_sm = $settings->background_left_sm;
                $background_left_xs = $settings->background_left_xs;
            }
        } else {
            $background_left = '';
            $background_left_sm ='';
            $background_left_xs ='';
        }
//	    文字
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->md;
                $description_fontsize_sm = $settings->description_fontsize->sm;
                $description_fontsize_xs = $settings->description_fontsize->xs;
            } else {
                $description_fontsize = $settings->description_fontsize;
                $description_fontsize_sm = $settings->description_fontsize_sm;
                $description_fontsize_xs = $settings->description_fontsize_xs;
            }
        } else {
            $description_fontsize = '';
            $description_fontsize_sm ='';
            $description_fontsize_xs ='';
        }
        if (isset($settings->description_lineheight) && $settings->description_lineheight) {
            if (is_object($settings->description_lineheight)) {
                $description_lineheight = $settings->description_lineheight->md;
                $description_lineheight_sm = $settings->description_lineheight->sm;
                $description_lineheight_xs = $settings->description_lineheight->xs;
            } else {
                $description_lineheight = $settings->description_lineheight;
                $description_lineheight_sm = $settings->description_lineheight_sm;
                $description_lineheight_xs = $settings->description_lineheight_xs;
            }
        } else {
            $description_lineheight = '';
            $description_lineheight_sm ='';
            $description_lineheight_xs ='';
        }

        $description_text_color = (isset($settings->description_text_color) && $settings->description_text_color) ? $settings->description_text_color : '#fff';

//		翻页
        $carousel_arrow=(isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        $show_arrow=$carousel_arrow===1?'block':'none';

        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti = $settings->arrow_position_verti->md;
                $arrow_position_verti_sm = $settings->background_width->sm;
                $arrow_position_verti_xs = $settings->background_width->xs;
            } else {
                $arrow_position_verti = $settings->arrow_position_verti;
                $arrow_position_verti_sm = $settings->arrow_position_verti_sm;
                $arrow_position_verti_xs = $settings->arrow_position_verti_xs;
            }
        } else {
            $arrow_position_verti = '';
            $arrow_position_verti_sm ='';
            $arrow_position_verti_xs ='';
        }
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori = $settings->arrow_position_hori->md;
                $arrow_position_hori_sm = $settings->arrow_position_hori->sm;
                $arrow_position_hori_xs = $settings->arrow_position_hori->xs;
            } else {
                $arrow_position_hori = $settings->arrow_position_hori;
                $arrow_position_hori_sm = $settings->arrow_position_hori_sm;
                $arrow_position_hori_xs = $settings->arrow_position_hori_xs;
            }
        } else {
            $arrow_position_hori = '';
            $arrow_position_hori_sm ='';
            $arrow_position_hori_xs ='';
        }

        $arrow_font_size=(isset($settings->arrow_font_size) && $settings->arrow_font_size) ? $settings->arrow_font_size : '16';
        $arrow_color=(isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '#fff';
        $arrow_hover_color=(isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '#fff';
        $arrow_width=(isset($settings->arrow_width) && $settings->arrow_width) ? $settings->arrow_width : '80';
        $arrow_height=(isset($settings->arrow_height) && $settings->arrow_height) ? $settings->arrow_height : '28';



        $output = '
            ' . $addonId . '{
                position: relative;
                width: 100%;
                height: ' . $carousel_height . 'px;
                overflow: hidden;
            }
            ' . $addonId . ' .content-swiper-container{
                width: 100%;
                height: 100%;
                margin-left: auto;
                margin-right: auto;
                position: relative;
                overflow: hidden;
                list-style: none;
                padding: 0;
                z-index: 1;
            }
            ' . $addonId . ' .swiper-slide{
                position: relative;
                overflow: hidden;
            }
            ' . $addonId . ' .swiper-slide img{
                width: 100%;
                height: 100%;
                display: block;
                object-fit: cover;
            }
            ' . $addonId . ' .swiper-content{
                width: ' . $background_width . 'px;
				height: ' . $background_height . 'px;
				background: ' . $background_color . ';
				position:absolute;
				bottom: 0;
				left: ' . $background_left . '%;
				display:flex;
				flex-direction: column;
            }
            ' . $addonId . ' .swiper-content .content-img{
                width: 100%;
            }
            ' . $addonId . ' .swiper-content .content-img img{
                object-fit: cover;
                height:auto;
            }
            ' . $addonId . ' .swiper-content .content-img::after{
                content: "";
                display: block;
                width: calc(100% - 54px);
                height: 1px;
                background: #00000078;
                left: 0;
                right: 0;
                margin: auto;
                position: absolute;
                bottom: 0;
            }
            ' . $addonId . ' .content-text{
                width: 100%;
                flex:1;
                padding: 57px 43px;
                overflow: hidden;
                font-size: ' . $description_fontsize . 'px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: ' . $description_text_color . ';
				line-height: ' . $description_lineheight . 'px;
            }
            ' . $addonId . ' .button-box{
                display: '.$show_arrow.';
                position:absolute;
                width: '.$arrow_width.'px;
                height: '.$arrow_height.'px;
                bottom: '.$arrow_position_verti.'px;
                right: '.$arrow_position_hori.'px;
            }
           ' . $addonId . ' .swiper-button-prev:after, ' . $addonId . ' .swiper-button-next:after{
                font-size: '.$arrow_font_size.'px;
                color: '.$arrow_color.';
           }
           ' . $addonId . ' .swiper-button-prev, ' . $addonId . ' .swiper-button-next{
                transition:all:.5s;
                outline:none;
                background-image: none;
           }
           ' . $addonId . ' .swiper-button-prev:hover::after,
           ' . $addonId . ' .swiper-button-next:hover::after{
                color: '.$arrow_hover_color.';
           }
           ' . $addonId . ' .swiper-button-prev:hover{
                transform: translateX(-10px);
           }
           ' . $addonId . ' .swiper-button-next:hover{
                transform: translateX(10px);
           }';
        $items   = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';

        foreach ($items as $key => $item){
            if (isset($item->carousel_item_img_padding) && $item->carousel_item_img_padding) {
                if (is_object($item->carousel_item_img_padding)) {
                    $padding = $item->carousel_item_img_padding->md;
                    $padding_sm = $item->carousel_item_img_padding->sm;
                    $padding_xs = $item->carousel_item_img_padding->xs;
                } else {
                    $padding = $item->carousel_item_img_padding;
                    $padding_sm = $item->carousel_item_img_padding_sm;
                    $padding_xs = $item->carousel_item_img_padding_xs;
                }
            } else {
                $padding = '';
                $padding_sm ='';
                $padding_xs ='';
            }
            if (isset($item->carousel_item_img_width) && $item->carousel_item_img_width) {
                if (is_object($item->carousel_item_img_width)) {
                    $carousel_item_img_width = $item->carousel_item_img_width->md;
                    $carousel_item_img_width_sm = $item->carousel_item_img_width->sm;
                    $carousel_item_img_width_xs = $item->carousel_item_img_width->xs;
                } else {
                    $carousel_item_img_width = $item->carousel_item_img_width;
                    $carousel_item_img_width_sm = $item->carousel_item_img_width_sm;
                    $carousel_item_img_width_xs = $item->carousel_item_img_width_xs;
                }
            } else {
                $carousel_item_img_width = '';
                $carousel_item_img_width_sm ='';
                $carousel_item_img_width_xs ='';
            }
            $output.=$addonId.' .swiper-slide[data-id="'.$key.'"] .content-img{
                padding: '.$padding.'
            }
            '.$addonId.' .swiper-slide[data-id="'.$key.'"] .content-img img{
                width: '.$carousel_item_img_width.'px;
            }
            @media (min-width: 768px) and (max-width: 991px){
                ' . $addonId.' .swiper-slide[data-id="'.$key.'"] .content-img{
                    padding: '.$padding_sm.'
                }
                '.$addonId.' .swiper-slide[data-id="'.$key.'"] .content-img img{
                    width: '.$carousel_item_img_width_sm.'px;
                }
            }
            @media (max-width: 767px){
                ' . $addonId.' .swiper-slide[data-id="'.$key.'"] .content-img{
                    padding: '.$padding_xs.'
                }
                '.$addonId.' .swiper-slide[data-id="'.$key.'"] .content-img img{
                    width: '.$carousel_item_img_width_xs.'px;
                }
            }';
        }

        $output.='
            @media (max-width:1432px) and (min-width: 991px){
                ' . $addonId . ' .swiper-content{
                    left: ' . ($background_left - 20 > 0 ? $background_left - 20 : 0) . '%;
                }
            }
            @media (min-width: 768px) and (max-width: 991px){
                ' . $addonId . '{
                    height: ' . $carousel_height_sm . 'px;
                }
                 ' . $addonId . ' .swiper-content{
                    width: ' . $background_width_sm . 'px;
                    height: ' . $background_height_sm . 'px;
				    left: ' . $background_left_sm . '%;
                }
                ' . $addonId . ' .button-box{
                    bottom: '.$arrow_position_verti_sm.'px;
                    right: '.$arrow_position_hori_sm.'px;
                }
                ' . $addonId . ' .content-text{
                    font-size: ' . $description_fontsize_sm . 'px;
				    line-height: ' . $description_lineheight_sm . 'px;
                    padding: 0 30px;
                }
                /*' . $addonId . ' .button-box{
                    display: none;
                }*/
            }
            @media (max-width: 767px){
                ' . $addonId . '{
                    height: ' . $carousel_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-content{
                    width: ' . $background_width_xs . 'px;
                    height: ' . $background_height_xs. 'px;
				    left: ' . $background_left_xs . '%;
                }
                ' . $addonId . ' .button-box{
                    bottom: '.$arrow_position_verti_xs.'px;
                    right: '.$arrow_position_hori_xs.'px;
                }
                ' . $addonId . ' .content-text{
                    font-size: ' . $description_fontsize_xs . 'px;
				    line-height: ' . $description_lineheight_xs . 'px;
                    padding: 0 15px;
                }
                ' . $addonId . ' .button-box{
                    display: none;
                }
            }
            ';

        return $output;
    }

    static function contentSwiperCss(){
        $output.='
        <# 
            let content_swiper_id="#swiper_"+data.id;
            let carousel_height=_.isObject(data.carousel_height)&&data.carousel_height.md?data.carousel_height.md:data.carousel_height;
            let carousel_height_sm=_.isObject(data.carousel_height)&&data.carousel_height.sm?data.carousel_height.sm:data.carousel_height;
            let carousel_height_xs=_.isObject(data.carousel_height)&&data.carousel_height.xs?data.carousel_height.xs:data.carousel_height;
            let background_width=_.isObject(data.background_width)&&data.background_width.md?data.background_width.md:data.background_width;
            let background_width_sm=_.isObject(data.background_width)&&data.background_width.sm?data.background_width.sm:data.background_width;
            let background_width_xs=_.isObject(data.background_width)&&data.background_width.xs?data.background_width.xs:data.background_width;
            let background_height=_.isObject(data.background_height)&&data.background_height.md?data.background_height.md:data.background_height;
            let background_height_sm=_.isObject(data.background_height)&&data.background_height.sm?data.background_height.sm:data.background_height;
            let background_height_xs=_.isObject(data.background_height)&&data.background_height.xs?data.background_height.xs:data.background_height;
            let background_color=data.background_color?data.background_color:"#000000AB";
            let background_left=_.isObject(data.background_left)&&data.background_left.md?data.background_left.md:data.background_left;
            let background_left_sm=_.isObject(data.background_left)&&data.background_left.sm?data.background_left.sm:data.background_left;
            let background_left_xs=_.isObject(data.background_left)&&data.background_left.xs?data.background_left.xs:data.background_left;
            let description_fontsize=_.isObject(data.description_fontsize)&&data.description_fontsize.md?data.description_fontsize.md:data.description_fontsize;
            let description_fontsize_sm=_.isObject(data.description_fontsize)&&data.description_fontsize.sm?data.description_fontsize.sm:data.description_fontsize;
            let description_fontsize_xs=_.isObject(data.description_fontsize)&&data.description_fontsize.xs?data.description_fontsize.xs:data.description_fontsize;
            let description_text_color=data.description_text_color?data.description_text_color:"#fff";
            let description_lineheight=_.isObject(data.description_lineheight)&&data.description_lineheight.md?data.description_lineheight.md:data.description_lineheight;
            let description_lineheight_sm=_.isObject(data.description_lineheight)&&data.description_lineheight.sm?data.description_lineheight.sm:data.description_lineheight;
            let description_lineheight_xs=_.isObject(data.description_lineheight)&&data.description_lineheight.xs?data.description_lineheight.xs:data.description_lineheight;
            let carousel_arrow=data.carousel_arrow?data.carousel_arrow:0;
            let show_arrow=carousel_arrow===1?"block":"none";
            let arrow_width=data.arrow_width?data.arrow_width:80;
            let arrow_height=data.arrow_height?data.arrow_height:80;
            let arrow_position_verti=_.isObject(data.arrow_position_verti)&&data.arrow_position_verti.md?data.arrow_position_verti.md:data.arrow_position_verti;
            let arrow_position_verti_sm=_.isObject(data.arrow_position_verti)&&data.arrow_position_verti.sm?data.arrow_position_verti.sm:data.arrow_position_verti;
            let arrow_position_verti_xs=_.isObject(data.arrow_position_verti)&&data.arrow_position_verti.xs?data.arrow_position_verti.xs:data.arrow_position_verti;
            let arrow_position_hori=_.isObject(data.arrow_position_hori)&&data.arrow_position_hori.md?data.arrow_position_hori.md:data.arrow_position_hori;
            let arrow_position_hori_sm=_.isObject(data.arrow_position_hori)&&data.arrow_position_hori.sm?data.arrow_position_hori.sm:data.arrow_position_hori;
            let arrow_position_hori_xs=_.isObject(data.arrow_position_hori)&&data.arrow_position_hori.xs?data.arrow_position_hori.xs:data.arrow_position_hori;
            let arrow_font_size=data.arrow_font_size?data.arrow_font_size:16;
            let arrow_color=data.arrow_color?data.arrow_color:"#fff";
            let arrow_hover_color=data.arrow_hover_color?data.arrow_hover_color:"";        
        #>
        <style>
         #jwpf-addon-{{data.id}} .tips{
            height: 40px;
            line-height: 40px;
            margin-bottom: 30px;
            background: rgba(255,141,115,0.88);
            box-sizing: border-box;
            padding: 0 10px;
        }
        {{content_swiper_id}}{
            position: relative;
            width: 100%;
            height: {{carousel_height||800}}px;
            overflow: hidden;
        }
        {{content_swiper_id}} .content-swiper-container{
            width: 100%;
            height: 100%;
        }
        {{content_swiper_id}} .swiper-slide{
            position: relative;
            overflow: hidden;
            width: 100%!important;
            height: 100%;
        }
        {{content_swiper_id}} .swiper-slide img{
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
        }
        {{content_swiper_id}} .swiper-content{
            width: {{background_width||680}}px;
            height: {{background_height||739}}px;
            background: {{background_color}};
            position: absolute;
            bottom: 0;
            left: {{background_left||52}}%;
            display:flex;
            flex-direction: column;
        }
        {{content_swiper_id}} .swiper-content .content-img{
            width: 100%;
        }
        {{content_swiper_id}} .swiper-content .content-img img{
            object-fit: cover;
            height:auto;
        }
        {{content_swiper_id}} .swiper-content .content-img::after{
            content: "";
            display: block;
            width: calc(100% - 54px);
            height: 1px;
            background: #00000078;
            left: 0;
            right: 0;
            margin: auto;
            position: absolute;
            bottom: 0;
        }
        {{content_swiper_id}} .content-text{
            width: 100%;
            flex:1;
            padding: 57px 43px;
            overflow: hidden;
            font-size: {{description_fontsize||16}}px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: {{description_text_color}};
            line-height: {{description_lineheight||40}}px;
        }
        {{content_swiper_id}} .button-box{
            display: {{show_arrow}};
            position: absolute;
            width: {{arrow_width}}px;
            height: {{arrow_height}}px;
            bottom: {{arrow_position_verti}}px;
            right: {{arrow_position_hori}}px;
        }
        {{content_swiper_id}} .swiper-button-prev:after, {{content_swiper_id}} .swiper-button-next:after{
            font-size: {{arrow_font_size}}px;
            color: {{arrow_color}};
        }
        {{content_swiper_id}} .swiper-button-prev, {{content_swiper_id}} .swiper-button-next{
            transition:all:.5s;
            outline:none;
            background-image: none;
        }
        {{content_swiper_id}} .swiper-button-prev:hover::after,
        {{content_swiper_id}} .swiper-button-next:hover::after{
            color: {{arrow_hover_color}};
        }
        {{content_swiper_id}} .swiper-button-prev:hover{
            transform: translateX(-10px);
        }
        {{content_swiper_id}} .swiper-button-next:hover{
            transform: translateX(10px);
        }
        <# _.each(data.jw_image_carousel_item,function(item,key){
            let padding = _.isObject(item.carousel_item_img_padding)&&item.carousel_item_img_padding.md?window.getMarginPadding(item.carousel_item_img_padding.md, "padding"):window.getMarginPadding(item.carousel_item_img_padding, "padding");
            let padding_sm = _.isObject(item.carousel_item_img_padding)&&item.carousel_item_img_padding.sm?window.getMarginPadding(item.carousel_item_img_padding.sm, "padding"):window.getMarginPadding(item.carousel_item_img_padding, "padding");
            let padding_xs = _.isObject(item.carousel_item_img_padding)&&item.carousel_item_img_padding.xs?window.getMarginPadding(item.carousel_item_img_padding.xs, "padding"):window.getMarginPadding(item.carousel_item_img_padding, "padding");
            let carousel_item_img_width=_.isObject(item.carousel_item_img_width)&&item.carousel_item_img_width.md?item.carousel_item_img_width.md:item.carousel_item_img_width;
            let carousel_item_img_width_sm=_.isObject(item.carousel_item_img_width)&&item.carousel_item_img_width.sm?item.carousel_item_img_width.sm:item.carousel_item_img_width;
            let carousel_item_img_width_xs=_.isObject(item.carousel_item_img_width)&&item.carousel_item_img_width.xs?item.carousel_item_img_width.xs:item.carousel_item_img_width;        
        #>
            {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img{
                {{padding}}
            }
            {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img img{
                width: {{carousel_item_img_width}}px;
            }
            @media (min-width: 768px) and (max-width: 991px){
                {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img{
                    {{padding_sm}};
                }
                {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img img{
                    width: {{carousel_item_img_width_sm}}px;
                }
            }
            @media (max-width: 767px){
                {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img{
                    {{padding_xs}};
                }
                {{content_swiper_id}} .swiper-slide[data-id="{{key}}"] .content-img img{
                    width: {{carousel_item_img_width_xs}}px;
                }
            }
        <# })#>
        </style>';
        return $output;
    }

    static function getTemplate(){
        $output='
        <# 
            let contentSwiperAddonId=data.id;
        #>
        <p class="alert alert-warning">本图片仅为布局样式，请在预览页面中查看该插件切换效果</p>
        <div class="swiper-box" id="swiper_{{contentSwiperAddonId}}">
            <div class="content-swiper-container">
                <div class="swiper-wrapper">
                    <# _.each(data.jw_image_carousel_item,function(item,key){ #>
                        <div class="swiper-slide" data-id="{{key}}">
                            <# if(item.is_link===1){ #>
                                <a href="{{item.image_carousel_img_link}}" >
                            <# } #>
                            <# if(item.image_carousel_img){ #>
                                 <# if(item.image_carousel_img.includes("http://")||item.image_carousel_img.includes("https://")){ #>
                                    <img src=\'{{item.image_carousel_img}}\' alt="">
                                <# }else{ #>
                                    <img src=\''.JURI::base(true).'{{item.image_carousel_img}}\' alt="">
                                <# } #>
                            <# } #>
                           
                            <div class="swiper-content ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.5s">
                                <div style="position: relative;width: 100%;height: 100%">
                                    <div class="content-img">
                                    <# if(item.carousel_item_img){ #>
                                        <# if(item.carousel_item_img.includes("http://")||item.carousel_item_img.includes("https://")){ #>
                                            <img src=\'{{item.carousel_item_img}}\' alt="">
                                        <# }else{ #>
                                            <img src=\''.JURI::base(true).'{{item.carousel_item_img}}\' alt="">
                                        <# } #>
                                    <# } #>
                                   
                                    </div>
                                    <# if(item.description){ #>
                                        <div class="content-text">
                                            {{item.item_description}}
                                        </div>
                                    <# }else{ #>
                                        <div class="content-text">
                                            这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。
                                        </div>
                                    <# } #>
                                    <div class="swiper-pagination button-box">
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>    
                                </div>
                            </div>
                            <# if(item.is_link===1){ #>
                                </a>
                            <# } #>
                        </div>
                    <# }) #>
                </div>
            </div>
        </div>
        ';
        return self::contentSwiperCss().$output;
    }
}
