<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonJZT_ADDON_LSGA_4D2L2F02N8_producttypeInfo extends JwpagefactoryAddons
{
    public static $this_obj;

    public function __construct($addon)
    {
        parent::__construct($addon);
        self::$this_obj = $this;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $detail = $_GET['details'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $cpcatid = $_GET['cpcatid'] ?? 0;
        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';
        $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : '';
        $nav_icon_postion = (isset($settings->nav_icon_postion) && $settings->nav_icon_postion) ? $settings->nav_icon_postion : '';
        $nav_image_postion = (isset($settings->nav_image_postion) && $settings->nav_image_postion) ? $settings->nav_image_postion : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
        $nav_text_align = (isset($settings->nav_text_align) && $settings->nav_text_align) ? $settings->nav_text_align : 'jwpf-text-left';
        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';
        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : '0';
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;

        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $article_helper;
        $categories_list = JwpagefactoryHelperCategories::getCategoriesList1('com_goods', $site_id, $type_parent, $type_start, $type_num,$detail);

        // print_r($categories_list);die;

        if ($type_parent == 'all') {
            $c_list = [];
            foreach ($categories_list as $key_l => $tab_l) {
                if ($tab_l['parent_id'] != 1) {
                    $c_list[$tab_l['parent_id']][] = $categories_list[$key_l];
                    unset($categories_list[$key_l]);
                }
            }
            foreach ($categories_list as $key_z => $tab_z) {
                $categories_list[$key_z]['subset'] = $c_list[$tab_z['tag_id']] ?? [];
            }
        }
        $output = '<div class="jwpf-addon jwpf-addon-tab ' . $addon_id . $class . '">';
        $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
        $output .= '<div class="jwpf-addon-content minBox jwpf-tab jwpf-' . $style . '-tab jwpf-tab-' . $nav_position . '">';
        //新修改
        $nav_img = isset($settings->nav_img) ? $settings->nav_img : '';//导航头部图片
        $dh_db_img = isset($settings->dh_db_img) ? $settings->dh_db_img : 'k';//导航头部图片
        $dh_db_img_border = isset($settings->dh_db_img_border) ? $settings->dh_db_img_border : 0;//导航头部图片圆角
        $dh_db_img_width = isset($settings->dh_db_img_width) ? $settings->dh_db_img_width : 50;//导航头部图片
        $dh_db_img_height = isset($settings->dh_db_img_height) ? $settings->dh_db_img_height : 50;//导航头部图片
        $nav_all_font_color = isset($settings->nav_all_font_color) ? $settings->nav_all_font_color : '#00000';//导航头部图片
        $nav_font_positions = isset($settings->nav_font_positions) ? $settings->nav_font_positions : 'left';//导航头部图片
        $nav_img_w = isset($settings->nav_img_w) ? $settings->nav_img_w : '';//导航图片宽
        $nav_img_h = isset($settings->nav_img_h) ? $settings->nav_img_h : '';//导航图片高
        $nav_img_m = isset($settings->nav_img_m) ? $settings->nav_img_m : '';//导航图片与导航之间的空间
        $nav_bor = isset($settings->nav_bor) ? $settings->nav_bor : 0;//导航边框设置
        $nav_bor_color = isset($settings->nav_bor_color) ? $settings->nav_bor_color : '#666';//导航边框颜色
        $nav_bor = explode(" ", $settings->nav_bor);
        $nav_bor_m = isset($settings->nav_bor_m) ? $settings->nav_bor_m : 0;//导航外层内边距
        $nav_bor_m = explode(" ", $settings->nav_bor_m);
        $nav_back_color = isset($settings->nav_back_color) ? $settings->nav_back_color : '';//导航外层背景
        $nav_padding = isset($settings->nav_padding) ? $settings->nav_padding : 0;//导航外层背景
        $nav_padding = explode(" ", $settings->nav_padding);
        $nav_font_width = isset($settings->nav_font_width) ? $settings->nav_font_width : 0;//导航文字自定义位置

        if ($style == 'custom') {
            //border边框
            $output .= '<ul class="jwpf-nav minUl jwpf-nav-' . $style . '" role="tablist"  style="height:fit-content;background-color: ' . $nav_back_color . ';
 		padding:' . ($nav_bor_m[0] ? $nav_bor_m[0] : 0) . ' ' . ($nav_bor_m[1] ? $nav_bor_m[1] : 0) . ' ' . ($nav_bor_m[2] ? $nav_bor_m[2] : 0) . ' ' . ($nav_bor_m[3] ? $nav_bor_m[3] : 0) . ';
 		border-top-width:' . ($nav_bor[0] ? $nav_bor[0] : 0) . ';border-left-width:' . ($nav_bor[1] ? $nav_bor[1] : 0) . ';border-bottom-width:' . ($nav_bor[2] ? $nav_bor[2] : 0) . ';border-right-width:' . ($nav_bor[3] ? $nav_bor[3] : 0) . '; border-color:' . $nav_bor_color . ';border-style:solid;">';
            if ($nav_img != '')  //导航图片
            {
                $output .= '<img style="max-width:none; width:' . $nav_img_w . '%;height: ' . $nav_img_h . 'px;margin-bottom:' . ($nav_img_m ? $nav_img_m : 0) . 'px;" src="' . $nav_img . '">';
            }
        } else {
            //border边框
            $output .= '<ul class="jwpf-nav minUl jwpf-nav-' . $style . '" role="tablist">';
        }
        $one = 1;


        if ($pro_type == 'type4') {

            // print_r($settings->jw_tab_item);die;
            foreach ($settings->jw_tab_item as $key => $tab) {

                $icon_top = '';
                $icon_bottom = '';
                $icon_right = '';
                $icon_left = '';
                $icon_block = '';
                //Image
                $image_top = '';
                $image_bottom = '';
                $image_right = '';
                $image_left = '';
                //Lazy load image
                $dimension = $this->get_image_dimension($tab->image);
                $dimension = implode(' ', $dimension);

                $placeholder = !$tab->image ? false : $this->get_image_placeholder($tab->image);
                if (strpos($tab->image, "http://") !== false || strpos($tab->image, "https://") !== false) {
                    $tab->image = $tab->image;
                }

                $title = (isset($tab->title) && $tab->title) ? ' ' . $tab->title . ' ' : '';
                $subtitle = (isset($tab->subtitle) && $tab->subtitle) ? '<span class="jwpf-tab-subtitle">' . $tab->subtitle . '</span>' : '';

                if (isset($tab->image_or_icon) && $tab->image_or_icon == 'image') {
                    if ($tab->image && $nav_image_postion == 'top') {
                        $image_top = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'bottom') {
                        $image_bottom = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'right') {
                        $image_right = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } else {
                        $image_left = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    }
                } else {
                    if (isset($tab->icon) && $tab->icon) {
                        $icon_arr = array_filter(explode(' ', $tab->icon));
                        if (count($icon_arr) === 1) {
                            $tab->icon = 'fa ' . $tab->icon;
                        }
                        if ($tab->icon && $nav_icon_postion == 'top') {
                            $icon_top = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'bottom') {
                            $icon_bottom = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'right') {
                            $icon_right = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } else {
                            $icon_left = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        }
                    }
                }
                if ($nav_icon_postion == 'top' || $nav_icon_postion == 'bottom' || $nav_image_postion == 'top' || $nav_image_postion == 'bottom') {
                    $icon_block = 'tab-img-or-icon-block-wrap';
                }

                $output .= '<li class="page_n ' . (($key == 0) ? "active" : "") . '">';
                if ($nav_font_width != 0) {
                    $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                } else {

                    $output .= '<a ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                }
                $output .= '</li>';
                // if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset']))
                // {
                // 	//二级导航为空
                // 	$output .= '<li class="page_n ' . (($one == 1) ? "active" : "") . '" style="padding:' . ($nav_padding[0] ? $nav_padding[0] : 0) . ' ' . ($nav_padding[1] ? $nav_padding[1] : 0) . ' ' . ($nav_padding[2] ? $nav_padding[2] : 0) . ' ' . ($nav_padding[3] ? $nav_padding[3] : 0) . '!important;">';
                // 	$output .= '<a style="text-indent:'.$nav_font_width.'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                // 	$output .= '</li>';
                // }
                // else
                // {
                // 	// 有二级导航
                // 	$output .= '<li>';
                // 	$output .= '<div class="page_n">';
                // 	$output .= '<a style="text-indent:'.$nav_font_width.'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                // 	$output .= '</div>';
                // 	$output .= '<ul class="er_type">';
                // 	foreach ($tab['subset'] as $ke => $tabe)
                // 	{
                // 		$output .= '<li class="page_n ' . (($one == 1) ? "active" : "") . '">';
                // 		$output .= '<a style="text-indent:'.$nav_font_width.'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-' . ($ke + 1) . '" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $ke) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $tabe['title'] . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                // 		$output .= '</li>';
                // 		$one    = $one + 1;
                // 	}
                // 	$output .= '</ul>';
                // 	$output .= '</li>';

                // }
                $one++;
            }
            //边框
            $output .= '</ul>';
            $oen2 = 1;
            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
            //列表数据的循环
            foreach ($settings->jw_tab_item as $k => $tab) {
                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($k == 0) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $tab->content . '</div>';
                //            $oen2 = $oen2 +1 ;
            }
            
        } else {
            foreach ($categories_list as $key => $tab) {

                $icon_top = '';
                $icon_bottom = '';
                $icon_right = '';
                $icon_left = '';
                $icon_block = '';
                //Image
                $image_top = '';
                $image_bottom = '';
                $image_right = '';
                $image_left = '';
                //Lazy load image
                $dimension = $this->get_image_dimension($tab->image);
                $dimension = implode(' ', $dimension);

                $placeholder = !$tab->image ? false : $this->get_image_placeholder($tab->image);
                if (strpos($tab->image, "http://") !== false || strpos($tab->image, "https://") !== false) {
                    $tab->image = $tab->image;
                }

                $title = (isset($tab['title']) && $tab['title']) ? ' ' . $tab['title'] . ' ' : '';
                $subtitle = (isset($tab['subtitle']) && $tab['subtitle']) ? '<span class="jwpf-tab-subtitle">' . $tab['subtitle'] . '</span>' : '';

                if (isset($tab->image_or_icon) && $tab->image_or_icon == 'image') {
                    if ($tab->image && $nav_image_postion == 'top') {
                        $image_top = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'bottom') {
                        $image_bottom = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'right') {
                        $image_right = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } else {
                        $image_left = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    }
                } else {
                    if (isset($tab->icon) && $tab->icon) {
                        $icon_arr = array_filter(explode(' ', $tab->icon));
                        if (count($icon_arr) === 1) {
                            $tab->icon = 'fa ' . $tab->icon;
                        }
                        if ($tab->icon && $nav_icon_postion == 'top') {
                            $icon_top = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'bottom') {
                            $icon_bottom = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'right') {
                            $icon_right = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } else {
                            $icon_left = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        }
                    }
                }
                if ($nav_icon_postion == 'top' || $nav_icon_postion == 'bottom' || $nav_image_postion == 'top' || $nav_image_postion == 'bottom') {
                    $icon_block = 'tab-img-or-icon-block-wrap';
                }

                //            $output .= '<li class="page_n ' . (($key == 0) ? "active" : "") . '">';
                //            $output .= '<a data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($key == 0) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                //            $output .= '</li>';
                if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset'])) {
                    //二级导航为空
                    $output .= '<li class="page_n ' . (($one == 1) ? "active" : "") . '" >';
                    if ($nav_font_width != 0) {

                        if($dh_db_img=='k')
                        {
                            if($tab['Lv_three_icon'])
                            {
                                $output.='<img src="'.$tab['Lv_three_icon'].'" style="width:'.$dh_db_img_width.'px;height:'.$dh_db_img_height.'px;border-radius:'.$dh_db_img_border.'px" />';
                            }
                            else
                            {
                                $output.='<img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg" style="width:'.$dh_db_img_width.'px;height:'.$dh_db_img_height.'px;border-radius:'.$dh_db_img_border.'px" />';
                            }
                        }
                        $output .= '<a style="text-indent:' . $nav_font_width . 'px;text-align: '.$nav_font_positions.';color:'.$nav_all_font_color.';" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                    } else {
                        
                        $output .= '<a style="text-align: '.$nav_font_positions.';color:'.$nav_all_font_color.';" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">';
                        if($dh_db_img=='k')
                        {
                            if($tab['Lv_three_icon'])
                            {
                                $output.='<img src="'.$tab['Lv_three_icon'].'" style="width:'.$dh_db_img_width.'px;height:'.$dh_db_img_height.'px;border-radius:'.$dh_db_img_border.'px" />' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                            }
                            else
                            {
                                $output.='<img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg" style="width:'.$dh_db_img_width.'px;height:'.$dh_db_img_height.'px;border-radius:'.$dh_db_img_border.'px" />' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                            }
                        }

                    }
                    $output .= '</li>';
                } else {
                    // 有二级导航
                    $output .= '<li>';
                    $output .= '<div class="page_n">';
                    if ($nav_font_width != 0) {
                        $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';

                    } else {

                        $output .= '<a  ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';
                    }
                    $output .= '</div>';
                    $output .= '<ul class="er_type">';
                    foreach ($tab['subset'] as $ke => $tabe) {
                        $output .= '<li class="page_n ' . (($one == 1) ? "active" : "") . '">';
                        if ($nav_font_width != 0) {
                            $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-' . ($ke + 1) . '" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $ke) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $tabe['title'] . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';

                        } else {
                            $output .= '<a ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-' . ($ke + 1) . '" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $ke) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $tabe['title'] . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';

                        }
                        $output .= '</li>';
                        $one = $one + 1;
                    }
                    $output .= '</ul>';
                    $output .= '</li>';

                }
                $one++;
            }
            //边框
            $output .= '</ul>';
            $oen2 = 1;
            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
            //列表数据的循环
            foreach ($categories_list as $k => $tab) {
                if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset'])) {
                    $conent = $this->render2($tab['tag_id'], $this);
                    $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                    $oen2 = $oen2 + 1;
                } else {
                    foreach ($tab['subset'] as $ak => $va) {
                        $conent = $this->render2($va['tag_id'], $this);
                        $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . ($this->addon->id + $ak) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                        $oen2 = $oen2 + 1;
                    }
                }
                //            $oen2 = $oen2 +1 ;
            }
           
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        // 固定图片高度
        if ($fix_img_height) {
            $output .= "<style>

                     {$addon_id} .jwpf-img-responsive{
                         height:{$fix_img_height_input}px !important;
                       }
                       @media (max-width: 992px) {
                     {$addon_id} .jwpf-img-responsive{
                        height:{$fix_img_height_input_m}px !important;
                        }
                    }

             </style>";
        }
        $output .= "<style>
                     {$addon_id} li{
                         list-style-type: none;
                       }  
                       {$addon_id} .er_type{
                          max-height: 0;
                          overflow: hidden;
                          transition: all 0.6s ease-out;
                       }
             </style>";


        // 二级导航在上面
        if ($nav_position == 'nav-top') {
            $output .= "<script>
//                    let nav_dhsub=jQuery('{$addon_id} .jwpf-nav-custom div.page_n').outerHeight(true);
//                    jQuery('{$addon_id} .jwpf-nav-custom .er_type').css('cssText','top:'+nav_dhsub+'px !important');
                    
                    
                    jQuery('{$addon_id} .jwpf-nav-custom>li').click(function (){
                        // 有二级导航
                            if(jQuery(this).find('ul').hasClass('er_type')){
                                 console.log(69697777)
                               jQuery('.er_type').css('max-height','0'); 
//                                //展开状态
//                                console.log(jQuery(this).next().css('max-height'))
                                jQuery(this).find('.er_type').css('max-height','1000px');
                            }else{
                                // 没有二级导航
                                console.log(6969)
                                jQuery('.er_type').css('cssText','max-height:0 !important;');  
                            }
                        }) 
                         jQuery('{$addon_id} .jwpf-nav-custom>li').mouseleave(function (){
                             console.log(5858)
                             jQuery(this).find(' .er_type').css('cssText','max-height:0 !important;'); 
                        }) 

                </script>";
        } else {
            // 二级导航在两侧
            $output .= "<script> 
                        jQuery('{$addon_id} div.page_n').click(function (){
                            if(jQuery(this).next().hasClass('er_type')){
//                            jQuery({$addon_id} 'div.page_n').next().css('max-height','0'); 
                                //展开状态
                                console.log(jQuery(this).next().css('max-height'))
                                if(jQuery(this).next().css('max-height')!='0px'){
                                     jQuery(this).next().css('max-height','0');
                                }else{
                                    jQuery(this).next().css('max-height','1000px');
                                }
                            }
                        }) 
                        // 开始有二级导航
                        if(jQuery('{$addon_id} .jwpf-nav-custom>li').eq(0).find('ul').length > 0){
                          jQuery('{$addon_id} .jwpf-nav-custom>li').eq(0).find('ul').css('max-height','1000px');
                        }
             </script>";
        }

        return $output;

    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';
        $nav_color2 = (isset($settings->nav_color2) && $settings->nav_color2) ? 'color: ' . $settings->nav_color2 . '!important;' : '';
        $nav_bg_color2 = (isset($settings->nav_bg_color2) && $settings->nav_bg_color2) ? 'background-color: ' . $settings->nav_bg_color2 . '!important;' : '';


        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';

        $tab_style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $style = '';
        $style .= (isset($settings->active_tab_color) && $settings->active_tab_color) ? 'color: ' . $settings->active_tab_color . ';' : '';
        $style .= (isset($settings->active_tab_border_width) && trim($settings->active_tab_border_width)) ? 'border-width: ' . $settings->active_tab_border_width . ';border-style: solid;' : '';
        $style .= (isset($settings->active_tab_border_color) && $settings->active_tab_border_color) ? 'border-color: ' . $settings->active_tab_border_color . ';' : '';
        $style2 = '';
        $style2 .= (isset($settings->active_tab_color2) && $settings->active_tab_color2) ? 'color: ' . $settings->active_tab_color2 . ';' : '';
        $style2 .= (isset($settings->active_tab_border_width2) && trim($settings->active_tab_border_width2)) ? 'border-width: ' . $settings->active_tab_border_width2 . ';border-style: solid;' : '';
        $style2 .= (isset($settings->active_tab_border_color2) && $settings->active_tab_border_color2) ? 'border-color: ' . $settings->active_tab_border_color2 . ';' : '';

        //Font style
        $font_style = '';
        $font_style .= (isset($settings->nav_fontsize) && $settings->nav_fontsize) ? 'font-size: ' . $settings->nav_fontsize . 'px;' : '';
        $font_style .= (isset($settings->nav_lineheight) && $settings->nav_lineheight) ? 'line-height: ' . $settings->nav_lineheight . 'px;' : '';
        //二级导航Font style
        $font_style2 = '';
        $font_style2 .= (isset($settings->nav_fontsize2) && $settings->nav_fontsize2) ? 'font-size: ' . $settings->nav_fontsize2 . 'px;' : '';
        $font_style2 .= (isset($settings->nav_lineheight2) && $settings->nav_lineheight2) ? 'line-height: ' . $settings->nav_lineheight2 . 'px;' : '';

        //Font style object
        $nav_font_style = (isset($settings->nav_font_style) && $settings->nav_font_style) ? $settings->nav_font_style : '';

        if (isset($nav_font_style->underline) && $nav_font_style->underline) {
            $font_style .= 'text-decoration:underline;';
        }
        if (isset($nav_font_style->italic) && $nav_font_style->italic) {
            $font_style .= 'font-style:italic;';
        }
        if (isset($nav_font_style->uppercase) && $nav_font_style->uppercase) {
            $font_style .= 'text-transform:uppercase;';
        }
        if (isset($nav_font_style->weight) && $nav_font_style->weight) {
            $font_style .= 'font-weight:' . $nav_font_style->weight . ';';
        }
        $nav_border = (isset($settings->nav_border) && trim($settings->nav_border)) ? $settings->nav_border : '';
        if (strpos($nav_border, 'px')) {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border-width: ' . $settings->nav_border . ';border-style:solid;' : '';
        } else {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border: ' . $settings->nav_border . 'px solid;' : '';
        }
        $font_style .= (isset($settings->nav_border_color) && $settings->nav_border_color) ? 'border-color: ' . $settings->nav_border_color . ';' : '';
        $font_style .= (isset($settings->nav_color) && $settings->nav_color) ? 'color: ' . $settings->nav_color . ';' : '';
        $font_style .= (isset($settings->nav_bg_color) && $settings->nav_bg_color) ? 'background-color: ' . $settings->nav_bg_color . ';' : '';
        $font_style .= (isset($settings->nav_border_radius) && $settings->nav_border_radius) ? 'border-radius: ' . $settings->nav_border_radius . 'px;' : '';
        $font_style .= (isset($settings->nav_padding) && trim($settings->nav_padding)) ? 'padding: ' . $settings->nav_padding . ';' : '';

        $font_style_sm = (isset($settings->nav_fontsize_sm) && $settings->nav_fontsize_sm) ? 'font-size: ' . $settings->nav_fontsize_sm . 'px;' : '';
        $font_style_sm .= (isset($settings->nav_padding_sm) && trim($settings->nav_padding_sm)) ? 'padding: ' . $settings->nav_padding_sm . ';' : '';
        $font_style_sm .= (isset($settings->nav_lineheight_sm) && $settings->nav_lineheight_sm) ? 'line-height: ' . $settings->nav_lineheight_sm . 'px;' : '';

        $font_style_xs = (isset($settings->nav_fontsize_xs) && $settings->nav_fontsize_xs) ? 'font-size: ' . $settings->nav_fontsize_xs . 'px;' : '';
        $font_style_xs .= (isset($settings->nav_padding_xs) && trim($settings->nav_padding_xs)) ? 'padding: ' . $settings->nav_padding_xs . ';' : '';
        $font_style_xs .= (isset($settings->nav_lineheight_xs) && $settings->nav_lineheight_xs) ? 'line-height: ' . $settings->nav_lineheight_xs . 'px;' : '';

        //Nav Width
        $nav_width = (isset($settings->nav_width) && $settings->nav_width) ? $settings->nav_width : 30;
        $nav_width_sm = (isset($settings->nav_width_sm) && $settings->nav_width_sm) ? $settings->nav_width_sm : 30;
        $nav_width_xs = (isset($settings->nav_width_xs) && $settings->nav_width_xs) ? $settings->nav_width_xs : 30;
        //二级导航width
        $nav_width2 = (isset($settings->nav_width2) && $settings->nav_width2) ? $settings->nav_width2 : 30;
        $nav_width2_sm = (isset($settings->nav_width2_sm) && $settings->nav_width2_sm) ? $settings->nav_width2_sm : 30;
        $nav_width2_xs = (isset($settings->nav_width2_xs) && $settings->nav_width2_xs) ? $settings->nav_width2_xs : 30;

        //        字体背景高度
        //        字体背景高度
        $pro_font_color_bg_height_sm = (isset($settings->pro_font_color_bg_height_sm) && $settings->pro_font_color_bg_height_sm) ? $settings->pro_font_color_bg_height_sm : 40;
        $pro_font_color_bg_height_xs = (isset($settings->pro_font_color_bg_height_xs) && $settings->pro_font_color_bg_height_xs) ? $settings->pro_font_color_bg_height_xs : 40;
        //Nav Margin
        $nav_margin = (isset($settings->nav_margin) && trim($settings->nav_margin)) ? 'padding: ' . $settings->nav_margin . ';' : 'padding: 0px 0px 5px 0px;';
        $nav_margin_kj = explode(" ", $nav_margin);
        $nav_margin_sm = (isset($settings->nav_margin_sm) && trim($settings->nav_margin_sm)) ? 'padding: ' . $settings->nav_margin_sm . ';' : '';
        $nav_margin_xs = (isset($settings->nav_margin_xs) && trim($settings->nav_margin_xs)) ? 'padding: ' . $settings->nav_margin_xs . ';' : '';
        //Nav Margin
        $nav_margin2 = (isset($settings->nav_margin2) && trim($settings->nav_margin2)) ? 'padding: ' . $settings->nav_margin2 . ';' : 'padding: 0px 0px 5px 0px;';
        $nav_margin2_sm = (isset($settings->nav_margin2_sm) && trim($settings->nav_margin2_sm)) ? 'padding: ' . $settings->nav_margin2_sm . ';' : '';
        $nav_margin2_xs = (isset($settings->nav_margin2_xs) && trim($settings->nav_margin2_xs)) ? 'padding: ' . $settings->nav_margin2_xs . ';' : '';


        //Font style object
        $nav_font_style2 = (isset($settings->nav_font_style2) && $settings->nav_font_style2) ? $settings->nav_font_style2 : '';

        if (isset($nav_font_style2->underline) && $nav_font_style2->underline) {
            $font_style2 .= 'text-decoration:underline;';
        }
        if (isset($nav_font_style2->italic) && $nav_font_style2->italic) {
            $font_style2 .= 'font-style:italic;';
        }
        if (isset($nav_font_style2->uppercase) && $nav_font_style2->uppercase) {
            $font_style2 .= 'text-transform:uppercase;';
        }
        if (isset($nav_font_style2->weight) && $nav_font_style2->weight) {
            $font_style2 .= 'font-weight:' . $nav_font_style2->weight . ';';
        }
        $nav_border2 = (isset($settings->nav_border2) && trim($settings->nav_border2)) ? $settings->nav_border2 : '';
        if (strpos($nav_border2, 'px')) {
            $font_style2 .= (isset($settings->nav_border2) && trim($settings->nav_border2)) ? 'border-width: ' . $settings->nav_border2 . ';border-style:solid;' : '';
        } else {
            $font_style2 .= (isset($settings->nav_border2) && trim($settings->nav_border2)) ? 'border: ' . $settings->nav_border2 . 'px solid;' : '';
        }
        $font_style2 .= (isset($settings->nav_border_color2) && $settings->nav_border_color2) ? 'border-color: ' . $settings->nav_border_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_color2) && $settings->nav_color2) ? 'color: ' . $settings->nav_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_bg_color2) && $settings->nav_bg_color2) ? 'background-color: ' . $settings->nav_bg_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_border_radius2) && $settings->nav_border_radius2) ? 'border-radius: ' . $settings->nav_border_radius2 . 'px;' : '';
        $font_style2 .= (isset($settings->nav_padding2) && trim($settings->nav_padding2)) ? 'padding: ' . $settings->nav_padding2 . ';' : '';

        $font_style2_sm = (isset($settings->nav_fontsize2_sm) && $settings->nav_fontsize2_sm) ? 'font-size: ' . $settings->nav_fontsize2_sm . 'px;' : '';
        $font_style2_sm .= (isset($settings->nav_padding2_sm) && trim($settings->nav_padding2_sm)) ? 'padding: ' . $settings->nav_padding2_sm . ';' : '';
        $font_style2_sm .= (isset($settings->nav_lineheight2_sm) && $settings->nav_lineheight2_sm) ? 'line-height: ' . $settings->nav_lineheight2_sm . 'px;' : '';

        $font_style2_xs = (isset($settings->nav_fontsize2_xs) && $settings->nav_fontsize2_xs) ? 'font-size: ' . $settings->nav_fontsize2_xs . 'px;' : '';
        $font_style2_xs .= (isset($settings->nav_padding2_xs) && trim($settings->nav_padding2_xs)) ? 'padding: ' . $settings->nav_padding2_xs . ';' : '';
        $font_style2_xs .= (isset($settings->nav_lineheight2_xs) && $settings->nav_lineheight2_xs) ? 'line-height: ' . $settings->nav_lineheight2_xs . 'px;' : '';


        //Nav Gutter
        if ($nav_position == 'nav-right') {
            $nav_gutter_right = isset($settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_sm = isset($settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_xs = isset($settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';

            $nav_gutter_left = isset($settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_sm = isset($settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_xs = isset($settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';
        } else {
            $nav_gutter_right = isset($settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_sm = isset($settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_xs = isset($settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';

            $nav_gutter_left = isset($settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_sm = isset($settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_xs = isset($settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';
        }

        //Content Style
        $content_style = '';
        $content_style .= (isset($settings->content_backround) && $settings->content_backround) ? 'background-color: ' . $settings->content_backround . ';' : '';
        $content_style .= (isset($settings->content_border) && $settings->content_border) ? 'border: ' . $settings->content_border . 'px solid;' : '';
        $content_style .= (isset($settings->content_color) && $settings->content_color) ? 'color: ' . $settings->content_color . ';' : '';
        $content_style .= (isset($settings->content_border_color) && $settings->content_border_color) ? 'border-color: ' . $settings->content_border_color . ';' : '';
        $content_style .= (isset($settings->content_border_radius) && $settings->content_border_radius) ? 'border-radius: ' . $settings->content_border_radius . 'px;' : '';
        $content_style .= (isset($settings->content_margin) && trim($settings->content_margin)) ? 'margin: ' . $settings->content_margin . ';' : '';
        $content_style .= (isset($settings->content_padding) && trim($settings->content_padding)) ? 'padding: ' . $settings->content_padding . ';' : '';
        $content_style .= (isset($settings->content_lineheight) && $settings->content_lineheight) ? 'line-height: ' . $settings->content_lineheight . 'px;' : '';
        //Font style object
        $content_font_style = (isset($settings->content_font_style) && $settings->content_font_style) ? $settings->content_font_style : '';
        if (isset($content_font_style->underline) && $content_font_style->underline) {
            $content_style .= 'text-decoration:underline;';
        }
        if (isset($content_font_style->italic) && $content_font_style->italic) {
            $content_style .= 'font-style:italic;';
        }
        if (isset($content_font_style->uppercase) && $content_font_style->uppercase) {
            $content_style .= 'text-transform:uppercase;';
        }
        if (isset($content_font_style->weight) && $content_font_style->weight) {
            $content_style .= 'font-weight:' . $content_font_style->weight . ';';
        }
        //Content tablet style
        $content_style_sm = (isset($settings->content_margin_sm) && trim($settings->content_margin_sm)) ? 'margin: ' . $settings->content_margin_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_padding_sm) && $settings->content_padding_sm) ? 'padding: ' . $settings->content_padding_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_lineheight_sm) && $settings->content_lineheight_sm) ? 'line-height: ' . $settings->content_lineheight_sm . 'px;' : '';

        //Content Mobile style
        $content_style_xs = (isset($settings->content_margin_xs) && trim($settings->content_margin_xs)) ? 'margin: ' . $settings->content_margin_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_padding_xs) && $settings->content_padding_xs) ? 'padding: ' . $settings->content_padding_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_lineheight_xs) && $settings->content_lineheight_xs) ? 'line-height: ' . $settings->content_lineheight_xs . 'px;' : '';
        //Box shadow
        $show_boxshadow = (isset($settings->show_boxshadow) && $settings->show_boxshadow) ? $settings->show_boxshadow : '';
        $box_shadow = '';
        if ($show_boxshadow) {
            $box_shadow .= (isset($settings->shadow_horizontal) && $settings->shadow_horizontal) ? $settings->shadow_horizontal . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_vertical) && $settings->shadow_vertical) ? $settings->shadow_vertical . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_blur) && $settings->shadow_blur) ? $settings->shadow_blur . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_spread) && $settings->shadow_spread) ? $settings->shadow_spread . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_color) && $settings->shadow_color) ? $settings->shadow_color : 'rgba(0, 0, 0, .5)';
        }
        //Icon Style
        $icon_style = '';
        $icon_style .= (isset($settings->icon_fontsize) && $settings->icon_fontsize) ? 'font-size: ' . $settings->icon_fontsize . 'px;' : '';
        $icon_style .= (isset($settings->icon_margin) && trim($settings->icon_margin)) ? 'margin: ' . $settings->icon_margin . ';' : '';
        $icon_style .= (isset($settings->icon_color) && $settings->icon_color) ? 'color: ' . $settings->icon_color . ';' : '';

        $icon_style_sm = (isset($settings->icon_fontsize_sm) && $settings->icon_fontsize_sm) ? 'font-size: ' . $settings->icon_fontsize_sm . 'px;' : '';
        $icon_style_sm .= (isset($settings->icon_margin_sm) && trim($settings->icon_margin_sm)) ? 'margin: ' . $settings->icon_margin_sm . ';' : '';

        $icon_style_xs = (isset($settings->icon_fontsize_xs) && $settings->icon_fontsize_xs) ? 'font-size: ' . $settings->icon_fontsize_xs . 'px;' : '';
        $icon_style_xs .= (isset($settings->icon_margin_xs) && trim($settings->icon_margin_xs)) ? 'margin: ' . $settings->icon_margin_xs . ';' : '';

        //Image Style
        $image_style = '';
        $image_style .= (isset($settings->image_height) && $settings->image_height) ? 'height: ' . $settings->image_height . 'px;' : '';
        $image_style .= (isset($settings->image_width) && $settings->image_width) ? 'width: ' . $settings->image_width . 'px;' : '';
        $image_style .= (isset($settings->image_margin) && trim($settings->image_margin)) ? 'margin: ' . $settings->image_margin . ';' : '';

        $image_style_sm = '';
        $image_style_sm .= (isset($settings->image_height_sm) && $settings->image_height_sm) ? 'height: ' . $settings->image_height_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_width_sm) && $settings->image_width_sm) ? 'width: ' . $settings->image_width_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_margin_sm) && trim($settings->image_margin_sm)) ? 'margin: ' . $settings->image_margin_sm . ';' : '';

        $image_style_xs = '';
        $image_style_xs .= (isset($settings->image_height_xs) && $settings->image_height_xs) ? 'height: ' . $settings->image_height_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_width_xs) && $settings->image_width_xs) ? 'width: ' . $settings->image_width_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_margin_xs) && trim($settings->image_margin_xs)) ? 'margin: ' . $settings->image_margin_xs . ';' : '';


        // min start
        $nav_block_positon = (isset($settings->nav_block_positon) && $settings->nav_block_positon) ? $settings->nav_block_positon . ';' : '';
        $nav_font_position = (isset($settings->nav_font_position) && $settings->nav_font_position) ? $settings->nav_font_position : 'flex-start';

        // min end--------
        //Css output
        $css = '';
        $css .= $addon_id . ' .jwpf-nav-custom a {';
        $css .= 'display:block;';
        $css .= '}';
        if ($tab_style == 'pills') {
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-pills > li.active > a,' . $addon_id . ' .jwpf-nav-pills > li.active > a:hover,' . $addon_id . ' .jwpf-nav-pills > li.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
        } else if ($tab_style == 'lines') {
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'border-bottom-color: ' . $settings->active_tab_bg . ';' : '';
            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-lines > li.active > a,' . $addon_id . ' .jwpf-nav-lines > li.active > a:hover,' . $addon_id . ' .jwpf-nav-lines > li.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
        } else if ($tab_style == 'custom') // 自定义样式
        {
            //Active Nav style
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            $style2 .= (isset($settings->active_tab_bg2) && $settings->active_tab_bg2) ? 'background-color: ' . $settings->active_tab_bg2 . ';' : '';

            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-custom li.active > a,' . $addon_id . ' .jwpf-nav-custom li.active > a:hover,' . $addon_id . ' .jwpf-nav-custom li.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
            if ($style2) {
                $css .= $addon_id . ' .jwpf-nav-custom .er_type li.active > a,' . $addon_id . ' .jwpf-nav-custom .er_type li.active > a:hover,' . $addon_id . ' .jwpf-nav-custom .er_type li.active > a:focus {';
                $css .= 'background-color: ' . $settings->active_tab_bg2 . ' !important;';
                $css .= 'color: ' . $settings->active_tab_color2 . ' !important;';
                $css .= 'border-width: ' . $settings->active_tab_border_width2 . ' !important;';
                $css .= 'border-color: ' . $settings->active_tab_border_color2 . ' !important;';
                $css .= 'border-style: solid !important;';
                $css .= '}';
            }
            $css .= $addon_id . ' .jwpf-nav-custom {';
            $css .= 'width: ' . $nav_width . '%;';
            $css .= $nav_gutter_right;
            $css .= '}';
            $css .= $addon_id . ' .er_type{';
            $css .= 'width: ' . $nav_width2 . '%;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            $css .= 'width:' . (100 - $nav_width) . '%;';
            $css .= $nav_gutter_left;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style;
            $css .= 'box-shadow:' . $box_shadow . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style;
            $css .= 'box-shadow:' . $box_shadow . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type a {';
            $css .= $font_style2;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type a {';
            $css .= $nav_color2;
            $css .= $nav_bg_color2;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= ''.$nav_margin_kj[0].''.$nav_margin_kj[1].' '.$nav_margin_kj[2].' '.$nav_margin_kj[3].' '.$nav_margin_kj[4].'';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style;
            $css .= '}';
            //Nav Hover Style
            $hover_style = '';
            $hover_style .= (isset($settings->hover_tab_color) && $settings->hover_tab_color) ? 'color: ' . $settings->hover_tab_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_border_width) && trim($settings->hover_tab_border_width)) ? 'border-width: ' . $settings->hover_tab_border_width . ';border-style: solid;' : '';
            $hover_style .= (isset($settings->hover_tab_border_color) && $settings->hover_tab_border_color) ? 'border-color: ' . $settings->hover_tab_border_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_bg) && $settings->hover_tab_bg) ? 'background-color: ' . $settings->hover_tab_bg . ';' : '';
            //Nav Hover Style
            $hover_style2 = '';
            $hover_style2 .= (isset($settings->hover_tab_color2) && $settings->hover_tab_color2) ? 'color: ' . $settings->hover_tab_color2 . ' !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_border_width2) && trim($settings->hover_tab_border_width2)) ? 'border-width: ' . $settings->hover_tab_border_width2 . ';border-style: solid !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_border_color2) && $settings->hover_tab_border_color2) ? 'border-color: ' . $settings->hover_tab_border_color2 . ' !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_bg2) && $settings->hover_tab_bg2) ? 'background-color: ' . $settings->hover_tab_bg2 . ' !important;' : '';

            if ($hover_style) {
                $css .= $addon_id . ' .jwpf-nav-custom li > a:hover,' . $addon_id . ' .jwpf-nav-custom li > a:focus {';
                $css .= $hover_style;
                $css .= '}';
            }
            if ($hover_style2) {
                $css .= $addon_id . ' .jwpf-nav-custom .er_type li a:hover,' . $addon_id . ' .jwpf-nav-custom .er_type li a:focus {';
                $css .= $hover_style2;
                $css .= '}';
            }

            //Icon hover and active color
            $icon_color_hover = (isset($settings->icon_color_hover) && $settings->icon_color_hover) ? 'color: ' . $settings->icon_color_hover . ';' : '';
            if ($icon_color_hover) {
                $css .= $addon_id . ' .jwpf-nav-custom li > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_hover;
                $css .= '}';
            }
            $icon_color_active = (isset($settings->icon_color_active) && $settings->icon_color_active) ? 'color: ' . $settings->icon_color_active . ';' : '';
            if ($icon_color_active) {
                $css .= $addon_id . ' .jwpf-nav-custom li.active > a > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li.active > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li.active > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_active;
                $css .= '}';
            }
        }
        if (!empty($font_style_sm) || !empty($pro_font_color_bg_height_sm) || !empty($nav_width_sm) || !empty($content_style_sm) || !empty($nav_margin_sm) || !empty($image_style_sm)) {
            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type{';
            if (!empty($nav_width2_sm)) {
                $css .= 'width: ' . $nav_width2_sm . '%;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_sm)) {
                $css .= 'width: ' . $nav_width_sm . '%;';
            }


            $css .= $nav_gutter_right_sm;
            $css .= '}';
            $css .= $addon_id . ' .pr_list_id .jwpf-article-info-wrap {';
            if (!empty($pro_font_color_bg_height_sm)) {
                $css .= 'height: ' . $pro_font_color_bg_height_sm . 'px !important;';
                $css .= 'line-height: ' . $pro_font_color_bg_height_sm . 'px !important;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_sm) && $nav_width_sm != 100) {
                $css .= 'width:' . (100 - $nav_width_sm) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_sm;
            $css .= '}';

            $css .= '}';
        }
        if (!empty($font_style_xs) || !empty($pro_font_color_bg_height_xs) || !empty($nav_width_xs) || !empty($content_style_xs) || !empty($nav_margin_xs) || !empty($image_style_xs)) {
            $css .= '@media (max-width: 767px) {';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type{';
            if (!empty($nav_width2_xs)) {
                $css .= 'width: ' . $nav_width2_xs . '%;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_xs)) {
                $css .= 'width: ' . $nav_width_xs . '%;';
            }
            $css .= $nav_gutter_right_xs;
            $css .= '}';
            $css .= $addon_id . ' .pr_list_id .jwpf-article-info-wrap {';
            if (!empty($pro_font_color_bg_height_xs)) {
                $css .= 'height: ' . $pro_font_color_bg_height_xs . 'px !important;';
                $css .= 'line-height: ' . $pro_font_color_bg_height_xs . 'px !important;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_xs) && $nav_width_xs != 100) {
                $css .= 'width:' . (100 - $nav_width_xs) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_xs;
            $css .= '}';

            $css .= '}';
        }
        // 除自定义导航外的背景颜色 以及导航块的位置
        if ($tab_style != 'custom') {
            $style .= (isset($settings->nav_all_bg_color) && $settings->nav_all_bg_color) ? 'background-color: ' . $settings->nav_all_bg_color . ';' : '';
            $style .= (isset($settings->nav_all_positon) && $settings->nav_all_positon) ? 'justify-content: ' . $settings->nav_all_positon . ';' : '';
            $nav_wrap = (isset($settings->nav_wrap) && $settings->nav_wrap) ?$settings->nav_wrap : '';
            if ($style) {
                $css .= $addon_id . ' .minUl {';
                if($nav_wrap!=0){//换行
                $css .= 'display:block;';
                }else{
                $css .= 'display:flex;';
                }
                $css .= $style;
                $css .= '}';
            }
        }
        $css .= $addon_id . ' .jwpf-nav-custom a {';
        $css .= 'width: 100%;';
        $css .= 'height: 100%;';
        $css .= 'display: flex;';
        $css .= 'justify-content: ' . $nav_font_position . ';';
        $css .= '}';
        if ($nav_position == 'nav-top') {
            $css .= $addon_id . ' .minUl {';
            $css .= 'flex-wrap:wrap;';
//            $css .= 'width:' . $nav_width . '% !important;';
            $css .= 'width:100% !important;';
            $css .= '}';

            $css .= $addon_id . ' .minBox {';
            $css .= 'flex-direction: column;';
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-nav-custom {';
            $css .= 'width:100%;display: flex;padding-right:0px;justify-content:' . $nav_block_positon;
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-tab-custom-content{';
            $css .= 'width:100%;padding-left:0px;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li .er_type{';
            $css .= 'position: absolute;';
            $css .= 'left: 0;';
            $css .= 'z-index: 99999;';
            $css .= '}';

            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_sm;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'justify-content: ' . $nav_font_position . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= 'width:' . $nav_width_sm . '%;';
            $css .= '}';
            $css .= '}';

            $css .= '@media (max-width: 768px) {';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_xs;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'justify-content: ' . $nav_font_position . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= 'width:' . $nav_width_xs . '%;';
            $css .= '}';
            $css .= '}';

        }

        $css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
        $page2_tab_fontcolor = (isset($settings->page2_tab_fontcolor)) ? $settings->page2_tab_fontcolor : '#000';
        $page2_tab_bordercolor = (isset($settings->page2_tab_bordercolor)) ? $settings->page2_tab_bordercolor : '#2a68a7';
        $page2_tab_bgcolor = (isset($settings->page2_tab_bgcolor)) ? $settings->page2_tab_bgcolor : '#ffffff';

        $page2_tab_cur_bgcolor = (isset($settings->page2_tab_cur_bgcolor)) ? $settings->page2_tab_cur_bgcolor : '#ffffff';
        $page2_tab_cur_fontcolor = (isset($settings->page2_tab_cur_fontcolor)) ? $settings->page2_tab_cur_fontcolor : '#000';
        $page2_tab_cur_bordercolor = (isset($settings->page2_tab_cur_bordercolor)) ? $settings->page2_tab_cur_bordercolor : '#2a68a7';

        $page2_tab_bgcolor_hov = (isset($settings->page2_tab_bgcolor_hov)) ? $settings->page2_tab_bgcolor_hov : '#ffffff';
        $page2_tab_fontcolor_hov = (isset($settings->page2_tab_fontcolor_hov)) ? $settings->page2_tab_fontcolor_hov : '#000';
        $page2_tab_bordercolor_hov = (isset($settings->page2_tab_bordercolor_hov)) ? $settings->page2_tab_bordercolor_hov : '#000';

        $show_page_col = (isset($settings->show_page_col)) ? $settings->show_page_col : 0;

        if ($show_page_col == 1) {
            $css .= "
		        /* 省略号翻页样式 */
                    {$addon_id} .page_plug{
                    margin: 0 auto;
                }
                {$addon_id} .page_plug a.current {
                    background: {$page2_tab_cur_bgcolor};
                    color: {$page2_tab_cur_fontcolor};
                    border: 1px solid {$page2_tab_cur_bordercolor};
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                }
                {$addon_id} .page_plug a:hover{
                background: {$page2_tab_bgcolor_hov};
                color:{$page2_tab_fontcolor_hov};
                border: 1px solid {$page2_tab_bordercolor_hov};
                }
                {$addon_id} .page_num {
                    color:{$page2_tab_fontcolor};
                    border: 1px solid {$page2_tab_bordercolor};
                width: 30px;
                height: 30px;
                line-height: 30px;
                background:{$page2_tab_bgcolor}; 
                }
                
                
		";
        }else{
            $css .= "
		        /* 省略号翻页样式 */
                    {$addon_id} .page_plug{
                    margin: 0 auto;
                }
                {$addon_id} .page_plug a.current {
                    background: #2a68a7;
                    color: #fff;
                    border: 1px solid #2a68a7;
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                }
                {$addon_id} .page_plug a:hover{
                    background: #2a68a7;
                    color: #fff;
                    border: 1px solid #2a68a7;
                }
                {$addon_id} .page_num {
                    color:#000;
                    border: 1px solid #2a68a7;
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    background:#fff; 
                }
                

		";
        }
        return $css;
    }

    public static function getTemplate()
    {
//        $settings = self::getTemplate->addon->settings;
//        echo "<pre>";
//        var_dump($settings);
//        exit();
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        };

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $article_helper;

        $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, "type1", 1, 10);
        $html_list = [];
        foreach ($categories_list as $key => $val) {
            $this_model = new JwpagefactoryAddonProduct_tab(self::$this_obj);
            $html_list[] = ['content' => $this_model->render2($val['tag_id'], self::$this_obj)];
        };
        $html_json = json_encode($html_list);

        $output = '
         <# 
          var type_parent = (typeof data.type_parent !== "undefined" && data.type_parent) ? data.type_parent : "type1";
         var navPosition = data.nav_position || "nav-left";
        var box_shadow = "";
        var jw_tab_item = ' . json_encode($categories_list) . ';
        console.log(jw_tab_item)
        var jw_tab_content = ' . $html_json . ';
         var type = (typeof data.type !== "undefined" && data.type) ? data.type : "nav";
        #>
        <style type="text/css">
            <# 
               
                if(data.show_boxshadow){
                    box_shadow += (!_.isEmpty(data.shadow_horizontal) && data.shadow_horizontal) ?  data.shadow_horizontal + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_vertical) && data.shadow_vertical) ?  data.shadow_vertical + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_blur) && data.shadow_blur) ?  data.shadow_blur + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_spread) && data.shadow_spread) ?  data.shadow_spread + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_color) && data.shadow_color) ?  data.shadow_color : "rgba(0, 0, 0, .5)";
                }
                if(data.style == "pills"){
            #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:hover,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:focus{
                        color: {{ data.active_tab_color }};
                        background-color: {{ data.active_tab_bg }};
                    }
            <# } #>
            <# if(data.nav_position != "nav-top"){ #>
             #jwpf-addon-{{ data.id }} .minUl {
                    display:block ;
                }
             <# } #>
            <# if(data.nav_position == "nav-top"){ #>
              #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                    position: absolute;
                    left: 0;
                    top:36px;
                    max-height:1000px;
                    z-index: 999;
                    overflow: hidden;
                    width:100%;
                }    
                #jwpf-addon-{{ data.id }} .page_n{
                    width:100%;
                }
            
            
               #jwpf-addon-{{ data.id }} .minUl {
                    display:flex !important;
                    flex-wrap:wrap !important;
                     justify-content:{{data.nav_block_positon}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top {
                    display:flex;
                    flex-direction:column;
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top .jwpf-tab-custom-content {
                    width:100%;
                    padding-left:0px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    display:flex;
                    padding-right:0px !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                    display:flex;
                } 

                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                  
                    width:100%;
                }
                
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width.sm}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                       
                        width:100%;
                        display:flex;
                    } 
                }
                @media (max-width: 768px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width.xs}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                       
                        width:100%;
                        display:flex;
                    } 
                }
            <# } #>
            <# if(data.style == "lines"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-bottom-color: {{ data.active_tab_bg }};
                }
            <# } #>
            
             #jwpf-addon-{{ data.id }} li{
                 list-style-type: none;
             }
            <# if(data.fix_img_height=="1"){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-img-responsive{
                         height:{{data.fix_img_height_input}}px !important;
                       }
                       @media (max-width: 992px) {
                     #jwpf-addon-{{ data.id }} .jwpf-img-responsive{
                        height:{{data.fix_img_height_input_m}}px !important;
                        }
                    }
            <# } #>
            
              #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap{
                            background:{{data.normal_font_color}} !important;
                        }
            
            
             #jwpf-addon-{{ data.id }} .jwpf-addon-article {
                            position: relative;
                            overflow: hidden;
                            border:{{data.normal_border_width}}px solid {{data.normal_border_color}};
                        }  
            
            
            
            <# if (data.style == "custom") { #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:focus{
                    color: {{ data.hover_tab_color }};
                    border-width: {{ data.hover_tab_border_width }};
                    border-color: {{ data.hover_tab_border_color }};
                    background-color: {{ data.hover_tab_bg }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type  li > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li > a:focus{
                    color: {{ data.hover_tab_color2 }};
                    border-width: {{ data.hover_tab_border_width2 }};
                    border-color: {{ data.hover_tab_border_color2 }};
                    background-color: {{ data.hover_tab_bg2 }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-width: {{ data.active_tab_border_width }};
                    border-color: {{ data.active_tab_border_color }};
                    background-color: {{ data.active_tab_bg }};
                }  
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a:focus{
                    color: {{ data.active_tab_color2 }};
                    border-width: {{ data.active_tab_border_width2 }};
                    border-color: {{ data.active_tab_border_color2 }};
                    background-color: {{ data.active_tab_bg2 }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_hover }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_active }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        flex: 1;
                    <# if(_.isObject(data.nav_margin)){ #>
                        padding: {{data.nav_margin.md}};
                    <# } else { #>
                        padding: {{data.nav_margin}};
                    <# } #>
                } 
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                    <# if(_.isObject(data.nav_margin2)){ #>
                        padding: {{data.nav_margin2.md}};
                    <# } else { #>
                        padding: {{data.nav_margin2}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                    <# if(_.isObject(data.nav_fontsize)){ #>
                        font-size: {{data.nav_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_fontsize}}px;
                    <# } #>
                    <# if(_.endsWith(data.nav_border, "x")) { #>
                        border-width: {{data.nav_border}};
                        border-style: solid;
                    <# } else { #>
                        border: {{_.trim(data.nav_border, " ")}}px solid;
                    <# } #>
                    border-color: {{data.nav_border_color}};
                    color: {{data.nav_color}};
                    background-color: {{data.nav_bg_color}};
                    border-radius: {{data.nav_border_radius}}px;
                     display:flex;
                    justify-content:{{data.nav_font_position}};
                    <# if(_.isObject(data.nav_padding)){ #>
                        padding: {{data.nav_padding.md}};
                    <# } else { #>
                        padding: {{data.nav_padding}};
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.nav_font_family}};
                    <# if(_.isObject(data.nav_lineheight)){ #>
                        line-height:{{data.nav_lineheight.md}}px;
                    <# }
                    if(_.isObject(data.nav_font_style)){
                        if(data.nav_font_style.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.nav_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.nav_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.nav_font_style.weight){
                        #>
                            font-weight:{{data.nav_font_style.weight}};
                        <# }
                    } #>
                }#jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li a {
                    <# if(_.isObject(data.nav_fontsize2)){ #>
                        font-size: {{data.nav_fontsize2.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_fontsize2}}px;
                    <# } #>
                    <# if(_.endsWith(data.nav_border2, "x")) { #>
                        border-width: {{data.nav_border2}};
                        border-style: solid;
                    <# } else { #>
                        border: {{_.trim(data.nav_border2, " ")}}px solid;
                    <# } #>
                    border-color: {{data.nav_border_color2}};
                    color: {{data.nav_color2}};
                    background-color: {{data.nav_bg_color2}};
                    border-radius: {{data.nav_border_radius2}}px;
                     display:flex;
                    justify-content:{{data.nav_font_position2}};
                    <# if(_.isObject(data.nav_padding2)){ #>
                       
                    <# } else { #>
                       
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.nav_font_family2}};
                    <# if(_.isObject(data.nav_lineheight2)){ #>
                        line-height:{{data.nav_lineheight2.md}}px;
                    <# }
                    if(_.isObject(data.nav_font_style2)){
                        if(data.nav_font_style2.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.nav_font_style2.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.nav_font_style2.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.nav_font_style2.weight){
                        #>
                            font-weight:{{data.nav_font_style2.weight}};
                        <# }
                    } #>
                }
       
                #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                    <# if(data.icon_color){ #>
                        color:{{data.icon_color}};
                    <# } #>
                    <# if(_.isObject(data.icon_fontsize)){ #>
                        font-size: {{data.icon_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.icon_fontsize}}px;
                    <# } #>
                    <# if(_.isObject(data.icon_margin)){ #>
                        margin: {{data.icon_margin.md}};
                    <# } else { #>
                        margin: {{data.icon_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                    <# if(_.isObject(data.image_height)){ #>
                        height: {{data.image_height.md}}px;
                    <# } else { #>
                        height: {{data.image_height}}px;
                    <# }
                    if(_.isObject(data.image_width)){
                    #>
                        width: {{data.image_width.md}}px;
                    <# } else { #>
                        width: {{data.image_width}}px;
                    <# }
                    if(_.isObject(data.image_margin)){
                    #>
                        margin: {{data.image_margin.md}};
                    <# } else { #>
                        margin: {{data.image_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    <# if(_.isObject(data.nav_width)){ #>
                        width: {{data.nav_width.md}}%;
                    <# } else { #>
                        width: {{data.nav_width}}%;
                    <# }
                    if(navPosition == "nav-right") {
                    #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-left: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-left: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else { 
                        if(_.isObject(data.nav_gutter)){
                    #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# }
                    } #>
                } 
        
              
                 #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                  <# if(_.isObject(data.nav_width2)){ #>
                        width: {{data.nav_width2.md}}%;
                 <# } else { #>
                        width: {{data.nav_width2}}%;
                 <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                    <# if(_.isObject(data.nav_width)){ #>
                        width: {{100-data.nav_width.md}}%;
                    <# } else { #>
                        width: {{100-data.nav_width}}%; 
                    <# } #>
                    <# if(navPosition == "nav-right"){ #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else { #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-left: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-left: {{data.nav_gutter}}px;
                        <# } #>
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                    background-color: {{data.content_backround}};
                    border: {{data.content_border}}px solid;
                    border-color: {{data.content_border_color}};
                    border-radius: {{data.content_border_radius}}px;
                    color: {{data.content_color}};
                    <# if(_.isObject(data.content_padding)){ #>
                        padding: {{data.content_padding.md}};
                    <# } else { #>
                        padding: {{data.content_padding}};
                    <# } #>
                    <# if(_.isObject(data.content_margin)){ #>
                        margin: {{data.content_margin.md}};
                    <# } else { #>
                        margin: {{data.content_margin}};
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.content_font_family}};
                    <# if(_.isObject(data.content_fontsize)){ #>
                        font-size:{{data.content_fontsize.md}}px;
                    <# }
                    if(_.isObject(data.content_lineheight)){ #>
                        
                    <# }
                    if(_.isObject(data.content_font_style)){
                        if(data.content_font_style.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.content_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.content_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.content_font_style.weight){
                        #>
                            font-weight:{{data.content_font_style.weight}};
                        <# }
                    } #>
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.sm}};
                            
                        <# } #>
                    } 
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                        <# if(_.isObject(data.nav_margin2)){ #>
                            padding: {{data.nav_margin2.sm}};
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.sm}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.sm}};
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: {{data.nav_width.sm}}%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                                <# if(_.isObject(data.nav_width2)) { #>
                                   width: {{data.nav_width2.sm}}%;
                                <# } else { #>
                                  width: {{data.nav_width2}}%;
                                <# } #>
                            
          
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.sm !== "100"){ #>
                                width: {{100-data.nav_width.sm}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.sm}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.sm}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>
                            
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.sm}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.sm}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.sm}};
                        <# } #>
                    }
                }
                @media (max-width: 767px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.xs}};
                        <# } #>
                    } 
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                        <# if(_.isObject(data.nav_margin2)){ #>
                            padding: {{data.nav_margin2.xs}};
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                      
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.xs}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.xs}};
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: {{data.nav_width.xs}}%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        } 
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                         <# if(_.isObject(data.nav_width2)) { #>
                                  width: {{data.nav_width2.xs}}%;
                                <# } else { #>
                                  width: {{data.nav_width2}}%;
                                <# } #>
                            
               
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.xs !== "100"){ #>
                                width: {{100-data.nav_width.xs}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.xs}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.xs}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>
                           
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.xs}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.xs}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.xs}};
                        <# } #>
                    }
                    
                }
            <# } #>

            <# if (data.style != "custom") { #>
                #jwpf-addon-{{ data.id }} .minUl {
                    display:flex;
                    justify-content:{{data.nav_all_positon}};
                    background-color:{{data.nav_all_bg_color}};
                }
                <# } #>
                
                
                      *{
                          padding: 0;
                          margin: 0;
                        }
                        #jwpf-addon-{{ data.id }} .page_plug{
                          width: 90%;
                          margin: 5px auto;
                          text-align: center;
                        }
                       #jwpf-addon-{{ data.id }} .page_plug a{
                          padding: 3px 8px;
                          border: 1px solid #2a68a7;
                          margin-right: 5px;
                          text-decoration: none;
                          color: #2a68a7;
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-addon-article {
                            position: relative;
                            overflow: hidden;
                            border:{{data.normal_border_width}}px solid {{data.normal_border_color}};
                        }  
                        #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover {

                            border:{{data.hover_border_width}}px solid {{data.hover_border_color}};
                        }  

                        #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap{
                            background:{{data.normal_font_color}} !important;
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap{
                            bottom: 0px;
                        }
                        #jwpf-addon-{{ data.id }} .introtext_type2{
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 1;
                            overflow: hidden;
                        }
           
                
                
                
                
                
                
                
            <# if(data.pro_type=="type1"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap {
                    background:{{data.normal_font_color}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap {
                    background:{{data.hover_font_color}} !important;
                }
                
               #jwpf-addon-{{ data.id }} .jwpf-addon-article .jwpf-article-info-wrap p {
                    color:{{data.pro_font_color}} !important;
                    font-size:{{data.content_fontsize_bt}}px !important;
                }
                 #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap p {
                     
                
                    color:{{data.pro_font_color_hover}} !important;
                }
          
               
                
                 #jwpf-addon-{{ data.id }} .pr_list_id a{
                    display:block;
                 }

            <# if(!data.show_title) { #>
                #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                    text-align: center;
                    transition: bottom 0.3s;
                    position: absolute;
                     <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                            bottom: -{{data.pro_font_color_bg_height.md}}px;
                             height: {{data.pro_font_color_bg_height.md}}px;
                            line-height: {{data.pro_font_color_bg_height.md}}px;
                      <# } else { #>
                               bottom: -{{data.pro_font_color_bg_height}}px;
                                height: {{data.pro_font_color_bg_height}}px;
                                line-height: {{data.pro_font_color_bg_height}}px;
                      <# } #>
                  
                    width:100%;
                  }
           <# }else { #>
                #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                           
                             height: {{data.pro_font_color_bg_height.md}}px;
                            line-height: {{data.pro_font_color_bg_height.md}}px;
                      <# } else { #>
                             
                                height: {{data.pro_font_color_bg_height}}px;
                                line-height: {{data.pro_font_color_bg_height}}px;
                      <# } #>
                    text-align: center;       
                    width:100%;
                    position: absolute;
                    bottom:0;
                }
            <# } #>
            @media (max-width: 992px) {
                  #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                     bottom:0;
                }
            }
              @media (max-width: 768px) {
                     #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap {
                        <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                        height:{{data.pro_font_color_bg_height.xs}}px !important;
                        line-height:{{data.pro_font_color_bg_height.xs}}px !important;
                        <# } else { #>
                            height:{{data.pro_font_color_bg_height}}px !important;
                             line-height:{{data.pro_font_color_bg_height}}px !important;
                       <# } #>
                    }
           
                }
                 @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap {
                    <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                        height:{{data.pro_font_color_bg_height.sm}}px !important;
                        line-height:{{data.pro_font_color_bg_height.sm}}px !important;
                    <# } else { #>
                        height:{{data.pro_font_color_bg_height}}px !important;
                        line-height:{{data.pro_font_color_bg_height}}px !important;
                   <# } #>

                }

          <# } #>
          <# if(data.pro_type=="type2"){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap {
                            background:{{data.normal_font_color}} !important;
                        }
                        
                        #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 {
                            background:{{data.normal_font_color}} !important;
                        } 
                        #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap-type2 {
                            background:{{data.hover_font_color}} !important;
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 .title_type2 {
                            color:{{data.pro_font_color_type2_title}};
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 .introtext_type2 {
                            color:{{data.pro_font_color_type2_intext}};
                        }
                       #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .title_type2 {
                            color:{{data.pro_font_color_type2_title_hover}} !important;
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .introtext_type2 {
                            color:{{data.pro_font_color_type2_intext_hover}} !important;
                        }
      
                       <# if(data.img_animated=="animated2"){ #>
                             #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap:hover img{
                                    transform:scale(1.2);
                                    -ms-transform:scale(1.2);
                                   -moz-transform:scale(1.2);
                                   -webkit-transform:scale(1.2);
                                   -o-transform:scale(1.2);
                             }
                        <# } #>
                         #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2{
                               padding: 0 10px 15px 10px;
                        }
                        #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .title_type2{
                            color: {{data.pro_font_color_type2_title}};
                            font-size:{{data.pro_font_title_size_type2}}px;
                            
                            
                        }
                        #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .introtext_type2{
                            color: {{data.pro_font_color_type2_intext}};
                            font-size:{{data.pro_font_intext_size_type2}}px;
                            text-overflow: ellipsis;
                              display: -webkit-box;
                              -webkit-box-orient: vertical;
                              -webkit-line-clamp: 2;
                              overflow: hidden;
                        }
                       #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap{
                           transition: all .5s;
                           -ms-transition: all .5s;
                           -moz-transition: all .5s;
                           -webkit-transition: all .5s;
                           -o-transition: all .5s;
                            display: block;
                        }  
                        #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap .img_box_type2{
                            overflow: hidden;
                        }  
                        #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap img{
                            transition: all .5s;
                             -ms-transition: all .5s;
                           -moz-transition: all .5s;
                           -webkit-transition: all .5s;
                           -o-transition: all .5s;
                        }
                        #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap:hover{
                               box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                               -moz-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                               -webkit-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                        }
            <# } #>
          
            <# if(data.pro_type=="type3"){ #>
                * {
                    margin: 0;
                    padding: 0;
                }
            
                #jwpf-addon-{{ data.id }} .pro3Ul {
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                }
            
                #jwpf-addon-{{ data.id }} .pro3li {
                    list-style: none;
                    width: {{100/data.columns}}%;
                    height: 400px;
                    position: relative;
                }
            
                #jwpf-addon-{{ data.id }} .pro3li .proImg {
                    width: 100%;
                    height: 100%;
                }
            
                #jwpf-addon-{{ data.id }} .liHoverBox {
                    display: none;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    background: {{data.type3bgclolor}};
                    text-align: center;
                    left: 0;
                    top: 0;
                    box-sizing: border-box;
                }
            
                #jwpf-addon-{{ data.id }} .hoverIcon {
                    margin: 0px auto 40px auto;
                    width: 45px;
                    height: 45px;
                }
            
                #jwpf-addon-{{ data.id }} .hoverCon {
                    font-size: {{data.type3fontsize}}px;
                    line-height: 50px;
                    padding-top: 15px;
                    margin-top: 0;
                    color: {{data.type3fontcolor}};
                    text-align: center;
                    background: url("/components/com_jwpagefactory/addons/product_list/assets/images/11_1103.jpg") no-repeat center top;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                #jwpf-addon-{{ data.id }} .pro3li:hover .liHoverBox {
                    display: flex;
                    justify-content: center;
                    flex-flow: column;
                }
                @media (max-width: 992px) {
                        #jwpf-addon-{{ data.id }} .liHoverBox {
                            display: none;
                            flex-flow: column;
                            justify-content: center;
                    } 
                }
                @media (max-width: 992px) {
                        #jwpf-addon-{{ data.id }} .hoverCon {
                         line-height: initial;
                    } 
                }
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .hoverIcon {
                       margin: 0px auto 8px auto;
                        width: 20px;
                        height: 20px;
                    }
                }

                @media (max-width:768px) {
                    #jwpf-addon-{{ data.id }} .pro3li {
                        width: {{100/data.columns_xs}}%;
                        margin-bottom: 10px;
            
                    }
                }


                <# if(data.fix_img_height){ #>

                       #jwpf-addon-{{ data.id }} .pro3li{
                             height:{{data.fix_img_height_input}}px !important;
                           }
                           @media (max-width: 768px) {
                         #jwpf-addon-{{ data.id }} .pro3li{
                            height:{{data.fix_img_height_input_m}}px !important;
                            }
                        }

                        <# } #>
            <# } #>
          
             #jwpf-addon-{{ data.id }} .jwpf-article-img-wrap{
            position:relative;
            cursor: pointer;
            display:block;
            }
                




               

        </style>
        <div class="jwpf-addon jwpf-addon-tab {{ data.class }}">
            <# if( !_.isEmpty( data.title )){ #>
                <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
            <# } 
            let icon_postion = (data.nav_icon_postion == \'top\' || data.nav_icon_postion == \'bottom\') ? \'tab-icon-block\' : \'\';
           
            #>
            <div class="jwpf-addon-content  jwpf-tab jwpf-{{data.style}}-tab jwpf-tab-{{navPosition}}">
            
                <ul class="jwpf-nav minUl jwpf-nav-{{ data.style }}">
             
                    <#             
                        _.each(jw_tab_item, function(tab, key){
                            var active = "";
                            if(key == 0){
                                active = "active";
                            }

                            var title = "";
                            var subtitle = "";
                            var icon_top ="";
                            var icon_bottom = "";
                            var icon_right = "";
                            var icon_left = "";
                            var icon_block = "";
                            
                            var image_top ="";
                            var image_bottom = "";
                            var image_right = "";
                            var image_left = "";
                            
                            
                            if(tab.title){
                                title = tab.title;
                            }
                            if(tab.subtitle){
                                subtitle = `<span class="jwpf-tab-subtitle">${tab.subtitle}</span>`;
                            }
                            if(data.nav_icon_postion == "top" || data.nav_icon_postion == "bottom" || data.nav_image_postion == "top" || data.nav_image_postion == "bottom"){
                                icon_block = "tab-img-or-icon-block-wrap";
                            }
                    #>
                           
                           
                     <# if(data.style=="custom"&&data.type_parent!="type1"){ #>
                             <li class="{{ active }}">
                                <# if(data.dh_db_img=="k"){ #>
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg"  />
                                <# } #>
                                <a data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} 我是一级导航 {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}</a>
                            </li>
                            <li>
                                <div class="page_n">
                                    <a class="nav_n" href="javascript:void(0)">我是一级导航</a>
                                </div>
                                <ul class="er_type">
                                    <li class="page_n">
                                        <a data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} 我是二级导航 {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}</a>
                                    </li>
                                </ul>
                           </li>
                            
                     <# }else{ #>
                           <li class="{{ active }}">
                           
                                <a data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}"><# if(data.dh_db_img=="k"){ #>
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg" style="width:{{ data.dh_db_img_width }}px;height:{{ data.dh_db_img_height }}px;border-radius:{{ data.dh_db_img_border }}px" />
                                <# } #>{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} 我是一级导航 {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}</a>
                            </li>
                     <# } #>
                       
                    <# }); #>
                </ul>
                <# if(data.pro_type=="type1"){ #>
                    <div class="pr_list_id jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                <# } #>
                <# if(data.pro_type=="type2"){ #>
                    <div class="pr_list_id_type2 jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                <# } #>
                               
                <# if(data.pro_type=="type3"){ #>
                    <div class=" jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                <# } #>
                 
                
                        <# _.each(jw_tab_content, function(tab, key){ #>
                                <#
                                    var active = "";
                                    if(key == 0){
                                        active = "active in";
                                    }
                                #>
                                
                            <div id="jwpf-tab-{{ data.id }}{{ key }}" class="jwpf-tab-pane jwpf-fade {{ active }}">
                                   
                                       
                                        
                                        <# if(data.pro_type=="type1"){ #>
                                            <div style="display: flex !important;justify-content: space-between;">
                                                <div style="width:32% !important" class="jwpf-addon-article">
                                                    <a class="jwpf-article-img-wrap pr_box">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                        <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                            <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                        </div>
                                                    </a>
                                                </div>
                                                
                                                <div style="width:32% !important" class="jwpf-addon-article">
                                                    <a class="jwpf-article-img-wrap pr_box">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                        <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                            <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                        </div>
                                                    </a>
                                                </div>
                                               <div style="width:32% !important" class="jwpf-addon-article">
                                                    <a class="jwpf-article-img-wrap pr_box">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                        <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                            <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                        </div>
                                                    </a>
                                                </div>
                                            </div>
                                        <# } #>
                                    
                                     
                                        <# if(data.pro_type=="type2"){ #>
                                         <div style="display: flex !important;justify-content: space-between;">
                                            <div style="width:32% !important" class="jwpf-addon-article">
                                                 <a class="jwpf-article-img-wrap pr_box">
                                                    <div class="img_box_type2">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                    </div>
                                                    <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                        <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                       <# if (data.show_intro) { #>
                                                            <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                       <# } #>
                                                    </div>
                                                </a>
                                            </div>
                                            <div style="width:32% !important" class="jwpf-addon-article">
                                                 <a class="jwpf-article-img-wrap pr_box">
                                                    <div class="img_box_type2">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                    </div>
                                                    <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                        <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                       <# if (data.show_intro) { #>
                                                            <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                       <# } #>
                                                    </div>
                                                </a>
                                            </div>
                                            <div style="width:32% !important" class="jwpf-addon-article">
                                                 <a class="jwpf-article-img-wrap pr_box">
                                                    <div class="img_box_type2">
                                                        <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                    </div>
                                                    <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                        <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                       <# if (data.show_intro) { #>
                                                            <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                       <# } #>
                                                    </div>
                                                </a>
                                            </div>
                                         </div>
                                       <# } #>

                                       <# if(data.pro_type=="type3"){ #>
                                         <div class="pro3">
                                            <ul class="pro3Ul">
                                               <li class="pro3li">
                                                   <a>
                                                      <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                      <div class="liHoverBox">
                                                          <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                          <div class="hoverCon">我是标题</div>
                                                      </div>
                                                   </a>
                                               </li>
                                               <li class="pro3li">
                                                   <a>
                                                      <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                      <div class="liHoverBox">
                                                          <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                          <div class="hoverCon">我是标题</div>
                                                      </div>
                                                   </a>
                                               </li>
                                               <li class="pro3li">
                                                   <a>
                                                      <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                      <div class="liHoverBox">
                                                          <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                          <div class="hoverCon">我是标题</div>
                                                      </div>
                                                   </a>
                                               </li>
                                            </ul>
                                        </div>

                                        <# } #>
                                </div>
                            <# }); #>
                       
                       
          
                   
                
                   
                </div>
            </div>
        </div>
        ';

        return $output;
    }

    public function render2($goods_catid, $this_obj)
    {
        $settings = $this_obj->addon->settings;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $cpcatid = $_GET['cpcatid'] ?? 0;
        $page_view_name = isset($_GET['view']);
        $addon_id = '#jwpf-addon-' . $this_obj->addon->id;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

        // Addon options
        $resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        if ($cpcatid == $goods_catid) {
            $page = $_GET['page'];
        } else {
            $page = 1;
        }

        $pro_font_color = (isset($settings->pro_font_color)) ? $settings->pro_font_color : '#fff';
        $pro_font_color_hover = (isset($settings->pro_font_color_hover)) ? $settings->pro_font_color_hover : '#fff';
        $pro_font_color_bg_height = (isset($settings->pro_font_color_bg_height)) ? $settings->pro_font_color_bg_height : 40;
        $pro_font_color_type2_title = (isset($settings->pro_font_color_type2_title)) ? $settings->pro_font_color_type2_title : '#000';
        $pro_font_color_type2_title_hover = (isset($settings->pro_font_color_type2_title_hover)) ? $settings->pro_font_color_type2_title_hover : '#000';
        $pro_font_title_size_type2 = (isset($settings->pro_font_title_size_type2)) ? $settings->pro_font_title_size_type2 : 14;
        $pro_font_color_type2_intext = (isset($settings->pro_font_color_type2_intext)) ? $settings->pro_font_color_type2_intext : '#000';
        $pro_font_color_type2_intext_hover = (isset($settings->pro_font_color_type2_intext_hover)) ? $settings->pro_font_color_type2_intext_hover : '#000';
        $pro_font_intext_size_type2 = (isset($settings->pro_font_intext_size_type2)) ? $settings->pro_font_intext_size_type2 : 14;
        $img_animated = (isset($settings->img_animated)) ? $settings->img_animated : 'animated1';
        $box_type2_shadow_color = (isset($settings->box_type2_shadow_color)) ? $settings->box_type2_shadow_color : '#fff';
        $box_type2_shadow_x = (isset($settings->box_type2_shadow_x)) ? $settings->box_type2_shadow_x : 0;
        $box_type2_shadow_Y = (isset($settings->box_type2_shadow_Y)) ? $settings->box_type2_shadow_Y : 0;
        $box_type2_shadow_mh = (isset($settings->box_type2_shadow_mh)) ? $settings->box_type2_shadow_mh : 0;
        $box_type2_shadow_kz = (isset($settings->box_type2_shadow_kz)) ? $settings->box_type2_shadow_kz : 0;
        $title_font_position = (isset($settings->title_font_position)) ? $settings->title_font_position : 'center';


        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        $columns_xs = (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : 2;
        $content_fontsize_bt = (isset($settings->content_fontsize_bt) && $settings->content_fontsize_bt) ? $settings->content_fontsize_bt : 14;

        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        $intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
        $hide_thumbnail = (isset($settings->hide_thumbnail)) ? $settings->hide_thumbnail : 0;
        $link_articles = (isset($settings->link_articles)) ? $settings->link_articles : 0;
        $link_catid = (isset($settings->catid)) ? implode(',', array_filter($settings->catid)) : '';
        $company_id = $company_id ? $company_id : ((isset($settings->company_id)) ? $settings->company_id : 0);
        $site_id = $site_id ? $site_id : ((isset($settings->site_id)) ? $settings->site_id : 0);
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
        $show_title = (isset($settings->show_title) && $settings->show_title) ? $settings->show_title : false;
        $pro_title_height_type2 = (isset($settings->pro_title_height_type2)) ? $settings->pro_title_height_type2 : 50;
        $pro_title_height_type2_padding = (isset($settings->pro_title_height_type2_padding) && trim($settings->pro_title_height_type2_padding)) ? 'padding:' . $settings->pro_title_height_type2_padding . ';' : '';

        // 选择的列数  这里的使用$columns是因为列表以pc和平板为主
        $num = 12 / $columns;
        // 宽度
        $width = 100 / $columns_xs;

        $all_articles_btn_text = (isset($settings->all_articles_btn_text) && $settings->all_articles_btn_text) ? $settings->all_articles_btn_text : 'See all posts';
        $all_articles_btn_class = (isset($settings->all_articles_btn_size) && $settings->all_articles_btn_size) ? ' jwpf-btn-' . $settings->all_articles_btn_size : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? ' jwpf-btn-' . $settings->all_articles_btn_type : ' jwpf-btn-default';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_shape) && $settings->all_articles_btn_shape) ? ' jwpf-btn-' . $settings->all_articles_btn_shape : ' jwpf-btn-rounded';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? ' jwpf-btn-' . $settings->all_articles_btn_appearance : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_block) && $settings->all_articles_btn_block) ? ' ' . $settings->all_articles_btn_block : '';
        $all_articles_btn_icon = (isset($settings->all_articles_btn_icon) && $settings->all_articles_btn_icon) ? $settings->all_articles_btn_icon : '';
        $all_articles_btn_icon_position = (isset($settings->all_articles_btn_icon_position) && $settings->all_articles_btn_icon_position) ? $settings->all_articles_btn_icon_position : 'left';
        $hover_border_width = isset($settings->hover_border_width) ? $settings->hover_border_width : 2;
        $hover_border_color = (isset($settings->hover_border_color) && $settings->hover_border_color) ? $settings->hover_border_color : 'rgba(21, 228, 116, 0)';
        $normal_border_width = isset($settings->normal_border_width) ? $settings->normal_border_width : 2;
        $normal_border_color = (isset($settings->normal_border_color) && $settings->normal_border_color) ? $settings->normal_border_color : 'rgba(21, 228, 116, 0)';
        $hover_font_color = (isset($settings->hover_font_color) && $settings->hover_font_color) ? $settings->hover_font_color : '#666';
        $normal_font_color = (isset($settings->normal_font_color) && $settings->normal_font_color) ? $settings->normal_font_color : '#666';
        $type3fontsize = (isset($settings->type3fontsize)) ? $settings->type3fontsize : '22';
        $type3fontcolor = (isset($settings->type3fontcolor)) ? $settings->type3fontcolor : '#fff';
        $type3bgclolor = (isset($settings->type3bgclolor)) ? $settings->type3bgclolor : 'rgba(0, 0, 0, 0.5)';
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;
        $page_location = (isset($settings->page_location)) ? $settings->page_location : 0;
        $page_top_location = (isset($settings->page_top_location)) ? $settings->page_top_location : 0;

        $page2_tab_fontcolor = (isset($settings->page2_tab_fontcolor)) ? $settings->page2_tab_fontcolor : '#ffffff';
        $page2_tab_bordercolor = (isset($settings->page2_tab_bordercolor)) ? $settings->page2_tab_bordercolor : '#2a68a7';
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';

        $output = "<style>
                        *{
                          padding: 0;
                          margin: 0;
                        }
                        {$addon_id} .page_plug{
                          width: 90%;
                          margin: 5px auto;
                          text-align: center;
                          color: #000;
                        }
                        {$addon_id} .page_plug a{
                          padding: 3px 8px;
                          border: 1px solid {$page2_tab_bordercolor};
                          margin-right: 5px;
                          text-decoration: none;
                          color:{$page2_tab_fontcolor};
                        }
                        {$addon_id} .jwpf-addon-article {
                            position: relative;
                            overflow: hidden;
                            border:{$normal_border_width}px solid {$normal_border_color};
                        }  
                        {$addon_id} .jwpf-addon-article:hover {

                            border:{$hover_border_width}px solid {$hover_border_color};
                        }  

                        {$addon_id} .jwpf-addon-article:hover  .jwpf-article-info-wrap{
                            background:{$normal_font_color} !important;
                        }
                        {$addon_id} .jwpf-addon-article:hover .jwpf-article-info-wrap{
                            bottom: 0px;
                        }
                        {$addon_id} .introtext_type2{
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 1;
                            overflow: hidden;
                        }
                        @media (max-width: 768px) {
                            .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} {
                                width:{$width}%;
                            }
                        }   
                    
                    ";

        //布局1
        if ($pro_type == 'type1') {
            $output .= "
            {$addon_id}  .jwpf-article-info-wrap {
                background:{$normal_font_color} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap {
                background:{$hover_font_color} !important;
            }
            
            {$addon_id}  .jwpf-addon-article .jwpf-article-info-wrap p {
                color:{$pro_font_color} !important;
            }
            
            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap p {
                color:{$pro_font_color_hover} !important;
            }
            
            
            ";
            if (!$show_title) {
                $output .= "  
                {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                    text-align: center;
                    transition: bottom 0.3s;
                    position: absolute;
                    bottom: -{$pro_font_color_bg_height}px;
                    height: {$pro_font_color_bg_height}px;
                    line-height: {$pro_font_color_bg_height}px;
                    width:100%;
                  }";
            } else {
                $output .= "
                {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                    height: {$pro_font_color_bg_height}px;
                    line-height: {$pro_font_color_bg_height}px;   
                    text-align: center;       
                    width:100%;
                    position: absolute;
                    bottom:0;
                }";
            }
            $output .= "
            @media (max-width: 992px) {
                  {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                     bottom:0;
                }
            }";
//            $output .= "
//            @media (min-width: 768px) and (max-width: 991px){
//            {$addon_id} .pr_list_id .jwpf-article-info-wrap{
//                height: ' . $pro_font_color_bg_height_sm . 'px !important;';
//                }
//            }";

        }
        //布局2
        if ($pro_type == 'type2') {
            $output .= "
            {$addon_id}  .jwpf-article-info-wrap {
                background:{$normal_font_color} !important;
            }
            
            {$addon_id}  .jwpf-article-info-wrap-type2 {
                background:{$normal_font_color} !important;
            }
            {$addon_id}  .jwpf-article-info-wrap-type2 .title_type2 {
                color:{$pro_font_color_type2_title};
            }
            {$addon_id}  .jwpf-article-info-wrap-type2 .introtext_type2 {
                color:{$pro_font_color_type2_intext};
            }
            {$addon_id}  .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .title_type2 {
                color:{$pro_font_color_type2_title_hover} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .introtext_type2 {
                color:{$pro_font_color_type2_intext_hover} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap-type2  {
                background:{$hover_font_color} !important;
            }   
            ";
            if ($img_animated == 'animated2') {
                $output .= "
                 {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover img{
                        transform:scale(1.2);
                        -ms-transform:scale(1.2);
                       -moz-transform:scale(1.2);
                       -webkit-transform:scale(1.2);
                       -o-transform:scale(1.2);
                 }";
            }
            $output .= "
            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2{
                   padding: 0 10px 15px 10px;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .title_type2{
                color: {$pro_font_color_type2_title};
                font-size:{$pro_font_title_size_type2}px;
                 height:{$pro_title_height_type2}px;
                 overflow: hidden;
                 {$pro_title_height_type2_padding};
            }

            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .introtext_type2{
                color: {$pro_font_color_type2_intext};
                font-size:{$pro_font_intext_size_type2}px;
                text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
            }
           {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap{
               transition: all .5s;
               -ms-transition: all .5s;
               -moz-transition: all .5s;
               -webkit-transition: all .5s;
               -o-transition: all .5s;
                display: block;
            }  
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap .img_box_type2{
                overflow: hidden;
            }  
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap img{
                transition: all .5s;
                 -ms-transition: all .5s;
               -moz-transition: all .5s;
               -webkit-transition: all .5s;
               -o-transition: all .5s;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover{
                   box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                   -moz-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                   -webkit-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
            }";
        }
        if ($pro_type == 'type3') {
            $output .= "
            * {
                margin: 0;
                padding: 0;
            }
        
            {$addon_id} .pro3Ul {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
            }
        
            {$addon_id} .pro3li {
                list-style: none;
                width: 100%;
                height: 400px;
                position: relative;
            }
        
            {$addon_id} .pro3li .proImg {
                width: 100%;
                height: 100%;
            }
        
            {$addon_id} .liHoverBox {
                display: none;
                position: absolute;
                width: 100%;
                height: 100%;
                background: {$type3bgclolor};
                text-align: center;
                left: 0;
                top: 0;
                box-sizing: border-box;
            }
        
            {$addon_id} .hoverIcon {
                margin: 0px auto 40px auto;
                width: 45px;
                height: 45px;
            }
        
            {$addon_id} .hoverCon {
                font-size: {$type3fontsize}px;
                line-height: 50px;
                padding-top: 15px;
                margin-top: 0;
                color: {$type3fontcolor};
                text-align: center;
                background: url('/components/com_jwpagefactory/addons/product_list/assets/images/11_1103.jpg') no-repeat center top;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            {$addon_id} .pro3li:hover .liHoverBox {
                display: flex;
                justify-content: center;
                flex-flow: column;
            }
                @media (max-width: 992px) {
                    {$addon_id} .liHoverBox {
                        display: flex;
                        flex-flow: column;
                        justify-content: center;
                } 
            }
            @media (max-width: 992px) {
                    {$addon_id} .hoverCon {
                     line-height: initial;
                } 
            }
            @media (max-width: 992px) {
                {$addon_id} .hoverIcon {
                   margin: 0px auto 8px auto;
                    width: 20px;
                    height: 20px;
                }
            }
            @media (max-width: 768px) {
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} {
                    max-width:{$width}%;
                flex: 0 0 {$width}%;
                }
            }
            @media (max-width:768px) {
                {$addon_id} .pro3li {
                    width: 100%;
                    margin-bottom: 10px;
        
                }
            }
            ";
            // 固定图片高度
            if ($fix_img_height) {

                $output .= "  
                                 {$addon_id} .pro3li{
                                     height:{$fix_img_height_input}px !important;
                                   }
                                   @media (max-width: 768px) {
                                 {$addon_id} .pro3li{
                                    height:{$fix_img_height_input_m}px !important;
                                    }
                                }
                       ";
            }

        }

        $output .= "</style>";
        require_once $article_helper;
        $items = JwpagefactoryHelperGoods::getGoodsList($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        $items_count = JwpagefactoryHelperGoods::getGoodsCount($ordering, $goods_catid, $include_subcat, $company_id, $site_id);

        if (!count($items)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';

            return $output;
        }

        if (count((array)$items)) {
            if ($pro_type == 'type1') {
                $output .= '<div class="pr_list_id jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        if ($resource == 'k2') {
                            if (isset($item->image_medium) && $item->image_medium) {
                                $image = $item->image_medium;
                            } elseif (isset($item->image_large) && $item->image_large) {
                                $image = $item->image_medium;
                            }
                        } else {
                            $image = $item->image_thumbnail;
                        }

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this_obj->get_image_placeholder($item->image_thumbnail);
                                $output .= '<a href="' . $item->link . '" itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this_obj->get_image_placeholder($image);
                                $output .= '<a class="jwpf-article-img-wrap pr_box" href="' . $item->link . '" itemprop="url"><img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                $output .= '<div style="background:' . $normal_font_color . '" class="jwpf-article-info-wrap pr_active">';
                                $output .= '<p style="margin:0;font-size: ' . $content_fontsize_bt . 'px;color:' . $pro_font_color . ';">' . $item->title . '</p>';
                                $output .= '</div>';
                                $output .= '</a>';
                            }
                        }
                    }


                    $output .= '</div>';
                    $output .= '</div>';
                }


//                    $output .= '</div>';


                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }


                    if (!empty($link_catid)) {
                        $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this_obj->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                    }
                }

                $output .= '</div>';
                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }

                    $output .= '<div style="position: relative;left: '.$page_location.'px; top:'.$page_top_location.'px;">';
                    $output .= '<div class="page_plug" >';
                    $page_count = $settings->page_count??0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count+1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';
                    $output .= "
                    <script>
                    </script>
                    ";
                }
                $output .= '</div>';
                $output .= '</div>';
            }

            if ($pro_type == 'type2') {
                $output .= '<div class="pr_list_id_type2 jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        $image = $item->image_thumbnail;

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this_obj->get_image_placeholder($item->image_thumbnail);
                                $output .= '<a href="' . $item->link . '" itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this_obj->get_image_placeholder($image);
                                $output .= '<a class="jwpf-article-img-wrap pr_box" href="' . $item->link . '" itemprop="url">';
                                $output .= '<div class="img_box_type2">';
                                $output .= '<img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                $output .= '</div>';
                                $output .= '<div style="background:' . $normal_font_color . '" class="jwpf-article-info-wrap-type2 pr_active">';
                                $output .= '<p class="title_type2" style="text-align: ' . $title_font_position . ';margin:0;">' . $item->title . '</p>';
                                // 显示简介
                                if ($show_intro) {
                                    $output .= '<div class="introtext_type2">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</div>';
                                }

                                $output .= '</div>';
                                $output .= '</a>';
                            }
                        }
                    }


                    $output .= '</div>';
                    $output .= '</div>';
                }


                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if (!empty($link_catid)) {
                        $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this_obj->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                    }
                }

                $output .= '</div>';


                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }

                    $output .= '<div>';
                    $output .= '<div class="page_plug">';
                    $page_count = $settings->page_count??0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count+1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';
                }
                $output .= '</div>';
                $output .= '</div>';
            }


            if ($pro_type == 'type3') {
                $output .= '<div class="pr_list_id_type2 jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $output .= ' <div class="pro3">';
                        $output .= ' <ul class="pro3Ul">';
                        $output .= '<li class="pro3li">';
                        $output .= '<a href="' . $item->link . '">';
                        $image = $item->image_thumbnail;
                        if (isset($image) && $image) {
                            $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                            $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
                        }
                        $output .= '<div class="liHoverBox">';
                        $output .= '<img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">';
                        $output .= '<div class="hoverCon">' . $item->title . '</div>';
                        $output .= '</div>';
                        $output .= '</a>';
                        $output .= '</li>';
                        $output .= '</ul>';
                        $output .= '</div>';


                    }
                    $output .= '</div>';
                    $output .= '</div>';
                }
                if ($show_page) {

                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div class="page_plug">';
                    // 判断是不是第一页
                    //    if($page != 1) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
                    //    }
                    $page_count = $settings->page_count??0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count+1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    // 判断是不是最后一页
                    //    if($page != $all_page) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
                    //    }

                    $output .= '</div>';
                    // $output .= '<div class="page_plug">共' . $items_count . '条</div>';

                }
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }

                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
            }


        }

        return $output;
    }

    public function scripts()
    {
        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/newnav.js');

        return $scripts;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();

        return $is_enabled;
    }

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");

        return $url;
    }


}