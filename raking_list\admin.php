<?php
/**
* <AUTHOR>
* @email        <EMAIL>
* @url          http://www.joomla.work
* @copyright    Copyright (c) 2010 - 2019 JoomWorker
* @license      GNU General Public License version 2 or later
* @date         2019/01/01 09:30
*/
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
   array(
       'type' => 'content',
       'addon_name' => 'raking_list',
       'title' => '排行榜',
       'desc' => '排行榜',
       'category' => '活动',
       'attr' => array(
           'general' => array(
               'admin_label' => array(
                   'type' => 'text',
                   'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                   'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                   'std' => ''
               ),
               'site_id' => array(
                   'std' => $site_id,
               ),
               'company_id' => array(
                   'std' => $company_id,
               ),
               'lxstyle' => array(
                   'type' => 'select',
                   'title' => '展示页面（首页/列表）',
                   'std' => 'homey',
                   'values' => array(
                       'homey' => '首页展示',
                       'listy' => '列表页展示',
                   )
               ),

              'gengduo' => array(
      					'type' => 'select',
      					'title' => '点击更多跳转页面',
      					'desc' => '显示页面模版',
      					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
      					'depends' => array(
                              array(
                                 'lxstyle', '=', 'homey'
                              )
                         	)
      				),

				//宽高
               'paved' => array(
                   'type' => 'checkbox',
                   'title' => '是否铺满容器',
                   'std' => 1,
               ),
               'width' => array(
                   'type' => 'slider',
                   'title' => '排行榜宽度',
                   'std' => 400,
                   'max' => 1000,
                   'min' => 200,
                   'responsive' => true,
                   'depends' => array(
                       array(
                           'paved', '=', 0
                       )
                   )
               ),
               // 搜索图标
               'seach_image' => array(
                   'type' => 'media',
                   'title' => '搜索小图标',
                   'std' => '/components/com_jwpagefactory/addons/raking_list/assets/images/sd.png'
               ),
               'is_gradient' => array(
                   'type' => 'checkbox',
                   'title' => '排行榜头部背景是否为渐变色',
                   'std' => 1
               ),
//                纯色的头部颜色
               'top_bgColor' => array(
                   'type' => 'color',
                   'title' => '排行榜头部背景颜色',
                   'std' => '#ffa',
                   'depends' => array(
                       array('is_gradient', '=', 0)
                   )
               ),
//                渐变的头部颜色
               'top_bgColor_left' => array(
                   'type' => 'color',
                   'title' => '排行榜头部背景颜色-左',
                   'std' => '#4fa5fc',
                   'depends' => array(
                       array('is_gradient', '=', 1)
                   )
               ),
               'top_bgColor_right' => array(
                   'type' => 'color',
                   'title' => '排行榜头部背景颜色-右',
                   'std' => '#3ea16a',
                   'depends' => array(
                       array('is_gradient', '=', 1)
                   )
               ),
               'top_tit' => array(
                   'type' => 'text',
                   'title' => '排行榜头部标题',
                   'std' => '培训排行榜',

               ),
               'top_titColor' => array(
                   'type' => 'color',
                   'title' => '排行榜头部标题颜色',
                   'std' => '#fff',
               ),
               'top_title_height' => array(
                   'type' => 'slider',
                   'title' => '排行榜头部高度',
                   'std' => 40,
                   'min' => 30,
                   'max' => 200,
                   'responsive' => true,
               ),
               'top_title_font_size' => array(
                   'type' => 'slider',
                   'title' => '排行榜头部字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 40,
                   'responsive' => true,
               ),

//              排行榜背景色
               'content_color' => array(
                   'type' => 'color',
                   'title' => '排行主体榜背景色',
                   'std' => '#F7F7F7'
               ),
//              筛选和搜索
//            padding
               'filter_padding' => array(
                   'type' => 'padding',
                   'title' => JText::_('筛选和搜索区域内边距'),
                   'std' => array('md' => '8px 16px 8px 16px', 'sm' => '8px 16px 8px 16px', 'xs' => '8px 16px 8px 16px'),
                   'responsive' => true,
               ),
//                筛选
               'filter_year_width' => array(
                   'type' => 'slider',
                   'title' => '年份筛选宽度',
                   'std' => 90,
                   'min' => 10,
                   'max' => 1000,
                   'responsive' => true,
               ),
               'filter_year_height' => array(
                   'type' => 'slider',
                   'title' => '年份筛选高度',
                   'std' => 38,
                   'min' => 10,
                   'max' => 200,
                   'responsive' => true,
               ),
               'filter_year_color' => array(
                   'type' => 'color',
                   'title' => '年份字体颜色',
                   'std' => '#72B19D',
               ),
               'filter_year_font_size' => array(
                   'type' => 'slider',
                   'title' => '年份字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 40,
                   'responsive' => true,
               ),
               'filter_year_background' => array(
                   'type' => 'color',
                   'title' => '年份筛选背景色',
                   'std' => '',
               ),
               'filter_year_border_color' => array(
                   'type' => 'color',
                   'title' => '年份筛选边框颜色',
                   'std' => '',
               ),
//              搜索输入框
               'search_input_width' => array(
                   'type' => 'slider',
                   'title' => '搜索框宽度',
                   'std' => array(
                       'md' => 600,
                       'sm' => 400,
                       'xs' => 200,
                   ),
                   'min' => 10,
                   'max' => 1000,
                   'responsive' => true,
               ),
               'search_input_height' => array(
                   'type' => 'slider',
                   'title' => '搜索框高度',
                   'std' => 35,
                   'min' => 10,
                   'max' => 200,
                   'responsive' => true,
               ),
               'search_input_radius' => array(
                   'type' => 'slider',
                   'title' => '搜索框圆角',
                   'std' => 50,
                   'min' => 10,
                   'max' => 100,
               ),
               'search_text_align' => array(
                   'type' => 'select',
                   'title' => '搜索框字体对齐方式',
                   'std' => 'center',
                   'values' => array(
                       'left' => '左对齐',
                       'center' => '居中',
                       'right' => '右对齐'
                   ),
                   'responsive' => true,
               ),
               'search_input_font_size' => array(
                   'type' => 'slider',
                   'title' => '搜索框字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 40,
                   'responsive' => true,
               ),
               'search_input_color' => array(
                   'type' => 'color',
                   'title' => '搜索框字体颜色',
                   'std' => '#999996',
               ),
               'search_input_background' => array(
                   'type' => 'color',
                   'title' => '搜索框背景颜色',
                   'std' => '#fff',
               ),
               'search_input_border_color' => array(
                   'type' => 'color',
                   'title' => '搜索框边框颜色',
                   'std' => '#ccc',
               ),
               'search_button_size' => array(
                   'type' => 'slider',
                   'title' => '搜索按钮大小',
                   'std' => 20,
                   'min' => 10,
                   'max' => 400,
                   'responsive' => true,
               ),
//                分割线
               'split_style' => array(
                   'type' => 'select',
                   'title' => '分割线样式',
                   'std' => 'solid',
                   'values' => array(
                       'solid' => JText::_('实线'),
                       'dashed' => JText::_('虚线'),
                       'dotted' => JText::_('点'),
                       'none' => JText::_('无'),

                   )
               ),
               'split_color' => array(
                   'type' => 'color',
                   'title' => '分割线颜色',
                   'std' => '#ddd',
               ),

//                排行榜
               'table_text_align' => array(
                   'type' => 'select',
                   'title' => '表格对齐方式',
                   'std' => 'center',
                   'values' => array(
                       'left' => '左对齐',
                       'center' => '居中',
                       'right' => '右对齐'
                   )
               ),
               'table_padding' => array(
                   'type' => 'padding',
                   'title' => JText::_('表格内边距'),
                   'std' => array('md' => '0px 16px 0px 16px', 'sm' => '0px 16px 0px 16px', 'xs' => '0px 16px 0px 16px'),
                   'responsive' => true,
               ),

               'table_head_line_height' => array(
                   'type' => 'slider',
                   'title' => '表头字体行高',
                   'std' => 35,
                   'min' => 10,
                   'max' => 60,
                   'responsive' => true,
               ),
               'table_head_font_size' => array(
                   'type' => 'slider',
                   'title' => '表头字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 40,
                   'responsive' => true,
               ),
               'table_head_font_color' => array(
                   'type' => 'color',
                   'title' => '表头字体颜色',
                   'std' => '#72B19D',
               ),
//                切换有无数据
               'has_data' => array(
                   'type' => 'buttons',
                   'title' => '有无数据时分别展示的内容',
                   'std' => 'yes',
                   'values' => array(
                       array(
                           'label' => '有数据',
                           'value' => 'yes'
                       ),
                       array(
                           'label' => '无数据',
                           'value' => 'no'
                       ),
                   ),
                   'tabs' => true,
               ),
               'table_body_line_height' => array(
                   'type' => 'slider',
                   'title' => '表格字体行高',
                   'std' => 35,
                   'min' => 10,
                   'max' => 40,
                   'depends' => array(
                       array('has_data', '=', 'yes')
                   ),
                   'responsive' => true,
               ),
               'table_body_font_size' => array(
                   'type' => 'slider',
                   'title' => '表格字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 40,
                   'depends' => array(
                       array('has_data', '=', 'yes')
                   ),
                   'responsive' => true,
               ),
               'table_body_font_color' => array(
                   'type' => 'color',
                   'title' => '表格字体颜色',
                   'std' => '#484848',
                   'depends' => array(
                       array('has_data', '=', 'yes')
                   ),
                   'responsive' => true,
               ),
//              暂无数据
               'no_data' => array(
                   'type' => 'text',
                   'title' => '暂无数据时的占位内容',
                   'std' => '暂无数据',
                   'depends' => array(
                       array('has_data', '=', 'no')
                   )
               ),
               'no_data_line_height' => array(
                   'type' => 'slider',
                   'title' => '暂无数据时的占位字体行高',
                   'std' => 35,
                   'min' => 10,
                   'max' => 40,
                   'depends' => array(
                       array('has_data', '=', 'no')
                   ),
                   'responsive' => true,
               ),
               'no_data_font_size' => array(
                   'type' => 'slider',
                   'title' => '暂无数据时的占位字体大小',
                   'std' => 16,
                   'min' => 10,
                   'max' => 100,
                   'depends' => array(
                       array('has_data', '=', 'no')
                   ),
                   'responsive' => true,
               ),
               'no_data_font_color' => array(
                   'type' => 'color',
                   'title' => '暂无数据时的占位字体颜色',
                   'std' => '#72B19D',
                   'depends' => array(
                       array('has_data', '=', 'no')
                   ),
               ),


               /*
                * 新加的左侧选项
                */

           ),
       )
   )
);
