<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'product_sidebar',
        'title' => JText::_('锚点侧边栏'),
        'desc' => JText::_('锚点侧边栏'),
        'category' => '常用插件',
        'attr' => array(
            'admin_label' => array(
                'type' => 'text',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                'std' => ''
            ),
            'part01' => array(
                'type' => 'separator',
                'title' => '锚点选项'
            ),
            'ps_tab_item_2' => array(
                'title' => JText::_('锚点内容'),
                'attr' => array(
                    'title' => array(
                        'type' => 'text',
                        'title' => JText::_('标题'),
                        'desc' => JText::_('标题'),
                        'std' => '点我'
                    ),
                    'text8' => array(
                        'type' => 'text',
                        'title' => JText::_('锚点id'),
                        'desc' => JText::_('请填写本页面中区块章节ID'),
                    ),
                ),
                'std' => array(
                    array(
                        'title' => '公司简介',
                        'text8' => ''
                    ),
                    array(
                        'title' => '资质荣誉',
                        'text8' => ''
                    ),
                    array(
                        'title' => '营销团队',
                        'text8' => ''
                    ),
                    array(
                        'title' => '合作伙伴',
                        'text8' => ''
                    )
                )
            ),
            'separator_options' => array(
                'type' => 'separator',
                'title' => '位置选项'
            ),
            'list_position' => array(
                'type' => 'select',
                'title' => '悬浮窗口位置',
                'values' => array(
                    'left_top' => '左上角',
                    'right_top' => '右上角',
                    'left_bottom' => '左下角',
                    'right_bottom' => '右下角',
                ),
                'std' => 'left_top'
            ),
            'list_pos_left' => array(
                'type' => 'slider',
                'title' => '列表左右位置(px)',
                'max' => 1000,
                'std' => 10,
            ),
            'list_pos_up' => array(
                'type' => 'slider',
                'title' => '列表垂直位置(px)',
                'max' => 1000,
                'std' => 10,
            ),
            'part02' => array(
                'type' => 'separator',
                'title' => '客服配置'
            ),
            'show_qq' => array(
                'type' => 'checkbox',
                'title' => '关闭在线客服',
                'std' => 0
            ),
            'qq' => array(
                'type' => 'text',
                'title' => JText::_('客服qq号'),
                'desc' => JText::_('客服qq号'),
                'depends' => array(
                    array('show_qq', '!=', 1)
                )
            ),
            'part04' => array(
                'type' => 'separator',
                'title' => '返回顶部配置'
            ),
            'show_top' => array(
                'type' => 'checkbox',
                'title' => '关闭返回顶部',
                'std' => 0
            ),
            'top_icon' => array(
                'type' => 'media',
                'title' => JText::_('返回顶部图标'),
                'depends' => array(
                    array('show_top', '!=', 1)
                ),
                'std' => 'https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png'
            ),
            'top_icon_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标滑过返回顶部图标'),
                'depends' => array(
                    array('show_top', '!=', 1)
                ),
                'std' => 'https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png'
            ),
            'part03' => array(
                'type' => 'separator',
                'title' => '锚点项设置'
            ),
            'tit_col1' => array(
                'type' => 'color',
                'title' => '锚点项背景颜色',
                'desc' => '',
                'std' => '#f5f5f5',
            ),
            'over_col2' => array(
                'type' => 'color',
                'title' => '锚点项滑过背景颜色',
                'desc' => '',
                'std' => '#00448F',
            ),
            'tit_col2' => array(
                'type' => 'color',
                'title' => '锚点项字体颜色',
                'desc' => '',
                'std' => '#333',
            ),
            'tit_col2_hover' => array(
                'type' => 'color',
                'title' => '锚点项滑过字体颜色',
                'desc' => '',
                'std' => '#fff',
            ),
            'text_size' => array(
                'type' => 'slider',
                'title' => '锚点项字体大小',
                'max' => 100,
                'std' => 14,
            ),
            'list_width' => array(
                'type' => 'slider',
                'title' => '锚点窗口宽度',
                'max' => 100,
                'unit' => true,
                'responsive' => true,
                'std' => array(
                    'md' => '10',
                    'unit' => '%'
                )
            ),
            'list_height' => array(
                'type' => 'slider',
                'title' => '锚点项高度',
                'max' => 100,
                'std' => 55,
            ),
        )
    )
);