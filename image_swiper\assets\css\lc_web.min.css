@charset "UTF-8";
.image-swiper * {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none; }

.image-swiper .lc-page {
  overflow: hidden;
  background: #fff; }

.image-swiper .lc-page img {
  border-width: 0;
  border: none;
  display: block;
  margin: 0 auto;
  max-width: 100%; }

.image-swiper .fl {
  float: left; }

.image-swiper .fr {
  float: right; }

.image-swiper .cb::before, .image-swiper .cb::after {
  display: block;
  content: "";
  height: 0;
  clear: both; }

.image-swiper .img_wrap img {
  width: 100%;
  height: 100%; }

.image-swiper .table {
  display: table;
  width: 100%;
  height: 100%; }

.image-swiper .table .table-cell {
  display: table-cell;
  width: 100%;
  height: 100%;
  vertical-align: middle; }

.image-swiper .lc_inner {
  width: 75%;
  margin: 0 auto; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc_inner {
    width: 93.33333%; } }

.image-swiper .lc-a-page1 {
  padding-top: 35.9375%;
  position: relative;
  background-size: cover;
  background-position: center top; }

.image-swiper .lc-a-page1 .lc_inner {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto; }

.image-swiper .lc-a-page1 .lc_inner .lc-touch {
  height: 100%;
  position: absolute;
  z-index: 2;
  right: 0;
  top: 0;
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s; }

.image-swiper .lc-a-page1 .lc_inner .lc-touch img {
  height: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
  max-width: 10000px; }

.image-swiper .lc-a-page1 .lc_inner .lc-text {
  height: 100%;
  width: 100%;
  position: relative;
  color: #fff; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 {
  top: 35.50725%;
  position: absolute;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
  font-size: 40px;
  line-height: 1.625; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 img {
  width: 100%; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .lc4 {
  font-size: 58px;
  top: 44.92754%;
  line-height: 1.41379;
  position: absolute;
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .lc-line-container {
  width: 100%;
  position: absolute;
  top: 59.27536%; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .line {
  height: 2px;
  width: 5.83333%;
  background: #fff;
  margin-bottom: 1.25%;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s; }

.image-swiper .lc-a-page1 .lc_inner .lc-text .text {
  font-size: 16px;
  line-height: 1.875;
  width: 33.75%;
  color: #fff;
  letter-spacing: 0.05em;
  -webkit-animation-delay: 1.2s;
  animation-delay: 1.2s; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 {
    font-size: 32px; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc4 {
    font-size: 46px; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 {
    font-size: 30px; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc4 {
    font-size: 43px; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc-line-container {
    width: 100%;
    position: absolute;
    top: 60.76812%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 {
    font-size: 26px; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc4 {
    font-size: 37px; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc-line-container {
    width: 100%;
    position: absolute; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .text {
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page1 {
    padding-top: 50.66667%;
    background-position: 62% top; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc3 {
    top: 39.47368%;
    font-size: 0.30rem;
    font-weight: bold; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc4 {
    font-size: 0.4rem;
    top: 50%;
    font-weight: bold; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .line {
    width: 0.84rem; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .lc-line-container {
    top: 67.10526%; }
  .image-swiper .lc-a-page1 .lc_inner .lc-text .text {
    display: none; } }

.image-swiper .lc-a-page2 {
  padding: 1.04167% 0;
  color: #333333;
  font-size: 26px; }

.image-swiper .lc-a-page2 .lc_inner {
  position: relative;
  height: 75px; }

.image-swiper .lc-a-page2 .lc-left {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }

.image-swiper .lc-a-page2 .lc-right {
  height: 100%; }

.image-swiper .lc-a-page2 .lc-tel {
  font-size: 24px; }

.image-swiper .lc-a-page2 .lc-text {
  font-size: 14px; }

.image-swiper .lc-a-page2 .lc-right-left {
  height: 100%; }

.image-swiper .lc-a-page2 .lc-line1 {
  width: 1px;
  height: 100%;
  background: #e6e6e6;
  margin: 0 60px; }

.image-swiper .lc-a-page2 .lc-right-right {
  height: 100%;
  width: 165px;
  position: relative; }

.image-swiper .lc-a-page2 .lc-right-right .lc-router-link {
  display: block;
  line-height: 44px;
  height: 44px;
  text-align: center;
  color: #fff;
  width: 100%;
  font-size: 14px;
  background: #4581fb;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto; }

.image-swiper .lc-a-page2 .lc-right-right .lc-router-link:hover {
  background: #3e74e2; }

.image-swiper .lc-a-page2 .lc-right-right .lc-router-link:hover:after {
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  content: ""; }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-a-page2 .lc-left {
    font-size: 24px; }
  .image-swiper .lc-a-page2 .lc-line1 {
    margin: 0 54px; } }

@media screen and (max-width: 1366px) {
  .image-swiper .lc-a-page2 .lc-left {
    font-size: 24px; }
  .image-swiper .lc-a-page2 .lc-line1 {
    margin: 0 40px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-a-page2 .lc-left {
    font-size: 22px; }
  .image-swiper .lc-a-page2 .lc-line1 {
    margin: 0 26px; }
  .image-swiper .lc-a-page2 .lc-tel {
    font-size: 22px; } }

@media screen and (max-width: 1100px) {
  .image-swiper .lc-a-page2 .lc-left {
    font-size: 20px; }
  .image-swiper .lc-a-page2 .lc-line1 {
    margin: 0 26px; }
  .image-swiper .lc-a-page2 .lc-tel {
    font-size: 20px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page2 {
    padding-top: 0.25rem; }
  .image-swiper .lc-a-page2 .lc_inner {
    height: auto;
    text-align: center;
    padding-bottom: 1.2rem; }
  .image-swiper .lc-a-page2 .lc-left, .image-swiper .lc-a-page2 .lc-right {
    width: 100%;
    float: none;
    height: auto;
    position: static; }
  .image-swiper .lc-a-page2 .lc-right-left, .image-swiper .lc-a-page2 .lc-right-right {
    float: none;
    height: auto;
    width: 100%;
    position: static; }
  .image-swiper .lc-a-page2 .lc-right-right .lc-router-link {
    width: 2.82rem;
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    height: 0.66rem;
    line-height: 0.66rem;
    font-size: 0.24rem;
    font-weight: bold; }
  .image-swiper .lc-a-page2 .lc-line1 {
    display: none; }
  .image-swiper .lc-a-page2 .lc-left {
    font-weight: bold;
    font-size: 0.3rem;
    line-height: 0.49rem; }
  .image-swiper .lc-a-page2 .lc-tel {
    font-size: 0.3rem;
    font-weight: bold;
    line-height: 0.43rem;
    margin-top: 0.09rem; }
  .image-swiper .lc-a-page2 .lc-right-right {
    position: absolute;
    width: 100%;
    bottom: 0.32rem; }
  .image-swiper .lc-a-page2 .lc-text {
    font-size: 0.22rem;
    margin-top: 0.06rem;
    line-height: 0.39rem; } }

.image-swiper .lc-title-container .lc-title {
  line-height: 1.5;
  font-size: 40px;
  text-align: center; }

.image-swiper .lc-title-container .lc-text {
  color: #fff;
  font-size: 16px;
  line-height: 1.875;
  width: 83.33333%;
  margin: 0 auto;
  padding-top: 2.22222%;
  padding-bottom: 3.40278%; }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-title-container .lc-title {
    font-size: 36px; }
  .image-swiper .lc-title-container .lc-text {
    font-size: 15px; } }

@media screen and (max-width: 1366px) {
  .image-swiper .lc-title-container .lc-title {
    font-size: 30px; }
  .image-swiper .lc-title-container .lc-text {
    font-size: 14px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-title-container .lc-title {
    font-size: 28px; }
  .image-swiper .lc-title-container .lc-text {
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-title-container .lc-title {
    font-size: 0.3rem;
    line-height: 0.5rem;
    font-weight: bold; }
  .image-swiper .lc-title-container .lc-text {
    width: 100%;
    font-size: 0.24rem;
    line-height: 0.36rem;
    padding-top: 0.25rem;
    padding-bottom: 0.44rem; } }

.image-swiper .lc-a-page3 {
  padding-top: 4.27083%;
  padding-bottom: 2.91667%; }

.image-swiper .lc-a-page3 .lc-title {
  color: #fff; }

.image-swiper .lc-a-page3 #certify .swiper-slide {
  width: 24.30556%;
  border-radius: 10px;
  overflow: hidden; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page3 {
    padding-top: 0.61rem;
    padding-bottom: 0.62rem; } }

.image-swiper .lc-a-page4 {
  padding-top: 3.28125%; }

.image-swiper .lc-a-page4 .lc-title-container {
  margin-bottom: 3.17708%; }

.image-swiper .lc-a-page4 .lc-title-container .lc-title {
  color: #4c4c4c; }

.image-swiper .lc-a-page4 .lc-a-page4-sw {
  padding-bottom: 5.55556%; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide {
  width: 29%;
  margin-right: 6.5%;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  background: #f8f8f8;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide .line {
  position: absolute;
  width: 12.35154%;
  top: 5.52017%;
  left: 6.17577%;
  width: 25px;
  height: 25px; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active {
  background: #4581fb; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .iw1, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .iw1 {
  opacity: 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .iw2, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .iw2 {
  opacity: 1; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .lc-t1, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .lc-t1 {
  color: #fff; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .lc-line1, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .lc-line1 {
  background: #fff; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .lc-t2, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .lc-t2 {
  color: #fff; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:hover .swiper-slide-container .lc-t3, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide.swiper-slide-active .swiper-slide-container .lc-t3 {
  color: #fff; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container {
  position: relative;
  padding-top: 111.87648%; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper {
  top: 5.52017%;
  right: 6.17577%;
  bottom: 5.52017%;
  left: 6.17577%;
  position: absolute;
  margin: auto;
  text-align: center; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line {
  position: absolute;
  width: 13.90374%;
  -webkit-animation: breathe 1s infinite ease-in-out;
  animation: breathe 1s infinite ease-in-out; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line .lc-border {
  padding-top: 100%;
  padding-top: 100%;
  border: 1px solid #fff;
  border-radius: 10px; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(1) {
  left: 0;
  top: 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(1) .lc-border {
  border-right: none;
  border-bottom: none;
  border-radius: 10px 0 0 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(2) {
  right: 0;
  top: 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(2) .lc-border {
  border-left: none;
  border-bottom: none;
  border-radius: 0 10px 0 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(3) {
  left: 0;
  bottom: 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(3) .lc-border {
  border-top: none;
  border-right: none;
  border-radius: 0 0 0 10px; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(4) {
  right: 0;
  bottom: 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line:nth-of-type(4) .lc-border {
  border-top: none;
  border-left: none;
  border-radius: 0 0 10px 0; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .img_wrap {
  width: 16.04278%;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 11.93317%; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .img_wrap .iw1, .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .img_wrap iw2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t1 {
  top: 37.47017%;
  position: absolute;
  font-size: 20px;
  line-height: 1.5;
  width: 100%;
  color: #333333;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-line1 {
  width: 16.04278%;
  height: 2px;
  background: #4581fb;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 50.83532%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t2 {
  font-size: 18px;
  line-height: 1.55556;
  margin-bottom: 2.13904%;
  color: #4c4c4c;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t3 {
  font-size: 14px;
  line-height: 2.14286;
  color: #666666;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-text {
  position: absolute;
  top: 60.62053%;
  width: 89.57219%;
  left: 0;
  right: 0;
  margin: 0 auto; }

.image-swiper .lc-a-page4 .lc-a-page4-sw-pa {
  opacity: 0;
  display: none; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t1 {
    font-size: 16px; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t2 {
    font-size: 14px; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t3 {
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide {
    width: 32%;
    margin-right: 1.5%; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t1 {
    font-size: 15px; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t2 {
    font-size: 13px; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide-container .swiper-slide-wrapper .lc-t3 {
    font-size: 12px;
    line-height: 1.85; } }

@media screen and (min-width: 1024px) {
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide:nth-of-type(3) {
    margin-right: 0; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page4 {
    padding-top: 0.5rem; }
  .image-swiper .lc-a-page4 .lc-title-container {
    margin-bottom: 0.58rem; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw {
    padding-bottom: 0.88rem; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide .swiper-slide-container .swiper-slide-wrapper .lc-t1 {
    font-size: 0.3rem; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide .swiper-slide-container .swiper-slide-wrapper .lc-t2 {
    font-size: 0.26rem; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw .swiper-slide .swiper-slide-container .swiper-slide-wrapper .lc-t3 {
    font-size: 0.24rem; }
  .image-swiper .lc-a-page4 .lc-a-page4-sw-pa {
    opacity: 1;
    bottom: 0.4rem;
    font-size: 0;
    line-height: 0;
    display: block; } }

.image-swiper .lc-a-page5 {
  padding-top: 3.125%;
  padding-bottom: .20833%; }

.image-swiper .lc-a-page5 .lc-title {
  color: #fff;
  margin-bottom: 4.86111%; }

.image-swiper .lc-a-page5 .lc-container .item {
  float: left;
  width: 46.52778%;
  margin-bottom: 4.02778%; }

.image-swiper .lc-a-page5 .lc-container .item:nth-of-type(2n) {
  float: right; }

.image-swiper .lc-a-page5 .lc-container .iw {
  width: 57.61194%;
  margin: 0 auto;
  margin-bottom: 2.38806%; }

.image-swiper .lc-a-page5 .lc-container .iw .box {
  position: relative;
  padding-top: 59.06736%; }

.image-swiper .lc-a-page5 .lc-container .iw img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0; }

.image-swiper .lc-a-page5 .lc-container .title {
  font-size: 30px;
  line-height: 1.66667;
  margin-bottom: 1.11111%;
  color: #fff; }

.image-swiper .lc-a-page5 .lc-container .text {
  font-size: 14px;
  line-height: 2.14286;
  color: #fff; }

@media screen and (max-width: 1900px) {
  .image-swiper .lc-a-page5 .lc-container .title {
    font-size: 25px; }
  .image-swiper .lc-a-page5 .lc-container .text {
    height: 90px; } }

@media screen and (max-width: 1500px) {
  .image-swiper .lc-a-page5 .lc-container .title {
    font-size: 22px; }
  .image-swiper .lc-a-page5 .lc-container .text {
    height: 90px; } }

@media screen and (max-width: 1366px) {
  .image-swiper .lc-a-page5 .lc-container .title {
    font-size: 18px; }
  .image-swiper .lc-a-page5 .lc-container .text {
    height: 90px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-a-page5 .lc-container .title {
    font-size: 16px; }
  .image-swiper .lc-a-page5 .lc-container .text {
    height: 90px;
    font-size: 12px;
    line-height: 20px;
    height: 60px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page5 {
    padding-top: .76rem;
    padding-bottom: .14rem; }
  .image-swiper .lc-a-page5 .lc-title {
    margin-bottom: .65rem; }
  .image-swiper .lc-a-page5 .lc-container .item {
    width: 100%;
    margin-bottom: .56rem; }
  .image-swiper .lc-a-page5 .lc-container .iw {
    margin-bottom: .21rem; }
  .image-swiper .lc-a-page5 .lc-container .title {
    font-size: 0.28rem;
    font-weight: bold;
    line-height: 0.48rem;
    margin-bottom: .12rem; }
  .image-swiper .lc-a-page5 .lc-container .text {
    height: auto;
    font-size: 0.24rem;
    line-height: .32rem; } }

.image-swiper .lc-a-page6 {
  padding-top: 4.375%; }

.image-swiper .lc-a-page6 .lc-title-container {
  margin-bottom: 3.40278%; }

.image-swiper .lc-a-page6 .lc-top {
  padding-top: 26.66667%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-size: contain;
  background-position: left top; }

.image-swiper .lc-a-page6 .lc-top .ih {
  height: 100%;
  position: absolute;
  right: 14.65278%;
  top: 0; }

.image-swiper .lc-a-page6 .lc-top .ih img {
  height: 100%; }

.image-swiper .lc-a-page6 .lc-top .left {
  position: absolute;
  bottom: 13.80208%;
  left: 5.20833%; }

.image-swiper .lc-a-page6 .lc-top .left .iw {
  margin-left: 24px;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
  margin-bottom: 14px; }

.image-swiper .lc-a-page6 .lc-top .left .iw img {
  margin-left: 0;
  margin-right: auto; }

.image-swiper .lc-a-page6 .lc-top .left .lc-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  line-height: 30px;
  line-height: 1.83333;
  margin-bottom: 5px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s; }

.image-swiper .lc-a-page6 .lc-top .left .lc-text {
  font-size: 16px;
  color: #fff;
  line-height: 2.25; }

.image-swiper .lc-a-page6 .lc-bottom {
  padding: 1.875% 0 2.5%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide {
  width: 17.43056%;
  margin-right: 3.19444%;
  border-radius: 8px;
  overflow: hidden;
  text-align: center; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide:nth-of-type(5) {
  margin-right: 0; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .box {
  padding-top: 16.33466%;
  background: #f5f5f5;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  padding-bottom: 3.18725%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw {
  width: 66px;
  height: 50px;
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: 5.57769%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw .iw-white {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  display: block;
  height: 100%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw .iw-blue {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  height: 100%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .ih {
  height: 26px;
  margin-top: 4.38247%;
  margin-bottom: 6.77291%; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .t1 {
  color: #4c4c4c;
  font-size: 16px; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide .t2 {
  color: #666666;
  font-size: 14px;
  line-height: 28px;
  height: 56px;
  width: 87.6494%;
  margin-left: auto;
  margin-right: auto; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .box {
  background: #4581fb; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .iw .iw-blue {
  margin-top: -50px; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .iw.ih .iw-blue {
  margin-top: -26px; }

.image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .t1, .image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .t2 {
  color: #fff; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-a-page6 .lc-top .left .iw {
    width: 40px;
    height: 40px; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-title {
    font-size: 20px; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-text {
    font-size: 14px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .t2 {
    height: 50px;
    line-height: 25px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-a-page6 .lc-top .left .iw {
    width: 40px;
    height: 40px; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-title {
    font-size: 20px; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-text {
    font-size: 14px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide {
    width: 19%;
    margin-right: 1.25%; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw {
    height: 40px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw img {
    height: 100%; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .ih {
    height: 26px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .t2 {
    height: 50px;
    line-height: 25px;
    font-size: 12px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .iw .iw-blue {
    margin-top: -40px; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .ih .iw-blue {
    margin-top: -26px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page6 .lc-top {
    height: 2.93rem;
    padding-top: 0;
    background-size: cover; }
  .image-swiper .lc-a-page6 .lc-top .ih {
    display: none; }
  .image-swiper .lc-a-page6 .lc-top .left {
    bottom: 0.48rem; }
  .image-swiper .lc-a-page6 .lc-top .left .iw {
    width: 0.52rem;
    height: 0.52rem;
    margin-left: 0.33rem;
    margin-bottom: 0.11rem; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-title {
    line-height: 0.48rem;
    margin-bottom: 0.16rem;
    font-size: 0.28rem; }
  .image-swiper .lc-a-page6 .lc-top .left .lc-text {
    font-size: 0.24rem;
    line-height: 0.3rem; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .iw {
    height: 0.5rem; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide .ih {
    height: 0.26rem; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .iw .iw-blue {
    margin-top: -0.5rem; }
  .image-swiper .lc-a-page6 .lc-bottom .swiper-slide:hover .ih .iw-blue {
    margin-top: -0.26rem; } }

@-webkit-keyframes breathe {
  0% {
    opacity: 0; }
  50% {
    opacity: 1; }
  100% {
    opacity: 0; } }

@keyframes breathe {
  0% {
    opacity: 0; }
  50% {
    opacity: 1; }
  100% {
    opacity: 0; } }

.image-swiper .lc-b .bgcc {
  background-size: cover;
  background-position: center; }

.image-swiper .lc-b .lc_inner.abcenter {
  left: 0;
  right: 0;
  position: absolute;
  top: 0;
  margin: 0 auto;
  height: 100%; }

.image-swiper .lc-b .lc-banner {
  padding-top: 37.96875%;
  position: relative;
  margin-bottom: 20px; }

.image-swiper .lc-b .lc-banner .t1 {
  font-size: 80px;
  color: #fff;
  padding-left: 6.2%; }

.image-swiper .lc-b .lc-banner .t2 {
  color: #fff;
  position: relative; }

.image-swiper .lc-b .lc-banner .t3 {
  font-size: 18px;
  color: #fff;
  padding-left: 13.8%;
  margin-top: 5%;
  line-height: 28px; }

.image-swiper .lc-b .lc-banner .wow {
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s; }

.image-swiper .lc-b .lc-banner .lc-container {
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -moz-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end; }

.image-swiper .lc-b .lc-banner .text {
  font-size: 31px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 88px; }

.image-swiper .lc-b .lc-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  width: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center; }

.image-swiper .lc-b .lc-container .lc-wrapper {
  width: 500px; }

.image-swiper .lc-b .lc-slide {
  position: relative;
  padding-top: 33.85417%;
  margin-bottom: 20px;
  background-size: cover;
  background-position: center; }

.image-swiper .lc-b .lc-slide:last-of-type {
  margin-bottom: 0; }

.image-swiper .lc-b .lc-slide .lc-title {
  font-size: 42px;
  line-height: 1.47619;
  margin-bottom: 20px;
  color: #fff; }

.image-swiper .lc-b .lc-slide .lc-text {
  line-height: 2.22222;
  color: #fff;
  font-size: 16px; }

.image-swiper .lc-b .lc-slide .lc-btn {
  line-height: 48px;
  width: 223px;
  background: #fff;
  border-radius: 24px;
  text-align: center;
  font-size: 16px;
  margin-top: 55px; }

.image-swiper .lc-b .lc-slide:nth-of-type(2n) .lc-container {
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -moz-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end; }

.image-swiper .lc-b .lc-slide:nth-of-type(2n) .lc-wrapper {
  width: auto; }

.image-swiper .lc-b .lc-slide:nth-of-type(1) {
  background-image: url("../images/lc47.jpg"); }

.image-swiper .lc-b .lc-slide:nth-of-type(1) .lc-btn {
  color: #919deb; }

.image-swiper .lc-b .lc-slide:nth-of-type(2) {
  background-image: url("../images/lc48.jpg"); }

.image-swiper .lc-b .lc-slide:nth-of-type(2) .lc-wrapper {
  padding-right: 8.40278%; }

.image-swiper .lc-b .lc-slide:nth-of-type(2) .lc-btn {
  color: #3898fe; }

.image-swiper .lc-b .lc-slide:nth-of-type(3) {
  background-image: url("../images/lc49.jpg"); }

.image-swiper .lc-b .lc-slide:nth-of-type(3) .lc-wrapper {
  width: 510px; }

.image-swiper .lc-b .lc-slide:nth-of-type(3) .lc-btn {
  color: #845cd9; }

.image-swiper .lc-b .lc-slide:nth-of-type(4) {
  background-image: url("../images/lc50.jpg"); }

.image-swiper .lc-b .lc-slide:nth-of-type(4) .lc-btn {
  color: #189cc2; }

.image-swiper .lc-b .lc-slide:nth-of-type(5) {
  background-image: url("../images/lc51.jpg"); }

.image-swiper .lc-b .lc-slide:nth-of-type(5) .lc-btn {
  color: #3898fe; }

@media screen and (max-width: 1366px) {
  .image-swiper .lc-b .lc-banner .t1 {
    font-size: 50px; }
  .image-swiper .lc-b .lc-slide .lc-title {
    font-size: 25px;
    line-height: 50px; }
  .image-swiper .lc-b .lc-slide .lc-text {
    line-height: 1.75;
    font-size: 14px; }
  .image-swiper .lc-b .lc-slide .lc-btn {
    margin-top: 32px;
    line-height: 42px;
    width: 210px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-b .lc-banner {
    height: 3.8rem;
    padding-top: 0;
    margin-bottom: 0; }
  .image-swiper .lc-b .lc-banner .lc-wrapper {
    width: auto; }
  .image-swiper .lc-b .lc-banner .t1 {
    font-size: 0.6rem; }
  .image-swiper .lc-b .lc-banner .t2 {
    width: 3.39rem; }
  .image-swiper .lc-b .lc-banner .t3 {
    font-size: 0.24rem; }
  .image-swiper .lc-b .lc-banner .text {
    font-size: 0.26rem;
    line-height: 0.6rem; }
  .image-swiper .lc-b .lc-slide {
    padding-top: 0;
    height: 9rem;
    text-align: center;
    margin-bottom: 0;
    margin-top: 0; }
  .image-swiper .lc-b .lc-slide .lc_inner.abcenter {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-top: 0.75rem; }
  .image-swiper .lc-b .lc-slide .lc-title {
    margin-bottom: 0.14rem;
    line-height: 0.56rem;
    font-size: 0.36rem;
    font-weight: bold; }
  .image-swiper .lc-b .lc-slide .lc-container {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -moz-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start; }
  .image-swiper .lc-b .lc-slide .lc-btn {
    margin-left: auto;
    margin-right: auto;
    margin-top: 0.53rem;
    width: 2.97rem;
    line-height: 0.64rem;
    border-radius: 0.32rem; }
  .image-swiper .lc-b .lc-slide .lc-text {
    font-size: 0.24rem;
    line-height: 0.41rem; }
  .image-swiper .lc-b .lc-slide:nth-of-type(2n) .lc-container {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center; }
  .image-swiper .lc-b .lc-slide:nth-of-type(2n) .lc-wrapper {
    padding-right: 0; }
  .image-swiper .lc-b .lc-slide:nth-of-type(1) {
    background-image: url("../images/lc52.jpg"); }
  .image-swiper .lc-b .lc-slide:nth-of-type(2) {
    background-image: url("../images/lc53.jpg"); }
  .image-swiper .lc-b .lc-slide:nth-of-type(3) {
    background-image: url("../images/lc54.jpg"); }
  .image-swiper .lc-b .lc-slide:nth-of-type(4) {
    background-image: url("../images/lc55.jpg"); }
  .image-swiper .lc-b .lc-slide:nth-of-type(5) {
    background-image: url("../images/lc56.jpg"); } }

.image-swiper .lc-b-swiper {
  padding-bottom: 1.97917%;
  padding-top: 2.60417%;
  background: #f5f5f5;
  margin-top: 20px;
  margin-bottom: 20px; }

.image-swiper .lc-b-swiper .mb40 {
  margin-bottom: 4.02778%; }

.image-swiper .lc-b-swiper .t1 {
  font-size: 42px;
  color: #58585b;
  text-align: center; }

.image-swiper .lc-b-swiper .t2 {
  color: #7c7c80;
  font-size: 18px;
  text-align: center; }

.image-swiper .lc-b-swiper .iw {
  border-radius: 20px;
  overflow: hidden; }

.image-swiper .lc-b-swiper .text-wrap {
  margin-top: 11.07692%;
  text-align: center; }

.image-swiper .lc-b-swiper .text-wrap .lc-title {
  color: #4c4c4c;
  font-size: 18px;
  line-height: 28px; }

.image-swiper .lc-b-swiper .text-wrap .lc-text {
  color: #999999;
  line-height: 24px;
  font-size: 14px; }

.image-swiper .lc-b-swiper .text-wrap .lc-num {
  color: #999999;
  line-height: 24px;
  font-size: 14px;
  margin-top: 3.07692%; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa {
  text-align: center;
  font-size: 0;
  line-height: 0;
  margin: 0 auto;
  margin-top: 2.84722%; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa .swiper-pagination-bullet {
  width: 42px;
  height: auto;
  border-radius: none;
  background: none;
  color: #999999;
  font-size: 16px;
  line-height: 35px;
  opacity: 1;
  position: relative; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa .swiper-pagination-bullet::after {
  width: 0%;
  position: absolute;
  left: 0;
  bottom: 0;
  content: '';
  display: block;
  height: 2px;
  background: #b50107;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa.swiper-pagination-bullets-dynamic {
  -webkit-transform: translateX(0%);
  -ms-transform: translateX(0%);
  transform: translateX(0%); }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev, .image-swiper .lc-b-swiper .lc_b_page1_sw_pa.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next, .image-swiper .lc-b-swiper .lc_b_page1_sw_pa.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next, .image-swiper .lc-b-swiper .lc_b_page1_sw_pa.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1); }

.image-swiper .lc-b-swiper .lc_b_page1_sw_pa .swiper-pagination-bullet-active::after {
  width: 100%; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_btn {
  display: none;
  position: absolute;
  top: 3.18rem;
  z-index: 3;
  width: 0.64rem;
  height: 0.64rem; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_btn img {
  display: block;
  width: 100%;
  height: 100%; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_btn.lc_b_page1_sw_prev {
  left: 0.24rem; }

.image-swiper .lc-b-swiper .lc_b_page1_sw_btn.lc_b_page1_sw_next {
  right: 0.24rem; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-b-swiper {
    margin-top: 0;
    padding-top: 0.37rem;
    background: #fff; }
  .image-swiper .lc-b-swiper .mb40 {
    margin-bottom: 0.4rem; }
  .image-swiper .lc-b-swiper .t1 {
    font-weight: bold;
    font-size: 0.36rem;
    color: #58585b;
    text-align: center; }
  .image-swiper .lc-b-swiper .t2 {
    color: #7c7c80;
    font-size: 0.24rem;
    text-align: center; }
  .image-swiper .lc-b-swiper .lc_b_page1_sw_btn {
    display: block; }
  .image-swiper .lc-b-swiper .lc_inner {
    width: 100%; }
  .image-swiper .lc-b-swiper .lc_b_page1_sw_pa {
    display: none; }
  .image-swiper .lc-b-swiper .text-wrap {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .lc-b-swiper .text-wrap .lc-title {
    font-size: 0.26rem;
    line-height: 0.4rem; }
  .image-swiper .lc-b-swiper .text-wrap .lc-text {
    font-size: 0.24rem;
    line-height: 0.37rem;
    margin-top: 0.1rem; }
  .image-swiper .lc-b-swiper .text-wrap .lc-num {
    font-size: 0.2rem;
    margin-top: 0.02rem;
    line-height: 0.33rem; }
  .image-swiper .lc-b-swiper .iw {
    position: relative; }
  .image-swiper .lc-b-swiper .iw:after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    content: '';
    display: block;
    background: rgba(0, 0, 0, 0.5);
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .lc-b-swiper .swiper-slide-active .text-wrap {
    opacity: 1; }
  .image-swiper .lc-b-swiper .swiper-slide-active .iw::after {
    opacity: 0; } }

@-webkit-keyframes huxi {
  0% {
    opacity: 1; }
  50% {
    opacity: 0; }
  100% {
    opacity: 1; } }

@keyframes huxi {
  0% {
    opacity: 1; }
  50% {
    opacity: 0; }
  100% {
    opacity: 1; } }

.image-swiper .inner {
  max-width: 1200px;
  margin: 0 auto;
  width: 95%; }

.image-swiper .bgcc {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover; }

.image-swiper .table {
  display: table;
  width: 100%;
  height: 100%; }

.image-swiper .table .table-cell {
  display: table-cell;
  width: 100%;
  height: 100%;
  vertical-align: middle; }

.image-swiper .fl {
  float: left; }

.image-swiper .fr {
  float: right; }

.image-swiper .cb:after {
  clear: both;
  content: "";
  display: block; }

.image-swiper .cb:before {
  clear: both;
  content: "";
  display: block; }

.image-swiper .a_wrap_b {
  padding-top: 3.22917%; }

.image-swiper .a_wrap_b .iw {
  width: 54.58333%; }

.image-swiper .a_wrap_b .re {
  position: relative; }

.image-swiper .a_wrap_b .re .iw.fr {
  position: absolute;
  right: 0;
  top: 0; }

@media screen and (max-width: 768px) {
  .image-swiper .a_wrap_b .iw {
    width: 64%; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_b .t1 {
    font-size: 20px; }
  .image-swiper .a_wrap_b .t2 {
    font-size: 14px; }
  .image-swiper .a_wrap_b .re .iw {
    width: 100%; }
  .image-swiper .a_wrap_b .re .iw.fr {
    position: static;
    margin-top: -17.3245%; } }

.image-swiper .a_wrap_a {
  color: #fff;
  text-align: center;
  position: relative; }

.image-swiper .a_wrap_a .t1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
  width: 100%;
  font-size: 24px; }

.image-swiper .a_wrap_a .t1 span {
  text-align: center;
  margin: 0 1%; }

.image-swiper .a_wrap_a .t1 .container {
  vertical-align: top; }

.image-swiper .a_wrap_a .inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 100%;
  padding-top: 3.125%; }

.image-swiper .a_wrap_a img {
  display: inline-block;
  letter-spacing: normal;
  vertical-align: bottom; }

.image-swiper .a_wrap_a .t2 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  padding-top: 1.83333%; }

.image-swiper .a_wrap_a .t2 img {
  display: block; }

.image-swiper .a_wrap_a .t3 {
  font-size: 18px;
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s; }

.image-swiper .a_wrap_a .t3 .line {
  height: 1px;
  background: #fff;
  color: #fff;
  display: inline-block;
  vertical-align: middle;
  width: -webkit-calc((100% - 350px - 5px - 5px - 5px - 5px) / 3);
  width: calc((100% - 350px - 5px - 5px - 5px - 5px) / 3);
  margin: 0 5px; }

.image-swiper .a_wrap_a .t3 span {
  width: 350px;
  text-align: center; }

@media screen and (max-width: 1605px) {
  .image-swiper .a_wrap_a .t2 img {
    width: 80%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .a_wrap_a .t1 {
    font-size: 16px; }
  .image-swiper .a_wrap_a .t2 img {
    width: 60%; } }

@media screen and (max-width: 1024px) {
  .image-swiper .a_wrap_a .t1 {
    font-size: 16px; }
  .image-swiper .a_wrap_a .t1 .img3 {
    width: 40px; }
  .image-swiper .a_wrap_a .t2 img {
    width: 50%; }
  .image-swiper .a_wrap_a .t3 {
    font-size: 14px; } }

@media screen and (max-width: 768px) {
  .image-swiper .a_wrap_a .t1 {
    font-size: 14px; }
  .image-swiper .a_wrap_a .t1 .img3 {
    width: 40px; }
  .image-swiper .a_wrap_a .t2 img {
    width: 50%; }
  .image-swiper .a_wrap_a .t3 {
    font-size: 14px; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_a .t1 {
    font-size: 12px; }
  .image-swiper .a_wrap_a .t1 .img3 {
    width: 40px; }
  .image-swiper .a_wrap_a .t2 img {
    width: 100%; }
  .image-swiper .a_wrap_a .t3 {
    font-size: 12px;
    display: none; } }

.image-swiper .a_wrap_b {
  padding-top: 3.22917%; }

.image-swiper .a_wrap_b .iw {
  width: 54.58333%; }

.image-swiper .a_wrap_b .re {
  position: relative; }

.image-swiper .a_wrap_b .re .iw.fr {
  position: absolute;
  right: 0;
  top: 0; }

@media screen and (max-width: 768px) {
  .image-swiper .a_wrap_b .iw {
    width: 64%; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_b .t1 {
    font-size: 20px; }
  .image-swiper .a_wrap_b .t2 {
    font-size: 14px; }
  .image-swiper .a_wrap_b .re .iw {
    width: 100%; }
  .image-swiper .a_wrap_b .re .iw.fr {
    position: static;
    margin-top: -17.3245%; } }

.image-swiper .page_a * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .page_a .title_container {
  text-align: center;
  margin-bottom: 3.83333%; }

.image-swiper .page_a .cn1 {
  color: #202020;
  font-size: 40px;
  line-height: 1.575; }

.image-swiper .page_a .cn2 {
  color: #a1a1a1;
  font-size: 24px;
  line-height: 1.95833;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto; }

.image-swiper .page_a .cn2 span {
  color: #dd0000; }

@media screen and (max-width: 1024px) {
  .image-swiper .page_a .cn1 {
    font-size: 30px; }
  .image-swiper .page_a .cn2 {
    font-size: 18px; } }

@media screen and (max-width: 750px) {
  .image-swiper .page_a .cn1 {
    font-size: 20px;
    line-height: 1.75; }
  .image-swiper .page_a .cn2 {
    font-size: 14px;
    line-height: 1.45; } }

.image-swiper .a_wrap_c {
  padding-top: 4.32292%;
  padding-bottom: 3.85417%; }

.image-swiper .a_wrap_c .title_container {
  margin-bottom: 3.41667%; }

.image-swiper .table_header, .image-swiper .table_container th {
  font-size: 18px;
  line-height: 2.5;
  text-align: center;
  background: #595959;
  color: #fff;
  font-weight: normal; }

.image-swiper .table_header.dd0000, .image-swiper .table_container th.dd0000 {
  background: #dd0000; }

.image-swiper .table_container .table_title {
  color: #dd0000;
  font-size: 30px;
  font-weight: bold;
  text-align: center;
  line-height: 1.46667;
  margin-bottom: 1.5%; }

.image-swiper .table_container .table_title2 {
  color: #686767;
  font-size: 18px;
  text-align: center;
  line-height: 1.66667;
  margin-bottom: 1.33333%; }

.image-swiper .table_container .w49 {
  width: -webkit-calc(50% - 5px);
  width: calc(50% - 5px); }

.image-swiper .table_container table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
  line-height: 37px;
  font-size: 18px;
  color: #595959; }

.image-swiper .table_container .table_slide {
  padding-top: 24px; }

.image-swiper .table_container .fl.w49 table {
  color: #dd0000; }

@media screen and (max-width: 1024px) {
  .image-swiper .table_container .table_slide {
    padding-top: 15px; }
  .image-swiper .table_container .table_header, .image-swiper .table_container th, .image-swiper .table_container .table_container th {
    font-size: 18px; }
  .image-swiper .table_container td {
    font-size: 16px; } }

@media screen and (max-width: 768px) {
  .image-swiper .table_container .table_title {
    font-size: 20px; }
  .image-swiper .table_container .table_title2 {
    font-size: 16px; }
  .image-swiper .table_container .table_slide {
    padding-top: 5px; }
  .image-swiper .table_container .table_header, .image-swiper .table_container th, .image-swiper .table_container .table_container th {
    font-size: 14px; }
  .image-swiper .table_container td {
    font-size: 14px;
    line-height: 1.3;
    padding: 5px 0; } }

@media screen and (max-width: 750px) {
  .image-swiper .table_container td {
    font-size: 12px; }
  .image-swiper .table_container .w49 {
    width: 50%; } }

.image-swiper .table_container1 {
  margin-top: 3.91667%;
  margin-bottom: 4.66667%; }

.image-swiper .table_container2 .table_slide {
  padding: 0; }

.image-swiper .table_container2 .table_title {
  margin-bottom: 0; }

.image-swiper .table_container2 th {
  width: 20%; }

.image-swiper .table_container2 .table_slide {
  background: #f5f5f5; }

.image-swiper .table_container2 table {
  background: #f5f5f5; }

.image-swiper .table_container2 table td {
  padding: 8px 0;
  line-height: 1.3; }

.image-swiper .a_wrap_d {
  background: #f5f5f5;
  padding-top: 4.0625%;
  padding-bottom: 3.17708%;
  text-align: center; }

.image-swiper .a_wrap_d .text_container {
  margin: 0 auto;
  display: inline-block;
  background: #d10303;
  padding: .75% 20px; }

.image-swiper .a_wrap_d .text_container .item {
  display: inline-block;
  color: #fff;
  font-size: 24px;
  line-height: 1.5;
  padding-right: 50px; }

.image-swiper .a_wrap_d .text_container .item:nth-of-type(3) {
  padding-right: 0; }

.image-swiper .a_wrap_d .text_container .item::before {
  display: inline-block;
  content: "";
  width: 10px;
  height: 10px;
  background: #fff;
  vertical-align: middle;
  margin-top: -3px;
  margin-right: 5px; }

@media screen and (max-width: 1024px) {
  .image-swiper .a_wrap_d .text_container .item {
    font-size: 16px; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_d .text_container .item {
    font-size: 12px;
    padding: 0 8px; } }

.image-swiper .a_wrap_e {
  padding-top: 5.57292%;
  padding-bottom: 3.85417%; }

.image-swiper .a_wrap_e .inner {
  position: relative; }

.image-swiper .a_wrap_e .inner .img74 {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto 0; }

@media screen and (max-width: 1024px) {
  .image-swiper .a_wrap_e .img74 {
    width: 50%; }
  .image-swiper .a_wrap_e .img75 {
    width: 60%; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_e .img74 {
    width: 60%; }
  .image-swiper .a_wrap_e .img75 {
    width: 60%; } }

@-webkit-keyframes Gradient {
  0% {
    background-position: 0% 50%; }
  50% {
    background-position: 100% 50%; }
  100% {
    background-position: 0% 50%; } }

@keyframes Gradient {
  0% {
    background-position: 0% 50%; }
  50% {
    background-position: 100% 50%; }
  100% {
    background-position: 0% 50%; } }

.image-swiper .a_wrap_f {
  background: -webkit-linear-gradient(135deg, #EE7752, #E73C7E, #23A6D5, #23D5AB);
  background: -o-linear-gradient(135deg, #EE7752, #E73C7E, #23A6D5, #23D5AB);
  background: linear-gradient(-45deg, #EE7752, #E73C7E, #23A6D5, #23D5AB);
  background-size: 400% 400%;
  -webkit-animation: Gradient 15s ease infinite;
  animation: Gradient 15s ease infinite;
  padding-top: 5%;
  padding-bottom: 4.32292%; }

.image-swiper .a_wrap_g {
  padding-top: 5.15625%;
  padding-bottom: 4.58333%; }

.image-swiper .a_wrap_g .inner {
  position: relative; }

.image-swiper .a_wrap_g .img92 {
  margin-left: 0;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto 0; }

@media screen and (max-width: 1024px) {
  .image-swiper .a_wrap_g .img92 {
    width: 50%; }
  .image-swiper .a_wrap_g .img91 {
    width: 46%; } }

@media screen and (max-width: 750px) {
  .image-swiper .a_wrap_g .img92 {
    width: 58%; }
  .image-swiper .a_wrap_g .img91 {
    width: 40%; } }

.image-swiper .a_wrap_h ul {
  width: 95.5%;
  margin: 0 auto; }

.image-swiper .a_wrap_h ul li {
  float: left;
  width: 32%;
  border: 5px solid #fff;
  border-radius: 5px;
  margin-right: 2%;
  -webkit-box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.2);
  margin-bottom: 2.44328%; }

.image-swiper .a_wrap_h ul li:nth-of-type(3n) {
  margin-right: 0; }

.image-swiper .a_wrap_i img {
  -webkit-animation: imgroy 3s ease-in-out infinite;
  animation: imgroy 3s ease-in-out infinite; }

@-webkit-keyframes imgroy {
  0% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); }
  25% {
    -webkit-transform: rotateY(15deg);
    transform: rotateY(15deg); }
  50% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); }
  75% {
    -webkit-transform: rotateY(-15deg);
    transform: rotateY(-15deg); }
  100% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); } }

@keyframes imgroy {
  0% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); }
  25% {
    -webkit-transform: rotateY(15deg);
    transform: rotateY(15deg); }
  50% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); }
  75% {
    -webkit-transform: rotateY(-15deg);
    transform: rotateY(-15deg); }
  100% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg); } }

.image-swiper .a_wrap_j .metro {
  padding-top: 65%;
  position: relative; }

.image-swiper .a_wrap_j .metro .item {
  position: absolute; }

.image-swiper .a_wrap_j .metro .item a {
  display: block;
  position: relative; }

.image-swiper .a_wrap_j .metro .item a img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.image-swiper .a_wrap_j .metro .item1 {
  width: 41.83333%;
  left: 0;
  top: 0; }

.image-swiper .a_wrap_j .metro .item1 a {
  padding-top: 49.20319%; }

.image-swiper .a_wrap_j .metro .item2 {
  width: 37.66667%;
  left: 43.08333%;
  top: 0; }

.image-swiper .a_wrap_j .metro .item2 a {
  padding-top: 54.64602%; }

.image-swiper .a_wrap_j .metro .item3 {
  width: 18%;
  right: 0;
  top: 0; }

.image-swiper .a_wrap_j .metro .item3 a {
  padding-top: 268.51852%; }

.image-swiper .a_wrap_j .metro .item4 {
  width: 25.5%;
  left: 0;
  bottom: 0; }

.image-swiper .a_wrap_j .metro .item4 a {
  padding-top: 169.28105%; }

.image-swiper .a_wrap_j .metro .item5 {
  width: 54.08333%;
  left: 26.75%;
  bottom: 25.38462%; }

.image-swiper .a_wrap_j .metro .item5 a {
  padding-top: 49.30663%; }

.image-swiper .a_wrap_j .metro .item6 {
  width: 19.91667%;
  left: 26.75%;
  bottom: 0; }

.image-swiper .a_wrap_j .metro .item6 a {
  padding-top: 76.98745%; }

.image-swiper .a_wrap_j .metro .item7 {
  width: 23.58333%;
  left: 47.91667%;
  bottom: 0; }

.image-swiper .a_wrap_j .metro .item7 a {
  padding-top: 65.01767%; }

.image-swiper .a_wrap_j .metro .item8 {
  width: 27.16667%;
  right: 0;
  bottom: 0; }

.image-swiper .a_wrap_j .metro .item8 a {
  padding-top: 56.44172%; }

.image-swiper .a_wrap_h {
  padding-top: 4.16667%;
  padding-bottom: 3.28125%; }

.image-swiper .a_wrap_h img {
  max-width: 100%; }

.image-swiper .en {
  text-transform: uppercase; }

.image-swiper .lc-d-banner {
  padding-top: 37.29167%;
  position: relative;
  background-position: center bottom; }

.image-swiper .lc-d-banner .lc-d-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: -webkit-calc(100% - 163px);
  height: calc(100% - 163px);
  color: #fff;
  text-align: center; }

.image-swiper .lc-d-banner .lc-d-top-en {
  font-size: 24px;
  line-height: 1.58333; }

.image-swiper .lc-d-banner .lc-d-top-cn {
  margin-bottom: 1.45833%;
  font-size: 65px;
  line-height: 1.2;
  font-weight: bold; }

.image-swiper .lc-d-banner .lc-d-top-cn2 {
  line-height: 1.55556;
  font-size: 36px; }

.image-swiper .lc-d-banner .lc-d-bottom {
  height: 163px;
  overflow: hidden;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3); }

.image-swiper .lc-d-banner .lc-d-bottom ul, .image-swiper .lc-d-banner .lc-d-bottom li {
  height: 100%; }

.image-swiper .lc-d-banner .lc-d-bottom li {
  width: 25%;
  float: left;
  text-align: center;
  padding-top: 6px;
  color: #fff;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-d-banner .lc-d-bottom li em {
  font-style: normal;
  position: relative;
  display: inline-block;
  height: 73px; }

.image-swiper .lc-d-banner .lc-d-bottom li em i {
  height: 17px;
  line-height: 17px;
  font-size: 12px;
  position: absolute;
  right: -11px;
  top: 17.80822%; }

.image-swiper .lc-d-banner .lc-d-bottom li em img {
  vertical-align: top;
  display: inline-block; }

.image-swiper .lc-d-banner .lc-d-bottom li .num {
  font-size: 0;
  line-height: 0; }

.image-swiper .lc-d-banner .lc-d-bottom li .num span {
  display: inline-block;
  font-size: 72px;
  line-height: 73px;
  height: 73px; }

.image-swiper .lc-d-banner .lc-d-bottom li .title {
  font-size: 24px;
  font-weight: bold;
  line-height: 43px; }

.image-swiper .lc-d-banner .lc-d-bottom li .text {
  line-height: 32px;
  font-size: 18px; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-d-banner .lc-d-top {
    height: -webkit-calc(100% - 155px);
    height: calc(100% - 155px); }
  .image-swiper .lc-d-banner .lc-d-top-en {
    font-size: 22px; }
  .image-swiper .lc-d-banner .lc-d-top-cn {
    font-size: 58px; }
  .image-swiper .lc-d-banner .lc-d-top-cn img {
    width: 450px; }
  .image-swiper .lc-d-banner .lc-d-top-cn2 {
    font-size: 30px; }
  .image-swiper .lc-d-banner .lc-d-bottom {
    height: 155px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num {
    height: 68px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num span {
    height: 68px;
    line-height: 68px;
    font-size: 50px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em {
    height: 68px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em i {
    right: -15px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .title {
    font-size: 20px;
    line-height: 1.79167; }
  .image-swiper .lc-d-banner .lc-d-bottom li .text {
    font-size: 16px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-d-banner .lc-d-top {
    height: -webkit-calc(100% - 130px);
    height: calc(100% - 130px); }
  .image-swiper .lc-d-banner .lc-d-top-en {
    font-size: 18px; }
  .image-swiper .lc-d-banner .lc-d-top-cn {
    font-size: 52px; }
  .image-swiper .lc-d-banner .lc-d-top-cn img {
    width: 365px; }
  .image-swiper .lc-d-banner .lc-d-top-cn2 {
    font-size: 24px; }
  .image-swiper .lc-d-banner .lc-d-bottom {
    height: 130px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num {
    height: 50px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num span {
    height: 50px;
    line-height: 50px;
    font-size: 42px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em {
    height: 50px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em i {
    right: -15px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .title {
    font-size: 20px;
    line-height: 1.79167; }
  .image-swiper .lc-d-banner .lc-d-bottom li .text {
    font-size: 16px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-d-banner .lc-d-top {
    height: -webkit-calc(100% - 110px);
    height: calc(100% - 110px); }
  .image-swiper .lc-d-banner .lc-d-top-en {
    font-size: 17px; }
  .image-swiper .lc-d-banner .lc-d-top-cn {
    font-size: 42px; }
  .image-swiper .lc-d-banner .lc-d-top-cn img {
    width: 345px; }
  .image-swiper .lc-d-banner .lc-d-top-cn2 {
    font-size: 20px; }
  .image-swiper .lc-d-banner .lc-d-bottom {
    height: 110px; }
  .image-swiper .lc-d-banner .lc-d-bottom li {
    padding-top: 0; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num {
    height: 44px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num span {
    height: 44px;
    line-height: 44px;
    font-size: 34px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em {
    height: 44px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em i {
    right: -15px; }
  .image-swiper .lc-d-banner .lc-d-bottom li .title {
    font-size: 18px;
    line-height: 1.79167; }
  .image-swiper .lc-d-banner .lc-d-bottom li .text {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d-banner {
    height: 3.36rem;
    padding: 0; }
  .image-swiper .lc-d-banner .lc-d-top-en {
    font-size: .18rem; }
  .image-swiper .lc-d-banner .lc-d-top-cn {
    font-size: 0.42rem; }
  .image-swiper .lc-d-banner .lc-d-top-cn img {
    width: 3.48rem; }
  .image-swiper .lc-d-banner .lc-d-top-cn2 {
    font-size: .25rem; }
  .image-swiper .lc-d-banner .lc-d-top {
    height: auto;
    top: 0;
    bottom: .72rem;
    position: absolute;
    margin: auto;
    width: 100%; }
  .image-swiper .lc-d-banner .lc-d-bottom {
    height: .72rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li {
    padding-top: .05rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num {
    height: .36rem;
    line-height: .36rem;
    font-size: .31rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em {
    height: .36rem;
    line-height: .36rem;
    font-size: .31rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em i {
    width: .08rem;
    right: -.12rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num em i img {
    width: 100%; }
  .image-swiper .lc-d-banner .lc-d-bottom li .num span {
    height: .36rem;
    line-height: .36rem;
    font-size: .31rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .title {
    line-height: .32rem;
    font-size: .19rem;
    margin-top: -.07rem; }
  .image-swiper .lc-d-banner .lc-d-bottom li .text {
    display: none; } }

.image-swiper .inner1450 {
  width: 75.52083%;
  margin: 0 auto;
  position: relative; }

@media screen and (max-width: 1023px) {
  .image-swiper .inner1450 {
    width: 6.88rem; } }

.image-swiper .lc-d-com {
  padding-top: 3.07292%;
  padding-bottom: 3.64583%; }

.image-swiper .lc-d-com .lc-d-com-list-container {
  margin-top: 4.62069%; }

.image-swiper .lc-d-com .lc-d-com-list-container li {
  position: relative;
  width: 19.31034%;
  margin-right: .82759%;
  float: left;
  -webkit-box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
  text-align: center;
  cursor: pointer; }

.image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(5) {
  margin-right: 0; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container {
  padding-top: 124.28571%;
  position: relative;
  background: #fff url("../images/lc74.png") left top/cover no-repeat;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container .lc-container-title-touch, .image-swiper .lc-d-com .lc-d-com-list-container li .container .lc-container-text-touch {
  display: none; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container:hover {
  background-color: #d02125; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container:hover .img_wrap .lc-img-red {
  opacity: 0; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container:hover .img_wrap .lc-img-white {
  opacity: 1; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container:hover .lc-line {
  width: 78.43137%; }

.image-swiper .lc-d-com .lc-d-com-list-container li .container:hover .lc-container-title, .image-swiper .lc-d-com .lc-d-com-list-container li .container:hover .lc-container-text {
  color: #fff; }

.image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap {
  width: 31.42857%;
  position: absolute;
  top: 17.24138%;
  left: 0;
  right: 0;
  margin: 0 auto; }

.image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap .lc-iw {
  padding-top: 100%;
  position: relative; }

.image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap .lc-img-red, .image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap .lc-img-white {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap .lc-img-red {
  opacity: 1; }

.image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap .lc-img-white {
  opacity: 0; }

.image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-title {
  line-height: 1.41667;
  font-weight: bold;
  color: #333;
  width: 100%;
  position: absolute;
  font-size: 24px;
  top: 46.83908%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  text-align: center; }

.image-swiper .lc-d-com .lc-d-com-list-container li .lc-line {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  width: 58.57143%;
  height: 1px;
  background: #f1f1f1;
  left: 0;
  right: 0;
  margin: 0 auto;
  position: absolute;
  top: 66.95402%; }

.image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text {
  position: absolute;
  line-height: 1.57143;
  font-size: 14px;
  color: #666;
  width: 85.71429%;
  top: 77.58621%;
  left: 0;
  right: 0;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  margin: 0 auto; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-title {
    font-size: 18px;
    top: 49.83908%; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text {
    width: 90%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-title {
    font-size: 16px;
    top: 49.83908%; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text {
    width: 90%;
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-line {
    top: 64.95402%; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text {
    width: 90%;
    font-size: 12px;
    top: 70.58621%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d-com {
    background: #fff;
    padding-bottom: .94rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container {
    margin-top: .57rem;
    height: 3.57rem;
    width: 100%;
    position: relative; }
  .image-swiper .lc-d-com .lc-d-com-list-container li {
    position: absolute;
    width: 1.75rem;
    height: 1.99rem;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-size: cover; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .img_wrap {
    width: .5rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .container {
    background: url("../images/lc110.png");
    background-size: 100% 100%; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:hover .container {
    background: url("../images/lc110.png");
    background-size: 100% 100%; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:hover .container .img_wrap .lc-img-white {
    opacity: 0; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:hover .container .img_wrap .lc-img-red {
    opacity: 1; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:hover .container .lc-container-title {
    color: #333; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:hover .container .lc-container-text {
    color: #999; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-title {
    font-size: .22rem;
    display: none;
    top: .98rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-title.lc-container-title-touch {
    display: block; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text {
    font-size: .18rem;
    line-height: .24rem;
    height: .48rem;
    overflow: hidden;
    color: #999999;
    display: none;
    top: 1.32rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li .lc-container-text.lc-container-text-touch {
    display: block; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(1) {
    left: 1.26rem;
    top: 0rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(2) {
    left: 3.83rem;
    top: 0rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(3) {
    left: 0rem;
    top: 1.6rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(4) {
    left: 2.57rem;
    top: 1.6rem; }
  .image-swiper .lc-d-com .lc-d-com-list-container li:nth-of-type(5) {
    left: 5.12rem;
    top: 1.6rem; } }

.image-swiper .lc-d-omnibearing {
  background: #f8f8f8;
  padding: 3.38542% 0 3.07292%; }

.image-swiper .lc-d-omnibearing .lc-d-re {
  margin-top: 4.27586%;
  position: relative;
  padding-top: 124px; }

.image-swiper .lc-d-omnibearing .lc-admin, .image-swiper .lc-d-omnibearing .lc-pattern {
  width: 217px;
  height: 74px;
  position: absolute;
  line-height: 74px;
  font-size: 30px;
  text-align: center;
  color: #fff;
  background: #d02125;
  border-radius: 6px;
  top: 0; }

.image-swiper .lc-d-omnibearing .lc-admin {
  left: 34.48276%;
  top: 0; }

.image-swiper .lc-d-omnibearing .lc-pattern {
  top: 78px;
  left: 8.41379%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-d-omnibearing .lc-admin, .image-swiper .lc-d-omnibearing .lc-pattern {
    width: 180px;
    height: 62px;
    line-height: 62px;
    font-size: 24px; }
  .image-swiper .lc-d-omnibearing .lc-d-re img {
    width: 742px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-d-omnibearing .lc-d-re {
    padding-top: 8.55172%; }
  .image-swiper .lc-d-omnibearing .lc-admin, .image-swiper .lc-d-omnibearing .lc-pattern {
    width: 150px;
    height: 51px;
    line-height: 51px;
    font-size: 18px; }
  .image-swiper .lc-d-omnibearing .lc-pattern {
    top: 40px; }
  .image-swiper .lc-d-omnibearing .lc-d-re img {
    width: 642px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-d-omnibearing .lc-admin, .image-swiper .lc-d-omnibearing .lc-pattern {
    width: 130px;
    height: 44px;
    line-height: 44px;
    font-size: 16px; }
  .image-swiper .lc-d-omnibearing .lc-pattern {
    top: 40px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-d-omnibearing .lc-admin, .image-swiper .lc-d-omnibearing .lc-pattern {
    width: 120px;
    height: 40px;
    line-height: 40px;
    font-size: 14px; }
  .image-swiper .lc-d-omnibearing .lc-d-re img {
    width: 492px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d-omnibearing {
    background: #fff; }
  .image-swiper .lc-d-omnibearing .lc-d-re {
    padding-top: .63rem; }
  .image-swiper .lc-d-omnibearing .lc-d-re img {
    width: 4.8rem; }
  .image-swiper .lc-d-omnibearing .lc-d-re .lc-admin, .image-swiper .lc-d-omnibearing .lc-d-re .lc-pattern {
    line-height: .42rem;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f85e62), to(#e63d41));
    background-image: -webkit-linear-gradient(#f85e62, #e63d41);
    background-image: -o-linear-gradient(#f85e62, #e63d41);
    background-image: linear-gradient(#f85e62, #e63d41);
    height: auto;
    font-size: .22rem; }
  .image-swiper .lc-d-omnibearing .lc-d-re .lc-admin {
    width: 1.4rem;
    left: 3.11rem; }
  .image-swiper .lc-d-omnibearing .lc-d-re .lc-pattern {
    width: 1.72rem;
    left: .08rem;
    top: .56rem; } }

.image-swiper .lc-d .lc-title {
  font-size: 48px;
  color: #333333;
  font-weight: bold;
  line-height: 1.41667;
  margin-bottom: .48276%;
  text-align: center; }

.image-swiper .lc-d .lc-text {
  color: #999999;
  font-size: 20px;
  text-align: center;
  line-height: 1.5; }

.image-swiper .lc-d .lc-text-touch {
  display: none; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-d .lc-title {
    font-size: 38px; }
  .image-swiper .lc-d .lc-text {
    font-size: 19px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-d .lc-title {
    font-size: 32px; }
  .image-swiper .lc-d .lc-text {
    font-size: 16px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-d .lc-title {
    font-size: 24px; }
  .image-swiper .lc-d .lc-text {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d .lc-title {
    font-size: .33rem;
    margin-bottom: .13rem; }
  .image-swiper .lc-d .lc-text {
    font-size: .19rem;
    line-height: .32rem;
    display: none; }
  .image-swiper .lc-d .lc-text-touch {
    display: block; } }

.image-swiper .lc-d-server {
  padding-top: 3.17708%;
  padding-bottom: 11.04167%;
  background-image: url("../images/lc84.jpg"); }

.image-swiper .lc-d-server ul {
  padding-top: 6.55172%; }

.image-swiper .lc-d-server ul li {
  width: 11.7931%;
  margin-right: .75862%;
  float: left;
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-d-server ul li:nth-of-type(8) {
  margin-right: 0; }

.image-swiper .lc-d-server ul li .lc-box {
  position: relative;
  padding-top: 105.26316%; }

.image-swiper .lc-d-server ul li .img_wrap {
  width: 36.25731%;
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 26.11111%; }

.image-swiper .lc-d-server ul li .lc-li-text {
  color: #333333;
  font-size: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 17.22222%;
  text-align: center;
  line-height: 1.5; }

.image-swiper .lc-d-server ul li:hover {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  transform: translateY(-10px); }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-d-server ul li .lc-li-text {
    font-size: 16px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-d-server ul li .lc-li-text {
    font-size: 14px;
    bottom: 15.22222%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d-server {
    margin-top: 0.4rem;
    padding-top: 0.5rem;
    background-size: 150%;
    padding-top: .61rem; }
  .image-swiper .lc-d-server ul {
    width: 4.97rem;
    margin: 0 auto; }
  .image-swiper .lc-d-server ul li {
    width: 25%;
    margin-right: 0; }
  .image-swiper .lc-d-server ul li .lc-li-text {
    font-size: .2rem; } }

.image-swiper .lc-d-hot {
  padding-bottom: 3.38542%; }

.image-swiper .lc-d-hot ul {
  padding-top: 3.69792%; }

.image-swiper .lc-d-hot ul li {
  width: 25%;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-d-hot ul li .lc-box {
  padding-top: 65.625%;
  position: relative;
  color: #fff; }

.image-swiper .lc-d-hot ul li .lc-box .lc-one {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  left: 0;
  padding: 0 12.08333% 0 12.29167%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-left {
  width: -webkit-calc(100% - 45.02762%);
  width: calc(100% - 45.02762%);
  position: relative;
  height: 100%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-top {
  position: absolute;
  top: 20.63492%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-app-name {
  font-size: 24px;
  line-height: 50px; }

.image-swiper .lc-d-hot ul li .lc-box .lc-app-logo {
  margin-right: 4px; }

.image-swiper .lc-d-hot ul li .lc-box .lc-app-view {
  width: 45.02762%;
  right: 16.29834%;
  margin-top: 3.31492%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-bottom {
  position: absolute;
  width: 100%;
  bottom: 14.92063%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-bottom .lc-qrcode-item {
  position: relative;
  width: 41%;
  margin-right: 9%; }

.image-swiper .lc-d-hot ul li .lc-box .lc-bottom .lc-qrcode-item:nth-of-type(2) {
  margin-right: 0; }

.image-swiper .lc-d-hot ul li .lc-box .lc-describe {
  font-size: 14px;
  line-height: 1.71429;
  text-align: center;
  width: 100%;
  margin-bottom: 4.87805%;
  text-transform: uppercase; }

.image-swiper .lc-d-hot .lc-d-hot-touch {
  display: none; }

.image-swiper .lc-d-hot .lc-more {
  font-size: 22px;
  width: 369px;
  line-height: 76px;
  border: 1px solid #c42527;
  text-align: center;
  margin: 3.69792% auto 0;
  color: #c42527;
  font-size: 22px;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  display: block; }

.image-swiper .lc-d-hot .lc-more:hover {
  border-top-left-radius: 40px;
  border-bottom-right-radius: 40px; }

.image-swiper .lc-d-hot .lc-describe-en {
  display: none; }

.image-swiper .lc-d-hot .lc-ercode {
  display: none; }

@media screen and (max-width: 1680px) {
  .image-swiper .lc-d-hot ul li .lc-box .lc-one {
    padding: 0 8.08333% 0 8.29167%; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-view {
    width: 41.02752%; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-left {
    width: -webkit-calc(100% - 41.5%);
    width: calc(100% - 41.5%); }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-logo {
    width: 40px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-name {
    line-height: 40px;
    font-size: 18px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe {
    font-size: 12px; }
  .image-swiper .lc-d-hot .lc-more {
    width: 300px;
    height: 61px;
    line-height: 61px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-logo {
    width: 34px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-name {
    line-height: 34px;
    font-size: 14px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe {
    font-size: 12px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe-en {
    display: block; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe-cn {
    display: none; }
  .image-swiper .lc-d-hot .lc-more {
    width: 240px;
    height: 49px;
    line-height: 49px;
    font-size: 18px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-d-hot ul li .lc-box .lc-top {
    top: 15.63%; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-logo {
    width: 34px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-app-name {
    line-height: 34px;
    font-size: 14px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe {
    font-size: 12px; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe-en {
    display: block; }
  .image-swiper .lc-d-hot ul li .lc-box .lc-describe-cn {
    display: none; }
  .image-swiper .lc-d-hot .lc-more {
    width: 230px;
    height: 47px;
    line-height: 47px;
    font-size: 16px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-d-hot {
    padding-bottom: 1.05rem; }
  .image-swiper .lc-d-hot ul {
    display: none; }
  .image-swiper .lc-d-hot .lc-d-hot-touch {
    position: relative;
    display: block; }
  .image-swiper .lc-d-hot .lc-d-hot-touch-sw {
    margin-top: .56rem; }
  .image-swiper .lc-d-hot .lc-d-hot-touch-sw .swiper-slide {
    width: 2.9rem;
    height: 4.7rem;
    padding-top: 2.18rem;
    position: relative; }
  .image-swiper .lc-d-hot .lc-logo-icon {
    width: .84rem;
    height: .84rem;
    position: absolute;
    top: 0.1rem;
    left: 0;
    right: 0;
    margin: 0 auto;
    opacity: 0;
    border-radius: 0.1rem;
    overflow: hidden;
    -webkit-box-shadow: 0 0 0.08rem 0.04rem rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 0.08rem 0.04rem rgba(0, 0, 0, 0.2); }
  .image-swiper .lc-d-hot .swiper-slide-active .lc-logo-icon {
    opacity: 1; }
  .image-swiper .lc-d-hot .lc-d-touch-view {
    width: 3.38rem;
    height: 6.44rem;
    position: absolute;
    top: 1.5rem;
    left: 2.2rem;
    right: 0;
    pointer-events: none;
    z-index: 5; }
  .image-swiper .lc-d-hot .lc-d-touch-view img {
    margin-left: 0; }
  .image-swiper .lc-d-hot .lc-ercode {
    position: relative;
    height: 2.12rem;
    display: block; }
  .image-swiper .lc-d-hot .lc-ercode-item {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1; }
  .image-swiper .lc-d-hot .lc-ercode-item.lc-active {
    opacity: 1;
    z-index: 2; }
  .image-swiper .lc-d-hot .lc-ercode {
    width: 5rem;
    margin: 1.36rem auto 0;
    text-align: center; }
  .image-swiper .lc-d-hot .lc-ercode .img_wrap {
    width: 1.04rem;
    height: 1.04rem;
    margin: 0 auto;
    -webkit-box-shadow: 8px 8px 15px 1px rgba(0, 0, 0, 0.2);
    box-shadow: 8px 8px 15px 1px rgba(0, 0, 0, 0.2); }
  .image-swiper .lc-d-hot .lc-ercode .lc-ercode-text {
    color: #d02125;
    font-size: .28rem;
    line-height: .48rem;
    margin-top: .08rem;
    font-family: "黑体", "微软雅黑", Arial, Helvetica, sans-serif; }
  .image-swiper .lc-d-hot .lc-more {
    width: 1.49rem;
    height: .41rem;
    line-height: .41rem;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f85e62), to(#e63d41));
    background-image: -webkit-linear-gradient(#f85e62, #e63d41);
    background-image: -o-linear-gradient(#f85e62, #e63d41);
    background-image: linear-gradient(#f85e62, #e63d41);
    color: #fff;
    font-size: .22rem;
    border: none;
    border-radius: 4px; } }

@media screen and (max-width: 750px) {
  .image-swiper .lc-d-hot .lc-d-touch-view {
    width: 3.47rem;
    left: 2.1rem; } }

.image-swiper .lc-e-banner {
  padding-top: 34.375%;
  position: relative;
  color: #fff; }

.image-swiper .lc-e-banner .lc-e-banner-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0; }

.image-swiper .lc-e-banner .lc_inner {
  height: 100%;
  position: relative; }

.image-swiper .lc-e-banner .lc-left, .image-swiper .lc-e-banner .lc-right {
  height: 100%; }

.image-swiper .lc-e-banner .lc-left .table, .image-swiper .lc-e-banner .lc-left .table-cell, .image-swiper .lc-e-banner .lc-right .table, .image-swiper .lc-e-banner .lc-right .table-cell {
  display: block;
  width: auto; }

.image-swiper .lc-e-banner .lc-text-container {
  position: absolute;
  top: 35%; }

.image-swiper .lc-e-banner .lc-t1 {
  font-size: 52px;
  line-height: 1.80769; }

.image-swiper .lc-e-banner .lc-t2 {
  font-size: 0;
  line-height: 0;
  margin-top: 4.12088%; }

.image-swiper .lc-e-banner .lc-t2 span {
  display: inline-block;
  font-size: 36px;
  vertical-align: top;
  line-height: 1.5;
  margin-right: 21px; }

.image-swiper .lc-e-banner .lc-t2 span:nth-of-type(1) {
  -webkit-animation-delay: .2s;
  animation-delay: .2s; }

.image-swiper .lc-e-banner .lc-t2 span:nth-of-type(2) {
  -webkit-animation-delay: .4s;
  animation-delay: .4s; }

.image-swiper .lc-e-banner .lc-t2 span:nth-of-type(3) {
  -webkit-animation-delay: .6s;
  animation-delay: .6s; }

.image-swiper .lc-e-banner .lc-t2 span:nth-of-type(4) {
  -webkit-animation-delay: .8s;
  animation-delay: .8s; }

.image-swiper .lc-e-banner .lc-right span {
  color: #d51920;
  line-height: 1.0875;
  font-family: "din";
  font-size: 80px; }

.image-swiper .lc-e-banner .lc-right .lc-right-container {
  position: absolute;
  top: 36.81818%;
  right: 0; }

.image-swiper .lc-e-banner .lc-right li {
  float: left;
  margin-left: 59px; }

.image-swiper .lc-e-banner .lc-right li:nth-of-type(1) {
  -webkit-animation-delay: .2s;
  animation-delay: .2s; }

.image-swiper .lc-e-banner .lc-right li:nth-of-type(2) {
  -webkit-animation-delay: .4s;
  animation-delay: .4s; }

.image-swiper .lc-e-banner .lc-right li:nth-of-type(3) {
  -webkit-animation-delay: .6s;
  animation-delay: .6s; }

.image-swiper .lc-e-banner .lc-right .lc-bottom {
  font-size: 16px;
  line-height: 1.75; }

.image-swiper .lc-e-banner em {
  font-style: normal;
  vertical-align: text-bottom; }

.image-swiper .lc-e-banner .f24 {
  font-size: 24px; }

.image-swiper .lc-e-banner .f36 {
  font-size: 36px; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-banner .lc-t1 {
    font-size: 36px; }
  .image-swiper .lc-e-banner .lc-t2 span {
    font-size: 25px; }
  .image-swiper .lc-e-banner .lc-right span {
    font-size: 60px; }
  .image-swiper .lc-e-banner .f24 {
    font-size: 20px; }
  .image-swiper .lc-e-banner .f36 {
    font-size: 26px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-e-banner .lc-t1 {
    font-size: 30px; }
  .image-swiper .lc-e-banner .lc-t2 span {
    font-size: 20px; }
  .image-swiper .lc-e-banner .lc-right span {
    font-size: 50px; }
  .image-swiper .lc-e-banner .lc-right li {
    margin-left: 40px; }
  .image-swiper .lc-e-banner .f24 {
    font-size: 19px; }
  .image-swiper .lc-e-banner .f36 {
    font-size: 24px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-banner {
    height: 7.32rem;
    padding: 0;
    background-position: 60% center; }
  .image-swiper .lc-e-banner .lc-text-container {
    top: 27.73224%; }
  .image-swiper .lc-e-banner .lc-t1 {
    font-size: 0.52rem;
    line-height: 0.7rem; }
  .image-swiper .lc-e-banner .lc-t2 span {
    font-size: 0.36rem; }
  .image-swiper .lc-e-banner .lc-right .lc-right-container {
    top: 54.91803%;
    width: 100%; }
  .image-swiper .lc-e-banner .lc-right ul {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between; }
  .image-swiper .lc-e-banner .lc-right ul::before, .image-swiper .lc-e-banner .lc-right ul::after {
    display: none; }
  .image-swiper .lc-e-banner .lc-right li {
    margin-left: 0; }
  .image-swiper .lc-e-banner .lc-right span {
    font-size: 0.8rem; }
  .image-swiper .lc-e-banner .lc-right .lc-bottom {
    font-size: 0.2rem; }
  .image-swiper .lc-e-banner .lc-right .f24 {
    font-size: 0.24rem; }
  .image-swiper .lc-e-banner .lc-right .f36 {
    font-size: 0.36rem; } }

.image-swiper .lc-about {
  padding-top: 4.21875%; }

.image-swiper .lc-about .lc-about-title-container {
  position: relative;
  padding-bottom: 2.15278%; }

.image-swiper .lc-about .lc-about-title-container .lc-cn {
  font-family: "pfsc";
  color: #333;
  font-size: 40px;
  line-height: 1.35; }

.image-swiper .lc-about .lc-about-title-container .lc-en {
  font-family: "pfsc";
  color: #999999;
  font-size: 24px;
  line-height: 1.375;
  margin-top: -8px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  text-transform: uppercase; }

.image-swiper .lc-about .lc-er-bread {
  line-height: 25px;
  color: #808080;
  position: absolute;
  bottom: 17.27273%;
  right: 0; }

.image-swiper .lc-about .lc-er-bread img {
  vertical-align: top;
  display: inline-block;
  margin-right: 4px; }

.image-swiper .lc-about .lc-about-text {
  color: #333;
  font-size: 20px;
  line-height: 1.8; }

.image-swiper .lc-about .lc-about-text .wow:nth-of-type(1) {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: 0s;
  animation-delay: 0s; }

.image-swiper .lc-about .lc-about-text .wow:nth-of-type(2) {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: .4s;
  animation-delay: .4s; }

.image-swiper .lc-about .lc-about-text .wow:nth-of-type(3) {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: .8s;
  animation-delay: .8s; }

.image-swiper .lc-about .er-about-nav-container {
  line-height: 37px;
  padding-bottom: 7px;
  position: relative;
  z-index: 3; }

.image-swiper .lc-about .er-about-nav-container li {
  padding-right: 28px;
  padding-left: 12px;
  position: relative;
  float: left;
  color: #666;
  font-size: 18px; }

.image-swiper .lc-about .er-about-nav-container li:last-of-type {
  padding-right: 0; }

.image-swiper .lc-about .er-about-nav-container li::before {
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto 0;
  width: 6px;
  height: 6px;
  content: '';
  background: #a9a9a9;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-about .er-about-nav-container li.active::before, .image-swiper .lc-about .er-about-nav-container li:hover::before {
  background: #d51920; }

.image-swiper .lc-about .er-about-nav-container li.active a::after, .image-swiper .lc-about .er-about-nav-container li:hover a::after {
  width: 100%; }

.image-swiper .lc-about .er-about-nav-container li a {
  display: block;
  padding: 0 4px;
  position: relative; }

.image-swiper .lc-about .er-about-nav-container li a::after {
  display: block;
  position: absolute;
  bottom: -9px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 0;
  height: 3px;
  content: '';
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  background: #d51920; }

.image-swiper .lc-about .lc-line {
  width: 100%;
  border-top: 1px solid #e0e0e0;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  padding-bottom: 5.625%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-about .lc-about-text {
    font-size: 16px;
    line-height: 1.75;
    padding-bottom: 3.2%; }
  .image-swiper .lc-about .lc-about-title-container .lc-cn {
    font-size: 34px; }
  .image-swiper .lc-about .lc-about-title-container .lc-en {
    font-size: 21px; }
  .image-swiper .lc-about .er-about-nav-container li {
    font-size: 16px;
    padding-right: 20px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-about .lc-about-text {
    font-size: 14px; }
  .image-swiper .lc-about .lc-about-title-container .lc-cn {
    font-size: 30px; }
  .image-swiper .lc-about .lc-about-title-container .lc-en {
    font-size: 19px; }
  .image-swiper .lc-about .lc-er-bread {
    font-size: 14px; }
  .image-swiper .lc-about .er-about-nav-container li {
    font-size: 14px;
    padding-right: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-about {
    padding-top: 0.2rem; }
  .image-swiper .lc-about .lc_inner {
    padding-top: 0.64rem;
    position: relative; }
  .image-swiper .lc-about .lc-about-text {
    font-size: 0.24rem; }
  .image-swiper .lc-about .lc-about-title-container {
    position: static; }
  .image-swiper .lc-about .lc-about-title-container .lc-cn {
    font-size: 0.4rem; }
  .image-swiper .lc-about .lc-about-title-container .lc-en {
    font-size: 0.24rem;
    margin-top: 0; }
  .image-swiper .lc-about .lc-er-bread {
    font-size: 0.2rem;
    line-height: 0.24rem;
    position: absolute;
    top: 0.2rem;
    bottom: auto;
    z-index: 3; }
  .image-swiper .lc-about .lc-er-bread img {
    height: 0.24rem; }
  .image-swiper .lc-about .er-about-nav-container {
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%; }
  .image-swiper .lc-about .er-about-nav-container ul {
    width: 10000px;
    float: none; }
  .image-swiper .lc-about .er-about-nav-container li {
    font-size: 0.24rem;
    padding-right: 0.4rem;
    padding-left: 0.17rem; }
  .image-swiper .lc-about .er-about-nav-container li::before {
    width: 0.06rem;
    height: 0.06rem; } }

.image-swiper .lc-company {
  position: relative; }

.image-swiper .lc-company .img_container {
  width: 50.05208%;
  float: right; }

.image-swiper .lc-company .img_container .img_wrap {
  position: relative;
  padding-top: 58.16857%;
  background: red; }

.image-swiper .lc-company .img_container .img_wrap img, .image-swiper .lc-company .img_container .img_wrap iframe, .image-swiper .lc-company .img_container .img_wrap video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.image-swiper .lc-company .lc_inner {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 3;
  position: absolute;
  top: 0;
  height: 100%;
  left: 0;
  right: 0;
  margin: 0 auto;
  padding-right: 40.52083%; }

.image-swiper .lc-company .lc-company-text {
  margin-top: 6.92771%;
  color: #666;
  font-size: 16px;
  line-height: 2.125;
  margin-bottom: 6.1747%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-company .lc-company-text {
    font-size: 14px;
    line-height: 1.75; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-company .img_container {
    width: 100%;
    float: none; }
  .image-swiper .lc-company .lc_inner {
    position: static;
    padding: 0;
    padding-top: 0.49rem;
    position: relative; }
  .image-swiper .lc-company .lc-company-text {
    font-size: 0.24rem;
    line-height: 1.5; }
  .image-swiper .lc-company .lc-e-more {
    position: absolute;
    top: 0.48rem;
    right: 0; } }

.image-swiper .lc-e-more {
  width: 46px;
  height: 46px;
  border-radius: 100%;
  border: 1px solid #c3c3c3;
  display: block;
  position: relative;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  z-index: 2; }

.image-swiper .lc-e-more::after {
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  display: block;
  content: '';
  background: #cf1920;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  border-radius: 100%; }

.image-swiper .lc-e-more svg {
  display: block;
  fill: none;
  stroke: #696766;
  stroke-width: 2;
  width: 14px;
  height: 10px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  z-index: 3; }

.image-swiper .lc-e-more:hover {
  border: 1px solid #d01920; }

.image-swiper .lc-e-more:hover svg {
  stroke: #fff; }

.image-swiper .lc-e-more:hover::after {
  width: 100%;
  height: 100%;
  z-index: -1; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-more {
    width: 41px;
    height: 41px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-more {
    width: 39px;
    height: 39px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-more {
    width: 0.46rem;
    height: 0.46rem; }
  .image-swiper .lc-e-more svg {
    stroke-width: 1; } }

.image-swiper .lc-e-title-container .lc-e-title-t1 {
  font-size: 30px;
  line-height: 1.66667;
  color: #000; }

.image-swiper .lc-e-title-container .lc-e-title-line {
  margin-bottom: 18px;
  width: 46px;
  height: 3px;
  background: #656565;
  margin-top: 13px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s; }

.image-swiper .lc-e-title-container .lc-e-title-text {
  font-size: 20px;
  line-height: 1.5;
  color: #5c5c5c;
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-title-container .lc-e-title-t1 {
    font-size: 25px; }
  .image-swiper .lc-e-title-container .lc-e-title-line {
    margin-bottom: 16px;
    width: 46px;
    height: 2px;
    margin-top: 11px; }
  .image-swiper .lc-e-title-container .lc-e-title-text {
    font-size: 17px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-title-container .lc-e-title-t1 {
    font-size: 22px; }
  .image-swiper .lc-e-title-container .lc-e-title-line {
    margin-bottom: 14px;
    width: 46px;
    margin-top: 10px; }
  .image-swiper .lc-e-title-container .lc-e-title-text {
    font-size: 15px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-title-container .lc-e-title-t1 {
    font-size: 0.3rem; }
  .image-swiper .lc-e-title-container .lc-e-title-line {
    margin-bottom: 0.27rem;
    width: 0.46rem;
    margin-top: 0.12rem; }
  .image-swiper .lc-e-title-container .lc-e-title-text {
    font-size: 0.26rem; } }

.image-swiper .lc-e-honor {
  padding-top: 7.65625%;
  position: relative; }

.image-swiper .lc-e-honor .lc-e-honer-container {
  padding-top: 6.92708%;
  padding-bottom: 8.17708%;
  position: relative;
  background-image: url("../images/lc121.jpg"); }

.image-swiper .lc-e-honor .lc-e-img_container {
  width: 32.76042%;
  position: absolute;
  bottom: 0;
  left: 0; }

.image-swiper .lc-e-honor .lc-e-img_container .lc-e-img_container {
  padding-top: 117.32909%; }

.image-swiper .lc-e-honor .lc-e-img_container .lc-e-img_container img {
  display: block;
  width: 100%;
  height: 100%; }

.image-swiper .lc-e-honor .lc-e-honer-sw {
  width: 77.77778%; }

.image-swiper .lc-e-honor .swiper-slide:hover img {
  -webkit-transform: rotateX(360deg);
  transform: rotateX(360deg);
  -webkit-transition: 1s;
  -o-transition: 1s;
  transition: 1s; }

.image-swiper .lc-e-honor .lc_inner {
  position: relative; }

.image-swiper .lc-e-honor .lc-e-honer-sw-next {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  right: -112px;
  border-color: #fff;
  z-index: 4; }

.image-swiper .lc-e-honor .lc-e-honer-sw-next svg {
  stroke: #fff; }

.image-swiper .lc-e-honor .lc-e-honer-sw-next:hover {
  border-color: #d01920; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-honor {
    padding-top: 10.65625%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-honor {
    padding-top: 20%; }
  .image-swiper .lc-e-honor .lc-e-honer-container {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-bottom: 0;
    padding-top: 4.34rem;
    background-size: cover;
    background-image: url("../images/lc127.jpg");
    padding-bottom: 1.37rem; }
  .image-swiper .lc-e-honor .lc-e-honer-sw {
    width: 100%; }
  .image-swiper .lc-e-honor .lc_inner {
    padding-top: 0; }
  .image-swiper .lc-e-honor .lc-e-img_container {
    width: 5.15rem;
    bottom: 5.99rem; }
  .image-swiper .lc-e-honor .lc-e-honer-sw-next {
    bottom: -0.92rem;
    top: auto;
    left: 0;
    right: 0;
    margin: 0 auto; }
  .image-swiper .lc-e-honor .lc-e-honer-sw img {
    width: 100%; } }

.image-swiper .lc-e-team {
  padding-top: 7.23958%;
  padding-bottom: 7.96875%; }

.image-swiper .lc-e-team .lc-e-team-pc {
  display: block;
  background: url("../limages/lc128.png");
  background-repeat: no-repeat;
  background-position: 68% center; }

.image-swiper .lc-e-team .lc-e-team-touch {
  display: none; }

.image-swiper .lc-e-team .lc-team-pc-left {
  width: 57.22222%;
  position: relative; }

.image-swiper .lc-e-team .lc-team-pc-right {
  width: 39.93056%; }

.image-swiper .lc-e-team .lc-team-pc-right-item {
  margin-top: 3.47826%;
  overflow: hidden; }

.image-swiper .lc-e-team .lc-team-pc-right-item:first-of-type {
  margin-top: 0; }

.image-swiper .lc-e-team .lc-team-pc-right-item img {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-e-team .lc-team-pc-right-item:hover img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  transform: scale(1.05); }

.image-swiper .lc-e-team .lc-e-more {
  position: absolute;
  top: 1.56495%;
  right: 0; }

.image-swiper .lc-e-team .lc-team-pc-left-text {
  color: #5c5c5c;
  font-size: 16px;
  line-height: 1.875;
  margin-top: 5.33981%;
  padding-right: 6.43204%; }

.image-swiper .lc-e-team .lc-team-team-container {
  margin-top: 1.09223%;
  margin-bottom: 4.00485%;
  padding-right: 6.43204%; }

.image-swiper .lc-e-team .lc-team-team-container .lc-team-team {
  width: 34.34466%; }

.image-swiper .lc-e-team .lc-team-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.67);
  -webkit-transform: scale(0) rotateX(180deg);
  transform: scale(0) rotateX(180deg); }

.image-swiper .lc-e-team .lc-team-sw {
  padding-bottom: 3.2767%; }

.image-swiper .lc-e-team .swiper-slide {
  cursor: pointer;
  overflow: hidden; }

.image-swiper .lc-e-team .swiper-slide img {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-e-team .swiper-slide:hover .lc-team-mask {
  -webkit-transform: scale(1) rotateX(0deg);
  transform: scale(1) rotateX(0deg); }

.image-swiper .lc-e-team .swiper-slide:hover img {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1); }

.image-swiper .lc-e-team .lc-team-sw-pagination {
  font-size: 0;
  line-height: 0;
  bottom: 0;
  position: absolute;
  width: 100%;
  text-align: center; }

.image-swiper .lc-e-team .lc-team-sw-pagination .swiper-pagination-bullet {
  vertical-align: top;
  height: 2px;
  width: 37px;
  border-radius: 0;
  background: #e6e6e6;
  opacity: 1; }

.image-swiper .lc-e-team .lc-team-sw-pagination .swiper-pagination-bullet-active {
  background: #d51920;
  margin: 0 2.42718%; }

@media screen and (max-width: 1680px) {
  .image-swiper .lc-e-team .lc-team-team-container {
    margin-top: 1%;
    margin-bottom: 1%; }
  .image-swiper .lc-e-team .lc-team-team-container .lc-team-team {
    width: 24%; }
  .image-swiper .lc-e-team .lc-team-pc-left-text {
    font-size: 14px;
    line-height: 1.5; }
  .image-swiper .lc-e-team .lc-team-mask {
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-team .lc-team-team-container {
    margin-top: 0%;
    margin-bottom: 0%;
    display: none; }
  .image-swiper .lc-e-team .lc-team-team-container .lc-team-team {
    width: 24%; }
  .image-swiper .lc-e-team .lc-team-pc-left-text {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 2.785%; }
  .image-swiper .lc-e-team .lc-team-mask {
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-e-team .lc-team-team-container {
    margin-top: 0%;
    margin-bottom: 0%;
    display: none; }
  .image-swiper .lc-e-team .lc-team-team-container .lc-team-team {
    width: 24%; }
  .image-swiper .lc-e-team .lc-team-pc-left-text {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 2.785%;
    margin-top: 2.785%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-team {
    padding-bottom: 0.42rem;
    padding-top: 0.42rem; }
  .image-swiper .lc-e-team .lc-e-team-pc {
    display: none;
    background-size: 50%;
    background-position: center 47%; }
  .image-swiper .lc-e-team .lc-e-team-touch {
    display: block;
    position: relative; }
  .image-swiper .lc-e-team .lc_inner {
    position: relative;
    padding-top: 0; }
  .image-swiper .lc-e-team .lc-team-pc-right {
    width: 100%;
    padding-top: 1.88rem;
    z-index: 2;
    position: relative; }
  .image-swiper .lc-e-team .lc-team-pc-left {
    width: 100%;
    position: static; }
  .image-swiper .lc-e-team .lc-e-title-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3; }
  .image-swiper .lc-e-team .lc-e-more {
    top: 0;
    z-index: 3; }
  .image-swiper .lc-e-team .lc-team-sw-pagination {
    opacity: 0; }
  .image-swiper .lc-e-team .lc-team-pc-left-text {
    margin-top: 0.47rem;
    font-size: 0.24rem;
    line-height: 1.5;
    background-repeat: no-repeat;
    background-size: 45%;
    background-position: center -65%;
    padding-right: 0; }
  .image-swiper .lc-e-team .lc-team-team-container {
    display: block;
    margin: 0 auto;
    width: 2.84rem;
    padding-top: 0.4rem;
    margin-bottom: 0.31rem;
    background: url("../images/lc128.png");
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: center 130%;
    padding-right: 0; }
  .image-swiper .lc-e-team .lc-team-team-container .lc-team-team {
    width: 100%; } }

.image-swiper .lc-e-cooperation {
  padding-top: 4.58333%;
  padding-bottom: 4.63542%;
  position: relative;
  background: #c32026;
  color: #fff;
  text-align: center; }

.image-swiper .lc-e-cooperation .left {
  width: 38.54167%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  overflow: hidden; }

.image-swiper .lc-e-cooperation .lc_inner {
  position: relative;
  top: 0;
  left: 0;
  height: 100%;
  right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0 auto;
  padding-left: 26.14583%;
  padding-bottom: 15.9375%; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-cn1 {
  font-size: 50px;
  line-height: 1.4; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-cn2 {
  font-size: 24px;
  line-height: 1.41667;
  margin-top: 1.38298%;
  margin-bottom: 4.46809%; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul-container {
  padding-top: 32.06019%;
  width: 91.91489%;
  position: relative; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul {
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  top: 0; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul li {
  width: 33.33%;
  float: left;
  height: 50%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid #fff;
  padding-top: 1%;
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(3n) {
  border-right: none; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(1), .image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(2), .image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(3) {
  border-bottom: 1px solid #fff; }

.image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(4), .image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(5), .image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(6) {
  padding-top: 2.5%;
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft; }

.image-swiper .lc-e-cooperation .lc-e-li-top {
  font-size: 68px;
  font-family: "din";
  line-height: 1.32353; }

.image-swiper .lc-e-cooperation .lc-e-li-top sup {
  font-size: 14px;
  vertical-align: text-top;
  position: absolute;
  right: -20px;
  top: 5%;
  line-height: 20px;
  height: 20px; }

.image-swiper .lc-e-cooperation .lc-e-li-top em {
  position: relative;
  font-style: normal; }

.image-swiper .lc-e-cooperation .lc-e-list-container {
  position: absolute;
  bottom: 9.70556%;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 75%;
  z-index: 3; }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide {
  width: 20%;
  border: 1px solid #e6e6e6;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  -webkit-animation-duration: 1s;
  animation-duration: 1s; }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(6), .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(7), .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(8), .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(9), .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(10) {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown; }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide img {
  -webkit-transition: 1s;
  -o-transition: 1s;
  transition: 1s; }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:hover img {
  -webkit-transform: rotateY(360deg);
  transform: rotateY(360deg); }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn1 {
    font-size: 40px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn2 {
    font-size: 19px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul .lc-e-li-top {
    font-size: 50px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn1 {
    font-size: 35px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn2 {
    font-size: 16px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul-container {
    width: 95%; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul .lc-e-li-top {
    font-size: 40px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul .lc-e-li-bottom {
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn1 {
    font-size: 30px; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn2 {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-cooperation {
    padding-top: 0; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn1 {
    font-size: 0.50rem; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-cn2 {
    font-size: 0.24rem; }
  .image-swiper .lc-e-cooperation .left {
    position: static;
    width: 100%; }
  .image-swiper .lc-e-cooperation .lc_inner {
    padding-left: 0;
    width: 100%;
    padding-bottom: 0.36rem;
    padding-top: 0.31rem; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul-container {
    width: 100%;
    padding-top: 48.26667%; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul .lc-e-li-bottom {
    font-size: 0.22rem; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul .lc-e-li-top {
    font-size: 0.68rem; }
  .image-swiper .lc-e-cooperation .lc-e-cooperation-ul li:nth-of-type(1) .lc-e-li-bottom {
    padding: 0 0.3rem; }
  .image-swiper .lc-e-cooperation .lc-e-list-container {
    width: 93.33333%;
    position: relative;
    overflow: hidden;
    bottom: 0; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide {
    width: 33.3%; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(1) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(2) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(3) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(4) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(5) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(6) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(7) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(8) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(9) {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft; }
  .image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide:nth-of-type(10) {
    display: none; } }

.image-swiper .lc-e-big {
  padding-top: 3.69792%;
  background-image: url("../images/lc131.jpg");
  background-size: cover; }

.image-swiper .lc-e-big .lc-e-big-sw {
  margin-top: -85px; }

.image-swiper .lc-e-big .lc-e-big-line {
  width: 100%;
  height: 2px;
  background: #e6e6e6; }

.image-swiper .lc-e-big .lc-e-big-container {
  padding-top: 537px;
  position: relative; }

.image-swiper .lc-e-big .lc-e-big-container .lc_inner {
  height: 170px; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-year {
  font-size: 34px;
  color: #333;
  width: 100%;
  top: 17px;
  position: absolute;
  line-height: 65px; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-year-text {
  color: #666666;
  font-size: 18px;
  width: 100%;
  top: 103px;
  position: absolute;
  line-height: 65px; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw, .image-swiper .lc-e-big .lc-e-big-container .swiper-wrapper, .image-swiper .lc-e-big .lc-e-big-container .swiper-slide {
  height: 100%;
  text-align: center; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw .swiper-slide {
  padding-top: 17px; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-prev, .image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-next {
  width: 32px;
  height: 32px;
  position: absolute;
  border-radius: 100%;
  top: 66px;
  z-index: 3;
  background: #fff;
  border: 1px solid #cecece; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-prev img:nth-of-type(1), .image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-next img:nth-of-type(1) {
  display: block; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-prev img:nth-of-type(2), .image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-next img:nth-of-type(2) {
  display: none; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-prev {
  left: 0; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-big-sw-next {
  right: 0; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-button-disabled {
  background: #d51920;
  border: 1px solid #d51920; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-button-disabled img:nth-of-type(2) {
  display: block; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-button-disabled img:nth-of-type(1) {
  display: none; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-dot {
  width: 18px;
  height: 18px;
  border: 1px solid #cccccc;
  background: #fff;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 73px;
  border-radius: 100%; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-dot::after {
  display: block;
  content: '';
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 100%;
  background: #666; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:nth-of-type(2n) .lc-e-year {
  font-size: 34px;
  color: #333;
  width: 100%;
  top: 103px; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:nth-of-type(2n) .lc-e-year-text {
  color: #666666;
  font-size: 18px;
  width: 100%;
  top: 17px; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:nth-of-type(2n) .lc-text-container {
  bottom: 368px;
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:nth-of-type(2n) .lc-text-container::after {
  -webkit-transform: rotatez(0deg);
  -ms-transform: rotate(0deg);
  transform: rotatez(0deg);
  bottom: -21px;
  top: auto; }

.image-swiper .lc-e-big .lc-e-big-container .lc-text-container {
  position: absolute;
  width: 100%;
  background: #fff;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 4.16667%;
  bottom: 30px;
  left: 0%;
  z-index: 2;
  right: 0;
  margin: 0 auto;
  -webkit-transform: translateX(0%);
  -ms-transform: translateX(0%);
  transform: translateX(0%);
  -webkit-box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.2);
  display: none;
  text-align: left;
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp; }

.image-swiper .lc-e-big .lc-e-big-container .lc-text-container img {
  width: 100%; }

.image-swiper .lc-e-big .lc-e-big-container .lc-text-container .lc-text-wrapper {
  font-size: 14px;
  line-height: 2;
  margin-top: 2.1148%;
  position: relative;
  height: 56px;
  overflow: hidden; }

.image-swiper .lc-e-big .lc-e-big-container .lc-text-container .lc-text-wrapper .lc-text-more {
  position: absolute;
  bottom: 0;
  color: #d51920;
  right: 0;
  background: #fff; }

.image-swiper .lc-e-big .lc-e-big-container .lc-text-container::after {
  display: block;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: auto;
  top: -21px;
  width: 31px;
  height: 21px;
  background: url("../images/lc135.png");
  -webkit-transform: rotatez(180deg);
  -ms-transform: rotate(180deg);
  transform: rotatez(180deg); }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-dot2, .image-swiper .lc-e-big .lc-e-big-container .lc-e-dot3 {
  display: block;
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transform: scale(0.2);
  -ms-transform: scale(0.2);
  transform: scale(0.2);
  opacity: 1;
  border-radius: 100%;
  border: 2px solid #d51920; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-dot2 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s; }

.image-swiper .lc-e-big .lc-e-big-container .lc-e-dot3 {
  -webkit-animation-delay: 1s;
  animation-delay: 1s; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-shot-img-container, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-shot-img-container {
  display: block; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot {
  border-color: #d51920; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot::after, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot::after {
  background: #d51920; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot2, .image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot3, .image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot4, .image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-dot5, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot2, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot3, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot4, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-dot5 {
  -webkit-animation-name: dotbefore;
  animation-name: dotbefore;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-text-container, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-text-container {
  display: block; }

.image-swiper .lc-e-big .lc-e-big-container .swiper-slide:hover .lc-e-year, .image-swiper .lc-e-big .lc-e-big-container .lc-active .lc-e-year {
  color: #d51920 !important; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-e-big .lc-e-big-container .lc-e-year {
    font-size: 28px; }
  .image-swiper .lc-e-big .lc-e-big-container .swiper-slide .lc-e-year-text {
    font-size: 16px; }
  .image-swiper .lc-e-big .lc-e-big-container .swiper-slide:nth-of-type(2n) .lc-e-year-text {
    font-size: 16px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-big .lc-e-big-container .lc-e-year {
    font-size: 24px; }
  .image-swiper .lc-e-big .lc-e-big-container .lc-e-year-text {
    font-size: 16px; } }

.image-swiper .lc-e-big .lc-e-more {
  display: none; }

.image-swiper .lc-e-big .lc-e-touch {
  display: none; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-big {
    background-position: 0 -3rem;
    background-size: 180%;
    background-repeat: no-repeat; }
  .image-swiper .lc-e-big .lc_inner {
    position: relative;
    padding-top: 0.49rem; }
  .image-swiper .lc-e-big .lc-e-big-container {
    display: none; }
  .image-swiper .lc-e-big .lc-e-more {
    display: block;
    position: absolute;
    top: 0.52rem;
    right: 0; }
  .image-swiper .lc-e-big .lc-e-touch {
    display: block;
    border-left: 1px solid #e6e6e6;
    padding-bottom: 0.91rem; }
  .image-swiper .lc-e-big .lc-e-touch .lc_inner {
    position: relative; }
  .image-swiper .lc-e-big .lc-e-touch .lc_inner::before {
    content: '';
    position: absolute;
    margin: auto 0;
    top: 0.84rem;
    bottom: 1.82rem;
    left: 4px;
    width: 1px;
    background: #e6e6e6; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-item {
    padding-left: 0.4rem;
    padding-bottom: 0.42rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-item-top {
    line-height: 0.51rem;
    position: relative;
    padding-bottom: 0.26rem;
    border-bottom: 1px dashed #999; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-touch-year {
    width: 1.2rem;
    font-size: 0.4rem;
    color: #333; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-touch-title {
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    width: 4.5rem;
    line-height: 0.51rem;
    font-size: 0.24rem;
    color: #666; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-touch-more {
    font-size: 0.2rem;
    color: #d51920; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-item-bottom {
    line-height: 0.36rem;
    height: 0.72rem;
    overflow: hidden;
    margin-top: 0.25rem;
    font-size: 0.24rem;
    color: #666; }
  .image-swiper .lc-e-big .lc-e-touch .lc-top-touch-dot {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    position: absolute;
    left: -0.4rem;
    top: 0;
    bottom: 0;
    margin: auto 0;
    border: 1px solid #9e9e9e; }
  .image-swiper .lc-e-big .lc-e-touch .lc-top-touch-dot::after {
    display: block;
    width: 4px;
    height: 4px;
    background: #000;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 100%; }
  .image-swiper .lc-e-big .lc-e-touch .lc-e-touch-loading {
    font-size: 0.24rem;
    color: #333;
    border-radius: 3px;
    line-height: 0.98rem;
    width: 93.33333%;
    margin: 0 auto;
    margin-top: 0;
    text-align: center;
    display: block;
    background: #f4f4f4; } }

@-webkit-keyframes dotbefore {
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.2; } }

@keyframes dotbefore {
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.2; } }

.image-swiper .z-sy7-o1 {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both; }

.image-swiper .z-sy7-o1on {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn; }

@media only screen and (min-width: 1440px) {
  .image-swiper .z-sy7-d1 {
    width: 100%;
    padding-bottom: 3.3854%;
    position: relative; }
  .image-swiper .z-sy7-d2 {
    width: 100%;
    height: 0;
    padding-bottom: 37.7604%;
    overflow: hidden;
    position: relative; }
  .image-swiper .z-sy7-d3 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-o1 {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
    overflow: hidden;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    -webkit-transition-delay: 0.8s;
    -o-transition-delay: 0.8s;
    transition-delay: 0.8s;
    z-index: 9; }
  .image-swiper .z-sy7-o1on {
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 10; }
  .image-swiper .z-sy7-o1 img {
    width: 100%;
    min-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-d2:hover img {
    -webkit-transform: scale(1.08);
    -ms-transform: scale(1.08);
    transform: scale(1.08);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-q0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-r0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-s0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-t0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 a {
    display: none; }
  .image-swiper .z-sy7-q1 {
    position: absolute;
    top: 11.4483%;
    right: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-q1 img {
    width: 100%; }
  .image-swiper .z-sy7-q1:nth-child(1) {
    width: 6.4583%; }
  .image-swiper .z-sy7-q1:nth-child(2) {
    width: 6.1458%; }
  .image-swiper .z-sy7-q1:nth-child(3) {
    width: 3.75%; }
  .image-swiper .z-sy7-q1:nth-child(4) {
    width: 7.4479%; }
  .image-swiper .z-sy7-q1:nth-child(5) {
    width: 3.6846%; }
  .image-swiper .z-sy7-q1:nth-child(6) {
    width: 3.9583%; }
  .image-swiper .z-sy7-q1:nth-child(7) {
    width: 3.8021%; }
  .image-swiper .z-sy7-r1 {
    -webkit-transition-delay: 0.3s;
    -o-transition-delay: 0.3s;
    transition-delay: 0.3s;
    font-size: 42px;
    line-height: 42px;
    color: #fff;
    position: absolute;
    top: 10.4827%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-s1 {
    -webkit-transition-delay: 0.3s;
    -o-transition-delay: 0.3s;
    transition-delay: 0.3s;
    font-size: 16px;
    line-height: 16px;
    color: #fff;
    position: absolute;
    top: 19.1724%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    text-transform: uppercase;
    z-index: 13; }
  .image-swiper .z-sy7-t1 {
    -webkit-transition-delay: 0.3s;
    -o-transition-delay: 0.3s;
    transition-delay: 0.3s;
    font-size: 18px;
    line-height: 36px;
    color: #fff;
    text-align: center;
    position: absolute;
    top: 25.1034%;
    left: 100%;
    width: 32.8125%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-r1:nth-child(1), .image-swiper .z-sy7-s1:nth-child(1), .image-swiper .z-sy7-t1:nth-child(1) {
    color: #3e3e3e; }
  .image-swiper .z-sy7-q1on {
    right: 54.53125%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-r1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-s1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-t1on {
    left: -webkit-calc((100% - 32.8125%) / 2);
    left: calc((100% - 32.8125%) / 2);
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-nav1-d1 {
    width: 66.6666%;
    height: 0;
    padding-bottom: 7.0833%;
    position: absolute;
    bottom: 0;
    left: -webkit-calc(50% - 33.3333%);
    left: calc(50% - 33.3333%);
    background: #fff;
    -webkit-box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    z-index: 15; }
  .image-swiper .z-nav1-d2 {
    width: 96.5625%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 1.71875%;
    overflow: hidden; }
  .image-swiper .z-nav1-lo1, .image-swiper .z-nav1-lo2, .image-swiper .z-nav1-lo3 {
    height: 100%;
    position: absolute;
    top: 0; }
  .image-swiper .z-nav1-d3 {
    height: 100%;
    float: left;
    position: relative; }
  .image-swiper .z-nav1-k1 {
    width: 0;
    height: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    background: #c2322c;
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:hover .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 {
    height: 44px;
    width: 100%;
    position: absolute;
    top: 20.2647%;
    left: 0; }
  .image-swiper .z-nav1-i1 img {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-i1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:hover .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:hover .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-p1 {
    width: 100%;
    font-size: 16px;
    line-height: 16px;
    color: #454343;
    text-align: center;
    position: absolute;
    top: 66.1765%;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:hover .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    left: -28px;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt2 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    right: -28px;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt1 img {
    width: 13px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img {
    width: 13px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt1:hover img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1:hover img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt2 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt2:hover img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2:hover img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; } }

@media only screen and (max-width: 1439px) and (min-width: 1270px) {
  .image-swiper .z-sy7-d1 {
    width: 100%;
    padding-bottom: 3.3854%;
    position: relative; }
  .image-swiper .z-sy7-d2 {
    width: 100%;
    height: 0;
    padding-bottom: 37.7604%;
    overflow: hidden;
    position: relative; }
  .image-swiper .z-sy7-d3 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-o1 {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
    overflow: hidden;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    -webkit-transition-delay: 0.8s;
    -o-transition-delay: 0.8s;
    transition-delay: 0.8s;
    z-index: 9; }
  .image-swiper .z-sy7-o1on {
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 10; }
  .image-swiper .z-sy7-o1 img {
    width: 100%;
    min-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-d2:hover img {
    -webkit-transform: scale(1.08);
    -ms-transform: scale(1.08);
    transform: scale(1.08);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-q0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-r0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-s0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-t0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 a {
    display: none; }
  .image-swiper .z-sy7-q1 {
    position: absolute;
    top: 11.4483%;
    right: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-q1 img {
    width: 100%; }
  .image-swiper .z-sy7-q1:nth-child(1) {
    width: 6.4583%; }
  .image-swiper .z-sy7-q1:nth-child(2) {
    width: 6.1458%; }
  .image-swiper .z-sy7-q1:nth-child(3) {
    width: 3.75%; }
  .image-swiper .z-sy7-q1:nth-child(4) {
    width: 7.4479%; }
  .image-swiper .z-sy7-q1:nth-child(5) {
    width: 3.6846%; }
  .image-swiper .z-sy7-q1:nth-child(6) {
    width: 3.9583%; }
  .image-swiper .z-sy7-q1:nth-child(7) {
    width: 3.8021%; }
  .image-swiper .z-sy7-r1 {
    font-size: 36px;
    line-height: 36px;
    color: #fff;
    position: absolute;
    top: 10.4827%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-s1 {
    font-size: 14px;
    line-height: 14px;
    color: #fff;
    position: absolute;
    top: 19.1724%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    text-transform: uppercase;
    z-index: 13; }
  .image-swiper .z-sy7-t1 {
    font-size: 16px;
    line-height: 32px;
    color: #fff;
    text-align: center;
    position: absolute;
    top: 25.1034%;
    left: 100%;
    width: 32.8125%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-r1:nth-child(1), .image-swiper .z-sy7-s1:nth-child(1), .image-swiper .z-sy7-t1:nth-child(1) {
    color: #3e3e3e; }
  .image-swiper .z-sy7-q1on {
    right: 54.53125%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-r1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-s1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-t1on {
    left: -webkit-calc((100% - 32.8125%) / 2);
    left: calc((100% - 32.8125%) / 2);
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-nav1-d1 {
    width: 66.6666%;
    height: 0;
    padding-bottom: 7.0833%;
    position: absolute;
    bottom: 0;
    left: -webkit-calc(50% - 33.3333%);
    left: calc(50% - 33.3333%);
    background: #fff;
    -webkit-box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    z-index: 15; }
  .image-swiper .z-nav1-d2 {
    width: 96.5625%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 1.71875%;
    overflow: hidden; }
  .image-swiper .z-nav1-lo1, .image-swiper .z-nav1-lo2, .image-swiper .z-nav1-lo3 {
    height: 100%;
    position: absolute;
    top: 0; }
  .image-swiper .z-nav1-d3 {
    height: 100%;
    float: left;
    position: relative; }
  .image-swiper .z-nav1-k1 {
    width: 0;
    height: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    background: #c2322c;
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:hover .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 {
    height: 38px;
    width: 100%;
    position: absolute;
    top: 18.2647%;
    left: 0; }
  .image-swiper .z-nav1-i1 img {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-i1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:hover .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:hover .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-p1 {
    width: 100%;
    font-size: 14px;
    line-height: 14px;
    color: #454343;
    text-align: center;
    position: absolute;
    top: 68.1765%;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:hover .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt2 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt1 img {
    width: 11px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img {
    width: 11px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt1:hover img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1:hover img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt2 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt2:hover img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2:hover img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; } }

@media only screen and (max-width: 1269px) and (min-width: 1024px) {
  .image-swiper .z-sy7-d1 {
    width: 100%;
    padding-bottom: 3.3854%;
    position: relative; }
  .image-swiper .z-sy7-d2 {
    width: 100%;
    height: 0;
    padding-bottom: 37.7604%;
    overflow: hidden;
    position: relative; }
  .image-swiper .z-sy7-d3 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-o1 {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
    overflow: hidden;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    -webkit-transition-delay: 0.8s;
    -o-transition-delay: 0.8s;
    transition-delay: 0.8s;
    z-index: 9; }
  .image-swiper .z-sy7-o1on {
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 10; }
  .image-swiper .z-sy7-o1 img {
    width: 100%;
    min-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-d2:active img {
    -webkit-transform: scale(1.08);
    -ms-transform: scale(1.08);
    transform: scale(1.08);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-sy7-q0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-r0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-s0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-t0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 a {
    display: none; }
  .image-swiper .z-sy7-q1 {
    position: absolute;
    top: 11.4483%;
    right: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-q1 img {
    width: 100%; }
  .image-swiper .z-sy7-q1:nth-child(1) {
    width: 6.4583%; }
  .image-swiper .z-sy7-q1:nth-child(2) {
    width: 6.1458%; }
  .image-swiper .z-sy7-q1:nth-child(3) {
    width: 3.75%; }
  .image-swiper .z-sy7-q1:nth-child(4) {
    width: 7.4479%; }
  .image-swiper .z-sy7-q1:nth-child(5) {
    width: 3.6846%; }
  .image-swiper .z-sy7-q1:nth-child(6) {
    width: 3.9583%; }
  .image-swiper .z-sy7-q1:nth-child(7) {
    width: 3.8021%; }
  .image-swiper .z-sy7-r1 {
    font-size: 30px;
    line-height: 30px;
    color: #fff;
    position: absolute;
    top: 10.4827%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-s1 {
    font-size: 12px;
    line-height: 12px;
    color: #fff;
    position: absolute;
    top: 19.1724%;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    text-transform: uppercase;
    z-index: 13; }
  .image-swiper .z-sy7-t1 {
    font-size: 14px;
    line-height: 28px;
    color: #fff;
    text-align: center;
    position: absolute;
    top: 25.1034%;
    left: 100%;
    width: 32.8125%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-r1:nth-child(1), .image-swiper .z-sy7-s1:nth-child(1), .image-swiper .z-sy7-t1:nth-child(1) {
    color: #3e3e3e; }
  .image-swiper .z-sy7-q1on {
    right: 54.53125%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-r1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-s1on {
    left: 46.25%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-t1on {
    left: -webkit-calc((100% - 32.8125%) / 2);
    left: calc((100% - 32.8125%) / 2);
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-nav1-d1 {
    width: 66.6666%;
    height: 0;
    padding-bottom: 7.0833%;
    position: absolute;
    bottom: 0;
    left: -webkit-calc(50% - 33.3333%);
    left: calc(50% - 33.3333%);
    background: #fff;
    -webkit-box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    box-shadow: 0 2px 15px rgba(139, 139, 139, 0.22);
    z-index: 15; }
  .image-swiper .z-nav1-d2 {
    width: 96.5625%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 1.71875%;
    overflow: hidden; }
  .image-swiper .z-nav1-lo1, .image-swiper .z-nav1-lo2, .image-swiper .z-nav1-lo3 {
    height: 100%;
    position: absolute;
    top: 0; }
  .image-swiper .z-nav1-d3 {
    height: 100%;
    float: left;
    position: relative; }
  .image-swiper .z-nav1-k1 {
    width: 0;
    height: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    background: #c2322c;
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:active .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 {
    height: 32px;
    width: 100%;
    position: absolute;
    top: 16.2647%;
    left: 0; }
  .image-swiper .z-nav1-i1 img {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-i1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:active .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3:active .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-p1 {
    width: 100%;
    font-size: 12px;
    line-height: 12px;
    color: #454343;
    text-align: center;
    position: absolute;
    top: 70.1765%;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3:active .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt2 {
    width: 4.01875%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt1 img {
    width: 10px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img {
    width: 10px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt1:active img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1:active img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt2 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt2:active img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2:active img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; } }

@media only screen and (max-width: 1023px) {
  .image-swiper .z-sy7-d1 {
    width: 100%;
    position: relative;
    background: #f4f4f4; }
  .image-swiper .z-sy7-d2 {
    width: 100%;
    height: 2.87rem;
    overflow: hidden;
    position: relative; }
  .image-swiper .z-sy7-d3 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-o1 {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
    overflow: hidden;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    -webkit-transition-delay: 0.8s;
    -o-transition-delay: 0.8s;
    transition-delay: 0.8s;
    z-index: 9; }
  .image-swiper .z-sy7-o1on {
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 10; }
  .image-swiper .z-sy7-o1 img {
    width: 100%;
    min-height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-q0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-r0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-s0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-t0 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
  .image-swiper .z-sy7-a1 a {
    display: none; }
  .image-swiper .z-sy7-q1 {
    position: absolute;
    top: 11.4483%;
    right: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-q1 img {
    width: 100%; }
  .image-swiper .z-sy7-q1:nth-child(1) {
    width: 1.24rem; }
  .image-swiper .z-sy7-q1:nth-child(2) {
    width: 1.18rem; }
  .image-swiper .z-sy7-q1:nth-child(3) {
    width: 0.72rem; }
  .image-swiper .z-sy7-q1:nth-child(4) {
    width: 1.43rem; }
  .image-swiper .z-sy7-q1:nth-child(5) {
    width: 0.7rem; }
  .image-swiper .z-sy7-q1:nth-child(6) {
    width: 0.76rem; }
  .image-swiper .z-sy7-q1:nth-child(7) {
    width: 0.73rem; }
  .image-swiper .z-sy7-r1 {
    font-size: 0.38rem;
    line-height: 0.38rem;
    color: #fff;
    position: absolute;
    top: 0.4rem;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    z-index: 13; }
  .image-swiper .z-sy7-s1 {
    font-size: 0.22rem;
    line-height: 0.22rem;
    color: #fff;
    position: absolute;
    top: 0.9rem;
    left: 100%;
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    text-transform: uppercase;
    z-index: 13; }
  .image-swiper .z-sy7-t1 {
    font-size: 0.22rem;
    line-height: 0.3rem;
    color: #fff;
    text-align: center;
    position: absolute;
    top: 1.2rem;
    left: 100%;
    width: -webkit-calc(100% - 0.48rem);
    width: calc(100% - 0.48rem);
    opacity: 0;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s;
    display: none;
    z-index: 13; }
  .image-swiper .z-sy7-r1:nth-child(1), .image-swiper .z-sy7-s1:nth-child(1), .image-swiper .z-sy7-t1:nth-child(1) {
    color: #3e3e3e; }
  .image-swiper .z-sy7-q1on {
    right: 60%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-r1on {
    left: 45%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-s1on {
    left: 45%;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-sy7-t1on {
    left: 0.24rem;
    opacity: 1;
    -webkit-transition: 0.8s;
    -o-transition: 0.8s;
    transition: 0.8s; }
  .image-swiper .z-nav1-d1 {
    width: 100%;
    height: 1.05rem;
    position: relative;
    background: #fff;
    -webkit-box-shadow: 0 0.02rem 0.15rem rgba(139, 139, 139, 0.22);
    box-shadow: 0 0.02rem 0.15rem rgba(139, 139, 139, 0.22);
    z-index: 15;
    overflow: hidden; }
  .image-swiper .z-nav1-d2 {
    width: -webkit-calc(1.56rem * 6);
    width: calc(1.56rem * 6);
    height: 100%;
    position: absolute;
    top: 0;
    left: 0.44rem;
    overflow: hidden; }
  .image-swiper .z-nav1-lo1, .image-swiper .z-nav1-lo2, .image-swiper .z-nav1-lo3 {
    height: 100%;
    position: absolute;
    top: 0; }
  .image-swiper .z-nav1-d3 {
    height: 100%;
    float: left;
    position: relative; }
  .image-swiper .z-nav1-k1 {
    width: 0;
    height: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    background: #c2322c;
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-k1 {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 {
    height: 0.44rem;
    width: 100%;
    position: absolute;
    top: 0.16rem;
    left: 0; }
  .image-swiper .z-nav1-i1 img {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-i1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-i1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(1) {
    opacity: 0; }
  .image-swiper .z-nav1-d3on .z-nav1-i1 img:nth-child(2) {
    opacity: 1; }
  .image-swiper .z-nav1-p1 {
    width: 100%;
    font-size: 0.22rem;
    line-height: 0.22rem;
    color: #454343;
    text-align: center;
    position: absolute;
    top: 0.7rem;
    left: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-d3on .z-nav1-p1 {
    color: #fff;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 {
    width: 0.44rem;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt2 {
    width: 0.44rem;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background: #fff; }
  .image-swiper .z-nav1-bt1 img {
    width: 10px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img {
    width: 10px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt1 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt1:active img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt1:active img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2 img:nth-child(1) {
    opacity: 1; }
  .image-swiper .z-nav1-bt2 img:nth-child(2) {
    opacity: 0; }
  .image-swiper .z-nav1-bt2:active img:nth-child(1) {
    opacity: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; }
  .image-swiper .z-nav1-bt2:active img:nth-child(2) {
    opacity: 1;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s; } }

.image-swiper .z-sy7-d1 {
  background: #fff !important;
  margin-bottom: 9.21875%;
  margin-top: 4.01042%; }

.image-swiper .z-sy7-d1 .lc-e-title-t1, .image-swiper .z-sy7-d1 .lc-e-title-text {
  text-align: center; }

.image-swiper .z-sy7-d1 .lc-e-title-line {
  margin-left: auto;
  margin-right: auto; }

.image-swiper .z-sy7-d1 .lc-e-title-container {
  padding-bottom: 2.39583%; }

@media screen and (max-width: 1023px) {
  .image-swiper .z-sy7-d1 .lc-e-title-container {
    padding-bottom: 0.34rem; } }

@media screen and (min-width: 1600px) {
  .image-swiper .lc-a-page4 .lc-a-page4-sw {
    padding-bottom: 2.84722%; } }

.image-swiper .lc-a-page7 {
  margin-bottom: 23px; }

.image-swiper .lc-a-page7 .lc_inner {
  overflow: hidden; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-container {
  padding-top: 39.16667%;
  background: #f7f7f7;
  position: relative; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw1 {
  position: absolute;
  left: 7.56944%;
  top: 8.15603%;
  width: 21.73611%; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw2 {
  position: absolute;
  left: 25.48611%;
  top: 12.05674%;
  width: 15.83333%;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw3 {
  position: absolute;
  right: 16.66667%;
  bottom: 0;
  width: 34.23611%; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-text-container {
  width: 49.30556%;
  right: 0;
  top: 9.39716%;
  position: absolute; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1 {
  font-size: 30px;
  color: #333;
  line-height: 1.73333;
  position: relative; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1::after {
  position: absolute;
  display: block;
  content: "";
  width: 27px;
  height: 27px;
  background: url("../images/lc139.png") center center/cover no-repeat;
  left: -8.4507%;
  top: 0;
  bottom: 0;
  margin: auto 0; }

.image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t2 {
  font-size: 16px;
  color: #333;
  line-height: 1.75; }

.image-swiper .lc-a-page7 .lc_inner li {
  margin-bottom: 6.61972%;
  padding-left: 8.4507%; }

.image-swiper .lc-a-page7 .lc_inner li:nth-of-type(1) {
  -webkit-animation-delay: .2s;
  animation-delay: .2s; }

.image-swiper .lc-a-page7 .lc_inner li:nth-of-type(2) {
  -webkit-animation-delay: .4s;
  animation-delay: .4s; }

.image-swiper .lc-a-page7 .lc_inner li:nth-of-type(3) {
  -webkit-animation-delay: .6s;
  animation-delay: .6s; }

.image-swiper .lc-a-page7 .lc_inner li:last-of-type {
  margin-bottom: 0; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1 {
    font-size: 24px; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t2 {
    font-size: 14px;
    padding-right: 3%; }
  .image-swiper .lc-a-page7 .lc_inner li {
    margin-bottom: 4.61972%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1 {
    font-size: 20px; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1::after {
    width: 20px;
    height: 20px; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t2 {
    font-size: 12px; }
  .image-swiper .lc-a-page7 .lc_inner li {
    margin-bottom: 3.61972%; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1 {
    font-size: 18px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-a-page7 {
    background: #f7f7f7;
    margin-bottom: 0; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-container {
    padding-top: 78.14286%;
    background: #f7f7f7;
    position: relative;
    padding-bottom: 1.92rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw1 {
    position: absolute;
    left: 1.18rem;
    top: 0.42rem;
    width: 3.13rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw2 {
    position: absolute;
    left: 3.78rem;
    top: 0.64rem;
    width: 2.28rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page7-iw3 {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    width: 4.94rem; }
  .image-swiper .lc-a-page7 .lc_inner li {
    margin-bottom: 0.26rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-text-container {
    position: static;
    width: 100%; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1 {
    line-height: 0.53rem;
    font-size: 0.3rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t1::after {
    width: 0.27rem;
    height: 0.27rem; }
  .image-swiper .lc-a-page7 .lc_inner .lc-a-page-7-t2 {
    line-height: 0.48rem;
    font-size: 0.24rem; } }

.image-swiper .inner1400 {
  width: 72.91667%;
  margin: 0 auto;
  position: relative; }

@media screen and (max-width: 1023px) {
  .image-swiper .inner1400 {
    width: 94.66667%; } }

.image-swiper .lc-f-banner {
  padding-top: 44.27083%;
  background: url("../images/lc140.jpg");
  background-size: cover;
  position: relative; }

.image-swiper .lc-f-banner .lc-f-iw1 {
  position: absolute;
  right: 5.20833%;
  top: 32.23529%;
  width: 36.45833%; }

.image-swiper .lc-f-banner .inner1400 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 50%; }

.image-swiper .lc-f-banner img {
  margin-left: 0; }

.image-swiper .lc-f-banner .lc-f-text-container {
  padding-top: 21.42857%; }

.image-swiper .lc-f-banner .lc-f-title {
  margin-bottom: 2.57143%;
  width: 19.42857%; }

.image-swiper .lc-f-banner .lc-f-title2 {
  margin-bottom: 2.85714%;
  width: 62.42857%;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s; }

.image-swiper .lc-f-banner .lc-item {
  color: #7c9fde;
  font-size: 22px;
  line-height: 1.90909;
  font-family: "pfsc";
  padding: 0 24px;
  border: 1px solid #7c9fde;
  margin-right: 21px;
  border-radius: 42px; }

.image-swiper .lc-f-banner .lc-item:nth-of-type(1) {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s; }

.image-swiper .lc-f-banner .lc-item:nth-of-type(2) {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s; }

.image-swiper .lc-f-banner .lc-item:nth-of-type(3) {
  -webkit-animation-delay: 1.2s;
  animation-delay: 1.2s; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-banner .lc-item {
    font-size: 18px;
    border-radius: 18px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-banner .lc-item {
    font-size: 16px;
    margin-right: 16px;
    padding: 0 16px;
    border-radius: 15px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-f-banner .lc-item {
    font-size: 14px;
    border-radius: 13px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-banner {
    padding-top: 56.66667%; }
  .image-swiper .lc-f-banner .lc-item {
    font-size: 14px;
    border-radius: 13px; }
  .image-swiper .lc-f-banner .lc-f-iw1 {
    right: 0; }
  .image-swiper .lc-f-banner .lc-item {
    font-size: 0.14rem;
    padding: 0 0.1rem;
    margin-right: 0.1rem; }
  .image-swiper .lc-f-banner .lc-f-title1 {
    width: 20.70423%; }
  .image-swiper .lc-f-banner .lc-f-title2 {
    width: 61.40845%; } }

.image-swiper .lc-f-title-container .lc-f-title-cn {
  color: #454545;
  font-size: 40px;
  line-height: 1.5;
  margin-bottom: 9px; }

.image-swiper .lc-f-title-container .lc-f-f24 {
  color: #959595;
  font-size: 24px;
  line-height: 1.375; }

.image-swiper .lc-f-title-container .lc-f-f30 {
  color: #959595;
  font-size: 30px;
  line-height: 1.33333; }

.image-swiper .lc-f-title-container .lc-f-f16 {
  color: #959595;
  font-size: 16px;
  line-height: 1.5; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-title-container .lc-f-title-cn {
    font-size: 32px; }
  .image-swiper .lc-f-title-container .lc-f-f24 {
    font-size: 19px; }
  .image-swiper .lc-f-title-container .lc-f-f30 {
    font-size: 24px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-title-container .lc-f-title-cn {
    font-size: 28px; }
  .image-swiper .lc-f-title-container .lc-f-f24 {
    font-size: 16px; }
  .image-swiper .lc-f-title-container .lc-f-f30 {
    font-size: 21px; }
  .image-swiper .lc-f-title-container .lc-f-f16 {
    font-size: 14px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-f-title-container .lc-f-title-cn {
    font-size: 24px; }
  .image-swiper .lc-f-title-container .lc-f-f24 {
    font-size: 14px; }
  .image-swiper .lc-f-title-container .lc-f-f30 {
    font-size: 18px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-title-container .lc-f-title-cn {
    font-size: 0.4rem;
    margin-bottom: 0.09rem; }
  .image-swiper .lc-f-title-container .lc-f-f24 {
    color: #959595;
    font-size: 0.24rem;
    line-height: 1.375; }
  .image-swiper .lc-f-title-container .lc-f-f30 {
    color: #959595;
    font-size: 0.3rem;
    line-height: 1.33333; }
  .image-swiper .lc-f-title-container .lc-f-f16 {
    font-size: 0.2rem; } }

.image-swiper .lc-f-brand-zone {
  background: #f6f8ff; }

.image-swiper .lc-f-brand-zone .inner1400 {
  padding-top: 9.47917%;
  padding-bottom: 8.59375%; }

.image-swiper .lc-f-brand-zone .lc-right {
  width: 55.85714%;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  position: relative;
  z-index: 2; }

.image-swiper .lc-f-brand-zone .lc-right .lc-right-contianer {
  overflow: hidden;
  border-radius: 5px; }

.image-swiper .lc-f-brand-zone .lc-right .lc-right-contianer img {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s; }

.image-swiper .lc-f-brand-zone .lc-right .lc-right-contianer:hover img {
  -webkit-transform: scale(1.02);
  -ms-transform: scale(1.02);
  transform: scale(1.02); }

.image-swiper .lc-f-brand-zone .lc-right-bg {
  width: 97.44246%;
  height: 158.16993%;
  position: absolute;
  background: #dbe5ff;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 35.82677%;
  border-top-left-radius: 242px;
  border-bottom-left-radius: 242px;
  z-index: -1; }

.image-swiper .lc-f-brand-zone .lc-left {
  width: 42.14286%; }

.image-swiper .lc-f-brand-zone .lc-text {
  color: #292929;
  font-size: 18px;
  line-height: 2.22222;
  margin-top: 5.17241%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-brand-zone .lc-text {
    font-size: 16px;
    line-height: 1.5;
    margin-top: 1.72414%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-brand-zone .lc-text {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-brand-zone .lc-left, .image-swiper .lc-f-brand-zone .lc-right {
    width: 100%;
    float: none; }
  .image-swiper .lc-f-brand-zone .lc-f-title-cn {
    text-align: center; }
  .image-swiper .lc-f-brand-zone .lc-f-f24 {
    text-align: center; }
  .image-swiper .lc-f-brand-zone .lc-text {
    text-align: center;
    color: #292929;
    font-size: 0.2rem;
    line-height: 2.44444;
    color: #292929;
    margin-top: 0.56rem;
    margin-bottom: 0.38rem; }
  .image-swiper .lc-f-brand-zone .lc-right-bg {
    display: none; } }

.image-swiper .lc-f-price {
  padding-top: 5.05208%; }

.image-swiper .lc-f-price .lc-f-title-container {
  text-align: center; }

.image-swiper .lc-f-price ul {
  margin-top: 6.21429%;
  padding-bottom: 8.14286%; }

.image-swiper .lc-f-price ul li {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 30%;
  margin-right: 5%;
  -webkit-box-shadow: 0 0 5px 0 rgba(14, 80, 188, 0.19);
  box-shadow: 0 0 5px 0 rgba(14, 80, 188, 0.19);
  float: left;
  border-radius: 8px;
  -webkit-animation-duration: 1s !important;
  animation-duration: 1s !important; }

.image-swiper .lc-f-price ul li:nth-of-type(3n) {
  margin-right: 0; }

.image-swiper .lc-f-price .lc-container {
  padding-top: 113.09524%;
  position: relative; }

.image-swiper .lc-f-price .lc-img-wrap {
  width: 26.19048%;
  position: absolute;
  top: 18.73684%;
  margin: 0 auto;
  left: 0;
  right: 0; }

.image-swiper .lc-f-price .lc-img-t1 {
  position: absolute;
  font-size: 24px;
  line-height: 1.45833;
  color: #272727;
  text-align: center;
  top: 52.21053%;
  width: 100%; }

.image-swiper .lc-f-price .lc-img-t2 {
  width: 100%;
  position: absolute;
  font-size: 16px;
  line-height: 1.875;
  color: #8b8b8b;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 5.95238%;
  top: 71.36842%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-price .lc-img-t1 {
    font-size: 18px; }
  .image-swiper .lc-f-price .lc-img-t2 {
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-price .lc-img-t1 {
    font-size: 16px; }
  .image-swiper .lc-f-price .lc-img-t2 {
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-f-price .lc-img-t1 {
    font-size: 16px;
    top: 48.21053%; }
  .image-swiper .lc-f-price .lc-img-t2 {
    font-size: 12px;
    top: 68.36842%;
    line-height: 1.75; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-price ul li {
    width: 76.33803%;
    float: none;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 0.62rem; }
  .image-swiper .lc-f-price ul li:nth-of-type(3n) {
    margin-right: auto; }
  .image-swiper .lc-f-price ul li:nth-of-type(3) {
    margin-bottom: 0; }
  .image-swiper .lc-f-price .lc-img-t1 {
    font-size: 0.32rem; }
  .image-swiper .lc-f-price .lc-img-t2 {
    font-size: 0.2rem; } }

.image-swiper .lc-f-territory .lc-f-title-container {
  border-top: 1px solid #e6e6e6;
  text-align: center;
  padding: 5.78571% 0 7.07143%; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-territory .lc-f-title-container {
    padding-top: 11.12676%;
    padding-bottom: 8.87324%; } }

.image-swiper .lc-f-brand-starting-line {
  padding-top: 6.14583%;
  padding-bottom: 7.1875%;
  background-image: url("../images/lc148.jpg"); }

.image-swiper .lc-f-brand-starting-line .lc-f-title-cn {
  color: #fff;
  font-family: "pfsc";
  margin-bottom: 0; }

.image-swiper .lc-f-brand-starting-line .lc-container {
  position: relative;
  margin-top: 3.5%; }

.image-swiper .lc-f-brand-starting-line .lc-left {
  width: 51.42857%; }

.image-swiper .lc-f-brand-starting-line .lc-right {
  width: 42.28571%;
  font-size: 16px;
  line-height: 2.5;
  color: #fff;
  right: 0;
  height: 100%;
  position: absolute; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-brand-starting-line .lc-right {
    line-height: 2.25; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-brand-starting-line .lc-right {
    line-height: 2.25;
    font-size: 14px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-f-brand-starting-line .lc-right {
    line-height: 2;
    font-size: 13px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-brand-starting-line .lc-f-title-cn {
    text-align: center;
    margin-bottom: 9.01408%; }
  .image-swiper .lc-f-brand-starting-line .lc-left {
    width: 100%;
    float: none; }
  .image-swiper .lc-f-brand-starting-line .lc-right {
    width: 100%;
    position: static;
    font-size: 0.2rem;
    line-height: 2.44444;
    color: #fff;
    margin-top: 0.56rem;
    margin-bottom: 0.38rem; } }

.image-swiper .lc-f-good-graces {
  padding-top: 5.41667%; }

.image-swiper .lc-f-good-graces .lc-f-title-container {
  text-align: center; }

.image-swiper .lc-f-good-graces ul li {
  width: 25%;
  float: left; }

.image-swiper .lc-f-good-graces ul li .lc-container {
  position: relative;
  padding-top: 160.57143%; }

.image-swiper .lc-f-good-graces .lc-img-wrap {
  width: 44%;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 17.61566%; }

.image-swiper .lc-f-good-graces .lc-t1 {
  width: 100%;
  left: 0;
  font-size: 24px;
  font-weight: bold;
  color: #252525;
  line-height: 1.41667;
  text-align: center;
  margin-bottom: 4.28571%;
  padding: 0 3%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-f-good-graces .lc-t2 {
  width: 100%;
  left: 0;
  text-align: center;
  font-size: 17px;
  line-height: 2;
  color: #373737;
  padding: 0 3%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-f-good-graces .lc-text-container {
  position: absolute;
  left: 0;
  width: 100%;
  top: 53.20285%; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-f-good-graces .lc-t1 {
    font-size: 20px; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-good-graces .lc-t1 {
    font-size: 18px; }
  .image-swiper .lc-f-good-graces .lc-t2 {
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-good-graces .lc-t1 {
    font-size: 16px; }
  .image-swiper .lc-f-good-graces .lc-t2 {
    font-size: 14px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-f-good-graces .lc-t1 {
    font-size: 14px; }
  .image-swiper .lc-f-good-graces .lc-t2 {
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-good-graces {
    padding-bottom: 0.42rem;
    padding-top: 0.74rem; }
  .image-swiper .lc-f-good-graces ul {
    margin-top: 0.95rem; }
  .image-swiper .lc-f-good-graces ul li {
    height: 4.63rem;
    width: 50%; }
  .image-swiper .lc-f-good-graces ul li .lc-container {
    padding-top: 0; }
  .image-swiper .lc-f-good-graces ul li .lc-img-wrap {
    width: 1.54rem;
    position: static; }
  .image-swiper .lc-f-good-graces ul li .lc-text-container {
    position: static;
    margin-top: 0.42rem; }
  .image-swiper .lc-f-good-graces ul li .lc-text-container .lc-t1 {
    font-size: 0.24rem; }
  .image-swiper .lc-f-good-graces ul li .lc-text-container .lc-t2 {
    font-size: 0.2rem; } }

.image-swiper .lc-f-silk-road {
  padding: 2.76042% 0;
  padding-bottom: 2.60417%;
  background-image: url("../images/lc154.jpg");
  background-size: cover;
  background-position: center; }

.image-swiper .lc-f-silk-road .lc-images-wrap {
  width: 19%;
  margin-right: 17.14286%;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  border-radius: 33px; }

.image-swiper .lc-f-silk-road .lc-text-container {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 45.21429%; }

.image-swiper .lc-f-silk-road .lc-text-container .lc-f-title-cn, .image-swiper .lc-f-silk-road .lc-text-container .lc-f-title-cn2 {
  color: #fff; }

.image-swiper .lc-f-silk-road .lc-text {
  font-size: 16px;
  color: #fff;
  line-height: 2.5;
  margin-top: 7.26698%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-silk-road .lc-text {
    font-size: 16px;
    color: #fff;
    line-height: 1.875; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-silk-road .lc-text {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-silk-road {
    padding-top: 0;
    padding-bottom: 1.15rem; }
  .image-swiper .lc-f-silk-road .inner1400 {
    padding-top: 2.41rem; }
  .image-swiper .lc-f-silk-road .lc-images-wrap {
    width: 3.31rem;
    margin: 0 auto;
    float: none; }
  .image-swiper .lc-f-silk-road .lc-text-container {
    position: static;
    width: 100%; }
  .image-swiper .lc-f-silk-road .lc-f-title-container {
    position: absolute;
    top: 0.67rem;
    left: 0;
    width: 100%;
    text-align: center; }
  .image-swiper .lc-f-silk-road .lc-text {
    font-size: 0.2rem;
    line-height: 0.44rem;
    margin-top: 0.61rem; } }

.image-swiper .lc-f-silk-road-price {
  padding-top: 5.3125%;
  padding-bottom: 5.625%; }

.image-swiper .lc-f-silk-road-price .lc-f-title-container {
  text-align: center; }

.image-swiper .lc-f-silk-road-price ul {
  margin-top: 4.21875%; }

.image-swiper .lc-f-silk-road-price ul li {
  width: 33.33%;
  border-right: 1px solid #e8e8e8;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  position: relative; }

.image-swiper .lc-f-silk-road-price ul li:nth-of-type(3) {
  border: none; }

.image-swiper .lc-f-silk-road-price ul li .lc-container {
  padding-top: 21.45923%;
  position: relative; }

.image-swiper .lc-f-silk-road-price .lc-left {
  width: 19.31%;
  float: left;
  position: absolute;
  left: 4.7%;
  top: 5%;
  margin-right: 5.1%;
  border-radius: 100%;
  overflow: hidden;
  -webkit-box-shadow: 0 2px 30px 0 rgba(38, 91, 228, 0.44);
  box-shadow: 0 2px 30px 0 rgba(38, 91, 228, 0.44); }

.image-swiper .lc-f-silk-road-price .lc-right {
  width: -webkit-calc(100% - 19.31% - 4.7% - 5.1%);
  width: calc(100% - 19.31% - 4.7% - 5.1%);
  height: 100%;
  font-size: 16px;
  line-height: 1.75;
  color: #454545;
  position: absolute;
  right: 0;
  top: 0; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-f-silk-road-price .lc-right {
    font-size: 14px;
    line-height: 1.75; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-silk-road-price .lc-right {
    font-size: 12px;
    line-height: 1.75; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-silk-road-price {
    padding-top: 0.9rem; }
  .image-swiper .lc-f-silk-road-price ul li {
    padding: 0.7rem 0;
    width: 6rem;
    margin: 0 auto;
    float: none;
    border: none;
    border-bottom: 1px solid #e6e6e6; }
  .image-swiper .lc-f-silk-road-price ul li .lc-right {
    font-size: 0.24rem; } }

.image-swiper .lc-f-huabiao {
  padding-top: 5.78125%;
  padding-bottom: 3.125%;
  background-image: url("../images/lc159.jpg");
  background-size: cover; }

.image-swiper .lc-f-huabiao .lc-f-title-container .lc-f-title-cn, .image-swiper .lc-f-huabiao .lc-f-title-container .lc-f-title-cn2 {
  color: #fff; }

.image-swiper .lc-f-huabiao .lc-img-container {
  border-radius: 6px;
  overflow: hidden;
  margin-top: 3.57143%; }

.image-swiper .lc-f-huabiao .lc-text-container {
  padding: 3.5% 0 0;
  font-size: 16px;
  color: #fff;
  line-height: 1.625; }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-huabiao .lc-text-container {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-huabiao {
    padding-top: 0.76rem;
    padding-bottom: 0.79rem; }
  .image-swiper .lc-f-huabiao .lc-text-container {
    font-size: 14px; }
  .image-swiper .lc-f-huabiao .lc-f-title-container {
    text-align: center; }
  .image-swiper .lc-f-huabiao .lc-f-title-container .lc-f-title-cn {
    margin-bottom: 0.23rem; }
  .image-swiper .lc-f-huabiao .lc-img-container {
    margin-top: 0.58rem; }
  .image-swiper .lc-f-huabiao .lc-text-container {
    font-size: 0.2rem;
    text-align: center;
    padding-top: 0.31rem; } }

.image-swiper .lc-f-huabiao-price {
  padding-top: 5.3125%;
  padding-bottom: 3.64583%; }

.image-swiper .lc-f-huabiao-price .lc-f-title-container {
  text-align: center; }

.image-swiper .lc-f-huabiao-price ul {
  margin-top: 6.57143%; }

.image-swiper .lc-f-huabiao-price ul li {
  width: 46.64%;
  margin: 0 1.68%;
  float: left;
  margin-bottom: 3.35714%; }

.image-swiper .lc-f-huabiao-price ul li:nth-of-type(1) .lc-container {
  background-image: url("../images/lc161.jpg"); }

.image-swiper .lc-f-huabiao-price ul li:nth-of-type(2) .lc-container {
  background-image: url("../images/lc162.jpg"); }

.image-swiper .lc-f-huabiao-price ul li:nth-of-type(3) .lc-container {
  background-image: url("../images/lc163.jpg"); }

.image-swiper .lc-f-huabiao-price ul li:nth-of-type(4) .lc-container {
  background-image: url("../images/lc164.jpg"); }

.image-swiper .lc-f-huabiao-price .lc-container {
  padding-top: 28.48392%;
  position: relative;
  border-radius: 9px;
  color: #fff;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  background-size: cover; }

.image-swiper .lc-f-huabiao-price .lc-left {
  font-size: 80px;
  line-height: 1.3;
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  text-align: center;
  width: 25.26799%; }

.image-swiper .lc-f-huabiao-price .lc-right {
  position: absolute;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  right: 0;
  top: 0;
  height: 100%;
  width: 74.73201%; }

.image-swiper .lc-f-huabiao-price .lc-right-t1 {
  font-size: 24px;
  font-weight: bold;
  padding-top: 10.49383%;
  margin-bottom: 4.11523%; }

.image-swiper .lc-f-huabiao-price .lc-right-t2 {
  font-size: 16px;
  line-height: 1.625;
  padding-right: 3%; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-f-huabiao-price .lc-left {
    font-size: 60px; }
  .image-swiper .lc-f-huabiao-price .lc-right-t1 {
    font-size: 20px;
    margin-bottom: 3.08642%; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-huabiao-price .lc-left {
    font-size: 50px; }
  .image-swiper .lc-f-huabiao-price .lc-right-t1 {
    font-size: 18px;
    margin-bottom: 2.46914%; }
  .image-swiper .lc-f-huabiao-price .lc-right-t2 {
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-huabiao-price .lc-right-t1 {
    margin-bottom: 2.46914%;
    padding-top: 9.49383%; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-huabiao-price .lc-right-t2 {
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-huabiao-price {
    padding-bottom: 0.63rem; }
  .image-swiper .lc-f-huabiao-price ul li {
    width: 100%;
    margin: 0;
    margin-bottom: 0.46rem; }
  .image-swiper .lc-f-huabiao-price .lc-container {
    height: 2.03rem;
    padding-top: 0; }
  .image-swiper .lc-f-huabiao-price .lc-right-t1 {
    font-size: 0.26rem; }
  .image-swiper .lc-f-huabiao-price .lc-right-t2 {
    font-size: 0.22rem; } }

.image-swiper .lc-f-brand-name-card {
  background: #f6f8ff;
  padding-top: 4.0625%;
  padding-bottom: 4.0625%; }

.image-swiper .lc-f-brand-name-card .inner1400 {
  position: relative; }

.image-swiper .lc-f-brand-name-card .lc-images-wrap {
  border-radius: 8px;
  overflow: hidden;
  width: 52.07143%;
  margin-right: 0; }

.image-swiper .lc-f-brand-name-card .lc-text-container .lc-f-title-container .lc-f-title-cn {
  color: #292929; }

.image-swiper .lc-f-brand-name-card .lc-text-container .lc-f-title-container .lc-f-title-cn2 {
  color: #7c7c7c; }

.image-swiper .lc-f-brand-name-card .lc-text-container .lc-text {
  color: #292929; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-brand-name-card {
    text-align: center;
    padding-bottom: 0.78rem; }
  .image-swiper .lc-f-brand-name-card .lc-images-wrap {
    width: 100%;
    float: none; }
  .image-swiper .lc-f-brand-name-card .lc-text-container {
    text-align: center; }
  .image-swiper .lc-f-brand-name-card .inner1400 {
    padding-top: 2.07rem; }
  .image-swiper .lc-f-brand-name-card .lc-f-title-container {
    top: 0.54rem; }
  .image-swiper .lc-f-brand-name-card .lc-text {
    margin-top: 0.68rem; } }

.image-swiper .lc-f-brand-name-card-price {
  padding-bottom: 2.39583%; }

.image-swiper .lc-f-brand-name-card-price .lc-f-title-container {
  margin-bottom: 0;
  padding-top: 6.21429%;
  padding-bottom: 5.21429%;
  text-align: center; }

.image-swiper .lc-f-brand-name-card-price li {
  width: 47%;
  float: left;
  margin: 0 1.5%;
  margin-bottom: 2.78571%; }

.image-swiper .lc-f-brand-name-card-price .lc-container {
  position: relative;
  padding-top: 26.44377%;
  border-radius: 6px;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); }

.image-swiper .lc-f-brand-name-card-price .lc-left {
  width: 11.09422%;
  left: 11.09422%;
  top: 31.03448%;
  position: absolute; }

.image-swiper .lc-f-brand-name-card-price .lc-right {
  width: 65.34954%;
  left: 30.54711%;
  top: 0;
  height: 100%;
  position: absolute; }

.image-swiper .lc-f-brand-name-card-price .lc-right-t1 {
  color: #454545;
  font-size: 24px;
  line-height: 1.41667;
  font-weight: bold;
  margin-bottom: 4.65116%; }

.image-swiper .lc-f-brand-name-card-price .lc-right-t2 {
  line-height: 1.625;
  color: #454545;
  font-size: 16px; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-brand-name-card-price .lc-right-t1 {
    font-size: 18px; }
  .image-swiper .lc-f-brand-name-card-price .lc-right-t2 {
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-brand-name-card-price .lc-right-t2 {
    font-size: 12px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-f-brand-name-card-price .lc-left {
    left: 9.09422%; }
  .image-swiper .lc-f-brand-name-card-price .lc-right {
    left: 27.54711%;
    width: 70%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-brand-name-card-price .lc-f-title-container {
    text-align: center;
    padding-top: 0.88rem;
    padding-bottom: 0.78rem; }
  .image-swiper .lc-f-brand-name-card-price ul li {
    width: 100%;
    margin: 0;
    margin-bottom: 0.45rem; }
  .image-swiper .lc-f-brand-name-card-price ul .lc-right-t1 {
    font-size: 0.26rem;
    margin-bottom: 3.65116%; }
  .image-swiper .lc-f-brand-name-card-price ul .lc-right-t2 {
    font-size: 0.2rem; } }

.image-swiper .lc-f-img-generalize {
  background-image: url("../images/lc170.jpg");
  background-position: center bottom; }

.image-swiper .lc-f-img-generalize .lc-images-wrap {
  width: 55.78571%;
  margin-right: 0;
  border-radius: 8px;
  overflow: hidden; }

.image-swiper .lc-f-img-generalize .lc-text-container {
  width: 40%; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-img-generalize .lc-images-wrap {
    width: 100%; }
  .image-swiper .lc-f-img-generalize .lc-text-container {
    width: 100%; } }

.image-swiper .lc-f-img-generalize-price {
  padding-top: 5.3125%;
  padding-bottom: 7.08333%; }

.image-swiper .lc-f-img-generalize-price .lc-f-title-container {
  text-align: center;
  margin-bottom: 6.35714%; }

.image-swiper .lc-f-img-generalize-price ul {
  width: 88.57143%;
  margin: 0 auto; }

.image-swiper .lc-f-img-generalize-price ul li {
  width: 30%;
  border-radius: 8px;
  overflow: hidden;
  float: left;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
  margin-right: 5%; }

.image-swiper .lc-f-img-generalize-price ul li:nth-of-type(3) {
  margin-right: 0; }

.image-swiper .lc-f-img-generalize-price ul li .lc-container {
  padding-top: 83.87097%;
  position: relative; }

.image-swiper .lc-f-img-generalize-price .lc_img_wrap {
  width: 22.58065%;
  top: 25.64103%;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto; }

.image-swiper .lc-f-img-generalize-price .lc-text {
  text-align: center;
  width: 90%;
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 69.23077%;
  color: #272727;
  font-size: 24px;
  line-height: 1.83333;
  text-align: center; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-f-img-generalize-price .lc-text {
    font-size: 18px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-img-generalize-price .lc-text {
    font-size: 16px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-f-img-generalize-price .lc-text {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-img-generalize-price {
    padding-top: 0.82rem; }
  .image-swiper .lc-f-img-generalize-price .lc-f-title-container {
    margin-bottom: 0.7rem; }
  .image-swiper .lc-f-img-generalize-price ul {
    width: 5.25rem; }
  .image-swiper .lc-f-img-generalize-price ul li {
    margin-right: 0;
    width: 100%;
    margin-bottom: 0.54rem;
    -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2); }
  .image-swiper .lc-f-img-generalize-price .lc-text {
    font-size: 0.34rem; } }

.image-swiper .lc-f-more-products .lc-f-title-container {
  text-align: center;
  border-top: 1px solid #e6e6e6;
  padding: 5.5% 0 3.57143%; }

.image-swiper .lc-f-more-products .lc-f-more-products-title {
  font-size: 24px;
  color: #454545;
  line-height: 1.83333; }

.image-swiper .lc-f-more-products form {
  display: block; }

.image-swiper .lc-f-more-products .lc-f-form-container {
  margin-top: 2.92857%;
  padding-bottom: 6.07143%; }

.image-swiper .lc-f-more-products .z-xxl1-i5 {
  position: relative;
  top: 0;
  width: 100%;
  left: 0; }

.image-swiper .lc-f-more-products .z-xxl1-i6 {
  width: 256px;
  width: 19.85714%; }

.image-swiper .lc-f-more-products .z-xxl1-i6:last-child {
  width: 14.28571%; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-more-products .lc-f-more-products-title {
    font-size: 22px; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-f-more-products .lc-f-more-products-title {
    font-size: 21px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-f-more-products .lc-f-more-products-title {
    font-size: 20px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-f-more-products .lc-f-more-products-title {
    font-size: 17px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-f-more-products .lc-f-more-products-title {
    font-size: 0.24rem;
    text-align: center; }
  .image-swiper .lc-f-more-products .z-xxl1-i6 {
    width: 100%; }
  .image-swiper .lc-f-more-products .z-xxl1-i6:last-child {
    width: 100%; } }

.image-swiper .z-hd-d1 {
  overflow: visible; }

.image-swiper .lc-nav-container {
  background: url("../images/lc176.jpg");
  background-size: cover;
  position: absolute;
  top: 84px;
  height: 220px;
  left: 0;
  width: 100%;
  z-index: 100;
  padding: 34px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: none; }

.image-swiper .lc-nav-container img {
  display: block; }

.image-swiper .lc-nav-container * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-nav-container.lc-w1 {
  width: 530px; }

.image-swiper .lc-nav-container.lc-w2 {
  width: 680px; }

.image-swiper .lc-nav-container.lc-w3 {
  width: 680px; }

.image-swiper .lc-nav-container.lc-w4 {
  width: 950px; }

@media only screen and (max-width: 1439px) and (min-width: 1270px) {
  .image-swiper .lc-nav-container {
    top: 70px; }
  .image-swiper .lc-nav-container .lc-nav-right-container .lc-ul .lc-li {
    font-size: 13px; } }

@media only screen and (max-width: 1269px) and (min-width: 1024px) {
  .image-swiper .lc-nav-container {
    top: 60px; }
  .image-swiper .lc-nav-container .lc-nav-right-container .lc-ul .lc-li {
    font-size: 12px; } }

.image-swiper .lc-nav-left-img-container {
  width: 280px; }

.image-swiper .lc-nav-left-img-container .lc-nav-left-img-wrapper {
  position: relative;
  padding-top: 53.57143%; }

.image-swiper .lc-nav-left-img-container .lc-nav-left-img-wrapper img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0; }

.image-swiper .lc-nav-right-container {
  padding-left: 39px; }

.image-swiper .lc-nav-right-container .lc-ul {
  width: 140px;
  position: relative; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li {
  width: 100%;
  line-height: 37px;
  height: 37px;
  color: #fff;
  font-size: 15px;
  position: relative;
  padding-left: 22px; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li .lc-li-after {
  position: absolute;
  left: 0;
  width: 1px;
  background: #fff;
  height: 100%;
  opacity: 1;
  top: 0;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  -webkit-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  transition-timing-function: linear;
  display: none; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li a {
  color: #fff; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li a:hover {
  color: #fff; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li .lc-li-dot {
  width: 8px;
  height: 14px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: -3px;
  margin: auto 0;
  display: none;
  -webkit-animation-duration: 0.5s !important;
  animation-duration: 0.5s !important; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li:hover .lc-li-after {
  height: 20px;
  -webkit-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%); }

.image-swiper .lc-nav-right-container .lc-ul .lc-li:hover .lc-li-dot {
  display: block; }

.image-swiper .lc-nav-right-container .lc-ul .lc-li:nth-of-type(3):hover .lc-li-after, .image-swiper .lc-nav-right-container .lc-ul .lc-li:nth-of-type(4):hover .lc-li-after {
  -webkit-transform: translateY(37px);
  -ms-transform: translateY(37px);
  transform: translateY(37px); }

.image-swiper .lc-nav-right-container .lc-ul .lc-nav-ul-top, .image-swiper .lc-nav-right-container .lc-ul .lc-nav-ul-bottom {
  width: 1px;
  height: 50%;
  position: absolute;
  left: 0;
  background: rgba(255, 255, 255, 0.6);
  -webkit-transition: height 0.5s linear;
  -o-transition: height 0.5s linear;
  transition: height 0.5s linear; }

.image-swiper .lc-nav-right-container .lc-ul .lc-nav-ul-top {
  top: 0; }

.image-swiper .lc-nav-right-container .lc-ul .lc-nav-ul-bottom {
  bottom: 0; }

.image-swiper .lc-nav-right-container .lc-ul:hover .lc-nav-ul-top {
  -webkit-transform: translateY(-16px);
  -ms-transform: translateY(-16px);
  transform: translateY(-16px); }

.image-swiper .lc-nav-right-container .lc-ul:hover .lc-nav-ul-bottom {
  -webkit-transform: translateY(16px);
  -ms-transform: translateY(16px);
  transform: translateY(16px); }

@media screen and (max-width: 1800px) {
  .image-swiper .z-hd-d3:nth-of-type(4) .lc-nav-container, .image-swiper .z-hd-d3:nth-of-type(5) .lc-nav-container, .image-swiper .z-hd-d3:nth-of-type(6) .lc-nav-container, .image-swiper .z-hd-d3:nth-of-type(7) .lc-nav-container, .image-swiper .z-hd-d3:nth-of-type(8) .lc-nav-container, .image-swiper .z-hd-d3:nth-of-type(9) .lc-nav-container {
    left: auto;
    right: 0; }
  .image-swiper .z-hd-d3:nth-of-type(4) .lc-nav-container {
    left: auto;
    right: 0;
    -webkit-transform: translateX(50%);
    -ms-transform: translateX(50%);
    transform: translateX(50%); } }

.image-swiper .lc-e-big-container2 {
  width: 75%;
  margin: 0 auto;
  height: 390px;
  left: 0;
  right: 0;
  top: 65px;
  position: absolute;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide {
  height: 100%;
  width: 100%;
  border: 1px solid #eeeeee;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 35px 30px;
  position: absolute;
  z-index: 2;
  visibility: hidden; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide.lc-active {
  visibility: visible;
  z-index: 3; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide-img-wrap {
  width: 40.86%;
  height: 318px;
  overflow: hidden;
  background-size: cover;
  background-position: center; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide-img-wrap img {
  width: auto;
  height: 100%;
  max-width: 1000px; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide-text-container {
  width: -webkit-calc(100% - 40.86% - 35px);
  width: calc(100% - 40.86% - 35px);
  padding-right: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  height: 318px;
  color: #333333;
  font-size: 14px;
  line-height: 24px; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide-text-container .swiper-slide {
  height: auto;
  text-align: left; }

.image-swiper .lc-e-big-container2 .lc-e-big-slide-text-container .swiper-slide em {
  font-style: normal; }

.image-swiper .lc-e-big-container2 .lc-e-swiper-scrollbar {
  width: 7px;
  background: none; }

.image-swiper .lc-e-big-container2 .lc-e-swiper-scrollbar .swiper-scrollbar-drag {
  width: 7px;
  background: #c32026;
  border-radius: 4px;
  z-index: 3; }

.image-swiper .lc-e-big-container2 .lc-e-swiper-scrollbar::after {
  display: block;
  width: 3px;
  margin: 0 auto;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  content: '';
  background: #e2e2e2;
  top: 0; }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-e-big-container2 {
    width: 93.33333%; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-big-container2 {
    width: 93.33333%; } }

.image-swiper .lc-e-shot-img-container {
  width: 37px;
  height: 17px;
  position: absolute;
  bottom: -17px;
  left: 50%;
  top: 0;
  margin-left: -18px;
  display: none; }

.image-swiper .lc-g-banner {
  padding-top: 40.67708%;
  position: relative; }

.image-swiper .lc-g-banner .lc-g-banner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/lc185.jpg");
  background-size: cover;
  background-position: center; }

.image-swiper .lc-g-banner .lc-g-img-wrap1 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s; }

.image-swiper .lc-g-banner .lc-g-img-wrap1 img {
  width: 41.61458%; }

.image-swiper .lc-g-banner .lc-g-img-wrap2 {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s; }

.image-swiper .lc-g-banner .lc-g-img-wrap2 img {
  width: 12.13542%; }

.image-swiper .lc-g-banner .lc-g-img-wrap3 {
  position: absolute;
  width: 19.375%;
  bottom: 10.76345%;
  left: 2.39583%; }

.image-swiper .lc-g-banner .lc-g-img-wrap3 img {
  margin-left: 0; }

.image-swiper .lc-g-banner .lc-g-text {
  line-height: 2.41667;
  color: #c4864b;
  font-size: 24px;
  text-align: center;
  margin-top: 1.71875%;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s; }

.image-swiper .lc-g-banner .lc-g-text span {
  display: inline-block;
  vertical-align: top;
  padding: 0 4.16667%;
  border: 1px solid #c5874c;
  letter-spacing: 0.62em;
  border-radius: 30px; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-g-banner .lc-g-img-wrap3 {
    width: 19.01042%; }
  .image-swiper .lc-g-banner .lc-g-img-wrap3 img {
    margin-left: 0; }
  .image-swiper .lc-g-banner .lc-g-text {
    font-size: 18px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-g-banner .lc-g-img-wrap3 {
    width: 18.22917%; }
  .image-swiper .lc-g-banner .lc-g-img-wrap3 img {
    margin-left: 0; }
  .image-swiper .lc-g-banner .lc-g-text {
    font-size: 16px; } }

.image-swiper .lc-g-title-container {
  text-align: center; }

.image-swiper .lc-g-title-container .lc-cn1 {
  line-height: 1.61538;
  color: #000000;
  font-size: 39px; }

.image-swiper .lc-g-title-container .lc-cn2 {
  color: #303030;
  font-size: 18px;
  line-height: 2.27778; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-g-title-container .lc-cn1 {
    font-size: 32px; }
  .image-swiper .lc-g-title-container .lc-cn2 {
    font-size: 16px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-g-title-container .lc-cn1 {
    font-size: 28px; }
  .image-swiper .lc-g-title-container .lc-cn2 {
    font-size: 14px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-g-title-container .lc-cn1 {
    font-size: 0.39rem; }
  .image-swiper .lc-g-title-container .lc-cn2 {
    font-size: 0.2rem; } }

.image-swiper .lc-g-serve-project {
  padding-top: 4.63542%;
  padding-bottom: 6.04167%; }

.image-swiper .lc-g-serve-project .lc-container {
  margin-top: 4.86111%;
  position: relative; }

.image-swiper .lc-g-serve-project .lc-img-container {
  width: 34.58333%;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%; }

.image-swiper .lc-g-serve-project .lc-img-container img {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 100%; }

.image-swiper .lc-g-serve-project ul {
  width: 60.90278%; }

.image-swiper .lc-g-serve-project ul li {
  margin-bottom: 5.70125%; }

.image-swiper .lc-g-serve-project ul li:last-of-type {
  margin-bottom: 0; }

.image-swiper .lc-g-serve-project ul li:nth-of-type(1) {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s; }

.image-swiper .lc-g-serve-project ul li:nth-of-type(2) {
  -webkit-animation-delay: 1s;
  animation-delay: 1s; }

.image-swiper .lc-g-serve-project ul li:nth-of-type(3) {
  -webkit-animation-delay: 1.1s;
  animation-delay: 1.1s; }

.image-swiper .lc-g-serve-project ul .lc-right-img-container {
  width: 16.19156%; }

.image-swiper .lc-g-serve-project ul .lc-right-text-container {
  font-size: 24px;
  width: 79.81756%;
  margin-top: 1.14025%; }

.image-swiper .lc-g-serve-project ul .lc-right-bottom {
  font-size: 24px;
  padding-left: 2em; }

.image-swiper .lc-g-serve-project ul .lc-right-top {
  color: #333333;
  font-size: 24px;
  line-height: 1.5;
  margin-bottom: .71429%; }

.image-swiper .lc-g-serve-project ul .lc-text {
  line-height: 1.5;
  font-size: 16px;
  color: #777777;
  margin-bottom: 1.71429%; }

.image-swiper .lc-g-serve-project ul .lc-con-bt .lc-item {
  float: left;
  width: 21.8%;
  margin-right: 3.2%;
  line-height: 2.1875;
  background: #e9e9e9;
  border-radius: 8px;
  color: #222;
  text-align: center;
  font-size: 16px; }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-g-serve-project ul .lc-right-text-container {
    font-size: 20px; }
  .image-swiper .lc-g-serve-project ul .lc-right-bottom {
    font-size: 20px; }
  .image-swiper .lc-g-serve-project ul .lc-right-top {
    font-size: 20px; }
  .image-swiper .lc-g-serve-project ul .lc-text {
    font-size: 16px; }
  .image-swiper .lc-g-serve-project ul .lc-con-bt .lc-item {
    line-height: 2.1875;
    font-size: 14px; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-g-serve-project ul .lc-right-text-container {
    font-size: 18px; }
  .image-swiper .lc-g-serve-project ul .lc-right-bottom {
    font-size: 18px; }
  .image-swiper .lc-g-serve-project ul .lc-right-top {
    font-size: 18px; }
  .image-swiper .lc-g-serve-project ul .lc-text {
    font-size: 14px; }
  .image-swiper .lc-g-serve-project ul .lc-con-bt .lc-item {
    line-height: 2.1875;
    font-size: 12px; } }

@media screen and (max-width: 1300px) {
  .image-swiper .lc-g-serve-project ul .lc-con-bt .lc-item {
    line-height: 2.1875;
    font-size: 12px;
    min-width: 100px;
    margin-bottom: 8px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-g-serve-project .lc-img-container {
    width: 100%;
    height: auto;
    position: static;
    float: none; }
  .image-swiper .lc-g-serve-project .lc-img-container img {
    position: static;
    width: 4.98rem; }
  .image-swiper .lc-g-serve-project ul {
    width: 100%;
    float: none;
    margin-top: 0.76rem; }
  .image-swiper .lc-g-serve-project ul .lc-right-text-container {
    font-size: 0.3rem; }
  .image-swiper .lc-g-serve-project ul .lc-right-bottom {
    font-size: 0.3rem; }
  .image-swiper .lc-g-serve-project ul .lc-right-top {
    font-size: 0.3rem; }
  .image-swiper .lc-g-serve-project ul .lc-text {
    font-size: 0.2rem; }
  .image-swiper .lc-g-serve-project ul .lc-con-bt .lc-item {
    line-height: 2.1875;
    font-size: 0.24rem;
    width: 1.98rem;
    min-width: 0;
    margin-right: 0.28rem;
    margin-bottom: 0.13rem; } }

.image-swiper .lc-g-serve-advantage {
  background-image: url("../images/lc193.jpg");
  padding-bottom: 5.26042%; }

.image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn1, .image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn2 {
  color: #fff; }

.image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-g-title-bg {
  position: relative; }

.image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn1 {
  line-height: 1.3;
  height: 229px;
  height: 166px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding-top: 60px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn2 {
  width: 55.11111%;
  margin: 0 auto;
  line-height: 1.55556;
  margin-top: 1.25%; }

.image-swiper .lc-g-serve-advantage ul {
  width: 92.36111%;
  margin: 0 auto;
  margin-top: 1.52778%; }

.image-swiper .lc-g-serve-advantage ul li {
  width: 14.13534%;
  position: relative;
  float: left;
  margin-right: 2.85714%;
  margin-bottom: 2.4812%; }

.image-swiper .lc-g-serve-advantage ul li img {
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear; }

.image-swiper .lc-g-serve-advantage ul li .lc_text_container {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  height: 100%; }

.image-swiper .lc-g-serve-advantage ul li .lc_text_wrapper {
  text-align: center;
  font-size: 22px;
  line-height: 1.36364;
  color: #333333; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(6n) {
  margin-right: 0; }

.image-swiper .lc-g-serve-advantage ul li:last-of-type .lc_text_wrapper {
  color: #cc0000; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(1) img {
  -webkit-animation-name: myrotate1;
  animation-name: myrotate1; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(2) img {
  -webkit-animation-name: myrotate2;
  animation-name: myrotate2; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(3) img {
  -webkit-animation-name: myrotate3;
  animation-name: myrotate3; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(4) img {
  -webkit-animation-name: myrotate4;
  animation-name: myrotate4; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(5) img {
  -webkit-animation-name: myrotate5;
  animation-name: myrotate5; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(6) img {
  -webkit-animation-name: myrotate6;
  animation-name: myrotate6; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(7) img {
  -webkit-animation-name: myrotate7;
  animation-name: myrotate7; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(8) img {
  -webkit-animation-name: myrotate8;
  animation-name: myrotate8; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(9) img {
  -webkit-animation-name: myrotate9;
  animation-name: myrotate9; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(10) img {
  -webkit-animation-name: myrotate10;
  animation-name: myrotate10; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(11) img {
  -webkit-animation-name: myrotate11;
  animation-name: myrotate11; }

.image-swiper .lc-g-serve-advantage ul li:nth-of-type(12) img {
  -webkit-animation-name: myrotate12;
  animation-name: myrotate12; }

@media screen and (max-width: 1900px) {
  .image-swiper .lc-g-serve-advantage ul li .lc_text_wrapper {
    font-size: 18px; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-g-serve-advantage .lc-g-title-bg img {
    width: 150px; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn1 {
    padding-top: 52px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-g-serve-advantage ul li .lc_text_wrapper {
    font-size: 16px; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-bg img {
    width: 130px; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn1 {
    padding-top: 48px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-g-serve-advantage ul {
    margin-top: 0.7rem;
    margin-bottom: 0.4rem; }
  .image-swiper .lc-g-serve-advantage ul li {
    width: 30%;
    margin-right: 5%; }
  .image-swiper .lc-g-serve-advantage ul li .lc_text_wrapper {
    font-size: 0.22rem; }
  .image-swiper .lc-g-serve-advantage ul li:nth-of-type(3n) {
    margin-right: 0; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-bg img {
    width: 1.66rem; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn1 {
    padding-top: 0.62rem; }
  .image-swiper .lc-g-serve-advantage .lc-g-title-container .lc-cn2 {
    width: 85.111%; } }

@-webkit-keyframes myrotate1 {
  0% {
    -webkit-transform: rotateZ(30deg);
    transform: rotateZ(30deg); }
  100% {
    -webkit-transform: rotateZ(390deg);
    transform: rotateZ(390deg); } }

@keyframes myrotate1 {
  0% {
    -webkit-transform: rotateZ(30deg);
    transform: rotateZ(30deg); }
  100% {
    -webkit-transform: rotateZ(390deg);
    transform: rotateZ(390deg); } }

@-webkit-keyframes myrotate2 {
  0% {
    -webkit-transform: rotateZ(60deg);
    transform: rotateZ(60deg); }
  100% {
    -webkit-transform: rotateZ(420deg);
    transform: rotateZ(420deg); } }

@keyframes myrotate2 {
  0% {
    -webkit-transform: rotateZ(60deg);
    transform: rotateZ(60deg); }
  100% {
    -webkit-transform: rotateZ(420deg);
    transform: rotateZ(420deg); } }

@-webkit-keyframes myrotate3 {
  0% {
    -webkit-transform: rotateZ(90deg);
    transform: rotateZ(90deg); }
  100% {
    -webkit-transform: rotateZ(450deg);
    transform: rotateZ(450deg); } }

@keyframes myrotate3 {
  0% {
    -webkit-transform: rotateZ(90deg);
    transform: rotateZ(90deg); }
  100% {
    -webkit-transform: rotateZ(450deg);
    transform: rotateZ(450deg); } }

@-webkit-keyframes myrotate4 {
  0% {
    -webkit-transform: rotateZ(120deg);
    transform: rotateZ(120deg); }
  100% {
    -webkit-transform: rotateZ(480deg);
    transform: rotateZ(480deg); } }

@keyframes myrotate4 {
  0% {
    -webkit-transform: rotateZ(120deg);
    transform: rotateZ(120deg); }
  100% {
    -webkit-transform: rotateZ(480deg);
    transform: rotateZ(480deg); } }

@-webkit-keyframes myrotate5 {
  0% {
    -webkit-transform: rotateZ(150deg);
    transform: rotateZ(150deg); }
  100% {
    -webkit-transform: rotateZ(510deg);
    transform: rotateZ(510deg); } }

@keyframes myrotate5 {
  0% {
    -webkit-transform: rotateZ(150deg);
    transform: rotateZ(150deg); }
  100% {
    -webkit-transform: rotateZ(510deg);
    transform: rotateZ(510deg); } }

@-webkit-keyframes myrotate6 {
  0% {
    -webkit-transform: rotateZ(180deg);
    transform: rotateZ(180deg); }
  100% {
    -webkit-transform: rotateZ(540deg);
    transform: rotateZ(540deg); } }

@keyframes myrotate6 {
  0% {
    -webkit-transform: rotateZ(180deg);
    transform: rotateZ(180deg); }
  100% {
    -webkit-transform: rotateZ(540deg);
    transform: rotateZ(540deg); } }

@-webkit-keyframes myrotate7 {
  0% {
    -webkit-transform: rotateZ(210deg);
    transform: rotateZ(210deg); }
  100% {
    -webkit-transform: rotateZ(570deg);
    transform: rotateZ(570deg); } }

@keyframes myrotate7 {
  0% {
    -webkit-transform: rotateZ(210deg);
    transform: rotateZ(210deg); }
  100% {
    -webkit-transform: rotateZ(570deg);
    transform: rotateZ(570deg); } }

@-webkit-keyframes myrotate8 {
  0% {
    -webkit-transform: rotateZ(240deg);
    transform: rotateZ(240deg); }
  100% {
    -webkit-transform: rotateZ(600deg);
    transform: rotateZ(600deg); } }

@keyframes myrotate8 {
  0% {
    -webkit-transform: rotateZ(240deg);
    transform: rotateZ(240deg); }
  100% {
    -webkit-transform: rotateZ(600deg);
    transform: rotateZ(600deg); } }

@-webkit-keyframes myrotate9 {
  0% {
    -webkit-transform: rotateZ(270deg);
    transform: rotateZ(270deg); }
  100% {
    -webkit-transform: rotateZ(630deg);
    transform: rotateZ(630deg); } }

@keyframes myrotate9 {
  0% {
    -webkit-transform: rotateZ(270deg);
    transform: rotateZ(270deg); }
  100% {
    -webkit-transform: rotateZ(630deg);
    transform: rotateZ(630deg); } }

@-webkit-keyframes myrotate10 {
  0% {
    -webkit-transform: rotateZ(300deg);
    transform: rotateZ(300deg); }
  100% {
    -webkit-transform: rotateZ(660deg);
    transform: rotateZ(660deg); } }

@keyframes myrotate10 {
  0% {
    -webkit-transform: rotateZ(300deg);
    transform: rotateZ(300deg); }
  100% {
    -webkit-transform: rotateZ(660deg);
    transform: rotateZ(660deg); } }

@-webkit-keyframes myrotate11 {
  0% {
    -webkit-transform: rotateZ(330deg);
    transform: rotateZ(330deg); }
  100% {
    -webkit-transform: rotateZ(690deg);
    transform: rotateZ(690deg); } }

@keyframes myrotate11 {
  0% {
    -webkit-transform: rotateZ(330deg);
    transform: rotateZ(330deg); }
  100% {
    -webkit-transform: rotateZ(690deg);
    transform: rotateZ(690deg); } }

@-webkit-keyframes myrotate12 {
  0% {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg); }
  100% {
    -webkit-transform: rotateZ(720deg);
    transform: rotateZ(720deg); } }

@keyframes myrotate12 {
  0% {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg); }
  100% {
    -webkit-transform: rotateZ(720deg);
    transform: rotateZ(720deg); } }

.image-swiper .lc-g-pro-show {
  padding-top: 5.15625%;
  padding-bottom: 11.09375%;
  background-image: url("../images/lc200.jpg"); }

.image-swiper .lc-g-pro-show ul {
  margin-top: 5.13889%; }

.image-swiper .lc-g-pro-show ul li {
  width: 31.94444%;
  float: left;
  margin-right: 2.01389%;
  position: relative; }

.image-swiper .lc-g-pro-show ul li:nth-of-type(3n) {
  margin-right: 0; }

.image-swiper .lc-g-pro-show ul li .lc-video-container {
  padding-top: 76.08696%;
  position: relative;
  background: red; }

.image-swiper .lc-g-pro-show ul li .lc-video-container > img, .image-swiper .lc-g-pro-show ul li .lc-video-container > iframe, .image-swiper .lc-g-pro-show ul li .lc-video-container > video {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  position: absolute;
  top: 0;
  left: 0; }

.image-swiper .lc-g-pro-show ul li .lc_play_btn {
  width: 12.3913%;
  position: absolute;
  height: 0;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto; }

.image-swiper .lc-g-pro-show ul li .lc_play_btn .lc_play_con {
  padding-top: 115.38462%;
  position: relative;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer; }

.image-swiper .lc-g-pro-show ul li .lc_play_btn .lc_play_con img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0; }

.image-swiper .lc-g-serve-flow {
  padding-top: 5.67708%;
  padding-bottom: 15.3125%;
  position: relative; }

.image-swiper .lc-g-serve-flow .lc-g-container1239 {
  width: 86.11111%;
  margin: 0 auto;
  margin-top: 3.81944%; }

.image-swiper .lc-g-serve-flow .lc-g-serve-flow-img-right {
  position: absolute;
  right: 0;
  bottom: 14.52703%;
  width: 12.8125%; }

.image-swiper .lc-g-serve-flow .lc-flow-item {
  width: 16.12903%;
  position: relative;
  float: left;
  margin-right: 11.77419%; }

.image-swiper .lc-g-serve-flow .lc-flow-item:nth-of-type(4) {
  margin-right: 0; }

.image-swiper .lc-g-serve-flow .lc-flow-item > img {
  width: 100%;
  max-width: 1000px; }

.image-swiper .lc-g-serve-flow .lc-flow-item .lc-flow-shot {
  width: 30%; }

.image-swiper .lc-g-serve-flow .lc-flow-shot {
  position: absolute;
  right: -51.5%;
  bottom: 18.18182%;
  width: 30%; }

.image-swiper .lc-g-serve-flow .lc-text-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }

.image-swiper .lc-g-serve-flow .lc-top {
  font-size: 36px;
  font-weight: bold;
  padding-left: 41%;
  color: #fff;
  font-style: italic;
  font-style: oblique;
  line-height: 1.27778; }

.image-swiper .lc-g-serve-flow .lc-bottom {
  position: absolute;
  top: 28.0303%;
  bottom: 0;
  left: 0;
  width: 100%;
  color: #fff;
  font-size: 18px;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 5%; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide {
  margin-bottom: 8.3871%; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(4) .lc-flow-shot {
  -webkit-transform: rotateZ(90deg);
  -ms-transform: rotate(90deg);
  transform: rotateZ(90deg);
  left: 0;
  right: 0;
  bottom: -58.87879%;
  top: auto;
  margin: 0 auto; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) {
  margin-bottom: 0; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item {
  float: right;
  margin-left: 11.77419%;
  margin-right: 0; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(1) .lc-flow-shot {
  display: none; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(4) {
  margin-left: 0; }

.image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item .lc-flow-shot {
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotate(180deg);
  transform: rotateZ(180deg); }

@media screen and (max-width: 1800px) {
  .image-swiper .lc-g-serve-flow .lc-top {
    font-size: 28px;
    padding-left: 42%; } }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-g-serve-flow .lc-bottom {
    font-size: 16px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-g-serve-flow .lc-top {
    font-size: 24px; }
  .image-swiper .lc-g-serve-flow .lc-bottom {
    font-size: 14px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-g-serve-flow .lc-top {
    font-size: 20px; }
  .image-swiper .lc-g-serve-flow .lc-bottom {
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-g-serve-flow {
    padding-bottom: 0.69rem; }
  .image-swiper .lc-g-serve-flow .lc-flow-item {
    width: 2.54rem;
    margin-bottom: 0.91rem; }
  .image-swiper .lc-g-serve-flow .lc-flow-item > img {
    width: 100%;
    max-width: 1000px; }
  .image-swiper .lc-g-serve-flow .lc-flow-item .lc-flow-shot {
    width: 0.6rem; }
  .image-swiper .lc-g-serve-flow .lc-top {
    font-size: 0.36rem; }
  .image-swiper .lc-g-serve-flow .lc-bottom {
    font-size: 0.24rem; }
  .image-swiper .lc-g-serve-flow .lc-flow-shot {
    right: -0.83rem; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide {
    margin-bottom: 0; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) {
    margin-bottom: 0; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item {
    float: left;
    margin-right: 0;
    margin-left: 0; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(2) {
    float: right; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(2) .lc-flow-shot {
    -webkit-transform: rotateZ(90deg);
    -ms-transform: rotate(90deg);
    transform: rotateZ(90deg);
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: -0.73rem; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(3) {
    float: right; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(3) .lc-flow-shot {
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotate(180deg);
    transform: rotateZ(180deg);
    left: -0.83rem;
    right: auto; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(4) {
    float: left; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(1) .lc-flow-item:nth-of-type(4) .lc-flow-shot {
    -webkit-transform: rotateZ(90deg);
    -ms-transform: rotate(90deg);
    transform: rotateZ(90deg);
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: -0.73rem; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item {
    float: left;
    margin-right: 0;
    margin-left: 0; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(1) .lc-flow-shot {
    -webkit-transform: rotateZ(0deg);
    -ms-transform: rotate(0deg);
    transform: rotateZ(0deg);
    display: block; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(2) {
    float: right; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(2) .lc-flow-shot {
    -webkit-transform: rotateZ(90deg);
    -ms-transform: rotate(90deg);
    transform: rotateZ(90deg);
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: -0.73rem; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(3) {
    float: right;
    margin-bottom: 0; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(3) .lc-flow-shot {
    display: none; }
  .image-swiper .lc-g-serve-flow .lc-g-con-slide:nth-of-type(2) .lc-flow-item:nth-of-type(4) {
    margin-bottom: 0;
    float: left; }
  .image-swiper .lc-g-serve-flow .lc-g-serve-flow-img-right {
    display: none; } }

.image-swiper .lc-team-slide {
  display: none; }

.image-swiper .lc-team-slide.active {
  display: block; }

.image-swiper .lc-team-slide.active .animated {
  display: block; }

.image-swiper .lc-team-slide .animated {
  display: none; }

.image-swiper .lc-team-btn-wrapper {
  position: absolute;
  left: 0;
  top: .69444%;
  z-index: 5;
  padding-left: 163px; }

.image-swiper .lc-team-btn-wrapper .lc-team-btn {
  background: #eeeded;
  width: 120px;
  color: #333;
  font-size: 16px;
  text-align: center;
  line-height: 40px;
  float: left;
  margin-right: 10px;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  cursor: pointer; }

.image-swiper .lc-team-btn-wrapper .lc-team-btn.active {
  color: #fff;
  background: #d51920; }

@media screen and (max-width: 1600px) {
  .image-swiper .lc-team-btn-wrapper {
    top: .99444%;
    padding-left: 143px; }
  .image-swiper .lc-team-btn-wrapper .lc-team-btn {
    width: 96px;
    line-height: 32px;
    font-size: 14px; } }

@media screen and (max-width: 1400px) {
  .image-swiper .lc-team-btn-wrapper {
    top: 1.29444%;
    padding-left: 123px; }
  .image-swiper .lc-team-btn-wrapper .lc-team-btn {
    width: 90px;
    line-height: 30px;
    font-size: 14px; } }

@media screen and (max-width: 1200px) {
  .image-swiper .lc-team-btn-wrapper {
    top: 1.67%;
    padding-left: 113px; }
  .image-swiper .lc-team-btn-wrapper .lc-team-btn {
    width: 72px;
    line-height: 24px;
    font-size: 12px; } }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-team-btn-wrapper {
    top: 1.6rem;
    padding-left: 0; }
  .image-swiper .lc-team-btn-wrapper .lc-team-btn {
    width: 2.11rem;
    line-height: .67rem;
    font-size: 0.24rem;
    margin-right: 0.32rem; }
  .image-swiper .lc-team-btn-wrapper .lc-team-btn:nth-of-type(3) {
    margin-right: 0; } }

.image-swiper .lc-e-team .lc_inner {
  position: relative; }

@media screen and (max-width: 1023px) {
  .image-swiper .lc-e-team .lc-team-pc-right {
    padding-top: 2.56rem; } }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide img {
  width: 100%;
  max-width: 1000px; }

.image-swiper .lc-e-cooperation .left img {
  width: 100%;
  max-width: 1000px;
  height: 100%; }

.image-swiper .lc-e-team .swiper-slide img {
  width: 100%;
  max-width: 1000px;
  height: 100%; }

.image-swiper .lc-team-team img {
  width: 100%;
  max-width: 1000px;
  height: 100%; }

.image-swiper .lc-e-honor .lc-e-honer-sw .swiper-slide img {
  width: 100%;
  max-width: 1000px;
  height: 100%; }

.image-swiper .lc-e-honor .lc-e-honer-container {
  background-position: center;
  background-size: cover; }

.image-swiper .lc-e-img_wrapper img {
  width: 100%;
  max-width: 1000px;
  height: 100%; }

.image-swiper .lc-e-cooperation .lc-e-list-container {
  animation-name: fadeInUp; }

.image-swiper .lc-e-cooperation .lc-e-list-container .lc-e-list-slide {
  animation-name: none !important;
  background: #fff; }

.image-swiper .z-sy7-d1 {
  margin-bottom: 4.16667%; }

@media screen and (max-width: 1023px) {
  .image-swiper .z-sy7-d1 {
    margin-bottom: 9.21875%; } }
