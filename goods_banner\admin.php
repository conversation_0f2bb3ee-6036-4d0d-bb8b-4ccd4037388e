<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');
$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', 0);
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'goods_banner',
		'title' => '产品旋转木马',
		'desc' => '产品旋转木马',
		'category' => '产品',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'carousel_options' => array(
					'type' => 'buttons',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_OPTIONS'),
					'std' => 'elements',
					'values' => array(
						array(
							'label' => '轮播元素',
							'value' => 'elements'
						),
						array(
							'label' => '轮播元素样式',
							'value' => 'item_style'
						),
                        array(
                            'label' => '数据相关',
                            'value' => 'item_data'
                        ),
					),
					'tabs' => true,
				),
				'image_carousel_layout' => array(
					'type' => 'thumbnail',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_LAYOUT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_LAYOUT_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'values' => array(
						'layout1' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/goods_banner/assets/images/pro1.png',
						'layout2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/goods_banner/assets/images/pro2.png',
						'layout3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/goods_banner/assets/images/pro3.png',
						'layout4' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/goods_banner/assets/images/pro4.png',
					),
					'std' => 'layout3',
				),
				'carousel_fade' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_FADE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_FADE_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
						array('image_carousel_layout', '=', 'layout1'),
					),
					'std' => 0,
				),
				'carousel_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_PRO_HEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_PRO_HEIGHT_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'min' => 100,
					'max' => 1500,
					'std' => 500,
					'responsive' => true,
				),
                'carousel_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('旋转木马的宽度px'),
                    'desc' => JText::_('如果嵌套在选项卡中，请设置宽度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 100,
                    'max' => 10000,
                    'responsive' => true,
                ),
				'carousel_item_number' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_NUMBER'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_NUMBER_DESC'),
					'min' => 1,
					'max' => 15,
					'responsive' => true,
					'depends' => array(
						array('carousel_options', '=', 'elements'),
						array('image_carousel_layout', '!=', 'layout1'),
						array('image_carousel_layout', '!=', 'layout3'),
					),
				),
				'carousel_margin' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_MARGIN'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_MARGIN_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
						array('image_carousel_layout', '!=', 'layout1'),
					),
					'std' => 15,
				),
				'carousel_center_padding' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_CENTER_PAD'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_CENTER_PAD_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
						array('image_carousel_layout', '!=', 'layout1'),
						array('image_carousel_layout', '!=', 'layout2'),
					),
					'std' => array('md' => 180, 'sm' => 90, 'xs' => 50),
					'min' => 0,
					'max' => 500,
					'responsive' => true,
				),
				'carousel_autoplay' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'std' => 0
				),
				'carousel_speed' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'std' => 2500
				),
				'carousel_interval' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_INTERVAL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_INTERVAL_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'std' => 4500
				),
                'image_style' => array(
                    'type' => 'select',
                    'title' => '图片填充方式',
                    'values' => array(
                        'none' => '无效果',
                        'contain' => '自适应显示',
                        'cover' => '占满切割显示',
                        'fill' => '占满不切割显示',
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
				'carousel_overlay' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_OVERLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_OVERLAY_DESC'),
					'depends' => array(
						array('carousel_options', '=', 'item_style'),
					),
					'std' => 0
				),
				'overlay_gradient' => array(
					'type' => 'gradient',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_OVERLAY_GRADIENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_OVERLAY_GRADIENT_DESC'),
					'std' => array(
						"color" => "rgba(0, 0, 0, 1)",
						"color2" => "rgba(0, 0, 0, 1)",
						"deg" => "0",
						"type" => "linear"
					),
					'depends' => array(
						array('carousel_overlay', '=', 1),
						array('carousel_options', '=', 'item_style'),
					)
				),
//				'content_settings' => array(
//					'type' => 'separator',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_STYLE_OPTION'),
//					'depends' => array(
//						array('carousel_options', '=', 'item_style'),
//					)
//				),
//				'item_content_verti_align' => array(
//					'type' => 'select',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_LAYOUT_CONT_VERT_ALIGN'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_LAYOUT_CONT_VERT_ALIGN_DESC'),
//					'depends' => array(
//						array('carousel_options', '=', 'item_style'),
//					),
//					'values' => array(
//						'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
//						'middle' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MIDDLE'),
//						'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
//					),
//					'std' => 'middle',
//				),
//				'item_content_hori_align' => array(
//					'type' => 'select',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONT_HORI_ALIGNMENT'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONT_HORI_ALIGNMENT_DESC'),
//					'depends' => array(
//						array('carousel_options', '=', 'item_style'),
//					),
//					'values' => array(
//						'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
//						'center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
//						'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
//					),
//					'std' => 'center',
//				),
//				'content_style' => array(
//					'type' => 'buttons',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_STYLE_OPTION'),
//					'depends' => array(
//						array('carousel_options', '=', 'item_style'),
//					),
//					'std' => 'title_style',
//					'values' => array(
//						array(
//							'label' => '标题样式',
//							'value' => 'title_style'
//						),
//						array(
//							'label' => '按钮标题样式',
//							'value' => 'subtitle_style'
//						),
//						array(
//							'label' => '描述内容样式',
//							'value' => 'desc_style'
//						),
//					),
//					'tabs' => true,
//				),
//				//Title style
//				'content_title_fontsize' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'responsive' => true,
//					'max' => 400,
//				),
//				'content_title_lineheight' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'max' => 400,
//				),
//
//				'content_title_font_family' => array(
//					'type' => 'fonts',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
//					'selector' => array(
//						'type' => 'font',
//						'font' => '{{ VALUE }}',
//						'css' => '.jwpf-carousel-extended-heading { font-family: "{{ VALUE }}"; }'
//					),
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_title_font_style' => array(
//					'type' => 'fontstyle',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_title_letterspace' => array(
//					'type' => 'select',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
//					'values' => array(
//						'-10px' => '-10px',
//						'-9px' => '-9px',
//						'-8px' => '-8px',
//						'-7px' => '-7px',
//						'-6px' => '-6px',
//						'-5px' => '-5px',
//						'-4px' => '-4px',
//						'-3px' => '-3px',
//						'-2px' => '-2px',
//						'-1px' => '-1px',
//						'0px' => 'Default',
//						'1px' => '1px',
//						'2px' => '2px',
//						'3px' => '3px',
//						'4px' => '4px',
//						'5px' => '5px',
//						'6px' => '6px',
//						'7px' => '7px',
//						'8px' => '8px',
//						'9px' => '9px',
//						'10px' => '10px'
//					),
//					'std' => '0px',
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_title_text_color' => array(
//					'type' => 'color',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_title_margin' => array(
//					'type' => 'margin',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
//					'placeholder' => '10',
//					'depends' => array(
//						array('content_style', '=', 'title_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'max' => 400,
//					'responsive' => true
//				),
//				//Subtitle style
//				'content_subtitle_fontsize' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'responsive' => true,
//					'max' => 400,
//				),
//				'content_subtitle_lineheight' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'max' => 400,
//				),
//
//				'content_subtitle_font_family' => array(
//					'type' => 'fonts',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
//					'selector' => array(
//						'type' => 'font',
//						'font' => '{{ VALUE }}',
//						'css' => '.jwpf-carousel-extended-subheading { font-family: "{{ VALUE }}"; }'
//					),
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_subtitle_font_style' => array(
//					'type' => 'fontstyle',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_subtitle_letterspace' => array(
//					'type' => 'select',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
//					'values' => array(
//						'-10px' => '-10px',
//						'-9px' => '-9px',
//						'-8px' => '-8px',
//						'-7px' => '-7px',
//						'-6px' => '-6px',
//						'-5px' => '-5px',
//						'-4px' => '-4px',
//						'-3px' => '-3px',
//						'-2px' => '-2px',
//						'-1px' => '-1px',
//						'0px' => 'Default',
//						'1px' => '1px',
//						'2px' => '2px',
//						'3px' => '3px',
//						'4px' => '4px',
//						'5px' => '5px',
//						'6px' => '6px',
//						'7px' => '7px',
//						'8px' => '8px',
//						'9px' => '9px',
//						'10px' => '10px'
//					),
//					'std' => '0px',
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'content_subtitle_text_color' => array(
//					'type' => 'color',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
//					'depends' => array(
//						array('content_style', '=', 'subtitle_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				//Description style
//				'description_fontsize' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'responsive' => true,
//					'max' => 400,
//				),
//				'description_lineheight' => array(
//					'type' => 'slider',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
//					'std' => '',
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'max' => 400,
//				),
//				'description_font_family' => array(
//					'type' => 'fonts',
//					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
//					'selector' => array(
//						'type' => 'font',
//						'font' => '{{ VALUE }}',
//						'css' => '.jwpf-carousel-extended-description { font-family: "{{ VALUE }}"; }'
//					),
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'description_font_style' => array(
//					'type' => 'fontstyle',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'description_letterspace' => array(
//					'type' => 'select',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
//					'values' => array(
//						'-10px' => '-10px',
//						'-9px' => '-9px',
//						'-8px' => '-8px',
//						'-7px' => '-7px',
//						'-6px' => '-6px',
//						'-5px' => '-5px',
//						'-4px' => '-4px',
//						'-3px' => '-3px',
//						'-2px' => '-2px',
//						'-1px' => '-1px',
//						'0px' => 'Default',
//						'1px' => '1px',
//						'2px' => '2px',
//						'3px' => '3px',
//						'4px' => '4px',
//						'5px' => '5px',
//						'6px' => '6px',
//						'7px' => '7px',
//						'8px' => '8px',
//						'9px' => '9px',
//						'10px' => '10px'
//					),
//					'std' => '0px',
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),
//
//				'description_text_color' => array(
//					'type' => 'color',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//				),

//				'description_margin' => array(
//					'type' => 'margin',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
//					'placeholder' => '10',
//					'depends' => array(
//						array('content_style', '=', 'desc_style'),
//						array('carousel_options', '=', 'item_style'),
//					),
//					'max' => 400,
//					'responsive' => true
//				),
                // 数据相关
                'detail_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭跳转详情'),
                    'std' => 0,
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('detail_open', '!=', '1'),
                    ),
                ),
                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_goods')['list'],
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'goods_ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),
                        'sortdesc' => JText::_('排序id倒序'),
                        'sortasc' => JText::_('排序id正序'),
                    ),
                    'std' => 'sortdesc',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'goods_num' => array(
                    'type' => 'number',
                    'title' => '显示数量',
                    'desc' => '显示数量',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                    'std' => 20
                ),
                'show_title_box' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示标题'),
                    'desc' => JText::_(''),
                    'std' => 0,
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'show_title_wz' => array(
                    'type' => 'select',
                    'title' => JText::_('标题位置'),
                    'values' => array(
                        'imgs' => JText::_('漂浮图片上'),
                        'imgtop' => JText::_('图片上方'),
                        'imgbot' => JText::_('图片下方'),

                    ),
                    'std' => 'imgs',
                    'depends' => array(
                        array('show_title_box', '=', 1),
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'show_title_box_type' => array(
                    'type' => 'select',
                    'title' => JText::_('选择标题显示类型'),
                    'values' => array(
                        'type1' => '滑过显示',
                        'type2' => '固定显示',
                    ),
                    'std' => 'type1',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1),
                        array('show_title_wz', '!=', 'imgtop')
                    ),
                ),
                'show_title_box_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题背景高度'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '16',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1)
                    ),
                ),
                'show_title_box_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '14',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1)
                    ),
                ),
                'show_title_box_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1)
                    ),
                    'std' => '#878383',
                ),
                'show_title_box_align' => array(
                    'type' => 'select',
                    'title' => JText::_('标题对齐方式'),
                    'std' => 'center',
                    'values' => array(
                        'center' => '居中',
                        'left' => '居左',
                        'right' => '居右',
                    ),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1)
                    ),
                ),
                'show_title_sto' => array(
                    'type' => 'select',
                    'title' => JText::_('标题粗细'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_title_box', '=', 1)
                    ),
                    'values' => array(
                        '400' => '400',
                        '500' => '500',
                        '600' => '600',
                        '700' => '700',
                        '800' => '800',
                    ),
                    'std' => '400',
                ),
                'show_jianjie_box' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示简介'),
                    'std' => 0,
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                    ),
                ),
                'show_jianjie_zs' => array(
                    'type' => 'text',
                    'title' => JText::_('简介字数'),
                    'std' => '12',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_jianjie_box', '=', 1)
                    ),
                ),
                'show_jianjie_box_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介行高'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '25',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_jianjie_box', '=', 1)
                    ),
                ),
                'show_jianjie_box_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'max' => 50,
                    'min' => 0,
                    'std' => '14',
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_jianjie_box', '=', 1)
                    ),
                ),
                'show_jianjie_box_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_jianjie_box', '=', 1)
                    ),
                    'std' => '#878383',
                ),
                'show_jianjie_box_align' => array(
                    'type' => 'select',
                    'title' => JText::_('简介对齐方式'),
                    'std' => 'center',
                    'values' => array(
                        'center' => '居中',
                        'left' => '居左',
                        'right' => '居右',
                    ),
                    'depends' => array(
                        array('carousel_options', '=', 'item_data'),
                        array('show_jianjie_box', '=', 1)
                    ),
                ),



				'controller_settings' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_SEPARATOR'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
				),
				'carousel_navigation' => array(
					'type' => 'buttons',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_NAVIGATION'),
					'depends' => array(
						array('carousel_options', '=', 'elements'),
					),
					'std' => 'bullet_controller',
					'values' => array(
						array(
							'label' => '分页控制器',
							'value' => 'bullet_controller'
						),
						array(
							'label' => '箭头控制器',
							'value' => 'arrow_controller'
						)
					),
				),
				'carousel_bullet' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_CONTROLLERS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_CONTROLLERS_DESC'),
					'std' => 1,
					'depends' => array(
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
					),
				),
				'bullet_position_verti' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_VERTICAL_POSITION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_VERTICAL_POSITION_DESC'),
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
					),
					'min' => -100,
					'max' => 100,
					'responsive' => true,
				),
				'bullet_position_hori' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_HORI_POSITION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_HORI_POSITION_DESC'),
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
					),
					'min' => -2000,
					'max' => 2000,
					'responsive' => true,
				),

				'bullet_style' => array(
					'type' => 'buttons',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_BULLET_STYLE'),
					'std' => 'normal_bullet',
					'values' => array(
						array(
							'label' => '正常状态',
							'value' => 'normal_bullet'
						),
						array(
							'label' => '选中状态',
							'value' => 'active_bullet'
						)
					),
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
					),
				),
				'bullet_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_HEIGHT'),
					'std' => '',
					'max' => 100,
					'min' => 10,
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					),
					'std' => 4,
				),
				'bullet_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WIDTH'),
					'std' => '',
					'max' => 100,
					'min' => 10,
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					),
					'std' => 25,
				),
				'bullet_background' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					)
				),
				'bullet_border_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_WIDTH'),
					'max' => 20,
					'std' => 0,
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					)
				),
				'bullet_border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					)
				),
				'bullet_border_radius' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS_DESC'),
					'max' => 1000,
					'std' => '',
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'normal_bullet'),
					)
				),
				//Bullet hover
				'bullet_active_background' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_bullet', '=', 1),
						array('carousel_navigation', '=', 'bullet_controller'),
						array('carousel_options', '=', 'elements'),
						array('bullet_style', '=', 'active_bullet'),
					)
				),

				// Arrow style
				'carousel_arrow' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_ARROWS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_ARROWS_DESC'),
					'std' => 1,
					'depends' => array(
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
					),
				),
				'arrow_position_verti' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_VERTICAL_POSITION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_VERTICAL_POSITION_DESC'),
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
					),
					'min' => -100,
					'max' => 100,
					'responsive' => true,
				),
				'arrow_position_hori' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_HORI_POSITION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTROLLER_HORI_POSITION_DESC'),
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
					),
					'min' => -200,
					'max' => 1000,
					'responsive' => true,
				),
				'arrow_icon' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TESTIMONIAL_PRO_ARROWS_ICON'),
					'values' => array(
						'angle' => 'Angle',
						'long_arrow' => 'Long Arrow',
					),
					'std' => 'angle',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
					)
				),

				'arrow_style' => array(
					'type' => 'buttons',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TESTIMONIAL_PRO_ARROWS_STYLE'),
					'std' => 'normal_arrow',
					'values' => array(
						array(
							'label' => '正常的箭头',
							'value' => 'normal_arrow'
						),
						array(
							'label' => '移入的箭头',
							'value' => 'hover_arrow'
						)
					),
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
					),
				),
				'arrow_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_HEIGHT'),
					'std' => '',
					'max' => 200,
					'min' => 10,
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					),
					'std' => 60,
				),
				'arrow_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WIDTH'),
					'std' => '',
					'max' => 200,
					'min' => 10,
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					),
					'std' => 60,
				),
				'arrow_background' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				'arrow_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				'arrow_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
					'max' => 100,
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				'arrow_border_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_WIDTH'),
					'max' => 20,
					'std' => 0,
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				'arrow_border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				'arrow_border_radius' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS_DESC'),
					'max' => 1000,
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'normal_arrow'),
					)
				),
				//Arrow hover
				'arrow_hover_background' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'hover_arrow'),
					)
				),
				'arrow_hover_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'hover_arrow'),
					)
				),
				'arrow_hover_border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR_HOVER'),
					'std' => '',
					'depends' => array(
						array('carousel_arrow', '=', 1),
						array('carousel_navigation', '=', 'arrow_controller'),
						array('carousel_options', '=', 'elements'),
						array('arrow_style', '=', 'hover_arrow'),
					)
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),

			),
		),
	)
);
