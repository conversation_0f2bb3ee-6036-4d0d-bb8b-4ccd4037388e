<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'jump_wave_item',
		'title' => JText::_('波浪跳动音符组件'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
		'category' => '龙采官网插件',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

                'tdbj' => array(
                    'type' => 'media',
                    'title' => '背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220630/5a8d93ae54853835ae38029c26d1c2c5.jpg',
                ),
                'tdtitle' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '新增趣味助学工具 灵活开展线上教学',
                ),
                'tdintro' => array(
                    'type' => 'text',
                    'title' => '简介',
                    'std' => '激励学员自发学习，提升学员完课率',
                ),

				'jw_tab_item' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '用户分群',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/88135b1101302330625816a2187fe8ea.png',
                        ),
                        array(
                            'title' => '表单问券',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/05dc53fa375ad0ddf6efb32d42a25235.png',
                        ),
                        array(
                            'title' => '在线打卡',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/78981796509e684eae48af1eec8bb6d5.png',
                        ),
                        array(
                            'title' => '排课表',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/9b4ab3600c094c4da3802706fd26400c.png',
                        ),
                        array(
                            'title' => '模拟考试',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/9f752976715ecc8c068b263ad5b093ac.png',
                        ),
                        array(
                            'title' => '营销游戏',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/70536711526855b3a4b8a380598c01a6.png',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '用户分群',
                        ),
                        
                        'img' => array(
                            'type' => 'media',
                            'title' => '图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220630/88135b1101302330625816a2187fe8ea.png'
                        ),
                        
                    ),
                   
                ),
                
                

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
