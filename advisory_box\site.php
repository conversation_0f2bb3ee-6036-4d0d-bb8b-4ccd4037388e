<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2024-02-28 17:56:27
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonAdvisory_box extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $imgurl = $config->img_url;
        $padding_tb = (isset($settings->padding_tb)) ? $settings->padding_tb : 22;
        $padding_lr = (isset($settings->padding_lr)) ? $settings->padding_lr : 22;
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $search_wz = (isset($settings->search_wz)) ? $settings->search_wz : 0;
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;//关闭跟随滚动
        $close_qq = (isset($settings->close_qq)) ? $settings->close_qq : 0;//关闭跟随滚动
        $wx = (isset($settings->wx)) ? $settings->wx : '';//wx
        $close_zd_ding = (isset($settings->close_zd_ding)) ? $settings->close_zd_ding : 0;//关闭跟随滚动
        $art_type_selector = (isset($settings->art_type_selector)) ? $settings->art_type_selector : 'type1';//布局样式
        $listheight=250;
        $qq_button_font_color = (isset($settings->qq_button_font_color)) ? $settings->qq_button_font_color : '#00DFB9';//qq咨询按钮字体颜色
        $qq_button_border_color = (isset($settings->qq_button_border_color)) ? $settings->qq_button_border_color : '#00DFB9';//qq咨询按钮边框颜色
        $wx_button_font_color = (isset($settings->wx_button_font_color)) ? $settings->wx_button_font_color : '#00DFB9';//微信咨询按钮字体颜色
        $wx_button_border_color = (isset($settings->wx_button_border_color)) ? $settings->wx_button_border_color : '#00DFB9';//微信咨询按钮边框颜色

        $output = "";

        if($close_zd_ding!=0)
        {
            $output .= "
            <style>
                        a.sp-scroll-up {
                            display: none !important;
                        }
                        </style>
                    ";
        }

        if ($art_type_selector == 'type1') {
            $output .= "<style>";
            if ($fix_img_height != 0) {

                $output .= "{$addon_id} .box{
                      background:{$settings->bg_color};
                      border-radius:{$settings->fc_border_radius}px;
                      /*position: fixed;*/
                      width:{$settings->fc_width}px;
                      padding-top:{$padding_tb}px;
                      padding-bottom:{$padding_tb}px;
                      padding-left:{$padding_lr}px;
                      padding-right:{$padding_lr}px;
                      }";
            } else {
                $output .= "{$addon_id} .box{
                      background:{$settings->bg_color};
                      border-radius:{$settings->fc_border_radius}px;
                      position: fixed;
                      width:{$settings->fc_width}px;
                      padding-top:{$padding_tb}px;
                      padding-bottom:{$padding_tb}px;
                      padding-left:{$padding_lr}px;
                      padding-right:{$padding_lr}px;
                      }";
            }
            if ($settings->theme == 'location1') {
                $output .= "
                         {$addon_id} .box{
                              bottom:0;
                              left:0;
                            }
                    ";
            }



            if ($settings->theme == 'location2') {
                $output .= "
                         {$addon_id} .box{
                             bottom:0;
                            right:0;
                            }
                    ";
            }
            if ($settings->theme == 'location3') {
                $output .= "
                         {$addon_id} .box{
                              top:0;
                                left:0;
                            }
                    ";
            }
            if ($settings->theme == 'location4') {
                $output .= "
                        {$addon_id} .box{
                             top:0;
                            right:0;
                        }
                    ";
            }
            if ($settings->theme == 'location5') {
                $output .= "
                        {$addon_id} .box{
                            top:calc(50% - 120px );
                            left:0;
                        }
                    ";
            }
            if ($settings->theme == 'location6') {
                $output .= "
                        {$addon_id} .box{
                            top:calc(50% - 120px );

                            right:0;
                        }
                    ";
            }
            $output .= "
             {$addon_id} .box .box_img_wx{
                    text-align: center;
             }
              {$addon_id} .box .box_img_wx img{
                height: {$settings->image_code_height}px;
                display: inline-block;

             }
             {$addon_id} .box .box_zx_text{
                text-align: center;
                color: {$settings->zx_text_color};
                font-size: {$settings->zx_font_size}px;
             }
             {$addon_id} .box .phone_zx{
                text-align: center;border-bottom: 1px solid #fff;
                padding: 12px;
             }
             {$addon_id} .box .phone_zx img{
                display: inline-block;
             }
              {$addon_id} .box .phone_zx span{
                color: {$settings->phone_font_color};
                font-size: {$settings->phone_font_size}px;
             }
             {$addon_id} .box .phone_wx{
                text-align: center;
                border-bottom: 1px solid #fff;
                padding: 12px;
             }
             {$addon_id} .box .phone_wx img{
                display: inline-block;
             }
            {$addon_id} .box .phone_wx span{
                color: {$settings->wx_font_color};
                font-size: {$settings->wx_font_size}px;
             }
             {$addon_id} .arrow{
                 text-align: center;
                 padding-top: 12px;
                 color: {$settings->wx_font_color};
             }";
            $output .= "</style>";
            $output .= '<div class="box">';
            $output .= '<div class="box_img_wx">';
            $output .= '<img src="' . $settings->image_code . '">';
            $output .= '</div>';
            $output .= '<div class="box_zx_text">' . $settings->zx_text . '</div>';
            $output .= '<div class="phone_zx">';
            $output .= '<img src="' . $imgurl . 'phone_zx.png">';
            $output .= '<span>' . $settings->phone_font_text . '</span>';
            $output .= '</div>';
            $output .= '<div class="phone_wx">';
            $output .= '<img src="' . $imgurl . 'phone_wx.png">';
            $output .= '<span>' . $settings->wx_font_text . '</span>';
            $output .= '</div>';
            $output .= '<div class="arrow">∧</div>';
            $output .= '</div>';
        }
        if ($art_type_selector == 'type2') {
            $bg_color = (isset($settings->bg_color)) ? $settings->bg_color : '#d7012a';
            $bg_color2 = (isset($settings->bg_color_2)) ? $settings->bg_color_2 : '#d7012a';
            $fc_border_radius = (isset($settings->fc_border_radius)) ? $settings->fc_border_radius : 40;
            // QQ咨询提示文字
            $qq_desc_type2 = (isset($settings->qq_desc_type2)) ? $settings->qq_desc_type2 : '在线沟通，请点我';
            // qq资讯按钮文字
            $qq_btn_text_type2 = (isset($settings->qq_btn_text_type2)) ? $settings->qq_btn_text_type2 : '在线咨询';
            // 是否关闭微信
            $close_wx_type2 = (isset($settings->close_wx_type2)) ? $settings->close_wx_type2 : 0;
            // 电话详情中电话标签
            $phone_text_type2 = (isset($settings->phone_text_type2))? $settings->phone_text_type2 : '咨询热线：';
            // 电话详情中qq客服标签
            $phone_qq_text_type2 = (isset($settings->phone_qq_text_type2)) ? $settings->phone_qq_text_type2 : '客服qq：';
            // 微信咨询提示文字
            $wx_desc_type2 = (isset($settings->wx_desc_type2))? $settings->wx_desc_type2 : '在线沟通，请点我';
            // 微信按钮文字
            $wx_btn_text_type2 = (isset($settings->wx_btn_text_type2))? $settings->wx_btn_text_type2 : '复制并咨询';
            // qq图标
            $qq_icon_type2 = (isset($settings->qq_icon_type2)) ? $settings->qq_icon_type2 : '';
            // 微信图标
            $wx_icon_type2 = (isset($settings->wx_icon_type2)) ? $settings->wx_icon_type2 : '';
            // 电话图标
            $phone_icon_type2 = (isset($settings->phone_icon_type2))? $settings->phone_icon_type2 : '';
            // 返回顶部图标
            $top_icon_type2 = (isset($settings->top_icon_type2))? $settings->top_icon_type2 : '';
            // 关闭电话
            $close_tel_type2 = (isset($settings->close_tel_type2))? $settings->close_tel_type2 : 0;

            $output .= "<style>";
            if($settings->fix_img_height == 0) {

                if ($settings->theme2 == 'location1') {
                    $output .= "
                         {$addon_id} .slide{
                             bottom:0;
                            right:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location2') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            right:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location3') {
                    $output .= "
                         {$addon_id} .slide{
                             bottom:0;
                            left:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location4') {
                    $output .= "
                        {$addon_id} .slide{
                            top:0;
                            left:0;
                        }
                    ";
                }
                if ($settings->theme2 == 'location5') {
                    $output .= "
                        {$addon_id} .slide{
                            top:calc(50% - 125px);
                            right:0;
                        }
                    ";
                }
                if ($settings->theme2 == 'location6') {
                    $output .= "
                        {$addon_id} .slide{
                            top:calc(50% - 125px);
                            left:0;
                        }
                    ";
                }


            }else{
                if ($settings->theme3 == 'location1') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            left:0;
                            }
                    ";
                }
                if ($settings->theme3 == 'location2') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            right:0;
                            }
                    ";
                }
            }

            $output .= "
           {$addon_id} *{
		        padding: 0;
		        margin: 0;
	        }";

            if($close_qq==1)
            {
                $listheight = 200;
            }

            if($fix_img_height !=0){
                $output .= "
            /*右侧悬浮菜单*/
           {$addon_id} .slide{
                width: 80px;
                height: auto;
		        position: absolute;
                margin: 10px;
                background: $bg_color;
                border-radius: {$fc_border_radius}px;
                z-index: 999;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 30px 0;
            }";
            }else{
                $output .= "
            /*右侧悬浮菜单*/
           {$addon_id} .slide{
                width: 80px;
                height: auto;
		        position: fixed;
                margin: 10px;
                background: $bg_color;
                border-radius: {$fc_border_radius}px;
                z-index: 999;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 30px 0;
            }";
            }
            $output .= "
           {$addon_id} .slide ul{
                list-style: none;
            }";
            $output .= "
             {$addon_id} .slide .icon{
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
             }
           {$addon_id} .slide .icon li{
                width: 49px;
                height: 50px;
                background: url(https://oss.lcweb01.cn/joomla/20210811/3771d792158d95693ead9bc9e6753963.png) no-repeat;
            }";
            $output .= "
           {$addon_id} .slide .icon .up{
                background-position:-330px -120px ;
            }";
            $output .= "
           {$addon_id} .slide .icon li.qq{
                background-position:-385px -73px ;
            }";
            if($qq_icon_type2){
                $output.= "{$addon_id} .slide .icon li.qq{
                    background-position:center center;
                    background: url({$qq_icon_type2}) no-repeat;
                    background-size: contain;
                }";
            }
            if($close_qq!=1)
            {
                $output .= "
                {$addon_id} .slide .icon li.tel{
                    background-position:-385px -160px ;
                }";
                if($phone_icon_type2){
                    $output.= "{$addon_id} .slide .icon li.tel{
                        background-position:center center;
                        background: url({$phone_icon_type2}) no-repeat;
                        background-size: contain;
                    }";
                }
            }
            else
            {
                $output .= "
                {$addon_id} .slide .icon li.tel{
                        background-position:-385px -160px ;
                        margin-top: 30px;
                    }";
                if($phone_icon_type2){
                    $output.= "{$addon_id} .slide .icon li.tel{
                        background-position:center center;
                        background: url({$phone_icon_type2}) no-repeat;
                        background-size: contain;
                        margin-top: 0;
                    }";
                }
            }
            $output .= "
           {$addon_id} .slide .icon li.wx{
                background-position:-385px -120px ;
            }";
            if($wx_icon_type2){
                $output.= "{$addon_id} .slide .icon li.wx{
                    background-position:center center;
                    background: url({$wx_icon_type2}) no-repeat;
                    background-size: contain;
                }";
            }
            $output .= "
           {$addon_id} .slide .icon li.down{
                background:url(https://oss.lcweb01.cn/joomla/20210811/ef436e28dbdec64ef2190db2149e403d.png);
                background-size: 40px 44px;
                left: 5px;
                top: 5px;
                position: relative;
            }";
            if($top_icon_type2){
                $output.= "{$addon_id} .slide .icon li.down{
                    background-position:center center;
                    background: url({$top_icon_type2}) no-repeat;
                    background-size: contain;
                    position: static;
                }";
            }
            if($settings->fix_img_height == 0){
                if ($settings->theme2 == 'location1' || $settings->theme2 == 'location2' || $settings->theme2 == 'location5') {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            right: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/

                        }";
                }
                if ($settings->theme2 == 'location3' || $settings->theme2 == 'location4' || $settings->theme2 == 'location6') {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            left: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/
                        }";
                }
            }else{
                if ($settings->theme3 == 'location1' ) {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            left: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/

                        }";
                }
                if ($settings->theme3 == 'location2' ) {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            right: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/
                        }";
                }
            }
            $output .= "
           {$addon_id} .slide .info.hover{
                width: 145px;

            }";
            $output .= "
           {$addon_id} .slide .info li{
                width: 145px;
                color: #CCCCCC;
                text-align: center;
            }";
            $output .= "
           {$addon_id} .slide .info li p{
                font-size: 1.1em;
                line-height: 2em;
                padding: 15px;
                text-align: left;
            }";
            $output .= "
           {$addon_id} .slide .info li.qq p a{
                display: block;
                margin-top: 12px;
                width: 100px;
                height: 32px;
                line-height: 32px;
                color: {$qq_button_font_color};
                font-size: 16px;
                text-align: center;
                text-decoration: none;
                border: 1px solid {$qq_button_border_color};
                border-radius: 5px;
            }";
            $output .= "
            {$addon_id} .slide .info li.wx p a{
                    display: block;
                    margin-top: 12px;
                    width: 100px;
                    height: 32px;
                    line-height: 32px;
                    color: {$wx_button_font_color};
                    font-size: 16px;
                    text-align: center;
                    text-decoration: none;
                    border: 1px solid {$wx_button_border_color};
                    border-radius: 5px;
                }";
            $output .= $addon_id . ' .input_ {
                outline: none;
                border: 0px;
                color: rgba(0, 0, 0, 0.0);
                position: absolute;
                left: -20000px;
                background-color: transparent;
            }
            '.$addon_id .' .div_ {
                position: absolute;
                left: -20000px;
                color: rgba(0, 0, 0, 0);
                background-color: transparent;
            }';
            $output .= "
           {$addon_id} .slide .info li.qq p a:hover{
                color: #FFFFFF;
                border: none;
                background: #00E0DB;
            }";
            $output .= "
           {$addon_id} .slide .info li div.img{
                height: 100%;
                background: #DEFFF9;
                margin: 15px;
            }";
            $output .= "
           {$addon_id} .slide .info li div.img img{
                width: 100%;
                height: 100%;
           }";

            $output .= "
                    /*自适应 当屏小于1050时隐藏*/
            @media screen and (max-width: 1050px) {
                .slide{
                    display: none;
                }
                #btn{
                    display: none;
                }

            }";
            /*控制菜单的按钮*/
            /*{$addon_id} .index_cy{
                 width: 30px;
                 height: 30px;
                 background: url(img/index_cy.png);
                 position: fixed;
                 right: 0;
                 top: 50%;
                 margin-top: 140px;
                 background-position: 62px 0;
                 cursor: pointer;
             }
            {$addon_id} .index_cy2{
                 width: 30px;
                 height: 30px;
                 background: url(img/index_cy.png);
                 position: fixed;
                 right: 0;
                 top: 50%;
                 margin-top: 140px;
                 background-position: 30px 0;
                 cursor: pointer;
             }*/

            $output .= "</style>";

            $output .= '<div class="slide">';
            $output .= '    <ul class="icon">';
            if($close_qq!=1)
            {
                $output .= '        <li class="qq"></li>';
            }

            if($close_tel_type2!=1){
                $output .= '        <li class="tel"></li>';
            }

            if($close_wx_type2!=1){
                $output .= '        <li class="wx"></li>';
            }
            if($close_zd_ding != 1){
                $output .= '        <li class="down" id="test"></li>';
            }
            $output .= '    </ul>';
            $output .= '<ul class="info">
                ';
            if($close_qq!=1)
            {
                $output.='<li class="qq">
                    <p style="color: ' . ($settings->text_color ?? '#fff') . ';">'.($qq_desc_type2).'<a href="https://wpa.qq.com/msgrd?v=3&uin=' . ($settings->qq ?? 0) . '&site=qq&menu=yes" target="_blank">'.($qq_btn_text_type2).'</a></p>
                </li>';
            }
            $output .='
                <li class="tel">
                    <p style=" color: ' . ($settings->text_color ?? '#fff') . ';   padding-top: 5px;padding-left: 15px;">'.($phone_text_type2).'<br>' . ($settings->phone_font_text ?? 0) . '<br>'.($phone_qq_text_type2).'<br>' . ($settings->qq) . '</p>
                </li>
            ';
            if($close_wx_type2 != 1){
                $output.='<li class="wx">';
                if($wx)
                {
                    $output .='<input readOnly="true" class="input_" id="biao1" value="'.$wx.'"/>';
                    $output .='<div id="biaoios" class="div_">'.$wx.'</div>';
                    $output .='<p style="color: ' . ($settings->text_color ?? '#fff') . ';">'.($wx_desc_type2).'<br/><a href="javascript:;" onclick="openWx()">'.($wx_btn_text_type2).'</a></p>';
                }
                else
                {
                    $output .='<div class="img" style="height: ' . ($settings->image_code_height ?? 0) . 'px"><img src="' . ($settings->wx_image_code ?? '') . '" /></div>';
                }
                $output.='</li>';
            }

            $output .='</ul>';

            $output .= '</div>';
            $output .= '
            <script type="text/javascript">
            jQuery(function($){
            $(".slide .icon li").not(".up,.down").mouseenter(function(){
                $(".slide .info").addClass("hover");
                $(".slide .info li").hide();
                $(".slide .info li."+$(this).attr("class")).show();//.slide .info li.qq
            });
            $(".slide").mouseleave(function(){
                $(".slide .info").removeClass("hover");
            });


            test.onclick = function(){//回到最顶部
            //                document.body.scrollTop = document.documentElement.scrollTop = 0;
                var currentPosition,timer;
                var speed=10;
                timer=setInterval(function(){
                    currentPosition=document.documentElement.scrollTop || document.body.scrollTop;
                    currentPosition-=speed; //speed变量
                    if(currentPosition>0){
                        window.scrollTo(0,currentPosition);
                    }else{
                        window.scrollTo(0,0);
                        clearInterval(timer);
                    }
                },1);
            }



            });
            function openWx(url){
                if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
                    var text = document.getElementById(\'biaoios\');
                    //获取隐藏的input，并写入text内容，在进行复制
                    var input = document.getElementById("biao1");
                    input.value = text.innerHTML;
                    input.select();
                    input.setSelectionRange(0, input.value.length);   //兼容ios
                    document.execCommand("Copy");
                    input.blur();
                }else{
                    var Url2=document.getElementById("biao1");//要复制文字的节点
                    Url2.select(); // 选择对象
                    //document.execCommand("Copy"); // 执行浏览器复制命令
                    $("#biao1").blur();
                    if(document.execCommand(\'copy\', false, null)){
                    var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
                    }
                }
                jQuery(" .white_content").fadeIn(500);
                window.location.href = "weixin://";
            }
            </script>';
        }
        if ($art_type_selector == 'type3') {
            $bg_color = (isset($settings->bg_color)) ? $settings->bg_color : '#cbbdbd';
            $bg_color2 = (isset($settings->bg_color_2)) ? $settings->bg_color_2 : '#8ea697';
            $fc_border_radius = (isset($settings->fc_border_radius)) ? $settings->fc_border_radius : 40;
            //图标
            $icon_1 = (isset($settings->icon_1)) ? $settings->icon_1 : 'https://oss.lcweb01.cn/joomla/20210818/7946af25c7d1940abd738932e71d3bc8.png';
            $icon_2 = (isset($settings->icon_2)) ? $settings->icon_2 : 'https://oss.lcweb01.cn/joomla/20210818/edbc585bb2d5e8c0f46532e3aea96bc7.png';
            $icon_3 = (isset($settings->icon_3)) ? $settings->icon_3 : 'https://oss.lcweb01.cn/joomla/20210818/76ccd47cdbb1e6b189477f7699f9a713.png';
            $icon_4 = (isset($settings->icon_4)) ? $settings->icon_4 : 'https://oss.lcweb01.cn/joomla/20210818/e7753758e7665cd0bb7f1152fbe0b597.png';
            $icon_5 = (isset($settings->icon_5)) ? $settings->icon_5 : 'https://oss.lcweb01.cn/joomla/20210811/fc206fae6db00d8071b9a398b7746a82.png';
            $icon_6 = (isset($settings->icon_6)) ? $settings->icon_6 : 'https://oss.lcweb01.cn/joomla/20211011/04d305f561b5697110876c3a722365a4.png';

            //标题
            $text_1 = (isset($settings->text_1)) ? $settings->text_1 : '微信客服';
            $text_2 = (isset($settings->text_2)) ? $settings->text_2 : '网站咨询';
            $text_3 = (isset($settings->text_3)) ? $settings->text_3 : '建站咨询';
            $text_4 = (isset($settings->text_4)) ? $settings->text_4 : '扫码';

            $output .= "<style>";
            if($settings->fix_img_height == 0) {

                if ($settings->theme2 == 'location1') {
                    $output .= "
                         {$addon_id} .slide{
                             bottom:0;
                            right:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location2') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            right:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location3') {
                    $output .= "
                         {$addon_id} .slide{
                             bottom:0;
                            left:0;
                            }
                    ";
                }
                if ($settings->theme2 == 'location4') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            left:0;
                            }
                    ";
                }

                if ($settings->theme2 == 'location5') {
                    $output .= "
                        {$addon_id} .slide{
                            top:calc(50% - 240px);
                            right:0;
                        }
                    ";
                }
                if ($settings->theme2 == 'location6') {
                    $output .= "
                        {$addon_id} .slide{
                            top:calc(50% - 240px);
                            left:0;
                        }
                    ";
                }

            }else{
                if ($settings->theme3 == 'location1') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            left:0;
                            }
                    ";
                }
                if ($settings->theme3 == 'location2') {
                    $output .= "
                         {$addon_id} .slide{
                             top:0;
                            right:0;
                            }
                    ";
                }
            }
            $output .= "
           {$addon_id} *{
		        padding: 0;
		        margin: 0;
		        text-align: center;
	        }";

            if($fix_img_height !=0){
                $output .= "
            /*右侧悬浮菜单*/
           {$addon_id} .slide{

                width: 80px;
                height: 480px;
		        position: absolute;
                margin: 10px;
                background: $bg_color;
                border-radius: {$fc_border_radius}px;
                z-index: 999;
            }";
            }else{
                $output .= "
            /*右侧悬浮菜单*/
           {$addon_id} .slide{

                width: 80px;

		        position: fixed;
                margin: 10px;
                background: $bg_color;
                border-radius: {$fc_border_radius}px;
                z-index: 999;
            }";
            }

            $output .= "
           {$addon_id} .slide ul{
                list-style: none;
                margin-bottom: 50px;
            }";
            $output .= "
           {$addon_id} .slide .icon li{
                width: 44px;
                height: 44px;
                margin-top: 20px;
                margin-left: 20px;
            }";
            $output .= "
           {$addon_id} .slide .icon li.qq{
                background:url({$icon_3});
                background-size: 44px 44px;
            }";
            $output .= "
           {$addon_id} .slide .icon li.tel{
                 background:url({$icon_2});
                background-size: 44px 44px;
            }";
            $output .= "
           {$addon_id} .slide .icon li.wx{
                background:url({$icon_1});
                background-size: 44px 44px;
                margin-top: 30px;
            }";
            $output .= "
           {$addon_id} .slide .icon li.wx2{
                background:url({$icon_4});
                background-size: 44px 44px;
            }";
            $output .= "
           {$addon_id} .slide .icon li.down{
                background:url(".$icon_5.");
                background-size: 44px 44px;
                top: 5px;
                position: relative;
            }";
            $output .= "
           {$addon_id} .slide .icon li.upup{
                background:url(".$icon_6.");
                background-size: 44px 44px;
                top: 5px;
                position: relative;
            }";
            if($settings->fix_img_height == 0){

                if ($settings->theme2 == 'location1' || $settings->theme2 == 'location2' || $settings->theme2 == 'location5') {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            right: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/

                        }";
                }
                if ($settings->theme2 == 'location3' || $settings->theme2 == 'location4' || $settings->theme2 == 'location6') {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            left: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/
                        }";
                }
            }else{
                if ($settings->theme3 == 'location1' ) {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            left: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/

                        }";
                }
                if ($settings->theme3 == 'location2' ) {
                    $output .= "
                       {$addon_id} .slide .info{
                            top: 50%;
                            height: 147px;
                            position: absolute;
                            right: 100%;
                            background: $bg_color2;
                            width: 0px;
                            overflow: hidden;
                            margin-top: -73.5px;
                            transition:0.5s;
                            /*border-radius:4px 0 0 4px ;*/
                        }";
                }
            }
            $output .= "
            {$addon_id} .slide .info.hover{
                width: 145px;

            }";
            $output .= "
            {$addon_id} .slide .info li{
                width: 145px;
                color: #CCCCCC;
                text-align: center;
            }";
            $output .= "
            {$addon_id} .slide .info li p{
                font-size: 1.1em;
                line-height: 2em;
                padding: 15px;
                text-align: left;
            }";
            $output .= "
            {$addon_id} .slide .info li.qq p a{
                display: block;
                margin-top: 12px;
                width: 100px;
                height: 32px;
                line-height: 32px;
                color: #00DFB9;
                font-size: 16px;
                text-align: center;
                text-decoration: none;
                border: 1px solid #00DFB9;
                border-radius: 5px;
            }";
            $output .= "
           {$addon_id} .slide .info li.qq p a:hover{
                color: #FFFFFF;
                border: none;
                background: #00E0DB;
            }";
            $output .= "
           {$addon_id} .slide .info li div.img{
                height: 100%;
                background: #DEFFF9;
                margin: 15px;
            }";
            $output .= "
           {$addon_id} .slide .info li div.img img{
                width: 100%;
                height: 100%;
            }";
            $output .= "
                    /*自适应 当屏小于1050时隐藏*/
            @media screen and (max-width: 1050px) {
                .slide{
                    display: none;
                }
                #btn{
                    display: none;
                }

            }";
            $output .= "</style>";
            $wechat_switch = (isset($settings->wechat_switch)) ? $settings->wechat_switch :0;
            $output .= '<div class="slide">';
            $output .= '    <ul class="icon">';
            $output .= '        <li class="wx" style="display:' . ($wechat_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($wechat_switch ?'none' :'block') . '">'.$text_1.'</span>';
            $output .= '        <li class="tel" style="display:' . ($settings->call_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($settings->call_switch ?'none' :'block') . '">'.$text_2.'</span>';
            $output .= '        <li class="qq" style="display:' . ($settings->qq_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($settings->qq_switch ?'none' :'block') . '">'.$text_3.'</span>';
            $output .= '        <li class="wx2" style="display:' . ($settings->scan_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($settings->scan_switch ?'none' :'block') . '">'.$text_4.'</span>';
            $output .= '        <li class="down" id="test" style="display:' . ($settings->top_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($settings->top_switch ?'none' :'block') . '">顶部</span>';
            $output .= '        <li onclick="backdown()"  class="upup down" id="" style="display:' . ($settings->down_switch ?'none' :'block') . '"></li>';
            $output .= '<span style="text-align: center;font-size: 10px;color:' . ($settings->text_color ?? '#fff') . ';display:' . ($settings->down_switch ?'none' :'block') . '">底部</span>';
            $output .= '    </ul>';

                $output .= '<ul class="info">
                <li class="qq">
                    <p style="color:' . ($settings->text_color ?? '#fff') . ' ">在线沟通，请点我<a href="https://wpa.qq.com/msgrd?v=3&uin=' . ($settings->qq ?? 0) . '&site=qq&menu=yes" target="_blank">在线咨询</a></p>
                </li>';
            if($settings->qq_switch==0) {
            $output .= '<li class="tel">
                    <p style="color:' . ($settings->text_color ?? '#fff') . ';    padding-top: 5px;padding-left: 15px;">咨询热线：<br><span style="">' . ($settings->phone_font_text ?? 0) . '</span><br>';
            }else{
                $output .= '<li class="tel">
                    <p style="margin-top:30px; color:' . ($settings->text_color ?? '#fff') . ';    padding-top: 5px;padding-left: 15px;">咨询热线：<br><span style="">' . ($settings->phone_font_text ?? 0) . '</span><br>';
            }
            if($settings->qq_switch==0){
            $output .= '客服qq：<br><span>' . ($settings->qq ?? 1207746733) . '</span></p>';
            }

            $output .= '    </li>';
            $output .= '<li class="wx">
                    <div class="img" style="height: ' . ($settings->image_code_height ?? 115) . 'px"><img src="' . ($settings->wx_image_code ?? '') . '" /></div>
                </li>
                <li class="wx2">
                    <div class="img" style="height: ' . ($settings->image_code_height2 ?? 115) . 'px"><img src="' . ($settings->wx_image_code2 ?? '') . '" /></div>
                </li>
            </ul>';

            $output .= '</div>';
            $output .= "
            <script type=\"text/javascript\">
            jQuery(function($){
            $(\"{$addon_id} .slide .icon li\").not(\".down\").mouseenter(function(){
                $(\"{$addon_id} .slide .info\").addClass(\"hover\");
                $(\"{$addon_id} .slide .info li\").hide();
                $(\"{$addon_id} .slide .info li.\"+$(this).attr(\"class\")).show();//.slide .info li.qq
            });
            $(\"{$addon_id} .slide\").mouseleave(function(){
                $(\"{$addon_id} .slide .info\").removeClass(\"hover\");
            });



                        test.onclick = function(){//回到最顶部
                            //    document.body.scrollTop = document.documentElement.scrollTop = 0;
                            var currentPosition,timer;
                            var speed=10;
                            timer=setInterval(function(){
                                currentPosition=document.documentElement.scrollTop || document.body.scrollTop;
                                currentPosition-=speed; //speed变量
                                if(currentPosition>0){
                                    window.scrollTo(0,currentPosition);
                                }else{
                                    window.scrollTo(0,0);
                                    clearInterval(timer);
                                }
                            },1);
                        }

                        $('#backDown').click(function () { $('html,body').animate({ scrollTop: document.getElementsByTagName('BODY')[0].scrollHeight}, 2000); return false; });


                        });
                        function backdown()
                        {
                            $('html,body').animate({ scrollTop: document.getElementsByTagName('BODY')[0].scrollHeight}, 2000);
                            return false;
                        }
                    </script>
            ";
        }
        if ($art_type_selector == 'type4') {
            $kf_url = (isset($settings->kf_url)) ? $settings->kf_url : '';
            $bg_color4 = (isset($settings->bg_color4)) ? $settings->bg_color4 : '#d9012a';

            //图标
            $type4_icon1 = (isset($settings->type4_icon1)) ? $settings->type4_icon1 : 'https://oss.lcweb01.cn/joomla/20220805/3e806ae6be42eb7ee6e442897f99b3bd.png';
            $type4_icon2 = (isset($settings->type4_icon2)) ? $settings->type4_icon2 : 'https://oss.lcweb01.cn/joomla/20220805/b5027f3a2aed599ad2d4b2c014d73cc8.png';
            $type4_icon3 = (isset($settings->type4_icon3)) ? $settings->type4_icon3 : 'https://oss.lcweb01.cn/joomla/20220805/57844ea89aba2aad549c84dbd904f151.png';
            $type4_icon4 = (isset($settings->type4_icon4)) ? $settings->type4_icon4 : 'https://oss.lcweb01.cn/joomla/20220805/053bf557e97a9f9f048fd2552d28f14e.png';
            //标题
            $type_text1 = (isset($settings->type_text1)) ? $settings->type_text1 : '************';
            $type_text2 = (isset($settings->type_text2)) ? $settings->type_text2 : '联系客服';
            $type_text3 = (isset($settings->type_text3)) ? $settings->type_text3 : '联系客服';
            $text_color = (isset($settings->text_color)) ? $settings->text_color : '#fff';

            // 关闭按钮配置项
            $close_tel_type4 = (isset($settings->close_tel_type4))? $settings->close_tel_type4 : 0;
            $close_qq_type4 = (isset($settings->close_qq_type4))? $settings->close_qq_type4 : 0;
            $close_message_type4 = (isset($settings->close_message_type4)) ? $settings->close_message_type4 : 0;
            $closeCount = 0;
            if($close_tel_type4 == 1){
                $closeCount++;
            }
            if($close_qq_type4 == 1){
                $closeCount++;
            }
            if($close_message_type4 == 1){
                $closeCount++;
            }
            if($close_zd_ding == 1){
                $closeCount++;
            }

            $output .= "
                <style>
                    {$addon_id} * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    @media only screen and (min-width: 1024px){
                        {$addon_id} .right1-c1 {
                            width: 50px;
                            height: 50px;
                            position: fixed;
                            bottom: 180px;
                            right: 30px;
                            z-index: 7;
                            border-radius: 50%;
                        }
                        {$addon_id} .right1-c2 {
                            transform: rotate(45deg);
                        }
                        {$addon_id} .off1 {
                            opacity: 0 !important;
                        }
                        {$addon_id} .right1-c2 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            border-radius: 50%;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-c3 {
                            background: {$bg_color4};
                        }
                        {$addon_id} .right1-c3 {
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            position: absolute;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-c3:nth-child(1) {
                            top: calc(50% - 15px);
                            left: calc(50% - 15px);
                        }
                        {$addon_id} .right1-c3:nth-child(2) {
                            top: calc(50% - 15px);
                            left: calc(50% + 5px);
                        }
                        {$addon_id} .right1-c3:nth-child(3) {
                            top: calc(50% + 5px);
                            left: calc(50% - 15px);
                        }
                        {$addon_id} .right1-c3:nth-child(4) {
                            top: calc(50% + 5px);
                            left: calc(50% + 5px);
                        }
                        {$addon_id} .right1-c4 {
                            transform: scale(1);
                            opacity: 1;
                        }
                        {$addon_id} .right1-c4 {
                            width: 70px;
                            height: 280px;
                            position: absolute;
                            top: -270px;
                            left: calc(50% - 70px/2);
                            transition: 0.5s;
                            opacity: 1;
                        }
                        {$addon_id} .right1-b1 {
                            width: 70px;
                            height: 260px;
                            position: relative;
                        }
                        {$addon_id} .right1-b2 {
                            width: 100%;
                            height: 25%;
                            position: relative;
                            cursor: pointer;
                            background: {$bg_color4};
                        }
                        {$addon_id} .right1-b3 {
                            border-radius: 0 0 0 30px;
                        }
                        {$addon_id} .right1-b3 {
                            width: 0;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            right: 100%;
                            overflow: hidden;
                            background: {$bg_color4};
                            font-size: 13px;
                            line-height: 65px;
                            color: {$text_color};
                            text-align: center;
                            white-space: nowrap;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-b4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        {$addon_id} .right1-b4 img {
                            max-width: 30px;
                            max-height: 29px;
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                            width: 100%;
                        }
                        {$addon_id} .right1-b2:last-child {
                            border-radius: 0 0 25px 0;
                        }
                        {$addon_id} .right1-b2:hover .right1-b3 {
                            width: 130px;
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        {$addon_id} .right1-c1 {
                            width: 50px;
                            height: 50px;
                            position: fixed;
                            bottom: 125px;
                            right: 5px;
                            z-index: 7;
                            border-radius: 50%;
                            cursor: pointer;
                        }
                        {$addon_id} .right1-c2 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                            border-radius: 50%;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-c3 {
                            width: 8px;
                            height: 8px;
                            border-radius: 50%;
                            background: {$bg_color4};
                            position: absolute;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-c3:nth-child(1) {
                            top: calc(50% - 15px);
                            left: calc(50% - 15px);
                        }
                        {$addon_id} .right1-c3:nth-child(2) {
                            top: calc(50% - 15px);
                            left: calc(50% + 5px);
                        }
                        {$addon_id} .right1-c3:nth-child(3) {
                            top: calc(50% + 5px);
                            left: calc(50% - 15px);
                        }
                        {$addon_id} .right1-c3:nth-child(4) {
                            top: calc(50% + 5px);
                            left: calc(50% + 5px);
                        }
                        {$addon_id} .right1-c1.on1 .right1-c4 {
                            transition: 0.5s;
                            transform: scale(1);
                            opacity: 1;
                            width: 51px;
                            height: 220px;
                            position: absolute;
                            top: -230px;
                            left: calc(50% - 51px/2);
                        }
                        {$addon_id} .right1-b1 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                        }
                        {$addon_id} .right1-b2 {
                            width: 100%;
                            height: 25%;
                            position: relative;
                            cursor: pointer;
                            background: #d9012a;
                        }
                        {$addon_id} .right1-b2:hover .right1-b3 {
                            width: 130px;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-b3 {
                            border-radius: 0 0 0 30px;
                        }
                        {$addon_id} .right1-b3 {
                            width: 0;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            right: 100%;
                            overflow: hidden;
                            background: #d9012a;
                            font-size: 13px;
                            line-height: 55px;
                            color: #fff;
                            text-align: center;
                            white-space: nowrap;
                            transition: 0.5s;
                        }
                        {$addon_id} .right1-c4 {
                            width: 51px;
                            height: 220px;
                            position: absolute;
                            top: -230px;
                            left: calc(50% - 51px/2);
                            transition: 0.5s;
                            transform: scale(0);
                            opacity: 0;
                        }
                        {$addon_id} .on1 {
                            transition: 0.5s;
                            transform: scale(1);
                            opacity: 1;
                        }
                        {$addon_id} .right1-b4 img {
                            max-width: 26px;
                            max-height: 24px;
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                        }
                        {$addon_id} .right1-b2:last-child {
                            border-radius: 0 0 25px 0;
                        }
                    }
                    {$addon_id} a, {$addon_id} dd, {$addon_id} dl, {$addon_id} h1, {$addon_id} h2, {$addon_id} h3, {$addon_id} h4, {$addon_id} h5, {$addon_id} h6, {$addon_id} li, {$addon_id} p, {$addon_id} ul {
                        margin: 0;
                        padding: 0;
                        color: inherit;
                        font-size: inherit;
                        font-weight: inherit;
                    }
                    {$addon_id} a {
                        text-decoration: none;
                    }
                </style>
            ";
            $output.="<style>";
                if($close_tel_type4 == 1){
                    $output.=".tel{display: none;}";
                }
                if($close_qq_type4 == 1){
                    $output.=".qq{display: none;}";
                }
                if($close_message_type4 == 1){
                    $output.=".message{display: none;}";
                }
                if($close_zd_ding == 1){
                    $output.=".go-back{display: none;}";
                }
                if($closeCount > 0){
                    $output.="{$addon_id} .right1-c1.on1 .right1-c4，
                    {$addon_id} .right1-c4{
                        height: calc(55px * (4 - {$closeCount}));
                        top: calc(-55px * (4 - {$closeCount}) - 10px);
                    }
                    {$addon_id} .right1-b1 {
                        height: calc(55px * (4 - {$closeCount}));
                    }
                    @media only screen and (min-width: 1024px){
                        {$addon_id} .right1-b2{
                            height: 55px;
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        {$addon_id} .right1-b2{
                            height: 55px;
                        }
                    }";
                }
            $output.="</style>";
            $output .= '
                <div class="right1-c1" style="opacity: 1;">
                    <div class="right1-c2">
                        <div class="right1-c3"></div>
                        <div class="right1-c3"></div>
                        <div class="right1-c3"></div>
                        <div class="right1-c3"></div>
                    </div>
                    <div class="right1-c4">
                        <div class="right1-b1">
                            <div class="right1-b2 tel">
                                <div class="right1-b3"><a href="tel:'.$type_text1.'">'.$type_text1.'</a> </div>
                                <div class="right1-b4">
                                    <a href="tel:'.$type_text1.'"><img src="'.$type4_icon1.'"></a>
                                </div>
                            </div>
                            <div class="right1-b2 qq">
                                <div class="right1-b3"><a
                                        href="'.$kf_url.'"
                                        target="_blank">'.$type_text2.'</a>
                                </div>
                                <div class="right1-b4">
                                    <img src="'.$type4_icon2.'">
                                </div>
                            </div>
                            <div class="right1-b2 message">
                                <div class="right1-b3"><a
                                        href="'.$kf_url.'"
                                        target="_blank">'.$type_text3.'</a> </div>
                                <div class="right1-b4"><img src="'.$type4_icon3.'"></div>
                            </div>
                            <div class="right1-b2 go-back">
                                <div class="right1-b4 top1"><img src="'.$type4_icon4.'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <script>
                    $("'.$addon_id.' .top1").click(function() {
                        $("html,body").animate({ "scrollTop": 0 }, 800);
                    });
                    $("'.$addon_id.' .right1-c2").click(function() {
                        if ($(window).width() < 1024) {
                            if ($("'.$addon_id.' .right1-c1").hasClass("on1")) {
                                $("'.$addon_id.' .right1-c1").removeClass("on1");
                            } else {
                                $("'.$addon_id.' .right1-c1").addClass("on1");
                            }
                        }
                    });
                    window.addEventListener("wheel", function(e) {
                        if ($(window).scrollTop() > $(window).height() / 3 * 2) {
                            // $("'.$addon_id.' .right1-c1").css("opacity", "1");
                            $("'.$addon_id.' .right1-c1").css("display", "block");
                        } else {
                            //$("'.$addon_id.' .right1-c1").css("opacity", "0");
                            $("'.$addon_id.' .right1-c1").css("display", "none");
                        }
                    }, {
                        passive: false
                    });


                </script>
            ';
        }
        if($art_type_selector=='type5'){
            // 点击按钮出弹窗
            $floating_id = 'floating-' . $this->addon->id;
            $dialog_id = 'dialog-' . $this->addon->id;

            $dialog_show = isset($settings->dialog_show) ? $settings->dialog_show : 0;
            $dialog_addon_id =  (isset($settings->dialog_addon_id) && $settings->dialog_addon_id) ? $settings->dialog_addon_id : '';

            $business_bridge_show = isset($settings->business_bridge_show) ? $settings->business_bridge_show : 0;

            $float_img = (isset($settings->float_img) && $settings->float_img) ? $settings->float_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240221/63de139cf8ba383e116f0d3100f07933.png';
            $phone_float_img = (isset($settings->phone_float_img) && $settings->phone_float_img) ? $settings->phone_float_img : 'https://oss.lcweb01.cn/joomla/20240223/3a24d396a119f326bce87b1fd4902287.png';

            $dialog_img = (isset($settings->dialog_img) && $settings->dialog_img) ? $settings->dialog_img : 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50';
            $close_img = (isset($settings->close_img) && $settings->close_img) ? $settings->close_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png';
            $mask_close = isset($settings->mask_close) ? $settings->mask_close : 1;
            $close_ids = $mask_close == 1 ? '#' . $dialog_id . ' .mask, #' . $dialog_id . ' .close' : '#' . $dialog_id . ' .close';

            $dialog_level = (isset($settings->dialog_level) && $settings->dialog_level) ? $settings->dialog_level : '99999999999999999999999999999';
            $float_level = (isset($settings->float_level) && $settings->float_level) ? $settings->float_level : '99999999';
            $mask_bg = (isset($settings->mask_bg) && $settings->mask_bg) ? $settings->mask_bg : 'rgba(0,0,0,0.5)';
            $dialog_img_fill = (isset($settings->dialog_img_fill) && $settings->dialog_img_fill) ? $settings->dialog_img_fill : 'cover';
            $close_img_fill = (isset($settings->close_img_fill) && $settings->close_img_fill) ? $settings->close_img_fill : 'cover';
            $float_img_fill = (isset($settings->float_img_fill) && $settings->float_img_fill) ? $settings->float_img_fill : 'cover';

            if (isset($settings->dialog_width) && $settings->dialog_width) {
                if (is_object($settings->dialog_width)) {
                    $dialog_width = $settings->dialog_width->md;
                    $dialog_width_sm = $settings->dialog_width->sm;
                    $dialog_width_xs = $settings->dialog_width->xs;
                } else {
                    $dialog_width = $settings->dialog_width;
                    $dialog_width_sm = $settings->dialog_width_sm;
                    $dialog_width_xs = $settings->dialog_width_xs;
                }
            } else {
                $dialog_width = '600';
                $dialog_width_sm = '600';
                $dialog_width_xs = '600';
            }

            if (isset($settings->dialog_height) && $settings->dialog_height) {
                if (is_object($settings->dialog_height)) {
                    $dialog_height = $settings->dialog_height->md;
                    $dialog_height_sm = $settings->dialog_height->sm;
                    $dialog_height_xs = $settings->dialog_height->xs;
                } else {
                    $dialog_height = $settings->dialog_height;
                    $dialog_height_sm = $settings->dialog_height_sm;
                    $dialog_height_xs = $settings->dialog_height_xs;
                }
            } else {
                $dialog_height = '400';
                $dialog_height_sm = '400';
                $dialog_height_xs = '400';
            }

            if (isset($settings->close_width) && $settings->close_width) {
                if (is_object($settings->close_width)) {
                    $close_width = $settings->close_width->md;
                    $close_width_sm = $settings->close_width->sm;
                    $close_width_xs = $settings->close_width->xs;
                } else {
                    $close_width = $settings->close_width;
                    $close_width_sm = $settings->close_width_sm;
                    $close_width_xs = $settings->close_width_xs;
                }
            } else {
                $close_width = '40';
                $close_width_sm = '40';
                $close_width_xs = '40';
            }

            if (isset($settings->close_height) && $settings->close_height) {
                if (is_object($settings->close_height)) {
                    $close_height = $settings->close_height->md;
                    $close_height_sm = $settings->close_height->sm;
                    $close_height_xs = $settings->close_height->xs;
                } else {
                    $close_height = $settings->close_height;
                    $close_height_sm = $settings->close_height_sm;
                    $close_height_xs = $settings->close_height_xs;
                }
            } else {
                $close_height = '40';
                $close_height_sm = '40';
                $close_height_xs = '40';
            }

            if (isset($settings->close_position) && $settings->close_position) {
                if (is_object($settings->close_position)) {
                    $close_position = $settings->close_position->md;
                    $close_position_sm = $settings->close_position->sm;
                    $close_position_xs = $settings->close_position->xs;
                } else {
                    $close_position = $settings->close_position;
                    $close_position_sm = $settings->close_position_sm;
                    $close_position_xs = $settings->close_position_xs;
                }
            } else {
                $close_position = '0 0 auto auto';
                $close_position_sm = '0 0 auto auto';
                $close_position_xs = '0 0 auto auto';
            }

            $close_position_arr = explode(" ", $close_position);
            $close_position_arr_sm = explode(" ", $close_position_sm);
            $close_position_arr_xs = explode(" ", $close_position_xs);

            if (isset($settings->float_position) && $settings->float_position) {
                if (is_object($settings->float_position)) {
                    $float_position = $settings->float_position->md;
                    $float_position_sm = $settings->float_position->sm;
                    $float_position_xs = $settings->float_position->xs;
                } else {
                    $float_position = $settings->float_position;
                    $float_position_sm = $settings->float_position_sm;
                    $float_position_xs = $settings->float_position_xs;
                }
            } else {
                $float_position = '0 0 auto auto';
                $float_position_sm = '0 0 auto auto';
                $float_position_xs = '0 0 auto auto';
            }

            $float_position_arr = explode(" ", $float_position);
            $float_position_arr_sm = explode(" ", $float_position_sm);
            $float_position_arr_xs = explode(" ", $float_position_xs);

            if (isset($settings->float_width) && $settings->float_width) {
                if (is_object($settings->float_width)) {
                    $float_width = $settings->float_width->md;
                    $float_width_sm = $settings->float_width->sm;
                    $float_width_xs = $settings->float_width->xs;
                } else {
                    $float_width = $settings->float_width;
                    $float_width_sm = $settings->float_width_sm;
                    $float_width_xs = $settings->float_width_xs;
                }
            } else {
                $float_width = '270';
                $float_width_sm = '270';
                $float_width_xs = '65';
            }

            if (isset($settings->float_height) && $settings->float_height) {
                if (is_object($settings->float_height)) {
                    $float_height = $settings->float_height->md;
                    $float_height_sm = $settings->float_height->sm;
                    $float_height_xs = $settings->float_height->xs;
                } else {
                    $float_height = $settings->float_height;
                    $float_height_sm = $settings->float_height_sm;
                    $float_height_xs = $settings->float_height_xs;
                }
            } else {
                $float_height = '364';
                $float_height_sm = '364';
                $float_height_xs = '228';
            }

            $output .= '<style>
                #' . $floating_id . '{
                    width: ' . $float_width . 'px;
                    height: ' . $float_height . 'px;
                    position: fixed;
                    top: ' . $float_position_arr[0] . ';
                    right: ' . $float_position_arr[1] . ';
                    bottom: ' . $float_position_arr[2] . ';
                    left: ' . $float_position_arr[3] . ';
                    z-index: ' . $float_level . ';
                }
                #' . $floating_id . ' img{
                    width: 100%;
                    height: 100%;
                    object-fit: ' . $float_img_fill . ';
                }
                #' . $floating_id . ' img.phone{
                    display: none;
                }
                #' . $dialog_id . '{
                    display: none;
                    width: 100vw;
                    height: 100vh;
                    position: fixed;
                    top: 0;
                    left: 0;
                    z-index: '. $dialog_level .';
                }
                #' . $dialog_id . ' .mask{
                    width: 100%;
                    height: 100%;
                    background-color: '. $mask_bg .';
                }
                #' . $dialog_id . ' .dialog{
                    position: absolute;
                    inset: 0;
                    margin: auto;
                    width: ' . $dialog_width . 'px;
                    height: ' . $dialog_height . 'px;
                }
                #' . $dialog_id . ' .dialog-content{
                    border-radius: 10px;
                    overflow: hidden;
                    width: 100%;
                    height: 100%;
                }
                #' . $dialog_id . ' .dialog img{
                    width: 100%;
                    height: 100%;
                    object-fit: ' . $dialog_img_fill . ';
                }
                #' . $dialog_id . ' .dialog .close{
                    position: absolute;
                    top: ' . $close_position_arr[0] . ';
                    right: ' . $close_position_arr[1] . ';
                    bottom: ' . $close_position_arr[2] . ';
                    left: ' . $close_position_arr[3] . ';
                    width: ' . $close_width . 'px;
                    height: ' . $close_height . 'px;
                }
                #' . $dialog_id . ' .dialog .close img{
                    object-fit: ' . $close_img_fill . ';
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #' . $floating_id . '{
                        width: ' . $float_width_sm . 'px;
                        height: ' . $float_height_sm . 'px;
                        top: ' . $float_position_arr_sm[0] . ';
                        right: ' . $float_position_arr_sm[1] . ';
                        bottom: ' . $float_position_arr_sm[2] . ';
                        left: ' . $float_position_arr_sm[3] . ';
                    }
                    #' . $dialog_id . ' .dialog{
                        width: ' . $dialog_width_sm . 'px;
                        height: ' . $dialog_height_sm . 'px;
                    }
                    #' . $dialog_id . ' .dialog .close{
                        top: ' . $close_position_arr_sm[0] . ';
                        right: ' . $close_position_arr_sm[1] . ';
                        bottom: ' . $close_position_arr_sm[2] . ';
                        left: ' . $close_position_arr_sm[3] . ';
                        width: ' . $close_width_sm . 'px;
                        height: ' . $close_height_sm . 'px;
                    }
                }
                @media (max-width: 767px) {
                    #' . $floating_id . '{
                        width: ' . $float_width_xs . 'px;
                        height: ' . $float_height_xs . 'px;
                        top: ' . $float_position_arr_xs[0] . ';
                        right: ' . $float_position_arr_xs[1] . ';
                        bottom: ' . $float_position_arr_xs[2] . ';
                        left: ' . $float_position_arr_xs[3] . ';
                    }
                    #' . $dialog_id . ' .dialog{
                        width: ' . $dialog_width_xs . 'px;
                        height: ' . $dialog_height_xs . 'px;
                    }
                    #' . $dialog_id . ' .dialog .close{
                        top: ' . $close_position_arr_xs[0] . ';
                        right: ' . $close_position_arr_xs[1] . ';
                        bottom: ' . $close_position_arr_xs[2] . ';
                        left: ' . $close_position_arr_xs[3] . ';
                        width: ' . $close_width_xs . 'px;
                        height: ' . $close_height_xs . 'px;
                    }
                    #' . $floating_id . ' img.phone{
                        display: block;
                    }
                    #' . $floating_id . ' img.pc{
                        display: none;
                    }
                }
            </style>';

            $output.='<div id="' . $floating_id . '">
                <img src="' . $float_img . '" class="pc">
                <img src="' . $phone_float_img . '" class="phone">
            </div>

            <div id="'. $dialog_id .'">
                <div class="mask"></div>
                <div class="dialog">
                    <div class="dialog-content">
                        <img src="'. $dialog_img .'">
                    </div>

                    <div class="close">
                        <img src="'. $close_img .'">
                    </div>
                </div>
            </div>';

            if($dialog_show){
                if($dialog_addon_id){
                    $output .= '<script>
                        $("#' . $floating_id . '").on("click", function(){
                            $("#dialog-' . $dialog_addon_id . '").show();
                        })
                    </script>';
                }else{
                    $output .= '<script>
                        $("#' . $floating_id . '").on("click", function(){
                            $("#' . $dialog_id . '").show();
                        })

                        $("'. $close_ids .'").on("click", function(){
                            $("#' . $dialog_id . '").hide();
                        })
                    </script>';
                };
            }
            // 点击按钮出弹窗结束

            // 点击弹出商桥
            if ($business_bridge_show) {
                $output .='<script>
                    (function(){
                        $("#' . $floating_id . '").on("click", function(){
                            $("#aff-im-root .embed-messageboard-header-close.embed-messageboard-header-max").trigger("click");
                            $("#aff-im-root .embed-icon-default").trigger("click");
                            $("#aff-im-root .embed-icon-content").trigger("click");
                            setTimeout(function(){
                                $("#aff-im-root .embed-icon-content").trigger("click");
                                $("#aff-im-root .embed-icon-default").trigger("click");
                            }, 1000);
                            $("#k_s_ol_inviteWin").show();
                        });
                    })()
                </script>';
            }
            // 点击弹出商桥结束
        }

        return $output;
    }

    public function css()
    {

    }
    //用于设计器中显示
    public static function getTemplate()
    {
        $output='<div>咨询浮窗插件 --- 本段文字用于编辑模式下占位，预览模式下不显示</div>';

        return $output;
    }
}
