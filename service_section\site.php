<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
error_reporting(0);
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonService_section extends JwpagefactoryAddons
{

	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id    = $_GET['site_id'] ?? 0;
		$layout_id  = $_GET['layout_id'] ?? 0;
		$addon_id   = '#jwpf-addon-' . $this->addon->id;
		$settings   = $this->addon->settings;
		//栏目名称
		$section_name = (isset($settings->section_name) && $settings->section_name) ? $settings->section_name : "服务项目";
		//栏目别称
		$section_tips = (isset($settings->section_tips) && $settings->section_tips) ? $settings->section_tips : "product display";
		//是否链接详情页
		$isLinkDetail = (isset($settings->isLinkDetail) && $settings->isLinkDetail) ? $settings->isLinkDetail : "0";
		//详情页模板
		$detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : "";
		//分类显示,选择几级分类
		$type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
		//从第n个分类开始显示(导航)
		$type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : 1;
		//显示n个分类(导航)
		$type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : 4;

		//取出该公司数据
		$categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
		require_once $categorie_helper;
		$item = JwpagefactoryHelperCategories::GetCategoriesthree('com_goods', $site_id, $detail_page_id, $layout_id, $company_id);
		//数据转为多维数组
		$item           = $this->subTree($item);
		$article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
		require_once $article_helper;

		$output = '';
		//最大的div
		$output .= '<div class="service-box">';

		//上部盒子
		$output .= '	<div class="top-box flex flex-between">';
		$output .= '		<div class="title-box">';
		$output .= '			<p class="title">' . $section_name . '</p>';
		$output .= '			<p class="tips">' . $section_tips . '</p>';
		$output .= '			<i class="line"></i>';
		$output .= '		</div>';
		$output .= '		<div class="tab-box flex">';


		//二级数据
		$two_arr = [];
		foreach ($item as $k2 => $v2)
		{
			if (!empty($v2['sub']))
			{
				foreach ($v2['sub'] as $kk => $vv)
				{
					array_push($two_arr, $vv);
				}
			}
			$item[$k2] = $v2;
		}
		//三级数据
		$three_arr = [];
		foreach ($two_arr as $k3 => $v3)
		{
			if (!empty($v3['sub']))
			{
				foreach ($v3['sub'] as $key2 => $val2)
				{
					array_push($three_arr, $val2);
				}
			}
		}

		//一级数据处理从第几条开始
		foreach ($item as $ke1 => $va1)
		{
			if ($ke1 < $type_start - 1)
			{
				unset($item[$ke1]);
			}
		}
		$item = array_merge($item);
		//二级数据处理从第几条开始
		foreach ($two_arr as $ke2 => $va2)
		{
			if ($ke2 < $type_start - 1)
			{
				unset($two_arr[$ke2]);
			}
		}

		$two_arr = array_merge($two_arr);
		//三级数据处理从第几条开始
		foreach ($three_arr as $ke3 => $va3)
		{
			if ($ke3 < $type_start - 1)
			{
				unset($three_arr[$ke3]);
			}
		}
		$three_arr = array_merge($three_arr);

		//一级数据处理显示几条
		foreach ($item as $ke1 => $va1)
		{
			if ($ke1 > $type_num - 1)
			{
				unset($item[$ke1]);
			}
		}
		$item = array_merge($item);
		//二级数据处理显示几条
		foreach ($two_arr as $ke2 => $va2)
		{
			if ($ke2 > $type_num - 1)
			{
				unset($two_arr[$ke2]);
			}
		}
		$two_arr = array_merge($two_arr);

		//三级数据处理显示几条
		foreach ($three_arr as $ke3 => $va3)
		{
			if ($ke3 > $type_num - 1)
			{
				unset($three_arr[$ke3]);
			}
		}
		$three_arr = array_merge($three_arr);

		$list_arr = [];

		switch ($type_parent)
		{
			case 'type1':

				foreach ($item as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '			<div class="item active" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}
					else
					{
						$output .= '			<div class="item" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}
					//取出包括二级三级的所有列表数据
					if (@count($v['sub']) != 0)
					{
						foreach ($v['sub'] as $k_i1 => $v_i1)
						{
							if (@count($v_i1['sub']) != 0)
							{
								foreach ($v_i1['sub'] as $k_i2 => $v_i2)
								{

									$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v_i2['tag_id'], 5);
									foreach ($Don_item as $k_n1 => $v_n1)
									{
										$v_n1['fid'] = $v['tag_id'];
										array_push($list_arr, $v_n1);
									}
								}
							}
							$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v_i1['tag_id'], 5);
							foreach ($Don_item as $k_n1 => $v_n1)
							{
								$v_n1['fid'] = $v['tag_id'];
								array_push($list_arr, $v_n1);
							}
						}
					}
					$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v['tag_id'], 5);
					foreach ($Don_item as $k_n1 => $v_n1)
					{
						$v_n1['fid'] = $v['tag_id'];
						array_push($list_arr, $v_n1);
					}
				}
//				$list_arr = array_reverse($list_arr);   //数组倒叙，开启会导致下边显示最新的五个一级数据，不开会导致显示最新的五个三级数据
				for ($i = 0; $i < count($item); $i++)
				{
					unset($item[$i]['sub']);
					for ($l = 0; $l < count($list_arr); $l++)
					{
						if ($item[$i]['tag_id'] == $list_arr[$l]['fid'])
						{
							$item[$i]['sub'][] = $list_arr[$l];
						}

					}
				}
//				var_dump($item);
//				exit();
				break;
			case 'type2':
				//上边导航的循环开始
				foreach ($two_arr as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '			<div class="item active" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}
					else
					{
						$output .= '			<div class="item" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}
					//取出包括二级三级的所有列表数据
					if (@count($v['sub']) != 0)
					{
						foreach ($v['sub'] as $k_i1 => $v_i1)
						{
							$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v_i1['tag_id'], 5);
							foreach ($Don_item as $k_n1 => $v_n1)
							{
								$v_n1['fid'] = $v['tag_id'];
								array_push($list_arr, $v_n1);
							}
						}
					}
					$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v['tag_id'], 5);
					foreach ($Don_item as $k_n1 => $v_n1)
					{
						$v_n1['fid'] = $v['tag_id'];
						array_push($list_arr, $v_n1);
					}
				}
				foreach ($two_arr as $k => $v)
				{
					unset($two_arr[$k]['sub']);
					foreach ($list_arr as $k_i => $v_i)
					{
						if ($two_arr[$k]['tag_id'] == $list_arr[$k_i]['fid'])
						{
							$two_arr[$k]['sub'][] = $v_i;
						}
					}
				}
				break;
			case 'type3':
				//上边导航的循环开始
				foreach ($three_arr as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '			<div class="item active" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}
					else
					{
						$output .= '			<div class="item" data-id="' . $v['tag_id'] . '">' . mb_substr($v['title'], 0, 4, "UTF8") . '</div>';
					}

					$Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v['tag_id'], 5);
					foreach ($Don_item as $k_n1 => $v_n1)
					{
						$v_n1['fid'] = $v['tag_id'];
						array_push($list_arr, $v_n1);
					}
				}
				foreach ($three_arr as $k => $v)
				{
					unset($three_arr[$k]['sub']);
					foreach ($list_arr as $k_i => $v_i)
					{
						if ($three_arr[$k]['tag_id'] == $list_arr[$k_i]['fid'])
						{
							$three_arr[$k]['sub'][] = $v_i;
						}
					}
				}
				break;
		}
		//循环结束

		$output .= '		</div>';
		$output .= '	</div>';//上部盒子闭合处



		//下边直接在这里循环
		switch ($type_parent)
		{
			case 'type1':
				foreach ($item as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '">';
					}

					else
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '" style="display:none">';
					}
					foreach ($v['sub'] as $key => $val)
					{
						if ($key == 0)
						{


							$output .= '		<div class="large item-box">';//左边的内容
							$output .= '			<div class="img-box">';
							$output .= '				<img src="' . $val['list_img'] . '" alt="">';
							$output .= '			</div>';
							$output .= '			<div class="content-box">';
							$output .= '				<div class="content flex">';
							$output .= '					<div class="text-box">';
							if ($isLinkDetail == 1)
							{
								$output .= '                 <a href="' . $val['link'] . '">';
							}
							else
							{
								$output .= '                 <a>';
							}
							$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
							$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
							$output .= '						<i class="icon"></i>';
							$output .= '		</a>';
							$output .= '					</div>';
							$output .= '				</div>';
							$output .= '			</div>';
							$output .= '		</div>';//左边内容结束

						}
					}


					$output .= '		<div class="list-small-box flex">';//右边内容开始
					foreach ($v['sub'] as $key => $val)
					{
						if ($val['fid'] == $v['tag_id'])
						{
							if ($key != 0)
							{
								if ($key < 5)
								{

									$output .= '			<div class="small item-box">';//右边一个小盒子开始
									$output .= '				<div class="img-box">';
									$output .= '					<img src="' . $val['list_img'] . '" alt="">';
									$output .= '				</div>';
									$output .= '				<div class="content-box">';
									$output .= '					<div class="content flex">';
									$output .= '						<div class="text-box">';
									if ($isLinkDetail == 1)
									{
										$output .= '                 <a href="' . $val['link'] . '">';
									}
									else
									{
										$output .= '                 <a>';
									}
									$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
									$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
									$output .= '							<i class="icon"></i>';
									$output .= '		</a>';
									$output .= '						</div>';
									$output .= '					</div>';
									$output .= '				</div>';
									$output .= '			</div>';//一个小盒子结束

								}
							}
						}
					}
					$output .= '		</div>';//右边内容结束


					$output .= '	</div>';//下边循环结束


				}
				break;
			case 'type2':
				foreach ($two_arr as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '">';
					}

					else
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '" style="display:none">';
					}
					foreach ($v['sub'] as $key => $val)
					{
						if ($key == 0)
						{


							$output .= '		<div class="large item-box">';//左边的内容
							$output .= '			<div class="img-box">';
							$output .= '				<img src="' . $val['list_img'] . '" alt="">';
							$output .= '			</div>';
							$output .= '			<div class="content-box">';
							$output .= '				<div class="content flex">';
							$output .= '					<div class="text-box">';
							if ($isLinkDetail == 1)
							{
								$output .= '                 <a href="' . $val['link'] . '">';
							}
							else
							{
								$output .= '                 <a>';
							}
							$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
							$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
							$output .= '						<i class="icon"></i>';
							$output .= '		</a>';
							$output .= '					</div>';
							$output .= '				</div>';
							$output .= '			</div>';
							$output .= '		</div>';//左边内容结束

						}
					}


					$output .= '		<div class="list-small-box flex">';//右边内容开始
					foreach ($v['sub'] as $key => $val)
					{
						if ($val['fid'] == $v['tag_id'])
						{
							if ($key != 0)
							{
								if ($key < 5)
								{

									$output .= '			<div class="small item-box">';//右边一个小盒子开始
									$output .= '				<div class="img-box">';
									$output .= '					<img src="' . $val['list_img'] . '" alt="">';
									$output .= '				</div>';
									$output .= '				<div class="content-box">';
									$output .= '					<div class="content flex">';
									$output .= '						<div class="text-box">';
									if ($isLinkDetail == 1)
									{
										$output .= '                 <a href="' . $val['link'] . '">';
									}
									else
									{
										$output .= '                 <a>';
									}
									$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
									$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
									$output .= '							<i class="icon"></i>';
									$output .= '		</a>';
									$output .= '						</div>';
									$output .= '					</div>';
									$output .= '				</div>';
									$output .= '			</div>';//一个小盒子结束

								}
							}

						}
					}
					$output .= '		</div>';//右边内容结束


					$output .= '	</div>';//下边循环结束


				}
				break;
			case
			'type3':
				foreach ($three_arr as $k => $v)
				{
					if ($k == 0)
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '">';
					}

					else
					{
						$output .= '	<div class="list-box flex" id="cate-' . $v['tag_id'] . '" style="display:none">';
					}
					foreach ($v['sub'] as $key => $val)
					{
						if ($key == 0)
						{

							if ($key < 5)
							{
								$output .= '		<div class="large item-box">';//左边的内容
								$output .= '			<div class="img-box">';
								$output .= '				<img src="' . $val['list_img'] . '" alt="">';
								$output .= '			</div>';
								$output .= '			<div class="content-box">';
								$output .= '				<div class="content flex">';
								$output .= '					<div class="text-box">';
								if ($isLinkDetail == 1)
								{
									$output .= '                 <a href="' . $val['link'] . '">';
								}
								else
								{
									$output .= '                 <a>';
								}
								$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
								$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
								$output .= '						<i class="icon"></i>';
								$output .= '		</a>';
								$output .= '					</div>';
								$output .= '				</div>';
								$output .= '			</div>';
								$output .= '		</div>';//左边内容结束

							}
						}
					}


					$output .= '		<div class="list-small-box flex">';//右边内容开始
					foreach ($v['sub'] as $key => $val)
					{
						if ($val['fid'] == $v['tag_id'])
						{
							if ($key != 0)
							{

								$output .= '			<div class="small item-box">';//右边一个小盒子开始
								$output .= '				<div class="img-box">';
								$output .= '					<img src="' . $val['list_img'] . '" alt="">';
								$output .= '				</div>';
								$output .= '				<div class="content-box">';
								$output .= '					<div class="content flex">';
								$output .= '						<div class="text-box">';
								if ($isLinkDetail == 1)
								{
									$output .= '                 <a href="' . $val['link'] . '">';
								}
								else
								{
									$output .= '                 <a>';
								}
								$output .= '						<h3>' . mb_substr($val['list_title'], 0, 8, 'UTF8') . '</h3>';
								$output .= '							<p>' . mb_substr($val['introtext'], 0, 8, "UTF8") . '</p>';
								$output .= '							<i class="icon"></i>';
								$output .= '		</a>';
								$output .= '						</div>';
								$output .= '					</div>';
								$output .= '				</div>';
								$output .= '			</div>';//一个小盒子结束

							}
						}
					}
					$output .= '		</div>';//右边内容结束


					$output .= '	</div>';//下边循环结束


				}
				break;
		}

		$output .= '</div>';//最大div结束



		return $output;
	}

	public
	function css()
	{
		//插件id
		$addonId  = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
		//主色
		$section_main_color = (isset($settings->section_main_color) && $settings->section_main_color) ? $settings->section_main_color : "#E29728";
		//栏目宽度
		$section_name_w_md = (isset($settings->section_name_w) && $settings->section_name_w) ? $settings->section_name_w : "";
		$section_name_w_sm = (isset($settings->section_name_w_sm) && $settings->section_name_w_sm) ? $settings->section_name_w_sm : "";
		$section_name_w_xs = (isset($settings->section_name_w_xs) && $settings->section_name_w_xs) ? $settings->section_name_w_xs : "";
		//栏目文字颜色
		$section_name_color = (isset($settings->section_name_color) && $settings->section_name_color) ? $settings->section_name_color : "#333";
		//栏目字体大小
		$section_name_fontsize_md = (isset($settings->section_name_fontsize) && $settings->section_name_fontsize) ? $settings->section_name_fontsize : "";
		$section_name_fontsize_sm = (isset($settings->section_name_fontsize_sm) && $settings->section_name_fontsize_sm) ? $settings->section_name_fontsize_sm : "";
		$section_name_fontsize_xs = (isset($settings->section_name_fontsize_xs) && $settings->section_name_fontsize_xs) ? $settings->section_name_fontsize_xs : "";
		//栏目别称文字颜色
		$section_tips_color = (isset($settings->section_tips_color) && $settings->section_tips_color) ? $settings->section_tips_color : "#fff";
		//栏目别称字体大小
		$section_tips_fontsize_md = (isset($settings->section_tips_fontsize) && $settings->section_tips_fontsize) ? $settings->section_tips_fontsize : "";
		$section_tips_fontsize_sm = (isset($settings->section_tips_fontsize_sm) && $settings->section_tips_fontsize_sm) ? $settings->section_tips_fontsize_sm : "";
		$section_tips_fontsize_xs = (isset($settings->section_tips_fontsize_xs) && $settings->section_tips_fontsize_xs) ? $settings->section_tips_fontsize_xs : "";
		//栏目别称高度
		$section_tips_H_md = (isset($settings->section_tips_H) && $settings->section_tips_H) ? $settings->section_tips_H : "";
		$section_tips_H_sm = (isset($settings->section_tips_H_sm) && $settings->section_tips_H_sm) ? $settings->section_tips_H_sm : "";
		$section_tips_H_xs = (isset($settings->section_tips_H_xs) && $settings->section_tips_H_xs) ? $settings->section_tips_H_xs : "";

		//栏目主色
		$section_column_color = (isset($settings->section_column_color) && $settings->section_column_color) ? $settings->section_column_color : "#E29728";
		//栏目边框颜色
		$section_column_borderColor = (isset($settings->section_column_borderColor) && $settings->section_column_borderColor) ? $settings->section_column_borderColor : "#EAEAEA";
		//栏目左边距
		$section_column_mgL_md = (isset($settings->section_column_mgL) && $settings->section_column_mgL) ? $settings->section_column_mgL : "";
		$section_column_mgL_sm = (isset($settings->section_column_mgL_sm) && $settings->section_column_mgL_sm) ? $settings->section_column_mgL_sm : "";
		$section_column_mgL_xs = (isset($settings->section_column_mgL_xs) && $settings->section_column_mgL_xs) ? $settings->section_column_mgL_xs : "";
		//选项卡背景色
		$section_tab_bgColor = (isset($settings->section_tab_bgColor) && $settings->section_tab_bgColor) ? $settings->section_tab_bgColor : "#333";
		//选项卡文字颜色
		$section_tab_color = (isset($settings->section_tab_color) && $settings->section_tab_color) ? $settings->section_tab_color : "#fff";
		//选项卡文字大小
		$section_tab_fontsize_md = (isset($settings->section_tab_fontsize) && $settings->section_tab_fontsize) ? $settings->section_tab_fontsize : "";
		$section_tab_fontsize_sm = (isset($settings->section_tab_fontsize_sm) && $settings->section_tab_fontsize_sm) ? $settings->section_tab_fontsize_sm : "";
		$section_tab_fontsize_xs = (isset($settings->section_tab_fontsize_xs) && $settings->section_tab_fontsize_xs) ? $settings->section_tab_fontsize_xs : "";
		//选项卡选中文字大小
		$section_tab_active_fontsize_md = (isset($settings->section_tab_active_fontsize) && $settings->section_tab_active_fontsize) ? $settings->section_tab_active_fontsize : "";
		$section_tab_active_fontsize_sm = (isset($settings->section_tab_active_fontsize_sm) && $settings->section_tab_active_fontsize_sm) ? $settings->section_tab_active_fontsize_sm : "";
		$section_tab_active_fontsize_xs = (isset($settings->section_tab_active_fontsize_xs) && $settings->section_tab_active_fontsize_xs) ? $settings->section_tab_active_fontsize_xs : "";
		//选项卡宽度
		$section_tab_w_md = (isset($settings->section_tab_w) && $settings->section_tab_w) ? $settings->section_tab_w : "";
		$section_tab_w_sm = (isset($settings->section_tab_w_sm) && $settings->section_tab_w_sm) ? $settings->section_tab_w_sm : "";
		$section_tab_w_xs = (isset($settings->section_tab_w_xs) && $settings->section_tab_w_xs) ? $settings->section_tab_w_xs : "";
		//选项卡高度
		$section_tab_H_md = (isset($settings->section_tab_H) && $settings->section_tab_H) ? $settings->section_tab_H : "";
		$section_tab_H_sm = (isset($settings->section_tab_H_sm) && $settings->section_tab_H_sm) ? $settings->section_tab_H_sm : "";
		$section_tab_H_xs = (isset($settings->section_tab_H_xs) && $settings->section_tab_H_xs) ? $settings->section_tab_H_xs : "";
		//选项卡选中背景色
		$section_tab_active_bgColor = (isset($settings->section_tab_active_bgColor) && $settings->section_tab_active_bgColor) ? $settings->section_tab_active_bgColor : "#E29728";
		//选项卡选中文字颜色
		$section_tab_active_color = (isset($settings->section_tab_active_color) && $settings->section_tab_active_color) ? $settings->section_tab_active_color : "#fff";
		//选项卡选中高度
		$section_tab_active_H_md = (isset($settings->section_tab_active_H) && $settings->section_tab_active_H) ? $settings->section_tab_active_H : "";
		$section_tab_active_H_sm = (isset($settings->section_tab_active_H_sm) && $settings->section_tab_active_H_sm) ? $settings->section_tab_active_H_sm : "";
		$section_tab_active_H_xs = (isset($settings->section_tab_active_H_xs) && $settings->section_tab_active_H_xs) ? $settings->section_tab_active_H_xs : "";
		//选项卡动画效果时间
		$section_tab_active_time = (isset($settings->section_tab_active_time) && $settings->section_tab_active_time) ? $settings->section_tab_active_time : "200";
		//左侧大图宽度
		$section_content_large_w_md = (isset($settings->section_content_large_w) && $settings->section_content_large_w) ? $settings->section_content_large_w : "";
		$section_content_large_w_sm = (isset($settings->section_content_large_w_sm) && $settings->section_content_large_w_sm) ? $settings->section_content_large_w_sm : "";
		$section_content_large_w_xs = (isset($settings->section_content_large_w_xs) && $settings->section_content_large_w_xs) ? $settings->section_content_large_w_xs : "";
		//左侧大图高度
		$section_content_large_h_md = (isset($settings->section_content_large_h) && $settings->section_content_large_h) ? $settings->section_content_large_h : "";
		$section_content_large_h_sm = (isset($settings->section_content_large_h_sm) && $settings->section_content_large_h_sm) ? $settings->section_content_large_h_sm : "";
		$section_content_large_h_xs = (isset($settings->section_content_large_h_xs) && $settings->section_content_large_h_xs) ? $settings->section_content_large_h_xs : "";
		//内容标题文字大小
		$section_content_title_fontsize_md = (isset($settings->section_content_title_fontsize) && $settings->section_content_title_fontsize) ? $settings->section_content_title_fontsize : "";
		$section_content_title_fontsize_sm = (isset($settings->section_content_title_fontsize_sm) && $settings->section_content_title_fontsize_sm) ? $settings->section_content_title_fontsize_sm : "";
		$section_content_title_fontsize_xs = (isset($settings->section_content_title_fontsize_xs) && $settings->section_content_title_fontsize_xs) ? $settings->section_content_title_fontsize_xs : "";
		//内容标题下边距
		$section_content_title_mgB_md = (isset($settings->section_content_title_mgB) && $settings->section_content_title_mgB) ? $settings->section_content_title_mgB : "";
		$section_content_title_mgB_sm = (isset($settings->section_content_title_mgB_sm) && $settings->section_content_title_mgB_sm) ? $settings->section_content_title_mgB_sm : "";
		$section_content_title_mgB_xs = (isset($settings->section_content_title_mgB_xs) && $settings->section_content_title_mgB_xs) ? $settings->section_content_title_mgB_xs : "";
		//内容小标题文字大小
		$section_content_sTitle_fontsize_md = (isset($settings->section_content_sTitle_fontsize) && $settings->section_content_sTitle_fontsize) ? $settings->section_content_sTitle_fontsize : "";
		$section_content_sTitle_fontsize_sm = (isset($settings->section_content_sTitle_fontsize_sm) && $settings->section_content_sTitle_fontsize_sm) ? $settings->section_content_sTitle_fontsize_sm : "";
		$section_content_sTitle_fontsize_xs = (isset($settings->section_content_sTitle_fontsize_xs) && $settings->section_content_sTitle_fontsize_xs) ? $settings->section_content_sTitle_fontsize_xs : "";
		//内容小标题下边距
		$section_content_sTitle_mgB_md = (isset($settings->section_content_sTitle_mgB) && $settings->section_content_sTitle_mgB) ? $settings->section_content_sTitle_mgB : "";
		$section_content_sTitle_mgB_sm = (isset($settings->section_content_sTitle_mgB_sm) && $settings->section_content_sTitle_mgB_sm) ? $settings->section_content_sTitle_mgB_sm : "";
		$section_content_sTitle_mgB_xs = (isset($settings->section_content_sTitle_mgB_xs) && $settings->section_content_sTitle_mgB_xs) ? $settings->section_content_sTitle_mgB_xs : "";
		//内容下方小图标大小
		$section_content_textIcon_w_md = (isset($settings->section_content_textIcon_w) && $settings->section_content_textIcon_w) ? $settings->section_content_textIcon_w : "";
		$section_content_textIcon_w_sm = (isset($settings->section_content_textIcon_w_sm) && $settings->section_content_textIcon_w_sm) ? $settings->section_content_textIcon_w_sm : "";
		$section_content_textIcon_w_xs = (isset($settings->section_content_textIcon_w_xs) && $settings->section_content_textIcon_w_xs) ? $settings->section_content_textIcon_w_xs : "";
		//内容下方小图标
		$service_content_textIcon = (isset($settings->service_content_textIcon) && $settings->service_content_textIcon) ? $settings->service_content_textIcon : "https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png";
		//动画效果时间
		$section_content_active_time = (isset($settings->section_content_active_time) && $settings->section_content_active_time) ? $settings->section_content_active_time : "200";
		$css                         = '';
		$css                         .=
			$addonId . ' .service-box {
				width: 100%;
				margin: 80px auto;
			}
			' . $addonId . ' .flex {
				display: flex;
			}
			' . $addonId . ' .flex-between {
				justify-content: space-between;
			}
			' . $addonId . ' p,h3 {
				margin: 0;
			}
			' . $addonId . ' img {
				width: 100%;
				display: block;
			}
			' . $addonId . ' .service-box .top-box {
				margin-bottom: 30px;
				align-items: flex-end;
			}
			' . $addonId . ' .service-box .title-box {
				border: ' . $section_column_borderColor . ' solid 1px;
				text-align: center;
				padding: 0 60px;
				height: 80px;
				margin-left: ' . $section_column_mgL_md . 'px;
				width: ' . $section_name_w_md . 'px;
			}
			' . $addonId . ' .service-box .title-box .title {
				font-size: ' . $section_name_fontsize_md . 'px;
				padding: 0 20px;
				background-color: #FFFFFF;
				line-height: 42px;
				font-weight: bold;
				margin-top: -21px;
				margin-bottom: 10px;
				color: ' . $section_name_color . ';
			}
			' . $addonId . ' .service-box .title-box .tips {
				font-size: ' . $section_tips_fontsize_md . 'px;
				background-color: ' . $section_column_color . ';
				color: ' . $section_tips_color . ';
				text-align: center;
				line-height: ' . $section_tips_H_md . 'px;
				text-transform: uppercase;
				position: relative;
			}
			' . $addonId . ' .service-box .title-box .tips::before,' . $addonId . ' .service-box .title-box .tips::after {
				content: "";
				width: 30px;
				height: 1px;
				background-color: ' . $section_column_color . ';
				position: absolute;
				top: 0;
				bottom: 0;
				margin: auto;
			}
			' . $addonId . ' .service-box .title-box .tips::before {
				left: -30px;
			}
			' . $addonId . ' .service-box .title-box .tips::after {
				right: -30px;
			}
			' . $addonId . ' .service-box .title-box .line {
				display: inline-block;
				width: 20px;
				height: 2px;
				background-color: ' . $section_column_color . ';
				margin-top: 0;
			}
			' . $addonId . ' .service-box .tab-box {
				width: ' . $section_tab_w_md . 'px;
				align-items: flex-end;
				margin-bottom: 10px;
			}
			' . $addonId . ' .service-box .tab-box .item {
				width: 100%;
				text-align: center;
				font-size: ' . $section_tab_fontsize_md . 'px;
				background-color: ' . $section_tab_bgColor . ';
				color: ' . $section_tab_color . ';
				height: ' . $section_tab_H_md . 'px;
				line-height: ' . $section_tab_H_md . 'px;
				/* padding: 0 30px; */
				cursor: pointer;
				transition: all ease-in-out ' . $section_tab_active_time . 'ms;
			}
			' . $addonId . ' .service-box .tab-box .item.active, ' . $addonId . ' .service-box .tab-box .item:hover {
				background-color: ' . $section_tab_active_bgColor . ';
				height: ' . $section_tab_active_H_md . 'px;
				line-height: ' . $section_tab_active_H_md . 'px;
				color: ' . $section_tab_active_color . ';
				font-size: ' . $section_tab_active_fontsize_md . 'px;
				/* font-size: 18px; */
			}
			' . $addonId . ' .service-box .list-box .large {
				width: ' . $section_content_large_w_md . 'px;
				height: ' . $section_content_large_h_md . 'px;
				margin-right: 1%;
			}
			' . $addonId . ' .service-box .list-box .list-small-box {
				width: calc(100% - ' . $section_content_large_w_md . 'px + 20px);
				height: ' . $section_content_large_h_md . 'px;
				flex-wrap: wrap;
				justify-content: space-between;
			}
			' . $addonId . ' .service-box .list-box .small {
				width: 49%;
				margin-right: 2%;
			}
			' . $addonId . ' .service-box .list-box .small:nth-child(2n) {
				margin-right: 0;
			}
			' . $addonId . ' .service-box .list-box .item-box {
				cursor: pointer;
				position: relative;
			}
			' . $addonId . ' .service-box .list-box .item-box .img-box {
				width: 100%;
				height: 100%;
				overflow: hidden;
			}
			' . $addonId . ' .service-box .list-box .item-box .img-box img {
				height: 100%;
				object-fit: cover;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
			}
			' . $addonId . ' .service-box .list-box .item-box:hover .img-box img {
				transform: scale(1.2);
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box {
				position: absolute;
				left: 0;
				top: 0;
				background-color: rgba(0, 0, 0, 0.7);
				color: #fff;
				width: 100%;
				height: 100%;
				opacity: 0;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box:hover {
				opacity: 1;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box .content {
				margin: 20px;
				border: #EAEAEA solid 1px;
				height: calc(100% - 40px);
				flex-direction: column;
				align-items: center;
				justify-content: center;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
				text-align: center;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box .text-box {
				transform: translateY(200px);
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box:hover .text-box {
				transform: translateY(-0px);
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box .content h3 {
				font-size: ' . $section_content_title_fontsize_md . 'px;
				font-weight: inherit;
				margin-bottom: ' . $section_content_title_mgB_md . 'px;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box .content p {
				font-size: ' . $section_content_sTitle_fontsize_md . 'px;
				font-weight: 400;
				margin-bottom: ' . $section_content_sTitle_mgB_md . 'px;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
			}
			' . $addonId . ' .service-box .list-box .item-box .content-box .content .icon {
				width: ' . $section_content_textIcon_w_md . 'px;
				height: ' . $section_content_textIcon_w_md . 'px;
				background: url(' . $service_content_textIcon . ') no-repeat center;
				background-size: contain;
				transition: all ease-in-out ' . $section_content_active_time . 'ms;
				display: inline-block;
			}
			' . $addonId . ' .service-box .list-box .small {
				height: 49%;
				margin-bottom: 1.6%;
			}
			@media (min-width: 768px) and (max-width: 991px) {
	            ' . $addonId . ' .service-box .top-box {
	                display: block;
	            }
	            ' . $addonId . ' .service-box .title-box { 
	                margin-left: 0px;
	                width: 100%;
	            }
	            ' . $addonId . ' .service-box .title-box .title {
					font-size: ' . $section_name_fontsize_sm . 'px;
				}
				' . $addonId . ' .service-box .title-box .tips {
					font-size: ' . $section_tips_fontsize_sm . 'px;
					line-height: ' . $section_tips_H_sm . 'px;
				}
				' . $addonId . ' .service-box .tab-box {
					width: 100%;
					margin-top: 20px;
				}
				' . $addonId . ' .service-box .tab-box .item {
					font-size: ' . $section_tab_fontsize_sm . 'px;
					height: ' . $section_tab_H_sm . 'px;
					line-height: ' . $section_tab_H_sm . 'px;
				}
				' . $addonId . ' .service-box .tab-box .item.active, ' . $addonId . ' .service-box .tab-box .item:hover {
					height: ' . $section_tab_active_H_sm . 'px;
					line-height: ' . $section_tab_active_H_sm . 'px;
					font-size: ' . $section_tab_active_fontsize_sm . 'px;
				}
				' . $addonId . ' .service-box .list-box {
					display: block;
				}
				' . $addonId . ' .service-box .list-box .large {
					width: 100%;
					height: ' . $section_content_large_h_sm . 'px;
					margin-right: 0;
					margin-bottom: 15px;
				}
				' . $addonId . ' .service-box .list-box .list-small-box {
					width: 100%;
					height: ' . $section_content_large_h_sm . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content h3 {
					font-size: ' . $section_content_title_fontsize_sm . 'px;
					margin-bottom: ' . $section_content_title_mgB_sm . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content p {
					font-size: ' . $section_content_sTitle_fontsize_sm . 'px;
					margin-bottom: ' . $section_content_sTitle_mgB_sm . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content .icon {
					width: ' . $section_content_textIcon_w_sm . 'px;
					height: ' . $section_content_textIcon_w_sm . 'px;
				}
	        }
			@media (max-width:768px) {
	            ' . $addonId . ' .service-box .top-box {
	                display: block;
	            }
	            ' . $addonId . ' .service-box .title-box { 
	                margin-left: 0px;
	                width: 100%;
	            }
	            ' . $addonId . ' .service-box .title-box .title {
					font-size: ' . $section_name_fontsize_xs . 'px;
				}
				' . $addonId . ' .service-box .title-box .tips {
					font-size: ' . $section_tips_fontsize_xs . 'px;
					line-height: ' . $section_tips_H_xs . 'px;
				}
				' . $addonId . ' .service-box .tab-box {
					width: 100%;
					margin-top: 20px;
				}
				' . $addonId . ' .service-box .tab-box .item {
					font-size: ' . $section_tab_fontsize_xs . 'px;
					height: ' . $section_tab_H_xs . 'px;
					line-height: ' . $section_tab_H_xs . 'px;
				}
				' . $addonId . ' .service-box .tab-box .item.active, ' . $addonId . ' .service-box .tab-box .item:hover {
					height: ' . $section_tab_active_H_xs . 'px;
					line-height: ' . $section_tab_active_H_xs . 'px;
					font-size: ' . $section_tab_active_fontsize_xs . 'px;
				}
				' . $addonId . ' .service-box .list-box {
					display: block;
				}
				' . $addonId . ' .service-box .list-box .large {
					width: 100%;
					height: ' . $section_content_large_h_xs . 'px;
					margin-right: 0;
					margin-bottom: 15px;
				}
				' . $addonId . ' .service-box .list-box .list-small-box {
					width: 100%;
					height: ' . $section_content_large_h_xs . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content h3 {
					font-size: ' . $section_content_title_fontsize_xs . 'px;
					margin-bottom: ' . $section_content_title_mgB_xs . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content p {
					font-size: ' . $section_content_sTitle_fontsize_xs . 'px;
					margin-bottom: ' . $section_content_sTitle_mgB_xs . 'px;
				}
				' . $addonId . ' .service-box .list-box .item-box .content-box .content .icon {
					width: ' . $section_content_textIcon_w_xs . 'px;
					height: ' . $section_content_textIcon_w_xs . 'px;
				}
	        }
	    ';

		return $css;
	}

//    内部js执行
	public
	function js()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;

		$settings = $this->addon->settings;

		$js = 'jQuery(function($){
		    $("' . $addon_id . ' .service-box .tab-box .item").click(function(){
				$(this).addClass("active").siblings().removeClass("active");
				let id = $(this).attr("data-id");
				$("' . $addon_id . ' .list-box").hide();
				$("' . $addon_id . ' #cate-" + id).show();
		    })
		})';

		return $js;
	}

	public
	static function getTemplate()
	{
		$output = '
        <#
        //插件id
        var addonId = "#jwpf-addon-" + data.id;
        //主色
        var section_main_color = (!_.isEmpty(data.section_main_color) && data.section_main_color) ? data.section_main_color : "#E29728";
        //栏目名称
        var section_name = (!_.isEmpty(data.section_name) && data.section_name) ? data.section_name : "服务项目";
        //栏目宽度
        var section_name_w = (!_.isEmpty(data.section_name_w) && data.section_name_w) ? data.section_name_w : "";
        //栏目文字颜色
        var section_name_color = (!_.isEmpty(data.section_name_color) && data.section_name_color) ? data.section_name_color : "#333";
        //栏目字体大小
        var section_name_fontsize = (!_.isEmpty(data.section_name_fontsize) && data.section_name_fontsize) ? data.section_name_fontsize : "";
        //栏目别称
        var section_tips = (!_.isEmpty(data.section_tips) && data.section_tips) ? data.section_tips : "product display";
        //栏目别称文字颜色
        var section_tips_color = (!_.isEmpty(data.section_tips_color) && data.section_tips_color) ? data.section_tips_color : "#fff";
        //栏目别称字体大小
        var section_tips_fontsize = (!_.isEmpty(data.section_tips_fontsize) && data.section_tips_fontsize) ? data.section_tips_fontsize : "";
        //栏目别称高度
        var section_tips_H = (!_.isEmpty(data.section_tips_H) && data.section_tips_H) ? data.section_tips_H : "";
        //栏目主色
        var section_column_color = (!_.isEmpty(data.section_column_color) && data.section_column_color) ? data.section_column_color : "#E29728";
        //栏目边框颜色
        var section_column_borderColor = (!_.isEmpty(data.section_column_borderColor) && data.section_column_borderColor) ? data.section_column_borderColor : "#EAEAEA";
        //栏目左边距
        var section_column_mgL = (!_.isEmpty(data.section_column_mgL) && data.section_column_mgL) ? data.section_column_mgL : "";
        //选项卡背景色
        var section_tab_bgColor = (!_.isEmpty(data.section_tab_bgColor) && data.section_tab_bgColor) ? data.section_tab_bgColor : "#333";
        //选项卡文字颜色
        var section_tab_color = (!_.isEmpty(data.section_tab_color) && data.section_tab_color) ? data.section_tab_color : "#fff";
        //选项卡文字大小
        var section_tab_fontsize = (!_.isEmpty(data.section_tab_fontsize) && data.section_tab_fontsize) ? data.section_tab_fontsize : "";
        //选项卡选中文字大小
        var section_tab_active_fontsize = (!_.isEmpty(data.section_tab_active_fontsize) && data.section_tab_active_fontsize) ? data.section_tab_active_fontsize : "";
        //选项卡宽度
        var section_tab_w = (!_.isEmpty(data.section_tab_w) && data.section_tab_w) ? data.section_tab_w : "";
        //选项卡高度
        var section_tab_H = (!_.isEmpty(data.section_tab_H) && data.section_tab_H) ? data.section_tab_H : "";
        //选项卡选中背景色
        var section_tab_active_bgColor = (!_.isEmpty(data.section_tab_active_bgColor) && data.section_tab_active_bgColor) ? data.section_tab_active_bgColor : "#E29728";
        //选项卡选中文字颜色
        var section_tab_active_color = (!_.isEmpty(data.section_tab_active_color) && data.section_tab_active_color) ? data.section_tab_active_color : "#fff";
        //选项卡选中高度
        var section_tab_active_H = (!_.isEmpty(data.section_tab_active_H) && data.section_tab_active_H) ? data.section_tab_active_H : "";
        //选项卡动画效果时间
        var section_tab_active_time = (!_.isEmpty(data.section_tab_active_time) && data.section_tab_active_time) ? data.section_tab_active_time : "200";
        //左侧大图宽度
        var section_content_large_w = (!_.isEmpty(data.section_content_large_w) && data.section_content_large_w) ? data.section_content_large_w : "";
        //左侧大图高度
        var section_content_large_h = (!_.isEmpty(data.section_content_large_h) && data.section_content_large_h) ? data.section_content_large_h : "";
        //内容标题文字大小
        var section_content_title_fontsize = (!_.isEmpty(data.section_content_title_fontsize) && data.section_content_title_fontsize) ? data.section_content_title_fontsize : "";
        //内容标题下边距
        var section_content_title_mgB = (!_.isEmpty(data.section_content_title_mgB) && data.section_content_title_mgB) ? data.section_content_title_mgB : "";
        //内容小标题文字大小
        var section_content_sTitle_fontsize = (!_.isEmpty(data.section_content_sTitle_fontsize) && data.section_content_sTitle_fontsize) ? data.section_content_sTitle_fontsize : "";
        //内容小标题下边距
        var section_content_sTitle_mgB = (!_.isEmpty(data.section_content_sTitle_mgB) && data.section_content_sTitle_mgB) ? data.section_content_sTitle_mgB : "";
        //内容下方小图标大小
        var section_content_textIcon_w = (!_.isEmpty(data.section_content_textIcon_w) && data.section_content_textIcon_w) ? data.section_content_textIcon_w : "";
        //内容下方小图标
        var service_content_textIcon = (!_.isEmpty(data.service_content_textIcon) && data.service_content_textIcon) ? data.service_content_textIcon : "https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png";
        //动画效果时间
        var section_content_active_time = (!_.isEmpty(data.section_content_active_time) && data.section_content_active_time) ? data.section_content_active_time : "200";
        //是否链接详情页
        var isLinkDetail = (!_.isEmpty(data.isLinkDetail) && data.isLinkDetail) ? data.isLinkDetail : "0";
        //详情页模板
        var detail_page_id = (!_.isEmpty(data.detail_page_id) && data.detail_page_id) ? data.detail_page_id : "";
        #>
        <style>
       {{ addonId }} .service-box {
			width: 100%;
			margin: 80px auto;
		}
		{{ addonId }} .flex {
			display: flex;
		}
		{{ addonId }} .flex-between {
			justify-content: space-between;
		}
		{{ addonId }} p,h3 {
			margin: 0;
		}
		{{ addonId }} img {
			width: 100%;
			display: block;
		}
		{{ addonId }} .service-box .top-box {
			margin-bottom: 30px;
			align-items: flex-end;
		}
		{{ addonId }} .service-box .title-box {
			border: {{ section_column_borderColor }} solid 1px;
			text-align: center;
			padding: 0 60px;
			height: 80px;
			margin-left: {{ section_column_mgL.md }}px;
			width: {{ section_name_w.md }}px;
		}
		{{ addonId }} .service-box .title-box .title {
			font-size: {{ section_name_fontsize.md }}px;
			padding: 0 20px;
			background-color: #FFFFFF;
			line-height: 42px;
			font-weight: bold;
			margin-top: -21px;
			margin-bottom: 10px;
			color: {{ section_name_color }};
		}
		{{ addonId }} .service-box .title-box .tips {
			font-size: {{ section_tips_fontsize.md }}px;
			background-color: {{ section_column_color }};
			color: {{ section_tips_color }};
			text-align: center;
			line-height: {{ section_tips_H.md }}px;
			text-transform: uppercase;
			position: relative;
		}
		{{ addonId }} .service-box .title-box .tips::before,{{ addonId }} .service-box .title-box .tips::after {
			content: "";
			width: 30px;
			height: 1px;
			background-color: {{ section_column_color }};
			position: absolute;
			top: 0;
			bottom: 0;
			margin: auto;
		}
		{{ addonId }} .service-box .title-box .tips::before {
			left: -30px;
		}
		{{ addonId }} .service-box .title-box .tips::after {
			right: -30px;
		}
		{{ addonId }} .service-box .title-box .line {
			display: inline-block;
			width: 20px;
			height: 2px;
			background-color: {{ section_column_color }};
			margin-top: 0;
		}
		{{ addonId }} .service-box .tab-box {
			width: {{ section_tab_w.md }}px;
			align-items: flex-end;
			margin-bottom: 10px;
		}
		{{ addonId }} .service-box .tab-box .item {
			width: 100%;
			text-align: center;
			font-size: {{ section_tab_fontsize.md }}px;
			background-color: {{ section_tab_bgColor }};
			color: {{ section_tab_color }};
			height: {{ section_tab_H.md }}px;
			line-height: {{ section_tab_H.md }}px;
			/* padding: 0 30px; */
			cursor: pointer;
			transition: all ease-in-out {{ section_tab_active_time }}ms;
		}
		{{ addonId }} .service-box .tab-box .item.active, {{ addonId }} .service-box .tab-box .item:hover {
			background-color: {{ section_tab_active_bgColor }};
			height: {{ section_tab_active_H.md }}px;
			line-height: {{ section_tab_active_H.md }}px;
			color: {{ section_tab_active_color }};
			font-size: {{ section_tab_active_fontsize.md }}px;
			/* font-size: 18px; */
		}
		{{ addonId }} .service-box .list-box .large {
			width: {{ section_content_large_w.md }}px;
			height: {{ section_content_large_h.md }}px;
			margin-right: 1%;
		}
		{{ addonId }} .service-box .list-box .list-small-box {
			width: calc(100% - {{ section_content_large_w.md }}px + 20px);
			height: {{ section_content_large_h.md }}px;
			flex-wrap: wrap;
			justify-content: space-between;
		}
		{{ addonId }} .service-box .list-box .small {
			width: 49%;
			margin-right: 2%;
		}
		{{ addonId }} .service-box .list-box .small:nth-child(2n) {
			margin-right: 0;
		}
		{{ addonId }} .service-box .list-box .item-box {
			cursor: pointer;
			position: relative;
		}
		{{ addonId }} .service-box .list-box .item-box .img-box {
			width: 100%;
			height: 100%;
			overflow: hidden;
		}
		{{ addonId }} .service-box .list-box .item-box .img-box img {
			height: 100%;
			object-fit: cover;
			transition: all ease-in-out {{ section_content_active_time }}ms;
		}
		{{ addonId }} .service-box .list-box .item-box:hover .img-box img {
			transform: scale(1.2);
		}
		{{ addonId }} .service-box .list-box .item-box .content-box {
			position: absolute;
			left: 0;
			top: 0;
			background-color: rgba(0, 0, 0, 0.7);
			color: #fff;
			width: 100%;
			height: 100%;
			opacity: 0;
			transition: all ease-in-out {{ section_content_active_time }}ms;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box:hover {
			opacity: 1;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box .content {
			margin: 20px;
			border: #EAEAEA solid 1px;
			height: calc(100% - 40px);
			flex-direction: column;
			align-items: center;
			justify-content: center;
			transition: all ease-in-out {{ section_content_active_time }}ms;
			text-align: center;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box .text-box {
			transform: translateY(200px);
			transition: all ease-in-out 300ms;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box:hover .text-box {
			transform: translateY(-0px);
		}
		{{ addonId }} .service-box .list-box .item-box .content-box .content h3 {
			font-size: {{ section_content_title_fontsize.md }}px;
			font-weight: inherit;
			margin-bottom: {{ section_content_title_mgB.md }}px;
			transition: all ease-in-out {{ section_content_active_time }}ms;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box .content p {
			font-size: {{ section_content_sTitle_fontsize.md }}px;
			font-weight: 400;
			margin-bottom: {{ section_content_sTitle_mgB.md }}px;
			transition: all ease-in-out {{ section_content_active_time }}ms;
		}
		{{ addonId }} .service-box .list-box .item-box .content-box .content .icon {
			width: {{ section_content_textIcon_w.md }}px;
			height: {{ section_content_textIcon_w.md }}px;
			background: url({{ service_content_textIcon }}) no-repeat center;
			background-size: contain;
			transition: all ease-in-out {{ section_content_active_time }}ms;
			display: inline-block;
		}
		{{ addonId }} .service-box .list-box .small {
			height: 49%;
			margin-bottom: 1.6%;
		}
		@media (min-width: 768px) and (max-width: 991px) {
            {{ addonId }} .service-box .top-box {
                display: block;
            }
            {{ addonId }} .service-box .title-box { 
                margin-left: {{ section_column_mgL.sm }}px;
                width: 100%;
            }
            {{ addonId }} .service-box .title-box .title {
				font-size: {{ section_name_fontsize.sm }}px;
			}
			{{ addonId }} .service-box .title-box .tips {
				font-size: {{ section_tips_fontsize.sm }}px;
				line-height: {{ section_tips_H.sm }}px;
			}
			{{ addonId }} .service-box .tab-box {
				width: 100%;
				margin-top: 20px;
			}
			{{ addonId }} .service-box .tab-box .item {
				font-size: {{ section_tab_fontsize.sm }}px;
				height: {{ section_tab_H.sm }}px;
				line-height: {{ section_tab_H.sm }}px;
			}
			{{ addonId }} .service-box .tab-box .item.active, {{ addonId }} .service-box .tab-box .item:hover {
				height: {{ section_tab_active_H.sm }}px;
				line-height: {{ section_tab_active_H.sm }}px;
				font-size: {{ section_tab_active_fontsize.sm }}px;
			}
			{{ addonId }} .service-box .list-box {
				display: block;
			}
			{{ addonId }} .service-box .list-box .large {
				width: 100%;
				height: {{ section_content_large_h.sm }}px;
				margin-right: 0;
				margin-bottom: 15px;
			}
			{{ addonId }} .service-box .list-box .list-small-box {
				width: 100%;
				height: {{ section_content_large_h.sm }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content h3 {
				font-size: {{ section_content_title_fontsize.sm }}px;
				margin-bottom: {{ section_content_title_mgB.sm }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content p {
				font-size: {{ section_content_sTitle_fontsize.sm }}px;
				margin-bottom: {{ section_content_sTitle_mgB.sm }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content .icon {
				width: {{ section_content_textIcon_w.sm }}px;
				height: {{ section_content_textIcon_w.sm }}px;
			}
        }
		@media (max-width:768px) {
            {{ addonId }} .service-box .top-box {
                display: block;
            }
            {{ addonId }} .service-box .title-box { 
                margin-left: {{ section_column_mgL.xs }}px;
                width: 100%;
            }
            {{ addonId }} .service-box .title-box .title {
				font-size: {{ section_name_fontsize.xs }}px;
			}
			{{ addonId }} .service-box .title-box .tips {
				font-size: {{ section_tips_fontsize.xs }}px;
				line-height: {{ section_tips_H.xs }}px;
			}
			{{ addonId }} .service-box .tab-box {
				width: 100%;
				margin-top: 20px;
			}
			{{ addonId }} .service-box .tab-box .item {
				font-size: {{ section_tab_fontsize.xs }}px;
				height: {{ section_tab_H.xs }}px;
				line-height: {{ section_tab_H.xs }}px;
			}
			{{ addonId }} .service-box .tab-box .item.active, {{ addonId }} .service-box .tab-box .item:hover {
				height: {{ section_tab_active_H.xs }}px;
				line-height: {{ section_tab_active_H.xs }}px;
				font-size: {{ section_tab_active_fontsize.xs }}px;
			}
			{{ addonId }} .service-box .list-box {
				display: block;
			}
			{{ addonId }} .service-box .list-box .large {
				width: 100%;
				height: {{ section_content_large_h.xs }}px;
				margin-right: 0;
				margin-bottom: 15px;
			}
			{{ addonId }} .service-box .list-box .list-small-box {
				width: 100%;
				height: {{ section_content_large_h.xs }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content h3 {
				font-size: {{ section_content_title_fontsize.xs }}px;
				margin-bottom: {{ section_content_title_mgB.xs }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content p {
				font-size: {{ section_content_sTitle_fontsize.xs }}px;
				margin-bottom: {{ section_content_sTitle_mgB.xs }}px;
			}
			{{ addonId }} .service-box .list-box .item-box .content-box .content .icon {
				width: {{ section_content_textIcon_w.xs }}px;
				height: {{ section_content_textIcon_w.xs }}px;
			}
        }
       </style>

       <div class="service-box">
			<div class="top-box flex flex-between">
				<div class="title-box">
					<p class="title">{{ section_name }}</p>
					<p class="tips">{{ section_tips }}</p>
					<i class="line"></i>
				</div>
				<div class="tab-box flex">
					<div class="item active">公寓酒店</div>
					<div class="item">连锁餐饮</div>
					<div class="item">全屋定制</div>
					<div class="item">别墅家装</div>
				</div>
			</div>
			<div class="list-box flex">
				<div class="large item-box">
					<div class="img-box">
						<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
					</div>
					<div class="content-box">
						<div class="content flex">
							<div class="text-box">
								<h3>简·轻奢</h3>
								<p>酒店酒吧个性设计</p>
								<i class="icon"></i>
							</div>
						</div>
					</div>
				</div>
				<div class="list-small-box flex">
					<div class="small item-box">
						<div class="img-box">
							<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
						</div>
						<div class="content-box">
							<div class="content flex">
								<div class="text-box">
									<h3>简·轻奢</h3>
									<p>酒店酒吧个性设计</p>
									<i class="icon"></i>
								</div>
							</div>
						</div>
					</div>
					<div class="small item-box">
						<div class="img-box">
							<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
						</div>
						<div class="content-box">
							<div class="content flex">
								<div class="text-box">
									<h3>简·轻奢</h3>
									<p>酒店酒吧个性设计</p>
									<i class="icon"></i>
								</div>
							</div>
						</div>
					</div>
					<div class="small item-box">
						<div class="img-box">
							<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
						</div>
						<div class="content-box">
							<div class="content flex">
								<div class="text-box">
									<h3>简·轻奢</h3>
									<p>酒店酒吧个性设计</p>
									<i class="icon"></i>
								</div>
							</div>
						</div>
					</div>
					<div class="small item-box"> 
						<div class="img-box">
							<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
						</div>
						<div class="content-box">
							<div class="content flex">
								<div class="text-box">
									<h3>简·轻奢</h3>
									<p>酒店酒吧个性设计</p>
									<i class="icon"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	
        ';

		return $output;
	}

//数组多层级 递归
	public
	function subTree($data, $pid = 1, $deep = 0)
	{   //用来存放数据
		$arr = [];
		//遍历数据库中的字段
		foreach ($data as $val)
		{
			//判断字段pid相等时放行
			if ($pid == $val['parent_id'])
			{
				//不同的层级
				$val['deep'] = $deep;
				//递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
				$val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
				//如果遇到pid==本条id时，将其存入数组
				$arr[] = $val;
			}
		}

		//返回数组
		return $arr;
	}
}
