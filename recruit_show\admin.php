<?php
/**
* <AUTHOR>
* @email        <EMAIL>
* @url          http://www.joomla.work
* @copyright    Copyright (c) 2010 - 2019 JoomWorker
* @license      GNU General Public License version 2 or later
* @date         2019/01/01 09:30
*/
//no direct accees
defined('_JEXEC') or die ('Restricted access');

$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
 	array(
 		'type' => 'content',
 		'addon_name' => 'recruit_show',
 		'title' => '招聘详情',
 		'desc' => '',
 		'category' => '招聘',
 		'attr' => array(
 			'general' => array(
 				'admin_label' => array(
 					'type' => 'text',
 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
 					'std' => ''
 				),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'recruit_layout' => array(
                    'type' => 'select',
                    'title' => '选择详情布局',
                    'values' => array(
                        'type01' => '布局01',
                    ),
                    'std' => 'type01'
                ),

                'recruit_item' => array(
                    'type' => 'buttons',
                    'title' => '显示项目配置',
                    'std' => 'content',
                    'values' => array(
                        array(
                            'label' => '详情内容',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '表单内容',
                            'value' => 'form'
                        ),
                        array(
                            'label' => '【应聘】按钮',
                            'value' => 'btn'
                        ),
                    ),
                    'tabs' => true,
                ),
                // 详情内容部分
                'show_content' => array(
                    'type' => 'checkbox',
                    'title' => '显示详情内容',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'content'),
                    ),
                ),
                'c_title_align' => array(
                    'type' => 'select',
                    'title' => '标题文字位置',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右'
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('recruit_item', '=', 'content'),
                        array('show_content', '=', 1),
                    ),
                ),
                'c_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'std' => 20,
                    'depends' => array(
                        array('recruit_item', '=', 'content'),
                        array('show_content', '=', 1),
                    ),
                ),
                'c_title_color' => array(
                    'type' => 'color',
                    'title' => '标题头文字颜色',
                    'std' => 'rgba(0, 0, 0, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'content'),
                        array('show_content', '=', 1),
                    ),
                ),
                'c_title_fontWeight' => array(
                    'type' => 'checkbox',
                    'title' => '标题文字加粗',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'content'),
                        array('show_content', '=', 1),
                    ),
                ),
                // 表单配置项
                'form_text01' => array(
                    'type' => 'separator',
                    'title' => '表单总体配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                    ),
                ),
                'show_form' => array(
                    'type' => 'checkbox',
                    'title' => '显示表单（简历需搭配招聘列表插件使用！）',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                    ),
                ),
                'form_item_right' => array(
                    'type' => 'slider',
                    'title' => '表单项间距',
                    'std' => 20,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_bottom' => array(
                    'type' => 'slider',
                    'title' => '表单项底部距离',
                    'std' => 20,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_label_width' => array(
                    'type' => 'slider',
                    'title' => '表单标签宽度',
                    'std' => 84,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_label_fontsize' => array(
                    'type' => 'slider',
                    'title' => '表单标签文字大小',
                    'std' => 14,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_label_color' => array(
                    'type' => 'color',
                    'title' => '表单标签文字颜色',
                    'std' => 'rgba(0, 0, 0, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_input_borderColor' => array(
                    'type' => 'color',
                    'title' => '表单输入框边框颜色',
                    'std' => 'rgba(0, 0, 0, 0.15)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_input_radius' => array(
                    'type' => 'slider',
                    'title' => '表单输入框边框圆角',
                    'std' => 10,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_input_bgColor' => array(
                    'type' => 'color',
                    'title' => '表单输入框背景颜色',
                    'std' => 'rgba(0, 0, 0, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_input_color' => array(
                    'type' => 'color',
                    'title' => '表单输入框文字颜色',
                    'std' => '#495057',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_area_height' => array(
                    'type' => 'slider',
                    'title' => '表单富文本高度',
                    'std' => 200,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_text02' => array(
                    'type' => 'separator',
                    'title' => '表单底部按钮配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_align' => array(
                    'type' => 'select',
                    'title' => '按钮位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右'
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_width' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'std' => 80,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_height' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'std' => 30,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_radius' => array(
                    'type' => 'slider',
                    'title' => '按钮边框圆角',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_borderWidth' => array(
                    'type' => 'slider',
                    'title' => '按钮边框宽度',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_borderColor' => array(
                    'type' => 'color',
                    'title' => '按钮边框颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_fontsize' => array(
                    'type' => 'slider',
                    'title' => '按钮文字大小',
                    'std' => 14,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_color' => array(
                    'type' => 'color',
                    'title' => '按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_bgColor' => array(
                    'type' => 'color',
                    'title' => '按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_color_hover' => array(
                    'type' => 'color',
                    'title' => '滑过按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_btn_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '滑过按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_borderColor' => array(
                    'type' => 'color',
                    'title' => '【重置】按钮边框颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_color' => array(
                    'type' => 'color',
                    'title' => '【重置】按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_bgColor' => array(
                    'type' => 'color',
                    'title' => '【重置】按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_borderColor_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【重置】按钮边框颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_color_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【重置】按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_reset_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【重置】按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_text04' => array(
                    'type' => 'separator',
                    'title' => '【身份证号】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_04' => array(
                    'type' => 'checkbox',
                    'title' => '显示【身份证号】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_04_has' => array(
                    'type' => 'checkbox',
                    'title' => '【身份证号】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_04', '=', 1),
                    ),
                ),
                'form_text05' => array(
                    'type' => 'separator',
                    'title' => '【婚否】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_05' => array(
                    'type' => 'checkbox',
                    'title' => '显示【婚否】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_05_has' => array(
                    'type' => 'checkbox',
                    'title' => '【婚否】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_05', '=', 1),
                    ),
                ),
                'form_text06' => array(
                    'type' => 'separator',
                    'title' => '【职称/资质】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_06' => array(
                    'type' => 'checkbox',
                    'title' => '显示【职称/资质】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_06_has' => array(
                    'type' => 'checkbox',
                    'title' => '【职称/资质】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_06', '=', 1),
                    ),
                ),
                'form_text07' => array(
                    'type' => 'separator',
                    'title' => '【毕业院校】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_07' => array(
                    'type' => 'checkbox',
                    'title' => '显示【毕业院校】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_07_has' => array(
                    'type' => 'checkbox',
                    'title' => '【毕业院校】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_07', '=', 1),
                    ),
                ),
                'form_text08' => array(
                    'type' => 'separator',
                    'title' => '【专业】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_08' => array(
                    'type' => 'checkbox',
                    'title' => '显示【专业】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_08_has' => array(
                    'type' => 'checkbox',
                    'title' => '【专业】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_08', '=', 1),
                    ),
                ),
                'form_text10' => array(
                    'type' => 'separator',
                    'title' => '【联系地址】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_10' => array(
                    'type' => 'checkbox',
                    'title' => '显示【联系地址】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_10_has' => array(
                    'type' => 'checkbox',
                    'title' => '【联系地址】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_10', '=', 1),
                    ),
                ),
                'form_text11' => array(
                    'type' => 'separator',
                    'title' => '【籍贯】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_11' => array(
                    'type' => 'checkbox',
                    'title' => '显示【籍贯】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_11_has' => array(
                    'type' => 'checkbox',
                    'title' => '【籍贯】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_11', '=', 1),
                    ),
                ),
                'form_text14' => array(
                    'type' => 'separator',
                    'title' => '【期望薪金】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_14' => array(
                    'type' => 'checkbox',
                    'title' => '显示【期望薪金】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_14_has' => array(
                    'type' => 'checkbox',
                    'title' => '【期望薪金】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_14', '=', 1),
                    ),
                ),
                'form_text15' => array(
                    'type' => 'separator',
                    'title' => '【学习经历】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_15' => array(
                    'type' => 'checkbox',
                    'title' => '显示【学习经历】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_15_has' => array(
                    'type' => 'checkbox',
                    'title' => '【学习经历】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_15', '=', 1),
                    ),
                ),
                'form_text16' => array(
                    'type' => 'separator',
                    'title' => '【工作经历】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_16' => array(
                    'type' => 'checkbox',
                    'title' => '显示【工作经历】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_16_has' => array(
                    'type' => 'checkbox',
                    'title' => '【工作经历】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_16', '=', 1),
                    ),
                ),
                'form_text17' => array(
                    'type' => 'separator',
                    'title' => '【对公司要求】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_17' => array(
                    'type' => 'checkbox',
                    'title' => '显示【对公司要求】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_17_has' => array(
                    'type' => 'checkbox',
                    'title' => '【对公司要求】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_17', '=', 1),
                    ),
                ),
                'form_text18' => array(
                    'type' => 'separator',
                    'title' => '【特长爱好】项配置',
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_18' => array(
                    'type' => 'checkbox',
                    'title' => '显示【特长爱好】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_18_has' => array(
                    'type' => 'checkbox',
                    'title' => '【特长爱好】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_18', '=', 1),
                    ),
                ),
                'form_item_19' => array(
                    'type' => 'checkbox',
                    'title' => '显示【简历上传】项',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                    ),
                ),
                'form_item_19_has' => array(
                    'type' => 'checkbox',
                    'title' => '【简历上传】项必填',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'form'),
                        array('show_form', '=', 1),
                        array('form_item_19', '=', 1),
                    ),
                ),
                // 底部招聘按钮
                'show_recruit_btn' => array(
                    'type' => 'checkbox',
                    'title' => '显示【应聘】按钮',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                    ),
                ),
                'action_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '【应聘】跳转详情页模板',
                    'desc' => '',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),
                'action_target' => array(
                    'type' => 'select',
                    'title' => '【应聘】跳转方式',
                    'desc' => '',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                    'std' => '_self',
                    'values' => array(
                        '_blank' => '空页面',
                        '_self' => '当前页'
                    ),
                ),
                'recruit_btn_align' => array(
                    'type' => 'select',
                    'title' => '【应聘】按钮位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右'
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_text' => array(
                    'type' => 'text',
                    'title' => '【应聘】按钮文字',
                    'std' => '应聘',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_width' => array(
                    'type' => 'slider',
                    'title' => '【应聘】按钮宽度',
                    'std' => 80,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_height' => array(
                    'type' => 'slider',
                    'title' => '【应聘】按钮高度',
                    'std' => 30,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_radius' => array(
                    'type' => 'slider',
                    'title' => '【应聘】按钮边框圆角',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_borderWidth' => array(
                    'type' => 'slider',
                    'title' => '【应聘】按钮边框宽度',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_borderColor' => array(
                    'type' => 'color',
                    'title' => '【应聘】按钮边框颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_fontsize' => array(
                    'type' => 'slider',
                    'title' => '【应聘】按钮文字大小',
                    'std' => 14,
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_color' => array(
                    'type' => 'color',
                    'title' => '【应聘】按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_bgColor' => array(
                    'type' => 'color',
                    'title' => '【应聘】按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_color_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【应聘】按钮文字颜色',
                    'std' => 'rgba(255, 255, 255, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【应聘】按钮背景颜色',
                    'std' => '#0E893B',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),
                'recruit_btn_borderColor_hover' => array(
                    'type' => 'color',
                    'title' => '滑过【应聘】按钮边框颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'btn'),
                        array('show_recruit_btn', '=', 1),
                    ),
                ),

 			),
 		),
 	)
);
