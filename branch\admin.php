<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-14 13:51:34
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-06-28 16:16:20
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\branch\admin.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'branch',
        'title' => JText::_('分公司列表'),
        'desc' => JText::_('分公司列表'),
        'category' => '龙采官网插件',
        'attr' => array(
            'style' => array(
                'type' => 'select',
                'title' => '选择样式',
                'values' => array(
                    'style1' => '样式1（请添加背景色）',
                    'style2' => '样式2（地图形式）',
                ),
                'std' => 'style1'
            ),
            'style1_settings' => array(
                'type' => 'buttons',
                'title' => '设置项',
                'tabs' => true,
                'std' => 'content',
                'values' => array(
                    array(
                        'label' => '内容',
                        'value' => 'content'
                    ),
                    array(
                        'label' => '样式',
                        'value' => 'style'
                    )
                ),
                'depends' => array(
                    array('style','=','style1'),
                )
            ),
            'style1_style_settings' => array(
                'type' => 'buttons',
                'title' => '样式一样式设置项',
                'std' => 'placeholder',
                'tabs' => true,
                'values' => array(
                    array(
                        'label' => '占位',
                        'value' => 'placeholder'
                    ),
                    array(
                        'label' => '列表项',
                        'value' => 'list'
                    )
                ),
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                )
            ),
            'placeholder' =>  array(
                'type' => 'text',
                'title' => '占位内容',
                'std' => '分公司',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'placeholder_color' =>  array(
                'type' => 'color',
                'title' => '占位内容颜色',
                'std' => '#fff',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'placeholder_color_hover' =>  array(
                'type' => 'color',
                'title' => '鼠标移入占位内容颜色',
                'std' => '#333',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'placeholder_font_size' => array(
                'title' => '占位内容字号',
                'type' => 'slider',
                'std' => 16,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'placeholder_line_height' => array(
                'title' => '占位内容行高',
                'type' => 'slider',
                'std' => 100,
                'max' => 300,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'placeholder_icon' =>  array(
                'type' => 'media',
                'title' => '占位图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20220602/901e0a593113c81639812c9e8de7d0c4.png',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'placeholder_active_icon' =>  array(
                'type' => 'media',
                'title' => '鼠标移入占位图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20220602/b98d4fac54bf7101f618597b85547e6d.png',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'placeholder_icon_size' =>  array(
                'type' => 'slider',
                'title' => '占位图标大小',
                'std' => 16,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'contact_img' =>  array(
                'type' => 'media',
                'title' => '联系我们图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20220602/41671e6ee31a849ccc3c591bf887a9af.png',
                'depends' => array(
                    array('style','=','style1'),
                )
            ),
            'placeholder_margin' =>  array(
                'type' => 'margin',
                'title' => '占位图标外边距',
                'std' => '-2px 6px 0 0',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','placeholder'),
                )
            ),
            'title' =>  array(
                'type' => 'text',
                'title' => '标题',
                'std' => '龙采分公司',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'intro' =>  array(
                'type' => 'textarea',
                'title' => '简介',
                'std' => '连续六年蝉联中国互联网百强，集团下设73家分公司，遍布全国16个省46个城市',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'link_text' =>  array(
                'type' => 'text',
                'title' => '联系我们标题',
                'std' => '联系我们',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'link_page_select' => array(
                'type'    => 'select',
                'title'   => JText::_('跳转页面选择'),
                'std'     => 'inner',
                'values' => array(
                    'inner'=>'内页',
                    'outer'=>'外页',
                ),
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'detail_page_id' => array(
                'type'    => 'select',
                'title'   => JText::_('跳转内页'),
                'std'     => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('style','=','style1'),
                    array('link_page_select', '=', 'inner'),
                    array('style1_settings','=','content'),
                )
            ),
            'detail_page_url' => array(
                'type'   => 'text',
                'title'  => '跳转页面',
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                    array('link_page_select', '=', 'outer'),
                    array('style1_settings','=','content'),
                )
            ),
            'target' => array(
                'type'   => 'select',
                'title'  => '打开方式',
                'values' => array(
                    '_self' => '默认',
                    '_blank' => '新窗口',
                ),
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'jw_tab_item_style1' => array(
                'title' => JText::_('列表项'),
                'attr' => array(
                    'title' => array(
                        'type' => 'text',
                        'title' => JText::_('标题'),
                        'desc' => JText::_('龙采科技集团有限责任公司（黑龙江总部）'),
                        'std' => '点我'
                    ),
                    // 2021.8.24关联分类
                    'link' => array(
                        'type' => 'text',
                        'title' => 'http://www.longcai.com',
                        'std' => 0,
                    ),
                ),
                'std' =>array(
                    array(
                        'title'=>'龙采科技集团有限责任公司（黑龙江总部）',
                        'link' => 'http://www.longcai.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（齐齐哈尔）',
                        'link' => 'http://www.longcai0452.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（牡丹江）',
                        'link' => 'http://www.longcai0453.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（佳木斯）',
                        'link' => 'http://www.longcai0454.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（绥化）',
                        'link' => 'http://www.longcai0455.com/'
                    ),
                    // array(
                    //     'title'=>'龙采科技集团有限责任公司（鸡西）',
                    //     'link' => 'http://www.longcai0467.com/'
                    // ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（大庆）',
                        'link' => 'http://www.longcai0459.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（大连）',
                        'link' => 'http://www.longcai0411.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（营口）',
                        'link' => 'http://www.longcai0417.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（盘锦）',
                        'link' => 'http://www.longcai0427.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（鞍山）',
                        'link' => 'http://www.longcai0412.com/'
                    ),
                    // array(
                    //     'title'=>'龙采科技集团有限责任公司（辽阳）',
                    //     'link' => 'http://www.longcai0419.com/'
                    // ),
                    // array(
                    //     'title'=>'龙采科技集团有限责任公司（阜新）',
                    //     'link' => 'http://www.longcai0418.com/'
                    // ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（山西）',
                        'link' => 'http://www.longcai0351.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（运城）',
                        'link' => 'http://www.longcai0359.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（晋中）',
                        'link' => 'http://www.longcai0354.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（阳泉）',
                        'link' => 'http://www.longcai0353.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（大同）',
                        'link' => 'http://www.longcai0352.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（忻州）',
                        'link' => 'http://www.longcai0350.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（长治）',
                        'link' => 'http://www.longcai0355.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（临汾）',
                        'link' => 'http://www.longcai0357.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（晋城）',
                        'link' => 'http://www.longcai0356.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（朔州）',
                        'link' => 'http://www.longcai0349.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（吕梁）',
                        'link' => 'http://www.longcai0358.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（沈阳）',
                        'link' => 'http://www.longcai024.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（长春）',
                        'link' => 'http://www.cclongcai.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（石家庄）',
                        'link' => 'http://www.longcai0311.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（青岛）',
                        'link' => 'http://www.qdlongcai.cn/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（山东）',
                        'link' => 'http://www.longcai0531.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（武汉）',
                        'link' => 'http://www.longcai027.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（南昌）',
                        'link' => 'http://www.longcai0791.com'
                    ),
                    // array(
                    //     'title'=>'龙采科技集团有限责任公司（上海）',
                    //     'link' => 'http://www.longcai021.cn'
                    // ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（南京）',
                        'link' => 'http://www.longcai025.com'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（深圳）',
                        'link' => 'http://www.longcai0755.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（泉州）',
                        'link' => 'http://www.longcai0595.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（厦门）',
                        'link' => 'http://www.longcai0591.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（福州）',
                        'link' => 'http://longcai0591.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（天津）',
                        'link' => 'http://www.longcai022.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（重庆）',
                        'link' => 'http://www.longcai023.com/'
                    ),
                    array(
                        'title'=>'龙采科技集团有限责任公司（陕西）',
                        'link' => 'http://www.longcai029.com/'
                    ),
                ),
                'depends'=> array(
                    array('style', '=', 'style1'),
                    array('style1_settings','=','content'),
                )
            ),
            'jw_tab_items_left_style1' => array(
                'type'   => 'slider',
                'title'  => '列表项左边距(%)',
                'std' => -21,
                'min' => -100,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                )
            ),
            'list_background_settings' =>  array(
                'type' => 'buttons',
                'title' => '列表背景设置',
                'std' => 'color',
                'values' => array(
                    array(
                        'label' => '纯色',
                        'value' => 'color'
                    ),
                    array(
                        'label' => '背景图',
                        'value' => 'img'
                    )
                ),
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                )
            ),
            'list_color' => array(
                'type' => 'color',
                'title' => '背景色',
                'std' => '#fff',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_background_settings','=','color'),
                )
            ),
            'nav_section_id_settings' => array(
                'type'   => 'separator',
                'title'  => '导航切换配置',
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                )
            ),
            'list_bg_img' => array(
                'type' => 'media',
                'title' => '背景图',
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_background_settings','=','img'),
                )
            ),
            'list_bg_img_repeat' => array(
                'type' => 'checkbox',
                'title' => '背景图是否重复',
                'std' => 0,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_background_settings','=','img'),
                )
            ),
            'list_bg_img_position' => array(
                'type' => 'text',
                'title' => '背景图位置(可以写%)',
                'std' => '0 0',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_background_settings','=','img'),
                )
            ),
            'list_width' => array(
                'type' => 'slider',
                'title' => '列表盒子宽度',
                'std' => 1560,
                'max' => 2000,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                )
            ),
            'list_height' => array(
                'type' => 'slider',
                'title' => '列表盒子高度',
                'std' => 500,
                'max' => 1000,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                )
            ),
            'list_inner_height' => array(
                'type' => 'slider',
                'title' => '列表高度（最大高度为列表盒子高度）',
                'std' => 440,
                'max' => 1000,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                )
            ),
            'list_inner_settings' => array(
                'type' => 'buttons',
                'title' => '列表设置项',
                'tabs' => true,
                'std' => 'left',
                'values' => array(
                    array(
                        'label' => '左',
                        'value' => 'left'
                    ),
                    array(
                        'label' => '右',
                        'value' => 'right'
                    ),
                ),
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                )
            ),
            'list_inner_left_color' => array(
                'type' => 'color',
                'title' => '列表左侧边框色',
                'std' => '#eee',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_padding' => array(
                'type' => 'padding',
                'title' => '列表左侧内边距',
                'std' => '32px 24px 32px 0',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_left_icon_width' => array(
                'type' => 'slider',
                'title' => '列表左侧图片宽度',
                'std' => 110,
                'max' => 1000,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_left_icon_height' => array(
                'type' => 'slider',
                'title' => '列表左侧图片高度',
                'std' => 90,
                'max' => 1000,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_title_size' => array(
                'type' => 'slider',
                'title' => '列表左侧标题字号',
                'std' => 24,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_title_line_height' => array(
                'type' => 'slider',
                'title' => '列表左侧标题行高',
                'std' => 30,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_title_margin_top' => array(
                'type' => 'slider',
                'title' => '列表左侧标题上边距',
                'std' => 24,
                'max' => 200,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_title_color' => array(
                'type' => 'color',
                'title' => '列表左侧标题字体颜色',
                'std' => '#252b3a',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_intro_size' => array(
                'type' => 'slider',
                'title' => '列表左侧简介字号',
                'std' => 16,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_intro_line_height' => array(
                'type' => 'slider',
                'title' => '列表左侧简介行高',
                'std' => 32,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_intro_margin_top' => array(
                'type' => 'slider',
                'title' => '列表左侧简介上边距',
                'std' => 12,
                'max' => 200,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_intro_color' => array(
                'type' => 'color',
                'title' => '列表左侧标题字体颜色',
                'std' => '#575d6c',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_contact_size' => array(
                'type' => 'slider',
                'title' => '列表左侧链接字号',
                'std' => 16,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_contact_line_height' => array(
                'type' => 'slider',
                'title' => '列表左侧链接行高',
                'std' => 18,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_contact_margin_top' => array(
                'type' => 'slider',
                'title' => '列表左侧链接上边距',
                'std' => 30,
                'max' => 200,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_contact_color' => array(
                'type' => 'color',
                'title' => '列表左侧标题链接颜色',
                'std' => '#252b3a',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_arrow_size' => array(
                'type' => 'slider',
                'title' => '列表左侧链接箭头大小',
                'std' => 6,
                'max' => 20,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_arrow_width' => array(
                'type' => 'slider',
                'title' => '列表左侧链接箭头粗细',
                'std' => 1,
                'max' => 10,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_inner_arrow_left' => array(
                'type' => 'slider',
                'title' => '列表左侧链接箭头左边距',
                'std' => 8,
                'max' => 20,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','left'),
                )
            ),
            'list_box_padding_right' => array(
                'type' => 'padding',
                'title' => '列表右侧内边距',
                'std' => '0 0 0 12px',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_mask_height_right' => array(
                'type' => 'slider',
                'title' => '列表右侧遮罩高度（%）',
                'std' => '45',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_mask_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧遮罩底部颜色',
                'std' => '#fff',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_mask_color2_right' => array(
                'type' => 'color',
                'title' => '列表右侧遮罩顶部颜色',
                'std' => 'rgba(255,255,255,0)',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_list_padding_bottom_right' => array(
                'type' => 'slider',
                'title' => '列表右侧列表底边距',
                'std' => '32',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_list_top_right' => array(
                'type' => 'slider',
                'title' => '列表右侧列表上边距',
                'std' => -4,
                'max' => 10,
                'min' => -10,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_margin_right' => array(
                'type' => 'margin',
                'title' => '列表右侧列表项外边距',
                'std' => '16px 16px 0 0',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_width_right' => array(
                'type' => 'slider',
                'title' => '列表右侧列表项列数',
                'std' => 3,
                'max' => 10,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_max_width_right' => array(
                'type' => 'slider',
                'title' => '列表右侧列表项最大宽度（%）',
                'std' => 31,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_padding_right' => array(
                'type' => 'padding',
                'title' => '列表右侧列表项内边距',
                'std' => '18px 18px 18px 18px',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_border_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项边框色',
                'std' => '#eee',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_border_hover_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项鼠标移入边框色',
                'std' => '#eee',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_title_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项标题字体颜色',
                'std' => '#252b3a',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_title_hover_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项标题鼠标移入字体颜色',
                'std' => '#c7000b',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_title_size' => array(
                'type' => 'slider',
                'title' => '列表右侧标题字号',
                'std' => 16,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_title_line_height_size' => array(
                'type' => 'slider',
                'title' => '列表右侧标题行高',
                'std' => 20,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_desc_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项描述/网址字体颜色',
                'std' => '#8a8e99',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_desc_hover_color_right' => array(
                'type' => 'color',
                'title' => '列表右侧列表项描述/网址鼠标移入字体颜色',
                'std' => '#8a8e99',
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_desc_size_right' => array(
                'type' => 'slider',
                'title' => '列表右侧描述/网址字号',
                'std' => 14,
                'max' => 50,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_desc_line_height_size_right' => array(
                'type' => 'slider',
                'title' => '列表右侧描述/网址行高',
                'std' => 22,
                'max' => 100,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'list_item_desc_top_size_right' => array(
                'type' => 'slider',
                'title' => '列表右侧描述/网址行高',
                'std' => 4,
                'max' => 20,
                'depends' => array(
                    array('style','=','style1'),
                    array('style1_settings','=','style'),
                    array('style1_style_settings','=','list'),
                    array('list_inner_settings','=','right'),
                )
            ),
            'nav_section_id_hide' => array(
                'type'   => 'text',
                'title'  => '鼠标移入时需要隐藏的导航区块id',
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                )
            ),
            'nav_section_id_show' => array(
                'type'   => 'text',
                'title'  => '鼠标移入时需要显示的导航区块id',
                'std' => '',
                'depends' => array(
                    array('style','=','style1'),
                )
            ),
            'background_img_type2_pc' =>  array(
                'type' => 'media',
                'title' => 'pc背景图片',
                'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/205277a59818c3a7178ef975f0bd91fa.jpeg',
                'depends' => array(
                    array('style','=','style2'),
                )
            ),
            'background_img_type2_sj' =>  array(
                'type' => 'media',
                'title' => '手机端背景图片',
                'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220518/54d72dc7232745dba6ca00c3964cba88.png',
                'depends' => array(
                    array('style','=','style2'),
                )
            ),
        ),
    )
);
