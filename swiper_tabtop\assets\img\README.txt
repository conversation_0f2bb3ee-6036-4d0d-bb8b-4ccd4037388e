Welcome to the downloads for pywin32.
    
To download pywin32, please select the "View All Downloads" button, then 
select the installer executable for your system.  Note that there is one
download package for each supported version of Python - please check what
version of Python you have installed and download the corresponding
package.

Some packages have a 32bit and a 64bit version available - you must download
the one which corresponds to the Python you have installed.  Even if you have
a 64bit computer, if you installed a 32bit version of Python you must install
the 32bit version of pywin32.

If the installation process informs you that Python is not found in the 
registry, it almost certainly means you have downloaded the wrong version -
either for the wrong version of Python, or the wrong "bittedness".

A changelog can be found at http://pywin32.cvs.sourceforge.net/viewvc/pywin32/pywin32/CHANGES.txt?view=markup
