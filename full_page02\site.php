<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonFull_page02 extends JwpagefactoryAddons
{
    public function render() {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $id = $this->addon->id;

        // 第一页是否展示
        $page01_hide = (isset($settings->page01_hide) && $settings->page01_hide) ? $settings->page01_hide : 0;
        // 第二页是否展示
        $page02_hide = (isset($settings->page02_hide) && $settings->page02_hide) ? $settings->page02_hide : 0;
        // 第三页是否展示
        $page03_hide = (isset($settings->page03_hide) && $settings->page03_hide) ? $settings->page03_hide : 0;
        // 第四页是否展示
        $page04_hide = (isset($settings->page04_hide) && $settings->page04_hide) ? $settings->page04_hide : 0;
        // 第五页是否展示
        $page05_hide = (isset($settings->page05_hide) && $settings->page05_hide) ? $settings->page05_hide : 0;
        // 第六页是否展示
        $page06_hide = (isset($settings->page06_hide) && $settings->page06_hide) ? $settings->page06_hide : 0;
        // 第七页是否展示
        $page07_hide = (isset($settings->page07_hide) && $settings->page07_hide) ? $settings->page07_hide : 0;

        /*
         * 第四页变量
         * */
        $page04_slider_title = (isset($settings->page04_slider_title) && $settings->page04_slider_title) ? $settings->page04_slider_title : '轴承传动行业的优质方案提供者';
        $page04_slider_desc = (isset($settings->page04_slider_desc) && $settings->page04_slider_desc) ? $settings->page04_slider_desc : '库存充足、品种齐全、专业销售世界优质品质轴承';
        $page04_slider_content = (isset($settings->page04_slider_content) && $settings->page04_slider_content) ? $settings->page04_slider_content : '<p>常备库存<span>7000万元</span>以上，为数十个行业客户提供<span>12000多种</span>不同规格的轴承</p><p style="font-size: 14px;margin-top: 10px;">The company\'s standing stock is more than 70 million yuan, providing more than 12,000 products of different specifications for dozens of industry customers.</p>';
        $page04_index_page_id = $settings->page04_index_page_id;
        $page04_index_link = '';
        if($page04_index_page_id){
            $id = base64_encode($page04_index_page_id);
            $page04_index_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        /*
         * 第五页变量
         * */
        $page05_ani_title01 = (isset($settings->page05_ani_title01) && $settings->page05_ani_title01) ? $settings->page05_ani_title01 : '货源稳定';
        $page05_ani_title02 = (isset($settings->page05_ani_title02) && $settings->page05_ani_title02) ? $settings->page05_ani_title02 : '库存充足';
        $page05_ani_title03 = (isset($settings->page05_ani_title03) && $settings->page05_ani_title03) ? $settings->page05_ani_title03 : '物流快捷';
        $page05_title_item = (isset($settings->page05_title_item) && $settings->page05_title_item) ? $settings->page05_title_item : array(
            (object)array(
                'title' => '瓦轴实力授权经销商',
            ),
            (object)array(
                'title' => '常备7000万元以上库存',
            ),
            (object)array(
                'title' => '20年服务品质 实力保证',
            ),
            (object)array(
                'title' => '提供一站式轴承技术支持',
            ),
        );
        $page05_desc_img = (isset($settings->page05_desc_img) && $settings->page05_desc_img) ? $settings->page05_desc_img : 'https://oss.lcweb01.cn/joomla/20211014/37d95cf9379f31e27d5893f97e913850.png';
        $page05_goods_img = (isset($settings->page05_goods_img) && $settings->page05_goods_img) ? $settings->page05_goods_img : 'https://oss.lcweb01.cn/joomla/20211014/0d47fca7f1e2f474bcb1c0c96c15842e.png';

        /*
         * 第六页变量
         * */
        // 地图链接
        $page06_map_page_id = $settings->page06_map_page_id;
        $page06_map_link = '';
        if($page06_map_page_id){
            $id = base64_encode($page06_map_page_id);
            $page06_map_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        // 留言链接
        $page06_message_page_id = $settings->page06_message_page_id;
        $page06_message_link = '';
        if($page06_message_page_id){
            $id = base64_encode($page06_message_page_id);
            $page06_message_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        $page06_ewm_img = (isset($settings->page06_ewm_img) && $settings->page06_ewm_img) ? $settings->page06_ewm_img : 'https://oss.lcweb01.cn/joomla/20211015/c87bd2547328cd4aa812aa4e6c1940fb.jpg';
        $page06_concat = (isset($settings->page06_concat) && $settings->page06_concat) ? $settings->page06_concat : '<p>地址：江苏省无锡市北塘区康桥丽景家园18-46号</p><p>电话：15061858253<br />联系人：董经理</p><p>电话：19895733877<br />联系人：董经理</p>';
        $page06_copyright = (isset($settings->page06_copyright) && $settings->page06_copyright) ? $settings->page06_copyright : '江苏江瓦轴承有限公司无锡分公司';
        $page06_record = (isset($settings->page06_record) && $settings->page06_record) ? $settings->page06_record : '';
        // 备案号链接
        $page06_record_link = (isset($settings->page06_record_link) && $settings->page06_record_link) ? $settings->page06_record_link : '';
        $page06_bot_concat = (isset($settings->page06_bot_concat) && $settings->page06_bot_concat) ? $settings->page06_bot_concat : '<p>企业愿景：瓦轴第一总代理   永居销售量榜首</p><p>企业价值观：服务每一位客户，成就每一位员工</p><p>企业精神：诚信  创新  协作  共赢</p>';



        //文章列表数据读取
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        //$page01_items = JwpagefactoryHelperArticles::getArticlesList($page01_limit, $page01_ordering, $page01_catid, $page01_include_subcat, $page01_post_type, $page01_tagids, $page01_detail_page_id, 1, $company_id, $layout_id, $site_id);
        //$page06_items = JwpagefactoryHelperArticles::getArticlesList($page06_limit, $page06_ordering, $page06_catid, $page06_include_subcat, $page06_post_type, $page06_tagids, $page06_detail_page_id, 1, $company_id, $layout_id, $site_id);
        // print_r($page01_items);die;


        $output = '
            <div class="swiper-container full-page" id="full-page">
                <div class="swiper-wrapper">';
                    if($page01_hide == 0) {
                        /*
                         * 第一页变量
                         * */
                        $page01_image_item = (isset($settings->page01_image_item) && $settings->page01_image_item) ? $settings->page01_image_item : array(
                            (Object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/dd28120daea31ff715b41cde162e580d.jpg',
                            ),
                            (Object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/63adf86827f4bbe06594873e4e52e9f5.jpg',
                            ),
                            (Object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/8c617a2b61cb943f0fc94382060587e7.jpg',
                            ),
                            (Object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/a6d5c68adedd7a90490bd29b22640c64.jpg',
                            ),
                        );
                        $output .= '
                            <div class="swiper-slide">
                                <div class="page page-1">
                                    <div class="swiper-container page1-swiper" style="width: 100%;height: 100%;">
                                        <div class="swiper-wrapper">
                        ';
                        foreach($page01_image_item as $key => $item) {
                            $style = 'background-image: url(' . $item->slider_img . ');';
                            $output .= '<div class="swiper-slide" style="' . $style . '"></div>';
                        }
                        $output .= '</div>
                                </div>
                            </div>
                        </div>';
                    }
                    if($page02_hide == 0) {
                        /*
                         * 第二页变量
                         * */
                        $page02_right_title = (isset($settings->page02_right_title) && $settings->page02_right_title) ? $settings->page02_right_title : '专业的幼教';
                        $page02_left_click = $settings->page02_left_click ?? 0;
                        $page02_video_src = (isset($settings->page02_video_src) && $settings->page02_video_src) ? $settings->page02_video_src : '';
                        $page02_right_desc = (isset($settings->page02_right_desc) && $settings->page02_right_desc) ? $settings->page02_right_desc : '践行者';
                        $page02_right_content = (isset($settings->page02_right_content) && $settings->page02_right_content) ? $settings->page02_right_content : '<p style="text-indent:32px;">
                            沃德兰·东大教育集团是致力于幼儿教育、培训，幼儿师资培训，幼儿教育用品等多领域发展的综合性教育机构，目前在中国拥有多家直营幼儿园。</p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团历经二十余年的辛勤耕耘，不断整合积淀了丰富的教育资源，与华东师范大学、北京师范大学、陈鹤琴教育思想研究会、复旦大学出版社等众多知名院校、专家学者，开展教学教研实践活动，研发出全套《幼儿园体验课程活动方案指导手册》（教师参考用书）以及《五大领域体验活动手册》（幼儿操作用书）、《活教育中的山西文化丛书》等，是促进学、研、产、用相结合的专业幼教服务机构。
                        </p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团多次荣获“中国民办十大知名品牌教育机构”、“全国十佳特色示范单位”、“全国学前教育先进单位”、“全国民办教育先进集体”、“全国特色民办幼儿园”、“最具社会责任感教育机构”、“最具企业竞争力教育机构”、“课程创新新锐奖”、“幼教中国影响力（学习力）机构奖”等多项殊荣。
                        </p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团与恒大、富力、绿地、万科、保利等国内多家房地产公司合作，通过“教育带动地产，地产联动教育”进行强强联手，带动了当地的幼教事业，达到三赢的社会效果。
                        </p>';
                        $click = '';
                        $iframe_src = '';
                        if($page02_left_click == 0) {
                            $click .= ' onclick="showVideo()"';
                            if($page02_video_src) {
                                $iframe_src .= ' src="' . $page02_video_src . '"';
                            }
                        }
                        $output .= '
                            <div class="swiper-slide">
                                <div class="page page-2 swiper-no-swiping">
                                    <!-- 左边 -->
                                    <div class="page-item"' . $click . '></div>
                                    <!-- 视频弹窗 -->
                                    <div class="theme-popover" style="display:none;">
                                        <div class="theme-poptit">
                                            <a href="javascript:;" title="关闭" class="close" onclick="hideVideo()">×</a>
                                        </div>
                                        <iframe width="100%" height="100%" align="middle"' . $iframe_src . '
                                            allowfullscreen=""></iframe>
                                    </div>
                                    <div class="page-item right-content">
                                        <div class="two-right-box ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                            <span class="two-right-box-p">' . $page02_right_title . '</span><br>
                                            <span class="two-right-box-p2">' . $page02_right_desc . '</span><br>
                                            <div class="two-right-box-p3">
                                                ' . $page02_right_content . '
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ';
                    }
                    if($page03_hide == 0) {
                        $page03_slider_title = (isset($settings->page03_slider_title) && $settings->page03_slider_title) ? $settings->page03_slider_title : '沃德兰 · 九大优势';
                        $page03_slider_desc = (isset($settings->page03_slider_desc) && $settings->page03_slider_desc) ? $settings->page03_slider_desc : 'COMPETITIVE EDGE';
                        $page03_list_item = (isset($settings->page03_list_item) && $settings->page03_list_item) ? $settings->page03_list_item : array(
                            (object)array(
                                'title' => '直营式管理',
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                                'slider_color' => 'rgb(69,84,105)',
                                'slider_border_color' => 'rgb(199,203,210)',
                                'slider_border_color_hover' => 'rgb(146,154,169)',
                                'title_link' => '',
                            ),
                            (object)array(
                                'title' => '直营式管理',
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                                'slider_color' => 'rgb(69,84,105)',
                                'slider_border_color' => 'rgb(199,203,210)',
                                'slider_border_color_hover' => 'rgb(146,154,169)',
                                'title_link' => '',
                            ),
                            (object)array(
                                'title' => '直营式管理',
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                                'slider_color' => 'rgb(69,84,105)',
                                'slider_border_color' => 'rgb(199,203,210)',
                                'slider_border_color_hover' => 'rgb(146,154,169)',
                                'title_link' => '',
                            )
                        );
                        $output .= '
                        <div class="swiper-slide">
                            <div class="page page-3 swiper-no-swiping">
                                <div class="content ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                    <div class="two-main-bt">
                                        <span class="two-main-bt-p">' . $page03_slider_title . '</span><br>
                                        <span class="two-main-bt-p2">' . $page03_slider_desc . '</span>
                                    </div>
                                    <div class="in-jmhz-there-box">';
                                        foreach ($page03_list_item as $key => $item) {
                                            $page03_link = '';
                                            if($item->title_link) {
                                                $page03_link = 'href="' .$item->title_link . ' target="_blank""';
                                            }
                                            $output .= '<div class="in-jmhz-there-div">
                                                <div class="in-jmhz-there-bt">
                                                    <div class="in-jmhz-there-bt-icon">
                                                        <img src="' . $item->slider_img . '">
                                                    </div>
                                                    <a ' . $page03_link . '>' . $item->title . '</a>
                                                </div>
                                            </div>';
                                        }
                                    $output .= '</div>
                                </div>
                            </div>
                        </div>';
                    }
                    if($page04_hide == 0) {
                        $page04_slider_title = (isset($settings->page04_slider_title) && $settings->page04_slider_title) ? $settings->page04_slider_title : '沃德兰 · 团队展示';
                        $page04_slider_desc = (isset($settings->page04_slider_desc) && $settings->page04_slider_desc) ? $settings->page04_slider_desc : 'TEAM';
                        $page04_image_item = (isset($settings->page04_image_item) && $settings->page04_image_item) ? $settings->page04_image_item : array(
                            (object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/64479efd0311661308e9b0eecba54b45.png',
                                'slider_link' => '',
                            ),
                            (object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/3c61c47e5fe291bb8cbb47d57ae3447f.png',
                                'slider_link' => '',
                            ),
                            (object)array(
                                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/c265f1017a1cf78c6007b662ab1edab1.png',
                                'slider_link' => '',
                            ),
                        );
                        $page04_btn_item = (isset($settings->page04_btn_item) && $settings->page04_btn_item) ? $settings->page04_btn_item : array(
                            (object)array(
                                'title' => '运营团队',
                                'slider_link' => '',
                            ),
                            (object)array(
                                'title' => '教研团队',
                                'slider_link' => '',
                            ),
                            (object)array(
                                'title' => '后勤团队',
                                'slider_link' => '',
                            ),
                        );
                        $output .= '
                            <div class="swiper-slide">
                                <div class="page page-4 swiper-no-swiping">
                                    <div class="content ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                        <div class="right right5">
                                            <div class="five-bt">
                                                <span class="five-bt-p">' . $page04_slider_title . '</span><br>
                                                <span class="four-bt-p2">' . $page04_slider_desc . '</span>
                                            </div>
                                            <div class="five-gun">
                                                <div class="swiper-container" id="page-4-swiper">
                                                    <div class="swiper-wrapper">';
                                                        foreach ($page04_image_item as $key => $item) {
                                                            $page04_link = '';
                                                            if($item->slider_link) {
                                                                $page04_link = 'href="' .$item->slider_link . ' target="_blank""';
                                                            }
                                                            $output .= '
                                                            <div class="swiper-slide">
                                                                <a ' . $page04_link . '>
                                                                    <img src="' . $item->slider_img . '">
                                                                </a>
                                                            </div>';
                                                        }
                                                    $output .= '</div>
                                                    <div class="banner-btn">
                                                        <a href="javascript:;" class="prevBtn"><i></i></a>
                                                        <a href="javascript:;" class="nextBtn"><i></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="five-class">';
                                                foreach ($page04_btn_item as $key => $item) {
                                                    $page04_btn_link = '';
                                                    if($item->slider_link) {
                                                        $page04_btn_link = 'href="' .$item->slider_link . ' target="_blank""';
                                                    }
                                                    $output .= '
                                                        <a ' . $page04_btn_link . '><button class="five-class-btn">' . $item->title . '</button></a>
                                                    ';
                                                }
                                                $output .= '
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ';
                    }
                    if($page05_hide == 0) {
                        $page05_ani_title01 = (isset($settings->page05_ani_title01) && $settings->page05_ani_title01) ? $settings->page05_ani_title01 : '沃德兰 · 新闻活动';
                        $page05_ani_title02 = (isset($settings->page05_ani_title02) && $settings->page05_ani_title02) ? $settings->page05_ani_title02 : 'NEWS';

                        $page05_include_subcat = (isset($settings->page05_include_subcat)) ? $settings->page05_include_subcat : 1;
                        $page05_post_type = (isset($settings->page05_post_type) && $settings->page05_post_type) ? $settings->page05_post_type : '';
                        $page05_ordering = (isset($settings->page05_ordering) && $settings->page05_ordering) ? $settings->page05_ordering : 'latest';
                        $page05_limit = (isset($settings->page05_limit) && $settings->page05_limit) ? $settings->page05_limit : 3;
                        $page05_tagids = (isset($settings->page05_tagids) && $settings->page05_tagids) ? $settings->page05_tagids : array();
                        $page05_catid = (isset($settings->page05_catid) && $settings->page05_catid) ? $settings->page05_catid : 0;
                        $page05_detail_page_id = (isset($settings->page05_detail_page_id)) ? $settings->page05_detail_page_id : 0;
                        //文章列表数据读取
                        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
                        require_once $article_helper;
                        $page05_items = JwpagefactoryHelperArticles::getArticlesList($page05_limit, $page05_ordering, $page05_catid, $page05_include_subcat, $page05_post_type, $page05_tagids, $page05_detail_page_id, 1, $company_id, $layout_id, $site_id);

                        $output .= '
                            <div class="swiper-slide">
                                <div class="page page-5 swiper-no-swiping">
                                    <div class="content ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                        <div class="five-main">
                                            <div class="six-bt">
                                                <span class="six-bt-p">' . $page05_ani_title01 . '</span><br>
                                                <span class="five-bt-p2">' . $page05_ani_title02 . '</span>
                                            </div>
                                            <div class="five-box">';
                                                foreach ($page05_items as $key => $item) {
                                                    $page05_img = 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/articles_list/assets/images/moren.png';
                                                    if($item->image_thumbnail){
                                                        $page05_img = $item->image_thumbnail;
                                                    }
                                                    $output .= '
                                                    <div class="five-div">
                                                        <div class="five-div-text">
                                                            <span class="five-div-text-p">' . $item->title .'</span><br>
                                                            <span class="five-div-text-p2"></span><br>
                                                            <span class="five-div-text-p3"><a href="' . $item->link .'">more
                                                                    &gt;</a></span><br>
                                                        </div>
                                                        <img src="' . $page05_img .'">
                                                    </div>';
                                                }
                                            $output .= '</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ';
                    }
                    if($page06_hide == 0) {
                        $page06_slider_title = (isset($settings->page06_slider_title) && $settings->page06_slider_title) ? $settings->page06_slider_title : '沃德兰 · 联盟合作';
                        $page06_slider_desc = (isset($settings->page06_slider_desc) && $settings->page06_slider_desc) ? $settings->page06_slider_desc : 'JOIN IN';
                        $page06_slider_content = (isset($settings->page06_slider_content) && $settings->page06_slider_content) ? $settings->page06_slider_content : '学前教育牵动着全社会，沃德兰国际教育致力于：“适应儿童发展、遵循自然本性、实现原本教育、养成高尚品格”的教育理念，确保优秀的教育质量、优秀的教育服务。现特面向社会诚招教育合作伙伴，诚邀有实力、有经验、有成熟课程体系的机构联盟发展，共谋幼教事业发展。';
                        $page06_slider_tel = (isset($settings->page06_slider_tel) && $settings->page06_slider_tel) ? $settings->page06_slider_tel : '0351-3239017 0351-3238128';
                        $page06_slider_btn = (isset($settings->page06_slider_btn) && $settings->page06_slider_btn) ? $settings->page06_slider_btn : '联系我们';
                        $page06_slider_btn_link = (isset($settings->page06_slider_btn_link) && $settings->page06_slider_btn_link) ? $settings->page06_slider_btn_link : '';
                        $page06_slider_img_right = (isset($settings->page06_slider_img_right) && $settings->page06_slider_img_right) ? $settings->page06_slider_img_right : 'https://oss.lcweb01.cn/joomla/20211216/01d428b32f89031a618c19dad7e56ea8.png';

                        $page06_link = '';
                        if($page06_slider_btn_link) {
                            $page06_link .= 'href="' .$page06_slider_btn_link .'" target="_blank"';
                        }

                        $output .= '
                            <div class="swiper-slide">
                                <div class="page page-6">
                                    <div class="content swiper-no-swiping">
                                        <div class="middle">
                                            <div class="six-main-left ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                                <span class="six-main-left-p">' . $page06_slider_title .'</span><br>
                                                <span class="six-main-left-p2">' . $page06_slider_desc .'</span><br>
                                                <span class="six-main-left-p3">' . $page06_slider_content .'</span><br>
                                                <span class="six-main-left-p4">' . $page06_slider_tel .'</span><br>
                                                <button class="six-btn"><a ' . $page06_link . ' class="six-lian">' . $page06_slider_btn .'</a></button>
                                            </div>
                                            <div class="six-main-right ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                                <img src="' . $page06_slider_img_right .'">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ';
                    }
                    if($page07_hide == 0) {
                        $page07_left_title01 = (isset($settings->page07_left_title01) && $settings->page07_left_title01) ? $settings->page07_left_title01 : '联系我们，关注我们';
                        $page07_left_title02 = (isset($settings->page07_left_title02) && $settings->page07_left_title02) ? $settings->page07_left_title02 : 'Contact Us';
                        $page07_left_title03 = (isset($settings->page07_left_title03) && $settings->page07_left_title03) ? $settings->page07_left_title03 : '我们始终愿意俯下身聆听您的声音';
                        $page07_left_title04 = (isset($settings->page07_left_title04) && $settings->page07_left_title04) ? $settings->page07_left_title04 : '400-***-****';
                        $page07_left_title05 = (isset($settings->page07_left_title05) && $settings->page07_left_title05) ? $settings->page07_left_title05 : 'www.xxxxxxxx.com';
                        $page07_content = (isset($settings->page07_content) && $settings->page07_content) ? $settings->page07_content : '<a>沃德兰·东大教育集团</a> | <a>新闻活动</a> | <a>联盟合作</a> | <a>招贤纳士</a>';
                        $page07_record_g = (isset($settings->page07_record_g) && $settings->page07_record_g) ? $settings->page07_record_g : '晋公网安备 14010502050981号';
                        $page07_record_link_g = (isset($settings->page07_record_link_g) && $settings->page07_record_link_g) ? $settings->page07_record_link_g : '';
                        $page07_record = (isset($settings->page07_record) && $settings->page07_record) ? $settings->page07_record : '晋ICP备17001037号-1';
                        $page07_record_link = (isset($settings->page07_record_link) && $settings->page07_record_link) ? $settings->page07_record_link : '';
                        $page07_copyright = (isset($settings->page07_copyright) && $settings->page07_copyright) ? $settings->page07_copyright : '山西资海科技科技有限公司';
                        $page07_copyright_link = (isset($settings->page07_copyright_link) && $settings->page07_copyright_link) ? $settings->page07_copyright_link : '';
                        $output .= '<div class="swiper-slide">
                            <div class="page page-7">
                                <div class="content swiper-no-swiping">
                                    <div class="sever-main-left ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                        <span class="sever-main-left-p">' . $page07_left_title01 . '</span><br>
                                        <span class="sever-main-left-p2">' . $page07_left_title02 . '</span> <br>
                                        <span class="sever-main-left-p3">' . $page07_left_title03 . '</span><br>
                                        <span class="sever-main-left-p4">' . $page07_left_title04 . '</span><br>
                                        <span class="sever-main-left-p5">' . $page07_left_title05 . '</span><br>
                                    </div>
                                    <!-- 地图 -->
                                    <div class="sever-main-right ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                        <div id="container_' . $id . '" class="container"></div>
                                    </div>
                                </div>
                                <!-- 底部 -->
                                <div class="footer-b swiper-no-swiping">
                                    <div class="bufu">
                                        <p class="footer-p2">
                                            ' . $page07_content  . '
                                        </p>
                                        <div class="footer-p"
                                            style="display:block;width:350px;height:20px;margin:0 auto; padding:0;">
                                            <a target="_blank" href="' . $page07_record_link_g . '"
                                                style="text-decoration:none;height:20px;line-height:20px;">
                                                <img src="https://oss.lcweb01.cn/joomla/20211015/d2b288ec8fca02e2036d473db62f96d6.png"
                                                    style="float:left;">
                                                <p style="float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px;">
                                                    ' . $page07_record_g  . '</p>
                                            </a>
                                            <a href="' . $page07_record_link . '">
                                                <p style="float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px;">
                                                    ' . $page07_record  . '</p>
                                            </a>
                                        </div>
                                        <span class="footer-p">技术支持：<a href="' . $page07_copyright_link . '">' . $page07_copyright  . '</a></span>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    }
			    $output .= '
			    </div>
                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
                <!-- Add Arrows -->
                <div class="swiper-button-next">
                    <img src="https://oss.lcweb01.cn/joomla/20211207/a6cc6a4bda51f3259dffc9c4aa3dd9bd.png">
                </div>
                <!-- <div class="swiper-button-prev"></div> -->
		    </div>
		';
        return $output;
    }
    public function css() {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;

        // 第三页
        $page03_slider_img = (isset($settings->page03_slider_img) && $settings->page03_slider_img) ? $settings->page03_slider_img : "https://oss.lcweb01.cn/joomla/20211207/71aed273f4687b0c6ac9421820810550.jpg";
        // 第四页
        $page04_slider_img = (isset($settings->page04_slider_img) && $settings->page04_slider_img) ? $settings->page04_slider_img : "https://oss.lcweb01.cn/joomla/20211207/b9473a1d184bf5234f3e730d505961aa.jpg";
        // 第五页
        $page05_slider_img = (isset($settings->page05_slider_img) && $settings->page05_slider_img) ? $settings->page05_slider_img : "https://oss.lcweb01.cn/joomla/20211207/bd862c691107888b4505e6af6a580ccf.jpg";
        // 第六页
        $page06_slider_img = (isset($settings->page06_slider_img) && $settings->page06_slider_img) ? $settings->page06_slider_img : "https://oss.lcweb01.cn/joomla/20211207/4ecd2249ab2ebe84efd99213cd4a4f30.jpg";
        // 第七页
        $page07_slider_img = (isset($settings->page07_slider_img) && $settings->page07_slider_img) ? $settings->page07_slider_img : "https://oss.lcweb01.cn/joomla/20211207/6e35db099855a042d258507a5407f8bc.jpg";

        // 第二页
        $page02_slider_img_left = (isset($settings->page02_slider_img_left) && $settings->page02_slider_img_left) ? $settings->page02_slider_img_left : "https://oss.lcweb01.cn/joomla/20211207/e9bf64334c2bfac98533991beede0ee4.jpg";
        $page02_slider_img_right = (isset($settings->page02_slider_img_right) && $settings->page02_slider_img_right) ? $settings->page02_slider_img_right : "https://oss.lcweb01.cn/joomla/20211207/5e0783cc2b6d1edc841a3a94cf543019.jpg";

        // 第四页
        $page04_slider_img_center = (isset($settings->page04_slider_img_center) && $settings->page04_slider_img_center) ? $settings->page04_slider_img_center : "https://oss.lcweb01.cn/joomla/20211216/2a7ae34d4d9a97a678526f6cedde2129.png";
        $page04_slider_icon = (isset($settings->page04_slider_icon) && $settings->page04_slider_icon) ? $settings->page04_slider_icon : "https://oss.lcweb01.cn/joomla/20211216/d7428b14b5aee5f142bcf33d89c65625.png";


        $css = $addonId . ' * {
				margin: 0;
				padding: 0;
			}
			' . $addonId . ' .full-page {
				width: 100%;
				height: 100vh;
				min-width: 1200px;
				margin-left: auto;
				margin-right: auto;
			}
			/* 新增样式 */
			/* 下一页按钮 */
			' . $addonId . ' .full-page .swiper-button-next {
				left: 0;
				right: 0;
				top: auto;
				margin: auto;
				bottom: 30px;
				/*transform: rotateZ(90deg);*/
				-webkit-animation: fade 2s infinite linear;
				animation: fade 3s infinite linear;
				color: #fff;
			}
			' . $addonId . ' .full-page .swiper-button-next::after {
				content: \'\';
			}
			' . $addonId . ' .full-page .swiper-button-next.swiper-button-disabled {
				display: none;
			}
			/* 切换点 */
			' . $addonId . ' .swiper-container-vertical>.swiper-pagination-bullets {
				right: 17px !important;
			}
			' . $addonId . ' .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
				width: 20px;
				height: 20px;
				background-color: rgba(255, 255, 255, 0);
				border: rgba(255, 255, 255, 0) solid 3px;
				opacity: 1;
				margin: 10px 0;
				position: relative;
			}
			' . $addonId . ' .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet::after {
				content: \'\';
				position: absolute;
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background-color: rgba(255, 255, 255, 0.3);
				margin: auto;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
			}
			' . $addonId . ' .full-page .swiper-pagination-bullet.swiper-pagination-bullet-active {
				border: rgba(255, 255, 255, 0.2) solid 3px;
			}
			' . $addonId . ' .full-page .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
				background-color: rgba(255, 255, 255, 0.8);
			}
			@-webkit-keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}

			@keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}
			@-webkit-keyframes scale {
				0% {
					transform: scale(1);
				}

				50% {
					transform: scale(1.1);
				}

				100% {
					transform: scale(1);
				}
			}

			@keyframes scale {
				0% {
					transform: scale(1);
				}

				50% {
					transform: scale(1.1);
				}

				100% {
					transform: scale(1);
				}
			}

			' . $addonId . ' .page {
				width: 100%;
				height: 100%;
				position: relative;
				background-color: rgb(255, 255, 255);
				background-repeat: no-repeat;
				background-position: 50% 100%;
				background-size: cover;
				/* Center slide text vertically */
				display: -webkit-box;
				display: -ms-flexbox;
				display: -webkit-flex;
				display: flex;
				-webkit-box-pack: center;
				-ms-flex-pack: center;
				-webkit-justify-content: center;
				justify-content: center;
				-webkit-box-align: center;
				-ms-flex-align: center;
				-webkit-align-items: center;
				align-items: center;
				overflow: hidden;
			}
			' . $addonId . ' .page .content {
				width: 1200px;
				box-sizing: border-box;
			}

			' . $addonId . ' .full-page .swiper-slide .page-2 {

			}

			' . $addonId . ' .full-page .swiper-slide .page-3 {
				background-image: url("' . $page03_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-4 {
				background-image: url("' . $page04_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-5 {
				background-image: url("' . $page05_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-6 {
				background-image: url("' . $page06_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-7 {
				background-image: url("' . $page07_slider_img . '");
			}
			/* 第二页 */
			' . $addonId . ' .page-2 {
				position: relative;
			}
			' . $addonId . ' .page-2 .page-item {
				display: inline-block;
				width: 50%;
				height: 100%;
				position: relative;
			}
			' . $addonId . ' .page-2 .page-item:first-child {
				background-image: url("' . $page02_slider_img_left . '");
				cursor: pointer;
			}
			' . $addonId . ' .page-2 .page-item.right-content {
				background-image: url("' . $page02_slider_img_right . '");
				display: flex;
				align-items: center;
				box-sizing: border-box;
			}
			' . $addonId . ' .page-2 .page-item.right-content .two-right-box {
				width: 80%;
				margin: auto;
			}
			' . $addonId . ' .page-2 .page-item.right-content .two-right-box .two-right-box-p {
				font-size: 50px;
				color: rgb(143, 195, 32);
			}
			' . $addonId . ' .page-2 .page-item.right-content .two-right-box .two-right-box-p2 {
				font-size: 60px;
				color: rgb(22, 22, 22);
				display: block;
				margin-bottom: 30px;
			}
			' . $addonId . ' .page-2 .page-item.right-content .two-right-box .two-right-box-p3 {
				font-size: 16px;
				color: rgb(57, 57, 57);
				line-height: 30px;
				text-align: justify;
			}
			' . $addonId . ' .page-2 .theme-popover {
				z-index: 99999;
				position: absolute;
				width: 50%;
				height: 50%;
				top: 20%;
				left: 0;
				right: 0;
				margin: auto;
			}
			' . $addonId . ' .page-2 .theme-poptit {
				padding: 12px;
				position: relative;
				margin-bottom: 18px;
			}
			' . $addonId . ' .page-2 .theme-poptit .close {
				float: right;
				color: #999;
				padding: 5px;
				margin: -2px -5px -5px;
				font: bold 14px/14px simsun;
				text-shadow: 0 1px 0 #ddd;
			}

			/* 第三页 */
			' . $addonId . ' .page-3 .content .top-box {
				display: flex;
			}
			' . $addonId . ' .page-3 .two-main-bt-p {
				font-size: 20px;
				color: rgb(17, 17, 17);
				margin-bottom: 20px;
			}
			' . $addonId . ' .page-3 .two-main-bt-p2 {
				font-size: 40px;
				color: rgb(50, 182, 81);
			}
			' . $addonId . ' .page-3 .two-main-bt {
				width: 40%;
				height: 146px;
				margin: 0 auto;
				text-align: center;
				padding-top: 20px;
			}
			' . $addonId . ' .page-3 .two-main-div {
				width: 80%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 1%;
			}
			' . $addonId . ' .page-3 .two-main-div2 {
				width: 94%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 2%;
			}
			' . $addonId . ' .page-3 .two-main-div3 {
				width: 100%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 2%;
			}
			' . $addonId . ' .page-3 .two-main-left {
				float: left;
				width: 366px;
				height: 100%;
				text-align: right;
			}
			' . $addonId . ' .page-3 .two-main-left img {
				display: block;
				float: right;
				margin-left: 30px;
				margin-bottom: 20px;
				width: 55px;
				height: 55px;
			}
			' . $addonId . ' .page-3 .in-jmhz-there-box {
				float: left;
				width: 100%;
				height: 65%;
			}
			' . $addonId . ' .page-3 .in-jmhz-there-div {
				float: left;
				width: 123px;
				height: 165px;
				background: tan;
				margin-left: 70px;
				margin-bottom: 10px;
				color: white;
				padding-left: 20px;
				padding-right: 20px;
				border-radius: 200px;
				cursor: pointer;
                box-sizing: content-box;
			}
			' . $addonId . ' .page-3 .in-jmhz-there-bt {
				width: 104px;
				height: 100px;
				margin: 0 auto;
				font-size: 20px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 1px;
			}
			' . $addonId . ' .page-3 .jmhz-there-bt a {
				color: rgb(19, 19, 19);
			}
			' . $addonId . ' .page-3 .jmhz-there-bt a:hover {
				color: rgb(50, 182, 81);
			}
			' . $addonId . ' .page-3 .in-jmhz-there-text {
				width: 100%;
				height: 218px;
				height: 81px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 25px;
				font-size: 8px;
				color: rgb(245, 252, 229);
			}
			' . $addonId . ' .page-3 .jmhz-there-text {
				width: 100%;
				height: 218px;
				height: 81px;
				font-size: 14px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 25px;
			}
			' . $addonId . ' .page-3 .in-jmhz-there-bt-icon {
				width: 68px;
				height: 68px;
				margin: 20px auto;
			}
			' . $addonId . ' .page-3 .jmhz-there-bt-icon img {
				width: 68px;
				height: 68px;
			}
			' . $addonId . ' .page-3 .in-jmhz-there-bt a {
				color: white !important;
				font-size: 16px;
			}';
            // 第三页
            $page03_list_item = (isset($settings->page03_list_item) && $settings->page03_list_item) ? $settings->page03_list_item : array(
                (object)array(
                    'title' => '直营式管理',
                    'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                    'slider_color' => 'rgb(69,84,105)',
                    'slider_border_color' => 'rgb(199,203,210)',
                    'slider_border_color_hover' => 'rgb(146,154,169)',
                    'title_link' => '',
                ),
                (object)array(
                    'title' => '直营式管理',
                    'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                    'slider_color' => 'rgb(69,84,105)',
                    'slider_border_color' => 'rgb(199,203,210)',
                    'slider_border_color_hover' => 'rgb(146,154,169)',
                    'title_link' => '',
                ),
                (object)array(
                    'title' => '直营式管理',
                    'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                    'slider_color' => 'rgb(69,84,105)',
                    'slider_border_color' => 'rgb(199,203,210)',
                    'slider_border_color_hover' => 'rgb(146,154,169)',
                    'title_link' => '',
                )
            );
            foreach ($page03_list_item as $key => $item) {
                $index = $key + 1;
                $css .= $addonId . ' .page-3 .in-jmhz-there-div:nth-child(' . $index . ') {';
                if($index == 1) {
                    $css .= 'margin-left: 130px;';
                }
                if($index == 4) {
                    $css .= 'margin-right: 30px;';
                }
                if($index == 5) {
                    $css .= 'margin-left: 0px;';
                }
                $css .= 'background: ' . $item->slider_color . ';
                    border: 10px solid ' . $item->slider_border_color . ';
                }
                ' . $addonId . ' .page-3 .in-jmhz-there-div:nth-child(' . $index . '):hover {
                border: 10px solid ' . $item->slider_border_color_hover . ';
                    transition-duration: 1s;
                }';
            }

            $css .= '/* 第四页 */
            ' . $addonId . ' .page-4 .content {
				height: 552px;
				background: url("' .$page04_slider_img_center . '") no-repeat center;
			}
			' . $addonId . ' .page-4 .banner-btn a {
				display: block;
				line-height: 40px;
				position: absolute;
				top: 200px;
				width: 40px;
				height: 40px;
				background-color: #000;
				opacity: 0.3;
				color: rgba(255, 255, 255, 0.3);
				overflow: hidden;
				z-index: 4;
			}
			' . $addonId . ' .page-4 .banner-btn a i {
				background: url("' .$page04_slider_icon . '") no-repeat center;
				background-size: 70%;
				width: 100%;
				height: 100%;
				cursor: pointer;
				display: block;
			}
			' . $addonId . ' .page-4 .banner-btn .prevBtn {
				left: 5px;
			}
			' . $addonId . ' .page-4 .banner-btn .nextBtn {
				right: 5px;
			}
			' . $addonId . ' .page-4 .banner-btn a.nextBtn i {
				transform: rotateZ(180deg);
			}
			' . $addonId . ' .page-4 .four-bt-p2 {
				font-size: 60px;
				color: white;
			}

			/* 第五页 */
			' . $addonId . ' .page-5 .content {}
			' . $addonId . ' .five-gun {
				float: left;
				width: 391px;
				height: 451px;
				margin-left: 405px;
				margin-top: -260px;
			}
			' . $addonId . ' .five-class {
				width: 235px;
				height: 206px;
				float: right;
				margin-top: -130px;
				margin-right: 60px;
			}
			' . $addonId . ' .five-class-btn {
				width: 100%;
				height: 50px;
				border: none;
				border: 1px solid white;
				background: none;
				margin-bottom: 20px;
				font-size: 20px;
				color: white;
				cursor: pointer;
			}
			' . $addonId . ' .five {
				float: left;
				width: 100%;
				height: 960px;
				background: rgb(254, 216, 66);
			}
			' . $addonId . ' .five-main {
				width: 1200px;
				height: 430px;
				margin: 0 auto;
			}
			' . $addonId . ' .five-bt {
				float: left;
				width: 1200px;
				height: 90px;
				padding-top: 20px;
				margin-top: 200px;
				margin-left: 90px;
			}
			' . $addonId . ' .six-bt {
				float: left;
				width: 200px;
				height: 90px;
				text-align: center;
				margin-top: 170px;
			}
			' . $addonId . ' .five-box {
				float: right;
				width: 1000px;
				height: 437px;
			}
			' . $addonId . ' .five-bt-p {
				font-size: 23px;
				color: white;
			}
			' . $addonId . ' .six-bt-p {
				font-size: 23px;
				color: black;
			}
			' . $addonId . ' .five-bt-p2 {
				font-size: 40px;
				color: rgb(50, 182, 81);
			}
			' . $addonId . ' .five-pn {
				width: 100px;
				height: 26px;
				float: right;
				margin-top: -500px;
			}
			' . $addonId . ' .five-pnt {
				width: 100px;
				height: 26px;
				float: right;
				margin-top: -100px;
			}
			' . $addonId . ' .five-pnt5 {
				width: 100px;
				height: 26px;
				float: right;
			}
			' . $addonId . ' .five-div {
				width: 300px;
				height: 200px;
				float: left;
				margin-left: 30px;
				margin-bottom: 30px;
				position: relative;
			}
			' . $addonId . ' .five-div img {
				width: 300px;
				height: 267px;
				border-radius: 10px;
				display: block;
			}
			' . $addonId . ' .five-div-text {
				width: 100%;
				height: 100%;
				opacity: 0;
				position: absolute;
				background: rgba(0, 0, 0, 0.5);
				padding-left: 10px;
				padding-right: 10px;
				border-radius: 10px;
			}
			' . $addonId . ' .five-div img {
				width: 100% !important;
				height: 100% !important;
			}
			' . $addonId . ' .five-div-text:hover {
				opacity: 1;
				transition-duration: 1s;
			}
			' . $addonId . ' .five-div-text-p {
				font-size: 18px;
				color: white;
				display: block;
				margin-top: 20px;
			}
			' . $addonId . ' .five-div-text-p2 {
				font-size: 13px;
				color: white;
				display: block;
				line-height: 30px;
			}
			' . $addonId . ' .five-div-text-p3 {
				font-size: 18px;
				color: white;
				display: block;
			}
			' . $addonId . ' .five-div-text-p3 a {
				color: white;
			}
			' . $addonId . ' .five-div-text-p3 a:hover {
				color: rgb(50, 182, 81);
			}

			/* 第六页 */
			' . $addonId . ' .page-6 .content {
				padding: 0 120px;
			}
			' . $addonId . ' .page-6 .six {
				float: left;
				width: 100%;
				height: 960px;
				background: white;
			}
			' . $addonId . ' .page-6 .six-main {
				width: 1200px;
				height: 550px;
				margin: 190px auto;
			}
			' . $addonId . ' .page-6 .six-main-left {
				float: left;
				width: 500px;
				height: 500px;
			}
			' . $addonId . ' .page-6 .six-main-right {
				float: right;
				width: 400px;
				height: 400px;
			}
			' . $addonId . ' .page-6 .six-main-right img {
				width: 400px;
				height: 400px;
			}
			' . $addonId . ' .page-6 .six-main-left-p {
				font-size: 35px;
				color: rgb(19, 19, 19);
			}
			' . $addonId . ' .page-6 .six-main-left-p2 {
				font-size: 60px;
				color: rgb(50, 182, 81);
				display: block;
			}
			' . $addonId . ' .page-6 .six-main-left-p3 {
				font-size: 16px;
				color: black;
				display: block;
				line-height: 30px;
			}
			' . $addonId . ' .page-6 .six-main-left-p4 {
				font-size: 16px;
				color: rgb(47, 47, 47);
				line-height: 25px;
			}
			' . $addonId . ' .page-6 .six-btn {
				float: left;
				width: 140px;
				height: 52px;
				font-size: 16px;
				color: white;
				border: none;
				outline: none;
				border-radius: 10px;
				background: rgb(50, 182, 81);
				cursor: pointer;
				margin-top: 15px;
			}
			' . $addonId . ' .page-6 .six-lian {
				border: 1px dashed white;
				padding-top: 10px;
				padding-bottom: 8px;
				padding-left: 30px;
				padding-right: 30px;
			}

			/* 第七页 */
			' . $addonId . ' .page-7 {
				display: block;
			}
			' . $addonId . ' .page-7 .content {
				height: calc(100vh - 14.1%);
				margin: auto;
				display: flex;
				align-items: center;
				justify-content: space-around;
			}
			' . $addonId . ' .page-7 .sever-main-left {
				float: left;
				width: 386px;
				height: 386px;
				text-align: center;
				line-height: 60px;
			}
			' . $addonId . ' .page-7 .sever-main-right {
				float: right;
				width: 741px;
				height: 386px;
			}
			' . $addonId . ' .page-7 .sever-main-right .container {
				width: 100%;
				height: 100%;
				font-size: 14px;
			}
			/*地图信息窗样式*/
			' . $addonId . ' .amap-info-content {
			    padding: 10px 18px 10px 10px;
			}
			' . $addonId . ' .bottom-center .amap-info-sharp {
			    margin-left: -8px;
			    bottom: -8px;
			}
			' . $addonId . ' .page-7 .sever-main-left-p {
				font-size: 28px;
			}
			' . $addonId . ' .page-7 .sever-main-left-p2 {
				font-size: 45px;
			}
			' . $addonId . ' .page-7 .sever-main-left-p3 {
				font-size: 20px;
			}
			' . $addonId . ' .page-7 .sever-main-left-p4 {
				font-size: 45px;
			}
			' . $addonId . ' .page-7 .sever-main-left-p5 {
				font-size: 15px;
				color: rgb(255, 255, 255);
				background: #32b651;
				border-radius: 20px;
				padding-left: 10px;
				padding-right: 10px;
				padding-bottom: 4px;
			}
			' . $addonId . ' .page-7 .seven-btn {
				width: 330px;
				height: 60px;
				border: none;
				outline: none;
				border-radius: 10px;
				background: rgb(50, 182, 81);
				font-size: 18px;
				color: white;
				cursor: pointer;
			}
			' . $addonId . ' .page-7 .bufu {
				width: 450px;
				height: 70px;
				margin: auto;
				margin-top: auto;
				margin-bottom: auto;
				position: relative;
				top: 50%;
				transform: translateY(-50%);
			}
			' . $addonId . ' .page-7 .footer-b {
				width: 100%;
				height: 14.1%;
				background: rgb(85, 85, 85);
				text-align: center;
				line-height: 23px;
				position: relative;
			}
			' . $addonId . ' .page-7 .footer-p {
				font-size: 12px;
				color: rgb(189, 189, 189);
			}
			' . $addonId . ' .page-7 .footer-p2 {
				font-size: 12px;
				color: white;
			}
			' . $addonId . ' .page-7 .footer-p a {
				color: rgb(189, 189, 189);
				display: inline-block;
			}
			' . $addonId . ' .page-7 .footer-p2 a:hover {
				color: white;
				text-decoration: none;
			}
			' . $addonId . ' .page-7 .footer-p2 a {
				color: white;
			}';
        return $css;
    }
    public function js() {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;

        // 第一页是否展示
        $page01_hide = (isset($settings->page01_hide) && $settings->page01_hide) ? $settings->page01_hide : 0;
        // 第四页是否展示
        $page04_hide = (isset($settings->page04_hide) && $settings->page04_hide) ? $settings->page04_hide : 0;
        // 第七页是否展示
        $page07_hide = (isset($settings->page07_hide) && $settings->page07_hide) ? $settings->page07_hide : 0;

        // 地图经度
        $page07_map_lon = (isset($settings->page07_map_lon) && $settings->page07_map_lon) ? $settings->page07_map_lon : '112.547132';
        // 地图纬度
        $page07_map_lat = (isset($settings->page07_map_lat) && $settings->page07_map_lat) ? $settings->page07_map_lat : '37.794354';
        $page07_map_title01 = (isset($settings->page07_map_title01) && $settings->page07_map_title01) ? $settings->page07_map_title01 : '山西资海科技科技有限公司';
        $page07_map_title02 = (isset($settings->page07_map_title02) && $settings->page07_map_title02) ? $settings->page07_map_title02 : '山西太原市南中环街清控创新基地B座4层';
        $page07_map_title03 = (isset($settings->page07_map_title03) && $settings->page07_map_title03) ? $settings->page07_map_title03 : '400-***-****';

        $js = '
            function showVideo() {
                $(\'' . $addonId . ' .theme-popover\').show();
            }
            function hideVideo() {
                $(\'' . $addonId . ' .theme-popover\').hide();
            }
            jQuery(document).ready(function($){
                var swiper = new Swiper(\'' . $addonId . ' #full-page\', {
                    direction: \'vertical\',
                    slidesPerView: 1,
                    mousewheel: true,
                    noSwiping: true,
                    pagination: {
                    el: \'' . $addonId . ' #full-page .swiper-pagination\',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: \'' . $addonId . ' #full-page .swiper-button-next\',
                    },
                    on: {
                        init: function() {
                            swiperAnimateCache(this); //隐藏动画元素
                            swiperAnimate(this); //初始化完成开始动画
                        },
                        slideChangeTransitionEnd: function() {
                            swiperAnimateCache(this); //隐藏动画元素
                            swiperAnimate(this); //每个slide切换结束时也运行当前slide动画
                        }
                    }
                });';
                if($page01_hide == 0) {
                    $js .= '
                    var page1Swiper = new Swiper(\'' . $addonId . ' .page1-swiper\', {
                        slidesPerView: 1,
                        autoplay: true,
                        speed: 800,
                        loop: true
                    });';
                }
                if($page04_hide == 0) {
                    $js .= '
                    var page4Swiper = new Swiper(\'' . $addonId . ' #page-4-swiper\', {
                        slidesPerView: 1,
                        autoplay: true,
                        speed: 800,
                        loop: true,
                        navigation: {
                            nextEl: \'' . $addonId . ' .prevBtn\',
                            prevEl: \'' . $addonId . ' .nextBtn\',
                        },
                    });';
                }
                if($page07_hide == 0) {
                    $js .= '
                        // 地图相关
                        var position = [' . $page07_map_lon . ',' . $page07_map_lat . ']
                        var map = new AMap.Map(\'container_' . $id . '\', {
                            viewMode: \'2D\', // 默认使用 2D 模式，如果希望使用带有俯仰角的 3D 模式，请设置 viewMode: \'3D\',
                            zoom: 16, //初始化地图层级
                            center: position //初始化地图中心点
                        });
                        addMarker();
                        openInfo();
                        //在指定位置打开信息窗体
                        function openInfo() {
                            //构建信息窗体中显示的内容
                            var info = [];
                            info.push("<div class=\'input-card content-window-card\'>");
                            info.push("<h4 style=\'font-size: 14px; font-weight: bold; color: #cc5522;margin-bottom:5px;\'>' . $page07_map_title01 . '</h4>");
                            info.push("<p class=\'input-item\'>地址: ' . $page07_map_title02 . '</p>");
                            info.push("<p class=\'input-item\'>电话: ' . $page07_map_title03 . '</p></div>");

                            infoWindow = new AMap.InfoWindow({
                                offset: new AMap.Pixel(0, -25),
                                content: info.join(\'\') //使用默认信息窗体框样式，显示信息内容
                            });

                            infoWindow.open(map, map.getCenter());
                        }
                        //添加marker标记
                        function addMarker() {
                            map.clearMap();
                            var marker = new AMap.Marker({
                                map: map,
                                position: position
                            });
                            //鼠标点击marker弹出自定义的信息窗体
                            marker.on(\'click\', function () {
                                openInfo();
                            });
                        }
                    ';
                }
		    $js .= '})';

        return $js;
    }
    public function scripts() {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js',
            JURI::base(true) . '//webapi.amap.com/maps?v=2.0&&key=509ed5f612c9e136d02124d1501da3eb',
        );
        return $js;
    }
    public function stylesheets() {
        $style_sheet = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
            JURI::base(true) . '/components/com_jwpagefactory/addons/full_page/assets/css/animate.min.css',
        );

        return $style_sheet;
    }
    public static function getTemplate() {
        $output = '
        <#
            var addonId = "#jwpf-addon-" + data.id;
            var slide_page = data.slide_page || "page01"
            // 第三页
            var page03_slider_img = data.page03_slider_img || "https://oss.lcweb01.cn/joomla/20211207/71aed273f4687b0c6ac9421820810550.jpg";
            // 第四页
            var page04_slider_img = data.page04_slider_img || "https://oss.lcweb01.cn/joomla/20211207/b9473a1d184bf5234f3e730d505961aa.jpg";
            // 第五页
            var page05_slider_img = data.page05_slider_img || "https://oss.lcweb01.cn/joomla/20211207/bd862c691107888b4505e6af6a580ccf.jpg";
            // 第六页
            var page06_slider_img = data.page06_slider_img || "https://oss.lcweb01.cn/joomla/20211207/4ecd2249ab2ebe84efd99213cd4a4f30.jpg";
            // 第七页
            var page07_slider_img = data.page07_slider_img || "https://oss.lcweb01.cn/joomla/20211207/6e35db099855a042d258507a5407f8bc.jpg";
        #>
        <style>
            {{ addonId }} * {
				margin: 0;
				padding: 0;
			}
			{{ addonId }} .full-page {
				width: 100%;
				height: 100vh;
				min-width: 1200px;
				margin-left: auto;
				margin-right: auto;
			}
			/* 新增样式 */
			/* 下一页按钮 */
			{{ addonId }} .full-page .swiper-button-next {
				left: 0;
				right: 0;
				top: auto;
				margin: auto;
				bottom: 30px;
				/*transform: rotateZ(90deg);*/
				-webkit-animation: fade 2s infinite linear;
				animation: fade 3s infinite linear;
				color: #fff;
			}

			{{ addonId }} .full-page .swiper-button-next::after {
				content: \'\';
			}

			{{ addonId }} .full-page .swiper-button-next.swiper-button-disabled {
				display: none;
			}

			/* 切换点 */
			{{ addonId }} .swiper-container-vertical>.swiper-pagination-bullets {
				right: 17px !important;
			}

			{{ addonId }} .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
				width: 20px;
				height: 20px;
				background-color: rgba(255, 255, 255, 0);
				border: rgba(255, 255, 255, 0) solid 3px;
				opacity: 1;
				margin: 10px 0;
				position: relative;
			}

			{{ addonId }} .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet::after {
				content: \'\';
				position: absolute;
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background-color: rgba(255, 255, 255, 0.3);
				margin: auto;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
			}
			{{ addonId }} .full-page .swiper-pagination-bullet.swiper-pagination-bullet-active {
				border: rgba(255, 255, 255, 0.2) solid 3px;
			}

			{{ addonId }} .full-page .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
				background-color: rgba(255, 255, 255, 0.8);
			}

			@-webkit-keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}

			@keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}
			@-webkit-keyframes scale {
				0% {
					transform: scale(1);
				}

				50% {
					transform: scale(1.1);
				}

				100% {
					transform: scale(1);
				}
			}

			@keyframes scale {
				0% {
					transform: scale(1);
				}

				50% {
					transform: scale(1.1);
				}

				100% {
					transform: scale(1);
				}
			}

			{{ addonId }} .page {
				width: 100%;
				height: 100%;
				position: relative;
				background-color: rgb(255, 255, 255);
				background-repeat: no-repeat;
				background-position: 50% 100%;
				background-size: cover;
				/* Center slide text vertically */
				display: -webkit-box;
				display: -ms-flexbox;
				display: -webkit-flex;
				display: flex;
				-webkit-box-pack: center;
				-ms-flex-pack: center;
				-webkit-justify-content: center;
				justify-content: center;
				-webkit-box-align: center;
				-ms-flex-align: center;
				-webkit-align-items: center;
				align-items: center;
				overflow: hidden;
			}
			{{ addonId }} .page .content {
				width: 1200px;
				box-sizing: border-box;
			}

			{{ addonId }} .full-page .swiper-slide .page-2 {

			}

			{{ addonId }} .full-page .swiper-slide .page-3 {
				background-image: url("{{ page03_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-4 {
				background-image: url("{{ page04_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-5 {
				background-image: url("{{ page05_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-6 {
				background-image: url("{{ page06_slider_img }}");
			}
			{{ addonId }} .full-page .swiper-slide .page-7 {
				background-image: url("{{ page07_slider_img }}");
			}
			/* 第二页 */
			<#
			    var page02_slider_img_left = data.page02_slider_img_left || "https://oss.lcweb01.cn/joomla/20211207/e9bf64334c2bfac98533991beede0ee4.jpg";
			    var page02_slider_img_right = data.page02_slider_img_right || "https://oss.lcweb01.cn/joomla/20211207/5e0783cc2b6d1edc841a3a94cf543019.jpg";
			#>
			{{ addonId }} .page-2 {
				position: relative;
			}
			{{ addonId }} .page-2 .page-item {
				display: inline-block;
				width: 50%;
				height: 100%;
				position: relative;
			}
			{{ addonId }} .page-2 .page-item:first-child {
				background-image: url("{{ page02_slider_img_left }}");
				cursor: pointer;
			}
			{{ addonId }} .page-2 .page-item.right-content {
				background-image: url("{{ page02_slider_img_right }}");
				display: flex;
				align-items: center;
				box-sizing: border-box;
			}
			{{ addonId }} .page-2 .page-item.right-content .two-right-box {
				width: 80%;
				margin: auto;
			}
			{{ addonId }} .page-2 .page-item.right-content .two-right-box .two-right-box-p {
				font-size: 50px;
				color: rgb(143, 195, 32);
			}
			{{ addonId }} .page-2 .page-item.right-content .two-right-box .two-right-box-p2 {
				font-size: 60px;
				color: rgb(22, 22, 22);
				display: block;
				margin-bottom: 30px;
			}
			{{ addonId }} .page-2 .page-item.right-content .two-right-box .two-right-box-p3 {
				font-size: 16px;
				color: rgb(57, 57, 57);
				line-height: 30px;
				text-align: justify;
			}
			{{ addonId }} .page-2 .theme-popover {
				z-index: 99999;
				position: absolute;
				width: 50%;
				height: 50%;
				top: 20%;
				left: 0;
				right: 0;
				margin: auto;
			}
			{{ addonId }} .page-2 .theme-poptit {
				padding: 12px;
				position: relative;
				margin-bottom: 18px;
			}
			{{ addonId }} .page-2 .theme-poptit .close {
				float: right;
				color: #999;
				padding: 5px;
				margin: -2px -5px -5px;
				font: bold 14px/14px simsun;
				text-shadow: 0 1px 0 #ddd;
			}

			/* 第三页 */
			{{ addonId }} .page-3 .content .top-box {
				display: flex;
			}
			{{ addonId }} .page-3 .two-main-bt-p {
				font-size: 20px;
				color: rgb(17, 17, 17);
				margin-bottom: 20px;
			}
			{{ addonId }} .page-3 .two-main-bt-p2 {
				font-size: 40px;
				color: rgb(50, 182, 81);
			}
			{{ addonId }} .page-3 .two-main-bt {
				width: 40%;
				height: 146px;
				margin: 0 auto;
				text-align: center;
				padding-top: 20px;
			}
			{{ addonId }} .page-3 .two-main-div {
				width: 80%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 1%;
			}
			{{ addonId }} .page-3 .two-main-div2 {
				width: 94%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 2%;
			}
			{{ addonId }} .page-3 .two-main-div3 {
				width: 100%;
				height: 12%;
				margin: 0 auto;
				margin-bottom: 2%;
			}
			{{ addonId }} .page-3 .two-main-left {
				float: left;
				width: 366px;
				height: 100%;
				text-align: right;
			}
			{{ addonId }} .page-3 .two-main-left img {
				display: block;
				float: right;
				margin-left: 30px;
				margin-bottom: 20px;
				width: 55px;
				height: 55px;
			}
			{{ addonId }} .page-3 .in-jmhz-there-box {
				float: left;
				width: 100%;
				height: 65%;
			}
			{{ addonId }} .page-3 .in-jmhz-there-div {
				float: left;
				width: 123px;
				height: 165px;
				background: tan;
				margin-left: 70px;
				margin-bottom: 10px;
				color: white;
				padding-left: 20px;
				padding-right: 20px;
				border-radius: 200px;
				cursor: pointer;
                box-sizing: content-box;
			}
			{{ addonId }} .page-3 .in-jmhz-there-bt {
				width: 104px;
				height: 100px;
				margin: 0 auto;
				font-size: 20px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 1px;
			}
			{{ addonId }} .page-3 .jmhz-there-bt a {
				color: rgb(19, 19, 19);
			}
			{{ addonId }} .page-3 .jmhz-there-bt a:hover {
				color: rgb(50, 182, 81);
			}
			{{ addonId }} .page-3 .in-jmhz-there-text {
				width: 100%;
				height: 218px;
				height: 81px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 25px;
				font-size: 8px;
				color: rgb(245, 252, 229);
			}
			{{ addonId }} .page-3 .jmhz-there-text {
				width: 100%;
				height: 218px;
				height: 81px;
				font-size: 14px;
				color: rgb(19, 19, 19);
				text-align: center;
				line-height: 25px;
			}
			{{ addonId }} .page-3 .in-jmhz-there-bt-icon {
				width: 68px;
				height: 68px;
				margin: 20px auto;
			}
			{{ addonId }} .page-3 .jmhz-there-bt-icon img {
				width: 68px;
				height: 68px;
			}
			{{ addonId }} .page-3 .in-jmhz-there-bt a {
				color: white !important;
				font-size: 16px;
			}
			<#
			    var page03_list_item = data.page03_list_item || [{
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    },
                    {
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    },
                    {
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    }];
			    _.each(page03_list_item, function (item, key){
			    var index = key + 1
            #>
                {{ addonId }} .page-3 .in-jmhz-there-div:nth-child({{ index }}) {
                    <# if(index == 1) {#>
                        margin-left: 130px;
                    <# } #>
                    <# if(index == 4) {#>
                        margin-right: 30px;
                    <# } #>
                    <# if(index == 5) {#>
                        margin-left: 0px;
                    <# } #>
                    background: {{ item.slider_color }};
                    border: 10px solid {{ item.slider_border_color }};
                }
                {{ addonId }} .page-3 .in-jmhz-there-div:nth-child({{ index }}):hover {
                    border: 10px solid {{ item.slider_border_color_hover }};
                    transition-duration: 1s;
                }
            <# }); #>

			/* 第四页 */
			<#
			    var page04_slider_img_center = data.page04_slider_img_center || "https://oss.lcweb01.cn/joomla/20211216/2a7ae34d4d9a97a678526f6cedde2129.png";
			    var page04_slider_icon = data.page04_slider_icon || "https://oss.lcweb01.cn/joomla/20211216/d7428b14b5aee5f142bcf33d89c65625.png";
			#>
			{{ addonId }} .page-4 .content {
				height: 552px;
				background: url("{{ page04_slider_img_center }}") no-repeat center;
			}
			{{ addonId }} .page-4 .banner-btn a {
				display: block;
				line-height: 40px;
				position: absolute;
				top: 200px;
				width: 40px;
				height: 40px;
				background-color: #000;
				opacity: 0.3;
				color: rgba(255, 255, 255, 0.3);
				overflow: hidden;
				z-index: 4;
			}
			{{ addonId }} .page-4 .banner-btn a i {
				background: url("{{ page04_slider_icon }}") no-repeat center;
				background-size: 70%;
				width: 100%;
				height: 100%;
				cursor: pointer;
				display: block;
			}
			{{ addonId }} .page-4 .banner-btn .prevBtn {
				left: 5px;
			}
			{{ addonId }} .page-4 .banner-btn .nextBtn {
				right: 5px;
			}
			{{ addonId }} .page-4 .banner-btn a.nextBtn i {
				transform: rotateZ(180deg);
			}
			{{ addonId }} .page-4 .four-bt-p2 {
				font-size: 60px;
				color: white;
			}

			/* 第五页 */
			{{ addonId }} .page-5 .content {}
			{{ addonId }} .five-gun {
				float: left;
				width: 391px;
				height: 451px;
				margin-left: 405px;
				margin-top: -260px;
			}
			{{ addonId }} .five-class {
				width: 235px;
				height: 206px;
				float: right;
				margin-top: -130px;
				margin-right: 60px;
			}
			{{ addonId }} .five-class-btn {
				width: 100%;
				height: 50px;
				border: none;
				border: 1px solid white;
				background: none;
				margin-bottom: 20px;
				font-size: 20px;
				color: white;
				cursor: pointer;
			}
			{{ addonId }} .five {
				float: left;
				width: 100%;
				height: 960px;
				background: rgb(254, 216, 66);
			}
			{{ addonId }} .five-main {
				width: 1200px;
				height: 430px;
				margin: 0 auto;
			}
			{{ addonId }} .five-bt {
				float: left;
				width: 1200px;
				height: 90px;
				padding-top: 20px;
				margin-top: 200px;
				margin-left: 90px;
			}
			{{ addonId }} .six-bt {
				float: left;
				width: 200px;
				height: 90px;
				text-align: center;
				margin-top: 170px;
			}
			{{ addonId }} .five-box {
				float: right;
				width: 1000px;
				height: 437px;
			}
			{{ addonId }} .five-bt-p {
				font-size: 23px;
				color: white;
			}
			{{ addonId }} .six-bt-p {
				font-size: 23px;
				color: black;
			}
			{{ addonId }} .five-bt-p2 {
				font-size: 40px;
				color: rgb(50, 182, 81);
			}
			{{ addonId }} .five-pn {
				width: 100px;
				height: 26px;
				float: right;
				margin-top: -500px;
			}
			{{ addonId }} .five-pnt {
				width: 100px;
				height: 26px;
				float: right;
				margin-top: -100px;
			}
			{{ addonId }} .five-pnt5 {
				width: 100px;
				height: 26px;
				float: right;
			}
			{{ addonId }} .five-div {
				width: 300px;
				height: 200px;
				float: left;
				margin-left: 30px;
				margin-bottom: 30px;
				position: relative;
			}
			{{ addonId }} .five-div img {
				width: 300px;
				height: 267px;
				border-radius: 10px;
				display: block;
			}
			{{ addonId }} .five-div-text {
				width: 100%;
				height: 100%;
				opacity: 0;
				position: absolute;
				background: rgba(0, 0, 0, 0.5);
				padding-left: 10px;
				padding-right: 10px;
				border-radius: 10px;
			}
			{{ addonId }} .five-div img {
				width: 100% !important;
				height: 100% !important;
			}
			{{ addonId }} .five-div-text:hover {
				opacity: 1;
				transition-duration: 1s;
			}
			{{ addonId }} .five-div-text-p {
				font-size: 18px;
				color: white;
				display: block;
				margin-top: 20px;
			}
			{{ addonId }} .five-div-text-p2 {
				font-size: 13px;
				color: white;
				display: block;
				line-height: 30px;
			}
			{{ addonId }} .five-div-text-p3 {
				font-size: 18px;
				color: white;
				display: block;
			}
			{{ addonId }} .five-div-text-p3 a {
				color: white;
			}
			{{ addonId }} .five-div-text-p3 a:hover {
				color: rgb(50, 182, 81);
			}

			/* 第六页 */
			{{ addonId }} .page-6 .content {
				padding: 0 120px;
			}
			{{ addonId }} .page-6 .six {
				float: left;
				width: 100%;
				height: 960px;
				background: white;
			}
			{{ addonId }} .page-6 .six-main {
				width: 1200px;
				height: 550px;
				margin: 190px auto;
			}
			{{ addonId }} .page-6 .six-main-left {
				float: left;
				width: 500px;
				height: 500px;
			}
			{{ addonId }} .page-6 .six-main-right {
				float: right;
				width: 400px;
				height: 400px;
			}
			{{ addonId }} .page-6 .six-main-right img {
				width: 400px;
				height: 400px;
			}
			{{ addonId }} .page-6 .six-main-left-p {
				font-size: 35px;
				color: rgb(19, 19, 19);
			}
			{{ addonId }} .page-6 .six-main-left-p2 {
				font-size: 60px;
				color: rgb(50, 182, 81);
				display: block;
			}
			{{ addonId }} .page-6 .six-main-left-p3 {
				font-size: 16px;
				color: black;
				display: block;
				line-height: 30px;
			}
			{{ addonId }} .page-6 .six-main-left-p4 {
				font-size: 16px;
				color: rgb(47, 47, 47);
				line-height: 25px;
			}
			{{ addonId }} .page-6 .six-btn {
				float: left;
				width: 140px;
				height: 52px;
				font-size: 16px;
				color: white;
				border: none;
				outline: none;
				border-radius: 10px;
				background: rgb(50, 182, 81);
				cursor: pointer;
				margin-top: 15px;
			}
			{{ addonId }} .page-6 .six-lian {
				border: 1px dashed white;
				padding-top: 10px;
				padding-bottom: 8px;
				padding-left: 30px;
				padding-right: 30px;
			}

			/* 第七页 */
			{{ addonId }} .page-7 {
				display: block;
			}
			{{ addonId }} .page-7 .content {
				height: calc(100vh - 14.1%);
				margin: auto;
				display: flex;
				align-items: center;
				justify-content: space-around;
			}
			{{ addonId }} .page-7 .sever-main-left {
				float: left;
				width: 386px;
				height: 386px;
				text-align: center;
				line-height: 60px;
			}
			{{ addonId }} .page-7 .sever-main-right {
				float: right;
				width: 741px;
				height: 386px;
			}
			{{ addonId }} .page-7 .sever-main-right .container {
				width: 100%;
				height: 100%;
			}
			{{ addonId }} .page-7 .sever-main-left-p {
				font-size: 28px;
			}
			{{ addonId }} .page-7 .sever-main-left-p2 {
				font-size: 45px;
			}
			{{ addonId }} .page-7 .sever-main-left-p3 {
				font-size: 20px;
			}
			{{ addonId }} .page-7 .sever-main-left-p4 {
				font-size: 45px;
			}
			{{ addonId }} .page-7 .sever-main-left-p5 {
				font-size: 15px;
				color: rgb(255, 255, 255);
				background: #32b651;
				border-radius: 20px;
				padding-left: 10px;
				padding-right: 10px;
				padding-bottom: 4px;
			}
			{{ addonId }} .page-7 .seven-btn {
				width: 330px;
				height: 60px;
				border: none;
				outline: none;
				border-radius: 10px;
				background: rgb(50, 182, 81);
				font-size: 18px;
				color: white;
				cursor: pointer;
			}
			{{ addonId }} .page-7 .bufu {
				width: 450px;
				height: 70px;
				margin: auto;
				margin-top: auto;
				margin-bottom: auto;
				position: relative;
				top: 50%;
				transform: translateY(-50%);
			}
			{{ addonId }} .page-7 .footer-b {
				width: 100%;
				height: 14.1%;
				background: rgb(85, 85, 85);
				text-align: center;
				line-height: 23px;
				position: relative;
			}
			{{ addonId }} .page-7 .footer-p {
				font-size: 12px;
				color: rgb(189, 189, 189);
			}
			{{ addonId }} .page-7 .footer-p2 {
				font-size: 12px;
				color: white;
			}
			{{ addonId }} .page-7 .footer-p a {
				color: rgb(189, 189, 189);
				display: inline-block;
			}
			{{ addonId }} .page-7 .footer-p2 a:hover {
				color: white;
				text-decoration: none;
			}
			{{ addonId }} .page-7 .footer-p2 a {
				color: white;
			}
        </style>
        <div class="swiper-container full-page" id="full-page">
			<div class="swiper-wrapper">
			    <# var page01_hide = data.page01_hide || 0;
                if(slide_page == "page01" && page01_hide == 0) {
			        var page01_image_item = data.page01_image_item || [{
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/dd28120daea31ff715b41cde162e580d.jpg\'
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/63adf86827f4bbe06594873e4e52e9f5.jpg\'
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/8c617a2b61cb943f0fc94382060587e7.jpg\'
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/a6d5c68adedd7a90490bd29b22640c64.jpg\'
                    }];
			    #>
				<div class="swiper-slide">
					<div class="page page-1 swiper-no-swiping">
						<div class="swiper-container page1-swiper" style="width: 100%;height: 100%;">
							<div class="swiper-wrapper">
								<# _.each(page01_image_item, function (item, key){
                                    var style = "background-image: url(" + item.slider_img + ");";
                                #>
                                    <div class="swiper-slide" style="{{style}}"></div>
                                <# }); #>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page02_hide = data.page02_hide || 0;
				if(slide_page == "page02" && page02_hide == 0) {
				    var page02_right_title = data.page02_right_title || "专业的幼教";
				    var page02_right_desc = data.page02_right_desc || "践行者";
				    var p2_content = "<p style=\'text-indent:32px;\'>";
                        p2_content += "沃德兰·东大教育集团是致力于幼儿教育、培训，幼儿师资培训，幼儿教育用品等多领域发展的综合性教育机构，目前在中国拥有多家直营幼儿园。</p>";
                        p2_content += "<p style=\'text-indent:32px;\'>";
                        p2_content += "沃德兰·东大教育集团历经二十余年的辛勤耕耘，不断整合积淀了丰富的教育资源，与华东师范大学、北京师范大学、陈鹤琴教育思想研究会、复旦大学出版社等众多知名院校、专家学者，开展教学教研实践活动，研发出全套《幼儿园体验课程活动方案指导手册》（教师参考用书）以及《五大领域体验活动手册》（幼儿操作用书）、《活教育中的山西文化丛书》等，是促进学、研、产、用相结合的专业幼教服务机构。";
                        p2_content += "</p>";
                        p2_content += "<p style=\'text-indent:32px;\'>";
                        p2_content += "沃德兰·东大教育集团多次荣获“中国民办十大知名品牌教育机构”、“全国十佳特色示范单位”、“全国学前教育先进单位”、“全国民办教育先进集体”、“全国特色民办幼儿园”、“最具社会责任感教育机构”、“最具企业竞争力教育机构”、“课程创新新锐奖”、“幼教中国影响力（学习力）机构奖”等多项殊荣。";
                        p2_content += "</p>";
                        p2_content += "<p style=\'text-indent:32px;\'>";
                        p2_content += "沃德兰·东大教育集团与恒大、富力、绿地、万科、保利等国内多家房地产公司合作，通过“教育带动地产，地产联动教育”进行强强联手，带动了当地的幼教事业，达到三赢的社会效果。";
                        p2_content += "</p>";
				    var page02_right_content = data.page02_right_content || p2_content;
				#>
				<div class="swiper-slide">
					<div class="page page-2 swiper-no-swiping">
						<!-- 左边 -->
						<div class="page-item"></div>
						<!-- 视频弹窗 -->
						<div class="theme-popover" style="display:none;">
							<div class="theme-poptit">
								<a href="javascript:;" title="关闭" class="close" onclick="hideVideo()">×</a>
							</div>
							<iframe width="100%" height="100%" align="middle"
								allowfullscreen=""></iframe>
						</div>
						<div class="page-item right-content">
							<div class="two-right-box ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
								<span class="two-right-box-p">{{ page02_right_title }}</span><br>
								<span class="two-right-box-p2">{{ page02_right_desc }}</span><br>
								<div class="two-right-box-p3">
									{{{ page02_right_content }}}
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page03_hide = data.page03_hide || 0;
				if(slide_page == "page03" && page03_hide == 0) {
				    var page03_slider_title = data.page03_slider_title || "沃德兰 · 九大优势";
                    var page03_slider_desc = data.page03_slider_desc || "COMPETITIVE EDGE";
                    var page03_list_item = data.page03_list_item || [{
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    },
                    {
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    },
                    {
                        title: \'直营式管理\',
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png\',
                        slider_color: \'rgb(69,84,105)\',
                        slider_border_color: \'rgb(199,203,210)\',
                        slider_border_color_hover: \'rgb(146,154,169)\',
                        title_link: \'\',
                    }];
				#>
				<div class="swiper-slide">
					<div class="page page-3 swiper-no-swiping">
						<div class="content ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
							<div class="two-main-bt">
								<span class="two-main-bt-p">{{ page03_slider_title }}</span><br>
								<span class="two-main-bt-p2">{{ page03_slider_desc }}</span>
							</div>
							<div class="in-jmhz-there-box">
                                <# _.each(page03_list_item, function (item, key){
                                #>
                                    <div class="in-jmhz-there-div">
                                        <div class="in-jmhz-there-bt">
                                            <div class="in-jmhz-there-bt-icon">
                                                <img src=\'{{ item.slider_img }}\'>
                                            </div>
                                            <a href="" target="_blank">{{ item.title }}</a>
                                        </div>
                                    </div>
                                <# }); #>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page04_hide = data.page04_hide || 0;
				if(slide_page == "page04" && page04_hide == 0) {
				    var page04_slider_title = data.page04_slider_title || "沃德兰 · 团队展示";
                    var page04_slider_desc = data.page04_slider_desc || "TEAM";
                    var page04_image_item = data.page04_image_item || [
                        {
                            slider_img: "https://oss.lcweb01.cn/joomla/20211216/64479efd0311661308e9b0eecba54b45.png",
                            slider_link: "",
                        },
                        {
                            slider_img: "https://oss.lcweb01.cn/joomla/20211216/3c61c47e5fe291bb8cbb47d57ae3447f.png",
                            slider_link: "",
                        },
                        {
                            slider_img: "https://oss.lcweb01.cn/joomla/20211216/c265f1017a1cf78c6007b662ab1edab1.png",
                            slider_link: "",
                        },
                    ];
                    var page04_btn_item = data.page04_btn_item || [
                        {
                            title: "运营团队",
                            slider_link: "",
                        },
                        {
                            title: "教研团队",
                            slider_link: "",
                        },
                        {
                            title: "后勤团队",
                            slider_link: "",
                        },
                    ];
				#>
				<div class="swiper-slide">
					<div class="page page-4 swiper-no-swiping">
						<div class="content ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
							<div class="right right5">
								<div class="five-bt">
									<span class="five-bt-p">{{ page04_slider_title }}</span><br>
									<span class="four-bt-p2">{{ page04_slider_desc }}</span>
								</div>
								<div class="five-gun">
									<div class="swiper-container" id="page-4-swiper">
										<div class="swiper-wrapper">
                                            <# _.each(page04_image_item, function (item, key){
                                            #>
                                                <div class="swiper-slide"><a href="" target="_blank"><img
                                                    src=\'{{ item.slider_img }}\'></a>
											    </div>
                                            <# }); #>
										</div>
										<div class="banner-btn">
											<a href="javascript:;" class="prevBtn"><i></i></a>
											<a href="javascript:;" class="nextBtn"><i></i></a>
										</div>
									</div>
								</div>
								<div class="five-class">
								    <# _.each(page04_btn_item, function (item, key){
                                    #>
                                        <a href="" target="_blank"><button class="five-class-btn">{{ item.title }}</button></a>
                                    <# }); #>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page05_hide = data.page05_hide || 0;
				if(slide_page == "page05" && page05_hide == 0) {
				    var page05_ani_title01 = data.page05_ani_title01 || "沃德兰 · 新闻活动";
				    var page05_ani_title02 = data.page05_ani_title02 || "NEWS";

				#>
				<div class="swiper-slide">
					<div class="page page-5 swiper-no-swiping">
						<div class="content ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
							<div class="five-main">
								<div class="six-bt">
									<span class="six-bt-p">{{ page05_ani_title01 }}</span><br>
									<span class="five-bt-p2">{{ page05_ani_title02 }}</span>
								</div>
								<div class="five-box">
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">沃德兰·东大托育中心托育体验日...</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/c92bc56f29c2ee8cbedb29757163ca31.jpg">
									</div>
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">优师资队伍建设 促教育质量提升</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/4699f27d0f3c8cbcda0fb1a78924478c.jpg">
									</div>
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">聚焦五星验收 品牌彰显实力</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/53c176a70e46d946c7b040c54df480d2.jpg">
									</div>
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">秋冬交替，天气转凉，沃德兰·东...</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/1e0604379bee8b1d84df699e26cf7adf.png">
									</div>
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">沃德兰东大教育集团第三届教育节...</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/6ef92c6683e87ad22bc22b36491799e2.jpg">
									</div>
									<div class="five-div">
										<div class="five-div-text">
											<span class="five-div-text-p">一封来自毕业幼儿的感谢信，道出...</span><br>
											<span class="five-div-text-p2"></span><br>
											<span class="five-div-text-p3"><a href="">more
													&gt;</a></span><br>
										</div>
										<img
											src="https://oss.lcweb01.cn/joomla/20211216/fbf4e9c8cc7055a7d5076b670bdd2c28.jpg">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page06_hide = data.page06_hide || 0;
				if(slide_page == "page06" && page06_hide == 0) {
				    var page06_slider_title = data.page06_slider_title || "沃德兰 · 联盟合作";
				    var page06_slider_desc = data.page06_slider_desc || "JOIN IN";
				    var page06_slider_content = data.page06_slider_content || "学前教育牵动着全社会，沃德兰国际教育致力于：“适应儿童发展、遵循自然本性、实现原本教育、养成高尚品格”的教育理念，确保优秀的教育质量、优秀的教育服务。现特面向社会诚招教育合作伙伴，诚邀有实力、有经验、有成熟课程体系的机构联盟发展，共谋幼教事业发展。";
				    var page06_slider_tel = data.page06_slider_tel || "0351-3239017 0351-3238128";
				    var page06_slider_btn = data.page06_slider_btn || "联系我们";
				    var page06_slider_img_right = data.page06_slider_img_right || "https://oss.lcweb01.cn/joomla/20211216/01d428b32f89031a618c19dad7e56ea8.png";
				#>
				<div class="swiper-slide">
					<div class="page page-6">
						<div class="content swiper-no-swiping">
							<div class="middle">
								<div class="six-main-left ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<span class="six-main-left-p">{{ page06_slider_title }}</span><br>
									<span class="six-main-left-p2">{{ page06_slider_desc }}</span><br>
									<span class="six-main-left-p3">{{ page06_slider_content }}</span><br>
									<span class="six-main-left-p4">{{ page06_slider_tel }}</span><br>
									<button class="six-btn"><span class="six-lian">{{ page06_slider_btn }}</span></button>
								</div>
								<div class="six-main-right ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img
										src=\'{{ page06_slider_img_right }}\'>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# var page07_hide = data.page07_hide || 0;
				if(slide_page == "page07" && page07_hide == 0) {
				    var page07_left_title01 = data.page07_left_title01 || "联系我们，关注我们";
				    var page07_left_title02 = data.page07_left_title02 || "Contact Us";
				    var page07_left_title03 = data.page07_left_title03 || "我们始终愿意俯下身聆听您的声音";
				    var page07_left_title04 = data.page07_left_title04 || "400-***-****";
				    var page07_left_title05 = data.page07_left_title05 || "www.xxxxxxxx.com";
				    var page07_content = data.page07_content || "<a>沃德兰·东大教育集团</a> | <a>新闻活动</a> | <a>联盟合作</a> | <a>招贤纳士</a>";
				    var page07_record_g = data.page07_record_g || "晋公网安备 14010502050981号";
				    var page07_record = data.page07_record || "晋ICP备17001037号-1";
				    var page07_copyright = data.page07_copyright || "山西资海科技科技有限公司";
				#>
				    <div class="swiper-slide">
                        <div class="page page-7">
                            <div class="content swiper-no-swiping">
                                <div class="sever-main-left ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                    <span class="sever-main-left-p">{{ page07_left_title01 }}</span><br>
                                    <span class="sever-main-left-p2">{{ page07_left_title02 }}</span> <br>
                                    <span class="sever-main-left-p3">{{ page07_left_title03 }}</span><br>
                                    <span class="sever-main-left-p4">{{ page07_left_title04 }}</span><br>
                                    <span class="sever-main-left-p5">{{ page07_left_title05 }}</span><br>
                                </div>
                                <!-- 地图 -->
                                <div class="sever-main-right ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                    <!--<div id="container" class="container"></div>-->
                                    <img src="https://oss.lcweb01.cn/joomla/20211218/fbfd8e5dce9b792452194701920916f7.png" />
                                </div>
                            </div>
                            <!-- 底部 -->
                            <div class="footer-b swiper-no-swiping">
                                <div class="bufu">
                                    <p class="footer-p2">
                                        {{{ page07_content }}}
                                    </p>
                                    <div class="footer-p"
                                        style="display:block;width:350px;height:20px;margin:0 auto; padding:0;">
                                        <a target="_blank" href=""
                                            style="text-decoration:none;height:20px;line-height:20px;">
                                            <img src="https://oss.lcweb01.cn/joomla/20211015/d2b288ec8fca02e2036d473db62f96d6.png"
                                                style="float:left;">
                                            <p style="float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px;">
                                                {{ page07_record_g }}</p>
                                        </a>
                                        <a href="">
                                            <p style="float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px;">
                                                {{ page07_record }}</p>
                                        </a>
                                    </div>
                                    <span class="footer-p">技术支持：<a href="">{{ page07_copyright }}</a></span>
                                </div>
                            </div>
                        </div>
                    </div>
				<# } #>
			</div>
			<!-- Add Pagination -->
			<div class="swiper-pagination"></div>
			<!-- Add Arrows -->
			<div class="swiper-button-next">
			    <img src="https://oss.lcweb01.cn/joomla/20211207/a6cc6a4bda51f3259dffc9c4aa3dd9bd.png">
			</div>
			<!-- <div class="swiper-button-prev"></div> -->
		</div>
        ';
        return $output;
    }
}