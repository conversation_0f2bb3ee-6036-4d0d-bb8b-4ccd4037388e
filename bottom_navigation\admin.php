<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct access
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
  array(
    'type' => 'general',
    'addon_name' => 'bottom_navigation',
    'title' => '网站底部',
    'desc' => '在页面底部显示导航链接及联系信息等',
    'category' => '导航',
    'attr' => array(
      'general' => array(
        'admin_label' => array(
          'type' => 'text',
          'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
          'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
          'std' => '网站底部'
        ),
        // 导航布局
        'nav_layout' => array(
          'type' => 'thumbnail',
          'title' => '布局',
          'desc' => '选择布局',
          'values' => array(
            'theme1' => 'https://oss.lcweb01.cn/jzt/255/image/20250829/eaabd7d36852f9854e8e07638b74cf1d.png',
            'theme2' => 'https://oss.lcweb01.cn/jzt/255/image/20250829/ae991044ec583a355e7d3fcd90b23a78.png',
            'theme3' => 'https://oss.lcweb01.cn/jzt/255/image/20250829/17afb0194e61695d69abf20f30964abe.png',
            'theme4' => 'https://oss.lcweb01.cn/jzt/255/image/20250901/2e1d7b43ef61bf09fd768eece2dbd7e8.png',
            'theme5' => 'https://oss.lcweb01.cn/jzt/255/image/20250901/d1b742f25a52d5730fca3ab0f3ac2613.png',
            'theme6' => 'https://oss.lcweb01.cn/jzt/255/image/20250901/29e55b94ee934fdf1aede405e326c4b3.png',
          ),
          'std' => 'theme1',
        ),

        // ========== 导航栏设置 ==========
        'nav_settings_button' => array(
          'type' => 'buttons',
          'title' => '导航栏设置',
          'std' => 'nav_style',
          'tabs' => true,
          'values' => array(
            array(
              'label' => '导航样式',
              'value' => 'nav_style'
            ),
            array(
              'label' => '布局配置',
              'value' => 'nav_layout_config'
            ),
          ),
          'depends' => array(array('nav_layout', '=', 'theme1')),
        ),
        // 导航样式设置
        'theme1_nav_bg_color' => array(
          'type' => 'color',
          'title' => '导航栏背景色',
          'desc' => '设置顶部导航栏的背景颜色',
          'std' => '#94070A',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_nav_text_color' => array(
          'type' => 'color',
          'title' => '导航栏文字颜色',
          'desc' => '设置导航栏文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_nav_hover_color' => array(
          'type' => 'color',
          'title' => '导航悬停颜色',
          'desc' => '设置导航项悬停时的文字颜色',
          'std' => '#cccccc',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_nav_font_size' => array(
          'type' => 'slider',
          'title' => '导航文字大小',
          'desc' => '设置导航文字字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 24,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_nav_font_weight' => array(
          'type' => 'select',
          'title' => '导航文字粗细',
          'desc' => '设置导航文字字体粗细',
          'values' => array(
            'normal' => '正常',
            'bold' => '粗体',
            '300' => '细体',
            '500' => '中等',
            '600' => '半粗',
            '700' => '粗体',
          ),
          'std' => 'normal',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_separator_color' => array(
          'type' => 'color',
          'title' => '分隔线颜色',
          'desc' => '设置导航项之间分隔线的颜色',
          'std' => 'rgba(255, 255, 255, 0.2)',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        'theme1_separator_height' => array(
          'type' => 'slider',
          'title' => '分隔线高度',
          'desc' => '设置导航项之间分隔线的高度',
          'std' => 96,
          'min' => 20,
          'max' => 150,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_style')
          ),
        ),

        // 导航布局配置
        'theme1_nav_height' => array(
          'type' => 'slider',
          'title' => '导航栏高度',
          'desc' => '设置导航栏高度',
          'std' => 124,
          'min' => 60,
          'max' => 200,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_layout_config')
          ),
        ),

        'theme1_nav_padding' => array(
          'type' => 'slider',
          'title' => '导航项内边距',
          'desc' => '设置导航项左右内边距',
          'std' => 40,
          'min' => 10,
          'max' => 80,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('nav_settings_button', '=', 'nav_layout_config')
          ),
        ),

        // ========== 底部区域设置 ==========
        'footer_settings_button' => array(
          'type' => 'buttons',
          'title' => '底部区域设置',
          'std' => 'footer_style',
          'tabs' => true,
          'values' => array(
            array(
              'label' => '底部样式',
              'value' => 'footer_style'
            ),
            array(
              'label' => 'Logo设置',
              'value' => 'footer_logo'
            ),
            array(
              'label' => '内容设置',
              'value' => 'footer_content'
            ),
          ),
          'depends' => array(array('nav_layout', '=', 'theme1')),
        ),

        // 底部样式设置
        'theme1_footer_bg_color' => array(
          'type' => 'color',
          'title' => '底部背景色',
          'desc' => '设置底部区域背景颜色',
          'std' => '#302A29',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_style')
          ),
        ),

        'theme1_footer_text_color' => array(
          'type' => 'color',
          'title' => '底部文字颜色',
          'desc' => '设置底部信息文字颜色',
          'std' => 'rgba(255, 255, 255, 0.5)',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_style')
          ),
        ),

        'theme1_footer_font_size' => array(
          'type' => 'slider',
          'title' => '底部文字大小',
          'desc' => '设置底部信息文字大小',
          'std' => 14,
          'min' => 10,
          'max' => 20,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_style')
          ),
        ),

        'theme1_footer_padding' => array(
          'type' => 'padding',
          'title' => '底部内边距',
          'desc' => '设置底部区域内边距',
          'std' => '30px 0',
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_style')
          ),
        ),

        'theme1_info_separator_margin' => array(
          'type' => 'slider',
          'title' => '信息分隔符间距',
          'desc' => '设置底部信息分隔符左右间距',
          'std' => 15,
          'min' => 5,
          'max' => 30,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_style')
          ),
        ),

        // Logo设置
        'theme1_logo_image' => array(
          'type' => 'media',
          'title' => 'Logo图片',
          'desc' => '选择底部Logo图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/9f02ce6979aeac873ba81ee75af6cd5f.png',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_logo')
          ),
        ),

        'theme1_logo_height' => array(
          'type' => 'slider',
          'title' => 'Logo高度',
          'desc' => '设置底部Logo图片高度',
          'std' => 65,
          'min' => 30,
          'max' => 120,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_logo')
          ),
        ),

        // 底部内容设置
        'theme1_footer_content' => array(
          'type' => 'editor',
          'title' => '底部信息',
          'desc' => '设置底部信息内容，支持HTML格式',
          'std' => '版权所有：山西龙采科技有限公司 &nbsp;&nbsp;&nbsp;&nbsp; 地址：太原市高新技术开发区晋阳街南巷1号 &nbsp;&nbsp;&nbsp;&nbsp; 备案号：晋ICP备11006061号-1 &nbsp;&nbsp;&nbsp;&nbsp; 邮编：030000',
          'depends' => array(
            array('nav_layout', '=', 'theme1'),
            array('footer_settings_button', '=', 'footer_content')
          ),
        ),

        // ========== Theme2 配置 ==========
        'theme2_settings_button' => array(
          'type' => 'buttons',
          'title' => '布局2设置',
          'std' => 'theme2_style',
          'tabs' => true,
          'values' => array(
            array(
              'label' => '样式设置',
              'value' => 'theme2_style'
            ),
            array(
              'label' => '内容设置',
              'value' => 'theme2_content'
            ),
          ),
          'depends' => array(array('nav_layout', '=', 'theme2')),
        ),

        // Theme2 样式设置
        'theme2_bg_color' => array(
          'type' => 'color',
          'title' => '背景颜色',
          'desc' => '设置底部区域背景颜色',
          'std' => '#195DB3',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_text_color' => array(
          'type' => 'color',
          'title' => '文字颜色',
          'desc' => '设置文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_nav_hover_color' => array(
          'type' => 'color',
          'title' => '导航悬停颜色',
          'desc' => '设置导航项悬停时的文字颜色',
          'std' => '#cccccc',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_nav_font_size' => array(
          'type' => 'slider',
          'title' => '导航文字大小',
          'desc' => '设置导航文字字体大小',
          'std' => 18,
          'min' => 12,
          'max' => 24,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_logo_height' => array(
          'type' => 'slider',
          'title' => 'Logo高度',
          'desc' => '设置Logo图片高度',
          'std' => 68,
          'min' => 40,
          'max' => 120,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_qr_size' => array(
          'type' => 'slider',
          'title' => '二维码尺寸',
          'desc' => '设置二维码图片尺寸',
          'std' => 130,
          'min' => 80,
          'max' => 200,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_nav_padding' => array(
          'type' => 'padding',
          'title' => '导航项内边距',
          'desc' => '设置导航项左右内边距',
          'std' => '0 30px 0 30px',
          'min' => 0,
          'max' => 100,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_separator_height' => array(
          'type' => 'slider',
          'title' => '分隔线高度',
          'desc' => '设置导航项之间分隔线的高度',
          'std' => 20,
          'min' => 10,
          'max' => 40,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_max_width' => array(
          'type' => 'slider',
          'title' => '内容区最大宽度(PC端)',
          'desc' => '设置PC端内容区域的最大宽度',
          'std' => 1300,
          'min' => 800,
          'max' => 2000,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_content_margin' => array(
          'type' => 'margin',
          'title' => '内容区外边距(平板/手机)',
          'desc' => '设置平板和手机端内容区域的外边距',
          'std' => array(
            'md' => '0 20px 0 20px',
            'sm' => '0 15px 0 15px',
            'xs' => '0 15px 0 15px'
          ),
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        // 底部信息样式设置
        'theme2_company_name_font_size' => array(
          'type' => 'slider',
          'title' => '公司名称文字大小',
          'desc' => '设置公司名称文字字体大小',
          'std' => 18,
          'min' => 12,
          'max' => 28,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_company_name_color' => array(
          'type' => 'color',
          'title' => '公司名称文字颜色',
          'desc' => '设置公司名称文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_contact_font_size' => array(
          'type' => 'slider',
          'title' => '联系信息文字大小',
          'desc' => '设置联系信息文字字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 24,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_contact_color' => array(
          'type' => 'color',
          'title' => '联系信息文字颜色',
          'desc' => '设置联系信息文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_footer_bottom_font_size' => array(
          'type' => 'slider',
          'title' => '版权信息文字大小',
          'desc' => '设置底部版权信息文字字体大小',
          'std' => 14,
          'min' => 10,
          'max' => 20,
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_footer_bottom_color' => array(
          'type' => 'color',
          'title' => '版权信息文字颜色',
          'desc' => '设置底部版权信息文字颜色',
          'std' => 'rgba(255, 255, 255, 0.8)',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        'theme2_footer_border_color' => array(
          'type' => 'color',
          'title' => '版权区域上描边颜色',
          'desc' => '设置版权区域上方分隔线的颜色',
          'std' => 'rgba(255, 255, 255, 0.2)',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_style')
          ),
        ),

        // Theme2 内容设置
        'theme2_logo_image' => array(
          'type' => 'media',
          'title' => 'Logo图片',
          'desc' => '选择Logo图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/9f02ce6979aeac873ba81ee75af6cd5f.png',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_company_name' => array(
          'type' => 'text',
          'title' => '公司名称',
          'desc' => '设置公司名称',
          'std' => '山西龙采科技有限公司',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_address' => array(
          'type' => 'text',
          'title' => '公司地址',
          'desc' => '设置公司地址',
          'std' => '太原市高新技术开发区晋阳街南巷1号',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_phone' => array(
          'type' => 'text',
          'title' => '联系电话',
          'desc' => '设置联系电话',
          'std' => '0351-7566017',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_email' => array(
          'type' => 'text',
          'title' => '邮箱地址',
          'desc' => '设置邮箱地址',
          'std' => '<EMAIL>',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_wechat_qr' => array(
          'type' => 'media',
          'title' => '微信二维码',
          'desc' => '选择微信二维码图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/c476df963dfd0651101794263fe154db.png',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_mobile_qr' => array(
          'type' => 'media',
          'title' => '手机二维码',
          'desc' => '选择手机二维码图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/c476df963dfd0651101794263fe154db.png',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        'theme2_copyright' => array(
          'type' => 'editor',
          'title' => '版权信息',
          'desc' => '设置底部版权信息，支持HTML格式',
          'std' => '版权所有：山西龙采科技有限公司 &nbsp;&nbsp;&nbsp;&nbsp; 技术支持：山西龙采科技有限公司 &nbsp;&nbsp;&nbsp;&nbsp; 备案号：晋ICP备11006061号-1',
          'depends' => array(
            array('nav_layout', '=', 'theme2'),
            array('theme2_settings_button', '=', 'theme2_content')
          ),
        ),

        // ========== Theme3 配置 ==========
        'theme3_settings_button' => array(
          'type' => 'buttons',
          'title' => '布局3设置',
          'std' => 'theme3_style',
          'tabs' => true,
          'values' => array(
            array(
              'label' => '样式设置',
              'value' => 'theme3_style'
            ),
            array(
              'label' => '内容设置',
              'value' => 'theme3_content'
            ),
          ),
          'depends' => array(array('nav_layout', '=', 'theme3')),
        ),

        // Theme3 样式设置
        'theme3_bg_image' => array(
          'type' => 'media',
          'title' => '背景图片',
          'desc' => '设置底部区域背景图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/8745084ba45b3ecf442f7e8e3c40eccd.jpeg',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_max_width' => array(
          'type' => 'slider',
          'title' => '内容区最大宽度',
          'desc' => '设置内容区域的最大宽度',
          'std' => 1400,
          'min' => 1000,
          'max' => 2000,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_padding' => array(
          'type' => 'padding',
          'title' => '底部内边距',
          'desc' => '设置底部区域内边距',
          'std' => '60px 0 30px',
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        // 标语样式设置
        'theme3_slogan_font_size' => array(
          'type' => 'slider',
          'title' => '标语文字大小',
          'desc' => '设置标语文字字体大小',
          'std' => 29,
          'min' => 20,
          'max' => 40,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_slogan_color' => array(
          'type' => 'color',
          'title' => '标语文字颜色',
          'desc' => '设置标语文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_english_font_size' => array(
          'type' => 'slider',
          'title' => '英文标语大小',
          'desc' => '设置英文标语字体大小',
          'std' => 19,
          'min' => 12,
          'max' => 28,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_separator_color' => array(
          'type' => 'color',
          'title' => '标语分隔线颜色',
          'desc' => '设置标语下方分隔线颜色',
          'std' => 'rgba(255, 255, 255, 0.2)',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        // 栏目样式设置
        'theme3_column_title_font_size' => array(
          'type' => 'slider',
          'title' => '栏目标题字体大小',
          'desc' => '设置栏目标题字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 24,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_column_title_color' => array(
          'type' => 'color',
          'title' => '栏目标题颜色',
          'desc' => '设置栏目标题文字颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_link_font_size' => array(
          'type' => 'slider',
          'title' => '链接文字大小',
          'desc' => '设置栏目链接文字大小',
          'std' => 16,
          'min' => 12,
          'max' => 20,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_link_color' => array(
          'type' => 'color',
          'title' => '链接文字颜色',
          'desc' => '设置栏目链接文字颜色',
          'std' => '#858585',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_link_hover_color' => array(
          'type' => 'color',
          'title' => '链接悬停颜色',
          'desc' => '设置栏目链接悬停时的颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        // 右侧区域样式设置
        'theme3_logo_height' => array(
          'type' => 'slider',
          'title' => 'Logo高度',
          'desc' => '设置Logo图片高度',
          'std' => 60,
          'min' => 30,
          'max' => 120,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_contact_label_font_size' => array(
          'type' => 'slider',
          'title' => '联系标签字体大小',
          'desc' => '设置"服务热线"标签字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 24,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_contact_label_color' => array(
          'type' => 'color',
          'title' => '联系标签颜色',
          'desc' => '设置"服务热线"标签颜色',
          'std' => 'rgba(255, 255, 255, 1)',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_phone_font_size' => array(
          'type' => 'slider',
          'title' => '电话号码字体大小',
          'desc' => '设置电话号码字体大小',
          'std' => 33,
          'min' => 20,
          'max' => 50,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_phone_color' => array(
          'type' => 'color',
          'title' => '电话号码颜色',
          'desc' => '设置电话号码颜色',
          'std' => '#005E3C',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_address_font_size' => array(
          'type' => 'slider',
          'title' => '地址文字大小',
          'desc' => '设置联系地址文字大小',
          'std' => 16,
          'min' => 12,
          'max' => 20,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_address_color' => array(
          'type' => 'color',
          'title' => '地址文字颜色',
          'desc' => '设置联系地址文字颜色',
          'std' => '#A1A1A1',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_social_label_font_size' => array(
          'type' => 'slider',
          'title' => '社交标签字体大小',
          'desc' => '设置"关注我们"标签字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 20,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_social_label_color' => array(
          'type' => 'color',
          'title' => '社交标签颜色',
          'desc' => '设置"关注我们"标签颜色',
          'std' => '#ffffff',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_social_icon_size' => array(
          'type' => 'slider',
          'title' => '社交图标尺寸',
          'desc' => '设置社交媒体图标尺寸',
          'std' => 52,
          'min' => 30,
          'max' => 80,
          'responsive' => true,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        // 底部版权样式设置
        'theme3_copyright_font_size' => array(
          'type' => 'slider',
          'title' => '版权信息字体大小',
          'desc' => '设置底部版权信息字体大小',
          'std' => 16,
          'min' => 12,
          'max' => 20,
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_copyright_color' => array(
          'type' => 'color',
          'title' => '版权信息颜色',
          'desc' => '设置底部版权信息颜色',
          'std' => '#989898',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        'theme3_copyright_border_color' => array(
          'type' => 'color',
          'title' => '版权区域上描边颜色',
          'desc' => '设置版权区域上方分隔线颜色',
          'std' => 'rgba(255, 255, 255, 0.2)',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_style')
          ),
        ),

        // Theme3 内容设置
        'theme3_chinese_slogan' => array(
          'type' => 'text',
          'title' => '中文标语',
          'desc' => '设置中文标语内容',
          'std' => '每个企业  都值得拥有科技产业',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_english_slogan' => array(
          'type' => 'text',
          'title' => '英文标语',
          'desc' => '设置英文标语内容',
          'std' => ' / Every family deserves change',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_logo_image' => array(
          'type' => 'media',
          'title' => 'Logo图片',
          'desc' => '选择Logo图片',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/9f02ce6979aeac873ba81ee75af6cd5f.png',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_contact_label' => array(
          'type' => 'text',
          'title' => '联系标签',
          'desc' => '设置联系方式标签文字',
          'std' => '服务热线',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_phone' => array(
          'type' => 'text',
          'title' => '联系电话',
          'desc' => '设置联系电话',
          'std' => '************',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_address' => array(
          'type' => 'text',
          'title' => '联系地址',
          'desc' => '设置联系地址',
          'std' => '联系地址：山西省太原市清控创新基地 控创新基地',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_social_label' => array(
          'type' => 'text',
          'title' => '社交标签',
          'desc' => '设置社交媒体标签文字',
          'std' => '关注我们：',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_social_icon1' => array(
          'type' => 'media',
          'title' => '社交图标1',
          'desc' => '选择第一个社交媒体图标',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/f16d94b7c39a061a5524bcd78e43337c.png',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_social_icon2' => array(
          'type' => 'media',
          'title' => '社交图标2',
          'desc' => '选择第二个社交媒体图标',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/3429b3d982d489d71d63dbc3932f5187.png',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_social_icon3' => array(
          'type' => 'media',
          'title' => '社交图标3',
          'desc' => '选择第三个社交媒体图标',
          'std' => 'https://oss.lcweb01.cn/jzt/255/image/20250828/acebedb985a2e3f5391bb53ffedaab38.png',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        'theme3_copyright' => array(
          'type' => 'editor',
          'title' => '版权信息',
          'desc' => '设置底部版权信息，支持HTML格式',
          'std' => 'CopyRight ◎ 2024&nbsp;&nbsp;&nbsp;&nbsp;山西龙采科技有限公司&nbsp;&nbsp;&nbsp;&nbsp;晋ICP备12001731号-2&nbsp;&nbsp;&nbsp;&nbsp;晋公网安备 42018502003958号',
          'depends' => array(
            array('nav_layout', '=', 'theme3'),
            array('theme3_settings_button', '=', 'theme3_content')
          ),
        ),

        // class
        'class' => array(
          'type' => 'text',
          'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
          'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
        ),
      ),
    ),
  )
);