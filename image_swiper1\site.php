<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonImage_swiper1 extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $isk2installed = self::isComponentInstalled('image_swiper1');

        if ($isk2installed === 0) {
            return '<div>出错了</div>';
        }
        $addonId = $this->addon->id;
        $items = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';


        $carousel_arrow_type = (isset($settings->carousel_arrow_type) && $settings->carousel_arrow_type) ? $settings->carousel_arrow_type : 'icon';
        $arrow_img = (isset($settings->arrow_img) && $settings->arrow_img) ? $settings->arrow_img : '/components/com_jwpagefactory/addons/image_swiper1/assets/images/normal_left.png';
        $introduce_content = (isset($settings->introduce_content) && $settings->introduce_content) ? $settings->introduce_content : '中华巴洛克历史文化保护街区有大量的中华巴洛克建筑群、特色院落和胡同，是珍贵的历史文化资源。
      街区曾经是哈尔滨三大商业区之一，有浓厚的商业氛围、人文氛围和生活氛围，具备休闲消费营商环境基础，以及文化旅游发展的巨大潜力。';


        $output='';

        $output.= '<div class="swiper-box" id="swiper_' . $addonId . '">';
//        旁边的轮播介绍的样式
        $output.='<div class="introduce">
                <div class="introduce-content">
                     '.$introduce_content.'
                </div>
            </div>';

        $output.='<div class="swiper-container-bg">
                <div class="swiper-container">
             <div class="swiper-wrapper">';


        foreach ($items as $key => $item) {
            $output .= '<div class="swiper-slide" data-id="' . $key . '">';
            if ($item->is_link === 1) {
                $target = $item->link_open_new_window === 1 ? '_blank' : '';
                $output .= '<a href="' . $item->image_carousel_img_link . '" target="' . $target . '">';
            }
            $output .= '<img src="' . $item->image_carousel_img . '" alt="">';


            $output .= '<div class="content-layout4">
                    <div class="first-title">' . sprintf("%02d", $key+1) . '</div>
                    <div class="subtitle">' . $item->item_subtitle . '</div>';
            if ($item->item_subtitle){
                $output.='<div class="split-layout"></div>';
            }
            $output .= '<div class="description">' . $item->item_description . '</div>
                </div>';


            if ($item->is_link === 1) {
                $output .= '</a>';
            }
            $output .= '</div>';
        }
        $output .= '
                </div>
            </div>
         </div>';


        $output.='<!-- Add Pagination -->
            <div class="swiper-pagination"></div>';
        if ($carousel_arrow_type === 'icon') {
            $output .= '
                    <!-- Add Arrows -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>';
        } else {
            $output .= '
                    <!-- Add Arrows -->
                <div class="swiper-button-next img">
                    <img src=\''. $arrow_img . '\' alt="">
                    
                </div>
                <div class="swiper-button-prev img">
                    <img src=\''. $arrow_img . '\' alt="">
                </div>
            </div>';
        }


        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;

//      能看到几个slides，手机端只能看到一个sliders
        $carousel_item_number = (isset($settings->carousel_item_number) && $settings->carousel_item_number) ? $settings->carousel_item_number : 3;






//      slide之间的距离
        $carousel_margin = (isset($settings->carousel_margin) && $settings->carousel_margin) ? $settings->carousel_margin : 0;

//        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;

        if ($carousel_autoplay === 1) {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        } else {
            $autoplay = 'false';
        }


//        分页器
        $pagination = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;
        if ($pagination === 1) {
            $pagination = '{
                    el: "#swiper_' . $addonId . ' .swiper-pagination",
                    clickable: true,
            }';
        } else {
            $pagination = '{}';
        }

//        翻页按钮
        $carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
        if ($carousel_arrow === 1) {
            $navigation = '{
                nextEl: "#swiper_' . $addonId . ' .swiper-button-next",
                prevEl: "#swiper_' . $addonId . ' .swiper-button-prev",
            }';
        } else {
            $navigation = '{}';
        }

//        按钮图片
        $arrow_img = (isset($settings->arrow_img) && $settings->arrow_img) ? $settings->arrow_img : '/components/com_jwpagefactory/addons/image_swiper/assets/images/normal_left.png';
        $arrow_img_active = (isset($settings->arrow_img_active) && $settings->arrow_img_active) ? $settings->arrow_img_active : '/components/com_jwpagefactory/addons/image_swiper/assets/images/active_left.png';



        $script = 'jQuery(document).ready(function($){
//        初始化swiper配置项
            function initSwiper(){
                let settings={
                    loop: true,
                    loopFillGroupWithBlank: true,
                    pagination: ' . $pagination . ',
                    autoplay: ' . $autoplay . ',                  
                    speed: ' . $carousel_speed . ',
                    navigation: '.$navigation.',
                    observer: true,
                    observeParents:true,
                    breakpoints: {
                        900: {
                          slidesPerView: 1,
                          spaceBetween: ' . $carousel_margin . ',
                          slidesPerGroup: 1,
                        },
                        1024: {
                          slidesPerView: 1,
                          spaceBetween: ' . $carousel_margin . ',
                          slidesPerGroup: 1,
                        },
                        1300: {
                          slidesPerView: ' . $carousel_item_number . ',
                          spaceBetween: ' . $carousel_margin . ',
                          slidesPerGroup: ' . $carousel_item_number . ',
                        },
                    }
                }
                let swiper = new Swiper("#jwpf-addon-' . $addonId . ' .swiper-container", settings);
                return swiper;
            }
            initSwiper();
//            屏幕改变大小再初始化一次
            window.onresize=function (){
                initSwiper();
            } 
            if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("jwpf-tab-pane")){
                     var Observer = new MutationObserver(function (mutations, instance) {
                        mutations.forEach(function (mutation) {
                          if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("active")){
                            initSwiper();
                          }
                        });
                     });
                     
                     Observer.observe($("#jwpf-addon-wrapper-'.$addonId.'").parent()[0], {
                        attributes: true
                     });
                }
                
//          当按钮为图片时，鼠标滑过改变图片
            $("#jwpf-addon-' . $addonId . ' .img").mouseenter(e=>{
                let img=$(e.currentTarget).find("img");
                $(img).attr("src" , "'.$arrow_img_active.'");
            }).mouseout(e=>{
                let img=$(e.currentTarget).find("img");
                $(img).attr("src" , "'.$arrow_img.'");
            })
        })';
        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;

//        轮播
        $addonId = '#swiper_' . $this->addon->id;


//        外部容器
        $carousel_left = (isset($settings->carousel_left) && $settings->carousel_left) ? $settings->carousel_left : 0;
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
            } else {
                $carousel_height = $settings->carousel_height;
            }
        } else {
            $carousel_height = '500';
        }


//        内部轮播区域

        if (isset($settings->carousel_view_height) && $settings->carousel_view_height) {
            if (is_object($settings->carousel_view_height)) {
                $carousel_view_height = $settings->carousel_view_height->md;
                $carousel_view_height_sm = $settings->carousel_view_height->sm;
                $carousel_view_height_xs = $settings->carousel_view_height->xs;
            } else {
                $carousel_view_height = $settings->carousel_view_height;
                $carousel_view_height_sm = $settings->carousel_view_height_sm;
                $carousel_view_height_xs = $settings->carousel_view_height_xs;
            }
        } else {
            $carousel_view_height = '500';
            $carousel_view_height_sm = '400';
            $carousel_view_height_xs = '300';
        }




        $carousel_view_left = '';
        if (isset($settings->carousel_view_left) && $settings->carousel_view_left) {
            if (is_object($settings->carousel_view_left)) {
                $carousel_view_left = $settings->carousel_view_left->md;
                $carousel_view_left_sm = $settings->carousel_view_left->sm;
                $carousel_view_left_xs = $settings->carousel_view_left->xs;
            } else {
                $carousel_view_left = $settings->carousel_view_left;
                $carousel_view_left_sm = $settings->carousel_view_left_sm;
                $carousel_view_left_xs = $settings->carousel_view_left_xs;
            }
        } else {
            $carousel_view_left = '0';
            $carousel_view_left_sm = '0';
            $carousel_view_left_xs = '0';
        }




        $height=$carousel_view_height.'px';
        $height_sm=$carousel_view_height_sm.'px';
        $height_xs=$carousel_view_height_xs.'px';

//        分页器








        $bullet_height = (isset($settings->bullet_height) && $settings->bullet_height) ? $settings->bullet_height : 8;
        $bullet_width = (isset($settings->bullet_width) && $settings->bullet_width) ? $settings->bullet_width : 8;
        $bullet_background = (isset($settings->bullet_background) && $settings->bullet_background) ? $settings->bullet_background : '#000';
        $bullet_border_width = (isset($settings->bullet_border_width) && $settings->bullet_border_width) ? $settings->bullet_border_width : 0;
        $bullet_border_color = (isset($settings->bullet_border_color) && $settings->bullet_border_color) ? $settings->bullet_border_color : '';
        $bullet_border_radius = (isset($settings->bullet_border_radius) && $settings->bullet_border_radius) ? $settings->bullet_border_radius : '50';
        $bullet_margin = (isset($settings->bullet_margin) && $settings->bullet_margin) ? $settings->bullet_margin : 8;
        $carousel_bullet_style = (isset($settings->carousel_bullet_style) && $settings->carousel_bullet_style) ? $settings->carousel_bullet_style : 'horizontal';
        if ($carousel_bullet_style==='horizontal'){
            $pagination_item_margin='margin-left:'.$bullet_margin.'px;';
        }else{
            $pagination_item_margin='margin-bottom:'.$bullet_margin.'px;';
        }
//        鼠标移入
        $bullet_active_height = (isset($settings->bullet_active_height) && $settings->bullet_active_height) ? $settings->bullet_active_height : 8;
        $bullet_active_width = (isset($settings->bullet_active_width) && $settings->bullet_active_width) ? $settings->bullet_active_width : 8;
        $bullet_active_background = (isset($settings->bullet_active_background) && $settings->bullet_active_background) ? $settings->bullet_active_background : '#007aff';
        $bullet_active_border_width = (isset($settings->bullet_active_border_width) && $settings->bullet_active_border_width) ? $settings->bullet_active_border_width : 0;
        $bullet_active_border_color = (isset($settings->bullet_active_border_color) && $settings->bullet_active_border_color) ? $settings->bullet_active_border_color : '';
        $bullet_active_border_radius = (isset($settings->bullet_active_border_radius) && $settings->bullet_active_border_radius) ? $settings->bullet_active_border_radius : '50';
        $bullet_opacity = (isset($settings->bullet_active_opacity) && $settings->bullet_active_opacity) ? $settings->bullet_active_opacity / 100 : '.2';

//      翻页按钮
        $carousel_arrow=(isset($settings->carousel_arrow)&&$settings->carousel_arrow)?$settings->carousel_arrow:0;
        $show_arrow = $carousel_arrow === 1 ? 'block' : 'none';

        $arrow_position_verti = '';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti = $settings->arrow_position_verti->md;
            } else {
                $arrow_position_verti = $settings->arrow_position_verti;
            }
        } else {
            $arrow_position_verti = '';
        }

        $arrow_position_verti_sm = '';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti_sm = $settings->arrow_position_verti->sm;
            } else {
                $arrow_position_verti_sm = $settings->arrow_position_verti_sm;
            }
        } else {
            $arrow_position_verti_sm = '';
        }
        $arrow_position_verti_xs = '';
        if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti) {
            if (is_object($settings->arrow_position_verti)) {
                $arrow_position_verti_xs = $settings->arrow_position_verti->xs;
            } else {
                $arrow_position_verti_xs = $settings->arrow_position_verti_xs;
            }
        } else {
            $arrow_position_verti_xs = '';
        }


        $arrow_position_hori = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori = $settings->arrow_position_hori->md;
            } else {
                $arrow_position_hori = $settings->arrow_position_hori;
            }
        } else {
            $arrow_position_hori = '';
        }

        $arrow_position_hori_sm = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori)) {
                $arrow_position_hori_sm = $settings->arrow_position_hori->sm;
            } else {
                $arrow_position_hori_sm = $settings->arrow_position_hori_sm;
            }
        } else {
            $arrow_position_hori_sm = '';
        }

        $arrow_position_hori_xs = '';
        if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori) {
            if (is_object($settings->arrow_position_hori->xs)) {
                $arrow_position_hori_xs = $settings->arrow_position_hori->xs;
            } else {
                $arrow_position_hori_xs = $settings->arrow_position_hori_xs;
            }
        } else {
            $arrow_position_hori_xs = '';
        }


        $arrow_width = (isset($settings->arrow_width) && $settings->arrow_width) ? $settings->arrow_width : '';
        $arrow_height = (isset($settings->arrow_height) && $settings->arrow_height) ? $settings->arrow_height : '';
        $arrow_background = (isset($settings->arrow_background) && $settings->arrow_background) ? $settings->arrow_background : '';
        $arrow_color = (isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '';
        $arrow_font_size = (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? $settings->arrow_font_size : '';
        $arrow_border_width = (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? $settings->arrow_border_width : '';
        $arrow_border_color = (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? $settings->arrow_border_color : '';
        $arrow_border_radius = (isset($settings->arrow_border_radius) && $settings->arrow_border_radius) ? $settings->arrow_border_radius : '';
//        鼠标移入
        $arrow_hover_background = (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? $settings->arrow_hover_background : '';
        $arrow_hover_border_width = (isset($settings->arrow_hover_border_width) && $settings->arrow_hover_border_width) ? $settings->arrow_hover_border_width : '';
        $arrow_hover_border_color = (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? $settings->arrow_hover_border_color : '';
        $arrow_hover_border_radius = (isset($settings->arrow_hover_border_radius) && $settings->arrow_hover_border_radius) ? $settings->arrow_hover_border_radius : '';
        $arrow_hover_color = (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '';

//        轮播项内容
//        对齐
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';
        $item_content_verti_align = (isset($settings->item_content_verti_align) && $settings->item_content_verti_align) ? $settings->item_content_verti_align : 'center';
//          标题字体大小

        $content_title_fontsize = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize = $settings->content_title_fontsize->md;
            } else {
                $content_title_fontsize = $settings->content_title_fontsize;
            }
        } else {
            $content_title_fontsize = '16';
        }

        $content_title_fontsize_sm = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize->sm)) {
                $content_title_fontsize_sm = $settings->content_title_fontsize->sm;
            } else {
                $content_title_fontsize_sm = $settings->content_title_fontsize_sm;
            }
        } else {
            $content_title_fontsize_sm = '16';
        }
        $content_title_fontsize_xs = '';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_xs = $settings->content_title_fontsize->xs;
            } else {
                $content_title_fontsize_xs = $settings->content_title_fontsize_xs;
            }
        } else {
            $content_title_fontsize_xs = '16';
        }


//        行高
        $content_title_lineheight=(isset($settings->content_title_lineheight) && $settings->content_title_lineheight) ? $settings->content_title_lineheight : '60';
        $content_title_font_family=(isset($settings->content_title_font_family) && $settings->content_title_font_family) ? $settings->content_title_font_family : '';
        $title_style='';
        $content_title_font_style = (isset($settings->content_title_font_style) && $settings->content_title_font_style) ? $settings->content_title_font_style : '';
        if (isset($content_title_font_style->underline) && $content_title_font_style->underline) {
            $title_style .= 'text-decoration:underline;';
        }
        if (isset($content_title_font_style->italic) && $content_title_font_style->italic) {
            $title_style .= 'font-style:italic;';
        }
        if (isset($content_title_font_style->uppercase) && $content_title_font_style->uppercase) {
            $title_style .= 'text-transform:uppercase;';
        }
        if (isset($content_title_font_style->weight) && $content_title_font_style->weight) {
            $title_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }
        $content_title_letterspace=(isset($settings->content_title_letterspace) && $settings->content_title_letterspace) ? $settings->content_title_letterspace :'';
        $content_title_text_color=(isset($settings->content_title_text_color) && $settings->content_title_text_color) ? $settings->content_title_text_color :'#333';

        $content_title_margin = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin = 'margin:' .$settings->content_title_margin->md . ';';
            } else {
                $content_title_margin = 'margin:'.$settings->content_title_margin. ';';
            }
        } else {
            $content_title_margin = '';
        }
        $content_title_margin_sm = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_sm = 'margin:' .$settings->content_title_margin->sm . ';';
            } else {
                $content_title_margin_sm = 'margin:' .$settings->content_title_margin_sm. ';';
            }
        } else {
            $content_title_margin_sm = '';
        }
        $content_title_margin_xs = '';
        if (isset($settings->content_title_margin) && $settings->content_title_margin) {
            if (is_object($settings->content_title_margin)) {
                $content_title_margin_xs = 'margin:' .$settings->content_title_margin->xs . ';';
            } else {
                $content_title_margin_xs = 'margin:' .$settings->content_title_margin_xs. ';';
            }
        } else {
            $content_title_margin_xs = '';
        }


//      副标题

        $content_subtitle_fontsize = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize->md;
            } else {
                $content_subtitle_fontsize = $settings->content_subtitle_fontsize;
            }
        } else {
            $content_subtitle_fontsize = '';
        }

        $content_subtitle_fontsize_sm = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize->sm;
            } else {
                $content_subtitle_fontsize_sm = $settings->content_subtitle_fontsize_sm;
            }
        } else {
            $content_subtitle_fontsize_sm = '';
        }
        $content_subtitle_fontsize_xs = '';
        if (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) {
            if (is_object($settings->content_subtitle_fontsize)) {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize->xs;
            } else {
                $content_subtitle_fontsize_xs = $settings->content_subtitle_fontsize_xs;
            }
        } else {
            $content_subtitle_fontsize_xs = '';
        }

        $content_subtitle_letterspace=(isset($settings->content_subtitle_letterspace) && $settings->content_subtitle_letterspace) ? $settings->content_subtitle_letterspace :'';
        $content_subtitle_lineheight=(isset($settings->content_subtitle_lineheight) && $settings->content_subtitle_lineheight) ? $settings->content_subtitle_lineheight :'';
        $content_subtitle_font_family=(isset($settings->content_subtitle_font_family) && $settings->content_subtitle_font_family) ? $settings->content_subtitle_font_family :'';
        $content_subtitle_text_color=(isset($settings->content_subtitle_text_color) && $settings->content_subtitle_text_color) ? $settings->content_subtitle_text_color :'';
        $subtitle_style='';
//        描述

        $description_fontsize = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize = $settings->description_fontsize->md;
            } else {
                $description_fontsize = $settings->description_fontsize;
            }
        } else {
            $description_fontsize = '';
        }
        $description_fontsize_sm = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize)) {
                $description_fontsize_sm = $settings->description_fontsize->sm;
            } else {
                $description_fontsize_sm = $settings->description_fontsize_sm;
            }
        } else {
            $description_fontsize_sm = '';
        }
        $description_fontsize_xs = '';
        if (isset($settings->description_fontsize) && $settings->description_fontsize) {
            if (is_object($settings->description_fontsize->xs)) {
                $description_fontsize_xs = $settings->description_fontsize->xs;
            } else {
                $description_fontsize_xs = $settings->description_fontsize_xs;
            }
        } else {
            $description_fontsize_xs = '';
        }

        $description_letterspace=(isset($settings->description_letterspace) && $settings->description_letterspace) ? $settings->description_letterspace :'';
        $description_lineheight=(isset($settings->description_lineheight) && $settings->description_lineheight) ? $settings->description_lineheight :'';
        $description_font_family=(isset($settings->description_font_family) && $settings->description_font_family) ? $settings->description_font_family :'';
        $description_text_color=(isset($settings->description_text_color) && $settings->description_text_color) ? $settings->description_text_color :'';
        $description_style='';

        $description_font_style = (isset($settings->description_font_style) && $settings->description_font_style) ? $settings->description_font_style : '';
        if (isset($description_font_style->underline) && $description_font_style->underline) {
            $description_style .= 'text-decoration:underline;';
        }
        if (isset($description_font_style->italic) && $description_font_style->italic) {
            $description_style .= 'font-style:italic;';
        }
        if (isset($description_font_style->uppercase) && $description_font_style->uppercase) {
            $description_style .= 'text-transform:uppercase;';
        }
        if (isset($description_font_style->weight) && $description_font_style->weight) {
            $description_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }

        $description_margin = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin = 'margin:' .$settings->description_margin->md. ';';
            } else {
                $description_margin = 'margin:' .$settings->description_margin.';';
            }
        } else {
            $description_margin = '';
        }
        $description_margin_sm = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin->sm)) {
                $description_margin_sm = 'margin:' .$settings->description_margin->sm. ';';
            } else {
                $description_margin_sm = 'margin:' .$settings->description_margin_sm. ';';
            }
        } else {
            $description_margin_sm = '';
        }
        $description_margin_xs = '';
        if (isset($settings->description_margin) && $settings->description_margin) {
            if (is_object($settings->description_margin)) {
                $description_margin_xs = 'margin:' .$settings->description_margin->xs. ';';
            } else {
                $description_margin_xs = 'margin:' .$settings->description_margin_xs. ';';
            }
        } else {
            $description_margin_xs = '';
        }


//        介绍

        $introduce_height = '';
        if (isset($settings->introduce_height) && $settings->introduce_height) {
            if (is_object($settings->introduce_height)) {
                $introduce_height = $settings->introduce_height->md;
            } else {
                $introduce_height = $settings->introduce_height;
            }
        } else {
            $introduce_height = '529';
        }
        $introduce_height_sm = '';
        if (isset($settings->introduce_height) && $settings->introduce_height) {
            if (is_object($settings->introduce_height)) {
                $introduce_height_sm = $settings->introduce_height->sm;
            } else {
                $introduce_height_sm = $settings->introduce_height_sm;
            }
        } else {
            $introduce_height_sm = '400';
        }

        $introduce_height_xs = '';
        if (isset($settings->introduce_height) && $settings->introduce_height) {
            if (is_object($settings->introduce_height)) {
                $introduce_height_xs = $settings->introduce_height->xs;
            } else {
                $introduce_height_xs = $settings->introduce_height_xs;
            }
        } else {
            $introduce_height_xs = '288';
        }

        $introduce_margin = '';
        if (isset($settings->introduce_margin) && $settings->introduce_margin) {
            if (is_object($settings->introduce_margin)) {
                $introduce_margin = $settings->introduce_margin->md;
            } else {
                $introduce_margin = $settings->introduce_margin;
            }
        } else {
            $introduce_margin = '0px 25px 0px 0px';
        }
        $introduce_margin_sm = '';
        if (isset($settings->introduce_margin) && $settings->introduce_margin) {
            if (is_object($settings->introduce_margin)) {
                $introduce_margin_sm = $settings->introduce_margin->sm;
            } else {
                $introduce_margin_sm = $settings->introduce_margin_sm;
            }
        } else {
            $introduce_margin_sm = '0px 25px 0px 0px';
        }


        $introduce_background_color = (isset($settings->introduce_background_color) && $settings->introduce_background_color) ? $settings->introduce_background_color : '#f9f9f9';
        $introduce_background_image = (isset($settings->introduce_background_image) && $settings->introduce_background_image) ? $settings->introduce_background_image : '';
        $introduce_font_color = (isset($settings->introduce_font_color) && $settings->introduce_font_color) ? $settings->introduce_font_color : '#333';

        $introduce_font_size = '';
        if (isset($settings->introduce_font_size) && $settings->introduce_font_size) {
            if (is_object($settings->introduce_font_size)) {
                $introduce_font_size = $settings->introduce_font_size->md;
            } else {
                $introduce_font_size = $settings->introduce_font_size;
            }
        } else {
            $introduce_font_size = '16';
        }
        $introduce_font_size_sm = '';
        if (isset($settings->introduce_font_size) && $settings->introduce_font_size) {
            if (is_object($settings->introduce_font_size)) {
                $introduce_font_size_sm = $settings->introduce_font_size->sm;
            } else {
                $introduce_font_size_sm = $settings->introduce_font_size_sm;
            }
        } else {
            $introduce_font_size_sm = '16';
        }
        $introduce_font_size_xs = '';
        if (isset($settings->introduce_font_size) && $settings->introduce_font_size) {
            if (is_object($settings->introduce_font_size)) {
                $introduce_font_size_xs = $settings->introduce_font_size->xs;
            } else {
                $introduce_font_size_xs = $settings->introduce_font_size_xs;
            }
        } else {
            $introduce_font_size_xs = '16';
        }


//      轮播项内容图是图片的时候

        $content_img_margin = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin = $settings->content_img_margin->md;
            } else {
                $content_img_margin = $settings->content_img_margin;
            }
        } else {
            $content_img_margin = '';
        }
        $content_img_margin_sm = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin_sm = $settings->content_img_margin->sm;
            } else {
                $content_img_margin_sm = $settings->content_img_margin_sm;
            }
        } else {
            $content_img_margin_sm = '';
        }

        $content_img_margin_xs = '';
        if (isset($settings->content_img_margin) && $settings->content_img_margin) {
            if (is_object($settings->content_img_margin)) {
                $content_img_margin_xs = $settings->carousel_height->xs;
            } else {
                $content_img_margin_xs = $settings->carousel_height_xs;
            }
        } else {
            $content_img_margin_xs = '';
        }


        $output = '
            ' . $addonId . '{
                position: relative;
                width: 100%;
                height: ' . $carousel_height . 'px;
                overflow: hidden;
                margin-left: ' . $carousel_left . 'px;
            }
            ' . $addonId . ' .swiper-container{
                width: 82%;
                margin-left:'.$carousel_view_left.'px;
            }
            ' . $addonId . ' .swiper-slide{
                position: relative;
            }
            ' . $addonId . ' .swiper-slide img{
                width: 100%;
                height: ' . $height . ';
                display: block;
                object-fit: cover;
            }
            ' . $addonId . ' .swiper-button-next.img img,
            ' . $addonId . ' .swiper-button-prev.img img{
                width: 100%;
                height: 100%;
                vertical:middle;
                outline:none;
            }
            ' . $addonId . ' .swiper-button-next.img img{
                transform: rotate(180deg);
            }
            ' . $addonId . ' .swiper-button-next.img::after,
            ' . $addonId . ' .swiper-button-prev.img::after{
                display: none;
            }
            ' . $addonId . ' .swiper-pagination{
                outline: none;
            }
            ' . $addonId . ' .swiper-pagination-bullet{
                width: ' . $bullet_width . 'px;
                height: ' . $bullet_height . 'px;
                background: ' . $bullet_background . ';
                border: ' . $bullet_border_width . 'px solid ' . $bullet_border_color . ';
                border-radius: ' . $bullet_border_radius . 'px;
                opacity: ' . $bullet_opacity . ';
                '.$pagination_item_margin.';
                outline: none;
            }
            ' . $addonId . ' .swiper-pagination-bullet:hover,
            ' . $addonId . ' .swiper-pagination-bullet.swiper-pagination-bullet-active{
                width: ' . $bullet_active_width . 'px;
                height: ' . $bullet_active_height . 'px;
                background: ' . $bullet_active_background . ';
                border: ' . $bullet_active_border_width . 'px solid ' . $bullet_active_border_color . ';
                border-radius: ' . $bullet_active_border_radius . 'px;
                opacity: 1;
            }
            ' . $addonId . ' .swiper-button-next,
            ' . $addonId . ' .swiper-button-prev{
                top: ' . $arrow_position_verti . 'px;
                width: ' . $arrow_width . 'px;
                height: ' . $arrow_height . 'px;
                line-height: ' . $arrow_height . 'px;
                text-align:center;
                background: ' . $arrow_background . ';
                border: ' . $arrow_border_width . 'px solid ' . $arrow_border_color . ';
                border-radius: ' . $arrow_border_radius . 'px;
                display: '.$show_arrow.';
                background-image: none;
            }
            ' . $addonId . ' .swiper-button-next:hover,
            ' . $addonId . ' .swiper-button-prev:hover{
                background: ' . $arrow_hover_background . ';
                border: ' . $arrow_hover_border_width . 'px solid ' . $arrow_hover_border_color . ';
                border-radius: ' . $arrow_hover_border_radius . 'px;
                transition:all .5s;
            }
            ' . $addonId . ' .swiper-button-next:hover{
                transform:translateX(15px);
            }
            ' . $addonId . ' .swiper-button-prev:hover{
                transform:translateX(-15px);
            }
            ' . $addonId . ' .swiper-button-next::after,
            ' . $addonId . ' .swiper-button-prev::after{
                color: ' . $arrow_color . ';
                font-size: ' . $arrow_font_size . 'px;
            }
            ' . $addonId . ' .swiper-button-next:hover::after,
            ' . $addonId . ' .swiper-button-prev:hover::after{
                color: ' . $arrow_hover_color . ';
                font-size: ' . $arrow_font_size . 'px;
            }
            ' . $addonId . ' .swiper-button-next{
                right: ' . $arrow_position_hori . 'px;
                outline:none;
            }
            ' . $addonId . ' .swiper-button-prev{
                left: ' . $arrow_position_hori . 'px;
                outline:none;
            }
            '.$addonId.' .content-layout4 .first-title{
                font-size: '.$content_title_fontsize.'px;
                font-family: '.$content_title_font_family.';
                color: '.$content_title_text_color.';
                line-height: '.$content_title_lineheight.'px;
                /*text-align: '.$item_content_hori_align.';*/
                '.$title_style.';
                letter-spacing: '.$content_title_letterspace.';
                '.$content_title_margin.'
            }
            
            '.$addonId.' .swiper-content{
                position:absolute;
                bottom: 0;
                left:0;
                right:0;
                margin:auto;
                display: flex;
                flex-direction: column;
                text-align:center;
                align-items: center;';
        if($item_content_verti_align==='middle'){
            $output.='
                top: 0;
                justify-content: center;
            }';
        }else if ($item_content_verti_align==='bottom'){
            $output.='justify-content: center}';
        }else{
            $output.='top:0;}';
        }
        $output.='
            '.$addonId.' .subtitle{
                font-size: '.$content_subtitle_fontsize.'px;
                line-height: '.$content_subtitle_lineheight.'px;
                font-family: '.$content_subtitle_font_family.'px;
                '.$subtitle_style.';
                letter-spacing: '.$content_subtitle_letterspace.';
                /*text-align: '.$item_content_hori_align.';*/
                color: '.$content_subtitle_text_color.';
            }
            '.$addonId.' .description{
                font-size: '.$description_fontsize.'px;
                font-family: '.$description_font_family.';
                color: '.$description_text_color.';
                line-height: '.$description_lineheight.'px;
                /*text-align: '.$item_content_hori_align.';*/
                '.$description_style.';
                letter-spacing: '.$description_letterspace.';
                '.$description_margin.'
            }
            '.$addonId.' .content-img{
                width:auto!important;
                height:auto!important;
                display: inline-block!important;
                margin: '.$content_img_margin.';
            }
            @media (min-width: 768px) and (max-width: 991px){
                ' . $addonId . ' .swiper-container{
                    width: 100%;
                    margin-left:'.$carousel_view_left_sm.'px;
                }
                .' . $addonId . ' .swiper-slide img{
                    height: ' . $height_sm . ';
                }
                   
                ' . $addonId . ' .swiper-button-next,
                ' . $addonId . ' .swiper-button-prev{
                    top: ' . $arrow_position_verti_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next{
                    right: ' . $arrow_position_hori_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev{
                    left: ' . $arrow_position_hori_sm . 'px;
                }
                '.$addonId.' .text-layout3 p{
                    font-size: '.$content_title_fontsize_sm.'px;
                    '.$content_title_margin_sm.'
                }
                '.$addonId.' .subtitle{
                    font-size: '.$content_subtitle_fontsize_sm.'px;
                }
                '.$addonId.' .description{
                    font-size: '.$description_fontsize_sm.'px;
                    '.$description_margin_sm.'
                }
                '.$addonId.' .content-img{
                    margin: '.$content_img_margin_sm.';
                }
                '.$addonId.' .content-layout4 .first-title{
                    font-size: '.$content_title_fontsize_sm.'px;
                    '.$content_title_margin_sm.'
                }
            }
            @media (max-width: 767px){
                ' . $addonId . ' .swiper-slide img{
                    height: auto!important;
                }
                ' . $addonId . ' .swiper-container{
                    width: 100%;
                    margin-left:'.$carousel_view_left_xs.'px;
                }
                .' . $addonId . ' .swiper-slide img{
                    height: ' . $height_sm . ';
                }
                ' . $addonId . ' .swiper-button-next,
                ' . $addonId . ' .swiper-button-prev{
                    display: none;
                    top: ' . $arrow_position_verti_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next{
                    right: ' . $arrow_position_hori_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev{
                    left: ' . $arrow_position_hori_xs . 'px;
                }
                '.$addonId.' .text-layout3 p,
                '.$addonId.' .content-layout4 .first-title{
                    font-size: '.$content_title_fontsize_xs.'px;
                    '.$content_title_margin_xs.'
                }               
                '.$addonId.' .subtitle{
                    font-size: '.$content_subtitle_fontsize_xs.'px;
                }
                '.$addonId.' .description{
                    font-size: '.$description_fontsize_xs.'px;
                    '.$description_margin_xs.'
                }
                '.$addonId.' .content-img{
                    margin: '.$content_img_margin_xs.';
                }
            }
            ' . $addonId . ' .swiper-pagination{
                display:none;
            }';


            $output.=$addonId . ' .introduce{
                width: 28%;
                height: '.$introduce_height.'px;
                background-color: '.$introduce_background_color.';
                padding: 30px;
                margin: '.$introduce_margin.';
                background-image: url("'.$introduce_background_image.'");
                background-size: cover;
                float: left;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                min-width: 392px;
            }
            '.$addonId . '{
                display:flex;
                align-items: center;
                height:830px;
                overflow: visible;
                margin-top: 82px;
            }
            '.$addonId . ' .swiper-container{
                height: calc('.$carousel_height.'px + 82px);
                margin-top: -84px;
            }
            '.$addonId.' .introduce-content{
                float: right;
                width: 289px;
                font-size: '.$introduce_font_size.'px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: '.$introduce_font_color.';
                line-height: 32px;
            }
            '.$addonId.' .swiper-container-bg{
                width: 60%;
                float: left;
                height: '.$carousel_height.'px;
                background-color: '.$introduce_background_color.';
                padding:0 34px;
            }
            '.$addonId.' .swiper-slide img{
                height: '.$height.';
                object-fit:cover;
            }';

            $items = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';

            foreach ($items as $key => $item) {
                $output.=''.$addonId.' .swiper-slide[data-id="'.($key*3+1).'"]{
                    margin-top: 0px;
                }'.$addonId.' .swiper-slide[data-id="'.($key*3).'"]{
                    margin-top:252px;
                }'.$addonId.' .swiper-slide[data-id="'.($key*3+2).'"]{
                    margin-top:152px;
                }';
            }

            $output.=''.$addonId.' .swiper-slide-active{
                margin-top: 252px;
            }
            /*'.$addonId.' .swiper-slide-next{
                margin-top: 0;
            }*/
            '.$addonId.' .content-layout4{
                width: 100%;
                height: calc(100% - 504px);
                padding: 0 20px;
            }
            '.$addonId.' .content-layout4 *{
                text-align: left;
            }
            '.$addonId.' .content-layout4 .split-layout{
                width: 26px;
                height: 2px;
                background: #CABD97;
            }
            @media (min-width: 1024px) and (max-width:1300px){
                '.$addonId . ' .swiper-slide{
                    margin-top: 0!important;
                    height:auto;
                }
                '.$addonId . ' .swiper-container{
                    height: auto;
                    margin-top:0;
                }
                '.$addonId.' .swiper-container-bg{
                    display:flex;
                    align-items:center;
                    height:auto;
                    padding: 50px 34px;
                }
            }
            @media  (max-width:1024px){
                '.$addonId.' .swiper-container-bg{
                    display:flex;
                    align-items:center;
                    height:auto;
                    padding: 50px 34px;
                }
                '.$addonId . ' .swiper-container{
                    height:auto;
                }
            }
            @media (min-width: 901px) and (max-width:992px){
                '.$addonId.' .introduce{
                    height: '.$introduce_height_sm.'px;
                    margin: '.$introduce_margin_sm.';
                }
                '.$addonId.' .swiper-container-bg{
                    width: 72%;
                }
                
                '.$addonId.' .introduce-content{
                    font-size: '.$introduce_font_size_sm.'px;
                }
                '.$addonId . ' .swiper-container{
                    margin: 0;
                    width: 100%;
                    height: auto;
                }
                '.$addonId . ' .swiper-slide{
                    margin-top: 0!important;
                }
                ' . $addonId . ' .swiper-slide img{
                    height: ' . $height_sm . ';
                }
            }
            @media (min-width: 768px) and (max-width: 900px){
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    display:none;
                }
                '.$addonId.' .introduce{
                    width: 100%;
                    height: '.$introduce_height_sm.'px;
                    margin: 0 0 20px 0;
                    float: none;
                    justify-content: center;
                }
                '.$addonId.' .swiper-container-bg{
                    width: 100%;
                    float: none;
                    height: auto;
                    padding: 20px 20px 40px!important;
                }
                '.$addonId.' .introduce-content{
                    font-size: '.$introduce_font_size_sm.'px;
                    width: 90%;
                }
                '.$addonId . '{
                    display:block;
                    height:auto;
                    overflow: visible;
                }
                '.$addonId . ' .swiper-container{
                    margin: 0;
                    width: 100%;
                    height: auto;
                }
                '.$addonId . ' .swiper-slide{
                    margin-top: 0!important;
                }
                '.$addonId.' .swiper-container-bg{
                    padding:0;
                }
                ' . $addonId . ' .swiper-slide img{
                    height: '.$height_sm.'!important;
                }
            } 
            @media (max-width: 767px){
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    display:none;
                }
                '.$addonId.' .introduce{
                    width: 100%;
                    height: '.$introduce_height_xs.'px;
                    margin: 0 0 20px 0;
                    float: none;
                    justify-content: center;
                }
                '.$addonId.' .introduce-content{
                    font-size: '.$introduce_font_size_xs.'px;
                    width: 90%;
                }
                ' . $addonId . ' .swiper-slide img{
                    height: '.$height_xs.'!important;
                }
                '.$addonId.' .swiper-container-bg{
                    width: 100%;
                    float: none;
                    height: auto;
                    padding: 20px 20px 40px!important;
                }
                
                '.$addonId . ' .swiper-container{
                    margin: 0;
                    width: 100%;
                    height: auto;
                }
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    display:none;
                }
                '.$addonId.' .introduce{
                    width: 100%;
                    height: '.$introduce_height_sm.'px;
                    margin: 0 0 20px 0;
                    float: none;
                    justify-content: center;
                }
                '.$addonId.' .swiper-container-bg{
                    width: 100%;
                    float: none;
                    height: auto;
                    padding: 20px 20px 40px!important;
                }
                '.$addonId.' .introduce-content{
                    font-size: '.$introduce_font_size_xs.'px;
                    width: 90%;
                }
                '.$addonId . '{
                    display:block;
                    height:auto;
                    overflow: visible;
                }
                '.$addonId . ' .swiper-container{
                    margin: 0;
                    width: 100%;
                    height: auto;
                }
                '.$addonId . ' .swiper-slide{
                    margin-top: 0!important;
                }
                '.$addonId.' .swiper-container-bg{
                    padding:0;
                }
                ' . $addonId . ' .swiper-slide img{
                    height: '.$height_xs.'!important;
                }
            }';

        return $output;
    }




    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }
}
