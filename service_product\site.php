<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonService_product extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $service_product_type = (isset($settings->service_product_type) && $settings->service_product_type) ? $settings->service_product_type : 'type1';

        $output = '';
        if ($service_product_type == 'type1') {
            $jw_tab_item_goods = (isset($settings->jw_tab_item_goods) && $settings->jw_tab_item_goods) ? $settings->jw_tab_item_goods : '';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .ind3-a1 {
                        width: calc(1848/1920*100%);
                        position: relative;
                        margin: 0 auto;
                        display: flex;
                        justify-content: space-between;
                    }
                
                    ' . $addon_id . ' .fadeInUp {
                        -webkit-animation-name: fadeInUp;
                        animation-name: fadeInUp;
                    }
                    ' . $addon_id . ' .animated {
                        -webkit-animation-duration: 1s;
                        animation-duration: 1s;
                        -webkit-animation-fill-mode: both;
                        animation-fill-mode: both;
                    }
                    ' . $addon_id . ' .a1 {
                        position: relative;
                    }
                    ' . $addon_id . ' .ind3-a2 {
                        width: calc(454/1848*100%);
                        height: 0;
                        padding-bottom: calc(712/1848*100%);
                        position: relative;
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .a1>a {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        display: block;
                    }
                    ' . $addon_id . ' a,' . $addon_id . ' dd,' . $addon_id . ' dl,' . $addon_id . ' h1,' . $addon_id . ' h2,' . $addon_id . ' h3,' . $addon_id . ' h4,' . $addon_id . ' h5,' . $addon_id . ' h6,' . $addon_id . ' li,' . $addon_id . ' p,' . $addon_id . ' ul {
                        margin: 0;
                        padding: 0;
                        color: inherit;
                        font-size: inherit;
                        font-weight: inherit;
                    }
                    ' . $addon_id . ' .i100 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .ind3-a3 {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        background: #000;
                    }
                    ' . $addon_id . ' .i100>img {
                        width: 100%;
                    }
                    ' . $addon_id . ' .ind3-a3 img {
                        min-height: 100%;
                        opacity: 0.6;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .ind3-a4 {
                        width: calc(374/454*100%);
                        position: absolute;
                        top: calc(100% - 148px);
                        left: calc(40/454*100%);
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .i200 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .ind3-a5 {
                        height: 64px;
                        position: relative;
                    }
                    ' . $addon_id . ' .i200>img {
                        height: 100%;
                    }
                    ' . $addon_id . ' img {
                        display: block;
                        border: none;
                    }
                    ' . $addon_id . ' .ind3-a6 {
                        font-size: 30px;
                        line-height: 34px;
                        color: #fff;
                        font-weight: bold;
                        font-style: italic;
                        font-weight: bold;
                        margin-bottom: 50px;
                    }
                    ' . $addon_id . ' .ind3-a8 {
                        position: relative;
                    }
                    ' . $addon_id . ' .ind3-a8>div {
                        font-size: 16px;
                        line-height: 36px;
                        color: #fff;
                    }
                    ' . $addon_id . ' .ind3-a2:hover .ind3-a3 img {
                        opacity: 1;
                        transform: scale(1.05);
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .ind3-a2:hover .ind3-a4 {
                        top: calc(280/712*100%);
                        transition: 0.5s;
                    }
                    ';
                    foreach ($jw_tab_item_goods as $k => $v) {
                      if ($v->hover_content_top) {
                        $output .= '
                        ' . $addon_id . ' .ind3-a2.item' . $k .':hover .ind3-a4 {
                          top: ' . $v->hover_content_top .'%;
                        }
                        ';
                      }
                    }
                    $output .= '
                    @media only screen and (max-width: 768px){
                        ' . $addon_id . ' .ind3-a1 {
                            display: none !important;
                        }
                    }
                    @media only screen and (min-width: 768px){
                        ' . $addon_id . ' .p-index-a2 {
                            display: none;
                        }
                    }
                    ' . $addon_id . ' .p-index-a2 {
                        width: 100%;
                        height: 0;
                        padding-bottom: calc(1056/750*100%);
                        position: relative;
                        overflow: hidden;
                        margin-top: 40px;
                    }
                    ' . $addon_id . ' .p-index-a2 .swiper-container {
                        width: calc(670*2/750*100%);
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                    }
                    ' . $addon_id . ' .swiper-container {
                        margin-left: auto;
                        margin-right: auto;
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-slide, ' . $addon_id . ' .swiper-wrapper {
                        -webkit-transform: translate3d(0,0,0);
                        transform: translate3d(0,0,0);
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .p-index-a2 .swiper-slide {
                        width: 50% !important;
                        height: 100% !important;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                    }
                    ' . $addon_id . ' .p-index-a2 .swiper-slide .p-index-a2-box {
                        transform: scale(0.89);
                        transform-origin: 0 50%;
                        transition: 0.6s;
                    }
                    ' . $addon_id . ' .p-index-a2-box {
                        position: absolute;
                        width: calc(630/670*100%);
                        height: 100%;
                        background: rgba(0, 0, 0, .3);
                        top: 0;
                        left: calc(40/670*100%);
                    }
                    ' . $addon_id . ' .p-index-a2-pos {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        top: 0;
                        left: 0;
                    }
                    ' . $addon_id . ' .p-index-a2-pos .p-index-a2-pos-img {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        top: 0;
                        left: 0;
                        display: block;
                        background: #000;
                    }
                    ' . $addon_id . ' .i300 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .p-index-a2-pos .p-index-a2-pos-img img {
                        opacity: 0.6;
                    }
                    ' . $addon_id . ' .i300>img {
                        width: 100%;
                        height: 100%;
                    }
                    ' . $addon_id . ' .p-index-a2-pos .p-index-a2-pos-box {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        padding-top: calc(400/1056*100%);
                        padding-left: calc(40/630*100%);
                        color: #fff;
                    }
                    ' . $addon_id . ' .p-index-a2-pos-ti2 {
                        font-size: 27px;
                        font-weight: bolder;
                        margin-bottom: 15px;
                    }
                    ' . $addon_id . ' .p-index-a2-pos-ti4 {
                        font-size: 15px;
                        margin-bottom: 5px;
                        line-height: 35px;
                    }
                    ' . $addon_id . ' .p-index-a2 .swiper-slide-active .p-index-a2-box {
                        transform: scale(1);
                        transition: 0.6s;
                    }
                </style>
            ';
            $output .= '
                <div class="ind3-a1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
            ';
            foreach ($jw_tab_item_goods as $k => $v) {
                if ($v->detail_page_id) {
                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                    $arrray = explode('&', $idurl);
                    foreach ($arrray as $key => $value) {
                        if (substr($value, 0, 3) == 'id=') {
                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                        }
                    }

                    $return = implode('&', $arrray);
                } else {
                    $return = $v->detail_page;
                }
                $output .= '
                    <div class="ind3-a2 a1 item'. $k .'">
                        <a href="' . $return . '" target="' . $v->target . '">
                            <div class="ind3-a3 i100">
                                <img src="' . $v->big_img . '">
                            </div>
                            <div class="ind3-a4">
                                <div class="ind3-a5 i200">
                                    <img src="' . $v->number_img . '">
                                </div>
                                <div class="ind3-a6">' . $v->title . '</div>
                                <div class="ind3-a8">
                                ';
                                    if ($v->goods_desc) {
                                        foreach ($v->goods_desc as $kk => $vv) {
                                            if ($vv->d_page_id) {
                                                $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                                                $arrray = explode('&', $idurl);
                                                foreach ($arrray as $key => $value) {
                                                    if (substr($value, 0, 3) == 'id=') {
                                                        $arrray[$key] = 'id=' . base64_encode($vv->d_page_id);
                                                    }
                                                }

                                                $return = implode('&', $arrray);
                                            } else {
                                                $return = $vv->d_page;
                                            }
                                            $output .= '<div onclick="toClickDesc(event,\'/'.$return. '\',\''. $vv->d_target . '\')">' . $vv->title_list . '</div>';
                                        }
                                    }
                                    $output .= '
                                </div>
                            </div>
                        </a>
                    </div>
                ';
            }
            $output .= '</div>';
            $output .= '
                <div class="p-index-a2 wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s"
                style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                    <div class="swiper-container swiper-container1 swiper-container-horizontal swiper-container-ios">
                        <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(0px, 0px, 0px);">
            ';
            foreach ($jw_tab_item_goods as $k => $v) {
                if ($v->detail_page_id) {
                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                    $arrray = explode('&', $idurl);
                    foreach ($arrray as $key => $value) {
                        if (substr($value, 0, 3) == 'id=') {
                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                        }
                    }

                    $return = implode('&', $arrray);
                } else {
                    $return = $v->detail_page;
                }
                $output .= '
                    <div class="swiper-slide swiper-slide-active" data-swiper-slide-index="2"
                    style="width: 335px;">
                        <a href="' . $return . '" target="' . $v->target . '" class="p-index-a2-box">
                            <div class="p-index-a2-pos">
                                <i class="p-index-a2-pos-img i300"><img
                                        src="' . $v->big_img . '" alt=""
                                        oncontextmenu="return false;"></i>
                                <div class="p-index-a2-pos-box">
                                    <div class="p-index-a2-pos-ti1" swiper-animate-effect="fadeInUp"
                                        data-wow-duration="1.5s">
                                        <img src="' . $v->number_img . '" alt=""
                                            oncontextmenu="return false;">
                                    </div>
                                    <div class="p-index-a2-pos-ti2" swiper-animate-effect="fadeInUp">' . $v->title . '</div>
                                    <div class="p-index-a2-pos-ti4" swiper-animate-effect="fadeInUp">
                ';
                if ($v->goods_desc) {
                    foreach ($v->goods_desc as $kk => $vv) {
                        if ($vv->d_page_id) {
                            $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                            $arrray = explode('&', $idurl);
                            foreach ($arrray as $key => $value) {
                                if (substr($value, 0, 3) == 'id=') {
                                    $arrray[$key] = 'id=' . base64_encode($vv->d_page_id);
                                }
                            }

                            $return = implode('&', $arrray);
                        } else {
                            $return = $vv->d_page;
                        }
                        $output .= '<div onclick="toClickDesc(event,\'/' . $return . '\',\'' . $vv->d_target . '\')">' . $vv->title_list . '</div>';
                    }
                }
                $output .= '
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                ';
            }
            $output .= '</div>
                    </div>
                </div>
                <script>
                    var swiper1 = new Swiper("' . $addon_id . ' .swiper-container1", {
                        slidesPerView: 2,
                        slidesPerGroup: 1,
                        loop: true,
                        speed: 600
                    });
                    function toClickDesc(e, link, target) {
                        if (link) {
                            e.preventDefault()
                            window.open(link, target || "_self")
                        }
                    }
                </script>
            ';
        }
        elseif ($service_product_type == 'type2') {
            $jw_tab_item_goods_two = (isset($settings->jw_tab_item_goods_two) && $settings->jw_tab_item_goods_two) ? $settings->jw_tab_item_goods_two : '';
            $toufang_font = (isset($settings->toufang_font) && $settings->toufang_font) ? $settings->toufang_font : '开始投放';
            $toufang_url = (isset($settings->toufang_url) && $settings->toufang_url) ? $settings->toufang_url : 'https://p.qiao.baidu.com/cps/chat?siteId=689930&amp;userId=2816398&amp;siteToken=593d428f846f036119c10b4fd588d20a';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' a {
                        text-decoration: none;
                    }
                    ' . $addon_id . ' .xxdgl_b {
                        padding: 49px 0 0;
                    }
                    ' . $addon_id . ' .a1 {
                        height: 800px;
                        position: relative;
                    }
                    ' . $addon_id . ' .a1 .swiper {
                        height: 100%;
                    }
                    ' . $addon_id . ' .swiper-pointer-events {
                        touch-action: pan-y;
                    }
                    ' . $addon_id . ' .swiper {
                        margin-left: auto;
                        margin-right: auto;
                        position: relative;
                        overflow: hidden;
                        list-style: none;
                        padding: 0;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: flex;
                        transition-property: transform;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .a1 .swiper .swiper-slide {
                        overflow: hidden;
                        display: flex;
                        justify-content: center;
                        align-items: flex-start;
                        flex-direction: column;
                        color: #fff;
                        padding-bottom: 140px;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        transition-property: transform;
                    }
                    ' . $addon_id . ' .a1 .swiper .slide-inner {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        background-size: cover;
                        background-position: center;
                    }
                    ' . $addon_id . ' .a1 .swiper .slide-inner.sj {
                        display: none;
                    }
                    ' . $addon_id . ' .a1 .swiper .swiper-slide h6 {
                        width: 81%;
                        margin: 0 auto;
                        font-size: 50px;
                        line-height: 1.4;
                        margin-bottom: 20px;
                        font-weight: bold;
                        z-index: 2;
                    }
                    ' . $addon_id . ' .a1 .swiper .swiper-slide p {
                        width: 81%;
                        margin: 0 auto;
                        font-size: 20px;
                        line-height: 2;
                        z-index: 2;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 {
                        width: 780px;
                        position: absolute;
                        bottom: 0;
                        left: calc(50% - 780px);
                        height: 140px;
                        z-index: 2;
                        display: flex;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item:hover {
                        background-color: #d02226;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item:first-child {
                        border-left: 1px solid rgba(232, 232, 232, 0.21);
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item {
                        cursor: pointer;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 220px;
                        background-color: transparent;
                        border-top: 1px solid rgba(232, 232, 232, 0.21);
                        border-right: 1px solid rgba(232, 232, 232, 0.21);
                        flex-direction: column;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item span {
                        width: 52px;
                        height: 52px;
                        display: block;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item span img {
                        height: 100%;
                        width: 100%;
                        display: block;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .item em {
                        margin-top: 15px;
                        font-size: 18px;
                        color: #FFFFFF;
                        line-height: 20px;
                        text-align: center;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .more {
                        width: 450px;
                        height: 140px;
                        background: #D02226;
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        line-height: 1;
                        font-size: 24px;
                        color: #FFFFFF;
                        z-index: 2;
                    }
                    ' . $addon_id . ' .xxdgl_b .a1 .a2 .select {
                        background-color: #d02226;
                    }
                    @media screen and (max-width: 768px){
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide {
                            padding-bottom: 50px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 {
                            height: 290px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.pc {
                            display: none;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.sj {
                            display: block;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            font-size: 17px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide p {
                            width: 150px;
                            font-size: 13px;
                            margin: 0 auto 0 10px;
                            line-height: 1.5;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 {
                            height: 65px;
                            left:0;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .more {
                            width: 115px;
                            height: 65px;
                            font-size: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item {
                            width: 80px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item span {
                            width: 25px;
                            height: 25px;
                            margin-bottom: 2.5px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item em {
                            margin-top: 0px;
                            font-size: 11px;
                            color: #FFFFFF;
                            line-height: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            width: calc(100% - 20px);
                        }
                    }
                    @media screen and (min-width: 1300px) and (max-width: 1560px){
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide {
                            padding-bottom: 50px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 {
                            height: 700px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.pc {
                            display: none;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.sj {
                            display: block;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            font-size: 46px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide p {
                            width: 500px;
                            font-size: 20px;
                            margin: 0 auto 0 20px;
                            line-height: 1.5;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 {
                            height: 130px;
                            left:0;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .more {
                            width: 400px;
                            height: 130px;
                            font-size: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item {
                            width: 200px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item span {
                            width: 52px;
                            height: 50px;
                            margin-bottom: 2.5px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item em {
                            margin-top: 7.5px;
                            font-size: 11px;
                            color: #FFFFFF;
                            line-height: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            width: calc(100% - 40px);
                        }
                    } 
                    @media screen and (min-width: 768px) and (max-width: 1300px){
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide {
                            padding-bottom: 50px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 {
                            height: 560px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.pc {
                            display: none;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .slide-inner.sj {
                            display: block;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            font-size: 46px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide p {
                            width: 500px;
                            font-size: 20px;
                            margin: 0 auto 0 20px;
                            line-height: 1.5;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 {
                            height: 130px;
                            left:0;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .more {
                            width: 240px;
                            height: 130px;
                            font-size: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item {
                            width: 160px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item span {
                            width: 52px;
                            height: 50px;
                            margin-bottom: 2.5px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .a2 .item em {
                            margin-top: 7.5px;
                            font-size: 11px;
                            color: #FFFFFF;
                            line-height: 13px;
                        }
                        ' . $addon_id . ' .xxdgl_b .a1 .swiper .swiper-slide h6 {
                            width: calc(100% - 40px);
                        }
                    } 
                </style>
            ';
            $output .= '
                <div class="xxdgl_b">
                    <div class="a1 wow fadeInUp animated animated" data-wow-duration="2s"
                        style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;">
                        <div class="swiper swiper-initialized swiper-horizontal swiper-pointer-events">
                            <div class="swiper-wrapper" id="swiper-wrapper-d4ff410ab8331023be" aria-live="off"
                                style="cursor: grab; transition-duration: 0ms; transform: translate3d(-10px, 0px, 0px);">
            ';
                                foreach ($jw_tab_item_goods_two as $k => $v) {
                                    $dd = ($k + 1) . '/' . count($jw_tab_item_goods_two);
                                    $output .= '
                                        <div class="swiper-slide swiper-slide-duplicate-active" data-swiper-slide-index="' . $k . '" role="group"
                                        aria-label="' . $dd . '" style="width: 1903px; transition: all 0ms ease 0s;">
                                            <div class="slide-inner pc"
                                                style="background-image: url(' . $v->big_img_pc . '); transform: translate3d(3806px, 0px, 0px); transition: all 0ms ease 0s;">
                                            </div>
                                            <div class="slide-inner sj" style="background-image:url(' . $v->big_img_sj . ')">
                                            </div>
                                            <h6 class="ani " swiper-animate-effect="fadeInDown" swiper-animate-duration="0.8s"
                                                swiper-animate-delay="0s"
                                                style="visibility: hidden; animation-duration: 0.8s; animation-delay: 0s;"
                                                swiper-animate-style-cache="visibility: hidden; animation-duration: 0.8s; animation-delay: 0s;">
                                                ' . $v->title . '</h6>
                                            <p class="ani     " swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s"
                                                swiper-animate-delay="0s"
                                                style="visibility: hidden; animation-duration: 0.8s; animation-delay: 0s;"
                                                swiper-animate-style-cache="visibility: hidden; animation-duration: 0.8s; animation-delay: 0s;">
                                                ' . $v->goods_desc . '</p>
                                        </div>
                                    ';
                                }
                                $output .= '<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                            </div>';
            $output .= '
                            <div class="a2">
            ';
                                foreach ($jw_tab_item_goods_two as $k => $v) {
                                    if ($k == 0) {
                                        $output .= '
                                            <div class="item select">
                                                <span><img src="' . $v->button_img . '" alt="" srcset="" oncontextmenu="return false;"></span>
                                                <em>' . $v->button_title . '</em>
                                            </div>
                                        ';
                                    } else {
                                        $output .= '
                                            <div class="item ">
                                                <span><img src="' . $v->button_img . '" alt="" srcset=""
                                                        oncontextmenu="return false;"></span>
                                                <em>' . $v->button_title . '</em>
                                            </div>
                                        ';
                                    }
                                }
            $output .= '    </div>
                            <a href="' . $toufang_url . '"
                                class="more" target="_blank">' . $toufang_font . '</a>
                        </div>
                    </div>
                </div>
                <script>
                    var interleaveOffset = 0.5; //鐟欏棗妯婂В鏂库偓锟�
                    var xxdgl_b = new Swiper("' . $addon_id . ' .xxdgl_b .swiper", {
                        loop: true,
                        speed: 1000,
                        grabCursor: true,
                        watchSlidesProgress: true,
                        mousewheelControl: true,
                        keyboardControl: true,
                        autoplay: {
                            delay: 5000,
                            disableOnInteraction: false,
                        },
                        on: {
                            init: function () {
                                swiperAnimateCache(this);
                                swiperAnimate(this);
                            },
                            slideChangeTransitionEnd: function (swiper) {
                                swiperAnimate(this);
                            },
                            slideChangeTransitionStart: function (swiper) {
                                $("' . $addon_id . ' .xxdgl_b .a1 .a2 .item").eq(swiper.realIndex).addClass("select").siblings().removeClass("select");
                            },
                            progress: function (swiper) {
                                for (var i = 0; i < swiper.slides.length; i++) {
                                    var slideProgress = swiper.slides[i].progress;
                                    var innerOffset = swiper.width * interleaveOffset;
                                    var innerTranslate = slideProgress * innerOffset;
                                    swiper.slides[i].querySelector(".slide-inner").style.transform =
                                        "translate3d(" + innerTranslate + "px, 0, 0)";
                                }
                            },
                            touchStart: function (swiper) {
                                for (var i = 0; i < swiper.slides.length; i++) {
                                    swiper.slides[i].style.transition = "";
                                }
                            },
                            setTransition: function (swiper, speed) {
                                swiperAnimate(this);
                                for (var i = 0; i < swiper.slides.length; i++) {
                                    swiper.slides[i].style.transition = speed + "ms";
                                    swiper.slides[i].querySelector(".slide-inner").style.transition =
                                        speed + "ms";
                                }
                            }
                        }
                    });
                    $(document).on("click", "' . $addon_id . ' .xxdgl_b .a1 .a2 .item", function () {
                        let index = $(this).index();
                        if ($(this).hasClass("select")) {
                            return false;
                        } else {
                            $(this).addClass("select").siblings().removeClass("select");
                            xxdgl_b.slideToLoop(index, 1000, false);
                        }
                    });
                </script>
            ';
        }
        elseif ($service_product_type == 'type3') {
            $jw_tab_item_goods_three = (isset($settings->jw_tab_item_goods_three) && $settings->jw_tab_item_goods_three) ? $settings->jw_tab_item_goods_three : '';
            $hover_bgcolor_type3 = (isset($settings->hover_bgcolor_type3) && $settings->hover_bgcolor_type3) ? $settings->hover_bgcolor_type3 : '#d9012a';
            $toufang_font_type3 = (isset($settings->toufang_font_type3) && $settings->toufang_font_type3) ? $settings->toufang_font_type3 : '开始投放';
            $toufang_font_color_type3 = (isset($settings->toufang_font_color_type3) && $settings->toufang_font_color_type3) ? $settings->toufang_font_color_type3 : '#fff';
            $toufang_border_color_type3 = (isset($settings->toufang_border_color_type3) && $settings->toufang_border_color_type3) ? $settings->toufang_border_color_type3 : '#fff';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .swiper-pointer-events {
                        touch-action: pan-y;
                    }
                    ' . $addon_id . ' .swiper {
                        margin-left: auto;
                        margin-right: auto;
                        position: relative;
                        overflow: hidden;
                        list-style: none;
                        padding: 0;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: flex;
                        transition-property: transform;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .ju2 li {
                        width: 20%;
                        position: relative;
                        animation-name: fadeInUp;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        flex-shrink: 0;
                        width: 100%;
                        /* height: 100%; */
                        position: relative;
                        transition-property: transform;
                    }
                    ' . $addon_id . ' .ju2 li>img {
                        width: 100%;
                        height: 100%;
                        transition: all .6s;
                    }
                    ' . $addon_id . ' .ju2_con {
                        width: 100%;
                        left: 0;
                        top: 0;
                        padding: 50% 0 0;
                        color: #fff;
                    }
                    ' . $addon_id . ' .ju2_con img {
                        margin: 0 auto;
                        transition: all .6s;
                    }
                    ' . $addon_id . ' .ju2_line {
                        width: 40px;
                        height: 3px;
                        background: #fff;
                        margin: 15% auto;
                        transition: all .6s;
                    }
                    ' . $addon_id . ' .ju2_con h3 {
                        font-size: 24px;
                        transition: all .6s;
                    }
                    ' . $addon_id . ' .ju2_con p {
                        font-size: 18px;
                        transition: all .6s;
                        opacity: 0;
                    }
                    ' . $addon_id . ' .ju2_btn {
                        width: 145px;
                        height: 48px;
                        border: solid 1px '.$toufang_border_color_type3.';
                        color: '.$toufang_font_color_type3.';
                        font-size: 18px;
                        border-radius: 24px;
                        display: block;
                        line-height: 46px;
                        margin: 0 auto;
                        transition: all .6s;
                        opacity: 0;
                    }
                    ' . $addon_id . ' .ju2 li::after {
                        width: 100%;
                        height: 0;
                        content: "";
                        background: '.$hover_bgcolor_type3.';
                        left: 0;
                        bottom: 0;
                        position: absolute;
                        z-index: -1;
                        transition: all .6s;
                    }
                    ' . $addon_id . ' .ju2 li:hover>img {
                        opacity: 0;
                    }
                    ' . $addon_id . ' .ju2 li:hover .ju2_con img {
                        transform: translateY(-60px);
                    }
                    ' . $addon_id . ' .ju2 li:hover .ju2_line {
                        opacity: 0;
                    }
                    ' . $addon_id . ' .ju2 li:hover .ju2_con h3 {
                        transform: translateY(-130px);
                    }
                    ' . $addon_id . ' .ju2 li:hover .ju2_con p {
                        opacity: 1;
                        transform: translateY(-110px);
                    }
                    ' . $addon_id . ' .ju2 li:hover .ju2_btn {
                        opacity: 1;
                    }
                    ' . $addon_id . ' .ju2 li:hover::after {
                        height: 100%;
                    }
                    ' . $addon_id . ' .abs {
                        position: absolute;
                    }
                    ' . $addon_id . ' .tc {
                        text-align: center;
                    }
                    @media only screen and (max-width: 1440px){
                        ' . $addon_id . ' .ju2_con {
                            padding: 40% 0 0;
                        }
                        ' . $addon_id . ' .ju2_con h3 {
                            font-size: 20px;
                        }
                        ' . $addon_id . ' .ju2_con p {
                            font-size: 14px;
                        }
                        ' . $addon_id . ' .ju2_btn {
                            font-size: 16px;
                        }
                        ' . $addon_id . ' .ju2 li:hover .ju2_con h3 {
                            transform: translateY(-122px);
                        }
                    }
                    @media only screen and (max-width: 768px){
                        ' . $addon_id . ' .ju2_con h3 {
                            font-size: 13px;
                            transition: all .6s;
                            margin: 5px;
                        }
                        ' . $addon_id . ' .ju2_con img {
                            width: 32px;
                            margin: 0 auto;
                            transition: all .6s;
                        }
                        ' . $addon_id . ' .ju2_btn {
                            width: 72.5px;
                            height: 24px;
                            font-size: 12px;
                            border-radius: 12px;
                            line-height: 23px;
                        }
                        ' . $addon_id . ' .ju2 li:hover .ju2_con h3 {
                            transform: translateY(-65px);
                        }
                        ' . $addon_id . ' .ju2 li:hover .ju2_con img {
                            transform: translateY(-30px);
                        }
                        ' . $addon_id . ' .ju2 li:hover .ju2_con p {
                            opacity: 1;
                            transform: translateY(-55px);
                        }
                        ' . $addon_id . ' .ju2 li:hover .ju2_btn {
                            opacity: 1;
                            transform: translateY(-45px);
                        }
                    }
                    ' . $addon_id . ' a {
                        text-decoration: none;
                    }
                    ' . $addon_id . ' li, ' . $addon_id . ' ul {
                        list-style: none;
                    }
                </style>
            ';
            $output .= '
                <div class="swiper ju2 swiper-initialized swiper-horizontal swiper-pointer-events">
                    <ul class="swiper-wrapper" id="swiper-wrapper-5750d96cff25d85e" aria-live="off"
                        style="transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;">
            ';
            foreach ($jw_tab_item_goods_three as $k => $v) {
                if ($v->detail_page_id) {
                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                    $arrray = explode('&', $idurl);
                    foreach ($arrray as $key => $value) {
                        if (substr($value, 0, 3) == 'id=') {
                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                        }
                    }

                    $return = implode('&', $arrray);
                } else {
                    $return = $v->detail_page;
                }
                $dd = ($k + 1) . ' / ' . count($jw_tab_item_goods_three);
                $output .= '
                    <li class="wow swiper-slide animated swiper-slide-active" role="group" aria-label="' . $dd . '"
                    style=" visibility: visible; animation-duration: 2s; animation-name: fadeInDown;"
                    data-wow-duration="2s">
                        <img src="' . $v->big_img . '" oncontextmenu="return false;">
                        <div class="ju2_con abs tc">
                            <img src="' . $v->icon . '" oncontextmenu="return false;">
                            <div class="ju2_line"></div>
                            <h3>' . $v->title . '</h3>
                            <p>' . $v->goods_desc . '</p>
                            <a href="' . $return . '" class="ju2_btn">'.$toufang_font_type3.'</a>
                        </div>
                    </li>
                ';
            }
            $output .= '       
                    </ul>
                    <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                </div>
                <script>
                    var swiper = new Swiper("' . $addon_id . ' .ju2", {
                        slidesPerView: 2,
                        spaceBetween: 10,
                        autoplay: true,
                        breakpoints: {
                            1024: {
                                slidesPerView: 5,
                                spaceBetween: 0,
                            },
                        },
                    });
                </script>
            ';
        }
        elseif ($service_product_type == 'type4') {
            $jw_tab_item_goods_four = (isset($settings->jw_tab_item_goods_four) && $settings->jw_tab_item_goods_four) ? $settings->jw_tab_item_goods_four : '';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    @media only screen and (min-width: 1440px){
                        ' . $addon_id . ' .t-ind6-a3 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .t-ind6-a31 {
                            width: calc((100% - 15px*4)/5);
                            height: 520px;
                            border-radius: 50px;
                            border: 2px dashed #e9e9e9;
                            overflow: hidden;
                            float: left;
                            margin-right: 6px;
                            margin-left: 6px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 {
                            width: 100%;
                            padding-top: 43px;
                            background: #f5f8fe;
                            padding-left: 25px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 24px;
                            line-height: 24px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(2) {
                            margin-top: 17px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 24px;
                            line-height: 24px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(3) {
                            width: 58px;
                            height: 6px;
                            background: #135efc;
                            margin-top: 30px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 24px;
                            line-height: 24px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a33 {
                            width: 100%;
                            margin-top: 40px;
                        }
                        ' . $addon_id . ' .t-ind6-a33 img {
                            width: calc(100%-20px);
                            margin: 0 auto;
                        }
                        ' . $addon_id . ' .t-ind6-a34 {
                            width: 100%;
                            padding-left: 25px;
                            margin-top: 37px;
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(1) {
                            font-size: 18px;
                            line-height: 18px;
                            color: #666666;
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(2) {
                            font-size: 24px;
                            line-height: 24px;
                            margin-top: 23px;
                            font-family: "uni";
                        }
                    }
                    @media only screen and (max-width: 1439px) and (min-width: 1024px){
                        ' . $addon_id . ' .t-ind6-a3 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .t-ind6-a31 {
                            width: calc((100% - 15px*4)/5);
                            height: calc(1000px*520/1400);
                            border-radius: 50px;
                            border: 2px dashed #e9e9e9;
                            overflow: hidden;
                            float: left;
                            margin-right: 6px;
                            margin-left: 6px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 {
                            width: 100%;
                            padding-top: calc(960px*43/1400);
                            background: #f5f8fe;
                            padding-left: 15px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 16px;
                            line-height: 16px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(2) {
                            margin-top: calc(960px*17/1400);
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 16px;
                            line-height: 16px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(3) {
                            width: 58px;
                            height: 6px;
                            background: #135efc;
                            margin-top: calc(960px*30/1400);
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 16px;
                            line-height: 16px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a33 {
                            width: 100%;
                            margin-top: calc(960px*40/1400);
                        }
                        ' . $addon_id . ' .t-ind6-a33 img {
                            width: calc(100% - 20px);
                            margin: 0 auto;
                        }
                        ' . $addon_id . ' .t-ind6-a34 {
                            width: 100%;
                            padding-left: 15px;
                            margin-top: calc(960px*37/1400);
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(1) {
                            font-size: 14px;
                            line-height: 14px;
                            color: #666666;
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(2) {
                            font-size: 18px;
                            line-height: 18px;
                            margin-top: calc(960px*23/1400);
                            font-family: "uni";
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        ' . $addon_id . ' .t-ind6-a3 {
                            width: 100%;
                            /* margin-top: 25px; */
                            overflow: hidden;
                            overflow-x: auto;
                            display: -webkit-box;
                        }
                        ' . $addon_id . ' .t-ind6-a31 {
                            width: 140px;
                            border-radius: 25px;
                            border: 1px dashed #e9e9e9;
                            overflow: hidden;
                            margin-right: 2.5px;
                            margin-left: 10px;
                            padding-bottom: 25px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 {
                            width: 100%;
                            padding-top: 20px;
                            background: #f5f8fe;
                            padding-left: 10px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div {
                            font-size: 15px;
                            line-height: 15px;
                            font-weight: bold;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(2) {
                            margin-top: 5px;
                        }
                        ' . $addon_id . ' .t-ind6-a32 div:nth-child(3) {
                            width: 29.5px;
                            height: 3px;
                            background: #135efc;
                            margin-top: 17px;
                        }
                        ' . $addon_id . ' .t-ind6-a33 {
                            width: 100%;
                            margin-top: 20px;
                        }
                        ' . $addon_id . ' .t-ind6-a33 img {
                            width: calc(100% - 20px);
                            margin: 0 auto;
                        }
                        ' . $addon_id . ' .t-ind6-a34 {
                            width: 100%;
                            padding-left: 10px;
                            margin-top: 20px;
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(1) {
                            font-size: 12px;
                            line-height: 12px;
                            color: #666666;
                        }
                        ' . $addon_id . ' .t-ind6-a34 div:nth-child(2) {
                            font-size: 15px;
                            line-height: 15px;
                            margin-top: 23px;
                            font-family: "uni";
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="t-ind6-a3 clear wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s"
                style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
            ';
            foreach ($jw_tab_item_goods_four as $k => $v) {
                $output .= '
                    <div class="t-ind6-a31">
                        <div class="t-ind6-a32">
                            <div>' . $v->title_one . '</div>
                            <div>' . $v->title_two . '</div>
                            <div></div>
                        </div>
                        <div class="t-ind6-a33"><img src="' . $v->big_img . '" oncontextmenu="return false;"></div>
                        <div class="t-ind6-a34">
                            <div>' . $v->title_three . '</div>
                            <div>' . $v->title_four . '</div>
                        </div>
                        <div class="t-ind6-a34">
                            <div>' . $v->title_five . '</div>
                            <div>' . $v->title_six . '</div>
                        </div>
                    </div>
                ';
            }
            $output .= '
                </div>
            ';
        }
        elseif ($service_product_type == 'type5') {
            $jw_tab_item_goods_five = (isset($settings->jw_tab_item_goods_five) && $settings->jw_tab_item_goods_five) ? $settings->jw_tab_item_goods_five : '';
            $background_img = (isset($settings->background_img) && $settings->background_img) ? $settings->background_img : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/05171d7d80a2933fec9dec6f751a7424.png';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .a1 {
                        height: 490px;
                        background: url(' . $background_img . ') center bottom no-repeat;
                        position: relative;
                        margin-top: 0.2rem;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(1) {
                        padding-right: 104px;
                        right: calc(50% + 186px);
                        top: 125px;
                        text-align: right;
                    }
                    ' . $addon_id . ' .a1 .item {
                        position: absolute;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(1) span {
                        right: 0;
                    }
                    ' . $addon_id . ' .a1 .item span {
                        width: 85px;
                        height: 72px;
                        display: block;
                        position: absolute;
                        top: calc(50% - 36px);
                    }
                    ' . $addon_id . ' .a1 .item span img {
                        width: 100%;
                        height: 100%;
                        display: block;
                    }
                    ' . $addon_id . ' .a1 .item span img.sj {
                        display: none;
                    }
                    ' . $addon_id . ' .a1 .item h6 {
                        font-size: 22px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #333333;
                        line-height: 30px;
                        margin-bottom: 10px;
                    }
                    ' . $addon_id . ' .a1 .item p {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: 300;
                        color: #999999;
                        line-height: 24px;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(2) {
                        padding-left: 104px;
                        left: calc(50% + 186px);
                        top: 125px;
                        text-align: left;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(2) span {
                        left: 0;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(3) {
                        padding-right: 104px;
                        right: calc(50% + 186px);
                        top: 330px;
                        text-align: right;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(3) span {
                        right: 0;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(4) {
                        padding-left: 104px;
                        left: calc(50% + 186px);
                        top: 330px;
                        text-align: left;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(4) span {
                        left: 0;
                    }
                    @media screen and (max-width: 1300px){
                        ' . $addon_id . ' .a1 {
                            height: 430px;
                            background: url(' . $background_img . ') center bottom no-repeat;
                            background-size: 680px auto;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(1) {
                            padding-right: 100px;
                            right: calc(50% + 140px);
                            top: 125px;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(2) {
                            padding-left: 100px;
                            left: calc(50% + 140px);
                            top: 125px;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(3) {
                            padding-right: 100px;
                            right: calc(50% + 140px);
                            top: 280px;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(4) {
                            padding-left: 100px;
                            left: calc(50% + 140px);
                            top: 280px;
                        }
                    }
                    @media screen and (max-width: 1060px){
                        ' . $addon_id . ' .a1 {
                            height: auto;
                            background: none;
                            position: relative;
                            padding-top: 40px;
                        }
                        ' . $addon_id . ' .a1 .item {
                            padding: 0 !important;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            position: relative;
                            margin-bottom: 40px;
                            right: auto !important;
                            left: auto !important;
                            top: auto !important;
                            bottom: auto !important;
                            text-align: left !important;
                            padding-left: 112px !important;
                        }
                        ' . $addon_id . ' .a1 .item span {
                            width: 85px;
                            height: 72px;
                            display: block;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .a1 .item span img.pc {
                            display: none;
                        }
                        ' . $addon_id . ' .a1 .item span img.sj {
                            display: block;
                        }
                        ' . $addon_id . ' .a1 .item h6 {
                            font-size: 30px;
                            line-height: 40px;
                            margin-bottom: 10px;
                        }
                        ' . $addon_id . ' .a1 .item p {
                            font-size: 26px;
                            line-height: 1.5;
                        }
                    }
                    @media screen and (max-width: 500px){
                        ' . $addon_id . ' .a1 {
                            height: auto;
                            background: none;
                            position: relative;
                            padding-top: 40px;
                        }
                        ' . $addon_id . ' .a1 .item {
                            padding: 0 !important;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            position: relative;
                            margin-bottom: 20px;
                            right: auto !important;
                            left: auto !important;
                            top: auto !important;
                            bottom: auto !important;
                            text-align: left !important;
                            padding-left: 56px !important;
                        }
                        ' . $addon_id . ' .a1 .item span {
                            width: 42.5px;
                            height: 36px;
                            display: block;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .a1 .item span img.pc {
                            display: none;
                        }
                        ' . $addon_id . ' .a1 .item span img.sj {
                            display: block;
                        }
                        ' . $addon_id . ' .a1 .item h6 {
                            font-size: 15px;
                            line-height: 20px;
                            margin-bottom: 5px;
                        }
                        ' . $addon_id . ' .a1 .item p {
                            font-size: 13px;
                            line-height: 1.5;
                        }
                    }
                    ' . $addon_id . ' .fadeInLeft {
                        -webkit-animation-name: fadeInLeft;
                        animation-name: fadeInLeft;
                      }
                    ' . $addon_id . ' .fadeInRight {
                        -webkit-animation-name: fadeInRight;
                        animation-name: fadeInRight;
                    }
                    @keyframes fadeInRight {
                        from {
                          opacity: 0;
                          -webkit-transform: translate3d(100%, 0, 0);
                          transform: translate3d(100%, 0, 0);
                        }
                      
                        to {
                          opacity: 1;
                          -webkit-transform: none;
                          transform: none;
                        }
                    }
                    @keyframes fadeInLeft {
                        from {
                          opacity: 0;
                          -webkit-transform: translate3d(-100%, 0, 0);
                          transform: translate3d(-100%, 0, 0);
                        }
                      
                        to {
                          opacity: 1;
                          -webkit-transform: none;
                          transform: none;
                        }
                      }
                      @-webkit-keyframes fadeInRight {
                        from {
                          opacity: 0;
                          -webkit-transform: translate3d(100%, 0, 0);
                          transform: translate3d(100%, 0, 0);
                        }
                      
                        to {
                          opacity: 1;
                          -webkit-transform: none;
                          transform: none;
                        }
                      }
                      @-webkit-keyframes fadeInLeft {
                        from {
                          opacity: 0;
                          -webkit-transform: translate3d(-100%, 0, 0);
                          transform: translate3d(-100%, 0, 0);
                        }
                      
                        to {
                          opacity: 1;
                          -webkit-transform: none;
                          transform: none;
                        }
                      }
                </style>
            ';
            $output .= '
                <div class="a1 wow fadeIn animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">
            ';
            foreach ($jw_tab_item_goods_five as $k => $v) {
                if ($k % 2 == 0) {
                    $output .= '
                        <div class="item wow fadeInLeft animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s; animation-name: fadeInLeft;">
                            <span>
                                <img class="pc" src="' . $v->icon_pc . '" oncontextmenu="return false;">
                                <img class="sj" src="' . $v->icon_sj . '" oncontextmenu="return false;">
                            </span>
                            <h6>' . $v->title . '</h6>
                            <p>' . $v->content . '</p>
                        </div>
                    ';
                } else {
                    $output .= '
                        <div class="item wow fadeInRight animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s; animation-name: fadeInRight;">
                            <span>
                                <img class="pc" src="' . $v->icon_pc . '" oncontextmenu="return false;">
                                <img class="sj" src="' . $v->icon_sj . '" oncontextmenu="return false;">
                            </span>
                            <h6>' . $v->title . '</h6>
                            <p>' . $v->content . '</p>
                        </div>
                    ';
                }
            }
            $output .= '
                </div>
            ';
        }
        elseif ($service_product_type == 'type6') {
            $jw_tab_item_goods_six = (isset($settings->jw_tab_item_goods_six) && $settings->jw_tab_item_goods_six) ? $settings->jw_tab_item_goods_six : '';
            $background_img_type6 = (isset($settings->background_img_type6) && $settings->background_img_type6) ? $settings->background_img_type6 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/f594ac3fd9eee028864699943d38839f.png';
            $background_img_sj_type6 = (isset($settings->background_img_sj_type6) && $settings->background_img_sj_type6) ? $settings->background_img_sj_type6 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/338dacfd82e09bd12308b5193efb5693.png';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .a1 {
                        height: 720px;
                        background: url(' . $background_img_type6 . ') center center no-repeat;
                        position: relative;
                        margin: 20px 0 20px;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(1) {
                        right: calc(50% + 182px);
                        top: -34px;
                        animation: sy1 3s -2s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item {
                        position: absolute;
                        width: 106px;
                        height: 106px;
                    }
                    ' . $addon_id . ' .a1 .item span {
                        display: block; 
                        width: 106px;
                        height: 106px;
                    }
                    ' . $addon_id . ' .a1 .item span img {
                        display: block;
                        width: 100%;
                        height: 100%;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(1) em {
                        // position: absolute;
                        // top: calc(50% - 22px);
                        // left: -80px;
                    }
                    ' . $addon_id . ' .a1 .item em {
                        line-height: 46px;
                        font-size: 18px;
                        color: #333333;
                        display: block;
                        text-align: center;
                        font-style: normal;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(2) {
                        left: calc(50% + 20px);
                        top: -61px;
                        animation: sy1 2.9s -3s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(3) {
                        left: calc(50% + 300px);
                        top: 120px;
                        animation: sy1 1.8s -2.2s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(4) {
                        left: calc(50% + 430px);
                        top: 304px;
                        animation: sy1 2.7s -2.6s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(5) {
                        left: calc(50% + 282px);
                        top: 515px;
                        animation: sy1 1.6s -2.5s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(6) {
                        right: calc(50% + 256px);
                        top: 550px;
                        animation: sy1 2.1s -1.6s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(7) {
                        right: calc(50% + 440px);
                        top: 410px;
                        animation: sy1 2s -2.5s linear infinite;
                    }
                    ' . $addon_id . ' .a1 .item:nth-child(8) {
                        right: calc(50% + 353px);
                        top: 217px;
                        animation: sy1 3.2s -1.8s linear infinite;
                    }
                    @keyframes sy1 {
                        0% {
                            transform: translateY(0px);
                        }
                        50% {
                            transform: translateY(20px);
                        }
                        100% {
                            transform: translateY(0px);
                        }
                    }
                    @media screen and (max-width: 1200px){
                        ' . $addon_id . ' .a1 {
                            height: 790px;
                            background: url(' . $background_img_sj_type6 . ') center center no-repeat;
                            background-size: 410px auto;
                            position: relative;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(1) {
                            right: calc(50% + 84px);
                            top: 34px;
                            animation: sy1 3s -2s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item span {
                            display: block;
                            width: 106px;
                            height: 106px;
                        }
                        ' . $addon_id . ' .a1 .item {
                            position: absolute;
                            width: 106px;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(1) em {
                            position: relative;
                            top: auto;
                            left: auto;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(2) {
                            left: calc(50% + 20px);
                            top: 20px;
                            animation: sy1 2.9s -3s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item em {
                            line-height: 44px;
                            font-size: 20px;
                            color: #333333;
                            display: block;
                            text-align: center;
                            letter-spacing: -1px;
                            font-style: normal;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(3) {
                            left: calc(50% + 200px);
                            top: 142px;
                            animation: sy1 1.8s -2.2s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(4) {
                            left: calc(50% + 235px);
                            top: 366px;
                            animation: sy1 2.7s -2.6s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(5) {
                            left: calc(50% + 106px);
                            top: 557px;
                            animation: sy1 1.6s -2.5s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(6) {
                            right: calc(50% + 20px);
                            top: 597px;
                            animation: sy1 2.1s -1.6s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(7) {
                            right: calc(50% + 207px);
                            top: 452px;
                            animation: sy1 2s -2.5s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(8) {
                            right: calc(50% + 237px);
                            top: 210px;
                            animation: sy1 3.2s -1.8s linear infinite;
                        }
                    }
                    @media screen and (max-width: 768px){
                        ' . $addon_id . ' .a1 {
                            height: 395px;
                            background: url(' . $background_img_sj_type6 . ') center center no-repeat;
                            background-size: 205px auto;
                            position: relative;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(1) {
                            right: calc(50% + 42px);
                            top: 17px;
                            animation: sy1 3s -2s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item {
                            position: absolute;
                            width: 58px;
                        }
                        ' . $addon_id . ' .a1 .item span {
                            display: block;
                            width: 58px;
                            height: 58px;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(1) em {
                            position: relative;
                            top: auto;
                            left: auto;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(2) {
                            left: calc(50% + 10px);
                            top: 10px;
                            animation: sy1 2.9s -3s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item em {
                            line-height: 22px;
                            font-size: 10px;
                            color: #333333;
                            display: block;
                            text-align: center;
                            letter-spacing: -1px;
                            font-style: normal;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(3) {
                            left: calc(50% + 100px);
                            top: 71px;
                            animation: sy1 1.8s -2.2s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(4) {
                            left: calc(50% + 117.5px);
                            top: 183px;
                            animation: sy1 2.7s -2.6s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(5) {
                            left: calc(50% + 58px);
                            top: 278.5px;
                            animation: sy1 1.6s -2.5s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(6) {
                            right: calc(50% + 10px);
                            top: 298.5px;
                            animation: sy1 2.1s -1.6s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(7) {
                            right: calc(50% + 103.5px);
                            top: 226px;
                            animation: sy1 2s -2.5s linear infinite;
                        }
                        ' . $addon_id . ' .a1 .item:nth-child(8) {
                            right: calc(50% + 118.5px);
                            top: 105px;
                            animation: sy1 3.2s -1.8s linear infinite;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="a1 wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
            ';
            foreach ($jw_tab_item_goods_six as $k => $v) {
                $output .= '
                    <div class="item">
                        <span><img src="' . $v->icon . '" oncontextmenu="return false;"></span>
                        <em>' . $v->title . '</em>
                    </div>
                ';
            }
            $output .= '
                </div>
            ';
        }
        elseif ($service_product_type == 'type7') {
            $jw_tab_item_goods_seven = (isset($settings->jw_tab_item_goods_seven) && $settings->jw_tab_item_goods_seven) ? $settings->jw_tab_item_goods_seven : '';
            $background_img_type7 = (isset($settings->background_img_type7) && $settings->background_img_type7) ? $settings->background_img_type7 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/aeffa7d711b34ccfd624ddfea2318c12.jpeg';
            $background_img_sj_type7 = (isset($settings->background_img_sj_type7) && $settings->background_img_sj_type7) ? $settings->background_img_sj_type7 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/82ca2ccb4f16c202fdb5f8fdb1074930.jpeg';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .i300 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .i300>img {
                        width: 100%;
                        height: 100%;
                    }
                    ' . $addon_id . ' .t3-lb-c1 {
                        display: none;
                    }
                    @media only screen and (min-width: 1600px){
                        ' . $addon_id . ' .t3-lb-b1 {
                            width: 100%;
                            height: 930px;
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-b2 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            /* left: calc(50% - 1920px/2); */
                        }
                        ' . $addon_id . ' .t3-lb-b2>img:nth-child(2) {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-b3 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-b4 {
                            width: 220px;
                            position: absolute;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-b41 {
                            position: absolute;
                            width: 100%;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(1) {
                            left: calc(50% - 407px);
                            top: calc(50% - 260px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(1)>.t3-lb-b41 {
                            top: -73px;
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b4>.t3-lb-b41>div:nth-child(1) {
                            font-size: 18px;
                            line-height: 18px;
                            font-weight: bold;
                            text-align: center;
                        }
                        ' . $addon_id . ' .t3-lb-b4>.t3-lb-b41>div:nth-child(2) {
                            width: 220px;
                            font-size: 14px;
                            line-height: 20px;
                            color: #454545;
                            margin-top: 15px;
                            text-align: center;
                        }
                        ' . $addon_id . ' .t3-lb-b4>div:nth-child(2) {
                            width: 220px;
                            height: 130px;
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-c1 {
                            width: 100%;
                            height: 100%;
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(2) {
                            left: calc(50% - 700px);
                            top: calc(50% - 60px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(2)>.t3-lb-b41 {
                            top: -73px;
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(3) {
                            left: calc(50% - 485px);
                            top: calc(50% + 137px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(3)>.t3-lb-b41 {
                            top: calc(100% + 38px);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(4) {
                            left: calc(50% - 118px);
                            top: calc(50% + 136px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(4)>.t3-lb-b41 {
                            top: calc(100% + 38px);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(5) {
                            left: calc(50% + 280px);
                            top: calc(50% + 138px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(5)>.t3-lb-b41 {
                            top: calc(100% + 38px);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(6) {
                            left: calc(50% + 443px);
                            top: calc(50% - 100px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(6)>.t3-lb-b41 {
                            top: calc(100% + 38px);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(7) {
                            left: calc(50% + 114px);
                            top: calc(50% - 270px);
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(7)>.t3-lb-b41 {
                            top: 25px;
                            left: calc(100% + 40px);
                        }
                        ' . $addon_id . ' .t3-lb-b4:hover {
                            transform: translateY(-20px);
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 1599px) and (min-width: 1024px){
                        ' . $addon_id . ' .t3-lb-b1 {
                            width: 100%;
                            height: 930px;
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-b2 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            /* left: calc(50% - 1920px/2); */
                        }
                        ' . $addon_id . ' .t3-lb-b2>img:nth-child(2) {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-b3 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-b4 {
                            width: calc(960px*220/1560);
                            position: absolute;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-b41 {
                            position: absolute;
                            width: 100%;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(1) {
                            left: calc(50% - (960px*407/1560));
                            top: calc(50% - (960px*260/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(1)>.t3-lb-b41 {
                            top: calc(960px*-73/1560);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b4>.t3-lb-b41>div:nth-child(1) {
                            font-size: calc(960px*18/1560);
                            line-height: calc(960px*18/1560);
                            font-weight: bold;
                            text-align: center;
                        }
                        ' . $addon_id . ' .t3-lb-b4>.t3-lb-b41>div:nth-child(2) {
                            width: calc(960px*220/1560);
                            font-size: calc(960px*14/1560);
                            line-height: calc(960px*20/1560);
                            color: #454545;
                            margin-top: calc(960px*15/1560);
                            text-align: center;
                        }
                        ' . $addon_id . ' .t3-lb-b4>div:nth-child(2) {
                            width: calc(960px*220/1560);
                            height: calc(960px*130/1560);
                            position: relative;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(2) {
                            left: calc(50% - (960px*700/1560));
                            top: calc(50% - (960px*60/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(2)>.t3-lb-b41 {
                            top: calc(960px*-73/1560);
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(3) {
                            left: calc(50% - (960px*485/1560));
                            top: calc(50% + (960px*137/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(3)>.t3-lb-b41 {
                            top: calc(100% + (960px*38/1560));
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(4) {
                            left: calc(50% - (960px*118/1560));
                            top: calc(50% + (960px*136/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(4)>.t3-lb-b41 {
                            top: calc(100% + (960px*38/1560));
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(5) {
                            left: calc(50% + (960px*280/1560));
                            top: calc(50% + (960px*138/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(5)>.t3-lb-b41 {
                            top: calc(100% + (960px*38/1560));
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(6) {
                            left: calc(50% + (960px*443/1560));
                            top: calc(50% - (960px*100/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(6)>.t3-lb-b41 {
                            top: calc(100% + (960px*38/1560));
                            left: 0;
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(7) {
                            left: calc(50% + (960px*114/1560));
                            top: calc(50% - (960px*270/1560));
                        }
                        ' . $addon_id . ' .t3-lb-b3 .t3-lb-b4:nth-child(7)>.t3-lb-b41 {
                            top: calc(960px*25/1560);
                            left: calc(100% + (960px*40/1560));
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        ' . $addon_id . ' .t3-lb-b3 {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-c1 {
                            display: block;
                        }
                        ' . $addon_id . ' .t3-lb-b2>img:nth-child(1) {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-c1 {
                            width: 100%;
                            height: 100%;
                        }
                        ' . $addon_id . ' .t3-lb-call {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .swiper-container {
                            margin-left: auto;
                            margin-right: auto;
                            /* position: relative; */
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .swiper-wrapper {
                            position: relative;
                            width: 100%;
                            height: 100%;
                            z-index: 1;
                            display: -webkit-box;
                            display: -webkit-flex;
                            display: -ms-flexbox;
                            display: flex;
                            -webkit-transition-property: -webkit-transform;
                            transition-property: -webkit-transform;
                            -o-transition-property: transform;
                            transition-property: transform;
                            transition-property: transform,-webkit-transform;
                            -webkit-box-sizing: content-box;
                            box-sizing: content-box;
                        }
                        ' . $addon_id . ' .swiper-slide {
                            -webkit-flex-shrink: 0;
                            -ms-flex-negative: 0;
                            flex-shrink: 0;
                            width: 100%;
                            height: 100%;
                            position: relative;
                            -webkit-transition-property: -webkit-transform;
                            transition-property: -webkit-transform;
                            -o-transition-property: transform;
                            transition-property: transform;
                            transition-property: transform,-webkit-transform;
                        }
                        ' . $addon_id . ' .t3-lb-c2 {
                            width: 100%;
                            padding-top: 250px;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(1) {
                            width: 100%;
                            height: 420px;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .t3-lb-call .swiper-slide-active .t3-lb-c2>div:nth-child(2) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(2) {
                            font-size: 46px;
                            line-height: 46px;
                            font-weight: bold;
                            margin-top: 195px;
                            text-align: center;
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-call .swiper-slide-active .t3-lb-c2>div:nth-child(3) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(3) {
                            font-size: 30px;
                            line-height: 60px;
                            color: #666666;
                            margin-top: 20px;
                            text-align: center;
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-cpag {
                            width: 100%;
                            position: absolute;
                            bottom: 105px;
                            left: 0;
                        }
                        ' . $addon_id . ' .swiper-pagination {
                            position: absolute;
                            text-align: center;
                            -webkit-transition: .3s opacity;
                            -o-transition: .3s opacity;
                            transition: .3s opacity;
                            -webkit-transform: translate3d(0,0,0);
                            transform: translate3d(0,0,0);
                            z-index: 10;
                        }
                        ' . $addon_id . ' .t3-lb-cpag .swiper-pagination-bullet.swiper-pagination-bullet-active {
                            border: none;
                            width: 20px;
                            height: 20px;
                            border-radius: 50%;
                            background: #517dec;
                        }
                        ' . $addon_id . ' .swiper-pagination-bullet-active {
                            opacity: 1;
                            background: #007aff;
                        }
                        ' . $addon_id . ' .swiper-pagination-bullet {
                            display: inline-block;
                        }
                        ' . $addon_id . ' .t3-lb-cpag .swiper-pagination-bullet {
                            width: 20px;
                            height: 20px;
                            border-radius: 50%;
                            border: 1px solid #9b9b9b;
                            background: #fff;
                            margin-right: 17px;
                        }
                    }
                    @media only screen and (max-width: 768px){
                        ' . $addon_id . ' .t3-lb-b3 {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-b2>img:nth-child(1) {
                            display: none;
                        }
                        ' . $addon_id . ' .t3-lb-c1 {
                            width: 100%;
                            height: 100%;
                            display: block;
                        }
                        ' . $addon_id . ' .t3-lb-call {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .swiper-container {
                            margin-left: auto;
                            margin-right: auto;
                            /* position: relative; */
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .swiper-wrapper {
                            position: relative;
                            width: 100%;
                            height: 100%;
                            z-index: 1;
                            display: -webkit-box;
                            display: -webkit-flex;
                            display: -ms-flexbox;
                            display: flex;
                            -webkit-transition-property: -webkit-transform;
                            transition-property: -webkit-transform;
                            -o-transition-property: transform;
                            transition-property: transform;
                            transition-property: transform,-webkit-transform;
                            -webkit-box-sizing: content-box;
                            box-sizing: content-box;
                        }
                        ' . $addon_id . ' .swiper-slide {
                            -webkit-flex-shrink: 0;
                            -ms-flex-negative: 0;
                            flex-shrink: 0;
                            width: 100%;
                            height: 100%;
                            position: relative;
                            -webkit-transition-property: -webkit-transform;
                            transition-property: -webkit-transform;
                            -o-transition-property: transform;
                            transition-property: transform;
                            transition-property: transform,-webkit-transform;
                        }
                        ' . $addon_id . ' .t3-lb-c2 {
                            width: 100%;
                            padding-top: 140.25px;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(1) {
                            width: 100%;
                            height: 231px;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .t3-lb-call .swiper-slide-active .t3-lb-c2>div:nth-child(2) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(2) {
                            font-size: 25.3px;
                            line-height: 25.3px;
                            font-weight: bold;
                            margin-top: 107.25px;
                            text-align: center;
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-call .swiper-slide-active .t3-lb-c2>div:nth-child(3) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-c2>div:nth-child(3) {
                            font-size: 16.5px;
                            line-height: 33px;
                            color: #666666;
                            margin-top: 11px;
                            text-align: center;
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .t3-lb-cpag {
                            width: 100%;
                            position: absolute;
                            bottom: 57.75px;
                            left: 0;
                        }
                        ' . $addon_id . ' .swiper-pagination {
                            position: absolute;
                            text-align: center;
                            -webkit-transition: .3s opacity;
                            -o-transition: .3s opacity;
                            transition: .3s opacity;
                            -webkit-transform: translate3d(0,0,0);
                            transform: translate3d(0,0,0);
                            z-index: 10;
                        }
                        ' . $addon_id . ' .t3-lb-cpag .swiper-pagination-bullet.swiper-pagination-bullet-active {
                            border: none;
                            width: 11px;
                            height: 11px;
                            border-radius: 50%;
                            background: #517dec;
                        }
                        ' . $addon_id . ' .swiper-pagination-bullet-active {
                            opacity: 1;
                            background: #007aff;
                        }
                        ' . $addon_id . ' .swiper-pagination-bullet {
                            display: inline-block;
                        }
                        ' . $addon_id . ' .t3-lb-cpag .swiper-pagination-bullet {
                            width: 11px;
                            height: 11px;
                            border-radius: 50%;
                            border: 0.55px solid #9b9b9b;
                            background: #fff;
                            margin-right: 9.35px;
                        }
                    }
                    ' . $addon_id . ' .i100>img {
                        width: 100%;
                    }
                </style>
            ';
            $output .= '
                <div class="t3-lb-b1">
                    <div class="t3-lb-b2 i300">
                        <img src="' . $background_img_type7 . '" oncontextmenu="return false;">
                        <img src="' . $background_img_sj_type7 . '" oncontextmenu="return false;">
                    </div>
                    <div class="t3-lb-b3">
            ';
            foreach ($jw_tab_item_goods_seven as $k => $v) {
                $output .= '
                    <div class="t3-lb-b4">
                        <div class="t3-lb-b41">
                            <div>' . $v->title . '</div>
                            <div>' . $v->content . '</div>
                        </div>
                        <div class="i300">
                            <img src="' . $v->icon_img_pc . '" oncontextmenu="return false;">
                        </div>
                    </div>
                ';
            }
            $output .= '
                    </div>
                    <div class="t3-lb-c1">
                        <div class="swiper-container t3-lb-call swiper-container-horizontal swiper-container-ios">
                            <div class="swiper-wrapper"
                                style="transition-duration: 0ms; transform: translate3d(80.5385px, 0px, 0px);">
            ';
            foreach ($jw_tab_item_goods_seven as $k => $v) {
                if ($k == 0) {
                    $output .= '
                        <div class="swiper-slide swiper-slide-active" style="width: 516.923px; margin-right: 20px;">
                            <div class="t3-lb-c2">
                                <div class="i100">
                                    <img src="' . $v->icon_img_sj . '" oncontextmenu="return false;">
                                </div>
                                <div>' . $v->title . '</div>
                                <div>' . $v->content . '</div>
                            </div>
                        </div>
                    ';
                } else {
                    $output .= '
                        <div class="swiper-slide" style="width: 516.923px; margin-right: 20px;">
                            <div class="t3-lb-c2">
                                <div class="i100">
                                    <img src="' . $v->icon_img_sj . '" oncontextmenu="return false;">
                                </div>
                                <div>' . $v->title . '</div>
                                <div>' . $v->content . '</div>
                            </div>
                        </div>
                    ';
                }
            }
            $output .= '
                            </div>
                        </div>
                        <div class="swiper-pagination t3-lb-cpag swiper-pagination-bullets">
                            <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                            <span class="swiper-pagination-bullet"></span>
                            <span class="swiper-pagination-bullet"></span>
                            <span class="swiper-pagination-bullet"></span>
                            <span class="swiper-pagination-bullet"></span>
                            <span class="swiper-pagination-bullet"></span>
                            <span class="swiper-pagination-bullet"></span>
                        </div>
                    </div>
                </div>
                <script>
                    var mySwiper2 = new Swiper("' . $addon_id . ' .t3-lb-call", {
                        slidesPerView : 1.3,
                        centeredSlides : true,
                        spaceBetween : 20,
                        pagination: {
                            el: "' . $addon_id . ' .t3-lb-cpag",
                        },
                    })
                </script>
            ';
        }
        elseif ($service_product_type == 'type8') {
            $jw_tab_item_goods_eight = (isset($settings->jw_tab_item_goods_eight) && $settings->jw_tab_item_goods_eight) ? $settings->jw_tab_item_goods_eight : '';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .z-f11-d1 {
                        z-index: 1;
                    }
                    ' . $addon_id . ' .i100 {
                        overflow: hidden;
                    }
                    @keyframes triangle{
                        0%{
                            transform: translateX(0) translateY(0);
                        }
                        25%{
                            transform: translateX(35vw) translateY(20vh);
                        }
                        75%{
                            transform: translateX(105vw) translateY(-20vh);
                        }
                        100%{
                            transform: translateX(140vw) translateY(0);
                        }
                    }
                    ' . $addon_id . ' .i100>img {
                        width: 100%;
                    }
                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            -webkit-transform: translate3d(0, 100%, 0);
                            transform: translate3d(0, 100%, 0);
                        }
                        to {
                            opacity: 1;
                            -webkit-transform: none;
                            transform: none;
                        }
                    }       
                    ' . $addon_id . ' .fadeInUp {
                        -webkit-animation-name: fadeInUp;
                        animation-name: fadeInUp;
                    }
                    ' . $addon_id . ' .animated {
                        -webkit-animation-duration: 1s;
                        animation-duration: 1s;
                        -webkit-animation-fill-mode: both;
                        animation-fill-mode: both;
                    }
                    ' . $addon_id . ' .z-f11-p4 img {
                        display: none;
                    }
                    @media only screen and (min-width: 921px){
                        ' . $addon_id . ' .z-f11-d3:nth-child(1) .z-f11-d4 {
                            background: linear-gradient(#f88774,#ec3175);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(2) .z-f11-d4 {
                            background: linear-gradient(#fdd287,#e4984b);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(3) .z-f11-d4 {
                            background: linear-gradient(#bc62ce,#6647b0);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(4) .z-f11-d4 {
                            background: linear-gradient(#31e69c,#03a990);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(5) .z-f11-d4 {
                            background: linear-gradient(#3faffc,#256ce7);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(1) .z-f11-bt1 {
                            color: #e9568b;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(2) .z-f11-bt1 {
                            color: #ecab5f;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(3) .z-f11-bt1 {
                            color: #8350ba;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(4) .z-f11-bt1 {
                            color: #15c295;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(5) .z-f11-bt1 {
                            color: #2c85ef;
                        }
                    }
                    @media only screen and (min-width: 1440px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 694px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 140px;
                            opacity: 0.2;
                            top: 16%;
                            left: -20vw;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 100px;
                            opacity: 0.4;
                            top: 22%;
                            left: -20vw;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 70px;
                            opacity: 0.6;
                            top: 32%;
                            left: -20vw;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 50px;
                            opacity: 0.8;
                            top: 36%;
                            left: -20vw;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 140px;
                            opacity: 0.2;
                            top: 50%;
                            left: -20vw;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 100px;
                            opacity: 0.4;
                            top: 62%;
                            left: -20vw;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 70px;
                            opacity: 0.6;
                            top: 74%;
                            left: -20vw;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 50px;
                            opacity: 0.8;
                            top: 46%;
                            left: -20vw;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: 1440px;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 720px);
                            z-index: 10;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            height: 100%;
                            float: left;
                            position: relative;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 138px;
                            position: absolute;
                            top: 240px;
                            left: calc(50% - 69px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 36px;
                            font-size: 20px;
                            line-height: 20px;
                            color: #fff;
                            text-align: center;
                            position: absolute;
                            top: 438px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            transition: 0.5s;
                            transform: rotateY(90deg);
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 100%;
                            height: 36px;
                            font-size: 30px;
                            line-height: 36px;
                            color: #FFFFFF;
                            text-align: center;
                            position: absolute;
                            top: 220px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 100%;
                            position: absolute;
                            top: 300px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 18px;
                            text-align: center;
                            width: 100%;
                            display: block;
                            color: #FFFFFF;
                        }
                        ' . $addon_id . ' .z-f11-d3:hover .z-f11-d4 {
                            transform: rotateY(0);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            width: 160px;
                            height: 50px;
                            border: 0;
                            border-radius: 25px;
                            background: #FFFFFF;
                            overflow: hidden;
                            font-size: 18px;
                            line-height: 50px;
                            text-align: center;
                            cursor: pointer;
                            position: absolute;
                            top: 580px;
                            left: calc(50% - 80px);
                        }
                    }
                    @media only screen and (max-width: 1439px) and (min-width: 1279px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 694px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 140px;
                            opacity: 0.2;
                            top: 16%;
                            left: -20vw;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 100px;
                            opacity: 0.4;
                            top: 22%;
                            left: -20vw;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 70px;
                            opacity: 0.6;
                            top: 32%;
                            left: -20vw;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 50px;
                            opacity: 0.8;
                            top: 36%;
                            left: -20vw;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 140px;
                            opacity: 0.2;
                            top: 50%;
                            left: -20vw;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 100px;
                            opacity: 0.4;
                            top: 62%;
                            left: -20vw;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 70px;
                            opacity: 0.6;
                            top: 74%;
                            left: -20vw;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 50px;
                            opacity: 0.8;
                            top: 46%;
                            left: -20vw;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: 1180px;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 590px);
                            z-index: 10;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            height: 100%;
                            float: left;
                            position: relative;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 138px;
                            position: absolute;
                            top: 240px;
                            left: calc(50% - 69px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 36px;
                            font-size: 20px;
                            line-height: 20px;
                            color: #fff;
                            text-align: center;
                            position: absolute;
                            top: 438px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            transition: 0.5s;
                            transform: rotateY(90deg);
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 100%;
                            height: 36px;
                            font-size: 30px;
                            line-height: 36px;
                            color: #FFFFFF;
                            text-align: center;
                            position: absolute;
                            top: 220px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 100%;
                            position: absolute;
                            top: 300px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 18px;
                            text-align: center;
                            width: 100%;
                            display: block;
                            color: #FFFFFF;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            width: 160px;
                            height: 50px;
                            border: 0;
                            border-radius: 25px;
                            background: #FFFFFF;
                            overflow: hidden;
                            font-size: 18px;
                            line-height: 50px;
                            text-align: center;
                            cursor: pointer;
                            position: absolute;
                            top: 580px;
                            left: calc(50% - 80px);
                        }
                        ' . $addon_id . ' .z-f11-d3:hover .z-f11-d4 {
                            transform: rotateY(0);
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 1269px) and (min-width: 921px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 560px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 140px;
                            opacity: 0.2;
                            top: 16%;
                            left: -20vw;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 100px;
                            opacity: 0.4;
                            top: 22%;
                            left: -20vw;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 70px;
                            opacity: 0.6;
                            top: 32%;
                            left: -20vw;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 50px;
                            opacity: 0.8;
                            top: 36%;
                            left: -20vw;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 140px;
                            opacity: 0.2;
                            top: 50%;
                            left: -20vw;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 100px;
                            opacity: 0.4;
                            top: 62%;
                            left: -20vw;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 70px;
                            opacity: 0.6;
                            top: 74%;
                            left: -20vw;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 50px;
                            opacity: 0.8;
                            top: 46%;
                            left: -20vw;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: 900px;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 450px);
                            z-index: 10;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            height: 100%;
                            float: left;
                            position: relative;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 110px;
                            position: absolute;
                            top: 200px;
                            left: calc(50% - 55px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 20px;
                            font-size: 16px;
                            line-height: 20px;
                            color: #fff;
                            text-align: center;
                            position: absolute;
                            top: 360px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            transition: 0.5s;
                            transform: rotateY(90deg);
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 100%;
                            height: 28px;
                            font-size: 24px;
                            line-height: 28px;
                            color: #FFFFFF;
                            text-align: center;
                            position: absolute;
                            top: 100px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 100%;
                            position: absolute;
                            top: 180px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 14px;
                            line-height: 28px;
                            text-align: center;
                            width: 100%;
                            display: block;
                            color: #FFFFFF;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            width: 140px;
                            height: 40px;
                            border: 0;
                            border-radius: 25px;
                            background: #FFFFFF;
                            overflow: hidden;
                            font-size: 14px;
                            line-height: 40px;
                            text-align: center;
                            cursor: pointer;
                            position: absolute;
                            top: 400px;
                            left: calc(50% - 70px);
                        }
                        ' . $addon_id . ' .z-f11-d3:hover .z-f11-d4 {
                            transform: rotateY(0);
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 920px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 705px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 140px;
                            opacity: 0.2;
                            top: 16%;
                            left: -200px;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 100px;
                            opacity: 0.4;
                            top: 22%;
                            left: -200px;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 70px;
                            opacity: 0.6;
                            top: 32%;
                            left: -200px;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 50px;
                            opacity: 0.8;
                            top: 36%;
                            left: -200px;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 140px;
                            opacity: 0.2;
                            top: 50%;
                            left: -200px;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 100px;
                            opacity: 0.4;
                            top: 62%;
                            left: -200px;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 70px;
                            opacity: 0.6;
                            top: 74%;
                            left: -200px;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 50px;
                            opacity: 0.8;
                            top: 46%;
                            left: -200px;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: calc(100% - 48px);
                            position: absolute;
                            top: 90px;
                            left: 24px;
                            z-index: 10;
                            height: 166px;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            float: left;
                            position: relative;
                            height: 166px;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 72px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 36px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 22px;
                            font-size: 22px;
                            line-height: 22px;
                            color: #fff;
                            text-align: center;
                            font-weight: bold;
                            position: absolute;
                            top: 118px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            display: none;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 0;
                            height: 0;
                            font-size: 0;
                            line-height: 0;
                            color: rgba(0,0,0,0);
                            text-align: center;
                            position: absolute;
                            top: 166px;
                            left: calc(50% - 16px);
                            border-bottom: 16px solid rgba(195,178,255,0.25);
                            border-left: 16px solid transparent;
                            border-right: 16px solid transparent;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 500%;
                            position: absolute;
                            top: 182px;
                            left: 0;
                            padding: 25px 50px;
                            box-sizing: border-box;
                            border: 0;
                            border-radius: 10px;
                            background: rgba(195,178,255,0.25);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(1) .z-f11-p4 {
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(2) .z-f11-p4 {
                            left: -100%;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(3) .z-f11-p4 {
                            left: -200%;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(4) .z-f11-p4 {
                            left: -300%;
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(5) .z-f11-p4 {
                            left: -400%;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(odd) {
                            float: left;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(even) {
                            float: right;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 26px;
                            line-height: 90px;
                            color: #FFFFFF;
                            display: block;
                            height: 90px;
                        }
                        ' . $addon_id . ' .z-f11-p4 img {
                            display: inline-block;
                            width: 30px;
                            margin-left: 20px;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            display: none;
                        }
                    }
                    @media only screen and (max-width: 680px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 528.75px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 105px;
                            opacity: 0.2;
                            top: 16%;
                            left: -150px;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 75px;
                            opacity: 0.4;
                            top: 22%;
                            left: -150px;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 52.5px;
                            opacity: 0.6;
                            top: 32%;
                            left: -150px;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 37.5px;
                            opacity: 0.8;
                            top: 36%;
                            left: -150px;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 105px;
                            opacity: 0.2;
                            top: 50%;
                            left: -150px;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 75px;
                            opacity: 0.4;
                            top: 62%;
                            left: -150px;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 52.5px;
                            opacity: 0.6;
                            top: 74%;
                            left: -150px;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 37.5px;
                            opacity: 0.8;
                            top: 46%;
                            left: -150px;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: calc(100% - 36px);
                            position: absolute;
                            top: 67.5px;
                            left: 18px;
                            z-index: 10;
                            height: 124.5px;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            float: left;
                            position: relative;
                            height: 124.5px;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 54px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 27px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 16.5px;
                            font-size: 16.5px;
                            line-height: 16.5px;
                            color: #fff;
                            text-align: center;
                            font-weight: bold;
                            position: absolute;
                            top: 88.5px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            display: none;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 0;
                            height: 0;
                            font-size: 0;
                            line-height: 0;
                            color: rgba(0,0,0,0);
                            text-align: center;
                            position: absolute;
                            top: 124.5px;
                            left: calc(50% - 12px);
                            border-bottom: 12px solid rgba(195,178,255,0.25);
                            border-left: 12px solid transparent;
                            border-right: 12px solid transparent;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 500%;
                            position: absolute;
                            top: 136.5px;
                            left: 0;
                            padding: 18.75px 37.5px;
                            box-sizing: border-box;
                            border: 0;
                            border-radius: 7.5px;
                            background: rgba(195,178,255,0.25);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(1) .z-f11-p4 {
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(odd) {
                            float: left;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(even) {
                            float: right;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 19.5px;
                            line-height: 67.5px;
                            color: #FFFFFF;
                            display: block;
                            height: 67.5px;
                        }
                        ' . $addon_id . ' .z-f11-p4 img {
                            display: inline-block;
                            width: 22.5px;
                            margin-left: 15px;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            display: none;
                        }
                    }
                    @media only screen and (max-width: 520px){
                        ' . $addon_id . ' .z-f11-d1 {
                            width: 100%;
                            height: 352.5px;
                            position: relative;
                            overflow: hidden;
                            background: #2b214f;
                        }
                        ' . $addon_id . ' .z-f11-t1 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-t2 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(1) {
                            width: 70px;
                            opacity: 0.2;
                            top: 16%;
                            left: -100px;
                            animation: triangle 32s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(2) {
                            width: 50px;
                            opacity: 0.4;
                            top: 22%;
                            left: -100px;
                            animation: triangle 28s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(3) {
                            width: 35px;
                            opacity: 0.6;
                            top: 32%;
                            left: -100px;
                            animation: triangle 24s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(4) {
                            width: 25px;
                            opacity: 0.8;
                            top: 36%;
                            left: -100px;
                            animation: triangle 20s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(5) {
                            width: 70px;
                            opacity: 0.2;
                            top: 50%;
                            left: -100px;
                            animation: triangle 34s 1s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(6) {
                            width: 50px;
                            opacity: 0.4;
                            top: 62%;
                            left: -100px;
                            animation: triangle 30s 3s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(7) {
                            width: 35px;
                            opacity: 0.6;
                            top: 74%;
                            left: -100px;
                            animation: triangle 26s 2s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-t2:nth-child(8) {
                            width: 25px;
                            opacity: 0.8;
                            top: 46%;
                            left: -100px;
                            animation: triangle 22s 4s linear infinite;
                        }
                        ' . $addon_id . ' .z-f11-d2 {
                            width: calc(100% - 24px);
                            position: absolute;
                            top: 45px;
                            left: 12px;
                            z-index: 10;
                            height: 83px;
                        }
                        ' . $addon_id . ' .z-f11-d3 {
                            width: 20%;
                            float: left;
                            position: relative;
                            height: 83px;
                        }
                        ' . $addon_id . ' .z-f11-i1 {
                            width: 36px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 18px);
                        }
                        ' . $addon_id . ' .z-f11-p2 {
                            width: 100%;
                            height: 11px;
                            font-size: 11px;
                            line-height: 11px;
                            color: #fff;
                            text-align: center;
                            font-weight: bold;
                            position: absolute;
                            top: 59px;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            display: none;
                        }
                        ' . $addon_id . ' .z-f11-p3 {
                            width: 0;
                            height: 0;
                            font-size: 0;
                            line-height: 0;
                            color: rgba(0,0,0,0);
                            text-align: center;
                            position: absolute;
                            top: 83px;
                            left: calc(50% - 8px);
                            border-bottom: 8px solid rgba(195,178,255,0.25);
                            border-left: 8px solid transparent;
                            border-right: 8px solid transparent;
                        }
                        ' . $addon_id . ' .z-f11-p4 {
                            width: 500%;
                            position: absolute;
                            top: 91px;
                            left: 0;
                            padding: 12.5px 25px;
                            box-sizing: border-box;
                            border: 0;
                            border-radius: 5px;
                            background: rgba(195,178,255,0.25);
                        }
                        ' . $addon_id . ' .z-f11-d3:nth-child(1) .z-f11-p4 {
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(odd) {
                            float: left;
                        }
                        ' . $addon_id . ' .z-f11-p4 span:nth-child(even) {
                            float: right;
                        }
                        ' . $addon_id . ' .z-f11-p4 span {
                            font-size: 13px;
                            line-height: 45px;
                            color: #FFFFFF;
                            display: block;
                            height: 45px;
                        }
                        ' . $addon_id . ' .z-f11-p4 img {
                            display: inline-block;
                            width: 15px;
                            margin-left: 10px;
                        }
                        ' . $addon_id . ' .z-f11-bt1 {
                            display: none;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="z-f11-d1">
                    <div class="z-f11-t1">
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                        <div class="z-f11-t2 i100">
                            <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/601af4cf26674b0836b6cc168bb4238c.png">
                        </div>
                    </div>
                    <div class="z-f11-d2 clear">
            ';
            foreach ($jw_tab_item_goods_eight as $k => $v) {
                if ($v->tz_page_type == 'Internal_pages') {
                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                    $arrray = explode('&', $idurl);
                    foreach ($arrray as $key => $value) {
                        if (substr($value, 0, 3) == 'id=') {
                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                        }
                    }

                    $return = implode('&', $arrray);
                } else {
                    $return = $v->detail_page;
                }
                $output .= '
                    <div class="z-f11-d3 wow fadeInUp animated animated" data-wow-duration="2s"
                    style="visibility: visible; animation-duration: 2s;">
                        <div class="z-f11-i1 i100">
                            <img src="' . $v->icon_img . '">
                        </div>
                        <div class="z-f11-p2">' . $v->title . '</div>
                        <div class="z-f11-d4">
                            <div class="z-f11-p3">' . $v->title . '</div>
                            <div class="z-f11-p4">
                ';
                foreach ($v->goods_desc as $kk => $vv) {
                    if ($vv->tz_page_type_s == 'Internal_pages') {
                        $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                        $arrray = explode('&', $idurl);
                        foreach ($arrray as $key => $value) {
                            if (substr($value, 0, 3) == 'id=') {
                                $arrray[$key] = 'id=' . base64_encode($vv->detail_page_id_s);
                            }
                        }

                        $return = implode('&', $arrray);
                    } else {
                        $return = $vv->detail_page_s;
                    }
                    $output .= '
                        <span>' . $vv->title_list . '<a href="' . $return . '"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/642cd4187e988861478f84d2e26a7356.png"></a></span>
                    ';
                }
                $output .= '
                            </div>
                            <a
                                href="' . $return . '">
                                <div class="z-f11-bt1">点击下载</div>
                            </a>
                        </div>
                    </div>
                ';
            }
            $output .= '
                    </div>
                </div>
                <script>
                    $("' . $addon_id . ' .z-f11-i1").click(function(){
                        if($(window).width()<1024)
                        {
                            if($(this).parents("' . $addon_id . ' .z-f11-d3").find(".z-f11-d4").css("display")=="none")
                            {
                                $("' . $addon_id . ' .z-f11-d4").hide();
                                $(this).parents("' . $addon_id . ' .z-f11-d3").find(".z-f11-d4").fadeIn(300);
                            }
                        }
                    });
                </script>
            ';
        }
        elseif ($service_product_type == 'type9') {
            $jw_tab_item_goods_nine = (isset($settings->jw_tab_item_goods_nine) && $settings->jw_tab_item_goods_nine) ? $settings->jw_tab_item_goods_nine : '';
            $background_img_type9 = (isset($settings->background_img_type9) && $settings->background_img_type9) ? $settings->background_img_type9 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/69994cf9a6ff1c56ed8a665a4426b8e5.jpeg';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .i100 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .i100>img {
                        width: 100%;
                    }
                    ' . $addon_id . ' .animated {
                        -webkit-animation-duration: 1s;
                        animation-duration: 1s;
                        -webkit-animation-fill-mode: both;
                        animation-fill-mode: both;
                    }
                    @keyframes bounceInDown {
                        from, 60%, 75%, 90%, to {
                            -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                            animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                        }
                        0% {
                            opacity: 0;
                            -webkit-transform: translate3d(0, -3000px, 0);
                            transform: translate3d(0, -3000px, 0);
                        }
                        60% {
                            opacity: 1;
                            -webkit-transform: translate3d(0, 25px, 0);
                            transform: translate3d(0, 25px, 0);
                        }
                        75% {
                            -webkit-transform: translate3d(0, -10px, 0);
                            transform: translate3d(0, -10px, 0);
                        }
                        90% {
                            -webkit-transform: translate3d(0, 5px, 0);
                            transform: translate3d(0, 5px, 0);
                        }
                        to {
                            -webkit-transform: none;
                            transform: none;
                        }
                    }
                    ' . $addon_id . ' .bounceInDown {
                        -webkit-animation-name: bounceInDown;
                        animation-name: bounceInDown;
                    }
                    @keyframes bounceInLeft {
                        from, 60%, 75%, 90%, to {
                            -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                            animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                        }
                        0% {
                            opacity: 0;
                            -webkit-transform: translate3d(-3000px, 0, 0);
                            transform: translate3d(-3000px, 0, 0);
                        }
                        60% {
                            opacity: 1;
                            -webkit-transform: translate3d(25px, 0, 0);
                            transform: translate3d(25px, 0, 0);
                        }
                        75% {
                            -webkit-transform: translate3d(-10px, 0, 0);
                            transform: translate3d(-10px, 0, 0);
                        }
                        90% {
                            -webkit-transform: translate3d(5px, 0, 0);
                            transform: translate3d(5px, 0, 0);
                        }
                        to {
                            -webkit-transform: none;
                            transform: none;
                        }
                    }
                    ' . $addon_id . ' .bounceInLeft {
                        -webkit-animation-name: bounceInLeft;
                        animation-name: bounceInLeft;
                    }
                    @keyframes bounceInUp {
                        from, 60%, 75%, 90%, to {
                            -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                            animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                        }
                        from {
                            opacity: 0;
                            -webkit-transform: translate3d(0, 3000px, 0);
                            transform: translate3d(0, 3000px, 0);
                        }
                        60% {
                            opacity: 1;
                            -webkit-transform: translate3d(0, -20px, 0);
                            transform: translate3d(0, -20px, 0);
                        }
                        75% {
                            -webkit-transform: translate3d(0, 10px, 0);
                            transform: translate3d(0, 10px, 0);
                        }
                        90% {
                            -webkit-transform: translate3d(0, -5px, 0);
                            transform: translate3d(0, -5px, 0);
                        }
                        to {
                            -webkit-transform: translate3d(0, 0, 0);
                            transform: translate3d(0, 0, 0);
                        }
                    }
                    ' . $addon_id . ' .bounceInUp {
                        -webkit-animation-name: bounceInUp;
                        animation-name: bounceInUp;
                    }
                    @keyframes bounceInRight {
                        from, 60%, 75%, 90%, to {
                            -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                            animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                        }
                        from {
                            opacity: 0;
                            -webkit-transform: translate3d(3000px, 0, 0);
                            transform: translate3d(3000px, 0, 0);
                        }
                        60% {
                            opacity: 1;
                            -webkit-transform: translate3d(-25px, 0, 0);
                            transform: translate3d(-25px, 0, 0);
                        }
                        75% {
                            -webkit-transform: translate3d(10px, 0, 0);
                            transform: translate3d(10px, 0, 0);
                        }
                        90% {
                            -webkit-transform: translate3d(-5px, 0, 0);
                            transform: translate3d(-5px, 0, 0);
                        }
                        to {
                            -webkit-transform: none;
                            transform: none;
                        }
                    }
                    ' . $addon_id . ' .bounceInRight {
                        -webkit-animation-name: bounceInRight;
                        animation-name: bounceInRight;
                    }
                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            -webkit-transform: translate3d(0, 100%, 0);
                            transform: translate3d(0, 100%, 0);
                        }
                        to {
                            opacity: 1;
                            -webkit-transform: none;
                            transform: none;
                        }
                    }
                    ' . $addon_id . ' .fadeInUp {
                        -webkit-animation-name: fadeInUp;
                        animation-name: fadeInUp;
                    }
                    @media only screen and (min-width: 1440px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            height: 750px;
                            position: relative;
                            background: #f3f3f3;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 1920px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 960px);
                            max-width: none;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 214px;
                            top: 99px;
                            left: calc(50% - (960px - 858px));
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 214px;
                            top: 196px;
                            left: calc(50% - (960px - 790px));
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 213px;
                            top: 305px;
                            left: calc(50% - (960px - 739px));
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 375px;
                            top: 403px;
                            left: calc(50% - (960px - 793px));
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: absolute;
                            width: 480px;
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(1) {
                            top: 125px;
                            left: calc(50% - 706px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(2) {
                            top: 125px;
                            right: calc(50% - 706px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(3) {
                            top: 425px;
                            left: calc(50% - 706px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(4) {
                            top: 425px;
                            right: calc(50% - 706px);
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 24px;
                            height: 50px;
                            border: 0;
                            border-radius: 4px;
                            background: #36b9c8;
                            margin-bottom: 24px;
                            font-size: 22px;
                            line-height: 50px;
                            color: #fff;
                            width: auto!important;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 30px;
                            line-height: 30px;
                            color: #fff;
                            position: relative;
                            top: 4px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 16px;
                            line-height: 28px;
                            color: #fff;
                            width: 100%;
                        }
                    }
                    @media only screen and (max-width: 1439px) and (min-width: 1279px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            height: 626px;
                            position: relative;
                            background: #f3f3f3;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 1600px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 800px);
                            max-width: none;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 180px;
                            top: 70px;
                            left: calc(50% - 90px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 180px;
                            top: 150px;
                            left: calc(50% - 110px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 180px;
                            top: 260px;
                            left: calc(50% - 110px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 320px;
                            top: 360px;
                            left: calc(50% - 150px);
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: absolute;
                            width: 400px;
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(1) {
                            top: 80px;
                            left: calc(50% - 580px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(2) {
                            top: 80px;
                            right: calc(50% - 580px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(3) {
                            top: 360px;
                            left: calc(50% - 580px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(4) {
                            top: 360px;
                            right: calc(50% - 580px);
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 24px;
                            height: 50px;
                            border: 0;
                            border-radius: 4px;
                            background: #36b9c8;
                            margin-bottom: 24px;
                            font-size: 22px;
                            line-height: 50px;
                            color: #fff;
                            width: auto!important;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 30px;
                            line-height: 30px;
                            color: #fff;
                            position: relative;
                            top: 4px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 14px;
                            line-height: 24px;
                            color: #fff;
                            width: 100%;
                        }
                    }
                    @media only screen and (max-width: 1279px) and (min-width: 1024px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            height: 500px;
                            position: relative;
                            background: #f3f3f3;
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 1280px;
                            position: absolute;
                            top: 0;
                            left: calc(50% - 640px);
                            max-width: none;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 120px;
                            top: 60px;
                            left: calc(50% - 60px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 120px;
                            top: 140px;
                            left: calc(50% - 80px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 120px;
                            top: 250px;
                            left: calc(50% - 80px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 200px;
                            top: 280px;
                            left: calc(50% - 100px);
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: absolute;
                            width: 340px;
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 20px;
                            height: 40px;
                            border: 0;
                            border-radius: 4px;
                            background: #36b9c8;
                            margin-bottom: 20px;
                            font-size: 18px;
                            line-height: 40px;
                            color: #fff;
                            width: auto!important;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 24px;
                            line-height: 24px;
                            color: #fff;
                            position: relative;
                            top: 2px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 12px;
                            line-height: 22px;
                            color: #fff;
                            width: 100%;
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(1) {
                            top: 50px;
                            left: calc(50% - 450px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(2) {
                            top: 50px;
                            right: calc(50% - 450px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(3) {
                            top: 260px;
                            left: calc(50% - 450px);
                        }
                        ' . $addon_id . ' .z-f2-d5:nth-child(4) {
                            top: 260px;
                            right: calc(50% - 450px);
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            height: 1560px;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 100%;
                            position: relative;
                            height: 100%;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 183px;
                            top: 51px;
                            left: calc(50% - 79px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 182px;
                            top: 133px;
                            left: calc(50% - 137px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 182px;
                            top: 224px;
                            left: calc(50% - 180px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 305px;
                            top: 312px;
                            left: calc(50% - 134px);
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 582px;
                            left: 24px;
                            width: calc(100% - 48px);
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: relative;
                            width: 100%;
                            margin-bottom: calc(100vw * (24/750));
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 24px;
                            height: 50px;
                            border: 0;
                            border-radius: 4px;
                            background: #36b9c8;
                            margin-bottom: 24px;
                            font-size: 22px;
                            line-height: 50px;
                            color: #fff;
                            width: 250px;
                            box-sizing: border-box;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 30px;
                            line-height: 30px;
                            color: #fff;
                            position: relative;
                            top: 4px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 22px;
                            line-height: 32px;
                            color: #fff;
                            width: 100%;
                        }
                    }
                    @media only screen and (max-width: 768px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            height: 1170px;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 100%;
                            position: relative;
                            height: 100%;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 137.25px;
                            top: 38.25px;
                            left: calc(50% - 59.25px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 136.5px;
                            top: 99.75px;
                            left: calc(50% - 102.75px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 136.5px;
                            top: 168px;
                            left: calc(50% - 135px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 228.75px;
                            top: 234px;
                            left: calc(50% - 100.5px);
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 436.5px;
                            left: 18px;
                            width: calc(100% - 36px);
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: relative;
                            width: 100%;
                            margin-bottom: calc(100vw * (24/750));
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 18px;
                            height: 37.5px;
                            border: 0;
                            border-radius: 3px;
                            background: #36b9c8;
                            margin-bottom: 18px;
                            font-size: 16.5px;
                            line-height: 37.5px;
                            color: #fff;
                            width: 187.5px;
                            box-sizing: border-box;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 22.5px;
                            line-height: 22.5px;
                            color: #fff;
                            position: relative;
                            top: 3px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 16.5px;
                            line-height: 24px;
                            color: #fff;
                            width: 100%;
                        }
                    }
                    @media only screen and (max-width: 480px){
                        ' . $addon_id . ' .z-f2-d2 {
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            height: 780px;
                        }
                        ' . $addon_id . ' .z-f2-d2>img {
                            width: 100%;
                            position: relative;
                            height: 100%;
                        }
                        ' . $addon_id . ' .z-f2-d3 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .z-f2-i1 {
                            position: absolute;
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(1) {
                            width: 91.5px;
                            top: 25.5px;
                            left: calc(50% - 39.5px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(2) {
                            width: 91px;
                            top: 66.5px;
                            left: calc(50% - 68.5px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(3) {
                            width: 91px;
                            top: 112px;
                            left: calc(50% - 90px);
                        }
                        ' . $addon_id . ' .z-f2-i1:nth-child(4) {
                            width: 152.5px;
                            top: 156px;
                            left: calc(50% - 67px);
                        }
                        ' . $addon_id . ' .z-f2-d4 {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 291px;
                            left: 12px;
                            width: calc(100% - 24px);
                        }
                        ' . $addon_id . ' .z-f2-d5 {
                            position: relative;
                            width: 100%;
                            margin-bottom: calc(100vw * (24/750));
                        }
                        ' . $addon_id . ' .z-f2-p3 {
                            padding: 0 12px;
                            height: 25px;
                            border: 0;
                            border-radius: 2px;
                            background: #36b9c8;
                            margin-bottom: 12px;
                            font-size: 11px;
                            line-height: 25px;
                            color: #fff;
                            width: 125px;
                            box-sizing: border-box;
                        }
                        ' . $addon_id . ' .z-f2-p3 span {
                            font-family: "din";
                            font-size: 15px;
                            line-height: 15px;
                            color: #fff;
                            position: relative;
                            top: 2px;
                        }
                        ' . $addon_id . ' .z-f2-p4 {
                            font-size: 11px;
                            line-height: 16px;
                            color: #fff;
                            width: 100%;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="z-f2-d2">
                    <img src="' . $background_img_type9 . '">
                    <div class="z-f2-d3">
                        <div class="z-f2-i1 i100 wow bounceInDown animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/3659067503cb5cd9c114c6bbfd7b0a19.png"></div>
                        <div class="z-f2-i1 i100 wow bounceInLeft animated animated" data-wow-delay="0.4s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.4s;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/d813f12eb5bdf09d9756a0624f7c4fec.png"></div>
                        <div class="z-f2-i1 i100 wow bounceInUp animated animated" data-wow-delay="0.5s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.5s;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/d584516fb95e1a2c7e3175a592427b73.png"></div>
                        <div class="z-f2-i1 i100 wow bounceInRight animated animated" data-wow-delay="0.6s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.6s;"><img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/f6b1a686a96601d44c83d294a351c120.png"></div>
                    </div>
                    <div class="z-f2-d4">
            ';
            foreach ($jw_tab_item_goods_nine as $k => $v) {
                $kkk = $k + 1;
                $output .= '
                    <div class="z-f2-d5 wow fadeInUp animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">
                        <div class="z-f2-p3"><span>' . $kkk . '.</span>' . $v->title . '</div>
                        <div class="z-f2-p4">' . $v->content . '</div>
                    </div>
                ';
            }
            $output .= '
                    </div>
                </div>
            ';
        }
        elseif ($service_product_type == 'type10') {
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .part-one {
                        margin-bottom: 0.6rem;
                    }
                    ' . $addon_id . ' ul,' . $addon_id . ' ol {
                        list-style: none;
                    }
                    ' . $addon_id . ' .part-one-item {
                        width: 329px;
                        height: 105px;
                        border-radius: 10px;
                        margin: 0 auto 12.5px;
                        overflow: hidden;
                        background: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220527/fa7f38437721d1cc9b6090e4204fadf5.png) no-repeat;
                    }
                    ' . $addon_id . ' .part-one-item-h3 {
                        height: 35px;
                        background: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220527/dae3b9c642c3a111c65e720124e1385f.png) no-repeat;
                        display: flex;
                        padding: 5px 7.5px;
                        align-items: center;
                    }
                    ' . $addon_id . ' .part-one-item-h3 i {
                        width: 25px;
                        height: 25px;
                        border-radius: 50%;
                        background: url(https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220527/436ce1d23ed7b2fbe00a768b67bacaf6.png) no-repeat;
                        text-align: center;
                        line-height: 25px;
                        font-size: 15px;
                        color: #fff;
                        font-style: normal;
                        background-size: 100%;
                        margin-right: 6.5px;
                    }
                    ' . $addon_id . ' .part-one-item-h3 span {
                        color: #fff;
                        font-size: 15px;
                        margin-right: 6.5px;
                    }
                    ' . $addon_id . ' .part-one-item-p {
                        height: 59px;
                        font-size: 12px;
                        color: #fff;
                        line-height: 20px;
                        display: flex;
                        align-items: center;
                        padding: 0 12.5px;
                        text-align: center;
                    }
                    ' . $addon_id . ' .part-one-item:nth-child(2n) .part-one-item-h3 {
                        text-align: right;
                        justify-content: flex-end;
                    }
                    ' . $addon_id . ' body, ' . $addon_id . ' h1, ' . $addon_id . ' h2, ' . $addon_id . ' h3, ' . $addon_id . ' h4, ' . $addon_id . ' h5, ' . $addon_id . ' h6, ' . $addon_id . ' hr, ' . $addon_id . ' p, ' . $addon_id . ' blockquote, ' . $addon_id . ' dl, ' . $addon_id . ' dt, ' . $addon_id . ' dd, ' . $addon_id . ' ul, ' . $addon_id . ' ol, ' . $addon_id . ' li, ' . $addon_id . ' pre, ' . $addon_id . ' fieldset, ' . $addon_id . ' lengend, ' . $addon_id . ' button, ' . $addon_id . ' input, ' . $addon_id . ' textarea, ' . $addon_id . ' th, ' . $addon_id . ' td {
                        margin: 0;
                        padding: 0;
                    }
                </style>
            ';
            $output .= '
                <ul class="part-one">
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <i>1</i>
                            <span>录播课</span>
                        </h3>
                        <p class="part-one-item-p">创建视频付费内容，学员购买后能随时在线收看。系统可跟踪学员学习进度、记录学员的完成量、观看时间。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <span>直播课</span>
                            <i>2</i>
                        </h3>
                        <p class="part-one-item-p">视频直播支持实时视频，支持桌面共享，直播时教师和学员可以文字互动、直播提醒，课后快速生成回放视频。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <i>3</i>
                            <span>体验课</span>
                        </h3>
                        <p class="part-one-item-p" style="text-align: left">直播课 录播课均可设置免费体验课。用来做引流的招生方式。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <span>音频课</span>
                            <i>4</i>
                        </h3>
                        <p class="part-one-item-p">机构可以创建音频付费内容，学员购买后能随时在线收听。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <i>5</i>
                            <span>考试系统</span>
                        </h3>
                        <p class="part-one-item-p">检测用户学习效果的学习工具，学习效果显而易见。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <span>营销中心</span>
                            <i>6</i>
                        </h3>
                        <p class="part-one-item-p">实现裂变传播的营销功能。用户自发形成刷屏式传播，促进销量提升同时可快速吸粉引流。 </p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <i>7</i>
                            <span>教师管理</span>
                        </h3>
                        <p class="part-one-item-p">教师权限独立分配，教师管理更便捷。</p>
                    </li>
                    <li class="part-one-item">
                        <h3 class="part-one-item-h3">
                            <span>数据统计</span>
                            <i>8</i>
                        </h3>
                        <p class="part-one-item-p">学校后台可对、累计用户，今日访客，新增用户，订单管理，今日收益，课程销量排行榜等数据进行统计。  </p>
                    </li>
                </ul>
            ';
        }
        elseif ($service_product_type == 'type11') {
            $jw_tab_item_goods_eleven = (isset($settings->jw_tab_item_goods_eleven) && $settings->jw_tab_item_goods_eleven) ? $settings->jw_tab_item_goods_eleven : [];
            $background_img_tian = (isset($settings->background_img_tian) && $settings->background_img_tian) ? $settings->background_img_tian : 'z1';
            $color_type11 = (isset($settings->color_type11) && $settings->color_type11) ? $settings->color_type11 : '#fff';
            $jjcolor_type11 = (isset($settings->jjcolor_type11) && $settings->jjcolor_type11) ? $settings->jjcolor_type11 : '#fff';

            if (isset($settings->fontsize_type11) && $settings->fontsize_type11) {
                if (is_object($settings->fontsize_type11)) {
                    $fontsize_type11_md = $settings->fontsize_type11->md;
                    $fontsize_type11_sm = $settings->fontsize_type11->sm;
                    $fontsize_type11_xs = $settings->fontsize_type11->xs;
                } else {
                    $fontsize_type11_md = $settings->fontsize_type11;
                    $fontsize_type11_sm = $settings->fontsize_type11_sm;
                    $fontsize_type11_xs = $settings->fontsize_type11_xs;
                }
            } else {
                $fontsize_type11_md = '30';
                $fontsize_type11_sm = '25';
                $fontsize_type11_xs = '20';
            }

            if (isset($settings->jjfontsize_type11) && $settings->jjfontsize_type11) {
                if (is_object($settings->jjfontsize_type11)) {
                    $jjfontsize_type11_md = $settings->jjfontsize_type11->md;
                    $jjfontsize_type11_sm = $settings->jjfontsize_type11->sm;
                    $jjfontsize_type11_xs = $settings->jjfontsize_type11->xs;
                } else {
                    $jjfontsize_type11_md = $settings->jjfontsize_type11;
                    $jjfontsize_type11_sm = $settings->jjfontsize_type11_sm;
                    $jjfontsize_type11_xs = $settings->jjfontsize_type11_xs;
                }
            } else {
                $jjfontsize_type11_md = '18';
                $jjfontsize_type11_sm = '15';
                $jjfontsize_type11_xs = '14';
            }
            // 背景图圆角
            $background_img_round_type11 = (isset($settings->background_img_round_type11) && $settings->background_img_round_type11) ? $settings->background_img_round_type11 : 0;
            // 每项左右间距
            $box_gap_type11 = (isset($settings->box_gap_type11) && $settings->box_gap_type11) ? $settings->box_gap_type11 : 0;
            // 遮罩颜色
            $cover_bgcolor_type11 = (isset($settings->cover_bgcolor_type11) && $settings->cover_bgcolor_type11) ? $settings->cover_bgcolor_type11 : 'rgba(0,0,0,0.7)';
            $hover_cover_bgcolor_type11 = (isset($settings->hover_cover_bgcolor_type11) && $settings->hover_cover_bgcolor_type11) ? $settings->hover_cover_bgcolor_type11 : 'rgba(0,0,0,0.4)';
            // 鼠标移入元素宽度
            if (isset($settings->active_box_width_type11) && $settings->active_box_width_type11) {
                if (is_object($settings->active_box_width_type11)) {
                    $active_box_width_type11_md = $settings->active_box_width_type11->md ? $settings->active_box_width_type11->md : 43;
                    $active_box_width_type11_sm = $settings->active_box_width_type11->sm ? $settings->active_box_width_type11->sm : 25;
                    $active_box_width_type11_xs = $settings->active_box_width_type11->xs ? $settings->active_box_width_type11->xs : 25;
                } else {
                    $active_box_width_type11_md = $settings->active_box_width_type11 ? $settings->active_box_width_type11 : 43;
                    $active_box_width_type11_sm = $settings->active_box_width_type11_sm ? $settings->active_box_width_type11_sm : 25;
                    $active_box_width_type11_xs = $settings->active_box_width_type11_xs ? $settings->active_box_width_type11_xs : 25;
                }
            } else {
                $active_box_width_type11_md = 43;
                $active_box_width_type11_sm = 25;
                $active_box_width_type11_xs = 25;
            }
            // 背景图高度
            if (isset($settings->background_img_height_type11) && $settings->background_img_height_type11) {
                if (is_object($settings->background_img_height_type11)) {
                    $background_img_height_type11_md = $settings->background_img_height_type11->md ? $settings->background_img_height_type11->md : 560;
                    $background_img_height_type11_sm = $settings->background_img_height_type11->sm ? $settings->background_img_height_type11->sm : 450;
                    $background_img_height_type11_xs = $settings->background_img_height_type11->xs ? $settings->background_img_height_type11->xs : 0;
                } else {
                    $background_img_height_type11_md = $settings->background_img_height_type11 ? $settings->background_img_height_type11 : 560;
                    $background_img_height_type11_sm = $settings->background_img_height_type11_sm ? $settings->background_img_height_type11_sm : 450;
                    $background_img_height_type11_xs = $settings->background_img_height_type11_xs ? $settings->background_img_height_type11_xs : 0;
                }
            } else {
                $background_img_height_type11_md = 560;
                $background_img_height_type11_sm = 450;
                $background_img_height_type11_xs = 0;
            }
            // 文字显示位置
            $text_position_type11 = (($settings->text_position_type11) && $settings->text_position_type11) ? $settings->text_position_type11 : 'cover_image_bottom';
            // 文字对齐方式
            $text_align_type11 = (($settings->text_align_type11) && $settings->text_align_type11) ? $settings->text_align_type11 : 'left';
            // 标题字体粗细
            $title_fontweight_type11 = (($settings->title_fontweight_type11) && $settings->title_fontweight_type11) ? $settings->title_fontweight_type11 : 'bold';
            // 文字内间距
            $text_padding_type11 = (($settings->text_padding_type11) && $settings->text_padding_type11) ? $settings->text_padding_type11 : '0 0 0 25px';

            // 2025.7.29 新增
            // 内容距底部距离
            $text_bottom_type11 = $this->getResponsiveValues('text_bottom_type11', ['md' => 30, 'sm' => '', 'xs' => '']);
            // 移入内容底部距离
            $hover_text_bottom_type11 = $this->getResponsiveValues('hover_text_bottom_type11', ['md' => '', 'sm' => '', 'xs' => '']);
            // 鼠标移入文字对齐方式
            $hover_text_align_type11 = $this->safeGetProp('hover_text_align_type11', '');
            // 鼠标移入标题文字大小
            $hover_title_fontsize_type11 = $this->getResponsiveValues('hover_title_fontsize_type11', ['md' => '', 'sm' => '', 'xs' => '']);
            // 鼠标移入标题字体颜色
            $hover_title_color_type11 = $this->safeGetProp('hover_title_color_type11', '');
            // 鼠标移入简介字体大小
            $jjhover_fontsize_type11 = $this->getResponsiveValues('jjhover_fontsize_type11', ['md' => '', 'sm' => '', 'xs' => '']);
            // 鼠标移入简介字体颜色
            $jjhover_color_type11 = $this->safeGetProp('jjhover_color_type11', '');
            // 简介正常隐藏移入显示
            $hide_desc_type11 = $this->safeGetProp('hide_desc_type11', 0);
            // 更多按钮正常隐藏移入显示
            $hide_more_type11 = $this->safeGetProp('hide_more_type11', 0);
            // 更多按钮图片宽度
            $more_img_width_type11 = $this->getResponsiveValues('more_img_width_type11', ['md' => 40, 'sm' => '', 'xs' => '']);
            // 更多按钮外边距
            $more_margin_type11 = $this->getResponsiveValues('more_margin_type11', ['md' => '30px 0 0 0', 'sm' => '', 'xs' => '']);

            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .overflow {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' a, ' . $addon_id . ' dd, ' . $addon_id . ' dl, ' . $addon_id . ' h1, ' . $addon_id . ' h2, ' . $addon_id . ' h3, ' . $addon_id . ' h4, ' . $addon_id . ' h5, ' . $addon_id . ' h6, ' . $addon_id . ' li, ' . $addon_id . ' p, ' . $addon_id . ' ul {
                        margin: 0;
                        padding: 0;
                        color: inherit;
                        font-size: inherit;
                        font-weight: inherit;
                    }
                    ' . $addon_id . ' dd, ' . $addon_id . ' dl, ' . $addon_id . ' li, ' . $addon_id . ' ul {
                        list-style: none;
                    }
            ';
            $pic_object_fit = ($background_img_tian == 'z1') ? 'fill' : 'cover';
            $output .= '
                ' . $addon_id . ' .djcs2_list li .pic {
                    width: 100%;
                    height: ' . $background_img_height_type11_md . 'px;
                    border-radius: ' . $background_img_round_type11 . 'px;
                    overflow: hidden;
                    position: relative;
                }
                ' . $addon_id . ' .djcs2_list li .pic img {
                    width: 100%;
                    height: 100%;
                    object-fit: ' . $pic_object_fit . ';
                }
            ';

            $num = (100 - $active_box_width_type11_md) / (count($jw_tab_item_goods_eleven) - 1);
            $nums = (100 - $active_box_width_type11_sm) / (count($jw_tab_item_goods_eleven) - 1);
            $output .='
                    ' . $addon_id . ' .djcs2_list {
                        margin: 0 -' . ( $box_gap_type11 / 2) . 'px;
                    }
                    ' . $addon_id . ' .djcs2_list li {
                        width: '.$num.'%;
                        transition: all 0.5s;
                        overflow: hidden;
                        float: left;
                        height: ' . (preg_match('/^cover_image/', $text_position_type11) ? $background_img_height_type11_md . 'px' : 'auto') . ';
                        position: relative;
                        padding: 0 ' . ( $box_gap_type11 / 2) . 'px;
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .djcs2_list li.active {
                        width: ' . $active_box_width_type11_md . '%;
                    }
                    ' . $addon_id . ' .djcs2_list li.active .djcs2_bg {
                        background: ' .$hover_cover_bgcolor_type11 .';
                    }
                    ' . $addon_id . ' .djcs2_list li .info_box {
                        width: 100%;
                        position: ' . (preg_match('/^cover_image/', $text_position_type11) ? 'absolute' : '') . ';
                        bottom: ' . $text_bottom_type11['md'] . 'px;
                        left: 0;
                        color: #fff;
                        /*padding-' . (preg_match('/^cover_image/', $text_position_type11) ? 'left' : 'top') . ': 25px;*/
                        padding: ' . $text_padding_type11 . ';
                        z-index: 1;
                        text-align: ' . $text_align_type11 . ';
                        transition: all 0.5s;
                    }
                    ' . $addon_id . ' .djcs2_list li.active .info_box {
                        bottom: ' . $hover_text_bottom_type11['md'] . 'px;
                        text-align: ' . $hover_text_align_type11 . ';
                    }
                    ' . $addon_id . ' .djcs2_list li .tit {
                        font-size: '.$fontsize_type11_md.'px;
                        font-weight: ' . $title_fontweight_type11 . ';
                        line-height: 1;
                        color:'.$color_type11.';
                    }
                    ' . $addon_id . ' .djcs2_list li.active .tit {
                        font-size: '. $hover_title_fontsize_type11['md'] .'px;
                        color:'.$hover_title_color_type11.';
                    }
                    ' . $addon_id . ' .djcs2_list li .des {
                        font-size: '.$jjfontsize_type11_md.'px;
                        margin-top: 10px;
                        color:'.$jjcolor_type11.';
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        ' . ($hide_desc_type11 == 1 ? 'display: none;' : '') . '
                    }
                    ' . $addon_id . ' .djcs2_list li.active .des {
                        font-size: '.$jjhover_fontsize_type11['md'].'px;
                        color:'.$jjhover_color_type11.';
                        ' . ($hide_desc_type11 == 1 ? 'display: -webkit-box;' : '') . '
                    }
                    ' . $addon_id . ' .djcs2_list li .more {
                        width: ' . $more_img_width_type11['md'] . 'px;
                        height: ' . $more_img_width_type11['md'] . 'px;
                        margin: ' . $more_margin_type11['md'] . ';
                        ' . ($hide_more_type11 == 1 ? 'display: none;' : '') . '
                    }
                    ' . $addon_id . ' .djcs2_list li.active .more {
                        ' . ($hide_more_type11 == 1 ? 'display: block;' : '') . '
                    }
                    ' . $addon_id . ' .djcs2_list li .more img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                    ' . $addon_id . ' .djcs2_bg {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: ' .$cover_bgcolor_type11 .';
                    }
                    ' . $addon_id . ' .djcs2_list_3g {
                        display: none;
                    }
                    @media (max-width: 1600px){
                        ' . $addon_id . ' .djcs2_list li .tit {
                            font-size: '.$fontsize_type11_md.'px;
                        }
                        ' . $addon_id . ' .djcs2_list li .des {
                            font-size: '.$jjfontsize_type11_md.'px;
                            color:'.$jjcolor_type11.';
                        }
                    }
                    @media (max-width: 1500px){
                        ' . $addon_id . ' .djcs2_list li {
                            width: '.$nums.'%;
                            transition: all 0.5s;
                            overflow: hidden;
                            float: left;
                            height: ' . (preg_match('/^cover_image/', $text_position_type11) ? $background_img_height_type11_sm . 'px' : 'auto') . ';
                            position: relative;
                        }
                        ' . $addon_id . ' .djcs2_list li.active {
                            width: ' . $active_box_width_type11_sm . '%;
                        }
                        ' . $addon_id . ' .djcs2_list li .pic {
                            height: ' . $background_img_height_type11_sm . 'px;
                        }
                        ' . $addon_id . ' .djcs2_list li .info_box {
                            bottom: ' . $text_bottom_type11['sm'] . 'px;
                        }
                        ' . $addon_id . ' .djcs2_list li.active .info_box {
                            bottom: ' . $hover_text_bottom_type11['sm'] . 'px;
                        }
                        ' . $addon_id . ' .djcs2_list li .tit {
                            font-size: '.$fontsize_type11_sm.'px;
                        }
                        ' . $addon_id . ' .djcs2_list li.active .tit {
                            font-size: '.$hover_title_fontsize_type11['sm'].'px;
                        }
                        ' . $addon_id . ' .djcs2_list li .des {
                            font-size: '.$jjfontsize_type11_sm.'px;
                        }
                        ' . $addon_id . ' .djcs2_list li.active .des {
                            font-size: '.$jjhover_fontsize_type11['sm'].'px;
                        }
                        ' . $addon_id . ' .djcs2_list li .more {
                            width: ' . $more_img_width_type11['sm'] . 'px;
                            height: ' . $more_img_width_type11['sm'] . 'px;
                            margin: ' . $more_margin_type11['sm'] . ';
                        }
                    }
                    ' . $addon_id . ' .fadeIn2 {
                        -webkit-animation-name: fadeIn2;
                        animation-name: fadeIn2;
                    }
                    @keyframes fadeIn2 {
                        from {
                            opacity: 0;
                            -webkit-transform: scale(0.9);
                            transform: scale(0.9);
                        }
                
                        to {
                            opacity: 1;
                            -webkit-transform: scale(1);
                            transform: scale(1);
                        }
                    }
                    ' . $addon_id . ' .swiper-container {
                        margin: 0 auto;
                        position: relative;
                        overflow: hidden;
                        list-style: none;
                        padding: 0;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-container-android .swiper-slide, ' . $addon_id . ' .swiper-wrapper {
                        -webkit-transform: translate3d(0px, 0, 0);
                        transform: translate3d(0px, 0, 0);
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform, -webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        /* width: 100%; */
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform, -webkit-transform;
                    }
                    ' . $addon_id . ' .swiper-pagination {
                        position: absolute;
                        text-align: center;
                        -webkit-transition: 300ms opacity;
                        -o-transition: 300ms opacity;
                        transition: 300ms opacity;
                        -webkit-transform: translate3d(0, 0, 0);
                        transform: translate3d(0, 0, 0);
                        z-index: 10;
                    }
                    ' . $addon_id . ' .swiper-container .swiper-notification {
                        position: absolute;
                        left: 0;
                        top: 0;
                        pointer-events: none;
                        opacity: 0;
                        z-index: -1000;
                    }
                    @media (max-width: 1000px){
                        ' . $addon_id . ' .djcs2_list {
                            display: none;
                        }
                        ' . $addon_id . ' .djcs2_list_3g {
                            display: block;
                        }
                        ' . $addon_id . ' .djcs2_list_3g .pic {
                            position: relative;
                            width: 100%;
                            height: ' . $background_img_height_type11_xs . 'px;
                            ' . ($background_img_height_type11_xs ? '' : 'padding-top: 100%;') . '
                        }
                        ' . $addon_id . ' .djcs2_list_3g .pic p {
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .djcs2_list_3g .pic img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                        ' . $addon_id . ' .djcs2_list_3g .info_box {
                            position: ' . (preg_match('/^cover_image/', $text_position_type11) ? 'absolute' : '') . ';
                            bottom: ' . ($hover_text_bottom_type11['xs'] ? $hover_text_bottom_type11['xs'] .'px' : '7%') . ';
                            left: 0;
                            z-index: 1;
                            padding: ' . $text_padding_type11 . ';
                            color: #fff;
                            text-align: ' . $text_align_type11 . ';
                        }
                        ' . $addon_id . ' .djcs2_list_3g .info_box:hover {
                            bottom: ' . $hover_text_bottom_type11['xs'] . 'px;
                        }
                        ' . $addon_id . ' .djcs2_list_3g .tit {
                            font-size: '.$fontsize_type11_xs.'px;
                            font-weight: ' . $title_fontweight_type11 . ';
                            color:'.$color_type11.';
                        }
                        ' . $addon_id . ' .djcs2_list_3g .des {
                            font-size: '.$jjfontsize_type11_xs.'px;
                            color:'.$jjcolor_type11.';
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="djcs_sec2 overflow">
                    <div class="djcs2_list overflow">
                        <ul class="clear">
            ';
            // 显示更多按钮
            $more_type11 = $this->safeGetProp('more_type11', 0);
            // 更多按钮图片
            $more_img_type11 = $this->safeGetProp('more_img_type11', '');

            foreach($jw_tab_item_goods_eleven as $k => $v)
            {
                $list_class = ($k == 0) ? 'active' : '';
                $output .= ' 
                    <li class="' . $list_class . '">
                        <div class="pic">
                            <img src="'.$v->big_img.'" alt="'.$v->title.'" />
                            <div class="djcs2_bg"></div>
                        </div>
                        <div class="info_box">
                            <div class="tit">'.$v->title.'</div>
                            <div class="des">'.$v->goods_desc.'</div>
                            ' . ($more_type11 && $more_img_type11 ? '<div class="more"><img src="' . $more_img_type11 . '" alt="" /></div>' : '') . '
                        </div>
                    </li>
                ';
            }
            $output .= '
                        </ul>
                    </div>
                    <div class="djcs2_list_3g wow fadeIn2 animated animated animated" data-wow-duration="1s" style="visibility: visible; animation-duration: 1s;">
                        <div class="swiper-container swiper-container-initialized swiper-container-horizontal">
                        <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(0px, 0px, 0px);">
            ';
            foreach($jw_tab_item_goods_eleven as $k => $v)
            {
                $output .= ' 
                    <div class="swiper-slide" style="width: 318px; margin-right: 10px;">
                            <div class="pic"><p><img src="'.$v->big_img.'" oncontextmenu="return false;"></p></div>
                            <div class="info_box">
                                <div class="tit">'.$v->title.'</div>
                                <div class="des">'.$v->goods_desc.'</div>
                            </div>
                            <div class="djcs2_bg"></div>					      	
                    </div>
                ';
            }
            $output .= '
                        </div>
                        <div class="swiper-pagination" id="swiper-pagination"></div>
                        <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
                    </div>
                    <script>
                        $("' . $addon_id . ' .djcs2_list li").eq(0).addClass("active")
                        $("' . $addon_id . ' .djcs2_list li").hover(function () {
                        $(this).addClass("active").siblings().removeClass("active");
                        })
                        var swiper = new Swiper("' . $addon_id . ' .djcs2_list_3g .swiper-container", {
                            autoplay: {
                                delay: 2000,
                                stopOnLastSlide: false,
                                disableOnInteraction: true,
                            },
                            breakpoints: {
                                640: {
                                    slidesPerView: 2.5,
                                    spaceBetween:10,
                                },
                                100: {
                                    slidesPerView: 1.5,
                                    spaceBetween:10,
                                },
                            }
                        });
                    </script>
                </div>
            ';
        }
        elseif ($service_product_type == 'type12') {
            $jw_tab_item_goods_twelve = (isset($settings->jw_tab_item_goods_twelve) && $settings->jw_tab_item_goods_twelve) ? $settings->jw_tab_item_goods_twelve : [];

            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .case>div {
                        width: 100%;
                        margin: auto;
                    }
                    ' . $addon_id . ' .case_content {
                        width: 100% !important;
                        overflow: hidden;
                        border-radius: 16px;
                        margin-bottom: 70px !important;
                        margin: 0px auto ;
                        box-shadow: 0 10px 15px 0 hsl(0deg 0% 75% / 50%) !important;
                        background-color: white;
                        
                    }
                    ' . $addon_id . ' .swiper-container {
                        margin: 0 auto;
                        position: relative;
                        overflow: hidden;
                        list-style: none;
                        padding: 0;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                    }
                    ' . $addon_id . ' .case_content_item {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        background-color: white;
                    }
                    ' . $addon_id . ' .case_content_item>div:first-of-type {
                        width: 660px;
                        display: flex;
                    }
                    ' . $addon_id . ' img {
                        width: 100%;
                        display: block;
                        border: none;
                    }
                    ' . $addon_id . ' .case_content_item_right {
                        padding: 0 52px;
                        flex: 1;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:first-of-type {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 24px;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div {
                        display: flex;
                        align-items: baseline;
                        font-size: 16px;
                        line-height: 30px;
                        margin-bottom: 20px;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div>div:first-of-type {
                        color: #999999;
                        width: 82px;
                        margin-right: 14px;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div>div:last-of-type {
                        flex: 1;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:first-of-type {
                        font-size: 20px;
                        margin-bottom: 20px;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type {
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div {
                        margin-right: 40px;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:first-of-type {
                        font-size: 24px;
                        color: #ef1f1f;
                    }
                    ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:last-of-type {
                        font-size: 16px;
                        color: #999999;
                    }
                    ' . $addon_id . ' a {
                        text-decoration: none;
                        color: inherit;
                    }
                    ' . $addon_id . ' .case_content_item_right>a>div:last-of-type {
                        color: white;
                        background-color: #ef1f1f;
                        text-align: center;
                        width: 190px;
                        font-size: 18px;
                        line-height: 50px;
                        border-radius: 10px;
                        margin-top: 26px;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .pointer {
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .swiper-button-prev, ' . $addon_id . ' .swiper-button-next {
                        display: none;
                        background-image: none !important;
                        background-color: rgba(31, 45, 61, .11);
                        width: 36px;
                        height: 36px;
                        align-items: center;
                        justify-content: center;
                        border-radius: 36px;
                    }
                    ' . $addon_id . ' .swiper-button-next, ' . $addon_id . ' .swiper-button-prev {
                        position: absolute;
                        top: 50%;
                        width: 36px;
                        height: 36px;
                        margin-top: -22px;
                        z-index: 10;
                        cursor: pointer;
                        background-size: 27px 44px;
                        background-position: center;
                        background-repeat: no-repeat;
                    }
                    ' . $addon_id . ' .swiper-button-next img, ' . $addon_id . ' .swiper-button-prev img{
                        width: 20px;
                    }
                    
                    ' . $addon_id . ' .swiper-button-next{
                
                        right: 10px;
                        left: auto;
                    }
                    ' . $addon_id . ' .swiper-button-prev{
                        left: 10px;
                        right: auto;
                    }
                    ' . $addon_id . ' .swiper-button-next img {
                        width: 20px;
                        transform: rotate(180deg);
                    }
                    ' . $addon_id . ' .swiper-container .swiper-notification {
                        position: absolute;
                        left: 0;
                        top: 0;
                        pointer-events: none;
                        opacity: 0;
                        z-index: -1000;
                    }
                    ' . $addon_id . ' .swiper-pagination {
                        position: relative !important;
                    }
                    ' . $addon_id . ' .swiper-pagination {
                        position: absolute;
                        text-align: center;
                        -webkit-transition: .3s opacity;
                        -o-transition: .3s opacity;
                        transition: .3s opacity;
                        -webkit-transform: translate3d(0,0,0);
                        transform: translate3d(0,0,0);
                        z-index: 10;
                    }
                    ' . $addon_id . ' .swiper-pagination-clickable .swiper-pagination-bullet {
                        cursor: pointer;
                    }
                    ' . $addon_id . ' .swiper-pagination-bullet {
                        background-color: #7e7d7d !important;
                        width: 30px !important;
                        height: 3px !important;
                        border-radius: 10px !important;
                        margin: 0 2px !important;
                    }
                    ' . $addon_id . ' .swiper-pagination-bullet {
                        width: 8px;
                        height: 8px;
                        display: inline-block;
                        border-radius: 100%;
                        background: #000;
                        opacity: .2;
                    }
                    ' . $addon_id . ' .swiper-pagination-bullet-active {
                        width: 40px !important;
                        background-color: #ef1f1f !important;
                        opacity: 1;
                    }
                    ' . $addon_id . ' .swiper-button-prev:after {
                        content: "";
                    }
                    ' . $addon_id . ' .swiper-button-next:after{
                        content: "";
                    }
                    @media (min-width: 1001px){
                        ' . $addon_id . ' .case_content:hover .swiper-button-prev, ' . $addon_id . ' .case_content:hover .swiper-button-next {
                            display: flex;
                        }
                    }
                    @media (max-width: 1500px){
                        ' . $addon_id . ' .case_content_item_right>div:first-of-type {
                            font-size: 15px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div {
                            font-size: 12px;
                            line-height: 15px;
                        }
                        ' . $addon_id . ' .case_content_item>div:first-of-type {
                            width: 420px;
                        }
                        ' . $addon_id . ' .case_content_item>div:first-of-type img{
                            height: 340px;
                        }
                        ' . $addon_id . ' .case_content_item_right>a>div:last-of-type {
                            width: 122px;
                            font-size: 12px;
                            line-height: 31px;
                        }
                    }
                    
                    @media (max-width: 1000px){
                        ' . $addon_id . ' .case_content_item_right {
                            padding: 20px;
                        }
                        ' . $addon_id . ' .case_content_item {
                            display: block;
                        }
                        ' . $addon_id . ' .case_content_item>div:first-of-type {
                            width: 100%;
                        }
                        ' . $addon_id . ' .case_content_item_right>a>div:last-of-type {
                            margin: auto;
                            margin-top: 32px;
                            width: 200px;
                            line-height: 50px;
                            font-size: 27px;
                            border-radius: 8px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:first-of-type {
                            font-size: 27px;
                            text-align: center;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div {
                            font-size: 25px;
                            line-height: 35px;
                        }
                        ' . $addon_id . ' .case_content {
                            width: 90% !important;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div>div:first-of-type {
                            width: 120px;
                            /* margin-right: 20rem; */
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3) {
                            display: flex;
                            align-items: center;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:first-of-type {
                            font-size: 25px;
                            width: 120px;
                            /* margin-right: 20rem; */
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:first-of-type {
                            font-size: 27px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:last-of-type {
                            font-size: 22px;
                        }
                        ' . $addon_id . ' .swiper-pagination {
                            display: none;
                        }
                    }
                    @media (max-width: 500px){
                        ' . $addon_id . ' .case_content_item>div:first-of-type img {
                            height: 270px;
                        }
                        ' . $addon_id . ' .case_content {
                            border-radius: 8px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:first-of-type {
                            font-size: 16px;
                            text-align: center;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div {
                            font-size: 15px;
                            line-height: 35px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(2)>div>div:first-of-type {
                            width: 65px;
                            /* margin-right: 20rem; */
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:first-of-type {
                            font-size: 15px;
                            width: 120px;
                            /* margin-right: 20rem; */
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:first-of-type {
                            font-size: 16px;
                        }
                        ' . $addon_id . ' .case_content_item_right>div:nth-of-type(3)>div:last-of-type>div>div:last-of-type {
                            font-size: 13px;
                        }
                        ' . $addon_id . ' .case_content_item_right>a>div:last-of-type {
                            margin: auto;
                            margin-top: 32px;
                            width: 108px;
                            line-height: 32px;
                            font-size: 15px;
                            border-radius: 4px;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="case">
                    <div class="case_content" style="position: relative; top: 0px; opacity: 1;">
                        <div class="swiper-container swiper swiper-container-horizontal">
                            <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-5836px, 0px, 0px);">
            ';
                            foreach($jw_tab_item_goods_twelve as $k => $v)
                            {

                                if ($v->tz_page_type == 'Internal_pages') {
                                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                                    $arrray = explode('&', $idurl);
                                    foreach ($arrray as $key => $value) {
                                        if (substr($value, 0, 3) == 'id=') {
                                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                                        }
                                    }

                                    $return = implode('&', $arrray);
                                } else {
                                    $return = $v->detail_page;
                                }
                                $output .= '
                                    <div class="swiper-slide" data-swiper-slide-index="'.$k.'" style="width: 1459px;">
                                        <div class="case_content_item">
                                            <div>
                                                <img src="'.$v->big_img.'" alt="" oncontextmenu="return false;">
                                            </div>
                                            <div class="case_content_item_right">
                                                <div>
                                                    '.$v->title.'
                                                </div>
                                                <div>
                                                    <div>
                                                        <div>'.$v->title_one.'</div>
                                                        <div>'.$v->goods_desc_one.'</div>
                                                    </div>
                                                    <div>
                                                        <div>'.$v->title_two.'</div>
                                                        <div>'.$v->goods_desc_two.'</div>
                                                    </div>
                                                    <div>
                                                        <div>'.$v->title_three.'</div>
                                                        <div>'.$v->goods_desc_three.'</div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div>'.$v->title_four.'</div>
                                                    <div>
                                                        <div>
                                                            <div>'.$v->number_one.'</div>
                                                            <div>'.$v->number_desc_one.'</div>
                                                        </div>
                                                        <div>
                                                            <div>'.$v->number_two.'</div>
                                                            <div>'.$v->number_desc_two.'</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <a href="'.$return.'">
                                                    <div class="pointer">'.$v->button_text.'</div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                ';
                            }
            $output .= '   
                            </div>
            
                            <!-- 如果需要导航按钮 -->
                            <div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/be7ebbc532ba7d17b3ebcba6283fad99.png" alt="" oncontextmenu="return false;">
                            </div>
                            <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/be7ebbc532ba7d17b3ebcba6283fad99.png" alt="" oncontextmenu="return false;">
                            </div>
                            <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                        </div>
                    </div>
                    <div class="swiper-pagination swiper-pagination-clickable swiper-pagination-bullets" style="position: relative; top: 0px; opacity: 1;">
                        <span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 1"></span>
                        <span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 2"></span>
                        <span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 3"></span>
                        <span class="swiper-pagination-bullet swiper-pagination-bullet-active" tabindex="0" role="button" aria-label="Go to slide 4"></span>
                        <span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 5"></span>
                        <span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 6"></span>
                    </div>
                </div>
                <script>
                    var swiper123 = new Swiper("' . $addon_id . ' .swiper", {
                        autoplay: {
                            delay: 5000,
                            disableOnInteraction: false,
                        },
                        speed: 2000,
                        loop: true,
                        pagination: {
                            el: "' . $addon_id . ' .swiper-pagination",
                            clickable: true,
                        },
                        navigation: {
                            nextEl: "' . $addon_id . ' .swiper-button-next",
                            prevEl: "' . $addon_id . ' .swiper-button-prev",
                        },
                    });
                </script>
            ';
        }
        elseif ($service_product_type == 'type13') {
            $jw_tab_item_goods_thirteen = (isset($settings->jw_tab_item_goods_thirteen) && $settings->jw_tab_item_goods_thirteen) ? $settings->jw_tab_item_goods_thirteen : [];
            $background_img_type13 = (isset($settings->background_img_type13) && $settings->background_img_type13) ? $settings->background_img_type13 : '';
            $title_type13 = (isset($settings->title_type13) && $settings->title_type13) ? $settings->title_type13 : '';
            $content_type13 = (isset($settings->content_type13) && $settings->content_type13) ? $settings->content_type13 : '';
            $buju_style_type13 = (isset($settings->buju_style_type13) && $settings->buju_style_type13) ? $settings->buju_style_type13 : 'type1';
            $imgtype = 'left';
            $contenttype = 'right';
            if($buju_style_type13=='type2')
            {
                $imgtype = 'right';
                $contenttype = 'left';
            }
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' .fadeInUp {
                        -webkit-animation-name: fadeInUp;
                        animation-name: fadeInUp;
                    }
                    @keyframes fadeInUp {
                        0% {
                            opacity: 0;
                            -webkit-transform: translate3d(0, 100%, 0);
                                    transform: translate3d(0, 100%, 0);
                        }
                        100% {
                            opacity: 1;
                            -webkit-transform: none;
                                    transform: none;
                        }
                    }
                    ' . $addon_id . ' .swiper-container {
                        margin-left: auto;
                        margin-right: auto;
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                    }
                    ' . $addon_id . ' .swiper-container-android .swiper-slide, ' . $addon_id . ' .swiper-wrapper {
                        -webkit-transform: translate3d(0,0,0);
                        transform: translate3d(0,0,0);
                    }
                    ' . $addon_id . ' .swiper-wrapper {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }
                    ' . $addon_id . ' .swiper-container-fade .swiper-slide {
                        pointer-events: none;
                        -webkit-transition-property: opacity;
                        -o-transition-property: opacity;
                        transition-property: opacity;
                    }
                    ' . $addon_id . ' .i100 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .swiper-slide {
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        -webkit-transition-property: -webkit-transform;
                        transition-property: -webkit-transform;
                        -o-transition-property: transform;
                        transition-property: transform;
                        transition-property: transform,-webkit-transform;
                    }
                    ' . $addon_id . ' .i100>img {
                        width: 100%;
                    }
                    ' . $addon_id . ' img {
                        display: block;
                        border: none;
                    }
                    ' . $addon_id . ' .i300 {
                        overflow: hidden;
                    }
                    ' . $addon_id . ' .i300>img {
                        width: 100%;
                        height: 100%;
                    }
                    ' . $addon_id . ' .business114 img:nth-child(1) {
                        opacity: 1;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .business114 img:nth-child(2) {
                        opacity: 0;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .business114:hover img:nth-child(1) {
                        opacity: 0;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .business114:hover img:nth-child(2) {
                        opacity: 1;
                        transition: 0.5s;
                    }
                    ' . $addon_id . ' .swiper-button-next, ' . $addon_id . ' .swiper-button-prev {
                        position: absolute;
                        top: 50%;
                        width: 27px;
                        height: 44px;
                        margin-top: -22px;
                        z-index: 10;
                        cursor: pointer;
                        background-size: 27px 44px;
                        background-position: center;
                        background-repeat: no-repeat;
                    }
                    @media only screen and (min-width: 1480px){
                        ' . $addon_id . ' .business4-a1 {
                            background-color: #faf9f7;
                            background-image: url('.$background_img_type13.');
                            background-repeat: repeat-x;
                            background-size: auto 100%;
                        }
                        ' . $addon_id . ' .business4-a1 {
                            width: 100%;
                            height: 630px;
                            position: relative;
                            margin: 0 auto;
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .business4-a2 {
                            width: 980px;
                            height: 630px;
                            position: relative;
                            float: '.$imgtype.';
                        }
                        ' . $addon_id . ' .business111 {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide img {
                            min-height: 100%;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .business4-a3 {
                            width: calc(100% - 980px);
                            height: 630px;
                            position: relative;
                            float: '.$contenttype.';
                            padding: 0 70px;
                            display: table;
                        }
                        ' . $addon_id . ' .business4-a4 {
                            display: table-cell;
                            vertical-align: middle;
                        }
                        ' . $addon_id . ' .business4-a5 {
                            font-size: 36px;
                            line-height: 36px;
                            color: #333333;
                            font-weight: bold;
                            margin-bottom: 32px;
                        }
                        ' . $addon_id . ' .business4-a6 {
                            width: 120px;
                            margin-bottom: 30px;
                        }
                        ' . $addon_id . ' .business4-a7 {
                            font-size: 16px;
                            line-height: 36px;
                            color: #7a7a7a;
                            margin-bottom: 40px;
                        }
                        ' . $addon_id . ' .business114 {
                            width: 49px;
                            height: 46px;
                            position: relative;
                            background: none;
                            padding: 0;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                        }
                        ' . $addon_id . ' .business112 {
                            float: left;
                            margin: 0;
                            margin-right: 5px;
                        }
                        ' . $addon_id . ' .business114 img {
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                    }
                    @media only screen and (max-width: 1479px) and (min-width: 1024px){
                        ' . $addon_id . ' .business4-a1 {
                            background-color: #faf9f7;
                            background-image: url('.$background_img_type13.');
                            background-repeat: repeat-x;
                            background-size: auto 100%;
                        }
                        ' . $addon_id . ' .business4-a1 {
                            width: 100%;
                            height: 426px;
                            position: relative;
                            margin: 0 auto;
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .business4-a2 {
                            width: 660px;
                            height: 426px;
                            position: relative;
                            float: '.$imgtype.';
                        }
                        ' . $addon_id . ' .business111 {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide img {
                            min-height: 100%;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .business4-a3 {
                            width: calc(100% - 660px);
                            height: 426px;
                            position: relative;
                            float: '.$contenttype.';
                            padding: 0 30px;
                            display: table;
                        }
                        ' . $addon_id . ' .business4-a4 {
                            display: table-cell;
                            vertical-align: middle;
                        }
                        ' . $addon_id . ' .business4-a5 {
                            font-size: 30px;
                            line-height: 30px;
                            color: #333333;
                            font-weight: bold;
                            margin-bottom: 24px;
                        }
                        ' . $addon_id . ' .business4-a6 {
                            width: 90px;
                            margin-bottom: 20px;
                        }
                        ' . $addon_id . ' .business4-a7 {
                            font-size: 14px;
                            line-height: 28px;
                            color: #7a7a7a;
                            margin-bottom: 30px;
                        }
                        ' . $addon_id . ' .business114 {
                            width: 49px;
                            height: 46px;
                            position: relative;
                            background: none;
                            padding: 0;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                        }
                        ' . $addon_id . ' .business112 {
                            float: left;
                            margin: 0;
                            margin-right: 5px;
                        }
                        ' . $addon_id . ' .business113 {
                            float: left;
                            margin: 0;
                        }
                        ' . $addon_id . ' .business114 img {
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        ' . $addon_id . ' .business4-a1 {
                            background-color: #faf9f7;
                            background-image: url('.$background_img_type13.');
                            background-repeat: repeat-y;
                            background-size: 100% auto;
                        }
                        ' . $addon_id . ' .business4-a1 {
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .business4-a2 {
                            width: 100%;
                            height: 484px;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide img {
                            min-height: 100%;
                        }
                        ' . $addon_id . ' .business4-a3 {
                            width: 100%;
                            position: relative;
                            padding: 60px 35px 80px;
                        }
                        ' . $addon_id . ' .business4-a4 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .business4-a5 {
                            font-size: 42px;
                            line-height: 42px;
                            color: #333333;
                            font-weight: bold;
                            margin-bottom: 32px;
                            text-align: center;
                        }
                        ' . $addon_id . ' .business4-a6 {
                            width: 200px;
                            margin: 0 auto;
                            margin-bottom: 44px;
                        }
                        ' . $addon_id . ' .business4-a7 {
                            font-size: 26px;
                            line-height: 52px;
                            color: #7a7a7a;
                            margin-bottom: 48px;
                        }
                        ' . $addon_id . ' .business4-a8 {
                            width: 164px;
                            margin: 0 auto;
                        }
                        ' . $addon_id . ' .business114 {
                            width: 78px;
                            height: 72px;
                            position: relative;
                            background: none;
                            padding: 0;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                        }
                        ' . $addon_id . ' .business112 {
                            float: left;
                        }
                        ' . $addon_id . ' .business113 {
                            float: right;
                        }
                        ' . $addon_id . ' .business114 img {
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                    }
                    @media only screen and (max-width: 550px){
                        ' . $addon_id . ' .business4-a1 {
                            background-color: #faf9f7;
                            background-image: url('.$background_img_type13.');
                            background-repeat: repeat-y;
                            background-size: 100% auto;
                        }
                        ' . $addon_id . ' .business4-a1 {
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            z-index: 1;
                        }
                        ' . $addon_id . ' .business4-a2 {
                            width: 100%;
                            height: 242px;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide {
                            width: 100%!important;
                            height: 100%!important;
                            position: relative;
                        }
                        ' . $addon_id . ' .business111 .swiper-slide img {
                            min-height: 100%;
                        }
                        ' . $addon_id . ' .business4-a3 {
                            width: 100%;
                            position: relative;
                            padding: 30px 17.5px 40px;
                        }
                        ' . $addon_id . ' .business4-a4 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .business4-a5 {
                            font-size: 21px;
                            line-height: 21px;
                            color: #333333;
                            font-weight: bold;
                            margin-bottom: 16px;
                            text-align: center;
                        }
                        ' . $addon_id . ' .business4-a6 {
                            width: 100px;
                            margin: 0 auto;
                            margin-bottom: 22px;
                        }
                        ' . $addon_id . ' .business4-a7 {
                            font-size: 13px;
                            line-height: 26px;
                            color: #7a7a7a;
                            margin-bottom: 24px;
                        }
                        ' . $addon_id . ' .business4-a8 {
                            width: 82px;
                            margin: 0 auto;
                        }
                        ' . $addon_id . ' .business114 {
                            width: 39px;
                            height: 36px;
                            position: relative;
                            background: none;
                            padding: 0;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                        }
                        ' . $addon_id . ' .business112 {
                            float: left;
                        }
                        ' . $addon_id . ' .business113 {
                            float: right;
                        }
                        ' . $addon_id . ' .business114 img {
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="business4-a1 clear wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="1.5s"
                style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.3s; animation-name: fadeInUp;">
                    <div class="business4-a2">
                        <div class="swiper-container business111 swiper-container-fade swiper-container-horizontal">
                            <div class="swiper-wrapper" style="transition-duration: 0ms;">
            ';
            foreach($jw_tab_item_goods_thirteen as $k => $v)
            {
                $output .= '
                    <div class="swiper-slide i100"
                    data-swiper-slide-index="0"
                    style="width: 980px; transition-duration: 0ms; opacity: 1; transform: translate3d(-2940px, 0px, 0px);">
                        <img src="'.$v->big_img.'">
                    </div>
                ';
            }
            $output .='
                            </div>
                        </div>
                    </div>
                    <div class="business4-a3">
                        <div class="business4-a4">
                            <div class="business4-a5">'.$title_type13.'</div>
                            <div class="business4-a6 i100">
                                <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/75e5c9d85cae812ba08526ea0a56a5a5.png">
                            </div>
                            <div class="business4-a7">'.$content_type13.'</div>
                            <div class="business4-a8 clear">
                                <div class="swiper-button-prev i300 business112 business114">
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/f244d986e4028180ae9a5498c7f2d42e.png">
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/067a97bbe593b4ebc4dc9bcdef47f66b.png">
                                </div>
                                <div class="swiper-button-next i300 business113 business114">
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/41ec44bc9898e2283b1003bc52fba7e0.png">
                                    <img src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/79d4b38264a94c6d382f349061288218.png">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <script>
                    var mySwiper = new Swiper ("' . $addon_id . ' .business111", {
                        loop: true,
                        navigation:{
                            prevEl: "' . $addon_id . ' .business112",
                            nextEl: "' . $addon_id . ' .business113",
                        },
                        autoplay:{
                            delay: 5000,
                            disableOnInteraction: false,
                        },
                        speed: 800,
                        effect : "fade",
                        fadeEffect: {
                            crossFade: true,
                        },
                    });
                </script>
            ';
        }
        elseif ($service_product_type == 'type14'){
            $jw_tab_item_goods_fourteen = (isset($settings->jw_tab_item_goods_fourteen) && $settings->jw_tab_item_goods_fourteen) ? $settings->jw_tab_item_goods_fourteen :'';

            $bjcolor_type14 = (isset($settings->bjcolor_type14) && $settings->bjcolor_type14) ? $settings->bjcolor_type14 : '#b4945b';
            $gdimg_type14 = (isset($settings->gdimg_type14) && $settings->gdimg_type14) ? $settings->gdimg_type14 : 'https://oss.lcweb01.cn/joomla/20220728/ab10fc783ae71bd3115597d4594c4743.gif';
            $gdimgs_type14 = (isset($settings->gdimgs_type14) && $settings->gdimgs_type14) ? $settings->gdimgs_type14 : 'https://oss.lcweb01.cn/joomla/20220728/4f99012b74fe6814af84554eda8abf94.gif';

            $qywidth_type14 = (isset($settings->qywidth_type14) && $settings->qywidth_type14) ? $settings->qywidth_type14 : '1080';
            $qyheight_type14 = (isset($settings->qyheight_type14) && $settings->qyheight_type14) ? $settings->qyheight_type14 : '374';
            $liwidth_type14 = (isset($settings->liwidth_type14) && $settings->liwidth_type14) ? $settings->liwidth_type14 : '251';
            $imgheight_type14 = (isset($settings->imgheight_type14) && $settings->imgheight_type14) ? $settings->imgheight_type14 : '259';
            $fontcolor_type14 = (isset($settings->fontcolor_type14) && $settings->fontcolor_type14) ? $settings->fontcolor_type14 : '#171717';
            $qycolor_type14 = (isset($settings->qycolor_type14) && $settings->qycolor_type14) ? $settings->qycolor_type14 : '#dcdcdc';

            $fonttit_type14 = (isset($settings->fonttit_type14) && $settings->fonttit_type14) ? $settings->fonttit_type14 : '20';
            $fontjj_type14 = (isset($settings->fontjj_type14) && $settings->fontjj_type14) ? $settings->fontjj_type14 : '16';

            $output .= '
                <style>
                    '.$addon_id.' div, ul, li, p {
                        margin: 0;
                        padding: 0;
                    }
                    '.$addon_id.' li {
                        list-style: none;
                    }
                    '.$addon_id.' .teamc {
                        width:'.$qywidth_type14.'px;
                        height: '.$qyheight_type14.'px;
                        background: '.$qycolor_type14.';
                        padding: 13px 0 0 2px;
                        margin:0 auto;
                    }
                    '.$addon_id.' .teamc ul{
                        margin: 0 auto;
                        width:100%;
                    }
                
                    '.$addon_id.' .teamc li {
                        width: '.$liwidth_type14.'px;
                        height: '.$qyheight_type14.'px;
                        float: left;
                        position: relative;
                        margin: 0 9px;
                        overflow: hidden;
                    }
                    '.$addon_id.' a {
                        color: #666;
                        text-decoration: none;
                    }
                    '.$addon_id.' .teamc li img {
                        width: 100%;
                        height: '.$imgheight_type14.'px;
                        display: block;
                    }
                    '.$addon_id.' .teamc li p {
                        position: absolute;
                        width: '.$liwidth_type14.'px;
                        height: calc( '.$qyheight_type14.'px - 13px );
                        padding: 11px 25px 0;
                        padding-top: calc('.$imgheight_type14.'px + 20px );
                        left: 0px;
                        top: 0;
                        font-size: '.$fontjj_type14.'px;
                        line-height: 30px;
                        color: '.$fontcolor_type14.';
                    }
                    '.$addon_id.' .teamc li.cur p {
                        background: '.$bjcolor_type14.';
                        padding-top: 11px;
                    }
                    '.$addon_id.' .teamc li p b {
                        color: '.$fontcolor_type14.';
                        display: none;
                        font-size: '.$fonttit_type14.'px;
                        line-height: 52px;
                        height: 54px;
                        background: url(https://oss.lcweb01.cn/joomla/20220729/8e69f1368527ba652f63933e6be0250e.jpg) no-repeat 0 bottom;
                    }
                    '.$addon_id.' .teamc li p span {
                        height: calc( '.$imgheight_type14.'px - 54px );
                        padding: 20px 0 0;
                        display: none;
                        overflow:hidden;
                    }
                    '.$addon_id.' .teamc li.cur p b, '.$addon_id.' .teamc li.cur p span {
                        display: block;
                    }
                    '.$addon_id.' .teamc li p a {
                        font-size: 14px;
                        color: '.$bjcolor_type14.';
                        text-align: center;
                        line-height: 42px;
                        padding: 0 0 30px;
                        display: block;
                        background: url('.$gdimg_type14.') no-repeat center bottom;
                    }
                    '.$addon_id.' .teamc li p a:hover, '.$addon_id.' .teamc li.cur p a {
                        color: #12100f;
                        background: url('.$gdimgs_type14.') no-repeat center bottom;
                    }
                    '.$addon_id.' .teamc li p a:hover {
                        text-decoration: underline;
                    }
                    '.$addon_id.' .cl{clear:both;}
                </style>
            ';
            $output .= '
                <div class="teamc">
                    <ul>';
                    foreach ($jw_tab_item_goods_fourteen as $k => $v) {
                        if($v->tz_page_type){

                            if ($v->detail_page_id) {
                                $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                                $arrray = explode('&', $idurl);
                                foreach ($arrray as $key => $value) {
                                    if (substr($value, 0, 3) == 'id=') {
                                        $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                                    }
                                }

                                $return = implode('&', $arrray);
                            } else {
                                $return = $v->detail_page;
                            }
                        }else{
                            $return ='javascript:void(0)';
                        }

                        $output .= '
                            <li class="'; if($k==0){ $output .= 'cur'; } $output .= '">
                                <a href="javascript:void(0)" rel="nofollow" title="' . $v->fu_title . '" style="cursor: default;">
                                    <img src="'.$v->big_img.'" alt="' . $v->fu_title . '" title="' . $v->title . '">
                                </a>
                                <p>
                                    <b>' . $v->fu_title . '</b>
                                    <span>'.$v->jianjie.'</span>
                                    <a href="'.$return.'" target="'.$v->target.'" title="' . $v->title . '" rel="nofollow">' . $v->title . '</a>
                                </p>
                            </li>
                        ';
                    }
                    $output .= '
                        <div class="cl"></div>
                    </ul>
                </div>
                <script>
                    $(function () {
                        $("'.$addon_id.' .teamc li").hover(function () {
                            $(this).parent().parent(".teamc").find("li").removeClass("cur");
                            $(this).addClass("cur");
                        });
                    })
                </script>
            ';
        }
        elseif ($service_product_type == 'type15'){
            $jw_tab_item_goods_fiveteen = (isset($settings->jw_tab_item_goods_fiveteen) && $settings->jw_tab_item_goods_fiveteen) ? $settings->jw_tab_item_goods_fiveteen :'';
            $contentcolor_type15_pc = (isset($settings->contentcolor_type15_pc) && $settings->contentcolor_type15_pc) ? $settings->contentcolor_type15_pc :'#616161';
            $contentcolor_type15_sj = (isset($settings->contentcolor_type15_sj) && $settings->contentcolor_type15_sj) ? $settings->contentcolor_type15_sj :'#000000';
            $fgxcolor_type15_hr = (isset($settings->fgxcolor_type15_hr) && $settings->fgxcolor_type15_hr) ? $settings->fgxcolor_type15_hr :'#a4802f';

            $output .= '
                <style>
                    '.$addon_id.' body, '.$addon_id.' div, '.$addon_id.' ul, '.$addon_id.' li, '.$addon_id.' p {
                        margin: 0;
                        padding: 0;
                    }
                    '.$addon_id.' .qua {
                        font-family: "Microsoft YaHei";
                        height: 620px;
                        width: 100%;
                        margin: 0 auto;
                    }
                    '.$addon_id.' .content{
                        width: 100%;
                       
                    }
                    '.$addon_id.' .qua_l {
                        width: 556px;
                        padding: 21px 0 0 6px;
                    }
                    '.$addon_id.' .fl {
                        float: left;
                    }
                    '.$addon_id.' a {
                        color: #666;
                        text-decoration: none;
                    }
                    '.$addon_id.' .qua_r {
                        width: 514px;
                        height: 623px;
                        border-left: solid 4px '.$fgxcolor_type15_hr.';
                    }
                    '.$addon_id.' .qua_r li {
                        height: 117px;
                        padding: 19px 0 0 25px;
                        color: '.$contentcolor_type15_pc.';
                        line-height: 24px;
                        font-size: 14px;
                    }
                    '.$addon_id.' li {
                        list-style: none;
                    }
                ';
                foreach($jw_tab_item_goods_fiveteen as $k => $v)
                {
                    $num = $k+1;
                    $output .= $addon_id.' .qua_r li.qual'.$num.' span {
                            background: url('.$v->icon_pc.') no-repeat 0 0;
                        }
                    ';
                }
                $output .='
                    '.$addon_id.' .qua_r li span {
                        width: 46px;
                        height: 46px;
                        display: block;
                        float: left;
                        overflow: hidden;
                        margin: 4px 0 0;
                    }
                    '.$addon_id.' .qua_r li.cur span {
                        background-position: 0 bottom;
                    }
                    '.$addon_id.' .qua_r li p {
                        float: left;
                        width: 408px;
                        padding: 0 0 0 19px;
                    }
                    '.$addon_id.' .qua_r li p img {
                        display: block;
                        padding: 0 0 10px;
                    }
                
                    '.$addon_id.' .m-pz {
                        width: 100%;
                        display: none;
                    }
                ';
                foreach($jw_tab_item_goods_fiveteen as $k => $v)
                {
                    $num = $k+1;
                    if($num==1)
                    {
                        $output .= $addon_id.' .m-pz h3 {
                                padding: 0 9.375% 15px 26.5625%;
                                color: '.$contentcolor_type15_sj.';
                                background: url('.$v->icon_sj.') no-repeat 37px 15px;
                                background-size: 30px;
                            }
                        ';
                    }
                    else
                    {
                        $output .= $addon_id.' .m-pz h3:nth-of-type('.$num.') {
                                background-image: url('.$v->icon_sj.');
                            }
                        ';
                    }
                }
                $output .='
                    '.$addon_id.' .m-pz h3 span {
                        display: block;
                        padding: 5px 0 0 0;
                        font-size: 13px;
                        line-height: 18px;
                    }
                    '.$addon_id.' .m-pz h3 img {
                        width: 135px;
                    }
                    @media screen and (max-width: 500px) {
                        '.$addon_id.' .qua {
                            display: none;
                        }
                        '.$addon_id.' .m-pz {
                            display: block;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="qua">
                    <div class="content">
            ';
            foreach($jw_tab_item_goods_fiveteen as $k => $v)
            {
                if($k==0)
                {
                    $output .= '
                        <div class="qua_l fl" id="shk'.$k.'" style="">
                            <a href="javascript:void(0)" title="展柜材质 " style="cursor: default;">
                                <img src="'.$v->big_img.'" alt="展柜材质 " title="展柜材质">
                            </a>
                        </div>
                    ';
                }
                else
                {
                    $output .= '
                        <div class="qua_l fl" id="shk'.$k.'" style="display: none;">
                            <a href="javascript:void(0)" title="展柜材质 " style="cursor: default;">
                                <img src="'.$v->big_img.'" alt="展柜材质 " title="展柜材质">
                            </a>
                        </div>
                    ';
                }
            }
            $output .='
                        <div class="qua_r fl">
                            <ul>
            ';
            foreach($jw_tab_item_goods_fiveteen as $k => $v)
            {
                $num = $k+1;
                if($k==0)
                {
                    $output .= '
                        <li class="qual'.$num.' cur" id="chk'.$k.'" onmouseover="ShowCheck(this);"><span></span>
                            <p>
                                <img src="'.$v->title_img_pc.'" alt="展柜材质" title="用心挑选材质">'.$v->content_pc.'
                            </p>
                        </li>
                    ';
                }
                else
                {
                    $output .= '
                        <li class="qual'.$num.'" id="chk'.$k.'" onmouseover="ShowCheck(this);"><span></span>
                            <p>
                                <img src="'.$v->title_img_pc.'" alt="展柜材质" title="用心挑选材质">'.$v->content_pc.'
                            </p>
                        </li>
                    ';
                }
            }
            $output .='                            
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="m-pz">
            ';
            foreach($jw_tab_item_goods_fiveteen as $k => $v)
            {
                $output .= '
                    <h3>
                        <img src="'.$v->title_img_sj.'" data-src="/Mobile/Images/Index/pz_tit2.png" alt="" class="loaded">
                        <span>'.$v->content_sj.'</span>
                    </h3>
                ';
            }
            $output .='
                </div>
                <script>
                    $(function () {
                        $("'.$addon_id.' .qua_r li").hover(function () {
                            $(this).parent().parent(".qua_r").find("li").removeClass("cur");
                            $(this).addClass("cur");
                        });
                    })
                    function ShowCheck(src) {
                        $(src).parent().children().removeClass("cur");
                        $(src).addClass("cur");
                        var Index = $(src).attr("id");
                        Index = Index.substring(3, Index.length);
                        var ShowIndex = "#shk" + Index;
                        $(src).parent().parent().parent().children(".qua_l").hide();
                        $(src).parent().parent().parent().find(ShowIndex).show();
                    }
                </script>
            ';
        }
        elseif ($service_product_type == 'type16'){
            $jw_tab_item_goods_sixteen = (isset($settings->jw_tab_item_goods_sixteen) && $settings->jw_tab_item_goods_sixteen) ? $settings->jw_tab_item_goods_sixteen :'';
            $bg_img_type16 = (isset($settings->bg_img_type16) && $settings->bg_img_type16) ? $settings->bg_img_type16 :'';
            $button_bottom_type16 = (isset($settings->button_bottom_type16) && $settings->button_bottom_type16) ? $settings->button_bottom_type16 :0;
            $button_bottom_type16_sj = (isset($settings->button_bottom_type16_sj) && $settings->button_bottom_type16_sj) ? $settings->button_bottom_type16_sj :0;

            $output .= '
                <style>
                    '.$addon_id.' .big-div {
                        width: 100%;
                    }
                    '.$addon_id.' .smail-div {
                        width: calc(100%/6);
                        aspect-ratio: 1;
                        float: left;
                        padding: auto 0.5%;
                        position: relative;
                    }
                    '.$addon_id.' .smail-div:hover {
                        background-image: url('.$bg_img_type16.');
                        background-position: center center;
                        background-repeat: no-repeat;
                        background-size: cover;
                    }
                    '.$addon_id.' .smail-div:not(:first-child)::after {
                        content:" ";
                        height: 25%;
                        display:block;
                        border-right: 1px solid #ccc;
                        top: 35%;
                        position: absolute;
                    }
                    '.$addon_id.' .smail-div:hover::after {
                        content: "";
                        height: 0;
                    }
                    '.$addon_id.' .smail-img {
                        width: 70px;
                        height: 70px;
                        margin: 40px auto;
                    }
                    '.$addon_id.' .smail-img img{
                        width: 100%;
                        height: 100%;
                    }
                    '.$addon_id.' .smail-text {
                        width: 100%;
                        height: 40px;
                        text-align: center;
                        font-size: 20px;
                        color: #183153;
                        font-weight: 600;
                    }
                    '.$addon_id.' .smail-desc {
                        width: 85%;
                        margin: 20px auto;
                        font-size: 14px;
                        color: #647C9E;   
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    '.$addon_id.' .smail-button {
                        width: 95%;
                        text-align: right;
                        font-size: 12px;
                        margin-bottom: '.$button_bottom_type16.'px;
                    }
                    '.$addon_id.' .smail-button a {
                        color: #647C9E;
                    }
                    '.$addon_id.' a {
                        text-decoration-line: none;
                    }
                    '.$addon_id.' .smail-img-two {
                        display: none;
                    }
                    '.$addon_id.' .smail-div:hover .smail-img-two{
                        display: block;
                    }
                    '.$addon_id.' .smail-div:hover .smail-img-one{
                        display: none;
                    }
                    '.$addon_id.' .smail-div:hover .smail-text,
                    '.$addon_id.' .smail-div:hover .smail-desc,
                    '.$addon_id.' .smail-div:hover .smail-button a {
                        color: #fff;
                    }
                    @media screen and (max-width: 1670px) {
                        '.$addon_id.' .smail-img {
                            width: 40px;
                            height: 40px;
                            margin: 30px auto;
                        }
                        '.$addon_id.' .smail-button {
                            margin-bottom: '.$button_bottom_type16.'px;
                        }
                    }
                    @media screen and (max-width: 1200px) {
                        '.$addon_id.' .smail-img {
                            width: 40px;
                            height: 40px;
                            margin: 30px auto;
                        }
                        '.$addon_id.' .smail-div {
                            width: calc(100%/3);
                        }
                        '.$addon_id.' .smail-button {
                            margin-bottom: '.$button_bottom_type16.'px;
                        }
                    }
                    @media screen and (max-width: 480px) {
                        '.$addon_id.' .smail-img {
                            width: 40px;
                            height: 40px;
                            margin: 30px auto;
                            margin-bottom: 20px;
                        }
                        '.$addon_id.' .smail-div {
                            width: calc(100%/2);
                        }
                        '.$addon_id.' .smail-text {
                            font-size: 16px;
                        }
                        '.$addon_id.' .smail-desc {
                            margin-top: 0;
                        }
                        '.$addon_id.' .smail-button {
                            margin-bottom: '.$button_bottom_type16_sj.'px;
                        }
                        '.$addon_id.' .smail-div.actives {
                            background-image: url('.$bg_img_type16.');
                            background-position: center center;
                            background-repeat: no-repeat;
                            background-size: cover;
                        }
                        '.$addon_id.' .smail-div.actives::after {
                            content: "";
                            height: 0;
                        }
                        '.$addon_id.' .smail-div.actives .smail-img-two{
                            display: block;
                        }
                        '.$addon_id.' .smail-div.actives .smail-img-one{
                            display: none;
                        }
                        '.$addon_id.' .smail-div.actives .smail-text,
                        '.$addon_id.' .smail-div.actives .smail-desc,
                        '.$addon_id.' .smail-div.actives .smail-button a {
                            color: #fff;
                        }
                    }
                </style>
            ';
            $output .= '
                <div class="big-div">
            ';
            foreach($jw_tab_item_goods_sixteen as $k => $v)
            {
                if ($v->tz_page_type == 'Internal_pages') {
                    $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                    $arrray = explode('&', $idurl);
                    foreach ($arrray as $key => $value) {
                        if (substr($value, 0, 3) == 'id=') {
                            $arrray[$key] = 'id=' . base64_encode($v->detail_page_id);
                        }
                    }

                    $return = implode('&', $arrray);
                } else {
                    $return = $v->detail_page;
                }
                $actives = '';
                if($k == 0) {
                    $actives = ' actives';
                }
                $output .= '
                    <div class="smail-div' . $actives . '">
                        <div class="smail-img">
                            <img class="smail-img-one" src="'.$v->icon.'" alt="">
                            <img class="smail-img-two" src="'.$v->icon_hg.'" alt="">
                        </div>
                        <div class="smail-text">'.$v->title.'</div>
                        <div class="smail-desc">'.$v->content_text.'</div>
                        <div class="smail-button">
                            <a href="'.$return.'">'.$v->button_text.'</a>
                        </div>
                    </div>
                ';
            }
            $output .='
                </div>
            ';
        }
        return $output;
    }

    // 处理适配的值
    public function getResponsiveValues($baseName, $defaults = null) 
    {

        $settings = $this->addon->settings;

        // 设置默认值（支持完整覆盖或部分覆盖）
        $finalDefaults = array_merge([
            'md' => '',
            'sm' => '',
            'xs' => ''
        ], (array)$defaults);
        
        // 动态构建属性名
        $value = $baseName;
        $valueSm = $baseName . '_sm';
        $valueXs = $baseName . '_xs';

        // 检查主属性是否存在
        if (isset($settings->$value)) {
            $mainValue = $settings->$value;
            
            if ($mainValue && is_object($mainValue)) {
                // 对象处理：从对象属性获取值
                return [
                    'md' => $mainValue->md ?? $finalDefaults['md'],
                    'sm' => $mainValue->sm ?? $finalDefaults['sm'],
                    'xs' => $mainValue->xs ?? $finalDefaults['xs']
                ];
            } elseif ($mainValue) {
                // 标量值处理：从后缀属性获取响应值
                return [
                    'md' => $mainValue,
                    'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
                    'xs' => $settings->$valueXs ?? $finalDefaults['xs']
                ];
            }
        }
        
        // 当主属性存在但为假值时（如0、空字符串等），返回默认值
        return $finalDefaults;
    }

    /**
     * 处理普通变量 安全获取对象属性值
     * @param object $obj 对象实例
     * @param string $prop 属性名称
     * @param mixed $default 默认值 (默认为'percent')
     * @param bool $strict 严格模式 (true: 0/false视为有效值; false: 0/false视为空值)
     * @return mixed 属性值或默认值
     */
    public function safeGetProp($prop, $default = '', $strict = false) 
    {

        $settings = $this->addon->settings;
        // 检查属性是否存在
        if (!isset($settings->$prop)) {
            return $default;
        }
        $value = $settings->$prop;
        
        // 严格模式：0/false视为有效值
        if ($strict) {
            return $value;
        }
        // 非严格模式：空值检查
        return trim($value) ? $value : $default;
    }

    public function css()
    {
    }

    public function scripts()
    {
        $settings = $this->addon->settings;
        $service_product_type = (isset($settings->service_product_type) && $settings->service_product_type) ? $settings->service_product_type : 'type1';

        $scripts = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        if ($service_product_type == 'type2') {
            array_push($scripts, JURI::base(true) . '/components/com_jwpagefactory/addons/service_product/assets/js/swiper.animate1.0.3.min.js');
            array_push($scripts, JURI::base(true) . '/components/com_jwpagefactory/addons/service_product/assets/js/wow.min.js');
        }
        return $scripts;
    }

    /* js */
    public function js()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $service_product_type = (isset($settings->service_product_type) && $settings->service_product_type) ? $settings->service_product_type : 'type1';

        $js = '
            //根据屏幕分辨率
            function IsPhone(){
                var mobile_flag = false;
                var screen_width = window.screen.width;
                var screen_height = window.screen.height;
                if(screen_width < 480){
                    mobile_flag = true;
                }
                return mobile_flag;
            }
            jQuery(document).ready(function($){';
                if($service_product_type == 'type16') {
                    $js .= '
                    if(IsPhone()) {
                        $("'.$addon_id.' .smail-div").click(function() {
                            $("'.$addon_id.' .smail-div").removeClass("actives");
                            $(this).addClass("actives");
                        })
                    }
                    ';
                }
            $js .= '})
        ';
        return $js;
    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
