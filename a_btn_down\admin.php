<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees123
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type'       => 'content',
        'addon_name' => 'a_btn_down',
        'title'      => JText::_('下载A标签'),
        'desc'       => JText::_('下载A标签'),
        'category'   => '常用插件',
        'attr'       => array(
            'general' => array(
                'mian-a'                => array(
                    'type'  => 'separator',
                    'title' => JText::_('全局设置'),
                ),
                'a_label'               => array(
                    'type'  => 'text',
                    'title' => JText::_('标签文字'),
                    'desc'  => JText::_('前端显示的文字'),
                    'std'   => '下载',
                ),
                'a_url'                 => array(
                    'type'  => 'text',
                    'title' => JText::_('下载地址'),
                    'desc'  => JText::_('此处填写是下载文件的地址'),
                    'std'   => '',
                ),
                'is_btn'                => array(
                    'type'   => 'checkbox',
                    'title'  => JText::_('是否按钮下载'),
                    'desc'   => JText::_('可以切换成文字下载和按钮下载'),
                    'values' => array(
                        1 => JText::_('是'),
                        0 => JText::_('否'),
                    ),
                    'std'    => 0,
                ),
                'a-options'             => array(
                    'type'    => 'separator',
                    'title'   => JText::_('文本选项'),
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),
                'is_a_decoration'       => array(
                    'type'   => 'checkbox',
                    'title'  => JText::_('是否显示下划线'),
                    'desc'   => JText::_('是否显示文字下划线'),
                    'values' => array(
                        1 => JText::_('是'),
                        0 => JText::_('否'),
                    ),
                    'std'    => 1,
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),
                'a_options_color'       => array(
                    'type'    => 'color',
                    'title'   => JText::_('字体颜色'),
                    'std'     => '#f71b1b',
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),
                'a_options_fontsize'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('字体大小'),
                    'std'     => 12,
                    'max'     => 100,
                    'depends' => array(
                        array('is_btn', '=', '0'),
                    ),
                ),
                'a_options_hover_color' => array(
                    'type'    => 'color',
                    'title'   => JText::_('鼠标悬停字体颜色'),
                    'std'     => '#f71b1b',
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),
                'a_options_target'      => array(
                    'type'    => 'select',
                    'title'   => JText::_('链接打开'),
                    'desc'    => JText::_('选择你的链接是否在一个新窗口打开'),
                    'values'  => array(
                        ''       => JText::_('当前窗口'),
                        '_blank' => JText::_('新窗口'),
                    ),
                    'depends' => array(
                        array('a_url', '!=', ''),
                        array('is_btn', '=', 0),
                    ),
                ),
                'a_options_padding'     => array(
                    'type'       => 'padding',
                    'title'      => JText::_('字体内边距'),
                    'std'        =>array('md' => '0px 10px 0px 10px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),

                'a_options_margin'      => array(
                    'type'       => 'margin',
                    'title'      => JText::_('字体外边距'),
                    'std'        => array('md' => '0px 10px 0px 10px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('is_btn', '=', 0),
                    ),
                ),
                'btn-options'             => array(
                    'type'    => 'separator',
                    'title'   => JText::_('按钮选项'),
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
                'btn_options_width'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('按钮宽度'),
                    'std'     => 100,
                    'max'     => 500,
                    'depends' => array(
                        array('is_btn', '=', '1'),
                    ),
                ),
                'btn_options_height'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('按钮高度'),
                    'std'     => 30,
                    'max'     => 500,
                    'depends' => array(
                        array('is_btn', '=', '1'),
                    ),
                ),
                 'btn_options_line_height'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('按钮行高'),
                    'std'     => 30,
                    'max'     => 500,
                    'depends' => array(
                        array('is_btn', '=', '1'),
                    ),
                ),
                 'btn_options_radius'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('按钮圆角'),
                    'std'     => 30,
                    'max'     => 500,
                    'depends' => array(
                        array('is_btn', '=', '1'),
                    ),
                ),
                'btn_options_color'       => array(
                    'type'    => 'color',
                    'title'   => JText::_('字体颜色'),
                    'std'     => '#ffffff',
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
               'btn_options_bg_color'       => array(
                    'type'    => 'color',
                    'title'   => JText::_('按钮背景颜色'),
                    'std'     => '#f71b1b',
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
                'btn_options_fontsize'    => array(
                    'type'    => 'slider',
                    'title'   => JText::_('字体大小'),
                    'std'     => 12,
                    'max'     => 23,
                    'depends' => array(
                        array('is_btn', '=', '1'),
                    ),
                ),
                'btn_options_hover_color' => array(
                    'type'    => 'color',
                    'title'   => JText::_('悬浮字体颜色'),
                    'std'     => '#f71b1b',
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
                'btn_options_hover_bg_color' => array(
                    'type'    => 'color',
                    'title'   => JText::_('悬浮按钮背景颜色'),
                    'std'     => '#f71b1b',
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
                'btn_options_target'      => array(
                    'type'    => 'select',
                    'title'   => JText::_('链接打开'),
                    'desc'    => JText::_('选择你的链接是否在一个新窗口打开'),
                    'values'  => array(
                        ''       => JText::_('当前窗口'),
                        '_blank' => JText::_('新窗口'),
                    ),
                    'depends' => array(
                        array('a_url', '!=', ''),
                        array('is_btn', '=', 1),
                    ),
                ),
                'btn_options_padding'     => array(
                    'type'       => 'padding',
                    'title'      => JText::_('字体内边距'),
                    'std'        => array('md' => '0px 10px 0px 10px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),

                'btn_options_margin'      => array(
                    'type'       => 'margin',
                    'title'      => JText::_('字体外边距'),
                    'std'        => array('md' => '0px 10px 0px 10px', 'sm' => '0px 10px 0px 10px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('is_btn', '=', 1),
                    ),
                ),
            ),
        ),
    )
);
