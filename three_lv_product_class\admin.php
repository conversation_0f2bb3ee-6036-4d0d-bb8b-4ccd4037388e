<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'three_lv_product_class',
        'title' => '三级产品分类选项卡',
        'desc' => '三级产品分类选项卡',
        'category' => '产品',
        'attr' => array(

            'shiyong_type' => array(
                'type' => 'select',
                'title' => '使用方式 ',
                'desc' => '使用方式',
                'values' => array(
                    'pc' => JText::_('pc端'),
                    // 'app' => JText::_('移动端'),
                ),
                'std' => 'pc',
            ),
            'link_tz_fs' => array(
                'type' => 'checkbox',
                'title' => JText::_('链接跳转'),
                'std' => 0,
            ),
            'style_type' => array(
                'type' => 'select',
                'title' => '菜单样式 ',
                'desc' => '菜单样式',
                'values' => array(
                    'type1' => JText::_('样式1'),
                    'type2' => JText::_('样式2'),
                ),
                'std' => 'type1',
            ),
            'type_start' => array(
                'type' => 'number',
                'title' => '从第n个分类开始显示',
                'desc' => '从一级分类的第n个分类开始显示',
                'std' => '1'
            ),
            'type_num' => array(
                'type' => 'number',
                'title' => '显示n条分类',
                'desc' => '显示n条分类,默认10条',
                'std' => '10'
            ),
            'limit' => array(//每个分类下的条数
                'type' => 'number',
                'title' => '每个分页显示几条数据',
                'std' => '10'
            ),
            'detail_page_id' => array(
                'type' => 'select',
                'title' => '详情页模版',
                'desc' => '显示文章详情页模版',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
            ),
            'show_pro_data' => array(
                'type' => 'checkbox',
                'title' => JText::_('获取分类下所有数据'),
                'std' => 1,
            ),
            'nav_ordering' => array(
                'type' => 'select',
                'title' => JText::_('导航排序'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                'values' => array(
                    'ASC' => JText::_('排序id正序'),
                    'DESC' => JText::_('排序id倒序'),
                ),
                'std' => 'ASC',
            ),
            'ordering' => array(
                'type' => 'select',
                'title' => JText::_('列表排序'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                'values' => array(
                    'sortasc' => JText::_('排序id正序'),
                    'sortdesc' => JText::_('排序id倒序'),
                ),
                'std' => 'DESC',
            ),
            'nav_max_width' => array(
                'type' => 'slider',
                'title' => JText::_('导航宽度'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                'max' => 50,
                'min' => 10,
                'std' => 15,
            ),
            'nav_tab_c' => array(
                'type' => 'separator',
                'title' => JText::_('一级导航设置')
            ),
            //导航样式1
            'nav_width_1' => array(
                'type' => 'slider',
                'title' => JText::_('一级导航宽度'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                'max' => 100,
                'std' => 100,
            ),
            'nav_height_1' => array(
                'type' => 'slider',
                'title' => JText::_('一级导航高度'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                'max' => 200,
                'std' => 50,
            ),
            'nav_margin_left_1' => array(
                'type' => 'slider',
                'title' => JText::_('一级导航左边距'),
                'max' => 100,
                'std' => 0,
            ),
            'nav_bg_color_1' => array(
                'type' => 'color',
                'title' => JText::_('一级导航背景颜色'),
                'std' => '#1046d6',
            ),
            'nav_color_1' => array(
                'type' => 'color',
                'title' => JText::_('一级导航字体颜色'),
                'std' => '#fff',
            ),
            'nav_icon_1' => array(
                'type' => 'text',
                'title' => JText::_('一级导航展开按钮'),
                'std' => '+',
            ),
            'nav_align_1' => array(
                'type' => 'select',
                'title' => JText::_('一级导航文字位置'),
                'desc' => JText::_('一级导航文字位置'),
                'values' => array(
                    'left' => JText::_('左'),
                    'center' => JText::_('中'),
                    'right' => JText::_('右'),
                ),
                'std' => 'center',
            ),
            'nav_1_size' => array(
                'type' => 'slider',
                'title' => JText::_('一级导航字体大小'),
                'max' => 50,
                'std' => 18,
            ),
            'nav_icon_1_size' => array(
                'type' => 'slider',
                'title' => JText::_('一级导航展开按钮字体大小'),
                'max' => 100,
                'std' => 16,
            ),
            'nav_tab_c_2' => array(
                'type' => 'separator',
                'title' => JText::_('二级导航设置')
            ),
            'nav_width_2' => array(
                'type' => 'slider',
                'title' => JText::_('二级导航宽度'),
                'max' => 100,
                'std' => 100,
            ),
            'nav_margin_left_2' => array(
                'type' => 'slider',
                'title' => JText::_('二级导航左边距'),
                'max' => 100,
                'std' => 0,
            ),
            'nav_bg_color_2' => array(
                'type' => 'color',
                'title' => JText::_('二级导航背景颜色'),
                'std' => '#fff',
            ),
            'nav_color_2' => array(
                'type' => 'color',
                'title' => JText::_('二级导航字体颜色'),
                'std' => '#000',
            ),
            'nav_icon_2' => array(
                'type' => 'text',
                'title' => JText::_('二级导航展开按钮'),
                'std' => '+',
            ),
            'nav_align_2' => array(
                'type' => 'select',
                'title' => JText::_('二级导航文字位置'),
                'desc' => JText::_('二级导航文字位置'),
                'values' => array(
                    'left' => JText::_('左'),
                    'center' => JText::_('中'),
                    'right' => JText::_('右'),
                ),
                'std' => 'center',
            ),
            'nav_2_size' => array(
                'type' => 'slider',
                'title' => JText::_('二级导航字体大小'),
                'max' => 50,
                'std' => 16,
            ),
            'nav_icon_2_size' => array(
                'type' => 'slider',
                'title' => JText::_('二级导航展开按钮字体大小'),
                'max' => 100,
                'std' => 16,
            ),
            'nav_tab_c_3' => array(
                'type' => 'separator',
                'title' => JText::_('三级导航设置')
            ),
            'nav_width_3' => array(
                'type' => 'slider',
                'title' => JText::_('三级导航宽度'),
                'max' => 100,
                'std' => 100,
            ),
            'nav_margin_left_3' => array(
                'type' => 'slider',
                'title' => JText::_('三级导航左边距'),
                'max' => 100,
                'std' => 0,
            ),
            'nav_bg_color_3' => array(
                'type' => 'color',
                'title' => JText::_('三级导航背景颜色'),
                'std' => '#fff',
            ),
            'nav_3_size' => array(
                'type' => 'slider',
                'title' => JText::_('三级导航字体大小'),
                'std' => 14,
                'max'=>50
            ),
            'nav_color_3' => array(
                'type' => 'color',
                'title' => JText::_('三级导航字体颜色'),
                'std' => '#605c5c',
            ),
            'nav_align_3' => array(
                'type' => 'select',
                'title' => JText::_('三级导航文字位置'),
                'desc' => JText::_('三级导航文字位置'),
                'values' => array(
                    'left' => JText::_('左'),
                    'center' => JText::_('中'),
                    'right' => JText::_('右'),
                ),
                'std' => 'center',
            ),

            'nav_bg_check' => array(
                'type' => 'checkbox',
                'title' => JText::_('开启导航选中设置'),
                'std' => '0',
            ),
            'nav_bgxz_color' => array(
                'type' => 'color',
                'title' => JText::_('导航选中背景色'),
                'std' => '#baab9e',
                'depends' => array(
                    array('nav_bg_check', '=', 1),
                ),
            ),
            'nav_font_color' => array(
                'type' => 'color',
                'title' => JText::_('选中字体颜色'),
                'std' => '#fff',
                'depends' => array(
                    array('nav_bg_check', '=', 1),
                ),
            ),

            //翻页
            'show_page_text' => array(
                'type' => 'separator',
                'title' => JText::_('翻页码设置'),
            ),
            'show_page' => array(
                'type' => 'checkbox',
                'title' => JText::_('显示翻页码'),
                'std' => '0',
            ),
            'page1_fontcolor' => array(
                'type' => 'color',
                'title' => JText::_('翻页字体颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#1e1e1e',
            ),
            'page1_bordercolor' => array(
                'type' => 'color',
                'title' => JText::_('翻页边框颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#b3b3b3',
            ),
            'page1_bgcolor' => array(
                'type' => 'color',
                'title' => JText::_('翻页背景颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#b3b3b3',
            ),
            'page1_cur_fontcolor' => array(
                'type' => 'color',
                'title' => JText::_('当前页字体颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#fff',
            ),
            'page1_cur_bordercolor' => array(
                'type' => 'color',
                'title' => JText::_('当前页边框颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#2a68a7',
            ),
            'page1_cur_bgcolor' => array(
                'type' => 'color',
                'title' => JText::_('当前页背景颜色'),
                'depends' => array(
                    array('show_page', '=', true),

                ),
                'std' => '#2a68a7',
            ),
            'page1_min_width' => array(
                'type' => 'slider',
                'title' => JText::_('翻页最小宽度'),
                'depends' => array(
                    array('show_page', '=', true),
                ),
                'std' => '',
            ),
            'page1_margin_bottom' => array(
                'type' => 'slider',
                'title' => JText::_('翻页下边距'),
                'depends' => array(
                    array('show_page', '=', true),
                ),
                'std' => '',
            ),
            'content' => array(
                'type' => 'separator',
                'title' => JText::_('内容设置')
            ),
            'page2_cont_bgcolor' => array(
                'type' => 'color',
                'title' => JText::_('内容背景颜色'),

                'std' => '',
            ),
            'page2_list_bgcolor' => array(
                'type' => 'color',
                'title' => JText::_('列表背景颜色'),
                'std' => '#baab9e',
            ),
            'page2_list_padding' => array(
                'type'       => 'padding',
                'title'      => JText::_('列表内边距'),
                'std'        =>'0px 0px 0px 0px',
                'responsive' => true,
            ),
            'page2_list_color' => array(
                'type' => 'color',
                'title' => JText::_('列表字体颜色'),
                'std' => '#000',
            ),
            'page2_list_size' => array(
                'type' => 'slider',
                'title' => JText::_('内容字体大小'),
                'std' => 16,
                'max' =>50,
            ),
            'vertical_position' => array(
                'type' => 'slider',
                'title' => JText::_('内容垂直位置调整'),
                'std' => 0,
                'max' =>300,
                'min' =>-300,
            ),
            'left_position' => array(
                'type' => 'slider',
                'title' => JText::_('内容左右位置调整'),
                'std' => 0,
                'max' =>300,
                'min' =>-300,
            ),
            'left_cont_width' => array(
                'type' => 'slider',
                'title' => JText::_('PC内容列表宽度'),
                'std' => 44,
                'max' =>100,
            ),
            'left_cont_width_jg' => array(
                'type' => 'slider',
                'title' => JText::_('PC内容列表间隔'),
                'desc' => '间隔往大设置的同时，将内容列表宽度缩小',
                'std' => 0.5,
                'max' =>10,
                'min' =>0,
            ),
            'left_phonecont_width' => array(
                'type' => 'slider',
                'title' => JText::_('手机内容列表宽度'),
                'std' => 43,
                'max' =>100,
            ),
            'title_font_height' => array(
                'type' => 'slider',
                'title' => JText::_('pc标题高度'),
                'std' => 60,
                'max' =>200,
                'min' =>10,
            ),
            'title_font_height_yd' => array(
                'type' => 'slider',
                'title' => JText::_('手机端标题高度'),
                'std' => 60,
                'max' =>200,
                'min' =>10,
            ),
            'img_height' => array(
                'type' => 'slider',
                'title' => JText::_('PC图片高度'),
                'std' => 'auto',
                'max' =>1000,
                'min' =>10,
            ),
            'phoneimg_height' => array(
                'type' => 'slider',
                'title' => JText::_('手机图片高度'),
                'std' => 'auto',
                'max' =>1000,
                'min' =>10,
            ),
            'show_nav' => array(
                'type' => 'checkbox',
                'title' => JText::_('默认不显示二三级导航'),
                'std' => '0',
            ),
            'show_nav_two' => array(
                'type' => 'checkbox',
                'title' => JText::_('默认不显示三级导航'),
                'std' => '0',
            ),
            'show_one_nav' => array(
                'type' => 'checkbox',
                'title' => JText::_('只显示一级导航'),
                'std' => '0',
            ),
            'img_style' => array(
                'type' => 'select',
                'title' => JText::_('图片填充方式'),
                'values' => array(
                    'contain' => JText::_('自适应显示'),
                    'unset' => JText::_('占满不切割显示'),
                    'none' => JText::_('占满切割显示'),
                ),
                'std' => 'fill',
            ),
            'nav_bg_color' => array(
                'type' => 'color',
                'title' => JText::_('导航整体背景颜色'),
                'std' => '#666',
            ),

        ),
    )
);
