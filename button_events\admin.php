<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
    array(
        'type' => 'general',
        'addon_name' => 'button_events',
        'title' => JText::_('按钮事件'),
        'desc' => JText::_('按钮事件'),
        'category' => '按钮',
        'attr' => array(
            'general' => array(
                'text' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT_DESC'),
                    'std' => 'Button',
                ),
                'tz_page_type' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(

                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                        'wx_links' => JText::_('微信'),
                        'QQ_links' => JText::_('QQ'),
                        'email_links' => JText::_('邮箱'),
                        'tel' => JText::_('电话'),
                        'none' => JText::_('不跳转'),

                    ),
                    'std' => 'Internal_pages',
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                ),
                'number_text' => array(
                    'type' => 'text',
                    'title' => '微信号/QQ号/邮箱号/手机号',
                    'depends' => array(
                        array('tz_page_type', '!=', 'Internal_pages'),
                        array('tz_page_type', '!=', 'external_links'),
                    ),
                ),
                'detail_page' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(array('tz_page_type', '=', 'external_links')),
                ),
                'target' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                    ),
                    'depends' => array(
                        array('tz_page_type', '!=', 'wx_links'),
                        array('tz_page_type', '!=', 'QQ_links'),
                        array('tz_page_type', '!=', 'email_links'),
                        array('tz_page_type', '!=', 'tel'),
                    ),
                ),
                'button_height' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'max' => 500,
                    'min' => 10,
                    'std' => 50,
                ),
                'button_width' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'max' => 500,
                    'min' => 10,
                    'std' => 150,
                ),
                'button_border_width' => array(
                    'type' => 'slider',
                    'title' => '按钮边框宽度',
                    'max' => 10,
                    'min' => 0,
                    'std' => 1,
                ),
                'button_font_size' => array(
                    'type' => 'slider',
                    'title' => '按钮字体大小',
                    'max' => 50,
                    'min' => 10,
                    'std' => 15,
                ),
                'button_color' => array(
                    'type' => 'color',
                    'title' => '按钮字体颜色',
                    'std' => '#000000',
                ),
                'button_color_hr' => array(
                    'type' => 'color',
                    'title' => '按钮滑过字体颜色',
                    'std' => '#000000',
                ),
                'button_bgcolor' => array(
                    'type' => 'color',
                    'title' => '按钮背景颜色',
                    'std' => '#ffffff',
                ),
                'button_bgcolor_hr' => array(
                    'type' => 'color',
                    'title' => '按钮滑过背景颜色',
                    'std' => '#ffffff',
                ),
                'button_border_color' => array(
                    'type' => 'color',
                    'title' => '按钮边框颜色',
                    'std' => '#000000',
                ),
                'button_border_color_hr' => array(
                    'type' => 'color',
                    'title' => '按钮滑过边框颜色',
                    'std' => '#000000',
                ),

                'button_weizhi' => array(
                    'type' => 'select',
                    'title' => '按钮位置',
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                    'std' => 'left',
                ),
                'button_border_radius' => array(
                    'type' => 'slider',
                    'title' => '按钮边框弧度',
                    'std' => '0',
                    'max' => 50,
                    'min' => 0,
                ),
                'button_hverm' => array(
                    'type' => 'checkbox',
                    'title' => '开启划过显示二维码',
                    'std' => '0',
                ),
                'ewm_img' => array(
                    'type' => 'media',
                    'title' => '二维码图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220330/104a593789c277a9bc97e031ed9ac93b.png',
                    'depends' => array(
                        array('button_hverm', '=', '1'),
                    ),
                ),
                'ewm_width' => array(
                    'type' => 'slider',
                    'title' => '二维码宽高',
                    'max' => '300',
                    'responsive' => true,
                    'std' => array(
                        'md' => 160,
                        'sm' => 160,
                        'xs' => 120
                    ),
                ),
                
            ),
        ),
    )
);
