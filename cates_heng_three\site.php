<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonCates_heng_three extends JwpagefactoryAddons
{

	public function render()
	{

		$page_view_name = isset($_GET['view']);

		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$style = (isset($settings->style) && $settings->style) ? $settings->style : 'panel-default';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		// Addon options
		$resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
		$catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
		$tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
		$k2catid = (isset($settings->k2catid) && $settings->k2catid) ? $settings->k2catid : 0;
		$include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
		$post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
		$ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
		$limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
		$offset = (isset($settings->offset) && $settings->offset) ? $settings->offset : 0;
		$columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
		$show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
		$intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
		$hide_thumbnail = (isset($settings->hide_thumbnail)) ? $settings->hide_thumbnail : 0;
		$show_author = (isset($settings->show_author)) ? $settings->show_author : 1;
		$show_category = (isset($settings->show_category)) ? $settings->show_category : 1;
		$show_date = (isset($settings->show_date)) ? $settings->show_date : 1;
		$show_readmore = (isset($settings->show_readmore)) ? $settings->show_readmore : 1;
		$readmore_text = (isset($settings->readmore_text) && $settings->readmore_text) ? $settings->readmore_text : 'Read More';
		$link_articles = (isset($settings->link_articles)) ? $settings->link_articles : 0;
		$link_catid = (isset($settings->link_catid)) ? $settings->link_catid : 0;
		$link_k2catid = (isset($settings->link_k2catid)) ? $settings->link_k2catid : 0;

		$all_articles_btn_text = (isset($settings->all_articles_btn_text) && $settings->all_articles_btn_text) ? $settings->all_articles_btn_text : 'See all posts';
		$all_articles_btn_class = (isset($settings->all_articles_btn_size) && $settings->all_articles_btn_size) ? ' jwpf-btn-' . $settings->all_articles_btn_size : '';
		$all_articles_btn_class .= (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? ' jwpf-btn-' . $settings->all_articles_btn_type : ' jwpf-btn-default';
		$all_articles_btn_class .= (isset($settings->all_articles_btn_shape) && $settings->all_articles_btn_shape) ? ' jwpf-btn-' . $settings->all_articles_btn_shape : ' jwpf-btn-rounded';
		$all_articles_btn_class .= (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? ' jwpf-btn-' . $settings->all_articles_btn_appearance : '';
		$all_articles_btn_class .= (isset($settings->all_articles_btn_block) && $settings->all_articles_btn_block) ? ' ' . $settings->all_articles_btn_block : '';
		$all_articles_btn_icon = (isset($settings->all_articles_btn_icon) && $settings->all_articles_btn_icon) ? $settings->all_articles_btn_icon : '';
		$all_articles_btn_icon_position = (isset($settings->all_articles_btn_icon_position) && $settings->all_articles_btn_icon_position) ? $settings->all_articles_btn_icon_position : 'left';
        $cate_type = (isset($settings->cate_type) && $settings->cate_type) ? $settings->cate_type : 1;
        $link_page = (isset($settings->link_page) && $settings->link_page) ? $settings->link_page : '';
        $cates_color = (isset($settings->cates_color) && $settings->cates_color) ? $settings->cates_color : '#fff';
        $cates_background = (isset($settings->cates_background) && $settings->cates_background) ? $settings->cates_background : '#0360b7';
        $cates_hover = (isset($settings->cates_hover) && $settings->cates_hover) ? $settings->cates_hover : '#969696';



		$output = '';
//		$article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/cates.php';
//        require_once $article_helper;
        if($offset>0){
            $offset = $offset-1;
        }
        $items = [];//JwpagefactoryHelperCates::getCates($cate_type, $limit,$offset);

//        var_dump($items);
		if (!count($items)) {
			$output .= '<p class="alert alert-warning">没有找到分类</p>';
			return $output;
		}





        $output .= ' <div class="LCS-HLJ-DZ0106-e">
                        <div class="LCS-HLJ-DZ0106-e-li">';
        if (count((array)$items)) {
            foreach ($items as $key => $item) {
                if (is_object($item)) {
                    //   跳转链接
                    if(!empty($link_page)){
                        $link_url = '/pages/index.php?Itemid='.$link_page.'&cate_id='.$item->id;
                    }else{
                        $link_url = 'javascript:;';
                    }
                    $output .= '<a href="'.$link_url.'" class="LCS-HLJ-DZ0106-e-a">'.$item->title.'</a>';
                }
            }
        }


        $output .= '</div>
                    </div>';

        $output .= '<style>
                       .clear:after{content:"";display: block;clear:both;}
                        .LCS-HLJ-DZ0106-e{width: 100%;}
                        .LCS-HLJ-DZ0106-e-li{width: 1200px;margin: 40px auto;text-align: center;}
                        .LCS-HLJ-DZ0106-e .LCS-HLJ-DZ0106-e-a{text-decoration: none;font-size: 16px;line-height: 42px;background: '.$cates_background.';border-radius: 20px;display: inline-block;color: '.$cates_color.';padding: 0 30px;margin: 0 15px;margin-bottom: 10px;}
                    </style>';
        return $output;
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
		$css_path = new JLayoutFile('addon.css.button', $layout_path);
		$settings = $this->addon->settings;

		$options = new stdClass;
		$options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
		$options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
		$options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
		$options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
		$options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
		$options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
		$options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
		$options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
		$options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';

		return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
	}

	static function isComponentInstalled($component_name)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);
		$query->select('a.enabled');
		$query->from($db->quoteName('#__extensions', 'a'));
		$query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
		$db->setQuery($query);
		$is_enabled = $db->loadResult();
		return $is_enabled;
	}

}