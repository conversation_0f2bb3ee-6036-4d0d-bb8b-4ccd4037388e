<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonIcon_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $isk2installed = self::isComponentInstalled('icon_swiper');

        if ($isk2installed === 0) {
            return '<div>出错了</div>';
        }

        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $items=(isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';


        $output='<div class="box">
           <div class="content" id="content_'.$addonId.'">';
        foreach ($items as $key => $item) {
            if($item->is_link){
                $output.='<a href="'.$item->image_carousel_img_link.'" target="'.($item->link_open_new_window===1?'_blank':'').'" style="display: block; height:100%;">';
            }
                $output.='
                    <div id = "con_'.$key.'" class="con" style="background-image: url(\''.$item->img.'\')">';
                        $output.='<div class="top">
                            <img src=\''.$item->logo.'\' class="logo jwpf-wow fadeInLeftBig clearfix" alt="">
                            <div class="top-right jwpf-wow fadeInRightBig clearfix">
                                <div class="title">'.$item->company.'</div>
                                <div class="url">'.$item->url.'</div>
                            </div>
                        </div>
                        <div class="bottom jwpf-wow fadeInRightBig clearfix">
                            '.$item->introduce.'
                        </div>';
                    $output.='</div>
                ';

            if($item->is_link){
                $output.='</a>';
            }
        }
        $output.='</div>
            <div class="swiper-box" id="swiper_' . $addonId . '">
                <div class="time-line"></div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">';
                    foreach ($items as $key => $item) {
                        $output.='<div class="swiper-slide" id="icon_'.$key.'">';
                            $output.='<div class="z-nav1-i1">
                                <img src=\''.$item->icon.'\' class="img1" alt="">
                                <img src=\''.$item->icon_active.'\' class="img2" alt="">
                            </div>
                            <div class="z-nav1-p1">'.$item->text.'</div>';
                        $output.='</div>';
                        if ($key===count($items)-1){
                            $output.='<div class="swiper-slide" id="icon_holder"></div>';
                        }
                    }
        $output.='
                </div>
            </div>
            
            <!-- Add Arrows -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>';




        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;

        if (isset($settings->carousel_item_number) && $settings->carousel_item_number) {
            if (is_object($settings->carousel_item_number)) {
                $carousel_item_number = $settings->carousel_item_number->md;
                $carousel_item_number_sm = $settings->carousel_item_number->sm;
                $carousel_item_number_xs= $settings->carousel_item_number->xs;
            } else {
                $carousel_item_number = $settings->carousel_item_number;
                $carousel_item_number_sm = $settings->carousel_item_number_sm;
                $carousel_item_number_xs = $settings->carousel_item_number_xs;
            }
        } else {
            $carousel_item_number = 5;
            $carousel_item_number_sm = 3;
            $carousel_item_number_xs = 2;
        }


        $script= 'jQuery(document).ready(function($){
                function initSwiper(slidesPerView,spaceBetween){
                    var clickEle=null;
                    let settings={
                        loopFillGroupWithBlank: true,           
                        speed: '.$carousel_speed.',
                        spaceBetween: 0,
                        loop: true,
                        observer: true,
                        observeParents:true,
                        navigation: {
                            nextEl: "#swiper_' . $addonId . ' .swiper-button-next",
                            prevEl: "#swiper_' . $addonId . ' .swiper-button-prev",
                        },
                        breakpoints: {
                            0: {
                                slidesPerView: ' . ($carousel_item_number_xs) . ',
                            },
                            767: {
                                slidesPerView: ' . ($carousel_item_number_sm) . ',
                            },
                            991: {
                                slidesPerView: ' . ($carousel_item_number) . ',
                            },
                        },
                    }
                    $("#swiper_' . $addonId . '").on("click", ".swiper-slide", function () {
                        let index=$(this).attr("id").replace("icon_","");
                        
                        $(this).addClass("active").siblings().removeClass("active");
                        
                        $("#content_'.$addonId.' #con_"+index).addClass("lc-active").siblings().removeClass("lc-active");
                        
                        $("#content_'.$addonId.' #con_"+index+" .logo").addClass("fadeInLeftBig jwpf-animated");
                        $("#content_'.$addonId.' #con_"+index).siblings().find(".logo").removeClass("fadeInLeftBig jwpf-animated").attr("style","");
                        
                        $("#content_'.$addonId.' #con_"+index+" .top-right").addClass("fadeInRightBig jwpf-animated");
                        $("#content_'.$addonId.' #con_"+index).siblings().find(".top-right").removeClass("fadeInRightBig jwpf-animated").attr("style","");
                        
                        $("#content_'.$addonId.' #con_"+index+" .bottom").addClass("fadeInRightBig jwpf-animated");
                        $("#content_'.$addonId.' #con_"+index).siblings().find(".bottom").removeClass("fadeInRightBig jwpf-animated").attr("style","");
                    })
                    let swiper = new Swiper("#swiper_' . $addonId . ' .swiper-container", settings);
                    return swiper;
                }
                
                
                initSwiper();
                $("#swiper_'.$addonId.' .swiper-slide-active").addClass("active").siblings().removeClass("active");
                $("#content_'.$addonId.' #con_0").addClass("lc-active").siblings().removeClass("lc-active");
                
                setTimeout(function(){
                    $("#content_'.$addonId.' #con_0").siblings().find(".logo").removeClass("fadeInLeftBig jwpf-animated").attr("style","");
                    $("#content_'.$addonId.' #con_0").siblings().find(".top-right").removeClass("fadeInRightBig jwpf-animated").attr("style","");
                    $("#content_'.$addonId.' #con_0").siblings().find(".bottom").removeClass("fadeInRightBig jwpf-animated").attr("style","");
                }, 500);
                
                window.onresize=function (){
                    initSwiper();
                }  
            })';



        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;

//        轮播
        $addonId = '#swiper_'.$this->addon->id;
        $id = '#content_'.$this->addon->id;

        if (isset($settings->carousel_width) && $settings->carousel_width) {
            if (is_object($settings->carousel_width)) {
                $carousel_width = $settings->carousel_width->md;
                $carousel_width_sm = $settings->carousel_width->sm;
                $carousel_width_xs= $settings->iconv_font_up->xs;
            } else {
                $carousel_width = $settings->carousel_width;
                $carousel_width_sm = $settings->carousel_width_sm;
                $carousel_width_xs = $settings->carousel_width_xs;
            }
        } else {
            $carousel_width = 67;
            $carousel_width_sm = 67;
            $carousel_width_xs = 67;
        }
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
                $carousel_height_sm = $settings->carousel_height->sm;
                $carousel_height_xs= $settings->carousel_height->xs;
            } else {
                $carousel_height = $settings->carousel_height;
                $carousel_height_sm = $settings->carousel_height_sm;
                $carousel_height_xs = $settings->carousel_height_xs;
            }
        } else {
            $carousel_height = 140;
            $carousel_height_sm = 140;
            $carousel_height_xs = 140;
        }
        if (isset($settings->arrow_width) && $settings->arrow_width) {
            if (is_object($settings->arrow_width)) {
                $arrow_width = $settings->arrow_width->md;
                $arrow_width_sm = $settings->arrow_width->sm;
                $arrow_width_xs= $settings->arrow_width->xs;
            } else {
                $arrow_width = $settings->arrow_width;
                $arrow_width_sm = $settings->arrow_width_sm;
                $arrow_width_xs = $settings->arrow_width_xs;
            }
        } else {
            $arrow_width = 50;
            $arrow_width_sm = 44;
            $arrow_width_xs = 22;
        }
        $arrow_background = (isset($settings->arrow_background) && $settings->arrow_background) ? $settings->arrow_background : '#fff';
        $arrow_border_color = (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? $settings->arrow_border_color : '#fff';
        $arrow_color = (isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '#ccc';
        $arrow_font_size= (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? $settings->arrow_font_size : 16;
        $arrow_hover_background= (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? $settings->arrow_hover_background : '#fff';
        $arrow_hover_border_color= (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? $settings->arrow_hover_border_color : '#d51920';
        $arrow_hover_color= (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '#fff';
        if (isset($settings->swiper_padding) && $settings->swiper_padding) {
            if (is_object($settings->swiper_padding)) {
                $swiper_padding = $settings->swiper_padding->md;
                $swiper_padding_sm = $settings->swiper_padding->sm;
                $swiper_padding_xs= $settings->swiper_padding->xs;
            } else {
                $swiper_padding = $settings->swiper_padding;
                $swiper_padding_sm = $settings->swiper_padding_sm;
                $swiper_padding_xs = $settings->swiper_padding_xs;
            }
        } else {
            $swiper_padding = '20px 0 20px 0';
            $swiper_padding_sm = '10px 0 10px 0';
            $swiper_padding_xs = '5px 0 5px 0';
        }
        if (isset($settings->swiper_img_height) && $settings->swiper_img_height) {
            if (is_object($settings->swiper_img_height)) {
                $swiper_img_height = $settings->swiper_img_height->md;
                $swiper_img_height_sm = $settings->swiper_img_height->sm;
                $swiper_img_height_xs= $settings->swiper_img_height->xs;
            } else {
                $swiper_img_height = $settings->swiper_img_height;
                $swiper_img_height_sm = $settings->swiper_img_height_sm;
                $swiper_img_height_xs = $settings->swiper_img_height_xs;
            }
        } else {
            $swiper_img_height = 50;
            $swiper_img_height_sm = 44;
            $swiper_img_height_xs = 22;
        }
        if (isset($settings->swiper_title_font) && $settings->swiper_title_font) {
            if (is_object($settings->swiper_title_font)) {
                $swiper_title_font = $settings->swiper_title_font->md;
                $swiper_title_font_sm = $settings->swiper_title_font->sm;
                $swiper_title_font_xs= $settings->swiper_title_font->xs;
            } else {
                $swiper_title_font = $settings->swiper_title_font;
                $swiper_title_font_sm = $settings->swiper_title_font_sm;
                $swiper_title_font_xs = $settings->swiper_title_font_xs;
            }
        } else {
            $swiper_title_font = 16;
            $swiper_title_font_sm = 14;
            $swiper_title_font_xs = 12;
        }
        if (isset($settings->swiper_title_margin_top) && $settings->swiper_title_margin_top) {
            if (is_object($settings->swiper_title_margin_top)) {
                $swiper_title_margin_top = $settings->swiper_title_margin_top->md;
                $swiper_title_margin_top_sm = $settings->swiper_title_margin_top->sm;
                $swiper_title_margin_top_xs= $settings->swiper_title_margin_top->xs;
            } else {
                $swiper_title_margin_top = $settings->swiper_title_margin_top;
                $swiper_title_margin_top_sm = $settings->swiper_title_margin_top_sm;
                $swiper_title_margin_top_xs = $settings->swiper_title_margin_top_xs;
            }
        } else {
            $swiper_title_margin_top = 0;
            $swiper_title_margin_top_sm = 0;
            $swiper_title_margin_top_xs = 0;
        }

        $swiper_title_color= (isset($settings->swiper_title_color) && $settings->swiper_title_color) ? $settings->swiper_title_color : '#454343';
        $swiper_title_color_active= (isset($settings->swiper_title_color_active) && $settings->swiper_title_color_active) ? $settings->swiper_title_color_active : '#fff';
        $swiper_background= (isset($settings->swiper_background) && $settings->swiper_background) ? $settings->swiper_background : '#fff';
        $swiper_background_active= (isset($settings->swiper_background_active) && $settings->swiper_background_active) ? $settings->swiper_background_active : '#c2322c';

        if (isset($settings->content_height) && $settings->content_height) {
            if (is_object($settings->content_height)) {
                $content_height = $settings->content_height->md;
                $content_height_sm = $settings->content_height->sm;
                $content_height_xs= $settings->content_height->xs;
            } else {
                $content_height = $settings->content_height;
                $content_height_sm = $settings->content_height_sm;
                $content_height_xs = $settings->content_height_xs;
            }
        } else {
            $content_height = 720;
            $content_height_sm = 720;
            $content_height_xs = 720;
        }
        if (isset($settings->content_company_top) && $settings->content_company_top) {
            if (is_object($settings->content_company_top)) {
                $content_company_top = $settings->content_company_top->md;
                $content_company_top_sm = $settings->content_company_top->sm;
                $content_company_top_xs= $settings->content_company_top->xs;
            } else {
                $content_company_top = $settings->content_company_top;
                $content_company_top_sm = $settings->content_company_top_sm;
                $content_company_top_xs = $settings->content_company_top_xs;
            }
        } else {
            $content_company_top = 0;
            $content_company_top_sm = 0;
            $content_company_top_xs = 0;
        }
        if (isset($settings->content_company_bottom) && $settings->content_company_bottom) {
            if (is_object($settings->content_company_bottom)) {
                $content_company_bottom = $settings->content_company_bottom->md;
                $content_company_bottom_sm = $settings->content_company_bottom->sm;
                $content_company_bottom_xs= $settings->content_company_bottom->xs;
            } else {
                $content_company_bottom = $settings->content_company_bottom;
                $content_company_bottom_sm = $settings->content_company_bottom_sm;
                $content_company_bottom_xs = $settings->content_company_bottom_xs;
            }
        } else {
            $content_company_bottom = 0;
            $content_company_bottom_sm = 0;
            $content_company_bottom_xs = 0;
        }
        if (isset($settings->content_img_width) && $settings->content_img_width) {
            if (is_object($settings->content_img_width)) {
                $content_img_width = $settings->content_img_width->md;
                $content_img_width_sm = $settings->content_img_width->sm;
                $content_img_width_xs = $settings->content_img_width->xs;
            } else {
                $content_img_width = $settings->content_img_width;
                $content_img_width_sm = $settings->content_img_width_sm;
                $content_img_width_xs = $settings->content_img_width_xs;
            }
        } else {
            $content_img_width = '';
            $content_img_width_sm = '';
            $content_img_width_xs = '';
        }
        $content_img_margin_right= (isset($settings->content_img_margin_right) && $settings->content_img_margin_right) ? $settings->content_img_margin_right : 35;
        if (isset($settings->content_company_font) && $settings->content_company_font) {
            if (is_object($settings->content_company_font)) {
                $content_company_font = $settings->content_company_font->md;
                $content_company_font_sm = $settings->content_company_font->sm;
                $content_company_font_xs = $settings->content_company_font->xs;
            } else {
                $content_company_font = $settings->content_company_font;
                $content_company_font_sm = $settings->content_company_font_sm;
                $content_company_font_xs = $settings->content_company_font_xs;
            }
        } else {
            $content_company_font = 42;
            $content_company_font_sm = 42;
            $content_company_font_xs = 42;
        }
        if (isset($settings->content_url_font) && $settings->content_url_font) {
            if (is_object($settings->content_url_font)) {
                $content_url_font = $settings->content_url_font->md;
                $content_url_font_sm = $settings->content_url_font->sm;
                $content_url_font_xs = $settings->content_url_font->xs;
            } else {
                $content_url_font = $settings->content_url_font;
                $content_url_font_sm = $settings->content_url_font_sm;
                $content_url_font_xs = $settings->content_url_font_xs;
            }
        } else {
            $content_url_font = 16;
            $content_url_font_sm = 16;
            $content_url_font_xs = 16;
        }
        if (isset($settings->content_introduce_font) && $settings->content_introduce_font) {
            if (is_object($settings->content_introduce_font)) {
                $content_introduce_font = $settings->content_introduce_font->md;
                $content_introduce_font_sm = $settings->content_introduce_font->sm;
                $content_introduce_font_xs = $settings->content_introduce_font->xs;
            } else {
                $content_introduce_font = $settings->content_introduce_font;
                $content_introduce_font_sm = $settings->content_introduce_font_sm;
                $content_introduce_font_xs = $settings->content_introduce_font_xs;
            }
        } else {
            $content_introduce_font = 18;
            $content_introduce_font_sm = 18;
            $content_introduce_font_xs = 18;
        }
        if (isset($settings->content_introduce_width) && $settings->content_introduce_width) {
            if (is_object($settings->content_introduce_width)) {
                $content_introduce_width = $settings->content_introduce_width->md;
                $content_introduce_width_sm = $settings->content_introduce_width->sm;
                $content_introduce_width_xs = $settings->content_introduce_width->xs;
            } else {
                $content_introduce_width = $settings->content_introduce_width;
                $content_introduce_width_sm = $settings->content_introduce_width_sm;
                $content_introduce_width_xs = $settings->content_introduce_width_xs;
            }
        } else {
            $content_introduce_width = 16;
            $content_introduce_width_sm = 16;
            $content_introduce_width_xs = 16;
        }
        $content_color= (isset($settings->content_color) && $settings->content_color) ? $settings->content_color : '#fff';


        $output = '
            #jwpf-addon-'.$this->addon->id.' .box{
                width: 100%;
            }
            /*轮播上面的图*/
            '.$id.'{
                width: 100%;
                height: '.$content_height.'px;
                position: relative;
            }
            '.$id.' .con{
                position: absolute;
                top: 0;
                left: 0;
                background-repeat: no-repeat;
                background-position:center;
                background-size: cover;
                width: 100%;
                height: 100%;
                display: none;
                justify-content: flex-start;
                align-items: center;
                flex-direction: column;
            }
            '.$id.' .con.lc-active{
                display: flex;
            }
            '.$id.' .top{
                margin-top: '.$content_company_top.'px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: '.$content_company_bottom.'px;
            }
            '.$id.' .logo{
                margin-right: '.$content_img_margin_right.'px;
                width: '.$content_img_width.'px;
            }
            '.$id.' .title{
                font-size: '.$content_company_font.'px;
                color: '.$content_color.';
                line-height: 1.5;
            }
            '.$id.' .url{
                font-size: '.$content_url_font.'px;
                color: '.$content_color.';
                line-height: 1.5;
            }
            '.$id.' .bottom{
                font-size: '.$content_introduce_font.'px;
                line-height: 2;
                color: '.$content_color.';
                width: '.$content_introduce_width.'%;
            }
            /*轮播*/
            '.$addonId.'{
                position: relative;
                width: '.$carousel_width.'%;
                overflow: hidden;
                padding: 0 '.$arrow_width.'px;
                transform: translateY(-50%);
                height: '.$carousel_height.'px;
                margin: 0 auto;
            }
            '.$addonId.' .swiper-container{
                height: 100%;
                background: '.$swiper_background.';
            }
            '.$addonId.' .swiper-slide{
                background: '.$swiper_background.';
                padding: '.$swiper_padding.';
                position:relative;
                box-sizing: content;
                min-height: 100%;
            }
            '.$addonId.' .swiper-slide::after{
                background: '.$swiper_background_active.';
                content: "";
                width: 0;
                height: 0;
                transition: all 1s;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                margin: auto;
            }
            '.$addonId.' .swiper-slide.active::after,
            '.$addonId.' .swiper-slide:hover::after{
                background: '.$swiper_background_active.';
                content: "";
                width: 100%;
                height: 100%;
            }
            
            '.$addonId.' .z-nav1-i1{
                position: relative;
                z-index: 1;
                height: '.$swiper_img_height.'px;
            }
            '.$addonId.' .swiper-slide .img2,
            '.$addonId.' .swiper-slide .img1{
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                margin: auto;
                height: 100%;
                transition: all 1s;
            }
            '.$addonId.' .swiper-slide .img2{
                display:none;
            }
            '.$addonId.' .swiper-slide .img1{
                display:block;
            }
            '.$addonId.' .swiper-slide.active .img2,
            '.$addonId.' .swiper-slide:hover .img2{
                display:block;
            }
            '.$addonId.' .swiper-slide.active .img1{
                display:none;
            }
            '.$addonId.' .z-nav1-p1{
                width: 100%;
                font-size: '.$swiper_title_font.'px;
                line-height: 16px;
                color: '.$swiper_title_color.';
                text-align: center;
                left: 0;
                transition: 1s;
                margin-top: '.$swiper_title_margin_top.'px;
                position: relative;
                z-index: 1;
            }
            '.$addonId.' .swiper-slide.active .z-nav1-p1,
            '.$addonId.' .swiper-slide:hover .z-nav1-p1{
                color: '.$swiper_title_color_active.';
            }
            '.$addonId.' .swiper-button-next,
            '.$addonId.' .swiper-button-prev{
                height: 100%;
                width: '.$arrow_width.'px;
                background: '.$arrow_background.';
                background-image: none;
                outline: none;
                margin-top: 0;
                top: 0;
                border: 1px solid '.$arrow_border_color.';
            }
            '.$addonId.' .swiper-button-next{
                right: 0;
            }
            '.$addonId.' .swiper-button-prev{
                left: 0;
            }
            '.$addonId.' .swiper-button-next::after,
            '.$addonId.' .swiper-button-prev::after{
                font-size: '.$arrow_font_size.'px;
                font-weight: bold;
                transform: scaleX(.7);
                color: '.$arrow_color.';
                transition: all .5s;
            }
            '.$addonId.' .swiper-button-next:hover,
            '.$addonId.' .swiper-button-prev:hover{
                background: '.$arrow_hover_background.';
                border-color: '.$arrow_hover_border_color.';
            }
            '.$addonId.' .swiper-button-next:hover::after,
            '.$addonId.' .swiper-button-prev:hover::after{
                color: '.$arrow_hover_color.';
            }
            '.$addonId.' #icon_holder::after{
                display: none;
            }
            @media (min-width: 767px) and (max-width: 991px){
                '.$addonId.'{
                    width: '.$carousel_width_sm.'%;
                    height: '.$carousel_height_sm.'px;
                    padding: 0 '.$arrow_width_sm.'px;
                }
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    width: '.$arrow_width_sm.'px;
                }
                '.$addonId.' .swiper-slide{
                    padding: '.$swiper_padding_sm.';
                }
                '.$addonId.' .z-nav1-i1{
                    height: '.$swiper_img_height_sm.'px;
                }
                '.$addonId.' .z-nav1-p1{
                    font-size: '.$swiper_title_font_sm.'px;
                    margin-top: '.$swiper_title_margin_top_sm.'px;
                }
                '.$id.'{
                    height: '.$content_height_sm.'px;
                }
                '.$id.' .top{
                    margin-top: '.$content_company_top_sm.'px;
                    margin-bottom: '.$content_company_bottom_sm.'px;
                }
                '.$id.' .logo{
                    width: '.$content_img_width_sm.'px;
                }
                '.$id.' .title{
                    font-size: '.$content_company_font_sm.'px;
                }
                '.$id.' .url{
                    font-size: '.$content_url_font_sm.'px;
                }
                '.$id.' .bottom{
                    font-size: '.$content_introduce_font_sm.'px;
                    width: '.$content_introduce_width_sm.'%;
                }
            }
            @media (max-width: 767px){
                '.$addonId.'{
                    width: '.$carousel_width_xs.'%;
                    height: '.$carousel_height_xs.'px;
                }
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    width: '.$arrow_width_xs.'px;
                    padding: 0 '.$arrow_width_xs.'px;
                }
                '.$addonId.' .swiper-slide{
                    padding: '.$swiper_padding_xs.';
                }
                '.$addonId.' .z-nav1-i1{
                    height: '.$swiper_img_height_xs.'px;
                }
                '.$addonId.' .z-nav1-p1{
                    font-size: '.$swiper_title_font_xs.'px;
                    margin-top: '.$swiper_title_margin_top_xs.'px;
                }
                '.$id.'{
                    height: '.$content_height_xs.'px;
                }
                '.$id.' .top{
                    margin-top: '.$content_company_top_xs.'px;
                    margin-bottom: '.$content_company_bottom_xs.'px;
                }   
                '.$id.' .logo{
                    width: '.$content_img_width_xs.'px;
                }
                '.$id.' .title{
                    font-size: '.$content_company_font_xs.'px;
                }
                '.$id.' .url{
                    font-size: '.$content_url_font_xs.'px;
                }
                '.$id.' .bottom{
                    font-size: '.$content_introduce_font_xs.'px;
                    width: '.$content_introduce_width_xs.'%;
                }
            }';

        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }
}
