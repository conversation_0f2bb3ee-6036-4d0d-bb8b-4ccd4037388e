<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'prototype_display',
		'title' => JText::_('样机展示'),
		'desc' => JText::_('样机展示'),
		'category' => '其他',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'image_item' => array(
	                'title' => JText::_('图片列表'),
	                'std' => array(
		                array(
			                'pc_image' => 'http://m.longcaiidc.top/img/it/it1.jpg',
			                'phone_image' => 'http://m.longcaiidc.top/img/it/it11.jpg',
		                ),
		                array(
			                'pc_image' => 'http://m.longcaiidc.top/img/it/it1.jpg',
							'phone_image' => 'http://m.longcaiidc.top/img/it/it11.jpg',
		                ),
		                array(
			                'pc_image' => 'http://m.longcaiidc.top/img/it/it1.jpg',
							'phone_image' => 'http://m.longcaiidc.top/img/it/it11.jpg',
		                ),
						array(
			                'pc_image' => 'http://m.longcaiidc.top/img/it/it1.jpg',
							'phone_image' => 'http://m.longcaiidc.top/img/it/it11.jpg',
		                ),
	                ),
	                'attr' => array(
		                'pc_image' => array(
			                'type' => 'media',
			                'title' => 'PC端图片',
			                'desc' => 'PC端图片',
			                'std' => 'http://m.longcaiidc.top/img/it/it1.jpg',
		                ),
						'phone_image' => array(
			                'type' => 'media',
			                'title' => '手机端图片',
			                'desc' => '手机端图片',
			                'std' => 'http://m.longcaiidc.top/img/it/it11.jpg',
		                ),
	                ),
                ),
				'item_setting'=>array(
					'type'=>'buttons',
					'title'=>'项目设置',
					'std'=>'image',
					'values'=>array(
						array(
							'label'=>'图片',
							'value'=>'image'
						),
						array(
							'label'=>'列表',
							'value'=>'list'
						),
					),
					'tabs'=>true
				),
				'image_setting'=>array(
					'type'=>'buttons',
					'title'=>'图片设置',
					'std'=>'pc',
					'values'=>array(
						array(
							'label'=>'PC端',
							'value'=>'pc'
						),
						array(
							'label'=>'移动端',
							'value'=>'mobile'
						),
					),
					'tabs'=>true,
					'depends'=>array(
						array(
							'item_setting','=','image'
						)
					)
				),
                'image_width'=>array(
                    'type'=>'slider',
                    'title'=>'图片宽度（%）',
                    'std'=>'45',
                    'max'=>'100',
                    'depends'=>array(
                        array(
                            'item_setting','=','image'
                        ),
                        array(
                            'image_setting','=','pc'
                        ),
                    )
                ),
				'pc_image_top'=> array(
					'type'=>'slider',
					'title'=>'图片上边距（%）',
					'std'=>4.2,
					'max'=>100,
					'depends'=>array(
						array(
							'image_setting','=','pc'
						),
						array(
							'item_setting','=','image'
						)
					)
				),
				'pc_image_left'=> array(
					'type'=>'slider',
					'title'=>'图片左边距（%）',
					'std'=>11.7,
					'max'=>100,
					'depends'=>array(
						array(
							'image_setting','=','pc'
						),
						array(
							'item_setting','=','image'
						)
					)
				),
				'mobile_image_top'=> array(
					'type'=>'slider',
					'title'=>'图片上边距（%）',
					'std'=>5.8,
					'max'=>100,
					'depends'=>array(
						array(
							'image_setting','=','mobile'
						),
						array(
							'item_setting','=','image'
						)
					)
				),
				'mobile_image_left'=> array(
					'type'=>'slider',
					'title'=>'图片左边距（%）',
					'std'=>6,
					'max'=>100,
					'depends'=>array(
						array(
							'image_setting','=','mobile'
						),
						array(
							'item_setting','=','image'
						)
					)
				),

                'list_width'=>array(
                    'type'=>'slider',
                    'title'=>'列表宽度（%）',
                    'std'=>'53',
                    'max'=>'100',
                    'depends'=>array(
                        array(
                            'item_setting','=','image'
                        ),
                        array(
                            'image_setting','=','pc'
                        ),
                    )
                ),
				'price' => array(
					'type' => 'number',
					'title' => JText::_('建站套餐/年'),
					'std' => 2400,
					'depends'=>array(
						array(
							'item_setting','=','list'
						)
					)
				),
				'price_color'=>array(
					'type'=>'color',
					'title'=>'套餐价格颜色',
					'std'=>'#ba1b16',
					'depends'=>array(
						array(
							'item_setting','=','list'
						)
					)
				),
				'price_font_size'=>array(
					'type'=>'slider',
					'title'=>'价格字号',
					'std'=>32,
					'max'=>50,
					'depends'=>array(
						array(
							'item_setting','=','list'
						)
					)
				),
				'button_setting'=>array(
					'type'=>'buttons',
					'title'=>'咨询按钮设置',
					'std'=>'normal',
					'values'=>array(
						array(
							'label'=>'正常状态',
							'value'=>'normal'
						),
						array(
							'label'=>'鼠标移入',
							'value'=>'hover'
						),
					),
					'tabs'=>true,
					'depends'=>array(
						array(
							'item_setting','=','list'
						)
					)
				),
				'button_link'=>array(
					'type' => 'media',
					'title' => '咨询链接',
					'desc' => '咨询链接',
					'placeholder' => 'http://',
					'hide_preview' => true,
					'std' => 'https://p.qiao.baidu.com/cps3/chatIndex?siteToken=fbaa9139afb66df3f8304f332d6d1a1a&speedLogId=159558041782688ab_1595580417826_47379&eid=30988229&reqParam={"from"%3A0%2C"sid"%3A"-100"%2C"tid"%3A"-1"%2C"ttype"%3A1%2C"siteId"%3A"15641746"%2C"userId"%3A"30988229"%2C"pageId"%3A0}',
					'depends'=>array(
						array(
							'item_setting','=','list'
						),
						array(
							'button_setting','=','normal'
						),
					)
				),
				'button_background'=>array(
					'type'=>'color',
					'title'=>'咨询按钮背景色',
					'std'=>'#ba1b16',
					'depends'=>array(
						array(
							'item_setting','=','list'
						),
						array(
							'button_setting','=','normal'
						),
					)
				),
				'button_color'=>array(
					'type'=>'color',
					'title'=>'按钮字体颜色',
					'std' =>'#fff',
					'depends'=>array(
						array(
							'item_setting','=','list'
						),
						array(
							'button_setting','=','normal'
						),
					)
				),
				'button_hover_background'=>array(
					'type'=>'color',
					'title'=>'咨询按钮背景色',
					'std'=>'#ba1b168f',
					'depends'=>array(
						array(
							'item_setting','=','list'
						),
						array(
							'button_setting','=','hover'
						),
					)
				),
				'button_hover_color'=>array(
					'type'=>'color',
					'title'=>'按钮字体颜色',
					'std' =>'#fff',
					'depends'=>array(
						array(
							'item_setting','=','list'
						),
						array(
							'button_setting','=','hover'
						),
					)
				)
			)
		),
	)
);
