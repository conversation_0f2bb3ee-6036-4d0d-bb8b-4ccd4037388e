<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'bounce_icon',
        'title' => JText::_('跳动图标'),
        'category' => '图标',
        'attr' => array(

            'jw_tab_item'         => array(
                'title' => JText::_('跳动小图标'),
                'attr'  => array(
                    'title'         => array(
                        'type'  => 'text',
                        'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE'),
                        'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE_DESC'),
                        'std'   => '点我'
                    ),
                    'image'         => array(
                        'type'    => 'media',
                        'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE'),
                        'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE_DESC'),
                        'std'     => 'https://oss.lcweb01.cn/joomla/20221019/84533a0b852a5fe9fc6c3c94403976fc.png',

                    ),
                    'wx_imagew' => array(
                        'type' => 'slider',
                        'title' =>  '小图标宽度',
                        'std' => 60,
                        'max' => 120,
                    ),
                    'wx_imageh' => array(
                        'type' => 'slider',
                        'title' =>  '小图标高度',
                        'std' => 60,
                        'max' => 120,
                    ),

                ),

            ),
            'bg_color' => array(
                'type' => 'color',
                'title' => JText::_('小图标背景颜色'),
                'std' => '#25a6c3',
            ),
            'wx_image_code2_w' => array(
                'type' => 'slider',
                'title' =>  '小图标背景宽度',
                'std' => 60,
                'max' => 120,
            ),
            'wx_image_code2_h' => array(
                'type' => 'slider',
                'title' =>  '小图标背景高度',
                'std' => 60,
                'max' => 120,
            ),

            'wx_image_code2_y' => array(
                'type' => 'slider',
                'title' =>  '跳动小图标奇数垂直位置',
                'std' => 40,
                'max' => 820,
            ),
            'wx_image_code2_y_j' => array(
                'type' => 'slider',
                'title' =>  '小图标偶数垂直位置',
                'std' => 0,
                'max' => 820,
            ),
            'wx_image_code2_m' => array(
                'type' => 'slider',
                'title' =>  '小图标左右间距',
                'std' => 30,
                'max' => 820,
            ),
            'jitter' => array(
                'type' => 'slider',
                'title' =>  '垂直跳动幅度',
                'std' => 15,
                'max' => 820,
            ),
        ),
    )
);