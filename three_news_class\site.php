<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonThree_news_class extends JwpagefactoryAddons
{

    public static $this_obj;

    public function __construct($addon)
    {
        parent::__construct($addon);
        self::$this_obj = $this;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id    = $_GET['site_id'] ?? 0;
        $layout_id  = $_GET['layout_id'] ?? 0;
        $page       = $_GET['page'] ?? 1;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $settings             = $this->addon->settings;
        $class_type_parent    = (isset($settings->class_type_parent) && $settings->class_type_parent) ? $settings->class_type_parent : 'all';
        $class_type_start     = (isset($settings->class_type_start) && $settings->class_type_start) ? $settings->class_type_start : '1';
        $class_type_num       = (isset($settings->class_type_num) && $settings->class_type_num) ? $settings->class_type_num : '10';
        $class_type           = (isset($settings->class_type) && $settings->class_type) ? $settings->class_type : 'com_goods';
        $limit                = (isset($settings->limit) && $settings->limit) ? $settings->limit : 10;
        $ordering             = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $catid                = (isset($settings->catid) && $settings->catid) ? $settings->catid : '';
        $include_subcat       = (isset($settings->include_subcat) && $settings->include_subcat) ? $settings->include_subcat : 1;
        $post_type            = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $tagids               = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $detail_page_id       = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
        $news_catid           = (isset($settings->news_catid) && $settings->news_catid) ? $settings->news_catid : '';
        $class_top_cn_title   = (isset($settings->class_top_cn_title) && $settings->class_top_cn_title) ? $settings->class_top_cn_title : '';
        $class_top_us_title   = (isset($settings->class_top_us_title) && $settings->class_top_us_title) ? $settings->class_top_us_title : '';
        $news_top_cn_title    = (isset($settings->news_top_cn_title) && $settings->news_top_cn_title) ? $settings->news_top_cn_title : '';
        $news_top_us_title    = (isset($settings->news_top_us_title) && $settings->news_top_us_title) ? $settings->news_top_us_title : '';
        $show_contact         = (isset($settings->show_contact) && $settings->show_contact) ? $settings->show_contact : 0;
        $contact_top_cn_title = (isset($settings->contact_top_cn_title) && $settings->contact_top_cn_title) ? $settings->contact_top_cn_title : '';
        $contact_top_us_title = (isset($settings->contact_top_us_title) && $settings->contact_top_us_title) ? $settings->contact_top_us_title : '';
        $contact_name         = (isset($settings->contact_name) && $settings->contact_name) ? $settings->contact_name : '';
        $contact_phone        = (isset($settings->contact_phone) && $settings->contact_phone) ? $settings->contact_phone : '';
        $contact_mail         = (isset($settings->contact_mail) && $settings->contact_mail) ? $settings->contact_mail : '';
        $contact_address      = (isset($settings->contact_address) && $settings->contact_address) ? $settings->contact_address : '';

        $class_top_bg                = (isset($settings->class_top_bg) && $settings->class_top_bg) ? $settings->class_top_bg : '#73a91c';
        $class_top_father_font_color = (isset($settings->class_top_father_font_color) && $settings->class_top_father_font_color) ? $settings->class_top_father_font_color : '#ffffff';
        $class_top_father_font_size  = (isset($settings->class_top_father_font_size) && $settings->class_top_father_font_size) ? $settings->class_top_father_font_size : 20;
        $class_top_child_font_color  = (isset($settings->class_top_child_font_color) && $settings->class_top_child_font_color) ? $settings->class_top_child_font_color : '#fff';
        $class_top_child_font_size   = (isset($settings->class_top_child_font_size) && $settings->class_top_child_font_size) ? $settings->class_top_child_font_size : 16;
        $class_top_font_hover_color  = (isset($settings->class_top_font_hover_color) && $settings->class_top_font_hover_color) ? $settings->class_top_font_hover_color : '#73a91c';
        $news_top_font_size        = (isset($settings->news_top_font_size) && $settings->news_top_font_size) ? $settings->news_top_font_size : 14;
        $news_top_font_hover_color = (isset($settings->news_top_font_hover_color) && $settings->news_top_font_hover_color) ? $settings->news_top_font_hover_color : '#73a91c';
        $news_top_dot_color        = (isset($settings->news_top_dot_color) && $settings->news_top_dot_color) ? $settings->news_top_dot_color : '#484040';
        $contact_font_size          = (isset($settings->contact_font_size) && $settings->contact_font_size) ? $settings->contact_font_size : 16;
        $contact_font_color         = (isset($settings->contact_font_color) && $settings->contact_font_color) ? $settings->contact_font_color : '#000000';
        $contact_content_font_size  = (isset($settings->contact_content_font_size) && $settings->contact_content_font_size) ? $settings->contact_content_font_size : 14;
        $contact_content_font_color = (isset($settings->contact_content_font_color) && $settings->contact_content_font_color) ? $settings->contact_content_font_color : '#2f2f2f';

        $output .= $addon_id . '<style>';
        $output .= $addon_id . ' ul {padding: 0;margin: 0;}';
        $output .= $addon_id . ' ul li {list-style: none;}';
        $output .= $addon_id . '.listhide {display: none;}';
        $output .= $addon_id . ' a {color: #337ab7;text-decoration: none;}';
        $output .= $addon_id . ' h1, h2, h3, h4, h5 {margin: 0;font-weight: bold;}';
        $output .= $addon_id . ' a:focus,a:hover {color: #23527c;text-decoration: none;}';
        $output .= $addon_id . ' a:focus {outline: none;outline-offset: -2px;}';
        $output .= $addon_id . ' .page-wrap {width:100%;background: #ffffff;padding-top: 20px;}';
        $output .= $addon_id . ' .page-wrap .container{ width:100%;}';
        $output .= $addon_id . ' @media (min-width: 990px) {.page-wrap-left {float:left;width: 260px;margin-right: 5.41666667%}}';
        $output .= $addon_id . ' .xypg-left-box {margin-bottom: 20px;}';
        $output .= $addon_id . ' .xypg-left-title {position: relative;text-align: center;padding: .7em 0;font-size: 20px;line-height: 1.5;background: ' . $class_top_bg . ';}';
        $output .= $addon_id . ' .xypg-left-title h3 {font-size: ' . $class_top_father_font_size . 'px;color:' . $class_top_father_font_color . ' ;line-height: 30px;font-weight: bold;position: relative;z-index: 9;}';
        $output .= $addon_id . ' .xypg-left-title i {position: absolute;left: -5px;top: -9px;font-style: normal;font-size: 67px;font-weight: bold;opacity: .1;color: #fff;line-height: 1;z-index: 1;text-transform: uppercase;}';
        $output .= $addon_id . ' .xypg-left-title span {font-weight: normal;font-size: ' . $class_top_child_font_size . 'px;color: ' . $class_top_child_font_color . ';opacity: .6;margin-left: 10px;text-transform: capitalize;color: #fff;}';
        $output .= $addon_id . ' .xypg-left-title span:after {margin-left: .5em}';
        $output .= $addon_id . ' .xypg-left-title span:before {margin-right: .5em}';
        $output .= $addon_id . ' .xypg-left-title span:after,.xypg-left-title span:before {content: "";display: inline-block;vertical-align: middle;width: 1.5em;height: 1px;background: #fff;}';
        $output .= $addon_id . ' .xypg-left-con {border: 1px solid #d2d2d2;border-top: none;}';
        $output .= $addon_id . ' .latest-news {padding: 10px 0;}';
        $output .= $addon_id . ' .latest-news li {padding: .5em 1em;}';
        $output .= $addon_id . ' .latest-news li a {display: block;line-height: 1.2;color: #5a5a5a;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;font-size:' . $news_top_font_size . 'px;}';
        $output .= $addon_id . ' .latest-news li a:before {content: "";display: inline-block;width: 14px;height: 14px;background: ' . $news_top_dot_color . ' url(../images/xypg-left-jt.png) -1px -16px no-repeat;border-radius: 50%;vertical-align: middle;margin-right: 5px;margin-bottom: 4px;}';
        $output .= $addon_id . ' .latest-news li a:hover {color: ' . $news_top_font_hover_color . ';}';
        $output .= $addon_id . ' .latest-news li a:hover:before {    background: ' . $news_top_font_hover_color .' url(../images/xypg-left-jt.png) -1px -16px no-repeat;color: ' . $news_top_font_hover_color .';}';
        $output .= $addon_id . ' .hot-keys {padding: 20px 7px 8px 7px;}';
        $output .= $addon_id . ' .hot-keys li {width: calc(50% - 14px);float: left;margin: 0 7px 14px 7px;}';
        $output .= $addon_id . ' .hot-keys li a {display: block;text-align: center;font-size: 14px;color: #5a5a5a; line-height: 2.857;border: 1px dashed #f1f1f1;}';
        $output .= $addon_id . ' .hot-keys li a:hover {color: #008ADA;border-color: #008ADA;}';
        $output .= $addon_id . ' .page-wrap-contact {padding: 10px 0;}';
        $output .= $addon_id . ' .page-wrap-contact h4 {font-size: ' . $contact_font_size . 'px;color: ' . $contact_font_color . ';text-align:center;}';
        $output .= $addon_id . ' .page-wrap-contact p {position: relative;margin-bottom: 0;padding: .8em 1.4em;color: ' . $contact_content_font_color . ';font-size: ' . $contact_content_font_size . 'px;}';
        $output .= $addon_id . ' .page-message-img img {border: 1px solid #d2d2d2;}';
        $output .= $addon_id . ' .page-wrap-right {overflow: hidden;}';
        $output .= $addon_id . ' .page-mob-tool {position: fixed;right: 15px;bottom: 100px;z-index: 9999;}';
        $output .= $addon_id . ' .page-mob-tool li {width: 40px;height: 40px;line-height: 40px;text-align: center;margin-bottom: 1px;cursor: pointer;position: relative;}';
        $output .= $addon_id . ' .page-mob-tool li:before {content: "";position: absolute;left: 0;right: 0;bottom: 0;top: 0;background: #292e39;opacity: .7;}';
        $output .= $addon_id . ' .page-mob-tool li i {display: block;width: 100%;height: 100%;}';
        $output .= $addon_id . ' .page-mob-tool li .icon-dots-horizontal:after {background-position: -30px -390px;}';
        $output .= $addon_id . ' .page-mob-tool li .icon-top:after {background-position: -30px -60px;}';
        $output .= $addon_id . ' @media (min-width: 992px) {.page-mob-tool {display: none;}}';
        $output .= $addon_id . ' .xymob-left-close-btn {display: none;}';
        $output .= $addon_id . ' /* 内页左侧导航栏样式 */';
        $output .= $addon_id . ' .xypg-left-nav {margin: 0 -1px;}';
        $output .= $addon_id . ' .xypg-left-nav > li {position: relative;border-bottom: 1px solid #eeeeee;}';
        $output .= $addon_id . ' .xypg-left-nav > li:last-child {border-bottom: none;}';
        $output .= $addon_id . ' .xypg-left-nav > li.item-active a{color:' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav > li > a {display: block;font-size: 16px;line-height: 22px;color: #2e2e2e;padding: 15px 18px;}';
        $output .= $addon_id . ' .xypg-left-nav > li .first-nav-btn {position: absolute;z-index: 99;width: 30px;height: 30px;text-align: center;top: 10px;right: 20px;cursor: pointer;background: url(../images/icon_spirit.png) 0 -240px no-repeat;}';
        $output .= $addon_id . ' .xypg-left-nav > li .first-nav-btn.clicked {background-position: 0 -270px;}';
        $output .= $addon_id . ' .xypg-left-nav > li:hover > a, .xypg-left-nav > li.clicked > a {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav > li:hover .first-nav-btn, .xypg-left-nav > li.clicked .first-nav-btn {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-subnav {display: none;padding: 0 10px;}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-subnav > li {border-top: 1px dashed #eeeeee;}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-subnav > li > a {padding: 8px;display: block;line-height: 20px;color: #818181;font-size: 16px;}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-subnav > li > a:hover {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-subnav > li.clicked > a {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-threenav > li > a {padding: 5px 10px;display: block;font-size: 14px;line-height: 24px;color: #999999;}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-threenav > li > a:before {content: "\2014";}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-threenav > li > a:hover {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' .xypg-left-nav .xypg-left-threenav > li.clicked > a {color: ' . $class_top_font_hover_color . ';}';
        $output .= $addon_id . ' @media (max-width: 990px) {.xypg-left-nav .xypg-left-threenav {display: block;}}';
        $output .= ' /* 内页新闻列表 */';
        $output .= $addon_id . ' .xypg-news-list li {border: 1px solid #e1e1e1;padding: 30px;background: #fff;margin-bottom: 35px;-webkit-transition: all .5s;transition: all .5s;}';
        $output .= $addon_id . ' .xypg-news-list li .tit {border-bottom: 1px solid #cbcbcb;margin-bottom: 15px;position: relative;}';
        $output .= $addon_id . ' .xypg-news-list li .tit h3 {padding-bottom: 15px;font-size: 18px;color: #565656;font-weight: normal;padding-right: 25%;margin: 0;}';
        $output .= $addon_id . ' .xypg-news-list li .tit .date {position: absolute;right: 0;top: 0;font-size: 14px;color: #666666;}';
        $output .= $addon_id . ' .xypg-news-list li .info {font-size: 14px;line-height: 2;color: #999999;}';
        $output .= $addon_id . ' .xypg-news-list li .more {margin-top: 15px;}';
        $output .= $addon_id . ' .xypg-news-list li .more span {display: inline-block;width: 130px;line-height: 33px;text-align: center;color: #000;font-size: 14px;color: #666666;border: 1px solid #e1e1e1;-webkit-transition: all .5s ease;transition: all .5s ease;}';
        $output .= $addon_id . ' .xypg-news-list li:hover {-webkit-box-shadow: 0px 2px 13px 1px rgba(8, 1, 3, 0.18);box-shadow: 0px 2px 13px 1px rgba(8, 1, 3, 0.18);}';
        $output .= $addon_id . ' .xypg-news-list li:hover .tit h3 {color: #292e39;}';
        $output .= $addon_id . ' .xypg-news-list li:hover .more span {color: #fff;background-color: #292e39;}';
        $output .= $addon_id . ' @media (max-width: 990px) {.xypg-news-list li .tit {border-bottom: none;}.xypg-news-list li .tit h3 {padding-right: 0;padding-bottom: 10px;}.xypg-news-list li .tit .date {position: static;border-bottom: 1px solid #cbcbcb;padding-bottom: 5px;color: #999;}}';
        $output .= ' /* 分页基本样式 */';
        $output .= $addon_id . ' .xypg-pagination {margin-top: 20px;text-align: center;font-size: 0;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-left,.xypg-pagination .pagin-mid,.xypg-pagination .pagin-right,.xypg-pagination .pagin-select {display: inline-block;vertical-align: middle;}';
        $output .= $addon_id . '  .xypg-pagination a,.xypg-pagination #current {display: inline-block;font-size: 14px;color: #333333;font-family: "Microsoft YaHei",Arial;line-height: 26px;border: 1px solid #cbcbcb; margin: 0 5px;padding: 0 8px;}';
        $output .= $addon_id . ' .xypg-pagination #current,.xypg-pagination a:hover {color: #fff;border-color: #292e39;background: #292e39;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-mid {font-size: 14px;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-mid .mob-pagin-mid {display: none;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-select {font-size: 14px;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-select select {height: 28px;padding: 0 8px;margin: -1px 5px 0 5px;}';
        $output .= $addon_id . ' @media (max-width: 768px) {.xypg-pagination .pagin-mid .pc-pagin-mid {display: none;}.xypg-pagination .pagin-mid .mob-pagin-mid {display: block;}.xypg-pagination .pagin-mid .mob-pagin-mid span {display: inline-block;padding: 0 8px;line-height: 28px;font-size: 14px;}.xypg-pagination .pagin-select {display: none;}}';
        $output .= $addon_id . ' /* 分页基本样式 */';
        $output .= $addon_id . ' .xypg-pagination {margin-top: 20px;text-align: center;font-size: 0;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-left,.xypg-pagination .pagin-mid,.xypg-pagination .pagin-right,.xypg-pagination .pagin-select {display: inline-block;vertical-align: middle;}';
        $output .= $addon_id . ' .xypg-pagination a,.xypg-pagination #current {display: inline-block;font-size: 14px;color: #333333;font-family: "Microsoft YaHei",Arial;line-height: 26px;border: 1px solid #cbcbcb;margin: 0 5px;padding: 0 8px;}';
        $output .= $addon_id . ' .xypg-pagination #current,.xypg-pagination a:hover {color: #fff;border-color: #292e39;background: #292e39;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-mid {font-size: 14px;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-mid .mob-pagin-mid {display: none;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-select {font-size: 14px;}';
        $output .= $addon_id . ' .xypg-pagination .pagin-select select {height: 28px;padding: 0 8px;margin: -1px 5px 0 5px;}';
        $output .= $addon_id . ' @media (max-width: 768px) {.xypg-pagination .pagin-mid .pc-pagin-mid {display: none;}.xypg-pagination .pagin-mid .mob-pagin-mid {display: block;}.xypg-pagination .pagin-mid .mob-pagin-mid span {display: inline-block;padding: 0 8px;line-height: 28px;font-size: 14px;}.xypg-pagination .pagin-select {display: none;}}';
        $output .= ' /*内页详情页面公用样式*/';
        $output .= $addon_id . ' .xypg-detail-title {font-size: 20px;color: #363636;font-weight: bold;margin-bottom: 10px;line-height: 1.7;text-align: center;}';
        $output .= $addon_id . ' .xypg-detail-info-bar {width: 100%;height: 35px;font-size: 14px;font-family: Arial;color: #9e9e9e;line-height: 35px;border-bottom: 1px dashed #cfcfcf;margin-bottom: 20px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-time {float: left;margin-right: 20px;position: relative;padding-left: 25px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-time i {position: absolute;width: 20px;height: 20px;top: calc(50% - 10px);left: 0;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-time i:after {opacity: .5;background-position: 0 -360px;}';
        $output .= $addon_id . ' .icon-shijian:before,.icon-chakan:before{display:none;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-numbers {float: right;margin-left: 20px; position: relative;padding-right: 25px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-numbers i {position: absolute;width: 20px;height: 20px;top: calc(50% - 10px);left: 0;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .detail-info-numbers i:after {opacity: .5;background-position: 0 -330px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down {float: right;position: relative;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-title {cursor: pointer;position: relative;padding-left: 25px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-title i {position: absolute;width: 20px;height: 20px;top: calc(50% - 10px);left: 0;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-title i:after {background-position: 0 -630px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-title:hover {color: #292e39;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list {position: absolute;z-index: 9999;width: 250%;right: 0;background: #fff;display: none;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li { padding: 10px 0;border-top: 1px dashed #cfcfcf;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li .icon {float: left;font-size: 40px;line-height: 40px;margin-right: 5px;height: 40px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li .list-con {overflow: hidden;line-height: 1;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li .list-con p {font-size: 12px;margin-bottom: 0;line-height: 20px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li .list-con a {font-size: 12px;}';
        $output .= $addon_id . ' .xypg-detail-info-bar .xypg-file-down .file-down-list li .list-con a:hover {color: #292e39;}';
        $output .= $addon_id . ' .xypg-detail-con-title {margin: 20px 0 10px 0;}';
        $output .= $addon_id . ' .xypg-detail-con-title span {display: inline-block;font-size: 18px;color: #292e39;}';
        $output .= $addon_id . ' .xypg-detail-con {font-size: 14px;color: #333;line-height: 1.7;}';
        $output .= $addon_id . ' .xypg-detail-con img,.xypg-detail-con p img {width: auto !important;max-width: 100%;height: auto !important;}';
        $output .= $addon_id . '</style>';

        $output .= '<div class="page-wrap">';
        $output .= '<div class="container">';
        $output .= '<div class="page-wrap-left xymob-menu-click">';
        $output .= '<div class="xymob-left-close-btn"><i class="icon-font icon-close"></i></div>';
        $output .= '<div class="xypg-left">';
        $output .= '<div class="xypg-left-box xypg-left-menu">';
        $output .= '<div class="xypg-left-title">';
        $output .= '<h3> ' . $class_top_cn_title . '<span>' . $class_top_us_title . '</span> </h3>';
        $output .= '</div>';
        $output .= '<div class="xypg-left-con">';
        $output .= '<ul class="xypg-left-nav">';
        $output .= '<div class="first-nav-btn"></div>';
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $item           = JwpagefactoryHelperCategories::getCategoriesList($class_type, $site_id, $class_type_parent, $class_type_start, $class_type_num);
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        $newsitem = JwpagefactoryHelperArticles::getArticlesListcopy(6, $ordering, $news_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        $tag_id   = array();
        foreach ($item as $key1 => $item1) {
            $output .= '<li id="li' . $item1['tag_id'] . '"  class="xypg-left-li"> <a href="#" onclick="showlists' . $this->addon->id . '(' . $item1["tag_id"] . ')">' . $item1['title'] . ' </a></li>';
            $tag_id[$key1] = $item1['tag_id'];
        }
        $output .= '</ul>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="xypg-left-box xypg-left-news">';
        $output .= '<div class="xypg-left-title">';
        $output .= '<h3>' . $news_top_cn_title . '<span>' . $news_top_us_title . '</span></h3>';
        $output .= '</div>';
        $output .= '<div class="xypg-left-con">';
        $output .= '<ul class="latest-news">';
        foreach ($newsitem as $nskey => $nsitem) {
            $output .= '<li><a href="#" title="' . $nsitem->title . '" onclick="showpages' . $this->addon->id . '(' . $nsitem->id . ')">' . $nsitem->title . '</a></li>';
        }
        $output .= '</ul>';
        $output .= '</div>';
        $output .= '</div>';
        if ($show_contact != 0) {
            $output .= '<div class="xypg-left-box xypg-left-contact" >';
            $output .= '<div class="xypg-left-title">';
            $output .= '<h3>' . $contact_top_cn_title . '<span>' . $contact_top_us_title . '</span></h3>';
            $output .= '<i></i></div>';
            $output .= '<div class="xypg-left-con">';
            $output .= '<div class="page-wrap-contact">';
            $output .= '<h4>' . $contact_name . '</h4>';
            $output .= '<p>电 话：' . $contact_phone . '</p>';
            $output .= '<p>邮 箱：' . $contact_mail . '</p>';
            $output .= '<p>地 址：' . $contact_address . '</p>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
        }
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="page-wrap-right">';
        $output .= '<div class="xypg-right-content">';

        $clist       = array();
        $clist_count = array();
        // $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        // require_once $article_helper;
        // $itemss = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        // $itemss_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $catid, $include_subcat, $company_id, $site_id, $post_type, $tagids);
        // echo $tag_id;
        // echo '<Br>';
        // echo '<pre>';
        // print_r($tag_id);
        // echo '</pre>';
        //

        //com_content com_goods com_shop
        foreach ($tag_id as $tkey => $tval) {
            if ($class_type == 'com_content') {
                // echo $tval;
                // echo '<Br>';
                $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
                require_once $article_helper;
                $itemss[$tval] = JwpagefactoryHelperArticles::getArticlesListcopy($limit, $ordering, $tval, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
                $items_count   = JwpagefactoryHelperArticles::getArticlesCount($ordering, $tval, $include_subcat, $company_id, $site_id, $post_type, $tagids);
            } elseif ($class_type == 'com_goods') {
                $goods_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
                require_once $goods_helper;
                $itemss[$tval] = JwpagefactoryHelperGoods::getGoodsListProductData($limit, $ordering, $tval, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
                $items_count   = JwpagefactoryHelperGoods::getGoodsListProductCount($limit, $ordering, $tval, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
            } else {
                // code...
            }
            // echo '<pre>';
            // print_r( $itemss[$tval]);
            // echo '</pre>';

            if ($items_count > 0) {
                $clist[$tkey]                = $itemss[$tval];
                $clist_count[$tkey]['count'] = $items_count;
                $clist_count[$tkey]['catid'] = $tval;

            } else {
                $clist[$tkey][0]['catid']    = $tval;
                $clist_count[$tkey]['count'] = $items_count;
                $clist_count[$tkey]['catid'] = $tval;
            }

        }
        $ouphtml = '';
        foreach ($clist as $key2 => $item2) {
            if (!empty($item2[0]->title)) {
                if ($key2 == 0) {
                    $ouphtml .= '<ul class="xypg-news-list" id="div' . $item2[0]->catid . '">';
                } else {
                    $ouphtml .= '<ul class="xypg-news-list listhide" id="div' . $item2[0]->catid . '">';
                }
                foreach ($item2 as $ke3 => $item3) {
                    $ouphtml .= '<li><a href="#" title="' . $item3->title . '" onclick="showpages' . $this->addon->id . '(' . $item3->id . ')">';
                    $ouphtml .= '<div class="tit">';
                    $ouphtml .= '<h3>' . $item3->title . '</h3>';
                    $ouphtml .= '<div class="date">' . date('Y-m-d', strtotime($item3->created)) . '</div>';
                    $ouphtml .= '</div>';
                    $ouphtml .= '<div class="info"> ' . mb_substr(strip_tags($item3->introtext), 0, 40, 'UTF-8') . '</div>';
                    $ouphtml .= '<div class="more"><span>了解详情 +</span></div>';
                    $ouphtml .= '</a></li>';
                }
                $ouphtml .= '</ul>';
            } else {
                $ouphtml .= '<ul class="xypg-news-list listhide" id="div' . $item2[0]['catid'] . '">';
                $ouphtml .= '<li style="text-align: center;">暂无内容</li>';
                $ouphtml .= '</ul>';
            }
        }
        $output .= $ouphtml;
        $oupchtml = '';
        foreach ($clist as $ckey2 => $citem2) {
            if (!empty($citem2[0]->title)) {
                foreach ($citem2 as $keyc3 => $itemc3) {
                    $oupchtml .= '<div class="xypg-news-detail listhide" id="page' . $itemc3->id . '">';
                    $oupchtml .= '<h1 class="xypg-detail-title"> ' . $itemc3->title . '</h1>';
                    $oupchtml .= '<div class="xypg-detail-info-bar">';
                    $oupchtml .= '<div class="detail-info-time"><i class="icon-font icon-shijian"></i>' . mb_substr(strip_tags($itemc3->introtext), 0, 10, 'UTF-8') . '</div>';
                    $oupchtml .= '<div class="detail-info-numbers"><i class="icon-font icon-chakan"></i>' . date('Y-m-d', strtotime($itemc3->created)) . '</div>';
                    $oupchtml .= '</div>';
                    $oupchtml .= '<div class="xypg-detail-con">' . $itemc3->fulltext . '</div>';
                    $oupchtml .= '</div>';
                }
            }
        }
        foreach ($newsitem as $nkey2 => $nitem2) {
            if (!empty($nitem2->title)) {
                $oupnchtml .= '<div class="xypg-news-detail listhide"  id="page' . $nitem2->id . '">';
                $oupnchtml .= '<h1 class="xypg-detail-title"> ' . $nitem2->title . '</h1>';
                $oupnchtml .= '<div class="xypg-detail-info-bar">';
                $oupnchtml .= '<div class="detail-info-time"><i class="icon-font icon-shijian"></i>' . mb_substr(strip_tags($nitem2->introtext), 0, 10, 'UTF-8') . '</div>';
                $oupnchtml .= '<div class="detail-info-numbers"><i class="icon-font icon-chakan"></i>' . date('Y-m-d', strtotime($nitem2->created)) . '</div>';
                $oupnchtml .= '</div>';
                $oupnchtml .= '<div class="xypg-detail-con">' . $nitem2->fulltext . '</div>';
                $oupnchtml .= '</div>';
            }
        }
        $output .= $oupchtml;
        $output .= $oupnchtml;
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="xypg-pagination">';
        $output .= '<div class="pagin-mid">';
        foreach ($clist_count as $cckey => $ccitem) {
            if ($ccitem['count'] != 0) {
                if ($cckey == 0) {
                    $output .= '<div class="pc-pagin-mid "  id="page' . $ccitem['catid'] . '">';
                } else {
                    $output .= '<div class="pc-pagin-mid listhide"  id="page' . $ccitem['catid'] . '">';
                }
                $itemsss_count = $ccitem['count'];
                if ($limit) {
                    $all_page = ceil($itemsss_count / $limit);
                }
                /* 编辑器省略号分页 */
                // $output .= '<div class="zxf_pagediv " id="false_page" style=" position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px; width:100%;display:flex;align-items: center;justify-content: center;opic">';
                if ($page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                    $output .= '    <a class="page-link" href="' . $url . '&page=' . ($page - 1) . '&cpcatid=' . $goods_catid . '">«</a>';
                }
                for ($i = 1; $i <= $all_page; $i++) {

                    if ($page == $i) {
                        $output .= '<a id="current" class="current">' . $i . '</a>';
                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                        $output .= '<a id="current" class="zxfPagenum" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';

                    }
                }
                // 判断是不是最后一页
                if ($page != $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                    $output .= '    <a lass="page-link" href="' . $url . '&page=' . ($page + 1) . '&cpcatid=' . $goods_catid . '" >»</a>';
                }
                $output .= '</div>';
            }
        }
        $output .= ' </div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="page-mob-tool">';
        $output .= '<ul>';
        $output .= '<li class="xymob-page-navbtn"><i class="icon-font icon-dots-horizontal"></i></li>';
        $output .= '<li class="xymob-page-backtop"><i class="icon-font icon-top"></i></li>';
        $output .= '</ul>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<script>
           function showlists' . $this->addon->id . '(id){
                    console.log(id)
                     $("' . $addon_id . ' .xypg-news-list").addClass("listhide");
                    $("' . $addon_id . ' #div"+id).removeClass("listhide");
                     $("' . $addon_id . ' .pc-pagin-mid").addClass("listhide");
                    $("' . $addon_id . ' #page"+id).removeClass("listhide");
                    $("' . $addon_id . ' .item-active").removeClass("item-active");
                    $("' . $addon_id . ' #li"+id).addClass("item-active");
                     $("' . $addon_id . ' .xypg-news-detail").addClass("listhide");
                }
            function showpages' . $this->addon->id . '(id){
                    console.log(id)
                     $("' . $addon_id . ' .xypg-news-list").addClass("listhide");
                     $("' . $addon_id . ' .xypg-pagination").addClass("listhide");
                     $("' . $addon_id . ' .xypg-news-detail").addClass("listhide");
                     $("' . $addon_id . ' #page"+id).removeClass("listhide");
            }
        </script>';
        return $output;
    }

    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

    //数组多层级 递归
    public function subTree($data, $pid = 1, $deep = 0)
    {
        //用来存放数据
        $arr = [];
        //遍历数据库中的字段
        foreach ($data as $val) {
            //判断字段pid相等时放行
            if ($pid == $val['parent_id']) {
                //不同的层级
                $val['deep'] = $deep;
                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
                $val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
                //如果遇到pid==本条id时，将其存入数组
                $arr[] = $val;
            }
        }

        //返回数组
        return $arr;
    }

    /**
     * 排序
     */
    public function Sort(array $item, string $field, string $sort)
    {
        if ($sort != 'DESC') {
            $last_names = array_column($item, $field);
            array_multisort($last_names, SORT_ASC, $item);
        } else {
            $last_names = array_column($item, $field);
            array_multisort($last_names, SORT_DESC, $item);
        }
        return $item;
    }
}
