<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 11:00:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'now_time',
        'title' => '当前日期显示',
        'desc' => '当前日期显示',
        'category' => '常用插件',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'date_style' => array(
                    'type'  => 'select',
                    'title' => '日期布局',
                    'desc' => '选择日期布局方式',
                    'values' => array(
                        'type01' => '布局01',
                    ),
                    'std' => 'type01'
                ),
                'date_title' => array(
                    'type' => 'text',
                    'title' => '日期前文字',
                    'std' => '欢迎来到资海云建站通！今天是',
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
                'date_str' => array(
                    'type' => 'text',
                    'title' => '日期分割字符',
                    'std' => '.',
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
                'date_title_after' => array(
                    'type' => 'text',
                    'title' => '日期后文字',
                    'std' => '！',
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
                'date_align' => array(
                    'type'  => 'select',
                    'title' => '文字对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'left'
                ),
                'date_color' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
                'date_fontsize' => array(
                    'type' => 'slider',
                    'title' => '文字大小',
                    'max' => 100,
                    'min' => 1,
                    'std' => 14,
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
                'date_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '文字行高',
                    'max' => 1000,
                    'min' => 1,
                    'std' => 32,
                    'depends' => array(
                        // array('date_style', '=', 'type01')
                    ),
                ),
            ),
        ),
    )
);
