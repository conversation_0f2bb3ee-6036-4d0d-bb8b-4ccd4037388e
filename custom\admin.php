<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
// no direct accees
// defined('_JEXEC') or die('Restricted access');
// $app = JFactory::getApplication();

// $input = $app->input;
// $layout_id = $input->get('layout_id', '');
// $site_id = $input->get('site_id', 0);
// $company_id = $input->get('company_id', 0);
// $config = new JConfig();
// JwAddonsConfig::addonConfig(
//     array(
//         'type' => 'repeatable',
//         'addon_name' => 'custom',
//         'title' => JText::_('自定义产品列表'),
//         'desc' => JText::_(''),
//         'category' => '其他',
//         'attr' => array(
//             'general' => array(
//                 'admin_label' => array(
//                     'type' => 'text',
//                     'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
//                     'std' => '自定义样式'
//                 ),
//                 'class' => array(
//                     'type' => 'text',
//                     'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
//                     'std' => ''
//                 ),

//                 'custor_type' => array(
//                     'type' => 'select',
//                     'title' => JText::_('类型'),
//                     'values' => array(
//                         'type1' => '产品列表',
//                     ),
//                     'std' => 'type1',
                    
//                 ),
//                 'goods_catid' => array(
//                     'type' => 'select',
//                     'title' => JText::_('选择产品分类'),
//                     'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
//                     'depends' => array(
//                         array('custor_type', '=', 'type1'),
//                     ),
//                 ),
//                 'ordering' => array(
//                     'type' => 'select',
//                     'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
//                     'values' => array(
//                         'sortdesc' => JText::_('排序id倒序'),
//                         'sortasc' => JText::_('排序id正序'),
//                         'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
//                         'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
//                         'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
//                         'featured' => JText::_('COM_JWPAGEFAC阴影水平偏移TORY_ADDON_ARTICLES_ORDERING_FEATURED'),
//                         'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
//                         'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
//                         'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
//                     ),
//                     'std' => 'sortdesc',
//                 ),
//                 'detail_page_id' => array(
//                     'type' => 'select',
//                     'title' => '详情页模版',
//                     'desc' => '显示文章详情页模版',
//                     'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
//                 ),
//                 'detail_link_style' => array(
//                     'type' => 'select',
//                     'title' => '详情页跳转方式',
//                     'values' => array(
//                         '_self' => '当前页',
//                         '_blank' => '新页面',
//                     ),
//                     'std' => '_self',
                    
//                 ),

//                 'classt' => array(
//                     'type' => 'text',
//                     'title' => JText::_('列表项class名'),
//                     'std' => ''
//                 ),
//                 'jw_form_builder_item' => array(
//                     'title' => JText::_('项目内容'),
//                     'std' => array(
//                         array(
//                             'title' => '名称',
//                             'field_name'=>'title',
//                             'class_name' => '',
//                         ),
                        
//                     ),
//                     'attr' => array(
//                         'title' => array(
//                             'type' => 'text',
//                             'title' => JText::_('标签名称'),
//                             'std' => '名称',
//                         ),
//                         'field_type' => array(
//                             'type' => 'select',
//                             'title' => JText::_('标签类型'),
//                             'values' => array(
//                                 'div' => '文本',
//                                 'img' => '图片',
//                             ),
//                             'std' => 'div',
//                         ),
//                         'field_name' => array(
//                             'type' => 'select',
//                             'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FORM_BUILDER_FIELD_NAME'),
//                             'values' => JwPageFactoryBase::getSelField($site_id),
//                             'std' => 'title',
//                         ),
//                         'class_name' => array(
//                             'type' => 'text',
//                             'title' => JText::_('class名'),
//                             'std' => '',
//                         ),
                        
//                     ),
//                 ),
//                 'limit' => array(
//                     'type' => 'number',
//                     'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
//                     'std' => '10',
                    
//                 ),
//                 'show_page' => array(
//                     'type' => 'checkbox',
//                     'title' => JText::_('显示翻页码'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
//                     'std' => '0',
//                 ),
                
//             ),
//         ),
//     )
// );
