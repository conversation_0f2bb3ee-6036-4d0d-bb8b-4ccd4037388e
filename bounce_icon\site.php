<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonBounce_icon extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $imgurl = $config->img_url;


        $output = '';
        $output .= "<style> ";

            $output .= "
    {$addon_id} img {
        display: inline-block;
    }
";

            $output .= "
    {$addon_id} .goods {
        width: 50%;
        text-align: center;
    }
";
            $output .= "
    {$addon_id} .goods img {
        animation: myfirst 2s infinite;
        animation-timing-function:ease;
    }
";
            $output .= "
    @keyframes myfirst { /*动画*/
        0% {
            transform: translate(0px, 0px);
        }
        50% {
            transform: translate(0px, -{$settings->jitter}px);
        }
        100% {
            transform: translate(0px, 0px);
        }
    }";
        $output .= "
    {$addon_id} .web_xd .web_xd_list ul{
        list-style:none;
    }";

        $output .= "
    {$addon_id} .web_xd .web_xd_list ul li{
            text-align: center;
            float:left;
            animation: myfirst 2s infinite;
            animation-timing-function:ease;
           
            margin-right: {$settings->wx_image_code2_m}px;
            position: relative;
    }";
        $output .= "
    {$addon_id} .web_xd .web_xd_list ul li h2{
            width: {$settings->wx_image_code2_w}px;
            height: {$settings->wx_image_code2_h}px;
            border-radius: 4em;
            background: {$settings->bg_color};
            display: flex;
            align-items: center;
            justify-content: center;
    }";

        $output .= "
    {$addon_id} .web_xd .web_xd_list ul li p{
           text-align: center;
    }";


    $output .= "</style> ";


            $output .= '<div class="web_xd">';
            $output .= '<div class="web_xd_list" >';
            $output .= '<ul>';
            foreach ($settings->jw_tab_item as $k => $v) {
                if(($k%2)==0){
                $output .= ' <li style="animation-delay: 0.4s;top: '.$settings->wx_image_code2_y.'px;">';
                $output .= '		<h2><img style="height:'.$v->wx_imageh.'px;width:'.$v->wx_imagew.'px;"  src="' . $v->image . '"></h2>';
                $output .= '        <p>' . $v->title . ' </p>';
                $output .= '</li>';
                }else{
                    $output .= ' <li style="top: '.$settings->wx_image_code2_y_j.'px;">';
                    $output .= '		<h2><img style="height:'.$v->wx_imageh.'px;width:'.$v->wx_imagew.'px;" src="' . $v->image . '"></h2>';
                    $output .= '        <p>' . $v->title . ' </p>';
                    $output .= '</li>';
                }
            }
            $output .= '</ul >';
            $output .= '</div>';
            $output .= '</div >';

            return $output;
        }

    public static function getTemplate(){
        $output = '
            <div style=" height:100px">此区域为跳动图标占位</div>
        ';
        return $output;

    }
    }