<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonBaroque_part03 extends JwpagefactoryAddons
{

    public function add($accordion_item) {
        $html = "";
        $html .= "<img src='" . $accordion_item->img . "' class='img' alt='' >";
            if ($accordion_item->is_show_cover == 1) {
                $html .= "<div class='cover ";
                if($accordion_item->is_cover == 1) {
                    $html .= " active";
                }
                $html .= "' style='background-color: " . $accordion_item->bg_color . "'>";

                $html .= "<div class='content-box'>";
                if($accordion_item->cover_content != ""){
                    $html .= "<img src='" . $accordion_item->cover_content . "' alt='' class='pc-img' >";
                    $html .= "<img src='" . $accordion_item->cover_content_wap . "' alt='' class='wap-img' >";
                }
                $html .= "</div>";
                $html .= "</div>";
            }
        return $html;
    }
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
        //pc 左侧图显示个数
        $section_left_num = (isset($settings->section_left_num) && $settings->section_left_num) ? $settings->section_left_num : 1;
//        $output = '<div>123</div>';


//
        $output = '<div class="section-8">';
        if($section_left_num && $section_left_num == 2) {
            $output .= '<div class="large">';
                foreach ($section_tab_item as $key => $tab) {
                    if ($key < 2) {
                        $link = '';
                        if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                            $id = base64_encode($tab->detail_page_id);
                            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                        $output .= '<a ';
                        if($link != ''){
                            $output .= 'target="_blank" href="' . $link .'" ';
                        }
                        $output .= 'class="img-box">' . $this->add($tab) . '</a>';
                    }
                }
            $output .= '</div>
            <div class="right-box">';
                foreach ($section_tab_item as $key => $tab) {
                    if ($key == 2) {
                        $link = '';
                        if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                            $id = base64_encode($tab->detail_page_id);
                            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                        $output .= '<a ';
                        if($link != ''){
                            $output .= 'target="_blank" href="' . $link .'" ';
                        }
                        $output .= 'class="img-box top-box">' . $this->add($tab) .'</a>';
                    }
                }
                $output .= '<div class="two-box">';
                    foreach ($section_tab_item as $key => $tab) {
                        if ($key  > 2 && $key < 5) {
                            $link = '';
                            if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                                $id = base64_encode($tab->detail_page_id);
                                $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                            }
                            $output .= '<a ';
                            if($link != ''){
                                $output .= 'target="_blank" href="' . $link .'" ';
                            }
                            $output .= 'class="img-box">' . $this->add($tab) .'</a>';
                        }
                    }
                $output .= '</div>
            </div>';
        } else {
            foreach ($section_tab_item as $key => $tab) {
                if ($key == 0) {
                    $link = '';
                    if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                        $id = base64_encode($tab->detail_page_id);
                        $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                    }
                    $output .= '<a ';
                    if($link != ''){
                        $output .= 'target="_blank" href="' . $link .'" ';
                    }
                    $output .= 'class="img-box large">' . $this->add($tab) .'</a>';
                }
            }
            $output .= '<div class="right-box">';
                foreach ($section_tab_item as $key => $tab) {
                    if ($key == 1) {
                        $link = '';
                        if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                            $id = base64_encode($tab->detail_page_id);
                            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                        $output .= '<a ';
                        if($link != ''){
                            $output .= 'target="_blank" href="' . $link .'" ';
                        }
                        $output .= 'class="img-box top-box">' . $this->add($tab) .'</a>';
                    }
                }
                $output .= '<div class="two-box">';
                foreach ($section_tab_item as $key => $tab) {
                    if ($key  > 1 && $key < 4) {
                        $link = '';
                        if($tab->is_link && $tab->is_link == 1 && $tab->detail_page_id){
                            $id = base64_encode($tab->detail_page_id);
                            $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                        $output .= '<a ';
                        if($link != ''){
                            $output .= 'target="_blank" href="' . $link .'" ';
                        }
                        $output .= 'class="img-box">' . $this->add($tab) .'</a>';
                    }
                }
                $output .= '</div>
            </div>';
        }


        $output .= '</div>';

        return $output;
    }

    public function scripts()
    {

    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;

        $js = 'jQuery(function($){
		    
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();

        //图片间距
        $section_img_mg = (isset($settings->section_img_mg)) ? $settings->section_img_mg : 12;
        //pc 左侧图显示个数
        $section_left_num = (isset($settings->section_left_num) && $settings->section_left_num) ? $settings->section_left_num : 1;

        //选项卡pc高度
        $section_height = (isset($settings->section_height) && $settings->section_height) ? $settings->section_height : 800;
        //pc 左侧图宽度（不填默认50%）
        $section_left_width = (isset($settings->section_left_width) && $settings->section_left_width) ? $settings->section_left_width : '';
        //pc 左侧第一张图高度
        $section_left_top_img_height = (isset($settings->section_left_top_img_height) && $settings->section_left_top_img_height) ? $settings->section_left_top_img_height : 120;
        //pc 右侧上方图高度
        $section_right_top_height = (isset($settings->section_right_top_height) && $settings->section_right_top_height) ? $settings->section_right_top_height : 400;
        //pc 右侧下方第一张图宽度
        $section_right_bottom_first_width = (isset($settings->section_right_bottom_first_width) && $settings->section_right_bottom_first_width) ? $settings->section_right_bottom_first_width : 358;

        //选项卡平板高度
        $section_height_sm = (isset($settings->section_height_sm) && $settings->section_height_sm) ? $settings->section_height_sm : 500;
        //pc 左侧图宽度（不填默认50%）
        $section_left_width_sm = (isset($settings->section_left_width_sm) && $settings->section_left_width_sm) ? $settings->section_left_width_sm : '';
        //pc 左侧第一张图高度
        $section_left_top_img_height_sm = (isset($settings->section_left_top_img_height_sm) && $settings->section_left_top_img_height_sm) ? $settings->section_left_top_img_height_sm : 120;
        //pc 右侧上方图高度
        $section_right_top_height_sm = (isset($settings->section_right_top_height_sm) && $settings->section_right_top_height_sm) ? $settings->section_right_top_height_sm : 400;
        //pc 右侧下方第一张图宽度
        $section_right_bottom_first_width_sm = (isset($settings->section_right_bottom_first_width_sm) && $settings->section_right_bottom_first_width_sm) ? $settings->section_right_bottom_first_width_sm : 358;

        //手机图高度
        $item_img_height = (isset($settings->item_img_height) && $settings->item_img_height) ? $settings->item_img_height : 358;
        //手机区域内边距
        $section_padding = (isset($settings->section_padding)) ? $settings->section_padding : 20;

//        $css = '';
        $css =
            $addonId . ' * {
				margin: 0;
			}
			' . $addonId . ' .section-8 {
				display: flex;
				width: 100%;
				overflow: hidden;
				height: ' . $section_height . 'px;
				justify-content: space-between;
			}
			' . $addonId . ' .section-8 .img-box {
				overflow: hidden;
				position: relative;
				cursor: pointer;
				display: block;
			}
			' . $addonId . ' .section-8 .img-box .img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				display: block;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-8 .img-box .cover {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				z-index: -1;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-8 .img-box:hover .cover,' . $addonId . ' .section-8 .img-box .cover.active {
			    z-index: 10;
			    opacity: 1;
			}
			' . $addonId . ' .section-8 .content-box {
			    position: absolute;
			}
			' . $addonId . ' .section-8 .content-box .pc-img {
			    display: block;
			}
			' . $addonId . ' .section-8 .content-box .wap-img {
			    display: none;
			}';
            foreach ($section_tab_item as $key => $tab) {
                if($section_left_num && $section_left_num == 2){
                    if($key == 0) {
                        $css .= $addonId . ' .section-8 .large .img-box:first-child .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                    if($key == 1) {
                        $css .= $addonId . ' .section-8 .large .img-box:last-child .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                    if($key == 2) {
                        $css .= $addonId . ' .top-box .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                    if($key > 2 && $key < 5) {
                        $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 2) . ') .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                }else {
                    if($key == 0) {
                        $css .= $addonId . ' .large .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                    if($key == 1) {
                        $css .= $addonId . ' .top-box .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                    if($key > 1 && $key < 4) {
                        $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 1) . ') .content-box {';
                        if($tab->is_show_cover == 1) {
                            if((isset($tab->content_top->md) && $tab->content_top->md) && $tab->content_top->md > 0) {
                                $css .= 'top: ' . $tab->content_top->md .'px;';
                            }
                            if((isset($tab->content_right->md) && $tab->content_right->md) && $tab->content_right->md > 0) {
                                $css .= 'right: ' . $tab->content_right->md .'px;';
                            }
                            if((isset($tab->content_bottom->md) && $tab->content_bottom->md) && $tab->content_bottom->md > 0) {
                                $css .= 'bottom: ' . $tab->content_bottom->md .'px;';
                            }
                            if((isset($tab->content_left->md) && $tab->content_left->md) && $tab->content_left->md > 0) {
                                $css .= 'left: ' . $tab->content_left->md .'px;';
                            }
                            if((isset($tab->cover_content_width->md) && $tab->cover_content_width->md) && $tab->cover_content_width->md > 0) {
                                $css .= 'width: ' . $tab->cover_content_width->md .'px;';
                            }
                            if((isset($tab->cover_content_height->md) && $tab->cover_content_height->md) && $tab->cover_content_height->md > 0) {
                                $css .= 'height: ' . $tab->cover_content_height->md .'px;';
                            }
                        }
                        $css .= '}';
                    }
                }
            }
            $css .= $addonId . ' .section-8 .img-box:hover .img {
				transform: scale(1.2);
			}
			' . $addonId . ' .section-8 .large {';
			    if($section_left_width && $section_left_width != "") {
                    $css .= 'width: ' . $section_left_width . 'px;';
			    } else {
			        $css .= 'width: calc(100% / 2);';
			    }
				$css .= 'height: ' . $section_height . 'px;
			}
			' . $addonId . ' .section-8 .large .img-box {
			    width: 100%;
			    height: 100%;
			}';
			if($section_left_num && $section_left_num == 2) {
                $css .= $addonId . ' .section-8 .large .img-box:first-child {';
			        if($section_left_top_img_height && $section_left_top_img_height != "") {
                        $css .= 'height: ' . $section_left_top_img_height . 'px;';
                    } else {
                        $css .= 'height: calc(100% / 2);';
                    }
                $css .= '}
			    ' . $addonId . ' .section-8 .large .img-box:last-child {';
			        if($section_left_top_img_height && $section_left_top_img_height != "") {
                        $css .= 'height: calc(100% - ' . $section_left_top_img_height . 'px - ' . $section_img_mg . 'px);';
                    } else {
                        $css .= 'height: calc(100% / 2 - ' . $section_img_mg . 'px);';
                    }
                    $css .= 'margin-top: ' . $section_img_mg . 'px;
			    }';
            }

            $css .= $addonId . ' .section-8 .right-box {';
			    if($section_left_width && $section_left_width != "") {
                    $css .= 'width: calc(100% - ' . $section_left_width . 'px - ' . $section_img_mg . 'px);';
			    } else {
				    $css .= 'width: calc(100% / 2 - ' . $section_img_mg . 'px);';
			    }
            $css .= '}
			' . $addonId . ' .section-8 .top-box {';
			    if($section_right_top_height && $section_right_top_height != "") {
                    $css .= 'height: ' . $section_right_top_height . 'px;';
				} else {
                    $css .= 'height: calc(100% / 2);';
                }
                $css .= 'margin-bottom: ' . $section_img_mg . 'px;
			}
			' . $addonId . ' .section-8 .right-box .two-box {
				display: flex;
				justify-content: space-between;';
				if($section_right_top_height && $section_right_top_height != "") {
                    $css .= 'height: calc(100% - ' . $section_right_top_height . 'px - ' . $section_img_mg . 'px);';
				} else {
                    $css .= 'height: calc(100% / 2 - ' . $section_img_mg . 'px);';
                }
            $css .= '}
			' . $addonId . ' .section-8 .right-box .two-box .img-box:first-child {';
			    if($section_right_bottom_first_width && $section_right_bottom_first_width != "") {
                    $css .= 'width: ' . $section_right_bottom_first_width. 'px;';
				} else {
                    $css .= 'width: calc(100% / 2);';
                }
            $css .= '}
			' . $addonId . ' .section-8 .right-box .two-box .img-box:last-child {';
			    if($section_right_bottom_first_width && $section_right_bottom_first_width != "") {
                    $css .= 'width: calc(100% - ' . $section_right_bottom_first_width. 'px - ' . $section_img_mg. 'px);';
				} else {
                    $css .= 'width: calc(100% / 2 - ' . $section_img_mg . 'px);';
                }
            $css .= '}
			@media (min-width: 768px) and (max-width: 991px) {
			    ' . $addonId . ' .section-8 {
                    height: ' . $section_height_sm . 'px;
                }
                ' . $addonId . ' .section-8 .large {';
                    if($section_left_width_sm && $section_left_width_sm != "") {
                        $css .= 'width: ' . $section_left_width_sm . 'px;';
                    } else {
                        $css .= 'width: calc(100% / 2);';
                    }
                    $css .= 'height: ' . $section_height_sm  . 'px;
                }';
                foreach ($section_tab_item as $key => $tab) {
                    if($section_left_num && $section_left_num == 2) {
                        if($key == 0) {
                            $css .= $addonId . ' .section-8 .large .img-box:first-child .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 1) {
                            $css .= $addonId . ' .section-8 .large .img-box:last-child .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 2) {
                            $css .= $addonId . ' .top-box .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key > 2 && $key < 5) {
                            $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 2) . ') .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                    }else {
                        if($key == 0) {
                            $css .= $addonId . ' .large .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 1) {
                            $css .= $addonId . ' .top-box .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key > 1 && $key < 4) {
                            $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 1) . ') .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->sm) && $tab->content_top->sm) && $tab->content_top->sm > 0) {
                                    $css .= 'top: ' . $tab->content_top->sm .'px;';
                                }
                                if((isset($tab->content_right->sm) && $tab->content_right->sm) && $tab->content_right->sm > 0) {
                                    $css .= 'right: ' . $tab->content_right->sm .'px;';
                                }
                                if((isset($tab->content_bottom->sm) && $tab->content_bottom->sm) && $tab->content_bottom->sm > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->sm .'px;';
                                }
                                if((isset($tab->content_left->sm) && $tab->content_left->sm) && $tab->content_left->sm > 0) {
                                    $css .= 'left: ' . $tab->content_left->sm .'px;';
                                }
                                if((isset($tab->cover_content_width->sm) && $tab->cover_content_width->sm) && $tab->cover_content_width->sm > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->sm .'px;';
                                }
                                if((isset($tab->cover_content_height->sm) && $tab->cover_content_height->sm) && $tab->cover_content_height->sm > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->sm .'px;';
                                }
                            }
                            $css .= '}';
                        }
                    }
                }
                if($section_left_num && $section_left_num == 2) {
                    $css .= $addonId . ' .section-8 .large .img-box:first-child {';
                        if($section_left_top_img_height_sm && $section_left_top_img_height_sm != "") {
                            $css .= 'height: ' . $section_left_top_img_height_sm . 'px;';
                        } else {
                            $css .= 'height: calc(100% / 2);';
                        }
                    $css .= '}
                    ' . $addonId . ' .section-8 .large .img-box:last-child {';
                        if($section_left_top_img_height_sm && $section_left_top_img_height_sm != "") {
                            $css .= 'height: calc(100% - ' . $section_left_top_img_height_sm . 'px - ' . $section_img_mg . 'px);';
                        } else {
                            $css .= 'height: calc(100% / 2 - ' . $section_img_mg . 'px);';
                        }
                    $css .= '}';
                }
                $css .= $addonId . ' .section-8 .right-box {';
                    if($section_left_width_sm && $section_left_width_sm != "") {
                        $css .= 'width: calc(100% - ' . $section_left_width_sm . 'px - ' . $section_img_mg . 'px);';
                    } else {
                        $css .= 'width: calc(100% / 2 - ' . $section_img_mg . 'px);';
                    }
                $css .= '}
                ' . $addonId . ' .section-8 .top-box {';
                    if($section_right_top_height_sm && $section_right_top_height_sm != "") {
                        $css .= 'height: ' . $section_right_top_height_sm . 'px;';
                    } else {
                        $css .= 'height: calc(100% / 2);';
                    }
                $css .= '}
                ' . $addonId . ' .section-8 .right-box .two-box {';
                    if($section_right_top_height_sm && $section_right_top_height_sm != "") {
                        $css .= 'height: calc(100% - ' . $section_right_top_height_sm . 'px - ' . $section_img_mg . 'px);';
                    } else {
                        $css .= 'height: calc(100% / 2 - ' . $section_img_mg . 'px);';
                    }
                $css .= '}
                ' . $addonId . ' .section-8 .right-box .two-box .img-box:first-child {';
                    if($section_right_bottom_first_width_sm && $section_right_bottom_first_width_sm != "") {
                        $css .= 'width: ' . $section_right_bottom_first_width_sm. 'px;';
                    } else {
                        $css .= 'width: calc(100% / 2);';
                    }
                $css .= '}
                ' . $addonId . ' .section-8 .right-box .two-box .img-box:last-child {';
                    if($section_right_bottom_first_width_sm && $section_right_bottom_first_width_sm != "") {
                        $css .= 'width: calc(100% - ' . $section_right_bottom_first_width_sm . 'px - ' . $section_img_mg. 'px);';
                    } else {
                        $css .= 'width: calc(100% / 2 - ' . $section_img_mg . 'px);';
                    }
                $css .= '}
			}
			@media (max-width: 767px) {
			    ' . $addonId . ' .section-8 {
			        display: block;
			        height: max-content;
			        padding: 0 ' . $section_padding . 'px;
			    }
			    ' . $addonId . ' .section-8 .large {
			        width: 100% !important;';
			        if($section_left_num && $section_left_num == 2) {
			            $css .= 'height: auto;';
			        }
			    $css .= '}
			    ' . $addonId . ' .section-8 .img-box {
			        width: 100% !important;
			        height: ' . $item_img_height . 'px !important;
			        margin-bottom: 20px;
			    }
			    ' . $addonId . ' .section-8 .right-box {
			        width: 100%;
			    }
			    ' . $addonId . ' .section-8 .right-box .two-box {
			        display: block;
			    }
			    ' . $addonId . ' .section-8 .content-box .pc-img {
                    display: none;
                }
                ' . $addonId . ' .section-8 .content-box .wap-img {
                    display: block;
                }';
                foreach ($section_tab_item as $key => $tab) {
                    if($section_left_num && $section_left_num == 2) {
                        if($key == 0) {
                            $css .= $addonId . ' .section-8 .large .img-box:first-child .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 1) {
                            $css .= $addonId . ' .section-8 .large .img-box:last-child .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 2) {
                            $css .= $addonId . ' .top-box .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key > 2 && $key < 5) {
                            $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 2) . ') .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                    } else {
                        if($key == 0) {
                            $css .= $addonId . ' .large .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key == 1) {
                            $css .= $addonId . ' .top-box .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                        if($key > 1 && $key < 4) {
                            $css .= $addonId . ' .two-box .img-box:nth-child(' . ($key - 1) . ') .content-box {';
                            if($tab->is_show_cover == 1) {
                                if((isset($tab->content_top->xs) && $tab->content_top->xs) && $tab->content_top->xs > 0) {
                                    $css .= 'top: ' . $tab->content_top->xs .'px;';
                                }
                                if((isset($tab->content_right->xs) && $tab->content_right->xs) && $tab->content_right->xs > 0) {
                                    $css .= 'right: ' . $tab->content_right->xs .'px;';
                                }
                                if((isset($tab->content_bottom->xs) && $tab->content_bottom->xs) && $tab->content_bottom->xs > 0) {
                                    $css .= 'bottom: ' . $tab->content_bottom->xs .'px;';
                                }
                                if((isset($tab->content_left->xs) && $tab->content_left->xs) && $tab->content_left->xs > 0) {
                                    $css .= 'left: ' . $tab->content_left->xs .'px;';
                                }
                                if((isset($tab->cover_content_width->xs) && $tab->cover_content_width->xs) && $tab->cover_content_width->xs > 0) {
                                    $css .= 'width: ' . $tab->cover_content_width->xs .'px;';
                                }
                                if((isset($tab->cover_content_height->xs) && $tab->cover_content_height->xs) && $tab->cover_content_height->xs > 0) {
                                    $css .= 'height: ' . $tab->cover_content_height->xs .'px;';
                                }
                            }
                            $css .= '}';
                        }
                    }
                }
            $css .= '}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		var section_tab_item = data.section_tab_item || array();
        //图片间距
		var section_img_mg = data.section_img_mg || 12;
        //pc 左侧图显示个数
		var section_left_num = data.section_left_num || 1;
		
		//选项卡pc高度
		var section_height = data.section_height || 800;
		//pc 左侧图宽度（不填默认50%）
		var section_left_width = data.section_left_width || "";
		//pc 左侧第一张图高度
		var section_left_top_img_height = data.section_left_top_img_height || 120;
        //pc 右侧上方图高度
		var section_right_top_height = data.section_right_top_height || 400;
		//pc 右侧下方第一张图宽度
		var section_right_bottom_first_width = data.section_right_bottom_first_width || 358;
		
		//选项卡平板高度
		var section_height_sm = data.section_height_sm || 500;
		//pc 左侧图宽度（不填默认50%）
		var section_left_width_sm = data.section_left_width_sm || "";
		//pc 左侧第一张图高度
		var section_left_top_img_height_sm = data.section_left_top_img_height_sm || 120;
        //pc 右侧上方图高度
		var section_right_top_height_sm = data.section_right_top_height_sm || 400;
		//pc 右侧下方第一张图宽度
		var section_right_bottom_first_width_sm = data.section_right_bottom_first_width_sm || 358;
		
		//手机图高度
		var item_img_height = data.item_img_height || 358;
		//手机内边距
		var section_padding = data.section_padding || 20;
		
		#>
        <style type="text/css">
			{{addonId}} * {
				margin: 0;
			}
			{{addonId}} .section-8 {
				display: flex;
				width: 100%;
				overflow: hidden;
				height: {{section_height}}px;
				justify-content: space-between;
			}
			{{addonId}} .section-8 .img-box {
				overflow: hidden;
				position: relative;
				cursor: pointer;
				display: block;
			}
			{{addonId}} .section-8 .img-box .img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				display: block;
				transition: all ease-in-out 300ms;
			}
			{{addonId}} .section-8 .img-box .cover {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				z-index: -1;
				transition: all ease-in-out 300ms;
			}
			{{addonId}} .section-8 .img-box:hover .cover,{{addonId}} .section-8 .img-box .cover.active {
			    z-index: 10;
			    opacity: 1;
			}
			{{addonId}} .section-8 .content-box {
			    position: absolute;
			}
			{{addonId}} .section-8 .content-box .pc-img {
			    display: block;
			}
			{{addonId}} .section-8 .content-box .wap-img {
			    display: none;
			}
			<# _.each(section_tab_item, function(accordion_item, key){ #>
			    <# if(section_left_num && section_left_num == 2) { #>
			            <# if(key == 0) { #>
                        {{addonId}} .section-8 .large .img-box:first-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .section-8 .large .img-box:last-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 2) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 2 && key < 5) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 2}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
			        <# } else { #>
                        <# if(key == 0) { #>
                        {{addonId}} .large .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 1 && key < 4) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 1}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.md > 0) { #>
                                    top: {{accordion_item.content_top.md}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.md > 0) { #>
                                    right: {{accordion_item.content_right.md}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.md > 0) { #>
                                    bottom: {{accordion_item.content_bottom.md}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.md > 0) { #>
                                    left: {{accordion_item.content_left.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.md > 0) { #>
                                    width: {{accordion_item.cover_content_width.md}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.md > 0) { #>
                                    height: {{accordion_item.cover_content_height.md}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                    <# } #>
			<# }) #>
			{{addonId}} .section-8 .img-box:hover .img {
				transform: scale(1.2);
			}
			{{addonId}} .section-8 .large {
			    <# if(section_left_width && section_left_width != "") { #>
			        width: {{section_left_width}}px;
			    <# } else { #>
			        width: calc(100% / 2);
			    <# } #>
				height: {{section_height}}px;
			}
			{{addonId}} .section-8 .large .img-box {
			    width: 100%;
			    height: 100%;
			}
			<# if(section_left_num && section_left_num == 2) { #>
			    {{addonId}} .section-8 .large .img-box:first-child {
			        <# if(section_left_top_img_height && section_left_top_img_height != "") { #>
                        height: {{section_left_top_img_height}}px;
                    <# } else { #>
                        height: calc(100% / 2);
                    <# } #>
			    }
			    {{addonId}} .section-8 .large .img-box:last-child {
			        <# if(section_left_top_img_height && section_left_top_img_height != "") { #>
                        height: calc(100% - {{section_left_top_img_height}}px - {{section_img_mg}}px);
                    <# } else { #>
                        height: calc(100% / 2 - {{section_img_mg}}px);
                    <# } #>
                    margin-top: {{section_img_mg}}px;
			    }
            <# } #>
			
			{{addonId}} .section-8 .right-box {
			    <# if(section_left_width && section_left_width != "") { #>
			        width: calc(100% - {{section_left_width}}px - {{section_img_mg}}px);
			    <# } else { #> 
				    width: calc(100% / 2 - {{section_img_mg}}px);
			    <# } #>
			}
			{{addonId}} .section-8 .top-box {
			    <# if(section_right_top_height && section_right_top_height != "") { #>
				    height: {{section_right_top_height}}px;
				<# } else { #>
                    height: calc(100% / 2);
                <# } #>
				margin-bottom: {{section_img_mg}}px;
			}
			{{addonId}} .section-8 .right-box .two-box {
				display: flex;
				justify-content: space-between;
				<# if(section_right_top_height && section_right_top_height != "") { #>
				    height: calc(100% - {{section_right_top_height}}px - {{section_img_mg}}px);
				<# } else { #>
                    height: calc(100% / 2 - {{section_img_mg}}px);
                <# } #>
			}
			{{addonId}} .section-8 .right-box .two-box .img-box:first-child {
			    <# if(section_right_bottom_first_width && section_right_bottom_first_width != "") { #>
				    width: {{section_right_bottom_first_width}}px;
				<# } else { #>
                    width: calc(100% / 2);
                <# } #>
			}
			{{addonId}} .section-8 .right-box .two-box .img-box:last-child {
			    <# if(section_right_bottom_first_width && section_right_bottom_first_width != "") { #>
				    width: calc(100% - {{section_right_bottom_first_width}}px - {{section_img_mg}}px);
				<# } else { #>
                    width: calc(100% / 2 - {{section_img_mg}}px);
                <# } #>
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    {{addonId}} .section-8 {
                    height: {{section_height_sm}}px;
                }
                {{addonId}} .section-8 .large {
                    <# if(section_left_width_sm && section_left_width_sm != "") { #>
                        width: {{section_left_width_sm}}px;
                    <# } else { #>
                        width: calc(100% / 2);
                    <# } #>
                    height: {{section_height_sm}}px;
                }
                <# _.each(section_tab_item, function(accordion_item, key){ #>
                    <# if(section_left_num && section_left_num == 2) { #>
			            <# if(key == 0) { #>
                        {{addonId}} .section-8 .large .img-box:first-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .section-8 .large .img-box:last-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 2) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 2 && key < 5) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 2}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
			        <# } else { #>
                        <# if(key == 0) { #>
                        {{addonId}} .large .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 1 && key < 4) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 1}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.sm > 0) { #>
                                    top: {{accordion_item.content_top.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.sm > 0) { #>
                                    right: {{accordion_item.content_right.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.sm > 0) { #>
                                    bottom: {{accordion_item.content_bottom.sm}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.sm > 0) { #>
                                    left: {{accordion_item.content_left.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.sm > 0) { #>
                                    width: {{accordion_item.cover_content_width.sm}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.sm > 0) { #>
                                    height: {{accordion_item.cover_content_height.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                    <# } #>
                <# }) #>
                <# if(section_left_num && section_left_num == 2) { #>
                    {{addonId}} .section-8 .large .img-box:first-child {
                        <# if(section_left_top_img_height_sm && section_left_top_img_height_sm != "") { #>
                            height: {{section_left_top_img_height_sm}}px;
                        <# } else { #>
                            height: calc(100% / 2);
                        <# } #>
                    }
                    {{addonId}} .section-8 .large .img-box:last-child {
                        <# if(section_left_top_img_height_sm && section_left_top_img_height_sm != "") { #>
                            height: calc(100% - {{section_left_top_img_height_sm}}px - {{section_img_mg}}px);
                        <# } else { #>
                            height: calc(100% / 2 - {{section_img_mg}}px);
                        <# } #>
                    }
                <# } #>
                {{addonId}} .section-8 .right-box {
                    <# if(section_left_width_sm && section_left_width_sm != "") { #>
                        width: calc(100% - {{section_left_width_sm}}px - {{section_img_mg}}px);
                    <# } else { #> 
                        width: calc(100% / 2 - {{section_img_mg}}px);
                    <# } #>
                }
                {{addonId}} .section-8 .top-box {
                    <# if(section_right_top_height_sm && section_right_top_height_sm != "") { #>
                        height: {{section_right_top_height_sm}}px;
                    <# } else { #>
                        height: calc(100% / 2);
                    <# } #>
                }
                {{addonId}} .section-8 .right-box .two-box {
                    <# if(section_right_top_height_sm && section_right_top_height_sm != "") { #>
                        height: calc(100% - {{section_right_top_height_sm}}px - {{section_img_mg}}px);
                    <# } else { #>
                        height: calc(100% / 2 - {{section_img_mg}}px);
                    <# } #>
                }
                {{addonId}} .section-8 .right-box .two-box .img-box:first-child {
                    <# if(section_right_bottom_first_width_sm && section_right_bottom_first_width_sm != "") { #>
                        width: {{section_right_bottom_first_width_sm}}px;
                    <# } else { #>
                        width: calc(100% / 2);
                    <# } #>
                }
                {{addonId}} .section-8 .right-box .two-box .img-box:last-child {
                    <# if(section_right_bottom_first_width_sm && section_right_bottom_first_width_sm != "") { #>
                        width: calc(100% - {{section_right_bottom_first_width_sm}}px - {{section_img_mg}}px);
                    <# } else { #>
                        width: calc(100% / 2 - {{section_img_mg}}px);
                    <# } #>
                }
			}
			@media (max-width: 767px) {
			    {{addonId}} .section-8 {
			        display: block;
			        height: max-content;
			        padding: 0 {{ section_padding }}px;
			    }
			    {{addonId}} .section-8 .large {
			        width: 100% !important;
			        <# if(section_left_num && section_left_num == 2) { #>
			        height: auto;
			        <# } #>
			    }
			    {{addonId}} .section-8 .img-box {
			        width: 100% !important;
			        height: {{item_img_height}}px !important;
			        margin-bottom: 20px;
			    }
			    {{addonId}} .section-8 .right-box {
			        width: 100%;
			    }
			    {{addonId}} .section-8 .right-box .two-box {
			        display: block;
			    }
			    {{addonId}} .section-8 .content-box .pc-img {
                    display: none;
                }
                {{addonId}} .section-8 .content-box .wap-img {
                    display: block;
                }
			    <# _.each(section_tab_item, function(accordion_item, key){ #>
			        <# if(section_left_num && section_left_num == 2) { #>
			            <# if(key == 0) { #>
                        {{addonId}} .section-8 .large .img-box:first-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .section-8 .large .img-box:last-child .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 2) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 2 && key < 5) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 2}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
			        <# } else { #>
                        <# if(key == 0) { #>
                        {{addonId}} .large .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key == 1) { #>
                        {{addonId}} .top-box .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                        <# if(key > 1 && key < 4) { #>
                        {{addonId}} .two-box .img-box:nth-child({{key - 1}}) .content-box {
                            <# if(accordion_item.is_show_cover == 1) { #>
                                <# if(accordion_item.content_top && accordion_item.content_top.xs > 0) { #>
                                    top: {{accordion_item.content_top.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_right && accordion_item.content_right.xs > 0) { #>
                                    right: {{accordion_item.content_right.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_bottom && accordion_item.content_bottom.xs > 0) { #>
                                    bottom: {{accordion_item.content_bottom.xs}}px;
                                <# } #>
                                <# if(accordion_item.content_left && accordion_item.content_left.xs > 0) { #>
                                    left: {{accordion_item.content_left.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_width && accordion_item.cover_content_width.xs > 0) { #>
                                    width: {{accordion_item.cover_content_width.xs}}px;
                                <# } #>
                                <# if(accordion_item.cover_content_height && accordion_item.cover_content_height.xs > 0) { #>
                                    height: {{accordion_item.cover_content_height.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        <# } #>
                    <# } #>
                <# }) #>
			}
		</style>
        <div class="section-8">
            <# 
                function add(accordion_item){
                    var html = "";
                    html += "<img src=\'" + accordion_item.img + "\' class=\'img\' alt=\'\' >";
                     if (accordion_item.is_show_cover == 1) {
                        html += "<div class=\'cover ";
                        if(accordion_item.is_cover == 1) {
                            html += " active";
                        }
                        html += "\' style=\'background-color: " + accordion_item.bg_color + "\'>";
                        html += "<div class=\'content-box\'>";
                        if(accordion_item.cover_content != ""){
                            html += "<img src=\'" + accordion_item.cover_content + "\' alt=\'\' class=\'pc-img\' >";
                            html += "<img src=\'" + accordion_item.cover_content_wap + "\' alt=\'\' class=\'wap-img\' >";
                        }
                        html += "</div>";
                        html += "</div>";
                     }
                     return html;
                }
            #>
            <# if(section_left_num && section_left_num == 2) { #>
                <div class="large">
                <# _.each(section_tab_item, function(accordion_item, key){ #>
                    <# if (key < 2) { #>
                    <a class="img-box">
                        {{{add(accordion_item)}}}
                    </a>
                    <# } #>  
                <# }); #>
                </div>
                <!-- 右侧图 -->
                <div class="right-box">
                    <# _.each(section_tab_item, function(accordion_item, key){ #>
                        <# if (key == 2) { #>
                        <a class="img-box top-box">
                            {{{add(accordion_item)}}}
                        </a>
                        <# } #>
                    <# }); #>
                    <div class="two-box">
                        <# _.each(section_tab_item, function(accordion_item, key){ #>
                        <# if (key > 2 && key < 5) { #>
                        <a class="img-box">
                            {{{add(accordion_item)}}}
                        </a>
                        <# } #>
                        <# }); #>
                    </div>
                </div>    
            <# } else { #>
                <# _.each(section_tab_item, function(accordion_item, key){ #>
                    <# if (key == 0) { #>
                    <!-- 第一个大图 -->
                    <a class="img-box large">
                        {{{add(accordion_item)}}}
                    </a>
                    <# } #>             
                <# }); #>
                <!-- 右侧图 -->
                <div class="right-box">
                    <# _.each(section_tab_item, function(accordion_item, key){ #>
                        <# if (key == 1) { #>
                        <a class="img-box top-box">
                            {{{add(accordion_item)}}}
                        </a>
                        <# } #>
                    <# }); #>
                    <div class="two-box">
                        <# _.each(section_tab_item, function(accordion_item, key){ #>
                        <# if (key > 1 && key < 4) { #>
                        <a class="img-box">
                            {{{add(accordion_item)}}}
                        </a>
                        <# } #>
                        <# }); #>
                    </div>
                </div>
           <# } #>
		</div>
		';

        return $output;
    }
}
