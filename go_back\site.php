<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonGo_back extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;

		// 布局
		$back_theme = isset($settings->back_theme) ? $settings->back_theme : 'back1';
		// 开启返回图标
		$is_back_icon = isset($settings->is_back_icon) ? $settings->is_back_icon : 1;
		// 返回图标
		$back_icon = isset($settings->back_icon) ? $settings->back_icon : 'https://oss.lcweb01.cn/joomla/20231010/9ca82c68ea332aaa56b806742cc73571.png';
		// 移入返回图标
		$back_icon_h = isset($settings->back_icon_h) ? $settings->back_icon_h : '';
		// 开启返回文字
		$is_back_text = isset($settings->is_back_text) ? $settings->is_back_text : 1;
		// 返回文字
		$back_text = isset($settings->back_text) ? $settings->back_text : '返回';

		$output = "";

		if ($back_theme == "back1") {
			$output .= '
			<div class="back-container" onClick="goBack()">';
				if ($is_back_icon == 1) {
					$output .= '
					<img class="back-icon" src="' . $back_icon .'" alt="" />';
					if ($back_icon_h) {
						$output .= '
						<img class="back-icon hover" src="' . $back_icon_h .'" alt="" />';
					}
				}
				if ($is_back_text == 1) {
					$output .= '
					<span class="back-text">' . $back_text . '</span>';
				}
			$output .= '
			</div>
			';
		}

		$output .= '
		<script>
			function goBack() {
				window.history.back();
			}
		</script>
		';
		
		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addonId = '#jwpf-addon-' . $this->addon->id;

		// 布局
		$back_theme = isset($settings->back_theme) ? $settings->back_theme : 'back1';
		// 移入返回图标
		$back_icon_h = isset($settings->back_icon_h) ? $settings->back_icon_h : '';
		// 返回图标宽度
		$back_icon_width_md = (isset($settings->back_icon_width) && $settings->back_icon_width) ? $settings->back_icon_width : 14;
		$back_icon_width_sm = (isset($settings->back_icon_width_sm) && $settings->back_icon_width_sm) ? $settings->back_icon_width_sm : '';
		$back_icon_width_xs = (isset($settings->back_icon_width_xs) && $settings->back_icon_width_xs) ? $settings->back_icon_width_xs : '';
		// 返回图标高度
		$back_icon_height_md = (isset($settings->back_icon_height) && $settings->back_icon_height) ? $settings->back_icon_height : 14;
		$back_icon_height_sm = (isset($settings->back_icon_height_sm) && $settings->back_icon_height_sm) ? $settings->back_icon_height_sm : '';
		$back_icon_height_xs = (isset($settings->back_icon_height_xs) && $settings->back_icon_height_xs) ? $settings->back_icon_height_xs : '';
		// 返回文字颜色
		$back_text_color = isset($settings->back_text_color) ? $settings->back_text_color : '#000';
		// 返回文字大小
		$back_text_fontsize_md = (isset($settings->back_text_fontsize) && $settings->back_text_fontsize) ? $settings->back_text_fontsize : 14;
		$back_text_fontsize_sm = (isset($settings->back_text_fontsize_sm) && $settings->back_text_fontsize_sm) ? $settings->back_text_fontsize_sm : '';
		$back_text_fontsize_xs = (isset($settings->back_text_fontsize_xs) && $settings->back_text_fontsize_xs) ? $settings->back_text_fontsize_xs : '';
		// 返回文字颜色 移入
		$back_text_color_h = isset($settings->back_text_color_h) ? $settings->back_text_color_h : '';

		$output = "";
		if ($back_theme == "back1") {
			$output .= "
			{$addonId} .back-container {
				display: flex;
				align-items: center;
				cursor: pointer;
			}
			{$addonId} .back-container .back-icon {
				width: {$back_icon_width_md}px;
				height: {$back_icon_height_md}px;
				margin-right: 5px;
			}
			{$addonId} .back-container .back-icon.hover {
				display: none;
			}";
			if ($back_icon_h) { 
				$output .= "
				{$addonId} .back-container:hover .back-icon {
					display: none;
				}
				{$addonId} .back-container:hover .back-icon.hover {
					display: block;
				}";
			}
			$output .= "
			{$addonId} .back-container .back-text {
				color: {$back_text_color};
				font-size: {$back_text_fontsize_md}px;
			}
			{$addonId} .back-container:hover .back-text {
				color: {$back_text_color_h};
			}
			@media (min-width: 768px) and (max-width: 991px) {
				{$addonId} .back-container .back-icon {
					width: {$back_icon_width_sm}px;
					height: {$back_icon_height_sm}px;
				}
				{$addonId} .back-container .back-text {
					font-size: {$back_text_fontsize_sm}px;
				}
			}
			@media (max-width: 767px) {
				{$addonId} .back-container .back-icon {
					width: {$back_icon_width_xs}px;
					height: {$back_icon_height_xs}px;
				}
				{$addonId} .back-container .back-text {
					font-size: {$back_text_fontsize_xs}px;
				}
			}";
		}

		return $output;
	}

	public static function getTemplate()
	{
		$output = '
			<#
				var addonId = "#jwpf-addon-" + data.id;
				// 布局样式
				var back_theme = data.back_theme || "back1";
				// 开启返回图标
				var is_back_icon = data.is_back_icon;
				// 返回图标
				var back_icon = data.back_icon || "https://oss.lcweb01.cn/joomla/20231010/9ca82c68ea332aaa56b806742cc73571.png";
				// 移入图标
				var back_icon_h = data.back_icon_h;
				// 图标宽度
				var back_icon_width_md = toChangeNum("back_icon_width").md || 14,
					back_icon_width_sm = toChangeNum("back_icon_width").sm,
					back_icon_width_xs = toChangeNum("back_icon_width").xs;
				// 图标高度
				var back_icon_height_md = toChangeNum("back_icon_height").md || 14,
					back_icon_height_sm = toChangeNum("back_icon_height").sm,
					back_icon_height_xs = toChangeNum("back_icon_height").xs;
				// 开启返回文字
				var is_back_text = data.is_back_text;
				// 返回文字
				var back_text = data.back_text || "返回";
				// 文字颜色
				var back_text_color = data.back_text_color || "#000";
				// 文字大小
				var back_text_fontsize_md = toChangeNum("back_text_fontsize").md || 14,
					back_text_fontsize_sm = toChangeNum("back_text_fontsize").sm,
					back_text_fontsize_xs = toChangeNum("back_text_fontsize").xs;
				// 移入文字颜色
				var back_text_color_h = data.back_text_color_h;
				// 移入文字大小
				// var back_text_fontsize_h_md = toChangeNum("back_text_fontsize_h").md,
				// 	back_text_fontsize_h_sm = toChangeNum("back_text_fontsize_h").sm,
				// 	back_text_fontsize_h_xs = toChangeNum("back_text_fontsize_h").xs;

				console.log(back_theme, "back_theme")

				// 处理适配
				function toChangeNum(key) {
					var md = "", sm = "", xs = "";
					if(_.isObject(data[key])){
						md = data[key].md || ""; sm = data[key].sm || ""; xs = data[key].xs || "";
					}
					return { md: md, sm: sm, xs: xs }
				}
			#>
			<style>
				<# if (back_theme == "back1") { #>
					{{addonId}} .back-container {
						display: flex;
						align-items: center;
						cursor: pointer;
					}
					{{addonId}} .back-container .back-icon {
						width: {{back_icon_width_md}}px;
						height: {{back_icon_height_md}}px;
						margin-right: 5px;
					}
					{{addonId}} .back-container .back-icon.hover {
						display: none;
					}
					<# if (back_icon_h) { #>
						{{addonId}} .back-container:hover .back-icon {
							display: none;
						}
						{{addonId}} .back-container:hover .back-icon.hover {
							display: block;
						}
					<# } #>
					{{addonId}} .back-container .back-text {
						color: {{back_text_color}};
						font-size: {{back_text_fontsize_md}}px;
					}
					{{addonId}} .back-container:hover .back-text {
						color: {{back_text_color_h}};
					}
					@media (min-width: 768px) and (max-width: 991px) {
						{{addonId}} .back-container .back-icon {
							width: {{back_icon_width_sm}}px;
							height: {{back_icon_height_sm}}px;
						}
						{{addonId}} .back-container .back-text {
							font-size: {{back_text_fontsize_sm}}px;
						}
					}
					@media (max-width: 767px) {
						{{addonId}} .back-container .back-icon {
							width: {{back_icon_width_xs}}px;
							height: {{back_icon_height_xs}}px;
						}
						{{addonId}} .back-container .back-text {
							font-size: {{back_text_fontsize_xs}}px;
						}
					}
				<# } #>
			</style>
			<# if (back_theme == "back1") { #>
				<div class="back-container">
					<# if (is_back_icon == 1) { #>
						<img class="back-icon" src=\'{{back_icon}}\' alt="" />
						<# if (back_icon_h) { #>
							<img class="back-icon hover" src=\'{{back_icon_h}}\' alt="" />
						<# } #>
					<# } #>
					<# if (is_back_text == 1) { #>
						<span class="back-text">{{ back_text }}</span>
					<# } #>
				</div>
			<# } #>
		';
		return $output;
	}
}
