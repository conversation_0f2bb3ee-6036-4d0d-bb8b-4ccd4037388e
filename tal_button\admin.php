<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
         'type' => 'content',
         'addon_name' => 'tal_button',
         'title' => JText::_('手机顶部按钮'),
         'desc' => JText::_('手机顶部按钮'),
         'category' => '按钮',
         'attr' => array(
             'general' => array(
                'search_type' => array(
                    'type'   => 'select',
                    'title'  => '请选择类型',
                    'desc'   => '',
                    'values' => array(
                        'type1' => '微信',
                        'type2' => 'QQ',
                    ),  
                    'std'    => 'type1',
                ),
                'title_text' => array(
                  'type' => 'text',
                  'title' => '标题内容',
                  'desc' => '',
                  'std' => '微信号',
                  'placeholder' => '微信号',
                ),
                'desc_text' => array(
                  'type' => 'text',
                  'title' => '弹出层提示内容',
                  'desc' => '弹出层提示内容',
                  'std' => '添加微信好友, 详细了解产品',
                  'placeholder' => '添加微信好友, 详细了解产品',
                ),
                'num_text' => array(
                  'type' => 'text',
                  'title' => '请输入微信号或QQ号',
                  'desc' => '',
                  'std' => '000000',
                  'placeholder' => '000000',
                ),
                'search_side' => array(
                  'type'   => 'select',
                  'title'  => '请选择位置',
                  'desc'   => '',
                  'values' => array(
                      'top' => '顶部',
                      'bottom' => '底部',
                  ),  
                  'std' => 'top'
              ),
                'div_bg1'=> array(
                  'type'  => 'color',
                  'title' => JText::_('标题背景颜色'),
                  'std'   => '#522f2f',
                  'placeholder' => '#522f2f',
                ),
                'button_bg2'=> array(
                  'type'  => 'color',
                  'title' => JText::_('按钮背景颜色'),
                  'std'   => '#f84d6a',
                  'placeholder' => '#f84d6a',
                ),
                'div_color1'=> array(
                  'type'  => 'color',
                  'title' => JText::_('标题字体颜色'),
                  'std'   => '#fff',
                  'placeholder' => '#fff',
                ),
                'button_color2'=> array(
                  'type'  => 'color',
                  'title' => JText::_('按钮字体颜色'),
                  'std'   => '#fff',
                  'placeholder' => '#fff',
                ),
             ),
         ),
    )
);
