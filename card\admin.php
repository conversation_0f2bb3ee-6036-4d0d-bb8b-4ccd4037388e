<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'card',
		'title' => JText::_('内容可选中'),
		'desc' => JText::_('内容可选中'),
		'category' => '常用插件',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
                'item' => array(
                    'title' => '内容设置',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题内容',
                            'desc' => '标题内容',
                            'std' => '建站通'
                        ),
                        'content' => array(
                            'type' => 'textarea',
                            'title' => '内容',
                            'desc' => '内容',
                            'std'=>'建站通：企业互联网行为建设平台。独创企业素材及物料管理功能，解决企业宣传物料碎片化问题。'
                        ),
                    ),
                ),
                'card_width'=>array(
                    'title'=>'卡片宽度',
                    'desc'=>'卡片宽度',
                    'type' =>'slider',
                    'std'=>228,
                    'responsive'=>true,
                    'max'=>600,
                    'min'=>80
                ),
                'card_height'=>array(
                    'title'=>'卡片高度',
                    'desc'=>'卡片高度',
                    'type' =>'slider',
                    'std'=>308,
                    'responsive'=>true,
                    'max'=>600,
                    'min'=>80
                ),
                'card_padding'=>array(
                    'title'=>'卡片内边距',
                    'desc'=>'卡片内边距',
                    'type' =>'padding',
                    'std'=>array(
                        'md'=>'24px 28px 24px 28px',
                        'sm'=>'24px 28px 24px 28px',
                        'xs'=>'24px 28px 24px 28px',
                    ),
                    'responsive'=>true,
                ),
                'card_radius'=>array(
                    'title'=>'卡片圆角',
                    'desc'=>'卡片圆角',
                    'type' =>'slider',
                    'std'=>8,
                    'max'=>400,
                    'min'=>0,
                ),
                'card_margin_right'=>array(
                    'title'=>'卡片右边距',
                    'desc'=>'卡片右边距',
                    'type' =>'slider',
                    'std'=>22,
                    'max'=>100,
                    'min'=>0,
                    'responsive'=>true,
                ),
                'card_left_margin'=>array(
                    'title'=>'卡片标题左边距',
                    'desc'=>'卡片标题左边距',
                    'type' =>'slider',
                    'std'=>10,
                    'max'=>100,
                    'min'=>0,
                    'responsive'=>true,
                ),
                'card_title_size'=>array(
                    'title'=>'卡片标题字体大小',
                    'desc'=>'卡片标题字体大小',
                    'type' =>'slider',
                    'std'=>22,
                    'max'=>30,
                    'min'=>10,
                    'responsive'=>true,
                ),
                'card_title_margin_bottom'=>array(
                    'title'=>'卡片标题下边距',
                    'desc'=>'卡片标题下边距',
                    'type' =>'slider',
                    'std'=>22,
                    'max'=>100,
                    'min'=>0,
                    'responsive'=>true,
                ),
                'card_font_size'=>array(
                    'title'=>'卡片正文字体大小',
                    'desc'=>'卡片正文字体大小',
                    'type' =>'slider',
                    'std'=>12,
                    'max'=>40,
                    'min'=>10,
                    'responsive'=>true,
                ),
                'style' => array(
                    'type' => 'buttons',
                    'title' => '卡片状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                ),
                'card_background'=>array(
                    'title'=>'卡片背景色',
                    'desc'=>'卡片背景色',
                    'type' =>'color',
                    'std'=>'rgb(229, 232, 236)',
                    'depends'=>array(
                        array('style','=','normal')
                    )
                ),
                'card_background_active'=>array(
                    'title'=>'卡片背景色',
                    'desc'=>'卡片背景色',
                    'type' =>'color',
                    'std'=>'rgb(238, 120, 0)',
                    'depends'=>array(
                        array('style','=','active')
                    )
                ),
                'card_title_color'=>array(
                    'title'=>'卡片标题字体颜色',
                    'desc'=>'卡片标题字体颜色',
                    'type' =>'color',
                    'std'=>'rgba(64,64,64,1)',
                    'depends'=>array(
                        array('style','=','normal')
                    )
                ),
                'card_title_color_active'=>array(
                    'title'=>'卡片标题字体颜色',
                    'desc'=>'卡片标题字体颜色',
                    'type' =>'color',
                    'std'=>'white',
                    'depends'=>array(
                        array('style','=','active')
                    )
                ),
                'card_content_color'=>array(
                    'title'=>'卡片内容字体颜色',
                    'desc'=>'卡片内容字体颜色',
                    'type' =>'color',
                    'std'=>'rgba(64,64,64,1)',
                    'depends'=>array(
                        array('style','=','normal')
                    )
                ),
                'card_content_color_active'=>array(
                    'title'=>'卡片内容字体颜色',
                    'desc'=>'卡片内容字体颜色',
                    'type' =>'color',
                    'std'=>'white',
                    'depends'=>array(
                        array('style','=','active')
                    )
                ),
                'card_icon' => array(
                    'type' => 'media',
                    'title' => '图标',
                    'std' => '/components/com_jwpagefactory/addons/card/assets/images/chooseout.png',
                    'depends'=>array(
                        array('style','=','normal')
                    )
                ),
                'card_icon_active' => array(
                    'type' => 'media',
                    'title' => '图标',
                    'std' => '/components/com_jwpagefactory/addons/card/assets/images/img_1.png',
                    'depends'=>array(
                        array('style','=','active')
                    )
                ),
                'card_icon_size'=>array(
                    'title'=>'卡片图标大小',
                    'desc'=>'卡片图标大小',
                    'type' =>'slider',
                    'std'=>26,
                    'max'=>30,
                    'min'=>10,
                    'responsive'=>true,
                ),
			),
		),
	)
);
