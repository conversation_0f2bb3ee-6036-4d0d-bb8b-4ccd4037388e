<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'general',
		'addon_name' => 'shop_three_class',
		'title' => JText::_('产品二级/三级分类菜单'),
		'desc' => JText::_('产品二级、三级分类菜单'),
		'category' => '产品',
		'attr' => array(
			'general' => array(
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),
				'themb' => array(
					'type' => 'select',
					'title' => '选择布局',
					'desc' => '',
					'values' => array(
                       'site01' => '三级分类菜单',
                       'site02' => '二级分类菜单',
                    ),
                    'std' => 'site01',
				),
				'tab_list_bgcolor' => array(
					'type' => 'color',
					'title' => '左侧菜单背景颜色',
					'desc' => '',
					'std' => '',
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
				'tab_active_bgcolor' => array(
					'type' => 'color',
					'title' => '左侧菜单滑过背景颜色',
					'desc' => '',
					'std' => '',
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
				'right_list_img' => array(
					'type' => 'slider',
					'title' => '左侧菜单字体大小',
					'std' => '15',
					'min' => 0,
					'max' => 100,
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),

				'tab_active_color' => array(
					'type' => 'color',
					'title' => '左侧菜单文字颜色',
					'desc' => '',
					'std' => '',
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
				'tab_list_width' => array(
					'type' => 'slider',
					'title' => '左侧列表宽度',
					'desc' => '',
					'responsive' => false,
					'std' => '160',
					'min' => 100,
					'max' => 600,
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
				'tab_list_height' => array(
					'type' => 'slider',
					'title' => '左侧列表高度',
					'desc' => '',
					'responsive' => false,
					'std' => '160',
					'min' => 100,
					'max' => 700,
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
//				'icon_m'      => array(
//					'type'       => 'margin',
//					'title'      => '左侧文字边距',
//					'responsive' => true,
//					'std'        => '10 0 10 10',
//				),
//				'right_list_bgcolor' => array(
//					'type' => 'color',
//					'title' => '右侧菜单背景颜色',
//					'desc' => '',
//					'std' => ''
//				),

				'right_list_three' => array(
					'type' => 'color',
					'title' => '右侧三级菜单字体颜色',
					'desc' => '',
					'std' => '',
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),
				'right_list_three_after' => array(
					'type' => 'color',
					'title' => '右侧三级菜单鼠标滑过字体颜色',
					'desc' => '',
					'std' => '',
					'depends' => array(
                        array('themb', '!=', 'site02'),
                    )
				),

				//二级分类
				'catordering' => array(
                    'type' => 'select',
                    'title' => JText::_('分类排序'),
                    'desc' => JText::_('从分类中选择排序的方式'),
                    'values' => array(
                        'sortasc' => JText::_('按排序id正序'),
                        'sortdesc' => JText::_('按排序id倒序'),
                    ),
                    'std' => 'sortasc',
                    'depends' =>array(
                        array('themb', '=', 'site02'),
                    ),
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从第n个分类开始显示',
                    'std' => '1',
                    'depends' =>array(
                        array('themb', '=', 'site02'),
                    ),
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类(0为不限)',
                    'desc' => '显示n条分类',
                    'std' => '10',
                    'depends' =>array(
                        array('themb', '=', 'site02'),
                    ),
                ),

				'second_bg' => array(
					'type' => 'color',
					'title' => '背景色',
					'std' => '#F6F6F6',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_title' => array(
					'type' => 'text',
					'title' => '名称',
					'std' => '产品展示',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_title_fontsize' => array(
					'type' => 'slider',
					'title' => '名称字体大小',
					'std' => '16',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_title_color' => array(
					'type' => 'color',
					'title' => '名称颜色',
					'std' => '#ba3606',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_class_fontsize' => array(
					'type' => 'slider',
					'title' => '分类字体大小',
					'std' => '14',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_class_lineheight' => array(
					'type' => 'slider',
					'title' => '分类行高',
					'std' => '50',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_checkbg_color' => array(
					'type' => 'color',
					'title' => '一级分类的选中背景色',
					'std' => '#BA3606',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_check_border' => array(
					'type' => 'slider',
					'title' => '一级分类的选中圆角',
					'std' => '8',
					'max' => 50,
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),
				'second_check_color' => array(
					'type' => 'color',
					'title' => '二级分类的选中字体色',
					'std' => '#ba3606',
					'depends' => array(
                        array('themb', '=', 'site02'),
                    )
				),


				'detail_page_id' => array(
					'type' => 'select',
					'title' => '列表模版',
					'desc' => '显示列表模版',
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
			)
		)
	)
);