$(function () {
    // number($('#lc-num1').first(), 789688, 789400, 10);
    // if (($(window).scrollTop() > ($('#lc-num1').offset().top - 400)) && ($(window).scrollTop() < ($('#lc-num1').offset().top - 300))) {
    //     number($('#num1').first(), 3, 0, 500);
    //     number($('#num2').first(), 15, 0, 100);
    //     number($('#num3').first(), 45, 0, 33);
    //     number($('#num4').first(), 72, 0, 13);
    //     number($('#num5').first(), 800, 700, 50);
    //     number($('#num6').first(), 120000, 119900, 50);
    // }



    /* 鏈ㄩ┈start */
    certifySwiper = new Swiper('#certify .swiper-container', {
        watchSlidesProgress: true,
        slidesPerView: 'auto',
        centeredSlides: true,
        loop: true,
        loopedSlides: 100,
        autoplay: true,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        pagination: {
            el: '.swiper-pagination',
            //clickable :true,
        },
        on: {
            progress: function certify(progress) {
                var lcSwiperSlideWidth = $("#certify .swiper-slide").width();
                // console.log(lcSwiperSlideWidth);
                for (i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i);
                    var slideProgress = this.slides[i].progress * 0.51;
                    // console.log(slideProgress);
                    modify = 1;
                    if (Math.abs(slideProgress) > 1) {
                        modify = (Math.abs(slideProgress) - 1) * 2 + 1;
                    }
                    translate = slideProgress * modify * (lcSwiperSlideWidth / 2) + 'px';
                    scale = 1 - Math.abs(slideProgress) / 10;
                    zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                    slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                    slide.css('zIndex', zIndex);
                    slide.css('opacity', 1);
                    if (Math.abs(slideProgress) > 3) {
                        slide.css('opacity', 0);
                    }
                }
            },
            setTransition: function (transition) {
                for (var i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i)
                    slide.transition(transition);
                }

            },
            resize: function certify(progress) {
                var lcSwiperSlideWidth = $("#certify .swiper-slide").width();
                // console.log(lcSwiperSlideWidth);
                for (i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i);
                    var slideProgress = this.slides[i].progress * 0.51;
                    // console.log(slideProgress);
                    modify = 1;
                    if (Math.abs(slideProgress) > 1) {
                        modify = (Math.abs(slideProgress) - 1) * 2 + 1;
                    }
                    translate = slideProgress * modify * (lcSwiperSlideWidth / 2) + 'px';
                    scale = 1 - Math.abs(slideProgress) / 10;
                    zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                    slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                    slide.css('zIndex', zIndex);
                    slide.css('opacity', 1);
                    if (Math.abs(slideProgress) > 3) {
                        slide.css('opacity', 0);
                    }
                }
            }
        }

    })
    /* 鏈ㄩ┈end */


    var lc_b_page1_sw = new Swiper(".lc_b_page1_sw", {
        loop: true,
        spaceBetween: 46,
        centeredSlides: false,
        initialSlide: 0,
        slidesPerView: 4,
        pagination: {
            el: ".lc_b_page1_sw_pa",
            clickable: true,
            renderBullet: function (index, className) {
                return '<span class="' + className + '">' + (index + 1) + '</span>';
            },
            dynamicBullets: true,
        },
        breakpoints: {
            1600: {
                slidesPerView: 4,
                spaceBetween: 20,
            },
            1400: {
                slidesPerView: 3,
                spaceBetween: 40,
            },
            1366: {
                slidesPerView: 3,
                spaceBetween: 26,
            },
            1200: {
                slidesPerView: 3,
                spaceBetween: 10,
            },
            1023: {
                slidesPerView: 1.6,
                spaceBetween: 14,
                centeredSlides: true,
            },
        },
        navigation: {
            nextEl: '.lc_b_page1_sw_next',
            prevEl: '.lc_b_page1_sw_prev',
        },
    })



    function oRseize() {
        var oDocumentWidth = $(document).width();
        // console.log(oDocumentWidth);

        if (oDocumentWidth > 1023) {

        } else {
            lc_a_page4_sw = null;
            lc_a_page4_sw = new Swiper(".lc-a-page4-sw", {
                loop: true,
                spaceBetween: 8,
                centeredSlides: true,
                initialSlide: 0,
                slidesPerView: 1.4,
                pagination: {
                    el: ".lc-a-page4-sw-pa",
                    clickable: true,
                }
            })
            lc_a_page6_sw = null;
            lc_a_page6_sw = new Swiper(".lc-a-page6-sw", {
                loop: false,
                spaceBetween: 10,
                centeredSlides: false,
                initialSlide: 0,
                slidesPerView: 2.2
            })
        }
    }
    oRseize();
    // $(window).on("resize", oRseize);
    /* app start */
    // function lcDBottomNum() {
    //     number($('#lc-d-bottom-num1').first(), 4, 0, 100);
    //     number($('#lc-d-bottom-num2').first(), 500, 409, 20);
    //     number($('#lc-d-bottom-num3').first(), 15, 0, 20);
    //     number($('#lc-d-bottom-num4').first(), 1000, 921, 20);
    //     number($('#lc-e-banner-num1').first(), 44, 18, 105);
    //     number($('#lc-e-banner-num2').first(), 800, 671, 20);
    //     number($('#lc-e-banner-num3').first(), 12000, 11821, 20);
    // }
    // lcDBottomNum();
    $(window).on("scroll", function () {
        if ($('.lc-d-bottom')[0]) {
            if (($(window).scrollTop() > ($('.lc-d-bottom').offset().top - 300)) && ($(window).scrollTop() < ($('.lc-d-bottom').offset().top - 300))) {
                lcDBottomNum();
            }
        }
    })
    /* app end */
    lcDHotSwiper = new Swiper('.lc-d-hot-touch-sw', {
        watchSlidesProgress: true,
        slidesPerView: 'auto',
        centeredSlides: true,
        loop: true,
        loopedSlides: 100,
        autoplay: true,
        on: {
            progress: function (progress) {
                for (i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i);
                    var slideProgress = this.slides[i].progress;
                    modify = 1;
                    if (Math.abs(slideProgress) > 1) {
                        modify = (Math.abs(slideProgress) - 1) * 0.3 + 1;
                    }
                    translate = slideProgress * modify * (274 / 2.15 / 100) + 'rem';
                    scale = 1 - Math.abs(slideProgress) / 5;
                    zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                    slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                    slide.css('zIndex', zIndex);
                    slide.css('opacity', 1);
                    if (Math.abs(slideProgress) > 3) {
                        slide.css('opacity', 0);
                    }
                }
            },
            setTransition: function (transition) {
                for (var i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i)
                    slide.transition(transition);
                }
            },
            slideChangeTransitionEnd: function () {

                $(".lc-ercode-item").eq(this.realIndex).addClass("lc-active").siblings().removeClass("lc-active");
            },
        }
    })
    /*  */
    var lc_e_honer_sw = new Swiper(".lc-e-honer-sw", {
        loop: true,
        spaceBetween: 23,
        centeredSlides: false,
        initialSlide: 0,
        slidesPerView: 3,
        navigation: {
            nextEl: ".lc-e-honer-sw-next"
        },
        breakpoints: {
            1023: {
                slidesPerView: 2,
                spaceBetween: 10,
                slidesPerColumn: 2,
                loop: false,
            },
        }
    });
    /*  */
    var lc_e_honer_swPC1 = null,
        lc_e_honer_swTouch1 = null,
        lc_e_honer_swPC2 = null,
        lc_e_honer_swTouch2 = null,
        lc_e_honer_swPC3 = null,
        lc_e_honer_swTouch3 = null;

    function teamFun() {
        lc_e_honer_swPC1 = new Swiper(".lc-team-sw-pc1", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-pc1",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
        lc_e_honer_swTouch1 = new Swiper(".lc-team-sw-Touch1", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-Touch1",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
        /*  */
        lc_e_honer_swPC2 = new Swiper(".lc-team-sw-pc2", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-pc2",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
        lc_e_honer_swTouch2 = new Swiper(".lc-team-sw-Touch2", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-Touch2",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
        /*  */
        lc_e_honer_swPC3 = new Swiper(".lc-team-sw-pc3", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-pc3",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
        lc_e_honer_swTouch3 = new Swiper(".lc-team-sw-Touch3", {
            autoplay: {delay:3000,disableOnInteraction: false,},
            loop: true,
            spaceBetween: 18,
            centeredSlides: false,
            initialSlide: 0,
            slidesPerView: 3,
            // pagination: {
            //     el: ".lc-team-sw-pagination-Touch3",
            //     clickable: true,
            // },
            observer: true,
            observeParents: true,
            breakpoints: {
                1023: {
                    slidesPerView: 3,
                    spaceBetween: 8,
                },
            }
        });
    }
    teamFun();
    /* 鍖哄煙鍒囨崲 start */
    // $(".lc-team-btn").click(function () {
    //     var _index = $(this).index();
    //     console.log(_index);
    //     $(this).addClass("active").siblings().removeClass("active");
    //     $(".lc-team-slide").eq(_index).addClass("active").siblings().removeClass("active");
    //     teamFun();
    // })
    /* 鍖哄煙鍒囨崲 end */
    /*  */
    var erAboutNavUlwidth = 0;

    $(".er-about-nav-container li").each(function (index, element) {
        // console.log(Math.ceil($(element).innerWidth()));
        erAboutNavUlwidth += Math.ceil($(element).innerWidth() + 1);
    });
    $(".er-about-nav-container ul").css({
        "width": erAboutNavUlwidth + "px"
    });
    var lc_e_big_sw = new Swiper(".lc-e-big-sw", {
        loop: false,
        spaceBetween: 0,
        centeredSlides: false,
        initialSlide: 0,
        slidesPerView: 5,
        loopedSlides: 100,
        navigation: {
            nextEl: ".lc-e-big-sw-next",
            prevEl: ".lc-e-big-sw-prev",
        },
        breakpoints: {
            1600: {
                slidesPerView: 4,
                spaceBetween: 8,
            },
        }
    });
    // $(".lc-e-big-sw .swiper-slide").hover(function () {
    //     $(".lc-e-big-sw").css({
    //         overflow: 'visible'
    //     })
    // }, function () {
    //     $(".lc-e-big-sw").css({
    //         overflow: 'hidden'
    //     })
    // })
    /* 灏忓僵铔�  */
    /* 瀵艰埅 */
    // $('body').on("mouseenter", ".z-hd-d3", function (param) {
    //     $(this).find(".lc-nav-container").stop(true, true).slideDown(200);
    // })
    // $('body').on("mouseleave", ".z-hd-d3", function (param) {
    //     $(this).find(".lc-nav-container").stop(true, true).slideUp(200);
    // });

    // $(".lc-nav-container").eq(3).css({display:"block"})

    var lc_e_big_slide_text_container = [];
    for (var i = 0; i < 20; i++) {
        // var lc_e_big_slide_text_container =
        if ($('.lc-e-big-slide-text-container' + i)[0]) {
            new Swiper('.lc-e-big-slide-text-container' + i, {
                direction: 'vertical',
                slidesPerView: 'auto',
                freeMode: true,
                scrollbar: {
                    el: '.lc-e-swiper-scrollbar' + i,
                    hide: false,
                    draggable: true,
                    snapOnRelease: false,
                },
                mousewheel: true,
            });
        }
    }
    // $(".lc-nav-right-container .lc-ul").append('<div class="lc-nav-ul-top"></div>');
    // $(".lc-nav-right-container .lc-ul").append('<div class="lc-nav-ul-bottom"></div>');
    // $(".lc-nav-right-container .lc-ul .lc-li").hover(function () {
    //     var _parent = $(this).parents(".lc-ul");
    //     var _index = $(this).index();
    //     var _topElE = _parent.find(".lc-nav-ul-top");
    //     var _bottomElE = _parent.find(".lc-nav-ul-bottom");
    //     var _Height = _parent.height();
    //     var _topHeight = _index * 37 + 18;
    //     var _bottomHeight = _Height - _topHeight;
    //     _topElE.css({
    //         height: _topHeight + "px"
    //     })
    //     _bottomElE.css({
    //         height: _bottomHeight + "px"
    //     })
    // })
    // console.log($('.lc-e-big-slide-text-container' + i))
    $(".lc-e-big-sw").on("click", ".swiper-slide", function () {
        // var index = +$(this).attr("data-swiper-slide-index");
        var index = $(this).index();
        $(this).addClass("lc-active").siblings().removeClass("lc-active");
        $(".lc-e-big-slide").eq(index).addClass("lc-active fadeInDown").siblings().removeClass("lc-active fadeInDown");
    })

})