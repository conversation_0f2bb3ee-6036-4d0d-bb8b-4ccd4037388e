<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-14 13:51:34
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-05-27 15:38:33
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\branch\site.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonClient_Case extends JwpagefactoryAdd<PERSON>
{

    public function render()
    {
        $isk2installed = self::isComponentInstalled('client_case');

        if ($isk2installed === 0) {
            return '<div>出错了</div>';
        }

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        
            
        $output='';
        
        //Output
        if($style === 'style1') {
            $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : array();
            $server_name = $_SERVER['SERVER_NAME'];
            $output.='<div class="xc5-a1 wow fadeInUp animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">';
                $output.='<div class="style1-box clearfix">';
                    foreach ($jw_tab_item as $key => $item) {
                        $output.='<div class="xc5-a2">
                            <div class="xc5-a21">
                                <div class="xc5-a21a i300">
                                    <img src=\''.$item->img.'\'>
                                </div>
                                <div class="xc5-a21b">
                                    <img src=\''.$item->qr_code.'\'>
                                </div>
                            </div>
                            <div class="xc5-a22">'.$item->title.'</div>
                            <div class="xc5-a23">'.$item->intro.'</div>
                            <div class="xc5-a24">';
                                if($item->need_link === 1){
                                    if($item->link_page_select == 'outer'){
                                        $output.='<a class="advertising-main" href="'.$item->detail_page_url.'" target="'.$item->target.'">';
                                    }else{
                                        $url = '/index.php/component/jwpagefactory/?view=page';
                                        $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0).'&id='.base64_encode($item->detail_page_id);
                                        $output.='<a class="advertising-main" href=\''.$url.'\' target="'.$item->target.'">';
                                    }
                                }else{
                                    $output.='<a class="advertising-main" href="javascript:;">';
                                }
                                
                                    $output.='点击查看
                                </a>
                            </div>
                        </div>';
                    }
                $output.='</div>';
                $output.='
            </div>';
        }elseif($style === 'style2'){
            $jw_tab_item_style2 = (isset($settings->jw_tab_item_style2) && $settings->jw_tab_item_style2) ? $settings->jw_tab_item_style2 : array();
            $server_name = $_SERVER['SERVER_NAME'];
            $output.='<div class="list">';
                foreach ($jw_tab_item_style2 as $key => $item) {
                    $output.='<div class="item wow fadeInUp animated animated" data-wow-delay="0.2s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.2s;">
                        <div class="img box_img">
                            <img src=\''.$item->img.'\' alt="" srcset="">
                        </div>
                        <div class="text">
                            <em>'.(str_pad(($key + 1),2,"0",STR_PAD_LEFT)).'</em>
                            <h1>'.$item->title.'</h1>
                            <p>'.$item->intro.'</p>';
                            if($item->need_link === 1){
                                if($item->link_page_select == 'outer'){
                                    $output.='<a class="advertising-main" href="'.$item->detail_page_url.'" target="'.$item->target.'">';
                                }else{
                                    $url = '/index.php/component/jwpagefactory/?view=page';
                                    $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0).'&id='.base64_encode($item->detail_page_id);
                                    $output.='<a class="advertising-main" href=\''.$url.'\' target="'.$item->target.'">';
                                }
                            }else{
                                $output.='<a class="advertising-main" href="javascript:;">';
                            }
                            $output.='进一步了解</a>
                        </div>
                    </div>';
                }
            $output.='</div>';
        }

        return $output;
    }

    public function css(){
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        $output = '';
        if($style === 'style1') {
            $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : array();
            $count = count($jw_tab_item)>0 ? count($jw_tab_item) : 1;
            $output.=$addon_id.' .xc5-a2 {
                width: calc((100% - 30px*2)/3);
                background: #fff;
                float: left;
                margin-right: 30px;
                margin-top: 30px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
                padding-bottom: 34px;
                position: relative;
                min-width: 334px;
            }
            '.$addon_id.' .style1-box{
                width: auto;
                height: 100%;
            }
            '.$addon_id.' .xc5-a2::after{
                content: "";
                display: block;
                clear: both;
            }
            '.$addon_id.' *{
                margin: 0;
                padding: 0;
                color: inherit;
                font-size: inherit;
                font-weight: inherit;
            }
            '.$addon_id.' a:hover{
                color: inherit;
                text-decoration: none;
            }
            '.$addon_id.' .xc5-a1 .xc5-a2:nth-child(3n) {
                margin-right: 0;
            }
            '.$addon_id.' .xc5-a21 {
                width: 100%;
                height: auto;
                position: relative;
            }
            '.$addon_id.' .i300 {
                overflow: hidden;
            }
            '.$addon_id.' .xc5-a21a {
                width: 100%;
                height: 100%;
            }
            '.$addon_id.' .i300 > img {
                width: 100%;
                height: 100%;
            }
            '.$addon_id.' .xc5-a21b {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                margin: 0;
                background: rgba(0, 0, 0, 0.7);
                transform: scale(0);
                transition: 0.5s;
            }
            '.$addon_id.' .xc5-a21b img {
                width: 128px;
                height: 128px;
                position: absolute;
                top: calc(50% - 128px/2);
                left: calc(50% - 128px/2);
            }
            '.$addon_id.' .xc5-a22 {
                font-size: 18px;
                line-height: 18px;
                margin-top: 35px;
                padding-left: 30px;
            }
            '.$addon_id.' .xc5-a23 {
                font-size: 16px;
                line-height: 16px;
                padding-left: 30px;
                color: #999999;
                margin-top: 25px;
            }
            '.$addon_id.' .xc5-a24 {
                width: 138px;
                transition: 0.5s;
                height: 48px;
                border: 2px solid #d0111b;
                border-radius: 100px;
                position: absolute;
                bottom: 30px;
                right: 30px;
                line-height: 44px;
                text-align: center;
                color: #d0111b;
                font-size: 16px;
            }
            '.$addon_id.' .xc5-a2:hover .xc5-a21b {
                transform: scale(1);
                transition: 0.5s;
            }
            '.$addon_id.' .xc5-a24:hover {
                background: #d9012a;
                color: #fff;
                transition: 0.5s;
            }
            @media (max-width: 1599px) and (min-width: 1024px){
                '.$addon_id.' .xc5-a2 {
                    width: calc((100% - (960px*30/1560)*2)/3);
                    background: #fff;
                    float: left;
                    margin-right: calc(960px*30/1560);
                    margin-top: calc(960px*30/1560);
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
                    padding-bottom: calc(960px*34/1560);
                    position: relative;
                    min-width: 334px;
                }
                '.$addon_id.' .xc5-a21 {
                    width: 100%;
                    height: auto;
                    position: relative;
                }
                '.$addon_id.' .xc5-a22 {
                    font-size: calc(960px*18/1560);
                    line-height: calc(960px*18/1560);
                    margin-top: calc(960px*35/1560);
                    padding-left: calc(960px*30/1560);
                }
                '.$addon_id.' .xc5-a23 {
                    font-size: calc(960px*16/1560);
                    line-height: calc(960px*16/1560);
                    padding-left: calc(960px*30/1560);
                    color: #999999;
                    margin-top: calc(960px*25/1560);
                }
                '.$addon_id.' .xc5-a24 {
                    width: calc(960px*138/1560);
                    transition: 0.5s;
                    height: calc(960px*48/1560);
                    border: 2px solid #d0111b;
                    border-radius: 100px;
                    position: absolute;
                    bottom: calc(960px*30/1560);
                    right: calc(960px*30/1560);
                    line-height: calc(960px*44/1560);
                    text-align: center;
                    color: #d0111b;
                    font-size: calc(960px*16/1560);
                }
                '.$addon_id.' .xc5-a21b {
                    display: none;
                }
            }
            @media (max-width: 1023px){
                '.$addon_id.' .xc5-a1 {
                    width: 100%;
                    overflow: auto;
                }
                '.$addon_id.' .style1-box{
                    width: auto;
                    height: 100%;
                    display: flex;
                }
                '.$addon_id.' .xc5-a2 {
                    width: calc(100% - 4vw * 2);
                    background: #fff;
                    margin-left: 4vw;
                    box-shadow: 0 0 1.3vw rgba(0, 0, 0, 0.08);
                    padding-bottom: 6.7vw;
                    position: relative;
                    min-width: calc(100% - 4vw * 2);;
                }
                '.$addon_id.' .xc5-a21 {
                    width: 100%;
                    height: auto;
                    position: relative;
                }
                '.$addon_id.' .xc5-a24 {
                    display: none;
                }
                '.$addon_id.' .xc5-a21b {
                    display: none;
                }
            }';
        }elseif($style === 'style2'){
            if (isset($settings->list_height_style2) && $settings->list_height_style2) {
                if (is_object($settings->list_height_style2)) {
                    $list_height_style2 = $settings->list_height_style2->md ? $settings->list_height_style2->md.'vw' : 'auto';
                    $list_height_style2_sm = $settings->list_height_style2->sm ? $settings->list_height_style2->sm.'vw' : 'auto';
                    $list_height_style2_xs = $settings->list_height_style2->xs ? $settings->list_height_style2->xs.'vw' : 'auto';
                } else {
                    $list_height_style2 = $settings->list_height_style2 ? $settings->list_height_style2.'vw' : 'auto';
                    $list_height_style2_sm = $settings->list_height_style2_sm ? $settings->list_height_style2_sm.'vw' : 'auto';
                    $list_height_style2_xs = $settings->list_height_style2_xs ? $settings->list_height_style2_xs.'vw' : 'auto';
                }
            } else {
                $list_height_style2 = '29.3vw';
                $list_height_style2_sm = '34vw';
                $list_height_style2_xs = 'auto';
            }
            if (isset($settings->list_margin_bottom_style2) && $settings->list_margin_bottom_style2) {
                if (is_object($settings->list_margin_bottom_style2)) {
                    $list_margin_bottom_style2 = $settings->list_margin_bottom_style2->md;
                    $list_margin_bottom_style2_sm = $settings->list_margin_bottom_style2->sm;
                    $list_margin_bottom_style2_xs = $settings->list_margin_bottom_style2->xs;
                } else {
                    $list_margin_bottom_style2 = $settings->list_margin_bottom_style2;
                    $list_margin_bottom_style2_sm = $settings->list_margin_bottom_style2_sm;
                    $list_margin_bottom_style2_xs = $settings->list_margin_bottom_style2_xs;
                }
            } else {
                $list_margin_bottom_style2 = '4';
                $list_margin_bottom_style2_sm = '4';
                $list_margin_bottom_style2_xs = '4';
            }
            if (isset($settings->img_border_radius_style2) && $settings->img_border_radius_style2) {
                if (is_object($settings->img_border_radius_style2)) {
                    $img_border_radius_style2 = $settings->img_border_radius_style2->md;
                    $img_border_radius_style2_sm = $settings->img_border_radius_style2->sm;
                    $img_border_radius_style2_xs = $settings->img_border_radius_style2->xs;
                } else {
                    $img_border_radius_style2 = $settings->img_border_radius_style2;
                    $img_border_radius_style2_sm = $settings->img_border_radius_style2_sm;
                    $img_border_radius_style2_xs = $settings->img_border_radius_style2_xs;
                }
            } else {
                $img_border_radius_style2 = 0.6;
                $img_border_radius_style2_sm = 0.9;
                $img_border_radius_style2_xs = 0.9;
            }
            if (isset($settings->img_width_style2) && $settings->img_width_style2) {
                if (is_object($settings->img_width_style2)) {
                    $img_width_style2 = $settings->img_width_style2->md;
                    $img_width_style2_sm = $settings->img_width_style2->sm;
                    $img_width_style2_xs = $settings->img_width_style2->xs;
                } else {
                    $img_width_style2 = $settings->img_width_style2;
                    $img_width_style2_sm = $settings->img_width_style2_sm;
                    $img_width_style2_xs = $settings->img_width_style2_xs;
                }
            } else {
                $img_width_style2 = 50;
                $img_width_style2_sm = 50;
                $img_width_style2_xs = 100;
            }
            if (isset($settings->content_padding_style2) && $settings->content_padding_style2) {
                if (is_object($settings->content_padding_style2)) {
                    $content_padding_style2 = $settings->content_padding_style2->md;
                    $content_padding_style2_sm = $settings->content_padding_style2->sm;
                    $content_padding_style2_xs = $settings->content_padding_style2->xs;
                } else {
                    $content_padding_style2 = $settings->content_padding_style2;
                    $content_padding_style2_sm = $settings->content_padding_style2_sm;
                    $content_padding_style2_xs = $settings->content_padding_style2_xs;
                }
            } else {
                $content_padding_style2 = '7vw 0 0 0';
                $content_padding_style2_sm = '5vw 0 0 0';
                $content_padding_style2_xs = '0 0 0 0';
            }
            if (isset($settings->index_size_style2) && $settings->index_size_style2) {
                if (is_object($settings->index_size_style2)) {
                    $index_size_style2 = $settings->index_size_style2->md;
                    $index_size_style2_sm = $settings->index_size_style2->sm;
                    $index_size_style2_xs = $settings->index_size_style2->xs;
                } else {
                    $index_size_style2 = $settings->index_size_style2;
                    $index_size_style2_sm = $settings->index_size_style2_sm;
                    $index_size_style2_xs = $settings->index_size_style2_xs;
                }
            } else {
                $index_size_style2 = 8;
                $index_size_style2_sm = 8;
                $index_size_style2_xs = 8;
            }
            $index_color_style2 = (isset($settings->index_color_style2) && $settings->index_color_style2) ? $settings->index_color_style2 : '#999999';
            if (isset($settings->index_top_style2) && $settings->index_top_style2) {
                if (is_object($settings->index_top_style2)) {
                    $index_top_style2 = $settings->index_top_style2->md;
                    $index_top_style2_sm = $settings->index_top_style2->sm;
                    $index_top_style2_xs = $settings->index_top_style2->xs;
                } else {
                    $index_top_style2 = $settings->index_top_style2;
                    $index_top_style2_sm = $settings->index_top_style2_sm;
                    $index_top_style2_xs = $settings->index_top_style2_xs;
                }
            } else {
                $index_top_style2 = 4;
                $index_top_style2_sm = 2;
                $index_top_style2_xs = 0;
            }
            if (isset($settings->index_left_style2) && $settings->index_left_style2) {
                if (is_object($settings->index_left_style2)) {
                    $index_left_style2 = $settings->index_left_style2->md;
                    $index_left_style2_sm = $settings->index_left_style2->sm;
                    $index_left_style2_xs = $settings->index_left_style2->xs;
                } else {
                    $index_left_style2 = $settings->index_left_style2;
                    $index_left_style2_sm = $settings->index_left_style2_sm;
                    $index_left_style2_xs = $settings->index_left_style2_xs;
                }
            } else {
                $index_left_style2 = 0;
                $index_left_style2_sm = 0;
                $index_left_style2_xs = 0;
            }
            if (isset($settings->title_size_style2) && $settings->title_size_style2) {
                if (is_object($settings->title_size_style2)) {
                    $title_size_style2 = $settings->title_size_style2->md;
                    $title_size_style2_sm = $settings->title_size_style2->sm;
                    $title_size_style2_xs = $settings->title_size_style2->xs;
                } else {
                    $title_size_style2 = $settings->title_size_style2;
                    $title_size_style2_sm = $settings->title_size_style2_sm;
                    $title_size_style2_xs = $settings->title_size_style2_xs;
                }
            } else {
                $title_size_style2 = 2.4;
                $title_size_style2_sm = 3;
                $title_size_style2_xs = 4.5;
            }
            if (isset($settings->title_height_style2) && $settings->title_height_style2) {
                if (is_object($settings->title_height_style2)) {
                    $title_height_style2 = $settings->title_height_style2->md;
                    $title_height_style2_sm = $settings->title_height_style2->sm;
                    $title_height_style2_xs = $settings->title_height_style2->xs;
                } else {
                    $title_height_style2 = $settings->title_height_style2;
                    $title_height_style2_sm = $settings->title_height_style2_sm;
                    $title_height_style2_xs = $settings->title_height_style2_xs;
                }
            } else {
                $title_height_style2 = 1.7;
                $title_height_style2_sm = 1.7;
                $title_height_style2_xs = 1.7;
            }
            $title_color_style2 = (isset($settings->title_color_style2) && $settings->title_color_style2) ? $settings->title_color_style2 : '#000000';
            if (isset($settings->title_margin_style2) && $settings->title_margin_style2) {
                if (is_object($settings->title_margin_style2)) {
                    $title_margin_style2 = $settings->title_margin_style2->md;
                    $title_margin_style2_sm = $settings->title_margin_style2->sm;
                    $title_margin_style2_xs = $settings->title_margin_style2->xs;
                } else {
                    $title_margin_style2 = $settings->title_margin_style2;
                    $title_margin_style2_sm = $settings->title_margin_style2_sm;
                    $title_margin_style2_xs = $settings->title_margin_style2_xs;
                }
            } else {
                $title_margin_style2 = '0 0 0.3vw 0';
                $title_margin_style2_sm = '0 0 2vw 0';
                $title_margin_style2_xs = '10px 0 10px 0';
            }
            if (isset($settings->intro_size_style2) && $settings->intro_size_style2) {
                if (is_object($settings->intro_size_style2)) {
                    $intro_size_style2 = $settings->intro_size_style2->md;
                    $intro_size_style2_sm = $settings->intro_size_style2->sm;
                    $intro_size_style2_xs = $settings->intro_size_style2->xs;
                } else {
                    $intro_size_style2 = $settings->intro_size_style2;
                    $intro_size_style2_sm = $settings->intro_size_style2_sm;
                    $intro_size_style2_xs = $settings->intro_size_style2_xs;
                }
            } else {
                $intro_size_style2 = 1;
                $intro_size_style2_sm = 1.3;
                $intro_size_style2_xs = 3.4;
            }
            if (isset($settings->intro_line_height_style2) && $settings->intro_line_height_style2) {
                if (is_object($settings->intro_line_height_style2)) {
                    $intro_line_height_style2 = $settings->intro_line_height_style2->md;
                    $intro_line_height_style2_sm = $settings->intro_line_height_style2->sm;
                    $intro_line_height_style2_xs = $settings->intro_line_height_style2->xs;
                } else {
                    $intro_line_height_style2 = $settings->intro_line_height_style2;
                    $intro_line_height_style2_sm = $settings->intro_line_height_style2_sm;
                    $intro_line_height_style2_xs = $settings->intro_line_height_style2_xs;
                }
            } else {
                $intro_line_height_style2 = 1.5;
                $intro_line_height_style2_sm = 1.5;
                $intro_line_height_style2_xs = 1.5;
            }
            $intro_color_style2 = (isset($settings->intro_color_style2) && $settings->intro_color_style2) ? $settings->intro_color_style2 : '#999999';
            if (isset($settings->intro_width_style2) && $settings->intro_width_style2) {
                if (is_object($settings->intro_width_style2)) {
                    $intro_width_style2 = $settings->intro_width_style2->md;
                    $intro_width_style2_sm = $settings->intro_width_style2->sm;
                    $intro_width_style2_xs = $settings->intro_width_style2->xs;
                } else {
                    $intro_width_style2 = $settings->intro_width_style2;
                    $intro_width_style2_sm = $settings->intro_width_style2_sm;
                    $intro_width_style2_xs = $settings->intro_width_style2_xs;
                }
            } else {
                $intro_width_style2 = 80;
                $intro_width_style2_sm = 83;
                $intro_width_style2_xs = 100;
            }
            if (isset($settings->intro_height_style2) && $settings->intro_height_style2) {
                if (is_object($settings->intro_height_style2)) {
                    $intro_height_style2 = $settings->intro_height_style2->md;
                    $intro_height_style2_sm = $settings->intro_height_style2->sm;
                    $intro_height_style2_xs = $settings->intro_height_style2->xs;
                } else {
                    $intro_height_style2 = $settings->intro_height_style2;
                    $intro_height_style2_sm = $settings->intro_height_style2_sm;
                    $intro_height_style2_xs = $settings->intro_height_style2_xs;
                }
            } else {
                $intro_height_style2 = 39;
                $intro_height_style2_sm = 13;
                $intro_height_style2_xs = 'auto';
            }
            if (isset($settings->intro_margin_style2) && $settings->intro_margin_style2) {
                if (is_object($settings->intro_margin_style2)) {
                    $intro_margin_style2 = $settings->intro_margin_style2->md;
                    $intro_margin_style2_sm = $settings->intro_margin_style2->sm;
                    $intro_margin_style2_xs = $settings->intro_margin_style2->xs;
                } else {
                    $intro_margin_style2 = $settings->intro_margin_style2;
                    $intro_margin_style2_sm = $settings->intro_margin_style2_sm;
                    $intro_margin_style2_xs = $settings->intro_margin_style2_xs;
                }
            } else {
                $intro_margin_style2 = '0 0 20px 0';
                $intro_margin_style2_sm = '0 0 20px 0';
                $intro_margin_style2_xs = '0 0 0 0';
            }
            if (isset($settings->intro_margin_even_style2) && $settings->intro_margin_even_style2) {
                if (is_object($settings->intro_margin_even_style2)) {
                    $intro_margin_even_style2 = $settings->intro_margin_even_style2->md;
                    $intro_margin_even_style2_sm = $settings->intro_margin_even_style2->sm;
                    $intro_margin_even_style2_xs = $settings->intro_margin_even_style2->xs;
                } else {
                    $intro_margin_even_style2 = $settings->intro_margin_even_style2;
                    $intro_margin_even_style2_sm = $settings->intro_margin_even_style2_sm;
                    $intro_margin_even_style2_xs = $settings->intro_margin_even_style2_xs;
                }
            } else {
                $intro_margin_even_style2 = '0 0 20px auto';
                $intro_margin_even_style2_sm = '0 0 20px auto';
                $intro_margin_even_style2_xs = '0 0 0 0';
            }
            if (isset($settings->more_width_style2) && $settings->more_width_style2) {
                if (is_object($settings->more_width_style2)) {
                    $more_width_style2 = $settings->more_width_style2->md;
                    $more_width_style2_sm = $settings->more_width_style2->sm;
                    $more_width_style2_xs = $settings->more_width_style2->xs;
                } else {
                    $more_width_style2 = $settings->more_width_style2;
                    $more_width_style2_sm = $settings->more_width_style2_sm;
                    $more_width_style2_xs = $settings->more_width_style2_xs;
                }
            } else {
                $more_width_style2 = 10.52;
                $more_width_style2_sm = 15;
                $more_width_style2_xs = 0;
            }
            if (isset($settings->more_height_style2) && $settings->more_height_style2) {
                if (is_object($settings->more_height_style2)) {
                    $more_height_style2 = $settings->more_height_style2->md;
                    $more_height_style2_sm = $settings->more_height_style2->sm;
                    $more_height_style2_xs = $settings->more_height_style2->xs;
                } else {
                    $more_height_style2 = $settings->more_height_style2;
                    $more_height_style2_sm = $settings->more_height_style2_sm;
                    $more_height_style2_xs = $settings->more_height_style2_xs;
                }
            } else {
                $more_height_style2 = 3.6;
                $more_height_style2_sm = 5;
                $more_height_style2_xs = 0;
            }
            $more_border_color_style2 = (isset($settings->more_border_color_style2) && $settings->more_border_color_style2) ? $settings->more_border_color_style2 : '#949494';
            $more_bg_color_style2 = (isset($settings->more_bg_color_style2) && $settings->more_bg_color_style2) ? $settings->more_bg_color_style2 : '#fff';
            $more_font_color_style2 = (isset($settings->more_font_color_style2) && $settings->more_font_color_style2) ? $settings->more_font_color_style2 : '#363636';
            $more_border_hover_color_style2 = (isset($settings->more_border_hover_color_style2) && $settings->more_border_hover_color_style2) ? $settings->more_border_hover_color_style2 : '#949494';
            $more_bg_color_hover_style2 = (isset($settings->more_bg_color_hover_style2) && $settings->more_bg_color_hover_style2) ? $settings->more_bg_color_hover_style2 : '#949494';
            $more_font_color_hover_style2 = (isset($settings->more_font_color_hover_style2) && $settings->more_font_color_hover_style2) ? $settings->more_font_color_hover_style2 : '#fff';
            if (isset($settings->more_font_size_style2) && $settings->more_font_size_style2) {
                if (is_object($settings->more_font_size_style2)) {
                    $more_font_size_style2 = $settings->more_font_size_style2->md;
                    $more_font_size_style2_sm = $settings->more_font_size_style2->sm;
                    $more_font_size_style2_xs = $settings->more_font_size_style2->xs;
                } else {
                    $more_font_size_style2 = $settings->more_font_size_style2;
                    $more_font_size_style2_sm = $settings->more_font_size_style2_sm;
                    $more_font_size_style2_xs = $settings->more_font_size_style2_xs;
                }
            } else {
                $more_font_size_style2 = 1;
                $more_font_size_style2_sm = 1.4;
                $more_font_size_style2_xs = 0;
            }
            if (isset($settings->more_radius_style2) && $settings->more_radius_style2) {
                if (is_object($settings->more_radius_style2)) {
                    $more_radius_style2 = $settings->more_radius_style2->md;
                    $more_radius_style2_sm = $settings->more_radius_style2->sm;
                    $more_radius_style2_xs = $settings->more_radius_style2->xs;
                } else {
                    $more_radius_style2 = $settings->more_radius_style2;
                    $more_radius_style2_sm = $settings->more_radius_style2_sm;
                    $more_radius_style2_xs = $settings->more_radius_style2_xs;
                }
            } else {
                $more_radius_style2 = 1.8;
                $more_radius_style2_sm = 5;
                $more_radius_style2_xs = 0;
            }
            if (isset($settings->more_margin_style2) && $settings->more_margin_style2) {
                if (is_object($settings->more_margin_style2)) {
                    $more_margin_style2 = $settings->more_margin_style2->md;
                    $more_margin_style2_sm = $settings->more_margin_style2->sm;
                    $more_margin_style2_xs = $settings->more_margin_style2->xs;
                } else {
                    $more_margin_style2 = $settings->more_margin_style2;
                    $more_margin_style2_sm = $settings->more_margin_style2_sm;
                    $more_margin_style2_xs = $settings->more_margin_style2_xs;
                }
            } else {
                $more_margin_style2 = '0 0 0 0';
                $more_margin_style2_sm = '0 0 0 0';
                $more_margin_style2_xs = '0 0 0 0';
            }
            if (isset($settings->more_margin_even_style2) && $settings->more_margin_even_style2) {
                if (is_object($settings->more_margin_even_style2)) {
                    $more_margin_even_style2 = $settings->more_margin_even_style2->md;
                    $more_margin_even_style2_sm = $settings->more_margin_even_style2->sm;
                    $more_margin_even_style2_xs = $settings->more_margin_even_style2->xs;
                } else {
                    $more_margin_even_style2 = $settings->more_margin_even_style2;
                    $more_margin_even_style2_sm = $settings->more_margin_even_style2_sm;
                    $more_margin_even_style2_xs = $settings->more_margin_even_style2_xs;
                }
            } else {
                $more_margin_even_style2 = '0 0 0 auto';
                $more_margin_even_style2_sm = '0 0 0 auto';
                $more_margin_even_style2_xs = '0 0 0 auto';
            }

            $output .= $addon_id.' .list .item {
                height: '.$list_height_style2.';
                position: relative;
                padding-right: '.$img_width_style2.'%;
                margin-bottom: '.$list_margin_bottom_style2.'vw;
            }
            '.$addon_id.' *{
                margin: 0;
                padding: 0;
                color: inherit;
                font-size: inherit;
                font-weight: inherit;
            }
            '.$addon_id.' a:hover{
                color: inherit;
                text-decoration: none;
            }
            '.$addon_id.' .list .item .img {
                position: absolute;
                width: '.$img_width_style2.'%;
                right: 0;
                top: 0;
                height: 100%;
                overflow: hidden;
                background-color: #fff;
                border-radius: '.$img_border_radius_style2.'vw;
            }
            '.$addon_id.' .box_img img {
                width: auto;
                max-height: 100%;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                display: block;
                transition: 0.5s;
            }
            '.$addon_id.' .list .item .text {
                height: 100%;
                width: 100%;
                padding: '.$content_padding_style2.';
                position: relative;
            }
            '.$addon_id.' .list .item .text em {
                line-height: 1;
                font-size: '.$index_size_style2.'vw;
                color: '.$index_color_style2.';
                display: inline-block;
                font-family: Impact, Haettenschweiler, "Arial Narrow Bold", sans-serif;
                position: absolute;
                overflow: hidden;
                z-index: 1;
                top: '.$index_top_style2.'vw;
                left: '.$index_left_style2.'vw;
                font-style: normal;
            }
            '.$addon_id.' .list .item .text em::after {
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                top: 0;
                content: "";
                background-image: linear-gradient(rgba(255, 255, 255, 0.79), #ffffff, #ffffff);
            }
            '.$addon_id.' .list .item .text h1 {
                font-size: '.$title_size_style2.'vw;
                line-height: '.$title_height_style2.';
                color: '.$title_color_style2.';
                margin: '.$title_margin_style2.';
                position: relative;
                z-index: 2;
                font-weight: bold;
            }
            '.$addon_id.' .list .item .text p {
                font-size: '.$intro_size_style2.'vw;
                line-height: '.$intro_line_height_style2.';
                color: '.$intro_color_style2.';
                width: '.$intro_width_style2.'%;
                max-height: '.$intro_height_style2.'vw;
                position: relative;
                z-index: 2;
                overflow: hidden;
                margin: '.$intro_margin_style2.';
            }
            '.$addon_id.' .list .item .text a {
                display: block;
                width: '.$more_width_style2.'vw;
                height: '.$more_height_style2.'vw;
                border: 1px solid '.$more_border_color_style2.';
                background: '.$more_bg_color_style2.';
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: '.$more_font_size_style2.'vw;
                color: '.$more_font_color_style2.';
                line-height: 1;
                border-radius: '.$more_radius_style2.'vw;
                transition: 0.5s;
                margin: '.$more_margin_style2.';
            }
            '.$addon_id.' .list .item .text a:hover {
                border: 1px solid '.$more_border_hover_color_style2.';
                background-color: '.$more_bg_color_hover_style2.';
                color: '.$more_font_color_hover_style2.';
            }
            '.$addon_id.' .list .item:nth-child(2n) {
                padding-left: '.$img_width_style2.'%;
                padding-right: 0;
            }
            '.$addon_id.' .list .item:nth-child(2n) .img {
                left: 0;
                right: auto;
            }
            '.$addon_id.' .list .item:nth-child(2n) .text {
                text-align: right;
            }
            '.$addon_id.' .list .item:nth-child(2n) .text em {
                right: 0;
            }
            '.$addon_id.' .list .item:nth-child(2n) .text p {
                margin: '.$intro_margin_even_style2.';
            }
            '.$addon_id.' .list .item:nth-child(2n) .text a {
                margin: '.$more_margin_even_style2.';
            }
            @media (max-width: 1258px) and (min-width: 992px) {
                '.$addon_id.' .list .item {
                    height: '.$list_height_style2_sm.';
                    margin-bottom: '.$list_margin_bottom_style2_sm.'vw;
                    padding-right: '.$img_width_style2_sm.'%;
                }
                '.$addon_id.' .list .item .img {
                    width: '.$img_width_style2_sm.'%;
                    border-radius: '.$img_border_radius_style2_sm.'vw;
                }
                '.$addon_id.' .list .item .text {
                    padding: '.$content_padding_style2_sm.';
                }
                '.$addon_id.' .list .item .text em {
                    top: '.$index_top_style2_sm.'vw;
                    left: '.$index_left_style2_sm.'vw;
                    font-size: '.$index_size_style2_sm.'vw;
                }
                '.$addon_id.' .list .item .text h1 {
                    font-size: '.$title_size_style2_sm.'vw;
                    line-height: '.$title_height_style2_sm.';
                    margin: '.$title_margin_style2_sm.';
                }
                '.$addon_id.' .list .item .text p {
                    height: auto;
                    margin: '.$intro_margin_style2_sm.';
                    line-height: '.$intro_line_height_style2_sm.';
                    width: '.$intro_width_style2_sm.'%;
                    max-height: '.$intro_height_style2_sm.'vw;
                    font-size: '.$intro_size_style2_sm.'vw;
                }
                '.$addon_id.' .list .item .text a{
                    width: '.$more_width_style2_sm.'vw;
                    height: '.$more_height_style2_sm.'vw;
                    font-size: '.$more_font_size_style2_sm.'vw;
                    border-radius: '.$more_radius_style2_sm.'vw;
                    margin: '.$more_margin_style2_sm.';
                }
                '.$addon_id.' .list .item:nth-child(2n) {
                    padding-left: '.$img_width_style2_sm.'%;
                    padding-right: 0;
                }
                '.$addon_id.' .list .item:nth-child(2n) .text p {
                    margin: '.$intro_margin_even_style2_sm.';
                }
                '.$addon_id.' .list .item:nth-child(2n) .text a {
                    margin: '.$more_margin_even_style2_sm.';
                }
            }
            @media (max-width: 991px){
                '.$addon_id.' .list .item {
                    height: '.$list_height_style2_xs.';
                    margin-bottom: '.$list_margin_bottom_style2_xs.'vw;
                    position: relative;
                    padding: 0 !important;
                }
                '.$addon_id.' .list .item .img {
                    position: relative;
                    width: '.$img_width_style2_xs.'%;
                    right: auto;
                    top: auto;
                    margin: auto;
                    height: auto;
                    overflow: hidden;
                    background-color: #fff;
                    border-radius: '.$img_border_radius_style2_xs.'vw;
                }
                '.$addon_id.' .list .item .img img {
                    width: 100%;
                    height: auto;
                    position: relative;
                    left: auto;
                    top: auto;
                    transform: translate(0, 0);
                    display: block;
                    transition: 0.5s;
                }
                '.$addon_id.' .list .item .text {
                    height: auto;
                    width: 100%;
                    padding: '.$content_padding_style2_xs.';
                    position: relative;
                    max-height: none;
                }
                '.$addon_id.' .list .item:nth-child(2n) .text p {
                    height: auto;
                    width: 100%;
                    padding-top: 0;
                    position: relative;
                    max-height: none;
                    margin: 0;
                }
                '.$addon_id.' .list .item .text em {
                    display: none;
                }
                '.$addon_id.' .list .item .text h1 {
                    font-size: '.$title_size_style2_xs.'vw;
                    line-height: '.$title_height_style2_xs.';
                    margin: '.$title_margin_style2_xs.';
                }
                '.$addon_id.' .list .item .text p {
                    font-size: '.$intro_size_style2_xs.'vw;
                    line-height: '.$intro_line_height_style2_xs.';
                    width: '.$intro_width_style2_xs.'%;
                    margin: '.$intro_margin_style2_xs.';
                    height: auto;
                    position: relative;
                    z-index: 2;
                }
                '.$addon_id.' .list .item .text a {
                    display: none;
                }
                '.$addon_id.' .list .item:nth-child(2n) .text{
                    text-align: left;
                }
            }';
        }
        return $output;
    }

    public function js(){
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'style1';
        $output = '';
        if($style === 'style1') {
            $output.='jQuery(document).ready(function($){
            })';
        }
        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }

}
