<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 14:00:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonRight_nclick extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 指定文章详情页ID
        $url='';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        //include k2 helper
        $k2helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/k2.php';
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
//        $output .="<div class='r_jn5689265'></div>";
        $output .= '<script>
                 window.oncontextmenu=function(e){
                            //取消默认的浏览器自带右键
                            e.preventDefault();
                        }
//                         jQuery(document).ready(function(){
//                            jQuery(".r_jn5689265").parents(".jwpf-section").css("display","none");
//
//                         })          
                </script>';

        return $output;
    }

    public function css()
    {
        $css = '';
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $prod_id = '#jwpf-addon-wrapper-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        // $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';
        $css .= $prod_id . '{ ';
        $css .= 'margin:0px !important;';
        $css .= 'padding:0px !important';
        $css .= '}';
        return $css;
        // return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        return '<div>本段文字用于禁止页面右键编辑模式下占位，预览模式下不显示</div>';
    }
}