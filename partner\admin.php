<?php

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'partner',
         'title' => '合作伙伴',
         'desc' => '',
         'category' => '龙采官网插件',
         'attr' => array(
             'general' => array(
                 'site_id' => array(
                     'std' => $site_id,
                 ),
                 'company_id' => array(
                     'std' => $company_id,
                 ),
                'adv_typeid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择后台对应的广告分类'),
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_advertise')['list'],
                ),
                'img_zt'      => array(
                    'type'    => 'select',
                    'title'   => '图片排版',
                    'values'  => array(
                        'qx'  => '倾斜',
                        'zc'  => '正常',
                        'scroll'  => '滚动',
                    ),
                    'std' => 'qx',
                ),
                'img_colm' => array(
                    'type' => 'slider',
                    'title' => '一行显示的数量',
                    'max' => '10',
                    'responsive' => true,
                    'std'        => array('md' => '7', 'sm' => '7', 'xs' => '3'),
                ),
                'sj_zong'   => array(
                    'type'    => 'slider',
                    'title'   => JText::_('手机每行总共的数量'),
                    'std'     => 5,
                    'depends' => array(array('img_zt', '=', 'qx')),
                ),
                'dizz'   => array(
                    'type'    => 'checkbox',
                    'title'   => JText::_('开启底部遮罩'),
                    'std'     => 0,
                    'depends' => array(array('img_zt', '!=', 'scroll')),
                ),
                // 图片宽度
                'img_width_scroll' => array(
                    'type' => 'slider',
                    'title' => '图片宽度',
                    'max' => '500',
                    'responsive' => true,
                    'std'        => array('md' => '160', 'sm' => '160', 'xs' => '160'),
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
                // 图片高度
                'img_height_scroll' => array(
                    'type' => 'slider',
                    'title' => '图片高度',
                    'max' => '500',
                    'responsive' => true,
                    'std'        => array('md' => '90', 'sm' => '90', 'xs' => '90'),
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
                // 图片显示方式
                'img_show_type_scroll' => array(
                    'type' => 'select',
                    'title' => '图片显示方式',
                    'values' => array(
                        'cover'  => '超出裁剪',
                        'contain'  => '保留宽高比，留白显示',
                        'fill'  => '占满拉伸',
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
                // 是否显示两侧遮罩
                'img_mask_scroll' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示两侧遮罩',
                    'std' => 1,
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
                // 动画时长
                'img_time_scroll' => array(
                    'type' => 'slider',
                    'title' => '动画时长（秒）',
                    'max' => '50',
                    'std' => array('md' => '16', 'sm' => '16', 'xs' => '10'),
                    'responsive' => true,
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
                // 行间距
                'img_row_gap_scroll' => array(
                    'type' => 'slider',
                    'title' => '行间距',
                    'max' => '200',
                    'std' => array('md' => '30', 'sm' => '30', 'xs' => '20'),
                    'responsive' => true,
                    'depends' => array(
                        'img_zt' => array('scroll'),
                    ),
                ),
             ),
         ),
     )
 );
