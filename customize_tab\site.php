<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonCustomize_tab extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $nav_font_position = (isset($settings->nav_font_position) && $settings->nav_font_position) ? $settings->nav_font_position : 'flex-start';//文本居中
        $art_type_selector_tab = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : 'type1';//导航样式
        $nav_bor_m = (isset($settings->nav_bor_m) && $settings->nav_bor_m) ? $settings->nav_bor_m : '';//导航样式


        //Output
        $output = '';

        if ($art_type_selector_tab == 'type1') {
            //导航
            $output .= "
                <style>
                    {$addon_id} .page_n{
                        float: left;
                        width: 100px;
                        height: 100px;
                        list-style: none;
                        text-align:center;
                        margin-right: {$nav_bor_m};
                    }
                    {$addon_id} .page_n::before{
                        position: absolute;
                        content: \"\";
                        width: 0;
                        height: 0;
                        border-top: 10px solid #3369ff;
                        border-left: 10px solid transparent;
                        border-right: 10px solid transparent;
                        left: 50%;
                        transform: translateX(-50%);
                        bottom: -10px;
                        display: none;
                    }
                    {$addon_id} .page_n.active::before{
                    display: block;
                    }
                    {$addon_id} .page_n.active{
                        background-image: linear-gradient(-50deg, #2e62ff 0%, #5697ff 100%), linear-gradient( #3369ff, #3369ff);
                        box-shadow: 0px 3px 9px 0px rgb(52 105 255 / 20%);
                        transform: translateY(-2px);
                        border-radius:10px; 
                    }
                    
                    {$addon_id} .page_n  a{
                        position: relative;
                        bottom: -55px;
                        pointer-events:none;
                    }
                    {$addon_id} .page_n  h2 {
                        pointer-events:none;
                        display: flex;
                        justify-content: center;

                    }
                    {$addon_id} .page_n.active  h2 .img1{
                        display: block;
                    }
                    {$addon_id} .page_n.active  h2 .img2{
                        display: none;
                    }
                    {$addon_id} .page_n  h2 .img1{
                        display: none;
                        pointer-events:none;
                    }
                    {$addon_id} .page_n  h2 .img2{
                        pointer-events:none;
                    }
                    {$addon_id} .page_n  h2 img{
                         width: 34px;
                         height: 34px;
                    }
                    @media (max-width: 768px){
                    {$addon_id}.jwpf_tab_cust {
                        display: none;
                    }
                 }
                </style>
            ";
            if ($settings->nav_position == 'flex-start') {
                $output .= "
                    <style>
                        {$addon_id} .jwpf_tab_nav{
                            display: flex;
                            justify-content: {$settings->nav_position};
                                margin-left: 301px;
                        }
                    </style>
                ";
            } else {
                $output .= "
                    <style>
                        {$addon_id} .jwpf_tab_nav{
                            display: flex;
                            justify-content: {$settings->nav_position};
                        }
                    </style>
                ";
            }
            //导航
            $output .= '<div class="jwpf_tab_cust">';
            $output .= '<ul class="jwpf_tab_nav" role="tablist">';
            foreach ($settings->jw_tab_item_cust as $key => $tab) {

                $output .= '<li data-id="ct-' . $key . '" class=" page_n ">';

                $output .= '<a title="' . $tab->title . '">' . $tab->title . ' </a>';
                $output .= '<h2><img class="img1" src="' . $tab->image1 . '"/><img class="img2" src="' . $tab->image2 . '"/></h2>';

                $output .= '</li>';

            }
            $output .= '</ul>';
            $output .= '</div>';


            //内容

            //内容css
            $output .= "
            <style>
                {$addon_id} .web_tab{
                    width: 1903px;
                    position: relative;
                    top: 20px;
                    display: none;
                }
                {$addon_id} .web_fun_m1{
                    
                    height: 414px;
                    background-size:100% 414px ;
                }
                {$addon_id} .web_fun_info1{
                    float: left;
                    position: relative;
                    top: 30%;
                    left: 15%;
                    margin-right: 20px;
                    width:400px;
                
                }
                {$addon_id} .web_fun_img1{
                    width: 452px;
                    height: 262px;
                    position: relative;
                    left: 32%;
                    top: 60px;
                    float: left;
                }
                {$addon_id} .web_fun_dian1 span{
                    margin-right: 10px;
                    height: 5px;
                    float: left;
                    width: 5px;
                    background: #0a88ff;
                    display: block;
                }
                 {$addon_id} .web_fun_img1 img{
                    width: 452px;
                    height: 262px;
                    position: absolute;
                    z-index: 2;
                        border-radius: 10px;
                 }
                 @media (max-width: 768px){
                    {$addon_id}.web_fun_m1 {
                        display: none;
                    }
                 }
            </style>";
            foreach ($settings->content as $key => $tab) {
                $tit_cle = (isset($tab->tit_cle) && $tab->tit_cle) ? $tab->tit_cle : '';//标题颜色
                $text_cle = (isset($tab->text_cle) && $tab->text_cle) ? $tab->text_cle : '';//简介颜色
                $zs_img_cle = (isset($tab->zs_img_cle) && $tab->zs_img_cle) ? $tab->zs_img_cle : '';//展示图片阴影颜色
                $bg_img = (isset($tab->bg_img) && $tab->bg_img) ? $tab->bg_img : '';//背景图片
                $zs_img = (isset($tab->zs_img) && $tab->zs_img) ? $tab->zs_img : '';//展示图片
                $title = (isset($tab->title) && $tab->title) ? $tab->title : '';//标题
                $desc1 = (isset($tab->desc1) && $tab->desc1) ? $tab->desc1 : '';//简介
                $tit_size = (isset($tab->tit_size) && $tab->tit_size) ? $tab->tit_size : '';//标题大小
                $size = (isset($tab->size) && $tab->size) ? $tab->size : '';//简介大小

                //内容css
                $output .= "
            <style>
                
               {$addon_id} .web_fun_img1::before {
                    position: absolute;
                    content: '';
                    width: 100%;
                    height: 100%;
                    right: -10px;
                    bottom: -10px;
                    background-color:$zs_img_cle;
                    border-radius: 10px;
                }
                {$addon_id} .title{
                    font-size: {$tit_size}px;
                    color: {$tit_cle};
                }
                {$addon_id} .txt{
                    font-size: {$size}px;
                    color: {$text_cle};
                }
                
                {$addon_id}  #ct-{$key}.active{
                    display: block;
                }
            </style>
            ";


                $output .= '<div class="web_tab  ' . (($key == 0) ? "active" : "") . '" id="ct-' . $key . '">';
                $output .= '						<div class="web_fun_m1 " style="background: url(' . $bg_img . ');background-size:1903px 414px;">';
                $output .= '										<div class="web_fun_info1">';
                $output .= '											<div class="title">' . $title . '</div><br>';
                $output .= '											<div class="web_fun_dian1">';
                $output .= '												<span></span><span></span><span></span>';
                $output .= '										    </div>';
                $output .= '										    <br>';
                $output .= '											<div class="txt">' . $desc1 . '</div>';
                $output .= '										</div>';
                $output .= '										<div class="web_fun_img1">';
                $output .= '											<img src="' . $zs_img . '">';
                $output .= '										</div>';
                $output .= '						</div>';
                $output .= '						</div>';
            }

            $output .= "
            <script>
                jQuery('{$addon_id} .page_n').hover(function (){
                    jQuery(this).siblings().removeClass(\"active\")  
                    jQuery(this).addClass('active');
                    var id =jQuery(this).attr('data-id');
                    jQuery('{$addon_id} #'+id).addClass('active').siblings().removeClass('active');
                });
            </script>
            ";

        }
        if ($art_type_selector_tab == 'type2') {
            $bg_img = (isset($settings->bg_img) && $settings->bg_img) ? $settings->bg_img : '';//背景图片
            $tab_active_bg_type2 = (isset($settings->tab_active_bg_type2) && $settings->tab_active_bg_type2) ? $settings->tab_active_bg_type2 : 'https://oss.lcweb01.cn/joomla/20221021/91f26b6816681dd50419004594c50580.png';//背景图片

            if (isset($settings->tab_padding_top_type2) && $settings->tab_padding_top_type2) {
                if (is_object($settings->tab_padding_top_type2)) {
                    $tab_padding_top_type2 = $settings->tab_padding_top_type2->md;
                    $tab_padding_top_type2_sm = $settings->tab_padding_top_type2->sm;
                    $tab_padding_top_type2_xs = $settings->tab_padding_top_type2->xs;
                } else {
                    $tab_padding_top_type2 = $settings->tab_padding_top_type2;
                    $tab_padding_top_type2_sm = $settings->tab_padding_top_type2_sm;
                    $tab_padding_top_type2_xs = $settings->tab_padding_top_type2_xs;
                }
            } else {
                $tab_padding_top_type2 = 60;
                $tab_padding_top_type2_sm = 60;
                $tab_padding_top_type2_xs = 50;
            }
            $jw_tab_item_cust = (isset($settings->jw_tab_item_cust) && $settings->jw_tab_item_cust) ? $settings->jw_tab_item_cust : array();
            $jw_tab_item_cust_nums = count($jw_tab_item_cust) > 0 ? count($jw_tab_item_cust) : 1;
            if (isset($settings->tab_height_type2) && $settings->tab_height_type2) {
                if (is_object($settings->tab_height_type2)) {
                    $tab_height_type2 = $settings->tab_height_type2->md;
                    $tab_height_type2_sm = $settings->tab_height_type2->sm;
                    $tab_height_type2_xs = $settings->tab_height_type2->xs;
                } else {
                    $tab_height_type2 = $settings->tab_height_type2;
                    $tab_height_type2_sm = $settings->tab_height_type2_sm;
                    $tab_height_type2_xs = $settings->tab_height_type2_xs;
                }
            } else {
                $tab_height_type2 = 149;
                $tab_height_type2_sm = 168;
                $tab_height_type2_xs = 168;
            }
            if (isset($settings->tab_padding_type2) && $settings->tab_padding_type2) {
                if (is_object($settings->tab_padding_type2)) {
                    $tab_padding_type2 = $settings->tab_padding_type2->md;
                    $tab_padding_type2_sm = $settings->tab_padding_type2->sm;
                    $tab_padding_type2_xs = $settings->tab_padding_type2->xs;
                } else {
                    $tab_padding_type2 = $settings->tab_padding_type2;
                    $tab_padding_type2_sm = $settings->tab_padding_type2_sm;
                    $tab_padding_type2_xs = $settings->tab_padding_type2_xs;
                }
            } else {
                $tab_padding_type2 = '30px 0';
                $tab_padding_type2_sm = '12px 6px';
                $tab_padding_type2_xs = '12px 6px';
            }
            if (isset($settings->tab_active_height_type2) && $settings->tab_active_height_type2) {
                if (is_object($settings->tab_active_height_type2)) {
                    $tab_active_height_type2 = $settings->tab_active_height_type2->md;
                    $tab_active_height_type2_sm = $settings->tab_active_height_type2->sm;
                    $tab_active_height_type2_xs = $settings->tab_active_height_type2->xs;
                } else {
                    $tab_active_height_type2 = $settings->tab_active_height_type2;
                    $tab_active_height_type2_sm = $settings->tab_active_height_type2_sm;
                    $tab_active_height_type2_xs = $settings->tab_active_height_type2_xs;
                }
            } else {
                $tab_active_height_type2 = 175;
                $tab_active_height_type2_sm = 198;
                $tab_active_height_type2_xs = 198;
            }

            $output .= "
            <style>
                {$addon_id} .wrip{
                    height: 850px;
                    background-image: url(\"{$bg_img}\");
                }
                {$addon_id} .container{
                    background: transparent;
                    border: none;
                    box-shadow: none;
                    width: 100%;
                    padding: 0;
                }
                {$addon_id} .presentation {
                    padding-top: {$tab_padding_top_type2}px;
                }
                {$addon_id} .presentation .anv .anv_li{
                    list-style-type: none;
                    float: left;
                    padding: {$tab_padding_type2};
                    width: calc(100%/{$jw_tab_item_cust_nums});/*选项卡宽度（100%除以选项卡个数）*/
                    cursor: pointer;
                    height: {$tab_height_type2}px;
                }
                {$addon_id} .presentation .anv .anv_li:hover,
                {$addon_id} .presentation .anv .anv_li.active{
                    height: {$tab_active_height_type2}px;/*选项卡选中高度*/
                    /*选项卡选中背景：背景色  背景图片*/
                    background: transparent url({$tab_active_bg_type2}) no-repeat center/cover;
                }
                {$addon_id} .presentation .anv li h3{
                    font-size: 19px;
                    margin: 0;
                    line-height: 40px;
                    letter-spacing: 2px;
                    font-weight: 700;
                }
            
                {$addon_id} .presentation .anv ul {
                    position: static;
                    width: 100%;
                    padding-left: 0;
                    margin: 0;
                }
                {$addon_id} .presentation .anv ul::after{
                    content: '';
                    display: block;
                    clear: both;
                  }
                {$addon_id} .eshop_label1{
                    position: absolute;
                    left: 50%;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    transform: translate(-50%,0%);
                }
                {$addon_id} .presentation .anv li .nav_piece{
                    position: relative;
                    display: flex;
                }
                {$addon_id} .presentation .anv li .nav_piece .nav_icon{
                    float: left;
                    margin: 5px 0;
                    padding: 0px 19px 0 31px;
                }
                {$addon_id} .presentation .anv li .nav_piece img{
                    width: 65px;
                    min-width: 65px;
                    max-width: 65px;
                }
                {$addon_id} .nav_right{
                    float: left;
                    
                    padding-right: 20px;
                }
                {$addon_id} .border_r{
                    border-right: #2C2D35 solid 1px;
                    width: 1px;
                    height: 53px;
                    float: right;
                    margin-top: 8px;
                }
                {$addon_id} .nav_right>h3 {
                    margin: 0;
                    line-height: 29px;
                    color: #fff;
                }
                {$addon_id} .nav_right>span{
                    font-size: 14px;
                    letter-spacing: 4px;
                    color: #fff;
                }
                .anv{
                    margin-bottom
                }
                
                
            
                /*内容部分*/
            
                {$addon_id} .presentation .content{
                    position: relative;
                    top: 0;
                    left: 0;
                }
                {$addon_id} .presentation .content ul {
                    margin-left: 0;
                    padding-left: 0;
                }            
                {$addon_id} .presentation .content li {
                    list-style-type: none;
                    width: 500px;
                    padding: 35px 0;
                    margin-bottom: 10px;
                }
                {$addon_id} .presentation li .content_piece{
                    padding-left: 20px;
                    width: 374px;
                    display: flex;
                }
                {$addon_id} .presentation li .content_icon{
                    width: 65px;
                    float: left;
                    position: relative;
                    top: 2px;
                    min-width: 65px;
                    max-width: 65px;
                    margin-right: 20px;/*列表图标右边距*/
                }
                {$addon_id} .presentation li .content_right{
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                  }
                {$addon_id} .presentation .content_piece .content_right h3{
                    font-size: 16px;
                    margin: 0;
                    color: #fff;
                    font-weight: 700;
                    letter-spacing: 4px;
                }
                {$addon_id} .presentation .content_piece .content_right span{
                    font-size: 12px;
                    color: #fff;
                    letter-spacing: 3px;
            
                }
                {$addon_id} .content_img{
                    position: absolute;
                    left: auto;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 0;
                }
                {$addon_id} .content_img img{
                    width: 473px;
                }
                {$addon_id} .content{
                    display: none;
                }
                {$addon_id} .content.active{
                    display: block;
                }
                @media (max-width: 991px) and (min-width: 768px) {
                    {$addon_id} .presentation {
                        padding-top: {$tab_padding_top_type2_sm}px;
                    }
                    {$addon_id} .presentation .anv .anv_li{
                        padding: {$tab_padding_type2_sm};
                        height: {$tab_height_type2_sm}px;
                    }
                    {$addon_id} .presentation .anv li .nav_piece img{
                        min-width: 24px!important;/*选项卡图片大小*/
                        max-width: 24px!important;
                        width: 24px!important;
                      }
                      {$addon_id} .presentation .anv li .nav_piece{
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                      }
                      {$addon_id} .presentation .showa0 .border_r{
                        display: none;
                      }
                      {$addon_id} .content_img img{
                        opacity: 0.2;/*原来右侧大图的透明度*/
                      }
                      {$addon_id} .presentation .anv li .nav_piece .nav_icon{
                        padding: 0!important;
                      }
                      {$addon_id} .presentation .nav_right{
                        padding: 0;
                        text-align: center;/*选项卡字体居中方式*/
                      }
                      {$addon_id} .presentation .anv .anv_li:hover,
                      {$addon_id} .presentation .anv .anv_li.active{
                        height: {$tab_active_height_type2_sm}px!important;
                      }
                      {$addon_id} .presentation .content li{
                        max-width: 100%;
                      }
                      {$addon_id} .wrip{
                        height: auto!important;
                      }
                      {$addon_id} .presentation .anv li h3{
                        font-size: 14px!important;
                      }
                      {$addon_id} .nav_right>span{
                        font-size: 12px!important;
                      }
                      {$addon_id} .presentation li .content_piece{
                        width: 100%!important;
                        padding-right: 20px;
                      }
                      {$addon_id} .content_img{
                        z-index: -1;
                      }
                }
                @media (max-width: 767px){
                    {$addon_id} .presentation {
                        padding-top: {$tab_padding_top_type2_xs}px;
                    }
                    {$addon_id} .presentation .anv li .nav_piece img{
                      min-width: 24px!important;/*x选项卡图片大小*/
                      max-width: 24px!important;
                      width: 24px!important;
                    }
                    {$addon_id} .presentation .anv li .nav_piece{
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                    }
                    {$addon_id} .presentation .showa0 .border_r{
                      display: none;
                    }
                    {$addon_id} .content_img img{
                      opacity: 0.2;/*原来右侧大图的透明度*/
                    }
                    {$addon_id} .presentation .anv .anv_li{
                      padding: {$tab_padding_type2_xs};
                      height: {$tab_height_type2_xs}px!important;
                    }
                    {$addon_id} .presentation .anv li .nav_piece .nav_icon{
                      padding: 0!important;
                    }
                    {$addon_id} .presentation .nav_right{
                      padding: 0;
                      text-align: center;/*选项卡字体居中方式*/
                    }
                    {$addon_id} .presentation .anv .anv_li:hover,
                    {$addon_id} .presentation .anv .anv_li.active{
                      height: {$tab_active_height_type2_xs}px!important;
                    }
                    {$addon_id} .presentation .content li{
                      max-width: 100%;
                    }
                    {$addon_id} .wrip{
                      height: auto!important;
                    }
                    {$addon_id} .presentation .anv li h3{
                      font-size: 14px!important;
                    }
                    {$addon_id} .nav_right>span{
                      font-size: 12px!important;
                    }
                    {$addon_id} .presentation li .content_piece{
                      width: 100%!important;
                      padding-right: 20px;
                    }
                    {$addon_id} .content_img{
                      z-index: -1;
                    }
                  }
            </style>
            ";


            $output .= "
                <div class=\"wrip\">
                <div class=\"presentation container\">
            ";
            //导航
            $output .= "
                <div class=\"anv\">
                    <ul>";
                foreach ($settings->jw_tab_item_cust as $k=>$item) {
                    $title = (isset($item->title) && $item->title) ? $item->title : '爱好者生态';//标题
                    $title_vice = (isset($item->title_vice) && $item->title_vice) ? $item->title_vice : '数字化改造体育产业';//副标题
                    $tit_cle = (isset($item->tit_cle) && $item->tit_cle) ? $item->tit_cle : '#01020f';//导航背景颜色
                    $tit_cle_click = (isset($item->tit_cle_click) && $item->tit_cle_click) ? $item->tit_cle_click : '#1d5b67';//导航点击背景颜色
                    $nav_back_col = (isset($item->nav_back_col) && $item->nav_back_col) ? $item->nav_back_col : 0;//开启导航右侧边框
                    $tit_cle_bor = (isset($item->tit_cle_bor) && $item->tit_cle_bor) ? $item->tit_cle_bor : 0;//导航右侧边框颜色
                    $nav_icon = (isset($item->nav_icon) && $item->nav_icon) ? $item->nav_icon : '/components/com_jwpagefactory/addons/customize_tab/assets/images/yuan.png';//导航左侧图标

                    $output .= "
                            <style>
                                {$addon_id} .presentation .showa{$k}{
                                    background-color: {$tit_cle};
                                }
                                {$addon_id} .presentation .showa{$k}.active{
                                        background-color: {$tit_cle_click};  
                                }    
                                {$addon_id} .presentation .showa{$k} .border_r{
                                    border-right: {$tit_cle_bor} solid 1px;
                                }
                            </style>";

                        $activeName = "";
                        if($k === 0){
                            $activeName = "active";
                        }
                    if($nav_back_col==1){
                    $output .= "
                            <li class=\"anv_li showa{$k} {$activeName}\" data-id='show{$k}' ><div class=\"nav_piece\"><span class=\"nav_icon\"><img  src=\"{$nav_icon}\" /></span><div class=\"nav_right\"><h3>{$title}</h3><span>{$title_vice}</span></div><div class='border_r'></div></div></li>
                            ";
                    }else{
                        $output .= "
                            <li class=\"anv_li showa{$k} {$activeName}\" data-id='show{$k}' ><div class=\"nav_piece\"><span class=\"nav_icon\"><img  src=\"{$nav_icon}\" /></span><div class=\"nav_right\"><h3>{$title}</h3><span>{$title_vice}</span></div></div></li>
                            ";
                    }
                }
            $output .= "
                    </ul>
                </div>";
            //内容
            foreach ($settings->content as $k=>$item) {
                $title1 = (isset($item->title1) && $item->title1) ? $item->title1 : '爱好者生态';//标题1
                $title2 = (isset($item->title2) && $item->title2) ? $item->title2 : '爱好者生态';//标题2
                $title3 = (isset($item->title3) && $item->title3) ? $item->title3 : '爱好者生态';//标题3
                $desc = (isset($item->desc) && $item->desc) ? $item->desc : '爱好者生态';//简介1
                $desc2 = (isset($item->desc2) && $item->desc2) ? $item->desc2 : '爱好者生态';//简介2
                $desc3 = (isset($item->desc3) && $item->desc3) ? $item->desc3 : '爱好者生态';//简介3
                $left_icon1 = (isset($item->left_icon1) && $item->left_icon1) ? $item->left_icon1 : '/components/com_jwpagefactory/addons/customize_tab/assets/images/tab2-icon1.png';//左侧图标
                $left_icon2 = (isset($item->left_icon2) && $item->left_icon2) ? $item->left_icon2 : '/components/com_jwpagefactory/addons/customize_tab/assets/images/tab2-icon2.png';//左侧图标
                $left_icon3 = (isset($item->left_icon3) && $item->left_icon3) ? $item->left_icon3 : '/components/com_jwpagefactory/addons/customize_tab/assets/images/tab2-icon3.png';//左侧图标
                $gradient = (isset($item->gradient) && $item->gradient) ? $item->gradient : '#08546b';//半透明渐变色
                $right_img1 = (isset($item->right_img1) && $item->right_img1) ? $item->right_img1 : '/components/com_jwpagefactory/addons/customize_tab/assets/images/tab2-img.png';//右侧图片

                $output .= "<style>
                    {$addon_id} .presentation .content li {
                        background:{$gradient};
                    }
                </style>";
                if($k==0){

                $output .= '
                <div class="content active" id=\'show'.$k.'\'>';
                }else{
                    $output .= '
                <div class="content " id=\'show'.$k.'\'>';
                }
                $output .= "
                    <ul>
                        <li ><div class=\"content_piece\"><span class=\"content_icon\"><img  src=\"$left_icon1\" /></span><div class=\"content_right\"><h3>{$title1}</h3><span>{$desc}</span></div></div></li>
                        <li ><div class=\"content_piece\"><span class=\"content_icon\"><img  src=\"$left_icon2\" /></span><div class=\"content_right\"><h3>{$title2}</h3><span>{$desc2}</span></div></div></li>
                        <li ><div class=\"content_piece\"><span class=\"content_icon\"><img  src=\"$left_icon3\" /></span><div class=\"content_right\"><h3>{$title3}</h3><span>{$desc3}</span></div></div></li>
                    </ul>
                    <div class=\"content_img\"><img src=\"$right_img1\" /></div>
                </div>
                ";
            }
            $output .= "
                </div>
                </div>
            ";

            $output .= "
            <script>
            $(window).on('load',function(){
                $('{$addon_id} .presentation .anv li.anv_li:nth-of-type(1)').addClass('active').siblings().removeClass('active');
            })
            
            $('{$addon_id} .presentation .anv .anv_li:first-child').addClass('active').siblings().removeClass('active');
               $('{$addon_id} .presentation .anv .anv_li').each(function(i,v){
                 $(v).mouseenter(function(){
                    $(v).addClass('active').siblings().removeClass('active');
                    var id =$(this).attr('data-id');
                    $('{$addon_id} .presentation #'+id).addClass('active').siblings().removeClass('active');
                })
            })
            </script>
            ";
        }
        return $output;
    }


}
