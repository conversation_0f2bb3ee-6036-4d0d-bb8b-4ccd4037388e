<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonAdvi_button extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $urlpath = $config->jzt_url;
        $QQnum = '*********';

        // 布局样式
        $consult_style = (isset($settings->consult_style) && $settings->consult_style) ? $settings->consult_style : 'theme01';

        $search_type = (isset($settings->search_type)) ? $settings->search_type : 'type1';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        if ($urlpath == $_SERVER['HTTP_HOST']) {
            $postUrl = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0", $absolute = true) . '&site_id=' . $site_id . '&';
        } else {
            $postUrl = $_SERVER['HTTP_HOST'].'/html/' . base64_encode($detail_page_id) . '.html?';
        }
        $search_wz = (isset($settings->search_wz)) ? $settings->search_wz : 'left';

        $output = '';

        if($consult_style == 'theme01') {
            $button_top = (isset($settings->button_top) && $settings->button_top) ? $settings->button_top : 65;
            $output .= '<div style="height:' . $settings->input_height . 'px ">';
            if($search_wz=="left"){
                $output .= '<button style="z-index: 9;position: fixed;left: 0;top: ' . $button_top .'%;width:' . $settings->button_width . 'px;border-radius:' . $settings->button_border_radius . 'px;" class="search_btn">' . $settings->search_button_text . '</button>';
            }
            if($search_wz=="right"){
                $output .= '<button style="z-index: 9;position: fixed;right: 0;top: ' . $button_top .'%;width:' . $settings->button_width . 'px;border-radius:' . $settings->button_border_radius . 'px;" class="search_btn">' . $settings->search_button_text . '</button>';
            }
            $output .= "</div>";
        }
        if($consult_style == 'theme02') {
            // 布局样式
            $theme02_close_img = (isset($settings->theme02_close_img) && $settings->theme02_close_img) ? $settings->theme02_close_img : 'https://oss.lcweb01.cn/joomla/20211221/7757b90e9ba9416adc8059287142a226.png';
            $theme02_title = (isset($settings->theme02_title) && $settings->theme02_title) ? $settings->theme02_title : '山西资海科技有限公司欢迎您 请问有什么可以帮助您？';
            $theme02_btn01_text = (isset($settings->theme02_btn01_text) && $settings->theme02_btn01_text) ? $settings->theme02_btn01_text : '稍后再说';
            $theme02_btn02_text = (isset($settings->theme02_btn02_text) && $settings->theme02_btn02_text) ? $settings->theme02_btn02_text : '立即咨询';

            $output .= '<div class="consult-02">
		        <div class="close-btn" onclick="closeConsult()">
		            <img src="' . $theme02_close_img . '" alt="">
		        </div>
		        <p class="c-title">' . $theme02_title . '</p>
		        <div class="bot-btn-box">
		            <div class="c-btn btn-cancel" onclick="closeConsult()">' . $theme02_btn01_text . '</div>
		            <div class="c-btn search_btn">' . $theme02_btn02_text . '</div>
                </div>
            </div>';
        }

        return $output;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        // 布局样式
        $consult_style = (isset($settings->consult_style) && $settings->consult_style) ? $settings->consult_style : 'theme01';

        $css = '';
        if($consult_style == 'theme01') {
            $css .= $addon_id . ' .search_btn {
                padding: 0 10px;
                border: 1px solid ' . $settings->button_border_color .';
                cursor: pointer;
                outline-color: transparent;
                background:' . $settings->button_color .';
                color:' . $settings->button_font_color .';
                height:' . $settings->input_height .'px;
            }';
        }
        if($consult_style == 'theme02') {
            $theme02_bg_img = (isset($settings->theme02_bg_img) && $settings->theme02_bg_img) ? $settings->theme02_bg_img : 'https://oss.lcweb01.cn/joomla/20211221/da664c5bf545b0c4a6c388ec1827cfb2.jpg';
            $theme02_bg_img_w = (isset($settings->theme02_bg_img_w) && $settings->theme02_bg_img_w) ? $settings->theme02_bg_img_w : 624;
            $theme02_bg_img_h = (isset($settings->theme02_bg_img_h) && $settings->theme02_bg_img_h) ? $settings->theme02_bg_img_h : 292;
            $theme02_bg_img_p = (isset($settings->theme02_bg_img_p) && $settings->theme02_bg_img_p) ? $settings->theme02_bg_img_p : '80px 60px 70px 120px';

            $theme02_close_img_w = (isset($settings->theme02_close_img_w) && $settings->theme02_close_img_w) ? $settings->theme02_close_img_w : 26;
            $theme02_close_img_h = (isset($settings->theme02_close_img_h) && $settings->theme02_close_img_h) ? $settings->theme02_close_img_h : 26;
            $theme02_title_fontsize = (isset($settings->theme02_title_fontsize) && $settings->theme02_title_fontsize) ? $settings->theme02_title_fontsize : 24;
            $theme02_title_color = (isset($settings->theme02_title_color) && $settings->theme02_title_color) ? $settings->theme02_title_color : '#1b5778';
            $theme02_title_weight = (isset($settings->theme02_title_weight) && $settings->theme02_title_weight) ? 'font-weight: bold;' : '';

            $theme02_btn_top = (isset($settings->theme02_btn_top) && $settings->theme02_btn_top) ? $settings->theme02_btn_top : 30;
            $theme02_btn_w = (isset($settings->theme02_btn_w) && $settings->theme02_btn_w) ? $settings->theme02_btn_w : 140;
            $theme02_btn_h = (isset($settings->theme02_btn_h) && $settings->theme02_btn_h) ? $settings->theme02_btn_h : 42;
            $theme02_btn_fontsize = (isset($settings->theme02_btn_fontsize) && $settings->theme02_btn_fontsize) ? $settings->theme02_btn_fontsize : 16;
            $theme02_btn_radius = (isset($settings->theme02_btn_radius) && $settings->theme02_btn_radius) ? $settings->theme02_btn_radius : 10;
            $theme02_btn_m = (isset($settings->theme02_btn_m) && $settings->theme02_btn_m) ? $settings->theme02_btn_m : 20;

            $theme02_btn01_color = (isset($settings->theme02_btn01_color) && $settings->theme02_btn01_color) ? $settings->theme02_btn01_color : '#1b5778';
            $theme02_btn01_bgColor = (isset($settings->theme02_btn01_bgColor) && $settings->theme02_btn01_bgColor) ? $settings->theme02_btn01_bgColor : '#fff';
            $theme02_btn01_bColor = (isset($settings->theme02_btn01_bColor) && $settings->theme02_btn01_bColor) ? $settings->theme02_btn01_bColor : '#1b5778';
            $theme02_btn01_bW = (isset($settings->theme02_btn01_bW) && $settings->theme02_btn01_bW) ? $settings->theme02_btn01_bW : 0;

            $theme02_btn02_color = (isset($settings->theme02_btn02_color) && $settings->theme02_btn02_color) ? $settings->theme02_btn02_color : '#fff';
            $theme02_btn02_bgColor = (isset($settings->theme02_btn02_bgColor) && $settings->theme02_btn02_bgColor) ? $settings->theme02_btn02_bgColor : '#1b5778';
            $theme02_btn02_bColor = (isset($settings->theme02_btn02_bColor) && $settings->theme02_btn02_bColor) ? $settings->theme02_btn02_bColor : '#1b5778';
            $theme02_btn02_bW = (isset($settings->theme02_btn02_bW) && $settings->theme02_btn02_bW) ? $settings->theme02_btn02_bW : 0;

            $css .= $addon_id . ' .consult-02 {
                position: fixed;
                width: ' . $theme02_bg_img_w . 'px;
                height: ' . $theme02_bg_img_h . 'px;
                background: url("' . $theme02_bg_img . '") no-repeat center;
                background-size: cover;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto;
                z-index: 999;
                padding: ' . $theme02_bg_img_p . ';
            }
            ' . $addon_id . ' .consult-02 .close-btn {
                position: absolute;
                cursor: pointer;
                top: 20px;
                right: 20px;
                width: ' . $theme02_close_img_w . 'px;
                height: ' . $theme02_close_img_h . 'px;
            }
            ' . $addon_id . ' .consult-02 .close-btn img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            ' . $addon_id . ' .consult-02 .c-title {
                font-size: ' . $theme02_title_fontsize . 'px;
                color: ' . $theme02_title_color . ';
                ' . $theme02_title_weight .'
            }
            ' . $addon_id . ' .consult-02 .bot-btn-box {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: ' . $theme02_btn_top . 'px;
            }
            ' . $addon_id . ' .consult-02 .bot-btn-box .c-btn {
                width: ' . $theme02_btn_w . 'px;
                height: ' . $theme02_btn_h . 'px;
                line-height: ' . $theme02_btn_h . 'px;
                text-align: center;
                font-size: ' . $theme02_btn_fontsize . 'px;
                background-color: ' . $theme02_btn01_bgColor . ';
                color: ' . $theme02_btn01_color . ';
                border: ' . $theme02_btn01_bColor . ' solid ' . $theme02_btn01_bW . 'px;
                border-radius: ' . $theme02_btn_radius . 'px;
                cursor: pointer;
            }
            ' . $addon_id . ' .consult-02 .bot-btn-box .c-btn.search_btn {
                background-color: ' . $theme02_btn02_bgColor . ';
                color: ' . $theme02_btn02_color . ';
                border: ' . $theme02_btn02_bColor . ' solid ' . $theme02_btn02_bW . 'px;
                margin-left: ' . $theme02_btn_m . 'px;
            }
            ';
        }
        return $css;
    }

    public function js() {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $urlpath = $config->jzt_url;

        $settings = $this->addon->settings;
        // 布局样式
        $consult_style = (isset($settings->consult_style) && $settings->consult_style) ? $settings->consult_style : 'theme01';

        $qqq = JwPageFactoryBase::getQqList($site_id, $company_id)['list'][0];
        $qq_id = (isset($settings->text_id) && $settings->text_id) ? $settings->text_id : $qqq;
                   

        $js = '
            jQuery(document).ready(function($){
                $("' . $addon_id . ' .search_btn").click(function(){
                    var newWindow = window.open();
                    newWindow.location.href = "http://wpa.qq.com/msgrd?v=3&uin=' . $qq_id . '&site=qq&menu=yes";

                    // jQuery.ajax({
                    //     type: "POST",
                    //     url: "' . $urlpath . '/api/Shangqiao/qq",
                    //     dataType: "json",
                    //     data: {
                    //         "company_id" :' . $company_id . ',
                    //         "site_id" :' . $site_id . ',
                    //     },
                    //     success: function (res) {
                    //         var stri = res.data
                    //         newWindow.location.href = `http://wpa.qq.com/msgrd?v=3&uin=${stri}&site=qq&menu=yes`;
                    //     }     
                    // });
                });';
            $js .= '
            });
        ';

        if($consult_style == 'theme02') {
            $js .= '
            function closeConsult() {
                $("' . $addon_id . ' .consult-02").hide();
            }';
        }
        return $js;
    }

    //用于设计器中显示
    public static function getTemplate()
    {

        $output = '
            <#
                var addonId = "#jwpf-addon-"+data.id;
                var consult_style = data.consult_style || "theme01";
                var button_top = data.button_top || 65;
            #>
            <style>
                <# if(consult_style == "theme01") { #>
                    {{ addonId }} .search_box{
                        
                    }
                    {{ addonId }} .search_btn{
                        padding: 0 10px;
                        border-radius: {{data.button_border_radius}}px;
                        border: 1px solid {{data.button_border_color}};
                        cursor: pointer;
                        outline-color: transparent;
                        background:{{data.button_color}};
                        color:{{data.button_font_color}};
                    }
                <# } #>
                <# if(consult_style == "theme02") { 
                    // 背景图
                    var theme02_bg_img = data.theme02_bg_img || "https://oss.lcweb01.cn/joomla/20211221/da664c5bf545b0c4a6c388ec1827cfb2.jpg";
                    var theme02_bg_img_w = data.theme02_bg_img_w || 624;
                    var theme02_bg_img_h = data.theme02_bg_img_h || 292;
                    var theme02_bg_img_p = data.theme02_bg_img_p || "80px 60px 70px 120px";
                    // 关闭按钮
                    var theme02_close_img = data.theme02_close_img || "https://oss.lcweb01.cn/joomla/20211221/7757b90e9ba9416adc8059287142a226.png";
                    var theme02_close_img_w = data.theme02_close_img_w || 26;
                    var theme02_close_img_h = data.theme02_close_img_h || 26;
                    // 内容
                    var theme02_title = data.theme02_title || "山西资海科技有限公司欢迎您 请问有什么可以帮助您？";
                    var theme02_title_fontsize = data.theme02_title_fontsize || 24;
                    var theme02_title_color = data.theme02_title_color || "#1b5778";
                    var theme02_title_weight = data.theme02_title_weight || 0;
                    // 按钮
                    var theme02_btn_top = data.theme02_btn_top || 30;
                    var theme02_btn_w = data.theme02_btn_w || 140;
                    var theme02_btn_h = data.theme02_btn_h || 42;
                    var theme02_btn_fontsize = data.theme02_btn_fontsize || 16;
                    var theme02_btn_radius = data.theme02_btn_radius || 10;
                    var theme02_btn_m = data.theme02_btn_m || 20;
                                        
                    var theme02_btn01_text = data.theme02_btn01_text || "稍后再说";
                    var theme02_btn01_color = data.theme02_btn01_color || "#1b5778";
                    var theme02_btn01_bgColor = data.theme02_btn01_bgColor || "#fff";
                    var theme02_btn01_bColor = data.theme02_btn01_bColor || "#1b5778";
                    var theme02_btn01_bW = data.theme02_btn01_bW || 0;
                    
                    var theme02_btn02_text = data.theme02_btn02_text || "立即咨询";
                    var theme02_btn02_color = data.theme02_btn02_color || "#fff";
                    var theme02_btn02_bgColor = data.theme02_btn02_bgColor || "#1b5778";
                    var theme02_btn02_bColor = data.theme02_btn02_bColor || "#1b5778";
                    var theme02_btn02_bW = data.theme02_btn02_bW || 0;
                #>
                    {{ addonId }} .consult-02 {
                        position: fixed;
                        width: {{ theme02_bg_img_w }}px;
                        height: {{ theme02_bg_img_h }}px;
                        background: url("{{ theme02_bg_img }}") no-repeat center;
                        background-size: cover;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        margin: auto;
                        z-index: 999;
                        padding: {{ theme02_bg_img_p }};
                    }
                    {{ addonId }} .consult-02 .close-btn {
                        position: absolute;
                        cursor: pointer;
                        top: 20px;
                        right: 20px;
                        width: {{ theme02_close_img_w }}px;
                        height: {{ theme02_close_img_h }}px;
                    }
                    {{ addonId }} .consult-02 .close-btn img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                    {{ addonId }} .consult-02 .c-title {
                        font-size: {{ theme02_title_fontsize }}px;
                        color: {{ theme02_title_color }};
                        <# if(theme02_title_weight == 1) { #>
                            font-weight: bold;
                        <# } #>
                    }
                    {{ addonId }} .consult-02 .bot-btn-box {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-top: {{ theme02_btn_top }}px;
                    }
                    {{ addonId }} .consult-02 .bot-btn-box .c-btn {
                        width: {{ theme02_btn_w }}px;
                        height: {{ theme02_btn_h }}px;
                        line-height: {{ theme02_btn_h }}px;
                        text-align: center;
                        font-size: {{ theme02_btn_fontsize }}px;
                        background-color: {{ theme02_btn01_bgColor }};
                        color: {{ theme02_btn01_color }};
                        border: {{ theme02_btn01_bColor }} solid {{ theme02_btn01_bW }}px;
                        border-radius: {{ theme02_btn_radius }}px;
                        cursor: pointer;
                    }
                    {{ addonId }} .consult-02 .bot-btn-box .c-btn.search_btn {
                        background-color: {{ theme02_btn02_bgColor }};
                        color: {{ theme02_btn02_color }};
                        border: {{ theme02_btn02_bColor }} solid {{ theme02_btn02_bW }}px;
                        margin-left: {{ theme02_btn_m }}px;
                    }
                <# } #>
            </style>
        <div style="height: 100px;">
            <span>本段文字用于QQ咨询按钮插件编辑模式下占位，预览模式下不显示</span>
        </div>
        <# if(consult_style == "theme01") { #>
            <div style="height:{{data.input_height}}px ">
                <# if(data.search_wz=="left") { #>
                    <button style="position: fixed;left: 0;top: {{ button_top }}%;z-index: 9;width:{{data.button_width}}px;height:{{data.input_height}}px" class="search_btn">{{data.search_button_text}}</button>
                <# } #>
                <# if(data.search_wz=="right") { #>
                    <button style=";position: fixed;right: 0;top: {{ button_top }}%;z-index: 9;width:{{data.button_width}}px;height:{{data.input_height}}px" class="search_btn">{{data.search_button_text}}</button>
                <# } #>
            </div>
		<# } #>
		<# if(consult_style == "theme02") { #>
		    <div class="consult-02">
		        <div class="close-btn">
		            <img src=\'{{ theme02_close_img }}\' alt="">
		        </div>
		        <p class="c-title">{{ theme02_title }}</p>
		        <div class="bot-btn-box">
		            <div class="c-btn btn-cancel">{{ theme02_btn01_text }}</div>
		            <div class="c-btn search_btn">{{ theme02_btn02_text }}</div>
                </div>
            </div>
		<# } #>
		';
        
        return $output;
    }
}