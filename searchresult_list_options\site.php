<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonSearchresult_list_options extends JwpagefactoryAddons
{
    public function render()
    {   
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $site_id = $_GET['site_id'] ?? 0; 
        $company_id = $_GET['company_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $search_name = $_GET['search_name'] ?? '';

        $news_close = (isset($settings->news_close)) ? $settings->news_close : 0;
        $news_catid = (isset($settings->news_catid)) ? $settings->news_catid : '';
        $pros_close = (isset($settings->pros_close)) ? $settings->pros_close : 0;

        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
        $type_num = (isset($settings->type_num)) ? $settings->type_num : 0;
        $catordering = isset($settings->catordering) ? $settings->catordering : 'sortasc'; //分类排序
        

        $pros_catid ='';
        if($pros_close!=1){
          $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
          require_once $article_helper;
          $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, $type_parent, $type_start, $type_num, $catordering);
          $gods=[];
          foreach ($categories_list as $keyw=> $valuew) {
            $gods[]=$valuew['tag_id'];
          }
          $pros_catid =implode(',',$gods);
          
        }
        if($news_close!=1){
          if(!empty($news_catid)){
            $news_catid =implode(',',$news_catid);
          }else{
            $news_catid ='';
          }
        }
        
        $config = new JConfig();
        $pageApi = $config->jzt_url;
        if($news_close!=1 || $pros_close!=1){
          $search_data_url = $pageApi . '/api/SearchJson/index?site_id=' . $site_id . '&search_name=' . $search_name.'&news_close='.$news_close.'&news_catid='.$news_catid.'&pros_close='.$pros_close.'&pros_catid='.$pros_catid;
        }else{
          $search_data_url = $pageApi . '/api/SearchJson/index?site_id=' . $site_id . '&search_name=' . $search_name.'&pros_close='.$pros_close.'&news_close='.$news_close;
        }

        $options_show =(isset($settings->options_show_values) && $settings->options_show_values) ? $settings->options_show_values : 'type1';

        // $search_data_url = $pageApi . '/api/SearchJson/index?site_id=' . $site_id . '&search_name=' . $search_name. '&options=' .$options_show;
        $search_data_urlssss = $pageApi . '/api/SearchJson/index?site_id=' . $site_id . '&search_name=' . $search_name.'&news_close='.$news_close.'&news_catid='.$news_catid.'&pros_close='.$pros_close.'&pros_catid='.$pros_catid;
       

        // Addon options
        $art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : '';
        $art_data_selector = (isset($settings->art_data_selector) && $settings->art_data_selector) ? $settings->art_data_selector : '';


        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;


        $art_date_font_size = (isset($settings->art_date_font_size) && $settings->art_date_font_size) ? $settings->art_date_font_size : 0;
        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;

        $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : '';
        $product_page_id = (isset($settings->product_page_id) && $settings->product_page_id) ? $settings->product_page_id : '';
        $content_url = JRoute::_("index.php/component/jwpagefactory/?view=page");
        $content_url2 = JRoute::_("html/");
       
        if($art_type_selector == 'art05')
        {
           $output = '<div class="title-font">搜索结果</div>
           <ul class="content-box" id="list_msg">
           </ul>
           <div id="error_msg"></div>';

           $output .= '<script>
            var jzt_url =  window.location.hostname;
            var is_master = false;
            if (jzt_url == "ijzt.china9.cn" || jzt_url == "jzt_dev_2.china9.cn") {
                is_master = true;
            }
            function get(keyword) {
              var reg = new RegExp("(^|&)"+keyword+"=([^&]*)(&|$)", "i");
              var r = window.location.search.substr(1).match(reg);
              if (r != null) return unescape(r[2]); return null;   //注意此处参数是中文，解码使用的方法是unescape ，那么在传值的时候如果是中文，需要使用escape("曲浩")方法来编码。
            }
            // 统一获取数据
            if (jzt_url == "ijzt.china9.cn" || jzt_url == "jzt_dev_2.china9.cn") {
              var htmlobj=jQuery.ajax({url:"' . $search_data_url . '",async:false});
            }
            else
            {
              var htmlobj=jQuery.ajax({url:"' . $search_data_urlssss . '"+"&search_name="+get("search_name"),async:false});
            }
            var htmldata = "";
            var htmlobjlist = JSON.parse(htmlobj.responseText);  
            if(!htmlobjlist[0]){
              htmldata = `<div style="text-align:center;font-size:20px;margin-top:60px;winth:100%;">暂无数据...</div>`;
              jQuery("#error_msg").append(htmldata);
            };
            function formatDate(date) {
              var date = new Date(date*1000);
              var YY = date.getFullYear() + "-";
              var MM = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
              var DD = (date.getDate() < 10 ? "0" + (date.getDate()) : date.getDate());
              // var hh = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
              // var mm = (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ":";
              // var ss = (date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds());
              return YY + MM + DD;
              // return YY + MM + DD +" "+hh + mm + ss;
            }
          </script>';
        }
        else
        {
          $output = '<div class="art_list_id jwpf-addon jwpf-addon-articles">
          <div class="jwpf-addon-content">
            <div id="result_box" class="jwpf-row">
            
            </div>
            <div id="result_box-FY">
              <table width="60%" align="right">
                <tr><td><div id="barcon" name="barcon"></div></td></tr>
              </table>
            </div>
          </div>
         </div>';
            $output .= '<script>
            var jzt_url =  window.location.hostname;
            var is_master = false;
            if (jzt_url == "ijzt.china9.cn" || jzt_url == "jzt_dev_2.china9.cn") {
                is_master = true;
            }
            function get(keyword) {
              var reg = new RegExp("(^|&)"+keyword+"=([^&]*)(&|$)", "i");
              var r = window.location.search.substr(1).match(reg);
              if (r != null) return unescape(r[2]); return null;   //注意此处参数是中文，解码使用的方法是unescape ，那么在传值的时候如果是中文，需要使用escape("曲浩")方法来编码。
            }
            // 统一获取数据
            if (jzt_url == "ijzt.china9.cn" || jzt_url == "jzt_dev_2.china9.cn") {
              var htmlobj=jQuery.ajax({url:"' . $search_data_url . '",async:false});
            }
            else
            {
              var htmlobj=jQuery.ajax({url:"' . $search_data_urlssss . '"+"&search_name="+get("search_name"),async:false});
            }
            var htmldata = "";
            var htmlobjlist = JSON.parse(htmlobj.responseText);  
            if(!htmlobjlist[0]){
              htmldata = `<div style="text-align:center;font-size:20px;margin-top:60px;winth:100%;">暂无数据...</div>`;
              jQuery("#result_box").append(htmldata);
            };
            function formatDate(date) {
              var date = new Date(date*1000);
              var YY = date.getFullYear() + "-";
              var MM = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
              var DD = (date.getDate() < 10 ? "0" + (date.getDate()) : date.getDate());
              // var hh = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
              // var mm = (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ":";
              // var ss = (date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds());
              return YY + MM + DD;
              // return YY + MM + DD +" "+hh + mm + ss;
            }
          </script>';

        }

        if ($art_type_selector == 'art01') {
            $output .= '<style>
                        *{
                            padding: 0;
                            margin: 0;
                        }
                        .art_list_id .page_plug{
                            width: 90%;
                            margin: 5px auto;
                            text-align: center;
                        }
                        .art_list_id .page_plug a{
                            padding: 3px 8px;
                            border: 1px solid #2a68a7;
                            margin-right: 5px;
                            text-decoration: none;
                            color: #2a68a7;
                        }
                        .art_list_id .jwpf-article-info-wrap>h3>a{
                          font-size:16px;
                          font-weight:400 !important;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                          overflow: hidden;
                          width: 100%;
                          display: block;
                        }
                        .art_list_id .jwpf-article-info-wrap .jwpf-meta-date{
                          font-size:'.$art_date_font_size.'px !important;
                          color:#999 !important;
                        }
                        .art_list_id .jwpf-article-info-wrap .jwpf-article-introtext{
                          font-size:14px;
                          color:#666;
                          line-height:1.7 !important;
                        }
                        .hidden{
                          display:none;
                        }
                        .show{
                          display:block;
                        }
                  </style>';
            // 判断是否显示简介
            if ($show_intro) {
                $output .= '<script>
                    for(i=0;i<htmlobjlist.length;i++){
                        if (htmlobjlist[i].type == 1) {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                            } else {
                              var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                            }
                        } else {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                            } else {
                              var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                            }
                        }
                        if(htmlobjlist[i].images){
                            htmldata += `
                              <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                                <div class="jwpf-addon-article">
                                    
                                        <a class="jwpf-article-img-wrap" href="${content_url}" itemprop="url">
                                            <img class="jwpf-img-responsive" src="${htmlobjlist[i].images}" alt="${htmlobjlist[i].title}" itemprop="thumbnailUrl" loading="lazy">
                                        </a>
                                    <div class="jwpf-article-info-wrap">
                                    <h3>
                                        <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                                    </h3>
                                    <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                                      <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                                    </div>
                                    <div style="font-size:' . $settings->art_desc_font_size . 'px;color:' . $settings->art_desc_font_color . '" class="jwpf-article-introtext">${htmlobjlist[i].introtext}</div>
                                  </div>
                                </div>
                              </div>
                            `;
                        }else{
                            htmldata += `
                              <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                                <div class="jwpf-addon-article">
                                    <div class="jwpf-article-info-wrap">
                                    <h3>
                                        <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                                    </h3>
                                    <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                                      <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                                    </div>
                                    <div style="font-size:' . $settings->art_desc_font_size . 'px;color:' . $settings->art_desc_font_color . '" class="jwpf-article-introtext">${htmlobjlist[i].introtext}</div>
                                  </div>
                                </div>
                              </div>
                            `;
                        }
                      
                    }
                    htmldata = htmldata.replace(/\/https/g,"https")
                    htmldata = htmldata.replace(/\/http/g,"http")
                    htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                    htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
                    if(htmlobjlist[0]){
                      jQuery("#result_box").append(htmldata);
                    }
                    
                  </script>';
            } else {
                $output .= '<script>
                    for(i=0;i<htmlobjlist.length;i++){
                        if (htmlobjlist[i].type == 1) {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                            } else {
                              var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                            }
                        } else {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                            } else {
                              var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                            }
                        }

                        if(htmlobjlist[i].images){
                            htmldata += `
                              <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                                <div class="jwpf-addon-article">
                                
                                  <a class="jwpf-article-img-wrap" href="${content_url}" itemprop="url">
                                    <img class="jwpf-img-responsive" src="${htmlobjlist[i].images}" alt="${htmlobjlist[i].title}" itemprop="thumbnailUrl" loading="lazy">
                                  </a>
                                

                                  <div class="jwpf-article-info-wrap">
                                    <h3>
                                      <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                                    </h3>
                                    <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                                      <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            `
                        }else{
                            htmldata += `
                              <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                                <div class="jwpf-addon-article">                            
                                  <div class="jwpf-article-info-wrap">
                                    <h3>
                                      <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                                    </h3>
                                    <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                                      <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            `
                        }
                          
                      htmldata = htmldata.replace(/\/https/g,"https")
                      htmldata = htmldata.replace(/\/http/g,"http")
                      htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                      htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
                    }
                    
                    if(htmlobjlist[0]){
                      jQuery("#result_box").append(htmldata);
                    }
                  </script>';
            }

        }




        if ($art_type_selector == 'art04') {
            $output .= '<style>
                          .minWrap {
                            display:flex;
                            justify-content: space-between;
                          }
                        </style>';

            if ($show_intro) {
                $output .= '<script>
                for(i=0;i<htmlobjlist.length;i++){
                    if (htmlobjlist[i].type == 1) {
                        if (is_master) {
                            var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                        } else {
                          var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                        }
                    } else {
                        if (is_master) {
                            var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                        } else {
                          var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                        }
                    }

                    htmldata += `
                    <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                      <div class="jwpf-addon-article">

                        <div class="jwpf-article-info-wrap minWrap">
                          <h3>
                            <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                          </h3>
                          <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                            <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                          </div>

                        </div>
                        <div style="font-size:' . $settings->art_desc_font_size . 'px;color:' . $settings->art_desc_font_color . '" class="jwpf-article-introtext">${htmlobjlist[i].introtext}</div>
                      </div>
                    </div>
                    `
                      htmldata = htmldata.replace(/\/https/g,"https") 
                      htmldata = htmldata.replace(/\/http/g,"http")
                      htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                      htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
                }
                if(htmlobjlist[0]){
                jQuery("#result_box").append(htmldata);
                }

                </script>';
            } else {
                $output .= '<script>
                for(i=0;i<htmlobjlist.length;i++){
                  if (htmlobjlist[i].type == 1) {
                      if (is_master) {
                          var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                      } else {
                        var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                      }
                  } else {
                      if (is_master) {
                          var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                      } else {
                        var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                      }
                  }
              htmldata += `
              <div class="jwpf-col-sm-' . round(12 / $columns) . ' art_list_id fy_data">
                <div class="jwpf-addon-article">

                  <div class="jwpf-article-info-wrap">
                    <h3>
                      <a style="font-size:' . $settings->art_title_font_size . 'px;text-align:' . $settings->art_title_selector . '" href="${content_url}" itemprop="url">${htmlobjlist[i].title}</a>
                    </h3>
                    <div class="jwpf-article-meta" style="text-align:' . $art_data_selector . '">
                      <span class="jwpf-meta-date" itemprop="datePublished">${formatDate(htmlobjlist[i].created)}</span>
                    </div>
                  </div>
                </div>
              </div>
              `
                      htmldata = htmldata.replace(/\/https/g,"https")
                      htmldata = htmldata.replace(/\/http/g,"http")
                      htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                      htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
              }
              if(htmlobjlist[0]){
              jQuery("#result_box").append(htmldata);
              }
              </script>';
            }
        }
// 分页
        if ($show_page) {
            $output .= '<script>
              var num=htmlobjlist.length;
              var itable = document.getElementsByClassName("fy_data")     
                  // /**
                  //  * 分页函数
                  //  * pno--页数
                  //  * psize--每页显示记录数
                  //  * 分页部分是从真实数据行开始，因而存在加减某个常数，以确定真正的记录数
                  //  * 纯js分页实质是数据行全部加载，通过是否显示属性完成分页功能
                  //  **/
                  function goPage(pno,psize){
                    var totalPage = 0;//总页数
                    var pageSize = ' . $limit . ';//每页显示数
                    //总共分几页
                    if(num/pageSize > parseInt(num/pageSize)){
                        totalPage=parseInt(num/pageSize)+1;
                      }else{
                        totalPage=parseInt(num/pageSize);
                      }
                    var currentPage = pno;//当前页数
                    var startRow = (currentPage - 1) * pageSize+1;//开始显示的数据
                      var endRow = currentPage * pageSize;//结束显示的数据
                      endRow = (endRow > num)? num : endRow;
                      console.log(endRow);
                      //遍历显示数据实现分页
                    for(var i=1;i<(num+1);i++){
                      var irow = itable[i-1];
                      if(i>=startRow && i<=endRow){
                        irow.style.display = "block";
                      }else{
                        irow.style.display = "none";
                      }
                    }
                    var tempStr = "共"+num+"条数据  共分"+totalPage+"页  当前第"+currentPage+"页"  ;
                    if(currentPage>1){
                      tempStr += "<a href=\"#\" onClick=\"goPage("+(1)+","+psize+")\">  首页 </a>";
                      tempStr += "<a href=\"#\" onClick=\"goPage("+(currentPage-1)+","+psize+")\"> <上一页 </a>"
                    }else{
                      tempStr += "  首页";
                      tempStr += " <上一页 ";
                    }
                    if(currentPage<totalPage){
                      tempStr += "<a href=\"#\" onClick=\"goPage("+(currentPage+1)+","+psize+")\">下一页></a>";
                      tempStr += "<a href=\"#\" onClick=\"goPage("+(totalPage)+","+psize+")\"> 尾页</a>";
                    }else{
                      tempStr += "下一页 >";
                      tempStr += " 尾页";
                    }
                    document.getElementById("barcon").innerHTML = tempStr;
                  }
                  goPage(1,' . $limit . ')
              </script>';
        }


        if ($art_type_selector == 'art05') {
            $output .= '<style>
            .content-box li a{
              font-size: 18px;
              height: 65px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #505050;
              line-height: 34px;
              padding-top: 20px;
              text-decoration:none;
          }
      
          .content-box li{
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #505050;
              line-height: 34px;
              border-bottom: 1px solid #E5E5E5;
              padding-top: 20px;
              padding-bottom: 20px;
          }
      
          .content-box li .list-font{
              font-size: '.$art_date_font_size.'px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #999999;
              line-height: 30px;
          }
      
          .title-font{
              width: 64px;
              height: 16px;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #272831;
              margin-left: 40px;
          }
                  </style>';
            // 判断是否显示简介
                $output .= '<script>
                    for(i=0;i<htmlobjlist.length;i++){
                        if (htmlobjlist[i].type == 1) {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                            } else {
                              var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                            }
                        } else {
                            if (is_master) {
                                var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                            } else {
                              var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                            }
                        }
                      htmldata += `
                      <li>
                        <a href="${content_url}">
                        ${htmlobjlist[i].title}
                      </a>
                          <div class="list-font">${formatDate(htmlobjlist[i].created)}</div>
                      </li>
                      `
                      htmldata = htmldata.replace(/\/https/g,"https")
                      htmldata = htmldata.replace(/\/http/g,"http")
                      htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                      htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
                    } 
                    if(htmlobjlist[0]){
                      jQuery("#list_msg").append(htmldata);
                    }
                  </script>';

        }
        if ($art_type_selector == 'art06') {
          $art73_label = (isset($settings->art73_label) && $settings->art73_label) ? $settings->art73_label : '企业资讯';
            $limit_type73 = (isset($settings->limit_type73) && $settings->limit_type73) ? $settings->limit_type73 : 12;
            $li_bottom_art73 = (isset($settings->li_bottom_art73) && $settings->li_bottom_art73) ? $settings->li_bottom_art73 : 30;
            $padding_art73 = (isset($settings->padding_art73) && $settings->padding_art73) ? $settings->padding_art73 : '0 0 40px 0';
            $li_width_art73 = (isset($settings->li_width_art73) && $settings->li_width_art73) ? $settings->li_width_art73 : 25;
            $li_padding_art73 = (isset($settings->li_padding_art73) && $settings->li_padding_art73) ? $settings->li_padding_art73 : '0 10px 0 10px';
            $output.='<style>
                '.$addon_id.' *{
                    margin: 0;
                    padding: 0;
                }
                '.$addon_id.' a:hover{
                    text-decoration: none;
                }
                '.$addon_id.' .news_list {
                    overflow: hidden;
                    padding-bottom: 20px;
                }
                '.$addon_id.' .news_list ul {
                    padding: '.$padding_art73.';
                    position: relative;
                    margin: 0;
                }
                '.$addon_id.' .news_list ul li {
                    width: '.$li_width_art73.'%;
                    padding: '.$li_padding_art73.';
                    float: left;
                    margin-bottom: '.$li_bottom_art73.'px;
                    list-style: none;
                }
                '.$addon_id.' .news_list ul li .item {
                    display: block;
                    background-color: #ffffff;
                    border-radius: 10px;
                    overflow: hidden;
                }
                '.$addon_id.' .box_img {
                    position: relative;
                    overflow: hidden;
                }
                '.$addon_id.' .news_list ul li .item .box_img {
                    height: 250px;
                }
                '.$addon_id.' .box_img img {
                    width: auto;
                    height: 100%;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    display: block;
                    transition: 0.5s;
                }
                '.$addon_id.' .news_list ul li:hover .item .box_img img{
                    transform: translate(-50%, -50%) scale(1.06);
                }
                '.$addon_id.' .news_list ul li .item .text {
                    padding: 20px;
                }
                '.$addon_id.' .txt-cut {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
                '.$addon_id.' .news_list ul li .item .text h1 {
                    height: 60px;
                    line-height: 30px;
                    overflow: hidden;
                    color: #000;
                    margin-bottom: 26px;
                    font-size: 18px;
                    transition: all 0.3s;
                    font-weight: bold;
                }
                '.$addon_id.' .news_list ul li:hover .item .text h1{
                    color: #d90029;
                }
                '.$addon_id.' .news_list ul li .item .text p {
                    transition: all 0.5s ease-in-out;
                    color: #b2b2b2;
                    font-size: 14px;
                    line-height: 1.6;
                }
                '.$addon_id.' .news_list ul li:hover .item .text p{
                    color: #d90029;
                }
                '.$addon_id.' .news_list ul li .item .text p span {
                    margin: 0 5px;
                    display: inline-block;
                }
                @media screen and (max-width: 1400px){
                    '.$addon_id.' .news_list ul li .item .box_img {
                        height: 200px;
                    }
                }
                @media screen and (max-width: 1200px){
                    '.$addon_id.' .news_list ul li .item .box_img {
                        height: 180px;
                    }
                }
                @media screen and (max-width: 1023px){
                    '.$addon_id.' .news_list ul li:hover .item .box_img img{
                        transform: translate(0, 0) scale(1.06);
                    }
                    '.$addon_id.' .news_list ul li .item{
                        border-radius: 5px;
                    }
                    '.$addon_id.' .news_list ul li .item .text{
                        padding: 10px;
                    }
                    '.$addon_id.' .news_list ul li {
                        width: 100%;
                        margin-bottom:10px;
                    }
                    '.$addon_id.' .news_list ul li .item .box_img img {
                        width: 100%;
                        height: auto;
                        position: relative;
                        left: auto;
                        top: auto;
                        transform: translate(0, 0);
                    }
                    '.$addon_id.' .news_list ul li .item .box_img,
                    '.$addon_id.' .news_list ul li .item .text h1 {
                        height: auto;
                    }
                    '.$addon_id.' .news_list ul li .item .text h1{
                        font-size: 14px;
                        line-height: 1.5;
                        margin-top: 0;
                        margin-bottom: 10px;
                    }
                    '.$addon_id.' .news_list ul li .item .text p{
                        font-size: 12px;
                    }
                }
            </style>';
            $output.='<div class="news_list">
            <ul id="news_list_ul">
            </ul>
            </div>';
            // 判断是否显示简介
              $output .= '<script>
                  for(i=0;i<htmlobjlist.length;i++){
                      if (htmlobjlist[i].type == 1) {
                          if (is_master) {
                              var content_url = "' . $content_url . '&id=' . base64_encode($detail_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";  
                          } else {
                              var content_url = "' . $content_url2 . $detail_page_id.'-"+htmlobjlist[i].id+".html";
                          }
                      } else {
                          if (is_master) {
                              var content_url = "' . $content_url . '&id=' . base64_encode($product_page_id) . '&detail="+htmlobjlist[i].id+"&Itemid=0&layout_id=' . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '";
                          } else {
                              var content_url = "' . $content_url2 .$product_page_id . '-"+htmlobjlist[i].id+".html";
                          }
                      }
                    if (htmlobjlist[i].type == 1) {
                        
                        if(htmlobjlist[i].images){
                            htmldata += `
                            <li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
                                <a target="_blank" href="${content_url}" class="item hover_img">
                                    <div class="box_img">
                                        <img src="${htmlobjlist[i].images}" alt="">
                                    </div>
                                    <div class="text">
                                        <h1 class="txt-cut">${htmlobjlist[i].title}</h1>
                                        <p>${formatDate(htmlobjlist[i].created)}<span>|</span>'.$art73_label.'</p>
                                    </div>
                                </a>
                            </li>
                            `
                        }else{
                            htmldata += `
                            <li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
                                <a target="_blank" href="${content_url}" class="item hover_img">
                                    <div class="text">
                                        <h1 class="txt-cut">${htmlobjlist[i].title}</h1>
                                        <p>${formatDate(htmlobjlist[i].created)}<span>|</span>'.$art73_label.'</p>
                                    </div>
                                </a>
                            </li>
                            `
                        }
                        
                    }
                    htmldata = htmldata.replace(/\/https/g,"https")
                    htmldata = htmldata.replace(/\/http/g,"http")
                    htmldata = htmldata.replace(/\/\/index\.php/g,"\/index\.php")
                    htmldata = htmldata.replace(/\/component\/jwpagefactory\/component\/jwpagefactory/g,"\/component\/jwpagefactory")
                  } 
                  if(htmlobjlist[0]){
                    jQuery("#news_list_ul").append(htmldata);
                  }
                </script>';

      }
        return $output;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';

        return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
    }

    public static function getTemplate()
    {
        $output = '
      <# if(data.art_type_selector == "art01") { #>
        <#
        var addonId = "jwpf-addon-"+data.id;
        #>

      <style type="text/css">

      *{
        padding: 0;
        margin: 0;
    }
    .art_list_id .jwpf-article-info-wrap>h3>a{
      font-size:16px;
      font-weight:400 !important;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 100%;
      display: block;
    }
    .art_list_id .jwpf-article-info-wrap .jwpf-meta-date{
      font-size:{{ data.art_date_font_size }}px !important;
      color:#999 !important;
    }
    .art_list_id .jwpf-article-info-wrap .jwpf-article-introtext{
      font-size:14px;
      color:#666;
      line-height:1.7 !important;
    }

        #{{ addonId }} li a{
         
        }
        #{{ addonId }} li a:hover{
          background-color: {{ data.link_bg_hover }};
        }
      </style>
      <div class="art_list_id" style="display: flex;flex-wrap: wrap;">
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">
            <a class="jwpf-article-img-wrap" href="#" itemprop="url">
              <img class="jwpf-img-responsive" src="/images/blog/b2ap3_large_joomlaseo_cover.jpg" alt="标题" itemprop="thumbnailUrl" loading="lazy">
            </a>
            <div class="jwpf-article-info-wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
              <# if(data.show_intro){ #>
                <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
                <# } #>
            </div>
          </div>
        </div>
      </div>
      <# } #>


      <# if(data.art_type_selector == "art05") { #>
        <style>
        #{{ addonId }}.content-box li a{
        font-size: 18px;
        height: 65px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #505050;
        line-height: 34px;
        padding-top: 20px;
        text-decoration:none;
    }

    #{{ addonId }}.content-box li{
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #505050;
      line-height: 34px;
      border-bottom: 1px solid #E5E5E5;
      padding-top: 20px;
      padding-bottom: 20px;
    }

    #{{ addonId }} .content-box li .list-font{
        font-size: {{ data.art_date_font_size }}px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        line-height: 30px;
    }

    #{{ addonId }} .title-font{
        width: 64px;
        height: 16px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #272831;
        margin-left: 40px;
    }
</style>
    <div class="title-font">搜索结果</div>
    <ul class="content-box">
        <li>
            <a href="">
            哈尔滨这几年在老道外新建了这么个城区——中华巴洛克。
        </a>
            <div class="list-font">新闻 丨 2021.06-18</div>
        </li>
        <li>
            <a href="">
            据说这座建筑群是全世界最大的巴洛克建筑群，它是北京的后海，上海的新天地。
        </a>
            <div class="list-font">新闻 丨 2021.06-18</div>
            
        </li>
        <li>
            <a href="">
            「中华巴洛克」是西洋建筑与中国建筑艺术在立面装饰技巧上的有机结合，既保留了巴洛克建筑的精美造型和装饰手法，强调追求新颖奇特、富丽堂皇的艺术效果。
        </a>
            <div class="list-font">新闻 丨 2021.06-18</div>
        </li>
        <li>
            <a href="">
            联合国人居范例奖评选专家和国际建筑艺术专家来哈尔滨考察后，对这片建筑群评价：无论是从巴洛克建筑的数量、还是它的历史厚重感来说。
        </a>
            <div class="list-font">新闻 丨 2021.06-18</div>
        </li>
    </ul>
      <# } #>

      <style type="text/css">


      </style>
      <# if(data.show_page){ #>
      <div style="text-align: right;" id="barcon" name="barcon">共10条数据  分1页  当前第1页  首页 &lt;上一页 下一页 &gt; 尾页</div>
      <# } #>



      <# if(data.art_type_selector == "art04") { #>

        <style type="text/css">
        .min_wrap {
          display:flex;
          justify-content: space-between;
          margin-top:10px;
        }

        </style>

        <#
        var addonId = "jwpf-addon-"+data.id;
        #>
               <div class="art_list_id" style="display: flex;flex-wrap: wrap;">
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>

            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>

            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>

            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
      
            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
      
            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
  
            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
        <div class="jwpf-col-sm-{{ 12 / data.columns }}">
          <div class="jwpf-addon-article">

            <div class="jwpf-article-info-wrap min_wrap">
              <h3>
                <a style="font-size:{{data.art_title_font_size}}px;text-align:{{data.art_title_selector}}" href="#" itemprop="url">我是标题</a>
              </h3>
              <div class="jwpf-article-meta" style="text-align:{{data.art_data_selector}}">
                <span class="jwpf-meta-date" itemprop="datePublished">2020-2-21</span>
              </div>
 
            </div>
            <# if(data.show_intro){ #>
              <div style="font-size:{{data.art_desc_font_size}}px;color:{{data.art_desc_font_color}}" class="jwpf-article-introtext">我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介</div>
              <# } #>
          </div>
        </div>
      </div>
       <# } #>
       <# if(data.art_type_selector == "art06") { #>
        <#
        var addonId = "jwpf-addon-"+data.id;
        #>
          <style>
          #{{ addonId }}  *{
              margin: 0;
              padding: 0;
          }
          #{{ addonId }}  a:hover{
              text-decoration: none;
          }
          #{{ addonId }}  .news_list {
              overflow: hidden;
              padding-bottom: 20px;
          }
          #{{ addonId }}  .news_list ul {
              padding: {{ data.padding_art73 }};
              position: relative;
              margin: 0;
          }
          #{{ addonId }}  .news_list ul li {
              width: {{ data.li_width_art73 }}%;
              padding: {{ data.li_padding_art73 }};
              float: left;
              margin-bottom: {{ data.li_bottom_art73 }}px;
              list-style: none;
          }
          #{{ addonId }}  .news_list ul li .item {
              display: block;
              background-color: #ffffff;
              border-radius: 10px;
              overflow: hidden;
          }
          #{{ addonId }}  .box_img {
              position: relative;
              overflow: hidden;
          }
          #{{ addonId }}  .news_list ul li .item .box_img {
              height: 250px;
          }
          #{{ addonId }}  .box_img img {
              width: auto;
              height: 100%;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              display: block;
              transition: 0.5s;
          }
          #{{ addonId }}  .news_list ul li:hover .item .box_img img{
              transform: translate(-50%, -50%) scale(1.06);
          }
          #{{ addonId }}  .news_list ul li .item .text {
              padding: 20px;
          }
          #{{ addonId }}  .txt-cut {
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
          }
          #{{ addonId }}  .news_list ul li .item .text h1 {
              height: 60px;
              line-height: 30px;
              overflow: hidden;
              color: #000;
              margin-bottom: 26px;
              font-size: 18px;
              transition: all 0.3s;
              font-weight: bold;
          }
          #{{ addonId }}  .news_list ul li:hover .item .text h1{
              color: #d90029;
          }
          #{{ addonId }}  .news_list ul li .item .text p {
              transition: all 0.5s ease-in-out;
              color: #b2b2b2;
              font-size: 14px;
              line-height: 1.6;
          }
          #{{ addonId }}  .news_list ul li:hover .item .text p{
              color: #d90029;
          }
          #{{ addonId }}  .news_list ul li .item .text p span {
              margin: 0 5px;
              display: inline-block;
          }
          @media screen and (max-width: 1400px){
              #{{ addonId }}  .news_list ul li .item .box_img {
                  height: 200px;
              }
          }
          @media screen and (max-width: 1200px){
              #{{ addonId }}  .news_list ul li .item .box_img {
                  height: 180px;
              }
          }
          @media screen and (max-width: 1023px){
              #{{ addonId }}  .news_list ul li:hover .item .box_img img{
                  transform: translate(0, 0) scale(1.06);
              }
              #{{ addonId }}  .news_list ul li .item{
                  border-radius: 5px;
              }
              #{{ addonId }}  .news_list ul li .item .text{
                  padding: 10px;
              }
              #{{ addonId }}  .news_list ul li {
                  width: 100%;
                  margin-bottom:10px;
              }
              #{{ addonId }}  .news_list ul li .item .box_img img {
                  width: 100%;
                  height: auto;
                  position: relative;
                  left: auto;
                  top: auto;
                  transform: translate(0, 0);
              }
              #{{ addonId }}  .news_list ul li .item .box_img,
              #{{ addonId }}  .news_list ul li .item .text h1 {
                  height: auto;
              }
              #{{ addonId }}  .news_list ul li .item .text h1{
                  font-size: 14px;
                  line-height: 1.5;
                  margin-top: 0;
                  margin-bottom: 10px;
              }
              #{{ addonId }}  .news_list ul li .item .text p{
                  font-size: 12px;
              }
          }
          </style>
          <div class="news_list">
          <ul><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=2&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/5.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">专业“捡漏”，这届年轻人真会过日子</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=3&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/2.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">阅文：我只是IP的搬运工</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=4&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/10.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">“工业化”，腾讯、网易、阿里游戏下个“突破点”？</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=5&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/4.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">两年来，互动视频发生了什么变化</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=6&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/8.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">我不是人：一年发论文180篇，横跨多个学科领域，收获99次引用</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=7&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/9.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">高空抛物悲剧频出，AI 监控系统：让我来「罩」着你</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=8&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/1.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">90%的“节点营销”，都是好创意、坏营销</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li><li class="wow fadeInUp animated animated" data-wow-delay="0.1s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.1s;">
          <a target="_blank" href="/index.php/component/jwpagefactory/?view=page&amp;id=MA==&amp;detail=9&amp;Itemid=0&amp;layout_id=5454&amp;site_id=0&amp;company_id=0&amp;catid_id=0&amp;catid=0" class="item hover_img">
              <div class="box_img">
                  <img src="/images/blog/7.jpg" alt="">
              </div>
              <div class="text">
                  <h1 class="txt-cut">视频号里为什么还没有出现电商巨头？</h1>
                  <p>04-22<span>|</span>{{ data.art73_label }}</p>
              </div>
          </a>
      </li></ul>
          </div>
        <# } #>';


        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }


    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

}
