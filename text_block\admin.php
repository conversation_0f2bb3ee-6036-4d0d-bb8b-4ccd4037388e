<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_text_block',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TEXT_BLOCK'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TEXT_BLOCK_DESC'),
		'category' => '常用插件',
		'attr' => array(
			'general' => array(

				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
				// Title
				'title' => array(
					'type' => 'textarea',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
						'p' => 'p',
						'span' => 'span',
						'div' => 'div'
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),

				'font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'-10px' => '-10px',
						'-9px' => '-9px',
						'-8px' => '-8px',
						'-7px' => '-7px',
						'-6px' => '-6px',
						'-5px' => '-5px',
						'-4px' => '-4px',
						'-3px' => '-3px',
						'-2px' => '-2px',
						'-1px' => '-1px',
						'0px' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),


				// Content
				'text' => array(
					'type' => 'editor',
					'title' => JText::_('内容(跳转链接请勿直接放入预览地址)'),
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
					'std' => '我为你祈祷，为你奉献。整只鱼有一种孤栖和非孤栖的习性。我的心很痛，我的心很痛，我的心很痛，我的心很痛。非常聪明的人，这是不可能的。我为你祈祷，为你奉献。我的生命有三分之一的危险。所有的元素都是元素。人们常用刺鼻龙。'
				),

                // 2021.08.26 新增客户端获取
                //文本块内容来源
                'text_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('从客户端获取（客户端-内容管理-信息管理）'),
                    'desc' => JText::_('开启后选择客户端-内容管理-信息管理 模块中上传的资料名称对应文本块内容'),
                    'std' => 0
                ),
				// 文本块 内容类型
                'text_id' => array(
                    'type' => 'select',
                    'title' => JText::_('选择文本块展示内容'),
                    'desc' => JText::_('对应客户端上传的内容名称，如果不选默认显示左侧编辑器的内容'),
                   	'values' => JwPageFactoryBase::getTInfoList($site_id, $company_id)['list'],

                    'depends' => array(
                        array('text_from', '=', 1),
                    ),
                ),
                // 2021.08.26 新增客户端获取

				'indent' => array(
					'type' => 'checkbox',
					'title' => JText::_('首行缩进'),
					'desc' => JText::_('首行缩进'),
					'std' => 0,
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),
				'alignment' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => '',
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'text_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY_DESC'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-content { font-family: "{{ VALUE }}"; }'
					),
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'text_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_SIZE'),
					'std' => '',
					'max' => 400,
					'responsive' => true,
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'text_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_LINE_HEIGHT'),
					'std' => '',
					'max' => 400,
					'responsive' => true,
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'text_fontweight' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONTWEIGHT'),
					'values' => array(
						100 => 100,
						200 => 200,
						300 => 300,
						400 => 400,
						500 => 500,
						600 => 600,
						700 => 700,
						800 => 800,
						900 => 900,
					),
					'std' => '',
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'dropcap' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP_DESC'),
					'std' => 0,
                    'depends' => array(
                        array('text_from', '!=', 1),
                    ),
				),

				'dropcap_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP_COLOR_DESC'),
					'depends' => array(
					    array('dropcap', '=', 1),
                        array('text_from', '!=', 1)
                    ),
				),

				'dropcap_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_DROPCAP_FONT_SIZE_DESC'),
                    'depends' => array(
                        array('dropcap', '=', 1),
                        array('text_from', '!=', 1)
                    ),
					'min' => 0,
					'max' => 200,
					'responsive' => true,
				),

                /*纵向排列*/
                'column'=> array(
                    'type'=>'checkbox',
                    'title'=>'是否纵向排列',
                    'std'=>0
                ),
                'writing_mode'=> array(
                    'type'=>'select',
                    'title'=>'排列方式',
                    'std'=>'',
                    'values'=>array(
                        'vertical-lr'=>'从左向右',
                        'vertical-rl'=>'从右往左'
                    ),
                    'depends'=>array(
                        array('column','=',1)
                    )
                ),
                'column_title_margin'=> array(
                    'type'=>'margin',
                    'title'=>'标题外边距',
                    'std'=>'',
                    'max'=>'0 0 0 0',
                    'depends'=>array(
                        array('column','=',1)
                    )
                ),
                /*纵向排列end*/

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),

			),
		),
	)
);
