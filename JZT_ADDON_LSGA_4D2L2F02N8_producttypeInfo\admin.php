<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(

    array(
        'type' => 'general', //插件
        'addon_name' => 'JZT_ADDON_LSGA_4D2L2F02N8_producttypeInfo',
        'title' => '产品分类详情',
        'desc' => '',
        'category' => '产品', //插件分组
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'layout_id' => array(
                    'std' => $layout_id,
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
                    )
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'pro_type' => array(
                    'type' => 'select',
                    'title' => '选择内容布局',
                    'desc' => '选择内容布局',
                    'values' => array(
                        'type1' => '标题覆盖图片',
                        'type2' => '标题在图片下方',
                        'type3' => '产品布局3',
                        'type4' => '自定义',
                    ),
                    'std' => 'type1',
                ),
                // 'style' => array(
                //     'type' => 'select',
                //     'title' => JText::_('样式'),
                //     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DESC'),
                //     'values' => array(
                //         'modern' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_MODERN'),
                //         'tabs' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DEFAULT'),
                //         'pills' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_PILLS'),
                //         'lines' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_LINES'),
                //         'custom' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_CUSTOM'),
                //     ),
                //     'std' => 'tabs'
                // ),
                'dh_db_img' => array(
                    'type' => 'select',
                    'title' => JText::_('导航头部图片开启'),
                    'desc' => JText::_('导航头部图片开启'),
                    'values' => array(
                        'k' => JText::_('开'),
                        'g' => JText::_('关'),
                    ),
                    'std' => 'k'
                ),
                'dh_db_img_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片圆角'),
                    'desc' => JText::_('导航头部图片圆角'),
                    'max' => 30,
                    'min' => 0,
                ),
                'dh_db_img_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片宽度'),
                    'desc' => JText::_('导航头部图片宽度'),
                    'max' => 400,
                    'min' => 50,
                    'std' => 50
                ),
                'dh_db_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片高度'),
                    'desc' => JText::_('导航头部图片高度'),
                    'max' => 400,
                    'min' => 50,
                    'std' => 50
                ),
                // 'type_parent' => array(
                //     'type' => 'select',
                //     'title' => '分类显示',
                //     'desc' => '分类显示',
                //     'values' => array(
                //         'type1' => '一级分类',
                //         'type2' => '二级分类',
                //         'all' => '混合显示'
                //     ),
                //     'std' => 'type1',
                //     'depends' =>array(
                //         array('style' ,'!=', ''),
                //         array('pro_type' ,'!=', 'type4'),
                //     ),
                // ),
                // Repeatable Item
                'jw_tab_item'         => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEMS'),
                    'attr'  => array(
                        'title'         => array(
                            'type'  => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE'),
                            'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE_DESC'),
                            'std'   => '点我'
                        ),
                        'subtitle'      => array(
                            'type'  => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE'),
                            'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE_DESC'),
                            'std'   => ' ',
                        ),
                        'image_or_icon' => array(
                            'type'   => 'buttons',
                            'title'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE'),
                            'std'    => 'icon',
                            'values' => array(
                                array(
                                    'label' => '图标',
                                    'value' => 'icon'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'image'
                                ),
                            ),
                            'tabs'   => true,
                        ),
                        'icon'          => array(
                            'type'    => 'icon',
                            'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_DESC'),
                            'std'     => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'icon'),
                            )
                        ),

                        'image'         => array(
                            'type'    => 'media',
                            'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE'),
                            'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE_DESC'),
                            'std'     => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'image'),
                            )
                        ),
                        'content'       => array(
                            'type' => 'editor',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT'),
                            'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT_DESC'),
                            'std'   => '我们的高级生活谴责特里·理查森和鱿鱼。3狼月办公室，非丘比特滑板dolor早午餐。食品车藜麦nesciunt laborum eiusmod。早午餐3狼月tempor, sunt aliqua把一只鸟在它鱿鱼单一起源咖啡nulla假设enda shoreditch等。'
                        ),
                        'closeClick'    => array(
                            'type'  => 'checkbox',
                            'title' => '关闭点击事件',
                            'std'   => 0
                        )

                    ),
                    'depends' =>array(
                        array('pro_type' ,'=', 'type4'),
                    ),
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从第n个分类开始显示',
                    'std' => '1'
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类(0为不限,混合显示时该设置无效)',
                    'desc' => '显示n条分类',
                    'std' => '10'
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),

                'k2catid' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID_DESC'),
                    'depends' => array('resource' => 'k2'),
                    'values' => JwPageFactoryBase::k2CatList(),
                    'multiple' => true,
                ),

                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10'
                ),

                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('PC及平板列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2'
                ),
                'columns_xs' => array(
                    'type' => 'number',
                    'title' => JText::_('手机列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2'
                ),

                'nav_wrap' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航换行'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                ),
                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                ),
                'page_count' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页隐藏数量'),
                    'std' => 0,
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                ),
                'page_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页左右位置调整'),
                    'std' => 0,
                    'max' =>300,
                    'min' =>-300,
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                ),
                'page_top_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页上下位置调整'),
                    'std' => 0,
                    'max' =>300,
                    'min' =>-300,
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                ),
                'show_page_col' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码颜色设置'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                ),
                'page2_tab_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#000',
                ),
                'page2_tab_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#100c0d',
                ),
                'page2_tab_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#ffffff',
                ),

                'page2_tab_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#69d640',
                ),
                'page2_tab_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#100d0d',
                ),
                'page2_tab_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#169fcb',
                ),
                'page2_tab_fontcolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#1a22b3',
                ),
                'page2_tab_bordercolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#0e0707',
                ),
                'page2_tab_bgcolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                    ),
                    'std' => '#984706',
                ),

                'separator_options' => array(
                    'type' => 'separator',
                    'title' => '内容插件选项'
                ),
                'hover_border_pub' => array(
                    'type' => 'buttons',
                    'title' => '边框状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type3')
                    ),
                ),
                'normal_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('正常边框宽度'),

                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => 2,
                ),
                'normal_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常边框颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => 'rgba(21, 228, 116, 0)',
                ),
                'normal_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常字体背景颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => '#000',
                ),
                'hover_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入后边框宽度'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => 2,
                ),
                'hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后边框颜色'),

                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => 'rgba(21, 228, 116, 0)',
                ),
                'hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后字体背景颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),

                    ),
                    'std' => '#000',
                ),
                'pro_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type1'),
                        array('hover_border_pub' ,'=','normal'),
                    ),
                    'std' => '#ffffff',
                ),
                'pro_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type1'),
                        array('hover_border_pub' ,'=','hover')
                    ),
                    'std' => '#ffffff',
                ),
                // 'pro_font_color_bg_type2' => array(
                //     'type' => 'color',
                //     'title' => JText::_('字体背景颜色'),
                //     'std' => '#ffffff',
                //     'depends' => array(
                //         array('pro_type' ,'=', 'type2'),
                //         array('hover_border_pub' ,'=', 'normal')
                //     ),
                // ),
                // 'pro_font_color_bg' => array(
                //     'type' => 'color',
                //     'title' => JText::_('字体背景颜色'),
                //     'std' => '#000000',
                //     'depends' => array(
                //         // array('pro_type' ,'=', 'type1'),
                //         array('hover_border_pub' ,'=', 'normal')
                //     ),
                // ),

                'fix_img_height' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否固定内容图片高度'),
                    'std' => 0,
                ),
                'fix_img_height_input' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置pc端图片高度'),
                    'depends' => array('fix_img_height' => 1),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                ),
                'fix_img_height_input_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置手机端图片高度'),
                    'depends' => array('fix_img_height' => 1),
                    'max' => 600,
                    'min' => 0,
                    'std' => 100,
                ),

                'pro_title_height_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题高度'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                    ),
                    'max' => 300,
                    'std' => '50',
                ),
                'pro_title_height_type2_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('标题间距'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                    ),
                    'std' => '10px 0px 10px 0px',
                ),


                'pro_font_color_type2_title' => array(
                    'type' => 'color',
                    'title' => JText::_('正常标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                    ),
                    'std' => '#000000',
                ),
                'pro_font_color_type2_title_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','hover'),
                    ),
                    'std' => '#000000',
                ),
//                'content_margin_type2_title' => array(
//                    'type' => 'margin',
//                    'title' => JText::_('标题间距'),
//                    'responsive' => true,
//                    'depends' => array('pro_type' => 'type2'),
//                    'std' => '',
//                ),

                'pro_font_title_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 200,
                    'min' => 0
                ),
                'title_font_position' => array(
                    'type' => 'select',
                    'title' => '标题位置设置',
                    'std' => 'center',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右',
                    ),
                    'depends' => array('pro_type' => 'type2'),
                ),
                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'depends' => array('pro_type' => 'type2'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => 1,
                ),
                'pro_font_color_type2_intext' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('pro_type','=','type2'),
                    ),
                    'std' => '#000000',
                ),
//                'content_margin_type2_intext' => array(
//                    'type' => 'margin',
//                    'title' => JText::_('简介间距'),
//                    'responsive' => true,
//                    'depends' => array('pro_type' => 'type2'),
//                    'std' => '',
//                ),
                'pro_font_color_type2_intext_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标进入简介字体颜色'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('pro_type','=','type2'),
                    ),
                    'std' => '#000000',
                ),
                'pro_font_intext_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('pro_type','=','type2'),
                    ),
                    'max' => 200,
                    'min' => 0
                ),
                'img_animated' => array(
                    'type' => 'select',
                    'title' => '选择图片动画',
                    'desc' => '图片动画',
                    'depends' => array('pro_type' => 'type2'),
                    'values' => array(
                        'animated1' => '无',
                        'animated2' => '放大',
                    ),
                    'std' => 'animated1',
                ),
                'box_type2_shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('盒子阴影颜色'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => '#ffffff',
                ),
                'box_type2_shadow_x' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影水平偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_Y' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影垂直偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_mh' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影模糊'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_kz' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影扩展'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'show_title' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('标题是否固定显示'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'depends' => array('pro_type' => 'type1'),
                    'std' => '0'
                ),
                'pro_font_color_bg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体背景高度'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 2000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array('md' => 40, 'sm' => 20, 'xs' => 20),
                ),
//                'content_fontsize' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('标题字体大小'),
//                    'depends' => array('pro_type' => 'type1'),
//                    'max' => 100,
//                    'responsive' => true,
//                    'std' => array('md' => 14, 'sm' => 12, 'xs' => 12),
//                ),
                'content_fontsize_bt' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 100,
                    'std' => 14,
                ),

//                'nav_width' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('导航宽度'),
//                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
//                    'responsive' => true,
//                    'max' => 100,
//                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_style', '=', 'normal'),
//                        array('custom_tab_style', '=', 'navigation'),
//                    ),
//                ),
                'tab_c' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航设置')
                ),
                // 非自定义导航块位置
                'nav_all_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'nav_font_position' => array(
                    'type' => 'select',
                    'title' => '导航字体居中设置',
                    'std' => 'flex-start',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'depends' => array(
                        array('style', '=', 'custom')
                    ),
                ),
                'nav_all_bg_color' => array(
                    'type' => 'color',
                    'title' => '导航条背景颜色',
                    'std' => '#000',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'nav_all_font_color' => array(
                    'type' => 'color',
                    'title' => '导航条字体颜色',
                    'std' => '#00000',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'nav_font_positions' => array(
                    'type' => 'select',
                    'title' => '导航字体居中设置',
                    'std' => 'left',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右',
                    ),
                    'depends' => array(
                        array('style', '!=', 'custom')
                    ),
                ),
                'type3fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入标题字体大小'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                    ),
                    'std' => '22',
                ),
                'type3fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                    ),
                    'std' => '#ffffff',
                ),
                'type3bgclolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入背景颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                    ),
                    'std' => 'rgba(0, 0, 0, 0.5)',
                ),




                // 自定义导航块
                'nav_block_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '=', 'custom'),array('nav_position', '=', 'nav-top')),
                ),

                //新修改
                //导航头部图片
                'nav_img' => array(
                    'type' => 'media',
                    'title' => '导航头部图片',
                    'std' => 'https://www.longcai.com/uploadfile/32858_20190510151455.jpg',
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                //导航头部图片宽高
                'nav_img_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片宽度'),
                    'responsive' => true,
                    'max' => 100,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_img_h' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片高度'),
                    'responsive' => true,
                    'max' => 300,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_img_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片和导航之间的空间'),
                    'responsive' => true,
                    'max' => 300,
                    'std' => array('md' => 10, 'sm' => 10, 'xs' => 10),
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                //导航外层边框
                'nav_bor' => array(
                    'type' => 'margin',
                    'title' => '导航外层边框',
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                    'std' => '1px 1px 1px 1px',
                ),
//				'nav_bor_m' => array(
//					'type' => 'margin',
//					'title' => '导航外层内边距',
//					'responsive' => true,
//					'depends' => array(
//						array('style', '=', 'custom'),
//					),
//					'std' => '0px 0px 5px 0px',
//				),
                //导航外层边框颜色
                'nav_bor_color' => array(
                    'type' => 'color',
                    'title' => '导航外层边框颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),
                //导航外层背景颜色
                'nav_back_color' => array(
                    'type' => 'color',
                    'title' => '导航外层背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                    ),
                ),

                //7.5


                //7.5


                //新修改

                //Custom Tab Style
                'custom_tab_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_OPTIONS'),
                    'std' => 'navigation',
                    'values' => array(
                        array(
                            'label' => '导航',
                            'value' => 'navigation'
                        ),
                        array(
                            'label' => '导航图标或者图片',
                            'value' => 'icon_image'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom')
                    ),
                ),

                'tab_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_SEPERATOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'nav-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'nav-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'nav-top' => JText::_('上')
                    ),
                ),


                'nav_gutter' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER_DESC'),
                    'responsive' => true,
                    'max' => 100,
                    'std' => array('md' => 15),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),











                'tab_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航样式'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),

                'nav_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('一级导航样式'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'responsive' => true,
                    'max' => 100,
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BG_COLOR'),
                    'std' => '#463f43',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'max' => 400,
                    'responsive' => true,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_STYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_RADIUS'),
                    'std' => '70',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('导航空白空间'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '0px 10px 5px 10px',
                ),
                'nav_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_PADDING'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                'nav_text_align' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_TEXT_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'left',
                ),
                //Hover Nav Style
                'hover_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BG'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Active Nav Style
                'active_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_COLOR'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Icon or Image style
//                'image_or_icon_style' => array(
//                    'type' => 'buttons',
//                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE_STYLE'),
//                    'std' => 'icon_style',
//                    'values' => array(
//                        array(
//                            'label' => '图标样式',
//                            'value' => 'icon_style'
//                        ),
//                        array(
//                            'label' => '图片样式',
//                            'value' => 'image_style'
//                        ),
//                    ),
//                    'tabs' => true,
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('custom_tab_style', '=', 'icon_image'),
//                    ),
//                ),
                //Icon Style
//                'icon_separator' => array(
//                    'type' => 'separator',
//                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ICON_OPTIONS'),
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('custom_tab_style', '=', 'icon_image'),
//                        array('image_or_icon_style', '=', 'icon_style'),
//                    ),
//                ),
                'nav_icon_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'icon_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_SIZE'),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => 16),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_HOVER'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_ACTIVE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_MARGIN'),
                    'responsive' => true,
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'std' => '',
                ),
                //Image Style
                'image_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_IMG_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'nav_image_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_ALIGNMENT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'image_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_HEIGHT'),
                    'responsive' => true,
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_WIDTH'),
                    'responsive' => true,
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'responsive' => true,
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'std' => '',
                ),
                //Content Style
                'content_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_backround' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),

//                'content_lineheight' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_LINEHEIGHT'),
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('custom_tab_style', '=', 'content'),
//                    ),
//                    'responsive' => true,
//                    'max' => 400,
//                    'std' => array('md' => ''),
//                ),
                'content_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-tab-custom-content > div{ font-family: {{ VALUE }}; }'
                    )
                ),
                'content_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONTSTYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER'),
                    'std' => 1,
                    'max' => 20,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTEN_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'show_boxshadow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW_DESC'),
                    'std' => 1,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_HORIZONTAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_VERTICAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_blur' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_BLUR'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_spread' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_SPREAD'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_MARGIN'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '',
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_PADDING'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),










                'nav_style2' => array(
                    'type' => 'buttons',
                    'title' => JText::_('二级导航样式'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal2',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal2'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover2'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active2'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_width2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'responsive' => true,
                    'max' => 100,
                    'std' => array('md' => 100, 'sm' => 100, 'xs' => 100),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#0d0d0d',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_bg_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_BG_COLOR'),
                    'std' => '#eeeeee',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_fontsize2' => array(
                    'type' => 'slider',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'max' => 400,
                    'responsive' => true,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight2' => array(
                    'type' => 'slider',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family2' => array(
                    'type' => 'fonts',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style2' => array(
                    'type' => 'fontstyle',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_STYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border2' => array(
                    'type' => 'margin',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_radius2' => array(
                    'type' => 'slider',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_margin2' => array(
                    'type' => 'margin',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_MARGIN'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '0px 0px 5px 0px',
                ),
                'nav_padding2' => array(
                    'type' => 'padding',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_NAV_PADDING'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                'nav_text_align2' => array(
                    'type' => 'select',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_TAB_TEXT_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'left',
                ),
                'nav_font_width' => array(
                    'type' => 'slider',
                    'title' => '导航文字自定义位置',
                    'std' => '',
                    'max' => 200,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Hover Nav Style
                'hover_tab_bg2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BG'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_width2' => array(
                    'type' => 'margin',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Active Nav Style
                'active_tab_bg2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BG'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_COLOR'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_width2' => array(
                    'type' => 'margin',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_color2' => array(
                    'type' => 'color',
                    'title' => '二级' . JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),

                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);