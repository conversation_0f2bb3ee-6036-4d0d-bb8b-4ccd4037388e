<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonEvent_calendar extends JwpagefactoryAddons
{

    public function render()
    {

        $company_id = $_GET['company_id'] ?? 0;
        $site_id    = $_GET['site_id'] ?? 0;
        $layout_id  = $_GET['layout_id'] ?? 0;


        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        //指定更多跳转页ID
        $gengduo = (isset($settings->gengduo)) ? $settings->gengduo : 0;
		//标题
        $title = (isset($settings->title) && $settings->title) ? $settings->title : "赛事活动";
        //取出赛事活动分类数据
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $pids=array();
        $pids[]=2;
        $types = JwpagefactoryHelperCategories::getCatematch($pids,$company_id, $site_id);



        $output = '<div id="calendar-app" v-cloak>
			<div class="c-top flex flex-between">
				<div class="left-info flex">
					<div class="title">'.$title.'</div>
					<div class="flex tags">';
                    if($types){

                        foreach ($types as $k1 => $v1){

    						$output .='<div class="item_">';
                            if($k1%2==0){
                                $output .='<img src="https://oss.lcweb01.cn/joomla/20210629/ce954e3f70b817bfc40cc94d175bf1f3.png" alt="" />';
                                $output .='<span class="color-'.$v1["tag_id"].'"  data-color="color1"> '.$v1["title"].'</span>';
                            }else{
                                $output .='<img src="https://oss.lcweb01.cn/joomla/20210629/64c70fe8d07f1901c8513c812afb0499.png" alt="" />';
                                $output .='<span class="color-'.$v1["tag_id"].'"  data-color="color2"> '.$v1["title"].'</span>';
                            }
    							
    						$output .='</div>';
                        }
                    }else{
                        $output .='<div class="item_">
                            <img src="https://oss.lcweb01.cn/joomla/20210629/64c70fe8d07f1901c8513c812afb0499.png" alt="" />
                            <span>赛事活动</span>
                        </div>'; 
                    }

				$output .='</div>
				</div>
				<div class="right-more">
					<a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($gengduo) . "&detail=" . '0' . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id ) . '">更多>></a>
				</div>
			</div>
			<div class="calendar-box">
				<div class="action-box flex flex-between">
					<div class="item">
					    <!-- 选择年 当前年份的前后十年 -->
						<select v-model="year">
							<option :value="nowYear - 10 + index" v-for="(item, index) in 10">{{ nowYear - 10 + index }}</option>
							<option :value="nowYear">{{ nowYear }}</option>
							<option :value="nowYear + item" v-for="(item, index) in 10">{{ nowYear + item }}</option>
						</select>
					</div>
					<div class="item">
					    <!-- 选择月 上翻 下翻 -->
						<span class="prev" @click="prevMonth">&lt;</span>
						<select v-model="month">
							<option :value="index" v-for="(item, index) in 12">{{ item }}月</option>
						</select>
						<span class="next" @click="nextMonth">&gt;</span>
					</div>
					<div class="item">
						<p class="to-today" @click="goToNowDate">返回今天</p>
					</div>
				</div>
				<!-- 日历 -->
				<div class="calendar">
				    <!-- 日历上面的周 -->
					<div class="week flex flex-around">
						<div class="item" v-for="(item, index) in weekInfo">{{ item }}</div>
					</div>
					<!-- 日历下面的日期 -->
					<div class="days flex">
					    <!-- 当月第一天 星期前面的日期（上月的） -->
						<template v-if="firstdayweek != 0">
							<div style="color: #999999;" class="item" v-for="(item, index) in firstdayweek" v-if="false">{{ beforedays - firstdayweek + index + 1 }}</div>
							<div style="color: #999999;" class="item" v-for="(item, index) in firstdayweek" v-if="true"></div>
						</template>
						<!-- 当月日期 -->
						<div :class="[\'item\',
                            \'days-\' + (index + 1), 
							{\'active\': isNowDate && nowDate == index + 1 }]" v-for="(item, index) in days" :data-value="index + 1" @click="dateClick(index + 1)">{{index + 1}}</div>
					</div>
				</div>
			</div>
			<!-- 日历弹框 -->
			<div class="c_alert" v-show="isShowDateInfo">
				<div class="close_" @click="isShowDateInfo = false">X</div>
				<div class="date-box">
					<div class="time">{{year}}-{{month+1}}-{{date}}</div>
					<div class="week">星期{{week | weekNumber}}</div>
					<div class="number">{{date}}</div>
				</div>
				<div class="list-box">
					<div class="list forlist">
						
					</div>
				</div>
			</div>
		</div>';
      

        return $output;
    }

    public function scripts()
    {
        return array(
            '/components/com_jwpagefactory/addons/event_calendar/assets/js/vue.js',
        );
    }

    public  function js() {

        $company_id = $_GET['company_id'] ?? 0;
        $site_id    = $_GET['site_id'] ?? 0;
        $layout_id  = $_GET['layout_id'] ?? 0;

        $yuming=$_SERVER['HTTP_HOST'];
        if($yuming=='jzt_dev_2.china9.cn'){
            $urlpath='http://jzt_dev_1.china9.cn';
        }elseif($yuming=='ijzt.china9.cn'){
            $urlpath='https://zhjzt.china9.cn';
        }else{
            $config = new JConfig();
            $urlpath = $config->jzt_url;
        }

        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //选项卡Item
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? json_encode($settings->section_tab_item) : [];
        //地图中心点名称
        $section_map_name = (isset($settings->section_map_name) && $settings->section_map_name) ? $settings->section_map_name : "";
        //地图中心点所在城市
        $section_map_city = (isset($settings->section_map_city) && $settings->section_map_city) ? $settings->section_map_city : "";
        //地图中心点经度
        $section_map_lng = (isset($settings->section_map_lng) && $settings->section_map_lng) ? $settings->section_map_lng : "";
        //地图中心点纬度
        $section_map_lat = (isset($settings->section_map_lat) && $settings->section_map_lat) ? $settings->section_map_lat : "";
        //地图中心点图标
        $section_map_centerIcon = (isset($settings->section_map_centerIcon) && $settings->section_map_centerIcon) ? $settings->section_map_centerIcon : "";
		//弹窗无数据提示
        $k_status_title = (isset($settings->k_status_title) && $settings->k_status_title) ? $settings->k_status_title : "暂无赛事信息";
        //地图中心点图标宽度
        $section_map_centerIcon_w = (isset($settings->section_map_centerIcon_w) && $settings->section_map_centerIcon_w) ? $settings->section_map_centerIcon_w : "";
        //地图中心点图标高度
        $section_map_centerIcon_h = (isset($settings->section_map_centerIcon_h) && $settings->section_map_centerIcon_h) ? $settings->section_map_centerIcon_h : "";

        $js = 'jQuery(function($){
		    var app = new Vue({
                el: \'#calendar-app\',
                data() {
                    return {
                        year: new Date().getFullYear(),		//当前选择的年
                        month: new Date().getMonth(),		//当前选择的月
                        date: null,  //当前点击日期
                        week:null,
                        nowYear: new Date().getFullYear(),		//今天所在的年
                        nowMonth: new Date().getMonth(),		//今天所在的月
                        nowDate: new Date().getDate(),		//今天所在的日
                        weekInfo: [\'周日\', \'周一\', \'周二\', \'周三\', \'周四\', \'周五\', \'周六\'],
                        days: null,		//当前年 月份的天数
                        beforedays: null,		//当前年 上月天数
                        firstdayweek: null,		//当前年 月 第一天周几
                        isShowDateInfo: false,
                        nowDatetd: new Date().getDate(),      //今天所在的日
                        nowYeartd: new Date().getFullYear(), 
                        nowMonthtd: new Date().getMonth(), 
                        info: {}
                    }
                },
                methods: {
                    init(year_, month_, date_) {
                        var nowdate = new Date(year_, month_, date_), //当前日期
                            year = nowdate.getFullYear(),
                            month = nowdate.getMonth() + 1,
                            date = nowdate.getDate(),
                            week = nowdate.getDay();
                        console.log(\'今天是\' + year + \'年\' +  + month + \'月\' + date + \'日，星期\' + week);
                        var days = new Date(year, month, 0).getDate(); //本月天数
                        var beforedays = new Date(year, month - 1 , 0).getDate(); //上月天数
                        var firstday = new Date(year, month, 0); //本月第一天的日期
                        firstday.setDate(1);
                        // console.log(firstday)
                        // console.log(nowdate)
                        var firstdayweek = firstday.getDay(); //本月第一天星期几
                        // console.log(\'firstdayweek\',firstdayweek);
                        // console.log(\'beforedays\',beforedays);
                        // console.log(days);
                        
                        this.days = days;
                        this.beforedays = beforedays;
                        this.firstdayweek = firstdayweek;
                        
                        this.year = year;
                        this.month = month - 1;
                        this.date = date;
                        //获取当月数据
                        this.changtime(year_, month_);
                    },
                    // 当前月份日历
                    goToNowDate() {
                        this.init(this.nowYear, this.nowMonth, this.nowDate);
                        var firstday = new Date(this.nowYear, this.nowMonth+1, 0); //本月第一天的日期
                        console.log("本月第一天"+firstday);
                        firstday.setDate(this.nowDate);
                        this.week=firstday.getDay();
                    },
                    changeInit() {
                        if(this.isNowDate){
                            this.goToNowDate();
                        }else {
                            this.init(this.year, this.month, 1);
                        }
                    },
                    //获取当月数据
                    changtime(year, month){
                            jQuery("' . $addon_id . ' .item").removeClass("color1");
                            jQuery("' . $addon_id . ' .item").removeClass("color2");
                            jQuery.ajax({
                                type: "POST",
                                url: "' . $urlpath . '/api/Shuju/match",
                                // url: "https://zhjzt.china9.cn/api/Shuju/match",

                                dataType: "json",
                                data: {
                                    "company_id" :' . $company_id . ',
                                    "site_id" :' . $site_id . ',
                                    "year": year,
                                    "month": month+1
                                },
                                success: function (res) {
                                    console.log(res.data);
                                    var lens=res.data.length;
                                    if(lens>0){
                                        var str="";
                                        for(var i=0;i < lens; i++){
                                            var color=jQuery("' . $addon_id . ' .color-"+res.data[i]["catid"]).data("color");
                                            console.log(color);
                                            jQuery("' . $addon_id . ' .days-"+res.data[i]["days"]).addClass(color);
                                        }
                                    }
                                }     
                            });
                        },
                    // 上月
                    prevMonth() {
                        if(this.month == 1) {
                            this.year = this.year - 1;
                            this.month = 12;
                        }else {
                            this.month --;
                        }
                    },
                    // 下月
                    nextMonth() {
                        if(this.month == 12) {
                            this.year = this.year + 1;
                            this.month = 1;
                        }else {
                            this.month ++;
                        }
                    },
                    //点击显示当天赛事数据
                    changlist(year, month,day){
                        jQuery.ajax({
                            type: "POST",
                            url: "' . $urlpath . '/api/Shuju/matchday",
                            // url: "https://zhjzt.china9.cn/api/Shuju/match",
    
                            dataType: "json",
                            data: {
                                "company_id" :' . $company_id . ',
                                "site_id" :' . $site_id . ',
                                "year": year,
                                "month": month+1,
                                "day": day
                            },
                            success: function (res) {
                                this.info = res.data
                                console.log("timelise",res.data);
                                jQuery("' . $addon_id . ' .forlist ").empty(); 
                                var lens=res.data.length;
                                if(lens>0){
                                    
                                    var str="";
                                    for(var i=0;i < lens; i++){
                                        str+=\'<div class="item" >\';
                                        str+=\'<p class="title oneline">\'+res.data[i]["title"]+\'</p>\';
                                        str+=\'<p class="address">\'+res.data[i]["address"]+\'</p>\';
                                        str+=\'</div>\';
                                    }

                                    jQuery("' . $addon_id . ' .forlist").append(str);

                                }else{
                                    var str="";

                                    str+=\'<div class="item" >\';
                                    str+=\'<p class="title oneline" style="font-size:16px">'.$k_status_title.'</p>\';
                                    str+=\'</div>\';

                                    jQuery("' . $addon_id . ' .forlist").append(str);

                                }
                            }     
                        });
                    },
                    // 点击日期
                    dateClick(date) {
                        console.log(date);
                        console.log(this.year);
                        console.log(this.month+1);
                        this.date = date;
                        var firstday = new Date(this.year, this.month+1, 0); //本月第一天的日期
                        firstday.setDate(date);
                        this.week=firstday.getDay();
                        this.isShowDateInfo = true;
                        
                        this.changlist(this.year, this.month, date);
                    }
                },
                created() {
                    this.goToNowDate();
                },
                watch: {
                    year(newV, oldV) {
                        if(Number(newV) != Number(oldV)) {
                            this.changeInit();
                        }
                    },
                    month(newV, oldV) {
                        if(Number(newV) != Number(oldV)) {
                            this.changeInit();
                        }
                    }
                },
                filters: {
                    weekNumber(data) {
                        console.log(data);
                        switch(data){
                            case 0:
                                return "日";
                            case 1:
                                return "一";

                            case 2:
                                return "二";

                            case 3:
                                return "三";

                            case 4:
                                return "四";

                            case 5:
                                return "五";

                            case 6:
                                return "六";

                        }
                    }
                },
                computed: {
                    //是否是今天所在的年月
                    isNowDate() {
                        if(this.year == this.nowYear && this.month == this.nowMonth) {
                            return true;
                        }else {
                            return false;
                        }
                    }
                }
            });
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        //选项卡背景色
        $section_tab_bgColor = (isset($settings->section_tab_bgColor) && $settings->section_tab_bgColor) ? $settings->section_tab_bgColor : "";
        //选项卡文字颜色
        $section_tab_color = (isset($settings->section_tab_color) && $settings->section_tab_color) ? $settings->section_tab_color : "";
        //选项卡文字大小
        $section_tab_fontsize = (isset($settings->section_tab_fontsize) && $settings->section_tab_fontsize) ? $settings->section_tab_fontsize : 16;
        $section_tab_fontsize_sm = (isset($settings->section_tab_fontsize_sm) && $settings->section_tab_fontsize_sm) ? $settings->section_tab_fontsize_sm : 16;
        $section_tab_fontsize_xs = (isset($settings->section_tab_fontsize_xs) && $settings->section_tab_fontsize_xs) ? $settings->section_tab_fontsize_xs : 14;
//		//选项卡选中文字大小
        $section_tab_active_fontsize = (isset($settings->section_tab_active_fontsize) && $settings->section_tab_active_fontsize) ? $settings->section_tab_active_fontsize : 16;
        $section_tab_active_fontsize_sm = (isset($settings->section_tab_active_fontsize_sm) && $settings->section_tab_active_fontsize_sm) ? $settings->section_tab_active_fontsize_sm : 14;
        $section_tab_active_fontsize_xs = (isset($settings->section_tab_active_fontsize_xs) && $settings->section_tab_active_fontsize_xs) ? $settings->section_tab_active_fontsize_xs : 14;
        //选项卡选中背景色
        $section_tab_active_bgColor = (isset($settings->section_tab_active_bgColor) && $settings->section_tab_active_bgColor) ? $settings->section_tab_active_bgColor : "";
        //选项卡选中文字颜色
        $section_tab_active_color = (isset($settings->section_tab_active_color) && $settings->section_tab_active_color) ? $settings->section_tab_active_color : "";
        //一行显示选项卡的个数
        $section_tab_num = (isset($settings->section_tab_num) && $settings->section_tab_num) ? $settings->section_tab_num : 8;
        $section_tab_num_sm = (isset($settings->section_tab_num_sm) && $settings->section_tab_num_sm) ? $settings->section_tab_num_sm : 6;
        $section_tab_num_xs = (isset($settings->section_tab_num_xs) && $settings->section_tab_num_xs) ? $settings->section_tab_num_xs : 3;
        //选项卡高度
        $section_tab_H = (isset($settings->section_tab_H) && $settings->section_tab_H) ? $settings->section_tab_H : 60;
        $section_tab_H_sm = (isset($settings->section_tab_H_sm) && $settings->section_tab_H_sm) ? $settings->section_tab_H_sm : 52;
        $section_tab_H_xs = (isset($settings->section_tab_H_xs) && $settings->section_tab_H_xs) ? $settings->section_tab_H_xs : 52;
        //选项卡外下边距
        $section_tab_mgB = (isset($settings->section_tab_mgB) && $settings->section_tab_mgB) ? $settings->section_tab_mgB : 20;
        $section_tab_mgB_sm = (isset($settings->section_tab_mgB_sm) && $settings->section_tab_mgB_sm) ? $settings->section_tab_mgB_sm : 20;
        $section_tab_mgB_xs = (isset($settings->section_tab_mgB_xs) && $settings->section_tab_mgB_xs) ? $settings->section_tab_mgB_xs : 10;
        //选项卡动画效果时间
        $section_tab_active_time = (isset($settings->section_tab_active_time) && $settings->section_tab_active_time) ? $settings->section_tab_active_time : "";
        //地图散点图标宽度
        $section_map_icon_w = (isset($settings->section_map_icon_w) && $settings->section_map_icon_w) ? $settings->section_map_icon_w : "";
        //地图散点图标高度
        $section_map_icon_h = (isset($settings->section_map_icon_h) && $settings->section_map_icon_h) ? $settings->section_map_icon_h : "";
        //左侧地图宽度
        $section_map_map_w = (isset($settings->section_map_map_w) && $settings->section_map_map_w) ? $settings->section_map_map_w : "";
        //左侧地图高度
        $section_map_map_h = (isset($settings->section_map_map_h) && $settings->section_map_map_h) ? $settings->section_map_map_h : 400;
        $section_map_map_h_sm = (isset($settings->section_map_map_h_sm) && $settings->section_map_map_h_sm) ? $settings->section_map_map_h_sm : 400;
        $section_map_map_h_xs = (isset($settings->section_map_map_h_xs) && $settings->section_map_map_h_xs) ? $settings->section_map_map_h_xs : 400;
        //右侧列表高度
        $section_map_content_h_sm = (isset($settings->section_map_content_h_sm) && $settings->section_map_content_h_sm) ? $settings->section_map_content_h_sm : 400;
        $section_map_content_h_xs = (isset($settings->section_map_content_h_xs) && $settings->section_map_content_h_xs) ? $settings->section_map_content_h_xs : 500;
        //右侧列表标题文字大小
        $section_content_title_fontsize = (isset($settings->section_content_title_fontsize) && $settings->section_content_title_fontsize) ? $settings->section_content_title_fontsize : 16;
        $section_content_title_fontsize_sm = (isset($settings->section_content_title_fontsize_sm) && $settings->section_content_title_fontsize_sm) ? $settings->section_content_title_fontsize_sm : 16;
        $section_content_title_fontsize_xs = (isset($settings->section_content_title_fontsize_xs) && $settings->section_content_title_fontsize_xs) ? $settings->section_content_title_fontsize_xs : 16;
        //右侧列表标题文字颜色
        $section_content_title_color = (isset($settings->section_content_title_color) && $settings->section_content_title_color) ? $settings->section_content_title_color : "";
        //右侧列表标题下边距
        $section_content_title_mgB = (isset($settings->section_content_title_mgB) && $settings->section_content_title_mgB) ? $settings->section_content_title_mgB : 8;
        $section_content_title_mgB_sm = (isset($settings->section_content_title_mgB_sm) && $settings->section_content_title_mgB_sm) ? $settings->section_content_title_mgB_sm : 8;
        $section_content_title_mgB_xs = (isset($settings->section_content_title_mgB_xs) && $settings->section_content_title_mgB_xs) ? $settings->section_content_title_mgB_xs : 6;
        //右侧列表地址文字大小
        $section_content_sTitle_fontsize = (isset($settings->section_content_sTitle_fontsize) && $settings->section_content_sTitle_fontsize) ? $settings->section_content_sTitle_fontsize : 14;
        $section_content_sTitle_fontsize_sm = (isset($settings->section_content_sTitle_fontsize_sm) && $settings->section_content_sTitle_fontsize_sm) ? $settings->section_content_sTitle_fontsize_sm : 14;
        $section_content_sTitle_fontsize_xs = (isset($settings->section_content_sTitle_fontsize_xs) && $settings->section_content_sTitle_fontsize_xs) ? $settings->section_content_sTitle_fontsize_xs : 12;
        //右侧列表地址文字颜色
        $section_content_sTitle_color = (isset($settings->section_content_sTitle_color) && $settings->section_content_sTitle_color) ? $settings->section_content_sTitle_color : "";

        $css =
            $addonId . ' * {
				margin: 0;
				box-sizing: content-box;
			}

			' . $addonId . ' a {
				color: #333333;
				text-decoration: none;
			}

			[v-cloak] {
				display: none;
			}

			' . $addonId . ' .flex {
				display: flex;
				align-items: center;
			}

			' . $addonId . ' .flex-between {
				justify-content: space-between;
			}
			
			' . $addonId . ' .flex-around {
				justify-content: space-around;
			}
			' . $addonId . ' .oneline {
			    overflow:hidden;
				white-space:nowrap;
				text-overflow: ellipsis;
			}
			
			' . $addonId . ' #calendar-app {
				width: 100%;
				position: relative;
			}
			' . $addonId . ' .c-top {
				border-bottom: #D8D8D8 solid 1px;
			}
			' . $addonId . ' .c-top .left-info .title {
			    width: 110px;
				font-size: 20px;
				line-height: 58px;
				font-weight: bold;
				color: #0E893B;
				border-bottom: #0E893B solid 2px;
				margin-right: 20px;
			}
			' . $addonId . ' .c-top .left-info .tags .item_ {
				margin-right: 20px;
				font-size: 14px;
				color: #666;
			}
			' . $addonId . ' .c-top .left-info .tags .item_ img {
				display: inline-block;
			}
			' . $addonId . ' .c-top .right-more a {
				font-size: 16px;
				color: #999;
			}
			' . $addonId . ' .calendar-box {
				background-color: #f7f7f7;
			}
			' . $addonId . ' .action-box {
				padding: 24px;
			}
			' . $addonId . ' .action-box .item {
				border: #d8d8d8 solid 1px;
				width: 120px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				font-size: 14px;
				cursor: pointer;
			}
			' . $addonId . ' .action-box .item:nth-child(2) {
				justify-content: space-between;
			}
			' . $addonId . ' .action-box .item select {
				border: none;
				background: none;
				font-size: 14px;
                width: max-content;
                padding: 0;
			}
			' . $addonId . ' .action-box .item .prev, .action-box .item .next {
				display: inline-block;
				line-height: 28px;
				width: 28px;
				text-align: center;
			}
			' . $addonId . ' .action-box .item .prev {
				border-right: #D8D8D8 solid 1px;
			}
			' . $addonId . ' .action-box .item .next {
				border-left: #D8D8D8 solid 1px;
			}
			' . $addonId . ' .calendar .week {
				border-bottom: #d8d8d8 dashed 2px;
				font-size: 16px;
			}
			' . $addonId . ' .calendar .week .item {
				height: 60px;
				line-height: 60px;
			}
			' . $addonId . ' .calendar .days {
				flex-wrap: wrap;
			}
			' . $addonId . ' .calendar .days .item {
				width: calc(100% / 7 - 1px);
				padding: 5% 0;
				text-align: center;
				border-right: #d8d8d8 dashed 1px;
				border-bottom: #d8d8d8 dashed 1px;
				font-size: 20px;
				height: 20px;
				line-height: 20px;
			}
			' . $addonId . ' .calendar .days .item.active {
				color: #fff;
                background:#eb8390;
			}
			' . $addonId . ' .calendar .days .item.color1 {
				border-bottom: #56D6A2 solid 2px;
				cursor: pointer;
			}
			' . $addonId . ' .calendar .days .item.color2 {
				border-bottom: #4C96EB solid 2px;
				cursor: pointer;
			}
			' . $addonId . ' #calendar-app .c_alert {
				width: 280px;
				height: 80%;
				position: absolute;
				z-index: 9999;
				right: -280px;
				top: 58px;
				color: #fff;
				text-align: center;
				background-image: linear-gradient(to bottom right, #228EFA, #0E893E);
			}
			' . $addonId . ' #calendar-app .c_alert .date-box {
				padding: 24px 0;
				padding-top: 40px;
				margin: 0 14px;
				border-bottom: #fff solid 2px;
				font-size: 16px;
			}
			' . $addonId . ' #calendar-app .c_alert .close_ {
				position: absolute;
				right: 12px;
				top: 12px;
				cursor: pointer;
			}
			' . $addonId . ' #calendar-app .c_alert .date-box .week {
				margin: 10px 0;
				font-size: 14px;
			}
			' . $addonId . ' #calendar-app .c_alert .date-box .number {
				font-size: 50px;
			}
			' . $addonId . ' #calendar-app .c_alert .list-box {
				padding: 0 14px;
				height: calc(100% - 230px);
				overflow-y: auto;
				margin: 14px 0;
			}
			' . $addonId . ' #calendar-app .c_alert .list-box .list .item {
				margin-bottom: 16px;
				font-size: 14px;
			}
			' . $addonId . ' #calendar-app .c_alert .list-box .list .item .title {
				margin-bottom: 5px;
			}
			@media (max-width: 768px){
				' . $addonId . ' #calendar-app .c_alert {
					width: 280px;
					height: 80%;
					z-index: 9999;
					position: unset;
					right: -280px;
					top: 58px;
					color: #fff;
					text-align: center;
					background-image: linear-gradient(to bottom right, #228EFA, #0E893E);
				}
			}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		#>
        <style type="text/css">
			{{ addonId }} * {
				margin: 0;
				box-sizing: content-box;
			}

			{{ addonId }} a {
				color: #333333;
				text-decoration: none;
			}

			[v-cloak] {
				display: none;
			}

			{{ addonId }} .flex {
				display: flex;
				align-items: center;
			}

			{{ addonId }} .flex-between {
				justify-content: space-between;
			}
			
			{{ addonId }} .flex-around {
				justify-content: space-around;
			}
			{{ addonId }} .oneline {
			  overflow:hidden;
				white-space:nowrap;
				text-overflow: ellipsis;
			}
			
			{{ addonId }} #calendar-app {
				width: 100%;
				position: relative;
			}
			{{ addonId }} .c-top {
				border-bottom: #D8D8D8 solid 1px;
			}
			{{ addonId }} .c-top .left-info {
			    width: calc(100% - 60px);
			}
			{{ addonId }} .c-top .left-info .title {
				width: 110px;
				font-size: 20px;
				line-height: 58px;
				font-weight: bold;
				color: #0E893B;
				border-bottom: #0E893B solid 2px;
				margin-right: 20px;
			}
			{{ addonId }} .c-top .left-info .tags {
			    flex-wrap: wrap;
			    width: calc(100% - 110px);
			}
			{{ addonId }} .c-top .left-info .tags .item_ {
				margin-right: 20px;
				font-size: 14px;
				color: #666;
			}
			{{ addonId }} .c-top .left-info .tags .item_ img {
				display: inline-block;
			}
			{{ addonId }} .c-top .right-more {
			    width: 60px;
			    text-align: right;
			}
			{{ addonId }} .c-top .right-more a {
				font-size: 14px;
				color: #999;
			}
			{{ addonId }} .calendar-box {
				background-color: #f7f7f7;
			}
			{{ addonId }} .action-box {
				padding: 24px;
			}
			{{ addonId }} .action-box .item {
				border: #d8d8d8 solid 1px;
				width: 120px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				font-size: 14px;
				cursor: pointer;
			}
			{{ addonId }} .action-box .item:nth-child(2) {
				justify-content: space-between;
			}
			{{ addonId }} .action-box .item select {
				border: none;
				background: none;
				font-size: 14px;
                width: max-content;
                padding: 0;
			}
			
			{{ addonId }} .action-box .item .prev, .action-box .item .next {
				display: inline-block;
				line-height: 28px;
				width: 28px;
				text-align: center;
			}
			{{ addonId }} .action-box .item .prev {
				border-right: #D8D8D8 solid 1px;
			}
			{{ addonId }} .action-box .item .next {
				border-left: #D8D8D8 solid 1px;
			}
			{{ addonId }} .calendar .week {
				border-bottom: #d8d8d8 dashed 2px;
				font-size: 16px;
			}
			{{ addonId }} .calendar .week .item {
				height: 60px;
				line-height: 60px;
			}
			{{ addonId }} .calendar .days {
				flex-wrap: wrap;
			}
			{{ addonId }} .calendar .days .item {
				width: calc(100% / 7 - 1px);
				padding: 5% 0;
				text-align: center;
				border-right: #d8d8d8 dashed 1px;
				border-bottom: #d8d8d8 dashed 1px;
				font-size: 20px;
				height: 20px;
				line-height: 20px;
			}
			{{ addonId }} .calendar .days .item.active {
				color: #0E893B;
			}
			{{ addonId }} .calendar .days .item.color1 {
				border-bottom: #56D6A2 solid 2px;
				cursor: pointer;
			}
			{{ addonId }} .calendar .days .item.color2 {
				border-bottom: #4C96EB solid 2px;
				cursor: pointer;
			}
			{{ addonId }} #calendar-app .c_alert {
				width: 280px;
				height: 80%;
				position: absolute;
				z-index: 9999;
				right: -280px;
				top: 58px;
				color: #fff;
				text-align: center;
				background-image: linear-gradient(to bottom right, #228EFA, #0E893E);
			}
			{{ addonId }} #calendar-app .c_alert .date-box {
				padding: 24px 0;
				padding-top: 40px;
				margin: 0 14px;
				border-bottom: #fff solid 2px;
				font-size: 16px;
			}
			{{ addonId }} #calendar-app .c_alert .close_ {
				position: absolute;
				right: 12px;
				top: 12px;
				cursor: pointer;
			}
			{{ addonId }} #calendar-app .c_alert .date-box .week {
				margin: 10px 0;
				font-size: 14px;
			}
			{{ addonId }} #calendar-app .c_alert .date-box .number {
				font-size: 50px;
			}
			{{ addonId }} #calendar-app .c_alert .list-box {
				padding: 0 14px;
				height: calc(100% - 230px);
				overflow-y: auto;
				margin: 14px 0;
			}
			{{ addonId }} #calendar-app .c_alert .list-box .list .item {
				margin-bottom: 16px;
				font-size: 14px;
			}
			{{ addonId }} #calendar-app .c_alert .list-box .list .item .title {
				margin-bottom: 5px;
			}
		</style>
        <div id="calendar-app">
			<div class="c-top flex flex-between">
				<div class="left-info flex">
					<div class="title">赛事活动</div>
					<div class="flex tags">
						<div class="item_">
							<img src="https://oss.lcweb01.cn/joomla/20210629/ce954e3f70b817bfc40cc94d175bf1f3.png" alt="" />
							<span>公开赛</span>
						</div>
						<div class="item_">
							<img src="https://oss.lcweb01.cn/joomla/20210629/64c70fe8d07f1901c8513c812afb0499.png" alt="" />
							<span>俱乐部联赛</span>
						</div>
					</div>
				</div>
				<div class="right-more">
					<a href="">更多>></a>
				</div>
			</div>
			<div class="calendar-box">
				<div class="action-box flex flex-between">
					<div class="item">
						<select>
							<option value="2018">2018</option>
							<option value="2019">2019</option>
							<option value="2020">2020</option>
							<option value="2021" selected>2021</option>
							<option value="2022">2022</option>
							<option value="2023">2023</option>
						</select>
					</div>
					<div class="item">
						<span class="prev">&lt;</span>
						<select>
							<option value="1">1月</option>
							<option value="2">2月</option>
							<option value="3">3月</option>
							<option value="4">4月</option>
							<option value="5">5月</option>
							<option value="6" selected>6月</option>
						</select>
						<span class="next">&gt;</span>
					</div>
					<div class="item">
						<p class="to-today">返回今天</p>
					</div>
				</div>
				<!-- 日历 -->
				<div class="calendar">
				    <div class="week flex flex-around">
				        <div class="item">周日</div>
				        <div class="item">周一</div>
				        <div class="item">周二</div>
				        <div class="item">周三</div>
				        <div class="item">周四</div>
				        <div class="item">周五</div>
				        <div class="item">周六</div>
				    </div> 
				    <div class="days flex">
				        <div class="item" style="color: rgb(153, 153, 153);"></div>
				        <div class="item" style="color: rgb(153, 153, 153);"></div>
				        <div class="item">1</div>
				        <div class="item">2</div>
				        <div class="item">3</div>
				        <div class="item">4</div>
				        <div class="item">5</div>
				        <div class="item">6</div>
				        <div class="item">7</div>
				        <div class="item">8</div>
				        <div class="item">9</div>
				        <div class="item">10</div>
				        <div class="item">11</div>
				        <div class="item">12</div>
				        <div class="item color2">13</div>
				        <div class="item">14</div>
				        <div class="item">15</div>
				        <div class="item">16</div>
				        <div class="item">17</div>
				        <div class="item">18</div>
				        <div class="item">19</div>
				        <div class="item">20</div>
				        <div class="item color1">21</div>
				        <div class="item">22</div>
				        <div class="item">23</div>
				        <div class="item">24</div>
				        <div class="item">25</div>
				        <div class="item">26</div>
				        <div class="item">27</div>
				        <div class="item active">28</div>
				        <div class="item">29</div>
				        <div class="item">30</div>
                    </div>
                </div>
			</div>
			<!-- 日历弹框 -->
			<div class="c_alert">
				<div class="close_">X</div>
				<div class="date-box">
					<div class="time">2021-05-28</div>
					<div class="week">星期五</div>
					<div class="number">28</div>
				</div>
				<div class="list-box">
					<div class="list">
						<div class="item">
							<p class="title oneline">2021年全国健身瑜伽俱乐部联赛</p>
							<p class="address">江苏 南京</p>
						</div>
					</div>
				</div>
			</div>
		</div>
		';

        return $output;
    }
}
