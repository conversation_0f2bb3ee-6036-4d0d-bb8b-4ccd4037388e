<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonLc_footer extends JwpagefactoryAddons
{

    public function render()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        
        // 2023.04.06
        $bgColor = isset($settings->bgColor) ? $settings->bgColor : '#1e1e1e';
        $bot_borderColor = isset($settings->bot_borderColor) ? $settings->bot_borderColor : '#393939';
        $bot_l_color = isset($settings->bot_l_color) ? $settings->bot_l_color : '#777777';
        $bot_l_color_h = isset($settings->bot_l_color_h) ? $settings->bot_l_color_h : '#ffffff';
        $bot_s_color = isset($settings->bot_s_color) ? $settings->bot_s_color : '#7a7a7a';
        $bot_s_color_h = isset($settings->bot_s_color_h) ? $settings->bot_s_color_h : '#ffffff';
        $bot_left_color = isset($settings->bot_left_color) ? $settings->bot_left_color : '#656565';
        $bot_right_color = isset($settings->bot_right_color) ? $settings->bot_right_color : '#777777';


        $output = ' <style>';
        $output .= $addonId . ' a{color:inherit;text-decoration:none}';
        $output .= $addonId . ' .i200{overflow:hidden}';
        $output .= $addonId . ' .cl{clear:both;}';
        $output .= $addonId . ' .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}';
        $output .= $addonId . ' .animated{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}';
        $output .= ' @media only screen and (min-width:1600px){';
        //        $output .= ' @media only screen and (max-width:1920px){';
        $output .= $addonId . ' .p-bot{display:none}    ';
        $output .= $addonId . ' .ft1-a1{animation:fadenum 2s}';
        $output .= $addonId . ' .ft1-b1 .ft1-b2{animation:fadenum 3s}';
        $output .= ' @keyframes fadenum{0%{transform:translateY(100px)}}';
        $output .= $addonId . ' .ftl-home{ margin-top: 10rem;}';
        $output .= $addonId . ' .ft1-a1{width:100%;height:100px}';
        $output .= $addonId . ' .ft1-a2{width:1560px;height:100%;position:relative;margin:0 auto}';
        $output .= $addonId . ' .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(1){font-size:18px;line-height:100px;color:#454545;font-weight:bold;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(2){width:1px;height:18px;background:#b2b2b2;position:relative;top:calc(50% - 18px/2);float:right}';
        $output .= $addonId . ' .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-a5{height:100px;position:relative;cursor:pointer}';
        $output .= $addonId . ' .ft1-a6{width:280px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 280px/2);transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a6{height:146px;transition:0.5s}';
        $output .= $addonId . ' .ft1-a7{width:100%;height:146px;position:relative;top:1px}';
        $output .= $addonId . ' .ft1-a8{width:100%;height:136px;background:#d9012a;padding:14px}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(1){height:108px;float:left}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2){width:120px;height:108px;float:left;display:table;margin-left:20px}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:15px;line-height:24px;color:#fff;font-weight:bold}';
        $output .= $addonId . ' .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:4.2rem;}';
        $output .= $addonId . ' .ft1-a10{height:100px;position:relative}';
        $output .= $addonId . ' .ft1-a10 .ft1-a11 img{width:26px;height:20px}';
        $output .= $addonId . ' .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}';
        $output .= $addonId . ' .ft1-b1{width:100%;position:relative;background:' . $bgColor . ';padding-top:80px}';
        $output .= $addonId . ' .ft1-b2{width:1560px;margin:0 auto;padding-bottom:64px;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-b3{position:relative}';
        $output .= $addonId . ' .ft1-b4{font-size:18px;line-height:18px;color:' . $bot_l_color . ';font-weight:bold;margin-bottom:36px;transition:0.5s}';
        $output .= $addonId . ' .ft1-b4:hover{color:' . $bot_l_color_h . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5{position:relative}';
        $output .= $addonId . ' .ft1-b5>div{font-size:14px;line-height:30px;color:' . $bot_s_color . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5>div:hover{color:' . $bot_s_color_h . ';text-decoration:underline;transition:0.5s}';
        $output .= $addonId . ' .ft1-b6{width:1560px;padding:36px 0;border-top:1px solid ' . $bot_borderColor . ';margin:0 auto}';
        $output .= $addonId . ' .ft1-b7{font-size:12px;line-height:1.2rem;color:' . $bot_left_color . ';vertical-align:middle;float:left}';
        $output .= $addonId . ' .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}';
        $output .= $addonId . ' .ft1-b8{float:right}';
        $output .= $addonId . ' .ft1-b8>div{font-size:12px;line-height:20px;color:' . $bot_right_color . ';float:left;margin-left:20px}';
        $output .= $addonId . ' .ft1-b8>div:first-child{margin-left:0}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (max-width:1599px) and (min-width:1400px){';
        $output .= $addonId . ' .p-bot{display:none}    ';
        $output .= $addonId . ' .ft1-a1{width:100%;height:100px;margin-top:65px}';
        $output .= $addonId . ' .ft1-a2{width:1360px;height:100%;position:relative;margin:0 auto}';
        $output .= $addonId . ' .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(1){font-size:18px;line-height:100px;color:#454545;font-weight:bold;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(2){width:1px;height:18px;background:#b2b2b2;position:relative;top:calc(50% - 18px/2);float:right}';
        $output .= $addonId . ' .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-a5{height:100px;position:relative;cursor:pointer}';
        $output .= $addonId . ' .ft1-a6{width:280px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 280px/2);transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:last-child .ft1-a6{left:calc(50% - 280px/2 - 50px)}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a6{height:146px;transition:0.5s}';
        $output .= $addonId . ' .ft1-a7{width:100%;height:146px;position:relative;top:1px}';
        $output .= $addonId . ' .ft1-a8{width:100%;height:136px;background:#d9012a;padding:14px}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(1){height:108px;float:left}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2){width:120px;height:108px;float:right;display:table}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:15px;line-height:24px;color:#fff;font-weight:bold}';
        $output .= $addonId . ' .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}';
        $output .= $addonId . ' .ft1-a10{height:100px;position:relative}';
        $output .= $addonId . ' .ft1-a10 .ft1-a11 img{width:26px;height:20px}';
        $output .= $addonId . ' .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}';
        $output .= $addonId . ' .ft1-b1{width:100%;position:relative;background:' . $bgColor . ';padding-top:80px}';
        $output .= $addonId . ' .ft1-b2{width:1360px;margin:0 auto;padding-bottom:64px;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-b3{position:relative}';
        $output .= $addonId . ' .ft1-b4{font-size:18px;line-height:18px;color:' . $bot_l_color . ';font-weight:bold;margin-bottom:36px;transition:0.5s}';
        $output .= $addonId . ' .ft1-b4:hover{color:' . $bot_l_color_h . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5{position:relative}';
        $output .= $addonId . ' .ft1-b5>div{font-size:14px;line-height:30px;color:' . $bot_s_color . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5>div:hover{color:' . $bot_s_color_h . ';text-decoration:underline;transition:0.5s}';
        $output .= $addonId . ' .ft1-b6{width:1360px;padding:26px 0;border-top:1px solid ' . $bot_borderColor . ';margin:0 auto}';
        $output .= $addonId . ' .ft1-b7{font-size:12px;line-height:20px;color:' . $bot_left_color . ';vertical-align:middle;float:left;width:auto;}';
        $output .= $addonId . ' .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px}';
        $output .= $addonId . ' .ft1-b8{float:right}';
        $output .= $addonId . ' .ft1-b8>div{font-size:12px;line-height:20px;color:' . $bot_right_color . ';float:left;margin-left:20px}';
        $output .= $addonId . ' .ft1-b8>div:first-child{margin-left:0}';
        $output .= ' }';
        $output .= ' @media only screen and (max-width:1399px) and (min-width:1200px){';
        $output .= $addonId . ' .p-bot{display:none}    ';
        $output .= $addonId . ' .ft1-a1{width:100%;height:100px;margin-top:65px}';
        $output .= $addonId . ' .ft1-a2{width:1160px;height:100%;position:relative;margin:0 auto}';
        $output .= $addonId . ' .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(1){font-size:16px;line-height:100px;color:#454545;font-weight:bold;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(2){width:1px;height:16px;background:#b2b2b2;position:relative;top:calc(50% - 16px/2);float:right}';
        $output .= $addonId . ' .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-a5{height:100px;position:relative;cursor:pointer}';
        $output .= $addonId . ' .ft1-a6{width:240px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 240px/2);transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:last-child .ft1-a6{left:calc(50% - 240px/2 - 40px)}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a6{height:126px;transition:0.5s}';
        $output .= $addonId . ' .ft1-a7{width:100%;height:126px;position:relative;top:1px}';
        $output .= $addonId . ' .ft1-a8{width:100%;height:116px;background:#d9012a;padding:10px}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(1){height:96px;float:left}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2){width:104px;height:96px;float:right;display:table}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:12px;line-height:20px;color:#fff;font-weight:bold}';
        $output .= $addonId . ' .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}';
        $output .= $addonId . ' .ft1-a10{height:100px;position:relative}';
        $output .= $addonId . ' .ft1-a10 .ft1-a11 img{width:26px;height:20px}';
        $output .= $addonId . ' .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}';
        $output .= $addonId . ' .ft1-b1{width:100%;position:relative;background:' . $bgColor . ';padding-top:60px}';
        $output .= $addonId . ' .ft1-b2{width:1160px;margin:0 auto;padding-bottom:44px;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-b3{position:relative}';
        $output .= $addonId . ' .ft1-b4{font-size:16px;line-height:16px;color:' . $bot_l_color . ';font-weight:bold;margin-bottom:28px;transition:0.5s}';
        $output .= $addonId . ' .ft1-b4:hover{color:' . $bot_l_color_h . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5{position:relative}';
        $output .= $addonId . ' .ft1-b5>div{font-size:12px;line-height:26px;color:' . $bot_s_color . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5>div:hover{color:' . $bot_s_color_h . ';text-decoration:underline;transition:0.5s}';
        $output .= $addonId . ' .ft1-b6{width:1160px;padding:20px 0;border-top:1px solid ' . $bot_borderColor . ';margin:0 auto}';
        $output .= $addonId . ' .ft1-b7{font-size:12px;line-height:20px;color:' . $bot_left_color . ';vertical-align:middle;float:left}';
        $output .= $addonId . ' .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}';
        $output .= $addonId . ' .ft1-b8{float:right}';
        $output .= $addonId . ' .ft1-b8>div{font-size:12px;line-height:20px;color:' . $bot_right_color . ';float:left;margin-left:20px}';
        $output .= $addonId . ' .ft1-b8>div:first-child{margin-left:0}';
        $output .= ' }';
        $output .= ' @media only screen and (max-width:1199px) and (min-width:1024px){';
        $output .= $addonId . ' .p-bot{display:none}    ';
        $output .= $addonId . ' .ft1-a1{width:100%;height:100px;margin-top:65px}';
        $output .= $addonId . ' .ft1-a2{width:960px;height:100%;position:relative;margin:0 auto}';
        $output .= $addonId . ' .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(1){font-size:16px;line-height:100px;color:#454545;font-weight:bold;float:left}';
        $output .= $addonId . ' .ft1-a3>div:nth-child(2){width:1px;height:16px;background:#b2b2b2;position:relative;top:calc(50% - 16px/2);float:right}';
        $output .= $addonId . ' .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-a5{height:100px;position:relative;cursor:pointer}';
        $output .= $addonId . ' .ft1-a6{width:240px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 240px/2);transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:last-child .ft1-a6{left:calc(50% - 240px/2 - 40px)}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a6{height:126px;transition:0.5s}';
        $output .= $addonId . ' .ft1-a7{width:100%;height:126px;position:relative;top:1px}';
        $output .= $addonId . ' .ft1-a8{width:100%;height:116px;background:#d9012a;padding:10px}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(1){height:96px;float:left}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2){width:104px;height:96px;float:right;display:table}';
        $output .= $addonId . ' .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:12px;line-height:20px;color:#fff;font-weight:bold}';
        $output .= $addonId . ' .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}';
        $output .= $addonId . ' .ft1-a10{height:100px;position:relative}';
        $output .= $addonId . ' .ft1-a10 .ft1-a11 img{width:26px;height:20px}';
        $output .= $addonId . ' .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}';
        $output .= $addonId . ' .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}';
        $output .= $addonId . ' .ft1-b1{width:100%;position:relative;background:' . $bgColor . ';padding-top:60px}';
        $output .= $addonId . ' .ft1-b2{width:960px;margin:0 auto;padding-bottom:44px;display:flex;justify-content:space-between}';
        $output .= $addonId . ' .ft1-b3{position:relative}';
        $output .= $addonId . ' .ft1-b4{font-size:16px;line-height:16px;color:' . $bot_l_color . ';font-weight:bold;margin-bottom:28px;transition:0.5s}';
        $output .= $addonId . ' .ft1-b4:hover{color:' . $bot_l_color_h . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5{position:relative}';
        $output .= $addonId . ' .ft1-b5>div{font-size:12px;line-height:26px;color:' . $bot_s_color . ';transition:0.5s}';
        $output .= $addonId . ' .ft1-b5>div:hover{color:' . $bot_s_color_h . ';text-decoration:underline;transition:0.5s}';
        $output .= $addonId . ' .ft1-b6{width:960px;padding:20px 0;border-top:1px solid ' . $bot_borderColor . ';margin:0 auto}';
        $output .= $addonId . ' .ft1-b7{font-size:12px;line-height:20px;color:' . $bot_left_color . ';vertical-align:middle;float:left}';
        $output .= $addonId . ' .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}';
        $output .= $addonId . ' .ft1-b8{float:right}';
        $output .= $addonId . ' .ft1-b8>div{font-size:12px;line-height:20px;color:' . $bot_right_color . ';float:left;margin-left:20px}';
        $output .= $addonId . ' .ft1-b8>div:first-child{margin-left:0}';
        $output .= ' }';
        $output .= ' @media only screen and (max-width:1023px){';
        $output .= $addonId . ' .p-bot{display:none}    ';
        $output .= $addonId . ' .ftl-home{width:100%;height:20rem;background:#1e1e1e;padding-top:.98rem;padding-bottom:.68rem;position:relative;z-index:5}';
        $output .= $addonId . ' .ft1-a1{display:none}';
        $output .= $addonId . ' .ft1-b1{display:none}';
        $output .= $addonId . ' .p-bot .p-bot-a1{animation:fadenum 2s}';
        $output .= ' @keyframes fadenum{';
        $output .= $addonId . ' 0%{opacity:0}';
        $output .= $addonId . ' 20%{opacity:0.1}';
        $output .= $addonId . ' 40%{opacity:0.2}';
        $output .= $addonId . ' 60%{opacity:0.3}';
        $output .= $addonId . ' 100%{transform:translateY(20px);opacity:1}';
        $output .= ' }';
        $output .= $addonId . ' .p-bot{background:#1e1e1e;padding-top:.98rem;padding-bottom:.68rem;position:relative;z-index:5}';
        $output .= $addonId . ' .p-bot-a1{display:flex;justify-content:center;padding-bottom:2rem}';
        $output .= $addonId . ' .p-bot-a1 .p-bot-a1-list img:first-child{width:1.5rem}';
        $output .= $addonId . ' .p-bot-a1 .p-bot-a1-list:nth-child(3) img:first-child{width:1.3rem}';
        $output .= $addonId . ' .p-bot-a1-list{margin-right:.7rem;position:relative;z-index:5;padding:1rem}';
        $output .= $addonId . ' .p-bot-a1-list:last-child{margin-right:0}';
        $output .= $addonId . ' .p-bot-a1-list img:last-child{width:5rem;height:5rem;position:absolute;bottom:110%;left:calc(50% - 5rem/2);transform:scale(0);opacity:0;transition:0.5s}';
        $output .= $addonId . ' .p-bot-a1-list:hover img:last-child{transform:scale(1);opacity:1;transition:0.5s}';
        $output .= $addonId . ' .p-bot-a2{color:#777777;font-size:1rem;text-align:center;border-bottom:.1rem solid rgba(255,255,255,.1);padding-bottom:1.5rem;margin-bottom:1.3rem}';
        $output .= $addonId . ' .p-bot-a2 a:first-child{margin-right:2rem}';
        $output .= $addonId . ' .p-bot-a3{font-size:.22rem;color:#656565;text-align:center;margin-bottom:1.25rem}';
        $output .= $addonId . ' .p-bot-a4{font-size:.22rem;color:#656565;text-align:center}';
        $output .= $addonId . ' .p-bot-a4 img{display:inline-block;margin-right:.06rem}';
        $output .= ' }';
        $output .= ' </style>';
        $output .= '<div class="ftl-home">';
        $output .= '  <div class="ft1-a1 wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">';
            $output .= '    <div class="ft1-a2 clear">';
                $output .= '      <div class="ft1-a3 clear">';
                    $output .= '        <div>' . $settings->media_title . '</div>';
                    $output .= '        <div></div>';
                $output .= '      </div>';
                $output .= '      <div class="ft1-a4">';

                    $beianhao_url = isset($settings->beianhao_url) ? $settings->beianhao_url : 'https://beian.miit.gov.cn/#/Integrated/index';
                    $wanganbeian_url = isset($settings->wanganbeian_url) ? $settings->wanganbeian_url : 'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=%2023010202010007';

                    foreach ($settings->jw_media_item as $key => $lcfooter) {
                        
                        $on_link = (isset($lcfooter->on_link)) ? $lcfooter->on_link : 0; 
                        $detail_page_ida = (isset($lcfooter->detail_page_ida)) ? $lcfooter->detail_page_ida : 0;
                        $a_url = $lcfooter->a_url ? $lcfooter->a_url : 'javascript:;'; 

                        if($on_link==1){
                            if($detail_page_ida){
                                $id=base64_encode($detail_page_ida);
                                $linka = 'component/jwpagefactory/?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                            }
                        }else{
                            $linka =$a_url;
                        }

                        $output .= '        <div class="ft1-a5">';
                            $output .= '          <div class="ft1-a6">';
                            $output .= '            <div class="ft1-a7">';
                            $output .= '              <div class="ft1-a8 clear">';
                            $output .= '                <div class="i200"> <img src="' . $lcfooter->qr_code . '"  width="107"> </div>';
                            $output .= '                <div>';
                            $output .= '                  <div>' . $lcfooter->content . '</div>';
                            $output .= '                </div>';
                            $output .= '              </div>';
                            $output .= '              <div class="ft1-a9"></div>';
                            $output .= '            </div>';
                            $output .= '          </div>';
                            $output .= '          <div class="ft1-a10"> <a href="' . $linka . '" >';
                            $output .= '            <div class="ft1-a11 i200"> <img src="' . $lcfooter->icon_url . '" > <img src="' . $lcfooter->icon_url2 . '" > </div>';
                            $output .= '            ';
                            $output .= '            <div class="ft1-a12">' . $lcfooter->title . '</div>';
                            $output .= '          </a></div>';
                        $output .= '        </div>';
                    }
                $output .= '      </div>';
            $output .= '    </div>';
        $output .= '  </div>';

        $output .= '  <div class="ft1-b1">';
        $output .= '    <div class="ft1-b2">';
        foreach ($settings->jw_link_item as $likey => $jwlinkitem) {
            $link_selet = (isset($jwlinkitem->link_selet)) ? $jwlinkitem->link_selet : "ww"; 
            $wdetail_page_id = (isset($jwlinkitem->wdetail_page_id)) ? $jwlinkitem->wdetail_page_id : 0;
            $title_url = $jwlinkitem->title_url ? $jwlinkitem->title_url : "javascript:;"; 

            if($link_selet=="nn"){
                if($wdetail_page_id){
                    $ids=base64_encode($wdetail_page_id);
                    $link_us = 'component/jwpagefactory/?view=page&id=' . $ids . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                }
            }else{
                $link_us =$title_url;
            }

            $output .= '      <div class="ft1-b3 wow fadeIn animated animated" data-wow-duration="2s" data-wow-delay="0.30000000000000004s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">';
                $output .= '        <div class="ft1-b4"><a href="'.$link_us.'" target="_blank">'.$jwlinkitem->title.'</a></div>';
                $output .= '        <div class="ft1-b5">';
                foreach ($jwlinkitem->jw_link_data as $jwlikey1 => $jwlinkdata) {

                    $link_nw = (isset($jwlinkdata->link_nw)) ? $jwlinkdata->link_nw : 0; 
                    $detail_page_id = (isset($jwlinkdata->detail_page_id)) ? $jwlinkdata->detail_page_id : 0;
                    $a_url = (isset($jwlinkdata->a_url)) ? $jwlinkdata->a_url : ''; 

                    if($link_nw==1){
                        if($detail_page_id){
                            $id=base64_encode($detail_page_id);
                            $link = 'component/jwpagefactory/?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        }
                    }else{
                        $link =$a_url;
                    }

                    $output .= '          <div><a href="'.$link.'" target="_blank">'.$jwlinkdata->title.'</a></div>';
                }
                $output .= '        </div>';
            $output .= '      </div>';
        }

        $youbian_img = $settings->youbian_img ? $settings->youbian_img : "https://oss.lcweb01.cn/joomla/20220812/f9fd0eb548f5aee1a910dd0d7df0190a.png"; 
        $zhuceren = $settings->zhuceren ? $settings->zhuceren : "注册人权利与责任"; 
        $ybh_img = $settings->ybh_img ? $settings->ybh_img : "0"; 
        $imga='';
        if($ybh_img==1){
        }else{
            $imga.='<img src="'.$youbian_img.'">';
        }
        
        $output .= '  </div>';

        $output .= '    <div class="ft1-b6 clear " >';
            $output .= '      <div class="ft1-b7"><a href="'.$beianhao_url.'" target="_blank">备案号：' . $settings->beianhao . '</a> 邮编：' . $settings->youbian . ' '.$imga.'<a href="'.$wanganbeian_url.'" target="_blank">' . $settings->wanganbeian . '  </a>'.$zhuceren.'</div>';
            $output .= '      <div class="ft1-b8 ">';
            $output .= '        <div><a href="' . $settings->shouqian_url . '" target="_blank">售前客服</a></div>';
            $output .= '        <div><a href="' . $settings->shouhou_url . '" target="_blank">售后客服</a></div>';
            $output .= '      </div>';
            $output .= '    <div class="cl"></div>';
        $output .= '    </div>';
        
        $output .= '  </div>';

        $output .= '  <div class="p-bot zmr-phone">';

            $output .= '    <div class="p-bot-a1">';
            foreach ($settings->jw_media_item as $key => $lcfooter2) {
                $output .= '      <div class="p-bot-a1-list wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;"> <img src="' . $lcfooter2->icon_url3 . '" alt=""> <a href="' . $lcfooter2->a_url . '" target="_blank"></a> <img src="' . $lcfooter2->qr_code . '" alt=""> </div>';
            }
            $output .= '    </div>';

            $output .= '    <div class="p-bot-a2"> <a href="' . $settings->shouqian_url . '" target="_blank">售前客服</a> <a href="' . $settings->shouhou_url . '" target="_blank">售后客服</a> </div>';
            $output .= '    <div class="p-bot-a3">备案号：' . $settings->beianhao . '邮编：' . $settings->youbian . '</div>';
            $output .= '    <div class="p-bot-a4"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/z28.png" alt="">' . $settings->wanganbeian . ' '.$zhuceren.'</div>';
        $output .= '  </div>';

        $output .= '</div>';

        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function css()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        // 上翻页按钮
        $swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
        $swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;

        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';

        $css = '
            /* 组件盒子 */
            ' . $addonId . ' .swiper-box-main {
                position: relative;
            }
            /* 切换 配置样式 */
            ' . $addonId . ' .swiper-button {
                width: auto;
                height: auto;
                top: ' . $swiper_button_top_md . '%;
            }
            ' . $addonId . ' .swiper-button:after {
                content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
            }
            ' . $addonId . ' .swiper-button-prev {
                left: ' . $swiper_button_left_md . 'px;
            }
            ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }
            ' . $addonId . ' .swiper-button-next {
                right: ' . $swiper_button_left_md . 'px;
            }
            ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
                background-image: url(' . $swiper_button_next . ');
            }
            /*轮播点*/
            ' . $addonId . ' .swiper-pagination {
                width: 100%;
            }
            ' . $addonId . ' .swiper-pagination-bullet {
                margin-right: ' . $swiper_p_margin_md . 'px;
                width: ' . $swiper_p_width_md . 'px;
                height: ' . $swiper_p_height_md . 'px;
                background: ' . $swiper_p_color . ';
                opacity: 1;
            }
            ' . $addonId . ' .swiper-pagination-bullet-active {
                width: ' . $swiper_p_width_a_md . 'px;
                height: ' . $swiper_p_height_a_md . 'px;
                background: ' . $swiper_p_color_a . ';
            }
            ' . $addonId . ' .swiper-pagination-bullet:last-child {
                margin-right: 0px;
            }
            @media (min-width: 768px) and (max-width: 991px) {
                /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' . $swiper_p_margin_sm . 'px;
                    width: ' . $swiper_p_width_sm . 'px;
                    height: ' . $swiper_p_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_sm . 'px;
                    height: ' . $swiper_p_height_a_sm . 'px;
                }
            }
            @media (max-width: 767px) {
                /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' . $swiper_p_margin_xs . 'px;
                    width: ' . $swiper_p_width_xs . 'px;
                    height: ' . $swiper_p_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_xs . 'px;
                    height: ' . $swiper_p_height_a_xs . 'px;
                }
            }';

        return $css;
    }

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;

        $js = 'jQuery(function($){';
        $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: true,
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
            });';
        $js .= '})';

        return $js;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        var jw_media_item = data.jw_media_item || 0;
        var jw_link_item = data.jw_link_item || 0;
        
        // 2023.04.06 背景色设置
        var bgColor = data.bgColor || "#1e1e1e";
        var bot_borderColor = data.bot_borderColor || "#393939";
        var bot_l_color = data.bot_l_color || "#777777";
        var bot_l_color_h = data.bot_l_color_h || "#ffffff";
        var bot_s_color = data.bot_s_color || "#7a7a7a";
        var bot_s_color_h = data.bot_s_color_h || "#ffffff";
        var bot_left_color = data.bot_left_color || "#656565";
        var bot_right_color = data.bot_right_color || "#777777";

        
        #>
        <style type="text/css">
            {{ addonId }} .swiper-box-main {
                position: relative;
            }
            
            {{ addonId }} a{color:inherit;text-decoration:none}
            {{ addonId }} .i200{overflow:hidden}
            {{ addonId }} .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}
            {{ addonId }} .animated{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}
            @media only screen and (min-width:1600px){
            
            {{ addonId }} .p-bot{display:none} 
            {{ addonId }} .ft1-a1{animation:fadenum 2s}
            {{ addonId }} .ft1-b1 .ft1-b2{animation:fadenum 3s}
            @keyframes fadenum{0%{transform:translateY(100px)}}
            {{ addonId }} .ftl-home{ margin-top: 10rem;}
            {{ addonId }} .ft1-a1{width:100%;height:100px}
            {{ addonId }} .ft1-a2{width:1560px;height:100%;position:relative;margin:0 auto} 
            {{ addonId }} .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}
            {{ addonId }} .ft1-a3>div:nth-child(1){font-size:18px;line-height:100px;color:#454545;font-weight:bold;float:left}
            {{ addonId }} .ft1-a3>div:nth-child(2){width:1px;height:18px;background:#b2b2b2;position:relative;top:calc(50% - 18px/2);float:right}
            {{ addonId }} .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}
            {{ addonId }} .ft1-a5{height:100px;position:relative;cursor:pointer}
            {{ addonId }} .ft1-a6{width:280px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 280px/2);transition:0.5s}
            {{ addonId }} .ft1-a5:hover .ft1-a6{height:146px;transition:0.5s}
            {{ addonId }} .ft1-a7{width:100%;height:146px;position:relative;top:1px}
            {{ addonId }} .ft1-a8{width:100%;height:136px;background:#d9012a;padding:14px}
            {{ addonId }} .ft1-a8>div:nth-child(1){height:108px;float:left}
            {{ addonId }} .ft1-a8>div:nth-child(2){width:120px;height:108px;float:left;display:table;margin-left:20px}
            {{ addonId }} .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:15px;line-height:24px;color:#fff;font-weight:bold}
            {{ addonId }} .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:4.2rem;}
            {{ addonId }} .ft1-a10{height:100px;position:relative}
            {{ addonId }} .ft1-a10 .ft1-a11 img{width:26px;height:20px}
            {{ addonId }} .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}
            {{ addonId }} .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}
            {{ addonId }} .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}
            {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}
            {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}
            {{ addonId }} .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}
            {{ addonId }} .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}
            {{ addonId }} .ft1-b1{width:100%;position:relative;background:{{bgColor}};padding-top:80px}
            {{ addonId }} .ft1-b2{width:1560px;margin:0 auto;padding-bottom:64px;display:flex;justify-content:space-between}
            {{ addonId }} .ft1-b3{position:relative}
            {{ addonId }} .ft1-b4{font-size:18px;line-height:18px;color:{{bot_l_color}};font-weight:bold;margin-bottom:36px;transition:0.5s}
            {{ addonId }} .ft1-b4:hover{color:{{bot_l_color_h}};transition:0.5s}
            {{ addonId }} .ft1-b5{position:relative}
            {{ addonId }} .ft1-b5>div{font-size:14px;line-height:30px;color:{{bot_s_color}};transition:0.5s}
            {{ addonId }} .ft1-b5>div:hover{color:{{bot_s_color_h}};text-decoration:underline;transition:0.5s}
            {{ addonId }} .ft1-b6{width:1560px;padding:36px 0;border-top:1px solid {{bot_borderColor}};margin:0 auto}
            {{ addonId }} .ft1-b7{font-size:12px;line-height:1.2rem;color:{{bot_left_color}};vertical-align:middle;float:left}
            {{ addonId }} .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}
            {{ addonId }} .ft1-b8{float:right}
            {{ addonId }} .ft1-b8>div{font-size:12px;line-height:20px;color:{{bot_right_color}};float:left;margin-left:20px}
            {{ addonId }} .ft1-b8>div:first-child{
                margin-left:0}
            }
            @media only screen and (max-width:1599px) and (min-width:1400px){
                {{ addonId }} .p-bot{display:none}  
                {{ addonId }} .ft1-a1{width:100%;height:100px;margin-top:65px}
                {{ addonId }} .ft1-a2{width:1360px;height:100%;position:relative;margin:0 auto}
                {{ addonId }} .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(1){font-size:18px;line-height:100px;color:#454545;font-weight:bold;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(2){width:1px;height:18px;background:#b2b2b2;position:relative;top:calc(50% - 18px/2);float:right}
                {{ addonId }} .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-a5{height:100px;position:relative;cursor:pointer}
                {{ addonId }} .ft1-a6{width:280px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 280px/2);transition:0.5s}
                {{ addonId }} .ft1-a5:last-child .ft1-a6{left:calc(50% - 280px/2 - 50px)}
                {{ addonId }} .ft1-a5:hover .ft1-a6{height:146px;transition:0.5s}
                {{ addonId }} .ft1-a7{width:100%;height:146px;position:relative;top:1px}
                {{ addonId }} .ft1-a8{width:100%;height:136px;background:#d9012a;padding:14px}
                {{ addonId }} .ft1-a8>div:nth-child(1){height:108px;float:left}
                {{ addonId }} .ft1-a8>div:nth-child(2){width:120px;height:108px;float:right;display:table}
                {{ addonId }} .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:15px;line-height:24px;color:#fff;font-weight:bold}
                {{ addonId }} .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}
                {{ addonId }} .ft1-a10{height:100px;position:relative}
                {{ addonId }} .ft1-a10 .ft1-a11 img{width:26px;height:20px}
                {{ addonId }} .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}
                {{ addonId }} .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}
                {{ addonId }} .ft1-b1{width:100%;position:relative;background:{{bgColor}};padding-top:80px}
                {{ addonId }} .ft1-b2{width:1360px;margin:0 auto;padding-bottom:64px;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-b3{position:relative}
                {{ addonId }} .ft1-b4{font-size:18px;line-height:18px;color:{{bot_l_color}};font-weight:bold;margin-bottom:36px;transition:0.5s}
                {{ addonId }} .ft1-b4:hover{color:{{bot_l_color_h}};transition:0.5s}
                {{ addonId }} .ft1-b5{position:relative}
                {{ addonId }} .ft1-b5>div{font-size:14px;line-height:30px;color:{{bot_s_color}};transition:0.5s}
                {{ addonId }} .ft1-b5>div:hover{color:{{bot_s_color_h}};text-decoration:underline;transition:0.5s}
                {{ addonId }} .ft1-b6{width:1360px;padding:26px 0;border-top:1px solid {{bot_borderColor}};margin:0 auto}
                {{ addonId }} .ft1-b7{font-size:12px;line-height:20px;color:{{bot_left_color}};vertical-align:middle;float:left}
                {{ addonId }} .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}
                {{ addonId }} .ft1-b8{float:right}
                {{ addonId }} .ft1-b8>div{font-size:12px;line-height:20px;color:{{bot_right_color}};float:left;margin-left:20px}
                {{ addonId }} .ft1-b8>div:first-child{margin-left:0}
            }
            @media only screen and (max-width:1399px) and (min-width:1200px){
                {{ addonId }} .p-bot{display:none}    
                {{ addonId }} .ft1-a1{width:100%;height:100px;margin-top:65px}
                {{ addonId }} .ft1-a2{width:1160px;height:100%;position:relative;margin:0 auto}
                {{ addonId }} .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(1){font-size:16px;line-height:100px;color:#454545;font-weight:bold;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(2){width:1px;height:16px;background:#b2b2b2;position:relative;top:calc(50% - 16px/2);float:right}
                {{ addonId }} .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-a5{height:100px;position:relative;cursor:pointer}
                {{ addonId }} .ft1-a6{width:240px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 240px/2);transition:0.5s}
                {{ addonId }} .ft1-a5:last-child .ft1-a6{left:calc(50% - 240px/2 - 40px)}
                {{ addonId }} .ft1-a5:hover .ft1-a6{height:126px;transition:0.5s}
                {{ addonId }} .ft1-a7{width:100%;height:126px;position:relative;top:1px}
                {{ addonId }} .ft1-a8{width:100%;height:116px;background:#d9012a;padding:10px}
                {{ addonId }} .ft1-a8>div:nth-child(1){height:96px;float:left}
                {{ addonId }} .ft1-a8>div:nth-child(2){width:104px;height:96px;float:right;display:table}
                {{ addonId }} .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:12px;line-height:20px;color:#fff;font-weight:bold}
                {{ addonId }} .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}
                {{ addonId }} .ft1-a10{height:100px;position:relative}
                {{ addonId }} .ft1-a10 .ft1-a11 img{width:26px;height:20px}
                {{ addonId }} .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}
                {{ addonId }} .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}
                {{ addonId }} .ft1-b1{width:100%;position:relative;background:{{bgColor}};padding-top:60px}
                {{ addonId }} .ft1-b2{width:1160px;margin:0 auto;padding-bottom:44px;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-b3{position:relative}
                {{ addonId }} .ft1-b4{font-size:16px;line-height:16px;color:{{bot_l_color}};font-weight:bold;margin-bottom:28px;transition:0.5s}
                {{ addonId }} .ft1-b4:hover{color:{{bot_l_color_h}};transition:0.5s}
                {{ addonId }} .ft1-b5{position:relative}
                {{ addonId }} .ft1-b5>div{font-size:12px;line-height:26px;color:{{bot_s_color}};transition:0.5s}
                {{ addonId }} .ft1-b5>div:hover{color:{{bot_s_color_h}};text-decoration:underline;transition:0.5s}
                {{ addonId }} .ft1-b6{width:1160px;padding:20px 0;border-top:1px solid {{bot_borderColor}};margin:0 auto}
                {{ addonId }} .ft1-b7{font-size:12px;line-height:20px;color:{{bot_left_color}};vertical-align:middle;float:left}
                {{ addonId }} .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}
                {{ addonId }} .ft1-b8{float:right}
                {{ addonId }} .ft1-b8>div{font-size:12px;line-height:20px;color:{{bot_right_color}};float:left;margin-left:20px}
                {{ addonId }} .ft1-b8>div:first-child{margin-left:0}
            }
            @media only screen and (max-width:1199px) and (min-width:1024px){
                {{ addonId }} .p-bot{display:none}    
                {{ addonId }} .ft1-a1{width:100%;height:100px;margin-top:65px}
                {{ addonId }} .ft1-a2{width:960px;height:100%;position:relative;margin:0 auto}
                {{ addonId }} .ft1-a3{width:calc(190/1560*100%);height:100%;position:relative;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(1){font-size:16px;line-height:100px;color:#454545;font-weight:bold;float:left}
                {{ addonId }} .ft1-a3>div:nth-child(2){width:1px;height:16px;background:#b2b2b2;position:relative;top:calc(50% - 16px/2);float:right}
                {{ addonId }} .ft1-a4{width:calc(1240/1560*100%);height:100%;position:relative;float:right;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-a5{height:100px;position:relative;cursor:pointer}
                {{ addonId }} .ft1-a6{width:240px;height:0;overflow:hidden;position:absolute;bottom:75px;left:calc(50% - 240px/2);transition:0.5s}
                {{ addonId }} .ft1-a5:last-child .ft1-a6{left:calc(50% - 240px/2 - 40px)}
                {{ addonId }} .ft1-a5:hover .ft1-a6{height:126px;transition:0.5s}
                {{ addonId }} .ft1-a7{width:100%;height:126px;position:relative;top:1px}
                {{ addonId }} .ft1-a8{width:100%;height:116px;background:#d9012a;padding:10px}
                {{ addonId }} .ft1-a8>div:nth-child(1){height:96px;float:left}
                {{ addonId }} .ft1-a8>div:nth-child(2){width:104px;height:96px;float:right;display:table}
                {{ addonId }} .ft1-a8>div:nth-child(2)>div{display:table-cell;vertical-align:middle;font-size:12px;line-height:20px;color:#fff;font-weight:bold}
                {{ addonId }} .ft1-a9{width:0;height:0;border-top:9px solid #d9012a;border-left:9px solid transparent;border-right:9px solid transparent;position:absolute;top:136px;left:22px}
                {{ addonId }} .ft1-a10{height:100px;position:relative}
                {{ addonId }} .ft1-a10 .ft1-a11 img{width:26px;height:20px}
                {{ addonId }} .ft1-a11{height:20px;position:relative;top:calc(50% - 20px/2);float:left;margin-right:20px}
                {{ addonId }} .ft1-a11 img:nth-child(1){position:relative;opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a11 img:nth-child(2){position:absolute;top:0;right:0;opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(1){opacity:0;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a11 img:nth-child(2){opacity:1;transition:0.5s}
                {{ addonId }} .ft1-a12{font-size:16px;line-height:100px;color:#454545;float:left;transition:0.5s}
                {{ addonId }} .ft1-a5:hover .ft1-a12{color:#d90029;transition:0.5s}
                {{ addonId }} .ft1-b1{width:100%;position:relative;background:{{bgColor}};padding-top:60px}
                {{ addonId }} .ft1-b2{width:960px;margin:0 auto;padding-bottom:44px;display:flex;justify-content:space-between}
                {{ addonId }} .ft1-b3{position:relative}
                {{ addonId }} .ft1-b4{font-size:16px;line-height:16px;color:{{bot_l_color}};font-weight:bold;margin-bottom:28px;transition:0.5s}
                {{ addonId }} .ft1-b4:hover{color:{{bot_l_color_h}};transition:0.5s}
                {{ addonId }} .ft1-b5{position:relative}
                {{ addonId }} .ft1-b5>div{font-size:12px;line-height:26px;color:{{bot_s_color}};transition:0.5s}
                {{ addonId }} .ft1-b5>div:hover{color:{{bot_s_color_h}};text-decoration:underline;transition:0.5s}
                {{ addonId }} .ft1-b6{width:960px;padding:20px 0;border-top:1px solid {{bot_borderColor}};margin:0 auto}
                {{ addonId }} .ft1-b7{font-size:12px;line-height:20px;color:{{bot_left_color}};vertical-align:middle;float:left}
                {{ addonId }} .ft1-b7 img{display:inline-block;height:16px;vertical-align:middle;margin:0 10px;width:auto;}
                {{ addonId }} .ft1-b8{float:right}
                {{ addonId }} .ft1-b8>div{font-size:12px;line-height:20px;color:{{bot_right_color}};float:left;margin-left:20px}
                {{ addonId }} .ft1-b8>div:first-child{margin-left:0}
            }
            @media only screen and (max-width:1023px){
                {{ addonId }} .p-bot{display:none}   
                {{ addonId }} .ftl-home{width:100%;height:20rem;background:#1e1e1e;padding-top:.98rem;padding-bottom:.68rem;position:relative;z-index:5}
                {{ addonId }} .ft1-a1{display:none}
                {{ addonId }} .ft1-b1{display:none}
                {{ addonId }} .p-bot .p-bot-a1{animation:fadenum 2s}
                @keyframes fadenum{
                    {{ addonId }} 0%{opacity:0}
                    {{ addonId }} 20%{opacity:0.1}
                    {{ addonId }} 40%{opacity:0.2}
                    {{ addonId }} 60%{opacity:0.3}
                    {{ addonId }} 100%{transform:translateY(20px);opacity:1}
                }
                {{ addonId }} .p-bot{background:#1e1e1e;padding-top:.98rem;padding-bottom:.68rem;position:relative;z-index:5}
                {{ addonId }} .p-bot-a1{display:flex;justify-content:center;padding-bottom:2rem}
                {{ addonId }} .p-bot-a1 .p-bot-a1-list img:first-child{width:1.5rem}
                {{ addonId }} .p-bot-a1 .p-bot-a1-list:nth-child(3) img:first-child{width:1.3rem}
                {{ addonId }} .p-bot-a1-list{margin-right:.7rem;position:relative;z-index:5;padding:1rem}
                {{ addonId }} .p-bot-a1-list:last-child{margin-right:0}
                {{ addonId }} .p-bot-a1-list img:last-child{width:5rem;height:5rem;position:absolute;bottom:110%;left:calc(50% - 5rem/2);transform:scale(0);opacity:0;transition:0.5s}
                {{ addonId }} .p-bot-a1-list:hover img:last-child{transform:scale(1);opacity:1;transition:0.5s}
                {{ addonId }} .p-bot-a2{color:#777777;font-size:1rem;text-align:center;border-bottom:.1rem solid rgba(255,255,255,.1);padding-bottom:1.5rem;margin-bottom:1.3rem}
                {{ addonId }} .p-bot-a2 a:first-child{margin-right:2rem}
                {{ addonId }} .p-bot-a3{font-size:.22rem;color:#656565;text-align:center;margin-bottom:1.25rem}
                {{ addonId }} .p-bot-a4{font-size:.22rem;color:#656565;text-align:center}
                {{ addonId }} .p-bot-a4 img{display:inline-block;margin-right:.06rem}
            }

        </style>
        
            <div class="ftl-home">
            <div class="ft1-a1 wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
            <div class="ft1-a2 clear">
            <div class="ft1-a3 clear">
                <div>{{data.media_title}}</div>
                <div></div>
            </div>
            <div class="ft1-a4">
     
                <# _.each(jw_media_item, function(lcfooter, key){ #>
                    
                    <div class="ft1-a5">
                        <div class="ft1-a6">
                            <div class="ft1-a7">
                                <div class="ft1-a8 clear">
                                    <div class="i200"> <img src=\'{{lcfooter.qr_code}}\'  width="107"> </div>
                                    <div>
                                        <div>{{lcfooter.content}}</div>
                                    </div>
                                </div>
                                <div class="ft1-a9"></div>
                            </div>
                        </div>
                        <div class="ft1-a10"> 
                            <a href="{{lcfooter.a_url}}" target="_blank">
                                <div class="ft1-a11 i200"> <img src=\'{{lcfooter.icon_url}}\' > <img src=\'{{lcfooter.icon_url2}}\' > </div>
                            </a>
                            <div class="ft1-a12">{{lcfooter.title}}</div>
                        </div>
                    </div>
                <# }) #>
            
            </div>
            </div>
            </div>
            <div class="ft1-b1">
            <div class="ft1-b2">
  
                <# _.each(jw_link_item, function(jwlinkitem, keys){ #>
                    <div class="ft1-b3 wow fadeIn animated animated" data-wow-duration="2s" data-wow-delay="0.30000000000000004s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
                        <div class="ft1-b4">
                            <a href="{{jwlinkitem.title_url}}" target="_blank">{{jwlinkitem.title}}</a>
                        </div>
                        <div class="ft1-b5">
                            <# _.each(jwlinkitem.jw_link_data, function(jwlinkdata, keys1){ #>
                                <div><a href="{{jwlinkdata.a_url}}" target="_blank">{{jwlinkdata.title}}</a></div>
                            <# }) #>
                        </div>
                    </div>
                <# }) #>

            </div>
            <# 
                var youbian_img = data.youbian_img || "https://oss.lcweb01.cn/joomla/20220812/f9fd0eb548f5aee1a910dd0d7df0190a.png";
                var zhuceren = data.zhuceren || "注册人权利与责任";
                var ybh_img = data.ybh_img || "0";

                
            #>
            <div class="ft1-b6 clear " >
            <div class="ft1-b7"><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">备案号：{{data.beianhao}}</a> 邮编：{{data.youbian}}
             <# if(data.ybh_img==1){
                }else{ #>
                    <img src=\' {{youbian_img}} \'>
            <# } #> 
            {{data.wanganbeian}}  {{zhuceren}}</div>
            <div class="ft1-b8 clear">
            <div><a href="{{data.shouqian_url}}" target="_blank">售前客服</a></div>
             <div><a href="{{data.shouhou_url}}" target="_blank">售后客服</a></div>
             </div>
             </div>
            <div class="p-bot zmr-phone">
             <div class="p-bot-a1">

                <# _.each(jw_media_item, function(lcfooter2, key){ #>
                    <div class="p-bot-a1-list wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;"> <img src=\'{{lcfooter2.icon_url3}}\' alt=""> <a href="{{lcfooter2.a_url}}" target="_blank"></a> <img src=\'{{lcfooter2.qr_code}}\' alt=""> </div>
                <# }) #>
                    
            </div>
            <div class="p-bot-a2"> <a href="{{data.shouqian_url}}" target="_blank">售前客服</a> <a href="{{data.shouhou_url}}" target="_blank">售后客服</a> </div>
            <div class="p-bot-a3">备案号：{{data.beianhao}} 邮编：{{data.youbian}}</div>
            <div class="p-bot-a4"><img src="https://oss.lcweb01.cn/joomla/20220616/d421f9ea3d2aa9cee13ce90d0ac6b450.png" alt="">{{data.wanganbeian}} 注册人权利与责任</div>
            </div>
            </div>

        ';

        return $output;
    }

}
