<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_modal',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_DESC'),
		'category' => '其他',
		'attr' => array(
			'general' => array(

				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'modal_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_DESC'),
					'values' => array(
						'button' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_BUTTON'),
						'image' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE'),
						'icon' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_TYPE_ICON'),
					),
					'std' => 'button',
				),
				'tc_type' => array(
					'type' => 'select',
					'title' => '选择弹出类型',
					'desc' => '',
					'values' => array(
						'text' => '文字',
						'register' => '注册表单',
						'feedback' => '意见反馈',
					),
					'std' => 'text',
				),
				'redirect_url' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FORM_BUILDER_REDIRECT_URL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FORM_BUILDER_REDIRECT_URL_DESC'),
					'depends' => array(
						array('tc_type', '=', 'register'),
					),
				),

				// Button
				'button_text' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT_DESC'),
					'std' => '文本按钮',
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_type' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
					'values' => array(
						'default' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
						'primary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
						'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
						'success' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
						'info' => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
						'warning' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
						'danger' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
						'dark' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
						'link' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
						'custom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
					),
					'std' => 'default',
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_FAMILY'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.btn { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(array('title', '!=', '')),
				),

				'button_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
					),
				),

				'button_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
					),
				),

				'button_padding' => array(
					'type' => 'padding',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING_DESC'),
					'std' => '',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
					),
				),

				'button_appearance' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
					'values' => array(
						'' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
						'gradient' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_GRADIENT'),
						'outline' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
						'3d' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
					),
					'std' => 'flat',
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_status' => array(
					'type' => 'buttons',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ENABLE_BACKGROUND_OPTIONS'),
					'std' => 'normal',
					'values' => array(
						array(
							'label' => 'Normal',
							'value' => 'normal'
						),
						array(
							'label' => 'Hover',
							'value' => 'hover'
						),
					),
					'tabs' => true,
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
						array('button_text', '!=', ''),
					)
				),

				'button_background_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_DESC'),
					'std' => '#444444',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_appearance', '!=', 'gradient'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'normal'),
						array('button_text', '!=', ''),
					)
				),

				'button_background_gradient' => array(
					'type' => 'gradient',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
					'std' => array(
						"color" => "#B4EC51",
						"color2" => "#429321",
						"deg" => "45",
						"type" => "linear"
					),
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_appearance', '=', 'gradient'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'normal'),
						array('button_text', '!=', ''),
					)
				),

				'button_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_DESC'),
					'std' => '#fff',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'normal'),
						array('button_text', '!=', ''),
					)
				),

				'button_background_color_hover' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER_DESC'),
					'std' => '#222',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_appearance', '!=', 'gradient'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'hover'),
						array('button_text', '!=', ''),
					)
				),

				'button_background_gradient_hover' => array(
					'type' => 'gradient',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
					'std' => array(
						"color" => "#429321",
						"color2" => "#B4EC51",
						"deg" => "45",
						"type" => "linear"
					),
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_appearance', '=', 'gradient'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'hover'),
						array('button_text', '!=', ''),
					)
				),

				'button_color_hover' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER_DESC'),
					'std' => '#fff',
					'depends' => array(
						array('modal_selector', '=', 'button'),
						array('button_type', '=', 'custom'),
						array('button_status', '=', 'hover'),
						array('button_text', '!=', ''),
					)
				),

				'button_size' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
					'values' => array(
						'' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
						'lg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
						'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
						'sm' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
						'xs' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
					),
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_shape' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_DESC'),
					'values' => array(
						'rounded' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUNDED'),
						'square' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_SQUARE'),
						'round' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUND'),
					),
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_block' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
					'values' => array(
						'' => JText::_('JNO'),
						'jwpf-btn-block' => JText::_('JYES'),
					),
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_icon' => array(
					'type' => 'icon',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				'button_icon_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
					'values' => array(
						'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'depends' => array(
						array('modal_selector', '=', 'button')
					),
				),

				// Image
				'selector_image' => array(
					'type' => 'media',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT_DESC'),
					'depends' => array('modal_selector' => 'image')
				),

				// Icon
				'show_ripple_effect' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_RIPPLE_EFFECT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_RIPPLE_EFFECT_DESC'),
					'std' => 0,
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_name' => array(
					'type' => 'icon',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_DESC'),
					'std' => '',
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_SIZE_DESC'),
					'std' => array('md' => 36),
					'max' => 400,
					'responsive' => true,
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_COLOR_DESC'),
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_background' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BG_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BG_COLOR_DESC'),
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_COLOR_DESC'),
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_border_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_WIDTH_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_WIDTH_SIZE_DESC'),
					'placeholder' => '3',
					'max' => 400,
					'responsive' => true,
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_border_radius' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_RADIUS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_BORDER_RADIUS_DESC'),
					'placeholder' => '5',
					'max' => 400,
					'responsive' => true,
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_icon_padding' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_PADDING'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_PADDING_DESC'),
					'placeholder' => '20',
					'max' => 400,
					'responsive' => true,
					'depends' => array('modal_selector' => 'icon')
				),

				'selector_text' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_DESC'),
					'std' => '',
					'depends' => array(array('modal_selector', '!=', 'button')),
					'placeholder' => 'Click to view detail' 
				),

				'selector_text_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_SIZE_DESC'),
					'placeholder' => 36,
					'max' => 300,
					'depends' => array(array('modal_selector', '!=', 'button')),
					'responsive' => true,
				),

				'selector_text_weight' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_WEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_WEIGHT_DESC'),
					'placeholder' => 700,
					'std' => '',
					'depends' => array(array('modal_selector', '!=', 'button')),
				),

				'selector_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_COLOR_DESC'),
					'std' => '',
					'depends' => array(array('modal_selector', '!=', 'button')),
				),

				'selector_text_margin' => array(
					'type' => 'margin',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_MARGIN'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ICON_TEXT_MARGIN_DESC'),
					'placeholder' => 10,
					'depends' => array(array('modal_selector', '!=', 'button')),
				),

				//Admin Only
				'separator' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_MODAL_CONTENT'),
					'depends' => array(array('tc_type', '=', 'text')),
				),

				'modal_content_type' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_CONTENT_TYPE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_CONTENT_TYPE_DESC'),
					'values' => array(
						'text' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_CONTENT_TYPE_TEXT'),
						'image' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_CONTENT_TYPE_IMAGE'),
						'video' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_CONTENT_TYPE_VIDEO'),
					),
					'std' => 'text',
					'depends' => array(array('tc_type', '=', 'text')),
				),

				'modal_content_text' => array(
					'type' => 'editor',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_TEXT_DESC'),
					'std' => '凯文鸡，牛腰肉球头，牛腰肉卷，t骨。肉卷羊腿猪肉意式烟肉肉条火腿火腿蹄肉球。肩熏肉和熏肉碎肉。西冷牛扒，牛里脊，牛腩，牛臀肉，咸牛柳，小牛排，意大利熏肠，鸡腿，鸡爪，火鸭，牛t骨，牛眼。猪腰肉绞碎，圆t形骨，肩肉，牛肋，猪，五香牛肉。鹿里脊肉，鸡腿，菲力牛排意大利腊肠，香肠小腿，汉堡，肉丸里脊，凯文三角。猪熏肠，里脊肉，肥背猪肉，香肠，牛肉干，三尖颈肉。br br意式烟肉，鸡肉，猪肉，五花肉，牛肉，意大利熏肠，肥背牛腰肉，牛肉，熏肉。西冷牛里脊肉，牛肉干，三尖火腿香肠，牛肉干，小排骨，舌肉卷，肥肉。熏肠、意式烟肉、肩肉、三尖熏牛肉、牛肉片、米农火腿、咸牛肉、意大利熏火腿、烤牛排骨。多纳香肠，火腿，沙朗肩肉，意式烟肉，牛肉里脊，米尼翁肉。肉丸，火腿，飞蹄，牛肉，菲力牛排，三角尖肉，鹿肉，鸭腿。',
					'depends' => array(
						array('tc_type', '=', 'text'),
						array('modal_content_type','=','text')
					),
				),

				'modal_content_image' => array(
					'type' => 'media',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_IMAGE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_IMAGE_DESC'),
					'depends' => array(
						array('modal_content_type', '=', 'image'),
						array('tc_type', '=', 'text')
					),
				),

				'modal_content_video_url' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_VIDEO'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_VIDEO_DESC'),
					'depends' => array(
						array('modal_content_type', '=', 'video'),
						array('tc_type', '=', 'text')
					),
				),

				'modal_popup_width' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_POPUP_WIDTH'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_POPUP_WIDTH_DESC'),
					'std' => '760',
					'depends' => array(
						array('modal_content_type', '!=', 'video'),
						array('tc_type', '=', 'text')
					),
				),

				'modal_popup_height' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_POPUP_HEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_POPUP_HEIGHT_DESC'),
					'std' => '440',
					'depends' => array(
						array('modal_content_type', '!=', 'video'),
						array('tc_type', '=', 'text')
					),
				),

				'alignment' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MODAL_SELECTOR_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'jwpf-text-left',
					'depends' => array(array('tc_type', '=', 'text')),
				),
				
				'tc_div_background' => array(
                     'type' => 'color',
                     'title' => JText::_('弹出层背景颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'input_border_color' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框边框颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'input_background' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框背景颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'input_size_color' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框字体颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'button_background' => array(
                     'type' => 'color',
                     'title' => JText::_('按钮背景颜色'),
                     'std' => '#ffffff',
                     'depends' => array(array('tc_type', '=', 'register')),
                 ),
                 'button_color_h' => array(
                     'type' => 'color',
                     'title' => JText::_('按钮字体颜色'),
                     'std' => '#000000',
                     'depends' => array(array('tc_type', '=', 'register')),
                 ),
                 'tc_div_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('弹出窗长度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '500',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
               'tc_div_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('弹出窗高度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '500',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
				
				'input_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框长度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '150',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                 'input_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框高度'),
                   'placeholder' => '10',
                   'max' => 100,
                   'min' => 0,
                   'std' => '36',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                'input_left_num' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框左右移动'),
                   'placeholder' => '0',
                   'max' => 1000,
                   'min' => 0,
                   'std' => '0',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                'input_top_num' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框上下距离'),
                   'placeholder' => '5',
                   'max' => 1000,
                   'min' => 5,
                   'std' => '5',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
               'input_border_radius' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框圆角'),
                   'placeholder' => '10',
                   'max' => 20,
                   'min' => 0,
                   'std' => '0',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
               'input_border_style' => array(
                   'type' => 'select',
                   'title' => '搜索框边框样式',
                   'values' => array(
                     'none' => '无边框',
                     'dotted' => '点线边框',
                     'dashed' => '虚线边框',
                     'solid' => '实线边框',
                   ),
                   'std' => 'solid',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
               'button_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮长度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '150',
                   'depends' => array(
                         array('tc_type', '=', 'register'),
                    ),
               ),
                 'button_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮高度'),
                   'placeholder' => '10',
                   'max' => 100,
                   'min' => 0,
                   'std' => '36',
                   'depends' => array(
                         array('tc_type', '=', 'register'),
                    ),
               ),
                'button_border_radius' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮圆角'),
                   'placeholder' => '10',
                   'max' => 20,
                   'min' => 0,
                   'std' => '2',
                   'depends' => array(
                         array('tc_type', '=', 'register'),
                    ),
               ),
               'submit_border_color' => array(
                     'type' => 'color',
                     'title' => JText::_('提交按钮边框颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'submit_background' => array(
                     'type' => 'color',
                     'title' => JText::_('提交按钮背景颜色'),
                     'std' => '#ffffff',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                 'submit_size_color' => array(
                     'type' => 'color',
                     'title' => JText::_('提交按钮字体颜色'),
                     'std' => '#000000',
                     'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 'submit_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('提交按钮长度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '150',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                 'submit_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('提交按钮高度'),
                   'placeholder' => '10',
                   'max' => 100,
                   'min' => 0,
                   'std' => '36',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                'submit_left_num' => array(
                   'type' => 'slider',
                   'title' => JText::_('提交按钮左右移动'),
                   'placeholder' => '0',
                   'max' => 1000,
                   'min' => 0,
                   'std' => '0',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                'submit_top_num' => array(
                   'type' => 'slider',
                   'title' => JText::_('提交按钮上下距离'),
                   'placeholder' => '5',
                   'max' => 1000,
                   'min' => 5,
                   'std' => '5',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
               'submit_border_radius' => array(
                   'type' => 'slider',
                   'title' => JText::_('提交按钮圆角'),
                   'placeholder' => '10',
                   'max' => 20,
                   'min' => 0,
                   'std' => '0',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
               'submit_border_style' => array(
                   'type' => 'select',
                   'title' => '提交按钮边框样式',
                   'values' => array(
                     'none' => '无边框',
                     'dotted' => '点线边框',
                     'dashed' => '虚线边框',
                     'solid' => '实线边框',
                   ),
                   'std' => 'solid',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
                 ),
                 
                  'textarea_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('文本域长度'),
                   'placeholder' => '100',
                   'max' => 200,
                   'min' => 0,
                   'std' => '60',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),
                 'textarea_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('文本域高度'),
                   'placeholder' => '10',
                   'max' => 100,
                   'min' => 0,
                   'std' => '5',
                   'depends' => array(
                         array('tc_type', '!=', 'text'),
                    ),
               ),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => '' 
				),
				
				
                
			),
		),
	)
);
