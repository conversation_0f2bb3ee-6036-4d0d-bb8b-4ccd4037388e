<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2024-02-26 17:09:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonAdaption extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;

        $ada = (isset($settings->ada) && $settings->ada) ? $settings->ada : 'phone';

        if($ada=='phone'){
            $output .= '
                <style>
                    body{max-width:750px;width:100%;margin:0 auto;}
                    body.helix-ultimate .jwpf-row-container{width:100%;}
                </style>
            ';
        }else{
            $output .= '<script>

                // jQuery(document).ready(function(){
                //     window.onresize=function (){
                //         let sheightow=jQuery(window).width();
                //         console.log(sheightow);
                //     }
                // })

            </script>';
        }


        return $output;
    }


    //用于设计器中显示
    public static function getTemplate()
    {
        return '<div>本段文字用于编辑模式下占位，预览模式下不显示</div>';
    }
}