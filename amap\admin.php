<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomW<PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

$params = JComponentHelper::getParams('com_jwpagefactory');
$amap_api = $params->get('amap_api', '');

$amap_config = array(
	'admin_label' => array(
		'type' => 'text',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
		'category' => '地图',
		'std' => ''
	),

	// Title
	'title' => array(
		'type' => 'text',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
		'std' => ''
	),

	'heading_selector' => array(
		'type' => 'select',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
		'values' => array(
			'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
			'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
			'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
			'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
			'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
			'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
		),
		'std' => 'h3',
		'depends' => array(array('title', '!=', '')),
	),

	'title_font_family' => array(
		'type' => 'fonts',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
		'depends' => array(array('title', '!=', '')),
		'selector' => array(
			'type' => 'font',
			'font' => '{{ VALUE }}',
			'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
		)
	),

	'title_fontsize' => array(
		'type' => 'slider',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
		'std' => '',
		'max' => 400,
		'responsive' => true,
		'depends' => array(array('title', '!=', '')),
	),

	'title_lineheight' => array(
		'type' => 'slider',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
		'std' => '',
		'max' => 400,
		'responsive' => true,
		'depends' => array(array('title', '!=', '')),
	),

	'title_font_style' => array(
		'type' => 'fontstyle',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
		'depends' => array(array('title', '!=', '')),
	),

	'title_letterspace' => array(
		'type' => 'select',
		'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
		'values' => array(
			'0' => 'Default',
			'1px' => '1px',
			'2px' => '2px',
			'3px' => '3px',
			'4px' => '4px',
			'5px' => '5px',
			'6px' => '6px',
			'7px' => '7px',
			'8px' => '8px',
			'9px' => '9px',
			'10px' => '10px'
		),
		'std' => '0',
		'depends' => array(array('title', '!=', '')),
	),

	'title_text_color' => array(
		'type' => 'color',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
		'depends' => array(array('title', '!=', '')),
	),

	'title_margin_top' => array(
		'type' => 'slider',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
		'placeholder' => '10',
		'max' => 400,
		'responsive' => true,
		'depends' => array(array('title', '!=', '')),
	),

	'title_margin_bottom' => array(
		'type' => 'slider',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
		'placeholder' => '10',
		'max' => 400,
		'responsive' => true,
		'depends' => array(array('title', '!=', '')),
	),

	// Map
	'separator_addon_options' => array(
		'type' => 'separator',
		'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS')
	)

);

if (empty($amap_api)) {
	$amap_config['message'] = array(
		'type' => 'message',
		'alert' => 'warning',
		'message' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_APIKEY_MISSING'),
	);
}

/* 新增参数 单点标注 */
// 地图展示类型
$amap_config['map_type'] = array(
    'type' => 'select',
    'title' => JText::_('地图展示形式'),
    'desc' => JText::_('这里选择您想要的地图展示形式'),
    'values' => array(
        'type01' => '默认',
        'type02' => '单点标注/多点切换',
        'type03' => '路径规划',
        'type04' => '3D地图(百度地图)',

    ),
    'std' => 'type01',
    'depends' => array(
//        array('map', '!=', '')
    ),
);

$amap_config['map'] = array(
	'type' => 'amap',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION_DESC'),
	'std' => '116.397464,39.908696'
);

//多点切换

$amap_config['duotu_location'] = array(
	'type' => 'checkbox',
	'title' => JText::_('开启多点切换'),
	'desc' => JText::_('开启后可设置多个地图坐标点，进行多个地图切换'),
	'values' => array(
		1 => JText::_('YES'),
		0 => JText::_('NO'),
	),
	'std' => 0,
    'depends' => array(
        array('map_type', '!=', 'type01'),
        array('map_type', '!=', 'type03'),
        array('map_type', '!=', 'type04'),
    ),
);

$amap_config['duotu_location_items'] = array(
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MULTI_LOCATION_ITEMS'),
	'attr' => array(
		'title' => array(
			'type' => 'text',
		    'title' => '当前标记点的名称',
		    'desc' => JText::_('用户自定义显示名称，当前标记点的名称'),
		    'std' => '北京市天安门',
		),
		
		'zbtype' => array(
            'type' => 'select',
		    'title' => JText::_('坐标系类型'),
		    'desc' => JText::_('gcj02坐标,表示高德坐标（gcj02坐标），GPS原始坐标,表示wgs84坐标（GPS原始坐标）'),
		    'values' => array(
		        'gaode' => 'gcj02坐标',
		        'wgs84' => 'GPS原始坐标',
		    ),
		    'std' => 'gaode',
		),

		'longitude' => array(
            'type' => 'text',
            'title' => '经度',
            'desc' => JText::_('请输入经度，如：116.397464'),
            'std' => '116.397464'
		),
        'latitude' => array(
            'type' => 'text',
            'title' => '纬度',
            'desc' => JText::_('请输入纬度，如：39.908696'),
            'std' => '39.908696'
        ),

        'left_img' => array(
            'type' => 'media',
            'title' => JText::_('左侧小图标'),
            'std' => 'https://oss.lcweb01.cn/joomla/20211213/152d1b1790b18bb7bcee4c93717e0d5b.png'
        ),
        'descrip' => array(
			'type' => 'text',
		    'title' => '当前标记点的描述',
		    'desc' => JText::_('地图右侧标题下的标题描述'),
		    'std' => '北京市天安门',
		),
		'dtname' => array(
			'type' => 'text',
		    'title' => '联系人',
		    'std' => '联系人：李经理',
		),
		'dttel' => array(
			'type' => 'text',
		    'title' => '电话',
		    'std' => '电话：010-6666666',
		),
		'dtadd' => array(
			'type' => 'text',
		    'title' => '地址',
		    'std' => '地址：北京市东城区长安街',
		),
		
	),
	'depends' => array(
		array('duotu_location', '!=', 0),
        array('map_type', '!=', 'type01'),
        array('map_type', '!=', 'type03'),
	)
);


//3D多图切换
$amap_config['sduotu_location_items'] = array(
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MULTI_LOCATION_ITEMS'),
	'attr' => array(

		'longitude' => array(
            'type' => 'text',
            'title' => '经度',
            'desc' => JText::_('请输入经度，如：116.397464'),
            'std' => '126.60804'
		),
        'latitude' => array(
            'type' => 'text',
            'title' => '纬度',
            'desc' => JText::_('请输入纬度，如：39.908696'),
            'std' => '45.770448'
        ),

        'left_img' => array(
            'type' => 'media',
            'title' => JText::_('左侧小图标'),
            'std' => 'https://oss.lcweb01.cn/joomla/20220519/3d7ec151c6a6042bc13a358aa562e954.png'
        ),
		'title' => array(
			'type' => 'text',
		    'title' => '当前标记点的名称',
		    'std' => '龙采科技集团有限责任公司（黑龙江总部）',
		),

        'descrip' => array(
			'type' => 'text',
		    'title' => '热线电话',
		    'std' => '************（转1）',
		),
		'dtname' => array(
			'type' => 'text',
		    'title' => '客户服务及投诉',
		    'std' => '************（转2）',
		),
		'dtadd' => array(
			'type' => 'text',
		    'title' => '地址',
		    'std' => '哈尔滨市道里区爱建路13号',
		),
		'dtdomain' => array(
			'type' => 'text',
		    'title' => '网址',
		    'std' => 'https://www.longcai.com',
		),
		
	),
	'depends' => array(
        array('map_type', '=', 'type04'),
	)
);
$amap_config['map_tubiao'] = array(
	'type' => 'text',
	'title' => '地图图标链接',
	'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/amap/assets/images/jump.png',
	'depends' => array(
        array('map_type', '=', 'type04'),
    ),
);
$amap_config['map_add'] = array(
	'type' => 'text',
	'title' => '地址',
	'std' => '哈尔滨市道里区爱建路13号',
	'depends' => array(
        array('map_type', '=', 'type04'),
    ),
);
$amap_config['map_email'] = array(
	'type' => 'text',
	'title' => '邮箱',
	'std' => '<EMAIL>',
	'depends' => array(
        array('map_type', '=', 'type04'),
    ),
);
$amap_config['map_tel'] = array(
	'type' => 'text',
	'title' => '电话',
	'std' => '************',
	'depends' => array(
        array('map_type', '=', 'type04'),
    ),
);
//

$amap_config['pc_table'] = array(
    'type' => 'select',
    'title' => JText::_('电脑站地图文字位置'),
    'values' => array(
        'wz1' => '左图右字',
        'wz2' => '左字右图',
    ),
    'std' => 'wz1',
    'depends' => array(
        array('duotu_location', '!=', 0),
    ),
);
$amap_config['sj_table'] = array(
    'type' => 'select',
    'title' => JText::_('手机站地图文字位置'),
    'values' => array(
        'wza1' => '上图下字',
        'wza2' => '上字下图',
    ),
    'std' => 'wza1',
    'depends' => array(
        array('duotu_location', '!=', 0),
    ),
);

$amap_config['libg_color'] = array(
	'type' => 'color',
	'title' => JText::_('划过背景色'),
	'std' => '#F8F8F8',
	'depends' => array(
        array('duotu_location', '!=', 0),
    ),
);
$amap_config['litit_color'] = array(
	'type' => 'color',
	'title' => JText::_('划过标题色'),
	'std' => '#265FAA',
	'depends' => array(
        array('duotu_location', '!=', 0),
    ),
);
$amap_config['lixq_color'] = array(
	'type' => 'color',
	'title' => JText::_('划过联系信息字体色'),
	'std' => '#9A9A9A',
	'depends' => array(
        array('duotu_location', '!=', 0),
    ),
);

//
//
//新增两项设置：经度map_lng和纬度map_lat
$amap_config['map_lng'] = array(
	'type' => 'text',
	'title' => '经度',
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION_DESC'),
	'std' => '116.397464',
	'depends' => array(
        array('duotu_location', '!=', '1'),
        array('map_type', '!=', 'type04'),
    ),
);
$amap_config['map_lat'] = array(
	'type' => 'text',
	'title' => '纬度',
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION_DESC'),
	'std' => '39.908696',
	'depends' => array(
        array('duotu_location', '!=', '1'),
		array('map_type', '!=', 'type04'),
    ),
);
$amap_config['map_coordinate'] = array(
    'type' => 'select',
    'title' => JText::_('坐标系类型'),
    'desc' => JText::_('gcj02坐标,表示高德坐标（gcj02坐标），GPS原始坐标,表示wgs84坐标（GPS原始坐标）'),
    'values' => array(
        'gaode' => 'gcj02坐标',
        'wgs84' => 'GPS原始坐标',
    ),
    'std' => 'gaode',
    'depends' => array(
        array('map_type', '!=', 'type01'),
        array('duotu_location', '!=', '1'),
		array('map_type', '!=', 'type04'),

    ),
);
$amap_config['map_name'] = array(
    'type' => 'text',
    'title' => '当前标记点的位置',
    'desc' => JText::_('用户自定义显示名称，当前标记点的名称'),
    'std' => '北京市天安门',
    'depends' => array(
        array('map_type', '!=', 'type01'),
        array('map_type', '!=', ''),
        array('duotu_location', '!=', '1'),
		array('map_type', '!=', 'type04'),
    ),
);


$amap_config['mobile_full'] = array(
    'type' => 'checkbox',
    'title' => '手机端是否全屏',
    'desc' => JText::_('在手机端显示想要全屏需要开启该选项,同时开启区块全屏显示'),
    'std' => 0,
    'depends' => array(
        array('map_type', '!=', 'type01'),
        array('map_type', '!=', ''),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['marker'] = array(
	'type' => 'media',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER_DESC'),
	'placeholder' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER_HINT'),
	'show_input' => true,
	'std' => '',
    'depends' => array(
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['infowindow'] = array(
	'type' => 'textarea',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_INFOWINDOW'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_INFOWINDOW_DESC'),
    'depends' => array(
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);
$amap_config['show_infowindow'] = array(
    'type' => 'checkbox',
    'title' => JText::_('默认显示位置信息'),
    'std' => 0,
    'depends' => array(
        array('multi_location', '!=', 1),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);
$amap_config['multi_location'] = array(
	'type' => 'checkbox',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MULTI_LOCATION'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_MULTI_LOCATION_DESC'),
	'values' => array(
		1 => JText::_('YES'),
		0 => JText::_('NO'),
	),
	'std' => 0,
    'depends' => array(
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['multi_location_items'] = array(
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_MULTI_LOCATION_ITEMS'),
	'attr' => array(
		'title' => array(
			'type' => 'text',
			'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TITLE'),
			'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TITLE_DESC'),
			'std' => JText::_('COM_JWPAGEFACTORY_ITEM') . ' 1'
		),

		'longitude' => array(
            'type' => 'text',
            'title' => '经度',
            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION_DESC'),
            'std' => '116.397464'
		),
        'latitude' => array(
            'type' => 'text',
            'title' => '纬度',
            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LOCATION_DESC'),
            'std' => '39.908696'
        ),
		'location_popup_text' => array(
			'type' => 'textarea',
			'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_INFOWINDOW'),
			'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_INFOWINDOW_DESC'),
			'std' => '',
		),
		'location_marker' => array(
			'type' => 'media',
			'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER'),
			'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER_DESC'),
			'placeholder' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_MARKER_HINT'),
			'show_input' => true,
			'std' => '',
		),
	),
	'depends' => array(
		array('multi_location', '!=', 0),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

	)
);


$amap_config['height'] = array(
	'type' => 'slider',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_HEIGHT'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_HEIGHT_DESC'),
	'placeholder' => '300',
	'std' => array('md' => 300),
	'max' => 2000,
	'responsive' => true,
	'depends' => array(array('map', '!=', '')),
);

$amap_config['lang'] = array(
	'type' => 'select',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LANG'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LANG_DESC'),
	'values' => array(
		'zh_cn' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LANG_ZH_CN'),
		'en' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LANG_EN'),
		'zh_en' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_LANG_ZH_EN'),
	),
	'std' => 'zh_cn',
    'depends' => array(
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
        array('map_type', '!=', 'type04'),
        array('map', '!=', '')
    ),
);

$amap_config['style'] = array(
	'type' => 'select',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_DESC'),
	'values' => array(
		'normal' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_NORMAL'),
		'dark' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_DARK'),
		'light' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_LIGHT'),
		'whitesmoke' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_WHITESMOKE'),
		'fresh' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_FRESH'),
		'grey' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_GREY'),
		'graffiti' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_GRAFFITI'),
		'macaron' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_MACARON'),
		'blue' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_BLUE'),
		'darkblue' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_DARKBLUE'),
		'wine' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_STYLE_WINE'),
	),
	'std' => 'normal',
	'depends' => array(
		array('map', '!=', ''),
		array('lang', '=', 'zh_cn'),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

	),
);

$amap_config['type'] = array(
	'type' => 'select',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_DESC'),
	'values' => array(
		'default' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_DEFAULT'),
		'roadnet' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_ROADNET'),
		'satellite' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_SATELLITE'),
		'hybrid' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_HYBRID'),
		'traffic' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_TYPE_TRAFFIC'),
	),
	'std' => 'default',
	'depends' => array(
	    array('map', '!=', ''),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['zoom'] = array(
	'type' => 'slider',
	'title' => JText::_('缩放级别'),
	'desc' => JText::_('可以调整地图的缩放级别，0是世界，5是省份，10是城区，15是街道'),
	'placeholder' => '15',
	'std' => '15',
	'max' => 25,
    'depends' => array(
        array('map', '!=', ''),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['mousescroll'] = array(
	'type' => 'select',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_DISABLE_MOUSE_SCROLL'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_DISABLE_MOUSE_SCROLL_DESC'),
	'values' => array(
		'false' => JText::_('JYES'),
		'true' => JText::_('JNO'),
	),
	'std' => 'true',
    'depends' => array(
        array('map', '!=', ''),
        array('map_type', '!=', 'type02'),
        array('map_type', '!=', 'type03'),
		array('map_type', '!=', 'type04'),

    ),
);

$amap_config['class'] = array(
	'type' => 'text',
	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
	'std' => ''
);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_amap',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AMAP_DESC'),
        'category' => '地图',
		'attr' => array(
			'general' => $amap_config
		),
	)
);
