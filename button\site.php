<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonButton extends JwpagefactoryAddons
{

	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : 'jwpf-text-left';
        $show_bus = (isset($settings->show_bus)) ? $settings->show_bus : 1;
		$class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
		$class .= (isset($settings->type) && $settings->type) ? ' jwpf-btn-' . $settings->type : '';
		$class .= (isset($settings->size) && $settings->size) ? ' jwpf-btn-' . $settings->size : '';
		$class .= (isset($settings->block) && $settings->block) ? ' ' . $settings->block : '';
		$class .= (isset($settings->shape) && $settings->shape) ? ' jwpf-btn-' . $settings->shape : ' jwpf-btn-rounded';
		$class .= (isset($settings->appearance) && $settings->appearance) ? ' jwpf-btn-' . $settings->appearance : '';
		$attribs = (isset($settings->target) && $settings->target) ? ' rel="noopener noreferrer" target="' . $settings->target . '"' : '';
		$tz_page_type = (isset($settings->tz_page_type)) ? $settings->tz_page_type : 'Internal_pages';
		$detail_page = (isset($settings->detail_page)) ? $settings->detail_page : '';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        if($detail_page_id){
            $idurl= $this->removeqsvar($_SERVER['REQUEST_URI'],['page']);
            $arrray=explode('&',$idurl);
            foreach ($arrray as $key=>$value){
                if(substr($value,0,3)=='id='){
                    $arrray[$key]='id='.base64_encode($detail_page_id);
                }
            }

            $return=implode('&',$arrray);
            $attribs .= ' href="' . $return . '"';
        }

		$attribs .= ' id="btn-' . $this->addon->id . '"';
		$text = (isset($settings->text) && $settings->text) ? $settings->text : '';
		$icon = (isset($settings->icon) && $settings->icon) ? $settings->icon : '';
		$icon_position = (isset($settings->icon_position) && $settings->icon_position) ? $settings->icon_position : 'left';
		$icon_arr = array_filter(explode(' ', $icon));
		$target = (isset($settings->target) && $settings->target) ? $settings->target : '_self';

		if (count($icon_arr) === 1) {
			$icon = 'fa ' . $icon;
		}


		$dialog_addon_id =  (isset($settings->dialog_addon_id) && $settings->dialog_addon_id) ? $settings->dialog_addon_id : '';


		if($settings->kqanimg == 1){
		    $img_btn = '
                <span>
                    <img class="icoImg" src="'. $settings->tbimg . '">';
		            if($settings->tbimg_hover) {
                        $img_btn .= '<img class="icoImgA" src="' . $settings->tbimg_hover . '">';
                    }
            $img_btn .= '</span>';
			$text = ($settings->tbimg) ? $img_btn . $text : $text;
		}else{
			if ($icon_position == 'left') {
				$text = ($icon) ? '<i class="' . $icon . '" aria-hidden="true"></i> ' . $text : $text;
			} else {
				$text = ($icon) ? $text . ' <i class="' . $icon . '" aria-hidden="true"></i>' : $text;
			}
		}

		$output = '<div class="' . $alignment . '">';
		//		商桥
		if($show_bus=='1'){
			$output .= '<a href="javascript:void(0)" ' . $attribs . ' style="cursor: pointer;" class="bus_bge jwpf-btn ' . $class . '">' . $text . '</a>';
		}else{
			if($tz_page_type=='Internal_pages')
			{
				$output .= '<a' . $attribs . ' class="jwpf-btn ' . $class . '">' . $text . '</a>';
			}
			else if($tz_page_type=='wx_links')
			{
				$output .='<input readOnly="true" class="input_" id="biao1" value="'.$text.'"/>';
                $output .='<div id="biaoios" class="div_">'.$text.'</div>';
				$output .= '<a onclick="openWx()" class="jwpf-btn ' . $class . '">' . $text . '</a>';
			}
			else if ($tz_page_type == 'dialog')
			{
				$output .= '<a class="jwpf-btn ' . $class . '" id="btn-'.$this->addon->id.'" style="cursor: pointer">' . $text . '</a>';
			}
			else
			{
				$output .= '<a href="' . $detail_page . '" class="jwpf-btn ' . $class . '" id="btn-'.$this->addon->id.'" target="'.$target.'">' . $text . '</a>';
			}
		}

		$output .= '</div>';
        if($show_bus=='1'){
            $output .= '<script>


                                   jQuery("' . $addon_id . ' .bus_bge").click(function(event) {
                                                if (jQuery("#nb_invite_ok").length > 0) {
                                                    jQuery("#nb_invite_ok").click();
                                                }else{
                                                     jQuery.ajax({
                                                        type: "POST",
                                                        url: "https://zhjzt.china9.cn/api/Shangqiao/index",
                                                        dataType: "json",
                                                        data: {
            //                                                "company_id" :' . $company_id . ',
            //                                                "site_id" :' . $site_id . ',
                                                         "company_id" :"167",
                                                            "site_id" :"338",
                                                        },
                                                        success: function (res) {
                                                            let stri = res.data
                                                            if(!stri){

                                                            }else{
                                                              console.log(stri)
                                                             let strpath = stri.indexOf("https") == -1 ? "" :stri.substring((stri.indexOf("https")), stri.indexOf("var s")-5);
                                                             console.log(strpath)
                                                               var hm= document.createElement("script");
                                                             hm.onload = hm.onreadystatechange = function() {
                                                                if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete" ) {
                                                                    hm.onload = hm.onreadystatechange = null;
                                                                }
                                                            };
                                                             hm.src = strpath;
                                                                jQuery("' . $addon_id . '").append(hm);
                                                            }

                                                        }
                                                    });
                                                }
                                        });

            </script>';

        }
		if($tz_page_type=='wx_links')
		{
			$output.='<script>
				var browser = {
				versions: function () {
					var u = navigator.userAgent,
							app = navigator.appVersion;
					return {
						trident: u.indexOf(\'Trident\') > -1, /*IE内核*/
						presto: u.indexOf(\'Presto\') > -1, /*opera内核*/
						webKit: u.indexOf(\'AppleWebKit\') > -1, /*苹果、谷歌内核*/
						gecko: u.indexOf(\'Gecko\') > -1 && u.indexOf(\'KHTML\') == -1, /*火狐内核*/
						mobile: !!u.match(/AppleWebKit.*Mobile.*/), /*是否为移动终端*/
						ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), /*ios终端*/
						android: u.indexOf(\'Android\') > -1 || u.indexOf(\'Linux\') > -1, /*android终端或者uc浏览器*/
						iPhone: u.indexOf(\'iPhone\') > -1, /*是否为iPhone或者QQHD浏览器*/
						iPad: u.indexOf(\'iPad\') > -1, /*是否iPad*/
						webApp: u.indexOf(\'Safari\') == -1, /*是否web应该程序，没有头部与底部*/
						souyue: u.indexOf(\'souyue\') > -1,
						superapp: u.indexOf(\'superapp\') > -1,
						weixin: u.toLowerCase().indexOf(\'micromessenger\') > -1,
						Safari: u.indexOf(\'Safari\') > -1
					};
				}(),
				language: (navigator.browserLanguage || navigator.language).toLowerCase()
			};

				function openWx(url){
					if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
						var text = document.getElementById(\'biaoios\');
						//获取隐藏的input，并写入text内容，在进行复制
						var input = document.getElementById("biao1");
						input.value = text.innerHTML;
						input.select();
						input.setSelectionRange(0, input.value.length);   //兼容ios
						document.execCommand("Copy");
						input.blur();
					}else{
						var Url2=document.getElementById("biao1");//要复制文字的节点
						Url2.select(); // 选择对象
						//document.execCommand("Copy"); // 执行浏览器复制命令
						$("#biao1").blur();
						if(document.execCommand(\'copy\', false, null)){
						var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
						}
					}
					jQuery(" .white_content").fadeIn(500);
					window.location.href = "weixin://";
				}

				</script>
				';
		}

		// 点击按钮出弹窗
		$tz_page_type = (isset($settings->tz_page_type) && $settings->tz_page_type) ? $settings->tz_page_type : 'Internal_pages';

		if($tz_page_type == 'dialog'){
			$btn_id = 'btn-' . $this->addon->id;
			$dialog_id = 'dialog-' . $this->addon->id;
			$dialog_img = (isset($settings->dialog_img) && $settings->dialog_img) ? $settings->dialog_img : 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50';
			$close_img = (isset($settings->close_img) && $settings->close_img) ? $settings->close_img : 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png';
			$mask_close = isset($settings->mask_close) ? $settings->mask_close : 1;
			$close_ids = $mask_close == 1 ? '#' . $dialog_id . ' .mask, #' . $dialog_id . ' .close' : '#' . $dialog_id . ' .close';

			$output.='<div id="'. $dialog_id .'">
				<div class="mask"></div>
				<div class="dialog">
					<div class="dialog-content">
						<img src="'. $dialog_img .'">
					</div>

					<div class="close">
						<img src="'. $close_img .'">
					</div>
				</div>
			</div>';

			if($dialog_addon_id){
				$output .= '<script>
					$("#' . $btn_id . '").on("click", function(){
						$("#dialog-' . $dialog_addon_id . '").show();
					})
				</script>';
			}else{
				$output .= '<script>
					$("#' . $btn_id . '").on("click", function(){
						$("#' . $dialog_id . '").show();
					})

					$("'. $close_ids .'").on("click", function(){
						$("#' . $dialog_id . '").hide();
					})
				</script>';
			}
		}
		// 点击按钮出弹窗结束

		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
		$css = '';

		$css_path = new JLayoutFile('addon.css.button', $layout_path);

		$options = new stdClass;
		$options->button_type = (isset($settings->type) && $settings->type) ? $settings->type : '';
		$options->button_appearance = (isset($settings->appearance) && $settings->appearance) ? $settings->appearance : '';
		$options->button_color = (isset($settings->color) && $settings->color) ? $settings->color : '';
		$options->button_border_width = (isset($settings->button_border_width) && $settings->button_border_width) ? $settings->button_border_width : '';
		$options->button_color_hover = (isset($settings->color_hover) && $settings->color_hover) ? $settings->color_hover : '';
		$options->button_background_color = (isset($settings->background_color) && $settings->background_color) ? $settings->background_color : '';
		$options->button_background_color_hover = (isset($settings->background_color_hover) && $settings->background_color_hover) ? $settings->background_color_hover : '';

		$options->open_bgimg = (isset($settings->open_bgimg) && $settings->open_bgimg) ? $settings->open_bgimg : 0;
		$options->bgimg = (isset($settings->bgimg) && $settings->bgimg) ? $settings->bgimg : 'https://oss.lcweb01.cn/joomla/20220707/40bd18d48b36f0680ad77999326161c4.png';
		$options->bgimg_hv = (isset($settings->bgimg_hv) && $settings->bgimg_hv) ? $settings->bgimg_hv : 'https://oss.lcweb01.cn/joomla/20220707/999b7a9498d320359df0df94a842a80c.png';


		$options->button_fontstyle = (isset($settings->fontstyle) && $settings->fontstyle) ? $settings->fontstyle : '';
		$options->button_font_style = (isset($settings->font_style) && $settings->font_style) ? $settings->font_style : '';
		$options->button_padding = (isset($settings->button_padding) && $settings->button_padding) ? $settings->button_padding : '';
		$options->button_padding_sm = (isset($settings->button_padding_sm) && $settings->button_padding_sm) ? $settings->button_padding_sm : '';
		$options->button_padding_xs = (isset($settings->button_padding_xs) && $settings->button_padding_xs) ? $settings->button_padding_xs : '';
		$options->fontsize = (isset($settings->fontsize) && $settings->fontsize) ? $settings->fontsize : '';
		//Button Type Link
		$options->link_button_color = (isset($settings->link_button_color) && $settings->link_button_color) ? $settings->link_button_color : '';
		$options->link_border_color = (isset($settings->link_border_color) && $settings->link_border_color) ? $settings->link_border_color : '';
		$options->link_button_border_width = (isset($settings->link_button_border_width) && $settings->link_button_border_width) ? $settings->link_button_border_width : '';
		$options->link_button_padding_bottom = (isset($settings->link_button_padding_bottom) && gettype($settings->link_button_padding_bottom) == 'string') ? $settings->link_button_padding_bottom : '';
		//Link Hover
		$options->link_button_hover_color = (isset($settings->link_button_hover_color) && $settings->link_button_hover_color) ? $settings->link_button_hover_color : '';
		$options->link_button_border_hover_color = (isset($settings->link_button_border_hover_color) && $settings->link_button_border_hover_color) ? $settings->link_button_border_hover_color : '';

		$options->fontsize_sm = (isset($settings->fontsize_sm) && $settings->fontsize_sm) ? $settings->fontsize_sm : '';
		$options->fontsize_xs = (isset($settings->fontsize_xs) && $settings->fontsize_xs) ? $settings->fontsize_xs : '';
		$options->button_letterspace = (isset($settings->letterspace) && $settings->letterspace) ? $settings->letterspace : '';
		$options->button_background_gradient = (isset($settings->background_gradient) && $settings->background_gradient) ? $settings->background_gradient : new stdClass();
		$options->button_background_gradient_hover = (isset($settings->background_gradient_hover) && $settings->background_gradient_hover) ? $settings->background_gradient_hover : new stdClass();


		$css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));

		//Icon style
		$icon_margin = (isset($settings->icon_margin) && trim($settings->icon_margin)) ? 'margin:' . $settings->icon_margin . ';' : '';
		$icon_margin_sm = (isset($settings->icon_margin_sm) && trim($settings->icon_margin_sm)) ? 'margin:' . $settings->icon_margin_sm . ';' : '';
		$icon_margin_xs = (isset($settings->icon_margin_xs) && trim($settings->icon_margin_xs)) ? 'margin:' . $settings->icon_margin_xs . ';' : '';

		// 边框
		$wbk_color = (isset($settings->wbk_color) && $settings->wbk_color) ? $settings->wbk_color : '';
		$wbk_color_hover = (isset($settings->wbk_color_hover) && $settings->wbk_color_hover) ? $settings->wbk_color_hover : '';
		// 动画
		$donghua_hover = (isset($settings->donghua_hover) && $settings->donghua_hover) ? $settings->donghua_hover : '';
		$button_background_color_hover = (isset($settings->background_color_hover) && $settings->background_color_hover) ? $settings->background_color_hover : '#00E66E';

		// 图片
		$kqanimg = (isset($settings->kqanimg) && $settings->kqanimg) ? $settings->kqanimg : '0';
		$tbimg_height = (isset($settings->tbimg_height) && $settings->tbimg_height) ? $settings->tbimg_height : '70';
		$annie_width = (isset($settings->annie_width) && $settings->annie_width) ? $settings->annie_width : '264';
		$tbimg_width = (isset($settings->tbimg_width) && $settings->tbimg_width) ? $settings->tbimg_width : '70';
		$annie_color = (isset($settings->annie_color) && $settings->annie_color) ? $settings->annie_color : '#fff';

		// 按钮高度
		$annie_height = (isset($settings->annie_height) && $settings->annie_height)? $settings->annie_height : $tbimg_height;
		$real_aimg_height = (isset($settings->annie_height) && $settings->annie_height)? $settings->annie_height : '';

		if($kqanimg == "1"){
			$css .= '
				'. $addon_id . ' .jwpf-btn {
					position: relative;
					display: inline-flex;
					align-items: center;
					height:'.$annie_height.'px;
					width:'.$annie_width.'px;
					padding-left: 20px;
					color:'.$annie_color.'!important;';
			    if($settings->alignment == 'jwpf-text-center') {
						$css .= '
					margin: auto;';
					}
			$css .= '
			}';
			$css .= '
				'. $addon_id . ' .jwpf-btn span{
					position: absolute;
			    display: flex;
			    align-items: center;
			    justify-content: center;
					' . ($real_aimg_height ? 'top: 50%; transform: translateY(-50%);' : 'top: -1px;') . '
					right: 0px;
					'.$icon_margin.'
					width:'.$tbimg_width.'px;
					height:'.$tbimg_height.'px;
				}';
				$css .= '
				'. $addon_id . ' .jwpf-btn:hover {
						color: ' . $settings->annie_color_hover .' !important;
				}';
				if($settings->tbimg_hover) {
					$css .= $addon_id . ' .jwpf-btn .icoImgA {
						position: absolute;
						right: 0px;
						top: -1px;
						width:'.$tbimg_width.'px;
						height:'.$tbimg_height.'px;
					}';
					$css .= $addon_id . ' .jwpf-btn .icoImgA,
					' . $addon_id . ' .jwpf-btn:hover .icoImg {
							opacity: 0;
							transform: scale(0);
							transition: 0.8s;
					}';
					$css .= $addon_id . ' .jwpf-btn .icoImg,
					' . $addon_id . ' .jwpf-btn:hover .icoImgA {
							opacity: 1;
							transform: scale(1);
							transition: 0.8s;
					}';
				}
		}
		$css .= $addon_id . ' .input_ {
            outline: none;
            border: 0px;
            color: rgba(0, 0, 0, 0.0);
            position: absolute;
            left: -20000px;
            background-color: transparent;
        }
        '.$addon_id .' .div_ {
            position: absolute;
            left: -20000px;
            color: rgba(0, 0, 0, 0);
            background-color: transparent;
        }';



		if($wbk_color!=""){
			$css .= $addon_id . ' .jwpf-btn-custom {
				border:1px solid '.$wbk_color.'!important;
			}';
		}
		if($wbk_color_hover!=""){
			$css .= $addon_id . ' .jwpf-btn-custom:hover {
				border:1px solid '.$wbk_color_hover.'!important;
			}';
		}
		if($donghua_hover!=""){
			$css .= $addon_id . ' .jwpf-btn-custom{
				position: relative;text-align: center;
				overflow:hidden;
			}';

			$css .= $addon_id . ' .jwpf-btn-custom::after{
			     	width: 0;
				    height: 0;
				    position: absolute;
				    top: 0;
				    bottom: 0;
				    left: 0;
				    right: 0;
				    margin: auto;
				    display: block;
				    content: "";
					background:'.$button_background_color_hover.';
				    -webkit-transition: 0.6s;
				    -o-transition: 0.6s;
				    transition: 0.6s;
				    border-radius: 100%;
				    z-index: -1;
			    }'.
			    $addon_id . ' .jwpf-btn-custom:hover::after{
			    	width: 100%;height: 100%;
			    	transform:scale(2,2);
				}'.
				$addon_id . ' .jwpf-btn-custom:hover{
			    	background:none!important;
				}';

		}else{
			$css .= $addon_id . ' .anim { display:none;';
			$css .= '}';
		}

		if($settings->appearance!="gradient"){

        	if($settings->open_bgimg==1){
        		$css .= $addon_id . ' .jwpf-btn-custom {
					background-color:transparent!important;
					background:url('.$settings->bgimg.') no-repeat center center/100% 100%;';
					if($settings->appearance == "3d"){
						$css .='border-bottom-color:transparent!important;';
					}
				$css .='}';
				$css .= $addon_id . ' .jwpf-btn-custom:hover {
					background-color:transparent!important;
					background:url('.$settings->bgimg_hv.') no-repeat center center/100% 100%;';
					if($settings->appearance == "3d"){
						$css .='border-bottom-color:transparent!important;';
					}
				$css .='
				}';

        	}
		}

		if ($icon_margin) {
			$css .= $addon_id . ' .jwpf-btn i {';
			$css .= $icon_margin;
			$css .= '}';
		}

		if ($icon_margin_sm) {
			$css .= '@media (min-width: 768px) and (max-width: 991px) {';
			$css .= $addon_id . ' .jwpf-btn i {';
			$css .= $icon_margin_sm;
			$css .= '}';
			$css .= '}';
		}

		if ($icon_margin_xs) {
			$css .= '@media (max-width: 767px) {';
			$css .= $addon_id . ' .jwpf-btn i {';
			$css .= $icon_margin_xs;
			$css .= '}';
			$css .= '}';
		}


		// 点击按钮出弹窗
		$dialog_id = '#dialog-' . $this->addon->id;
		$dialog_addon_id =  (isset($settings->dialog_addon_id) && $settings->dialog_addon_id) ? $settings->dialog_addon_id : '';
		$tz_page_type = (isset($settings->tz_page_type) && $settings->tz_page_type) ? $settings->tz_page_type : 'Internal_pages';
		$dialog_level = (isset($settings->dialog_level) && $settings->dialog_level) ? $settings->dialog_level : '99999999999999999999999999999';
		$mask_bg = (isset($settings->mask_bg) && $settings->mask_bg) ? $settings->mask_bg : 'rgba(0,0,0,0.5)';
		$dialog_img_fill = (isset($settings->dialog_img_fill) && $settings->dialog_img_fill) ? $settings->dialog_img_fill : 'cover';
		$close_img_fill = (isset($settings->close_img_fill) && $settings->close_img_fill) ? $settings->close_img_fill : 'cover';

		if (isset($settings->dialog_width) && $settings->dialog_width) {
            if (is_object($settings->dialog_width)) {
                $dialog_width = $settings->dialog_width->md;
                $dialog_width_sm = $settings->dialog_width->sm;
                $dialog_width_xs = $settings->dialog_width->xs;
            } else {
                $dialog_width = $settings->dialog_width;
                $dialog_width_sm = $settings->dialog_width_sm;
                $dialog_width_xs = $settings->dialog_width_xs;
            }
		} else {
            $dialog_width = '600';
            $dialog_width_sm = '600';
            $dialog_width_xs = '600';
		}

		if (isset($settings->dialog_height) && $settings->dialog_height) {
            if (is_object($settings->dialog_height)) {
                $dialog_height = $settings->dialog_height->md;
                $dialog_height_sm = $settings->dialog_height->sm;
                $dialog_height_xs = $settings->dialog_height->xs;
            } else {
                $dialog_height = $settings->dialog_height;
                $dialog_height_sm = $settings->dialog_height_sm;
                $dialog_height_xs = $settings->dialog_height_xs;
            }
		} else {
            $dialog_height = '400';
            $dialog_height_sm = '400';
            $dialog_height_xs = '400';
		}

		if (isset($settings->close_width) && $settings->close_width) {
            if (is_object($settings->close_width)) {
                $close_width = $settings->close_width->md;
                $close_width_sm = $settings->close_width->sm;
                $close_width_xs = $settings->close_width->xs;
            } else {
                $close_width = $settings->close_width;
                $close_width_sm = $settings->close_width_sm;
                $close_width_xs = $settings->close_width_xs;
            }
		} else {
            $close_width = '40';
            $close_width_sm = '40';
            $close_width_xs = '40';
		}

		if (isset($settings->close_height) && $settings->close_height) {
            if (is_object($settings->close_height)) {
                $close_height = $settings->close_height->md;
                $close_height_sm = $settings->close_height->sm;
                $close_height_xs = $settings->close_height->xs;
            } else {
                $close_height = $settings->close_height;
                $close_height_sm = $settings->close_height_sm;
                $close_height_xs = $settings->close_height_xs;
            }
		} else {
            $close_height = '40';
            $close_height_sm = '40';
            $close_height_xs = '40';
		}

		if (isset($settings->close_position) && $settings->close_position) {
            if (is_object($settings->close_position)) {
                $close_position = $settings->close_position->md;
                $close_position_sm = $settings->close_position->sm;
                $close_position_xs = $settings->close_position->xs;
            } else {
                $close_position = $settings->close_position;
                $close_position_sm = $settings->close_position_sm;
                $close_position_xs = $settings->close_position_xs;
            }
		} else {
            $close_position = '0 0 auto auto';
            $close_position_sm = '0 0 auto auto';
            $close_position_xs = '0 0 auto auto';
		}

        $close_position_arr = explode(" ", $close_position);
        $close_position_arr_sm = explode(" ", $close_position_sm);
        $close_position_arr_xs = explode(" ", $close_position_xs);

		$css .= $dialog_id . '{
			display: none;
			width: 100vw;
			height: 100vh;
			position: fixed;
			top: 0;
			left: 0;
			z-index: '. $dialog_level .';
		}
		' . $dialog_id . ' .mask{
			width: 100%;
			height: 100%;
			background-color: '. $mask_bg .';
		}
		' . $dialog_id . ' .dialog{
			position: absolute;
			inset: 0;
			margin: auto;
			width: ' . $dialog_width . 'px;
			height: ' . $dialog_height . 'px;
		}
		' . $dialog_id . ' .dialog-content{
			border-radius: 10px;
			overflow: hidden;
            width: 100%;
            height: 100%;
		}
		' . $dialog_id . ' .dialog img{
			width: 100%;
			height: 100%;
			object-fit: ' . $dialog_img_fill . ';
		}
		' . $dialog_id . ' .dialog .close{
			position: absolute;
			top: ' . $close_position_arr[0] . ';
			right: ' . $close_position_arr[1] . ';
            bottom: ' . $close_position_arr[2] . ';
            left: ' . $close_position_arr[3] . ';
			width: ' . $close_width . 'px;
			height: ' . $close_height . 'px;
		}
		' . $dialog_id . ' .dialog .close img{
			object-fit: ' . $close_img_fill . ';
		}
		@media (min-width: 768px) and (max-width: 991px) {
			' . $dialog_id . ' .dialog{
				width: ' . $dialog_width_sm . 'px;
                height: ' . $dialog_height_sm . 'px;
			}
            ' . $dialog_id . ' .dialog .close{
                top: ' . $close_position_arr_sm[0] . ';
                right: ' . $close_position_arr_sm[1] . ';
                bottom: ' . $close_position_arr_sm[2] . ';
                left: ' . $close_position_arr_sm[3] . ';
                width: ' . $close_width_sm . 'px;
                height: ' . $close_height_sm . 'px;
            }
		}
		@media (max-width: 767px) {
				' . $dialog_id . ' .dialog{
						width: ' . $dialog_width_xs . 'px;
						height: ' . $dialog_height_xs . 'px;
				}
				' . $dialog_id . ' .dialog .close{
						top: ' . $close_position_arr_xs[0] . ';
						right: ' . $close_position_arr_xs[1] . ';
						bottom: ' . $close_position_arr_xs[2] . ';
						left: ' . $close_position_arr_xs[3] . ';
						width: ' . $close_width_xs . 'px;
						height: ' . $close_height_xs . 'px;
				}
		}';
		// 点击按钮出弹窗结束

		return $css;
	}

	public static function getTemplate()
	{
		$output = '
		<#
			var modern_font_style = false;
			var classList = data.class;
			classList += " jwpf-btn-"+data.type;
			classList += " jwpf-btn-"+data.size;
			classList += " jwpf-btn-"+data.shape;
			if(!_.isEmpty(data.appearance)){
				classList += " jwpf-btn-"+data.appearance;
			}

			classList += " "+data.block;

			var button_fontstyle = data.fontstyle || "";
			var button_font_style = data.font_style || "";

			var button_padding = "";
			var button_padding_sm = "";
			var button_padding_xs = "";
			if(data.button_padding){
				if(_.isObject(data.button_padding)){
					if(_.trim(data.button_padding.md) !== ""){
						button_padding = _.split(data.button_padding.md, " ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}

					if(_.trim(data.button_padding.sm) !== ""){
						button_padding_sm = _.split(data.button_padding.sm, " ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}

					if(_.trim(data.button_padding.xs) !== ""){
						button_padding_xs = _.split(data.button_padding.xs, " ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}
				} else {
					if(_.trim(data.button_padding) !== ""){
						button_padding = _.split(data.button_padding, " ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}
				}

			}

			/*按钮边框*/
			var wbk_color = "";
			wbk_color = (!_.isEmpty(data.wbk_color) && data.wbk_color) ? data.wbk_color : "";
			var wbk_color_hover = "";
			wbk_color_hover = (!_.isEmpty(data.wbk_color_hover) && data.wbk_color_hover) ? data.wbk_color_hover : "";

		#>
		<style type="text/css">

			#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-{{ data.type }}{
				letter-spacing: {{ data.letterspace }};

				<# if(_.isObject(button_font_style) && button_font_style.underline) { #>
					text-decoration: underline;
					<# modern_font_style = true #>
				<# } #>

				<# if(_.isObject(button_font_style) && button_font_style.italic) { #>
					font-style: italic;
					<# modern_font_style = true #>
				<# } #>

				<# if(_.isObject(button_font_style) && button_font_style.uppercase) { #>
					text-transform: uppercase;
					<# modern_font_style = true #>
				<# } #>

				<# if(_.isObject(button_font_style) && button_font_style.weight) { #>
					font-weight: {{ button_font_style.weight }};
					<# modern_font_style = true #>
				<# } #>

				<# if(!modern_font_style) { #>
					<# if(_.isArray(button_fontstyle)) { #>
						<# if(button_fontstyle.indexOf("underline") !== -1){ #>
							text-decoration: underline;
						<# } #>
						<# if(button_fontstyle.indexOf("uppercase") !== -1){ #>
							text-transform: uppercase;
						<# } #>
						<# if(button_fontstyle.indexOf("italic") !== -1){ #>
							font-style: italic;
						<# } #>
						<# if(button_fontstyle.indexOf("lighter") !== -1){ #>
							font-weight: lighter;
						<# } else if(button_fontstyle.indexOf("normal") !== -1){#>
							font-weight: normal;
						<# } else if(button_fontstyle.indexOf("bold") !== -1){#>
							font-weight: bold;
						<# } else if(button_fontstyle.indexOf("bolder") !== -1){#>
							font-weight: bolder;
						<# } #>
					<# } #>
				<# } #>
			}

			<# if(data.type == "custom"){ #>
				#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-custom{
						<# if(_.isObject(data.fontsize)){ #>
							font-size: {{data.fontsize.md}}px;
						<# } else { #>
							font-size: {{data.fontsize}}px;
						<# } #>
					color: {{ data.color }};
					padding: {{ button_padding }};
					<# if(data.appearance == "outline"){ #>
						border-color: {{ data.background_color }};
						background-color: transparent;
					<# } else if(data.appearance == "3d"){ #>

						<# if(data.open_bgimg==1){ #>
							background:url({{data.bgimg}}) no-repeat center center/100% 100%;
						<# }else{ #>
							border-bottom-color: {{ data.background_color_hover }};
							background-color: {{ data.background_color }};
						<# } #>

					<# } else if(data.appearance == "gradient"){ #>
						border: none;
						<# if(typeof data.background_gradient.type !== "undefined" && data.background_gradient.type == "radial"){ #>
							background-image: radial-gradient(at {{ data.background_gradient.radialPos || "center center"}}, {{ data.background_gradient.color }} {{ data.background_gradient.pos || 0 }}%, {{ data.background_gradient.color2 }} {{ data.background_gradient.pos2 || 100 }}%);
						<# } else { #>
							background-image: linear-gradient({{ data.background_gradient.deg || 0}}deg, {{ data.background_gradient.color }} {{ data.background_gradient.pos || 0 }}%, {{ data.background_gradient.color2 }} {{ data.background_gradient.pos2 || 100 }}%);
						<# } #>
					<# } else { #>
						<# if(data.open_bgimg==1){ #>
							background:url({{data.bgimg}}) no-repeat center center/100% 100%;
						<# }else{ #>
							background-color: {{ data.background_color }};
						<# } #>

					<# } #>

					<# if(wbk_color != ""){ #>
						border:1px solid {{data.wbk_color}};
					<# } #>

				}

				#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-custom:hover{
					color: {{ data.color_hover }};

					<# if(data.open_bgimg==1){ #>
						background:url({{data.bgimg_hv}}) no-repeat center center/100% 100%;
					<# }else{ #>
						background-color: {{ data.background_color_hover }};
					<# } #>


					<# if(data.appearance == "outline"){ #>
						border-color: {{ data.background_color_hover }};
					<# } else if(data.appearance == "gradient"){ #>
						<# if(typeof data.background_gradient_hover.type !== "undefined" && data.background_gradient_hover.type == "radial"){ #>
							background-image: radial-gradient(at {{ data.background_gradient_hover.radialPos || "center center"}}, {{ data.background_gradient_hover.color }} {{ data.background_gradient_hover.pos || 0 }}%, {{ data.background_gradient_hover.color2 }} {{ data.background_gradient_hover.pos2 || 100 }}%);
						<# } else { #>
							background-image: linear-gradient({{ data.background_gradient_hover.deg || 0}}deg, {{ data.background_gradient_hover.color }} {{ data.background_gradient_hover.pos || 0 }}%, {{ data.background_gradient_hover.color2 }} {{ data.background_gradient_hover.pos2 || 100 }}%);
						<# } #>
					<# } #>

					<# if(wbk_color_hover != ""){ #>
						border:1px solid {{data.wbk_color_hover}};
					<# } #>
				}
				@media (min-width: 768px) and (max-width: 991px) {
					#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-custom{
						<# if(_.isObject(data.fontsize)){ #>
							font-size: {{data.fontsize.sm}}px;
						<# } #>
						padding: {{ button_padding_sm }};
					}
				}
				@media (max-width: 767px) {
					#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-custom{
						<# if(_.isObject(data.fontsize)){ #>
							font-size: {{data.fontsize.xs}}px;
						<# } #>
						padding: {{ button_padding_xs }};
					}
				}
			<# } #>
			<# if(data.type == "link"){ #>
				#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-link{
					color: {{data.link_button_color}};
					border-color: {{data.link_border_color}};
					border-width: 0 0 {{data.link_button_border_width}}px 0;
					padding: 0 0 {{data.link_button_padding_bottom}}px 0;
					text-decoration: none;
					border-radius: 0;
				}
				<# if(data.link_button_status == "hover") { #>
					#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-link:hover,
					#jwpf-addon-{{ data.id }} #btn-{{ data.id }}.jwpf-btn-link:focus{
						color: {{data.link_button_hover_color}};
						border-color: {{data.link_button_border_hover_color}};
					}
				<# } #>
			<# } #>

			#jwpf-addon-{{ data.id }} .jwpf-btn i{
				<# if(_.isObject(data.icon_margin)){ #>
					margin: {{data.icon_margin.md}};
				<# } else { #>
					margin: {{data.icon_margin}};
				<# } #>
			}
			@media (min-width: 768px) and (max-width: 991px) {
				#jwpf-addon-{{ data.id }} .jwpf-btn i{
					<# if(_.isObject(data.icon_margin)){ #>
						margin: {{data.icon_margin.sm}};
					<# } #>
				}
			}
			@media (max-width: 767px) {
				#jwpf-addon-{{ data.id }} .jwpf-btn i{
					<# if(_.isObject(data.icon_margin)){ #>
						margin: {{data.icon_margin.xs}};
					<# } #>
				}
			}

			<# if(data.kqanimg == "1"){ 
				var real_annie_height = data.annie_height;
			#>
				#jwpf-addon-{{ data.id }} .jwpf-btn{
					display: inline-flex;
					position: relative;
					align-items: center;
					height:{{data.annie_height || data.tbimg_height}}px;
					width:{{data.annie_width}}px;
					padding-left:20px;
					color:{{data.annie_color}}!important;
					<# if(data.alignment == "jwpf-text-center"){ #>
					margin: auto;
					<# } #>
				}
				#jwpf-addon-{{ data.id }} .jwpf-btn span{
					position: absolute;
				    display: flex;
				    align-items: center;
				    justify-content: center;
					right: 0px;
					<# if (real_annie_height) { #>
					top: 50%; transform: translateY(-50%);
					<# } else { #>
					top: -1px;
					<# } #>
					<# if(data.icon_margin){ #>
						margin:{{data.icon_margin.md}};
					<# } #>
					width:{{data.tbimg_width}}px;
					height:{{data.tbimg_height}}px;
				}
				#jwpf-addon-{{ data.id }} .jwpf-btn:hover {
				    color: {{data.annie_color_hover}} !important;
				}
				<# if(data.tbimg_hover) { #>
                    #jwpf-addon-{{ data.id }} .jwpf-btn .icoImgA{
                        position: absolute;
                        right: 0px;
					    top: 0px;
                        width:{{data.tbimg_width}}px;
                        height:{{data.tbimg_height}}px;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-btn .icoImgA,
                    #jwpf-addon-{{ data.id }} .jwpf-btn:hover .icoImg {
                        opacity: 0;
                        transform: scale(0);
                        transition: 0.8s;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-btn .icoImg,
                    #jwpf-addon-{{ data.id }} .jwpf-btn:hover .icoImgA {
                        opacity: 1;
                        transform: scale(1);
                        transition: 0.8s;
                    }
			    <# } #>
			<# } #>
		</style>
		<div class="{{ data.alignment }}">
			<#
			let icon_arr = (typeof data.icon !== "undefined" && data.icon) ? data.icon.split(" ") : "";
			let icon_name = data.icon;
			icon_name = icon_arr.length === 1 ? "fa "+data.icon : data.icon;
			#>

			<#
			if(data.kqanimg == 1){
			#>
				<a href="{{ data.detail_page_id }}" id="btn-{{ data.id }}" target="{{ data.target }}" class="jwpf-btn {{ classList }}">
					<span>
					    <img class="icoImg" src = "{{data.tbimg}}">
					    <# if(data.tbimg_hover) { #>
					    <img class="icoImgA" src = "{{data.tbimg_hover}}">
					    <# } #>
					</span>
					{{ data.text }}
				</a>
			<#
			}else{
			#>
				<a href="{{ data.detail_page_id }}" id="btn-{{ data.id }}" target="{{ data.target }}" class="jwpf-btn {{ classList }}"><# if(data.icon_position == "left" && !_.isEmpty(data.icon)) { #><i class="{{ icon_name }}"></i> <# } #>{{ data.text }}<# if(data.icon_position == "right" && !_.isEmpty(data.icon)) { #> <i class="{{ icon_name }}"></i><# } #></a>
			<#
			}
			#>
		</div>';

		return $output;
	}
    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
