<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonSwiper_roundarrow extends JwpagefactoryAddons
{

    public function render()
    {

        $settings = $this->addon->settings;
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $bg_image   = (isset($settings->bg_image)) ? $settings->bg_image : 'https://oss.lcweb01.cn/joomla/20220614/0b21cab17a6a618033185e35e798cdcc.jpg';
        $close_button   = (isset($settings->close_button)) ? $settings->close_button : 0;
        $phone_height   = (isset($settings->phone_height)) ? $settings->phone_height : '700';
        $phone_imgwz   = (isset($settings->phone_imgwz)) ? $settings->phone_imgwz : '26';
        $title_image   = (isset($settings->title_image)) ? $settings->title_image : 'https://oss.lcweb01.cn/joomla/20220614/206a955845191fe677953bdac6233da7.png';
        $title_color   = (isset($settings->title_color)) ? $settings->title_color : '#fff';
        $titles_color   = (isset($settings->titles_color)) ? $settings->titles_color : '#cfcfcf';
        if (isset($settings->title_fontsize) && $settings->title_fontsize) {
            if (is_object($settings->title_fontsize)) {
                $title_fontsize_md = $settings->title_fontsize->md;
                $title_fontsize_sm = $settings->title_fontsize->sm;
                $title_fontsize_xs = $settings->title_fontsize->xs;
            } else {
                $title_fontsize_md = $settings->title_fontsize;
                $title_fontsize_sm = $settings->title_fontsize_sm;
                $title_fontsize_xs = $settings->title_fontsize_xs;
            }
        } else {
            $title_fontsize_md = '52';
            $title_fontsize_sm = '32';
            $title_fontsize_xs = '32';
        }
        if (isset($settings->titles_fontsize) && $settings->titles_fontsize) {
            if (is_object($settings->titles_fontsize)) {
                $titles_fontsize_md = $settings->titles_fontsize->md;
                $titles_fontsize_sm = $settings->titles_fontsize->sm;
                $titles_fontsize_xs = $settings->titles_fontsize->xs;
            } else {
                $titles_fontsize_md = $settings->titles_fontsize;
                $titles_fontsize_sm = $settings->titles_fontsize_sm;
                $titles_fontsize_xs = $settings->titles_fontsize_xs;
            }
        } else {
            $titles_fontsize_md = '18';
            $titles_fontsize_sm = '14';
            $titles_fontsize_xs = '14';
        }

        //$output .= '<link rel="stylesheet" href="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'css/swiper.min.css">';
        $output .= '<style>';
        $output .= $addonId . ' html,body{position:relative;height:100%}';
        $output .= $addonId . ' body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}';
        $output .= $addonId . ' a{color:inherit;text-decoration:none}';
        $output .= $addonId . ' .swiper-slide{text-align:left;font-size:18px;background:none;display:-webkit-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;left: -3rem}';
        $output .= $addonId . ' .yingxiao_bg{width:100%; height:max-content;background:url('.$bg_image.') no-repeat center top;background-size:cover;overflow:hidden;}';
        $output .= $addonId . ' .yingxiao{width: 81.25%;margin:0 auto;position:relative;top:6%;padding: 3rem;}';
        $output .= $addonId . ' .yingxiao .swiper-container{ width:90%;}';
        $output .= $addonId . ' .yingxiao_con{width:42%;color:#fff;padding:0 7% 0 0;float:left}';
        $output .= $addonId . ' .yingxiao .swiper-wrapper .swiper-slide:nth-child(0){padding:5% 12% 0 3%;}';
        $output .= $addonId . ' .yingxiao_con img{width:179px;height:155px;margin: auto 0;margin-bottom: 2rem;}';
        $output .= $addonId . ' .yingxiao_con h2{font-size:'.$title_fontsize_md.'px;font-weight:bold;color:'.$title_color.';}';
        $output .= $addonId . ' .yingxiao_con p{color:'.$titles_color.';font-size:'.$titles_fontsize_md.'px;margin:7% 0 12%}';
        $output .= $addonId . ' .f_more{
            font-size:16px;width:160px;height:55px;border:solid 1px #949494;border-radius:26px;text-align:center;line-height:50px;display:block;position:relative;overflow:hidden;';
            if($close_button==1){
                $output .='display:none;';
            }
        $output .='}';
        

        $output .= $addonId . ' .f_more:before{content:"";width:0;height:100%;background:#d0111b;position:absolute;right:0;bottom:0;transition:0.5s;z-index:-1}';
        $output .= $addonId . ' .f_more:after{position:absolute;left:0;bottom:0;z-index:-2;content:"";box-sizing:border-box;width:100%;height:100%;border-radius:28px}';
        $output .= $addonId . ' .f_more:hover,.f_more.active{color:#fff;border-color:#d0111b;transition-delay:0.1s}';
        $output .= $addonId . ' .f_more:hover:before,.f_more.active:before{width:100%;right:auto;left:0}';
        $output .= $addonId . ' .f_more1{font-size:16px;width:160px;height:55px;border:solid 1px #949494;border-radius:26px;text-align:center;line-height:50px;display:block;position:relative;overflow:hidden}';
        $output .= $addonId . ' .f_more1:before{content:"";width:0;height:100%;background:#d0111b;position:absolute;right:0;bottom:0;transition:0.5s;z-index:-1}';
        $output .= $addonId . ' .f_more1:after{position:absolute;left:0;bottom:0;z-index:-2;content:"";box-sizing:border-box;width:100%;height:100%;border-radius:28px}';
        $output .= $addonId . ' .f_more1:hover,.f_more1.active{color:#fff;border-color:#d0111b;transition-delay:0.1s}';
        $output .= $addonId . ' .f_more1:hover:before,.f_more1.active:before{width:100%;right:auto;left:0}';
        $output .= $addonId . ' .yingxiao_pic{width:36.5%;animation-delay:.8s;float:right}';
        $output .= $addonId . ' .yingxiao_pic img{width:100%}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev,.yingxiao .swiper-button-next{width:65px;height:65px;opacity:.8;margin-top:-33px;top:50%}';
        $output .= $addonId . ' .yingxiao .swiper-button-next{background:none;right:0}';
        $output .= $addonId . ' .yingxiao .swiper-button-next:before{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a30.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transform:scale(0);transition:0.8s}';
        $output .= $addonId . ' .yingxiao .swiper-button-next::after{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_next.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transition:0.8s}';
        $output .= $addonId . ' .yingxiao .swiper-button-next:hover::after{transform:scale(0)}';
        $output .= $addonId . ' .yingxiao .swiper-button-next:hover::before{transform:scale(1)}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev{background:none;left:0}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev:before{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a311.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transform:scale(0);transition:0.8s}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev::after{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_prev.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transition:0.8s}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev:hover::after{transform:scale(0)}';
        $output .= $addonId . ' .yingxiao .swiper-button-prev:hover::before{transform:scale(1)}';
        $output .= ' @media only screen and (max-width:1023px){';
            $output .= $addonId . ' .swiper-wrapper {
                left: 0;
                padding: 0;
                margin: 0;
            }';
            $output .= $addonId . ' .yingxiao {
                width:100%;
                padding: 0;
                overflow: hidden;
            }';
            $output .= $addonId . ' .swiper-slide {
                height: 100%;
                text-align: center;
                font-size: 18px;
                background: none;
                display: -webkit-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                -ms-flex-pack: center;
                -ms-flex-align: center;
                align-items: center;
                left: 0;
            }';
            $output .= $addonId . ' .yingxiao_con {
                width: 100%;
                height: '.$phone_height.'px;
                float: none;
                padding: 0;
            }';
            $output .= $addonId . ' .yingxiao_con img {
                width: 125px;
                height: 103px;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                margin: auto;
            }';
            $output .= $addonId . ' .yingxiao_con h2 {
                font-size: '.$title_fontsize_xs.'px;
                font-weight: bold;
                margin: auto;
                position: absolute;
                top: 8%;
                text-align: center;
                left: 0;
                right: 0;
                color:'.$title_color.';
            }';
            $output .= $addonId . ' .yingxiao_con p {
                color: '.$titles_color.';
                font-size: '.$titles_fontsize_xs.'px;
                margin: auto;
                width: 90%;
                position: absolute;
                top: 15%;
                left: 0;
                right: 0;
                text-align: center;
                overflow:hidden;
                text-overflow:ellipsis;
                display:-webkit-box;
                -webkit-line-clamp:3;
                -webkit-box-orient:vertical;
            }';
            $output .= $addonId . ' .yingxiao_pic{
                position: absolute;
                bottom: '.$phone_imgwz.'%;
                width: 100%;
                float: none;
                left: 0;
            }';
            $output .= $addonId . ' .yingxiao_pic img {
                width: 100%;
                height: 340px;
                object-fit: contain;
            }';
            $output .= $addonId . ' .f_more {
                position: absolute;
                bottom: 10%;
                left: 0;
                right: 0;
                margin: auto;';
                if($close_button==1){
                    $output .='display:none;';
                }
            $output .='}';
            $output .= $addonId . ' .yingxiao .swiper-button-prev,
            ' . $addonId . ' .yingxiao .swiper-button-next {
                width: 34px;
                height: 34px;
                opacity: .8;
                top: 50%;
            }';
            $output .= $addonId . ' .yingxiao .swiper-button-prev {
                left: 10px !important;
            }';
            $output .= $addonId . ' .yingxiao .swiper-button-prev::after,
            ' . $addonId . ' .yingxiao .swiper-button-prev:before {
                background-size: 34px 34px;
                left: 0px !important;
            }';
            $output .= $addonId . ' .yingxiao .swiper-button-next {
                right: 10px !important;
            }';
            $output .= $addonId . ' .yingxiao .swiper-button-next::after,
            ' . $addonId . ' .yingxiao .swiper-button-next::before {
                background-size: 34px 34px;
                right: 0px !important;
            }';
        $output .= '}';
        $output .= '</style>';
        $output .= '<div class="yingxiao_bg"> ';
        $output .= ' <!-- Swiper -->';
        $output .= ' <div class="swiper yingxiao">';
        $output .= ' <ul class="swiper-wrapper" >';

        foreach ($settings->jw_roundarrow_tab_item as $key => $jwrtitem) {
            $output .= '<li class="swiper-slide" data-swiper-slide-index="0">';
            $output .= '<div class="yingxiao_con"> <img src="'.$title_image.'">';
            $output .= '<h2>' . $jwrtitem->title . '</h2>';
            $output .= '<p>' . $jwrtitem->title_ss . '</p>';
            if ($jwrtitem->tz_page_type == 'Internal_pages') {
                $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                $arrray = explode('&', $idurl);
                foreach ($arrray as $key => $value) {
                    if (substr($value, 0, 3) == 'id=') {
                        $arrray[$key] = 'id=' . base64_encode($jwrtitem->detail_page_id);
                    }
                }

                $return = implode('&', $arrray);
            } else {
                $return = $jwrtitem->detail_page;
            }
            $output .= '<a href="' . $return . '" class="f_more">进一步了解</a> </div>';
            
            $output .= '<div class="yingxiao_pic"><img src="' . $jwrtitem->bg_img . '"></div>';
            $output .= '</li>';
        }
        //  $output .= '<li class="swiper-slide" data-swiper-slide-index="0">';
        // $output .= '<div class="yingxiao_con"> <img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_bd.png">';
        // $output .= '<h2>搜索推广</h2>';
        // $output .= '<p>根据客户主动搜索，展示您的推广内容，精准定位，性价比高。</p>';
        // $output .= '<a href="#" class="f_more">进一步了解</a> </div>';
        // $output .= '<div class="yingxiao_pic"><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a8f82fd3c829e88dec8012b1b72aabd1.png"></div>';
        // $output .= '</li>';
        // $output .= '<li class="swiper-slide" data-swiper-slide-index="1">';
        // $output .= '<div class="yingxiao_con"> <img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_bd.png">';
        // $output .= '<h2>信息流推广</h2>';
        // $output .= '<p>将您的推广信息自然融入在各类资讯、信息中，易传播，易操作。</p>';
        // $output .= '<a href="#" class="f_more">进一步了解</a> </div>';
        // $output .= '<div class="yingxiao_pic"><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/5f316af2f2f8065cbc70a8ae6b2dd4e1.png"></div>';
        // $output .= '</li>';
        // $output .= '<li class="swiper-slide" data-swiper-slide-index="2">';
        // $output .= '<div class="yingxiao_con"> <img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_bd.png">';
        // $output .= '<h2>开屏推广</h2>';
        // $output .= '<p>百度开屏矩阵，是以围绕百度APP搭建的百度系开屏产品矩阵。</p>';
        // $output .= '<a href="/poly" class="f_more">进一步了解</a> </div>';
        // $output .= '<div class="yingxiao_pic"><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/355807c23967f1649abbda99f2b3dfff.png"></div>';
        // $output .= '</li>';
        // $output .= '<li class="swiper-slide swiper-slide-prev" data-swiper-slide-index="3">';
        // $output .= '<div class="yingxiao_con"> <img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_bd.png">';
        // $output .= '<h2>聚屏推广</h2>';
        // $output .= '<p>整合优质品牌广告流量，以开屏广告样式强势品牌曝光。搭建百度系开屏产品矩阵，整合百度优质广告资源与流量，以APP开屏广告的样式，强势品牌曝光。</p>';
        // $output .= '<a href="/poly" class="f_more">进一步了解</a> </div>';
        // $output .= '<div class="yingxiao_pic"><img  src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/c890e2b6552eac5339a932b4d2ef42f3.png"></div>';
        // $output .= '</li>';
        // $output .= '<li class="swiper-slide " data-swiper-slide-index="4">';
        // $output .= '<div class="yingxiao_con"> <img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_bd.png">';
        // $output .= '<h2>百青藤推广</h2>';
        // $output .= '<p>百度系+联盟资源，碎片化场景全面覆盖用户，为您拓展更多商机。</p>';
        // $output .= '<a href="/ivy" class="f_more">进一步了解</a> </div>';
        // $output .= '<div class="yingxiao_pic"><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/e4ceaceaf7d82d2cdc41aaaf66d16099.png"></div>';
        // $output .= '</li>';
        $output .= '</ul>';
        $output .= '<div class="swiper-button-next"></div>';
        $output .= '<div class="swiper-button-prev"></div>';
        $output .= '</div>';
        $output .= '</div>';
        //$output .= '<script src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'js/swiper.min.js"></script> ';
        $output .= '<script>';
            $output .= 'var swiper = new Swiper("'. $addonId . ' .swiper", {
                spaceBetween: 150,
                navigation: {
                    nextEl: "'. $addonId . ' .swiper-button-next",
                    prevEl:  "'. $addonId . ' .swiper-button-prev",
                },
                breakpoints: {
                    0: {
                        spaceBetween: 0,
                    },
                    1023: {
                        spaceBetween: 150,
                    },
                },
                loop: true,
            });';
        $output .= '</script>';

        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function css()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $css='';
        return $css;
    }

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $js = '';
        return $js;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item;
        // 是否开启切换按钮
        var is_swiper_button = data.is_swiper_button || 0;
        // 上翻页按钮
        var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png";
        // 下翻页按钮
        var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png";
        // 切换按钮宽度
        var swiper_button_width = data.swiper_button_width || 24;
        // 切换按钮高度
        var swiper_button_height = data.swiper_button_height || 24;
        // 切换按钮上边距（百分比）
        var swiper_button_top = data.swiper_button_top || 48;
        // 切换按钮两侧边距（px）
        var swiper_button_left = data.swiper_button_left || 10;
        // 是否开启轮播点
        var is_swiper_pagination = data.is_swiper_pagination || 0;
        // 轮播点宽度
        var swiper_p_width = data.swiper_p_width || 8;
        // 轮播点高度
        var swiper_p_height = data.swiper_p_height || 8;
        // 轮播点间距
        var swiper_p_margin = data.swiper_p_margin || 5;
        // 轮播点颜色
        var swiper_p_color = data.swiper_p_color || "#f0f0f0";
        // 选中轮播点宽度
        var swiper_p_width_a = data.swiper_p_width_a;
        // 选中轮播点高度
        var swiper_p_height_a = data.swiper_p_height_a;
        // 选中轮播点颜色
        var swiper_p_color_a = data.swiper_p_color_a || "#007aff";

        var title_color   = data.title_color || "#fff";
        var titles_color   = data.titles_color || "#cfcfcf";
        
        #>

        <style>
            {{addonId}} html,body{position:relative;height:100%}
            {{addonId}} body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}
            {{addonId}} a{color:inherit;text-decoration:none}
            {{addonId}} .swiper-slide{text-align:left;font-size:18px;background:none;display:-webkit-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;left: -3rem}
            {{addonId}} .yingxiao_bg{width:100%; height:max-content;background:url({{data.bg_image}}) no-repeat center top;background-size:cover;overflow:hidden;}
            {{addonId}} .yingxiao{width: 94%;margin:0 auto;position:relative;top:6%;padding: 3rem;}
            {{addonId}} .yingxiao .swiper-container{ width:90%;}
            {{addonId}} .yingxiao_con{width:42%;color:#fff;padding:0 7% 0 0;float:left}
            {{addonId}} .yingxiao .swiper-wrapper .swiper-slide:nth-child(0){padding:5% 12% 0 3%;}
            {{addonId}} .yingxiao_con img{width:179px;height:155px;margin: auto 0;margin-bottom: 2rem;}
            {{addonId}} .yingxiao_con h2{

                <# if(_.isObject(data.title_fontsize)){ #>
                    font-size: {{ data.title_fontsize.md }}px;
                <# } else { #>
                    font-size: {{ data.title_fontsize }}px;
                <# } #>

                font-weight:bold;color:{{title_color}};
            }
            {{addonId}} .yingxiao_con p{
                color:{{titles_color}};

                <# if(_.isObject(data.titles_fontsize)){ #>
                    font-size: {{ data.titles_fontsize.md }}px;
                <# } else { #>
                    font-size: {{ data.titles_fontsize }}px;
                <# } #>

                margin:7% 0 12%;
            }
            {{addonId}} .f_more{font-size:16px;width:160px;height:55px;border:solid 1px #949494;border-radius:26px;text-align:center;line-height:50px;display:block;position:relative;overflow:hidden;
                <# if(data.close_button==1){ #>
                    display:none;
                <# } #>
            }
            {{addonId}} .f_more:before{content:"";width:0;height:100%;background:#d0111b;position:absolute;right:0;bottom:0;transition:0.5s;z-index:-1}
            {{addonId}} .f_more:after{position:absolute;left:0;bottom:0;z-index:-2;content:"";box-sizing:border-box;width:100%;height:100%;border-radius:28px}
            {{addonId}} .f_more:hover,.f_more.active{color:#fff;border-color:#d0111b;transition-delay:0.1s}
            {{addonId}} .f_more:hover:before,.f_more.active:before{width:100%;right:auto;left:0}
            {{addonId}} .f_more1{font-size:16px;width:160px;height:55px;border:solid 1px #949494;border-radius:26px;text-align:center;line-height:50px;display:block;position:relative;overflow:hidden}
            {{addonId}} .f_more1:before{content:"";width:0;height:100%;background:#d0111b;position:absolute;right:0;bottom:0;transition:0.5s;z-index:-1}
            {{addonId}} .f_more1:after{position:absolute;left:0;bottom:0;z-index:-2;content:"";box-sizing:border-box;width:100%;height:100%;border-radius:28px}
            {{addonId}} .f_more1:hover,.f_more1.active{color:#fff;border-color:#d0111b;transition-delay:0.1s}
            {{addonId}} .f_more1:hover:before,.f_more1.active:before{width:100%;right:auto;left:0}
            {{addonId}} .yingxiao_pic{width:36.5%;animation-delay:.8s;float:right}
            {{addonId}} .yingxiao_pic img{width:100%}
            {{addonId}} .yingxiao .swiper-button-prev,.yingxiao .swiper-button-next{width:65px;height:65px;opacity:.8;margin-top:-33px;top:50%}
            {{addonId}} .yingxiao .swiper-button-next{background:none;right:0}
            {{addonId}} .yingxiao .swiper-button-next:before{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a30.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transform:scale(0);transition:0.8s}
            {{addonId}} .yingxiao .swiper-button-next::after{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_next.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transition:0.8s}
            {{addonId}} .yingxiao .swiper-button-next:hover::after{transform:scale(0)}
            {{addonId}} .yingxiao .swiper-button-next:hover::before{transform:scale(1)}
            {{addonId}} .yingxiao .swiper-button-prev{background:none;left:0}
            {{addonId}} .yingxiao .swiper-button-prev:before{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a311.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transform:scale(0);transition:0.8s}
            {{addonId}} .yingxiao .swiper-button-prev::after{width:100%;height:100%;background:url('.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/yingxiao_prev.png) no-repeat;display:block;content:"";left:0;top:0;content:"";position:absolute;transition:0.8s}
            {{addonId}} .yingxiao .swiper-button-prev:hover::after{transform:scale(0)}
            {{addonId}} .yingxiao .swiper-button-prev:hover::before{transform:scale(1)}
            @media only screen and (max-width:1023px){
                {{addonId}} .swiper-wrapper {
                    left: 0;
                    padding: 0;
                    margin: 0;
                }
                {{addonId}} .yingxiao {
                    width:100%;
                    padding: 0;
                    overflow: hidden;
                }
                {{addonId}} .swiper-slide {
                    height: 100%;
                    text-align: center;
                    font-size: 18px;
                    background: none;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: -webkit-flex;
                    -ms-flex-pack: center;
                    -ms-flex-align: center;
                    align-items: center;
                    left: 0;
                }
                {{addonId}} .yingxiao_con {
                    width: 100%;
                    height: {{data.phone_height}}px;
                    float: none;
                    padding: 0;
                }
                {{addonId}} .yingxiao_con img {
                    width: 125px;
                    height: 103px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                }
                {{addonId}} .yingxiao_con h2 {
      
                    <# if(_.isObject(data.title_fontsize)){ #>
                        font-size: {{ data.title_fontsize.xs }}px;
                    <# } else { #>
                        font-size: {{ data.title_fontsize }}px;
                    <# } #>

                    font-weight: bold;
                    margin: auto;
                    position: absolute;
                    top: 8%;
                    text-align: center;
                    left: 0;
                    right: 0;
                }
                {{addonId}} .yingxiao_con p {
                    color: {{titles_color}};
                    <# if(_.isObject(data.titles_fontsize)){ #>
                        font-size: {{ data.titles_fontsize.xs }}px;
                    <# } else { #>
                        font-size: {{ data.titles_fontsize }}px;
                    <# } #>

                    margin: auto;
                    width: 90%;
                    position: absolute;
                    top: 15%;
                    left: 0;
                    right: 0;
                    text-align: center;
                    overflow:hidden;
                    text-overflow:ellipsis;
                    display:-webkit-box;
                    -webkit-line-clamp:3;
                    -webkit-box-orient:vertical;
                }
                {{addonId}} .yingxiao_pic{
                    position: absolute;
                    bottom: {{data.phone_imgwz}}%;
                    width: 100%;
                    float: none;
                    left: 0;
                }
                {{addonId}} .yingxiao_pic img {
                    width: 100%;
                    height: 340px;
                    object-fit: contain;
                }
                {{addonId}} .f_more {
                    position: absolute;
                    bottom: 10%;
                    left: 0;
                    right: 0;
                    margin: auto;
                    <# if(data.close_button==1){ #>
                        display:none;
                    <# } #>
                }
                {{addonId}} .yingxiao .swiper-button-prev,
                {{addonId}} .yingxiao .swiper-button-next {
                    width: 34px;
                    height: 34px;
                    opacity: .8;
                    top: 50%;
                }
                {{addonId}} .yingxiao .swiper-button-prev {
                    left: 10px !important;
                }
                {{addonId}} .yingxiao .swiper-button-prev::after,
                {{addonId}} .yingxiao .swiper-button-prev:before {
                    background-size: 34px 34px;
                    left: 0px !important;
                }
                {{addonId}} .yingxiao .swiper-button-next {
                    right: 10px !important;
                }
                {{addonId}} .yingxiao .swiper-button-next::after,
                {{addonId}} .yingxiao .swiper-button-next::before {
                    background-size: 34px 34px;
                    right: 0px !important;
                }
        }
        </style>


        <div class="yingxiao_bg"> 
            <div class="swiper yingxiao">
                <ul class="swiper-wrapper" >

                <# _.each(data.jw_roundarrow_tab_item, function (jwrtitem, key){ 
                    #>
                    <li class="swiper-slide" data-swiper-slide-index="0">
                    <div class="yingxiao_con"> <img src=\'{{data.title_image}}\'">
                    <h2>{{jwrtitem.title}}</h2>
                    <p>{{jwrtitem.title_ss}}</p>
                    <a href="{{jwrtitem.a_url}}" class="f_more">进一步了解</a> </div>
                    <div class="yingxiao_pic"><img src=\'{{jwrtitem.bg_img}}\'></div>
                    </li>
                <# }) #>
                
                </ul>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>


        ';

        return $output;
    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

}
