<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonSearch_box_pro extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id     = $_GET['company_id'] ?? 0;
        $site_id        = $_GET['site_id'] ?? 0;
        $layout_id      = $_GET['layout_id'] ?? 0;
        $settings       = $this->addon->settings;
        $addon_id       = '#jwpf-addon-' . $this->addon->id;
        $config         = new JConfig();
        $search_type    = (isset($settings->search_type)) ? $settings->search_type : 'type1';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $is_text_button = (isset($settings->is_text_button)) ? $settings->is_text_button : 0;
        $is_input_img   = (isset($settings->is_input_img)) ? $settings->is_input_img : 0;
        $search_style_type   = (isset($settings->search_style_type)) ? $settings->search_style_type : 'type1';

        if ($config->jzt_url == 'http://' . $_SERVER['HTTP_HOST']) {
            $urlpath = $config->jzt_url;
        } else {
            $yuming=$_SERVER['HTTP_HOST'];
            if($yuming=='jzt_dev_2.china9.cn'){
                $urlpath='http://jzt_dev_2.china9.cn';
            }else{
              $urlpath = $config->ijzt_url;
            }
        }
        $thisUrl1 = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';
        $thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id . '&';
        $postUrl = '/html/' . base64_encode($detail_page_id) . '.html?site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id . '&';
                
        // 指定文章详情页ID
        $output = '';
        $output .= '<style>
                        ' . $addon_id . ' .search_box{
                          border: 1px solid;
                          outline-color: transparent;
                          border-color: ' . $settings->border_color . ';
                          width:' . $settings->input_width . 'px;
                          background:' . $settings->input_bg . ';
                          color:' . $settings->input_color . ';
                          padding-left: ' . $settings->input_padding_left . 'px !important;
                        }' . $addon_id . ' .search_box::-webkit-input-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                       ' . $addon_id . ' .search_box::-moz-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                         ' . $addon_id . ' .search_box::-ms-input-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                        ' . $addon_id . ' .search_btn{
                          padding: 0 10px;
                          border: 1px solid ' . $settings->button_border_color . ';
                          cursor: pointer;
                          outline-color: transparent;
                          background:' . $settings->button_color . ';
                          color:' . $settings->button_font_color . ';'.
                          
                     'background-image: url('.$settings->search_button_gb_img.');/*设置图片显示的宽1*/
                                        background-repeat: no-repeat;
                                        background-size:100% 100%;}'.
                      $addon_id . ' .icon{
                          position: absolute;
                          z-index:5;
                          background-image: url(' . $settings->search_input_gb_img . ');
                          background-repeat: no-repeat; /*设置图片不重复*/
                          background-position:' . $settings->search_input_position_width . 'px ' . $settings->search_input_position_height . 'px; /*图片显示的位置*/
                          background-size: 100% 100%;
                          width:' . $settings->search_input_width . 'px; /*设置图片显示的宽*/
                          height: ' . $settings->search_input_height . 'px; /*图片显示的高*/

                          }' .
            '</style>';
        $output .= '<div style="display:flex;justify-content: ' . $settings->search_wz . ';height:' . $settings->input_height . 'px ">';

        if ($is_input_img) {
          if ($settings->search_wz=='flex-start'){
            $output .= '<i class="icon" style="left:0;"></i>';
          }elseif($settings->search_wz=='center'){
            $output .= '<i class="icon" style="left:40.72%;"></i>';
          }elseif($settings->search_wz=='flex-end'){
            $rightseix=intval($settings->input_width)+intval($settings->search_input_width);
             $output .= '<i class="icon" style="right:'.$rightseix.'px;"></i>';
          }
        }
        $output .= '<input placeholder="' . $settings->search_input_text . '" style="width:' . $settings->input_width . 'px;border-radius:' . $settings->search_border_radius . 'px;" class="search_box" value="" onkeydown="ev' . $this->addon->id . '()" name="key" id="key">';
        $output .= '<input class="subBuootm" type="hidden" value="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0", $absolute = true) . '&site_id=' . $site_id . '">';
        $output .= '<a style="width:' . $settings->button_width . 'px;height:' . $settings->button_height . 'px;border-radius:' . $settings->button_border_radius . 'px;margin-left:' . $settings->search_button_num . 'px;line-height:' . $settings->button_height . 'px;" href="'.$thisUrl1.'" class="search_btn tzd">' . $settings->search_button_text . '</a>';
        $output .= "</div>";
        //        搜索详情页
        if ($search_type == 'type1') {
            $output .= '<script>
                            var hostname =\'https://\'+ window.location.hostname;
                            var hostnames =\'https://\'+ window.location.hostname;
                            var jzt_url = "' . $config->jzt_url . '";
                            var ijzt_url = "' . $config->ijzt_url . '";
                            
                            $("' . $addon_id . ' .tzd").click(function(){
                                if($("' . $addon_id . ' #key").val() == "")
                                {
                                    alert("请输入搜索关键字");
                                    $("' . $addon_id . ' #key").focus();
                                    return false;
                                }
                            })
            
                            $("' . $addon_id . ' .search_box").bind("input propertychange change",function(){
                                var aa=$("' . $addon_id . ' #key").val();
                                a = encodeURI(encodeURI(aa));
                                if(a){
                                    if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                        var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                                    } else {
                                        var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                                    }
                                    $("' . $addon_id . ' .tzd").attr("href",ff);
                                }
                            })
                            
                            function ev' . $this->addon->id . '()
                            {
                                var event = window.event || arguments.callee.caller.arguments[0];
                                if (event.keyCode == 13)
                                {
                                    var search_na=jQuery("' . $addon_id . ' #key").val();
                                    search_name = encodeURI(encodeURI(search_na));
                                    if(search_name==""){
                                        alert("请输入搜索内容")
                                    }else{ 

                                        if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                            window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                                        } else {
                                            window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                                        }
                                      
                                    }
                                }
                            }
                           
                    </script>';
        }
        //        咨询
        if ($search_type == 'type2') {
            $output .= '<script>
                              jQuery("' . $addon_id . ' .search_btn").click(function(){
                                    var search_name=jQuery("' . $addon_id . ' .search_box").val()
                                    let myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
                                    var tel = /^0\d{2,3}-?\d{7,8}$/;
                                    var emailreg = new RegExp("^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"); //正则表达式

                              if (myreg.test(search_name) || tel.test(search_name) || emailreg.test(search_name)) {
                               jQuery.ajax({
                                    type: "POST",
                                    url: "' . $urlpath . '/api/Consult/create",
                                    dataType: "json",
                                    data: {
                                        mobile: search_name,
                                        "company_id" :"' . $company_id . '",
                                        "site_id" :"' . $site_id . '"
                                    },
                                    success: function (e) {
                                      alert("提交成功")
                                    }
                                });
                               }else{
                                alert("格式不正确");
                              }
                            });
                        </script>';
        }
        if ($search_style_type == 'type2') {
          $search_button_img   = (isset($settings->search_button_img)) ? $settings->search_button_img : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png';
          $search_input_text   = (isset($settings->search_input_text)) ? $settings->search_input_text : '填写搜索的内容';
          $input_bg   = (isset($settings->input_bg)) ? $settings->input_bg : '#ccc';
          $input_color_pla   = (isset($settings->input_color_pla)) ? $settings->input_color_pla : '#bbbbbb';
          $input_color   = (isset($settings->input_color)) ? $settings->input_color : '#bbbbbb';
          $button_width   = (isset($settings->button_width)) ? $settings->button_width : 60;
          $button_height   = (isset($settings->button_height)) ? $settings->button_height : 36;
          $input_width   = (isset($settings->input_width)) ? $settings->input_width : 150;
          $input_height   = (isset($settings->input_height)) ? $settings->input_height : 36;
          $search_border_radius   = (isset($settings->search_border_radius)) ? $settings->search_border_radius : 40;
          $input_font_size   = (isset($settings->input_font_size)) ? $settings->input_font_size : 14;
          $input_font_size_hla   = (isset($settings->input_font_size_hla)) ? $settings->input_font_size_hla : 14;
          $output = '<style>
              '.$addon_id.' .box_serch{
                  display:flex;
              }
              '.$addon_id.' .input_serch {
                  width: '.$input_width.'px;
                  height: '.$input_height.'px;
                  background: '.$input_bg.';
                  border-left: 0px solid #B3B3B3;
                  border-top: 0px solid #B3B3B3;
                  border-bottom: 0px solid #B3B3B3;
                  border-right: none;
                  border-radius: '.$search_border_radius.'px 0px 0px '.$search_border_radius.'px;
                  padding-left: 25px;
                  color:'.$input_color.';
                  font-size:'.$input_font_size.'px;
              }
              '.$addon_id.' input:focus { outline: none; }
              '.$addon_id.' .input_serch::-webkit-input-placeholder{
                  color: '.$input_color_pla.';
                  font-size: '.$input_font_size_hla.'px;
                  line-height: normal;
                  
              }
              '.$addon_id.' .input_submit {
                  width: '.$button_width.'px;
                  height: '.$button_height.'px;
                  background-color: '.$input_bg.';
                  background-image: url('.$search_button_img.');
                  background-repeat: no-repeat;
                  background-position-x: center;
                  background-position-y: center;
                  border-right: 0px solid #B3B3B3;
                  border-top: 0px solid #B3B3B3;
                  border-bottom: 0px solid #B3B3B3;
                  border-left: none;
                  border-radius: 0px '.$search_border_radius.'px '.$search_border_radius.'px 0px;
              }
          </style>';
          $output .='
          <div class="box_serch">
              <input class="input_serch" placeholder="'.$search_input_text.'" type="text" onkeydown="ev' . $this->addon->id . '()" name="key" id="key">
              <a class="input_submit search_btn tzd"  href="'.$thisUrl1.'"></a>
          </div>
          ';
          $output .= '<script>
            var hostname =\'https://\'+ window.location.hostname;
            var hostnames =\'https://\'+ window.location.hostname;
            var jzt_url = "' . $config->jzt_url . '";
            var ijzt_url = "' . $config->ijzt_url . '";
            
            $("' . $addon_id . ' .tzd").click(function(){
                if($("#key").val() == "")
                {
                    alert("请输入搜索关键字");
                    $("#key").focus();
                    return false;
                }
            })

            $("' . $addon_id . ' .input_serch").bind("input propertychange change",function(){
                var aa=$("' . $addon_id . ' #key").val();
                a = encodeURI(encodeURI(aa));
                if(a){
                  if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                    var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                  } else {
                    var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                  }
                    $(".tzd").attr("href",ff);
                }
            })
            
            function ev' . $this->addon->id . '()
            {
                var event = window.event || arguments.callee.caller.arguments[0];
                if (event.keyCode == 13)
                {
                    var search_na=jQuery("' . $addon_id . ' #key").val();
                    search_name = encodeURI(encodeURI(search_na));
                    if(search_name==""){
                        alert("请输入搜索内容")
                    }else{ 

                        if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                            window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                        } else {
                            window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                        }
                      
                    }
                }
            }

            </script>';
        }
		if($search_style_type == 'type3' && $search_type == 'type1') {
			/* 整体配置 */
			// 搜索结果页跳转方式
			$search_target04 = isset($settings->search_target04) ? $settings->search_target04 : '_self';
			// 搜索框位置
			$search_align04 = isset($settings->search_align04) ? $settings->search_align04 : 'flex-start';
			// 整体宽度 search_width04
			$search_width04_md = isset($settings->search_width04) ? $settings->search_width04 : 458;
			$search_width04_sm = isset($settings->search_width04_sm) ? $settings->search_width04_sm : '';
			$search_width04_xs = isset($settings->search_width04_xs) ? $settings->search_width04_xs : '';
			// 整体高度
			$search_height04_md = isset($settings->search_height04) ? $settings->search_height04 : 40;
			$search_height04_sm = isset($settings->search_height04_sm) ? $settings->search_height04_sm : '';
			$search_height04_xs = isset($settings->search_height04_xs) ? $settings->search_height04_xs : '';
			// 背景颜色
			$search_bgColor04 = isset($settings->search_bgColor04) ? $settings->search_bgColor04 : '#FFFFFF';
			// 边框宽度
			$search_border_width04_md = isset($settings->search_border_width04) ? $settings->search_border_width04 : 1;
			$search_border_width04_sm = isset($settings->search_border_width04_sm) ? $settings->search_border_width04_sm : '';
			$search_border_width04_xs = isset($settings->search_border_width04_xs) ? $settings->search_border_width04_xs : '';
			// 边框颜色
			$search_border_color04 = isset($settings->search_border_color04) ? $settings->search_border_color04 : '#588AAD';
			// 边框样式
			$search_border_style04 = isset($settings->search_border_style04) ? $settings->search_border_style04 : 'solid';
			// 边框圆角
			$search_border_radius04_md = isset($settings->search_border_radius04) ? $settings->search_border_radius04 : 1;
			$search_border_radius04_sm = isset($settings->search_border_radius04_sm) ? $settings->search_border_radius04_sm : '';
			$search_border_radius04_xs = isset($settings->search_border_radius04_xs) ? $settings->search_border_radius04_xs : '';
			
			/* 图标部分 */
			// 是否显示左侧图标部分
			$is_search_icon04 = isset($settings->is_search_icon04) ? $settings->is_search_icon04 : 1;
			// 左侧搜索图标
			$search_icon04 = isset($settings->search_icon04) ? $settings->search_icon04 : 'https://oss.lcweb01.cn/joomla/20230515/6463e2733c5489861bcd6e9c987f8ef1.png';
			// 左侧宽度
			$icon_width04_md = isset($settings->icon_width04) ? $settings->icon_width04 : 50;
			$icon_width04_sm = isset($settings->icon_width04_sm) ? $settings->icon_width04_sm : '';
			$icon_width04_xs = isset($settings->icon_width04_xs) ? $settings->icon_width04_xs : '';
			// 左侧高度
			$icon_height04_md = isset($settings->icon_height04) ? $settings->icon_height04 : 20;
			$icon_height04_sm = isset($settings->icon_height04_sm) ? $settings->icon_height04_sm : '';
			$icon_height04_xs = isset($settings->icon_height04_xs) ? $settings->icon_height04_xs : '';
			// 竖线宽度
			$icon_border_width04_md = isset($settings->icon_border_width04) ? $settings->icon_border_width04 : 1;
			$icon_border_width04_sm = isset($settings->icon_border_width04_sm) ? $settings->icon_border_width04_sm : '';
			$icon_border_width04_xs = isset($settings->icon_border_width04_xs) ? $settings->icon_border_width04_xs : '';
			// 竖线颜色
			$icon_border_color04 = isset($settings->icon_border_color04) ? $settings->icon_border_color04 : '#D0D0D0';
			/* 输入框部分 */
			// 输入框提示文字
			$input_text04 = isset($settings->input_text04) ? $settings->input_text04 : '';
			// 输入框文字大小
			$input_fontsize04_md = isset($settings->input_fontsize04) ? $settings->input_fontsize04 : 14;
			$input_fontsize04_sm = isset($settings->input_fontsize04_sm) ? $settings->input_fontsize04_sm : '';
			$input_fontsize04_xs = isset($settings->input_fontsize04_xs) ? $settings->input_fontsize04_xs : '';
			// 输入框文字颜色
			$input_color04 = isset($settings->input_color04) ? $settings->input_color04 : '#202020';
			/* 搜索按钮 */
			// 按钮文字
			$button_text04 = isset($settings->button_text04) ? $settings->button_text04 : '';
			// 按钮宽度
			$button_width04_md = isset($settings->button_width04) ? $settings->button_width04 : 80;
			$button_width04_sm = isset($settings->button_width04_sm) ? $settings->button_width04_sm : '';
			$button_width04_xs = isset($settings->button_width04_xs) ? $settings->button_width04_xs : '';
			// 按钮背景颜色
			$button_bgColor04 = isset($settings->button_bgColor04) ? $settings->button_bgColor04 : '#0B5EA0';
			// 按钮文字颜色
			$button_color04 = isset($settings->button_color04) ? $settings->button_color04 : '#FFFFFF';
			// 按钮文字大小
			$button_fontsize04_md = isset($settings->button_fontsize04) ? $settings->button_fontsize04 : 14;
			$button_fontsize04_sm = isset($settings->button_fontsize04_sm) ? $settings->button_fontsize04_sm : '';
			$button_fontsize04_xs = isset($settings->button_fontsize04_xs) ? $settings->button_fontsize04_xs : '';

			$output = "
				<style>
					{$addon_id} .search04 {
						display: flex;
						justify-content: {$search_align04};
					}
					/* 搜索框整体 */
					{$addon_id} .search04 .search-box {
						display: flex;
						align-items: center;
						width: {$search_width04_md}px;
						height: {$search_height04_md}px;
						background: {$search_bgColor04};
						border-radius: {$search_border_radius04_md};
						border: {$search_border_width04_md}px {$search_border_style04} {$search_border_color04};
						overflow: hidden;
					}
					/* 左侧图标 */
					{$addon_id} .search04 .search-box .search-icon-box {
						width: {$icon_width04_md}px;
						height: {$icon_height04_md}px;
						display: flex;
						align-items: center;
						justify-content: center;
						border-right: {$icon_border_width04_md}px solid {$icon_border_color04};
					}
					{$addon_id} .search04 .search-box .search-icon-box img {
						width: 100%;
						height: 100%;
						object-fit: scale-down;
					}
					/* 搜索框 */
					{$addon_id} .search04 .search-box .search {
						flex: 1;
						height: {$icon_height04_md}px;
						line-height: {$icon_height04_md}px;
						padding: 0 15px;
						box-sizing: border-box;
						font-size: {$input_fontsize04_md}px;
						color: {$input_color04};
						border: none;
						box-shadow: none;
						background: none;
						font-family: \"微软雅黑\";
					}
					{$addon_id} .search04 .search-box .search:focus {
						outline: none;
						border: none;
						box-shadow: none;
					}
					/* 搜索按钮 */
					{$addon_id} .search04 .search-box .search-btn {
						width: {$button_width04_md}px;
						height: 100%;
						line-height: " . ($search_height04_md - $search_border_width04_md * 2) . "px;
						font-size: {$button_fontsize04_md}px;
						text-align: center;
						color: {$button_color04};
						background: {$button_bgColor04};
						cursor: pointer;
					}
					@media (min-width: 768px) and (max-width: 991px) {
						/* 搜索框整体 */
						{$addon_id} .search04 .search-box {
							width: {$search_width04_sm}px;
							height: {$search_height04_sm}px;
							border-radius: {$search_border_radius04_sm};
							border-width: {$search_border_width04_sm}px;
						}
						/* 左侧图标 */
						{$addon_id} .search04 .search-box .search-icon-box {
							width: {$icon_width04_sm}px;
							height: {$icon_height04_sm}px;
							border-width: {$icon_border_width04_sm}px;
						}
						/* 搜索框 */
						{$addon_id} .search04 .search-box .search {
							height: {$icon_height04_sm}px;
							line-height: {$icon_height04_sm}px;
							font-size: {$input_fontsize04_sm}px;
						}
						/* 搜索按钮 */
						{$addon_id} .search04 .search-box .search-btn {
							width: {$button_width04_sm}px;
							line-height: " . ($search_height04_sm - $search_border_width04_sm * 2) . "px;
							font-size: {$button_fontsize04_sm}px;
						}
					}
					@media (max-width: 767px) {
						/* 搜索框整体 */
						{$addon_id} .search04 .search-box {
							width: {$search_width04_xs}px;
							height: {$search_height04_xs}px;
							border-radius: {$search_border_radius04_xs};
							border-width: {$search_border_width04_xs}px;
						}
						/* 左侧图标 */
						{$addon_id} .search04 .search-box .search-icon-box {
							width: {$icon_width04_xs}px;
							height: {$icon_height04_xs}px;
							border-width: {$icon_border_width04_xs}px;
						}
						/* 搜索框 */
						{$addon_id} .search04 .search-box .search {
							height: {$icon_height04_xs}px;
							line-height: {$icon_height04_xs}px;
							font-size: {$input_fontsize04_xs}px;
						}
						/* 搜索按钮 */
						{$addon_id} .search04 .search-box .search-btn {
							width: {$button_width04_xs}px;
							line-height: " . ($search_height04_xs - $search_border_width04_xs * 2) . "px;
							font-size: {$button_fontsize04_xs}px;
						}
					}
				</style>
			";
			$output .= '
				<div class="search04">
					<div class="search-box">';
						if($is_search_icon04 == 1) {
							$output .= '
							<div class="search-icon-box">
								<img src="' . $search_icon04 . '" alt="" />
							</div>';
						}
						$output .= '
						<input placeholder="' . $input_text04 . '" class="search srch_txt" value="" onkeydown="ev' . $this->addon->id . '()" name="key" id="key" >
                        <a class="search-btn tzd" href="'.$thisUrl1.'">' . $button_text04 . '</a>
					</div>
				</div>
			';
			$output .= '
				<script>
					var hostname =\'https://\'+ window.location.hostname;
					var hostnames =\'https://\'+ window.location.hostname;
					var jzt_url = "' . $config->jzt_url . '";
					var ijzt_url = "' . $config->ijzt_url . '";
					$("' . $addon_id . ' .tzd").click(function(){
                        if($("' . $addon_id . ' #key").val() == "")
                        {
                            alert("请输入搜索关键字");
                            $("' . $addon_id . ' #key").focus();
                            return false;
                        }
                    })
    
                    $("' . $addon_id . ' .srch_txt").bind("input propertychange change",function(){
                        var aa=$("' . $addon_id . ' #key").val();
                        a = encodeURI(encodeURI(aa));
                        if(a){
                          if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                            var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                          } else {
                            var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                          }
                            $("' . $addon_id . ' .tzd").attr("href",ff);
                        }
                    })
                    
                    function ev' . $this->addon->id . '()
                    {
                        var event = window.event || arguments.callee.caller.arguments[0];
                        if (event.keyCode == 13)
                        {
                            var search_na=jQuery("' . $addon_id . ' #key").val();
                            search_name = encodeURI(encodeURI(search_na));
                            if(search_name==""){
                                alert("请输入搜索内容")
                            }else{ 

                                if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                    window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                                } else {
                                    window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                                }
                              
                            }
                        }
                    }
				</script>
			';
		}
		return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
		public static function getTemplate()
		{
			$output = '
				<#
					var addonId = "jwpf-addon-"+data.id;
					var search_style_type = data.search_style_type || "type1";

					// 处理适配
					function toChangeNum(key) {
						var md = "", sm = "", xs = "";
						if(_.isObject(data[key])){
							md = data[key].md || ""; sm = data[key].sm || ""; xs = data[key].xs || "";
						}
						return { md: md, sm: sm, xs: xs }
					}
				#>
				<# if(search_style_type == "type3") {
					/* 整体配置 */
					// 搜索框位置
					var search_align04 = data.search_align04 || "flex-start";
					// 整体宽度 search_width04
					var search_width04_md = toChangeNum("search_width04").md || "458",
						search_width04_sm = toChangeNum("search_width04").sm,
						search_width04_xs = toChangeNum("search_width04").xs;
					// 整体高度
					var search_height04_md = toChangeNum("search_height04").md || "40",
						search_height04_sm = toChangeNum("search_height04").sm,
						search_height04_xs = toChangeNum("search_height04").xs;
					// 背景颜色
					var search_bgColor04 = data.search_bgColor04 || "#FFFFFF";
					// 边框宽度
					var search_border_width04_md = toChangeNum("search_border_width04").md || "1",
						search_border_width04_sm = toChangeNum("search_border_width04").sm,
						search_border_width04_xs = toChangeNum("search_border_width04").xs;
					// 边框颜色
					var search_border_color04 = data.search_border_color04 || "#588AAD";
					// 边框样式
					var search_border_style04 = data.search_border_style04 || "solid";
					// 边框圆角
					var search_border_radius04_md = toChangeNum("search_border_radius04").md || "1",
						search_border_radius04_sm = toChangeNum("search_border_radius04").sm, 
						search_border_radius04_xs = toChangeNum("search_border_radius04").xs;
					/* 图标部分 */
					// 图标
					var search_icon04 = data.search_icon04 || "https://oss.lcweb01.cn/joomla/20230515/6463e2733c5489861bcd6e9c987f8ef1.png";
					// 左侧宽度
					var icon_width04_md = toChangeNum("icon_width04").md || "50",
						icon_width04_sm = toChangeNum("icon_width04").sm,
						icon_width04_xs = toChangeNum("icon_width04").xs;
					// 左侧高度
					var icon_height04_md = toChangeNum("icon_height04").md || "20",
						icon_height04_sm = toChangeNum("icon_height04").sm,
						icon_height04_xs = toChangeNum("icon_height04").xs;
					// 竖线宽度
					var icon_border_width04_md = toChangeNum("icon_border_width04").md || "1",
						icon_border_width04_sm = toChangeNum("icon_border_width04").sm,
						icon_border_width04_xs = toChangeNum("icon_border_width04").xs;
					// 竖线颜色
					var icon_border_color04 = data.icon_border_color04 || "#D0D0D0";
					/* 输入框部分 */
					// 输入框文字大小
					var input_fontsize04_md = toChangeNum("input_fontsize04").md || "14",
						input_fontsize04_sm = toChangeNum("input_fontsize04").sm,
						input_fontsize04_xs = toChangeNum("input_fontsize04").xs;
					// 输入框文字颜色
					var input_color04 = data.input_color04 || "#202020";
					/* 搜索按钮 */
					// 按钮宽度
					var button_width04_md = toChangeNum("button_width04").md || "80",
						button_width04_sm = toChangeNum("button_width04").sm,
						button_width04_xs = toChangeNum("button_width04").xs;
					// 按钮背景颜色
					var button_bgColor04 = data.button_bgColor04 || "#0B5EA0";
					// 按钮文字颜色
					var button_color04 = data.button_color04 || "#FFFFFF";
					// 按钮文字大小
					var button_fontsize04_md = toChangeNum("button_fontsize04").md || "14",
						button_fontsize04_sm = toChangeNum("button_fontsize04").sm,
						button_fontsize04_xs = toChangeNum("button_fontsize04").xs;
				#>
					<style>
						#{{ addonId }} .search04 {
							display: flex;
							justify-content: {{search_align04}};
						}
						/* 搜索框整体 */
						#{{ addonId }} .search04 .search-box {
							display: flex;
							align-items: center;
							width: {{ search_width04_md }}px;
							height: {{ search_height04_md }}px;
							background: {{ search_bgColor04 }};
							border-radius: {{ search_border_radius04_md }};
							border: {{ search_border_width04_md }}px {{ search_border_style04 }} {{ search_border_color04 }};
							overflow: hidden;
						}
						/* 左侧图标 */
						#{{ addonId }} .search04 .search-box .search-icon-box {
							width: {{ icon_width04_md }}px;
							height: {{ icon_height04_md }}px;
							display: flex;
							align-items: center;
							justify-content: center;
							border-right: {{ icon_border_width04_md }}px solid {{ icon_border_color04 }};
						}
						#{{ addonId }} .search04 .search-box .search-icon-box img {
							width: 100%;
							height: 100%;
							object-fit: scale-down;
						}
						/* 搜索框 */
						#{{ addonId }} .search04 .search-box .search {
							flex: 1;
							height: {{ icon_height04_md }}px;
							line-height: {{ icon_height04_md }}px;
							padding: 0 15px;
							box-sizing: border-box;
							font-size: {{ input_fontsize04_md }}px;
							color: {{ input_color04 }};
							border: none;
							box-shadow: none;
							background: none;
							font-family: "微软雅黑";
						}
						#{{ addonId }} .search04 .search-box .search:focus {
							outline: none;
							border: none;
							box-shadow: none;
						}
						/* 搜索按钮 */
						#{{ addonId }} .search04 .search-box .search-btn {
							width: {{ button_width04_md }}px;
							height: 100%;
							line-height: {{ search_height04_md - search_border_width04_md * 2 }}px;
							font-size: {{ button_fontsize04_md }}px;
							text-align: center;
							color: {{ button_color04 }};
							background: {{ button_bgColor04 }};
						}
						@media (min-width: 768px) and (max-width: 991px) {
							/* 搜索框整体 */
							#{{ addonId }} .search04 .search-box {
								width: {{ search_width04_sm }}px;
								height: {{ search_height04_sm }}px;
								border-radius: {{ search_border_radius04_sm }};
								border-width: {{ search_border_width04_sm }}px;
							}
							/* 左侧图标 */
							#{{ addonId }} .search04 .search-box .search-icon-box {
								width: {{ icon_width04_sm }}px;
								height: {{ icon_height04_sm }}px;
								border-width: {{ icon_border_width04_sm }}px;
							}
							/* 搜索框 */
							#{{ addonId }} .search04 .search-box .search {
								height: {{ icon_height04_sm }}px;
								line-height: {{ icon_height04_sm }}px;
								font-size: {{ input_fontsize04_sm }}px;
							}
							/* 搜索按钮 */
							#{{ addonId }} .search04 .search-box .search-btn {
								width: {{ button_width04_sm }}px;
								line-height: {{ search_height04_sm - search_border_width04_sm * 2 }}px;
								font-size: {{ button_fontsize04_sm }}px;
							}
						}
						@media (max-width: 767px) {
							/* 搜索框整体 */
							#{{ addonId }} .search04 .search-box {
								width: {{ search_width04_xs }}px;
								height: {{ search_height04_xs }}px;
								border-radius: {{ search_border_radius04_xs }};
								border-width: {{ search_border_width04_xs }}px;
							}
							/* 左侧图标 */
							#{{ addonId }} .search04 .search-box .search-icon-box {
								width: {{ icon_width04_xs }}px;
								height: {{ icon_height04_xs }}px;
								border-width: {{ icon_border_width04_xs }}px;
							}
							/* 搜索框 */
							#{{ addonId }} .search04 .search-box .search {
								height: {{ icon_height04_xs }}px;
								line-height: {{ icon_height04_xs }}px;
								font-size: {{ input_fontsize04_xs }}px;
							}
							/* 搜索按钮 */
							#{{ addonId }} .search04 .search-box .search-btn {
								width: {{ button_width04_xs }}px;
								line-height: {{ search_height04_xs - search_border_width04_xs * 2 }}px;
								font-size: {{ button_fontsize04_xs }}px;
							}
						}
					</style>
					<div class="search04">
						<div class="search-box">
							<# if(data.is_search_icon04 == 1) { #>
								<div class="search-icon-box">
									<img src={{ search_icon04 }} alt="" />
								</div>
							<# } #>
							<input placeholder="{{data.input_text04}}" class="search" value="">
							<div class="search-btn">{{data.button_text04}}</div>
						</div>
					</div>
				<# } else {#>
					<style>
						#{{ addonId }} .search_box{
							border: 1px solid;
							outline-color: transparent;
							border-color: {{data.border_color}};
							width: {{data.input_width}}px;
							background: {{data.input_bg}};
							color:{{data.input_color}};
							border-radius: {{data.search_border_radius}}px;
							padding-left: {{data.input_padding_left}}px;
						}
						#{{ addonId }} .search_box::-webkit-input-placeholder{
							color: {{data.input_color_pla}} !important;
						}
						#{{ addonId }} .search_box::-moz-placeholder{
							color: {{data.input_color_pla}} !important;
						}
						#{{ addonId }} .search_box::-ms-input-placeholder{
							color: {{data.input_color_pla}} !important;
						}
						#{{ addonId }} .search_btn{
							padding: 0 10px;
							border: 1px solid {{data.button_border_color}};
							cursor: pointer;
							outline-color: transparent;
							background:{{data.button_color}};
							color:{{data.button_font_color}};
							border-radius: {{data.button_border_radius}}px;
							margin-left: {{data.search_button_num}}px;
							<# if(data.is_text_button===0){ #>
								background-image: url({{data.search_button_gb_img}}); /*引入图片图片2*/
								background-repeat: no-repeat;
								background-size: 100% 100%;
							<# } #>
						}
						#{{ addonId }} .icon{
							position: absolute;left: 0；z-index:5;
							background-image: url({{data.search_input_gb_img}}); /*引入图片图片*/
							background-repeat: no-repeat; /*设置图片不重复*/
							background-position: {{data.search_input_position_width}}px {{data.search_input_position_height}}px; /*图片显示的位置*/
							background-size: 100% 100%;
							width: {{data.search_input_width}}px; /*设置图片显示的宽*/
							height: {{data.search_input_height}}px; /*图片显示的高*/
						}
					</style>
					<div style="display:flex;justify-content: {{data.search_wz}};height:{{data.input_height}}px ">
						<# if(data.is_input_img){#>
							<# if (data.search_wz=="flex-start"){  #>
							<i class="icon" style="left:0;"></i>
							<# } #>
							<# if(data.search_wz=="center"){#>
							<i class="icon" style="left:40.72%;"></i>
							<# } #>
							<# if(data.search_wz=="flex-end"){#>
								<# var rightsiex= parseInt(data.input_width)+ parseInt(data.search_input_width); #>
								<i class="icon" style="right: {{rightsiex}}px;"></i>
							<# } #>
						<# } #>
						<input placeholder="{{data.search_input_text}}" style="width:{{data.input_width}}px" class="search_box" value="">
						<button style="width:{{data.button_width}}px;height:{{data.button_height}}px;" class="search_btn">{{data.search_button_text}}</button>
					</div>
				<# } #>
			';
			
			return $output;
	}
}
