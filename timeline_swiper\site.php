<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonTimeline_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $isk2installed = self::isComponentInstalled('timeline_swiper');

        if ($isk2installed === 0) {
            return '<div>出错了</div>';
        }

        $layout_id = $_GET['layout_id'] ?? 0;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addonId = $this->addon->id;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $items=(isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';
        $items_type2=(isset($settings->jw_image_carousel_item_type2) && $settings->jw_image_carousel_item_type2) ? $settings->jw_image_carousel_item_type2 : '';
        $items_type3=(isset($settings->jw_image_carousel_item_type3) && $settings->jw_image_carousel_item_type3) ? $settings->jw_image_carousel_item_type3 : '';

        $hover_img=(isset($settings->type_arrow_down_type1) && $settings->type_arrow_down_type1) ? $settings->type_arrow_down_type1 : 'https://oss.lcweb01.cn/joomla/20221019/84533a0b852a5fe9fc6c3c94403976fc.png';
        $show_type=(isset($settings->show_type) && $settings->show_type) ? $settings->show_type : 'type1';

        if($show_type=='type1')
        {
            $output='<div class="box">
            <div class="content" id="content_'.$addonId.'">';
            foreach ($items as $key => $item) {
                $output.='
                    <div id = "con_'.$key.'" class="con jwpf-wow clearfix jwpf-animated">
                        <div class="lc-e-big-slide">
                            <div class="content-img" style="background-image: url(\''.$item->img.'\')"></div>
                            <div class="swiper-container">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">';
                $output.=$item->text;
                $output.='</div>
                                </div>
                                <!--Add Scroll Bar-->
                                <div class="swiper-scrollbar"></div>
                            </div>
                        </div>
                    </div>';
            }
            $output.='</div>
                <div class="swiper-box" id="swiper_' . $addonId . '">
                    <div class="time-line"></div>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">';
            foreach ($items as $key => $item) {
                $output.='<div class="swiper-slide" id="time_'.$key.'">';
                    if($item->is_link===1){
                    $target=$item->link_open_new_window===1?'_blank':'';
                    $output.='<a href="'.$item->image_carousel_img_link.'" target="'.$target.'">';
                }
                    $output.='<div class="lc-e-shot-img-container">
                            <img src=\''.$hover_img.'\' alt="">
                        </div>
                        <div class="lc-e-year">'.$item->time.'</div>
                        <div class="lc-e-dot">
                            <div class="lc-e-dot2"></div>
                            <div class="lc-e-dot3"></div>
                            <div class="lc-e-dot4"></div>
                            <div class="lc-e-dot5"></div>
                        </div>
                        <div class="lc-e-year-text">'.$item->time_description.'</div>';
                    if($item->is_link===1){
                        $output.='</a>';
                    }
                $output.='</div>';
            }
            $output.='
                        </div>
                        <!-- Add Arrows -->
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </div>';
        }
        elseif ($show_type=='type2')
        {
            $left_img_type2 = (isset($settings->left_img_type2) && $settings->left_img_type2) ? $settings->left_img_type2 : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan7.png';
            $right_img_type2 = (isset($settings->right_img_type2) && $settings->right_img_type2) ? $settings->right_img_type2 : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan8.png';
            $output='

            <div class="fazhan guanyu" id="point8">
                <div class="fazhan-list width12">
                    <div class="swiper-container swiper-container-initialized swiper-container-horizontal" id="gallery">
                        <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(0px, 0px, 0px);">
                  ';
            foreach ($items_type2 as $key => $item) {
                $output.='
                    <div class="swiper-slide swiper-slide-active" style="width: 1200px; margin-right: 0px;">
                        <div class="fazhan-info">
                            <div class="time">
                                '.$item->time.'<span>'.$item->time_dw.'</span>
                            </div>
                            <div class="time-list">
                                '.$item->text.'
                            </div>
                        </div>
                    </div>';
            }
            $output.='
                    </div>
                    <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                </div>
                <div class="fazhan-thumbs-all">
                    <div class="swiper-container fazhan-thumbs swiper-container-initialized swiper-container-horizontal swiper-container-thumbs" id="thumbs">
                      <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(0px, 0px, 0px);">
            ';
            foreach ($items_type2 as $key => $item) {
                $output.='
                    <div class="swiper-slide" style="width: 292.5px; margin-right: 0px;">
                        <div class="fazhan-date" title="'.$item->title.'">
                        </div>
                    </div>';
            }
            $output.='
                      </div>
                            <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                            </div>
                            <div class="date-prev">
                                <img src="'.$left_img_type2.'">
                            </div>
                            <div class="date-next">
                                <img src="'.$right_img_type2.'">
                            </div>
                        </div>
                    </div>
                </div>
            ';
        }elseif($show_type=='type3'){
            $type3_title_fontsize=(isset($settings->type3_title_fontsize) && $settings->type3_title_fontsize) ? $settings->type3_title_fontsize : '16';
            $type3_cont_fontsize=(isset($settings->type3_cont_fontsize) && $settings->type3_cont_fontsize) ? $settings->type3_cont_fontsize : '12';
            $type3_title_color=(isset($settings->type3_title_color) && $settings->type3_title_color) ? $settings->type3_title_color : '#fff';
            $type3_cont_color=(isset($settings->type3_cont_color) && $settings->type3_cont_color) ? $settings->type3_cont_color : '#fff';
            $type3_title_fontsizehv=(isset($settings->type3_title_fontsizehv) && $settings->type3_title_fontsizehv) ? $settings->type3_title_fontsizehv : '18';
            $type3_cont_fontsizehv=(isset($settings->type3_cont_fontsizehv) && $settings->type3_cont_fontsizehv) ? $settings->type3_cont_fontsizehv : '14';
            $type3_title_colorhv=(isset($settings->type3_title_colorhv) && $settings->type3_title_colorhv) ? $settings->type3_title_colorhv : '#fff';
            $type3_cont_colorhv=(isset($settings->type3_cont_colorhv) && $settings->type3_cont_colorhv) ? $settings->type3_cont_colorhv : '#fff';

            $type3_leftright_button=$settings->type3_leftright_button ? $settings->type3_leftright_button : 0;
            $left_img_type3=(isset($settings->left_img_type3) && $settings->left_img_type3) ? $settings->left_img_type3 : 'https://oss.lcweb01.cn/joomla/20220310/ff0105994d6c3d6096d897a4c39aedb3.png';
            $right_img_type3=(isset($settings->right_img_type3) && $settings->right_img_type3) ? $settings->right_img_type3 : 'https://oss.lcweb01.cn/joomla/20220310/7b4f427b07a444ae7b707230c435a813.png';
            $bgimg_type3=(isset($settings->bgimg_type3) && $settings->bgimg_type3) ? $settings->bgimg_type3 : '';
            $type3_item_number=(isset($settings->type3_item_number) && $settings->type3_item_number) ? $settings->type3_item_number : '7';

            $suz=[];
            $i=0;
            foreach ($items_type3 as $key => $value) {
                $suz[$i][]=$value;
                if(($key+1)%$type3_item_number==0){
                    $i=$i+1;
                }
            }
            $output='
                <div class="about-main2" id="swp_'.$addonId.'" style="background: url('.$bgimg_type3.')no-repeat center center/100% 100%;">
                    <div class="licheng-bg">
                        <div class="warper pr">
                            <div class="licheng-content swiper-container">
                                <div class="swiper-wrapper" style="height: 100%; transition-duration: 0.3s; transform: translate3d(0px, 0px, 0px);">';

                                    foreach ($suz as $keys => $values) {

                                        $output.='<div class="swiper-slide swiper-slide-visible swiper-slide-active" style="width: 100%; height: 100%;">
                                            <ul class="">';
                                                foreach ($values as $key => $item) {

                                                    if($key<$type3_item_number){
                                                        if($key%2==0){
                                                            $output.='
                                                            <li class="item1">
                                                                <a class="">
                                                                    <div class="lc-left">
                                                                        <span class="date">'.$item->title.'</span>
                                                                        <p class="jiej">'.$item->text.'</p>
                                                                        <div class="dwi">
                                                                            <img src="https://oss.lcweb01.cn/joomla/20220310/a6a2720137fe62db08724b64d32757f5.png" class="dwsx" alt="">
                                                                            <img src="https://oss.lcweb01.cn/joomla/20220310/ab102164e104da8cece7bbee69f22e2f.png" class="dwyq" alt="">
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </li>';
                                                        }else{

                                                        }
                                                    }
                                                }
                                                $output.='<div class="clear"></div></ul>
                                                <img src="https://oss.lcweb01.cn/joomla/20220310/c25331efc95b2841ea46d0c94a34dc44.png" class="quxian">

                                                <ul class="first">';
                                                foreach ($values as $key => $item) {
                                                    if($key<$type3_item_number){
                                                        if($key%2==0){

                                                        }else{
                                                            $output.='
                                                                <li class="item2 ">
                                                                    <a class="">
                                                                        <div class="lc-left">
                                                                            <div class="dwis">
                                                                                <img src="https://oss.lcweb01.cn/joomla/20220310/ab102164e104da8cece7bbee69f22e2f.png" class="dwyqs" alt="">
                                                                                <img src="https://oss.lcweb01.cn/joomla/20220310/974c419cb88f3c1a6228e3454d982b64.png" class="dwsx" alt="">
                                                                            </div>
                                                                            <span class="date">'.$item->title.'</span>
                                                                            <p class="jiej">'.$item->text.'</p>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                            ';
                                                        }
                                                    }
                                                }
                                            $output.='

                                            </ul>
                                        </div>';

                                    }



                                $output.='</div>';
                                if($type3_leftright_button==1){
                                    $output.='
                                        <div class="lc-swiper-btn">
                                            <a class="btn-prev fl"><img src="'.$left_img_type3.'" alt=""></a>
                                            <a class="btn-next fr"><img src="'.$right_img_type3.'" alt=""></a>
                                        </div>
                                    ';
                                }

                            $output.='</div>
                        </div>
                    </div>
                </div>
            ';
        }elseif($show_type=='type4'){
            $addonId = '#jwpf-addon-' . $this->addon->id;

            $pcbj_img=(isset($settings->pcbj_img) && $settings->pcbj_img) ? $settings->pcbj_img : 'https://oss.lcweb01.cn/joomla/20220726/7d25807010545e5d3b19b70dbe25501f.jpg';
            $sjbj_img=(isset($settings->sjbj_img) && $settings->sjbj_img) ? $settings->sjbj_img : 'https://oss.lcweb01.cn/joomla/20220726/f1ca3cef341601f0a70de33f96bc1774.jpg';

            $left_img_type4=(isset($settings->left_img_type4) && $settings->left_img_type4) ? $settings->left_img_type4 : 'https://oss.lcweb01.cn/joomla/20220726/f482b27ddea8a6cfeac7e99f894e7c1e.png';
            $right_img_type4=(isset($settings->right_img_type4) && $settings->right_img_type4) ? $settings->right_img_type4 : 'https://oss.lcweb01.cn/joomla/20220726/5579f1155efd2264f1b3a3f1f5d02e74.png';
            $left_hvimg_type4=(isset($settings->left_hvimg_type4) && $settings->left_hvimg_type4) ? $settings->left_hvimg_type4 : 'https://oss.lcweb01.cn/joomla/20220726/d6121db286e35e70bc6d7cd663c64b07.png';
            $right_hvimg_type4=(isset($settings->right_hvimg_type4) && $settings->right_hvimg_type4) ? $settings->right_hvimg_type4 : 'https://oss.lcweb01.cn/joomla/20220726/bb7dacf8db27f3230b9635eb56174c50.png';

            $right_intro4_color=(isset($settings->right_intro4_color) && $settings->right_intro4_color) ? $settings->right_intro4_color : '#fff';
            $right_intro4_fontsize=(isset($settings->right_intro4_fontsize) && $settings->right_intro4_fontsize) ? $settings->right_intro4_fontsize : '14';

            $left_img4=(isset($settings->left_img4) && $settings->left_img4) ? $settings->left_img4 : 'https://oss.lcweb01.cn/joomla/20220725/0127b2b87bc6dab447a4eb0295029a40.jpg';
            $left_logo_img4=(isset($settings->left_logo_img4) && $settings->left_logo_img4) ? $settings->left_logo_img4 : 'https://oss.lcweb01.cn/joomla/20220725/a7df04956fd059d322a730a96e5aa62f.png';
            $left_bot_img4=(isset($settings->left_bot_img4) && $settings->left_bot_img4) ? $settings->left_bot_img4 : 'https://oss.lcweb01.cn/joomla/20220725/b436eb94220ae6e0c9a629d0e08b44ca.png';
            $left_title4=(isset($settings->left_title4) && $settings->left_title4) ? $settings->left_title4 : '百年味道';
            $left2_title4=(isset($settings->left2_title4) && $settings->left2_title4) ? $settings->left2_title4 : '百年传承';

            $jw_image_carousel_item04 = (isset($settings->jw_image_carousel_item04) && $settings->jw_image_carousel_item04) ? $settings->jw_image_carousel_item04 : array(
                array(
                    'title'=>'1906—2015',
                    'time_description'=>'百年历史马迭尔',
                    'img'=>'https://oss.lcweb01.cn/joomla/20220725/76019bd1d1c00eca8050f79d3c19edb0.jpg',
                    'adv_typeid'=>'',
                    'title_color'=>'#fff',
                    'title_font'=>'44',
                    'des_color'=>'#fff',
                    'des_font'=>'24',

                ),
                array(
                    'title'=>'2016—至今',
                    'time_description'=>'新时代下的马迭尔',
                    'img'=>'https://oss.lcweb01.cn/joomla/20220725/3cd06e486adc56a2183c684f37659f1f.jpg',
                    'adv_typeid'=>'',
                    'title_color'=>'#fff',
                    'title_font'=>'44',
                    'des_color'=>'#fff',
                    'des_font'=>'24',
                ),
            );

            $output='

                <div class="madieer2-a1">
                    <div class="madieer2-a2 clear">
                        <div class="madieer2-b1 clear wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.3s; animation-name: fadeInUp;">';
                            foreach ($jw_image_carousel_item04 as $key => $item) {
                                if($key==0){
                                    $output.='
                                        <div class="madieer2-b2 on1">
                                            <div class="madieer2-b3 i100">
                                                <img src="'.$item->img.'">
                                            </div>
                                            <div class="madieer2-b4" style="color:'.$item->title_color.';font-size:'.$item->title_font.'px;">'.$item->title.'</div>
                                            <div class="madieer2-b5" style="color:'.$item->des_color.';font-size:'.$item->des_font.'px;">'.$item->time_description.'</div>
                                            <div class="madieer2-b11"></div>
                                        </div>
                                    ';
                                }
                            }
                            $output.='
                            <div class="madieer2-b6">
                                <div class="madieer2-b7 i100">
                                    <img src="'.$left_img4.'">
                                </div>
                                <div class="madieer2-b8 i100">
                                    <img src="'.$left_logo_img4.'">
                                </div>
                                <div class="madieer2-b9">
                                    <strong>'.$left_title4.'</strong> '.$left2_title4.'
                                </div>
                                <div class="madieer2-b10 i200">
                                    <img src="'.$left_bot_img4.'">
                                </div>
                            </div>';
                            foreach ($jw_image_carousel_item04 as $key => $item) {
                                if($key==1){
                                    $output.='
                                        <div class="madieer2-b2">
                                            <div class="madieer2-b3 i100">
                                                <img src="'.$item->img.'">
                                            </div>
                                            <div class="madieer2-b4" style="color:'.$item->title_color.';font-size:'.$item->title_font.'px;">'.$item->title.'</div>
                                            <div class="madieer2-b5" style="color:'.$item->des_color.';font-size:'.$item->des_font.'px;">'.$item->time_description.'</div>
                                            <div class="madieer2-b11"></div>
                                        </div>
                                    ';
                                }
                            }

                        $output.='</div>
                        <div class="madieer2-c1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.3s; animation-name: fadeInUp;">';
                                $output.='<div class="madieer2-c2" style="display: block;">
                                        <div class="madieer2-c3">';
                                        foreach ($jw_image_carousel_item04 as $key => $item) {
                                            if($item->adv_typeid){
                                                if($key==0){
                                                    $hzhb_list = JwPageFactoryBase::getAdvLists($site_id, $company_id,$item->adv_typeid);
                                                    if($hzhb_list){
                                                        foreach ($hzhb_list as $keyd => $itemd) {
                                                            $showUnit = isset($item->showUnit) ? $item->showUnit : 1;
                                                            $unit = isset($item->unit) ? $item->unit : '年';
                                                            $output.='<div class="madieer2-c4" style="display: none;">
                                                                <div class="madieer2-c5">
                                                                    <span>'.$itemd->title.'</span>';
                                                                    if($showUnit){
                                                                        $output.='<span>'.$unit.'</span>';
                                                                    }
                                                                    $output.='</div>
                                                                <div class="madieer2-c6 i100">
                                                                    <img src="'.$itemd->images.'">
                                                                </div>
                                                                <div class="madieer2-c7" style="font-size:'.$right_intro4_fontsize.'px;line-height: 24px;font-weight: normal;color:'.$right_intro4_color.';">'.$itemd->introtext.'</div>

                                                            </div>';
                                                        }
                                                    }else{
                                                        $output.='<div style="padding:20px;text-align:center;color:#fff;">暂无数据</div>';
                                                    }
                                                }
                                            }
                                        }
                                        $output.='
                                        </div>
                                        <div class="madieer2-c9 madieer2-c11 i300"><img src="'.$left_img_type4.'"><img src="'.$left_hvimg_type4.'"></div>
                                        <div class="madieer2-c10 madieer2-c11 i300"><img src="'.$right_img_type4.'"><img src="'.$right_hvimg_type4.'"></div>
                                    </div>';
                                $output.='<div class="madieer2-c2" style="display: none;">
                                    <div class="madieer2-c3">';
                                    foreach ($jw_image_carousel_item04 as $key => $item1) {
                                        if($key==1){
                                            if($item1->adv_typeid){
                                                $hzhb_list1 = JwPageFactoryBase::getAdvLists($site_id, $company_id,$item1->adv_typeid);
                                                if($hzhb_list1){
                                                    foreach ($hzhb_list1 as $keyd => $itemd) {
                                                        $output.='<div class="madieer2-c4" style="display: none;">
                                                            <div class="madieer2-c5"><span>'.$itemd->title.'</span><span>年</span></div>
                                                            <div class="madieer2-c6 i100"><img src="'.$itemd->images.'"></div>
                                                            <div class="madieer2-c7" style="font-size:'.$right_intro4_fontsize.'px;line-height: 24px;font-weight: normal;color:'.$right_intro4_color.';">'.$itemd->introtext.'</div>
                                                        </div>';
                                                    }
                                                }else{
                                                    $output.='<div style="padding:20px;text-align:center;color:#fff;">暂无数据</div>';
                                                }
                                            }

                                        }
                                    }

                                    $output.='</div>
                                    <div class="madieer2-c9 madieer2-c11 i300">
                                        <img src="'.$left_img_type4.'">
                                        <img src="'.$left_hvimg_type4.'">
                                    </div>
                                    <div class="madieer2-c10 madieer2-c11 i300">
                                        <img src="'.$right_img_type4.'">
                                        <img src="'.$right_hvimg_type4.'">
                                    </div>
                                </div>';

                        $output.='</div>
                    </div>
                </div>

                <script>
                    if($("'.$addonId.' .madieer2-b2").length){
                        $("'.$addonId.' .madieer2-b2").eq(0).addClass("on1");
                        $("'.$addonId.' .madieer2-c2").eq(0).show();
                        $("'.$addonId.' .madieer2-c2").eq(0).find(".madieer2-c4").eq(0).show();
                        $("'.$addonId.' .madieer2-c2").eq(1).find(".madieer2-c4").eq(0).show();
                    }
                    $("'.$addonId.' .madieer2-b2").click(function(){
                        if(!$(this).hasClass("on1")){
                            $("'.$addonId.' .madieer2-b2").removeClass("on1");
                            $(this).addClass("on1");
                            if($(this).index()==0){
                                $("'.$addonId.' .madieer2-c2").fadeOut(500).eq(0).fadeIn(500);
                            }else{
                                $("'.$addonId.' .madieer2-c2").fadeOut(500).eq(1).fadeIn(500);
                            }
                        }
                    });
                    $("'.$addonId.' .madieer2-c9").click(function(){
                        for(var i=0;i<$(this).parent(".madieer2-c2").find(".madieer2-c4").length;i++){
                            if($(this).parent(".madieer2-c2").find(".madieer2-c4").eq(i).css("display")!="none"){
                                if(i==0){
                                    $(this).parent(".madieer2-c2").find(".madieer2-c4").fadeOut(500).eq($(this).parent(".madieer2-c2").find(".madieer2-c4").length-1).fadeIn(500);
                                }else{
                                    $(this).parent(".madieer2-c2").find(".madieer2-c4").fadeOut(500).eq(i-1).fadeIn(500);
                                }
                                break;
                            }
                        }
                    });
                    $("'.$addonId.' .madieer2-c10").click(function(){
                        for(var i=0;i<$(this).parent(".madieer2-c2").find(".madieer2-c4").length;i++){
                            if($(this).parent(".madieer2-c2").find(".madieer2-c4").eq(i).css("display")!="none"){
                                if(i==($(this).parent(".madieer2-c2").find(".madieer2-c4").length-1)){
                                    $(this).parent(".madieer2-c2").find(".madieer2-c4").fadeOut(500).eq(0).fadeIn(500);
                                }else{
                                    $(this).parent(".madieer2-c2").find(".madieer2-c4").fadeOut(500).eq(i+1).fadeIn(500);
                                }
                                break;
                            }
                        }
                    });
                </script>
            ';
        }elseif($show_type=='type5'){
            $output ='<script src="http://jzt_dev_2.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/js/jquery.SuperSlide.2.1.js"></script>';

            $jw_image_carousel_item5 = (isset($settings->jw_image_carousel_item5) && $settings->jw_image_carousel_item5) ? $settings->jw_image_carousel_item5 : array(
                array(
                    'time'=>'2014',
                    'time_description'=>'广晟入资国星光电',
                    'img'=>'https://oss.lcweb01.cn/joomla/20230116/e70e78c900af849b6391acad094ec398.png',
                ),
                array(
                    'time'=>'2010',
                    'time_description'=>'公司首次公开发行股票并在深圳证交所挂牌上市',
                    'img'=>'https://oss.lcweb01.cn/joomla/20230116/099a8574648d20336d8994118058b3c5.png',
                ),
                array(
                    'time'=>'1999',
                    'time_description'=>'公司开始自主创新的道路',
                    'img'=>'https://oss.lcweb01.cn/joomla/20230116/137830ebdba82193afda3fd092fc37a1.jpg',
                ),
                array(
                    'time'=>'1976',
                    'time_description'=>'公司正式投产GaAsP LED红黄光芯片及Lamp LED器件封装，是国内最早生产LED的企业之一',
                    'img'=>'https://oss.lcweb01.cn/joomla/20230116/b5b02bdb03bdf548508ea01c77406587.jpg',
                ),
            );

            $jw_lb_item5 = (isset($settings->jw_lb_item5) && $settings->jw_lb_item5) ? $settings->jw_lb_item5 : array(
                array(
                    'title'=>'产业链融合的高科技上市公司',
                    'time_id'=>'',
                ),
                array(
                    'title'=>'新世纪的民营自主创新型企业',
                    'time_id'=>'',
                ),
                array(
                    'title'=>'改革开放时代的代工型企业',
                    'time_id'=>'',
                ),
                array(
                    'title'=>'计划经济时代的国营工厂',
                    'time_id'=>'',
                ),
            );

            $output.='
                <div class="page_info2">
                    <div class="rongyu wrap">
                        <!--4个发展历程-->
                        <ul class="rongyu_a">';
                            foreach ($jw_image_carousel_item5 as $key => $item) {
                                $output.='<li>
                                        <h4>
                                            <img src="'.$item->img.'" alt="" width="214" height="158" title="" align=""><br>
                                        </h4>
                                        <span>'.$item->time.'</span>
                                        <p>
                                            '.$item->time_description.'
                                        </p>
                                    </li>';
                            }

                        $output.='</ul>
                        <div class="licheng_tab">
                            <ul>';

                                foreach ($jw_lb_item5 as $keyd => $itemd) {

                                    $output.='<li class="tp'.$itemd->time_id.' '; if($keyd==0){ $output.=' on'; } $output.=' ">
                                            <p>
                                                <strong>'.$itemd->title.'</strong>
                                            </p>
                                            ';

                                    if($itemd->time_id){

                                        $advtype = JwPageFactoryBase::getNoctiveTypeId($itemd->time_id);
                                        $output.='
                                            <span class="xbt" ><strong>'.$advtype->title.'</strong></span>
                                        ';

                                    }else{
                                        $output.='
                                            <span class="xbt" ><strong>请选择公告分类</strong></span>
                                        ';
                                    }

                                    $output.='
                                    </li>';

                                }

                            $output.='</ul>
                        </div>
                        <div class="licheng_info">
                            <ul>';
                                $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/notice.php';
                                require_once $article_helper;
                                foreach ($jw_lb_item5 as $keyd => $itemd) {
                                    $kes=$keyd+1;
                                    $output.='<li class="info'.$kes.'" >
                                            <div class="licheng_a">
                                                <div class="tempWrap" style="overflow: hidden; position: relative; width: 100%;">
                                                    <ul class="bd" style="width: 2119px; left: 0px; position: relative; overflow: hidden; padding: 0px; margin: 0px;">';

                                                        $items = JwpagefactoryHelperNotice::getNotice(50, 'latest', $itemd->time_id);

                                                        if(count((array)$items)) {
                                                            foreach ($items as $keyt => $itemdt) {
                                                                $output.='<li typeid='.$itemdt->catid.' '; if($keyt==0){ $output.='class="on"'; }  $output.='style="float: left; width: 50px;">'.$itemdt->title.'</li>';
                                                            }
                                                        }

                                                    $output.='</ul>
                                                </div>
                                                <span class="prev prevStop"></span>
                                                <span class="next"></span>
                                            </div>

                                            <script type="text/javascript">
                                                var widts=$(window).width();
                                                if(widts>=768){
                                                    jQuery(".info'.$kes.'").slide({titCell:".licheng_a .hd ul",mainCell:".licheng_a .bd",autoPage:true,effect:"left",vis:6,trigger:"click",pnLoop:false,
                                                        startFun:function(i){
                                                            $(".tempWrap").css("width","100%");
                                                            $(".rongyu_b .bd").css("padding-right","100px");
                                                        }
                                                    });
                                                }else if(widts>=400){
                                                    jQuery(".info'.$kes.'").slide({titCell:".licheng_a .hd ul",mainCell:".licheng_a .bd",autoPage:true,effect:"left",vis:3,trigger:"click",pnLoop:false,
                                                        startFun:function(i){
                                                            $(".tempWrap").css("width","100%");
                                                            $(".rongyu_b .bd").css("padding-right","100px");
                                                        }
                                                    });
                                                }else{
                                                    jQuery(".info'.$kes.'").slide({titCell:".licheng_a .hd ul",mainCell:".licheng_a .bd",autoPage:true,effect:"left",vis:2,trigger:"click",pnLoop:false,
                                                        startFun:function(i){
                                                            $(".tempWrap").css("width","100%");
                                                            $(".rongyu_b .bd").css("padding-right","100px");
                                                        }
                                                    });
                                                }

                                            </script>
                                            <div class="licheng_b">
                                                <ul>';
                                                foreach ($items as $keyt => $itemdt) {
                                                    $output.='<li '; if($keyt==0){ $output.='style="display: list-item;"'; }else{ $output.='style="display: none;"'; } $output.='>
                                                        '.$itemdt->fulltext.'

                                                    </li>';
                                                }

                                                $output.='</ul>
                                            </div>
                                        </li>
                                    ';
                                }

                            $output.='</ul>
                        </div>
                    </div>
                </div>
                <script>
                    $(function(){
                        $("'.$addon_id.' .rongyu_a li").click(function(){
                            var f=$(this).children("span").html();

                            $("'.$addon_id.' .licheng_info>ul>li").each(function(){
                                $(this).find(".licheng_a li").each(function(){
                                    if($(this).html()==f){

                                        $(this).trigger("mouseleave");

                                        $("'.$addon_id.' .licheng_info").children("ul").children("li").css({"display":"none"});

                                        $(this).parents(".bd").parents(".licheng_a").parent().css({"display":"block"});

                                        var tid=$(this).attr("typeid");

                                        $("'.$addon_id.' .licheng_tab ul li").removeClass("on");
                                        $("'.$addon_id.' .licheng_tab ul .tp"+tid).addClass("on");
                                        if($(this).index()>5){
                                            $(this).parents(".bd").css("left",($(this).index()-5)*-163);
                                        }
                                    }
                                })
                            })

                        })
                    })

                    $("'.$addon_id.' .rongyu_b ul li").hover(function(){
                        $(this).addClass("on").siblings().removeClass("on");
                        $("'.$addon_id.' .rongyu_c ul").eq($(this).index()).show().siblings().hide();
                    })
                    $("'.$addon_id.' .rongyu_b ul li").first().addClass("on");
                    $("'.$addon_id.' .rongyu_c ul").first().show();


                    $("'.$addon_id.' .licheng_tab ul li").last().css("border-right","1px solid #fff");
                    $("'.$addon_id.' .licheng_info").children("ul").children("li").css({"display":"none"});
                    $("'.$addon_id.' .info1").css({"display":"block"});
                    $("'.$addon_id.' .licheng_tab ul li").hover(function(){
                        $(this).addClass("on").siblings().removeClass("on");
                        var aa=$(this).index()+1;

                        $("'.$addon_id.' .licheng_info").children("ul").children("li").css({"display":"none"});
                        $("'.$addon_id.' .info"+aa).css({"display":"block"});
                    })
                    $("'.$addon_id.' .licheng_tab ul li").first().addClass("on");
                    $("'.$addon_id.' .licheng_info").children("ul").children("li").first().show();
                    $("'.$addon_id.' .licheng_a .bd li").hover(function(){
                        $(this).addClass("on").siblings().removeClass("on");
                        $(this).parents("li").find(".licheng_b").find("li").eq($(this).index()).show().siblings().hide();
                    })
                    $("'.$addon_id.' .licheng_b").each(function(){
                        $(this).find("li").first().show();
                    })
                    $("'.$addon_id.' .licheng_a").each(function(){
                        $(this).find("li").first().addClass("on");
                    })
                </script>
            ';

        }

        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $settings = $this->addon->settings;
        $show_type = (isset($settings->show_type) && $settings->show_type) ? $settings->show_type : 'type1';

        if($show_type!="type5"){
            $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        }
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $show_type = (isset($settings->show_type) && $settings->show_type) ? $settings->show_type : 'type1';
        if($show_type=='type1')
        {
            $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;


            //      slide之间的距离
            $carousel_margin = (isset($settings->carousel_margin) && $settings->carousel_margin) ? $settings->carousel_margin : 15;


            $carousel_item_number = (isset($settings->carousel_item_number) && $settings->carousel_item_number) ? $settings->carousel_item_number : 4;


            $script= 'jQuery(document).ready(function($){
                function initSwiper(slidesPerView,spaceBetween){
                    var clickEle=null;
                    let settings={
                        loopFillGroupWithBlank: true,
                        speed: '.$carousel_speed.',
                        observer: true,
                        observeParents:true,
                        navigation: {
                            nextEl: "#swiper_' . $addonId . ' .swiper-button-next",
                            prevEl: "#swiper_' . $addonId . ' .swiper-button-prev",
                        },
                        breakpoints: {
                            768: {
                                slidesPerView: 1,
                                spaceBetween: ' . $carousel_margin . ',
                            },
                            992: {
                                slidesPerView: ' . $carousel_item_number . ',
                                spaceBetween: ' . $carousel_margin . ',
                            },
                        },
                        on:{
                            slideChange:function(){
                                if(window.innerWidth>768) return;
                                $("#swiper_'.$addonId.' #time_"+this.realIndex).addClass("active").siblings().removeClass("active");
                                $("#content_'.$addonId.' #con_"+this.realIndex).addClass("lc-active fadeInDown").siblings().removeClass("lc-active fadeInDown");
                                setTimeout(function(){
                                    $("#content_'.$addonId.' #con_"+this.realIndex).removeClass("fadeInDown").attr("style","");
                                }, 500);
                            }
                        }
                    }
                    $("#swiper_' . $addonId . '").on("click", ".swiper-slide", function () {
                        let index=$(this).attr("id").replace("time_","");

                        $(this).addClass("active").siblings().removeClass("active");

                        $("#content_'.$addonId.' #con_"+index).addClass("lc-active fadeInDown").siblings().removeClass("lc-active fadeInDown");
                        setTimeout(function(){
                            $("#content_'.$addonId.' #con_"+index).removeClass("fadeInDown").attr("style","");
                        }, 500);
                    })

                    $("#swiper_'.$addonId.' #time_0").addClass("active").siblings().removeClass("active");
                    $("#content_'.$addonId.' #con_0").addClass("lc-active fadeInDown").siblings().removeClass("lc-active fadeInDown");
                    setTimeout(function(){
                        $("#content_'.$addonId.' #con_0").removeClass("fadeInDown").attr("style","");
                    }, 500);

                    let swiper = new Swiper("#swiper_' . $addonId . ' .swiper-container", settings);
                    return swiper;
                }


                initSwiper();


                window.onresize=function (){
                    initSwiper();
                }

                if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("jwpf-tab-pane")){
                     var Observer = new MutationObserver(function (mutations, instance) {
                        mutations.forEach(function (mutation) {
                          if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("active")){
                            initSwiper();
                          }
                        });
                     });

                     Observer.observe($("#jwpf-addon-wrapper-'.$addonId.'").parent()[0], {
                        attributes: true
                     });
                }

            //                   文章滚动
                let contentSwiper = new Swiper("#content_'.$addonId.' .swiper-container", {
                    direction: "vertical",
                    roundLengths : true,
                    slidesPerView: "auto",
                    freeMode: true,
                    scrollbar: {
                        el: "#content_'.$addonId.' .swiper-scrollbar",
                    },
                    mousewheel: true,
                });
            })';
        }
        elseif($show_type=='type2')
        {
            $script = "
                $(function() {

                    var DEFAULT_VERSION = 9;
                    var ua = navigator.userAgent.toLowerCase();
                    var isIE = ua.indexOf('msie') > -1;
                    var safariVersion;
                    if (isIE) {
                        safariVersion = ua.match(/msie ([\d.]+)/)[1];
                        var sa = parseInt(safariVersion);
                        if (safariVersion <= DEFAULT_VERSION) {

                            var length = $('#gallery .swiper-wrapper .swiper-slide').length;
                            var width = $('#gallery').width();
                            var width2 = $('#thumbs').width();
                            $('#gallery .swiper-wrapper').css('width',width*length);
                            $('#gallery .swiper-wrapper .swiper-slide').css('width','calc(100% / '+length+')');
                            $('#thumbs .swiper-wrapper').css('width',(width/4)*length);
                            $('#thumbs .swiper-wrapper .swiper-slide').css('width',width/4);
                            $('#gallery').scrollLeft(width*10);
                            $('#thumbs').scrollLeft(width2*10);
                            $('#thumbs .swiper-wrapper .swiper-slide').eq(10).addClass('swiper-slide-thumb-active');

                            $('#thumbs .swiper-wrapper .swiper-slide').click(function(){
                                var index=$(this).index();
                                $(this).addClass('swiper-slide-thumb-active').siblings().removeClass('swiper-slide-thumb-active');
                                $('#gallery').animate({'scrollLeft': width*index}, 500);
                            })

                            $('.date-prev').click(function(){
                                if($('.swiper-slide-thumb-active').offset().left<3*width2/8){
                                    $('#thumbs').animate({'scrollLeft': $('#thumbs').scrollLeft() - width2/4}, 500);
                                }
                                $('.swiper-slide-thumb-active').prev().addClass('swiper-slide-thumb-active').siblings().removeClass('swiper-slide-thumb-active');
                                var index=$('.swiper-slide-thumb-active').index();
                                $('#gallery').animate({'scrollLeft': width*index}, 500);
                            })
                            $('.date-next').click(function(){
                                if($('.swiper-slide-thumb-active').position().left>5*width2/8){
                                    $('#thumbs').animate({'scrollLeft': $('#thumbs').scrollLeft()+ width2/4}, 500);
                                }
                                $('.swiper-slide-thumb-active').next().addClass('swiper-slide-thumb-active').siblings().removeClass('swiper-slide-thumb-active');
                                var index=$('.swiper-slide-thumb-active').index();
                                $('#gallery').animate({'scrollLeft': width*index}, 500);
                            })
                        } else {

                            swiper2()
                        }
                    } else {

                        swiper2()
                    }

                });

                function swiper1(){
                    var mySwiper = new Swiper('.banners', {
                        autoplay: false,
                        loop:false,
                    })
                }

                function swiper2(){
                    var gallerySwiper = new Swiper('#gallery',{
                        spaceBetween: 0,
                        initialSlide :0,
                        thumbs: {
                        swiper: {
                            el: '#thumbs',
                            spaceBetween: 0,
                            slidesPerView: 4,
                            watchSlidesVisibility: true,
                        },
                        }
                    })
                    $('.date-prev').click(function(){
                        gallerySwiper.slidePrev();
                        })
                    $('.date-next').click(function(){
                        gallerySwiper.slideNext();
                    })
                }
            ";
        }elseif($show_type=='type3'){
            $script = "
                jQuery(document).ready(function($){
                    var mySwiper = new Swiper('#swp_".$addonId." .swiper-container.licheng-content', {
                        autoplay: false, //自动滑动
                    })
                    $('#swp_".$addonId." .btn-prev').click(function() {
                        // mySwiper.swipePrev();
                        mySwiper.slidePrev();

                    })
                    $('#swp_".$addonId." .btn-next').click(function() {
                        // mySwiper.swipeNext();
                        mySwiper.slideNext();

                    })
                })
            ";
        }

        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;
        $show_type=(isset($settings->show_type) && $settings->show_type) ? $settings->show_type : 'type1';
        if($show_type=='type1')
        {
            //        轮播
            $addonId = '#swiper_'.$this->addon->id;
            $id = '#content_'.$this->addon->id;

            $carousel_width = (isset($settings->carousel_width) && $settings->carousel_width) ? $settings->carousel_width : 75;
            $arrow_width = (isset($settings->arrow_width) && $settings->arrow_width) ? $settings->arrow_width : 34;
            $arrow_height = (isset($settings->arrow_height) && $settings->arrow_height) ? $settings->arrow_height : 34;
            $arrow_background = (isset($settings->arrow_background) && $settings->arrow_background) ? $settings->arrow_background : '#fff';
            $arrow_border_color = (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? $settings->arrow_border_color : '#cecece';
            $arrow_color = (isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '#d51920';
            $arrow_font_size= (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? $settings->arrow_font_size : 16;
            $arrow_hover_background= (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? $settings->arrow_hover_background : '#d51920';
            $arrow_hover_border_color= (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? $settings->arrow_hover_border_color : '#d51920';
            $arrow_hover_color= (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '#fff';
            if (isset($settings->time_font_up) && $settings->time_font_up) {
                if (is_object($settings->time_font_up)) {
                    $time_font_up = $settings->time_font_up->md;
                    $time_font_up_sm = $settings->time_font_up->sm;
                    $time_font_up_xs= $settings->time_font_up->xs;
                } else {
                    $time_font_up = $settings->time_font_up;
                    $time_font_up_sm = $settings->time_font_up_sm;
                    $time_font_up_xs = $settings->time_font_up_xs;
                }
            } else {
                $time_font_up = '28';
                $time_font_up_sm = '24';
                $time_font_up_xs = '20';
            }
            if (isset($settings->time_font_down) && $settings->time_font_down) {
                if (is_object($settings->time_font_down)) {
                    $time_font_down = $settings->time_font_down->md;
                    $time_font_down_sm = $settings->time_font_down->sm;
                    $time_font_down_xs= $settings->time_font_down->xs;
                } else {
                    $time_font_down = $settings->time_font_down;
                    $time_font_down_sm = $settings->time_font_down_sm;
                    $time_font_down_xs = $settings->time_font_down_xs;
                }
            } else {
                $time_font_down = '34';
                $time_font_down_sm = '34';
                $time_font_down_xs = '20';
            }
            $time_font_color= (isset($settings->time_font_color) && $settings->time_font_color) ? $settings->time_font_color : '#333';
            if (isset($settings->time_title_font) && $settings->time_title_font) {
                if (is_object($settings->time_title_font)) {
                    $time_title_font = $settings->time_title_font->md;
                    $time_title_font_sm = $settings->time_title_font->sm;
                    $time_title_font_xs= $settings->time_title_font->xs;
                } else {
                    $time_title_font = $settings->time_title_font;
                    $time_title_font_sm = $settings->time_title_font_sm;
                    $time_title_font_xs = $settings->time_title_font_xs;
                }
            } else {
                $time_title_font = '16';
                $time_title_font_sm = '16';
                $time_title_font_xs = '16';
            }
            $time_title_font_color= (isset($settings->time_title_font_color) && $settings->time_title_font_color) ? $settings->time_title_font_color : '#666';
            $time_active_font_color= (isset($settings->time_active_font_color) && $settings->time_active_font_color) ? $settings->time_active_font_color : '#d51920';
            $time_dot_color= (isset($settings->time_dot_color) && $settings->time_dot_color) ? $settings->time_dot_color : '#d51920';
            $timeline_color= (isset($settings->timeline_color) && $settings->timeline_color) ? $settings->timeline_color : '#e6e6e6';
            if (isset($settings->content_width) && $settings->content_width) {
                if (is_object($settings->content_width)) {
                    $content_width = $settings->content_width->md;
                    $content_width_sm = $settings->content_width->sm;
                    $content_width_xs= $settings->content_width->xs;
                } else {
                    $content_width = $settings->content_width;
                    $content_width_sm = $settings->content_width_sm;
                    $content_width_xs = $settings->content_width_xs;
                }
            } else {
                $content_width = '75';
                $content_width_sm = '75';
                $content_width_xs = '100';
            }
            if (isset($settings->content_height) && $settings->content_height) {
                if (is_object($settings->content_height)) {
                    $content_height = $settings->content_height->md;
                    $content_height_sm = $settings->content_height->sm;
                    $content_height_xs= $settings->content_height->xs;
                } else {
                    $content_height = $settings->content_height;
                    $content_height_sm = $settings->content_height_sm;
                    $content_height_xs = $settings->content_height_xs;
                }
            } else {
                $content_height = '390';
                $content_height_sm = '390';
                $content_height_xs = '390';
            }
            if (isset($settings->content_img_width) && $settings->content_img_width) {
                if (is_object($settings->content_img_width)) {
                    $content_img_width = $settings->content_img_width->md;
                } else {
                    $content_img_width = $settings->content_img_width;
                }
            } else {
                $content_img_width = '40.85';
            }
            $content_img_margin_right= (isset($settings->content_img_margin_right) && $settings->content_img_margin_right) ? $settings->content_img_margin_right : 35;
            $content_border_color= (isset($settings->content_border_color) && $settings->content_border_color) ? $settings->content_border_color : '#eee';
            $content_padding= (isset($settings->content_padding) && $settings->content_padding) ? $settings->content_padding : '35px 30px 35px 30px';
            $content_scroll_color= (isset($settings->content_scroll_color) && $settings->content_scroll_color) ? $settings->content_scroll_color : '#e2e2e2';
            $content_scrollbar_color= (isset($settings->content_scrollbar_color) && $settings->content_scrollbar_color) ? $settings->content_scrollbar_color : '#c32026';
            $content_img_height= (isset($settings->content_img_height) && $settings->content_img_height) ? $settings->content_img_height : 30;




            $output = '
                #jwpf-addon-'.$this->addon->id.' .box{
                    width: 100%;
                }
                /*对应的图片和文字*/
                '.$id.'{
                    height: '.$content_height.'px;
                    width: '.$content_width.'%;
                    margin: 0 auto;
                    position: relative;
                }
                '.$id.'>div{
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                '.$id.' .content-img{
                    width: '.$content_img_width.'%;
                    height: 100%;
                    background-size: cover;
                    float: left;
                    margin-right: '.$content_img_margin_right.'px;
                }
                '.$id.' .swiper-container{
                    width: calc(100% - '.$content_img_width.'% - '.$content_img_margin_right.'px);
                    height: 100%;
                    padding-right: 30px;
                    box-sizing: border-box;
                }
                '.$id.' .lc-e-big-slide {
                    height: 100%;
                    width: 100%;
                    border: 1px solid '.$content_border_color.';
                    -webkit-box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    box-sizing: border-box;
                    padding: '.$content_padding.';
                    z-index: 2;
                    visibility: hidden;
                    overflow:hidden;
                }
                '.$id.' .swiper-container{
                    float: left;
                }
                '.$id.' .swiper-slide{
                    height:auto;
                }
                '.$id.' .lc-active .lc-e-big-slide{
                    visibility: visible;
                }
                '.$id.' .swiper-scrollbar{
                    width: 3px;
                    background: '.$content_scroll_color.';
                }
                '.$id.' .swiper-scrollbar-drag{
                    width: 7px;
                    background: '.$content_scrollbar_color.';
                    border-radius: 4px;
                    left: -80%;
                    margin:auto;
                }
                /*时间轴轮播*/
                '.$addonId.'{
                    position: relative;
                    width: 100%;
                    min-height: 160px;
                    overflow: hidden;
                }
                '.$addonId.' .swiper-container{
                    width: '.$carousel_width.'%;
                    min-height: 160px;
                }
                '.$addonId.' .swiper-container .swiper-slide{
                    min-height: 160px;
                }
                '.$addonId.' .swiper-slide img{
                    width: 100%;
                    height: 100%;
                    display: block;
                    object-fit: cover;
                }
                '.$addonId.' .time-line{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    height: 2px;
                    width: 100%;
                    background: '.$timeline_color.';
                }
                '.$addonId.' div.lc-e-shot-img-container{
                    color: #000;
                    list-style: none;
                    font: normal normal 0.28rem/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB,\\5b8b\4f53,sans-serif;
                    font-size: 16px;
                    text-align: center;
                    margin: 0;
                    padding: 0;
                    -webkit-tap-highlight-color: rgba(255,255,255,0);
                    -webkit-user-select: none;
                    position: absolute;
                    width: 37px;
                    height: 17px;
                    bottom: -17px;
                    left: 50%;
                    top: 0;
                    transform: translateX(-50%);
                    display: none;
                }
                '.$addonId.' .lc-e-year{
                    list-style: none;
                    font: normal normal 0.28rem/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB,\\5b8b\4f53,sans-serif;
                    text-align: center;
                    margin: 0;
                    padding: 0;
                    -webkit-tap-highlight-color: rgba(255,255,255,0);
                    -webkit-user-select: none;
                    color: '.$time_font_color.';
                    width: 100%;
                    position: absolute;
                    line-height: 65px;
                }
                '.$addonId.' .swiper-slide:nth-of-type(2n+1) .lc-e-year{
                    top: 17px;
                    font-size: '.$time_font_up.'px;
                }
                '.$addonId.' .swiper-slide:nth-of-type(2n) .lc-e-year{
                    font-size: '.$time_font_down.'px;
                    top: 103px;
                }
                '.$addonId.' .lc-e-dot{
                    color: #000;
                    list-style: none;
                    font: normal normal 0.28rem/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB,\\5b8b\4f53,sans-serif;
                    font-size: 16px;
                    text-align: center;
                    padding: 0;
                    -webkit-tap-highlight-color: rgba(255,255,255,0);
                    -webkit-user-select: none;
                    width: 18px;
                    height: 18px;
                    border: 1px solid #cccccc;
                    background: #fff;
                    position: absolute;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                    top: 73px;
                    border-radius: 100%;
                }
                '.$addonId.' .lc-e-dot2,
                '.$addonId.' .lc-e-dot3,
                '.$addonId.' .lc-e-dot4,
                '.$addonId.' .lc-e-dot5{
                    color: #000;
                    list-style: none;
                    font: normal normal 0.28rem/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB,\\5b8b\4f53,sans-serif;
                    font-size: 16px;
                    text-align: center;
                    margin: 0;
                    padding: 0;
                    -webkit-tap-highlight-color: rgba(255,255,255,0);
                    -webkit-user-select: none;
                    display: block;
                    content: "";
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: -6px;
                    bottom: -6px;
                    box-sizing: border-box;
                    transform: scale(0.2);
                    opacity: 1;
                    border-radius: 100%;
                    border: 2px solid '.$time_dot_color.';
                    animation-name: dotbefore;
                }
                '.$addonId.' .lc-e-dot2{
                    animation-delay: 0s;
                }
                '.$addonId.' .lc-e-dot3{
                    animation-delay: 1s;
                }
                '.$addonId.' .lc-e-dot::after{
                    display: block;
                    content: "";
                    width: 8px;
                    height: 8px;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                    border-radius: 100%;
                    background: #666;
                }
                '.$addonId.' .lc-e-year-text{
                    list-style: none;
                    font: normal normal 0.28rem/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB,\\5b8b\4f53,sans-serif;
                    text-align: center;
                    margin: 0;
                    padding: 0;
                    -webkit-tap-highlight-color: rgba(255,255,255,0);
                    -webkit-user-select: none;
                    color: '.$time_title_font_color.';
                    width: 100%;
                    top: 103px;
                    position: absolute;
                    line-height: 65px;
                    font-size: '.$time_title_font.'px;
                }
                '.$addonId.' .swiper-slide:nth-of-type(2n+1) .lc-e-year-text{
                    top: 103px;
                }
                '.$addonId.' .swiper-slide:nth-of-type(2n) .lc-e-year-text{
                    top: 17px;
                }
                '.$addonId.' .swiper-slide:hover .lc-e-shot-img-container,
                '.$addonId.' .swiper-slide.active .lc-e-shot-img-container{
                    display: block;
                }
                '.$addonId.' .swiper-slide:hover .lc-e-year,
                '.$addonId.' .swiper-slide.active .lc-e-year{
                    color: '.$time_active_font_color.';
                }
                '.$addonId.' .swiper-slide:hover .lc-e-dot,
                '.$addonId.' .swiper-slide.active .lc-e-dot{
                    border-color: '.$time_dot_color.';
                }
                '.$addonId.' .swiper-slide:hover .lc-e-dot::after,
                '.$addonId.' .swiper-slide.active .lc-e-dot::after{
                    background: '.$time_dot_color.';
                }
                '.$addonId.' .swiper-slide:hover .lc-e-dot2,
                '.$addonId.' .swiper-slide.active .lc-e-dot2,
                '.$addonId.' .swiper-slide:hover .lc-e-dot3,
                '.$addonId.' .swiper-slide.active .lc-e-dot3,
                '.$addonId.' .swiper-slide:hover .lc-e-dot4,
                '.$addonId.' .swiper-slide.active .lc-e-dot4,
                '.$addonId.' .swiper-slide:hover .lc-e-dot5,
                '.$addonId.' .swiper-slide.active .lc-e-dot5{
                    -webkit-animation-name: dotbefore;
                    animation-name: dotbefore;
                    -webkit-animation-duration: 2s;
                    animation-duration: 2s;
                    -webkit-animation-iteration-count: infinite;
                    animation-iteration-count: infinite;
                    -webkit-animation-fill-mode: both;
                    animation-fill-mode: both;
                    -webkit-animation-timing-function: linear;
                    animation-timing-function: linear;
                }
                '.$addonId.' .swiper-slide:hover .lc-e-dot2
                '.$addonId.' .swiper-slide.active .lc-e-dot2{
                    animation-delay: 0s;
                }
                @keyframes dotbefore{
                    100% {
                        -webkit-transform: scale(1);
                        transform: scale(1);
                        opacity: 0.2;
                    }
                }
                '.$addonId.' .swiper-button-next,
                '.$addonId.' .swiper-button-prev{
                    width: '.$arrow_width.'px;
                    height: '.$arrow_height.'px;
                    margin-top:0;
                    transform: translateY(-50%);
                    border: 1px solid '.$arrow_border_color.';
                    border-radius: 50%;
                    background: '.$arrow_background.';
                    outline: none;
                    background-image: none;
                }
                '.$addonId.' .swiper-button-next::after,
                '.$addonId.' .swiper-button-prev::after{
                    font-size: '.$arrow_font_size.'px;
                    color: '.$arrow_color.';
                }
                '.$addonId.' .swiper-button-disabled{
                    background: '.$arrow_hover_background.';
                    border: 1px solid '.$arrow_hover_border_color.';
                    opacity: 1;
                }
                '.$addonId.' .swiper-button-disabled::after{
                    color: '.$arrow_hover_color.';
                }
                @media (min-width: 767px) and (max-width: 991px){
                    '.$addonId.' .swiper-slide:nth-of-type(2n+1) .lc-e-year{
                        font-size: '.$time_font_up_sm.'px;
                    }
                    '.$addonId.' .swiper-slide:nth-of-type(2n) .lc-e-year{
                        font-size: '.$time_font_down_sm.'px;
                    }
                    '.$addonId.' .lc-e-year-text{
                        font-size: '.$time_title_font_sm.'px;
                    }
                    '.$id.'{
                        height: '.$content_height_sm.'px;
                        width: '.$content_width_sm.'%;
                    }
                    '.$id.' .content-img{
                        width: 100%;
                        height: '.$content_img_height.'%;
                        background-position: center;
                        margin-right: 0;
                        margin-bottom: 35px;
                    }
                    '.$id.' .swiper-container{
                        width: 100%;
                        height: calc(100% - '.$content_img_height.'% - 35px);
                    }
                }
                @media (max-width: 767px){
                    '.$id.'{
                        height: '.$content_height_xs.'px;
                        width: '.$content_width_xs.'%;
                    }
                    '.$addonId.' .swiper-slide:nth-of-type(2n+1) .lc-e-year{
                        font-size: '.$time_font_up_xs.'px;
                    }
                    '.$addonId.' .swiper-slide:nth-of-type(2n) .lc-e-year{
                        font-size: '.$time_font_down_xs.'px;
                    }
                    '.$addonId.' .lc-e-year-text{
                        font-size: '.$time_title_font_xs.'px;
                    }
                    '.$id.'{
                        height: '.$content_height_xs.'px;
                        width: '.$content_width_xs.'%;
                    }
                    '.$id.' .content-img{
                        width: 100%;
                        height: '.$content_img_height.'%;
                        background-position: center;
                        margin-right: 0;
                        margin-bottom: 35px;
                    }
                    '.$id.' .swiper-container{
                        width: 100%;
                        height: calc(100% - '.$content_img_height.'% - 35px);
                    }
                }';
        }
        elseif($show_type=='type2')
        {
            $addonId = '#jwpf-addon-' . $this->addon->id;
            $time_font_color_type2 = (isset($settings->time_font_color_type2) && $settings->time_font_color_type2) ? $settings->time_font_color_type2 : '#1196DB';
            $time_font_size_type2 = (isset($settings->time_font_size_type2) && $settings->time_font_size_type2) ? $settings->time_font_size_type2 : 48;
            $time_dw_font_size_type2 = (isset($settings->time_dw_font_size_type2) && $settings->time_dw_font_size_type2) ? $settings->time_dw_font_size_type2 : 16;
            $zuobiao_img_type2 = (isset($settings->zuobiao_img_type2) && $settings->zuobiao_img_type2) ? $settings->zuobiao_img_type2 : 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan5.png';
            $time_infoW_type2 = isset($settings->time_infoW_type2) && $settings->time_infoW_type2 ? $settings->time_infoW_type2 : '60';

            $output ='
            '.$addonId.' * {margin: 0;padding: 0;box-sizing: border-box;}
            '.$addonId.' .fazhan {background: #F0F0F0;padding-bottom: 50px;}
            '.$addonId.' .width12 {width: 90%;margin: auto;}
            '.$addonId.' #gallery {overflow: hidden;}
            '.$addonId.' .swiper-container {margin-left: auto;margin-right: auto;position: relative;overflow: hidden;list-style: none;padding: 0;z-index: 1;}
            '.$addonId.' .swiper-wrapper {position: relative;width: 100%;height: 100%;z-index: 1;display: flex;transition-property: transform;box-sizing: content-box;}
            '.$addonId.' #gallery .swiper-wrapper .swiper-slide {float: left;}
            '.$addonId.' .swiper-slide {flex-shrink: 0;width: 100%;height: 100%;position: relative;transition-property: transform;}
            '.$addonId.' .swiper-container .swiper-notification {position: absolute;left: 0;top: 0;pointer-events: none;opacity: 0;z-index: -1000;}
            '.$addonId.' .fazhan-info {display: flex;padding: 30px 0 0 20%;width: ' . $time_infoW_type2 . '%;}
            '.$addonId.' .fazhan-info .time {float: left;color: '.$time_font_color_type2.';font-size: '.$time_font_size_type2.'px;padding: 0 40px;white-space: nowrap;}
            '.$addonId.' .fazhan-info .time span {font-size: '.$time_dw_font_size_type2.'px;}
            '.$addonId.' .fazhan-info .time-list {float: left;width: 60%;}
            '.$addonId.' .fazhan-info .time-list {padding-top: 30px;}
            '.$addonId.' .fazhan-thumbs-all {position: relative;}
            '.$addonId.' #thumbs {overflow: hidden;}
            '.$addonId.' .fazhan-thumbs {position: relative;padding: 40px 0;}
            '.$addonId.' #thumbs .swiper-wrapper .swiper-slide {float: left;}
            '.$addonId.' .fazhan-thumbs .swiper-wrapper .swiper-slide {position: relative;display: block;justify-content: center;align-items: center;}
            '.$addonId.' .fazhan-thumbs .swiper-slide-thumb-active::before {content: "";position: absolute;top: -40px;left: calc(50% - 20px);width: 40px;height: 40px;background: url('.$zuobiao_img_type2.') no-repeat;background-size: 100% 100%;}
            '.$addonId.' .fazhan-thumbs .swiper-wrapper .swiper-slide::after {content: "";position: absolute;top: 50%;width: 100%;height: 1px;background: #ccc;left: 0;z-index: -1;}
            '.$addonId.' .fazhan-date {margin-left: calc(50% - 10px);position: relative;width: 20px;height: 20px;background: white;border: 1px solid #333;}
            '.$addonId.' .fazhan-thumbs .swiper-slide-thumb-active .fazhan-date::before {content: "";position: absolute;top: 4px;left: 3px;width: 9px;height: 9px;border: 1px solid #1196DB;}
            '.$addonId.' .fazhan-date::after {content: attr(title);position: absolute;width: 50px;left: -10px;top: 30px;}
            '.$addonId.' .date-prev {position: absolute;width: 25px;top: 40%;left: -30px;}
            '.$addonId.' img {display: block;width: 100%;border: none;}
            '.$addonId.' .date-next {position: absolute;width: 25px;top: 40%;right: -30px;}
            @media screen and (max-width: 768px) {
                '.$addonId.' .fazhan-info {display: block;padding: 0;width: 100%;}
                '.$addonId.' .fazhan-info .time-list {float: left;width: 100%;}
                '.$addonId.' .fazhan-info .time-list li {width: 100%;}
                '.$addonId.' .date-prev {top: auto;left: 0;}
                '.$addonId.' .date-next {top: auto;right: 0;}
            }
            ';
        }
        elseif($show_type=='type3'){
            $addon_id = '#jwpf-addon-' . $this->addon->id;

            $type3_title_fontsize=(isset($settings->type3_title_fontsize) && $settings->type3_title_fontsize) ? $settings->type3_title_fontsize : '16';
            $type3_cont_fontsize=(isset($settings->type3_cont_fontsize) && $settings->type3_cont_fontsize) ? $settings->type3_cont_fontsize : '12';
            $type3_title_color=(isset($settings->type3_title_color) && $settings->type3_title_color) ? $settings->type3_title_color : '#fff';
            $type3_cont_color=(isset($settings->type3_cont_color) && $settings->type3_cont_color) ? $settings->type3_cont_color : '#fff';
            $type3_title_fontsizehv=(isset($settings->type3_title_fontsizehv) && $settings->type3_title_fontsizehv) ? $settings->type3_title_fontsizehv : '18';
            $type3_cont_fontsizehv=(isset($settings->type3_cont_fontsizehv) && $settings->type3_cont_fontsizehv) ? $settings->type3_cont_fontsizehv : '14';
            $type3_title_colorhv=(isset($settings->type3_title_colorhv) && $settings->type3_title_colorhv) ? $settings->type3_title_colorhv : '#fff';
            $type3_cont_colorhv=(isset($settings->type3_cont_colorhv) && $settings->type3_cont_colorhv) ? $settings->type3_cont_colorhv : '#fff';
            $type3_jt_top=(isset($settings->type3_jt_top) && $settings->type3_jt_top) ? $settings->type3_jt_top : '225';

            $type3_item_number=(isset($settings->type3_item_number) && $settings->type3_item_number) ? $settings->type3_item_number : '7';


            $type3_leftright_button=$settings->type3_leftright_button ? $settings->type3_leftright_button : 0;
            $left_img_type3=(isset($settings->left_img_type3) && $settings->left_img_type3) ? $settings->left_img_type3 : 'https://oss.lcweb01.cn/joomla/20220310/ff0105994d6c3d6096d897a4c39aedb3.png';
            $right_img_type3=(isset($settings->type3_title_colorhv) && $settings->type3_title_colorhv) ? $settings->type3_title_colorhv : 'https://oss.lcweb01.cn/joomla/20220310/7b4f427b07a444ae7b707230c435a813.png';
            $output ='
                '.$addon_id.' *{margin:0;padding:0;}
                '.$addon_id.' a{color:#333;transition:all 0.3s ease-in-out;cursor:pointer;text-decoration: none;}
                '.$addon_id.' a:hover{color:#0a3875;}
                '.$addon_id.' ul,dl{list-style:none;}
                '.$addon_id.' i{font-style: normal;}
                '.$addon_id.' img{display:inline-block;vertical-align:middle;border:0;}
                '.$addon_id.' .warper{max-width:1800px;margin:0 auto;}
                '.$addon_id.' .fl{float:left}
                '.$addon_id.' .fr{float:right}
                '.$addon_id.' .clear{clear:both;display:block;overflow:hidden;visibility:hidden;width:0;height:0;}
                '.$addon_id.' .clearfix{*zoom:1;}
                '.$addon_id.' .pr{position: relative;}
                '.$addon_id.' .warper{max-width: 1800px; margin: 0 auto;}
                '.$addon_id.' .about-main2{height: 640px;width: 100%;overflow: hidden;}
                '.$addon_id.' .about-main2 .licheng-bg{width: 100%;height: 100%;position: relative;}
                '.$addon_id.' .about-main2 .licheng-bg .warper{height: 100%;}
                '.$addon_id.' .about-main2 .licheng-bg .quxian{position: absolute;
                    bottom: 0;
                    top: 0;
                    right: 0;
                    left: 0;
                    margin: auto;
                    width: 98%;}
                '.$addon_id.' .about-main2 .ny-title{padding-top: 80px;}
                '.$addon_id.' .ny-title .title-en{font-size: 36px; color: #b4b4b4; font-family: arial; line-height: 36px; font-weight: normal;}
                '.$addon_id.' .ny-title .title-ch{font-size: 26px; color: #333333; margin: 15px 0; font-weight: normal;}
                '.$addon_id.' .ny-title i{display: block; width: 42px; height: 1px; background-color: #b5b5b5;}
                '.$addon_id.' .about-main2 .licheng-content{position: absolute;width: 100%;height: 100%;}
                '.$addon_id.' .about-main2 .licheng-content .swiper-wrapper,.about-main2 .licheng-content .swiper-slide,.about-main2 .licheng-content ul{width: 100%;}';

                if($type3_item_number%2==0){
                    $output .=$addon_id.' .about-main2 .licheng-content ul li{width: calc( 100% / ( ( '.$type3_item_number.' + 1 ) / 2 ) );float: left;height:100%;}';
                    $output .=$addon_id.' .about-main2 .licheng-content ul.first{margin-left:calc( ( 100% / ( ('.$type3_item_number.' + 1 ) / 2 ) / 2 ) );}';

                }else{
                    $output .=$addon_id.' .about-main2 .licheng-content ul .item1{width: calc( 100% / (( '.$type3_item_number.' + 1 ) / 2 ) );float: left;height:100%;}';
                    $output .=$addon_id.' .about-main2 .licheng-content ul .item2{width: calc( 100% / (( '.$type3_item_number.' + 1 ) / 2 ) );float: left;height:100%;}';
                    $output .=$addon_id.' .about-main2 .licheng-content ul.first{margin-left:calc( ( 100% / (( '.$type3_item_number.' + 1 ) / 2 ) / 2 ) );}';

                }

                $output .=
                $addon_id.' .about-main2 .licheng-content ul li a{display: block;width: 100%;position: relative;height: 323px}
                '.$addon_id.' .about-main2 .licheng-content ul li .date{color: '.$type3_title_color.';font-size: '.$type3_title_fontsize.'px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item2 .line,.about-main2 .licheng-content ul li.item4 .line{margin-top: 30px;}
                '.$addon_id.' .about-main2 .licheng-content ul li .lc-left{text-align: center;height:100%;}
                '.$addon_id.' .about-main2 .licheng-content ul li .line{height: 250px;width: 1px;display: block;background-color: #0A3875;position: relative;left: 30px;}
                '.$addon_id.' .about-main2 .licheng-content ul li .lc-right{position: absolute;left: 30px;width: 400px;height:100%;display: table;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item5 .lc-right{width: 200px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item2 .lc-right, .about-main2 .licheng-content ul li.item4 .lc-right{width: 400px;}
                '.$addon_id.' .about-main2 .licheng-content ul li .lc-right .right-text{padding: 20px;display: table-cell;vertical-align: middle;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item1 a{top: 72px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item1 .lc-right{bottom: 68px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item2 a{top: 0px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item2 .lc-right{top: 80px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item3 a{top: 136px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item3 .lc-right{bottom: 100px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item4 a{top: 278px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item4 .lc-right{top: 70px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item5 a{top: 99px;}
                '.$addon_id.' .about-main2 .licheng-content ul li.item5 .lc-right{bottom: 100px;}
                '.$addon_id.' .about-main2 .licheng-content .right-text h3{font-size: 24px;margin-bottom: 10px;}
                '.$addon_id.' .about-main2 .licheng-content .right-text p{font-size: 16px;}
                '.$addon_id.' .lc-swiper-btn{width: 100%;position: absolute;top: '.$type3_jt_top.'px;z-index:5;}
                '.$addon_id.' .lc-swiper-btn a{display: block;position: absolute;}
                '.$addon_id.' .lc-swiper-btn .btn-prev{position: absolute;left: 0px;top: 67px;width:50px;height:50px;}
                '.$addon_id.' .lc-swiper-btn .btn-next{position: absolute;right: 0;top: 67px;width:50px;height:50px;}
                '.$addon_id.' .lc-swiper-btn .iconfont{font-size: 30px;}
                '.$addon_id.' .dwi img,.dwis img{width:auto;}
                '.$addon_id.' .lc-swiper-btn .btn-prev img,.btn-next img{width:100%;}
                '.$addon_id.' .swiper-container{margin:0 auto;position:relative;overflow:hidden;direction:ltr;-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;-ms-backface-visibility:hidden;-o-backface-visibility:hidden;backface-visibility:hidden;z-index:1}.swiper-wrapper{position:relative;width:100%;-webkit-transition-property:-webkit-transform, left, top;-webkit-transition-duration:0s;-webkit-transform:translate3d(0px,0,0);-webkit-transition-timing-function:ease;-moz-transition-property:-moz-transform, left, top;-moz-transition-duration:0s;-moz-transform:translate3d(0px,0,0);-moz-transition-timing-function:ease;-o-transition-property:-o-transform, left, top;-o-transition-duration:0s;-o-transform:translate3d(0px,0,0);-o-transition-timing-function:ease;-o-transform:translate(0px,0px);-ms-transition-property:-ms-transform, left, top;-ms-transition-duration:0s;-ms-transform:translate3d(0px,0,0);-ms-transition-timing-function:ease;transition-property:transform, left, top;transition-duration:0s;transform:translate3d(0px,0,0);transition-timing-function:ease;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}.swiper-free-mode > .swiper-wrapper{-webkit-transition-timing-function:ease-out;-moz-transition-timing-function:ease-out;-ms-transition-timing-function:ease-out;-o-transition-timing-function:ease-out;transition-timing-function:ease-out;margin:0 auto}.swiper-slide{float:left;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}.swiper-wp8-horizontal{-ms-touch-action:pan-y}.swiper-wp8-vertical{-ms-touch-action:pan-x}
                '.$addon_id.' .dwi{
                    position: absolute;
                    margin-top: 15px;
                    left: 0;
                    right: 0;
                    margin: auto;
                    bottom: 64px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;}
                '.$addon_id.' .dwyq{width:20px!important;height:22px;}
                '.$addon_id.' .jiej{color:#fff;transition:all 0.3s;position: relative;top:0px;height:30px;font-size:'.$type3_cont_fontsize.'px}
                '.$addon_id.' .date{transition:all 0.3s;position: relative;top:0px;height:30px;display: inline-block;}
                '.$addon_id.' .dwyqs{width:20px!important;height:22px;}
                '.$addon_id.' .dwis{
                    position: relative;
                    margin-top: 15px;
                    left: 0;
                    right: 0;
                    margin: auto;
                    top: -14px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;}
                '.$addon_id.' .dwsx{height:137px;transition: all 0.3s;width:8px!important;}
                '.$addon_id.' .dwi .dwsx{position:relative;bottom:34px;}
                '.$addon_id.' .dwis .dwsx{position:relative;top:0px;}

                '.$addon_id.' .item1:hover .dwsx{bottom:49px;}
                '.$addon_id.' .item1:hover .date{top:-15px;font-size:'.$type3_title_fontsizehv.'px!important;color:'.$type3_title_colorhv.'!important;;}
                '.$addon_id.' .item1:hover .jiej{top:-15px;font-size:'.$type3_cont_fontsizehv.'px;color:'.$type3_cont_colorhv.'!important;;}
                '.$addon_id.' .item2:hover .dwsx{top:15px;}
                '.$addon_id.' .item2:hover .date{top:15px;font-size:'.$type3_title_fontsizehv.'px!important;color:'.$type3_title_colorhv.'!important;;}
                '.$addon_id.' .item2:hover .jiej{top:15px;font-size:'.$type3_cont_fontsizehv.'px;color:'.$type3_cont_colorhv.'!important;}
            ';
        }
        elseif($show_type=='type4'){
            $addon_id = '#jwpf-addon-' . $this->addon->id;

            $pcbj_img=(isset($settings->pcbj_img) && $settings->pcbj_img) ? $settings->pcbj_img : 'https://oss.lcweb01.cn/joomla/20220726/7d25807010545e5d3b19b70dbe25501f.jpg';
            $sjbj_img=(isset($settings->sjbj_img) && $settings->sjbj_img) ? $settings->sjbj_img : 'https://oss.lcweb01.cn/joomla/20220726/f1ca3cef341601f0a70de33f96bc1774.jpg';
            $right_bg=(isset($settings->right_bg) && $settings->right_bg) ? $settings->right_bg : 'rgba(0,51,38,0.5)';

            $pc_zyjj=(isset($settings->pc_zyjj) && $settings->pc_zyjj) ? $settings->pc_zyjj : '100';

            $right_title4_color=(isset($settings->right_title4_color) && $settings->right_title4_color) ? $settings->right_title4_color : '#b3a36b';
            $right_title4_fontsize=(isset($settings->right_title4_fontsize) && $settings->right_title4_fontsize) ? $settings->right_title4_fontsize : '72';

            $left2_title4_color=(isset($settings->left2_title4_color) && $settings->left2_title4_color) ? $settings->left2_title4_color : '#ecd3a5';
            $left2_title4_fontsize=(isset($settings->left2_title4_fontsize) && $settings->left2_title4_fontsize) ? $settings->left2_title4_fontsize : '30';


            $output ='
                '.$addon_id.' .i100 {
                    overflow: hidden;
                }
                '.$addon_id.' .i100 > img {
                    width: 100%;
                }
                '.$addon_id.' .i300 > img {
                  width: 100%;
                  height: 100%;
                }
                @media only screen and (min-width: 1480px) {
                    '.$addon_id.' .madieer2-a0{width: 100%;height: 100%;position: fixed;bottom: 0;left: 0;z-index: -2;}
                    '.$addon_id.' .madieer2-a0{background-image: url('.$pcbj_img.');background-repeat: repeat-y;background-size: 100% auto;}
                    '.$addon_id.' .madieer2-a0>div{width: 1920px;position: absolute;bottom: 0;left: calc(50% - 1920px/2);}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(1){display: block;}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(2){display: none;}
                    '.$addon_id.' .madieer2-a1{width: 100%;padding: 95px '.$pc_zyjj.'px 80px;position: relative;z-index: 1;overflow: hidden;background: url('.$pcbj_img.')no-repeat center center/100% 100%;}
                    '.$addon_id.' .madieer2-a2{width: 100%;position: relative;margin: 0 auto;}
                    '.$addon_id.' .madieer2-b1{width: 40%;height: 634px;position: relative;float: left;}
                    '.$addon_id.' .madieer2-b2{width: 100%;height: 180px;position: relative;margin-bottom: calc((634px - 180px*2 - 230px)/2);background: #000;}
                    '.$addon_id.' .madieer2-b3{width: 100%;height: 100%;position: relative;opacity: 0.4;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b2:hover .madieer2-b3{opacity: 1;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b3 img{min-height: 100%;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b2:hover .madieer2-b3 img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b4{font-size: 44px;line-height: 44px;color: #fff;text-align: center;font-style: italic;width: 100%;position: absolute;top: 46px;left: 0;}
                    '.$addon_id.' .madieer2-b4 strong{font-size: 24px;vertical-align: middle;}
                    '.$addon_id.' .madieer2-b5{font-size: 24px;line-height: 24px;color: #fff;text-align: center;width: 100%;position: absolute;top: 106px;left: 0;}
                    '.$addon_id.' .madieer2-b6{width: 100%;height: 230px;position: relative;margin-bottom: calc((634px - 180px*2 - 230px)/2);}
                    '.$addon_id.' .madieer2-b7{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .madieer2-b7 img{min-height: 100%;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b6:hover .madieer2-b7 img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b8{width: 102px;position: absolute;top: calc(50% - 149px/2);left: 102px;}
                    '.$addon_id.' .madieer2-b6:hover .madieer2-b8{transform: rotateY(360deg);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b9{font-size: '.$left2_title4_fontsize.'px;line-height: '.$left2_title4_fontsize.'px;color: '.$left2_title4_color.';position: absolute;top: 70px;left: 252px;}
                    '.$addon_id.' .madieer2-b10{height: 35px;position: absolute;top: 122px;left: 252px;}
                    '.$addon_id.' .madieer2-b11{width: 0;height: 0;border-left: 12px solid '.$right_title4_color.';border-top: 11px solid transparent;border-bottom: 11px solid transparent;position: absolute;left: 100%;top: calc(50% - 11px);display: none;}
                    '.$addon_id.' .madieer2-b2.on1 .madieer2-b11{display: block;}
                    '.$addon_id.' .madieer2-c1{width: 57%;height: 634px;position: relative;float: right;background: '.$right_bg.';}
                    '.$addon_id.' .madieer2-c2{width: 100%;height: 100%;position: absolute;top: 0;left: 0;display: none;}
                    '.$addon_id.' .madieer2-c3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .madieer2-c4{width: 100%;height: 100%;position: absolute;top: 0;left: 0;padding: 44px 80px 0;display: none;}
                    '.$addon_id.' .madieer2-c5{line-height: '.$right_title4_fontsize.'px;color: '.$right_title4_color.';text-align: center;font-style: italic;margin-bottom: 20px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(1){font-size: '.$right_title4_fontsize.'px;margin-right: 10px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(2){font-size: 24px;font-weight: bold;}
                    '.$addon_id.' .madieer2-c6{width: 84%;height: 300px;position: relative;margin: 0 auto;margin-bottom: 28px;}
                    '.$addon_id.' .madieer2-c6 img{width:100%;height: 100%;transition: 0.5s;object-fit:contain;}
                    '.$addon_id.' .madieer2-c6:hover img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-c7{width: 100%;font-size: 22px;line-height: 40px;color: #fff;text-align: center;font-weight: bold;margin-bottom: 8px;}
                    '.$addon_id.' .madieer2-c8{width: 100%;font-size: 16px;line-height: 30px;color: #fff;text-align: center;}
                    '.$addon_id.' .madieer2-c9{left: 48px;}
                    '.$addon_id.' .madieer2-c10{right: 48px;}
                    '.$addon_id.' .madieer2-c11{width: 58px;height: 58px;position: relative;border-radius: 50%;position: absolute;top: 310px;}
                    '.$addon_id.' .madieer2-c11 img{position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(1){opacity: 1;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(2){opacity: 0;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11:hover img:nth-child(1){opacity: 0;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11:hover img:nth-child(2){opacity: 1;transition: 0.5s;}
                }
                @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                    '.$addon_id.' .madieer2-a0{width: 100%;height: 100%;position: fixed;bottom: 0;left: 0;z-index: -2;}
                    '.$addon_id.' .madieer2-a0{background-image: url('.$sjbj_img.');background-repeat: repeat-y;background-size: 100% auto;}
                    '.$addon_id.' .madieer2-a0>div{width: 1480px;position: absolute;bottom: 0;left: calc(50% - 1480px/2);}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(1){display: block;}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(2){display: none;}
                    '.$addon_id.' .madieer2-a1{width: 100%;padding: 95px '.$pc_zyjj.'px 80px;position: relative;z-index: 1;overflow: hidden;background: url('.$pcbj_img.')no-repeat center center/100% 100%;}
                    '.$addon_id.' .madieer2-a2{width: 100%;position: relative;margin: 0 auto;}
                    '.$addon_id.' .madieer2-b1{width: 40%;height: 420px;position: relative;float: left;}
                    '.$addon_id.' .madieer2-b2{width: 100%;height: 114px;position: relative;margin-bottom: calc((420px - 114px*2 - 144px)/2);background: #000;}
                    '.$addon_id.' .madieer2-b3{width: 100%;height: 100%;position: relative;opacity: 0.4;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b2:hover .madieer2-b3{opacity: 1;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b3 img{min-height: 100%;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b2:hover .madieer2-b3 img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b4{font-size: 36px;line-height: 36px;color: #fff;text-align: center;font-style: italic;width: 100%;position: absolute;top: 20px;left: 0;}
                    '.$addon_id.' .madieer2-b4 strong{font-size: 18px;vertical-align: middle;}
                    '.$addon_id.' .madieer2-b5{font-size: 18px;line-height: 18px;color: #fff;text-align: center;width: 100%;position: absolute;top: 74px;left: 0;}
                    '.$addon_id.' .madieer2-b6{width: 100%;height: 144px;position: relative;margin-bottom: calc((420px - 114px*2 - 144px)/2);}
                    '.$addon_id.' .madieer2-b7{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .madieer2-b7 img{min-height: 100%;transition: 0.5s;}
                    '.$addon_id.' .madieer2-b6:hover .madieer2-b7 img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b8{width: 70px;position: absolute;top: calc(50% - 102px/2);left: 40px;}
                    '.$addon_id.' .madieer2-b6:hover .madieer2-b8{transform: rotateY(360deg);transition: 0.5s;}
                    '.$addon_id.' .madieer2-b9{font-size: 22px;line-height: 22px;color: '.$left2_title4_color.';position: absolute;top: 40px;left: 150px;}
                    '.$addon_id.' .madieer2-b10{height: 30px;position: absolute;top: 80px;left: 150px;}
                    '.$addon_id.' .madieer2-b11{width: 0;height: 0;border-left: 12px solid '.$right_title4_color.';border-top: 11px solid transparent;border-bottom: 11px solid transparent;position: absolute;left: 100%;top: calc(50% - 11px);display: none;}
                    '.$addon_id.' .madieer2-b2.on1 .madieer2-b11{display: block;}
                    '.$addon_id.' .madieer2-c1{width: 57%;height: 420px;position: relative;float: right;background: '.$right_bg.';}
                    '.$addon_id.' .madieer2-c2{width: 100%;height: 100%;position: absolute;top: 0;left: 0;display: none;}
                    '.$addon_id.' .madieer2-c3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .madieer2-c4{width: 100%;height: 100%;position: absolute;top: 0;left: 0;padding: 24px 40px 0;display: none;}
                    '.$addon_id.' .madieer2-c5{line-height: 48px;color: '.$right_title4_color.';text-align: center;font-style: italic;margin-bottom: 10px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(1){font-size: 48px;margin-right: 6px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(2){font-size: 18px;font-weight: bold;}
                    '.$addon_id.' .madieer2-c6{width: 100%;height: 178px;position: relative;margin: 0 auto;margin-bottom: 20px;}
                    '.$addon_id.' .madieer2-c6 img{width:100%;height: 100%;transition: 0.5s;object-fit:contain;}
                    '.$addon_id.' .madieer2-c6:hover img{transform: scale(1.08);transition: 0.5s;}
                    '.$addon_id.' .madieer2-c7{width: 100%;font-size: 18px;line-height: 30px;color: #fff;text-align: center;font-weight: bold;margin-bottom: 8px;}
                    '.$addon_id.' .madieer2-c8{width: 100%;font-size: 14px;line-height: 24px;color: #fff;text-align: center;}
                    '.$addon_id.' .madieer2-c9{left: 30px;}
                    '.$addon_id.' .madieer2-c10{right: 30px;}
                    '.$addon_id.' .madieer2-c11{width: 44px;height: 44px;position: relative;border-radius: 50%;position: absolute;top: 200px;}
                    '.$addon_id.' .madieer2-c11 img{position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(1){opacity: 1;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(2){opacity: 0;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11:hover img:nth-child(1){opacity: 0;transition: 0.5s;}
                    '.$addon_id.' .madieer2-c11:hover img:nth-child(2){opacity: 1;transition: 0.5s;}
                }
                @media only screen and (max-width: 1023px) {
                    '.$addon_id.' .madieer2-a0{width: 100%;height: 100%;position: fixed;bottom: 0;left: 0;z-index: -2;}
                    '.$addon_id.' .madieer2-a0{background-image: url('.$pcbj_img.');background-repeat: repeat-y;background-size: 100% auto;}
                    '.$addon_id.' .madieer2-a0>div{width: 100%;position: absolute;bottom: 0;left: 0;}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(1){display: none;}
                    '.$addon_id.' .madieer2-a0>div>img:nth-child(2){display: block;}
                    '.$addon_id.' .madieer2-a1{width: 100%;padding: 43px 25px 116px;position: relative;z-index: 1;overflow: hidden;background: url('.$sjbj_img.')no-repeat center center/100% 100%;}
                    '.$addon_id.' .madieer2-a2{width: 100%;position: relative;margin: 0 auto;}
                    '.$addon_id.' .madieer2-b1{width: 100%;height: 93px;position: relative;margin-bottom: 27px;}
                    '.$addon_id.' .madieer2-b2{width: calc(50% - 5px);height: 93px;position: relative;background: #000;}
                    '.$addon_id.' .madieer2-b2:first-child{float: left;}
                    '.$addon_id.' .madieer2-b2:last-child{float: right;}
                    '.$addon_id.' .madieer2-b3{width: 100%;height: 100%;position: relative;opacity: 0.4;}
                    '.$addon_id.' .madieer2-b3 img{min-height: 100%;}
                    '.$addon_id.' .madieer2-b4{font-size: 22px;line-height: 22px;color: #fff;text-align: center;font-style: italic;width: 100%;position: absolute;top: 22px;left: 0;}
                    '.$addon_id.' .madieer2-b4 strong{font-size: 12px;vertical-align: middle;}
                    '.$addon_id.' .madieer2-b5{font-size: 13px;line-height: 13px;color: #fff;text-align: center;width: 100%;position: absolute;top: 54px;left: 0;}
                    '.$addon_id.' .madieer2-b6{display: none;}
                    '.$addon_id.' .madieer2-b11{width: 0;height: 0;border-top: 9px solid '.$right_title4_color.';border-left: 9px solid transparent;border-right: 9px solid transparent;position: absolute;top: 100%;left: calc(50% - 9px);display: none;}
                    '.$addon_id.' .madieer2-b2.on1 .madieer2-b11{display: block;}
                    '.$addon_id.' .madieer2-c1{width: 100%;height: 384px;position: relative;background: '.$right_bg.';}
                    '.$addon_id.' .madieer2-c2{width: 100%;height: 100%;position: absolute;top: 0;left: 0;display: none;}
                    '.$addon_id.' .madieer2-c3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .madieer2-c4{width: 100%;height: 100%;position: absolute;top: 0;left: 0;padding: 35px 31px 0;display: none;}
                    '.$addon_id.' .madieer2-c5{line-height: 41px;color: '.$right_title4_color.';text-align: center;font-style: italic;margin-bottom: 5px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(1){font-size: 41px;margin-right: 5px;}
                    '.$addon_id.' .madieer2-c5 span:nth-child(2){font-size: 13px;font-weight: bold;}
                    '.$addon_id.' .madieer2-c6{width: 100%;height: 156px;position: relative;margin: 0 auto;margin-bottom: 18px;}
                    '.$addon_id.' .madieer2-c6 img{height: 100%;width:100%;object-fit:contain;}
                    '.$addon_id.' .madieer2-c7{width: 100%;font-size: 15px;line-height: 20px;color: #fff;text-align: center;font-weight: bold;margin-bottom: 12px;}
                    '.$addon_id.' .madieer2-c8{width: 100%;font-size: 13px;line-height: 25px;color: #fff;text-align: center;}
                    '.$addon_id.' .madieer2-c9{left: calc(50% - 45px);}
                    '.$addon_id.' .madieer2-c10{right: calc(50% - 45px);}
                    '.$addon_id.' .madieer2-c11{width: 36px;height: 36px;position: relative;border-radius: 50%;position: absolute;bottom: -60px;}
                    '.$addon_id.' .madieer2-c11 img{position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(1){opacity: 1;}
                    '.$addon_id.' .madieer2-c11 img:nth-child(2){opacity: 0;}
                }
            ';

        }elseif($show_type=='type5'){
            $addon_id = '#jwpf-addon-' . $this->addon->id;
            $output ='


                '.$addon_id.' ul, ol {
                    list-style-type: none;
                }
                '.$addon_id.' *{padding:0px;margin:0px;}

                '.$addon_id.' .page_info2 {
                    background-color: #fff;
                }

                '.$addon_id.' .page_info2 .jianjie {
                    padding: 38px 0 70px 0;
                    color: #454545;
                    font-size: 14px;
                    line-height: 32px;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg ul {
                    width: 1017px;
                    overflow: hidden;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg li {
                    width: 249px;
                    float: left;
                    background: #fff;
                    margin-right: 5px;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg li div,
                '.$addon_id.' .page_info2 .jianjie .jimg li p {
                    width: 100%;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg li h4 {
                    text-align: center;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg li span {
                    font-size: 16px;
                    font-weight: bold;
                    color: #134d95;
                    /* float:left; */
                    margin-left: 0 !important;
                }

                '.$addon_id.' .page_info2 .jianjie .jimg li p {
                    float: left;
                    /* margin-left:85px; */
                }
                '.$addon_id.' .wrap {
                    width: 1007px;
                    margin: 0 auto;
                }
                '.$addon_id.' .page_info2 .rongyu {
                    font-size: 14px;
                    padding: 23px 0 85px 0;
                }

                '.$addon_id.' .page_info2 .rongyu_a {
                    width: 1030px;
                    margin-left: -20px;
                    zoom: 1;
                    overflow: hidden;
                    display: flex;
                }

                '.$addon_id.' .page_info2 .rongyu_a li {
                    width: 237px;
                    height: auto;
                    background-color: #fff;
                    margin-left: 20px;
                    float: left;
                    padding-bottom: 10px;
                }

                '.$addon_id.' .page_info2 .rongyu_a li h4 {
                    height: 158px;
                    text-align: center;
                    margin-bottom: 12px;
                }

                '.$addon_id.' .page_info2 .rongyu_a li h4 img {
                    width: 100%;
                    height: 158px;
                    object-fit: cover;
                }

                '.$addon_id.' .page_info2 .rongyu.sp .rongyu_a li h4 img {
                    object-fit: contain;
                }

                '.$addon_id.' .page_info2 .rongyu_a li span {
                    width: 56px;
                    height: 55px;
                    float: left;
                    background: url("https://oss.lcweb01.cn/joomla/20230131/5943ff5c8fc75224f3477ddfc1e2f57a.jpg");
                    line-height: 37px;
                    color: #fff;
                    text-align: center;
                    position: relative;
                    left: 0;
                    background-repeat: no-repeat;
                    background-position: center center;
                }

                '.$addon_id.' .page_info2 .rongyu_a li p {
                    width: 163px;
                    float: left;
                    /* height:50px; */
                    line-height: 25px;
                    margin-left: 8px;
                }

                '.$addon_id.' .page_info2 .rongyu_b,
                '.$addon_id.' .page_info2 .licheng_a {
                    position: relative;
                    padding: 38px 0 0 0;
                }

                '.$addon_id.' .page_info2 .rongyu_b .bd,
                '.$addon_id.' .page_info2 .licheng_a .bd {
                    background: url("https://oss.lcweb01.cn/joomla/20230131/2af8f9b100520f7f7b8a5657b13c45c4.jpg") repeat-x 0 8px;
                    height: 70px;
                    padding-right: 100px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .bd li,
                '.$addon_id.' .page_info2 .licheng_a .bd li {
                    float: left;
                    margin-left: 86px;
                    margin-right: 27px;
                    padding-top: 25px;
                    font-weight: 700;
                    cursor: pointer;
                    width: 50px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .bd .on,
                '.$addon_id.' .page_info2 .licheng_a .bd .on {
                    color: #134d95;
                    background: url("https://oss.lcweb01.cn/joomla/20230131/abb7c97d71d96bc063a08c4a947c9a11.jpg") no-repeat 0px 8px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .prev,
                '.$addon_id.' .page_info2 .licheng_a .prev {
                    background: url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat 0px 0px;
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    position: absolute;
                    left: -30px;
                    cursor: pointer;
                    top: 38px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .next,
                '.$addon_id.' .page_info2 .licheng_a .next {
                    background: url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat -56px 0px;
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    position: absolute;
                    right: -30px;
                    cursor: pointer;
                    top: 38px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .prev:hover,
                '.$addon_id.' .page_info2 .licheng_a .prev:hover {
                    background: #e9e9e9 url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat 0px -118px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .next:hover,
                '.$addon_id.' .page_info2 .licheng_a .next:hover {
                    background: #e9e9e9 url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat -56px -118px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .prevStop:hover,
                '.$addon_id.' .page_info2 .licheng_a .prevStop:hover {
                    background: url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat 0px -56px;
                }

                '.$addon_id.' .page_info2 .rongyu_b .nextStop:hover,
                '.$addon_id.' .page_info2 .licheng_a .nextStop:hover {
                    background: url("https://oss.lcweb01.cn/joomla/20230131/e930f31b16889ac578c2bde44bee0f55.jpg") no-repeat -56px -56px;
                }

                '.$addon_id.' .page_info2 .rongyu_c {
                    zoom: 1;
                    overflow: hidden;
                }

                '.$addon_id.' .page_info2 .rongyu_title {
                    zoom: 1;
                    overflow: hidden;
                }

                '.$addon_id.' .page_info2 .rongyu_title h2 {
                    width: 448px;
                    float: left;
                    margin-right: 5px;
                    font-size: 24px;
                    font-weight: 700;
                    padding: 10px 0 25px 30px;
                }

                '.$addon_id.' .page_info2 .rongyu_c ul {
                    display: none;
                }

                '.$addon_id.' .page_info2 .rongyu_c li {
                    width: 448px;
                    float: left;
                    margin-right: 5px;
                    padding-left: 30px;
                    line-height: 35px;
                }

                '.$addon_id.' .page_info2 .rongyu_c .wt {
                    background-color: #fff;
                }

                '.$addon_id.' .page_info2 .licheng_tab {
                    padding-top: 40px;
                    zoom: 1;
                    overflow: hidden;
                }

                '.$addon_id.' .page_info2 .licheng_tab li {
                    width: 250px;
                    float: left;
                    height: 68px;
                    border-bottom: 2px solid #f2f2f2;
                    border-right: 1px solid #f2f2f2;
                    text-align: center;
                    padding-top: 10px;
                    background-color: #fff;
                    cursor: pointer;
                }

                '.$addon_id.' .page_info2 .licheng_tab li p {
                    color: #5e5e5e;
                    font-size: 14px;
                }

                '.$addon_id.' .page_info2 .licheng_tab li span {
                    color: #94b0ca;
                }

                '.$addon_id.' .page_info2 .licheng_tab .on {
                    width: 250px;
                    background: url("https://oss.lcweb01.cn/joomla/20230130/3be32e64df774aaa0c793373995aec93.jpg") no-repeat center top 5px;
                    height: 78px;
                    border-bottom: none;
                    border-right: none;
                }

                '.$addon_id.' .page_info2 .licheng_tab .on p {
                    color: #fff;
                }

                '.$addon_id.' .page_info2 .licheng_tab .on span {
                    color: #fff;
                }

                '.$addon_id.' .page_info2 .licheng_b li {
                    display: none;
                    padding: 29px;
                    background-color: #f4f4f4;
                }

                '.$addon_id.' .page_info2 .licheng_b li p {
                    padding-top: 10px;
                }

                '.$addon_id.' .page_info2 .licheng_info>ul>li {
                    display: none;
                    width:100%;
                }
                '.$addon_id.' .xbt{font-size:16px;}

                @media only screen and (max-width: 768px) {
                    '.$addon_id.' .wrap {
                        width:100%;
                    }
                    '.$addon_id.' .page_info2 .rongyu_a{width:100%}
                    '.$addon_id.' .page_info2 .rongyu_a li{width:calc( 50% - 20px )}
                    '.$addon_id.' .page_info2 .licheng_tab .on{
                        background: url("https://oss.lcweb01.cn/joomla/20230130/3be32e64df774aaa0c793373995aec93.jpg") no-repeat center top 0px;
                        height:68px;
                    }
                    '.$addon_id.' .page_info2 .rongyu{
                        padding:23px 0 25px 0;
                    }
                    '.$addon_id.' .page_info2 .rongyu_a{
                        display:inherit;
                    }
                    '.$addon_id.' .page_info2 .licheng_tab li{
                        width:50%;
                    }
                    '.$addon_id.' .page_info2 .licheng_tab .on{width:50%;}
                    '.$addon_id.' .page_info2 .rongyu_b .prev, '.$addon_id.' .page_info2 .licheng_a .prev{
                        left:0px;
                    }
                    '.$addon_id.' .page_info2 .rongyu_b .next, '.$addon_id.' .page_info2 .licheng_a .next{
                        right:0px;
                    }
                    '.$addon_id.' .xbt{font-size:14px;}
                }
            ';

        }
        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }
}
