<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonPie_chart extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;


        $pie_width = (isset($settings->pie_width)) ? $settings->pie_width : '600';//饼图宽高
        $pie_startAngle = (isset($settings->pie_startAngle)) ? $settings->pie_startAngle : '120';//起始角度
        $pie_border = (isset($settings->pie_border)) ? $settings->pie_border : '10';//边框大小
        $piesm_zb = (isset($settings->piesm_zb)) ? $settings->piesm_zb : '30';//中心圆占比
        $piebig_wz = (isset($settings->piebig_wz)) ? $settings->piebig_wz : 'right';//文字位置
        $piebig_wzpy = (isset($settings->piebig_wzpy)) ? $settings->piebig_wzpy : '70';//文字位置偏移
        $piebig_xz = (isset($settings->piebig_xz)) ? $settings->piebig_xz : 'tangential';//文字旋转
        $piesm_wzpy = (isset($settings->piesm_wzpy)) ? $settings->piesm_wzpy : '-30';//图标偏移
        $piebig_wzpyzy = (isset($settings->piebig_wzpyzy)) ? $settings->piebig_wzpyzy : '-60';//文字左右偏移

        if($piebig_wz=='right'){
            $pieweiz='insideRight';
        }else{
            $pieweiz='insideLeft';
        }
        // 序号文字属性
        $xhbold_checked = (isset($settings->xhbold_checked)) ? $settings->xhbold_checked : '1';
        if($xhbold_checked==1){
            $xhjs='bold';
        }else{
            $xhjs='normal';
        }
        $xhtitle_fontsize = (isset($settings->xhtitle_fontsize)) ? $settings->xhtitle_fontsize : '46';
        $xhtitle_color = (isset($settings->xhtitle_color)) ? $settings->xhtitle_color : '#fff';

        // 中文文字属性
        $chinabold_checked = (isset($settings->chinabold_checked)) ? $settings->chinabold_checked : '1';
        if($chinabold_checked==1){
            $chinajs='bold';
        }else{
            $chinajs='normal';
        }
        $chinatitle_fontsize = (isset($settings->chinatitle_fontsize)) ? $settings->chinatitle_fontsize : '16';
        $chinatitle_color = (isset($settings->chinatitle_color)) ? $settings->chinatitle_color : '#fff';
        $chinatitle_lineheight = (isset($settings->chinatitle_lineheight)) ? $settings->chinatitle_lineheight : '25';
        // 英文文字属性
        $engbold_checked = (isset($settings->engbold_checked)) ? $settings->engbold_checked : '0';
        if($engbold_checked==1){
            $engjs='bold';
        }else{
            $engjs='normal';
        }
        $engtitle_fontsize = (isset($settings->engtitle_fontsize)) ? $settings->engtitle_fontsize : '14';
        $engtitle_color = (isset($settings->engtitle_color)) ? $settings->engtitle_color : '#fff';
        $engtitle_lineheight = (isset($settings->chinatitle_lineheight)) ? $settings->engtitle_lineheight : '25';
        $pieid='pies_'.$this->addon->id;

        $output = "";
            
            $output .='<style>
                '.$addon_id.' #'.$pieid.'{
                    width:'.$pie_width.'px;
                    height:'.$pie_width.'px;
                    margin:0 auto;
                }
            </style>';
            $output .= '<div id="'.$pieid.'"></div>';
            $smbgcolor=[];
            $smbgimg=[];
            $smbgimg1=[];
            $smtitle=[];
            $smsx=[];

            $bigdata=[];
            $bigdataxr=[];

            $NameArr  = [];//小圆数据
            if (isset($settings->jw_carousel_item) && count((array)$settings->jw_carousel_item)) {
                foreach ($settings->jw_carousel_item as $key => $value) {
                    // 内圆背景
                    $cos=(isset($value->piesm_bgcolor) && $value->piesm_bgcolor) ? $value->piesm_bgcolor : 'rgba(255,255,255,0.2)';
                    $smbgcolor[]="'".$cos."'";
                    // 内圆图片
                    $bgimg=(isset($value->piesm_img) && $value->piesm_img) ? $value->piesm_img : 'https://oss.lcweb01.cn/joomla/20210818/7946af25c7d1940abd738932e71d3bc8.png';
                    $smbgimg[]="'a".$key."':'".$bgimg."'";
                    $smbgimg1[]="case '".$key."':str='{a".$key."|}';break";
                    // 内圆占比
                    $smzb=(isset($value->piesm_zb) && $value->piesm_zb) ? $value->piesm_zb : '15';
                    $NameArr[$key]['val']=$smzb;
                    $NameArr[$key]['name']=$key;

                    $smtitle[]="{value:".$smzb.",name:'".$key."'}";
                    // 内圆小图宽高
                    $piesm_imgwidth=(isset($value->piesm_imgwidth) && $value->piesm_imgwidth) ? $value->piesm_imgwidth : '30';
                    $piesm_imgheight=(isset($value->piesm_imgheight) && $value->piesm_imgheight) ? $value->piesm_imgheight : '30';
                    $smsx[]="a".$key.":{width:".$piesm_imgwidth.",height:".$piesm_imgheight.",lineHeight:30,backgroundColor: {image: sportsIcon.a".$key."},align:'center'}";

                    // 外圆
                    $pieqk_zb=(isset($value->pieqk_zb) && $value->pieqk_zb) ? $value->pieqk_zb : '15';//扇形大小
                    $qkbg_checked=(isset($value->qkbg_checked) && $value->qkbg_checked) ? $value->qkbg_checked : 'color1';//使用背景色
                    $pie_bgcolor=(isset($value->pie_bgcolor) && $value->pie_bgcolor) ? $value->pie_bgcolor : '#FF5A4D';//使用背景色
                    $pie_bgcolor1=(isset($value->pie_bgcolor1) && $value->pie_bgcolor1) ? $value->pie_bgcolor1 : '#FF6BDA';//使用背景色
                    $pie_bgcolor2=(isset($value->pie_bgcolor2) && $value->pie_bgcolor2) ? $value->pie_bgcolor2 : '#FB6ED1';//使用背景色

                    $title=(isset($value->title) && $value->title) ? $value->title : '01';//序号
                    $china_title=(isset($value->china_title) && $value->china_title) ? $value->china_title : '解决方案';//序号
                    $english_title=(isset($value->english_title) && $value->english_title) ? $value->english_title : 'Solution';//序号

                    if($qkbg_checked=='color1'){
                        $bigdata[]="{value:".$pieqk_zb.",name:'".$title."',itemStyle:{color:new echarts.graphic.LinearGradient(0, 0, 0, 1,[{offset:0,color:'".$pie_bgcolor1."'},{offset:1,color:'".$pie_bgcolor2."'}])},},";
                    }else{
                        $bigdata[]="{value:".$pieqk_zb.",name:'".$title."',itemStyle:{color:'".$pie_bgcolor."'},},";
                    }

                    $bigdataxr[]="case '".$title."':str='{rate|".$title."} '+' {nameStyle1|".$china_title."}\\n{nameStyle| \\xa0\\xa0\\xa0\\xa0 ".$english_title."}';break;";

                    // 链接
                    $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;                     
                }
                if($NameArr){
                    $dd=rsort($NameArr);
                }
                // echo "<pre>";
                // print_r($NameArr);
                // echo "</pre>";
                $comma_separated ='';
                $comma_separated1 ='';
                $comma_separated2 ='';
                $smname ='';
                $smttsx ='';
                $bigda ='';
                $bigdaxr ='';

                if($smbgcolor){
                    $comma_separated = implode(",", $smbgcolor); 
                }
                if($smbgimg){
                    $comma_separated1 = implode(",", $smbgimg); 
                }
                if($smbgimg1){
                    $comma_separated2 = implode(";", $smbgimg1); 
                }
                if($smtitle){
                    $smname = implode(",", $smtitle); 
                }
                if($smsx){
                    $smttsx = implode(",", $smsx).","; 
                }
                if($bigdata){
                    $bigda = implode("", $bigdata); 
                }
                if($bigdataxr){
                    $bigdaxr = implode("", $bigdataxr); 
                }

            }  
            $output .="

                <script type=\"text/javascript\">
                    
                    var  colorList1=[".$comma_separated."];
                    var sportsIcon = {
                        ".$comma_separated1."
                    };
                    var dom = document.getElementById('".$pieid."');
                    console.log(dom);
                    var myChart = echarts.init(dom);
                    var option;
                    option = {

                        silent: true,
                        series: [
                            
                            {
                                radius: ['0%', '100%'],
                                center: ['50%', '50%'],
                                type: 'sunburst',
                                startAngle: ".$pie_startAngle.",
                                minAngle: 15,
                                nodeClick:'link',
                                sort:'null',
                                z:1,
                                label: {
                                    fontSize: 16,
                                    color: '#fff',
                                    normal:{
                                        padding:[0,".$piebig_wzpyzy."],
                                        formatter: function(params){
                                            var str = '';
                                            switch(params.name){
                                                ".$bigdaxr."
                                            }
                                            return str
                                        },
                                        rich: {
                                            rate: {
                                                fontSize: ".$xhtitle_fontsize.",
                                                color: '".$xhtitle_color."',
                                                fontWeight:'".$xhjs."',
                                                textShadowColor:'transparent',
                                            },
                                            
                                            nameStyle1: {
                                                fontSize: ".$chinatitle_fontsize.",
                                                color: '".$chinatitle_color."',
                                                align: 'center',
                                                fontWeight:'".$chinajs."',
                                                lineHeight:".$chinatitle_lineheight.",
                                            },
                                            nameStyle: {
                                                fontSize: ".$engtitle_fontsize.",
                                                color: '".$engtitle_color."',
                                                align: 'center',
                                                lineHeight:".$engtitle_lineheight.",
                                                fontWeight:'".$engjs."',
                                            },
                                            
                                        }
                                    }
                                },
                                levels: [
                                   {},
                                    
                                    {
                                        selectedMode: 'true',
                                        itemStyle: {
                                            color: '#fff'
                                        },
                                        label: {
                                            rotate: '".$piebig_xz."',
                                            align: '".$piebig_wz."',
                                            position: '".$pieweiz."',
                                            distance: '".$piebig_wzpy."' ,
                                        }
                                    }
                                  ],
                                itemStyle: {
                                    borderWidth: ".$pie_border.",
                                    color:'#fff',
                                },
                                data: [
                                       ".$bigda."
                                       
                                    ],
                            },
                            {
                                radius: ['0%', '".$piesm_zb."%'],
                                center: ['50%', '50%'],
                                type: 'pie',
                                startAngle: ".$pie_startAngle.",
                                z:2,
                                legendHoverLink:false,
                                hoverAnimation:false,
                                selectedOffset:0,
                                label: {
                                    position: 'inner',
                                    fontSize: 14,
                                    normal:{
                                        formatter: function(params){
                                            var str = '';
                                            switch(params.name){
                                                ".$comma_separated2."
                                                
                                            }
                                            return str
                                        },
                                        padding: [0, -20],
                                        rich: {
                                            ".$smttsx."
                                            
                                            textStyle: {
                                                fontSize: 20,
                                                color: '#1ab4b8',
                                                align: 'left'
                                            }
                                        }
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        color: function(params) {
                                            return colorList1[params.dataIndex]
                                        }
                                    }
                                },
                                labelLine: {
                                    normal: {
                                        show: false,
                                        length: ".$piesm_wzpy.",
                                        length2: 0,
                                        align: 'center'
                                    },
                                    emphasis: {
                                        show: false
                                    },
                                },
                                data: [
                                    ".$smname."
                                ],
                            },
                            
                        ]
                    };

                    myChart.setOption(option);

                    </script>
            ";        

            

        return $output;
    }
    public function scripts()
    {
        $scripts = array('https://zhjzt.china9.cn/new_client/js/<EMAIL>');
        return $scripts;
    }


    public static function getTemplate()
    {

        $output = "
            <style type='text/css'>
                #jwpf-addon-{{ data.id }} #pies{
                    width:{{data.pie_width}}px;
                    height:{{data.pie_width}}px;
                    margin:0 auto;
                }
            </style>
            <div id='pies'>
                <img src='https://oss.lcweb01.cn/joomla/20211218/3e17249a0a80739b8cf68e4738495a26.png' width='100%'>
            </div>
        ";

        return $output;
    }

}