
//写Cookie
function kkk(objValue) {
    var str = 'xx' + "=" + escape(objValue); //编码
    // if (objHours > 0) {//为0时不设定过期时间，浏览器关闭时cookie自动消失
    //     var date = new Date();
    //     var ms = objHours * 3600 * 1000;
    //     date.setTime(date.getTime() + ms);
    //     str += "; expires=" + date.toGMTString();
    // }
    document.cookie = str;
}
function qkkk(key)
{
    localStorage.getItem('xx') //取出缓存
}