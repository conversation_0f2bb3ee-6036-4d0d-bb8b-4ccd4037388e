<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'customize_tab',
        'title' => JText::_('自定义选项卡'),
        'desc' => JText::_('自定义选项卡'),
        'category' => '选项卡',
        'attr' => array(

            'art_type_selector' => array(
                'type' => 'select',
                'title' => '选择导航样式',
                'values' => array(
                    'type1' => '导航1',
                    'type2' => '导航2',
                ),
                'std' => 'type1'
            ),
            'nav_position' => array(
                'type' => 'select',
                'title' => '选择导航位置',
                'values' => array(
                    'flex-start' => '左',
                    'center' => '中',
                    'flex-end' => '右',
                ),
                'std' => 'flex-start',
                'depends' => array(array('art_type_selector', '=', 'type1')),
            ),

            // Repeatable Item 导航
            'jw_tab_item_cust' => array(
                'title' => JText::_('导航'),
                'attr' => array(
                    'content_style_settings' => array(
                        'type' => 'select',
                        'title' => JText::_('内容样式设置'),
                        'std' => 'style1',
                        'values' => array(
                            'style1' => '样式一',
                            'style2' => '样式二',
                        )
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => JText::_('导航标题'),
                        'desc' => JText::_(''),
                        'std' => '点我'
                    ),
                    'title_vice' => array(
                        'type' => 'text',
                        'title' => JText::_('导航副标题'),
                        'desc' => JText::_(''),
                        'std' => '点我',
                        'depends' => array(
                            array('content_style_settings', '!=', 'style1'),
                        ),
                    ),

                    'tit_cle' => array(
                        'type' => 'color',
                        'title' => '导航背景颜色',
                        'desc' => '',
                        'std' => '#000',
                        'depends' => array(
                            array('content_style_settings', '!=', 'style1'),
                        ),
                    ),

                    'tit_cle_click' => array(
                        'type' => 'color',
                        'title' => '导航点击时背景颜色',
                        'desc' => '',
                        'std' => '#000',
                        'depends' => array(
                            array('content_style_settings', '!=', 'style1'),
                        ),
                    ),

                    'nav_back_col'        => array(
                        'type'    => 'checkbox',
                        'title'   => '开启导航右侧边框',
                        'std'     => 0,
                        'depends' => array(
                            array('content_style_settings', '!=', 'style1'),
                        ),
                    ),
                    'tit_cle_bor' => array(
                        'type' => 'color',
                        'title' => '导航边右侧边框颜色',
                        'desc' => '',
                        'std' => '#000',
                        'depends' => array(
                            array('nav_back_col', '=', 1),
                            array('content_style_settings', '!=', 'style1'),
                        ),
                    ),
                    'nav_icon' => array(
                        'type' => 'media',
                        'title' => JText::_('图标'),
                        'std' => '/components/com_jwpagefactory/addons/customize_tab/assets/images/yuan.png',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                    'image1' => array(
                        'type' => 'media',
                        'title' => JText::_('图标1'),
                        'std' => 'https://oss.lcweb01.cn/joomla/20221020/c720f563d1ee8e42ae6b7d215599af15.png',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'image2' => array(
                        'type' => 'media',
                        'title' => JText::_('图标2'),
                        'std' => 'https://oss.lcweb01.cn/joomla/20221020/c720f563d1ee8e42ae6b7d215599af15.png',
                        'depends' => array(array('content_style_settings', '=', 'style1')),
                    ),
                ),
                'std' =>array(
                    array(
                        'title'=>'互联网思维',
                        'title_vice'=>'互联网思维改造传统体育产业',
                        'tit_cle'=>'#000',
                        'tit_cle_click'=>'#000',
                        'nav_back_col'=>0,
                        'tit_cle_bor'=>'#000',
                        'nav_icon'=>'https://oss.lcweb01.cn/joomla/20221020/c720f563d1ee8e42ae6b7d215599af15.png',
                        'image1'=>'https://oss.lcweb01.cn/joomla/20221020/c720f563d1ee8e42ae6b7d215599af15.png',
                        'image2'=>'https://oss.lcweb01.cn/joomla/20221020/c720f563d1ee8e42ae6b7d215599af15.png',
                    )
                ),
            ),


            //内容
            'content' => array(
                'title' => JText::_('内容'),
                'attr' => array(
                    'content_style_settings' => array(
                        'type' => 'select',
                        'title' => JText::_('内容样式设置'),
                        'std' => 'style1',
                        'values' => array(
                            'style1' => '样式一',
                            'style2' => '样式二',
                        )
                    ),

                    'title' => array(
                        'type' => 'text',
                        'title' => JText::_('标题'),
                        'desc' => JText::_('标题'),
                        'std' => '标题',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'title1' => array(
                        'type' => 'text',
                        'title' => JText::_('标题1'),
                        'std' => '标题1',
                    ),
                    'title2' => array(
                        'type' => 'text',
                        'title' => JText::_('标题2'),
                        'std' => '标题',
                    ),
                    'title3' => array(
                        'type' => 'text',
                        'title' => JText::_('标题3'),
                        'std' => '标题',
                    ),
                    'desc1' => array(
                        'type' => 'text',
                        'title' => JText::_('简介'),
                        'std' => '简介1',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'desc' => array(
                        'type' => 'text',
                        'title' => JText::_('简介1'),
                        'desc' => JText::_('简介'),
                        'std' => '简介1',

                    ),
                    'desc2' => array(
                        'type' => 'text',
                        'title' => JText::_('简介2'),
                        'desc' => JText::_('简介'),
                        'std' => '简介1',

                    ),
                    'desc3' => array(
                        'type' => 'text',
                        'title' => JText::_('简介3'),
                        'desc' => JText::_('简介'),
                        'std' => '简介1',

                    ),
                    'tit_size' => array(
                        'type' => 'slider',
                        'title' => '标题文字大小',
                        'std' => 35,
                        'max' => 120,
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'size' => array(
                        'type' => 'slider',
                        'title' => '文本文字大小',
                        'std' => 13,
                        'max' => 120,
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'tit_cle' => array(
                        'type' => 'color',
                        'title' => '标题颜色',
                        'desc' => '',
                        'std' => '#000',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'text_cle' => array(
                        'type' => 'color',
                        'title' => '文本文字颜色',
                        'desc' => '',
                        'std' => '#ff5500',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),

                    'bg_img' => array(
                        'type' => 'media',
                        'title' => '背景图片',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'zs_img' => array(
                        'type' => 'media',
                        'title' => '展示图片',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),
                    'zs_img_cle' => array(
                        'type' => 'color',
                        'title' => '展示图片阴影颜色',
                        'desc' => '',
                        'std' => '#2f64ff',
                        'depends' => array(array('content_style_settings', '=', 'style1')),

                    ),

                    'gradient' => array(
                        'type' => 'color',
                        'title' => '背景色',
                        'desc' => '',
                        'std' => '#2f64ff',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                    'left_icon1' => array(
                        'type' => 'media',
                        'title' => '左侧图标1',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                    'left_icon2' => array(
                        'type' => 'media',
                        'title' => '左侧图标2',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                    'left_icon3' => array(
                        'type' => 'media',
                        'title' => '左侧图标3',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                    'right_img1' => array(
                        'type' => 'media',
                        'title' => '右侧图片1',
                        'std' => '',
                        'depends' => array(array('content_style_settings', '!=', 'style1'),),

                    ),
                ),
                'std' =>array(
                    array(
                        'title'=>'互联网思维',
                        'title1'=>'互联网思维1',
                        'title2'=>'互联网思维2',
                        'title3'=>'互联网思维3',
                        'desc1'=>'互联网思维改造传统体育产业',
                        'desc'=>'互联网思维改造传统体育产业',
                        'desc2'=>'互联网思维改造传统体育产业',
                        'desc3'=>'互联网思维改造传统体育产业',
                        'tit_size'=>35,
                        'size'=>13,
                        'tit_cle'=>'#000',
                        'text_cle'=>'#ff5500',
                        'bg_img'=>'',
                        'zs_img'=>'https://oss.lcweb01.cn/joomla/20221021/3e7c8d525ca85c81dfb42f5655f7b8df.png',
                        'zs_img_cle'=>'#2f64ff',
                        'gradient'=>'#2f64ff',
                        'left_icon1'=>'https://oss.lcweb01.cn/joomla/20221021/79b48ce563dcd24067ea5c1c0197fa4b.png',
                        'left_icon2'=>'https://oss.lcweb01.cn/joomla/20221021/279219fb024576f4c6b03138c828a028.png',
                        'left_icon3'=>'https://oss.lcweb01.cn/joomla/20221021/4d5da9245fd735ad48449b1e6e5b76af.png',
                        'right_img1'=>'https://oss.lcweb01.cn/joomla/20221021/3e7c8d525ca85c81dfb42f5655f7b8df.png',
                    )
                )
            ),

            'bg_img' => array(
                'type' => 'media',
                'title' => '背景图片',
                'std' => '',
                'depends' => array(array('art_type_selector', '!=', 'type1'),),

            ),

            'tab_active_bg_type2' => array(
                'type' => 'media',
                'title' => '选项卡选中背景图',
                'std' => 'https://oss.lcweb01.cn/joomla/20221021/91f26b6816681dd50419004594c50580.png',
                'depends' => array(array('art_type_selector', '=', 'type2'),),
            ),

            // 样式二补配置项
            'tab_padding_top_type2' => array(
                'type' => 'slider',
                'title' => '样式二上内边距',
                'std' => array('md' => 60, 'sm' => 60, 'xs' => 50),
                'max' => 200,
                'responsive' => true,
                'depends' => array(array('art_type_selector', '=', 'type2'),),
            ),
            'tab_height_type2' => array(
                'type' => 'slider',
                'title' => '样式二选项卡高度',
                'std' => array('md' => 149, 'sm' => 168, 'xs' => 168),
                'max' => 500,
                'responsive' => true,
                'depends' => array(array('art_type_selector', '=', 'type2'),),
            ),
            'tab_active_height_type2' => array(
                'type' => 'slider',
                'title' => '样式二选项卡选中高度',
                'std' => array('md' => 175, 'sm' => 198, 'xs' => 198),
                'max' => 500,
                'responsive' => true,
                'depends' => array(array('art_type_selector', '=', 'type2'),),
            ),
            'tab_padding_type2' => array(
                'type' => 'padding',
                'title' => '样式二选项卡内边距',
                'std' => array('md' => '30px 0px 30px 0px', 'sm' => '12px 6px 12px 6px', 'xs' => '12px 6px 12px 6px'),
                'responsive' => true,
                'depends' => array(array('art_type_selector', '=', 'type2'),),
            ),
        ),
    )
);
