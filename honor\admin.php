<?php

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'honor',
         'title' => '荣誉证书',
         'desc' => '',
         'category' => '龙采官网插件',
         'attr' => array(
             'general' => array(
                 'site_id' => array(
                     'std' => $site_id,
                 ),
                 'company_id' => array(
                     'std' => $company_id,
                 ),
                'adv_typeid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择后台对应的广告分类'),
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_advertise')['list'],
                ),
                'bgimage' => array(
					'type' => 'media',
					'title' => JText::_('背景图'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT_DESC'),
					'show_input' => true,
					'std' => 'https://oss.lcweb01.cn/joomla/20220518/96529d63c154443df192811e57e6578b.jpg',
				),
                //2022.7.18新增按钮设置
                'button_width' => array(
					'type' => 'slider',
					'title' => JText::_('按钮宽度'),
					'std' => '160',
					'max' => 300,
				),
                'button_height' => array(
					'type' => 'slider',
					'title' => JText::_('按钮高度'),
					'std' => '55',
					'max' => 100,
				),
                'button_cont' => array(
					'type' => 'text',
					'title' => JText::_('按钮文字'),
					'std' => '了解更多',
				),
                'button_size' => array(
					'type' => 'slider',
					'title' => JText::_('文字大小'),
                    'std' => '16',
					'max' => 50,
				),
                'button_color' => array(
					'type' => 'color',
					'title' => JText::_('字体颜色'),
					'std' => '#000',
				),
                'button_colorhv' => array(
					'type' => 'color',
					'title' => JText::_('划过字体颜色'),
					'std' => '#fff',
				),
                'button_bg' => array(
					'type' => 'color',
					'title' => JText::_('划过按钮背景色'),
					'std' => '#d0111b',
				),
                //
                'tz_page_type' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(
                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                    ),
                    'std' => 'external_links',
                    
                ),

                // 新加跳转的链接地址
                'link' => array(
                    'type' => 'text',
                    'title' => JText::_('链接跳转地址'),
                    'desc' => JText::_('链接必须以http://或https://开始'),
                    'placeholder' => 'http://',
                    'std' => '',
                    'depends' => array(
                        array('tz_page_type', '=', 'external_links'),
                    )
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('tz_page_type', '=', 'Internal_pages')
                    ),
                ),
                // 新加跳转方式
                'media_target' => array(
                    'type' => 'select',
                    'title' => JText::_('链接跳转方式'),
                    'desc' => JText::_('链接跳转方式'),
                    'values' => array(
                        '' => JText::_('当前页面'),
                        '_blank' => JText::_('新窗口'),
                    ),
                ),

                'openjs' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否加载js（如果手机站加载js后不滚动，请关闭此按钮）'),
					'std' => '1',
				),
             ),
         ),
     )
 );
