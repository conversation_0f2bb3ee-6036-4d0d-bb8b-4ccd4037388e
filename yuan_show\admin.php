<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'yuan_show',
		'title' => JText::_('圆形缩放展示组件'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
		'category' => '龙采官网插件',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'jw_tab_item' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '无感知AI考勤',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/d380a73bcc5866b4d7302d405a84967b.png',
                            'text' => '快速识别高效感应
                                        生物防假，杜绝代打卡
                                        无需等待，路过即考勤',

                        ),
                        array(
                            'title' => '手机考勤',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/13c5e7ae71c24569765e12f7b18d1579.png',
                            'text' => '定位不准确
                                        考勤易做假
                                        受网络影响',
                        ),
                        array(
                            'title' => '刷卡考勤',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/be3040ea8dd7b905601aced5b4a3c8ac.png',
                            'text' => '制卡费用高
                                        卡片易丢失损坏
                                        代打卡无法监控',
                        ),
                        array(
                            'title' => '指纹考勤',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220630/da3b670960ba619435d92ad9feccc2d7.png',
                            'text' => '打卡需要排队等侯
                                        指纹识别时间过长
                                        手部信息不易识别',
                        ),
                        
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '无感知AI考勤',
                        ),
                        
                        'img' => array(
                            'type' => 'media',
                            'title' => '图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220630/88135b1101302330625816a2187fe8ea.png'
                        ),

                        'text' => array(
                            'type' => 'editor',
                            'title' => JText::_('简介'),
                            
                            'std' => '快速识别高效感应
                                    生物防假，杜绝代打卡
                                    无需等待，路过即考勤'
                        ),
                        
                    ),
                   
                ),
                'bg_img' => array(
                    'type' => 'media',
                    'title' => '背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220630/2e472beb40666c32370f53890029119d.png',
                ),
                'hover_bg' => array(
                    'type' => 'media',
                    'title' => '划过背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220630/45454e6228b4f8d530113cc4383f0dd2.png',
                ),
				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
