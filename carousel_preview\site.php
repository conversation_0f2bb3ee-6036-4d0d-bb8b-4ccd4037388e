<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonCarousel_preview extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';

        //Addons option
        $autoplay = (isset($settings->autoplay) && $settings->autoplay) ? 1 : 0;
        $alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : 0;
        $interval = (isset($settings->interval) && $settings->interval) ? ((int)$settings->interval * 1000) : 5000;
        $carousel_autoplay = ($autoplay) ? ' data-jwpf-ride="jwpf-carousel"' : '';
        if ($autoplay == 0) {
            $interval = 'false';
        }

        $output = '<div id="jwpf-carousel-' . $this->addon->id . '" data-interval="' . $interval . '" class="jwpf-carousel jwpf-slide' . $class . '"' . $carousel_autoplay . '>';

        if($settings->controllers) {
            $output .= '<ol class="jwpf-carousel-indicators jwpf-carousel-indicators_preview" >';
            foreach ($settings->jw_carousel_item as $key => $value) {
                $output .= '<li data-jwpf-target="#jwpf-carousel-' . $this->addon->id . '" ' . (($key == 0) ? ' class="active"' : '') . ' data-jwpf-slide-to="' . $key . '"></li>';
            }
            $output .= '</ol>';
        }
        if($settings->preview) {
            $output .= '<ol class="jwpf-carousel-indicators jwpf-carousel-indicators_preview" data-preview="has">';
            foreach ($settings->jw_carousel_item as $key => $value) {
                $output .= '<li data-jwpf-target="#jwpf-carousel-' . $this->addon->id . '" ' . (($key == 0) ? ' class="active"' : '') . ' data-jwpf-slide-to="' . $key . '">';
                $output .= '<img src="' . $value->bg . '" />';
                $output .= '</li>';
            }
            $output .= '</ol>';
        }

        $output .= '<div class="jwpf-carousel-inner ' . $alignment . '">';
        if (isset($settings->jw_carousel_item) && count((array)$settings->jw_carousel_item)) {
            foreach ($settings->jw_carousel_item as $key => $value) {
                $button_url = (isset($value->button_url) && $value->button_url) ? $value->button_url : '';

                $output .= '<div class="jwpf-item jwpf-item-' . $this->addon->id . $key . ' ' . ((isset($value->bg) && $value->bg) ? ' jwpf-item-has-bg' : '') . (($key == 0) ? ' active' : '') . '">';
                $alt_text = isset($value->title) ? $value->title : '';
                $output .= (isset($value->bg) && $value->bg) ? '<img src="' . $value->bg . '" alt="' . $alt_text . '">' : '';

                $output .= '<div class="jwpf-carousel-item-inner">';
                $output .= '<div class="jwpf-carousel-caption">';
                $output .= '<div class="jwpf-carousel-text">';

                if ((isset($value->title) && $value->title) || (isset($value->content) && $value->content)) {
                    $output .= (isset($value->title) && $value->title) ? '<h2>' . $value->title . '</h2>' : '';
                    $output .= (isset($value->content) && $value->content) ? '<div class="jwpf-carousel-content">' . $value->content . '</div>' : '';
                    if (isset($value->button_text) && $value->button_text) {
                        $button_class = (isset($value->button_type) && $value->button_type) ? ' jwpf-btn-' . $value->button_type : ' jwpf-btn-default';
                        $button_class .= (isset($value->button_size) && $value->button_size) ? ' jwpf-btn-' . $value->button_size : '';
                        $button_class .= (isset($value->button_shape) && $value->button_shape) ? ' jwpf-btn-' . $value->button_shape : ' jwpf-btn-rounded';
                        $button_class .= (isset($value->button_appearance) && $value->button_appearance) ? ' jwpf-btn-' . $value->button_appearance : '';
                        $button_class .= (isset($value->button_block) && $value->button_block) ? ' ' . $value->button_block : '';
                        $button_icon = (isset($value->button_icon) && $value->button_icon) ? $value->button_icon : '';
                        $button_icon_position = (isset($value->button_icon_position) && $value->button_icon_position) ? $value->button_icon_position : 'left';
                        $button_target = (isset($value->button_target) && $value->button_target) ? $value->button_target : '_self';

                        $icon_arr = array_filter(explode(' ', $button_icon));
                        if (count($icon_arr) === 1) {
                            $button_icon = 'fa ' . $button_icon;
                        }

                        if ($button_icon_position == 'left') {
                            $value->button_text = ($button_icon) ? '<i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i> ' . $value->button_text : $value->button_text;
                        } else {
                            $value->button_text = ($button_icon) ? $value->button_text . ' <i aria-hidden="true" class="' . $button_icon . '" aria-hidden="true"></i>' : $value->button_text;
                        }

                        $output .= '<a href="' . $button_url . '" target="' . $button_target . '" ' . ($button_target === '_blank' ? 'rel="noopener noreferrer"' : '') . ' id="btn-' . ($this->addon->id + $key) . '" class="jwpf-btn' . $button_class . '">' . $value->button_text . '</a>';
                    }
                }

                $output .= '</div>';
                $output .= '</div>';

                $output .= '</div>';
                $output .= '</div>';
            }
        }

        $output .= '</div>';

        if ($settings->arrows) {
            $output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow left jwpf-carousel-control" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-chevron-left" aria-hidden="true"></i></a>';
            $output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow right jwpf-carousel-control" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-chevron-right" aria-hidden="true"></i></a>';
        }

        $output .= '</div>';

        return $output;
    }

    public function css()
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css = ''; //pc样式
        $css_sm = '';
        $css_xs = '';

        // 轮播项 样式
        foreach ($settings->jw_carousel_item as $key => $value) {
            // print_r($value);
            if (isset($value->button_text)) {
                $css_path = new JLayoutFile('addon.css.button', $layout_path);
                $css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $value, 'id' => 'btn-' . ($this->addon->id + $key)));
            }
            $title_css = ''; // 标题pc样式
            $title_css_sm = ''; // 标题平板样式
            $title_css_xs = ''; // 标题手机样式
            // 标题文字大小
            $title_fontsize = $value->title_fontsize;
            $title_fontsize_md = '';
            $title_fontsize_sm = '';
            $title_fontsize_xs = '';
            if (isset($title_fontsize) && $title_fontsize) {
                if (is_object($title_fontsize)) {
                    $title_fontsize_md = $title_fontsize->md;
                    $title_fontsize_sm = $title_fontsize->sm;
                    $title_fontsize_xs = $title_fontsize->xs;
                } else {
                    $title_fontsize_md = $title_fontsize;
                    $title_fontsize_sm = $value->title_fontsize_sm;
                    $title_fontsize_xs = $value->title_fontsize_xs;
                }
            }
            if($title_fontsize_md) {
                $title_css .= 'font-size:' . $title_fontsize_md . 'px;';
            }
            if($title_fontsize_sm) {
                $title_css_sm .= 'font-size:' . $title_fontsize_sm . 'px;';
            }
            if($title_fontsize_xs) {
                $title_css_xs .= 'font-size:' . $title_fontsize_xs . 'px;';
            }
            // 标题文字行高
            $title_lineHeight = $value->title_lineheight;
            $title_lineHeight_md = '';
            $title_lineHeight_sm = '';
            $title_lineHeight_xs = '';
            if (isset($title_lineHeight) && $title_lineHeight) {
                if (is_object($title_lineHeight)) {
                    $title_lineHeight_md = $title_lineHeight->md;
                    $title_lineHeight_sm = $title_lineHeight->sm;
                    $title_lineHeight_xs = $title_lineHeight->xs;
                } else {
                    $title_lineHeight_md = $title_lineHeight;
                    $title_lineHeight_sm = $value->title_lineheight_sm;
                    $title_lineHeight_xs = $value->title_lineheight_xs;
                }
            }
            if($title_lineHeight_md) {
                $title_css .= 'line-height:' . $title_lineHeight_md . 'px;';
            }
            if($title_lineHeight_sm) {
                $title_css_sm .= 'line-height:' . $title_lineHeight_sm . 'px;';
            }
            if($title_lineHeight_xs) {
                $title_css_xs .= 'line-height:' . $title_lineHeight_xs . 'px;';
            }
            // 标题颜色
            $title_css .= (isset($value->title_color) && !empty($value->title_color)) ? 'color:' . $value->title_color . ';' : '';
            // 标题 字体样式
            if (isset($value->title_font_family) && $value->title_font_family) {
                $font_path = new JLayoutFile('addon.css.fontfamily', $layout_path);
                $font_path->render(array('font' => $value->title_font_family));
                $title_css .= 'font-family: ' . $value->title_font_family . ';';
            }
            // 标题文字 内边距
            $title_padding = $value->title_padding;
            $title_padding_md = '';
            $title_padding_sm = '';
            $title_padding_xs = '';
            if (isset($title_padding) && $title_padding) {
                if (is_object($title_padding)) {
                    $title_padding_md = $title_padding->md;
                    $title_padding_sm = $title_padding->sm;
                    $title_padding_xs = $title_padding->xs;
                } else {
                    $title_padding_md = $title_padding;
                    $title_padding_sm = $value->title_padding_sm;
                    $title_padding_xs = $value->title_padding_xs;
                }
            }
            if($title_padding_md) {
                $title_css .= 'padding:' . $title_padding_md . ';';
            }
            if($title_padding_sm) {
                $title_css_sm .= 'padding:' . $title_padding_sm . ';';
            }
            if($title_padding_xs) {
                $title_css_xs .= 'padding:' . $title_padding_xs . ';';
            }
            // 标题文字 外边距
            $title_margin = $value->title_margin;
            $title_margin_md = '';
            $title_margin_sm = '';
            $title_margin_xs = '';
            if (isset($title_margin) && $title_margin) {
                if (is_object($title_margin)) {
                    $title_margin_md = $title_margin->md;
                    $title_margin_sm = $title_margin->sm;
                    $title_margin_xs = $title_margin->xs;
                } else {
                    $title_margin_md = $title_margin;
                    $title_margin_sm = $value->title_margin_sm;
                    $title_margin_xs = $value->title_margin_xs;
                }
            }
            if($title_margin_md) {
                $title_css .= 'margin:' . $title_margin_md . ';';
            }
            if($title_margin_sm) {
                $title_css_sm .= 'margin:' . $title_margin_sm . ';';
            }
            if($title_margin_xs) {
                $title_css_xs .= 'margin:' . $title_margin_xs . ';';
            }
            // 标题pc样式
            if (!empty($title_css)) {
                $css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2 {';
                $css .= $title_css;
                $css .= '}';
            }
            // 标题平板样式
            if (!empty($title_css_sm)) {
                $css_sm .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2 {';
                $css_sm .= $title_css_sm;
                $css_sm .= '}';
            }
            // 标题手机样式
            if (!empty($title_css_xs)) {
                $css_xs .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption h2 {';
                $css_xs .= $title_css_xs;
                $css_xs .= '}';
            }

            $content_css = ''; // 内容pc样式
            $content_css_sm = ''; // 内容平板样式
            $content_css_xs = ''; // 内容手机样式
            // 内容文字大小
            $content_fontsize = $value->content_fontsize;
            $content_fontsize_md = '';
            $content_fontsize_sm = '';
            $content_fontsize_xs = '';
            if (isset($content_fontsize) && $content_fontsize) {
                if (is_object($content_fontsize)) {
                    $content_fontsize_md = $content_fontsize->md;
                    $content_fontsize_sm = $content_fontsize->sm;
                    $content_fontsize_xs = $content_fontsize->xs;
                } else {
                    $content_fontsize_md = $content_fontsize;
                    $content_fontsize_sm = $value->content_fontsize_sm;
                    $content_fontsize_xs = $value->content_fontsize_xs;
                }
            }
            if($content_fontsize_md) {
                $content_css .= 'font-size:' . $content_fontsize_md . 'px;';
            }
            if($content_fontsize_sm) {
                $content_css_sm .= 'font-size:' . $content_fontsize_sm . 'px;';
            }
            if($content_fontsize_xs) {
                $content_css_xs .= 'font-size:' . $content_fontsize_xs . 'px;';
            }
            // 内容文字行高
            $content_lineHeight = $value->content_lineheight;
            $content_lineHeight_md = '';
            $content_lineHeight_sm = '';
            $content_lineHeight_xs = '';
            if (isset($content_lineHeight) && $content_lineHeight) {
                if (is_object($content_lineHeight)) {
                    $content_lineHeight_md = $content_lineHeight->md;
                    $content_lineHeight_sm = $content_lineHeight->sm;
                    $content_lineHeight_xs = $content_lineHeight->xs;
                } else {
                    $content_lineHeight_md = $content_lineHeight;
                    $content_lineHeight_sm = $value->content_lineheight_sm;
                    $content_lineHeight_xs = $value->content_lineheight_xs;
                }
            }
            if($content_lineHeight_md) {
                $content_css .= 'line-height:' . $content_lineHeight_md . 'px;';
            }
            if($content_lineHeight_sm) {
                $content_css_sm .= 'line-height:' . $content_lineHeight_sm . 'px;';
            }
            if($content_lineHeight_xs) {
                $content_css_xs .= 'line-height:' . $content_lineHeight_xs . 'px;';
            }
            // 内容颜色
            $content_css .= (isset($value->content_color) && !empty($value->content_color)) ? 'color:' . $value->content_color . ';' : '';
            // 内容 字体样式
            if (isset($value->content_font_family) && $value->content_font_family) {
                $font_path = new JLayoutFile('addon.css.fontfamily', $layout_path);
                $font_path->render(array('font' => $value->content_font_family));
                $content_css .= 'font-family: ' . $value->content_font_family . ';';
            }
            // 内容文字 内边距
            $content_padding = $value->content_padding;
            $content_padding_md = '';
            $content_padding_sm = '';
            $content_padding_xs = '';
            if (isset($content_padding) && $content_padding) {
                if (is_object($content_padding)) {
                    $content_padding_md = $content_padding->md;
                    $content_padding_sm = $content_padding->sm;
                    $content_padding_xs = $content_padding->xs;
                } else {
                    $content_padding_md = $content_padding;
                    $content_padding_sm = $value->content_padding_sm;
                    $content_padding_xs = $value->content_padding_xs;
                }
            }
            if($content_padding_md) {
                $content_css .= 'padding:' . $content_padding_md . ';';
            }
            if($content_padding_sm) {
                $content_css_sm .= 'padding:' . $content_padding_sm . ';';
            }
            if($content_padding_xs) {
                $content_css_xs .= 'padding:' . $content_padding_xs . ';';
            }
            // 内容文字 外边距
            $content_margin = $value->content_margin;
            $content_margin_md = '';
            $content_margin_sm = '';
            $content_margin_xs = '';
            if (isset($content_margin) && $content_margin) {
                if (is_object($content_margin)) {
                    $content_margin_md = $content_margin->md;
                    $content_margin_sm = $content_margin->sm;
                    $content_margin_xs = $content_margin->xs;
                } else {
                    $content_margin_md = $content_margin;
                    $content_margin_sm = $value->content_margin_sm;
                    $content_margin_xs = $value->content_margin_xs;
                }
            }
            if($content_margin_md) {
                $content_css .= 'margin:' . $content_margin_md . ';';
            }
            if($content_margin_sm) {
                $content_css_sm .= 'margin:' . $content_margin_sm . ';';
            }
            if($content_margin_xs) {
                $content_css_xs .= 'margin:' . $content_margin_xs . ';';
            }
            // 内容pc样式
            if (!empty($content_css)) {
                $css .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content {';
                $css .= $content_css;
                $css .= '}';
            }
            // 内容平板样式
            if (!empty($content_css_sm)) {
                $css_sm .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content {';
                $css_sm .= $content_css_sm;
                $css_sm .= '}';
            }
            // 内容手机样式
            if (!empty($content_css_xs)) {
                $css_xs .= $addon_id . ' .jwpf-item-' . $this->addon->id . $key . ' .jwpf-carousel-caption .jwpf-carousel-content {';
                $css_xs .= $content_css_xs;
                $css_xs .= '}';
            }
        }
        // 旋转木马项目滑动速度 ms
        $speed = (isset($settings->speed) && $settings->speed) ? $settings->speed : 600;
        $css .= $addon_id . ' .jwpf-carousel-inner > .jwpf-item {
            -webkit-transition-duration: ' . $speed . 'ms;
            transition-duration: ' . $speed . 'ms;
        }';

        if($settings->controllers) {
            $controllers_width_size = (isset($settings->controllers_width_size) && $settings->controllers_width_size) ? $settings->controllers_width_size : 0;
            $controllers_height_size = (isset($settings->controllers_height_size) && $settings->controllers_height_size) ? $settings->controllers_height_size : 0;
            $controllers_radius_size = (isset($settings->controllers_radius_size) && $settings->controllers_radius_size) ? $settings->controllers_radius_size : 0;
            $controllers_color = (isset($settings->controllers_color) && $settings->controllers_color) ? $settings->controllers_color : '#fff';
            $controllers_active_color = (isset($settings->controllers_active_color) && $settings->controllers_active_color) ? $settings->controllers_active_color : '#000';

            $css .= $addon_id . ' .jwpf-carousel-indicators li {
                width:' . $controllers_width_size . 'px !important;
                height:' . $controllers_height_size . 'px !important;
                border-radius:' . $controllers_radius_size . 'px !important;
                background-color:' . $controllers_color . ';
            }
            ' . $addon_id . ' .jwpf-carousel-indicators .active {
                background-color:' . $controllers_active_color . ' !important;
                filter: brightness(100%) !important;
                -webkit-filter: brightness(100%) !important;
            }';
        }
        if($settings->preview) {
            // 预览图 宽度 高度 圆角 距底部距离
            $width_size = isset($settings->width_size) && $settings->width_size ? $settings->width_size : '130';
            $height_size = isset($settings->height_size) && $settings->height_size ? $settings->height_size : '60';
            $radius_size = isset($settings->radius_size) && $settings->radius_size ? $settings->radius_size : '10';
            $bottom_size = isset($settings->bottom_size) && $settings->bottom_size ? $settings->bottom_size : '10';
            $css .= $addon_id . ' .jwpf-carousel-indicators_preview {
                bottom: ' . $bottom_size . 'px !important;
            }
            ' . $addon_id . ' .jwpf-carousel-indicators_preview li,
            ' . $addon_id . ' .jwpf-carousel-indicators_preview li img {
                width: ' . $width_size . 'px !important;
                height: ' . $height_size . 'px !important;
                border-radius: ' . $radius_size . 'px !important;
            }
            ';
        }
        if($settings->arrows) {
            $arrows_color = (isset($settings->arrows_color) && $settings->arrows_color) ? $settings->arrows_color : '';
            $arrows_size = (isset($settings->arrows_size) && $settings->arrows_size) ? $settings->arrows_size : '';
            $css .= $addon_id . ' .jwpf-carousel-arrow i {
                color: ' . $arrows_color . ';
                font-size: ' . $arrows_size . 'px;
            }';
        }

        $preview_css = ''; // 预览图pc样式
        $preview_css_sm = ''; // 预览图平板样式
        $preview_css_xs = ''; // 预览图手机样式
        // 预览图 内边距
        $preview_padding = $settings->preview_padding;
        $preview_padding_md = '';
        $preview_padding_sm = '';
        $preview_padding_xs = '';
        if (isset($preview_padding) && $preview_padding) {
            if (is_object($preview_padding)) {
                $preview_padding_md = $preview_padding->md;
                $preview_padding_sm = $preview_padding->sm;
                $preview_padding_xs = $preview_padding->xs;
            } else {
                $preview_padding_md = $preview_padding;
                $preview_padding_sm = $settings->preview_padding_sm;
                $preview_padding_xs = $settings->preview_padding_xs;
            }
        }
        if($preview_padding_md) {
            $preview_css .= 'padding:' . $preview_padding_md . ';';
        }
        if($preview_padding_sm) {
            $preview_css_sm .= 'padding:' . $preview_padding_sm . ';';
        }
        if($preview_padding_xs) {
            $preview_css_xs .= 'padding:' . $preview_padding_xs . ';';
        }
        // 预览图 内边距
        $preview_margin = $settings->preview_margin;
        $preview_margin_md = '';
        $preview_margin_sm = '';
        $preview_margin_xs = '';
        if (isset($preview_margin) && $preview_margin) {
            if (is_object($preview_margin)) {
                $preview_margin_md = $preview_margin->md;
                $preview_margin_sm = $preview_margin->sm;
                $preview_margin_xs = $preview_margin->xs;
            } else {
                $preview_margin_md = $preview_margin;
                $preview_margin_sm = $settings->preview_margin_sm;
                $preview_margin_xs = $settings->preview_margin_xs;
            }
        }
        if($preview_margin_md) {
            $preview_css .= 'margin:' . $preview_margin_md . ';';
        }
        if($preview_margin_sm) {
            $preview_css_sm .= 'margin:' . $preview_margin_sm . ';';
        }
        if($preview_margin_xs) {
            $preview_css_xs .= 'margin:' . $preview_margin_xs . ';';
        }

        // 预览图pc样式
        if (!empty($preview_css) && $settings->preview) {
            $css .= $addon_id . ' .jwpf-carousel-indicators_preview li {';
            $css .= $preview_css;
            $css .= '}';
        }
        // 预览图平板样式
        if (!empty($preview_css_sm) && $settings->preview) {
            $css_sm .= $addon_id . ' .jwpf-carousel-indicators_preview li {';
            $css_sm .= $preview_css_sm;
            $css_sm .= '}';
        }
        // 预览图手机样式
        if (!empty($preview_css_xs) && $settings->preview) {
            $css_xs .= $addon_id . ' .jwpf-carousel-indicators_preview li {';
            $css_xs .= $preview_css_xs;
            $css_xs .= '}';
        }

        $css .= '
        @media (min-width: 768px) and (max-width: 991px) {
            ' . $css_sm . '
        }
        ';
        $css .= '
        @media (max-width: 767px) {
            ' . $css_xs . '
        }
        ';
        return $css;
    }


    public static function getTemplate()
    {
        $output = '
        <#
        var interval = data.interval ? parseInt(data.interval) * 1000 : 5000;
        if(data.autoplay==0){
            interval = "false";
        }
        var autoplay = data.autoplay ? \'data-jwpf-ride="jwpf-carousel"\' : "";
        #>
        <style type="text/css">
            #jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
                -webkit-transition-duration: {{ data.speed }}ms;
                transition-duration: {{ data.speed }}ms;
            } 
            <# if(data.controllers){ #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators li {
                    width:{{data.controllers_width_size}}px !important;
                    height:{{data.controllers_height_size}}px !important;
                    border-radius:{{data.controllers_radius_size}}px !important;
                    background-color:{{data.controllers_color}};
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators  .active{
                    width:{{data.controllers_width_size}}px !important;
                    height:{{data.controllers_height_size}}px !important;
                    border-radius:{{data.controllers_radius_size}}px !important;
                    background-color:{{data.controllers_active_color}};
                }
            <# } #>
            <# if(data.arrows){ #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-arrow i {
                    color: {{data.arrows_color}};
                    font-size: {{data.arrows_size}}px;
                }
            <# } #>
            <#
                var preview_padding = "";
                var preview_padding_sm = "";
                var preview_padding_xs = "";
                if(data.preview_padding){
                    if(_.isObject(data.preview_padding)){
                        if(data.preview_padding.md.trim() !== ""){
                            preview_padding = data.preview_padding.md.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }
                        if(data.preview_padding.sm.trim() !== ""){
                            preview_padding_sm = data.preview_padding.sm.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }

                        if(data.preview_padding.xs.trim() !== ""){
                            preview_padding_xs = data.preview_padding.xs.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }
                    }
                }
            #>
            <#
                var preview_margin = "";
                var preview_margin_sm = "";
                var preview_margin_xs = "";
                if(data.preview_margin){
                    if(_.isObject(data.preview_margin)){
                        if(data.preview_margin.md.trim() !== ""){
                            preview_margin = data.preview_margin.md.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }
    
                        if(data.preview_margin.sm.trim() !== ""){
                            preview_margin_sm = data.preview_margin.sm.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }
    
                        if(data.preview_margin.xs.trim() !== ""){
                            preview_margin_xs = data.preview_margin.xs.split(" ").map(item => {
                                if(_.isEmpty(item)){
                                    return "0";
                                }
                                return item;
                            }).join(" ")
                        }
                    }
                }
            #>
            #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview  li{
                width:{{  data.width_size }}px !important;
                height:{{ data.height_size}}px !important;
                padding: {{ preview_padding }} !important;
                margin: {{ preview_margin }} !important;
                border-radius:{{ data.radius_size }}px !important;
            }
            #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview li img {
                width:{{  data.width_size }}px !important;
                height:{{ data.height_size }}px !important;
                border-radius:{{ data.radius_size }}px !important;
            }
            #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview  .active{
                width:{{ data.width_size }}px !important;
                height:{{ data.height_size }}px !important;
                padding: {{ preview_padding }} !important;
                margin: {{ preview_margin}} !important;
            }
            #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview{
                bottom:{{ data.bottom_size }}px !important;
            }
            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview li{
                    padding: {{ preview_padding_sm }};
                    margin: {{ preview_margin_sm }};
                }
                        
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview .active {
                    padding: {{ preview_padding_sm }};
                    margin: {{ preview_margin_sm }};
                }       
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview  li{
                    padding: {{ preview_padding_xs }};
                    margin: {{ preview_margin_xs  }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-indicators_preview  .active{
                    padding: {{ preview_padding_xs }};
                    margin: {{ preview_margin_xs  }};
                }
            }
            <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>
                <# var button_fontstyle = carousel_item.button_fontstyle || ""; #>
                #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-{{ carousel_item.type }}{
                    letter-spacing: {{ carousel_item.button_letterspace }};
                    <# if(_.isArray(button_fontstyle)) { #>
                        <# if(button_fontstyle.indexOf("underline") !== -1){ #>
                            text-decoration: underline;
                        <# } #>
                        <# if(button_fontstyle.indexOf("uppercase") !== -1){ #>
                            text-transform: uppercase;
                        <# } #>
                        <# if(button_fontstyle.indexOf("italic") !== -1){ #>
                            font-style: italic;
                        <# } #>
                        <# if(button_fontstyle.indexOf("lighter") !== -1){ #>
                            font-weight: lighter;
                        <# } else if(button_fontstyle.indexOf("normal") !== -1){#>
                            font-weight: normal;
                        <# } else if(button_fontstyle.indexOf("bold") !== -1){#>
                            font-weight: bold;
                        <# } else if(button_fontstyle.indexOf("bolder") !== -1){#>
                            font-weight: bolder;
                        <# } #>
                    <# } #>
                }

                <# if(carousel_item.button_type == "custom"){ #>
                    #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom{
                        color: {{ carousel_item.button_color }};
                        <# if(carousel_item.button_appearance == "outline"){ #>
                            border-color: {{ carousel_item.button_background_color }}
                        <# } else if(carousel_item.button_appearance == "3d"){ #>
                            border-bottom-color: {{ carousel_item.button_background_color_hover }};
                            background-color: {{ carousel_item.button_background_color }};
                        <# } else if(carousel_item.button_appearance == "gradient"){ #>
                            border: none;
                            <# if(typeof carousel_item.button_background_gradient.type !== "undefined" && carousel_item.button_background_gradient.type == "radial"){ #>
                                background-image: radial-gradient(at {{ carousel_item.button_background_gradient.radialPos || "center center"}}, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
                            <# } else { #>
                                background-image: linear-gradient({{ carousel_item.button_background_gradient.deg || 0}}deg, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
                            <# } #>
                        <# } else { #>
                            background-color: {{ carousel_item.button_background_color }};
                        <# } #>
                    }

                    #jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom:hover{
                        color: {{ carousel_item.button_color_hover }};
                        background-color: {{ carousel_item.button_background_color_hover }};
                        <# if(carousel_item.button_appearance == "outline"){ #>
                            border-color: {{ carousel_item.button_background_color_hover }};
                        <# } else if(carousel_item.button_appearance == "gradient"){ #>
                            <# if(typeof carousel_item.button_background_gradient_hover.type !== "undefined" && carousel_item.button_background_gradient_hover.type == "radial"){ #>
                                background-image: radial-gradient(at {{ carousel_item.button_background_gradient_hover.radialPos || "center center"}}, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
                            <# } else { #>
                                background-image: linear-gradient({{ carousel_item.button_background_gradient_hover.deg || 0}}deg, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
                            <# } #>
                        <# } #>
                    }

                <# } #>
                <#
                    var padding = "";
                    var padding_sm = "";
                    var padding_xs = "";
                    if(carousel_item.title_padding){
                        if(_.isObject(carousel_item.title_padding)){
                            if(carousel_item.title_padding.md.trim() !== ""){
                                padding = carousel_item.title_padding.md.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.title_padding.sm.trim() !== ""){
                                padding_sm = carousel_item.title_padding.sm.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.title_padding.xs.trim() !== ""){
                                padding_xs = carousel_item.title_padding.xs.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }
                        }

                    }

                    var margin = "";
                    var margin_sm = "";
                    var margin_xs = "";
                    if(carousel_item.title_margin){
                        if(_.isObject(carousel_item.title_margin)){
                            if(carousel_item.title_margin.md.trim() !== ""){
                                margin = carousel_item.title_margin.md.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.title_margin.sm.trim() !== ""){
                                margin_sm = carousel_item.title_margin.sm.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.title_margin.xs.trim() !== ""){
                                margin_xs = carousel_item.title_margin.xs.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }
                        }

                    }


                    var content_padding = "";
                    var content_padding_sm = "";
                    var content_padding_xs = "";
                    if(carousel_item.content_padding){
                        if(_.isObject(carousel_item.content_padding)){
                            if(carousel_item.content_padding.md.trim() !== ""){
                                content_padding = carousel_item.content_padding.md.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.content_padding.sm.trim() !== ""){
                                content_padding_sm = carousel_item.content_padding.sm.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.content_padding.xs.trim() !== ""){
                                content_padding_xs = carousel_item.content_padding.xs.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }
                        }

                    }

                    var content_margin = "";
                    var content_margin_sm = "";
                    var content_margin_xs = "";
                    if(carousel_item.content_margin){
                        if(_.isObject(carousel_item.content_margin)){
                            if(carousel_item.content_margin.md.trim() !== ""){
                                content_margin = carousel_item.content_margin.md.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.content_margin.sm.trim() !== ""){
                                content_margin_sm = carousel_item.content_margin.sm.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }

                            if(carousel_item.content_margin.xs.trim() !== ""){
                                content_margin_xs = carousel_item.content_margin.xs.split(" ").map(item => {
                                    if(_.isEmpty(item)){
                                        return "0";
                                    }
                                    return item;
                                }).join(" ")
                            }
                        }

                    }
                #>

                #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                    <# if(_.isObject(carousel_item.title_fontsize)){ #>
                        font-size: {{ carousel_item.title_fontsize.md }}px;
                    <# } else { #>
                        font-size: {{ carousel_item.title_fontsize }}px;
                    <# } #>
                    <# if(_.isObject(carousel_item.title_lineheight)){ #>
                        line-height: {{ carousel_item.title_lineheight.md }}px;
                    <# } else { #>
                        line-height: {{ carousel_item.title_lineheight }}px;
                    <# } #>
                    color: {{ carousel_item.title_color }};
                    padding: {{ padding }};
                    margin: {{ margin }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                    <# if(_.isObject(carousel_item.content_fontsize)){ #>
                        font-size: {{ carousel_item.content_fontsize.md }}px;
                    <# } else { #>
                        font-size: {{ carousel_item.content_fontsize }}px;
                    <# } #>
                    <# if(_.isObject(carousel_item.content_lineheight)){ #>
                        line-height: {{ carousel_item.content_lineheight.md }}px;
                    <# } else { #>
                        line-height: {{ carousel_item.content_lineheight }}px;
                    <# } #>
                    color: {{ carousel_item.content_color }};
                    padding: {{ content_padding }};
                    margin: {{ content_margin }};
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                        <# if(_.isObject(carousel_item.title_fontsize)){ #>
                            font-size: {{ carousel_item.title_fontsize.sm }}px;
                        <# } #>
                        <# if(_.isObject(carousel_item.title_lineheight)){ #>
                            line-height: {{ carousel_item.title_lineheight.sm }}px;
                        <# } #>
                        padding: {{ padding_sm }};
                        margin: {{ margin_sm }};
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                        <# if(_.isObject(carousel_item.content_fontsize)){ #>
                            font-size: {{ carousel_item.content_fontsize.sm }}px;
                        <# } #>
                        <# if(_.isObject(carousel_item.content_lineheight)){ #>
                            line-height: {{ carousel_item.content_lineheight.sm }}px;
                        <# } #>
                        padding: {{ content_padding_sm }};
                        margin: {{ content_margin_sm }};
                    }
                }

                @media (max-width: 767px) {
                    #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption h2{
                        <# if(_.isObject(carousel_item.title_fontsize)){ #>
                            font-size: {{ carousel_item.title_fontsize.xs }}px;
                        <# } #>
                        <# if(_.isObject(carousel_item.title_lineheight)){ #>
                            line-height: {{ carousel_item.title_lineheight.xs }}px;
                        <# } #>
                        padding: {{ padding_xs }};
                        margin: {{ margin_xs }};
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-item-{{ data.id }}{{ key }} .jwpf-carousel-caption .jwpf-carousel-content{
                        <# if(_.isObject(carousel_item.content_fontsize)){ #>
                            font-size: {{ carousel_item.content_fontsize.xs }}px;
                        <# } #>
                        <# if(_.isObject(carousel_item.content_lineheight)){ #>
                            line-height: {{ carousel_item.content_lineheight.xs }}px;
                        <# } #>
                        padding: {{ content_padding_xs }};
                        margin: {{ content_margin_xs }};
                    }
                }
            <# }); #>
        </style>
        <div class="jwpf-carousel jwpf-slide {{ data.class }}" id="jwpf-carousel-{{ data.id }}" data-interval="{{ interval }}" {{{ autoplay }}}>
            <# if(data.controllers){ #>
                <ol class="jwpf-carousel-indicators">
                <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>
                    <# var active = (key == 0) ? "active" : ""; #>
                    <li data-jwpf-target="#jwpf-carousel-{{ data.id }}"  class="{{ active }}"  data-jwpf-slide-to="{{ key }}"></li>
                <# }); #>
                </ol>
            <# } #>
            <# if(data.preview){ #>
                <ol class="jwpf-carousel-indicators_preview jwpf-carousel-indicators">
                <# _.each(data.jw_carousel_item, function (preview_item, key1){ #>
                    <# var active = (key1 == 0) ? "active" : ""; #>
                    <li data-jwpf-target="#jwpf-carousel-{{ data.id }}"  class="{{ active }}"  data-jwpf-slide-to="{{ key1 }}">
                        <# if(preview_item.bg && preview_item.bg.indexOf("http://") == -1 && preview_item.bg.indexOf("https://") == -1){ #>
                            <img src=\'{{ pagefactory_base + preview_item.bg }}\' alt="{{ preview_item.title }}">
                        <# } else if(preview_item.bg){ #>
                            <img src=\'{{ preview_item.bg }}\' alt="{{ preview_item.title }}">
                        <# } #>
                    </li>
                <# }); #>
                </ol>
            <# } #>
            <div class="jwpf-carousel-inner {{ data.alignment }}">
                <# _.each(data.jw_carousel_item, function (carousel_item, key){ #>
                    <#
                        var classNames = (key == 0) ? "active" : "";
                        classNames += (carousel_item.bg) ? " jwpf-item-has-bg" : "";
                        classNames += " jwpf-item-"+data.id+""+key;
                    #>
                    <div class="jwpf-item {{ classNames }}">
                        <# if(carousel_item.bg && carousel_item.bg.indexOf("http://") == -1 && carousel_item.bg.indexOf("https://") == -1){ #>
                            <img src=\'{{ pagefactory_base + carousel_item.bg }}\' alt="{{ carousel_item.title }}">
                        <# } else if(carousel_item.bg){ #>
                            <img src=\'{{ carousel_item.bg }}\' alt="{{ carousel_item.title }}">
                        <# } #>
                        <div class="jwpf-carousel-item-inner">
                            <div class="jwpf-carousel-caption">
                                <div class="jwpf-carousel-text">
                                    <# if(carousel_item.title || carousel_item.content) { #>
                                        <# if(carousel_item.title) { #>
                                            <h2 class="jw-editable-content" id="addon-title-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carousel_item-{{key}}-title">{{ carousel_item.title }}</h2>
                                        <# } #>
                                        <div class="jwpf-carousel-content jw-editable-content" id="addon-content-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carousel_item-{{key}}-content">{{{ carousel_item.content }}}</div>
                                        <# if(carousel_item.button_text) { #>
                                            <#
                                                var btnClass = "";
                                                btnClass += carousel_item.button_type ? " jwpf-btn-"+carousel_item.button_type : " jwpf-btn-default" ;
                                                btnClass += carousel_item.button_size ? " jwpf-btn-"+carousel_item.button_size : "" ;
                                                btnClass += carousel_item.button_shape ? " jwpf-btn-"+carousel_item.button_shape : " jwpf-btn-rounded" ;
                                                btnClass += carousel_item.button_appearance ? " jwpf-btn-"+carousel_item.button_appearance : "" ;
                                                btnClass += carousel_item.button_block ? " "+carousel_item.button_block : "" ;
                                                var button_text = carousel_item.button_text;

                                                let icon_arr = (typeof carousel_item.button_icon !== "undefined" && carousel_item.button_icon) ? carousel_item.button_icon.split(" ") : "";
                                                let icon_name = icon_arr.length === 1 ? "fa "+carousel_item.button_icon : carousel_item.button_icon;

                                                if(carousel_item.button_icon_position == "left"){
                                                    button_text = (carousel_item.button_icon) ? \'<i class="\'+icon_name+\'"></i> \'+carousel_item.button_text : carousel_item.button_text ;
                                                }else{
                                                    button_text = (carousel_item.button_icon) ? carousel_item.button_text+\' <i class="\'+icon_name+\'"></i>\' : carousel_item.button_text ;
                                                }
                                            #>
                                            <a href=\'{{ carousel_item.button_url }}\' target="{{ carousel_item.button_target }}" id="btn-{{ data.id + "" + key}}" class="jwpf-btn{{ btnClass }}">{{{ button_text }}}</a>
                                        <# } #>
                                    <# } #>
                                </div>
                            </div>
                        </div>
                    </div>
                <# }); #>
            </div>
            <# if(data.arrows) { #>
                <a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow left jwpf-carousel-control" data-slide="prev"><i class="fa fa-chevron-left"></i></a>
                <a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow right jwpf-carousel-control" data-slide="next"><i class="fa fa-chevron-right"></i></a>
            <# } #>
        </div>
        ';

        return $output;
    }
}
