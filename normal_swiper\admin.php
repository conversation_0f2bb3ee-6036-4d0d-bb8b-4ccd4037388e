<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'normal_swiper',
        'title' => JText::_('左右轮播'),
        'desc' => JText::_('左右轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'image_carousel_layout' => array(
                    'type' => 'select',
                    'title' => JText::_('布局方式'),
                    'desc' => JText::_('布局方式'),
                    'values' => array(
                        'layout1' => '布局一',
                        // 'layout2' => '布局二',
                        'layout3' => '布局三',
                        'layout4' => '布局四',
                    ),
                    'std' => 'layout1',
                ),
                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播项样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播元素样式'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'ht_image_data' => array(
                            'type'  => 'checkbox',
                            'title' => '后台获取图片管理',
                            'desc' => '开启后台获取图片管理，选择对应要展示的后台添加的图片数据，无需在操作端再添加图片',
                            'std'   => 0
                        ),
                        'ht_image_id' => array(
                            'type' => 'select',
                            'title' => JText::_('选择图片'),
                            'values' => JwPageFactoryBase::getimageList($site_id, $company_id)['list'],
                            'depends' => array(
                                array('ht_image_data', '=', '1'),
                            ),
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                            'depends' => array(
                                array('ht_image_data', '=', '0'),
                            ),
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        // 详情页链接配置
                        'detail_page_link_config' => array(
                            'type' => 'select',
                            'title' => JText::_('详情页链接配置'),
                            'desc' => JText::_('详情页链接配置'),
                            'std' => 'link',
                            'values' => array(
                                'link' => '链接',
                                'page' => '当前站点页面',
                            ),
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '当前站点页面',
                            'desc' => '当前站点页面',
                            'std' => 0,
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('detail_page_link_config', '=', 'page')
                            ),
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1),
                                array('detail_page_link_config', '=', 'link'),
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

                        //                        字还是图片
                        'item_content_type' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => '文字',
                                    'value' => 'font'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'item_title' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项标题'),
                            'desc' => JText::_('轮播项标题'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'item_subtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项副标题'),
                            'desc' => JText::_('轮播项副标题'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('轮播项描述'),
                            'desc' => JText::_('轮播项描述'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'carousel_item_img' => array(
                            'type' => 'media',
                            'title' => '轮播图中间图片链接',
                            'desc' => '轮播图中间图片链接',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            )
                        ),
                        'carousel_item_img_width' => array(
                            'type' => 'slider',
                            'title' => '轮播图中间图片宽度所占比',
                            'desc' => '轮播图中间图片宽度所占比',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'max' => 100
                        ),
                    ),
                ),
                'jw_image_carousel_item_3' => array(
                    'title' => JText::_('轮播元素样式'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3')
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

                        //                        字还是图片
                        'item_content_type' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => '标题和简介',
                                    'value' => 'font'
                                ),
                                array(
                                    'label' => '内容',
                                    'value' => 'img'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'item_title' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项标题'),
                            'desc' => JText::_('轮播项标题'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        //文本块内容来源
                        'text_from' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('从客户端获取（客户端-内容管理-信息管理）'),
                            'desc' => JText::_('开启后选择客户端-内容管理-信息管理 模块中上传的资料名称对应文本块内容'),
                            'std' => 0
                        ),
                        // 文本块 内容类型
                        'text_id' => array(
                            'type' => 'select',
                            'title' => JText::_('选择文本块展示内容'),
                            'desc' => JText::_('对应客户端上传的内容名称，如果不选默认显示左侧编辑器的内容'),
                               'values' => JwPageFactoryBase::getTInfoList($site_id, $company_id)['list'],

                            'depends' => array(
                                array('text_from', '=', 1),
                            ),
                        ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('轮播项描述'),
                            'desc' => JText::_('轮播项描述'),
                            'depends' => array(
                                array('item_content_type', '=', 'font'),
                                array('text_from', '=', 0)
                            )
                        ),
                        'content_nav' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => 'logo',
                                    'value' => 'logo'
                                ),
                                array(
                                    'label' => '箭头',
                                    'value' => 'arrow'
                                ),
                                array(
                                    'label' => '标签',
                                    'value' => 'tags'
                                ),
                                array(
                                    'label' => '选项卡',
                                    'value' => 'tabs'
                                )
                            ),
                            'tabs' => true,
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            )
                        ),
                        'logo' => array(
                            'type' => 'media',
                            'title' => 'logo',
                            'desc' => 'logo',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img'),
                                array('content_nav', '=', 'logo')
                            )
                        ),
                        'arrow' => array(
                            'type' => 'media',
                            'title' => '箭头图片',
                            'desc' => '箭头图片',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img'),
                                array('content_nav', '=', 'arrow')
                            )
                        ),
                        'tags' => array(
                            'type' => 'text',
                            'title' => '标签（用英文逗号隔开）',
                            'desc' => '用英文逗号隔开',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img'),
                                array('content_nav', '=', 'tags')
                            )
                        ),
                        'tabs' => array(
                            'title' => '选项卡',
                            'std' => array(
                                array(
                                    'title' => '证书',
                                ),
                                array(
                                    'title' => '精彩图片',
                                )
                            ),
                            'attr' => array(
                                'title' => array(
                                    'type' => 'text',
                                    'title' => '选项卡标题',
                                    'desc' => '选项卡标题',
                                    'std' => '',
                                ),
                                'tabs_settings' => array(
                                    'type' => 'buttons',
                                    'title' => JText::_('选项卡设置'),
                                    'std' => 'normal',
                                    'values' => array(
                                        array(
                                            'label' => '正常',
                                            'value' => 'normal'
                                        ),
                                        array(
                                            'label' => '选中',
                                            'value' => 'active'
                                        ),
                                    ),
                                    'tabs' => true
                                ),
                                'tabs_normal_img' => array(
                                    'type' =>'media',
                                    'title' => '选项卡图片',
                                    'desc' => '选项卡图片',
                                    'std' => '',
                                    'depends' => array(
                                        array('tabs_settings', '=', 'normal')
                                    )
                                ),
                                'tabs_active_img' => array(
                                    'type' =>'media',
                                    'title' => '选项卡选中图片',
                                    'desc' => '选项卡选中图片',
                                   'std' => '',
                                    'depends' => array(
                                        array('tabs_settings', '=', 'active')
                                    )
                                ),
                                // 一屏显示几个项目
                                'swiper_items_num' => array(
                                    'type' =>'slider',
                                    'title' => JText::_('一屏显示几个项目'),
                                    'desc' => JText::_('一屏显示几个项目'),
                                    'std' => 3,
                                ),
                                'swiper_tabs_content' => array(
                                   'title' => '轮播',
                                   'std' => array(
                                      array(
                                          'swiper_img' => ''
                                      )
                                    ),
                                    'attr' => array(
                                        'ht_image_data' => array(
                                            'type'  => 'checkbox',
                                            'title' => '后台获取图片管理',
                                            'desc' => '开启后台获取图片管理，选择对应要展示的后台添加的图片数据，无需在操作端再添加图片',
                                            'std'   => 0
                                        ),
                                        'ht_image_id' => array(
                                            'type' => 'select',
                                            'title' => JText::_('选择图片'),
                                            'values' => JwPageFactoryBase::getimageList($site_id, $company_id)['list'],
                                            'depends' => array(
                                                array('ht_image_data', '=', '1'),
                                            ),
                                        ),
                                        'swiper_img' => array(
                                            'type' => 'media',
                                            'title' => '轮播项图片',
                                            'desc' => '轮播项图片',
                                            'std' => '',
                                            'depends' => array(
                                                array('ht_image_data', '=', '0'),
                                            )
                                        )
                                    )
                                )
                            ),
                            'depends' => array(
                                array('item_content_type', '=', 'img'),
                                array('content_nav', '=', 'tabs')
                            )
                        ),
                    ),
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'min' => 100,
                    'max' => 2000,
                    'std' => array(
                        'md' => 830,
                        'sm' => 830,
                        'xs' => ''
                    ),
                    'responsive' => true,
                ),
                // 一屏显示的轮播数
                'swiper_items_num' => array(
                    'type' =>'slider',
                    'title' => JText::_('一屏显示的轮播数'),
                    'desc' => JText::_('一屏显示的轮播数'),
                    'std' => 1,
                    'min' => 1,
                    'max' => 6,
                    'responsive' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    )
                ),
                // 轮播项间隔
                'swiper_items_space' => array(
                    'type' =>'slider',
                    'title' => JText::_('轮播项间隔'),
                    'desc' => JText::_('轮播项间隔，开启“轮播项是否添加边框”后，如果不想要两个轮播项之间的边框变粗，可以设置为-1'),
                    'std' => 30,
                    'min' => -1,
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    )
                ),
                'img_4' => array(
                    'type' =>'media',
                    'title' => '图片',
                    'desc' => '图片',
                   'std' => '',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                'carousel_height_4' => array(
                    'type' => 'slider',
                    'title' => JText::_('插件高度'),
                    'desc' => JText::_('插件高度'),
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    ),
                    'min' => 100,
                    'max' => 2000,
                    'std' => 400
                ),
                'items_num_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('显示项目数'),
                    'desc' => JText::_('显示项目数'),
                    'std' => array('md' => 4, 'sm' => 2, 'xs' => 1),
                    'responsive' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                ),
                // 每项间隔
                'items_space_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('项目间距'),
                    'desc' => JText::_('项目间距'),
                    'std' => 30,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 1
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 2500
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 4500
                ),
                // 图片显示方式
                'img_show_type' => array(
                    'type' => 'select',
                    'title' => JText::_('图片显示方式'),
                    'desc' => JText::_('图片显示方式'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'values' => array(
                        'cover' => JText::_('超出裁剪'),
                        'contain' => JText::_('超出缩放'),
                        'fill' => JText::_('占满拉伸'),
                    ),
                    'std' => 'cover',
                ),
                // 轮播项是否添加边框
                'swiper_items_border_layout1' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('轮播项是否添加边框'),
                    'desc' => JText::_('轮播项是否添加边框，开启后，如果不想要两个轮播项之间的边框变粗，可以设置“轮播项间隔”为-1'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout1'),
                    ),
                    'std' => 0,
                ),
                // 轮播项边框宽度
                'swiper_items_border_layout1_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播项边框宽度'),
                    'desc' => JText::_('轮播项边框宽度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout1'),
                        array('swiper_items_border_layout1', '=', '1'),
                    ),
                    'std' => 1,
                ),
                // 轮播项边框圆角
                'swiper_items_border_layout1_radius' => array(
                    'type' => 'margin',
                    'title' => JText::_('轮播项边框圆角'),
                    'desc' => JText::_('轮播项边框圆角'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout1'),
                        array('swiper_items_border_layout1', '=', '1'),
                    ),
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                ),
                // 轮播项边框颜色
                'swiper_items_border_layout1_color' => array(
                    'type' => 'color',
                    'title' => JText::_('轮播项边框颜色'),
                    'desc' => JText::_('轮播项边框颜色'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '=', 'layout1'),
                        array('swiper_items_border_layout1', '=', '1'),
                    ),
                    'std' => '#000000',
                ),
                // 是否固定图片宽高
                'img_fixed' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否固定图片宽高'),
                    'desc' => JText::_('是否固定图片宽高'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'std' => 0
                ),
                // 图片宽度
                'img_width' => array(
                    'type' => 'number',
                    'title' => JText::_('图片宽度'),
                    'desc' => JText::_('图片宽度'),
                    'responsive' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('img_fixed', '=', '1'),
                    ),
                    'std' => 200,
                ),
                // 图片高度
                'img_height' => array(
                    'type' => 'number',
                    'title' => JText::_('图片高度'),
                    'desc' => JText::_('图片高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('img_fixed', '=', '1'),
                    ),
                    'std' => 250,
                ),
                'item_content_in_item' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('内容是否覆盖在轮播图上'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'std' => 0,
                ),

                'item_content_verti_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容垂直对齐'),
                    'desc' => JText::_('内容垂直对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'middle' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MIDDLE'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                    ),
                    'std' => 'middle',
                ),
                'item_content_hori_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容水平对齐'),
                    'desc' => JText::_('内容水平对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'values' => array(
                        'left' => JText::_('左对齐'),
                        'center' => JText::_('居中'),
                        'right' => JText::_('右对齐'),
                    ),
                    'std' => 'center',
                ),
                // 轮播项内容内边距
                'item_content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容内边距'),
                    'desc' => JText::_('内容内边距'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                ),
                // 是否启用动画
                'carousel_animation' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否启用动画'),
                    'desc' => JText::_('是否启用动画'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 1
                ),
                // 内容是否添加背景
                'item_content_bg' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('内容是否添加背景'),
                    'desc' => JText::_('内容是否添加背景'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 0
                ),
                // 轮播项内容背景
                'item_content_bg_style' => array(
                    'type' => 'buttons',
                    'title' => '内容背景选项',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '颜色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('item_content_bg', '!=', 0),
                    ),
                ),
                // 纯色
                'item_content_bgColor' => array(
                    'type' => 'color',
                    'title' => '内容背景颜色',
                    'std' => 'rgba(0, 0, 0, 0.6)',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('item_content_bg_style', '=', 'color'),
                        array('item_content_bg', '!=', 0),
                    ),
                ),
                // 渐变
                'item_content_bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '内容背景',
                    'std' => array(
                        "color" => "rgba(0, 0, 0, 0.6)",
                        "color2" => "transparent",
                        "deg" => "0",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('item_content_bg_style', '=', 'gradient'),
                        array('item_content_bg', '!=', 0),
                    ),
                ),
                'content_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('内容布局'),
                    'desc' => JText::_('副标题样式3无效'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 'title_style',
                    'values' => array(
                        array(
                            'label' => '标题',
                            'value' => 'title_style'
                        ),
                        array(
                            'label' => '副标题',
                            'value' => 'subtitle_style'
                        ),
                        array(
                            'label' => '描述',
                            'value' => 'desc_style'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'content' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),
                'content_3' => array(
                    'type' => 'separator',
                    'title' => JText::_('文字内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '=', 'layout3')
                    ),
                ),
                //Title style
                'content_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'responsive' => true,
                    'max' => 100,
                ),
                'content_title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => '60',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'max' => 100,
                ),
                // 标题显示字数
                'content_title_fontsize_limit' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题显示字数'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'max' => 100,
                ),
                'content_title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('字体'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-heading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'content_title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'content_title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'content_title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 'red',
                ),

                'content_title_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_height_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('标题高度'),
                    'desc' => JText::_('标题高度'),
                    'responsive' => true,
                    'std' => 76,
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'title_style')
                    ),
                ),
                // 标题超出隐藏的行数
                'title_hide_line_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('标题超出隐藏的行数'),
                    'desc' => JText::_('标题超出隐藏的行数'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'title_style')
                    ),
                   'std' => 2,
                   'max' => 10,
                ),

                //Subtitle style
                'content_subtitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'content_subtitle_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'max' => 400,
                ),
                // 标题显示字数
                'content_subtitle_fontsize_limit' => array(
                    'type' => 'slider',
                    'title' => JText::_('副标题显示字数'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'max' => 100,
                ),
                'content_subtitle_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-subheading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),

                'content_subtitle_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),

                'content_subtitle_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),

                'content_subtitle_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'std' => 'red'
                ),

                //Description style
                'description_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'description_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'max' => 400,
                ),
                // 描述显示字数
                'description_fontsize_limit' => array(
                    'type' => 'slider',
                    'title' => JText::_('描述显示字数'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'max' => 100,
                ),
                'description_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-description { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'description_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'description_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),

                'description_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 'red'
                ),
                'description_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => '0px 0px 0px 0px',
                    'max' => 400,
                    'responsive' => true
                ),
                'description_height_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('描述高度'),
                    'desc' => JText::_('描述高度'),
                    'std' => 168,
                    'responsive' => true,
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'desc_style')
                    ),
                ),
                'description_hide_line_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('描述超出隐藏的行数'),
                    'desc' => JText::_('描述超出隐藏的行数'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'desc_style')
                    ),
                  'std' => 6,
                  'max' => 10,
                ),
                // logo外边距
                'content_img_margin_3' => array(
                    'type' =>'margin',
                    'title' => JText::_('logo外边距'),
                    'desc' => JText::_('logo外边距'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3')
                    ),
                  'std' => '0px 0px 21px 0px'
                ),
                // logo高度
                'content_img_height_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('logo高度'),
                    'desc' => JText::_('logo高度'),
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3')
                    ),
                    'std' => 44
                ),
                // 箭头图片宽度
                'content_img_width_3' => array(
                    'type' =>'slider',
                    'title' => JText::_('箭头图片宽度'),
                    'desc' => JText::_('箭头图片宽度'),
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3')
                    ),
                   'std' => 25,
                ),
                'inner_desc_3' => array(
                    'type' =>'separator',
                    'title' => JText::_('内容描述布局'),
                    'desc' => JText::_('内容描述布局'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'img_style')
                    ),
                ),
                'description_fontsize_3_inner' => array(
                    'type' => 'slider',
                    'title' => JText::_('描述字号'),
                    'std' => '16',
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'description_lineheight_3_inner' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                    'max' => 400,
                ),
                'description_font_family_3_inner' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-description { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                ),

                'description_font_style_3_inner' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                ),
                'description_text_color_3_inner' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                    'std' => 'red'
                ),
                'description_margin_3_inner' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                    'std' => '0px 0px 0px 0px',
                    'max' => 400,
                    'responsive' => true
                ),
                'description_height_3_inner' => array(
                    'type' =>'slider',
                    'title' => JText::_('描述高度'),
                    'desc' => JText::_('描述高度'),
                    'std' => 56,
                    'responsive' => true,
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '=', 'layout3'),
                    ),
                ),
                'description_hide_line_3_inner' => array(
                    'type' =>'slider',
                    'title' => JText::_('描述超出隐藏的行数'),
                    'desc' => JText::_('描述超出隐藏的行数'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '=', 'layout3'),
                        array('content_style', '=', 'img_style')
                    ),
                  'std' => 6,
                  'max' => 10,
                ),
                'content_img_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('内容图片外边距'),
                    'desc' => JText::_('内容图片外边距'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('content_style', '=', 'img_style'),
                        array('carousel_options', '=', 'item_style'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3')
                    ),
                    'std' => '0px 0px 0px 0px',
                    'max' => 400,
                    'responsive' => true
                ),

                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器设置'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),

                'carousel_navigation' => array(
                    'type' => 'buttons',
                    'title' => JText::_('控制器选项'),
                    'desc' => JText::_('样式三翻页按钮无效'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'std' => 'bullet_controller',
                    'values' => array(
                        array(
                            'label' => '分页器',
                            'value' => 'bullet_controller'
                        ),
                        array(
                            'label' => '翻页按钮',
                            'value' => 'arrow_controller'
                        )
                    ),
                ),
                'carousel_bullet' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示分页器'),
                    'desc' => JText::_('是否显示分页器'),
                    'std' => 1,
                    'depends' => array(
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                // 移动端是否显示分页器
                'bullet_position_hori_mobile' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('移动端是否显示分页器'),
                    'desc' => JText::_('移动端是否显示分页器'),
                    'std' => 1,
                    'depends' => array(
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('carousel_bullet', '=', 1),
                    ),
                ),
                'bullet_position_hori' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器距轮播容器右端距离'),
                    'desc' => JText::_('分页器距轮播容器右端距离'),
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('carousel_bullet_center', '!=', 1),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'min' => -1000,
                    'max' => 1000,
                    'std' => 115,
                    'responsive' => true,
                ),
                'bullet_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('分页器样式'),
                    'std' => 'normal_bullet',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_bullet'
                        ),
                        array(
                            'label' => '选中状态',
                            'value' => 'active_bullet'
                        )
                    ),
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                'bullet_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器每项之间的距离'),
                    'max' => 100,
                    'min' => 1,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 15,
                ),
                'bullet_opacity' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器不透明度'),
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 8,
                ),
                'bullet_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器高度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 6,
                ),
                'bullet_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器宽度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 6,
                ),
                'bullet_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                    )
                ),
                'bullet_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'std' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    )
                ),
                'bullet_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'normal_bullet'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    )
                ),
                //Bullet hover
                'bullet_active_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器高度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'active_bullet'),
                    ),
                    'std' => 23,
                ),
                'bullet_active_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器宽度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'active_bullet'),
                    ),
                    'std' => 23,
                ),
                'bullet_active_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),
                'bullet_active_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'std' => 1,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),
                'bullet_active_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_navigation', '=', 'bullet_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),

                // Arrow style
                'carousel_arrow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否展示翻页按钮'),
                    'desc' => JText::_('是否展示翻页按钮'),
                    'std' => 1,
                    'depends' => array(
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                // 移动端是否显示翻页按钮
                'mobile_show_arrow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('移动端是否显示翻页按钮'),
                    'desc' => JText::_('移动端是否显示翻页按钮'),
                    'std' => 0,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                ),
                'arrow_position_verti' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮距容器顶端距离'),
                    'desc' => JText::_('翻页按钮距容器顶端距离'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('image_carousel_layout', '!=', 'layout3'),
                    ),
                    'min' => -1000,
                    'max' => 1000,
                    'std' => '',
                    'responsive' => true,
                ),
                // 翻页按钮布局选项
                'arrow_position_hori_verti' => array(
                    'type' => 'select',
                    'title' => JText::_('翻页按钮布局选项'),
                    'desc' => JText::_('翻页按钮布局选项'),
                    'std' => 'both',
                    'values' => array(
                        'both' => '左右',
                        'left' => '左',
                        'right' => '右',
                    ),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('carousel_navigation', '=', 'arrow_controller'),
                    )
                ),
                'arrow_position_hori' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮距容器左右边界距离'),
                    'desc' => JText::_('当“翻页按钮布局选项”选择“左右”时，左右翻页按钮距容器左右边界距离相等；当“翻页按钮布局选项”选择“左”或“右”时，调整按钮左或右边距；'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                    'min' => -1000,
                    'max' => 1000,
                    'std' => 242,
                    'responsive' => true,
                ),
                // 翻页按钮间距
                'arrow_gap' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮间距'),
                    'desc' => JText::_('翻页按钮间距'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout3'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_position_hori_verti', '!=', 'both'),
                    ),
                    'min' => -1000,
                    'max' => 1000,
                    'std' => 0,
                    'responsive' => true,
                ),
                'carousel_arrow_type' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮类型'),
                    'desc' => JText::_('翻页按钮类型'),
                    'std' => 'icon',
                    'values' => array(
                        array(
                            'label' => '箭头图标',
                            'value' => 'icon'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        )
                    ),
                    'depends' => array(
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('carousel_arrow', '=', 1)
                    ),
                    'tabs' => true
                ),
                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                // 是否禁用动画
                'arrow_animation' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否禁用动画'),
                    'desc' => JText::_('是否禁用动画'),
                    'std' => 1,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                    ),
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 60,
                ),
                'arrow_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 60,
                ),
                'arrow_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_background_4' => array(
                    'type' => 'media',
                    'title' => JText::_('左箭头图片，右箭头自动翻转'),
                    'std' => '',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                'arrow_hover_background_4' => array(
                    'type' => 'media',
                    'title' => JText::_('左箭头图片，右箭头自动翻转'),
                    'std' => '',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                // 箭头大小
                'arrow_width_4' => array(
                    'type' =>'slider',
                    'title' => JText::_('箭头宽度'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '48',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                'arrow_height_4' => array(
                    'type' =>'slider',
                    'title' => JText::_('箭头高度'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '48',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                'arrow_gap_4' => array(
                    'type' =>'slider',
                    'title' => JText::_('箭头间距'),
                    'max' => 500,
                    'min' => 0,
                    'std' => '32',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                // 箭头容器外边距
                'arrow_margin_4' => array(
                    'type' =>'margin',
                    'title' => JText::_('箭头容器外边距'),
                    'desc' => JText::_('箭头容器外边距'),
                    'placeholder' => '10px',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    ),
                   'std' => '53px 0px 0px 0px',
                  'max' => 400
                ),
                // 每次移动距离
                'arrow_move_distance_4' => array(
                    'type' =>'slider',
                    'title' => JText::_('每次移动距离'),
                   'max' => 100,
                   'min' => 0,
                   'std' => '100',
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout4'),
                    )
                ),
                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮圆角'),
                    'desc' => JText::_('按钮圆角'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                //Arrow hover
                'arrow_hover_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    ),
                    'std' => 60,
                ),
                'arrow_hover_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    ),
                    'std' => 60,
                ),
                'arrow_hover_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_hover_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮边框宽度'),
                    'std' => '',
                    'min' => 0,
                    'max' => 20,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_hover_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮圆角'),
                    'desc' => JText::_('按钮圆角'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'icon')
                    )
                ),
                'arrow_img' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'desc' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'std' => '/components/com_jwpagefactory/addons/normal_swiper/assets/images/normal_left.png',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type', '=', 'img')
                    )
                ),
                'arrow_img_active' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'desc' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'std' => '/components/com_jwpagefactory/addons/normal_swiper/assets/images/active_left.png',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout4'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type', '=', 'img')
                    )
                ),
            ),
        ),
    )
);
