<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>or<PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonRoll_plug extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $roll_plug_type = (isset($settings->roll_plug_type) && $settings->roll_plug_type) ? $settings->roll_plug_type : 'type1';

        $output = '';
        if ($roll_plug_type == 'type1') {
            $jw_tab_item_goods = (isset($settings->jw_tab_item_goods) && $settings->jw_tab_item_goods) ? $settings->jw_tab_item_goods : '';
            $margin_bottom_type1 = (isset($settings->margin_bottom_type1) && $settings->margin_bottom_type1) ? $settings->margin_bottom_type1 : 0;
            $height_pc_type1 = (isset($settings->height_pc_type1) && $settings->height_pc_type1) ? $settings->height_pc_type1 : 0;
            $height_sj_type1 = (isset($settings->height_sj_type1) && $settings->height_sj_type1) ? $settings->height_sj_type1 : 0;
            $font_size_pc_type1 = (isset($settings->font_size_pc_type1) && $settings->font_size_pc_type1) ? $settings->font_size_pc_type1 : 0;
            $font_size_sj_type1 = (isset($settings->font_size_sj_type1) && $settings->font_size_sj_type1) ? $settings->font_size_sj_type1 : 0;
            $font_color_odd_type1 = (isset($settings->font_color_odd_type1) && $settings->font_color_odd_type1) ? $settings->font_color_odd_type1 : 0;
            $font_color_even_type1 = (isset($settings->font_color_even_type1) && $settings->font_color_even_type1) ? $settings->font_color_even_type1 : 0;
            $even_button_xf_type1 = (isset($settings->even_button_xf_type1) && $settings->even_button_xf_type1) ? $settings->even_button_xf_type1 : 'xf';
            $roll_fx_type1 = (isset($settings->roll_fx_type1) && $settings->roll_fx_type1) ? $settings->roll_fx_type1 : 'left';
            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    ' . $addon_id . ' marquee {
                        font-weight: bolder;
                        font-size: '.$font_size_pc_type1.'px;
                        color: white;
                        margin-bottom: '.$margin_bottom_type1.'px;
                    }
                    ' . $addon_id . ' marquee img{
                        width: auto;
                        height: 35px;
                        margin-right: 20px;
                        padding-left: 20px;
                        display: unset;
                    }
                    ' . $addon_id . ' .child {
                        padding-left: 20px;
                        padding-right: 70px;
                        height: '.$height_pc_type1.'px;
                        margin-right: 40px;
                        border-radius:25px 0px 25px 25px;
                        display: inline-block;
                        line-height: '.$height_pc_type1.'px;
                    }
                ';
                if($even_button_xf_type1=='xf')
                {
                    $output .='
                        ' . $addon_id . ' marquee:nth-child(even) .big-p .child:nth-child(odd) {
                            background-color: '.$font_color_odd_type1.';
                        }
                        ' . $addon_id . ' marquee:nth-child(even) .big-p .child:nth-child(even) {
                            background-color: '.$font_color_even_type1.';
                        }
                        ' . $addon_id . ' .child:nth-child(even) {
                            background-color: '.$font_color_odd_type1.';
                        }
                        ' . $addon_id . ' .child:nth-child(odd) {
                            background-color: '.$font_color_even_type1.';
                        }
                    ';
                }
                else
                {
                    $output .='
                        ' . $addon_id . ' .child:nth-child(even) {
                            background-color: '.$font_color_even_type1.';
                        }
                        ' . $addon_id . ' .child:nth-child(odd) {
                            background-color: '.$font_color_odd_type1.';
                        }
                    ';
                }
                $output .='
                    
                    @media only screen and (max-width: 768px){
                        ' . $addon_id . ' .child {
                            height: '.$height_sj_type1.'px;
                            line-height: '.$height_sj_type1.'px;
                            padding-right: 15px;
                            border-radius:12px 0px 12px 12px;
                        }
                        ' . $addon_id . ' marquee {
                            font-size: '.$font_size_sj_type1.'px;
                        }
                        ' . $addon_id . ' marquee img{
                            width: auto;
                            height: 22px;
                            margin-right: 10px;
                            padding-left: 0px;
                            display: unset;
                        }
                    }
                </style>
            ';
            foreach ($jw_tab_item_goods as $k => $v) {
                $output .= '
                    <marquee direction="'.$roll_fx_type1.'" scrollamount="'.$v->scrollamount.'">
                        <p class="big-p">
                ';
                foreach($v->content as $kk => $vv)
                {   
                    $output .= '
                        <span class="child">
                            <img src="'.$vv->icon.'" alt="">
                            <span>'.$vv->title.'</span>
                        </span>
                    ';
                }
                $output .= '
                        </p>
                    </marquee>
                ';
            }
        }
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
        $settings = $this->addon->settings;
        $service_product_type = (isset($settings->service_product_type) && $settings->service_product_type) ? $settings->service_product_type : 'type1';

        $scripts = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        if ($service_product_type == 'type2') {
            array_push($scripts, JURI::base(true) . '/components/com_jwpagefactory/addons/service_product/assets/js/swiper.animate1.0.3.min.js');
            array_push($scripts, JURI::base(true) . '/components/com_jwpagefactory/addons/service_product/assets/js/wow.min.js');
        }
        return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
