<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 14:00:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonNow_time extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $date_title = (isset($settings->date_title) && $settings->date_title) ? $settings->date_title : '';
        $date_str = (isset($settings->date_str) && $settings->date_str) ? $settings->date_str : '';
        $date_title_after = (isset($settings->date_title_after) && $settings->date_title_after) ? $settings->date_title_after : '';

        $output .= "<div class='date-box'>" . $date_title . "<span id='year'></span>" . $date_str . "<span id='month'></span>" . $date_str . "<span id='date'></span>" . $date_title_after . "</div>";

        return $output;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $date_style = (isset($settings->date_style) && $settings->date_style) ? $settings->date_style : 'type01';
        $date_align = (isset($settings->date_align) && $settings->date_align) ? $settings->date_align : 'left';
        $date_color = (isset($settings->date_color) && $settings->date_color) ? $settings->date_color : '#333';
        $date_fontsize = (isset($settings->date_fontsize) && $settings->date_fontsize) ? $settings->date_fontsize : 14;
        $date_lineHeight = (isset($settings->date_lineHeight) && $settings->date_lineHeight) ? $settings->date_lineHeight : 32;

        $css = "
            {$addonId} .date-box {
                font-size: {$date_fontsize}px;
                line-height: {$date_lineHeight}px;
                color: {$date_color};
                text-align: {$date_align};
            }
        ";
        return $css;
    }

    public function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $js = "
            jQuery(document).ready(function($){
                " . $this->jsStr() . "
                $('" . $addonId . " #year').html(year);
                $('" . $addonId . " #month').html(month);
                $('" . $addonId . " #date').html(date);
            })
        ";
        return $js;
    }

    // js公共
    public function jsStr() {
        return "
            var datetime = new Date();
            //console.log(datetime);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? '0' + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? '0' + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? '0' + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? '0' + datetime.getSeconds() : datetime.getSeconds();
            // console.log(year);
            // console.log(month);
            // console.log(date);
            // console.log(hour);
            // console.log(minute);
            // console.log(second);
        ";
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = "
            <# 
                " . self::jsStr() . "
                var addonId = '#jwpf-addon-' + data.id;
                // 日期布局
                var date_style = data.date_style || 'type01';
                // 日期前文字
                var date_title = data.date_title || '';
                // 日期分割字符
                var date_str = data.date_str || '';
                // 日期后文字
                var date_title_after = data.date_title_after || '';
                // 文字对齐方式
                var date_align = data.date_align || 'left';
                // 文字颜色
                var date_color = data.date_color || '#333';
                // 文字大小
                var date_fontsize = data.date_fontsize || 14;
                // 文字行高
                var date_lineHeight = data.date_lineHeight || 32;
            #>
            <style>
                {{ addonId }} .date-box {
                    font-size: {{ date_fontsize }}px;
                    line-height: {{ date_lineHeight }}px;
                    color: {{ date_color }};
                    text-align: {{ date_align }};
                }
            </style>
            <div class='date-box'>{{date_title}}{{year}}{{date_str}}{{month}}{{date_str}}{{date}}{{date_title_after}}</div>
        ";
        return $output;
    }
}