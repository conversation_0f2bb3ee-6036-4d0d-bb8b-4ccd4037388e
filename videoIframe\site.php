<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonVideoIframe extends JwpagefactoryAddons
{
    //在预览页面中渲染
    public function render()
    {
        $settings = $this->addon->settings;

        //此处载入所有配置项变量
        //$class = (isset($settings->class) && $settings->class) ? $settings->class : '';

        //配置项变量
        $links = (isset($settings->links) && $settings->links) ? $settings->links : 'https://v.qq.com/txp/iframe/player.html?vid=e0020p564tf';
        $center = (isset($settings->center)) ? $settings->center : 0;//是否居中
        $quanp = (isset($settings->quanp)) ? $settings->quanp : 1;//是否全屏
        $sp_width = (isset($settings->sp_width) && $settings->sp_width) ? $settings->sp_width : '1000';
        $sp_width_sm = (isset($settings->sp_width_sm) && $settings->sp_width_sm) ? $settings->sp_width_sm : '600';
        $sp_width_xs = (isset($settings->sp_width_xs) && $settings->sp_width_xs) ? $settings->sp_width_xs : '300';

        $sp_height = (isset($settings->sp_height) && $settings->sp_height) ? $settings->sp_height : '550';
        $sp_height_sm = (isset($settings->sp_height_sm) && $settings->sp_height_sm) ? $settings->sp_height_sm : '550';
        $sp_height_xs = (isset($settings->sp_height_xs) && $settings->sp_height_xs) ? $settings->sp_height_xs : '550';

        $start_paly = (isset($settings->start_paly)) ? $settings->start_paly : 0;//开启自动播放
        if($start_paly==1){
            $links=$links."&autoplay=true";
        }
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        
        $output = '';
        $output .= '';
        $output .= '<style>';

        $output .=  $addon_id. ' iframe {';

            if($quanp==0){
                $output .= ' width: '.$sp_width.'px !important;';
            }else{
                $output .= ' width:100%!important;';
            }

            if($sp_height){
                $output .= 'height: '.$sp_height.'px !important;';
            } else { 
                $output .= 'height: 550px !important;';
            } 
                
            if($center==1){
                $output .= 'display:block;margin:0 auto;';
            }
                
        $output .=' }' ;

        $output .=  '@media (min-width: 768px) and (max-width: 991px) {';
        $output .=   $addon_id. ' iframe {';
                    if($quanp==0){
                        $output .= ' width: '.$sp_width_sm.'px !important;';
                    }else{
                        $output .= ' width:100%!important;';
                    }

                    if($sp_height){
                        $output .= 'height: '.$sp_height_sm.'px !important;';
                    } else { 
                        $output .= 'height: 550px !important;';
                    } 
                        
                    if($center==1){
                        $output .= 'display:block;margin:0 auto;';
                    }
            $output .=  '}
        }     
        @media (max-width: 767px) {';
        $output .=   $addon_id. ' iframe {';

                if($quanp==0){
                    $output .= ' width: '.$sp_width_xs.'px !important;';
                }else{
                    $output .= ' width:100%!important;';
                }

                if($sp_height){
                    $output .= 'height: '.$sp_height_xs.'px !important;';
                } else { 
                    $output .= 'height: 550px !important;';
                } 

            $output .=  '}
        }';


        $output .= '</style>';
        $output .= '<iframe src="' . $links . '" frameborder="0"></iframe>';
        $output .= '';


        return $output;
    }

    //引用js文件，同时作用于编辑器和预览页
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.countdown.min.js'

        );
        return $scripts;
    }

    //引用css文件，同时作用于编辑器和预览页
    public function stylesheets()
    {
        $style_sheet = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css'

        );
        return $style_sheet;
    }

    //在预览页面中使用的JS脚本
    public function js()
    {
        $js = '';
        return $js;
    }

    //在预览页面中使用的css样式
    public function css()
    {
        $css = '';
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = '';
        $output .= '
        <#
            var addonId = "jwpf-addon-"+data.id;
            let links = (!_.isEmpty(data.links) && data.links) ? data.links : "https://v.qq.com/txp/iframe/player.html?vid=e0020p564tf";
            if(data.start_paly==1){
                links=links+"&autoplay=true";
            }

        #>';
        $output .= '<style>';
        $output .= '

            #{{ addonId }} iframe { 
                <# if(data.quanp==0){ #>

                    <# if(_.isObject(data.sp_width)){ #>
                        width: {{ data.sp_width.md }}px !important;
                    <# } else { #>
                        width: {{ data.sp_width }}px !important;
                    <# } #>
                    
                <# }else{ #>
                    width:100%!important;
                <# } #>

                <# if(_.isObject(data.sp_height)){ #>
                    height: {{ data.sp_height.md }}px !important;
                <# } else { #>
                    height: {{ data.sp_height }}px !important;
                <# } #>
                
                <# if(data.center==1){ #>
                    display:block;margin:0 auto;
                <# } #>
                
            }

            @media (min-width: 768px) and (max-width: 991px) {
                #{{ addonId }} iframe {
                    <# if(data.quanp==0){ #>

                        <# if(_.isObject(data.sp_width)){ #>
                            width: {{ data.sp_width.sm }}px !important;
                        <# } else { #>
                            width: {{ data.sp_width }}px !important;
                        <# } #>
                        
                    <# }else{ #>
                        width:100%!important;
                    <# } #>

                    <# if(_.isObject(data.sp_height)){ #>
                        height: {{ data.sp_height.sm }}px !important;
                    <# } else { #>
                        height: {{ data.sp_height }}px !important;
                    <# } #>
                }
            }
                
            @media (max-width: 767px) {
                #{{ addonId }} iframe {

                    <# if(data.quanp==0){ #>

                        <# if(_.isObject(data.sp_width)){ #>
                            width: {{ data.sp_width.xs }}px !important;
                        <# } else { #>
                            width: {{ data.sp_width }}px !important;
                        <# } #>
                        
                    <# }else{ #>
                        width:100%!important;
                    <# } #>

                    <# if(_.isObject(data.sp_height)){ #>
                        height: {{ data.sp_height.xs }}px !important;
                    <# } else { #>
                        height: {{ data.sp_height }}px !important;
                    <# } #>
                }
            }
            
        ';
        $output .= '</style>';
        $output .= '<iframe src=\'{{links}}\' frameborder="0"></iframe>';


        $output .= '';
        return $output;
    }

}