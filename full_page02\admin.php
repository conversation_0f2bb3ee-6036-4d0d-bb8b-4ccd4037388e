<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');
$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'full_page02',
        'title' => '全屏滚动网页-2',
        'desc' => '全屏滚动网页(支持更改文字/图片)',
        'category' => '全屏滚动网页',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '切换项配置'
                ),
                'slide_page' => array(
                    'type' => 'buttons',
                    'title' => '全屏每页配置（切换效果预览页查看）',
                    'std' => 'page01',
                    'values' => array(
                        array(
                            'label' => '第一页',
                            'value' => 'page01'
                        ),
                        array(
                            'label' => '第二页',
                            'value' => 'page02'
                        ),
                        array(
                            'label' => '第三页',
                            'value' => 'page03'
                        ),
                        array(
                            'label' => '第四页',
                            'value' => 'page04'
                        ),
                        array(
                            'label' => '第五页',
                            'value' => 'page05'
                        ),
                        array(
                            'label' => '第六页',
                            'value' => 'page06'
                        ),
                        array(
                            'label' => '第七页',
                            'value' => 'page07'
                        ),
                    ),
                    'tabs' => true,
                ),
                'page01_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页面',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_image_item' => array(
                    'title' => '切换图项目（预览查看切换效果）',
                    'attr' => array(
                        'slider_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '轮播图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211207/dd28120daea31ff715b41cde162e580d.jpg',
                        ),
                    ),
                    'std' => array(
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/dd28120daea31ff715b41cde162e580d.jpg',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/63adf86827f4bbe06594873e4e52e9f5.jpg',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/8c617a2b61cb943f0fc94382060587e7.jpg',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/a6d5c68adedd7a90490bd29b22640c64.jpg',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                        array('page01_hide', '!=', 1),
                    ),
                ),
                'page02_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页面',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img_left' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '左侧背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/e9bf64334c2bfac98533991beede0ee4.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page02_left_click' => array(
                    'type' => 'checkbox',
                    'title' => '关闭左侧点击视频弹窗事件',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page02_video_src' => array(
                    'type' => 'text',
                    'title' => '弹窗视频链接地址',
                    'std' => 'https://v.qq.com/txp/iframe/player.html?vid=q3165m1tvfu',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                        array('page02_left_click', '!=', 1)
                    ),
                ),
                'page02_slider_img_right' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '右侧背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/5e0783cc2b6d1edc841a3a94cf543019.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page02_right_title' => array(
                    'type' => 'text',
                    'title' => '右侧内容标题',
                    'std' => '专业的幼教',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page02_right_desc' => array(
                    'type' => 'text',
                    'title' => '右侧内容副标题',
                    'std' => '践行者',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page02_right_content' => array(
                    'type' => 'editor',
                    'title' => '右侧内容',
                    'std' => '<p style="text-indent:32px;">
                            沃德兰·东大教育集团是致力于幼儿教育、培训，幼儿师资培训，幼儿教育用品等多领域发展的综合性教育机构，目前在中国拥有多家直营幼儿园。</p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团历经二十余年的辛勤耕耘，不断整合积淀了丰富的教育资源，与华东师范大学、北京师范大学、陈鹤琴教育思想研究会、复旦大学出版社等众多知名院校、专家学者，开展教学教研实践活动，研发出全套《幼儿园体验课程活动方案指导手册》（教师参考用书）以及《五大领域体验活动手册》（幼儿操作用书）、《活教育中的山西文化丛书》等，是促进学、研、产、用相结合的专业幼教服务机构。
                        </p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团多次荣获“中国民办十大知名品牌教育机构”、“全国十佳特色示范单位”、“全国学前教育先进单位”、“全国民办教育先进集体”、“全国特色民办幼儿园”、“最具社会责任感教育机构”、“最具企业竞争力教育机构”、“课程创新新锐奖”、“幼教中国影响力（学习力）机构奖”等多项殊荣。
                        </p>
                        <p style="text-indent:32px;">
                            沃德兰·东大教育集团与恒大、富力、绿地、万科、保利等国内多家房地产公司合作，通过“教育带动地产，地产联动教育”进行强强联手，带动了当地的幼教事业，达到三赢的社会效果。
                        </p>',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                        array('page02_hide', '!=', 1),
                    ),
                ),
                'page03_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页面',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/71aed273f4687b0c6ac9421820810550.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                        array('page03_hide', '!=', 1),
                    ),
                ),
                'page03_slider_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '沃德兰 · 九大优势',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                        array('page03_hide', '!=', 1),
                    ),
                ),
                'page03_slider_desc' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => 'COMPETITIVE EDGE',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                        array('page03_hide', '!=', 1),
                    ),
                ),
                'page03_part02' => array(
                    'type' => 'separator',
                    'title' => '列表项目配置',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                        array('page03_hide', '!=', 1),
                    ),
                ),
                'page03_list_item' => array(
                    'title' => '列表项目',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '直营式管理',
                        ),
                        'slider_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '图标',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                        ),
                        'slider_color' => array(
                            'type' => 'color',
                            'title' => '背景颜色',
                            'std' => 'rgb(69,84,105)',
                        ),
                        'slider_border_color' => array(
                            'type' => 'color',
                            'title' => '描边颜色',
                            'std' => 'rgb(69,84,105)',
                        ),
                        'slider_border_color_hover' => array(
                            'type' => 'color',
                            'title' => '鼠标移入描边颜色',
                            'std' => 'rgb(146,154,169)',
                        ),
                        'title_link' => array(
                            'type' => 'text',
                            'title' => '标题链接地址',
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '直营式管理',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/890b7e05baedea5470137efbe22ef714.png',
                            'slider_color' => 'rgb(69,84,105)',
                            'slider_border_color' => 'rgb(199,203,210)',
                            'slider_border_color_hover' => 'rgb(146,154,169)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '体验式教育',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/4ff4c2e338980e2e2bdca7d6ab23fe84.png',
                            'slider_color' => 'rgb(67, 189, 154)',
                            'slider_border_color' => 'rgb(198, 235, 225)',
                            'slider_border_color_hover' => 'rgb(133, 208, 187)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '课程的优势',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/9e997a157a8aba0a45e9dacbb8d2cbf1.png',
                            'slider_color' => 'rgb(249, 134, 52)',
                            'slider_border_color' => 'rgb(253, 218, 194)',
                            'slider_border_color_hover' => 'rgb(255, 184, 136)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '亲人般互动',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/40ac0b7e85f09ca66c084bbc4e5cf2e3.png',
                            'slider_color' => 'rgb(1, 143, 148)',
                            'slider_border_color' => 'rgb(178, 221, 223)',
                            'slider_border_color_hover' => 'rgb(114, 207, 212)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '卓越的品牌',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/a665316b5c70f8d2429e06f91041b162.png',
                            'slider_color' => 'rgb(78, 139, 196)',
                            'slider_border_color' => 'rgb(202, 220, 237)',
                            'slider_border_color_hover' => 'rgb(125, 183, 239)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '高效的招生',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/c3b01d0712d424cb7bb081dbb8e38d8d.png',
                            'slider_color' => 'rgb(17, 133, 176)',
                            'slider_border_color' => 'rgb(182, 217, 230)',
                            'slider_border_color_hover' => 'rgb(132, 207, 234)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '联盟的优势',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/2d6d478b5261f423f9a220d90c687eaa.png',
                            'slider_color' => 'rgb(156, 184, 51)',
                            'slider_border_color' => 'rgb(224, 233, 193)',
                            'slider_border_color_hover' => 'rgb(216, 234, 153)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '预算式管理',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/07e5a9be48d7928b5a9d4fcbe09763e1.png',
                            'slider_color' => 'rgb(211, 62, 34)',
                            'slider_border_color' => 'rgb(241, 196, 188)',
                            'slider_border_color_hover' => 'rgb(241, 153, 137)',
                            'title_link' => '',
                        ),
                        array(
                            'title' => '统一的采购',
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211207/4baab08c6af5453bb2567b06a733d19d.png',
                            'slider_color' => 'rgb(134, 89, 166)',
                            'slider_border_color' => 'rgb(217, 203, 225)',
                            'slider_border_color_hover' => 'rgb(200, 159, 224)',
                            'title_link' => '',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                        array('page03_hide', '!=', 1),
                    ),
                ),
                'page04_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页面',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '大背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/b9473a1d184bf5234f3e730d505961aa.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_slider_img_center' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '中间内容背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211216/2a7ae34d4d9a97a678526f6cedde2129.png',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_slider_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '沃德兰 · 团队展示',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_slider_desc' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => 'TEAM',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_part02' => array(
                    'type' => 'separator',
                    'title' => '中间切换图列表项目配置',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_slider_icon' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '切换项目按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211216/d7428b14b5aee5f142bcf33d89c65625.png',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_image_item' => array(
                    'title' => '切换图项目（预览查看切换效果）',
                    'attr' => array(
                        'slider_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '轮播图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211216/64479efd0311661308e9b0eecba54b45.png',
                        ),
                        'slider_link' => array(
                            'type' => 'text',
                            'title' => '链接地址',
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/64479efd0311661308e9b0eecba54b45.png',
                            'slider_link' => '',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/3c61c47e5fe291bb8cbb47d57ae3447f.png',
                            'slider_link' => '',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211216/c265f1017a1cf78c6007b662ab1edab1.png',
                            'slider_link' => '',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_part03' => array(
                    'type' => 'separator',
                    'title' => '右侧按钮列表项目配置',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),
                'page04_btn_item' => array(
                    'title' => '列表项目',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '运营团队',
                        ),
                        'slider_link' => array(
                            'type' => 'text',
                            'title' => '链接地址',
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '运营团队',
                            'slider_link' => '',
                        ),
                        array(
                            'title' => '教研团队',
                            'slider_link' => '',
                        ),
                        array(
                            'title' => '后勤团队',
                            'slider_link' => '',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                        array('page04_hide', '!=', 1),
                    ),
                ),

                'page05_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页面',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/bd862c691107888b4505e6af6a580ccf.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_ani_title01' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '沃德兰 · 新闻活动',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_ani_title02' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => 'NEWS',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_part01' => array(
                    'type' => 'separator',
                    'title' => '新闻配置（新闻列表预览查看数据）',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_resource' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE_DESC'),
                    'values' => array(
                        'article' => JText::_('建站通'),
                        'k2' => JText::_('无'),
                    ),
                    'std' => 'article',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'multiple' => true,
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                        array('page05_resource', '=', 'article')
                    ),
                ),
                'page05_post_type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_ALL'),
                        'standard' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STANDARD'),
                        'audio' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_AUDIO'),
                        'video' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_VIDEO'),
                        'gallery' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_GALLERY'),
                        'link' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_LINK'),
                        'quote' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_QUOTE'),
                        'status' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STATUS'),
                    ),
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                        array('page05_resource', '=', 'article')
                    )
                ),
                'page05_include_subcat' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES_DESC'),
                    'values' => array(
                        1 => JText::_('COM_JWPAGEFACTORY_YES'),
                        0 => JText::_('COM_JWPAGEFACTORY_NO'),
                    ),
                    'std' => 1,
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(

                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),

                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),

                        'sortdesc' => JText::_('排序id倒序'),
                        'sortasc' => JText::_('排序id正序'),

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'sortdesc',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page05_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '6',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                        array('page05_hide', '!=', 1),
                    ),
                ),
                'page06_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/4ecd2249ab2ebe84efd99213cd4a4f30.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '沃德兰 · 联盟合作',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_desc' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => 'JOIN IN',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_content' => array(
                    'type' => 'text',
                    'title' => '内容',
                    'std' => '学前教育牵动着全社会，沃德兰国际教育致力于：“适应儿童发展、遵循自然本性、实现原本教育、养成高尚品格”的教育理念，确保优秀的教育质量、优秀的教育服务。现特面向社会诚招教育合作伙伴，诚邀有实力、有经验、有成熟课程体系的机构联盟发展，共谋幼教事业发展。',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_tel' => array(
                    'type' => 'text',
                    'title' => '电话部分',
                    'std' => '0351-3239017 0351-3238128',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_btn' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'std' => '联系我们',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_btn_link' => array(
                    'type' => 'text',
                    'title' => '按钮链接',
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page06_slider_img_right' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '右侧图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211216/01d428b32f89031a618c19dad7e56ea8.png',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_hide', '!=', 1),
                    ),
                ),
                'page07_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                    ),
                ),
                'page07_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/6e35db099855a042d258507a5407f8bc.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_part01' => array(
                    'type' => 'separator',
                    'title' => '左侧内容配置',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_left_title01' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '联系我们，关注我们',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_left_title02' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => 'Contact Us',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_left_title03' => array(
                    'type' => 'text',
                    'title' => '内容',
                    'std' => '我们始终愿意俯下身聆听您的声音',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_left_title04' => array(
                    'type' => 'text',
                    'title' => '电话',
                    'std' => '400-***-****',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_left_title05' => array(
                    'type' => 'text',
                    'title' => '网址',
                    'std' => 'www.xxxxxxxx.com',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),

                'page07_part02' => array(
                    'type' => 'separator',
                    'title' => '右侧地图配置（预览查看地图效果）',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_map_lon' => array(
                    'type' => 'text',
                    'title' => '地图经度（使用高德地图坐标拾取）',
                    'std' => '112.547132',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_map_lat' => array(
                    'type' => 'text',
                    'title' => '地图纬度（使用高德地图坐标拾取）',
                    'std' => '37.794354',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_map_title01' => array(
                    'type' => 'text',
                    'title' => '地图信息窗公司名称',
                    'std' => '山西资海科技科技有限公司',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_map_title02' => array(
                    'type' => 'text',
                    'title' => '地图信息窗地址',
                    'std' => '山西太原市南中环街清控创新基地B座4层',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_map_title03' => array(
                    'type' => 'text',
                    'title' => '地图信息窗电话',
                    'std' => '400-***-****',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_part03' => array(
                    'type' => 'separator',
                    'title' => '底部配置',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_content' => array(
                    'type' => 'editor',
                    'title' => '导航文字部分',
                    'std' => '<a href="">沃德兰·东大教育集团</a> |
                        <a href="">新闻活动</a> |
                        <a href="">联盟合作</a> |
                        <a href="">招贤纳士</a>',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_record_g' => array(
                    'type' => 'text',
                    'title' => '公安备案号',
                    'std' => '晋公网安备 14010502050981号',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_record_link_g' => array(
                    'type' => 'text',
                    'title' => '公安备案号跳转链接',
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_record' => array(
                    'type' => 'text',
                    'title' => '备案号',
                    'std' => '晋ICP备17001037号-1',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_record_link' => array(
                    'type' => 'text',
                    'title' => '备案号跳转链接',
                    'std' => 'http://beian.miit.gov.cn/',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_copyright' => array(
                    'type' => 'text',
                    'title' => '技术支持',
                    'std' => '山西资海科技科技有限公司',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
                'page07_copyright_link' => array(
                    'type' => 'text',
                    'title' => '技术支持跳转链接',
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page07'),
                        array('page07_hide', '!=', 1),
                    ),
                ),
            )
        )
    )
);