<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input      = $app->input;
$layout_id  = $input->get('layout_id', '');
$site_id    = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type'       => 'content',
        'addon_name' => 'button_list',
        'title'      => JText::_('按钮组事件'),
        'desc'       => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category'   => '按钮',
        'attr'       => array(
            'general' => array(
                'site_id'                                 => array(
                    'std' => $site_id,
                ),
                'company_id'                              => array(
                    'std' => $company_id,
                ),
                // Repeatable Item
                'jw_tab_item_button' => array(
                    'title' => JText::_('自定义按钮组'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮文字'),
                            'desc' => JText::_('按钮文字'),
                            'std' => '标题',
                        ),
                        'select_show' => array(
                            'type' => 'select',
                            'title' => '按钮事件',
                            'desc' => '',
                            'values' => array(
                                'tp' => JText::_('图片展示'),
                                'tz' => JText::_('链接跳转'),
                            ),
                            'std' => 'tp',
                        ),
                        'button_img' => array(
                            'type' => 'media',
                            'title' => JText::_('按钮图标'),
                            'desc' => JText::_('按钮图标'),
                            'std' => '',
                        ),
                        'button_img_hg' => array(
                            'type' => 'media',
                            'title' => JText::_('按钮滑过图标'),
                            'desc' => JText::_('按钮滑过图标'),
                            'std' => '',
                        ),
                        'button_img_hr' => array(
                            'type' => 'media',
                            'title' => JText::_('滑过显示图片'),
                            'desc' => JText::_('滑过显示图片'),
                            'std' => '',
                            'depends' => array(
                                array('select_show', '=', 'tp'),
                            ),
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                            'depends' => array(
                                array('select_show', '=', 'tz'),
                            ),
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('tz_page_type', '=', 'Internal_pages'),
                                array('select_show', '=', 'tz'),
                            ),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(
                                array('tz_page_type', '=', 'external_links'),
                                array('select_show', '=', 'tz'),
                            ),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                            'depends' => array(
                                array('select_show', '=', 'tz'),
                            ),
                        ),
                        'button_desc' => array(
                            'type' => 'text',
                            'title' => '滑过显示内容',
                            'std' => '',
                            'depends' => array(
                                array('select_show', '=', 'tp'),
                            ),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '龙采官方微博',
                            'detail_page' => 'https://weibo.com/p/1006062488214857/home?from=page_100606&amp;mod=TAB#place',
                            'select_show' => 'tz',
                            'target' => '',
                            'detail_page_id' => 0,
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/264423a89b45472f4e32ca53c4b46251.png',
                            'button_img_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/7a8f0364daadfae2a6991950eecb4c97.png',
                            'button_img_hr' => '',
                            'button_desc' => '扫码关注龙采科技官方微博，了解更多企业资讯！',
                        ),
                        array(
                            'title' => '龙采官方微信',
                            'detail_page' => '',
                            'select_show' => 'tp',
                            'target' => '',
                            'detail_page_id' => 0,
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/819e2aec481a2e681fb1884e90476a47.png',
                            'button_img_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/1dc69249c9fa6d581e3d9d2f97c18d03.png',
                            'button_img_hr' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/cb290f9ee917af837daebc9923dfdbfc.jpeg',
                            'button_desc' => '扫码关注龙采科技官方微信，了解更多企业资讯！',
                        ),
                        array(
                            'title' => '龙采官方抖音',
                            'detail_page' => '',
                            'select_show' => 'tp',
                            'target' => '',
                            'detail_page_id' => 0,
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/3893dded670146184abca18cd9d903b7.png',
                            'button_img_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/95d4a0038801abfb9020ec401531130a.png',
                            'button_img_hr' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/632410bb44dca5e6fbf0714c23b8fab2.png',
                            'button_desc' => '扫码关注龙采科技官方抖音，了解更多企业资讯！',
                        ),
                        array(
                            'title' => '龙采官方百家号',
                            'detail_page' => '',
                            'select_show' => 'tp',
                            'target' => '',
                            'detail_page_id' => 0,
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/955bdf6a783d9072db82cd6e4f31cc60.png',
                            'button_img_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/7c72f12d67aba22c7006b6dc1e10b17b.png',
                            'button_img_hr' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/866d19743af5647ff93c324cba75446b.png',
                            'button_desc' => '扫码关注龙采科技百家号，了解更多龙采资讯！',
                        ),
                    ),
                ),
            ),
        ),
    )
);
