<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonSwiper_search2 extends JwpagefactoryAddons
{

	public function render()
	{
        $addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
        if (isset($settings->height) && $settings->height) {
            if (is_object($settings->height)) {
                $height_md = $settings->height->md;
                $height_sm = $settings->height->sm;
                $height_xs = $settings->height->xs;
            } else {
                $height_md = $settings->height;
                $height_sm = $settings->height_sm;
                $height_xs = $settings->height_xs;
            }
        } else {
            $height_md = '446';
            $height_sm = '446';
            $height_xs = '446';
        }
        $output .= '<link rel="stylesheet" href="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_search2/assets/'.'css/swiper.min.css">';
        $output .= ' <style>';
        $output .= $addonId . ' dd,dl,li,ul{list-style:none}';
        $output .= $addonId . ' html,body{position:relative;height:100%}';
        $output .= $addonId . ' body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}';
        $output .= $addonId . ' .search2{width:100%}';
        $output .= $addonId . ' .search2 ul{right: 4.2rem;}';
        $output .= $addonId . ' .search2 li{width:31% !important;height:'.$height_md.'px;background:#f2f6ff;text-align:center;margin-left:3rem;}';
        $output .= $addonId . ' .search2 li img{width:41%;margin:0 auto;transition:all .6s;margin-top:8%;}';
        $output .= $addonId . ' .search2 li:hover img{transform:translateY(-10px)}';
        $output .= $addonId . ' .search2 li h3{font-size:1.4rem;color:#333;margin:4% 0;font-weight: bold;}';
        $output .= $addonId . ' .search2 li p{color:#454545;font-size:1rem;}';
        $output .= $addonId . ' .search2_btn{font-size:26px;color:#fff;background:#d9012a;border-radius:34px;line-height:68px;width:270px;margin:0 auto;font-weight:bold}';
           $output .=' @media only screen and (max-width: 1023px)';
        $output .= ' {';
        $output .= $addonId . ' .search2{margin:.6rem 0}';
        $output .= $addonId . ' .search2 ul{right: 11rem;height: calc('.$height_xs.' + 50px) !important;}';
        $output .= $addonId . ' .search2 li{width:50% !important;height:'.$height_xs.'px;background:#f2f6ff;padding:1.5rem 0.45rem 2.5% 0;text-align:center;margin-left: 1.5rem;}';
        $output .= $addonId . ' .search2 li img{width:15rem;height:13rem;margin:0 auto;transition:all .6s}';
        $output .= $addonId . ' .search2 li h3{font-size:1.9rem;color:#333;margin:1rem 0 1rem;font-weight: 300;}';
        $output .= $addonId . ' .search2 li p{color:#333;font-size:1.5rem;font-weight: 300;}';
        $output .='}';
        $output .= ' </style>';
        $output .= '<div class="swiper swiper-container search2 wow fadeInUp swiper-initialized swiper-horizontal swiper-pointer-events animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;background: #f2f6ff;">';
        $output .= '  <ul class="flex between swiper-wrapper" id="swiper-wrapper-beade326b54e9d12" aria-live="polite" style="transition-duration: 0ms; ">';
        foreach ($settings->jw_search2_item as $key => $lcjwsearch) {
        $output .= '    <li class="swiper-slide swiper-slide-active" > <img src="'.$lcjwsearch->bg_img.'" >';
        $output .= '      <h3>'.$lcjwsearch->title.'</h3>';
        $output .= '      <p>'.$lcjwsearch->content.'</p>';
        $output .= '    </li>';
        }
        $output .= '  </ul>';
        $output .= '  <span class="swiper-notification"></span>';
        $output .= '</div>';
        $output .= '<script>';
        $output .= "var swiper = new Swiper('".$addonId."  .swiper-container', {
          slidesPerView: 3,
          centeredSlides: true,
          loop: true,
          freeMode : true,//是否滑动
          paginationClickable: true,
          pagination: {
            el: '.swiper-pagination',
            clickable: true,
          },
        });";
       $output .= '</script>';
	   
       return $output;

	}

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

	public function css()
	{
		$addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
        // 上翻页按钮
		$swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
		$swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;

        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';


        $css = '
		    /* 组件盒子 */
		    ' . $addonId . ' .swiper-box-main {
		        position: relative;
		    }
		    /* 切换 配置样式 */
	        ' . $addonId . ' .swiper-button {
	            width: auto;
	            height: auto;
                top: ' . $swiper_button_top_md . '%;
	        }
	        ' . $addonId . ' .swiper-button:after {
	            content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev {
	            left: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }
            ' . $addonId . ' .swiper-button-next {
	            right: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url(' . $swiper_button_next . ');
	        }
	        /*轮播点*/
	        ' . $addonId . ' .swiper-pagination {
	            width: 100%;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet {
	            margin-right: ' .$swiper_p_margin_md . 'px;
	            width: ' . $swiper_p_width_md . 'px;
                height: ' . $swiper_p_height_md . 'px;
                background: ' . $swiper_p_color . ';
	            opacity: 1;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet-active {
	            width: ' . $swiper_p_width_a_md . 'px;
                height: ' . $swiper_p_height_a_md . 'px;
                background: ' . $swiper_p_color_a . ';
	        }
	        ' . $addonId . ' .swiper-pagination-bullet:last-child {
	            margin-right: 0px;
	        }
	        @media (min-width: 768px) and (max-width: 991px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_sm . 'px;
                    width: ' . $swiper_p_width_sm . 'px;
                    height: ' . $swiper_p_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_sm . 'px;
                    height: ' . $swiper_p_height_a_sm . 'px;
                }
	        }
	        @media (max-width: 767px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_xs . 'px;
                    width: ' . $swiper_p_width_xs . 'px;
                    height: ' . $swiper_p_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_xs . 'px;
                    height: ' . $swiper_p_height_a_xs . 'px;
                }
	        }';

		return $css;
	}

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
         //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;


        $js = 'jQuery(function($){';
        $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: true,
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
            });';
        $js .= '})';

        return $js;
    }

	public static function getTemplate()
	{
		$output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_search2_item = data.jw_search2_item || 0;
        
        #>
		<style type="text/css"> 
		    
            {{ addonId }} dd,dl,li,ul{list-style:none}
            {{ addonId }} html,body{position:relative;height:100%}
            {{ addonId }} body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}
            {{ addonId }} .search2{width:100%}
            {{ addonId }} .search2 ul{right: 4.2rem;}
            {{ addonId }} .search2 li{
                width:31% !important;
                <# if(_.isObject(data.height)){ #>
                    height: {{ data.height.md }}px;
                <# } else { #>
                    height: {{ data.height }}px;
                <# } #>

                background:#f2f6ff;
                text-align:center;
                margin-left:3rem;
            }
            {{ addonId }} .search2 li img{width:41%;margin:0 auto;transition:all .6s;margin-top:8%;}
            {{ addonId }} .search2 li:hover img{transform:translateY(-10px)}
            {{ addonId }} .search2 li h3{font-size:1.4rem;color:#333;margin:4% 0;font-weight: bold;}
            {{ addonId }} .search2 li p{color:#454545;font-size:1rem;}
            {{ addonId }} .search2_btn{font-size:26px;color:#fff;background:#d9012a;border-radius:34px;line-height:68px;width:270px;margin:0 auto;font-weight:bold}
            @media only screen and (max-width: 1023px){
                {{ addonId }} .search2{margin:.6rem 0}
                {{ addonId }} .search2 ul{
                    right: 11rem;
                    <# if(_.isObject(data.height)){ #>
                        height: calc({{ data.height.xs }}px + 50px)!important;;
                    <# } else { #>
                        height: calc({{ data.height }}px + 50px)!important;;
                    <# } #>

                }
                {{ addonId }} .search2 li{
                    width:50% !important;
                    
                    <# if(_.isObject(data.height)){ #>
                        height: {{ data.height.xs }}px;
                    <# } else { #>
                        height: {{ data.height }}px;
                    <# } #>
                    background:#f2f6ff;
                    padding:1.5rem 0.45rem 2.5% 0;
                    text-align:center;
                    margin-left: 1.5rem;
                }
                {{ addonId }} .search2 li img{width:15rem;height:13rem;margin:0 auto;transition:all .6s}
                {{ addonId }} .search2 li h3{font-size:1.9rem;color:#333;margin:1rem 0 1rem;font-weight: 300;}
                {{ addonId }} .search2 li p{color:#333;font-size:1.5rem;font-weight: 300;}
            }
		</style>
		
        <div class="swiper swiper-container search2 wow fadeInUp swiper-initialized swiper-horizontal swiper-pointer-events animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;background: #f2f6ff;">
            <ul class="flex between swiper-wrapper" id="swiper-wrapper-beade326b54e9d12" aria-live="polite" style="transition-duration: 0ms; ">
                <# _.each(data.jw_search2_item, function (lcjwsearch, key){ #>
                
                    <li class="swiper-slide swiper-slide-active" > <img src=\'{{lcjwsearch.bg_img}}\' >
                        <h3>{{lcjwsearch.title}}</h3>
                        <p>{{lcjwsearch.content}}</p>
                    </li>
                
                <# }) #>
            </ul>
            <span class="swiper-notification"></span>
        </div>

		';

		return $output;
	}

}