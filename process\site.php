<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonProcess extends JwpagefactoryAddons
{

    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        
		if (isset($settings->biaoti_fontsize) && $settings->biaoti_fontsize) {
		    if (is_object($settings->biaoti_fontsize)) {
		        $biaoti_fontsize_md = $settings->biaoti_fontsize->md;
		        $biaoti_fontsize_sm = $settings->biaoti_fontsize->sm;
		        $biaoti_fontsize_xs = $settings->biaoti_fontsize->xs;
		    } else {
		        $biaoti_fontsize_md = $settings->biaoti_fontsize;
		        $biaoti_fontsize_sm = $settings->biaoti_fontsize_sm;
		        $biaoti_fontsize_xs = $settings->biaoti_fontsize_xs;
		    }
		} else {
		    $biaoti_fontsize_md = '48';
		    $biaoti_fontsize_sm = '30';
		    $biaoti_fontsize_xs = '26';
		}

		if (isset($settings->jianjie_fontsize) && $settings->jianjie_fontsize) {
		    if (is_object($settings->jianjie_fontsize)) {
		        $jianjie_fontsize_md = $settings->jianjie_fontsize->md;
		        $jianjie_fontsize_sm = $settings->jianjie_fontsize->sm;
		        $jianjie_fontsize_xs = $settings->jianjie_fontsize->xs;
		    } else {
		        $jianjie_fontsize_md = $settings->jianjie_fontsize;
		        $jianjie_fontsize_sm = $settings->jianjie_fontsize_sm;
		        $jianjie_fontsize_xs = $settings->jianjie_fontsize_xs;
		    }
		} else {
		    $jianjie_fontsize_md = '24';
		    $jianjie_fontsize_sm = '18';
		    $jianjie_fontsize_xs = '14';
		}

		if (isset($settings->mk_fontsize) && $settings->mk_fontsize) {
		    if (is_object($settings->mk_fontsize)) {
		        $mk_fontsize_md = $settings->mk_fontsize->md;
		        $mk_fontsize_sm = $settings->mk_fontsize->sm;
		        $mk_fontsize_xs = $settings->mk_fontsize->xs;
		    } else {
		        $mk_fontsize_md = $settings->mk_fontsize;
		        $mk_fontsize_sm = $settings->mk_fontsize_sm;
		        $mk_fontsize_xs = $settings->mk_fontsize_xs;
		    }
		} else {
		    $mk_fontsize_md = '24';
		    $mk_fontsize_sm = '18';
		    $mk_fontsize_xs = '12';
		}

		$bgimg = (isset($settings->bgimg) && $settings->bgimg) ? $settings->bgimg : 'https://oss.lcweb01.cn/joomla/20220530/1f9a981cf4b250d1167d9929a443aa5a.jpg';
		$sjbgimg = (isset($settings->sjbgimg) && $settings->sjbgimg) ? $settings->sjbgimg : 'https://oss.lcweb01.cn/joomla/20220530/64c10e2a9efaf3ba5f4c983466fb5d63.jpg';

		$biaoti = (isset($settings->biaoti) && $settings->biaoti) ? $settings->biaoti : '快捷的操作流程';
		$biaoti_color = (isset($settings->biaoti_color) && $settings->biaoti_color) ? $settings->biaoti_color : '#fff';
		$jianjie = (isset($settings->jianjie) && $settings->jianjie) ? $settings->jianjie : 'WEB工作台+手机端+智能硬件';
		$jianjie_color = (isset($settings->jianjie_color) && $settings->jianjie_color) ? $settings->jianjie_color : '#fff';
		$mk_bgimg = (isset($settings->mk_bgimg) && $settings->mk_bgimg) ? $settings->mk_bgimg : 'https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png';
		$hv_bgimg = (isset($settings->hv_bgimg) && $settings->hv_bgimg) ? $settings->hv_bgimg : 'https://oss.lcweb01.cn/joomla/20220530/0b3b00f9e65f2c5aee19ac367b118c3b.png';

		$output = '';
		$output .= '
			<style>
				'.$addon_id.' *{padding:0px;margin:0px;}
				'.$addon_id.' .sec7{background: url("'.$bgimg.'") no-repeat center bottom;height: 599px;overflow: hidden;}
				'.$addon_id.' .sec7_tit p:nth-child(1){font-size: '.$biaoti_fontsize_md.'px;color: '.$biaoti_color.';font-weight: bold;text-align: center;}
				'.$addon_id.' .sec7_tit p:nth-child(2){font-size: '.$jianjie_fontsize_md.'px;color: '.$jianjie_color.';text-align: center;}
				'.$addon_id.' .sec7_tit{padding-bottom:45px;padding-top: 80px}
				'.$addon_id.' .sec7 ul{padding: 0 7%}
				'.$addon_id.' .sec7 ul li{height:269px;width: 18%;margin:0 1%;float: left;text-align: center;}
				'.$addon_id.' .sort{width:80px;height:80px;background:#0a161f;color: #fff;border-radius: 100%;line-height: 80px;font-size: 48px;margin:0 auto;}
				'.$addon_id.' .sec7 ul li p{font-size: '.$mk_fontsize_md.'px;color: #0a161f;padding-top: 20px;line-height: 35px;}
				'.$addon_id.' .sec7 ul li .back p{color: #fff}
				'.$addon_id.' .face{height: 269px;width: 100%;position:absolute;-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;background: url("'.$mk_bgimg.'") no-repeat  center top;}
				'.$addon_id.' .back{height: 269px;width: 100%;-webkit-transform:rotateY(180deg);-moz-transform:rotateY(180deg);background: url("'.$hv_bgimg.'") no-repeat  center top;}
				'.$addon_id.' .sort{margin-top: 50px;}
				'.$addon_id.' .back .sort{background:#fff;color:#0a161f;}
				'.$addon_id.' .film{width:100%;-webkit-transform-style:preserve-3d;-webkit-transition:1.5s;-moz-transform-style:preserve-3d;-moz-transition:1.5s;}
				'.$addon_id.' .clear{clear:both;}
				'.$addon_id.' .qcontainer:hover .film {
					-webkit-transform: rotateY(180deg);
					-moz-transform: rotateY(180deg);
				}

				'.$addon_id.' .sec7{ display:block;}
				'.$addon_id.' .sec6{ display:none;}
				@media (max-width:1200px)
				{
					'.$addon_id.' .sec7 ul li p{ font-size: '.$mk_fontsize_sm.'px;}
				}
				@media (max-width:750px)
				{
					'.$addon_id.' .sec7{ display:none;}
					'.$addon_id.' .sec6{ display:block;}
					'.$addon_id.' .se6_li_con{top:30%!important;}
					'.$addon_id.' .sec6{position: relative;overflow: hidden;}
					'.$addon_id.' .sec6_pic img{width: 100%;display: block;}
					'.$addon_id.' .sec6_main{position: absolute;top:0;left:0;width: 100%}
					'.$addon_id.' .sec6_tit{color: #fff}
					'.$addon_id.' .sec6_tit{padding-top: 5%}
					'.$addon_id.' .sec6_tit p:nth-child(1){font-size: '.$biaoti_fontsize_xs.'px;font-weight: bold;text-align: center;}
					'.$addon_id.' .sec6_tit p:nth-child(2){font-size: '.$jianjie_fontsize_xs.'px;text-align: center;}
					'.$addon_id.' .sec6 ul{width:96%;margin:7% auto 0;position: relative; }
					'.$addon_id.' .sec6 li{position:relative;width:31%;height:0;padding-top:35%;float: left;margin:0 1.16%;}
					'.$addon_id.' .sec6 li img{position:absolute;top:0;left:0;width:100%;height:100%}
					'.$addon_id.' .sec6 li:nth-child(4){position: absolute;top:81%;left:17%;}
					'.$addon_id.' .sec6 li:nth-child(5){position: absolute;top:81%;left:50%;}

					'.$addon_id.' .se6_li_con{width: 100%;position:absolute;top:15%;left:0;text-align: center;}
					'.$addon_id.' .se6_li_con p{font-size:'.$mk_fontsize_xs.'px;color: #0a161f;line-height: 1.3;padding-top:8%}
					'.$addon_id.' .sort {
						width: 40px;
						height: 40px;
						border-radius: 40px;
						background: #0a161f;
						line-height: 40px;
						color: #fff;
						margin: 0 auto;
						font-size: 26px;
					}
					'.$addon_id.' .sec6{background: #fff;width: 100%}

				}
				@media (max-width:450px)
				{
					'.$addon_id.' .sec7{ display:none;}
					'.$addon_id.' .sec6{ display:block;}
					'.$addon_id.' .se6_li_con{top:15%!important;}
				}

			</style>
		';
		
		$jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : array(
            array(
                'xuhao' => '01',
                'title' => '合适位置安装位置',
            ),
            array(
                'xuhao' => '02',
                'title' => '后台录入人脸信息',
            ),
            array(
                'xuhao' => '03',
                'title' => '摄像头人脸采集',
            ),
            array(
                'xuhao' => '04',
                'title' => '后台分析识别',
            ),
            array(
                'xuhao' => '05',
                'title' => '移动考勤通知后台记录轨迹',
            )
        );

		if (isset($settings->jw_tab_item) && !empty($settings->jw_tab_item)) {
			$output .= '<div class="sec7">
				<div class="sec7_tit">
					<p class="mx zoomIn animated" style="visibility: visible; animation-name: zoomIn;">'.$biaoti.'</p>
					<p class="mx fadeInUp animated" style="visibility: visible; animation-name: fadeInUp;">'.$jianjie.'</p>
				</div>';
				$output .= '<ul>';
				$dely="0";
				foreach ($settings->jw_tab_item as $item_key => $tab_item) {
					$dely+="0.2";

					$output .= '<li class="mx fadeInUp qcontainer animated" data-wow-delay="'.$dely.'s" style="visibility: visible; animation-name: fadeInUp; animation-delay: '.$dely.'s;">

						<div class="film">
							<div class="face front"><div class="sort">'.$tab_item->xuhao.'</div><p>'.$tab_item->title.'</p></div>
							<div class="face back"><div class="sort">'.$tab_item->xuhao.'</div><p>'.$tab_item->title.'</p></div>
						</div>

						</li>';
				}
					$output .= '<div class="clear"></div>
				</ul>';

				$output .= '

			</div>

			<div class="sec6">

				<div class="sec6_pic"><img src="'.$sjbgimg.'" alt=""></div>

				<div class="sec6_main">

					<div class="sec6_tit">

						<p class="mx zoomIn animated" style="visibility: visible;">'.$biaoti.'</p>

						<p class="mx fadeInUp animated" style="visibility: visible;">'.$jianjie.'</p>

					</div>

					<ul>';
					foreach ($settings->jw_tab_item as $item_key => $tab_item) {
						$output .= '<li class="mx zoomIn animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s;">

							<img src="'.$mk_bgimg.'" alt="">
							<div class="se6_li_con">
								<div class="sort">'.$tab_item->xuhao.'</div><p>'.$tab_item->title.'</p>
							</div>      

						</li>';
					}
						$output .= '<div class="clear"></div>

					</ul>

				</div>
			</div>';
		}

        return $output;
    }

    public function scripts()
    {

    }

    public  function js()
	{
       
    }

    public function css()
    {
        
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
			var addonId = "#jwpf-addon-"+data.id;
			var img_zt = data.img_zt || "qx";
			

		#>

			<style>
				{{addonId}} *{padding:0px;margin:0px;}
				{{addonId}}  .sec7{background: url("https://oss.lcweb01.cn/joomla/20220530/1f9a981cf4b250d1167d9929a443aa5a.jpg") no-repeat center bottom;height: 599px;overflow: hidden;}
				{{addonId}}  .sec7_tit p:nth-child(1){font-size: 48px;color: #fff;font-weight: bold;text-align: center;}
				{{addonId}}  .sec7_tit p:nth-child(2){font-size: 24px;color: #fff;text-align: center;}
				{{addonId}}  .sec7_tit{padding-bottom:45px;padding-top: 80px}
				{{addonId}}  .sec7 ul{padding: 0 7%}
				{{addonId}}  .sec7 ul li{height:269px;width: 18%;margin:0 1%;float: left;text-align: center;}
				{{addonId}}  .sort{width:80px;height:80px;background:#0a161f;color: #fff;border-radius: 100%;line-height: 80px;font-size: 48px;margin:0 auto;}
				{{addonId}}  .sec7 ul li p{font-size: 24px;color: #0a161f;padding-top: 20px;line-height: 35px;}
				{{addonId}}  .sec7 ul li .back p{color: #fff}
				{{addonId}}  .face{height: 269px;width: 100%;position:absolute;-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;background: url("https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png") no-repeat  center top;}
				{{addonId}}  .back{height: 269px;width: 100%;-webkit-transform:rotateY(180deg);-moz-transform:rotateY(180deg);background: url("https://oss.lcweb01.cn/joomla/20220530/0b3b00f9e65f2c5aee19ac367b118c3b.png") no-repeat  center top;}
				{{addonId}}  .sort{margin-top: 50px;}
				{{addonId}}  .back .sort{background:#fff;color:#0a161f;}
				{{addonId}}  .film{width:100%;-webkit-transform-style:preserve-3d;-webkit-transition:1.5s;-moz-transform-style:preserve-3d;-moz-transition:1.5s;}
				{{addonId}}  .clear{clear:both;}
				{{addonId}}  .qcontainer:hover .film {
					-webkit-transform: rotateY(180deg);
					-moz-transform: rotateY(180deg);
				}

				{{addonId}}  .sec7{ display:block;}
				{{addonId}}  .sec6{ display:none;}
				@media (max-width:1200px)
				{
					{{addonId}}  .sec7 ul li p{ font-size: 18px;}
				}
				@media (max-width:750px)
				{
					{{addonId}}  .sec7{ display:none;}
					{{addonId}}  .sec6{ display:block;}
					{{addonId}}  .se6_li_con{top:30%!important;}
					{{addonId}}  .sec6{position: relative;overflow: hidden;}
					{{addonId}}  .sec6_pic img{width: 100%;display: block;}
					{{addonId}}  .sec6_main{position: absolute;top:0;left:0;width: 100%}
					{{addonId}}  .sec6_tit{color: #fff}
					{{addonId}}  .sec6_tit{padding-top: 5%}
					{{addonId}}  .sec6_tit p:nth-child(1){font-size: 26px;font-weight: bold;text-align: center;}
					{{addonId}}  .sec6_tit p:nth-child(2){font-size: 14px;text-align: center;}
					{{addonId}}  .sec6 ul{width:96%;margin:7% auto 0;position: relative; }
					{{addonId}}  .sec6 li{position:relative;width:31%;height:0;padding-top:35%;float: left;margin:0 1.16%;}
					{{addonId}}  .sec6 li img{position:absolute;top:0;left:0;width:100%;height:100%}
					{{addonId}}  .sec6 li:nth-child(4){position: absolute;top:81%;left:17%;}
					{{addonId}}  .sec6 li:nth-child(5){position: absolute;top:81%;left:50%;}

					{{addonId}}  .se6_li_con{width: 100%;position:absolute;top:15%;left:0;text-align: center;}
					{{addonId}}  .se6_li_con p{font-size:12.5px;color: #0a161f;line-height: 1.3;padding-top:8%}
					{{addonId}}  .sort {
						width: 40px;
						height: 40px;
						border-radius: 40px;
						background: #0a161f;
						line-height: 40px;
						color: #fff;
						margin: 0 auto;
						font-size: 26px;
					}
					{{addonId}}  .sec6{background: #fff;width: 100%}

				}
				@media (max-width:450px)
				{
					{{addonId}}  .sec7{ display:none;}
					{{addonId}}  .sec6{ display:block;}
					{{addonId}}  .se6_li_con{top:15%!important;}
				}

			</style>

			<div class="sec7">
				<div class="sec7_tit">
					<p class="mx zoomIn animated" style="visibility: visible; animation-name: zoomIn;">快捷的操作流程</p>
					<p class="mx fadeInUp animated" style="visibility: visible; animation-name: fadeInUp;">WEB工作台+手机端+智能硬件</p>
				</div>
				<ul>
					<li class="mx fadeInUp qcontainer animated" data-wow-delay="0.2s" style="visibility: visible; animation-name: fadeInUp; animation-delay: 0.2s;">

						<div class="film">
							<div class="face front"><div class="sort">01</div><p>合适位置安装位置</p></div>
							<div class="face back"><div class="sort">01</div><p>合适位置安装位置</p></div>
						</div>

					</li>
					<li class="mx fadeInUp qcontainer animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">



						<div class="film">



							<div class="face front"><div class="sort">02</div><p>后台录入人脸信息</p></div>



							<div class="face back"><div class="sort">02</div><p>后台录入人脸信息</p></div>



						</div>



					</li>

					<li class="mx fadeInUp qcontainer animated" data-wow-delay="0.6s" style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">



						<div class="film">



							<div class="face front"><div class="sort">03</div><p>摄像头人脸采集</p></div>



							<div class="face back"><div class="sort">03</div><p>摄像头人脸采集</p></div>



						</div>



					</li>

					<li class="mx fadeInUp qcontainer animated" data-wow-delay="0.8s" style="visibility: visible; animation-delay: 0.8s; animation-name: fadeInUp;">



						<div class="film">



							<div class="face front"><div class="sort">04</div><p>后台分析识别</p></div>



							<div class="face back"><div class="sort">04</div><p>后台分析识别</p></div>



						</div>



					</li>

					<li class="mx fadeInUp qcontainer animated" data-wow-delay="1s" style="visibility: visible; animation-delay: 1s; animation-name: fadeInUp;">



						<div class="film">



							<div class="face front"><div class="sort">05</div><p>移动考勤通知<br>后台记录轨迹</p></div>



							<div class="face back"><div class="sort">05</div><p>移动考勤通知<br>后台记录轨迹</p></div>



						</div>



					</li>
					<div class="clear"></div>
				</ul>
			</div>

			<div class="sec6">

				<div class="sec6_pic"><img src="https://oss.lcweb01.cn/joomla/20220530/64c10e2a9efaf3ba5f4c983466fb5d63.jpg" alt=""></div>

				<div class="sec6_main">

					<div class="sec6_tit">

						<p class="mx zoomIn animated" style="visibility: visible;">快捷的操作流程</p>

						<p class="mx fadeInUp animated" style="visibility: visible;">WEB工作台+手机端+智能硬件</p>

					</div>

					<ul>

						<li class="mx zoomIn animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s;">

							<img src="https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png" alt="">

							<div class="se6_li_con">

								<div class="sort">01</div><p>合适位置安装位置</p>

							</div>      

						</li>

						

						<li class="mx zoomIn animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s;">

							<img src="https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png" alt="">

							<div class="se6_li_con">

								<div class="sort">02</div><p>后台录入人脸信息</p>

							</div>  

						</li>

						<li class="mx zoomIn animated" data-wow-delay="0.6s" style="visibility: visible; animation-delay: 0.6s;">

							<img src="https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png" alt="">

							<div class="se6_li_con">

								<div class="sort">03</div><p>摄像头人脸采集</p>

							</div>  

						</li>

						<li class="mx zoomIn animated" data-wow-delay="0.8s" style="visibility: visible; animation-delay: 0.8s;">

							<img src="https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png" alt="">

							<div class="se6_li_con">

								<div class="sort">04</div><p>后台分析识别</p>

							</div>  

						</li>

						<li class="mx zoomIn animated" data-wow-delay="1s" style="visibility: visible; animation-delay: 1s;">

							<img src="https://oss.lcweb01.cn/joomla/20220530/84612b8315e34c80639aa4850a6b30ad.png" alt="">

							<div class="se6_li_con">

								<div class="sort">05</div><p>移动考勤通知<br>后台记录轨迹</p>

							</div>  

						</li>

						<div class="clear"></div>

					</ul>

				</div>
			</div>

		';

        return $output;
    }
}
