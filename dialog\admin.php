<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2024-02-26 17:57:11
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

/* $app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
$imgurl = $config->img_url; */

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'dialog',
        'title' => JText::_('弹窗'),
        'desc' => JText::_(''),
        'category' => '图片',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                // 点击按钮出弹窗
				'dialog_addon_id' => array(
					'title' => '弹窗插件ID',
					'type' => 'text',
					'std' => ''
				),
                'dialog_settings' => array(
                    'type' => 'buttons',
                    'title' => JText::_('弹窗设置'),
                    'std' => 'content',
                    'values' => array(
                            array(
                                    'label' => '弹窗主体',
                                    'value' => 'content'
                            ),
                            array(
                                    'label' => '关闭按钮',
                                    'value' => 'close'
                            ),
                            array(
                                    'label' => '遮罩',
                                    'value' => 'mask'
                            ),
                    ),
                    'tabs' => true,
                ),
                'dialog_img' => array(
                    'type' => 'media',
                    'title' => JText::_('弹窗图片'),
                    'std' => 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50',
                    'depends' => array(

                        array('dialog_settings', '=', 'content'),
                    )
                ),
                'close_img' => array(
                    'type' => 'media',
                    'title' => JText::_('关闭按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png',
                    'depends' => array(

                        array('dialog_settings', '=', 'close'),
                    )
                ),
                'close_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('关闭按钮宽度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(

                        array('dialog_settings', '=', 'close'),
                    )
                ),
                'close_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(

                        array('dialog_settings', '=', 'close'),
                    )
                ),
                'close_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(

                        array('dialog_settings', '=', 'close'),
                    )
                ),
                'close_position' => array(
                    'type' => 'margin',
                    'title' => JText::_('弹窗关闭按钮位置'),
                    'std' => '0 0 auto auto',
                    'responsive' => true,
                    'depends' => array(

                        array('dialog_settings', '=', 'close'),
                    )
                ),
                'dialog_level' => array(
                    'type' => 'text',
                    'title' => JText::_('弹窗层级'),
                    'std' => '99999999999999999999999999999',
                    'depends' => array(

                        array('dialog_settings', '=', 'content'),
                    )
                ),
                'dialog_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗宽度'),
                    'std' => '600',
                    'responsive' => true,
                    'max' => '2000',
                    'depends' => array(

                        array('dialog_settings', '=', 'content'),
                    )
                ),
                'dialog_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '400',
                    'responsive' => true,
                    'max' => '1000',
                    'depends' => array(

                        array('dialog_settings', '=', 'content'),
                    )
                ),
                'dialog_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(

                        array('dialog_settings', '=', 'content'),
                    )
                ),
                'mask_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('遮罩背景色'),
                    'std' => 'rgba(0,0,0,0.5)',
                    'depends' => array(

                        array('dialog_settings', '=', 'mask'),
                    )
                ),
                'mask_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('点击遮罩关闭弹窗'),
                    'std' => 1,
                    'depends' => array(

                        array('dialog_settings', '=', 'mask'),
                    )
                ),
                // 点击按钮出弹窗结束
            ),
        ),
    )
);
