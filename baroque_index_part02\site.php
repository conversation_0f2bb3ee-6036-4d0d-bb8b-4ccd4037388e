<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonBaroque_index_part02 extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();

        $output = '<div class="section-5">';
            foreach ($section_tab_item as $key => $tab) {
                $link = '';
                if($tab->detail_page_id){
                    $id = base64_encode($tab->detail_page_id);
                    $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                }
				if($tab->title_id){
                    $link .= '#'.$tab->title_id;
                }
                $output .= '<a ';
                if($link != ''){
                    $output .= 'target="_blank" href="' . $link .'" ';
                }
                $output .= 'class="item" style="background-color:' . $tab->bgColor . '">';
                $output .= '<img src="' . $tab->icon . '" class="icon" alt="" />';
                $output .= '<p class="name">' . $tab->name . '</p>';
                $output .= '</a>';
            }
        $output .= '</div>';

        return $output;
    }

    public function scripts()
    {

    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;

        $js = 'jQuery(function($){
		    
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
        //选项卡高度
        $section_tab_height_md = (isset($settings->section_tab_height) && $settings->section_tab_height) ? $settings->section_tab_height : 256;
        $section_tab_height_sm = (isset($settings->section_tab_height_sm) && $settings->section_tab_height_sm) ? $settings->section_tab_height_sm : 240;
        $section_tab_height_xs = (isset($settings->section_tab_height_xs) && $settings->section_tab_height_xs) ? $settings->section_tab_height_xs : 158;
        //文字大小
        $section_tab_fontsize_md = (isset($settings->section_tab_fontsize) && $settings->section_tab_fontsize) ? $settings->section_tab_fontsize : 18;
        $section_tab_fontsize_sm = (isset($settings->section_tab_fontsize_sm) && $settings->section_tab_fontsize_sm) ? $settings->section_tab_fontsize_sm : 18;
        $section_tab_fontsize_xs = (isset($settings->section_tab_fontsize_xs) && $settings->section_tab_fontsize_xs) ? $settings->section_tab_fontsize_xs : 16;
        //文字颜色
        $section_tab_fontcolor = (isset($settings->section_tab_fontcolor) && $settings->section_tab_fontcolor) ? $settings->section_tab_fontcolor : "#ffffff";
        //图标大小
        $section_tab_icon_size_md = (isset($settings->section_tab_icon_size) && $settings->section_tab_icon_size) ? $settings->section_tab_icon_size : 100;
        $section_tab_icon_size_sm = (isset($settings->section_tab_icon_size_sm) && $settings->section_tab_icon_size_sm) ? $settings->section_tab_icon_size_sm : 80;
        $section_tab_icon_size_xs = (isset($settings->section_tab_icon_size_xs) && $settings->section_tab_icon_size_xs) ? $settings->section_tab_icon_size_xs : 50;
        //pc端选项卡一行显示个数
        $section_tab_num_md = (isset($settings->section_tab_num) && $settings->section_tab_num) ? $settings->section_tab_num : 5;
        //平板端选项卡一行显示个数
        $section_tab_num_sm = (isset($settings->section_tab_num_sm) && $settings->section_tab_num_sm) ? $settings->section_tab_num_sm : 5;
        //手机端选项卡一行显示个数
        $section_tab_num_xs = (isset($settings->section_tab_num_xs) && $settings->section_tab_num_xs) ? $settings->section_tab_num_xs : 3;

        $css =
            $addonId . ' * {
				margin: 0;
			}
			' . $addonId . ' .section-5 {
				width: 100%;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
			}
			' . $addonId . ' .section-5 .item {
				width: calc(100% / ' . $section_tab_num_md . ');
				text-decoration: none;
				background-color: #585656;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				height: ' . $section_tab_height_md . 'px;
			}
			' . $addonId . ' .section-5 .item .icon {
				display: block;
				margin-bottom: 14px;
				transition: all ease-in-out 300ms;
				width: ' . $section_tab_icon_size_md . 'px;
			}
			' . $addonId . ' .section-5 .item .icon:hover {
				transform: scale(1.2);
			}
			' . $addonId . ' .section-5 .item .name {
				color: ' . $section_tab_fontcolor . ';
				font-size: ' . $section_tab_fontsize_md . 'px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				line-height: 32px;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    ' . $addonId . ' .section-5 .item {
				    width: calc(100% / ' . $section_tab_num_sm . ');
				    height: ' . $section_tab_height_sm . 'px;
				}
				' . $addonId . ' .section-5 .item .icon {  
				    width: ' . $section_tab_icon_size_sm . 'px;
				}
				' . $addonId . ' .section-5 .item .name {
				    font-size: ' . $section_tab_fontsize_sm . 'px;
				}
			}
			@media (max-width: 767px) {
			    ' . $addonId . ' .section-5 .item {
				    width: calc(100% / ' . $section_tab_num_xs . ');
				    height: ' . $section_tab_height_xs . 'px;
				}
				' . $addonId . ' .section-5 .item .icon {  
				    width: ' . $section_tab_icon_size_xs . 'px;
				}
				' . $addonId . ' .section-5 .item .name {
				    font-size: ' . $section_tab_fontsize_xs . 'px;
				}
			}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		var section_tab_item = data.section_tab_item || array();
		//选项卡高度
		var section_tab_height = data.section_tab_height || 256;
		//文字大小
		var section_tab_fontsize = data.section_tab_fontsize || 18;
		//文字颜色
		var section_tab_fontcolor = data.section_tab_fontcolor || "#ffffff";
		//图标大小
		var section_tab_icon_size = data.section_tab_icon_size || 100;
		//选项卡一行显示个数
		var section_tab_num = data.section_tab_num || 5;
		#>
        <style type="text/css">
			{{addonId}} * {
				margin: 0;
			}
			{{addonId}} .section-5 {
				width: 100%;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
			}
			{{addonId}} .section-5 .item {
				width: calc(100% / {{ section_tab_num.md }});
				text-decoration: none;
				background-color: #585656;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				height: {{ section_tab_height.md }}px;
			}
			{{addonId}} .section-5 .item .icon {
				display: block;
				margin-bottom: 14px;
				transition: all ease-in-out 300ms;
				width: {{section_tab_icon_size.md}}px;
			}
			{{addonId}} .section-5 .item .icon:hover {
				transform: scale(1.2);
			}
			{{addonId}} .section-5 .item .name {
				color: {{ section_tab_fontcolor }};
				font-size: {{ section_tab_fontsize.md }}px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				line-height: 32px;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    {{addonId}} .section-5 .item {
				    width: calc(100% / {{ section_tab_num.sm }});
				    height: {{ section_tab_height.sm }}px;
				}
				{{addonId}} .section-5 .item .icon {  
				    width: {{section_tab_icon_size.sm}}px;
				}
				{{addonId}} .section-5 .item .name {
				    font-size: {{ section_tab_fontsize.sm }}px;
				}
			}
			@media (max-width: 767px) {
			    {{addonId}} .section-5 .item {
				    width: calc(100% / {{ section_tab_num.xs }});
				    height: {{ section_tab_height.xs }}px;
				}
				{{addonId}} .section-5 .item .icon {  
				    width: {{section_tab_icon_size.xs}}px;
				}
				{{addonId}} .section-5 .item .name {
				    font-size: {{ section_tab_fontsize.xs }}px;
				}
			}
		</style>
        <div class="section-5">
            <# _.each(section_tab_item, function(accordion_item, key){ #>
                <a href="" class="item" style="background-color: {{ accordion_item.bgColor }};">
                    <img src=\'{{ accordion_item.icon }}\' alt="" class="icon" />
                    <p class="name">{{accordion_item.name}}</p>
                </a>      
            <# }); #>	
		</div>
		';

        return $output;
    }
}
