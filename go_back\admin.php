<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees123
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'general',
		'addon_name' => 'go_back',
		'title' => '返回上一页',
		'desc' => '插件用于返回上一个页面',
		'category' => '其他',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => '管理标签',
					'desc' => '定义一个管理标签，便于识别。',
					'std' => ''
				),
				'separator_options' => array(
					'type' => 'separator',
					'title' => '插件选项'
				),
				'back_theme' => array(
					'type' => 'select',
					'title' => '布局选择',
					'values' => array(
						'back1' => '布局1',
					),
					'std' => 'back1'
				),
				'back_style' => array(
					'type' => 'buttons',
					'title' => '插件设置',
					'std' => 'icon',
					'values' => array(
						array(
							'label' => '图标',
							'value' => 'icon'
						),
						array(
							'label' => '文字',
							'value' => 'text'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('back_theme', '=', 'back1')
					),
				),
				'is_back_icon' => array(
					'type' => 'checkbox',
					'title' => '开启图标',
					'std' => 1,
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'icon'),
					),
				),
				'back_icon' => array(
					'type' => 'media',
					'title' => '返回图标',
					'std' => 'https://oss.lcweb01.cn/joomla/20231010/9ca82c68ea332aaa56b806742cc73571.png',
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'icon'),
						array('is_back_icon', '=', 1),
					),
				),
				'back_icon_h' => array(
					'type' => 'media',
					'title' => '移入图标',
					'std' => '',
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'icon'),
						array('is_back_icon', '=', 1),
					),
				),
				'back_icon_width' => array(
					'type' => 'slider',
					'title' => '图标宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'icon'),
						array('is_back_icon', '=', 1),
					),
				),
				'back_icon_height' => array(
					'type' => 'slider',
					'title' => '图标高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'icon'),
						array('is_back_icon', '=', 1),
					),
				),
				'is_back_text' => array(
					'type' => 'checkbox',
					'title' => '开启文字',
					'std' => 1,
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
					),
				),
				'back_text' => array(
					'type' => 'text',
					'title' => '返回文字',
					'std' => '返回',
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
						array('is_back_text', '=', 1),
					),
				),
				'back_text_style' => array(
					'type' => 'buttons',
					'title' => '文字状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '移入',
							'value' => 'hover'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
						array('is_back_text', '=', 1),
					),
				),
				'back_text_color' => array(
					'type' => 'color',
					'title' => '文字颜色',
					'std' => '#000',
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
						array('is_back_text', '=', 1),
						array('back_text_style', '=', 'normal'),
					),
				),
				'back_text_fontsize' => array(
					'type' => 'slider',
					'title' => '文字大小',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
						array('is_back_text', '=', 1),
						array('back_text_style', '=', 'normal'),
					),
				),
				'back_text_color_h' => array(
					'type' => 'color',
					'title' => '移入文字颜色',
					'depends' => array(
						array('back_theme', '=', 'back1'),
						array('back_style', '=', 'text'),
						array('is_back_text', '=', 1),
						array('back_text_style', '=', 'hover'),
					),
				),
				// 'back_text_fontsize_h' => array(
				// 	'type' => 'slider',
				// 	'title' => '移入文字大小',
				// 	'max' => 100,
				// 	'min' => 0,
				// 	'responsive' => true,
				// 	'depends' => array(
				// 		array('back_theme', '=', 'back1'),
				// 		array('back_style', '=', 'text'),
				// 		array('is_back_text', '=', 1),
				// 		array('back_text_style', '=', 'hover'),
				// 	),
				// ),
			),
		),
	)
);
