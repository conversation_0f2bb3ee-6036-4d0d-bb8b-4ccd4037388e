<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_image',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_DESC'),
		'category' => '图片',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'image_layout' => array(
					'type' => 'select',
					'title' => JText::_('选择布局'),
					'values' => array(
						'layout1' => '图片', //布局一
						'layout2' => '背景图', //布局二
						'layout3' => '动图', //布局三
					),
					'std' => 'layout1',
				),


				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'responsive' => true,
					'max' => 400,
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'responsive' => true,
					'max' => 400,
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),


					),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'responsive' => true,
					'max' => 400,
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'responsive' => true,
					'max' => 400,
				),

				'title_padding' => array(
					'type' => 'padding',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
					'placeholder' => '10',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'responsive' => true,
					'std' => ''
				),

				'title_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_POSITION'),
					'values' => array(
						'top' => 'Top',
						'bottom' => 'Bottom',
					),
					'std' => 'top',
					'depends' => array(
						array('title', '!=', ''),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'option_type' => array(
					'type' => 'buttons',
					'title' => '设置',
					'std' => 'plain',
					'values' => array(
						array(
							'label' => '普通',
							'value' => 'plain'
						),
						array(
							'label' => '高级',
							'value' => 'senior'
						),
					),
					'tabs' => true,
					'depends' => array(
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				// 2021.11.15 后台获取数据广告管理
				'aboutcase' => array(
					'type'  => 'checkbox',
					'title' => '后台获取广告管理',
					'desc' => '开启后台获取广告图后，选择对应要展示的后台添加的广告图即可，无需在操作端再添加图片',
					'std'   => 0,
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('ht_image_data', '!=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),
				'ht_image_data' => array(
					'type'  => 'checkbox',
					'title' => '后台获取图片管理',
					'desc' => '开启后台获取图片管理，选择对应要展示的后台添加的图片数据，无需在操作端再添加图片',
					'std'   => 0,
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('aboutcase', '!=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),
				'ht_image_id' => array(
					'type' => 'select',
					'title' => JText::_('选择图片'),
					'values' => JwPageFactoryBase::getimageList($site_id, $company_id)['list'],
					'depends' => array(
						array('ht_image_data', '=', '1'),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'adv_id' => array(
					'type' => 'select',
					'title' => JText::_('选择广告图'),
					'values' => JwPageFactoryBase::getAdvList($site_id, $company_id)['list'],
					'depends' => array(
						array('aboutcase', '=', '1'),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),


				'image' => array(
					'type' => 'media',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT_DESC'),
					'show_input' => true,
					'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('aboutcase', '!=', '1'),
						array('ht_image_data', '!=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'image_open_bfb' => array(
					'type' => 'checkbox',
					'title' => JText::_('图片宽度使用百分比'),
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std' => 0
				),
				'image_width_bfb' => array(
					'type' => 'slider',
					'title' => JText::_('图片宽度%'),
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('image_open_bfb', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'max' => 100,
					'min' => 0,
					'std' => 100
				),

				'image_width' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WIDTH'),
					'max' => 2000,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('image_open_bfb', '!=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std' => 425
				),

				'image_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_HEIGHT'),
					'max' => 2000,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std' => 425
				),

				'border_radius' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS'),
					'std' => 0,
					'max' => 1200,
					'depends' => array(
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),

				'alt_text' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ALT_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ALT_TEXT_DESC'),
					'std' => 'Image',
					'depends' => array(
						array('image', '!=', ''),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'jwpf-text-center',
					'depends' => array(
						array('image', '!=', ''),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),
				'image_fit' => array(
					'type' => 'select',
					'title' => '图片填充方式',
					'values' => array(
						'fill' => '占满不切割显示',
						'cover' => '占满切割显示',
						'scale-down' => '自适应显示',
					),
					'depends' => array(
						array('image_layout', '!=', 'layout3'),
					),
				),

				'open_lightbox' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_OPEN_LIGHTBOX'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_OPEN_LIGHTBOX_DESC'),
					'std' => 0,
					'depends' => array(
						array('image', '!=', ''),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'overlay_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_OVERLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_OVERLAY_DESC'),
					'std' => 'rgba(119, 219, 31, .5)',
					'depends' => array(
						array('image', '!=', ''),
						array('open_lightbox', '!=', 0),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
				),

				'layout1_open_link' => array(
					'type' => 'checkbox',
					'title' => '关闭跳转链接',
					'std' => 0,
					'depends' => array(
						array('image', '!=', ''),
						array('open_lightbox', '!=', 1),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),
					)
				),

				'link' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头 【注：如果同时选择了内部链接，会优先跳内部链接】'),
					'std' => '',
					'depends' => array(
						array('image', '!=', ''),
						array('open_lightbox', '!=', 1),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),
						array('layout1_open_link', '!=', 1),
					),
				),


				'detail_page_id' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('image', '!=', ''),
						array('open_lightbox', '!=', 1),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),
						array('layout1_open_link', '!=', 1),

					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),

					'depends' => array(
						array('image', '!=', ''),
						array('open_lightbox', '!=', 1),
						array('option_type', '=', 'plain'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),
						array('layout1_open_link', '!=', 1),

					),
				),

				//高级设置
				'isudown' => array(
					'type' => 'checkbox',
					'title' => '开启图片上下跳动',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'isFixed' => array(
					'type' => 'checkbox',
					'title' => '开启图片浮动',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),

				'fixed_style' => array(
					'type' => 'select',
					'title' => '图片浮动方式',
					'std' => 'left',
					'values' => array(
						'left' => '左浮动',
						'right' => '右浮动',
						'right_bottom' => '右下浮动',
						'left-center' => '左居中',
						'right-center' => '右居中',
						'custom' => '自定义'
					),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isFixed', '=', 1),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'fixed_top' => array(
					'type' => 'number',
					'title' => '上浮动',
					'std' => 40,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isFixed', '=', 1),
						array('fixed_style', '=', 'custom'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'fixed_right' => array(
					'type' => 'number',
					'title' => '右浮动',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isFixed', '=', 1),
						array('fixed_style', '=', 'custom'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'fixed_bottom' => array(
					'type' => 'number',
					'title' => '下浮动',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isFixed', '=', 1),
						array('fixed_style', '=', 'custom'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'fixed_left' => array(
					'type' => 'number',
					'title' => '左浮动',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isFixed', '=', 1),
						array('fixed_style', '=', 'custom'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),

				'image_fangda' => array(
					'type' => 'checkbox',
					'title' => JText::_('开启图片滑动放大'),
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),


				'fix_img_height' => array(
					'type' => 'checkbox',
					'title' => JText::_('开启小图标跳动'),
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'wx_image_code2' => array(
					'type' => 'media',
					'title' => '跳动小图标',
					'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('fix_img_height', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'wx_image_code2_w' => array(
					'type' => 'slider',
					'title' =>  '跳动小图标宽度',
					'std' => 0,
					'max' => 120,
					'depends' => array(
						array('fix_img_height', '=', '1'),
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'wx_image_code2_h' => array(
					'type' => 'slider',
					'title' =>  '跳动小图标高度',
					'std' => 0,
					'max' => 120,
					'depends' => array(
						array('fix_img_height', '=', '1'),
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'wx_image_code2_x' => array(
					'type' => 'slider',
					'title' =>  '跳动小图标左右位置',
					'std' => 0,
					'max' => 820,
					'depends' => array(
						array('fix_img_height', '=', '1'),
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'wx_image_code2_y' => array(
					'type' => 'slider',
					'title' =>  '跳动小图标上下位置',
					'std' => 0,
					'max' => 820,
					'depends' => array(
						array('fix_img_height', '=', '1'),
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				//2021.12.08添加
				'isMask' => array(
					'type' => 'checkbox',
					'title' => '开启图片遮罩',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'zz_animt' => array(
					'type' => 'select',
					'title' => JText::_('遮罩背景动画'),
					'values' => array(
						'bot' => '从下到上显示',
						'center' => '从中间放大',
					),
					'std' => 'bot',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_buju', '=', 'quan'),
					)
				),
				'zz_buju' => array(
					'type' => 'select',
					'title' => JText::_('选择遮罩效果'),
					'values' => array(
						'quan' => '全屏遮罩',
						'biao' => '标题遮罩',
					),
					'std' => 'quan',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					)
				),
				'zz_width' => array(
					'type' => 'slider',
					'title' => JText::_('遮罩标题宽度%'),
					'std' => '100',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					)
				),
				'mask_title_borderradius'    => array(
					'type'    => 'margin',
					'title'   => JText::_('标题圆角'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std' => '0px 0px 0px 0px',

				),

				'mask_title' => array(
					'type' => 'text',
					'title' => JText::_('遮罩图片标题'),
					'desc' => JText::_('遮罩图片标题'),
					'std' => '图片标题',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'zzfont_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'mask_title_fontsize' => array(
					'type'       => 'slider',
					'title'      => JText::_('标题字体大小'),
					'depends'    => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'max'        => 50,
					'std'        => 20,
				),
				'mask_title_font_color'    => array(
					'type'    => 'color',
					'title'   => JText::_('标题字体颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std'    => '#ffffff',
				),
				'mask_title_font_hvcolor'    => array(
					'type'    => 'color',
					'title'   => JText::_('划过标题字体颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std'    => '#ffffff',
				),
				'mask_title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('标题行高'),
					'std' => '65',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'max' => 500,
				),

				'mask_intro' => array(
					'type' => 'textarea',
					'title' => JText::_('遮罩图片简介'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std' => '图片简介'
				),
				'mask_intro_fontsize' => array(
					'type'       => 'slider',
					'title'      => JText::_('简介字体大小'),
					'depends'    => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'max'        => 50,
					'std'        => 14,
				),
				'mask_intro_font_color'    => array(
					'type'    => 'color',
					'title'   => JText::_('简介字体颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std'    => '#ffffff',
				),

				'mask_bg_color'    => array(
					'type'    => 'color',
					'title'   => JText::_('遮罩背景颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std'    => 'rgba(15, 63, 119, 0.7)',
				),
				'mask_bg_hvcolor'    => array(
					'type'    => 'color',
					'title'   => JText::_('划过背景颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std'    => 'rgba(255, 255, 255, .6)',
				),

				'mask_intro' => array(
					'type' => 'textarea',
					'title' => JText::_('遮罩图片简介'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					),
					'std' => '图片简介'
				),

				'button_title' => array(
					'type' => 'text',
					'title' => JText::_('按钮上的文字'),
					'std' => '了解更多',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					)
				),
				'mask_button_color'    => array(
					'type'    => 'color',
					'title'   => JText::_('按钮上字体颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std'    => '#ffffff',
				),
				'button_bg_color'    => array(
					'type'    => 'color',
					'title'   => JText::_('按钮背景颜色'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std'    => '#89c400',
				),
				'zz_padding' => array(
					'type' => 'padding',
					'title' => JText::_('遮罩内容的上间距(%)'),
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('isMask', '=', '1'),
						array('zz_buju', '!=', 'biao'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

						array('zz_animt', '!=', 'center'),

					),
					'std' => '35% 1% 10% 1%'
				),


				// 2022.8.18
				'is_hover' => array(
					'type' => 'checkbox',
					'title' => '开启划过显示二维码',
					'std' => 0,
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('image_layout', '!=', 'layout2'),
						array('image_layout', '!=', 'layout3'),

					)
				),
				'erweim' => array(
					'type' => 'media',
					'title' => JText::_('二维码'),
					'std' => 'https://oss.lcweb01.cn/joomla/20220818/dbab5d8110af6fe79939930cbc6aac5b.png',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),
				'erweim_bg' => array(
					'type' => 'media',
					'title' => JText::_('二维码背景图'),
					'std' => 'https://oss.lcweb01.cn/joomla/20220818/74ca67254c87c948346935fb67d22fc1.png',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),
				'erweim_width' => array(
					'type' => 'slider',
					'title' => JText::_('背景图宽度'),
					'std' => '100',
					'max' => '200',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),
				'erweim_rele' => array(
					'type' => 'select',
					'title' => JText::_('二维码位置'),
					'values' => array(
						'top' => '图片上方',
						'bot' => '图片下方',
					),
					'std' => 'bot',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),
				'erweim_top' => array(
					'type' => 'slider',
					'title' => JText::_('上/下间距'),
					'std' => '30',
					'max' => '500',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),
				'erweim_left' => array(
					'type' => 'slider',
					'title' => JText::_('左/右间距'),
					'std' => '0',
					'max' => '1000',
					'depends' => array(
						array('option_type', '=', 'senior'),
						array('is_hover', '=', '1'),
					),
				),

				//
				//
				// 背景图布局
				'pc_img' => array(
					'type' => 'media',
					'title' => 'PC图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220723/1b7d1451d17a22abb1dd4b7d0debeb5c.jpg',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'phone_img' => array(
					'type' => 'media',
					'title' => '手机图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220723/bc5fe0ea84872eee9803013b2b58e8bb.jpg',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),



				'open_text' => array(
					'type' => 'checkbox',
					'title' => '开启标题',
					'std' => '1',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'content' => array(
					'type' => 'text',
					'title' => '标题',
					'std' => '经典系列',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_text', '=', '1'),
					)
				),
				'content_fontsize' => array(
					'type' => 'slider',
					'title' => '标题字体大小',
					'std' => array(
						'md' => 52,
						'sm' => 35,
						'xs' => 28
					),
					'responsive' => true,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_text', '=', '1'),
					)
				),
				'content_color' => array(
					'type' => 'color',
					'title' => '标题颜色',
					'std' => '#fff',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_text', '=', '1'),
					)
				),
				'open_img' => array(
					'type' => 'checkbox',
					'title' => '开启标题下图片',
					'std' => '1',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'bg_img' => array(
					'type' => 'media',
					'title' => '图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220723/635e913d671e56cfda28915753d4eddd.png',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_img', '=', '1'),
					)
				),
				'bg_img_height' => array(
					'type' => 'slider',
					'title' => '图片高度',
					'std' => array(
						'md' => 68,
						'sm' => 50,
						'xs' => 35
					),
					'responsive' => true,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_img', '=', '1'),
					)
				),
				'open_button' => array(
					'type' => 'checkbox',
					'title' => '开启按钮',
					'std' => '1',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'button_width' => array(
					'type' => 'slider',
					'title' => '按钮宽度',
					'std' => array(
						'md' => 171,
						'sm' => 130,
						'xs' => 110
					),
					'responsive' => true,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_button', '=', '1'),
					)
				),
				'button_img' => array(
					'type' => 'media',
					'title' => '按钮背景图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220723/e7ada5b4ef717d3cf6e46bdf7905c3cb.png',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_button', '=', '1'),
					)
				),
				'button_imghv' => array(
					'type' => 'media',
					'title' => '按钮划过背景图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220723/0756cbcc1dcd81b4e7c2fe20736db15f.png',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_button', '=', '1'),
					)
				),
				'button_text' => array(
					'type' => 'text',
					'title' => '按钮文字',
					'std' => 'read more',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_button', '=', '1'),
					)
				),

				//

				'open_link' => array(
					'type' => 'checkbox',
					'title' => '开启跳转链接',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'sec_link' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'std' => 'nei',
					'values' => array(
						'nei' => '内部页面',
						'wai' => '外部链接',
					),
					'depends' => array(
						array('open_link', '=', '1'),
						array('image_layout', '=', 'layout2'),
					)
				),
				'link_w' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('open_link', '=', '1'),
						array('sec_link', '=', 'wai'),
						array('image_layout', '=', 'layout2'),
					),
				),
				'ndetail_page_id' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('open_link', '=', '1'),
						array('sec_link', '=', 'nei'),
						array('image_layout', '=', 'layout2'),
					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'targets' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'depends' => array(
						array('open_link', '=', '1'),
						array('image_layout', '=', 'layout2'),
					),
				),

				'open_intro' => array(
					'type' => 'checkbox',
					'title' => '开启简介',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'intro_text' => array(
					'type' => 'editor',
					'title' => '简介文字',
					'std' => '',
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_intro', '=', '1'),
					)
				),
				'fontbg_family' => array(
					'type' => 'fonts',
					'title' => JText::_('简介字体'),
					'depends' => array(
						array('image_layout', '=', 'layout2'),
						array('open_intro', '=', '1'),
					),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'cont_top' => array(
					'type' => 'slider',
					'title' => '内容上部距离（%）',
					'std' => array(
						'md' => 48,
						'sm' => 48,
						'xs' => 48
					),
					'responsive' => true,
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),
				'cont_left' => array(
					'type' => 'slider',
					'title' => '内容左侧边距',
					'max' => 1000,
					'std' => array(
						'md' => 150,
						'sm' => 120,
						'xs' => 18
					),
					'depends' => array(
						array('image_layout', '=', 'layout2'),
					)
				),

				//布局3
				'type3_img' => array(
					'type' => 'media',
					'title' => '背景图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20230105/288455b29b142e19a02b268e2cf25360.jpg',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title1' => array(
					'type' => 'text',
					'title' => '标题1',
					'std' => '护助时代',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title1_open' => array(
					'type' => 'checkbox',
					'title' => '标题1开启跳转',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'tz1_fs' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'values' => array(
						'nei' => JText::_('内部页面'),
						'wai' => JText::_('外部链接'),
					),
					'std' => 'nei',
					'depends' => array(
						array('type3_title1_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),
				'link1' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('type3_title1_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz1_fs', '=', 'wai'),

					),
				),
				'detail_page_id1' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('type3_title1_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz1_fs', '=', 'nei'),

					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target1' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '_self',
					'depends' => array(
						array('type3_title1_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),


				'type3_title2' => array(
					'type' => 'text',
					'title' => '标题2',
					'std' => '参访交流',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title2_open' => array(
					'type' => 'checkbox',
					'title' => '标题2开启跳转',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'tz2_fs' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'values' => array(
						'nei' => JText::_('内部页面'),
						'wai' => JText::_('外部链接'),
					),
					'std' => 'nei',
					'depends' => array(
						array('type3_title2_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),
				'link2' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('type3_title2_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz2_fs', '=', 'wai'),

					),
				),
				'detail_page_id2' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('type3_title2_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz2_fs', '=', 'nei'),

					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target2' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '_self',
					'depends' => array(
						array('type3_title2_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),

				'type3_title3' => array(
					'type' => 'text',
					'title' => '标题3',
					'std' => '介护教育',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title3_open' => array(
					'type' => 'checkbox',
					'title' => '标题3开启跳转',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'tz3_fs' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'values' => array(
						'nei' => JText::_('内部页面'),
						'wai' => JText::_('外部链接'),
					),
					'std' => 'nei',
					'depends' => array(
						array('type3_title3_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),
				'link3' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('type3_title3_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz3_fs', '=', 'wai'),

					),
				),
				'detail_page_id3' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('type3_title3_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz3_fs', '=', 'nei'),

					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target3' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '_self',
					'depends' => array(
						array('type3_title3_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),


				'type3_title4' => array(
					'type' => 'text',
					'title' => '标题4',
					'std' => '日语培训',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title4_open' => array(
					'type' => 'checkbox',
					'title' => '标题4开启跳转',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'tz4_fs' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'values' => array(
						'nei' => JText::_('内部页面'),
						'wai' => JText::_('外部链接'),
					),
					'std' => 'nei',
					'depends' => array(
						array('type3_title4_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),
				'link4' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('type3_title4_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz5_fs', '=', 'wai'),

					),
				),
				'detail_page_id4' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('type3_title4_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz5_fs', '=', 'nei'),

					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target4' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '_self',
					'depends' => array(
						array('type3_title4_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),


				'type3_title5' => array(
					'type' => 'text',
					'title' => '标题5',
					'std' => '留学合作',
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'type3_title5_open' => array(
					'type' => 'checkbox',
					'title' => '标题5开启跳转',
					'std' => 0,
					'depends' => array(
						array('image_layout', '=', 'layout3'),
					)
				),
				'tz5_fs' => array(
					'type' => 'select',
					'title' => '跳转页面',
					'values' => array(
						'nei' => JText::_('内部页面'),
						'wai' => JText::_('外部链接'),
					),
					'std' => 'nei',
					'depends' => array(
						array('type3_title5_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),

				'link5' => array(
					'type' => 'text',
					'title' => JText::_('外部链接->http(https)开头'),
					'std' => '',
					'depends' => array(
						array('type3_title5_open', '=', '1'),
						array('tz5_fs', '=', 'wai'),
						array('image_layout', '=', 'layout3'),
					),
				),
				'detail_page_id5' => array(
					'type' => 'select',
					'title' => '内部页面链接',
					'desc' => '',
					'depends' => array(
						array('type3_title5_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
						array('tz5_fs', '=', 'nei'),
					),
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'target5' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
					'values' => array(
						'_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '_self',
					'depends' => array(
						array('type3_title5_open', '=', '1'),
						array('image_layout', '=', 'layout3'),
					),
				),

				// 点击按钮出弹窗
				'dialog_addon_id' => array(
					'title' => '弹窗插件ID（需要搭配弹窗插件使用）',
					'desc' => '需要搭配弹窗插件使用',
					'type' => 'text',
					'std' => '',
					'depends' => array(
						array('layout1_open_link', '=', '1'),
					)
				),
				// 点击按钮出弹窗结束

				// 点击按钮弹出商桥
				'business_bridge_show' => array(
					'title' => '是否需要点击弹出商桥',
					'desc' => '需要搭配原始html使用，仅支持百度爱番番和快商通',
					'type' => 'checkbox',
					'std' => 0,
					'depends' => array(
						array('layout1_open_link', '=', '1'),
					),
				),
				// 点击按钮弹出商桥结束

				//
				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),

			),
		),
	)
);
