<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'search_box',
         'title' => JText::_('输入框'),
         'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
         'category' => '常用插件',
         'attr' => array(
             'general' => array(
                 'search_type' => array(
                     'type' => 'select',
                     'title' => '选择输入框类型',
                     'desc' => '',
                     'values' => array(
                         'type1' => '搜索结果',
                         'type2' => '咨询',
                     ),
                     'std' => 'type1'
                 ),
                 'detail_page_id' => array(
                     'type' => 'select',
                     'title' => '选择搜索结果页',
                     'desc' => '',
                     'depends' => array('search_type' => 'type1'),
                     'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                 ),
                 'search_button_text' => array(
                     'type' => 'text',
                     'title' => JText::_('按钮文本'),
                     'desc' => JText::_(''),
                     'std' => '确定',
                 ),
                 'search_button_font_size' => array(
                  'type' => 'slider',
                  'title' => JText::_('按钮文本大小'),
                  'desc' => JText::_('按钮文本大小'),
                  'max' => 50,
                  'min' => 1,
                  'std' => 16,
              ),
                 'search_input_text' => array(
                     'type' => 'text',
                     'title' => JText::_('输入框提示文本内容'),
                     'desc' => JText::_(''),
                     'std' => '填写搜索的内容',
                 ),

                 'input_bg' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框背景颜色'),
                     'std' => '#ffffff'
                 ),
                 'input_color_pla' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框提示文本字体颜色'),
                     'std' => '#bbbbbb'
                 ),
                 'input_color' => array(
                     'type' => 'color',
                     'title' => JText::_('输入框输入字体颜色'),
                     'std' => '#bbbbbb'
                 ),
                 'search_wz' => array(
                   'type' => 'select',
                   'title' => '搜索框位置',
                   'values' => array(
                     'flex-start' => '左',
                     'center' => '中',
                     'flex-end' => '右',
                   ),
                   'std' => 'flex-start'
                 ),
                 'border_color' => array(
                   'type' => 'color',
                   'title' => JText::_('搜索框边框颜色'),
                   'std' => '#000000'
                 ),
                 'button_color' => array(
                   'type' => 'color',
                   'title' => JText::_('按钮颜色'),
                   'std' => '#eeeeee'
                 ),
                 'button_font_color' => array(
                   'type' => 'color',
                   'title' => JText::_('按钮字体颜色'),
                   'std' => '#000000'
                 ),
                 'button_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮长度'),
                   'placeholder' => '100',
                   'max' => 300,
                   'min' => 0,
                   'std' => '60'
                 ),
                 'button_border_color' => array(
                   'type' => 'color',
                   'title' => JText::_('按钮边框颜色'),
                   'std' => '#000000'
                 ),
                 'input_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框长度'),
                   'placeholder' => '100',
                   'max' => 1200,
                   'min' => 0,
                   'std' => '150'
               ),
                 'input_height' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框高度'),
                   'placeholder' => '10',
                   'max' => 100,
                   'min' => 0,
                   'std' => '36'
               ),
                'button_border_radius' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮圆角'),
                   'placeholder' => '10',
                   'max' => 20,
                   'min' => 0,
                   'std' => '2'
               ),
                'search_border_radius' => array(
                   'type' => 'slider',
                   'title' => JText::_('搜索框圆角'),
                   'placeholder' => '10',
                   'max' => 20,
                   'min' => 0,
                   'std' => '0'
               ),
               'search_button_num' => array(
                   'type' => 'slider',
                   'title' => JText::_('间距'),
                   'placeholder' => '10',
                   'max' => 50,
                   'min' => 0,
                   'std' => '0'
               ),
             ),
         ),
     )
 );
