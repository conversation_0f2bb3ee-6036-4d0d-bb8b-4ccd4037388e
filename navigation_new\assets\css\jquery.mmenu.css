html.mm-opened .mm-page,
html.mm-opened #mm-blocker,
html.mm-opened .mm-fixed-top,
html.mm-opened .mm-fixed-bottom,
html.mm-opened .mm-menu.mm-horizontal > .mm-panel {
  -webkit-transition: none 0.4s ease;
  -moz-transition: none 0.4s ease;
  -ms-transition: none 0.4s ease;
  -o-transition: none 0.4s ease;
  transition: none 0.4s ease;
  -webkit-transition-property: top, right, bottom, left, border, -webkit-transform;
  -moz-transition-property: top, right, bottom, left, border, -moz-transform;
  -ms-transition-property: top, right, bottom, left, border, -ms-transform;
  -o-transition-property: top, right, bottom, left, border, -o-transform;
  transition-property: top, right, bottom, left, border, transform; }
html.mm-opened .mm-page,
html.mm-opened #mm-blocker {
  left: 0%;
  top: 0;
  margin: 0;
  border: 0px solid rgba(0, 0, 0, 0); }
html.mm-opened.mm-opening .mm-page,
html.mm-opened.mm-opening #mm-blocker {
  border: 0px solid rgba(100, 100, 100, 0); }

.mm-menu .mm-hidden {
  display: none; }

.mm-fixed-top,
.mm-fixed-bottom {
  position: fixed;
  left: 0; }

.mm-fixed-top {
  top: 0; }

.mm-fixed-bottom {
  bottom: 0; }

html.mm-opened .mm-page,
.mm-menu > .mm-panel {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box; }

html.mm-opened,
html.mm-opened body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative; }

html.mm-opened .mm-page {
  height: 100%;
  overflow: hidden;
  position: absolute; }

html.mm-background .mm-page {
  background: inherit; }

#mm-blocker {
  background: #fff;
  opacity: 0;
  display: none;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 9999; }

html.mm-opened #mm-blocker,
html.mm-blocking #mm-blocker {
  display: block; }

.mm-menu.mm-current {
  display: block; }

.mm-menu {
  background: inherit;
  display: none;
  overflow: hidden;
  height: 100%;
  padding: 0;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0; }
  .mm-menu > .mm-panel {
    background: inherit;
    -webkit-overflow-scrolling: touch;
    overflow: scroll;
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    padding: 20px;
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 0; }
    .mm-menu > .mm-panel.mm-opened {
      left: 0%; }
    .mm-menu > .mm-panel.mm-subopened {
      left: -40%; }
    .mm-menu > .mm-panel.mm-highest {
      z-index: 1; }
    .mm-menu > .mm-panel.mm-hidden {
      display: block;
      visibility: hidden; }

.mm-menu .mm-list {
  padding: 20px 0; }
.mm-menu > .mm-list {
  padding: 20px 0 40px 0; }

.mm-panel > .mm-list {
  margin-left: -20px;
  margin-right: -20px; }
  .mm-panel > .mm-list:first-child {
    padding-top: 0; }

.mm-list,
.mm-list > li {
  list-style: none;
  display: block;
  padding: 0;
  margin: 0; }

.mm-list * {
  -webkit-text-size-adjust: none;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  text-shadow: none; }
.mm-list a,
.mm-list a:hover {
  text-decoration: none; }
.mm-list > li {
  position: relative; }
  .mm-list > li > a,
  .mm-list > li > span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: inherit;
    line-height: 20px;
    display: block;
    padding: 10px 10px 10px 20px;
    margin: 0; }
.mm-list > li:not(.mm-subtitle):not(.mm-label):not(.mm-noresults)::after {
  content: '';
  border-bottom-width: 1px;
  border-bottom-style: solid;
  display: block;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0; }
.mm-list > li:not(.mm-subtitle):not(.mm-label):not(.mm-noresults):after {
  width: auto;
  margin-left: 20px;
  position: relative;
  left: auto; }
.mm-list a.mm-subopen {
  width: 40px;
  height: 100%;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2; }
  .mm-list a.mm-subopen::before {
    content: '';
    border-left-width: 1px;
    border-left-style: solid;
    display: block;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0; }
  .mm-list a.mm-subopen.mm-fullsubopen {
    width: 100%; }
    .mm-list a.mm-subopen.mm-fullsubopen:before {
      border-left: none; }
  .mm-list a.mm-subopen + a,
  .mm-list a.mm-subopen + span {
    padding-right: 5px;
    margin-right: 40px; }
.mm-list > li.mm-selected > a.mm-subopen {
  background: transparent; }
.mm-list > li.mm-selected > a.mm-fullsubopen + a,
.mm-list > li.mm-selected > a.mm-fullsubopen + span {
  padding-right: 45px;
  margin-right: 0; }
.mm-list a.mm-subclose {
  text-indent: 20px;
  padding-top: 30px;
  margin-top: -20px; }
.mm-list > li.mm-label {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 10px;
  text-transform: uppercase;
  text-indent: 20px;
  line-height: 25px;
  padding-right: 5px; }
.mm-list > li.mm-spacer {
  padding-top: 40px; }
  .mm-list > li.mm-spacer.mm-label {
    padding-top: 25px; }
.mm-list a.mm-subopen:after,
.mm-list a.mm-subclose:before {
  content: '';
  border: 2px solid transparent;
  display: block;
  width: 7px;
  height: 7px;
  margin-bottom: -5px;
  position: absolute;
  bottom: 50%;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg); }
.mm-list a.mm-subopen:after {
  border-top: none;
  border-left: none;
  right: 18px; }
.mm-list a.mm-subclose:before {
  border-right: none;
  border-bottom: none;
  margin-bottom: -15px;
  left: 22px; }

.mm-menu.mm-vertical .mm-list .mm-panel {
  display: none;
  padding: 10px 0 10px 10px; }
  .mm-menu.mm-vertical .mm-list .mm-panel li:last-child:after {
    border-color: transparent; }
.mm-menu.mm-vertical .mm-list li.mm-opened > .mm-panel {
  display: block; }
.mm-menu.mm-vertical .mm-list > li.mm-opened > a.mm-subopen {
  height: 40px; }
  .mm-menu.mm-vertical .mm-list > li.mm-opened > a.mm-subopen:after {
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    top: 16px;
    right: 16px; }

html.mm-opened .mm-page {
 /* box-shadow: 0 0 20px rgba(0, 0, 0, 0.5); */}

.mm-ismenu {
  background: #333333; }

.mm-menu {
  color: rgba(255, 255, 255, 0.6); }
  .mm-menu .mm-list > li:after {
    border-color: rgba(0, 0, 0, 0.15); }
  .mm-menu .mm-list > li > a.mm-subclose {
    background: rgba(0, 0, 0, 0.1);
    color: rgba(255, 255, 255, 0.3); }
  .mm-menu .mm-list > li > a.mm-subopen:after, .mm-menu .mm-list > li > a.mm-subclose:before {
    border-color: rgba(255, 255, 255, 0.3); }
  .mm-menu .mm-list > li > a.mm-subopen:before {
    border-color: rgba(0, 0, 0, 0.15); }
  .mm-menu .mm-list > li.mm-selected > a:not(.mm-subopen),
  .mm-menu .mm-list > li.mm-selected > span {
    background: rgba(0, 0, 0, 0.1); }
  .mm-menu .mm-list > li.mm-label {
    background: rgba(255, 255, 255, 0.05); }
  .mm-menu.mm-vertical .mm-list li.mm-opened > a.mm-subopen,
  .mm-menu.mm-vertical .mm-list li.mm-opened > ul {
    background: rgba(255, 255, 255, 0.05); }

html.mm-opened.mm-opening .mm-page,
html.mm-opened.mm-opening #mm-blocker,
html.mm-opened.mm-opening .mm-fixed-top,
html.mm-opened.mm-opening .mm-fixed-bottom {
  left: 40%; }

.mm-menu {
  width: 40%; }

@media all and (max-width: 175px) {
  .mm-menu {
    width: 140px; }

  html.mm-opened.mm-opening .mm-page,
  html.mm-opened.mm-opening #mm-blocker,
  html.mm-opened.mm-opening .mm-fixed-top,
  html.mm-opened.mm-opening .mm-fixed-bottom {
    left: 140px; } }
@media all and (min-width: 550px) {
  .mm-menu {
    width: 30; }

  html.mm-opened.mm-opening .mm-page,
  html.mm-opened.mm-opening #mm-blocker,
  html.mm-opened.mm-opening .mm-fixed-top,
  html.mm-opened.mm-opening .mm-fixed-bottom {
    left: 30; } }
html.mm-nooverflowscrolling.mm-opened {
  height: auto;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: scroll; }
  html.mm-nooverflowscrolling.mm-opened body {
    overflow: auto; }
  html.mm-nooverflowscrolling.mm-opened .mm-page {
    min-height: 1000px;
    position: fixed; }
  html.mm-nooverflowscrolling.mm-opened .mm-menu {
    height: auto;
    min-height: 1000px;
    overflow: auto;
    overflow-x: hidden;
    position: relative;
    left: auto;
    top: auto; }
  html.mm-nooverflowscrolling.mm-opened > .mm-panel {
    position: relative;
    height: auto;
    display: none;
    left: 0; }
    html.mm-nooverflowscrolling.mm-opened > .mm-panel.mm-current {
      display: block; }
