<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-14 13:51:34
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-06-02 10:36:04
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\branch\admin.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'client_case',
        'title' => JText::_('客户案例'),
        'desc' => JText::_('客户案例'),
        'category' => '龙采官网插件',
        'attr' => array(
            'general' => array(
                'style' => array(
                    'type' => 'select',
                    'title' => '选择样式',
                    'values' => array(
                        'style1' => '样式1',
                        'style2' => '样式2',
                    ),
                    'std' => 'style1'
                ),
                'jw_tab_item' => array(
                    'title' => JText::_('列表项'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => ''
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => JText::_('介绍'),
                            'std' => ''
                        ),
                        'img'=> array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'std' => ''
                        ),
                        'qr_code'=> array(
                            'type' => 'media',
                            'title' => JText::_('二维码'),
                            'std' => ''
                        ),
                        'need_link' => array(
                            'type'    => 'checkbox',
                            'title'   => JText::_('是否添加跳转链接'),
                            'std'     => 1,
                        ),
                        'link_page_select' => array(
                            'type'    => 'select',
                            'title'   => JText::_('跳转页面选择'),
                            'std'     => 'inner',
                            'values' => array(
                                'inner'=>'内页',
                                'outer'=>'外页',
                            ),
                            'depends' => array(
                                array('need_link', '=', 1)
                            )
                        ),
                        'detail_page_id' => array(
                            'type'    => 'select',
                            'title'   => JText::_('跳转内页'),
                            'std'     => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('need_link', '=', 1),
                                array('link_page_select', '=', 'inner'),
                            )
                        ),
                        'detail_page_url' => array(
                            'type'   => 'text',
                            'title'  => '跳转页面',
                            'std' => '',
                            'depends' => array(
                                array('need_link', '=', 1),
                                array('link_page_select', '=', 'outer'),
                            )
                        ),
                        'target' => array(
                            'type'   => 'select',
                            'title'  => '打开方式',
                            'values' => array(
                                '_blank' => '新窗口',
                                '_self' => '默认',
                            ),
                            'std' => '_blank',
                            'depends' => array(
                                array('need_link', '=', 1),
                            )
                        ),
                    ),
                    'std' =>array(
                        array(
                            'title'=>'房小仲',
                            'intro' => '行业类目：房地产、商业租赁。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/c86e6fd91105484185c4f3a8bd3b1252.jpg',
                            'qr_code'=>'https://oss.lcweb01.cn/joomla/20220602/bf4afe636882e02f71317433a398de50.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                        array(
                            'title'=>'良塾优学',
                            'intro' => '行业类目：智力训练、益智游戏。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/4b84837bb1685e72d35e15126b643c76.jpg',
                            'qr_code'=>'https://oss.lcweb01.cn/joomla/20220602/b60d1bb267fc6a20353177b564efd57c.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                        array(
                            'title'=>'德爱到家',
                            'intro' => '行业类目：物业管理、专业服务。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/262badd5c48d8020203536042fc10570.jpg',
                            'qr_code'=>'https://oss.lcweb01.cn/joomla/20220602/20c112f96ce6a01c9b5966feb4dca34a.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                    ),
                    'depends'=> array(
                        array('style', '=', 'style1'),
                    )
                ),
                'jw_tab_item_style2' => array(
                    'title' => JText::_('列表项'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => ''
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => JText::_('介绍'),
                            'std' => ''
                        ),
                        'img'=> array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'std' => ''
                        ),
                        'need_link' => array(
                            'type'    => 'checkbox',
                            'title'   => JText::_('是否添加跳转链接'),
                            'std'     => 1,
                        ),
                        'link_page_select' => array(
                            'type'    => 'select',
                            'title'   => JText::_('跳转页面选择'),
                            'std'     => 'outer',
                            'values' => array(
                                'inner'=>'内页',
                                'outer'=>'外页',
                            ),
                            'depends' => array(
                                array('need_link', '=', 1)
                            )
                        ),
                        'detail_page_id' => array(
                            'type'    => 'select',
                            'title'   => JText::_('跳转内页'),
                            'std'     => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('need_link', '=', 1),
                                array('link_page_select', '=', 'inner'),
                            )
                        ),
                        'detail_page_url' => array(
                            'type'   => 'text',
                            'title'  => '跳转页面',
                            'std' => '',
                            'depends' => array(
                                array('need_link', '=', 1),
                                array('link_page_select', '=', 'outer'),
                            )
                        ),
                        'target' => array(
                            'type'   => 'select',
                            'title'  => '打开方式',
                            'values' => array(
                                '_blank' => '新窗口',
                                '_self' => '默认',
                            ),
                            'std' => '_blank',
                            'depends' => array(
                                array('need_link', '=', 1),
                            )
                        ),
                    ),
                    'std' =>array(
                        array(
                            'title'=>'我们更懂开发更懂客户的需求',
                            'intro' => '凝聚互联网匠心精神、严格坚持6道工序、打造产品极致体验',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/82b76fc49fac665c58aab14d06bba212.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                        array(
                            'title'=>'专家私人订制/严谨流程',
                            'intro' => '互联网专家及17年资深开发技术，免费咨询。专门的独立项目组，实时与客户对接。产品经理全程跟进把控，严格保障系统质量与周期。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/c8ef5fda024f0605f74827f556ee9d1d.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                        array(
                            'title'=>'高质量代码/立体的感官视觉',
                            'intro' => '高质量代码，强逻辑思维，无BUG与后门。系统稳定流畅，后续升级迭代投入低，时间快。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/ff9f616f9d6a65c81b31fc113d780f39.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                        array(
                            'title'=>'致用户体验/管家式售后',
                            'intro' => '专业需求分析师与客户对接，深入了解需求后，再确定合理的报价范围。技术、客服、营销专员定期、长期全程跟进售后工作。',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220602/01e165c7bb4ec87a063065e8305f2d6b.jpg',
                            'need_link' => 1,
                            'link_page_select' => 'outer',
                            'detail_page_url' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&userId=28669567&siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '_blank'
                        ),
                    ),
                    'depends'=> array(
                        array('style', '=', 'style2'),
                    )
                ),
                
                'settings_style2' => array(
                    'title' => '样式2配置项(pc分辨率:大于1258,平板分辨率:1258~992)',
                    'desc' => 'pc分辨率:大于1258,平板分辨率:1258~992)',
                    'type' => 'buttons',
                    'tab' => true,
                    'std' => 'list',
                    'values' => array(
                        array(
                            'label' => '整体',
                            'value' => 'list'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        )
                    ),
                    'depends'=> array(
                        array('style', '=', 'style2'),
                    )
                ),
                'list_height_style2' => array(
                    'title' => '列表项高度(vw)',
                    'type' => 'slider',
                    'std' => array('md' => '29', 'sm' => '34', 'xs'=> ''),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'list'),
                    )
                ),
                'list_margin_bottom_style2' => array(
                    'title' => '列表项间距(vw)',
                    'type' => 'slider',
                    'std' => array('md' => '4', 'sm' => '4', 'xs'=> '4'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'list'),
                    )
                ),
                'img_border_radius_style2' => array(
                    'title' => '图片圆角(vw)',
                    'type' => 'slider',
                    'std' => array('md' => '0.6', 'sm' => '0.9', 'xs'=> '0.9'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'img'),
                    )
                ),
                'img_width_style2' => array(
                    'title' => '图片宽度(%)',
                    'type' => 'slider',
                    'std' => array('md' => 50, 'sm' => 50, 'xs'=> 100),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'img'),
                    )
                ),
                'content_padding_style2' => array(
                    'title' => '内容部分内边距',
                    'type' => 'padding',
                    'std' => array('md' => '7vw 0 0 0', 'sm' => '5vw 0 0 0', 'xs'=> '0 0 0 0'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                    )
                ),
                'content_settings_style2' => array(
                    'title' => '样式2内容部分配置项',
                    'type' => 'buttons',
                    'tab' => true,
                    'std' => 'index',
                    'values' => array(
                        array(
                            'label' => '序号',
                            'value' => 'index'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '介绍',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '按钮',
                            'value' => 'more'
                        )
                    ),
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                    )
                ),
                'index_size_style2' => array(
                    'title' => '序号字号(移动端不显示。vw)',
                    'type' => 'slider',
                    'std' => array('md' => 8, 'sm' => 8, 'xs'=> 8),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'index'),
                    )
                ),
                'index_color_style2' => array(
                    'title' => '序号颜色(移动端不显示。vw)',
                    'type' => 'color',
                    'std' => '#999999',
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'index'),
                    )
                ),
                'index_top_style2' => array(
                    'title' => '序号上边距(移动端不显示。vw)',
                    'type' => 'slider',
                    'std' => array('md' => 4, 'sm' => 2, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'index'),
                    )
                ),
                'index_left_style2' => array(
                    'title' => '序号左边距(移动端不显示。vw)',
                    'type' => 'slider',
                    'std' => array('md' => 0, 'sm' => 0, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'index'),
                    )
                ),
                'title_size_style2' => array(
                    'title' => '标题字号(vw)',
                    'type' => 'slider',
                    'std' => array('md' => 2.4, 'sm' => 3, 'xs'=> 4.5),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'title'),
                    )
                ),
                'title_height_style2' => array(
                    'title' => '标题行高',
                    'type' => 'slider',
                    'std' => array('md' => 1.7, 'sm' => 1.7, 'xs'=> 1.7),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'title'),
                    )
                ),
                'title_color_style2' => array(
                    'title' => '标题颜色',
                    'type' => 'color',
                    'std' => '#000000',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'title'),
                    )
                ),
                'title_margin_style2' => array(
                    'title' => '标题外边距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 0.3vw 0', 'sm' => '0 0 2vw 0', 'xs' => '10px 0 10px 0'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'title'),
                    )
                ),
                'intro_width_style2' => array(
                    'title' => '介绍部分宽度（%）',
                    'type' => 'slider',
                    'std' => array('md' => 80, 'sm' => 83, 'xs'=> 100),
                    'max' => 100,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_height_style2' => array(
                    'title' => '介绍部分最大高度（vw，移动端无效）',
                    'type' => 'slider',
                    'std' => array('md' => 9, 'sm' => 13, 'xs'=> 0),
                    'max' => 100,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_size_style2' => array(
                    'title' => '介绍字号(vw)',
                    'type' => 'slider',
                    'std' => array('md' => 1, 'sm' => 1.3, 'xs'=> 3.4),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_line_height_style2' => array(
                    'title' => '介绍行高',
                    'type' => 'slider',
                    'std' => array('md' => 1.5, 'sm' => 1.5, 'xs'=> 1.5),
                    'max' => 10,
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_color_style2' => array(
                    'title' => '介绍颜色',
                    'type' => 'color',
                    'std' => '#999999',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_margin_style2' => array(
                    'title' => '奇数介绍外边距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 20px 0', 'sm' => '0 0 20px 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'intro_margin_even_style2' => array(
                    'title' => '偶数介绍外边距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 20px auto', 'sm' => '0 0 20px auto', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'content'),
                    )
                ),
                'more_width_style2' => array(
                    'title' => '按钮宽度（移动端不显示。vw）',
                    'type' => 'slider',
                    'std' => array('md' => 10.52, 'sm' => 15, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_height_style2' => array(
                    'title' => '按钮高度（移动端不显示。vw）',
                    'type' => 'slider',
                    'std' => array('md' => 3.6, 'sm' => 5, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_color_settings_style2' => array(
                    'title' => '按钮颜色配置项',
                    'type' => 'buttons',
                    'tab' => true,
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_border_color_style2' => array(
                    'title' => '按钮边框色',
                    'type' => 'color',
                    'std' => '#949494',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'normal'),
                    )
                ),
                'more_bg_color_style2' => array(
                    'title' => '按钮背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'normal'),
                    )
                ),
                'more_font_color_style2' => array(
                    'title' => '按钮字体颜色',
                    'type' => 'color',
                    'std' => '#363636',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'normal'),
                    )
                ),
                'more_border_hover_color_style2' => array(
                    'title' => '按钮边框色',
                    'type' => 'color',
                    'std' => '#949494',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'hover'),
                    )
                ),
                'more_bg_color_hover_style2' => array(
                    'title' => '按钮背景色',
                    'type' => 'color',
                    'std' => '#949494',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'hover'),
                    )
                ),
                'more_font_color_hover_style2' => array(
                    'title' => '按钮字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                        array('more_color_settings_style2', '=', 'hover'),
                    )
                ),
                'more_font_size_style2' => array(
                    'title' => '按钮字号(移动端隐藏。vw)',
                    'type' => 'slider',
                    'std' => array('md' => 1, 'sm' => 1.4, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_radius_style2' => array(
                    'title' => '按钮圆角(移动端隐藏。vw)',
                    'type' => 'slider',
                    'std' => array('md' => 1.8, 'sm' => 5, 'xs' => 0),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_margin_style2' => array(
                    'title' => '奇数按钮外边距(移动端隐藏)',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
                'more_margin_even_style2' => array(
                    'title' => '偶数按钮外边距(移动端隐藏)',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 0 auto', 'sm' => '0 0 0 auto', 'xs' => '0 0 0 auto'),
                    'responsive' => true,
                    'depends'=> array(
                        array('style', '=', 'style2'),
                        array('settings_style2', '=', 'content'),
                        array('content_settings_style2', '=', 'more'),
                    )
                ),
            )
        ),
    )
);
