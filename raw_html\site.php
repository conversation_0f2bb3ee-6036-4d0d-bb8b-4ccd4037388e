<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonRaw_html extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		//Options
		$html = (isset($settings->html) && $settings->html) ? $settings->html : '';

		$head_wz = (isset($settings->head_wz) && $settings->head_wz) ? $settings->head_wz : 0;

		$output='';
		//Output
		if($head_wz==1){
			if ($html) {

				$noBr = str_replace(PHP_EOL, '', $html);

				$output .= '
					<script>
						var htmlStr = \''.$noBr.'\';
						var headHTML = document.getElementsByTagName(\'head\')[0].innerHTML;
						// 如果htmlStr中存在script标签，并且script标签中可能还有其他属性，则将script标签的innerHTML属性赋值给htmlStr
						if (htmlStr.indexOf(\'<script\') > -1) {
							let srcUrl = \'\';
							let htmlStrFormat = htmlStr.replace(/<script[^>]*>/gi, function (match) {
								// 获取script标签的src属性
								let url = match.match(/src=[\'"]([^\'"]+)[\'"]/i);
								// 如果src属性存在，则将script标签的innerHTML属性赋值给str
								if (url && url[1]) {
									srcUrl = url[1];
								}
								// 返回script标签中innerHTML属性的内容
								return match.match(/<script[^>]*>([\s\S]*?)/i)[1];
							});
							// 创建script标签
							var script = document.createElement(\'script\');
							// 设置script标签的innerHTML属性
							script.innerHTML = htmlStrFormat;
							// 设置script标签的src属性
							srcUrl&&(script.src = srcUrl);
							// 将script标签添加到head标签中
							document.getElementsByTagName(\'head\')[0].appendChild(script);
						}else{
							headHTML += htmlStr
							document.getElementsByTagName(\'head\')[0].innerHTML = headHTML;
						}
					</script>
				';
			}
		}else{

			if ($html) {
				$output .= '<div class="jwpf-addon jwpf-addon-raw-html ' . $class . '">';
				$output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
				$output .= '<div class="jwpf-addon-content">';
				$output .= $html;
				$output .= '</div>';
				$output .= '</div>';

			}
		}

		return $output;
		return;
	}

	public static function getTemplate()
	{
		$output = '
			<div class="jwpf-addon jwpf-addon-raw-html {{ data.class }}">
				<# if( !_.isEmpty( data.title ) ){ #><{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{{ data.title }}}</{{ data.heading_selector }}><# } #>
				<div class="jwpf-addon-content jw-inline-editable-element" data-id={{data.id}} data-fieldName="html" contenteditable="true">
					{{{ data.html }}}
				</div>
			</div>
		';

		return $output;
	}

}
