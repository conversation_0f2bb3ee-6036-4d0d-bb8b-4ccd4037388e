<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'timeline_swiper',
        'title' => JText::_('时间轴轮播'),
        'desc' => JText::_('时间轴轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'show_type' => array(
                    'type' => 'select',
                    'title' => JText::_('样式布局'),
                    'desc' => JText::_('样式布局'),
                    'values' => array(
                        'type1' => JText::_('布局一'),
                        'type2' => JText::_('布局二'),
                        'type3' => JText::_('布局三'),
                        'type4' => JText::_('布局四'),
                        'type5' => JText::_('布局五'),

                    ),
                    'std' => 'type1',
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'time'=>'2021',
                            'time_description'=>'请输入内容',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221011/2ee6712966e0e9f08ebe6a3490b86822.jpeg',
                            'text'=>''
                        ),
                        array(
                            'time'=>'2021',
                            'time_description'=>'请输入内容',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221011/2ee6712966e0e9f08ebe6a3490b86822.jpeg',
                            'text'=>''
                        ),
                        array(
                            'time'=>'2021',
                            'time_description'=>'请输入内容',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221011/2ee6712966e0e9f08ebe6a3490b86822.jpeg',
                            'text'=>''
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'time'=>array(
                            'type' => 'text',
                            'title' => '时间',
                            'desc' => '时间',
                            'std'=>'2021'
                        ),
                        'time_description'=>array(
                            'type' => 'text',
                            'title' => '时间描述',
                            'desc' => '时间描述',
                            'std'=>'时间描述'
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '链接地址',
                            'desc' => '链接地址',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                        'img'=> array(
                            'type' => 'media',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221011/2ee6712966e0e9f08ebe6a3490b86822.jpeg',
                        ),
                        'text' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std'=>''
                        ),
                    ),
                    'depends'=>array(
                        array('show_type','=','type1')
                    )
                ),

                'type_arrow_down_type1' => array(
                    'type' => 'media',
                    'title' => JText::_('下箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20221019/84533a0b852a5fe9fc6c3c94403976fc.png',
                    'depends' => array(
                        array('show_type','=','type1')
                    )
                ),

                'jw_image_carousel_item_type2' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title'=>'2021',
                            'time'=>'2021',
                            'time_dw'=>'年',
                            'text'=>''
                        ),
                        array(
                            'title'=>'2021',
                            'time'=>'2021',
                            'time_dw'=>'年',
                            'text'=>''
                        ),
                        array(
                            'title'=>'2021',
                            'time'=>'2021',
                            'time_dw'=>'年',
                            'text'=>''
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'time'=>array(
                            'type' => 'text',
                            'title' => '时间',
                            'desc' => '时间',
                            'std'=>'2021'
                        ),
                        'time_dw'=>array(
                            'type' => 'text',
                            'title' => '时间单位',
                            'desc' => '时间单位',
                            'std'=>'年'
                        ),
                        'text' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std'=>''
                        ),
                    ),
                    'depends'=>array(
                        array('show_type','=','type2')
                    )
                ),
                // 布局3
                'jw_image_carousel_item_type3' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title'=>'2018年10月',
                            'text'=>'专注于企业协同办公管理'
                        ),
                        array(
                            'title'=>'2019年10月',
                            'text'=>'推出企业自主建站管理'
                        ),
                        array(
                            'title'=>'2020年5月',
                            'text'=>'上线企业运营内容管理'
                        ),
                        array(
                            'title'=>'2020年10月',
                            'text'=>'推出语音智能办公助手'
                        ),
                        array(
                            'title'=>'2020年12月',
                            'text'=>'推出企业智能印章解决方案'
                        ),
                        array(
                            'title'=>'2021年3月',
                            'text'=>'推出智能合同智能财务'
                        ),
                        array(
                            'title'=>'2021年6月',
                            'text'=>'推出智能化、平台化、数字化的一站式管理平台'
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'text' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std'=>''
                        ),
                    ),
                    'depends'=>array(
                        array('show_type','=','type3')
                    )
                ),
                'type3_arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('字体样式'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '划过状态',
                            'value' => 'hover'
                        )
                    ),
                    'depends'=>array(
                        array('show_type','=','type3'),
                    )
                ),

                'type3_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => '16',
                    'min' => 12,
                    'max' => 50,
                    'depends' => array(
                        array('type3_arrow_style','=','normal'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_cont_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容字体大小'),
                    'std' => '12',
                    'min' => 12,
                    'max' => 50,
                    'depends' => array(
                        array('type3_arrow_style','=','normal'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('type3_arrow_style','=','normal'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_cont_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('type3_arrow_style','=','normal'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_title_fontsizehv' => array(
                    'type' => 'slider',
                    'title' => JText::_('划过标题字体大小'),
                    'std' => '18',
                    'depends' => array(
                        array('type3_arrow_style','=','hover'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_cont_fontsizehv' => array(
                    'type' => 'slider',
                    'title' => JText::_('划过内容字体大小'),
                    'std' => '14',
                    'depends' => array(
                        array('type3_arrow_style','=','hover'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_title_colorhv' => array(
                    'type' => 'color',
                    'title' => JText::_('划过标题字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('type3_arrow_style','=','hover'),
                        array('show_type','=','type3'),
                    )
                ),
                'type3_cont_colorhv' => array(
                    'type' => 'color',
                    'title' => JText::_('划过内容字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('type3_arrow_style','=','hover'),
                        array('show_type','=','type3'),
                    )
                ),
                'bgimg_type3'=> array(
                    'type' => 'media',
                    'title' => JText::_('背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220310/639e940017be8c9ca301c3832f415cb4.png',
                    'depends'=>array(
                        array('show_type','=','type3'),
                    ),
                ),

                'type3_item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一屏显示的个数'),
                    'std' => 7,
                    'max' => 10,
                    'depends'=>array(
                        array('show_type','=','type3'),
                    ),
                ),
                'type3_leftright_button' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启左右键'),
                    'desc' => JText::_('一屏显示的slides'),
                    'std' => 0,
                    'max' => 1000,
                    'depends'=>array(
                        array('show_type','=','type3'),
                    )
                ),
                'type3_jt_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮上下位置'),
                    'std' =>225 ,
                    'max' => 500,
                    'depends'=>array(
                        array('show_type','=','type3'),
                        array('type3_leftright_button','=',1),
                    ),
                ),
                'left_img_type3'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220310/ff0105994d6c3d6096d897a4c39aedb3.png',
                    'depends'=>array(
                        array('show_type','=','type3'),
                        array('type3_leftright_button','=',1),
                    ),
                ),
                'right_img_type3'=> array(
                    'type' => 'media',
                    'title' => JText::_('右侧按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220310/7b4f427b07a444ae7b707230c435a813.png',
                    'depends'=>array(
                        array('type3_leftright_button','=',1),
                        array('show_type','=','type3'),
                    ),
                ),
                //
                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_OPTIONS'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播元素样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends'=>array(
                        array('show_type','=','type1'),
                    )
                ),
                'carousel_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间轴宽度占比'),
                    'desc' => JText::_('时间轴宽度占比'),
                    'min' => 0,
                    'max' => 100,
                    'std' =>75,
                    'depends'=>array(
                        array('carousel_options','=','elements'),
                        array('show_type','=','type1'),
                    )
                ),
                'carousel_item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'std' => 4,
                    'depends'=>array(
                        array('carousel_options','=','elements'),
                        array('show_type','=','type1'),
                    )
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends'=>array(
                        array('carousel_options', '=', 'elements'),
                        array('show_type','=','type1'),
                    ),
                    'std' => 2500
                ),
                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => '控制器设置',
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('show_type','=','type1'),
                    ),
                ),

                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '禁用状态',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends'=>array(
                        array('carousel_options','=','elements'),
                        array('show_type','=','type1'),
                    )
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    ),
                    'std' => 34,
                ),
                'arrow_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    ),
                    'std' => 34,
                ),

                'arrow_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    )
                ),

                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#d51920',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    )
                ),
                'arrow_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框色'),
                    'std' => '#cecece',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => 16,
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                        array('show_type','=','type1'),
                    )
                ),
                //Arrow hover
                'arrow_hover_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#d01920',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                        array('show_type','=','type1'),
                    )
                ),
                'arrow_hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框色'),
                    'std' => '#d01920',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                        array('show_type','=','type1'),
                    )
                ),
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                        array('show_type','=','type1'),
                    )
                ),

                'item_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播元素样式'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '激活状态',
                            'value' => 'active'
                        )
                    ),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    )
                ),
                'time_setting' => array(
                    'type' => 'separator',
                    'title' => '时间区域设置',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'time_font_up' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间字号（上）'),
                    'std' => array('md'=>28,'sm'=>24,'xs'=>20),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                    'responsive'=>true
                ),
                'time_font_down' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间字号（下）'),
                    'std' => array('md'=>34,'sm'=>34,'xs'=>20),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                    'responsive'=>true
                ),
                'time_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('时间字体颜色'),
                    'std' => '#333',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                ),
                'time_title_font' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间标题字号'),
                    'std' => array('md'=>16,'sm'=>16,'xs'=>16),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                    'responsive'=>true
                ),
                'time_title_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('时间标题字体颜色'),
                    'std' => '#666',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                ),
                'timeline_color' => array(
                    'type' => 'color',
                    'title' => JText::_('时间轴颜色'),
                    'std' => '#e6e6e6',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal'),
                        array('show_type','=','type1'),
                    ),
                ),
                'time_active_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('时间字体颜色'),
                    'std' => '#d51920',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','active'),
                        array('show_type','=','type1'),
                    ),
                ),

                'time_dot_color' => array(
                    'type' => 'color',
                    'title' => JText::_('时间下方圆点颜色'),
                    'std' => '#d51920',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','active'),
                        array('show_type','=','type1'),
                    ),
                ),

                'content_setting' => array(
                    'type' => 'separator',
                    'title' => '内容区域设置',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容宽度占比'),
                    'std' => array('md'=>75,'sm'=>75,'xs'=>100),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                    'max'=>100,
                    'responsive'=>true
                ),
                'content_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容高度'),
                    'std' => 390,
                    'max'=> 1500,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                    'responsive'=>true
                ),
                'content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容边框色'),
                    'std' => '#eee',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_img_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容图片宽度占比'),
                    'std' => 40.86,
                    'max'=> 100,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容图片宽度占比（仅调整平板和移动端）'),
                    'std' => 30,
                    'max'=> 100,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_img_margin_right' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容图片右边距'),
                    'std' => 35,
                    'max'=> 500,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容内边距'),
                    'std' => '35px 30px 35px 30px',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_scroll_color' => array(
                    'type' => 'color',
                    'title' => JText::_('滚动条颜色'),
                    'std' => '#e2e2e2',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'content_scrollbar_color' => array(
                    'type' => 'color',
                    'title' => JText::_('滚动条拖拽部分颜色'),
                    'std' => '#c32026',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('show_type','=','type1'),
                    ),
                ),
                'time_font_color_type2' => array(
                    'type' => 'color',
                    'title' => JText::_('时间字体颜色'),
                    'std' => '#333',
                    'depends'=>array(
                        array('show_type','=','type2'),
                    ),
                ),
                'time_font_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间字体大小'),
                    'std' => 48,
                    'min'=> 10,
                    'max'=> 100,
                    'depends'=>array(
                        array('show_type','=','type2'),
                    ),
                ),
                'time_dw_font_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('时间单位字体大小'),
                    'std' => 16,
                    'min'=> 10,
                    'max'=> 100,
                    'depends'=>array(
                        array('show_type','=','type2'),
                    ),
                ),
                'time_infoW_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('中间内容区域宽度（单位：%）'),
                    'std' => 60,
                    'min'=> 10,
                    'max'=> 100,
                    'depends'=>array(
                        array('show_type','=','type2'),
                    ),
                ),
                'zuobiao_img_type2'=> array(
                    'type' => 'media',
                    'title' => JText::_('坐标图片'),
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan5.png',
                    'depends'=>array(
                        array('show_type','!=','type3'),
                        array('show_type','!=','type4'),
                        array('show_type','!=','type5'),

                    ),
                ),
                //布局四
                'pc_zyjj'=> array(
                    'type' => 'slider',
                    'title' => JText::_('左右内间距'),
                    'std' => '100',
                    'max' => '500',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'pcbj_img'=> array(
                    'type' => 'media',
                    'title' => JText::_('PC背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/7d25807010545e5d3b19b70dbe25501f.jpg',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'sjbj_img'=> array(
                    'type' => 'media',
                    'title' => JText::_('手机背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/f1ca3cef341601f0a70de33f96bc1774.jpg',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'right_bg'=> array(
                    'type' => 'color',
                    'title' => JText::_('右侧轮播背景色'),
                    'std' => 'rgba(0,51,38,0.5)',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left_img4'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧中间模块背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220725/0127b2b87bc6dab447a4eb0295029a40.jpg',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left_logo_img4'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧中间模块logo图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220725/a7df04956fd059d322a730a96e5aa62f.png',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left_bot_img4'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧中间模块标题下图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220725/b436eb94220ae6e0c9a629d0e08b44ca.png',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left_title4'=> array(
                    'type' => 'text',
                    'title' => JText::_('左侧中间模块标题1'),
                    'std' => '百年味道',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left2_title4'=> array(
                    'type' => 'text',
                    'title' => JText::_('左侧中间模块标题2'),
                    'std' => '百年传承',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),

                'left2_title4_color'=> array(
                    'type' => 'color',
                    'title' => JText::_('左侧中间模块标题颜色'),
                    'std' => '#ecd3a5',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'left2_title4_fontsize'=> array(
                    'type' => 'slider',
                    'title' => JText::_('左侧中间模块标题字体大小'),
                    'std' => '30',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),



                'jw_image_carousel_item04' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'title'=>'1906—2015',
                            'showUnit' => 1,
                            'unit' => '年',
                            'time_description'=>'百年历史马迭尔',
                            'img'=>'https://oss.lcweb01.cn/joomla/20220725/76019bd1d1c00eca8050f79d3c19edb0.jpg',
                            'adv_typeid'=>''
                        ),
                        array(
                            'title'=>'2016—至今',
                            'showUnit' => 1,
                            'unit' => '年',
                            'time_description'=>'新时代下的马迭尔',
                            'img'=>'https://oss.lcweb01.cn/joomla/20220725/3cd06e486adc56a2183c684f37659f1f.jpg',
                            'adv_typeid'=>''
                        ),

                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '时间',
                            'std' => '1906——2015',
                        ),
                        'showUnit' => array(
                            'type' => 'checkbox',
                            'title' => '是否显示单位',
                            'std' => 1,
                        ),
                        'unit' => array(
                            'type' => 'text',
                            'title' => '单位',
                            'std' => '年',
                            'depends'=>array(
                                array('showUnit','=',1),
                            ),
                        ),
                        'title_font' => array(
                            'type' => 'slider',
                            'title' => '时间字体大小',
                            'std' => '44',
                        ),
                        'title_color' => array(
                            'type' => 'color',
                            'title' => '时间字体颜色',
                            'std' => '#fff',
                        ),
                        'time_description'=>array(
                            'type' => 'text',
                            'title' => '描述',
                            'std'=>'百年历史马迭尔'
                        ),
                        'des_font' => array(
                            'type' => 'slider',
                            'title' => '描述字体大小',
                            'std' => '24',
                        ),
                        'des_color' => array(
                            'type' => 'color',
                            'title' => '描述字体颜色',
                            'std' => '#fff',
                        ),
                        'img' => array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'std' => 'https://oss.lcweb01.cn/joomla/20220725/76019bd1d1c00eca8050f79d3c19edb0.jpg',
                        ),
                        'adv_typeid' => array(
                            'type' => 'select',
                            'title' => JText::_('选择后台对应的广告分类'),
                            'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_advertise')['list'],
                        ),

                    ),
                    'depends'=>array(
                        array('show_type','=','type4')
                    )
                ),

                'right_title4_color'=> array(
                    'type' => 'color',
                    'title' => JText::_('右侧模块标题颜色'),
                    'std' => '#b3a36b',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'right_title4_fontsize'=> array(
                    'type' => 'slider',
                    'title' => JText::_('右侧模块标题字体大小'),
                    'std' => '72',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'right_intro4_color'=> array(
                    'type' => 'color',
                    'title' => JText::_('右侧内容字体颜色'),
                    'std' => '#fff',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'right_intro4_fontsize'=> array(
                    'type' => 'slider',
                    'title' => JText::_('右侧内容字体大小'),
                    'std' => '14',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),

                'left_img_type4'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/f482b27ddea8a6cfeac7e99f894e7c1e.png',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                'right_img_type4'=> array(
                    'type' => 'media',
                    'title' => JText::_('右侧按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/5579f1155efd2264f1b3a3f1f5d02e74.png',
                    'depends'=>array(
                        array('show_type','=','type4'),

                    ),
                ),
                'left_hvimg_type4'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧划过按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/d6121db286e35e70bc6d7cd663c64b07.png',
                    'depends'=>array(
                        array('show_type','=','type4'),

                    ),
                ),
                'right_hvimg_type4'=> array(
                    'type' => 'media',
                    'title' => JText::_('右侧划过按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220726/bb7dacf8db27f3230b9635eb56174c50.png',
                    'depends'=>array(
                        array('show_type','=','type4'),
                    ),
                ),
                //

                'left_img_type2'=> array(
                    'type' => 'media',
                    'title' => JText::_('左侧按钮图片'),
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan7.png',
                    'depends'=>array(
                        array('show_type','!=','type3'),
                        array('show_type','!=','type4'),
                        array('show_type','!=','type5'),

                    ),
                ),
                'right_img_type2'=> array(
                    'type' => 'media',
                    'title' => JText::_('右侧按钮图片'),
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/timeline_swiper/assets/images/guan8.png',
                    'depends'=>array(
                        array('show_type','!=','type3'),
                        array('show_type','!=','type4'),
                        array('show_type','!=','type5'),

                    ),
                ),

                //布局5
                'jw_image_carousel_item5' => array(
                    'title' => JText::_('事件推荐'),
                    'std' => array(
                        array(
                            'time'=>'2014',
                            'time_description'=>'广晟入资国星光电',
                            'img'=>'https://oss.lcweb01.cn/joomla/20230116/e70e78c900af849b6391acad094ec398.png',
                        ),
                        array(
                            'time'=>'2010',
                            'time_description'=>'公司首次公开发行股票并在深圳证交所挂牌上市',
                            'img'=>'https://oss.lcweb01.cn/joomla/20230116/099a8574648d20336d8994118058b3c5.png',
                        ),
                        array(
                            'time'=>'1999',
                            'time_description'=>'公司开始自主创新的道路',
                            'img'=>'https://oss.lcweb01.cn/joomla/20230116/137830ebdba82193afda3fd092fc37a1.jpg',
                        ),
                        array(
                            'time'=>'1976',
                            'time_description'=>'公司正式投产GaAsP LED红黄光芯片及Lamp LED器件封装，是国内最早生产LED的企业之一',
                            'img'=>'https://oss.lcweb01.cn/joomla/20230116/b5b02bdb03bdf548508ea01c77406587.jpg',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'time'=>array(
                            'type' => 'text',
                            'title' => '事件时间',
                            'std'=>'2014'
                        ),
                        'time_description'=>array(
                            'type' => 'text',
                            'title' => '事件描述',
                            'std'=>'广晟入资国星光电'
                        ),

                        'img'=> array(
                            'type' => 'media',
                            'title' => JText::_('事件图片'),
                            'std' => 'https://oss.lcweb01.cn/joomla/20230116/e70e78c900af849b6391acad094ec398.png',
                        ),
                    ),
                    'depends'=>array(
                        array('show_type','=','type5')
                    )
                ),
                'jw_lb_item5' => array(
                    'title' => JText::_('历程轮播(关联后台的公告分类)'),
                    'std' => array(
                        array(
                            'title'=>'产业链融合的高科技上市公司',
                            'time_id'=>'',
                        ),
                        array(
                            'title'=>'新世纪的民营自主创新型企业',
                            'time_id'=>'',
                        ),
                        array(
                            'title'=>'改革开放时代的代工型企业',
                            'time_id'=>'',
                        ),
                        array(
                            'title'=>'计划经济时代的国营工厂',
                            'time_id'=>'',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'std' => '产业链融合的高科技上市公司',
                        ),
                        'time_id'=>array(
                            'type' => 'select',
                            'title' => '选择关联的公告分类',
                            'values' => JwPageFactoryBase::getTypeList($site_id, $company_id,'com_notice')['list'],

                        ),


                    ),
                    'depends'=>array(
                        array('show_type','=','type5')
                    )
                ),
            ),
        ),
    )
);
