<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'general',
        'addon_name' => 'jw_button',
        'title' => JText::_('COM_JWPAGEFACTORY_ADDON_BUTTON'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_BUTTON_DESC'),
        'category' => '按钮',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),


                'show_bus' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启商桥'),
                    'desc' => JText::_(''),
                    'std' => 0,
                ),


                'text' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT_DESC'),
                    'std' => 'Button',
                ),
                'alignment' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT_DESC'),
                    'values' => array(
                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'jwpf-text-left',
                ),
                'font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-btn { font-family: "{{ VALUE }}"; }'
                    )
                ),
                'font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
                ),
                'letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0'
                ),
                // 'url' => array(
                // 	'type' => 'media',
                // 	'format' => 'attachment',
                // 	'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL'),
                // 	'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL_DESC'),
                // 	'placeholder' => 'http://',
                // 	'hide_preview' => true,
                // ),
                'tz_page_type' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(
                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                        'wx_links' => JText::_('微信'),
                        'dialog' => JText::_('不跳转页面，打开弹窗（请在预览页查看效果）'),
                    ),
                    'std' => 'Internal_pages',
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                ),

                'detail_page' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(array('tz_page_type', '=', 'external_links')),
                ),
                'target' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                    ),
                    'depends' => array(array('tz_page_type', '!=', 'dialog')),
                ),
                'type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
                    'values' => array(
                        'default' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
                        'primary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
                        'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
                        'success' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
                        'info' => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
                        'warning' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
                        'danger' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
                        'dark' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
                        'link' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
                    ),
                    'std' => 'default',
                ),
                'appearance' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
                        'gradient' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_GRADIENT'),
                        'outline' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
                        '3d' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
                    ),
                    'std' => '',
                    'depends' => array(
                        array('type', '!=', 'link'),
                    )
                ),
                'fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => array('md' => 16),
                    'responsive' => true,
                    'max' => 400,
                    'depends' => array(
                        array('type', '=', 'custom'),
                    )
                ),
                'button_status' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ENABLE_BACKGROUND_OPTIONS'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '划过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('type', '=', 'custom'),
                    )
                ),

                'open_bgimg' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('使用背景图'),
                    'std' => 0,
                    'depends' => array(
                        array('appearance', '!=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),
                    )
                ),
                'bgimg' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220707/40bd18d48b36f0680ad77999326161c4.png',
                    'depends' => array(
                        array('appearance', '!=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),
                        array('open_bgimg', '=', '1'),
                    )
                ),

                'background_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_DESC'),
                    'std' => '#03E16D',
                    'depends' => array(
                        array('appearance', '!=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),
                        array('open_bgimg', '!=', '1'),

                    )
                ),
                'background_gradient' => array(
                    'type' => 'gradient',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
                    'std' => array(
                        "color" => "#B4EC51",
                        "color2" => "#429321",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('appearance', '=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),
                    )
                ),

                'color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_DESC'),
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),
                    ),
                ),
                'wbk_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'normal'),

                    ),
                    'std' => ''
                ),

                'bgimg_hv' => array(
                    'type' => 'media',
                    'title' => JText::_('划过背景图'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220707/999b7a9498d320359df0df94a842a80c.png',
                    'depends' => array(
                        array('appearance', '!=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),
                        array('open_bgimg', '=', '1'),
                    )
                ),

                'background_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER_DESC'),
                    'std' => '#00E66E',
                    'depends' => array(
                        array('appearance', '!=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),
                        array('open_bgimg', '!=', '1'),

                    )
                ),
                'background_gradient_hover' => array(
                    'type' => 'gradient',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
                    'std' => array(
                        "color" => "#429321",
                        "color2" => "#B4EC51",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('appearance', '=', 'gradient'),
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),
                    )
                ),
                'color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER_DESC'),
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),
                    ),
                ),
                'wbk_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮悬停边框颜色'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),

                    ),
                    'std' => ''
                ),

                'donghua_hover' => array(
                    'type' => 'select',
                    'title' => JText::_('按钮悬停动画'),
                    'desc' => JText::_('按钮悬停动画'),
                    'values' => array(
                        'zhongxin' => JText::_('中心扩散'),
                    ),
                    'depends' => array(
                        array('type', '=', 'custom'),
                        array('button_status', '=', 'hover'),

                    ),
                    'std' => ''
                ),
                //Link Button Style
                'link_button_status' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '划过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('type', '=', 'link'),
                    )
                ),
                'link_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'normal'),
                    )
                ),
                'link_button_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_WIDTH'),
                    'max' => 30,
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'normal'),
                    )
                ),
                'link_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'normal'),
                    )
                ),
                'link_button_padding_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_PADDING_BOTTOM'),
                    'max' => 100,
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'normal'),
                    )
                ),
                //Link Hover
                'link_button_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER'),
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'hover'),
                    )
                ),
                'link_button_border_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR_HOVER'),
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'link'),
                        array('link_button_status', '=', 'hover'),
                    )
                ),
                'button_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING_DESC'),
                    'std' => '',
                    'depends' => array(
                        array('type', '=', 'custom'),
                    ),
                    'responsive' => true
                ),
                'size' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
                        'lg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
                        'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
                        'sm' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
                        'xs' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
                    ),
                ),
                'shape' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_DESC'),
                    'values' => array(
                        'rounded' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUNDED'),
                        'square' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_SQUARE'),
                        'round' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUND'),
                    ),
                    'depends' => array(
                        array('type', '!=', 'link'),
                    )
                ),
                'block' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
                    'values' => array(
                        '' => JText::_('JNO'),
                        'jwpf-btn-block' => JText::_('JYES'),
                    ),
                    'depends' => array(
                        array('type', '!=', 'link'),
                    )
                ),
                'icon' => array(
                    'type' => 'icon',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
                ),
                'icon_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_MARGIN'),
                    'responsive' => true,
                    'std' => '0px 0px 0px 0px',
                ),
                'icon_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
                    'values' => array(
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'left',
                    'depends'=>array(
                        array('kqanimg', '=', '0'),
                    )
                ),
                'kqanimg' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('使用按钮图片'),
                    'desc' => JText::_('是否使用按钮图片，注意：按钮图片和按钮图标只可设置一个'),
                    'std' => 0,
                ),
                'tbimg_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片宽度'),
                    'std' => 70,
                    'max' => 300,
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                    )
                ),
                'tbimg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'std' => 70,
                    'max' => 300,
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                    )
                ),
                'annie_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'std' => 264,
                    'max' => 1000,
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                    )
                ),
                'annie_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'std' => '',
                    'max' => 1000,
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                    )
                ),
                'img_button_status' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '划过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('kqanimg', '=', '1'),
                    )
                ),
                'tbimg' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片'),
                    'depends' => array(
                        array('kqanimg', '=', '1'),
                        array('img_button_status', '=', 'normal'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210810/7df62bf4215b64f1aea7f3ca2d8ac35a.png',
                ),
                'annie_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'std' => '#fff',
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                        array('img_button_status', '=', 'normal'),
                    )
                ),
                'tbimg_hover' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片'),
                    'depends' => array(
                        array('kqanimg', '=', '1'),
                        array('img_button_status', '=', 'hover'),
                    ),
                    'std' => '',
                ),
                'annie_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'std' => '',
                    'depends'=>array(
                        array('kqanimg', '=', '1'),
                        array('img_button_status', '=', 'hover'),
                    )
                ),

                // 点击按钮出弹窗
				'dialog_addon_id' => array(
					'title' => '弹窗插件ID（需要搭配弹窗插件使用）',
                    'desc' => '需要搭配弹窗插件使用',
					'type' => 'text',
					'std' => '',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog')
                    )
				),
                'dialog_settins' => array(
                    'type' => 'buttons',
                    'title' => JText::_('弹窗设置'),
                    'std' => 'content',
                    'values' => array(
                            array(
                                    'label' => '弹窗主体',
                                    'value' => 'content'
                            ),
                            array(
                                    'label' => '关闭按钮',
                                    'value' => 'close'
                            ),
                            array(
                                    'label' => '遮罩',
                                    'value' => 'mask'
                            ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_img' => array(
                    'type' => 'media',
                    'title' => JText::_('弹窗图片'),
                    'std' => 'https://s.cn.bing.net/th?id=OHR.PeakDistrictNP_ZH-CN1987784653_1920x1080.webp&qlt=50',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_img' => array(
                    'type' => 'media',
                    'title' => JText::_('关闭按钮图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/1619/image/20240220/68532274cd0b8cdbb379b31beda54959.png',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('关闭按钮宽度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '40',
                    'responsive' => true,
                    'max' => '500',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'close_position' => array(
                    'type' => 'margin',
                    'title' => JText::_('弹窗关闭按钮位置'),
                    'std' => '0 0 auto auto',
                    'responsive' => true,
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'close'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_level' => array(
                    'type' => 'text',
                    'title' => JText::_('弹窗层级'),
                    'std' => '99999999999999999999999999999',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗宽度'),
                    'std' => '600',
                    'responsive' => true,
                    'max' => '2000',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('弹窗高度'),
                    'std' => '400',
                    'responsive' => true,
                    'max' => '1000',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'dialog_img_fill' => array(
                    'type' => 'select',
                    'title' => JText::_('弹窗图片填充模式'),
                    'std' => 'cover',
                    'values' => (array(
                        'fill' => JText::_('填充拉伸'),
                        'contain' => JText::_('填充同时保留宽高比'),
                        'cover' => JText::_('超出裁剪')
                    )),
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'content'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'mask_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('遮罩背景色'),
                    'std' => 'rgba(0,0,0,0.5)',
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'mask'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                'mask_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('点击遮罩关闭弹窗'),
                    'std' => 1,
                    'depends' => array(
                        array('tz_page_type', '=', 'dialog'),
                        array('dialog_settins', '=', 'mask'),
                        array('dialog_addon_id', '=', '')
                    )
                ),
                // 点击按钮出弹窗结束


                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);
