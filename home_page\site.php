<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonHome_page extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;

        //设置首页
        $start_sy = (isset($settings->start_sy) && $settings->start_sy) ? $settings->start_sy : '1';
        $sy_text = (isset($settings->sy_text) && $settings->sy_text) ? $settings->sy_text : '设置首页';
        $sy_lineheight = (isset($settings->sy_lineheight) && $settings->sy_lineheight) ? $settings->sy_lineheight : '35';
        $syhover = (isset($settings->syhover) && $settings->syhover) ? $settings->syhover : 'normal';
        $sy_img = (isset($settings->sy_img) && $settings->sy_img) ? $settings->sy_img : 'https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png';
        $sy_title_size = (isset($settings->sy_title_size) && $settings->sy_title_size) ? $settings->sy_title_size : '12';
        $sy_color = (isset($settings->sy_color) && $settings->sy_color) ? $settings->sy_color : '#000';
        $sy_img_hover = (isset($settings->sy_img_hover) && $settings->sy_img_hover) ? $settings->sy_img_hover : 'https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png';
        $sy_title_sizehover = (isset($settings->sy_title_sizehover) && $settings->sy_title_sizehover) ? $settings->sy_title_sizehover : '12';
        $sy_color_hover = (isset($settings->sy_color_hover) && $settings->sy_color_hover) ? $settings->sy_color_hover : '#333';

        //加入收藏
        $start_sc = (isset($settings->start_sc) && $settings->start_sc) ? $settings->start_sc : '1';
        $sc_text = (isset($settings->sc_text) && $settings->sc_text) ? $settings->sc_text : '加入收藏';
        $sc_lineheight = (isset($settings->sc_lineheight) && $settings->sc_lineheight) ? $settings->sc_lineheight : '35';
        $schover = (isset($settings->schover) && $settings->schover) ? $settings->schover : 'normal';
        $sc_img = (isset($settings->sc_img) && $settings->sc_img) ? $settings->sc_img : 'https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png';
        $sc_title_size = (isset($settings->sc_title_size) && $settings->sc_title_size) ? $settings->sc_title_size : '12';
        $sc_color = (isset($settings->sc_color) && $settings->sc_color) ? $settings->sc_color : '#000';
        $sc_img_hover = (isset($settings->sc_img_hover) && $settings->sc_img_hover) ? $settings->sc_img_hover : 'https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png';
        $sc_title_sizehover = (isset($settings->sc_title_sizehover) && $settings->sc_title_sizehover) ? $settings->sc_title_sizehover : '12';
        $sc_color_hover = (isset($settings->sc_color_hover) && $settings->sc_color_hover) ? $settings->sc_color_hover : '#333';


        $addon_id = '#jwpf-addon-' . $this->addon->id;


        $output = '';

            $output .= "<style>
                {$addon_id} .fl{
                    float:left;
                }
                {$addon_id} .cl{
                    clear:both;
                }
                {$addon_id} .homepage{
                    line-height:{$sy_lineheight}px;
                    font-size:{$sy_title_size}px;
                    color:{$sy_color};
                    background:url({$sy_img })no-repeat center left;
                    padding-left:16px;
                    cursor:pointer;
                }

                {$addon_id} .homepage:hover{
                    font-size:{$sy_title_sizehover}px;
                    color:{$sy_color_hover};
                    background:url({$sy_img_hover})no-repeat center left;
                }

                {$addon_id} .joinsc{
                    line-height:{$sc_lineheight}px;
                    font-size:{$sc_title_size}px;
                    color:{$sc_color};
                    background:url({$sc_img})no-repeat center left;
                    padding-left:16px;
                    margin-left:10px;
                    cursor:pointer;
                }

                {$addon_id} .joinsc:hover{
                    font-size:{$sc_title_sizehover}px;
                    color:{$sc_color_hover};
                    background:url({$sc_img_hover})no-repeat center left;
                }

            </style>";
        
            $output .= '<div class="jwpf-home hysc">';
                if($start_sy == 1){ 
                    $output .= '<div class="homepage fl" onclick="SetHome(window.location)">'.$sy_text.'</div>';
                }
                if($start_sc == 1) { 
                    $output .= '<div class="joinsc fl" onclick="AddFavorite(window.location,document.title)">'.$sc_text.'</div>';
                }
                $output .= '<div class="cl"></div>';
            $output .= '</div>'; 

            $output .= '
                 <script type="text/javascript" language="javascript">
                    //加入收藏
                    function AddFavorite(sURL, sTitle) {
                        sURL = encodeURI(sURL);

                        try{  
                            window.external.addFavorite(sURL, sTitle);  
                        }catch(e) {  
                            try{  
                                window.sidebar.addPanel(sTitle, sURL, "");  
                            }catch (e) {  
                                alert("加入收藏失败，请使用Ctrl+D进行添加,或手动在浏览器里进行设置.");
                            }  
                        }
                    }
                 
                    //设为首页
                    function SetHome(){
                        var domain = document.domain;
                        var url="http://"+domain;
                        if (document.all) {
                            document.body.style.behavior="url(#default#homepage)";
                            document.body.setHomePage(url);
                        }else{
                            alert("您好,您的浏览器不支持自动设置页面为首页功能,请您手动在浏览器里设置该页面为首页!");
                        }
                    }
                </script>
            ';

        return $output;
    }


    public static function getTemplate()
    {

        $output = '

    		<#
        		/*设置首页*/
        		var start_sy = (!_.isEmpty(data.start_sy) && data.start_sy) ? data.start_sy : 1;
        		var sy_text = (!_.isEmpty(data.sy_text) && data.sy_text) ? data.sy_text : "设置首页";
        		var sy_lineheight = (!_.isEmpty(data.sy_lineheight) && data.sy_lineheight) ? data.sy_lineheight : "35";
        		var syhover = (!_.isEmpty(data.syhover) && data.syhover) ? data.syhover : "normal";
        		var sy_img = (!_.isEmpty(data.sy_img) && data.sy_img) ? data.sy_img : "https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png";
        		var sy_title_size = (!_.isEmpty(data.sy_title_size) && data.sy_title_size) ? data.sy_title_size : "12";
        		var sy_color = (!_.isEmpty(data.sy_color) && data.sy_color) ? data.sy_color : "#000";
        		var sy_img_hover = (!_.isEmpty(data.sy_img_hover) && data.sy_img_hover) ? data.sy_img_hover :"https://oss.lcweb01.cn/joomla/20211203/e5dc00571de1763c2c8dc5d4d10a8e3e.png";
                var sy_title_sizehover = (!_.isEmpty(data.sy_title_sizehover) && data.sy_title_sizehover) ? data.sy_title_sizehover : "12";
                var sy_color_hover = (!_.isEmpty(data.sy_color_hover) && data.sy_color_hover) ? data.sy_color_hover : "#333";
                
                /*收藏*/
                var start_sc = (!_.isEmpty(data.start_sc) && data.start_sc) ? data.start_sc : 1;
                var sc_text = (!_.isEmpty(data.sc_text) && data.sc_text) ? data.sc_text : "加入收藏";
                var sc_lineheight = (!_.isEmpty(data.sc_lineheight) && data.sc_lineheight) ? data.sc_lineheight : "35";
                var schover = (!_.isEmpty(data.schover) && data.schover) ? data.schover : "normal";
                var sc_img = (!_.isEmpty(data.sc_img) && data.sc_img) ? data.sc_img : "https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png";
                var sc_title_size = (!_.isEmpty(data.sc_title_size) && data.sc_title_size) ? data.sc_title_size : "12";
                var sc_color = (!_.isEmpty(data.sc_color) && data.sc_color) ? data.sc_color : "#000";
                var sc_img_hover = (!_.isEmpty(data.sc_img_hover) && data.sc_img_hover) ? data.sc_img_hover :"https://oss.lcweb01.cn/joomla/20211203/698f83bd8be7b3c226571436a9e66b89.png";
                var sc_title_sizehover = (!_.isEmpty(data.sc_title_sizehover) && data.sc_title_sizehover) ? data.sc_title_sizehover : "12";
                var sc_color_hover = (!_.isEmpty(data.sc_color_hover) && data.sc_color_hover) ? data.sc_color_hover : "#333";
    		
    		#>
    		<style>
    			
                #jwpf-addon-{{ data.id }} .fl{
                    float:left;
                }
                #jwpf-addon-{{ data.id }} .cl{
                    clear:both;
                }
                #jwpf-addon-{{ data.id }} .homepage{
                    line-height:{{ sy_lineheight }}px;
                    font-size:{{ sy_title_size }}px;
                    color:{{ sy_color }};
                    background:url({{ sy_img }})no-repeat center left;
                    padding-left:16px;
                }

                #jwpf-addon-{{ data.id }} .homepage:hover{
                    font-size:{{ sy_title_sizehover }}px;
                    color:{{ sy_color_hover }};
                    background:url({{ sy_img_hover }})no-repeat center left;
                }

                #jwpf-addon-{{ data.id }} .joinsc{
                    line-height:{{ sc_lineheight }}px;
                    font-size:{{ sc_title_size }}px;
                    color:{{ sc_color }};
                    background:url({{ sc_img }})no-repeat center left;
                    padding-left:16px;
                    margin-left:10px;
                }

                #jwpf-addon-{{ data.id }} .joinsc:hover{
                    font-size:{{ sc_title_sizehover }}px;
                    color:{{ sc_color_hover }};
                    background:url({{ sc_img_hover }})no-repeat center left;
                }

    		</style>


            <div class="jwpf-home hysc">
                <# if (data.start_sy == 1) { #>
                    <div class="homepage fl">
                        {{ sy_text }}
                    </div>
                <# } #>
                <# if (data.start_sc == 1) { #>
                    <div class="joinsc fl">
                        {{ sc_text }}
                    </div>
                <# } #>
                <div class="cl"></div>
            </div>

		';

        return $output;
    }

}