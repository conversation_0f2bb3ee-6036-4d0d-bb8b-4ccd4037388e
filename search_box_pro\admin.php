<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input      = $app->input;
$layout_id  = $input->get('layout_id', '');
$site_id    = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type'       => 'content',
		'addon_name' => 'search_box_pro',
		'title'      => JText::_('自定义搜索框'),
		'desc'       => JText::_('搜索框自定义版'),
		'category'   => '搜索',
		'attr'       => array(
			'general' => array(
				'search_type'          => array(
					'type'   => 'select',
					'title'  => '选择输入框类型',
					'desc'   => '',
					'values' => array(
						'type1' => '搜索结果',
						'type2' => '咨询',
					),
					'std'    => 'type1',
				),
				'search_style_type'          => array(
					'type'   => 'select',
					'title'  => '选择输入框样式布局',
					'desc'   => '',
					'values' => array(
						'type1' => '默认样式',
						'type2' => '布局2',
						'type3' => '布局3',
					),
					'std'    => 'type1',
					'depends' => array(
						array('search_type', '=', 'type1'),
					),
				),
				'detail_page_id'       => array(
					'type'    => 'select',
					'title'   => '选择搜索结果页',
					'desc'    => '',
					'depends' => array('search_type' => 'type1'),
					'values'  => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'is_text_button'       => array(
					'type'  => 'checkbox',
					'title' => JText::_('是否文字按钮(YES是文字按钮,NO图片按钮)'),
					'desc'  => JText::_('YES是文字按钮,NO图片按钮'),
					'std'   => 1,
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_button_gb_img' => array(
					'type'    => 'media',
					'title'   => JText::_('按钮背景图'),
					'desc'    => JText::_(''),
					'format'  => 'image',
					'std'     => 'components/com_jwpagefactory/addons/search_box_pro/assets/images/bg.png',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_text_button', '=', 0),
					),
				),
				'search_button_img' => array(
					'type'    => 'media',
					'title'   => JText::_('按钮图标'),
					'format'  => 'image',
					'std'     => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_button_text'   => array(
					'type'    => 'text',
					'title'   => JText::_('按钮文本'),
					'desc'    => JText::_(''),
					'std'     => '确定',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_text_button', '=', 1),
					),
				),
				'search_input_text'    => array(
					'type'  => 'text',
					'title' => JText::_('输入框提示文本内容'),
					'desc'  => JText::_(''),
					'std'   => '填写搜索的内容',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'is_input_img'       => array(
					'type'  => 'checkbox',
					'title' => JText::_('是否添加输入框图标'),
					'desc'  => JText::_('YES是,NO否'),
					'std'   => 0,
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_input_gb_img' => array(
					'type'    => 'media',
					'title'   => JText::_('输入框图标'),
					'desc'    => JText::_(''),
					'format'  => 'image',
					'std'     => 'components/com_jwpagefactory/addons/search_box_pro/assets/images/input_icon.png',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_input_img', '=', 1),
					),
				),
				'input_padding_left'          => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框左内边距'),
					'placeholder' => '10',
					'max'         => 1200,
					'min'         => 0,
					'std'         => '10',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_input_width'          => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框图标长度'),
					'placeholder' => '100',
					'max'         => 1200,
					'min'         => 0,
					'std'         => '30',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_input_img', '=', 1),
					),
				),
				'search_input_height'         => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框图标高度'),
					'placeholder' => '10',
					'max'         => 100,
					'min'         => 0,
					'std'         => '36',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_input_img', '=', 1),
					),
				),
				'search_input_position_width'          => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框图标左右'),
					'placeholder' => '100',
					'max'         => 1200,
					'min'         => -1200,
					'std'         => '0',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_input_img', '=', 1),
					),
				),
				'search_input_position_height'         => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框图标上下'),
					'placeholder' => '10',
					'max'         => 1200,
					'min'         => -1200,
					'std'         => '0',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
						array('is_input_img', '=', 1),
					),
				),
				'input_bg'             => array(
					'type'  => 'color',
					'title' => JText::_('输入框背景颜色'),
					'std'   => '#ccc',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_color_pla'      => array(
					'type'  => 'color',
					'title' => JText::_('输入框提示文本字体颜色'),
					'std'   => '#bbbbbb',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_color'          => array(
					'type'  => 'color',
					'title' => JText::_('输入框输入字体颜色'),
					'std'   => '#bbbbbb',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_font_size'          => array(
					'type'        => 'slider',
					'title'       => JText::_('输入框输入字体大小'),
					'placeholder' => 14,
					'max'         => 100,
					'min'         => 0,
					'std'         => 14,
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_font_size_hla'         => array(
					'type'        => 'slider',
					'title'       => JText::_('输入框提示文本字体大小'),
					'placeholder' => 14,
					'max'         => 100,
					'min'         => 10,
					'std'         => 14,
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_wz'            => array(
					'type'   => 'select',
					'title'  => '搜索框位置',
					'values' => array(
						'flex-start' => '左',
						'center'     => '中',
						'flex-end'   => '右',
					),
					'std'    => 'flex-start',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'border_color'         => array(
					'type'  => 'color',
					'title' => JText::_('搜索框边框颜色'),
					'std'   => '#000000',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_color'         => array(
					'type'  => 'color',
					'title' => JText::_('按钮颜色'),
					'std'   => '#eeeeee',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_font_color'    => array(
					'type'  => 'color',
					'title' => JText::_('按钮字体颜色'),
					'std'   => '#000000',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_width'         => array(
					'type'        => 'slider',
					'title'       => JText::_('按钮长度'),
					'placeholder' => '100',
					'max'         => 300,
					'min'         => 0,
					'std'         => '60',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_height'         => array(
					'type'        => 'slider',
					'title'       => JText::_('按钮高度'),
					'placeholder' => '100',
					'max'         => 300,
					'min'         => 0,
					'std'         => '36',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_border_color'  => array(
					'type'  => 'color',
					'title' => JText::_('按钮边框颜色'),
					'std'   => '#000000',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_width'          => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框长度'),
					'placeholder' => '100',
					'max'         => 1200,
					'min'         => 0,
					'std'         => '150',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'input_height'         => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框高度'),
					'placeholder' => '10',
					'max'         => 100,
					'min'         => 0,
					'std'         => '36',
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'button_border_radius' => array(
					'type'        => 'slider',
					'title'       => JText::_('按钮圆角'),
					'placeholder' => '10',
					'max'         => 20,
					'min'         => 0,
					'std'         => '2',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_border_radius' => array(
					'type'        => 'slider',
					'title'       => JText::_('搜索框圆角'),
					'placeholder' => '10',
					'max'         => 20,
					'min'         => 0,
					'std'         => 40,
					'depends' => array(
						array('search_style_type', '!=', 'type3'),
					),
				),
				'search_button_num'    => array(
					'type'        => 'slider',
					'title'       => JText::_('间距'),
					'placeholder' => '10',
					'max'         => 50,
					'min'         => 0,
					'std'         => '0',
					'depends' => array(
						array('search_style_type', '!=', 'type2'),
						array('search_style_type', '!=', 'type3'),
					),
				),
				/* 布局3 配置项 */
				'type04_part01' => array(
					'type' => 'separator',
					'title' => '整体配置',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					),
				),
				'search_target04' => array(
					'type' => 'select',
					'title' => '搜索结果页打开方式',
					'std' => '_self',
					'values' => array(
						'_self' => '当前页面',
						'_blank' => '新窗口',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'search_align04' => array(
					'type' => 'select',
					'title' => '搜索框位置',
					'values' => array(
						'flex-start' => '左',
						'center' => '中',
						'flex-end' => '右',
					),
					'std' => 'flex-start',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					),
				),
				'search_width04' => array(
					'type' => 'slider',
					'title' => '整体宽度',
					'max' => 1000,
					'min' => 0,
					'std' => array(
						'md' => 458,
						'sm' => '',
						'sx' => ''
					),
					'responsive' => true,
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					),
				),
				'search_height04' => array(
					'type' => 'slider',
					'title' => '整体高度',
					'max' => 1000,
					'min' => 0,
					'std' => array(
						'md' => 40,
						'sm' => '',
						'sx' => ''
					),
					'responsive' => true,
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					),
				),
				'search_bgColor04' => array(
					'type' => 'color',
					'title' => '背景颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'search_border_width04' => array(
					'type' => 'slider',
					'title' => '边框宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 1,
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'search_border_color04' => array(
					'type' => 'color',
					'title' => '边框颜色',
					'std' => '#588AAD',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'search_border_style04' => array(
					'type' => 'select',
					'title' => '边框样式',
					'values' => array(
						'none' => '无边框',
						'solid' => '实线',
						'dashed' => '虚线',
						'dotted' => '点状边框',
						'double' => '双线',
						'groove' => '3D 凹槽边框',
						'ridge' => '3D 垄状边框',
						'inset' => '3D inset 边框',
						'outset' => '3D outset 边框',
					),
					'std' => 'solid',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'search_border_radius04' => array(
					'type' => 'margin',
					'title' => '边框圆角',
					'max' => 1000,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => '1px',
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					)
				),
				'type04_part02' => array(
					'type' => 'separator',
					'title' => '局部配置',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
					),
				),
				'search_style04' => array(
					'type' => 'buttons',
					'title' => '搜索样式配置',
					'std' => 'icon_s',
					'values' => array(
						array(
							'label' => '图标',
							'value' => 'icon_s'
						),
						array(
							'label' => '搜索框',
							'value' => 'input_s'
						),
						array(
							'label' => '搜索按钮',
							'value' => 'button_s'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('search_style_type', '=', 'type3')
					),
				),
				'is_search_icon04' => array(
					'type' => 'checkbox',
					'title' => '显示左侧图标',
					'std' => 1,
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
					),
				),
				'search_icon04' => array(
					'type' => 'media',
					'title' => '左侧图标',
					'std' => 'https://oss.lcweb01.cn/joomla/20230515/6463e2733c5489861bcd6e9c987f8ef1.png',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
						array('is_search_icon04', '=', '1'),
					),
				),
				'icon_width04' => array(
					'type' => 'slider',
					'title' => '左侧图标部分宽度',
					'max' => 1000,
					'min' => 0,
					'std' => array(
						'md' => 50,
						'sm' => '',
						'sx' => ''
					),
					'responsive' => true,
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
						array('is_search_icon04', '=', '1'),
					),
				),
				'icon_height04' => array(
					'type' => 'slider',
					'title' => '左侧图标部分高度',
					'max' => 1000,
					'min' => 0,
					'std' => array(
						'md' => 20,
						'sm' => '',
						'sx' => ''
					),
					'responsive' => true,
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
						array('is_search_icon04', '=', '1'),
					),
				),
				'icon_border_width04' => array(
					'type' => 'slider',
					'title' => '竖线宽度（高度与左侧部分高度一致）',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 1,
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
						array('is_search_icon04', '=', '1'),
					)
				),
				'icon_border_color04' => array(
					'type' => 'color',
					'title' => '竖线颜色',
					'std' => '#D0D0D0',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'icon_s'),
						array('is_search_icon04', '=', '1'),
					)
				),
				'input_text04' => array(
					'type' => 'text',
					'title' => '输入框提示文本内容',
					'std' => '填写搜索的内容',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'input_s'),
					),
				),
				'input_fontsize04' => array(
					'type' => 'slider',
					'title' => '输入框文字大小',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'input_s'),
					)
				),
				'input_color04' => array(
					'type' => 'color',
					'title' => '输入框文字颜色',
					'std' => '#202020',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'input_s'),
					)
				),
				'button_text04' => array(
					'type' => 'text',
					'title' => '搜索按钮文字',
					'std' => '搜索',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'button_s'),
					),
				),
				'button_width04' => array(
					'type' => 'slider',
					'title' => '按钮宽度',
					'max' => 1000,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 80,
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'button_s'),
					)
				),
				'button_bgColor04' => array(
					'type' => 'color',
					'title' => '按钮背景颜色',
					'std' => '#0B5EA0',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'button_s'),
					)
				),
				'button_color04' => array(
					'type' => 'color',
					'title' => '按钮文字颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'button_s'),
					)
				),
				'button_fontsize04' => array(
					'type' => 'slider',
					'title' => '按钮文字大小',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => '',
					),
					'depends' => array(
						array('search_style_type', '=', 'type3'),
						array('search_style04', '=', 'button_s'),
					)
				),
			),
		),
	)
);
