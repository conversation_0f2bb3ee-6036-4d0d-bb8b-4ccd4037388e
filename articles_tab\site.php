<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonArticles_tab extends JwpagefactoryAddons
{

    public static $this_obj;

    public function __construct($addon)
    {
        parent::__construct($addon);
        self::$this_obj = $this;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        $cpcatid = $_GET['cpcatid'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $settings = $this->addon->settings;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;

        $title_text_color = (isset($settings->title_text_color) && $settings->title_text_color) ? $settings->title_text_color : '#ffffff';
        $title_text_color_z = (isset($settings->title_text_color_z) && $settings->title_text_color_z) ? $settings->title_text_color_z : '#ffffff';
        $title_bg_color = (isset($settings->title_bg_color) && $settings->title_bg_color) ? $settings->title_bg_color : '#4e4c4c';
        $title_bg_color_z = (isset($settings->title_bg_color_z) && $settings->title_bg_color_z) ? $settings->title_bg_color_z : '#0056ff';
        $title_width = (isset($settings->title_width) && $settings->title_width) ? $settings->title_width : 125;
        $title_height = (isset($settings->title_height) && $settings->title_height) ? $settings->title_height : 35;
        $title_jj = (isset($settings->title_jj) && $settings->title_jj) ? $settings->title_jj : '';
        $content_jj = (isset($settings->content_jj) && $settings->content_jj) ? $settings->content_jj : '';
        $title_border = (isset($settings->title_border) && $settings->title_border) ? $settings->title_border : '';
        $title_size = (isset($settings->title_size) && $settings->title_size) ? $settings->title_size : '';

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        $items = JwpagefactoryHelperArticles::getArticlesList(50, $ordering, $catid, 1, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        // $items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $catid, 1, $company_id, $site_id, $post_type, $tagids);
        if (!count($items)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            return $output;
        }

        $output = '
            <style>
            @media (min-width: 768px) {
                '.$addon_id.' .div-nav{display: flex;} 
                '.$addon_id.' .nav ul{width: '.$title_width.'px;zoom:1;padding-left: 0px;} 
                '.$addon_id.' .nav ul li{float: left;list-style:none;margin-top:'.$title_jj.'px;width: '.$title_width.'px;height: '.$title_height.'px;border-radius: '.$title_border.'px;} 
                '.$addon_id.' .nav ul li:last-child{border: none;} 
                '.$addon_id.' .nav ul li a{display: block; width: 100%;height: 100%;line-height: '.$title_height.'px;background: '.$title_bg_color.';text-align: center;color: '.$title_text_color.';border-radius: '.$title_border.'px;font-size:'.$title_size.'px;} 
                '.$addon_id.' .nav ul li.act a{background: '.$title_bg_color_z.';color: '.$title_text_color_z.';}
                '.$addon_id.' .tabBox{margin-left:'.$content_jj.'px;}
            }
            @media (max-width: 768px) {
                '.$addon_id.' .nav ul{    
                    width: 100%;
                    overflow: hidden;
                    zoom: 1;
                    display: flex;
                    flex-wrap: wrap;
                    flex: 1;
                    justify-content: space-between;
                    padding:0;
                } 
                '.$addon_id.' .nav ul li{
                    border-right: 1px solid #717070;
                    width: 31%;
                    list-style:none;
                    background: #4e4c4c;
                    overflow: hidden;
                    border-radius: 25px;
                    margin-top:10px;
                } 
                '.$addon_id.' .nav ul li:last-child{border: none;} 
                '.$addon_id.' .nav ul li a{    
                    padding: 5px 5px 5px 5px;
                    display: block;
                    width: 100%;
                    height: 100%;
                    background: '.$title_bg_color.';
                    text-align: center;
                    color: '.$title_text_color.';
                    overflow: auto;
                } 
                '.$addon_id.' .nav ul li.act a{background: '.$title_bg_color_z.';color: '.$title_text_color_z.';}
                '.$addon_id.' .tabBox{margin-left:0px;}
            }
            </style>
            <div class="div-nav">
                <div class="nav">
                    <ul >
        ';
        foreach($items as $k => $v)
        {
            if($k==0)
            {
                $output.='<li class="act"><a href="javascript:;">'.$v->title.'</a></li>';
            }
            else
            {
                $output.='<li><a href="javascript:;">'.$v->title.'</a></li>';
            }
        }
        $output.='
                    </ul>
                </div>
                <div class="tabBox">
                ';
        foreach($items as $k => $v)
        {
            $output.='<div class="box">'.$v->fulltext.'</div>';
        }
        $output.='
                </div>
            </div>

            <!-- <script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js" type="text/javascript" charset="utf-8"></script> -->
            <script type="text/javascript">
                $("'.$addon_id.' .box").hide();
                $("'.$addon_id.' .box:eq(0)").show();
                $("'.$addon_id.' .nav ul li").click(function(){
                    $(this).addClass(\'act\').siblings(\'li\').removeClass(\'act\');
                    console.log($(this).index())
                    var i = $(this).index();
                        $("'.$addon_id.' .box").hide();
                        $("'.$addon_id.' .box:eq("+i+")").show();
                })

            </script>
        ';

        return $output;
    }

    function css()
    {
    }

    

}