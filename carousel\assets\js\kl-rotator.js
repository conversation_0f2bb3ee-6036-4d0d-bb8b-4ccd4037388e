kingslandLinassi = {};

kingslandLinassi.Rotator = function() {

	var currentRotatorIndex, settings, imageElements, imageElementCount, cancelId, isTransition;

	$ = jQuery;

	var initialise = function(options){

		settings = $.extend({
			rotatorId: 'rotator',
			classKey: 'rotator-image',
			bulletId: 'rotator-bullets',
			debugId: '',
			captionFade: 1000,
			labelFade: 1000,
			enabled: true,
			transitionDelay: 6000,
			fadeDelay: 1500,
			initialiseScale: 120, 		// used to increase or decrease the first transition delay.
			useMobileImage: false
		},options);

		currentRotatorIndex = 0;

		imageElements = $("#" + settings.rotatorId + " ." + settings.classKey);
		imageElementCount = imageElements.length;

		if (settings.debugId == "featured") {
			currentRotatorIndex = Math.floor((Math.random() * imageElementCount));
			settings.transitionDelay = 360000;
			settings.initialiseScale = 0;
		}

		cancelId = [];
	
		if (imageElementCount == 1) {
			// $("#" + settings.rotatorId + " .rotatorLabel").hide();
			$("#" + settings.rotatorId + " .rotatorPrev").hide();
			$("#" + settings.rotatorId + " .rotatorNext").hide();
		};

		var bullets = document.createElement("ul");

		for (i = 0 ; i < imageElementCount; i++) {
			var li = document.createElement("li");
			$(li).data('index',(i + 1));
			bullets.appendChild(li);		
		}
	
		$("#" + settings.bulletId).html(bullets);

		$("#" + settings.bulletId + " ul li").on("click",function(){
			
			if (!isTransition) {

				var clickedIndex = $(this).data('index');
		
				if (clickedIndex !== currentRotatorIndex) {
					if (cancelId.length > 0) {

						for(i=0; i < cancelId.length; i++) {
							window.clearTimeout(cancelId[i]);
						}
						cancelId = [];

						currentRotatorIndex = clickedIndex - 1;

						if(settings.enabled) {
							cancelId.push(transitionRotator(true));
						}
					}
				}
			}
		});

	    $("#" + settings.rotatorId + " .rotatorPrev").on("click",function(){

			if (!isTransition) {

				if (cancelId.length > 0) {

					for(i=0; i < cancelId.length; i++) {
						window.clearTimeout(cancelId[i]);
					}
					cancelId = [];

					prevIndex = currentRotatorIndex - 2;
					if (currentRotatorIndex == 0) { prevIndex = imageElementCount - 2 }; // We're on the last image, the index is for the first image
					if (currentRotatorIndex == 1) { prevIndex = imageElementCount - 1 }; // We're on the first image - set previous = last image

					if ( prevIndex < 0 ) { prevIndex = (imageElementCount - 1) };
					currentRotatorIndex = prevIndex;

					if(settings.enabled) {
						cancelId.push(transitionRotator(true));
					}
				}
			}

	    });

	    $("#" + settings.rotatorId + " .rotatorNext").on("click",function(){

			if (!isTransition) {

				if (cancelId.length > 0) {

					for(i=0; i < cancelId.length; i++) {
						window.clearTimeout(cancelId[i]);
					}
					cancelId = [];

					nextIndex = currentRotatorIndex;
					currentRotatorIndex = nextIndex;

					if(settings.enabled) {
						cancelId.push(transitionRotator(true));
					}
				}
			}

	    });


		if (settings.enabled) {
			cancelId.push(transitionRotator(true));
		}

	};

	var transitionRotator = function(initialise) {
		var transitionDelay = initialise ? settings.transitionDelay / settings.initialiseScale : settings.transitionDelay;
		getNextImage($(imageElements[currentRotatorIndex]));

		var cancelToken = setTimeout(function(){

			isTransition = true;

			var fadeOut = (function(){
				$(imageElements).fadeOut(settings.fadeDelay * 2);
			})();

			// if (imageElementCount >= 2  || settings.debugId == "full") {
				processCaptionText($(imageElements[currentRotatorIndex]),settings.captionFade);
				processLabelText($(imageElements[currentRotatorIndex]),settings.labelFade);
			// }

			var fadeIn = (function() { 
				$(imageElements[currentRotatorIndex]).fadeIn({ 
					duration: settings.fadeDelay,
					start: function() {

						$("#" + settings.bulletId + " ul li").removeClass("selected");
						var selected = "#" + settings.bulletId + " ul li:eq(" + (currentRotatorIndex) + ")";
						$(selected).addClass("selected");
					},
					complete: function() {

						// Set styles to bold / clear styles on other labels.
						$("#" + settings.rotatorId + " .third-label .lblContent").removeClass("on");
						$("#" + settings.rotatorId + " .third-label:nth-child(" + (currentRotatorIndex+1) + ") .lblContent").addClass("on");
						// Move third-bar to left * slide number.
						$("#" + settings.rotatorId + " .third-bar").css("left", (33.3 * currentRotatorIndex) + "%");

						var removeCancelToken = jQuery.inArray(cancelToken,cancelId)
						if (removeCancelToken > 0) {
							cancelId.splice(removeCancelToken,1);
						}
						isTransition = false;
						if (imageElementCount > 1) {
							getNextRotator();
						}

					}
				});
			})();
		}, transitionDelay);

		return cancelToken;
	};

	var processCaptionText = function(rotator, captionFade) {
		var captionText = $(rotator).data("captionText");

		$("#" + settings.rotatorId + " .rotatorText").fadeOut( {
			duration: captionFade,
			done: function() {
				if (captionText != "") {
					$("#" + settings.rotatorId + " .rotatorText").html('<span>' + captionText +'</span>');	
				}
				else {
					$("#" + settings.rotatorId + " .rotatorText").html('');
				}
				$("#" + settings.rotatorId + " .rotatorText").fadeIn(captionFade);
			}
		});
		if (settings.debugId == "home" || settings.debugId == "full") {
			$("#" + settings.rotatorId + " .rotatorText").delay(3000).fadeOut(1500);
		}
	}

	var processLabelText = function(rotator, labelFade) {
		var labelText = $(rotator).data("labelText");

		$("#" + settings.rotatorId + " .rotatorLabel").fadeOut( {
			duration: labelFade,
			done: function() {
				if (labelText != "") {
					$("#" + settings.rotatorId + " .rotatorLabel").html('<span>' + labelText +'</span>');
				}
				else {
					$("#" + settings.rotatorId + " .rotatorLabel").html('');
				}				
				$("#" + settings.rotatorId + " .rotatorLabel").html('<span>' + labelText +'</span>');
				$("#" + settings.rotatorId + " .rotatorLabel").fadeIn(labelFade);
			}
		});
	}

	var getNextRotator = function() {
		// get current index of array 

		switch (currentRotatorIndex) {
			case -1:
				alert("Critcal Error, cannot find rotator.");
				break;
			default:
				if (currentRotatorIndex < (imageElementCount -1)) {
					currentRotatorIndex++;
				} else {
					currentRotatorIndex = 0;
				}
				cancelId.push(transitionRotator(false));
				break;
		}
	}

	var getNextImage = function (rotator) {

		if (typeof(rotator) === "undefined") {
			//console.log("error locating data settings");
			return;
		}

		var backgroundImage = !settings.useMobileImage ? $(rotator).data("image") : $(rotator).data("mobileImage");
		var currentBackground = $(rotator).css('background-image');

		if (currentBackground.indexOf(backgroundImage) === -1) {
			$(rotator).css('background-image','url(' + backgroundImage +')').attr('title', $(rotator).data('caption'));
		} else {

		}
		
	}

	// exports
	return  {
		initialise: initialise,
		diag: function(){
			//console.dir(settings.data);
		}
	};

}


kingslandLinassi.TextRotator = function() {

	var currentRotatorIndex, settings, cancelId, isTransition;

	$ = jQuery;

	var initialise = function(options){

		settings = $.extend({
			rotatorId: 'text-rotator',
			classKey: 'banner-text',
			enabled: true,
			transitionDelay: 9000,
			fadeDelay: 1000,
			initialiseScale: 120 		// used to increase or decrease the first transition delay.
		},options);

		//Count the elements
		tElements = $("#" + settings.rotatorId + " ." + settings.classKey);
		tElementCount = tElements.length;

		//Randomise first display element
		currentRotatorIndex = Math.floor((Math.random() * tElementCount));

		//Set up rotator and start automatically
		cancelId = [];
		if (settings.enabled) {
			cancelId.push(transitionRotator(true));
		}

	};

	var transitionRotator = function(initialise) {

		var transitionDelay = initialise ? settings.transitionDelay / settings.initialiseScale : settings.transitionDelay;

		var cancelToken = setTimeout(function(){

			isTransition = true;

			var fadeIn = (function() {
				$("#" + settings.rotatorId + " ." + settings.classKey).fadeOut({ duration: settings.fadeDelay });
				$(tElements[currentRotatorIndex]).fadeIn({ 
					duration: settings.fadeDelay,
					start: function() {
					},
					complete: function() {

						var removeCancelToken = jQuery.inArray(cancelToken,cancelId)
						if (removeCancelToken > 0) {
							cancelId.splice(removeCancelToken,1);
						}
						isTransition = false;
						if (tElementCount > 1) {
							getNextRotator();
						}

					}
				});
			})();
		}, transitionDelay);

		return cancelToken;
	};

	var getNextRotator = function() {
		// get current index of array 

		switch (currentRotatorIndex) {
			case -1:
				alert("Critcal Error, cannot find rotator.");
				break;
			default:
				if (currentRotatorIndex < (tElementCount -1)) {
					currentRotatorIndex++;
				} else {
					currentRotatorIndex = 0;
				}
				cancelId.push(transitionRotator(false));
				break;
		}
	}

	// exports
	return  {
		initialise: initialise,
		diag: function(){
			//console.dir(settings.data);
		}
	};

}