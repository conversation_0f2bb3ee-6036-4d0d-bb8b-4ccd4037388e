<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'service_number',
        'title' => JText::_('服务数量'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '龙采官网插件',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'number_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => 'pc布局1',
                        'type2' => '手机端布局1',
                        'type3' => '布局2',
                        'type4' => '布局3',
                        'type5' => '布局5',
                        'type6' => '布局6',

                    ),
                    'std' => 'type1',
                ),
                // Repeatable Item
                'jw_tab_item_one' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('数字底部标题(换行用<br/>)'),
                            'desc' => JText::_('数字底部标题(换行用<br/>)'),
                            'std' => '标题',
                        ),
                        'number' => array(
                            'type' => 'text',
                            'title' => JText::_('数字内容'),
                            'desc' => JText::_('数字内容'),
                            'std' => '1000',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '年技术沉淀',
                            'number' => '17',
                        ),
                        array(
                            'title' => '家分公司',
                            'number' => '73',
                        ),
                        array(
                            'title' => '1000余员工的技术团队',
                            'number' => '1000+',
                        ),
                        array(
                            'title' => '140000余家企业服务经验',
                            'number' => '140000+',
                        ),
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type1'),
                    ),
                ),
                // Repeatable Item
                'jw_tab_item_two' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('数字底部标题(换行用<br/>)'),
                            'desc' => JText::_('数字底部标题(换行用<br/>)'),
                            'std' => '标题',
                        ),
                        'number_img' => array(
                            'type' => 'media',
                            'title' => JText::_('数字图片'),
                            'desc' => JText::_('数字图片'),
                            'std' => '',
                        ),
                        'bigtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('数量上方标题(换行用<br/>)'),
                            'desc' => JText::_('数量上方标题(换行用<br/>)'),
                            'std' => '',
                        ),
                        'bigdesc' => array(
                            'type' => 'text',
                            'title' => JText::_('数量上方简介(换行用<br/>)'),
                            'desc' => JText::_('数量上方简介(换行用<br/>)'),
                            'std' => '',
                        ),
                        'button' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮文字内容(换行用<br/>)'),
                            'desc' => JText::_('按钮文字内容(换行用<br/>)'),
                            'std' => '',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                                'wx_links' => JText::_('微信'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '年技术沉淀',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/d73c54f3ddafc22fe186adb74b05c5ac.png',
                            'bigtitle' => '企业互联网战略<br/>服务品牌',
                            'bigdesc' => '我们为企业提供高品质互联网<br/>营销整合服务',
                            'button' => '进一步了解',
                        ),
                        array(
                            'title' => '家分公司',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/5a854dae52a59a54290ec4226b09930c.png',
                            'bigtitle' => '企业互联网战略<br/>服务品牌',
                            'bigdesc' => '我们为企业提供高品质互联网<br/>营销整合服务',
                            'button' => '进一步了解',
                        ),
                        array(
                            'title' => '余员工的技术团队',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/d0a1f2fb951e4fa9247ab69f9baaf3ef.png',
                            'bigtitle' => '企业互联网战略<br/>服务品牌',
                            'bigdesc' => '我们为企业提供高品质互联网<br/>营销整合服务',
                            'button' => '进一步了解',
                        ),
                        array(
                            'title' => '万余家企业服务经验',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/c50bb0d148e4cc9678c0b04d0e8ea7e7.png',
                            'bigtitle' => '企业互联网战略<br/>服务品牌',
                            'bigdesc' => '我们为企业提供高品质互联网<br/>营销整合服务',
                            'button' => '进一步了解',
                        ),
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type2'),
                    ),
                ),
                // Repeatable Item
                'jw_tab_item_three' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'number' => array(
                            'type' => 'text',
                            'title' => JText::_('数字内容'),
                            'desc' => JText::_('数字内容'),
                            'std' => '1000',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('数字标题'),
                            'desc' => JText::_('数字标题'),
                            'std' => '标题',
                        ),
                        'unit' => array(
                            'type' => 'text',
                            'title' => JText::_('数字单位'),
                            'desc' => JText::_('数字单位'),
                            'std' => '单位',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '互联网屏',
                            'number' => '170',
                            'unit' => '万+',
                        ),
                        array(
                            'title' => '覆盖省',
                            'number' => '34',
                            'unit' => '个',
                        ),
                        array(
                            'title' => '覆盖城市',
                            'number' => '348',
                            'unit' => '个',
                        ),
                        array(
                            'title' => '覆盖人群',
                            'number' => '3',
                            'unit' => '亿+',
                        ),
                        array(
                            'title' => '库存流量',
                            'number' => '19',
                            'unit' => '亿/天',
                        ),
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type3'),
                    ),
                ),
                // Repeatable Item
                'jw_tab_item_four' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'number' => array(
                            'type' => 'text',
                            'title' => JText::_('数字内容'),
                            'desc' => JText::_('数字内容'),
                            'std' => '1000',
                        ),
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('数字标题'),
                            'desc' => JText::_('数字标题'),
                            'std' => '标题',
                        ),
                        'unit' => array(
                            'type' => 'text',
                            'title' => JText::_('数字单位'),
                            'desc' => JText::_('数字单位'),
                            'std' => '单位',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '网民覆盖',
                            'number' => '98',
                            'unit' => '%',
                        ),
                        array(
                            'title' => '日均广告流量',
                            'number' => '200',
                            'unit' => '亿+',
                        ),
                        array(
                            'title' => '网民大数据画像',
                            'number' => '7',
                            'unit' => '亿+',
                        ),
                        array(
                            'title' => '信息流日活',
                            'number' => '1.6',
                            'unit' => '亿+',
                        ),
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type4'),
                    ),
                ),
                'type2_bg_img' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220512/05692aaa2eb7fadd95a4bf28633dacb9.jpeg',
                    'depends' => array(
                        array('number_type', '=', 'type2'),
                    ),
                ),
                'jw_tab_item05' => array(
                    'title' => '自定义内容列表',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '累计服务超30万会员',
                        ),
                        'number' => array(
                            'type' => 'number',
                            'title' => '数字内容',
                            'std' => '30',
                        ),
                        'num_unit' => array(
                            'type' => 'text',
                            'title' => '数字单位',
                            'std' => '万',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '累计服务超30万会员',
                            'number' => '30',
                            'num_unit' => '万',
                        ),
                        array(
                            'title' => '国家软件著作权',
                            'number' => '5',
                            'num_unit' => '+',
                        ),
                        array(
                            'title' => '场馆选择了馆家',
                            'number' => '5000',
                            'num_unit' => '家',
                        ),
                        array(
                            'title' => '超百人的专业团队',
                            'number' => '100',
                            'num_unit' => '+',
                        ),
                        array(
                            'title' => '安全运行天数',
                            'number' => '1000',
                            'num_unit' => '+',
                        ),
                        array(
                            'title' => '客户续费率',
                            'number' => '90',
                            'num_unit' => '%',
                        )
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type5'),
                    ),
                ),
                'jw_tab_item06' => array(
                    'title' => '自定义内容列表',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '覆盖全国26个省市区',
                        ),
                        'intro' => array(
                            'type' => 'text',
                            'title' => '简介',
                            'std' => '拥有加盟店260家',
                        ),
                        'number' => array(
                            'type' => 'number',
                            'title' => '数字内容',
                            'std' => '260',
                        ),
                        'num_unit' => array(
                            'type' => 'text',
                            'title' => '数字单位',
                            'std' => '+',
                        ),
                        'num_start_offset' => array(
                            'type' => 'slider',
                            'title' => '开始数字与结束数字的差值',
                            'std' => '20',
                        ),
                        'during' => array(
                            'type' => 'slider',
                            'title' => '动画时长',
                            'std' => '200',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '覆盖全国26个省市区',
                            'intro' => '拥有加盟店260家',
                            'number' => '260',
                            'num_unit' => '+',
                            'num_start_offset' => '20',
                            'during' => '200',
                        ),
                        array(
                            'title' => '全国建立30000+销售终端',
                            'intro' => '打破东北地域限制，成功实现品牌焕新',
                            'number' => '30000',
                            'num_unit' => '+',
                            'num_start_offset' => '20',
                            'during' => '200',
                        ),
                        array(
                            'title' => '年营收3亿以上',
                            'intro' => '不断创新营销，拓展商业模式',
                            'number' => '3',
                            'num_unit' => '亿+',
                            'num_start_offset' => '2',
                            'during' => '2000',
                        ),
                        array(
                            'title' => '全国建立五大生产基地',
                            'intro' => '可实现30亿产值配置',
                            'number' => '30',
                            'num_unit' => '亿+',
                            'num_start_offset' => '20',
                            'during' => '200',
                        ),
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                //type6
                'font_type6' => array(
                    'type' => 'slider',
                    'title' => JText::_('数字字体大小'),
                    'responsive' => true,
                    'std' => array(
                        'md' => 72,
                        'sm' => 28,
                        'xs' => 28
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                'dwfont_type6' => array(
                    'type' => 'slider',
                    'title' => JText::_('单位字体大小'),
                    'responsive' => true,
                    'std' => array(
                        'md' => 36,
                        'sm' => 18,
                        'xs' => 18
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                'titlefont_type6' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'responsive' => true,
                    'std' => array(
                        'md' => 20,
                        'sm' => 16,
                        'xs' => 14
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                'jianjiefont_type6' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'responsive' => true,
                    'std' => array(
                        'md' => 16,
                        'sm' => 14,
                        'xs' => 14
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                'dwtop_type6' => array(
                    'type' => 'slider',
                    'title' => JText::_('单位顶部距离'),
                    'responsive' => true,
                    'std' => array(
                        'md' => 30,
                        'sm' => 12,
                        'xs' => 12
                    ),
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),
                //
                
                'number_color_one' => array(
                    'type' => 'color',
                    'title' => JText::_('数字字体颜色'),
                    'std' => '#2c2c2c',
                    'depends' => array(
                        array('number_type', '!=', 'type2'),
                        array('number_type', '!=', 'type3'),
                    ),
                ),
                'number_color_two' => array(
                    'type' => 'color',
                    'title' => JText::_('数字字体颜色'),
                    'std' => '#0083ff',
                    'depends' => array(
                        array('number_type', '=', 'type3'),
                    ),
                ),
                'title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#696969',
                    'depends' => array(
                        array('number_type', '!=', 'type2'),
                    ),
                ),
                'unit_color' => array(
                    'type' => 'color',
                    'title' => JText::_('单位字体颜色'),
                    'std' => '#333333',
                    'depends' => array(
                        array('number_type', '!=', 'type1'),
                        array('number_type', '!=', 'type2'),
                    ),
                ),
                'jianjie6_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'std' => '#9e9e9e',
                    'depends' => array(
                        array('number_type', '=', 'type6'),
                    ),
                ),

                'border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框字体颜色'),
                    'std' => '#696969',
                    'depends' => array(
                        array('number_type', '=', 'type5'),
                    ),
                ),
            ),
        ),
    )
);
