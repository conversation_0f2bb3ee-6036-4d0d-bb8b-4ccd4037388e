<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'service_tab_list_carousel',
        'title' => '分类选项卡跑马灯',
        'desc' => '分类选项卡跑马灯',
        'category' => '选项卡',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                /*
                 * 新加的左侧选项
                 */
                'type_parent' => array(
                    'type' => 'select',
                    'title' => '分类显示',
                    'desc' => '分类显示',
                    'values' => array(
                        'type1' => '一级分类',
                        'type2' => '二级分类',
                        'type3' => '三级分类'
                    ),
                    'std' => 'type1',
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从第n个分类开始显示',
                    'std' => '1'
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类(0为不限)',
                    'desc' => '显示n条分类',
                    'std' => '10'
                ),
                'limit' => array(//每个分类下的条数
                    'type' => 'number',
                    'title' => '分类下的条数',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10'
                ),
                /*
                 * 新加的左侧选项
                 */
                'content_style' => array(
                    'type' => 'buttons',
                    'title' => '样式选项',
                    'std' => 'tab_style',
                    'values' => array(
                        array(
                            'label' => '选项卡',
                            'value' => 'tab_style'
                        ),
                        array(
                            'label' => '跑马灯',
                            'value' => 'carousel_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'social_separator_1' => array(
                    'type' => 'separator',
                    'title' => '选项卡',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                    ),
                ),
//                'service_tab_item_number' => array(
//                    'type' => 'slider',
//                    'title' => '选项卡个数',
//                    'desc' => '',
//                    'min' => 1,
//                    'max' => 8,
//                    'responsive' => true,
//                    'std' => array(
//                        'md' => 8,
//                        'sm' => 6,
//                        'xs' => 3
//                    ),
//                    'depends' => array(
//                        array('content_style', '=', 'tab_style'),
//                    ),
//                ),



                'service_tab_bgColor' => array(
                    'type' => 'color',
                    'title' => '导航背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                    ),
                ),
                'service_tab_line' => array(
                    'type' => 'color',
                    'title' => '导航分割线颜色',
                    'std' => '#f0f0f0',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                    ),
                ),
                'service_tab_item_style' => array(
                    'type' => 'buttons',
                    'title' => '当前修改部分',
                    'std' => 'tab_icon_style',
                    'values' => array(
                        array(
                            'label' => '图标',
                            'value' => 'tab_icon_style'
                        ),
                        array(
                            'label' => '文字',
                            'value' => 'tab_text_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                    ),
                ),
                'social_separator_5' => array(
                    'type' => 'separator',
                    'title' => '图标选项',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_icon_style'),
                    ),
                ),
                'service_tab_item_icon_width' => array(
                    'type' => 'slider',
                    'title' => '图标宽度',
                    'std' => '60',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_icon_style'),
                    ),
                    'responsive' => true,
                    'max' => 150,
                ),
                'service_tab_item_icon_height' => array(
                    'type' => 'slider',
                    'title' => '图标高度',
                    'std' => '60',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_icon_style'),
                    ),
                    'responsive' => true,
                    'max' => 150,
                ),
                'social_separator_6' => array(
                    'type' => 'separator',
                    'title' => '文字选项',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'service_tab_item_fontsize' => array(
                    'type' => 'slider',
                    'title' => '字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                    'responsive' => true,
                    'max' => 40,
                ),
                'service_tab_item_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '文字行高',
                    'std' => '32',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                    'max' => 400,
                ),
//				'service_tab_item_style' => array(
//					'type' => 'fontstyle',
//					'title' => '文字样式',
//					'depends' => array(
//						array('content_style', '=', 'tab_style'),
//					),
//				),
                'service_tab_item_letterSpace' => array(
                    'type' => 'select',
                    'title' => '文字间距',
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'service_tab_item_color' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#444',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'service_tab_item_textAlign' => array(
                    'type' => 'select',
                    'title' => '对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'service_tab_item_margin' => array(
                    'type' => 'margin',
                    'title' => '文字外边距',
                    'desc' => '',
                    'placeholder' => '10',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                    'max' => 400,
                    'std' => '10px 0 0 0',
                    'responsive' => true
                ),
                'service_tab_item_hover_bg' => array(
                    'type' => 'color',
                    'title' => '鼠标滑过背景颜色',
                    'std' => '#2164d9',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'service_tab_item_hover_color' => array(
                    'type' => 'color',
                    'title' => '鼠标滑过文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('content_style', '=', 'tab_style'),
                        array('service_tab_item_style', '=', 'tab_text_style'),
                    ),
                ),
                'social_separator_2' => array(
                    'type' => 'separator',
                    'title' => '下方跑马灯',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'isLinkDetail' => array(
                    'type' => 'checkbox',
                    'title' => '是否链接详情页',
                    'desc' => '',
                    'std' => 0,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('isLinkDetail', '=', 1),
                    ),
                ),
                'service_carousel_bgColor' => array(
                    'type' => 'color',
                    'title' => '跑马灯背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'service_carousel_item_number' => array(
                    'type' => 'slider',
                    'title' => '跑马灯一页显示个数',
                    'desc' => '',
                    'min' => 1,
                    'max' => 6,
                    'std' => 4,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'service_carousel_mgB' => array(
                    'type' => 'slider',
                    'title' => '跑马灯内容距离底部切换按钮高度',
                    'desc' => '',
                    'min' => 0,
                    'max' => 200,
                    'std' => 20,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'service_carousel_item_style' => array(
                    'type' => 'buttons',
                    'title' => '当前修改部分',
                    'std' => 'carousel_img_style',
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'carousel_img_style'
                        ),
                        array(
                            'label' => '文字',
                            'value' => 'carousel_text_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                    ),
                ),
                'social_separator_3' => array(
                    'type' => 'separator',
                    'title' => '图片选项',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_img_style'),
                    ),
                ),
                'service_carousel_item_imgH' => array(
                    'type' => 'slider',
                    'title' => '图片高度',
                    'desc' => '',
                    'min' => 100,
                    'max' => 400,
                    'std' => 200,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_img_style'),
                    ),
                ),
                'service_carousel_item_imgTime' => array(
                    'type' => 'slider',
                    'title' => '图片动画效果时间',
                    'desc' => '',
                    'min' => 100,
                    'max' => 500,
                    'std' => 200,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_img_style'),
                    ),
                ),
                'social_separator_4' => array(
                    'type' => 'separator',
                    'title' => '文字选项',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_item_fontsize' => array(
                    'type' => 'slider',
                    'title' => '字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                    'responsive' => true,
                    'max' => 40,
                ),
                'service_carousel_item_textH' => array(
                    'type' => 'slider',
                    'title' => '文字高度',
                    'std' => '80',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                    'responsive' => true,
                    'max' => 200,
                ),
                'service_carousel_textColor' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_textBgColor' => array(
                    'type' => 'color',
                    'title' => '文字背景颜色',
                    'std' => '#2164d9',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_textIconBgColor' => array(
                    'type' => 'color',
                    'title' => '下方图标背景颜色',
                    'std' => '#e29728',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_textIcon' => array(
                    'type' => 'media',
                    'title' => '下方小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_item_fontsizeHover' => array(
                    'type' => 'slider',
                    'title' => '鼠标滑过字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                    'responsive' => true,
                    'max' => 40,
                ),
                'service_carousel_textHoverBgColor' => array(
                    'type' => 'color',
                    'title' => '鼠标滑过背景颜色',
                    'std' => '#0143b7',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_textHoverColor' => array(
                    'type' => 'color',
                    'title' => '鼠标滑过文字颜色',
                    'std' => '#e29728',
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'service_carousel_item_textTime' => array(
                    'type' => 'slider',
                    'title' => '动画效果时间',
                    'desc' => '',
                    'min' => 100,
                    'max' => 500,
                    'std' => 200,
                    'depends' => array(
                        array('content_style', '=', 'carousel_style'),
                        array('service_carousel_item_style', '=', 'carousel_text_style'),
                    ),
                ),
                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);