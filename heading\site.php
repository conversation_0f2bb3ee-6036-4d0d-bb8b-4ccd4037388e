<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonHeading extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
		$class .= (isset($settings->alignment) && $settings->alignment) ? ' ' . $settings->alignment : ' jwpf-text-center';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h2';
		$use_link = (isset($settings->use_link)) ? $settings->use_link : 0;
//		$title_link = ($use_link) ? ((isset($settings->title_link) && $settings->title_link) ? $settings->title_link : '') : false;
		$link_target = (isset($settings->link_new_tab) && $settings->link_new_tab) ? 'target="_blank" rel="noopener noreferrer"' : '';
		$title_icon = (isset($settings->title_icon) && $settings->title_icon) ? $settings->title_icon : '';
		$title_icon_position = (isset($settings->title_icon_position) && $settings->title_icon_position) ? $settings->title_icon_position : 'before';
		$title_icon_color = (isset($settings->title_icon_color) && $settings->title_icon_color) ? $settings->title_icon_color : '';
		$tz_page_type = (isset($settings->tz_page_type)) ? $settings->tz_page_type : 'Internal_pages';
		$detail_page = (isset($settings->detail_page)) ? $settings->detail_page : '';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $attribs='';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        if($detail_page_id){
            $id=base64_encode($detail_page_id);
            $host=$_SERVER['HTTP_HOST'];
			if($host=='jzt_dev_2.china9.cn')
			{
				$host = 'http://jzt_dev_2.china9.cn/';
			}
			else
			{
				$host = 'https://ijzt.china9.cn/';
			}
            $return= $host.'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
            $attribs .= ' href="' . $return . '"';
        }

		$output = '';
		if ($title) {
			$output .= '<div class="jwpf-addon jwpf-addon-header' . $class . '">';
			if($use_link){
				if($tz_page_type=='Internal_pages')
				{
					$output .=  '<a ' . $link_target . ' ' . $attribs . '>' ;
				}
				else
				{
					$output .= '<a ' . $link_target . ' href="' . $detail_page . '">' ;
				}

           }
		 //   else
		 //   {
			// $output .= ($detail_page_id) ? '<a ' . $link_target . ' href="javascript:;">' : '';
		 //   }
            if($settings->icon!='' && $settings->icon_r!=''){
                $output .= '<' . $heading_selector . ' class="jwpf-addon-title" style="display: inline;
    background: url('.$settings->icon.')center left no-repeat,url('.$settings->icon_r.')right center no-repeat;
    padding: 0 '.($settings->icon_pad ?? 43).'px;background-size: '.($settings->icon_pad_w ?? 20).'px '.($settings->icon_pad_h ?? 20).'px;">';
            }else{
                $output .= '<' . $heading_selector . ' class="jwpf-addon-title">';
            }


			if ($title_icon) {
				$icon_arr = array_filter(explode(' ', $title_icon));
				if (count($icon_arr) === 1) {
					$title_icon = 'fa ' . $title_icon;
				}
			}
			if ($title_icon && $title_icon_position == 'before') {
				$output .= '<span class="' . $title_icon . ' jwpf-addon-title-icon" aria-hidden="true"></span> ';
			}
			$output .= nl2br($title);
			if ($title_icon && $title_icon_position == 'after') {
				$output .= ' <span class="' . $title_icon . ' jwpf-addon-title-icon" aria-hidden="true"></span>';
			}
			$output .= '</' . $heading_selector . '>';
           if($use_link){
                $output .= '</a>';
           }

			$output .= '</div>';
		}

		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;

		$style = '';
		$style_sm = '';
		$style_xs = '';

		$style .= (isset($settings->title_margin) && trim($settings->title_margin)) ? 'margin: ' . $settings->title_margin . '; ' : '';
		$style .= (isset($settings->title_text_transform) && trim($settings->title_text_transform)) ? 'text-transform: ' . $settings->title_text_transform . '; ' : '';
		$style_sm .= (isset($settings->title_margin_sm) && trim($settings->title_margin_sm)) ? 'margin: ' . $settings->title_margin_sm . '; ' : '';
		$style_xs .= (isset($settings->title_margin_xs) && trim($settings->title_margin_xs)) ? 'margin: ' . $settings->title_margin_xs . '; ' : '';

		$style .= (isset($settings->title_padding) && trim($settings->title_padding)) ? 'padding: ' . $settings->title_padding . '; ' : '';
		$style_sm .= (isset($settings->title_padding_sm) && trim($settings->title_padding_sm)) ? 'padding: ' . $settings->title_padding_sm . '; ' : '';
		$style_xs .= (isset($settings->title_padding_xs) && trim($settings->title_padding_xs)) ? 'padding: ' . $settings->title_padding_xs . '; ' : '';

		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h2';

		$title_icon = (isset($settings->title_icon) && $settings->title_icon) ? $settings->title_icon : '';
		$title_icon_color = (isset($settings->title_icon_color) && $settings->title_icon_color) ? $settings->title_icon_color : '';

		if (isset($settings->title_text_shadow) && is_object($settings->title_text_shadow)) {
			$ho = (isset($settings->title_text_shadow->ho) && $settings->title_text_shadow->ho != '') ? $settings->title_text_shadow->ho . 'px' : '0px';
			$vo = (isset($settings->title_text_shadow->vo) && $settings->title_text_shadow->vo != '') ? $settings->title_text_shadow->vo . 'px' : '0px';
			$blur = (isset($settings->title_text_shadow->blur) && $settings->title_text_shadow->blur != '') ? $settings->title_text_shadow->blur . 'px' : '0px';
			$color = (isset($settings->title_text_shadow->color) && $settings->title_text_shadow->color != '') ? $settings->title_text_shadow->color : '';

			if (!empty($color)) {
				$style .= "text-shadow: ${ho} ${vo} ${blur} ${color};";
			}
		}

		$css = '';
		if ($style) {
			$css .= $addon_id . ' ' . $heading_selector . '.jwpf-addon-title {' . $style . '}';
		}

		if ($title_icon && $title_icon_color) {
			$css .= $addon_id . ' ' . $heading_selector . '.jwpf-addon-title .jwpf-addon-title-icon {color: ' . $title_icon_color . '}';
		}


        /*纵向排列*/
        $column = (isset($settings->column) && ($settings->column || $settings->column==0)) ? $settings->column: 0;
        $writing_mode = (isset($settings->writing_mode) && $settings->writing_mode) ? $settings->writing_mode: '';

        if($column==1){
            if($writing_mode=='vertical-lr'){
                $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
                #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{
                    float: left;
                }';
            }elseif($writing_mode=='vertical-rl'){
                $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
                #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{
                    float: right;
                }';
            }
            $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
            #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{ 
                writing-mode: '.$writing_mode.';
            }';
        }

		if ($style_sm) {
			$css .= '@media (min-width: 768px) and (max-width: 991px) {';
			$css .= $addon_id . ' ' . $heading_selector . '.jwpf-addon-title {' . $style_sm . '}';
			$css .= '}';
		}

		if ($style_xs) {
			$css .= '@media (max-width: 767px) {';
			$css .= $addon_id . ' ' . $heading_selector . '.jwpf-addon-title {' . $style_xs . '}';
			$css .= '}';
		}

		return $css;
	}
    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
	public static function getTemplate()
	{
		$output = '
        <#
            var link_target = (data.link_new_tab) ? \'target="_blank"\' : "" ;

            var margin = "";
			var margin_sm = "";
			var margin_xs = "";
			if(data.title_margin){
				if(_.isObject(data.title_margin)){
                    if(data.title_margin.md.trim() != ""){
                        margin = data.title_margin.md.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_margin.sm.trim() != ""){
                        margin_sm = data.title_margin.sm.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_margin.xs.trim() != ""){
                        margin_xs = data.title_margin.xs.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
				} else {
                    if(data.title_margin.trim() != ""){
                        margin = data.title_margin.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
				}

			}

			var padding = "";
			var padding_sm = "";
			var padding_xs = "";
			if(data.title_padding){
				if(_.isObject(data.title_padding)){
					if(data.title_padding.md.trim() !== ""){
						padding = data.title_padding.md.split(" ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}

					if(data.title_padding.sm.trim() !== ""){
						padding_sm = data.title_padding.sm.split(" ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}

					if(data.title_padding.xs.trim() !== ""){
						padding_xs = data.title_padding.xs.split(" ").map(item => {
							if(_.isEmpty(item)){
								return "0";
							}
							return item;
						}).join(" ")
					}
				} else {
					padding = data.title_padding.split(" ").map(item => {
						if(_.isEmpty(item)){
							return "0";
						}
						return item;
					}).join(" ")
				}

            }
            
            var titleTextShadow = "";
			if(_.isObject(data.title_text_shadow)){
				let ho = data.title_text_shadow.ho || 0,
					vo = data.title_text_shadow.vo || 0,
					blur = data.title_text_shadow.blur || 0,
					color = data.title_text_shadow.color || 0;

                titleTextShadow = ho+\'px \'+vo+\'px \'+blur+\'px \'+color;
			}
			
			
//			纵向排列
			let column=data.column||0;
			let writing_mode = data.writing_mode||"";
        #>
        <style type="text/css">
            #jwpf-addon-{{ data.id }} {{ data.heading_selector }}.jwpf-addon-title{
                margin: {{ margin }};
                padding: {{ padding }};
                text-shadow: {{ titleTextShadow }};
                text-transform: {{ data.title_text_transform }};
            }

            <# if(data.title_icon && data.title_icon_color) { #>
                #jwpf-addon-{{ data.id }} {{ data.heading_selector }}.jwpf-addon-title .jwpf-addon-title-icon {
                    color: {{ data.title_icon_color }}
                }
            <# } #>
            
            <# if(column==1){ #>
                #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                    writing-mode: {{writing_mode}};
                }
                <# if(writing_mode=="vertical-lr"){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                    #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                        float: left;
                    }
                <# }else if(writing_mode=="vertical-rl"){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                    #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                        float: right;
                    }
                <# } #>
			<# } #>

            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} {{ data.heading_selector }}.jwpf-addon-title{
                    margin: {{ margin_sm }};
                    padding: {{ padding_sm }};
                }
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} {{ data.heading_selector }}.jwpf-addon-title{
                    margin: {{ margin_xs }};
                    padding: {{ padding_xs }};
                }
            }
        </style>
        <div class="jwpf-addon jwpf-addon-header {{ data.class }} {{ data.alignment }}">
            <# 
            let heading_selector = data.heading_selector || "h2";
            if(data.use_link && data.detail_page_id){ #><a {{ link_target }} href=\'{{ data.detail_page_id }}\'><# } 
            #>
                <{{ heading_selector }} class="jwpf-addon-title">
                <# 
                let icon_arr = (typeof data.title_icon !== "undefined" && data.title_icon) ? data.title_icon.split(" ") : "";
                let icon_name = icon_arr.length === 1 ? "fa "+data.title_icon : data.title_icon;
                #>
                <# if(data.title_icon && data.title_icon_position == "before"){ #><span class="{{ icon_name }} jwpf-addon-title-icon"></span><# } #>
                <span class="jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{{ data.title }}}</span>
                <# if(data.title_icon && data.title_icon_position == "after"){ #> <span class="{{ icon_name }} jwpf-addon-title-icon"></span><# } #>
                </{{ heading_selector }}>
            <# if(data.use_link && data.detail_page_id){ #></a><# } #>
        </div>
        ';

		return $output;
	}
}
