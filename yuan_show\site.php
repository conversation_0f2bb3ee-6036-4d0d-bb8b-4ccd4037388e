<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonYuan_show extends JwpagefactoryAddons
{

	public function render()
	{  

        $settings = $this->addon->settings;
        $addonId = '#jwpf-addon-' . $this->addon->id;

		$output .= ' <style>';
        $output .= $addonId . ' ul,p { margin: 0; padding: 0; }';
        $output .= $addonId . ' li { list-style: none; }';
        $output .= $addonId . ' .sec2{padding:0.75rem 0}';
        $output .= $addonId . ' .yuan{width: 14rem;height: 14rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan1.png") no-repeat center top;margin:0 auto;}';
        $output .= $addonId . ' .sec2 ul{padding: 0 6%}';
        $output .= $addonId . ' .sec2 ul li{width: 25%;float: left;text-align: center;}';
        $output .= $addonId . ' .sec2 ul li .yuan{color: #ffffff;font-weight: bold;font-size: 1.6rem;position: relative;}';
        $output .= $addonId . ' .sec2_jian{color: #404040;font-size: 1.5rem;padding-top: 1rem;}';
        $output .= $addonId . ' .sec2 ul li .yuan i img{padding-top: 4rem;padding-bottom: 1rem;margin: auto;}';
        $output .= $addonId . ' .sec2 ul li:hover .yuan{background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan2.png") no-repeat center top;}';
        $output .= $addonId . ' .sec2 ul li i,.sec2 ul li p{-webkit-transition:opacity 0.35s,-webkit-transform 0.35s;transition:opacity 0.35s,transform 0.35s;-webkit-transform:scale(1);transform:scale(1)}';
        $output .= $addonId . ' .sec2 ul li:hover i img,.sec2 ul li:hover p{-webkit-transform:scale(0.9);transform:scale(0.9)}';
        $output .= ' @media only screen and (max-width:1023px){';
        $output .= $addonId . ' .yuan{width: 6rem;height: 6rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan1.png") no-repeat center top;margin:0 auto;background-size: 100% 100%;}';
             $output .= $addonId . ' .sec2 ul li:hover .yuan{width: 6rem;height: 6rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan2.png") no-repeat center top;margin:0 auto;background-size: 100% 100%;}';
        $output .= $addonId . ' .sec2 ul li .yuan i img{width: 2rem;height: 3.4rem;padding-top: 1.5rem;padding-bottom: 0.3rem;margin: auto;}';
         $output .= $addonId . ' .sec2 ul li i,.sec2 ul li p{-webkit-transition:opacity 0.35s,-webkit-transform 0.35s;transition:opacity 0.35s,transform 0.35s;-webkit-transform:scale(1);transform:scale(1);font-size:10px}';
        $output .= $addonId . ' .sec2 ul li:hover i img,.sec2 ul li:hover p{-webkit-transform:scale(0.9);transform:scale(0.9)}';
        $output .= $addonId . ' .sec2_jian{color: #404040;font-size: 0.6rem;padding-top: 1rem;}';
        $output .= ' }';
        $output .= ' </style>';
        $output .= '<div class="sec2">';
        $output .= '  <ul>';

        $bg_img = (isset($settings->bg_img) && $settings->bg_img) ? $settings->bg_img : 'https://oss.lcweb01.cn/joomla/20220630/2e472beb40666c32370f53890029119d.png';
        $hover_bg = (isset($settings->hover_bg) && $settings->hover_bg) ? $settings->hover_bg : 'https://oss.lcweb01.cn/joomla/20220630/45454e6228b4f8d530113cc4383f0dd2.png';

        $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : array(
                array(
                    'title' => '无感知AI考勤',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/d380a73bcc5866b4d7302d405a84967b.png',
                    'text' => '快速识别高效感应<br>
                                生物防假，杜绝代打卡<br>
                                无需等待，路过即考勤',
                ),
                array(
                    'title' => '手机考勤',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/13c5e7ae71c24569765e12f7b18d1579.png',
                    'text' => '定位不准确<br>
                            考勤易做假<br>
                            受网络影响',
                ),
                array(
                    'title' => '刷卡考勤',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/be3040ea8dd7b905601aced5b4a3c8ac.png',
                    'text' => '制卡费用高<br>
                                卡片易丢失损坏<br>
                                代打卡无法监控',
                ),
                array(
                    'title' => '指纹考勤',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/da3b670960ba619435d92ad9feccc2d7.png',
                    'text' => '打卡需要排队等侯<br>
                                指纹识别时间过长<br>
                                手部信息不易识别',
                ),
                
            );


            if (isset($jw_tab_item) && !empty($jw_tab_item)) {
                foreach ($jw_tab_item as $item_key => $tab_item) {
                    if($item_key==0){

                        $output .= '    <li class="mx fadeInLeft animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInLeft;">';
                        $output .= '      <div class="yuan"> <i><img src="'.$tab_item['img'].'" alt=""></i>';
                        $output .= '        <p>'.$tab_item['title'].'</p>';
                        $output .= '      </div>';
                        $output .= '      <div class="sec2_jian">'.$tab_item['text'].'</div>';
                        $output .= '    </li>';

                    }elseif($item_key==1){

                        $output .= '    <li class="mx fadeInLeft animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;">';
                        $output .= '     <div class="yuan"> <i><img src="'.$tab_item['img'].'" alt=""></i>';
                        $output .= '        <p>'.$tab_item['title'].'</p>';
                        $output .= '      </div>';
                        $output .= '      <div class="sec2_jian">'.$tab_item['text'].'</div>';
                        $output .= '    </li>';
                    }elseif($item_key==2){

                        $output .= '    <li class="mx fadeInRight animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInRight;">';
                        $output .= '     <div class="yuan"> <i><img src="'.$tab_item['img'].'" alt=""></i>';
                        $output .= '        <p>'.$tab_item['title'].'</p>';
                        $output .= '      </div>';
                        $output .= '      <div class="sec2_jian">'.$tab_item['text'].'</div>';
                        $output .= '    </li>';
        
                    }else{
                        $output .= '    <li class="mx fadeInRight animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInRight;">';
                        $output .= '     <div class="yuan"> <i><img src="'.$tab_item['img'].'" alt=""></i>';
                        $output .= '        <p>'.$tab_item['title'].'</p>';
                        $output .= '      </div>';
                        $output .= '      <div class="sec2_jian">'.$tab_item['text'].'</div>';
                        $output .= '    </li>';
                    }


                }
            }


        // $output .= '    <li class="mx fadeInLeft animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInLeft;">';
        // $output .= '      <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/ai.png" alt=""></i>';
        // $output .= '        <p>无感知AI考勤</p>';
        // $output .= '      </div>';
        // $output .= '      <div class="sec2_jian"> 快速识别高效感应<br>';
        // $output .= '        生物防假，杜绝代打卡<br>';
        // $output .= '        无需等待，路过即考勤 </div>';
        // $output .= '    </li>';
        // $output .= '    <li class="mx fadeInLeft animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;">';
        // $output .= '      <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/shouji.png" alt=""></i>';
        // $output .= '        <p>手机考勤</p>';
        // $output .= '      </div>';
        // $output .= '      <div class="sec2_jian"> 定位不准确<br>';
        // $output .= '        考勤易做假<br>';
        // $output .= '        受网络影响 </div>';
        // $output .= '    </li>';
        // $output .= '    <li class="mx fadeInRight animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInRight;">';
        // $output .= '      <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/card.png" alt=""></i>';
        // $output .= '        <p>刷卡考勤</p>';
        // $output .= '      </div>';
        // $output .= '      <div class="sec2_jian"> 制卡费用高<br>';
        // $output .= '        卡片易丢失损坏<br>';
        // $output .= '        代打卡无法监控 </div>';
        // $output .= '    </li>';
        // $output .= '    <li class="mx fadeInRight animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInRight;">';
        // $output .= '      <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/zhiwen.png" alt=""></i>';
        // $output .= '        <p>指纹考勤</p>';
        // $output .= '      </div>';
        // $output .= '      <div class="sec2_jian"> 打卡需要排队等侯<br>';
        // $output .= '        指纹识别时间过长<br>';
        // $output .= '        手部信息不易识别 </div>';
        // $output .= '    </li>';
        $output .= '    <div class="clear"></div>';
        $output .= '  </ul>';
        $output .= '</div>';
		return $output;

	}

	public static function getTemplate()
	{
		$output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item;
        
        #>
		
            <style>
                {{addonId}} ul,p { margin: 0; padding: 0; }
                {{addonId}} li { list-style: none; }
                {{addonId}} .sec2{padding:0.75rem 0}
                {{addonId}} .yuan{width: 14rem;height: 14rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan1.png") no-repeat center top;margin:0 auto;}
                {{addonId}} .sec2 ul{padding: 0 6%}
                {{addonId}} .sec2 ul li{width: 25%;float: left;text-align: center;}
                {{addonId}} .sec2 ul li .yuan{color: #ffffff;font-weight: bold;font-size: 1.6rem;position: relative;}
                {{addonId}} .sec2_jian{color: #404040;font-size: 1.5rem;padding-top: 1rem;}
                {{addonId}} .sec2 ul li .yuan i img{padding-top: 4rem;padding-bottom: 1rem;margin: auto;}
                {{addonId}} .sec2 ul li:hover .yuan{background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan2.png") no-repeat center top;}
                {{addonId}} .sec2 ul li i,.sec2 ul li p{-webkit-transition:opacity 0.35s,-webkit-transform 0.35s;transition:opacity 0.35s,transform 0.35s;-webkit-transform:scale(1);transform:scale(1)}
                {{addonId}} .sec2 ul li:hover i img,.sec2 ul li:hover p{-webkit-transform:scale(0.9);transform:scale(0.9)}
                @media only screen and (max-width:1023px){
                    {{addonId}} .yuan{width: 6rem;height: 6rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan1.png") no-repeat center top;margin:0 auto;background-size: 100% 100%;}
                    {{addonId}} .sec2 ul li:hover .yuan{width: 6rem;height: 6rem;background: url("'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/yuan2.png") no-repeat center top;margin:0 auto;background-size: 100% 100%;}
                    {{addonId}} .sec2 ul li .yuan i img{width: 2rem;height: 3.4rem;padding-top: 1.5rem;padding-bottom: 0.3rem;margin: auto;}
                    {{addonId}} .sec2 ul li i,.sec2 ul li p{-webkit-transition:opacity 0.35s,-webkit-transform 0.35s;transition:opacity 0.35s,transform 0.35s;-webkit-transform:scale(1);transform:scale(1);font-size:10px}
                    {{addonId}} .sec2 ul li:hover i img,.sec2 ul li:hover p{-webkit-transform:scale(0.9);transform:scale(0.9)}
                    {{addonId}} .sec2_jian{color: #404040;font-size: 0.6rem;padding-top: 1rem;}
                }
            </style>

		      
            <div class="sec2">
                <ul>
                   <li class="mx fadeInLeft animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInLeft;">
                   <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/ai.png" alt=""></i>
                     <p>无感知AI考勤</p>
                    </div>
                    <div class="sec2_jian"> 快速识别高效感应<br>
                     生物防假，杜绝代打卡<br>
                     无需等待，路过即考勤 </div>
                   </li>
                  <li class="mx fadeInLeft animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;">
                     <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/shouji.png" alt=""></i>
                      <p>手机考勤</p>
                     </div>
                     <div class="sec2_jian"> 定位不准确<br>
                   考勤易做假<br>
                     受网络影响 </div>
                  </li>
                  <li class="mx fadeInRight animated" data-wow-delay="0.2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInRight;">
                   <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/card.png" alt=""></i>
                    <p>刷卡考勤</p>
                     </div>
                   <div class="sec2_jian"> 制卡费用高<br>
                      卡片易丢失损坏<br>
                     代打卡无法监控 </div>
                  </li>
                  <li class="mx fadeInRight animated" data-wow-delay="0.4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInRight;">
                    <div class="yuan"> <i><img src="'.str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/yuan_show/assets/images/zhiwen.png" alt=""></i>
                   <p>指纹考勤</p>
                     </div>
                    <div class="sec2_jian"> 打卡需要排队等侯<br>
                       指纹识别时间过长<br>
                      手部信息不易识别 </div>
                   </li>
                 <div class="clear"></div>
                </ul>
            </div>

		';

		return $output;
	}

}