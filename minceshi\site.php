<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonMinceshi extends JwpagefactoryAddons
{
    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '标题';
        $bg_image = (isset($settings->bg_image) && $settings->bg_image) ? $settings->bg_image : '';
        $overlay_mode = (isset($settings->overlay_mode) && $settings->overlay_mode) ? $settings->overlay_mode : ''; 
        $model_height = (isset($settings->model_height) && $settings->model_height) ? $settings->model_height : 350;
        $model_height_sm = (isset($settings->model_height_sm) && $settings->model_height_sm) ? $settings->model_height_sm : '';
        $model_height_xs = (isset($settings->model_height_xs) && $settings->model_height_xs) ? $settings->model_height_xs : '';
        $bg_image_height = (isset($settings->bg_image_height) && $settings->bg_image_height) ? $settings->bg_image_height : 350;
        $bg_image_height_sm = (isset($settings->bg_image_height_sm) && $settings->bg_image_height_sm) ? $settings->bg_image_height_sm : '';
        $bg_image_height_xs = (isset($settings->bg_image_height_xs) && $settings->bg_image_height_xs) ? $settings->bg_image_height_xs : '';
        $small_image = (isset($settings->small_image) && $settings->small_image) ? $settings->small_image : ''; 
        $small_image_hover = (isset($settings->small_image_hover) && $settings->small_image_hover) ? $settings->small_image_hover : ''; 
        $text = (isset($settings->text) && $settings->text) ? $settings->text : ''; 
        $overlay_color = (isset($settings->overlay_color) && $settings->overlay_color) ? $settings->overlay_color : ''; 
        $small_image_top = (isset($settings->small_image_top) && $settings->small_image_top) ? $settings->small_image_top : 120; 
        $small_image_top_sm = (isset($settings->small_image_top_sm) && $settings->small_image_top_sm) ? $settings->small_image_top_sm : ''; 
        $small_image_top_xs = (isset($settings->small_image_top_xs) && $settings->small_image_top_xs) ? $settings->small_image_top_xs : ''; 
        $small_image_top_hover = (isset($settings->small_image_top_hover) && $settings->small_image_top_hover) ? $settings->small_image_top_hover : 80; 
        $small_image_top_hover_sm = (isset($settings->small_image_top_hover_sm) && $settings->small_image_top_hover_sm) ? $settings->small_image_top_hover_sm : ''; 
        $small_image_top_hover_xs = (isset($settings->small_image_top_hover_xs) && $settings->small_image_top_hover_xs) ? $settings->small_image_top_hover_xs : ''; 
        $content_padding = (isset($settings->content_padding) && $settings->content_padding) ? $settings->content_padding : ''; 
        $title_size = (isset($settings->title_size) && $settings->title_size) ? $settings->title_size : 14; 
        $title_size_sm = (isset($settings->title_size_sm) && $settings->title_size_sm) ? $settings->title_size_sm : 14;
        $title_size_xs = (isset($settings->title_size_xs) && $settings->title_size_xs) ? $settings->title_size_xs : 14;

        $model_cover = (isset($settings->model_cover) && $settings->model_cover) ? $settings->model_cover : 0;
        $model_cover_height = (isset($settings->model_cover_height) && $settings->model_cover_height) ? $settings->model_cover_height : 350;
        $model_cover_height_sm = (isset($settings->model_cover_height_sm) && $settings->model_cover_height_sm) ? $settings->model_cover_height_sm : 350;
        $model_cover_height_xs = (isset($settings->model_cover_height_xs) && $settings->model_cover_height_xs) ? $settings->model_cover_height_xs : 350;

        $title_align = (isset($settings->title_align) && $settings->title_align) ? $settings->title_align : 'center';
        $title_padding = (isset($settings->title_padding) && $settings->title_padding) ? $settings->title_padding : '';

        // 正常状态遮罩层颜色
        $overlay_color_n = isset($settings->overlay_color_n) && $settings->overlay_color_n ? $settings->overlay_color_n : 'rgba(0, 0, 0, 0.5)';

        // 开启下划线
        $title_line = isset($settings->title_line) && $settings->title_line ? $settings->title_line : 0;
        // 下划线宽度
        $title_line_w = (isset($settings->title_line_w) && $settings->title_line_w) ? $settings->title_line_w : 55;
        $title_line_w_sm = (isset($settings->title_line_w_sm) && $settings->title_line_w_sm) ? $settings->title_line_w_sm : '';
        $title_line_w_xs = (isset($settings->title_line_w_xs) && $settings->title_line_w_xs) ? $settings->title_line_w_xs : '';
        // 下划线高度
        $title_line_h = (isset($settings->title_line_h) && $settings->title_line_h) ? $settings->title_line_h : 2;
        $title_line_h_sm = (isset($settings->title_line_h_sm) && $settings->title_line_h_sm) ? $settings->title_line_h_sm : '';
        $title_line_h_xs = (isset($settings->title_line_h_xs) && $settings->title_line_h_xs) ? $settings->title_line_h_xs : '';

        // 下划线颜色
        $title_line_color = isset($settings->title_line_color) && $settings->title_line_color ? $settings->title_line_color : '#FFFFFF';
        // 下划线对齐方式
        $title_line_align = isset($settings->title_line_align) && $settings->title_line_align ? $settings->title_line_align : 'flex-start';
        // 下划线内边距
        $line_padding = isset($settings->line_padding) && $settings->line_padding ? $settings->line_padding : '';

        // 开启下划线
        $bg_s_hover = isset($settings->bg_s_hover) && $settings->bg_s_hover ? $settings->bg_s_hover : 0;


        $output = "<style>

        ${addon_id} .minBox {
            height: ${model_height}px;
            background: #666666 url(${bg_image}) no-repeat center;
            background-size: 100% ${bg_image_height}px;
            overflow:hidden;
            position: relative;
            transition: all .4s;
        }
        ${addon_id} .minBox:hover {";
            if($bg_s_hover == 1) {
                $output .= "background-size: 110% " . ($bg_image_height * 1.1) . "px;";
            }
        $output .= "}
        ${addon_id}  .minBox_content {";
            if($model_cover == 1) {
                $output .= "height: ${model_cover_height}px;";
            }else {
                $output .= "height: 100%;";
            }
            $output .= "width: 100%;
            background-color: {$overlay_color_n};
            transition: all .4s;
            display: flex;
            flex-direction: column;
            align-items: center;
            position:absolute;
            left: 0;
            bottom: 0;
        }
        ${addon_id}  .minBox_contentImg {
            margin-top: ${small_image_top}px;";
            if($model_cover != 1) {
                $output .= "min-height:55px;";
            }
            $output .= "width:55px;
            transition: all .4s;
            background:url(${small_image}) no-repeat ;
            background-size: 100% 100%;
        }
        ${addon_id}  .minBox_content_title {
            color: #fff;
            margin: 20px 0;
            padding:${title_padding};
            font-size: ${title_size}px;
            width: 100%;
            text-align: ${title_align};
        }
        ${addon_id}  .minBox_content_title::before {
            content: '';
            width: 10px;
            border-bottom: 2px solid #fff;
            margin: 0 auto 20px;
            transition: all .4s;
        }
        ${addon_id}  .minBox_content:hover {
            background-color: ${overlay_color} ;
        }
        ${addon_id}  .minBox_content_con {
            color: #fff;
            padding:${content_padding};
            opacity: 0;
            transition: all .4s;
        }
        ${addon_id}  .minBox_content:hover .minBox_content_con {
            padding-top: 0;
            opacity: 1;
        }
        ${addon_id}  .minBox_content:hover .minBox_contentImg {
            margin-top: ${small_image_top_hover}px;
            background:url(${small_image_hover}) no-repeat ;
            background-size: 100% 100%;
        }
        ${addon_id}  .minBox_content:hover .minBox_content_title::before {
            margin-bottom: 0px;
            opacity: 0;
        }
        {$addon_id} .minBox_content .minBox_content_line {
            display: flex;
            align-items: center;
            justify-content: {$title_line_align};
            width: 100%;
            padding: {$line_padding};
        }
        {{addonId}} .minBox_content .minBox_content_line::after {
            content: '';
            width: {$title_line_w}px;
            height: {$title_line_h}px;
            background-color: {$title_line_color};
        }
        @media (min-width: 768px) and (max-width: 991px) {
            ${addon_id}  .minBox {
                height: ${model_height_sm}px;
                background: #666666 url(${bg_image}) no-repeat center;
                background-size: 100% ${bg_image_height_sm}px;
            }
            ${addon_id}  .minBox_content {";
                if($model_cover == 1) {
                    $output .= "height: ${$model_cover_height_sm}px;";
                }
            $output .= "}
            ${addon_id}  .minBox_contentImg {
                margin-top: ${small_image_top_sm}px;";
                if($model_cover != 1) {
                    $output .= "min-height:55px;";
                }
                $output .= "width:55px;
                transition: all .4s;
                background:url(${small_image}) no-repeat ;
                background-size: 100% 100%;
            }
            ${addon_id}  .minBox_content_title {
                font-size: ${title_size_sm}px;
            }
            ${addon_id}  .minBox_content:hover .minBox_contentImg {
                margin-top: ${small_image_top_hover_sm}px;
                background:url(${small_image_hover}) no-repeat ;
                background-size: 100% 100%;
            }
            {$addon_id} .minBox_content .minBox_content_line::after  {
                width: {$title_line_w_sm}px;
                height: {$title_line_h_sm}px;
            }
        }
        @media (max-width: 767px) {
            ${addon_id} .minBox {
                height: ${model_height_xs}px;
                background: #666666 url(${bg_image}) no-repeat center;
                background-size: 100% ${bg_image_height_xs}px;
            }
            ${addon_id}  .minBox_content {";
                if($model_cover == 1) {
                    $output .= "height: ${$model_cover_height_xs}px;";
                }
            $output .= "}
            ${addon_id}  .minBox_contentImg {
                margin-top: ${small_image_top_xs}px;";
                if($model_cover != 1) {
                    $output .= "min-height:55px;";
                }
                $output .= "width:55px;
                transition: all .4s;
                background:url(${small_image_top_xs}) no-repeat ;
                background-size: 100% 100%;
            }
            ${addon_id}  .minBox_content_title {
                font-size: ${title_size_xs}px;
            }
            ${addon_id}  .minBox_content:hover .minBox_contentImg {
                margin-top: ${small_image_top_hover_xs}px;
                background:url(${small_image_hover}) no-repeat ;
                background-size: 100% 100%;
            }
            {$addon_id} .minBox_content .minBox_content_line::after {
                width: {$title_line_w_xs}px;
                height: {$title_line_h_xs}px;
            }
        }

        </style>
        <div id='${addon_id}'>
        <div class='minBox '>
           <div class='minBox_content'>
           <div class='minBox_contentImg'></div>
                <div class='minBox_content_title'>{$title}</div>";
                if($title_line == 1) {
                    $output .= '<div class="minBox_content_line"></div>';
                }
                $output .= "
                <div class='minBox_content_con'>${text}</div>
           </div>
        </div>
        </div>
        ";
        return $output;
    }

    public function css()
    {

    }



    public static function getTemplate()
	{
        
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        let bg_image = (!_.isEmpty(data.bg_image) && data.bg_image) ? data.bg_image : "";
        let overlay_mode = (!_.isEmpty(data.overlay_mode) && data.overlay_mode) ? data.overlay_mode : "normal";
        let model_height = (!_.isEmpty(data.model_height.md) && data.model_height.md) ? data.model_height.md : 350;
        let model_height_sm = (!_.isEmpty(data.model_height.sm) && data.model_height.sm) ? data.model_height.sm : 350;
        let model_height_xs = (!_.isEmpty(data.model_height.xs) && data.model_height.xs) ? data.model_height.xs : 350;
        
        let model_cover = data.model_cover || 0;
        if(!_.isEmpty(data.model_cover_height)){
            var model_cover_height = (!_.isEmpty(data.model_cover_height.md) && data.model_cover_height.md) ? data.model_cover_height.md : 350;
            var model_cover_height_sm = (!_.isEmpty(data.model_cover_height.sm) && data.model_cover_height.sm) ? data.model_cover_height.sm : 350;
            var model_cover_height_xs = (!_.isEmpty(data.model_cover_height.xs) && data.model_cover_height.xs) ? data.model_cover_height.xs : 350;
        
        }
        
        let bg_image_height = (!_.isEmpty(data.bg_image_height.md) && data.bg_image_height.md) ? data.bg_image_height.md : 350;
        let bg_image_height_sm = (!_.isEmpty(data.bg_image_height.sm) && data.bg_image_height.sm) ? data.bg_image_height.sm : "";
        let bg_image_height_xs = (!_.isEmpty(data.bg_image_height.xs) && data.bg_image_height.xs) ? data.bg_image_height.xs : "";
        let small_image = (!_.isEmpty(data.small_image) && data.small_image) ? data.small_image : "";
        let small_image_hover = (!_.isEmpty(data.small_image_hover) && data.small_image_hover) ? data.small_image_hover : "";
        let text = (!_.isEmpty(data.text) && data.text) ? data.text : "";
        let overlay_color = (!_.isEmpty(data.overlay_color) && data.overlay_color) ? data.overlay_color : "";
        let small_image_top = (!_.isEmpty(data.small_image_top) && data.small_image_top) ? data.small_image_top.md : 120;
        let small_image_top_sm = (!_.isEmpty(data.small_image_top.sm) && data.small_image_top.sm) ? data.small_image_top.sm : "";
        let small_image_top_xs = (!_.isEmpty(data.small_image_top.xs) && data.small_image_top.xs) ? data.small_image_top.xs : "";
        let small_image_top_hover_md = (!_.isEmpty(data.small_image_top_hover.md) && data.small_image_top_hover.md) ? data.small_image_top_hover.md : ""; 
        let small_image_top_hover_sm = (!_.isEmpty(data.small_image_top_hover.sm) && data.small_image_top_hover.sm) ? data.small_image_top_hover.sm : ""; 
        let small_image_top_hover_xs = (!_.isEmpty(data.small_image_top_hover.xs) && data.small_image_top_hover.xs) ? data.small_image_top_hover.xs : ""; 
        let content_padding = (!_.isEmpty(data.content_padding) && data.content_padding) ? data.content_padding : "";
        let title_size = (!_.isEmpty(data.title_size) && data.title_size) ? data.title_size : 14;
        
        let title_align = data.title_align || "center";
        let title_padding = (!_.isEmpty(data.title_padding) && data.title_padding) ? data.title_padding : "";

        let overlay_color_n = data.overlay_color_n || "rgba(0, 0, 0, 0.5)";
        
        var title_line = data.title_line || 0;
        if(!_.isEmpty(data.title_line_w)){
            var title_line_w = (!_.isEmpty(data.title_line_w.md) && data.title_line_w.md) ? data.title_line_w.md : 55;
            var title_line_w_sm = (!_.isEmpty(data.title_line_w.sm) && data.title_line_w.sm) ? data.title_line_w.sm : "";
            var title_line_w_xs = (!_.isEmpty(data.title_line_w.xs) && data.title_line_w.xs) ? data.title_line_w.xs : "";
        }
        if(!_.isEmpty(data.title_line_h)){
            var title_line_h = (!_.isEmpty(data.title_line_h.md) && data.title_line_h.md) ? data.title_line_h.md : 2;
            var title_line_h_sm = (!_.isEmpty(data.title_line_h.sm) && data.title_line_h.sm) ? data.title_line_h.sm : "";
            var title_line_h_xs = (!_.isEmpty(data.title_line_h.xs) && data.title_line_h.xs) ? data.title_line_h.xs : "";
        }
        var title_line_color = data.title_line_color || "#FFFFFF";
        var title_line_align = data.title_line_align || "flex-start";
        var line_padding = (!_.isEmpty(data.line_padding) && data.line_padding) ? data.line_padding : "";

        var bg_s_hover = data.bg_s_hover || 0;

        #>
        <style>
            {{addonId}} .minBox {
                height: {{model_height}}px;
                background: #666666 url({{bg_image}}) no-repeat center;
                background-size: 100% {{bg_image_height}}px;
                overflow: hidden;
                position: relative;
                transition: all .4s;
            }
            {{addonId}} .minBox:hover {
                <# if(bg_s_hover == 1) { #>
                background-size: 110% {{bg_image_height * 1.1}}px;
                <# } #>
            }
            {{addonId}} .minBox_content {
                <# if(model_cover == 1) { #>
                    height: {{model_cover_height}}px;
                <# } else { #>
                    height: 100%;
                <# } #>
                width: 100%;
                background-color: {{ overlay_color_n }};
                transition: all .4s;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: absolute;
                left: 0;
                bottom: 0;
            }
            {{addonId}} .minBox_contentImg {
                margin-top: {{small_image_top}}px;
                transition: all .4s;
                <# if(model_cover != 1) { #>
                    min-height:55px;
                <# } #>
                width:55px;
                background:url({{small_image}}) no-repeat ;
                background-size: 100% 100%;
            }
            {{addonId}} .minBox_content_title {
                color: #fff;
                margin: 20px 0;
                padding:{{title_padding}};
                font-size:{{title_size.md || 14}}px;
                width: 100%;
                text-align: {{ title_align }};
            }
            {{addonId}} .minBox_content_title::before {
                content: "";
                width: 10px;
                border-bottom: 2px solid #fff;
                margin: 0 auto 20px;
                transition: all .4s;
            }
            {{addonId}} .minBox_content:hover {
                background-color: {{overlay_color}};
            }
            {{addonId}} .minBox_content_con {
                color: #fff;
                padding:{{content_padding}};
                opacity: 0;
                transition: all .4s;
            }
            {{addonId}} .minBox_content:hover .minBox_content_con {
                padding-top: 0;
                opacity: 1;
            }
            {{addonId}} .minBox_content:hover .minBox_contentImg {
                margin-top: {{small_image_top_hover_md}}px;
                background:url({{small_image_hover}}) no-repeat ;
                background-size: 100% 100%;
            }
            {{addonId}} .minBox_content:hover .minBox_content_title::before {
                margin-bottom: 0px;
                opacity: 0;
            }
            {{addonId}} .minBox_content .minBox_content_line {
                display: flex;
                align-items: center;
                justify-content: {{ title_line_align }};
                width: 100%;
                padding: {{ line_padding }};
            }
            {{addonId}} .minBox_content .minBox_content_line::after {
                content: "";
                width: {{title_line_w}}px;
                height: {{title_line_h}}px;
                background-color: {{title_line_color}};
            }
            @media (min-width: 768px) and (max-width: 991px) {
                {{addonId}}  .minBox {
                    height: {{model_height_sm}}px;
                    background: #666666 url({{bg_image}}) no-repeat center;
                    background-size: 100% {{bg_image_height_sm}}px;
                }
                {{addonId}} .minBox_content {
                    <# if(model_cover == 1) { #>
                        height: {{model_cover_height_sm}}px;
                    <# } #>
                }
                {{addonId}} .minBox_contentImg {
                    margin-top: {{small_image_top_sm}}px;
                    transition: all .4s;
                    <# if(model_cover != 1) { #>
                        min-height:55px;
                    <# } #>
                    width:55px;
                    background:url({{small_image}}) no-repeat ;
                    background-size: 100% 100%;
                }
                {{addonId}} .minBox_content_title {
                    font-size:{{title_size.sm || 14}}px;
                }
                {{addonId}} .minBox_content:hover .minBox_contentImg {
                    margin-top: {{small_image_top_hover_sm}}px;
                    background:url({{small_image_hover}}) no-repeat ;
                    background-size: 100% 100%;
                }
                {{addonId}} .minBox_content .minBox_content_line::after  {
                    width: {{title_line_w_sm}}px;
                    height: {{title_line_h_sm}}px;
                }
            }
            @media (max-width: 767px) {
                {{addonId}}  .minBox {
                    height: {{model_height_xs}}px;
                    background: #666666 url({{bg_image}}) no-repeat center;
                    background-size: 100% {{bg_image_height_xs}}px;
                }
                {{addonId}} .minBox_content {
                    <# if(model_cover == 1) { #>
                        height: {{model_cover_height_xs}}px;
                    <# } #>
                }
                {{addonId}} .minBox_contentImg {
                    margin-top: {{small_image_top_xs}}px;
                    transition: all .4s;
                    <# if(model_cover != 1) { #>
                        min-height:55px;
                    <# } #>
                    width:55px;
                    background:url({{small_image}}) no-repeat ;
                    background-size: 100% 100%;
                }
                {{addonId}} .minBox_content_title {
                    font-size:{{title_size.xs || 14}}px;
                }
                {{addonId}} .minBox_content:hover .minBox_contentImg {
                    margin-top: {{small_image_top_hover_xs}}px;
                    background:url({{small_image_hover}}) no-repeat ;
                    background-size: 100% 100%;
                }
                {{addonId}} .minBox_content .minBox_content_line::after {
                    width: {{title_line_w_xs}}px;
                    height: {{title_line_h_xs}}px;
                }
            }

        </style>
        <div  id="jwpf-addon-{{data.id}}">
        <div class="minBox ">
            <div class="minBox_content">
                <div class="minBox_contentImg" alt=""> </div>
                <div class="minBox_content_title">
                 {{data.title}}
                </div>
                <# if(title_line == 1) { #>
                <div class="minBox_content_line"></div>
                <# } #>
                <div class="minBox_content_con">
                    {{{text}}}
                </div>
            </div>
        </div>
        </div>
        ';
        return $output;
    }

}