<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
error_reporting(0);
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonShop_three_class extends JwpagefactoryAddons
{

	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id    = $_GET['site_id'] ?? 0;
		$layout_id  = $_GET['layout_id'] ?? 0;
        $catid_id  = $_GET['catid_id'] ?? 0;

		$addon_id = '#jwpf-addon-' . $this->addon->id;

		$settings = $this->addon->settings;
		$catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;

        $themb = (isset($settings->themb) && $settings->themb) ? $settings->themb : 'site01';

		//左侧菜单背景颜色
		$tab_list_bgcolor = (isset($settings->tab_list_bgcolor)) ? $settings->tab_list_bgcolor : 0;
		//左侧菜单背景透明度
		$transparent = (isset($settings->transparent)) ? $settings->transparent : 0;
		//左侧菜单滑过背景颜色
		$tab_active_bgcolor = (isset($settings->tab_active_bgcolor)) ? $settings->tab_active_bgcolor : 0;
		//左侧菜单字体大小
		$lift_list_a_size = (isset($settings->right_list_img)) ? $settings->right_list_img : 0;
		//左侧菜单文字颜色
		$tab_active_color = (isset($settings->tab_active_color)) ? $settings->tab_active_color : 0;
		//左侧列表宽度
		$tab_list_width = (isset($settings->tab_list_width)) ? $settings->tab_list_width : 0;
		//左侧列表高度
		$tab_list_height = (isset($settings->tab_list_height)) ? $settings->tab_list_height : 0;
		//右侧菜单背景颜色 （暂时不用）
//		$right_list_bgcolor = (isset($settings->right_list_bgcolor)) ? $settings->right_list_bgcolor : 0;
		//右侧三级菜单字体颜色
		$right_list_three = (isset($settings->right_list_three)) ? $settings->right_list_three : 0;
		//右侧二级菜单字体颜色
		$right_list_tow_after = (isset($settings->right_list_three_after)) ? $settings->right_list_three_after : 0;
		//右侧三级菜单鼠标滑过字体颜色
		$right_list_three_after = (isset($settings->right_list_three_after)) ? $settings->right_list_three_after : 0;
		//指定列表模板ID
		$detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
		//左侧文字边距
		$icon_m = (isset($settings->icon_m)) ? $settings->icon_m : 0;
		$icon_m = explode(" ", $icon_m);


        //布局2
        $second_bg = (isset($settings->second_bg)) ? $settings->second_bg : '#F6F6F6';
        $second_title = (isset($settings->second_title)) ? $settings->second_title : '产品展示';
        $second_title_fontsize = (isset($settings->second_title_fontsize)) ? $settings->second_title_fontsize : '16';
        $second_title_color = (isset($settings->second_title_color)) ? $settings->second_title_color : '#ba3606';
        $second_class_fontsize = (isset($settings->second_class_fontsize)) ? $settings->second_class_fontsize : '14';
        $second_checkbg_color = (isset($settings->second_checkbg_color)) ? $settings->second_checkbg_color : '#BA3606';
        $second_check_border = (isset($settings->second_check_border)) ? $settings->second_check_border : '8';
        $second_check_color = (isset($settings->second_check_color)) ? $settings->second_check_color : '#ba3606';
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : '0';
        $catordering = (isset($settings->catordering) && $settings->catordering) ? $settings->catordering : 'sortasc';
        $second_class_lineheight = (isset($settings->second_class_lineheight) && $settings->second_class_lineheight) ? $settings->second_class_lineheight : '50';

		$output = '';

        
        if($themb=='site02'){
            $output .= '<style>
                '.$addon_id.' a{text-decoration:none;color:inherit}
                '.$addon_id.' *,ul{margin:0;padding:0;}
                '.$addon_id.' li{
                        list-style-type:none
                    }
               '.$addon_id.' img{border:0; vertical-align:middle;display: inline-block;}

                '.$addon_id.' .clear{clear:both;}
                
                '.$addon_id.' .tishi{text-align: center;margin-top: 46px;}
                '.$addon_id.' .tishi p{font-size: '.$second_title_fontsize.'px;font-weight: bold;color: #504F4F;}
                '.$addon_id.' .tishi div{font-size: 24px;color: #808080;font-weight: bold;margin-top: 28px;}
                '.$addon_id.' .type_list{width: 100%;margin:0 auto;background: '. $second_bg.';border-top: 1px solid #F3F3F3;padding:0px 20px;}
                '.$addon_id.' .type_list .typelist{width: 100%;margin:0 auto;}
                '.$addon_id.' .type_list .typelist #toggle{font-size: '.$second_title_fontsize.'px;font-weight: bold;color: '.$second_title_color.';height: 58px;line-height: 58px;cursor: pointer;}
                '.$addon_id.' .type_list .typelist #toggle img{float: right;position: relative;left: 0;top:20px;}
                '.$addon_id.' .type-list{border-top: 1px solid #EAEAEA;}
                '.$addon_id.' .type-list dl{height: 58px;line-height: 58px;}
                '.$addon_id.' .type-list dl dt,.type-list dl dd{display: inline-block;float: left;color: #4D4D4D;font-size: '.$second_class_fontsize.'px;}
                '.$addon_id.' .type-list dl dt{font-weight: bold;color: #333333;line-height:'.$second_class_lineheight.'px;}
                '.$addon_id.' .type-list dl dd{margin-left: 32px;}
                '.$addon_id.' .type-list dl dd .active{color: '.$second_check_color.';font-weight: bold;}
                '.$addon_id.' .type-list dl dt a {
                    padding: 5px 12px;
                    border-radius: '.$second_check_border.'px;
                    color: #333;
                    font-weight: bold;
                }
                '.$addon_id.' .actt{
                    font-weight:bold;background:'.$second_checkbg_color.';color:#fff!important;
                }
            </style>
            ';

            $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
            require_once $article_helper;
            $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods',$site_id, 'type1', $type_start, $type_num, $catordering,1);

            $output .= '
                <div class="type_list">
                    <div class="typelist">
                        <div id="toggle">
                            '.$second_title.' <img src="https://oss.lcweb01.cn/joomla/20221111/3ecd367d6c2ad650d0aa5acd61941b12.png" width="18" alt="">
                            <div class="clear"></div>
                        </div>
                        <a href="javascript:;" id="top"></a>
                        <div class="type-list" style="border-bottom: 0px !important; display: block;">';
                            foreach ($categories_list as $key => $value) {
                                $output .= '<dl>
                                    <dt><a ';if($catid_id==$value['tag_id']){ $output .= 'class="actt"';} $output .= ' href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&detail=" . $value['tag_id'] . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $value['tag_id'] . '&goods_catid=' . $value['tag_id'], $absolute = true) . '">'.$value['title'].'：</a></dt>
                                    <div style="display: inline-block;float: left;line-height:'.$second_class_lineheight.'px;">';
                                        foreach ($value['sub'] as $key1 => $value1) {
                                            $output .= '
                                                <dd><a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&detail=" . $value1['tag_id'] . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $value1['tag_id'] . '&goods_catid=' . $value1['tag_id'], $absolute = true) . '" ';if($catid_id==$value1['tag_id']){ $output .= 'class="active"';} $output .= '>'.$value1['title'].'</a></dd><i></i>
                                            ';
                                        }
                                        
                                    $output .= '</div>
                                    <p class="clear"></p>
                                </dl>
                            <p class="clear"></p>';
                            }
                            $output .= '
                           
                        </div>
                    </div>
                </div>
                <script>
                    $(function(){
                        $("'.$addon_id.' i").eq(8).html("<br />")
                        $("'.$addon_id.' .type-list").show();
                        $("'.$addon_id.' #toggle").click(function(){
                            $("'.$addon_id.' .type-list").toggle("slow");
                        })
                        
                        $("'.$addon_id.' .active").parent().parent().parent().find("dt a").addClass("actt");

                    })
                </script>
            ';
        }else{

    		//页面css
    		$output .= '<style>
     		.m-categorys .m-prudaltc {
                display: none;
            }

            .m-idx .m-prudaltc {
                display: block;
            }

            .m-categorys.m_aretive .m-prudaltc {
                display: block;
            }

            .m-prudaltc {
            	/*opacity:' . ($transparent/10) . ';*/
                position: absolute;
                top: 40px;
                left: 0;
                width: ' . $tab_list_width . 'px;
                height: ' . $tab_list_height .'px;
                border-top: none;
                filter: progid:DXImageTransform.Microsoft.gradient(enabled=\'true\', startColorstr=\'#7F151515\', endColorstr=\'#7F151515\');
                background: ' . $tab_list_bgcolor . ';
            }

            .m-prudaltc > ul {
                padding: 8px 0;
            }

            .m-prudaltc ul li {
                list-style: none;
                *zoom: 1;
                border-left: 2px solid transparent;
                height: 29px;
                line-height: 29px;
            }

            .m-prudaltc .m-pctreedl {
                position: relative;
                padding-left: 2px;
                margin: 0 8px;
            }

            .m-prudaltc .m-pctreedl dt {
                font-weight: normal;
                margin-left: 1px;
                overflow: hidden;
                padding-left: 12px;
                position: relative;
            }

            .m-prudaltc .m-pctreedl dt a {
                font-size: ' . $lift_list_a_size . 'px;
                float: left;
                white-space: nowrap;
                color: ' . $tab_active_color . ';
                
                text-overflow: ellipsis;
                overflow: inherit;
                text-align: left;
                line-height: 29px;
            }

            

            /*li滑过样式*/
            .m-prudaltc li:hover {
                border-left: 2px solid #000;
                padding-left: 0;
                background: ' . $tab_active_bgcolor . ';
                z-index: 2;
            }

            .m-prudaltc li:hover a {
                color: #333;
            }

            .m-prudaltc .m-twotc {
                position: absolute;
                left:100%;
                top: 1px;
                width: 600px;
                height: 500px;
                border-right: 1px solid #f2f2f2;
                margin-left: 1px;
                border-bottom: 1px solid #f2f2f2;
                /*border-top: 1px solid #02a0e9;*/
                background: #fff;
                display: none;
                overflow-x: hidden;
                overflow-y: auto;
            }

            .m-prudaltc .m-twolt {
                float: left;
                padding: 5px 27px 13px 5px;
                width: 758px;
                overflow: hidden;
                min-height: 447px;
            }

            .m-prudaltc li:hover .m-twotc,
            .m-prudaltc li:hover .line {
                display: block;
            }

            .m-prudaltc .m-twolt dl {
                padding: 3px 0;
                overflow: hidden;
            }

            .m-prudaltc .m-twolt dl.first {
                border: none;
            }

            .m-prudaltc .m-twolt dt {
                float: left;
                width: 110px;
                height: 22px;
                overflow: hidden;
                margin-right: 8px;
                position: relative;
                font-weight: 700;
                font-size: 12px;
                line-height: 22px;
            }

            .m-prudaltc .m-twolt dt a {
                font-size: 12px;
                font-weight: bold;
                width: 180px;
                padding-left: 20px;
                display: block;
                text-align: left;
                height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            

            .m-prudaltc .m-twolt dd {
                float: left;
                width: 595px;
                overflow: hidden;
                /*border-bottom: 1px solid #EDEDED;*/
                padding-bottom: 5px;
            }

            .m-prudaltc .m-twolt dl:last-child dd {
                border: none;
            }
    		.m-prudaltc dt p {
    			float:right;
    			margin:0;
    			padding:0
    			overflow:hidden;
    		}
            .m-prudaltc .m-twolt dd a {
                float: left;
                white-space: nowrap;
                line-height: 18px;
                height: 18px;
                /*     margin-bottom: 9px;
               */
                padding: 0 9px;
                color: ' . $right_list_three . ';
                font-size: 12px;
                margin-top: 3px;
                border-left: 1px solid #ededed;
            }

            .m-prudaltc .m-twolt dd a:hover {
                color: ' . $right_list_three_after . ';
            }

            .m-prudaltc .m-twobrand {
                overflow: hidden;
            }

            .m-prudaltc .m-twobrand span {
                float: left;
                width: 89px;
                height: 44px;
                margin-bottom: 2px;
                margin-right: 2px;
                overflow: hidden;
            }

            .m-prudaltc .m-twobrand img {
                margin-bottom: 2px;
                /** height: 44px; **/
                -webkit-transition: all 0.3s;
                transition: all 0.3s;
                width: 100%;
            }

            .m-prudaltc .m-twort {
                position: relative;
                z-index: 2;
                float: right;
                width: 202px;
                padding: 20px 10px 0;
                margin-left: -1px;
                min-height: 500px;
                background: #f2f2f2;
            }

            .m-prudaltc .m-twobrand span:hover img {
                -webkit-transform: translatex(-3px);
                transform: translatex(-3px);
            }

            .m-prudaltc .m-twbrad {
                -webkit-transition: all 0.3s;
                transition: all 0.3s;
            }

            .m-prudaltc .m-twbrad img {
                width: 100%;
                margin-bottom: 10px;
            }

            .m-prudaltc .m-twbrad:hover {
                -webkit-transform: translatex(-5px);
                transform: translatex(-5px);
            }

     		</style>';


    		//取出该公司数据
    		$categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
    		require_once $categorie_helper;
    		$item = JwpagefactoryHelperCategories::GetCategoriesthree('com_goods', $site_id, $detail_page_id, $layout_id, $company_id);
    		//数据转为多维数组
    		$item = $this->subTree($item);


    		//页面结构
    		$output .= '<div class="m-prudaltc">';
    		$output .= '<ul>';
    		foreach ($item as $k => $v)
    		{
    			$output .= '<li>'; //li是一级数据
    			$output .= '<dl class="m-pctreedl">';
    			$output .= '                <dt><a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&detail=" . $v['tag_id'] . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid . '&catid=' . $catid, $absolute = true) . '"> ' . $v['title'] . ' </a><p> > </p> </dt>';
    			$output .= '                <dd class="line"></dd>';
    			$output .= '</dl>';
    			$output .= '<div class="m-twotc">';
    			$output .= '         <div class="m-navemerge">';
    			$output .= '              <div class="m-navemergebg"></div>';
    			$output .= '               <div class="m-twolt">';
    			if (@count($v['sub']) != 0)
    			{
    				foreach ($v['sub'] as $k_i1 => $v_i1)
    				{
    					$output .= '                    <dl>'; //dl是二级数据
    					$output .= '                         <dt><a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&detail=" . $v_i1['tag_id'] . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid . '&catid=' . $catid, $absolute = true) . '"> ' . $v_i1['title'] . ' </a> <i> | </i></dt>';
    					$output .= '                          <dd>'; //dd是三级数据
    					if (@count($v_i1['sub']) != 0)
    					{
    						foreach ($v_i1['sub'] as $k_i2 =>$v_i2){
    							$output .= '                          <a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&detail=" . $v_i2['tag_id'] . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid . '&catid=' . $catid, $absolute = true) . '">'.$v_i2['title'].'</a>'; //dd是三级数据
    						}
    					}
    					$output .= '                          </dd>'; //dd是三级数据
    					$output .= '                       </dl>';
    				}
    			}

    			$output .= '                    </div>';
    			$output .= '                    <div class="m-twort">';
    			$output .= '                        <div class="m-twobrand">';
    			$output .= '                            <div class="m-twobrandbrand"><!----></div>';
    			$output .= '                            <div class="m-classifybanner"></div>';
    			$output .= '                        </div>';
    			$output .= '                    </div>';
    			$output .= '                </div>';
    			$output .= '            </div>';
    			$output .= '        </li>';
    		}

    		$output .= '    </ul>';
    		$output .= '</div>';

        }


		return $output;

	}



	//数组多层级 递归
	public function subTree($data, $pid = 1, $deep = 0)
	{   //用来存放数据
		$arr = [];
		//遍历数据库中的字段
		foreach ($data as $val)
		{
			//判断字段pid相等时放行
			if ($pid == $val['parent_id'])
			{
				//不同的层级
				$val['deep'] = $deep;
				//递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
				$val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
				//如果遇到pid==本条id时，将其存入数组
				$arr[] = $val;
			}
		}

		//返回数组
		return $arr;
	}

	public static function getTemplate(){
		$output='
		<div>
           <div style="margin:100px">此位置由产品三级分类菜单在编辑器中占位使用,不影响预览页面结构</div>
        </div>';
		return $output;



	}
}