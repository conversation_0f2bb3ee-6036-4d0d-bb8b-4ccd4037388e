<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type'       => 'repeatable',
        'addon_name' => 'swiper_search2',
        'title'      => JText::_('动态图文轮播组件'),
        'desc'       => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
        'category'   => '轮播',
        'attr'       => array(
            'general' => array(
                'admin_label'     => array(
                    'type'  => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std'   => '',
                ),
                'jw_search2_item' => array(
                    'title' => '内容列表',
                    'std'   => array(
                        array(
                            'title'   => '专属平台流量',
                            'content' => '基于中文搜索引擎百度搜索',
                            'bg_img'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_search2/assets/' . 'img/f_pic14.png',
                        ),
                        array(
                            'title'   => '关键词搜索',
                            'content' => '通过关键词触发展现广告，准确触达客户',
                            'bg_img'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_search2/assets/' . 'img/f_pic15.png',
                        ),
                        array(
                            'title'   => '为品牌赋能',
                            'content' => '广告内容深度触达，提升品牌曝光度和品牌美誉度',
                            'bg_img'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_search2/assets/' . 'img/f_pic16.png',
                        ),
                    ),
                    'attr'  => array(
                        'title'   => array(
                            'type'  => 'text',
                            'title' => '标题',
                            'std'   => '专属平台流量',

                        ),
                        'content' => array(
                            'type'  => 'text',
                            'title' => '介绍',
                            'std'   => '基于中文搜索引擎百度搜索',
                        ),
                        'bg_img'  => array(
                            'type'   => 'media',
                            'title'  => '图标图',
                            'format' => 'image',
                            'std'    => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_search2/assets/' . 'img/f_pic14.png',
                        ),
                    ),
                ),
                'height' => array(
                    'type' => 'slider',
                    'title' => '模块高',
                    'responsive' => true,
                    'std' => array(
                        'md' => 446,
                        'sm' => 446,
                        'xs' => 446
                    ),
                    'max'=>'1000'
                ),
            ),
        ),
    )
);
