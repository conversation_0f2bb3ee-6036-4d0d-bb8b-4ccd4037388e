<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');
$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'full_page03',
        'title' => '智能全屏滚动',
        'desc' => '全屏滚动网页(支持更改文字/图片，请先搭好页面最后在拖该插件)',
        'category' => '全屏滚动网页',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),

                'part03' => array(
                    'type' => 'separator',
                    'title' => '适配配置'
                ),
                'pc_hide' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏在pc端',
                    'std' => 0,
                ),
                'pad_hide' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏在平板',
                    'std' => 0,
                ),
                'wap_hide' => array(
                    'type' => 'checkbox',
                    'title' => '隐藏在手机',
                    'std' => 0,
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '切换项配置'
                ),
                'full_page' => array(
                    'type' => 'buttons',
                    'title' => '全屏控制器配置（切换效果预览页查看）',
                    'std' => 'pagination',
                    'values' => array(
                        array(
                            'label' => '分页控制器',
                            'value' => 'pagination'
                        ),
                        array(
                            'label' => '箭头控制器',
                            'value' => 'navigation'
                        ),
                    ),
                    'tabs' => true,
                ),
                'pagination_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭分页控制器',
                    'std' => 0,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                    ),
                ),
                'pagination_fraction_hide' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启分式指示器',
                    'std' => 0,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                    ),
                ),
                'pagination_fraction_color' => array(
                    'type' => 'color',
                    'title' => '分式指示器字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_fraction_hide', '=', 1),
                    ),
                ),
                'pagination_fraction_font_size' => array(
                    'type' => 'slider',
                    'title' => '分式指示器字号',
                    'std' => 18,
                    'max' => 50,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_fraction_hide', '=', 1),
                    ),
                ),
                'pagination_fraction_left' => array(
                    'type' => 'slider',
                    'title' => '分式指示器水平距离',
                    'std' => 35,
                    'max' => 200,
                    'min' => -200,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_fraction_hide', '=', 1),
                    ),
                ),
                'pagination_fraction_top' => array(
                    'type' => 'slider',
                    'title' => '分式指示器垂直位移（%）',
                    'std' => -24,
                    'min' => -100,
                    'max' => 100,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_fraction_hide', '=', 1),
                    ),
                ),
                'editTotal' => array(
                    'type' => 'checkbox',
                    'title' => '编辑总页数',
                    'std' => 0,
                    'depends' => array(
                        array('pagination_fraction_hide','=',1),
                    )
                ),
                'totalPage'=> array(
                    'type' => 'slider',
                    'title' => '总页数',
                    'std' => '',
                    'max' => 100,
                    'depends'=> array(
                        array('pagination_fraction_hide','=',1),
                        array('editTotal','=',1)
                    )
                ),
                'pagination_r' => array(
                    'type' => 'slider',
                    'title' => '轮播点右边距（px）',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 16,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                    )
                ),
                'pagination_m' => array(
                    'type' => 'slider',
                    'title' => '轮播点间距（px）',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 10,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                    )
                ),
                'pagination_tab' => array(
                    'type' => 'buttons',
                    'title' => '分页器样式配置（预览页查看）',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                    ),
                    'tabs' => true,
                ),
                'pagination_w' => array(
                    'type' => 'slider',
                    'title' => '轮播点宽度',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'normal'),
                    )
                ),
                'pagination_h' => array(
                    'type' => 'slider',
                    'title' => '轮播点高度',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'normal'),
                    )
                ),
                'pagination_color' => array(
                    'type' => 'color',
                    'title' => '轮播点颜色',
                    'desc' => '',
                    'std' => 'rgba(255, 255, 255, 0.3)',
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'normal'),
                    )
                ),
                'pagination_color_a' => array(
                    'type' => 'color',
                    'title' => '轮播点颜色',
                    'desc' => '',
                    'std' => 'rgba(255, 255, 255, 0.8)',
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'active'),
                    )
                ),
                'pagination_bColor_a' => array(
                    'type' => 'color',
                    'title' => '轮播点边框颜色',
                    'desc' => '',
                    'std' => 'rgba(255, 255, 255, 0.2)',
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'active'),
                    )
                ),
                'pagination_b_w' => array(
                    'type' => 'slider',
                    'title' => '轮播点边框宽度',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 3,
                    'depends' => array(
                        array('full_page', '=', 'pagination'),
                        array('pagination_hide', '!=', 1),
                        array('pagination_tab', '=', 'active'),
                    )
                ),

                'change_pagination' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启特殊颜色',
                    'std' => 0,
                ),
                'pagenation_items' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'pagination_color' => 'rgba(255, 255, 255, 0.3)',
                            'pagination_color_a' => 'rgba(255, 255, 255, 0.8)',
                            'pagination_bColor_a' => 'rgba(255, 255, 255, 0.2)',
                            'pagination_b_w'=>3
                        ),
                    ),
                    'attr' => array(
                        'pagination_color' => array(
                            'type' => 'color',
                            'title' => '轮播点颜色',
                            'desc' => '',
                            'std' => 'rgba(255, 255, 255, 0.3)',
                        ),
                        'pagination_color_a' => array(
                            'type' => 'color',
                            'title' => '选中轮播点颜色',
                            'desc' => '',
                            'std' => 'rgba(255, 255, 255, 0.8)',
                        ),
                        'pagination_bColor_a' => array(
                            'type' => 'color',
                            'title' => '选中轮播点边框颜色',
                            'desc' => '',
                            'std' => 'rgba(255, 255, 255, 0.2)',
                        ),
                        'pagination_b_w' => array(
                            'type' => 'slider',
                            'title' => '选中轮播点边框宽度',
                            'desc' => '',
                            'max' => 1000,
                            'min' => 0,
                            'std' => 3,
                        ),
                        'pagination_fraction_color' => array(
                            'type' => 'color',
                            'title' => '分式指示器字体颜色',
                            'std' => '#fff',
                        ),
                    ),
                    'depends' => array(
                        array('change_pagination','=',1)
                    )
                ),
                'navigation_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭箭头控制器',
                    'std' => 0,
                    'depends' => array(
                        array('full_page', '=', 'navigation'),
                    ),
                ),
                'navigation_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '下翻页按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211207/a6cc6a4bda51f3259dffc9c4aa3dd9bd.png',
                    'depends' => array(
                        array('full_page', '=', 'navigation'),
                        array('navigation_hide', '!=', 1),
                    ),
                ),
                'navigation_img_w' => array(
                    'type' => 'slider',
                    'title' => '下翻页按钮宽度',
                    'desc' => '',
                    'max' => 1000,
                    'min' => 10,
                    'std' => 30,
                    'depends' => array(
                        array('full_page', '=', 'navigation'),
                        array('navigation_hide', '!=', 1),
                    )
                ),
                'part04' => array(
                    'type' => 'separator',
                    'title' => '其他配置'
                ),
                'section_page' => array(
                    'type' => 'buttons',
                    'title' => '网页高级设置（预览页查看）',
                    'std' => 'navigation',
                    'values' => array(
                        array(
                            'label' => '导航（即页面头部区块）',
                            'value' => 'navigation'
                        ),
                        array(
                            'label' => '底部（即页面底部区块）',
                            'value' => 'footer'
                        ),
                    ),
                    'tabs' => true,
                ),
                'first_include'=> array(
                    'type' => 'checkbox',
                    'title' => '导航计入轮播',
                    'std' => 1,
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                    )
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '导航区块配置(用于导航顶端固定)',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1)
                    )
                ),
                'nav_section_id' => array(
                    'type' => 'text',
                    'title' => '绑定导航区块章节id',
                    'desc' => '需要在导航所在的区块设置章节id，这里输入导航所在区块的章节id',
                    'std' => '',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1)
                    )
                ),
                'nav_section_bg' => array(
                    'type' => 'color',
                    'title' => '导航区块背景颜色(导航不计入轮播之后需在此配置)',
                    'desc' => '',
                    'std' => 'rgba(0, 0, 0, 0.6)',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1)
                    )
                ),
                'nav_section_p' => array(
                    'type' => 'padding',
                    'title' => '导航区块内边距(请勿设置负值且单位为px，导航不计入轮播之后需在此配置)',
                    'desc' => '',
                    'std' => '30px 0 30px 0',
                    'responsive'=> true,
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1)
                    )
                ),
                'change_nav' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航变色',
                    'desc' => '此功能用于每屏展示不同的导航区块',
                    'std' => '0',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1)
                    )
                ),
                'nav_section_id2' => array(
                    'type' => 'text',
                    'title' => '页面下滑需显示的导航区块章节id',
                    'desc' => '需要在导航所在的区块设置章节id，这里输入导航所在区块的章节id',
                    'std' => '',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1),
                        array('change_nav','=','1')
                    )
                ),
                'nav_section_bg2' => array(
                    'type' => 'color',
                    'title' => '页面下滑导航区块背景颜色，开启导航变色后需在此配置',
                    'desc' => '',
                    'std' => 'rgba(0, 0, 0, 0.6)',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1),
                        array('change_nav','=','1')
                    )
                ),
                'interval_change_nav' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航间隔切换（网页不同屏显示不同的导航样式）',
                    'desc' => '按照配置的轮播项显示不同导航',
                    'std' => '0',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1),
                        array('change_nav','=','1')
                    )
                ),
                'nav_section_interval1' => array(
                    'type' => 'text',
                    'title' => '当轮播项为以下配置时显示正常状态下的导航（请用英文逗号隔开）',
                    'desc' => '请用英文逗号隔开',
                    'std' => '1,3',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1),
                        array('change_nav','=','1'),
                        array('interval_change_nav','=','1'),
                    )
                ),
                'nav_section_interval2' => array(
                    'type' => 'text',
                    'title' => '当轮播项为以下配置时显示下滑状态下的导航',
                    'desc' => '请用英文逗号隔开',
                    'std' => '2,4',
                    'depends' => array(
                        array('section_page', '=', 'navigation'),
                        array('first_include', '!=', 1),
                        array('change_nav','=','1'),
                        array('interval_change_nav','=','1'),
                    )
                ),
                'last_include'=> array(
                    'type' => 'checkbox',
                    'title' => '底部计入轮播（底部不计入轮播，须同时配置底部区块id以及去掉底部后轮播的尾屏区块id）',
                    'std' => 1,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                    )
                ),
                'footer_section' => array(
                    'type' => 'buttons',
                    'title' => '相关区块配置',
                    'std' => 'footer',
                    'values' => array(
                        array(
                            'label' => '底部区块',
                            'value' => 'footer'
                        ),
                        array(
                            'label' => '轮播尾屏区块',
                            'value' => 'swiper_last'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('last_include', '!=', 1)
                    )
                ),
                'set_footer_section_id' => array(
                    'type' => 'checkbox',
                    'title' => '设置底部区块id(不设置默认页面最后一个区块，会包含隐藏在手机端的区块)',
                    'std' => 0,
                    'depends'=> array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('last_include','=',0)
                    )
                ),
                'footer_section_id' => array(
                    'type' => 'text',
                    'title' => '底部区块id（建议设置）',
                    'std' => 0,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_bottom' => array(
                    'type' => 'slider',
                    'title' => '底部区块下边距',
                    'std' => 0,
                    'max'=>1000,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_padding' => array(
                    'type' => 'padding',
                    'title' => '底部区块内边距(底部不计入轮播需再次设置)',
                    'std' => '50px 0 50px 0',
                    'responsive' => true,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_bg_color' => array(
                    'type' => 'color',
                    'title' => '底部区块背景色(底部不计入轮播需再次设置)',
                    'std' => '',
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_bg_img' => array(
                    'type' => 'media',
                    'title' => '底部区块背景图(底部不计入轮播需再次设置)',
                    'std' => '',
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_poistion' => array(
                    'title' => '底部区块定位',
                    'std' => 'fixed',
                    'type' => 'select',
                    'values' => array(
                        'fixed' => '固定定位',
                        'relative' => '相对定位',
                        'absolute' => '绝对定位',
                        'static' => '不定位'
                    ),
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_margin' => array(
                    'title' => '底部区块边距（不定位为外边距，其余为定位值）',
                    'std' => 'auto 0 0 0',
                    'type' => 'margin',
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_height' => array(
                    'title' => '底部区块高度',
                    'std' => 'auto',
                    'type' => 'slider',
                    'responsive' => true,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0)
                    )
                ),
                'footer_z_index' => array(
                    'type' => 'slider',
                    'title' => '底部区块层级',
                    'std' => 0,
                    'max' => 2000,
                    'depends' => array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'footer'),
                        array('set_footer_section_id','=',1),
                        array('last_include','=',0),
                        array('footer_poistion','!=',"static")
                    )
                ),
                'last_section_id' => array(
                    'type' => 'text',
                    'title' => '轮播尾屏区块id',
                    'std' => 0,
                    'depends'=> array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'swiper_last'),
                        array('last_include', '=', 0)
                    )
                ),
                'up_height_offset'=> array(
                    'type' => 'slider',
                    'title' => '轮播尾屏上移距离偏移',
                    'std' => 0,
                    'max' => 1000,
                    'min' => -1000,
                    'depends'=> array(
                        array('section_page', '=', 'footer'),
                        array('footer_section', '=', 'swiper_last'),
                        array('last_include','=',0)
                    )
                ),
                'part05' => array(
                    'type' => 'separator',
                    'title' => '高级配置'
                ),
                'has_animate'=> array(
                    'type' => 'checkbox',
                    'title' => '是否含有动画（请将pc端区块、平板区块、手机端区块各放到一起，并按照pc、平板、手机的顺序铺页面）',
                    'desc' => '如果在预览页面发现有插件不显示，有可能是插件中包含有动画，请开启“是否包含动画”开关并确保pc、平板、手机端端区块是各自放置在一起，并按照pc、平板、手机的顺序铺页面的',
                    'std' => 1,
                ),
                'need_resize_listener'=> array(
                    'type' => 'checkbox',
                    'title' => '是否需要屏幕大小改变后刷新页面',
                    'desc' => '',
                    'std' => 1,
                ),
            )
        )
    )
);
