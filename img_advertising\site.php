<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-07 09:33:20
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-06-09 13:37:34
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\img_advertising\site.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonImg_advertising extends JwpagefactoryAddons
{
    public function render(){
        $settings = $this->addon->settings;
        $advertising_style = (isset($settings->advertising_style) && $settings->advertising_style) ? $settings->advertising_style : 'type1';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;

        $output = '';

        if($advertising_style === 'type1'){
            $style1_img = (isset($settings->style1_img) && $settings->style1_img) ? $settings->style1_img : '/components/com_jwpagefactory/addons/img_advertising/assets/img/bg-style1.png';
            $need_link = (isset($settings->need_link) && ($settings->need_link || $settings->need_link == 0)) ? $settings->need_link : 1;
            $link_page_select = (isset($settings->link_page_select) && $settings->link_page_select) ? $settings->link_page_select : 'inner';
            $detail_page_url = (isset($settings->detail_page_url) && $settings->detail_page_url) ? $settings->detail_page_url : '';
            $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
            $target = (isset($settings->target) && $settings->target) ? $settings->target : '_self';

            $server_name = $_SERVER['SERVER_NAME'];
            if($need_link === 1){
                if($link_page_select == 'outer'){
                    $output.='<a class="advertising-main" href=\''.$detail_page_url.'\' target="'.$target.'">';
                }else{
                    $url = '/index.php/component/jwpagefactory/?view=page';
                    $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0).'&id='.base64_encode($detail_page_id);
                    
                    $output.='<a class="advertising-main" href=\'http://'.$url.'\' target="'.$target.'">';
                }
            }else{
                $output.='<a class="advertising-main" href="javascript:;">';
            }
                $output.='<img id="advertising-img" src=\''.$style1_img.'\'>
                <img class="close-button" src=\'/components/com_jwpagefactory/addons/img_advertising/assets/img/close.png\' onClick="closeAdertising(this);return false;">
            </a>';
        }

        return $output;
    }
    public function css(){
        $addonId = "#jwpf-addon-".$this->addon->id;
        $settings = $this->addon->settings;
        $advertising_style = (isset($settings->advertising_style) && $settings->advertising_style) ? $settings->advertising_style : 'type1';

        $output = '';
        
        if($advertising_style === 'type1'){
            $style1_fixed_img_size = (isset($settings->style1_fixed_img_size) && $settings->style1_fixed_img_size) ? $settings->style1_fixed_img_size : 0;
            $style1_img_width = (isset($settings->style1_img_width) && $settings->style1_img_width) ? $settings->style1_img_width : 340;
            $style1_img_height = (isset($settings->style1_img_height) && $settings->style1_img_height) ? $settings->style1_img_height : 160;
            $style1_img_style = (isset($settings->style1_img_style) && $settings->style1_img_style) ? $settings->style1_img_style : 'cover';
            $style1_img_opacity = (isset($settings->style1_img_opacity) && $settings->style1_img_opacity) ? $settings->style1_img_opacity : 80;
            $style1_img_x = (isset($settings->style1_img_x) && $settings->style1_img_x) ? $settings->style1_img_x : 0;
            $style1_img_y = (isset($settings->style1_img_y) && $settings->style1_img_y) ? $settings->style1_img_y : 0;

            $output.=$addonId.' .advertising-main{
                position: fixed;
                top: '.$style1_img_y.'px;
                left: '.$style1_img_x.'px;
                cursor: pointer;
                display: block;
            }
            '.$addonId.' #advertising-img{';
                if($style1_fixed_img_size === 1){
                    $output.='width: '.$style1_img_width.'px;
                    height: '.$style1_img_height.'px;
                    object-fit: '.$style1_img_style.';';
                }
                $output.='opacity: '.($style1_img_opacity / 100).';
            }
            '.$addonId.' .close-button{
                position: absolute;
                right: 0;
                top: 0;
                transform: translate(100%, -100%);
                width: 20px;
                height: 20px;
            }';
        }

        return $output;
    }
    public function js(){
        $addonId = "#jwpf-addon-".$this->addon->id;
        $settings = $this->addon->settings;
        $advertising_style = (isset($settings->advertising_style) && $settings->advertising_style) ? $settings->advertising_style : 'type1';

        $output = '';
        
        if($advertising_style === 'type1'){
            $style1_fixed_img_size = (isset($settings->style1_fixed_img_size) && $settings->style1_fixed_img_size) ? $settings->style1_fixed_img_size : 0;
            $style1_img_width = (isset($settings->style1_img_width) && $settings->style1_img_width) ? $settings->style1_img_width : 340;
            $style1_img_height = (isset($settings->style1_img_height) && $settings->style1_img_height) ? $settings->style1_img_height : 160;
            $style1_img_move_speed = (isset($settings->style1_img_move_speed) && $settings->style1_img_move_speed) ? $settings->style1_img_move_speed : 50;
            $style1_img_offset_x = (isset($settings->style1_img_offset_x) && $settings->style1_img_offset_x) ? $settings->style1_img_offset_x : 2;
            $style1_img_offset_y = (isset($settings->style1_img_offset_y) && $settings->style1_img_offset_y) ? $settings->style1_img_offset_y : 2;
            $style1_img_x = (isset($settings->style1_img_x) && $settings->style1_img_x) ? $settings->style1_img_x : 0;
            $style1_img_y = (isset($settings->style1_img_y) && $settings->style1_img_y) ? $settings->style1_img_y : 0;

            $output.='var sobj = null;
            var close'.$this->addon->id.' = false;
            $(window).on("load",function(){
                var main = document.querySelector("'.$addonId.' .advertising-main");
                var imgElement = document.querySelector("'.$addonId.' .advertising-main #advertising-img");
                sy = '.$style1_img_y.';
                speedy = '.$style1_img_offset_y.';
                sx = '.$style1_img_x.';
                speedx = '.$style1_img_offset_x.';';
                if($style1_fixed_img_size === 0){
                    $output.='imgh = imgElement.clientHeight;
                    imgw = imgElement.clientWidth;';
                }else{
                    $output.='imgh = '.$style1_img_height.';
                    imgw = '.$style1_img_width.';';
                }
                $output.='winh = document.documentElement.clientHeight;
                winw = document.documentElement.clientWidth;
                function start() {
                    sobj = setInterval(function() {
                        sy += speedy;
                        sx += speedx;
                        //y轴运动
                        if (sy <= 0) {
                        speedy = -speedy;
                        }
                        if (sy >= winh - imgh) {
                        speedy = -speedy;
                        sy = winh - imgh;
                        }
                        //x轴运动
                        if (sx <= 0) {
                        speedx = -speedx;
                        }
                        if (sx >= winw - imgw) {
                        speedx = -speedx;
                        sx = winw - imgw;
                        }
                        main.style.top = sy + "px";
                        main.style.left = sx + "px";
                    }, '.$style1_img_move_speed.')
                }
                start();
                main.onmouseover= function() {
                    clearInterval(sobj);
                }
                main.onmouseout= function() {
                    if(!close'.$this->addon->id.'){
                        start();
                    }
                }
            })
            function closeAdertising(that){
                var main = document.querySelector("'.$addonId.' .advertising-main");
                main.style.display = "none";
                close'.$this->addon->id.' = true;
                clearInterval(sobj);
            }';
        }

        return $output;
    }
    public static function getTemplate(){
        $output = '<style>
            {{addonId}} .tips {
                height: 40px;
                line-height: 40px;
                margin-bottom: 30px;
                background: rgba(255,141,115,0.88);
                box-sizing: border-box;
                padding: 0 10px;
            }
        </style>
        <div class="tips">本图片仅为布局样式，请在预览页面中查看该插件动画效果</div>
        <# if(data.advertising_style === "type1"){
            let style1_img = data.style1_img ? data.style1_img : "https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg";
            let style1_fixed_img_size = data.style1_fixed_img_size || 0;
            let style1_img_width = data.style1_img_width || 340;
            let style1_img_height = data.style1_img_height || 160;
            let style1_img_style = data.style1_img_style || "cover";
            let style1_img_opacity = data.style1_img_opacity || 80;
            let style1_img_x = data.style1_img_x || 0;
            let style1_img_y = data.style1_img_y || 0;
            let addonId = "#jwpf-addon-"+data.id;
            console.log(style1_img_y); #>
            
            <style>
                {{addonId}} .tips {
                    height: 40px;
                    line-height: 40px;
                    margin-bottom: 30px;
                    background: rgba(255,141,115,0.88);
                    box-sizing: border-box;
                    padding: 0 10px;
                }
                {{addonId}} .advertising-main{
                    position: fixed;
                    top: {{style1_img_y}}px;
                    left: {{style1_img_x}}px;
                    cursor: pointer;
                    display: block;
                }
                {{addonId}} #advertising-img{
                    <# if(style1_fixed_img_size === 1){ #>
                        width: {{style1_img_width}}px;
                        height: {{style1_img_height}}px;
                        object-fit: {{style1_img_style}};
                    <# } #>
                    opacity: {{style1_img_opacity / 100}};
                }
                {{addonId}} .close-button{
                    position: absolute;
                    right: 0;
                    top: 0;
                    transform: translate(100%, -100%);
                    width: 20px;
                    height: 20px;
                }
            </style>
            <a class="advertising-main" href="javascript:;">
                <img id="advertising-img" src=\'{{style1_img}}\'>
                <img class="close-button" src=\'/components/com_jwpagefactory/addons/img_advertising/assets/img/close.png\' onClick="closeAdertising(this)">
            </a>
        <# } #>';
        return $output;
    }
}