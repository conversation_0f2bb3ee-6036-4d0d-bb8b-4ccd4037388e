<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'progress_swiper',
        'title' => JText::_('进度条轮播'),
        'desc' => JText::_('进度条轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'jw_image_carousel_item' => array(
	                'title' => JText::_('轮播项'),
	                'std' => array(
		                array(
			                'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
		                ),
		                array(
			                'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
		                ),
		                array(
			                'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
		                ),
	                ),
	                'attr' => array(
		                'title' => array(
			                'type' => 'text',
			                'title' => '轮播项标签名',
			                'desc' => '轮播项标签名',
			                'std' => 'Carousel Item Tittle',
		                ),
		                'image_carousel_img' => array(
			                'type' => 'media',
			                'title' => '轮播项图片',
			                'desc' => '轮播项图片',
			                'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
		                ),
		                'is_link' => array(
			                'type' => 'checkbox',
			                'title' => JText::_('是否为该项添加链接'),
			                'desc' => JText::_('是否为该项添加链接'),
			                'std' => '0',
		                ),
		                'image_carousel_img_link' => array(
			                'type' => 'media',
			                'title' => '图片链接',
			                'desc' => '图片链接',
			                'placeholder' => 'http://',
			                'hide_preview' => true,
			                'std' => '',
			                'depends' => array(
				                array('is_link', '=', 1)
			                )
		                ),
		                'link_open_new_window' => array(
			                'type' => 'checkbox',
			                'title' => JText::_('在新标签页中打开'),
			                'std' => 0,
			                'depends' => array(array('image_carousel_img_link', '!=', '')),
		                ),
	                ),
                ),
	            // Arrow style
                'arrow_style' => array(
	                'type' => 'buttons',
	                'title' => JText::_('翻页按钮风格'),
	                'std' => 'normal_arrow',
	                'values' => array(
		                array(
			                'label' => '正常状态',
			                'value' => 'normal_arrow'
		                ),
		                array(
			                'label' => '鼠标移入',
			                'value' => 'hover_arrow'
		                )
	                ),
                ),
                'arrow_color' => array(
	                'type' => 'color',
	                'title' => JText::_('箭头颜色'),
	                'std' => '#C2C2C2FF',
	                'depends' => array(
		                array('arrow_style', '=', 'normal_arrow'),
	                )
                ),
	            //Arrow hover
                'arrow_hover_color' => array(
	                'type' => 'color',
	                'title' => JText::_('箭头颜色'),
	                'std' => '#BB7F89FF',
	                'depends' => array(
		                array('arrow_style', '=', 'hover_arrow'),
	                )
                ),
            ),
        )
    )
);