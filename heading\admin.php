<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'heading',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADING'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADING_DESC'),
		'category' => '常用插件',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => '',
				),

				'title' => array(
					'type' => 'textarea',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => '这里是标题'
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => 'h1',
						'h2' => 'h2',
						'h3' => 'h3',
						'h4' => 'h4',
						'h5' => 'h5',
						'h6' => 'h6',
						'p' => 'p',
						'span' => 'span',
						'div' => 'div'
					),
					'std' => 'h2'
				),

				'use_link' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADING_USE_LINK'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADING_USE_LINK_DESC'),
					'std' => 0
				),

//				'title_link' => array(
//					'type' => 'media',
//					'format' => 'attachment',
//					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
//					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_DESC'),
//					'placeholder' => 'http://',
//					'std' => '',
//					'hide_preview' => true,
//					'depends' => array('use_link' => 1)
//				),
				'tz_page_type' => array(
					'type' => 'select',
					'title' => JText::_('跳转方式'),
					'desc' => JText::_('跳转方式'),
					'values' => array(
						'Internal_pages' => JText::_('内部页面'),
						'external_links' => JText::_('外部链接'),
					),
					'std' => 'Internal_pages',
					'depends' => array(
						array('use_link' , '=',  1),
					),
				),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
					'depends' => array(
						array('tz_page_type', '=', 'Internal_pages'),
						array('use_link' , '=',  1),
					),
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),
				'detail_page' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(
						array('tz_page_type', '=', 'external_links'),
						array('use_link' , '=',  1),
					),
                ),
				'link_new_tab' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
					'values' => array(
						0 => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
						1 => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
					),
					'std' => '',
					'depends' => array('use_link' => 1)
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
					'std' => '',
					'max' => 400,
					'responsive' => true
				),
				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
					'std' => '',
					'max' => 400,
					'responsive' => true
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'-10px' => '-10px',
						'-9px' => '-9px',
						'-8px' => '-8px',
						'-7px' => '-7px',
						'-6px' => '-6px',
						'-5px' => '-5px',
						'-4px' => '-4px',
						'-3px' => '-3px',
						'-2px' => '-2px',
						'-1px' => '-1px',
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0'
				),
				'title_text_transform' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TEXT_TRANSFORM'),
					'values' => array(
						'none' => 'None',
						'capitalize' => 'Capitalize',
						'uppercase' => 'Uppercase',
						'lowercase' => 'Lowercase',
					),
					'std' => 'none'
				),

				'title_margin' => array(
					'type' => 'margin',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
					'std' => '0px 0px 30px 0px',
					'responsive' => true
				),

				'title_padding' => array(
					'type' => 'padding',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING'),
					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PADDING_DESC'),
					'std' => '0px 0px 0px 0px',
					'responsive' => true
				),

				'title_icon' => array(
					'type' => 'icon',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_ICON'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_icon_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_ICON_POSITION'),
					'values' => array(
						'before' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_ICON_POSITION_BEFORE'),
						'after' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_ICON_POSITION_AFTER'),
					),
					'std' => 'before',
					'depends' => array(array('title_icon', '!=', '', 'title', '!=', '')),
				),

				'title_icon_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_ICON_COLOR'),
					'depends' => array(array('title_icon', '!=', '', 'title', '!=', '')),
				),

				'alignment' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'jwpf-text-center',
				),
				'title_text_shadow' => array(
					'type' => 'boxshadow',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TEXT_SHADOW'),
					'std' => '',
					'config' => array(
						'spread' => false
					)
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
                'icon' => array(
                    'type' => 'media',
                    'title' => '标题icon左',
                    'std' => '',
                ),
                'icon_r' => array(
                    'type' => 'media',
                    'title' => '标题icon右',
                    'std' => '',
                ),
                'icon_pad' => array(
                    'title' => '标题icon左右间距',
                    'type' => 'slider',
                    'max' => 200,
                    'std' => '43',
                ),
                'icon_pad_w' => array(
                    'title' => '标题icon宽',
                    'type' => 'slider',
                    'max' => 200,
                    'std' => '20',
                ),
                'icon_pad_h' => array(
                    'title' => '标题icon高',
                    'type' => 'slider',
                    'max' => 200,
                    'std' => '20',
                ),

                /*纵向排列*/
                'column'=> array(
                    'type'=>'checkbox',
                    'title'=>'是否纵向排列',
                    'std'=>0
                ),
                'writing_mode'=> array(
                    'type'=>'select',
                    'title'=>'排列方式',
                    'std'=>'',
                    'values'=>array(
                        'vertical-lr'=>'从左向右',
                        'vertical-rl'=>'从右往左'
                    ),
                    'depends'=>array(
                        array('column','=',1)
                    )
                ),
                /*纵向排列end*/
			),
		),
	)
);
