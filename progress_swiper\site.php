<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonProgress_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;

        $addonId       = $this->addon->id;
        $items         = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';
        $isk2installed = self::isComponentInstalled('progress_swiper');

        if ($isk2installed === 0)
        {
            return '<div>出错了</div>';
        }


        $output = '<div class="swiper-container" id="swiper_'.$addonId.'">
		    <div class="swiper-wrapper">';
        foreach ($items as $key => $item)
        {
            $output .= '<div class="swiper-slide" data-id="' . $key . '">';
            if ($item->is_link === 1)
            {
                $target = $item->link_open_new_window === 1 ? '_blank' : '';
                $output .= '<a href="' . $item->image_carousel_img_link . '" target="' . $target . '">';
            }

            $output .= '<img src=\'' . $item->image_carousel_img . '\' alt="">';

            if ($item->is_link === 1)
            {
                $output .= '</a>';
            }
            $output .= '</div>';
        }
        $output .= '</div>
		    <!-- Add Pagination -->
		    <div class="pagination-box">
		    	<div class="progress-box">
		    		<p id="now-page"></p>
		    		<div class="swiper-pagination"></div>
		    		<p>'.sprintf("%02d", count($items)).'</p>
				</div>
				<div class="navigation-box">
						<!-- Add Arrows -->
				    <div class="swiper-button-prev">
				        ←
                    </div>
				    <div class="swiper-button-next">
				        →
                    </div>
				</div>
			</div>
		</div>';

        return $output;
    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');

        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js');

        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed    = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;


//        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;

        if ($carousel_autoplay === 1)
        {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        }
        else
        {
            $autoplay = 'false';
        }


        $progress_hover_color=(isset($settings->progress_hover_color) && $settings->progress_hover_color) ? $settings->progress_hover_color : '#6E6E6EFF';


        $script = 'jQuery(document).ready(function($){
//        初始化swiper配置项
            function initSwiper(){
                let navigation={};
                let settings={
                    loop: true,
                    loopFillGroupWithBlank: true,             
                    speed: ' . $carousel_speed . ',
                    autoplay: ' . $autoplay . ', 
                    observer: true,
                    observeParents:true,
                    pagination: {
				        el: ".swiper-pagination",
				        type: "progressbar",
				        progressbarFillClass : "swiper-pagination-progressbar-fill my-pagination-progressbar-fill",
				    },
				    navigation: {
				        nextEl: "#swiper_' . $addonId . ' .swiper-button-next",
				        prevEl: "#swiper_' . $addonId . ' .swiper-button-prev",
				    },
				    on:{
				        slideChangeTransitionStart: function(){
				            let num=this.realIndex+1;
					        let n=num.toString();
						    let s=n.padStart(2,"0");
						    $("#now-page").html(s);
					    },
				    }
                }
                let swiper = new Swiper("#swiper_' . $addonId . '", settings);
                return swiper;
            }
//            根据屏幕初始化swiper
            initSwiper();
//            屏幕改变大小再初始化一次
            window.onresize=function (){
                initSwiper();
                $("#swiper_' . $addonId . ' .my-pagination-progressbar-fill").css("background","'.$progress_hover_color.'");
            } 
            $("#swiper_' . $addonId . ' .my-pagination-progressbar-fill").css("background","'.$progress_hover_color.'");
            
            //            如果是放在选项卡里，因为包裹swiper的元素是display:none，swiper的初始化有问题，所以监听父元素添加了active类，display:block后再初始化一次
            if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("jwpf-tab-pane")){
                     var Observer = new MutationObserver(function (mutations, instance) {
                        mutations.forEach(function (mutation) {
                          if($("#jwpf-addon-wrapper-'.$addonId.'").parent().attr("class").includes("active")){
                            initSwiper();
                            $("#swiper_' . $addonId . ' .my-pagination-progressbar-fill").css("background","'.$progress_hover_color.'");
                          }
                        });
                     });
                     
                     Observer.observe($("#jwpf-addon-wrapper-'.$addonId.'").parent()[0], {
                        attributes: true
                     });
                }
        })';

        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;

//        轮播
        $addonId = '#swiper_' . $this->addon->id;

//		箭头
        $arrow_color=(isset($settings->arrow_color) && $settings->arrow_color) ? $settings->arrow_color : '#C2C2C2FF';
        $arrow_hover_color=(isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? $settings->arrow_hover_color : '#fff';
        $progress_color=(isset($settings->progress_color) && $settings->progress_color) ? $settings->progress_color : '#DCDCDCFF';


        $output = $addonId . ' .swiper-slide img{
			width:100%;
	    }
	    '.$addonId . ' .pagination-box{
	        width: 55%;
	        height: 80px;
	        position: absolute;
	        left: 4%;
	        bottom: 0;
	        background:#fff;
	        z-index: 1;
	        padding: 0 20px;
	        display:flex;
	        justify-content: space-between;
	    }
	    '.$addonId . ' .progress-box{
	        width: 55%;
	        display: flex;
	        align-items: center;
	        height: 100%;
	        justify-content: flex-start;
	    }
	    '.$addonId . ' .progress-box p{
	        font-size: 18px;
	        line-height: 80px;
	        margin-bottom: 0;
	        padding: 0 20px;
	    }
	    '.$addonId . ' .navigation-box{
	        width: 30%;
	        height: 100%;
	        display:flex;
	        justify-content:flex-end;
	        align-items:center;
	    }
	    '.$addonId . ' .swiper-button-next,'.$addonId . ' .swiper-button-prev{
	        position:static;
	        display:block;
	        width:auto;
	        color: '.$arrow_color.';
	        outline:none;
	        line-height: 80px;
	        font-size: 36px;
	        text-align: center;
	        background-image: none;
	        height: 80px;
	        margin-top: 0;
	        background-image: none;
	    }
	    '.$addonId . ' .swiper-button-prev{
	        margin-right: 20px;
	    }
	    '.$addonId . ' .swiper-button-next:hover,'.$addonId . ' .swiper-button-prev:hover{
	        color: '.$arrow_hover_color.';
	    }
	    '.$addonId . ' .swiper-button-prev::after,
	    '.$addonId . ' .swiper-button-next::after{
	        display: none;
	    }
	    
	    '.$addonId . ' .swiper-pagination-progressbar{
	        flex-grow: 1;
	        width: 80%;
	        height:1px;
	        position: static;
	        background: '.$progress_color.';
	    }
	     '.$addonId . ' .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{
	        height:3px;
	        bottom: 0;
	        margin:auto;
	    }
	    @media (max-width: 991px) and (min-width: 768px){
	        '.$addonId . ' .swiper-button-prev,
		    '.$addonId . ' .swiper-button-next{
		        font-size: 30px;
		    }
	    }
	    @media (max-width: 767px){
	        '.$addonId . ' .pagination-box{
	            width:calc(100% - 40px);
	            height: 60px;
	            left:0;
	        }
		    '.$addonId . ' .swiper-button-next,'.$addonId . ' .swiper-button-prev{
		        transform:translateY(2px);
		    }
		    '.$addonId . ' .swiper-button-prev,
		    '.$addonId . ' .swiper-button-next{
	            height: 60px;
		        line-height: 60px;
		        font-size: 24px;
		    }
	    }
	    @media (max-width: 450px){
	        '.$addonId . ' .pagination-box{
	            display:none;
	        }
	    }';

        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db    = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();

        return $is_enabled;
    }
}
