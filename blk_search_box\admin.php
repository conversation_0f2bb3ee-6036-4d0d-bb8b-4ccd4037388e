<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'blk_search_box',
         'title' => JText::_('搜索框'),
         'desc' => JText::_('巴洛克搜索框'),
         'category' => '搜索',
         'attr' => array(
             'general' => array(
                'search_type' => array(
                    'type' => 'select',
                    'title' => '选择搜索框类型',
                    'desc' => '',
                    'values' => array(
                        'type1' => '巴洛克搜索框',
                        'type2' => '龙采官网搜索框',
                        'type3' => '搜索框布局3',
                    ),
                    'std' => 'type1'
                ),
                'detail_page_id' => array(
                     'type' => 'select',
                     'title' => '选择搜索结果页',
                     'desc' => '',
                     'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),
                'content_text' => array(
                  'type' => 'text',
                  'title' => '热门搜索内容',
                  'desc' => '',
                  'std' => '巴洛克建筑 巴洛克音乐 巴洛克风格三天特征 巴洛克建筑艺术 巴洛克时期',
                  'depends' => array('search_type' => 'type1'),

                ),
                // 龙采官网
        
                'tab_item2' => array(
					'title' => JText::_('热搜词汇'),
					'attr' => array(
						'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '',
                        ),
                    	'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							
						),

						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
		                    ),
						),

						// 新加跳转的链接地址
                        'media_url' => array(
					        'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								array('media_url_show', '=',1),
								array('tz_page_type', '=','external_links'),

							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=',1),
								array('tz_page_type', '=','Internal_pages'),

							),
						),
                        // 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => JText::_('链接跳转方式'),
							'desc' => JText::_('链接跳转方式'),
							'values' => array(
								'' => JText::_('当前页面'),
								'_blank' => JText::_('新窗口'),
							),
							'depends' => array(
								array('media_url_show', '=',1),
							)
						),

					),
					'depends' => array(
						array('search_type', '=', 'type2'),
                    ),
                    'std' => array(
                        array(
                            'title' => '网站制作',
                        ),
                        array(
                            'title' => '百度推广',
                        ),
                        array(
                            'title' => '小程序制作',
                        ),
                        array(
                            'title' => ' APP开发',
                        ),
                        array(
                            'title' => '电商制作',
                        ),
                        array(
                            'title' => '行业解决方案',
                        ),
                    ),
				),
                //
                'bg_tm_num' => array( 
                  'type' => 'slider',
                  'title' => JText::_('弹出层背景透明度'),
                  'responsive' => true,
                  'max' => 9,
                  'min' => 1,
                  'std' => 5,
                  'depends' => array(
                    array('search_type', '=', 'type1'),
                  ),
                ),
                'search_icon' => array( 
                    'type' => 'media',
                    'title' => JText::_('pc搜索图标'),
                    'responsive' => true,
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png',
                ),
                'search_icon_sj' => array( 
                    'type' => 'media',
                    'title' => JText::_('手机搜索图标'),
                    'responsive' => true,
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/blk_search_box/assets/images/serch.png',
                ),
                'but_color' => array(
                  'type' => 'color',
                  'title' => '搜索按钮背景色',
                  'depends' => array('search_type' => 'type3'),
                  'std' => '#222c36',
                ),
                'sech_height' => array(
                  'type' => 'slider',
                  'title' => '搜索框宽高',
                  'depends' => array('search_type' => 'type3'),
                  'std' => '30',
                ),
                'sech_kuang' => array(
                  'type' => 'slider',
                  'title' => '搜索框宽度(%)',
                  'depends' => array('search_type' => 'type3'),
                  'std' => '94',
                ),
                //龙采官网
                'bg_color' => array(
                  'type' => 'color',
                  'title' => '弹出层背景色',
                  'depends' => array('search_type' => 'type2'),
                  'std' => '#d9012a',
                ),
                'top_logo' => array( 
                    'type' => 'media',
                    'title' => JText::_('头部logo'),
                    'responsive' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220512/072b87c7f0a1b39665c391254710082d.png',
                    'depends' => array('search_type' => 'type2'),
                ),
                'dt_img' => array( 
                    'type' => 'media',
                    'title' => JText::_('右侧动图'),
                    'responsive' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220512/cb47628729e2d5c63251a30a6a1f4f3a.gif',
                    'depends' => array('search_type' => 'type2'),
                ),
                'tc_sechimg' => array( 
                    'type' => 'media',
                    'title' => JText::_('弹出层搜索图标'),
                    'responsive' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220512/ccbbc35634a9a09347a0beb9ed39574a.png',
                    'depends' => array('search_type' => 'type2'),
                ),
             ),
         ),
     )
 );
