<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'event_list',
		'title' => '赛事活动列表',
		'desc' => '赛事活动列表',
		'category' => '活动',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),

				// 布局样式
				'bjstyle' => array(
                   'type' => 'select',
                   'title' => '布局样式',
                   'std' => 'list1',
                   'values' => array(
                       'list1' => '赛事列表',
                       // 'list2' => '近期赛事',
                   )
               	),

				// 分类样式

				'fltype' => array(
					'type' => 'checkbox',
					'title' => JText::_('设置分类样式'),
					'std' => 0,
					'depends' => array('bjstyle' => 'list1'),
				),

				// 字体大小、颜色、背景、选中色
				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('字体大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => 16,
					'depends' => array('fltype' => 1),
					
					'max' => 400,
					'responsive' => true
				),
				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('字体颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('fltype' => 1),
                    'std' => '#4a4a4a',
					
				),
				'title_active_color' => array(
					'type' => 'color',
					'title' => JText::_('选中字体颜色'),
					'depends' => array('fltype' => 1),
                    'std' => '#0E893B',
					
				),
				'seach_color' => array(
					'type' => 'color',
					'title' => JText::_('搜索图标背景色'),
					'depends' => array('fltype' => 1),
                    'std' => 'rgba(14,137,59,1)',
					
				),
				
				
				// 列表样式

				'listys' => array(
					'type' => 'checkbox',
					'title' => JText::_('设置列表样式'),
					'std' => 0,
					'depends' => array('bjstyle' => 'list1'),

				),

				// 字体大小、颜色\行高
				'bttitle_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('标题大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => 18,
					'depends' => array('listys' => 1),
					
					'max' => 400,
					'responsive' => true
				),
				'bttitle_text_color' => array(
					'type' => 'color',
					'title' => JText::_('标题颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('listys' => 1),
                    'std' => 'rgba(14,137,59,1)',
					
				),
				'btlink_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => 30,
                    'max' => 400,

                    'depends' => array(
                        array('listys', '=', '1'),
                    ),
                    'responsive' => true
                ),
                
				'xqtitle_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('列表字体大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => 18,
					'depends' => array('listys' => 1),
					
					'max' => 400,
					'responsive' => true
				),
				'xqtitle_text_color' => array(
					'type' => 'color',
					'title' => JText::_('列表字体颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('listys' => 1),
                    'std' => '#4a4a4a',
					
				),
				'lblink_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表行高'),
                    'std' => 28,
                    'max' => 400,

                    'depends' => array(
                        array('listys', '=', '1'),
                    ),
                    'responsive' => true
                ),

				// 翻页
				'show_page' => array(
					'type' => 'checkbox',
					'title' => JText::_('显示翻页码'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
					'std' => 0,
					'depends' => array('bjstyle' => 'list1'),
				),
				'limit' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
					'std' => '10',
					'depends' => array('show_page' => 1)
				),
				



				// list2 近期赛事样式
				'listy2' => array(
					'type' => 'checkbox',
					'title' => JText::_('设置列表样式'),
					'std' => 0,
					'depends' => array('bjstyle' => 'list2'),
				),

				// 更多
				'gengduo' => array(
  					'type' => 'select',
  					'title' => '点击更多跳转页面',
  					'desc' => '显示页面模版',
					'depends' => array('listy2' => 1),
  					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
  					'depends' => array(
                          array(
                             'lxstyle', '=', 'homey'
                          )
                     )
  				),
				// 左上角大标题颜色、字体大小
				'dtitle_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('大标题字体大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => array('md' => '24', 'sm' => '20', 'xs' => '18'),
					'max' => 100,
					'depends' => array('listy2' => 1),

					'responsive' => true
				),
				'dtitle_text_color' => array(
					'type' => 'color',
					'title' => JText::_('大标题字体颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('listy2' => 1),
                    'std' => '#0E893B',
					
				),

				// 内容上下间距
				'con_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容上下间距'),
                    'max' => 100,
                    'min' => 0,
                    'depends' => array('listy2' => 1),
					'responsive' => true,
					'std' => array('md' => '30', 'sm' => '20', 'xs' => '15'),

                ),
				// li划过背景色
				'hr_background' => array(
                    'type' => 'color',
                    'title' => '滑过背景色设置',
                    'std' => '#0E893B',
                    'depends' => array(
                        array( 'listy2' ,'=', '1'),
                    ),
                ),
                // li的padding间距
                'list_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('列表间距'),
                    'responsive' => true,
                    'std' => array('md' => '20px 20px 20px 20px', 'sm' => '10px 10px 10px 10px', 'xs' => '10px 10px 10px 10px'),
                    'depends' => array(
                       array('listy2', '=', '1'),
                    ),
                ),

				// 标题字体大小、颜色\行高
				'lbttitle_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('列表标题大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => array('md' => '16', 'sm' => '16', 'xs' => '14'),
					'depends' => array('listy2' => 1),
					'max' => 400,
					'responsive' => true
				),
				'lbttitle_text_color' => array(
					'type' => 'color',
					'title' => JText::_('列表标题颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('listy2' => 1),
                    'std' => '#252525',
					
				),
				'lbtlink_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表标题行高'),
                    'max' => 400,
					'std' => array('md' => '24', 'sm' => '24', 'xs' => '20'),

                    'depends' => array(
                        array('listy2', '=', '1'),
                    ),
                    'responsive' => true
                ),
                
                // 分类背景
                'class_background' => array(
                    'type' => 'color',
                    'title' => '分类标签背景色',
                    'std' => 'rgba(123,229,186,1)',
                    'depends' => array(
                        array( 'listy2' ,'=', '1'),
                    ),
                ),
                // 列表
				'lititle_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('列表字体大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => array('md' => '14', 'sm' => '14', 'xs' => '13'),

					'depends' => array('listy2' => 1),
					'max' => 400,
					'responsive' => true
				),
				'lititle_text_color' => array(
					'type' => 'color',
					'title' => JText::_('列表字体颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array('listy2' => 1),
                    'std' => 'rgba(44,58,88,1)',
					
				),
				'lilink_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表行高'),
                    'max' => 400,
					'std' => array('md' => '28', 'sm' => '28', 'xs' => '24'),

                    'depends' => array(
                        array('listy2', '=', '1'),
                    ),
                    'responsive' => true
                ),
				// 一行显示几个
                'item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一行显示的数量'),
                    'desc' => JText::_('一行显示的数量'),
                    'min' => 1,
                    'max' => 8,
                    'responsive' => true,
                    'std' => array('md' => 4, 'sm' => 3, 'xs' => 2),
                    'depends' => array(
                        array('listy2', '=', '1'),
                    ),
                ),
                // 显示几个
                'dis_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('显示几行'),
                    'desc' => JText::_('显示几行'),
                    'min' => 1,
                    'max' => 8,
                    'responsive' => true,
                    'std' => array('md' => 2, 'sm' => 2, 'xs' => 2),
                    'depends' => array(
                        array('listy2', '=', '1'),
                    ),
                ),
			),
		),
	)
);
