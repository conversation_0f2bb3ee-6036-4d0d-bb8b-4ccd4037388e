<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonTz_button_wx extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $service_product_type = (isset($settings->service_product_type) && $settings->service_product_type) ? $settings->service_product_type : 'type1';
        $xcx_app_id = (isset($settings->xcx_app_id) && $settings->xcx_app_id) ? $settings->xcx_app_id : '';
        $xcx_app_secret = (isset($settings->xcx_app_secret) && $settings->xcx_app_secret) ? $settings->xcx_app_secret : '';
        $bgcolor_type1 = (isset($settings->bgcolor_type1) && $settings->bgcolor_type1) ? $settings->bgcolor_type1 : '';
        $font_color_type1 = (isset($settings->font_color_type1) && $settings->font_color_type1) ? $settings->font_color_type1 : '';
        $font_size_type1 = (isset($settings->font_size_type1) && $settings->font_size_type1) ? $settings->font_size_type1 : 16;
        $button_width_type1 = (isset($settings->button_width_type1) && $settings->button_width_type1) ? $settings->button_width_type1 : 100;
        $button_height_type1 = (isset($settings->button_height_type1) && $settings->button_height_type1) ? $settings->button_height_type1 : 50;
        $border_color_type1 = (isset($settings->border_color_type1) && $settings->border_color_type1) ? $settings->border_color_type1 : '';
        $border_width_type1 = (isset($settings->border_width_type1) && $settings->border_width_type1) ? $settings->border_width_type1 : 1;
        $border_radius_type1 = (isset($settings->border_radius_type1) && $settings->border_radius_type1) ? $settings->border_radius_type1 : 0;

        $output = '';
        if ($service_product_type == 'type1') {
            $output .= '
                <style>
                    .wxbutton{
                        background-color: '.$bgcolor_type1.';
                        border-width: '.$border_width_type1.'px;
                        border-color: '.$border_color_type1.';
                        border-radius: '.$border_radius_type1.'px;
                        width: '.$button_width_type1.'px;
                        height: '.$button_height_type1.'px;
                        font-size: '.$font_size_type1.'px;
                        color: '.$font_color_type1.';
                    }
                </style>
            ';
            $output .= '
            <button class="wxbutton">跳转</button>
            ';
            $output .= '
                <script>
                    $("'.$addon_id.' .wxbutton").click(function(){
                        $.ajax({
                            url:"https://zhjzt.china9.cn/api/wx/selurl?appid='.$xcx_app_id.'&appsecret='.$xcx_app_secret.'",
                            type:"get",
                            success:function(result){
                                if(result.code==200)
                                {
                                    window.location = result.data;
                                }
                                else
                                {
                                    alert(result.msg);
                                }
                            }
                        });
                    });
                </script>
            ';
        }
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
    }

    /* js */
    public function js()
    {
        
    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
