<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonRecruit_show extends JwpagefactoryAddons
{

	public function render()
	{
        $settings = $this->addon->settings;
        $addon_id = $this->addon->id;
       
        $company_id = $_GET['company_id'] ?? '';
        $site_id = $_GET['site_id'] ?? '';
        $catid_id = $_GET['catid_id'] ?? '';
        $layout_id = $_GET['layout_id'] ?? '';

        //获取招聘详情的数据源
        $app = JFactory::getApplication();
        $input = $app->input;
        $article_id = $input->get('detail');
        $detail_id = base64_decode($input->get('id'));//职位页面

        // 如果简历页没有接收的职位id，取最后一条职位信息
        if(!$article_id){
            $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
            require_once $article_helper;
            $tab_item = JwpagefactoryHelperArticles::getRecruitList(1, 'desc', '', '', '', '', $company_id, $layout_id, $site_id);
            if($tab_item){
                foreach($tab_item as $key => $item){
                    $article_id->title=$item->title;
                    $article_id=$item->id;
                }
            }

        }

        //读取找数据
        $article = JwPageFactoryBase::getRecruitById($article_id, $catid_id);

        // 【应聘】跳转页面
        $action_detail_page_id = (isset($settings->action_detail_page_id)) ? $settings->action_detail_page_id : 0;
        $action_target = (isset($settings->action_target) && $settings->action_target) ? $settings->action_target : "_self";

        $id = base64_encode($action_detail_page_id);//应聘页面
        $action_link='component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id.'&detail='.$article_id;;
        // 详情布局
        $recruit_layout = (isset($settings->recruit_layout) && $settings->recruit_layout) ? $settings->recruit_layout : "type01";

        // 显示详情内容
        $show_content = (isset($settings->show_content) && $settings->show_content) ? $settings->show_content : 0;
        // 显示表单
        $show_form = (isset($settings->show_form) && $settings->show_form) ? $settings->show_form : 0;
        // 显示【身份证号】项
        $form_item_04 = (isset($settings->form_item_04) && $settings->form_item_04) ? $settings->form_item_04 : 0;
        // 【身份证号】项必填
        $form_item_04_has = (isset($settings->form_item_04_has) && $settings->form_item_04_has) ? $settings->form_item_04_has : 0;
        // 显示【婚否】项
        $form_item_05 = (isset($settings->form_item_05) && $settings->form_item_05) ? $settings->form_item_05 : 0;
        // 【婚否】项必填
        $form_item_05_has = (isset($settings->form_item_05_has) && $settings->form_item_05_has) ? $settings->form_item_05_has : 0;
        // 显示【职称/资质】项
        $form_item_06 = (isset($settings->form_item_06) && $settings->form_item_06) ? $settings->form_item_06 : 0;
        // 【职称/资质】项必填
        $form_item_06_has = (isset($settings->form_item_06_has) && $settings->form_item_06_has) ? $settings->form_item_06_has : 0;
        // 显示【毕业院校】项
        $form_item_07 = (isset($settings->form_item_07) && $settings->form_item_07) ? $settings->form_item_07 : 0;
        // 【毕业院校】项必填
        $form_item_07_has = (isset($settings->form_item_07_has) && $settings->form_item_07_has) ? $settings->form_item_07_has : 0;
        // 显示【专业】项
        $form_item_08 = (isset($settings->form_item_08) && $settings->form_item_08) ? $settings->form_item_08 : 0;
        // 【专业】项必填
        $form_item_08_has = (isset($settings->form_item_08_has) && $settings->form_item_08_has) ? $settings->form_item_08_has : 0;
        // 显示【联系地址】项
        $form_item_10 = (isset($settings->form_item_10) && $settings->form_item_10) ? $settings->form_item_10 : 0;
        // 【联系地址】项必填
        $form_item_10_has = (isset($settings->form_item_10_has) && $settings->form_item_10_has) ? $settings->form_item_10_has : 0;
        // 显示【籍贯】项
        $form_item_11 = (isset($settings->form_item_11) && $settings->form_item_11) ? $settings->form_item_11 : 0;
        // 【籍贯】项必填
        $form_item_11_has = (isset($settings->form_item_11_has) && $settings->form_item_11_has) ? $settings->form_item_11_has : 0;
        // 显示【期望薪金】项
        $form_item_14 = (isset($settings->form_item_14) && $settings->form_item_14) ? $settings->form_item_14 : 0;
        // 【期望薪金】项必填
        $form_item_14_has = (isset($settings->form_item_14_has) && $settings->form_item_14_has) ? $settings->form_item_14_has : 0;
        // 显示【学习经历】项
        $form_item_15 = (isset($settings->form_item_15) && $settings->form_item_15) ? $settings->form_item_15 : 0;
        // 【学习经历】项必填
        $form_item_15_has = (isset($settings->form_item_15_has) && $settings->form_item_15_has) ? $settings->form_item_15_has : 0;
        // 显示【工作经历】项
        $form_item_16 = (isset($settings->form_item_16) && $settings->form_item_16) ? $settings->form_item_16 : 0;
        // 【工作经历】项必填
        $form_item_16_has = (isset($settings->form_item_16_has) && $settings->form_item_16_has) ? $settings->form_item_16_has : 0;
        // 显示【对公司要求】项
        $form_item_17 = (isset($settings->form_item_17) && $settings->form_item_17) ? $settings->form_item_17 : 0;
        // 【对公司要求】项必填
        $form_item_17_has = (isset($settings->form_item_17_has) && $settings->form_item_17_has) ? $settings->form_item_17_has : 0;
        // 显示【特长爱好】项
        $form_item_18 = (isset($settings->form_item_18) && $settings->form_item_18) ? $settings->form_item_18 : 0;
        // 【特长爱好】项必填
        $form_item_18_has = (isset($settings->form_item_18_has) && $settings->form_item_18_has) ? $settings->form_item_18_has : 0;
        // 显示【简历上传】项
        $form_item_19 = (isset($settings->form_item_19) && $settings->form_item_19) ? $settings->form_item_19 : 0;
        // 【特长爱好】项必填
        $form_item_19_has = (isset($settings->form_item_19_has) && $settings->form_item_19_has) ? $settings->form_item_19_has : 0;
        // 显示【应聘】按钮
        $show_recruit_btn = (isset($settings->show_recruit_btn) && $settings->show_recruit_btn) ? $settings->show_recruit_btn : 0;
        // 【应聘】按钮文字
        $recruit_btn_text = (isset($settings->recruit_btn_text) && $settings->recruit_btn_text) ? $settings->recruit_btn_text : 0;



		$output = '<div class="recruit-container">';
		    if($show_content == 1) {
                $output .= '<h2 class="r-title">'.$article->title.'</h2> 
                <div class="content-box">
                    '.$article->fulltext.'
                </div>';
            }
            if($show_form == 1) {

                $yuming=$_SERVER['HTTP_HOST'];
                if($yuming=='jzt_dev_2.china9.cn'){
                    $urlpath='http://jzt_dev_1.china9.cn';
                }elseif($yuming=='ijzt.china9.cn'){
                    $urlpath='https://zhjzt.china9.cn';
                }else{
                    $config = new JConfig();
                    $urlpath = $config->jzt_url;
                }

                $output .= '<div class="recruit_'.$addon_id.'" >';

                    $output .= '
                    <div class="form-container">
                        <div class="form-box">
                            <div class="form-item">
                                <label>本人姓名：</label>
                                <input type="text" id="userName">
                                <span class="tag">*</span>
                            </div>
                            <div class="form-item">
                                <label>性别：</label>
                                <div class="radio-box">
                                    <input type="radio" name="sex" value="男" checked>&nbsp;男&nbsp;&nbsp;
                                    <input type="radio" name="sex" value="女">&nbsp;女
                                </div>
                                <span class="tag">*</span>
                            </div>
                            <div class="form-item">
                                <label>出生年月：</label>
                                <input type="date" id="birthday">
                                <span class="tag">*</span>
                            </div>';
                            if($form_item_04 == 1) {
                                $output .= '<div class="form-item">
                                    <label>身份证号：</label>
                                    <input type="text" id="cardNumber" '. ($form_item_04_has ? ' required' : '') . '>';
                                    if($form_item_04_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_05 == 1) {
                                $output .= '<div class="form-item">
                                    <label>婚否：</label>
                                    <div class="radio-box">
                                        <input type="radio" name="wed" value="已婚" checked>&nbsp;已婚&nbsp;&nbsp;
                                        <input type="radio" name="wed" value="未婚">&nbsp;未婚
                                    </div>';
                                    if($form_item_05_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_06 == 1) {
                                $output .= '<div class="form-item">
                                    <label>职称/资质：</label>
                                    <input type="text" id="qualifications" '. ($form_item_06_has ? ' required' : '') . '>';
                                    if($form_item_06_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_07 == 1) {
                                $output .= '<div class="form-item">
                                    <label>毕业院校：</label>
                                    <input type="text" id="school" '. ($form_item_07_has ? ' required' : '') . '>';
                                    if($form_item_07_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_08 == 1) {
                                $output .= '<div class="form-item">
                                    <label>专业：</label>
                                    <input type="text" id="major" '. ($form_item_08_has ? ' required' : '') . '>';
                                    if($form_item_08_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            $output .= '<div class="form-item">
                                <label>	最高学历：</label>
                                <input type="text" id="education">
                                <span class="tag">*</span>
                            </div>';
                            if($form_item_10 == 1) {
                                $output .= '<div class="form-item">
                                    <label>联系地址：</label>
                                    <input type="text" id="address" '. ($form_item_10_has ? ' required' : '') . '>';
                                    if($form_item_10_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_11 == 1) {
                                $output .= '<div class="form-item">
                                    <label>籍贯：</label>
                                    <input type="text" id="place" '. ($form_item_11_has ? ' required' : '') . '>';
                                    if($form_item_11_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            $output .= '<div class="form-item">
                                <label>联系电话：</label>
                                <input type="text" id="phone">
                                <span class="tag">*</span>
                            </div>
                            <div class="form-item">
                                <label>应聘职位：</label>
                                <input type="text" id="job" value="'.$article->title.'">
                                <span class="tag">*</span>
                            </div>';
                            if($form_item_14 == 1) {
                                $output .= '<div class="form-item">
                                    <label>期望薪金：</label>
                                    <input type="text" id="money" '. ($form_item_14_has ? ' required' : '') . '>';
                                    if($form_item_14_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_15 == 1) {
                                $output .= '<div class="form-item area">
                                    <label>学习经历</label>
                                    <textarea class="jwpf-form-control" name="" id="study" cols="30" rows="10" '. ($form_item_15_has ? ' required' : '') . '></textarea>';
                                    if($form_item_15_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_16 == 1) {
                                $output .= '<div class="form-item area">
                                    <label>工作经历</label>
                                    <textarea class="jwpf-form-control" name="" id="work" cols="30" rows="10" '. ($form_item_16_has ? ' required' : '') . '></textarea>';
                                    if($form_item_16_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_17 == 1) {
                                $output .= '<div class="form-item area">
                                    <label>对公司要求</label>
                                    <textarea class="jwpf-form-control" name="" id="ask" cols="30" rows="10" '. ($form_item_17_has ? ' required' : '') . '></textarea>';
                                    if($form_item_17_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_18 == 1) {
                                $output .= '<div class="form-item area">
                                    <label>特长爱好</label>
                                    <textarea class="jwpf-form-control" name="" id="specialty" cols="30" rows="10" '. ($form_item_18_has ? ' required' : '') . '></textarea>';
                                    if($form_item_18_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                            if($form_item_19 == 1) {
                                $output .= '<div class="form-item area">
                                    <label>简历上传</label>
                                    <input type="file" name="jlfile" id="jlfile" >
                                    ';
                                    if($form_item_19_has == 1) {
                                        $output .= '<span class="tag">*</span>';
                                    }
                                $output .= '</div>';
                            }
                        $output .= '</div>
                        <div class="alert alert-warning" style="display:none;padding: 0.75rem 1.25rem;"></div>

                        <div class="btn-box">
                            <span class="sure-btn">
                                提交
                            </span>
                            <span class="reset-btn">重置</span>
                        </div>
                    </div>';

                $output .= '</div>';
                $output .= '
                <script>
                    jQuery(function($){
                        function myAlert(msg) {
                            $(".recruit_' . $addon_id . ' .alert").show();
                            $(".recruit_' . $addon_id . ' .alert").html(msg);
                        }
                        function closeAlert() {
                            $(".recruit_' . $addon_id . ' .alert").hide();
                        }
                        $(".recruit_' . $addon_id . ' .sure-btn").click(function(){
                            var name=$("#userName").val();
                            var cardNumber=$("#cardNumber").val();
                            var qualifications=$("#qualifications").val();
                            var school=$("#school").val();
                            var major=$("#major").val();
                            var address=$("#address").val();
                            var place=$("#place").val();
                            var money=$("#money").val();
                            var study=$("#study").val();
                            var work=$("#work").val();
                            var ask=$("#ask").val();
                            var specialty=$("#specialty").val();
                            var sex=$("input:radio[name=\"sex\"]:checked").val();
                            var wed=$("input:radio[name=\"wed\"]:checked").val();
                            var birthday=$("#birthday").val();
                            var education=$("#education").val();
                            var phone=$("#phone").val();
                            var job=$("#job").val();
                            var jlfile=$("#jlfile").val();

                            ';
                            if(!$article_id){
                                $output .= '
                                    myAlert("请搭配招聘列表使用");
                                    return false;
                                ';
                            }
                            if($form_item_19==1 && $form_item_19_has == 1) {
                                $output .= 'var files=$("#jlfile")[0].files;
                                console.log(files)';
                            }
                            $output .= '
                            if(!name){
                                myAlert("本人姓名不能为空");
                                return false;
                            }else {
                                closeAlert();
                            }
                            if(!education){
                                myAlert("最高学历不能为空");
                                return false;
                            }else {
                                closeAlert();
                            }
                            if(!phone){
                                myAlert("联系电话不能为空");
                                return false;
                            }else {
                                closeAlert();
                            }
                            if(!job){
                                myAlert("应聘职位不能为空");
                                return false;
                            }else {
                                closeAlert();
                            }

                            ';
                             
                            if($form_item_04==1 && $form_item_04_has == 1) {
                                $output .= '
                                    if(!cardNumber){
                                        myAlert("身份证不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_06==1 && $form_item_06_has == 1) {
                                $output .= '
                                    if(!qualifications){
                                        myAlert("职称资质不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_07==1 && $form_item_07_has == 1) {
                                $output .= '
                                    if(!school){
                                        myAlert("毕业院校不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_08==1 && $form_item_08_has == 1) {
                                $output .= '
                                    if(!major){
                                        myAlert("专业不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_10==1 && $form_item_10_has == 1) {
                                $output .= '
                                    if(!address){
                                        myAlert("联系地址不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_11==1 && $form_item_11_has == 1) {
                                $output .= '
                                    if(!place){
                                        myAlert("籍贯不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_14==1 && $form_item_14_has == 1) {
                                $output .= '
                                    if(!money){
                                        myAlert("期望薪金不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_15==1 && $form_item_15_has == 1) {
                                $output .= '
                                    if(!study){
                                        myAlert("学习经历不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_16==1 && $form_item_16_has == 1) {
                                $output .= '
                                    if(!work){
                                        myAlert("工作经历不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_17==1 && $form_item_17_has == 1) {
                                $output .= '
                                    if(!ask){
                                        myAlert("对公司要求不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_18==1 && $form_item_18_has == 1) {
                                $output .= '
                                    if(!specialty){
                                        myAlert("特长爱好不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';
                            }
                            if($form_item_19==1 && $form_item_19_has == 1) {
                                $output .= '
                                    if(files.length<=0){
                                        myAlert("文件不能为空");
                                        return false;
                                    }else {
                                        closeAlert();
                                    }
                                ';

                                $output .= '
                                    var data = new FormData();
                                    data.append("company_id","' . $company_id . '");
                                    data.append("site_id","' . $site_id . '");
                                    data.append("name",name);
                                    data.append("sex",sex);
                                    data.append("birthday",birthday);
                                    data.append("cardNumber",cardNumber);
                                    data.append("place",place);
                                    data.append("wed",wed);
                                    data.append("school",school);
                                    data.append("major",major);
                                    data.append("education",education);
                                    data.append("qualifications",qualifications);
                                    data.append("job",job);
                                    data.append("money",money);
                                    data.append("phone",phone);
                                    data.append("address",address);
                                    data.append("ask",ask);
                                    data.append("study",study);
                                    data.append("work",work);
                                    data.append("specialty",specialty);
                                    data.append("zwid","'.$article_id.'");
                                    data.append("jlfile",files[0]);
                                    $.ajax({
                                        type: "POST",
                                        url: "' . $urlpath . '/api/Message/resumesubmit_form",
                                        dataType: "json",
                                        data: data,
                                        processData:false,
                                        contentType:false,
                                        success: function (res) {
                                            if(res.code==200){
                                                myAlert(res.content || res.msg);
                                                return false;
                                            }else{
                                                myAlert(res.content || res.msg);
                                                return false;
                                            }
                                        }     
                                    });
                                });';

                            }else{
                                $output .= '
                                    var data = new FormData();
                                    data.append("company_id","' . $company_id . '");
                                    data.append("site_id","' . $site_id . '");
                                    data.append("name",name);
                                    data.append("sex",sex);
                                    data.append("birthday",birthday);
                                    data.append("cardNumber",cardNumber);
                                    data.append("place",place);
                                    data.append("wed",wed);
                                    data.append("school",school);
                                    data.append("major",major);
                                    data.append("education",education);
                                    data.append("qualifications",qualifications);
                                    data.append("job",job);
                                    data.append("money",money);
                                    data.append("phone",phone);
                                    data.append("address",address);
                                    data.append("ask",ask);
                                    data.append("study",study);
                                    data.append("work",work);
                                    data.append("specialty",specialty);
                                    data.append("zwid","'.$article_id.'");
                                    $.ajax({
                                        type: "POST",
                                        url: "' . $urlpath . '/api/Message/resumesubmit_form",
                                        dataType: "json",
                                        data: data,
                                        processData:false,
                                        contentType:false,
                                        success: function (res) {
                                            if(res.code==200){
                                                myAlert(res.content);
                                                return false;
                                            }else{
                                                myAlert(res.content);
                                                return false;
                                            }
                                        }     
                                    });
                                });';
                            }

                        
                        $output .= '$(".recruit_' . $addon_id . ' .reset-btn").click(function(){
                            $("#userName").val("");
                            $("#cardNumber").val("");
                            $("#qualifications").val("");
                            $("#school").val("");
                            $("#major").val("");
                            $("#address").val("");
                            $("#place").val("");
                            $("#money").val("");
                            $("#study").val("");
                            $("#work").val("");
                            $("#ask").val("");
                            $("#specialty").val("");
                            $("#birthday").val("");
                            $("#education").val("");
                            $("#phone").val("");
                            $("#job").val("");
                        }); 
                        
                    })
                </script>
                ';
            }
            if($show_recruit_btn == 1) {
                $output .= '<div class="recruit-btn-box">
                    <a href="'.$action_link.'" target="'.$action_target.'">' . $recruit_btn_text . '</a>
                </div>';
            }
        $output .= '</div>';
		return $output;
	}

	public function scripts()
	{
//		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.magnific-popup.min.js');
	}

	public function stylesheets()
	{
//		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/magnific-popup.css');
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;

        // 详情布局
        $recruit_layout = (isset($settings->recruit_layout) && $settings->recruit_layout) ? $settings->recruit_layout : "type01";

		// 标题文字位置
        $c_title_align = (isset($settings->c_title_align) && $settings->c_title_align) ? $settings->c_title_align : "center";
        // 标题文字大小
        $c_title_fontsize = (isset($settings->c_title_fontsize) && $settings->c_title_fontsize) ? $settings->c_title_fontsize : 20;
        // 标题头文字颜色
        $c_title_color = (isset($settings->c_title_color) && $settings->c_title_color) ? $settings->c_title_color : "rgba(0, 0, 0, 1)";
        // 标题文字加粗
        $c_title_fontWeight = (isset($settings->c_title_fontWeight) && $settings->c_title_fontWeight) ? $settings->c_title_fontWeight : 0;
        // 表单项间距
        $form_item_right = (isset($settings->form_item_right) && $settings->form_item_right) ? $settings->form_item_right : 20;
        // 表单项底部距离
        $form_item_bottom = (isset($settings->form_item_bottom) && $settings->form_item_bottom) ? $settings->form_item_bottom : 20;
        // 表单标签宽度
        $form_label_width = (isset($settings->form_label_width) && $settings->form_label_width) ? $settings->form_label_width : 84;
        // 表单标签文字大小
        $form_label_fontsize = (isset($settings->form_label_fontsize) && $settings->form_label_fontsize) ? $settings->form_label_fontsize : 14;
        // 表单标签文字颜色
        $form_label_color = (isset($settings->form_label_color) && $settings->form_label_color) ? $settings->form_label_color : "rgba(0, 0, 0, 1)";
        // 表单输入框边框颜色
        $form_input_borderColor = (isset($settings->form_input_borderColor) && $settings->form_input_borderColor) ? $settings->form_input_borderColor : "rgba(0, 0, 0, .15)";
        // 表单输入框边框圆角
        $form_input_radius = (isset($settings->form_input_radius) && $settings->form_input_radius) ? $settings->form_input_radius : 10;
        // 表单输入框背景颜色
        $form_input_bgColor = (isset($settings->form_input_bgColor) && $settings->form_input_bgColor) ? $settings->form_input_bgColor : "rgba(0, 0, 0, 0)";
        // 表单输入框文字颜色
        $form_input_color = (isset($settings->form_input_color) && $settings->form_input_color) ? $settings->form_input_color : "#495057";
        // 表单富文本高度
        $form_area_height = (isset($settings->form_area_height) && $settings->form_area_height) ? $settings->form_area_height : 200;
        // 表单按钮位置
        $form_btn_align = (isset($settings->form_btn_align) && $settings->form_btn_align) ? $settings->form_btn_align : "center";
        // 按钮宽度
        $form_btn_width = (isset($settings->form_btn_width) && $settings->form_btn_width) ? $settings->form_btn_width : 80;
        // 按钮高度
        $form_btn_height = (isset($settings->form_btn_height) && $settings->form_btn_height) ? $settings->form_btn_height : 30;
        // 按钮边框圆角
        $form_btn_radius = (isset($settings->form_btn_radius) && $settings->form_btn_radius) ? $settings->form_btn_radius : 0;
        // 按钮边框宽度
        $form_btn_borderWidth = (isset($settings->form_btn_borderWidth) && $settings->form_btn_borderWidth) ? $settings->form_btn_borderWidth : 0;
        // 按钮边框颜色
        $form_btn_borderColor = (isset($settings->form_btn_borderColor) && $settings->form_btn_borderColor) ? $settings->form_btn_borderColor : "rgba(255, 255, 255, 0)";
        // 按钮文字大小
        $form_btn_fontsize = (isset($settings->form_btn_fontsize) && $settings->form_btn_fontsize) ? $settings->form_btn_fontsize : 14;
        // 按钮文字颜色
        $form_btn_color = (isset($settings->form_btn_color) && $settings->form_btn_color) ? $settings->form_btn_color : "rgba(255, 255, 255, 1)";
        // 按钮背景颜色
        $form_btn_bgColor = (isset($settings->form_btn_bgColor) && $settings->form_btn_bgColor) ? $settings->form_btn_bgColor : "#0E893B";
        // 滑过按钮文字颜色
        $form_btn_color_hover = (isset($settings->form_btn_color_hover) && $settings->form_btn_color_hover) ? $settings->form_btn_color_hover : "rgba(255, 255, 255, 1)";
        // 滑过按钮背景颜色
        $form_btn_bgColor_hover = (isset($settings->form_btn_bgColor_hover) && $settings->form_btn_bgColor_hover) ? $settings->form_btn_bgColor_hover : "#0E893B";
        // 【重置】按钮边框颜色
        $form_reset_borderColor = (isset($settings->form_reset_borderColor) && $settings->form_reset_borderColor) ? $settings->form_reset_borderColor : "rgba(255, 255, 255, 0)";
        // 【重置】按钮文字颜色
        $form_reset_color = (isset($settings->form_reset_color) && $settings->form_reset_color) ? $settings->form_reset_color : "rgba(255, 255, 255, 1)";
        // 【重置】按钮背景颜色
        $form_reset_bgColor = (isset($settings->form_reset_bgColor) && $settings->form_reset_bgColor) ? $settings->form_reset_bgColor : "#0E893B";
        // 滑过【重置】按钮边框颜色
        $form_reset_borderColor_hover = (isset($settings->form_reset_borderColor_hover) && $settings->form_reset_borderColor_hover) ? $settings->form_reset_borderColor_hover : "rgba(255, 255, 255, 0)";
        // 滑过【重置】按钮文字颜色
        $form_reset_color_hover = (isset($settings->form_reset_color_hover) && $settings->form_reset_color_hover) ? $settings->form_reset_color_hover : "rgba(255, 255, 255, 1)";
        // 滑过【重置】按钮背景颜色
        $form_reset_bgColor_hover = (isset($settings->form_reset_bgColor_hover) && $settings->form_reset_bgColor_hover) ? $settings->form_reset_bgColor_hover : "#0E893B";
        // 【应聘】按钮位置
        $recruit_btn_align = (isset($settings->recruit_btn_align) && $settings->recruit_btn_align) ? $settings->recruit_btn_align : "center";
        // 【应聘】按钮宽度
        $recruit_btn_width = (isset($settings->recruit_btn_width) && $settings->recruit_btn_width) ? $settings->recruit_btn_width : 80;
        // 【应聘】按钮高度
        $recruit_btn_height = (isset($settings->recruit_btn_height) && $settings->recruit_btn_height) ? $settings->recruit_btn_height : 30;
        // 【应聘】按钮边框圆角
        $recruit_btn_radius = (isset($settings->recruit_btn_radius) && $settings->recruit_btn_radius) ? $settings->recruit_btn_radius : 0;
        // 【应聘】按钮边框宽度
        $recruit_btn_borderWidth = (isset($settings->recruit_btn_borderWidth) && $settings->recruit_btn_borderWidth) ? $settings->recruit_btn_borderWidth : 0;
        // 【应聘】按钮边框颜色
        $recruit_btn_borderColor = (isset($settings->recruit_btn_borderColor) && $settings->recruit_btn_borderColor) ? $settings->recruit_btn_borderColor : "rgba(255, 255, 255, 0)";
        // 【应聘】按钮文字大小
        $recruit_btn_fontsize = (isset($settings->recruit_btn_fontsize) && $settings->recruit_btn_fontsize) ? $settings->recruit_btn_fontsize : 14;
        // 【应聘】按钮文字颜色
        $recruit_btn_color = (isset($settings->recruit_btn_color) && $settings->recruit_btn_color) ? $settings->recruit_btn_color : "rgba(255, 255, 255, 1)";
        // 【应聘】按钮背景颜色
        $recruit_btn_bgColor = (isset($settings->recruit_btn_bgColor) && $settings->recruit_btn_bgColor) ? $settings->recruit_btn_bgColor : "#0E893B";
        // 滑过【应聘】按钮文字颜色
        $recruit_btn_color_hover = (isset($settings->recruit_btn_color_hover) && $settings->recruit_btn_color_hover) ? $settings->recruit_btn_color_hover : "rgba(255, 255, 255, 1)";
        // 滑过【应聘】按钮背景颜色
        $recruit_btn_bgColor_hover = (isset($settings->recruit_btn_bgColor_hover) && $settings->recruit_btn_bgColor_hover) ? $settings->recruit_btn_bgColor_hover : "#0E893B";
        // 滑过【应聘】按钮边框颜色
        $recruit_btn_borderColor_hover = (isset($settings->recruit_btn_borderColor_hover) && $settings->recruit_btn_borderColor_hover) ? $settings->recruit_btn_borderColor_hover : "rgba(255, 255, 255, 0)";

        // form-item 宽度间距
        $base = $form_item_right * 2 / 3;

		$css = $addon_id . ' * {
            margin: 0;
            padding: 0;
        }
        ' . $addon_id . ' .r-title {
            text-align: ' . $c_title_align . ';
            font-size: ' . $c_title_fontsize . 'px;';
            if($c_title_fontWeight == 1) {
                $css .= 'font-weight: bold;';
            }
            $css .= 'margin-bottom: 12px;
            color: ' . $c_title_color . ';
        }
        ' . $addon_id . ' .content-box {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        ' . $addon_id . ' .form-container .form-box {
            display: flex;
            flex-wrap: wrap;
            font-size: 14px;
        }
        ' . $addon_id . ' .form-container .form-box .form-item {
            width: calc(100% / 3 - ' . $base. 'px);
            display: flex;
            align-items: center;
            margin-right: ' . $form_item_right . 'px;
            margin-bottom: ' . $form_item_bottom . 'px;
        }
        ' . $addon_id . ' .form-container .form-box .form-item:nth-child(3n) {
            margin-right: 0;
        }
        ' . $addon_id . ' .form-item label {
            width: ' . $form_label_width . 'px;
            text-align: right;
            font-size: ' . $form_label_fontsize . 'px;
            color: ' . $form_label_color . ';
        }
        ' . $addon_id . ' .form-item input[type="text"],' . $addon_id . ' .form-item input[type="date"] {
            height: 42px;
            line-height: 42px;
            width: calc(100% - ' . $form_label_width . 'px - 10px);
            padding: 0 10px;
            box-sizing: border-box;
            border: ' . $form_input_borderColor . ' solid 1px;
            border-radius: ' . $form_input_radius . 'px;
            color: ' . $form_input_color . ';
            background-color: ' . $form_input_bgColor . ';
        }
        ' . $addon_id . ' .form-item .tag {
            color: #bf0719;
            margin-left: 5px;
        }
        ' . $addon_id . ' .form-container .form-box .form-item.area {
            width: 100%;
            margin-right: 0;
        }
        ' . $addon_id . ' .form-item.area label {
            width: ' . $form_label_fontsize . 'px;
            margin-right: 5px;
        }
        ' . $addon_id . ' .form-item.area textarea {
            width: calc(100% - ' . $form_label_fontsize. 'px - 5px);
            height: ' . $form_area_height . 'px;
            border: ' . $form_input_borderColor . ' solid 1px;
            border-radius: ' . $form_input_radius . 'px;
            color: ' . $form_input_color . ';
            background-color: ' . $form_input_bgColor . ';
            resize: none;
            padding: 20px;
            box-sizing: border-box;
        }
        ' . $addon_id . ' .btn-box {
            display: flex;
            align-items: center;
            justify-content: ' . $form_btn_align . ';
            margin-bottom: 40px;
            margin-top:20px;
        }
        ' . $addon_id . ' .btn-box span {
            width: ' . $form_btn_width . 'px;
            height: ' . $form_btn_height . 'px;
            line-height: ' . $form_btn_height . 'px;
            border-radius: ' . $form_btn_radius . 'px;
            border: ' . $form_btn_borderColor. ' solid ' . $form_btn_borderWidth . 'px;
            text-align: center;
            margin-right: 30px;
            background-color: ' . $form_btn_bgColor . ';
            color: ' . $form_btn_color . ';
            font-size: ' . $form_btn_fontsize . 'px;
            cursor: pointer;
        }
        ' . $addon_id . ' .btn-box span:hover {
            background-color: ' . $form_btn_bgColor_hover . ';
            color: ' . $form_btn_color_hover . ';
        }
        ' . $addon_id . ' .btn-box span:last-child {
            margin-right: 0px;
        }
        ' . $addon_id . ' .btn-box span.sure-btn {
            
        }
        ' . $addon_id . ' .btn-box span.reset-btn {
            border-color: ' . $form_reset_borderColor . ';
            background-color: ' . $form_reset_bgColor . ';
            color: ' . $form_reset_color . ';
        }
        ' . $addon_id . ' .btn-box span.reset-btn:hover {
            border-color: ' . $form_reset_borderColor_hover . ';
            background-color: ' . $form_reset_bgColor_hover . ';
            color: ' . $form_reset_color_hover . ';
        }
        ' . $addon_id . ' .recruit-btn-box {
            display: flex;
            align-items: center;
            justify-content: ' . $recruit_btn_align . ';
        }
        ' . $addon_id . ' .recruit-btn-box a {
            width: ' . $recruit_btn_width . 'px;
            height: ' . $recruit_btn_height . 'px;
            line-height: ' . $recruit_btn_height . 'px;
            border-radius: ' . $recruit_btn_radius . 'px;
            border: ' . $recruit_btn_borderColor. ' solid ' . $recruit_btn_borderWidth . 'px;
            text-align: center;
            background-color: ' . $recruit_btn_bgColor . ';
            color: ' . $recruit_btn_color . ';
            font-size: ' . $recruit_btn_fontsize . 'px;
            cursor: pointer;
        }
        ' . $addon_id . ' .recruit-btn-box a:hover {
            border-color: ' . $recruit_btn_borderColor_hover . ';
            background-color: ' . $recruit_btn_bgColor_hover . ';
            color: ' . $recruit_btn_color_hover . ';
        }
        @media only screen and (max-width: 750px) { 
          ' . $addon_id . ' .form-container .form-box .form-item{
            width: 100%!important;
            margin-right: 0px!important;
          }
        }

        ';
		return $css;

	}

	public static function getTemplate()
	{
		$output = '
		<#
		    var addonId = "#jwpf-addon-" + data.id;
		    
		    // 详情布局
		    var recruit_layout = data.recruit_layout || "type01";
		    
		    // 显示详情内容
		    var show_content = data.show_content || 0;
		    // 标题文字位置
		    var c_title_align = data.c_title_align || "center";
		    // 标题文字大小
		    var c_title_fontsize = data.c_title_fontsize || 20;
		    // 标题头文字颜色
		    var c_title_color = data.c_title_color || "rgba(0, 0, 0, 1)";
		    // 标题文字加粗
		    var c_title_fontWeight = data.c_title_fontWeight || 0;
		    // 显示表单
		    var show_form = data.show_form || 0;
		    // 表单项间距
		    var form_item_right = data.form_item_right || 20;
		    // 表单项底部距离
		    var form_item_bottom = data.form_item_bottom || 20;
		    // 表单标签宽度
		    var form_label_width = data.form_label_width || 84;
		    // 表单标签文字大小
		    var form_label_fontsize = data.form_label_fontsize || 14;
		    // 表单标签文字颜色
		    var form_label_color = data.form_label_color || "rgba(0, 0, 0, 1)";
		    // 表单输入框边框颜色
		    var form_input_borderColor = data.form_input_borderColor || "rgba(0, 0, 0, .15)";
		    // 表单输入框边框圆角
		    var form_input_radius = data.form_input_radius || 10;
		    // 表单输入框背景颜色
		    var form_input_bgColor = data.form_input_bgColor || "rgba(0, 0, 0, 0)";
		    // 表单输入框文字颜色
		    var form_input_color = data.form_input_color || "#495057";
		    // 表单富文本高度
		    var form_area_height = data.form_area_height || 200;
		    // 表单按钮位置
		    var form_btn_align = data.form_btn_align || "center";
		    // 按钮宽度
		    var form_btn_width = data.form_btn_width || 80;
		    // 按钮高度
		    var form_btn_height = data.form_btn_height || 30;
		    // 按钮边框圆角
		    var form_btn_radius = data.form_btn_radius || 0;
		    // 按钮边框宽度
		    var form_btn_borderWidth = data.form_btn_borderWidth || 0;
		    // 按钮边框颜色
		    var form_btn_borderColor = data.form_btn_borderColor || "rgba(255, 255, 255, 0)";
		    // 按钮文字大小
		    var form_btn_fontsize = data.form_btn_fontsize || 14;
		    // 按钮文字颜色
		    var form_btn_color = data.form_btn_color || "rgba(255, 255, 255, 1)";
		    // 按钮背景颜色
		    var form_btn_bgColor = data.form_btn_bgColor || "#0E893B";
		    // 滑过按钮文字颜色
		    var form_btn_color_hover = data.form_btn_color_hover || "rgba(255, 255, 255, 1)";
		    // 滑过按钮背景颜色
		    var form_btn_bgColor_hover = data.form_btn_bgColor_hover || "#0E893B";
		    // 【重置】按钮边框颜色
		    var form_reset_borderColor = data.form_reset_borderColor || "rgba(255, 255, 255, 0)";
		    // 【重置】按钮文字颜色
		    var form_reset_color = data.form_reset_color || "rgba(255, 255, 255, 1)";
		    // 【重置】按钮背景颜色
		    var form_reset_bgColor = data.form_reset_bgColor || "#0E893B";
		    // 滑过【重置】按钮边框颜色
		    var form_reset_borderColor_hover = data.form_reset_borderColor_hover || "rgba(255, 255, 255, 0)";
		    // 滑过【重置】按钮文字颜色
		    var form_reset_color_hover = data.form_reset_color_hover || "rgba(255, 255, 255, 1)";
		    // 滑过【重置】按钮背景颜色
		    var form_reset_bgColor_hover = data.form_reset_bgColor_hover || "#0E893B";
		    // 显示【身份证号】项
		    var form_item_04 = data.form_item_04 || 0;
		    // 【身份证号】项必填
		    var form_item_04_has = data.form_item_04_has || 0;
		    // 显示【婚否】项
		    var form_item_05 = data.form_item_05 || 0;
		    // 【婚否】项必填
		    var form_item_05_has = data.form_item_05_has || 0;
		    // 显示【职称/资质】项
		    var form_item_06 = data.form_item_06 || 0;
		    // 【职称/资质】项必填
		    var form_item_06_has = data.form_item_06_has || 0;
		    // 显示【毕业院校】项
		    var form_item_07 = data.form_item_07 || 0;
		    // 【毕业院校】项必填
		    var form_item_07_has = data.form_item_07_has || 0;
		    // 显示【专业】项
		    var form_item_08 = data.form_item_08 || 0;
		    // 【专业】项必填
		    var form_item_08_has = data.form_item_08_has || 0;
		    // 显示【联系地址】项
		    var form_item_10 = data.form_item_10 || 0;
		    // 【联系地址】项必填
		    var form_item_10_has = data.form_item_10_has || 0;
		    // 显示【籍贯】项
		    var form_item_11 = data.form_item_11 || 0;
		    // 【籍贯】项必填
		    var form_item_11_has = data.form_item_11_has || 0;
		    // 显示【期望薪金】项
		    var form_item_14 = data.form_item_14 || 0;
		    // 【期望薪金】项必填
		    var form_item_14_has = data.form_item_14_has || 0;
		    // 显示【学习经历】项
		    var form_item_15 = data.form_item_15 || 0;
		    // 【学习经历】项必填
		    var form_item_15_has = data.form_item_15_has || 0;
		    // 显示【工作经历】项
		    var form_item_16 = data.form_item_16 || 0;
		    // 【工作经历】项必填
		    var form_item_16_has = data.form_item_16_has || 0;
		    // 显示【对公司要求】项
		    var form_item_17 = data.form_item_17 || 0;
		    // 【对公司要求】项必填
		    var form_item_17_has = data.form_item_17_has || 0;
		    // 显示【特长爱好】项
		    var form_item_18 = data.form_item_18 || 0;
		    // 【特长爱好】项必填
		    var form_item_18_has = data.form_item_18_has || 0;
		    // 显示【应聘】按钮
		    var show_recruit_btn = data.show_recruit_btn || 0;
		    // 【应聘】按钮位置
		    var recruit_btn_align = data.recruit_btn_align || "center";
		    // 【应聘】按钮文字
		    var recruit_btn_text = data.recruit_btn_text || "应聘";
		    // 【应聘】按钮宽度
		    var recruit_btn_width = data.recruit_btn_width || 80;
		    // 【应聘】按钮高度
		    var recruit_btn_height = data.recruit_btn_height || 30;
		    // 【应聘】按钮边框圆角
		    var recruit_btn_radius = data.recruit_btn_radius || 0;
		    // 【应聘】按钮边框宽度
		    var recruit_btn_borderWidth = data.recruit_btn_borderWidth || 0;
		    // 【应聘】按钮边框颜色
		    var recruit_btn_borderColor = data.recruit_btn_borderColor || "rgba(255, 255, 255, 0)";
		    // 【应聘】按钮文字大小
		    var recruit_btn_fontsize = data.recruit_btn_fontsize || 14;
		    // 【应聘】按钮文字颜色
		    var recruit_btn_color = data.recruit_btn_color || "rgba(255, 255, 255, 1)";
		    // 【应聘】按钮背景颜色
		    var recruit_btn_bgColor = data.recruit_btn_bgColor || "#0E893B";
		    // 滑过【应聘】按钮文字颜色
		    var recruit_btn_color_hover = data.recruit_btn_color_hover || "rgba(255, 255, 255, 1)";
		    // 滑过【应聘】按钮背景颜色
		    var recruit_btn_bgColor_hover = data.recruit_btn_bgColor_hover || "#0E893B";
		    // 滑过【应聘】按钮边框颜色
		    var recruit_btn_borderColor_hover = data.recruit_btn_borderColor_hover || "rgba(255, 255, 255, 0)";
		    		    
		    // form-item 宽度间距
		    var base = form_item_right * 2 / 3;
		#>
		<style>
			{{ addonId }} * {
			    margin: 0;
			    padding: 0;
            }
            {{ addonId }} .r-title {
			    text-align: {{ c_title_align }};
			    font-size: {{ c_title_fontsize }}px;
			    <# if(c_title_fontWeight == 1) { #>
			        font-weight: bold;
			    <# } #>
			    margin-bottom: 12px;
			    color: {{ c_title_color }};
            }
            {{ addonId }} .content-box {
                font-size: 16px;
                line-height: 1.5;
                margin-bottom: 20px;
            }
            {{ addonId }} .form-container .form-box {
                display: flex;
                flex-wrap: wrap;
                font-size: 14px;
            }
            {{ addonId }} .form-container .form-box .form-item {
                width: calc(100% / 3 - {{ base }}px);
                display: flex;
                align-items: center;
                margin-right: {{ form_item_right }}px;
                margin-bottom: {{ form_item_bottom }}px;
            }
            {{ addonId }} .form-container .form-box .form-item:nth-child(3n) {
                margin-right: 0;
            }
            {{ addonId }} .form-item label {
                width: {{ form_label_width }}px;
                text-align: right;
                font-size: {{ form_label_fontsize }}px;
                color: {{ form_label_color }};
            }
            {{ addonId }} .form-item input[type="text"],{{ addonId }} .form-item input[type="date"] {
                height: 42px;
                line-height: 42px;
                width: calc(100% - {{ form_label_width }}px - 10px);
                padding: 0 10px;
                box-sizing: border-box;
                border: {{ form_input_borderColor }} solid 1px;
                border-radius: {{ form_input_radius }}px;
                color: {{ form_input_color }};
                background-color: {{ form_input_bgColor }};
            }
            {{ addonId }} .form-item .tag {
                color: #bf0719;
                margin-left: 5px;
            }
            {{ addonId }} .form-container .form-box .form-item.area {
                width: 100%;
                margin-right: 0;
            }
            {{ addonId }} .form-item.area label {
                width: {{ form_label_fontsize }}px;
                margin-right: 5px;
            }
            {{ addonId }} .form-item.area textarea {
                width: calc(100% - {{form_label_fontsize}}px - 5px);
                height: {{ form_area_height }}px;
                border: {{ form_input_borderColor }} solid 1px;
                border-radius: {{ form_input_radius }}px;
                color: {{ form_input_color }};
                background-color: {{ form_input_bgColor }};
                resize: none;
                padding: 20px;
                box-sizing: border-box;
            }
            {{ addonId }} .btn-box {
                display: flex;
                align-items: center;
                justify-content: {{ form_btn_align }};
                margin-bottom: 40px;
            }
            {{ addonId }} .btn-box span {
                width: {{ form_btn_width }}px;
                height: {{ form_btn_height }}px;
                line-height: {{ form_btn_height }}px;
                border-radius: {{ form_btn_radius }}px;
                border: {{ form_btn_borderColor }} solid {{ form_btn_borderWidth }}px;
                text-align: center;
                margin-right: 30px;
                background-color: {{ form_btn_bgColor }};
                color: {{ form_btn_color }};
                font-size: {{ form_btn_fontsize }}px;
                cursor: pointer;
            }
            {{ addonId }} .btn-box span:hover {
                background-color: {{ form_btn_bgColor_hover }};
                color: {{ form_btn_color_hover }};
            }
            {{ addonId }} .btn-box span:last-child {
                margin-right: 0px;
            }
            {{ addonId }} .btn-box span.sure-btn {
                
            }
            {{ addonId }} .btn-box span.reset-btn {
                border-color: {{ form_reset_borderColor }};
                background-color: {{ form_reset_bgColor }};
                color: {{ form_reset_color }};
            }
            {{ addonId }} .btn-box span.reset-btn:hover {
                border-color: {{ form_reset_borderColor_hover }};
                background-color: {{ form_reset_bgColor_hover }};
                color: {{ form_reset_color_hover }};
            }
            {{ addonId }} .recruit-btn-box {
                display: flex;
                align-items: center;
                justify-content: {{ recruit_btn_align }};
            }
            {{ addonId }} .recruit-btn-box span {
                width: {{ recruit_btn_width }}px;
                height: {{ recruit_btn_height }}px;
                line-height: {{ recruit_btn_height }}px;
                border-radius: {{ recruit_btn_radius }}px;
                border: {{ recruit_btn_borderColor }} solid {{ recruit_btn_borderWidth }}px;
                text-align: center;
                background-color: {{ recruit_btn_bgColor }};
                color: {{ recruit_btn_color }};
                font-size: {{ recruit_btn_fontsize }}px;
                cursor: pointer;
            }
            {{ addonId }} .recruit-btn-box span:hover {
                border-color: {{ recruit_btn_borderColor_hover }};
                background-color: {{ recruit_btn_bgColor_hover }};
                color: {{ recruit_btn_color_hover }};
            }
		</style>
		<div class="recruit-container">
		    <# if(show_content == 1) { #>
                <h2 class="r-title">示例职位名称</h2> 
                <div class="content-box">
                    示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文，示例职位详情正文。
                </div>
            <# } #>
            <# if(show_form == 1) { #>
                <div class="form-container">
                    <div class="form-box">
                        <div class="form-item">
                            <label>本人姓名：</label>
                            <input type="text" id="userName">
                            <span class="tag">*</span>
                        </div>
                        <div class="form-item">
                            <label>性别：</label>
                            <div class="radio-box">
                                <input type="radio" name="sex" value="男" checked>&nbsp;男&nbsp;&nbsp;
                                <input type="radio" name="sex" value="女">&nbsp;女
                            </div>
                            <span class="tag">*</span>
                        </div>
                        <div class="form-item">
                            <label>出生年月：</label>
                            <input type="date" id="birthday">
                            <span class="tag">*</span>
                        </div>
                        <# if(form_item_04 == 1) { #>
                            <div class="form-item">
                                <label>身份证号：</label>
                                <input type="text" id="cardNumber">
                                <# if(form_item_04_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_05 == 1) { #>
                            <div class="form-item">
                                <label>婚否：</label>
                                <div class="radio-box">
                                    <input type="radio" name="wed" value="已婚" checked>&nbsp;已婚&nbsp;&nbsp;
                                    <input type="radio" name="wed" value="未婚">&nbsp;未婚
                                </div>
                                <# if(form_item_05_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_06 == 1) { #>
                            <div class="form-item">
                                <label>职称/资质：</label>
                                <input type="text" id="qualifications">
                                <# if(form_item_06_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_07 == 1) { #>
                            <div class="form-item">
                                <label>毕业院校：</label>
                                <input type="text" id="school">
                                <# if(form_item_07_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_08 == 1) { #>
                            <div class="form-item">
                                <label>专业：</label>
                                <input type="text" id="major">
                                <# if(form_item_08_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <div class="form-item">
                            <label>	最高学历：</label>
                            <input type="text" id="education">
                            <span class="tag">*</span>
                        </div>
                        <# if(form_item_10 == 1) { #>
                            <div class="form-item">
                                <label>联系地址：</label>
                                <input type="text" id="address">
                                <# if(form_item_10_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_11 == 1) { #>
                            <div class="form-item">
                                <label>籍贯：</label>
                                <input type="text" id="place">
                                <# if(form_item_11_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <div class="form-item">
                            <label>联系电话：</label>
                            <input type="text" id="phone">
                            <span class="tag">*</span>
                        </div>
                        <div class="form-item">
                            <label>应聘职位：</label>
                            <input type="text" id="job">
                            <span class="tag">*</span>
                        </div>
                        <# if(form_item_14 == 1) { #>
                            <div class="form-item">
                                <label>期望薪金：</label>
                                <input type="text" id="money">
                                <# if(form_item_14_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_15 == 1) { #>
                            <div class="form-item area">
                                <label>学习经历</label>
                                <textarea class="jwpf-form-control" name="" id="study" cols="30" rows="10"></textarea>
                                <# if(form_item_15_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_16 == 1) { #>
                            <div class="form-item area">
                                <label>工作经历</label>
                                <textarea class="jwpf-form-control" name="" id="work" cols="30" rows="10"></textarea>
                                <# if(form_item_16_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_17 == 1) { #>
                            <div class="form-item area">
                                <label>对公司要求</label>
                                <textarea class="jwpf-form-control" name="" id="ask" cols="30" rows="10"></textarea>
                                <# if(form_item_17_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                        <# if(form_item_18 == 1) { #>
                            <div class="form-item area">
                                <label>特长爱好</label>
                                <textarea class="jwpf-form-control" name="" id="specialty" cols="30" rows="10"></textarea>
                                <# if(form_item_18_has == 1) { #>
                                    <span class="tag">*</span>
                                <# } #>
                            </div>
                        <# } #>
                    </div>
                    <div class="btn-box">
                        <span class="sure-btn">确定</span>
                        <span class="reset-btn">重置</span>
                    </div>
                </div>
            <# } #>
            <# if(show_recruit_btn == 1) { #>
                <div class="recruit-btn-box">
                    <span>{{ recruit_btn_text }}</span>
                </div>
            <# } #>
        </div>
		';
		return $output;
	}

}
