<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');


$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'scale_swiper',
        'title' => JText::_('异形轮播'),
        'desc' => JText::_('异形轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),


                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),

                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_goods')['list'],
                ),


                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('排序'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(

                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),

                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('显示几条'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10'
                ),

                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),

                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播元素样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                ),




                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'depends' => array(
                        array('carousel_options', '=', '1'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'item_title'=>'标题',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

                        'item_title' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项标题'),
                            'desc' => JText::_('轮播项标题'),
                        ),
                    ),
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 830,
                        'sm' => 600,
                        'xs' => 300
                    ),
                    'responsive' => true,
                ),
                'carousel_item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'responsive' => true,
                    'std' => array('md' => 3, 'sm' => 2, 'xs' => 1),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'carousel_margin' => array(
                    'type' => 'number',
                    'title' => JText::_('slide之间的距离'),
                    'desc' => JText::_('slide之间的距离'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 33,
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 1
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 2500
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 4500
                ),
                'item_content_verti_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容垂直对齐'),
                    'desc' => JText::_('内容垂直对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('顶部对齐'),
                        'middle' => JText::_('中间对齐'),
                        'bottom' => JText::_('底部对齐'),
                    ),
                    'std' => 'middle',
                ),
                'item_content_hori_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容水平对齐'),
                    'desc' => JText::_('内容水平对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'values' => array(
                        'left' => JText::_('左对齐'),
                        'center' => JText::_('居中'),
                        'right' => JText::_('右对齐'),
                    ),
                    'std' => 'center',
                ),
                'content' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                //Title style
//                'title_c' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('标题字数限制'),
//                    'std' => '10',
//                    'max' => 20,
//                    'depends' => array(
//                        array('carousel_options', '=', 'item_style'),
//                    ),
//                ),
                'content_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => 22,
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive' => true,
                    'max' => 100,
                ),
                'content_title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => '60',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 100,
                ),
                'content_title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('字体'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-heading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(

                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(

                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(

                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'#333',
                ),
                'content_title_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10',
                    'depends' => array(

                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                    'responsive' => true
                ),

            ),
        )
    )
);
