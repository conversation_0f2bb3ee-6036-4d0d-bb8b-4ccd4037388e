<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'product_list',
        'title' => JText::_('产品列表'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '产品',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => '',
                ),

                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '!=', 'type26'),
                    ),
                ),

                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }',
                    ),
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px',
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),

                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'responsive' => true,
                    'max' => 400,
                ),

                'separator_options' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS'),
                ),
                'pro_type' => array(
                    'type' => 'thumbnail',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types1.png', //'标题覆盖图片',
                        'type2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types2.png', //'标题在图片下方',
                        'type3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types3.png', //'产品布局3',
                        'type4' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types4.png', //'产品布局4',
                        'type5' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types5.png', //'产品布局5',
                        'type6' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types6.png', //'产品布局6',
                        'type7' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types7.png', //'产品布局7',
                        'type8' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types8.png', //'产品布局8',
                        'type9' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types9.png', //'产品布局9',
                        'type10' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types10.png', //'产品布局10',
                        'type11' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types11.png', //'产品布局11',
                        'type12' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types12.png', //'产品布局12-产品图片展示',
                        // 'type13' => '产品布局13',
                        'type14' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types14.png', //'产品布局14',
                        'type15' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types15.png', //'自定义产品列表1',
                        'type16' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types16.png', //'产品布局16',
                        // 'type17' => '产品布局17',
                        // 'type18' => '产品布局18',
                        'type19' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types19.png', //'产品布局19',
                        'type20' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types20.png', //'产品布局20',
                        // 'type21' => '产品布局21',
                        'type22' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types22.png', //'产品布局22',
                        'type23' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type23.png', //'产品布局23',
                        'type24' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types24.png', //'自定义产品列表2',
                        'type25' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types25.png', //'产品布局25',
                        'type26' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types26.png', //'产品布局26',
                        'type27' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/types27.png', //'产品布局27',
                        'type28' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type28.png', //'产品布局28',
                        'type29' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type29.png', //'产品布局29',
                        'type30' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/30.png', //'产品布局30',
                        'type31' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type31.png', //'产品布局31',
                        'type32' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type32.png', //'产品布局32',
                        'type33' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type33.png', //'产品布局33',
                        'type34' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type34.png', //'产品布局34',
                        'type35' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type35.png', //'产品布局35',
                        'type36' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type36.png', //'产品布局36',
                        'type37' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type37.png', //'产品布局37',
                        'type38' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type38.png', //'产品布局38',
                        'type39' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type39.png', //'产品布局39',
                        'type40' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type40.png', //'产品布局40',
                        'type41' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type41.png', //'产品布局41',
                        'type42' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type42.png', //'产品布局42',
                        'type43' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type43.png', //'产品布局43',
                        'type44' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type44.png', //'产品布局44',
                        'type45' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type45.png', //'产品布局45',
                        'type46' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type46.png', //'产品布局46',
                        'type47' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type47.png', //'产品布局47',
                        'type48' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type48.jpg', //'产品布局48',
                        'type49' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type49.jpg', //'产品布局49',
                        'type50' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type50.jpg', //'产品布局50',
                        'type51' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type51.jpg', //'产品布局51',
                        'type52' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type52.jpg', //'产品布局52',
                        'type53' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type53.png', //'产品布局53',
                        'type54' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type54.png', //'产品布局54',
                        'type55' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type55.png', //'产品布局55',
                        'type56' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type56.png', //'产品布局56',
                        'type57' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type57.png', //'产品布局57',
                        'type58' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type58.jpg', //'产品布局58',
                        'type59' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type59.png', //'产品布局59',
                        'type60' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type60.png', //'产品布60',
                        'type61' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type61.png', //'产品布局61',
                        'type62' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type62.png', //'产品布局62',
                        'type63' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type63.jpg', //'产品布局63',
                        'type64' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type64.jpg', //'产品布局64',
                        'type65' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type65.jpg', //'产品布局65',
                        'type66' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type66.jpg', //'产品布局66',
                        'type67' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type67.png', //'产品布局67',
                        'type68' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_list/assets/images/type68.png', //'产品布局68',
                        "type69" => str_replace("administrator/", "", JURI::base()) . "components/com_jwpagefactory/addons/product_list/assets/images/type69.png", //'产品布局69',
                        "type70" => str_replace("administrator/", "", JURI::base()) . "components/com_jwpagefactory/addons/product_list/assets/images/type70.jpg", //'产品布局70',
                        "type71" => str_replace("administrator/", "", JURI::base()) . "components/com_jwpagefactory/addons/product_list/assets/images/type71.png", //'产品布局71',
                        "type72" => str_replace("administrator/", "", JURI::base()) . "components/com_jwpagefactory/addons/product_list/assets/images/type72.jpg", //'产品布局72',
                    ),
                    'std' => 'type1',
                ),

                // 2022.3.17布局53
                'type53_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('列表内间距'),
                    'std' => array('md' => '20px 0px 20px 0px', 'sm' => '20px 0px 20px 0px', 'xs' => '10px 0px 10px 0px'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type53_img_width_input' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片宽度'),
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => array('md' => '280', 'sm' => '240', 'xs' => '90'),
                    'responsive' => true,
                ),
                'type53_img_height_input' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => array('md' => '335', 'sm' => '289', 'xs' => '108'),
                    'responsive' => true,
                ),
                'type53_left_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容左间距'),
                    'std' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type53_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('more划过颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                    'std' => '#f00',
                ),
                'type53_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => 16,
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type53_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type53_label_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签字体大小(取后台标签1字段)'),
                    'std' => 12,
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type53_label_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签字体颜色'),
                    'std' => '#555',
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),

                //
                // 2022.2.24布局47
                'type47_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表高度'),
                    'depends' => array('pro_type' => 'type47'),
                    'max' => 700,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array('md' => '490', 'sm' => '400', 'xs' => '300'),
                ),
                'type47_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'depends' => array('pro_type' => 'type47'),
                    'max' => 700,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array('md' => '300', 'sm' => '250', 'xs' => '200'),
                ),
                'type47_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type47'),
                    'std' => '#9a9a9a',
                ),
                'type47_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type47'),
                    'max' => 50,
                    'min' => 0,
                    'std' => 18,
                ),
                'type47_label_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签字体大小'),
                    'depends' => array('pro_type' => 'type47'),
                    'max' => 50,
                    'min' => 0,
                    'std' => 13,
                ),
                'type47_jianjie_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array('pro_type' => 'type47'),
                    'std' => '#949494',
                ),
                'type47_jianjie_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array('pro_type' => 'type47'),
                    'max' => 50,
                    'min' => 0,
                    'std' => 12,
                ),
                'type47_text_padding' => array(
                    'type' => 'padding',
                    'title' => '文本内间距',
                    'std' => '30px 25px 30px 25px',
                    'depends' => array(
                        array('pro_type', '=', 'type47'),
                    ),
                ),
                'type47_hv_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过图片时显示的符号背景色'),
                    'depends' => array('pro_type' => 'type47'),
                    'std' => '#404040',
                ),

                // 2022.3.15布局49
                'type49_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'depends' => array('pro_type' => 'type49'),
                    'max' => 700,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array('md' => '360', 'sm' => '', 'xs' => ''),
                ),
                'type49_title_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('标题滑入字体颜色'),
                    'depends' => array('pro_type' => 'type49'),
                    'std' => '#fff',
                ),
                'type49_desc_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('简介滑入字体颜色'),
                    'depends' => array('pro_type' => 'type49'),
                    'std' => '#fff',
                ),
                'img_style_type49' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('pro_type', '=', 'type49'),
                    ),
                ),
                // 2022.3.15布局50
                'type50_img_height_pc' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc图片高度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => 320,
                ),
                'type50_img_height_xm' => array(
                    'type' => 'slider',
                    'title' => JText::_('移动端图片高度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => 150,
                ),
                'type50_title_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('标题滑入字体颜色'),
                    'depends' => array('pro_type' => 'type50'),
                    'std' => '#000',
                ),
                'type50_desc_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('简介滑入字体颜色'),
                    'depends' => array('pro_type' => 'type50'),
                    'std' => '#848484',
                ),
                'type50_bg_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('滑入背景颜色'),
                    'depends' => array('pro_type' => 'type50'),
                    'std' => '',
                ),
                'type50_jg_button' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('间隔是否开启'),
                    'depends' => array(
                        array('pro_type', '=', 'type50'),
                    ),
                    'std' => 0,
                ),
                'type50_border_top_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑入上边框宽度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 10,
                    'min' => 0,
                    'std' => 2,
                ),
                'type50_border_bottom_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑入下边框宽度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 10,
                    'min' => 0,
                    'std' => 2,
                ),
                'type50_border_left_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑入左边框宽度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 10,
                    'min' => 0,
                    'std' => 0,
                ),
                'type50_border_right_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑入右边框宽度'),
                    'depends' => array('pro_type' => 'type50'),
                    'max' => 10,
                    'min' => 0,
                    'std' => 0,
                ),
                'type50_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'depends' => array('pro_type' => 'type50'),
                    'std' => '#4a4a4a',
                ),
                'img_style_type50' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('pro_type', '=', 'type50'),
                    ),
                ),
                // 2022.3.22布局54
                'type54_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type54'),
                    'std' => '#404040',
                ),
                'type54_title_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('标题滑入字体颜色'),
                    'depends' => array('pro_type' => 'type54'),
                    'std' => '#e6ab43',
                ),
                'type54_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type54'),
                    'max' => 100,
                    'min' => 12,
                    'std' => 24,
                ),
                'type54_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array('pro_type' => 'type54'),
                    'std' => '#999',
                ),
                'type54_desc_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('简介滑入字体颜色'),
                    'depends' => array('pro_type' => 'type54'),
                    'std' => '#999',
                ),
                'type54_desc_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array('pro_type' => 'type54'),
                    'max' => 100,
                    'min' => 12,
                    'std' => 15,
                ),
                'type54_left_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('滑入左侧分割线颜色'),
                    'depends' => array('pro_type' => 'type54'),
                    'std' => '#e6ab43',
                ),
                // 2022.3.24布局55
                'type55_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#333',
                ),
                'type55_title_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('标题滑入字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#909744',
                ),
                'type55_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type55'),
                    'max' => 100,
                    'min' => 12,
                    'std' => 18,
                ),
                'type55_title_two_color' => array(
                    'type' => 'color',
                    'title' => JText::_('副标题字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#9fa0a0',
                ),
                'type55_title_two_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('副标题滑入字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#868585',
                ),
                'type55_title_two_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('副标题字体大小'),
                    'depends' => array('pro_type' => 'type55'),
                    'max' => 100,
                    'min' => 12,
                    'std' => 13,
                ),
                'type55_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#1d1e1f',
                ),
                'type55_desc_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('简介滑入字体颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#1d1e1f',
                ),
                'type55_desc_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array('pro_type' => 'type55'),
                    'max' => 100,
                    'min' => 12,
                    'std' => 13,
                ),
                'type55_line_color_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('滑入分割线颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#ccc',
                ),
                'type55_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('列表背景颜色'),
                    'depends' => array('pro_type' => 'type55'),
                    'std' => '#fff',
                ),
                // 2021.12.30布局39
                'type39_borderradius' => array(
                    'type' => 'slider',
                    'title' => JText::_('圆角'),
                    'depends' => array('pro_type' => 'type39'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '10',
                ),
                'type39_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('图片遮罩色'),
                    'depends' => array('pro_type' => 'type39'),
                    'std' => 'rgba(0,0,0,0.4)',
                ),
                'type39_flcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('分类字体颜色'),
                    'depends' => array('pro_type' => 'type39'),
                    'std' => '#fff',
                ),
                'type39_flsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('分类字体大小'),
                    'depends' => array('pro_type' => 'type39'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '14',
                ),
                'type39_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type39'),
                    'std' => '#fff',
                ),
                'type39_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type39'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '24',
                ),
                'type39_title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题位置间距'),
                    'std' => array('md' => '120', 'sm' => '80', 'xs' => '40'),
                    'depends' => array('pro_type' => 'type39'),
                    'responsive' => true,
                    'max' => 400,
                    'min' => 0,
                ),
                'type39_button_title' => array(
                    'type' => 'text',
                    'title' => JText::_('查看更多文字'),
                    'depends' => array('pro_type' => 'type39'),
                    'std' => '点击查看更多',
                ),
                'type39_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('查看更多字体颜色'),
                    'depends' => array('pro_type' => 'type39'),
                    'std' => '#fff',
                ),
                'type39_button_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('查看更多字体大小'),
                    'depends' => array('pro_type' => 'type39'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '18',
                ),
                'type39_img_on' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示右上角图片'),
                    'depends' => array(
                        array('pro_type', '=', 'type39'),
                    ),
                    'std' => 1,
                ),
                'type39_img' => array(
                    'type' => 'media',
                    'title' => JText::_('右上角图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211230/6a88e7dae836850ad763539b40bc2b7c.png',
                    'depends' => array(
                        array('pro_type', '=', 'type39'),
                        array('type39_img_on', '!=', '0'),
                    ),
                ),
                'art23_two_button' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示两行'),
                    'depends' => array(
                        array('pro_type', '=', 'type23'),
                    ),
                    'std' => 0,
                ),
                // 布局39结束
                // 布局40开始
                'type40_type' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启滚动'),
                    'depends' => array('pro_type' => 'type40'),
                    'std' => '0',
                ),
                'type40_imgwidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片宽度'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_type', '=', '1'),
                    ),
                    'max' => 500,
                    'min' => 0,
                    'std' => '320',
                ),
                'type40_borderradius' => array(
                    'type' => 'slider',
                    'title' => JText::_('圆角'),
                    'depends' => array('pro_type' => 'type40'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '10',
                ),
                'type40_img_on' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示右上角图片文字'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                    ),
                    'std' => 1,
                ),
                'type40_img' => array(
                    'type' => 'media',
                    'title' => JText::_('右上角图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220106/225e3bba26314229ad1d351fdb0bd825.png',
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_img_on', '!=', '0'),
                    ),
                ),
                'type40_rightcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_img_on', '!=', '0'),
                    ),
                    'std' => '#fff',
                ),
                'type40_rightsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_img_on', '!=', '0'),
                    ),
                    'max' => 100,
                    'min' => 12,
                    'std' => '14',
                ),
                'type40_rlineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体行高'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_img_on', '!=', '0'),
                    ),
                    'max' => 100,
                    'min' => 10,
                    'std' => '88',
                ),
                'type40_type_on' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示分类'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                    ),
                    'std' => 1,
                ),
                'type40_flcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('分类字体颜色'),

                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_type_on', '!=', '0'),
                    ),
                    'std' => '#fff',
                ),
                'type40_flsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('分类字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_type_on', '!=', '0'),
                    ),
                    'max' => 100,
                    'min' => 12,
                    'std' => '14',
                ),
                'type40_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('分类字体行高'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_type_on', '!=', '0'),
                    ),
                    'max' => 100,
                    'min' => 10,
                    'std' => '60',
                ),
                'type40_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('分类背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                        array('type40_type_on', '!=', '0'),
                    ),
                    'std' => '#333',
                ),
                'type40_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type40'),
                    'std' => '#fff',
                ),
                'type40_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type40'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '24',
                ),
                'type40_title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体行高'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                    ),
                    'max' => 100,
                    'min' => 10,
                    'std' => '68',
                ),
                'type40_titbg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type40'),
                    ),
                    'std' => '#0e80d8',
                ),

                //     'pro_type' => array(
                //     'type' => 'select',
                //     'title' => '选择布局',
                //     'desc' => '选择布局',
                //     'values' => array(
                //         'type1' => '标题覆盖图片',
                //         'type2' => '标题在图片下方',
                //         'type3' => '产品布局3',
                //         'type4' => '产品布局4',
                //         'type5' => '产品布局5',
                //         'type6' => '产品布局6',
                //         'type7' => '产品布局7',
                //         'type8' => '产品布局8',
                //         'type9' => '产品布局9',
                //         'type10' => '产品布局10',
                //         'type11' => '产品布局11',
                //         'type12' => '产品布局12-产品图片展示',
                //         // 'type13' => '产品布局13',
                //         'type14' => '产品布局14',
                //         'type15' => '自定义产品列表1',
                //         'type16' => '产品布局16',
                //         // 'type17' => '产品布局17',
                //         // 'type18' => '产品布局18',
                //         'type19' => '产品布局19',
                //         'type20' => '产品布局20',
                //         // 'type21' => '产品布局21',
                //         'type22' => '产品布局22',
                //         'type23' => '产品布局23',
                //         'type24' => '自定义产品列表2',
                //         'type25' => '产品布局25',
                //         'type26' => '产品布局26',
                //         'type27' => '产品布局27',
                //     ),
                //     'std' => 'type1',
                // ),
                'ys_bj_type23' => array(
                    'type' => 'select',
                    'title' => '选择展示内容',
                    'depends' => array('pro_type' => 'type23'),
                    'values' => array(
                        'ht' => '后台数据内容展示',
                        'zdy' => '自定义数据,图片展示',
                    ),
                    'std' => 'ht',
                ),

                'ys_bjcolor_type23' => array(
                    'type' => 'color',
                    'title' => '标题背景颜色',
                    'depends' => array('pro_type' => 'type23'),
                    'std' => 'rgba(179, 30, 35, 0.7)',
                ),

                'jw_image_carousel_item_type23' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
                    'depends' => array(
                        array('pro_type', '=', 'type23'),
                        array('ys_bj_type23', '=', 'zdy'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                    ),
                ),

                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从一级分类的第n个分类开始显示',
                    'std' => '1',
                    'depends' => array('pro_type' => 'type30'),
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类',
                    'desc' => '显示n条分类,默认10条',
                    'std' => '10',
                    'depends' => array('pro_type' => 'type30'),
                ),

                'jw_image_carousel_item_type24' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
                    'depends' => array(
                        array('pro_type', '=', 'type24'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                    ),
                ),

                'gl_show' => array(
                    'type' => 'slider',
                    'title' => JText::_('不高亮显示数量'),
                    'max' => 100,
                    'min' => 1,
                    'std' => 5,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type30'),
                    ),
                ),
                'mr_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧标题字体颜色'),
                    'std' => '#333333',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type30'),
                    ),
                ),
                'mr_title_color_gl' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧高亮标题字体颜色'),
                    'std' => '#0a4a9b',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type30'),
                    ),
                ),
                'right_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧标题字体颜色'),
                    'std' => '#0a4a9b',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type30'),
                    ),
                ),
                'right_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧内容字体颜色'),
                    'std' => '#626262',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type30'),
                    ),
                ),

                //2022.8.15 添加图片边框
                'type20_img_border' => array(
                    'type' => 'checkbox',
                    'title' => '开启图片边框',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'std' => 0,
                ),
                'type20_imgs_border' => array(
                    'type' => 'media',
                    'title' => '图片边框',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                        array('type20_img_border', '=', '1'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220815/ea209abe4fbb65e05f06908e5e20a355.png',
                ),
                'type20_border_padding' => array(
                    'type' => 'padding',
                    'title' => '图片外间距',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                        array('type20_img_border', '=', '1'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ), //

                'type20_img_w' => array(
                    'type' => 'slider',
                    'title' => '图片的宽度设置',
                    'desc' => '宽',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 250,
                ),

                'type20_img_h' => array(
                    'type' => 'slider',
                    'title' => '图片的高度设置',
                    'desc' => '高',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),

                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 200,
                ),

                'type20_img_img_num' => array(
                    'type' => 'slider',
                    'title' => '间距',
                    'desc' => '间距',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'max' => 10,
                    'min' => 1,
                    'std' => 5,
                ),

                'type20_img_button_num' => array(
                    'type' => 'slider',
                    'title' => '图片和按钮间距',
                    'desc' => '图片和按钮间距',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'max' => 200,
                    'min' => 0,
                    'std' => 20,
                ),

                'type20_img_bg' => array(
                    'type' => 'checkbox',
                    'title' => '标题使用背景图片',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'std' => '0',
                ),
                'type20_img_bgs' => array(
                    'type' => 'media',
                    'title' => '背景图片',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                        array('type20_img_bg', '=', '1'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220812/db9845ef07cfa318309a96210f502259.gif',
                ),
                'type20_img_hvbgs' => array(
                    'type' => 'media',
                    'title' => '滑过背景图片',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                        array('type20_img_bg', '=', '1'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220812/c17374409c4bd9a8abc63eaf382b72f2.png',
                ),

                'type20_button_bg' => array(
                    'type' => 'color',
                    'title' => '标题背景颜色',
                    'desc' => '标题背景颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                        array('type20_img_bg', '!=', '1'),

                    ),
                    'std' => '#ccc',
                ),
                'type20_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'std' => '16',
                ),
                'type20_button_font_color' => array(
                    'type' => 'color',
                    'title' => '标题字体颜色',
                    'desc' => '标题字体颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'std' => '#00000',
                ),
                'type20_lineheight' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'depends' => array(
                        array('pro_type', '=', 'type20'),
                    ),
                    'std' => '50',
                ),

                'jw_image_carousel_item' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                    ),
                    'attr' => array(
                        'item_title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_SUBTITLE_DESC'),
                        ),
                        'item_subtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_SUBTITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_SUBTITLE_DESC'),
                        ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('描述'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_DESCRIPTION_DESC'),
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type15'),
                    ),
                ),

                'bc_image_carousel_img' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/type15_bc.png',
                    'depends' => array(
                        array('pro_type', '=', 'type15'),
                    ),
                ),

                // type46
                'type46_img_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片宽度(%)'),
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => '25',
                        'sm' => '33',
                        'xs' => '50',
                    ),
                    'responsive' => true,
                ),
                'type46_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度(px)'),
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => array(
                        'md' => '303',
                        'sm' => '250',
                        'xs' => '230',
                    ),
                    'responsive' => true,
                ),
                'font_state_type46' => array(
                    'type' => 'buttons',
                    'title' => '正常/划过效果设置',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                    ),
                ),

                'type46_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => 20,
                    'depends' => array(
                        array('font_state_type46', '=', 'normal'),
                        array('pro_type', '=', 'type46'),
                    ),
                ),
                'type46_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#474747',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'normal'),
                    ),
                ),
                'type46_label_fontsize' => array(
                    'type' => 'slider',
                    'desc' => '该字段取值为产品的标签1',
                    'title' => JText::_('标签字体大小'),
                    'std' => 12,
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'normal'),
                    ),
                ),
                'type46_label_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签字体颜色'),
                    'std' => '#a5a5a5',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'normal'),
                    ),
                ),

                'type46_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('文本背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'normal'),
                    ),
                ),

                'type46_font_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过标题颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_label_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过标签颜色'),
                    'std' => '#a5a5a5',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_jianjie_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'std' => 13,
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_jianjie_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过简介颜色'),
                    'std' => '#bebebe',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_hvbg' => array(
                    'type' => 'color',
                    'title' => JText::_('划过背景色'),
                    'std' => '#383838',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('划过边框大小'),
                    'std' => 3,
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'type46_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('划过边框颜色'),
                    'std' => '#383838',
                    'depends' => array(
                        array('pro_type', '=', 'type46'),
                        array('font_state_type46', '=', 'hover'),
                    ),
                ),
                'pro57_page_top_title' => array(
                    'type' => 'text',
                    'title' => 'TOP栏目文字',
                    'std' => '标准下载',
                    'depends' => array(
                        array('pro_type', '=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                    ),
                ),
                'pro57_page_top_title_text_color' => array(
                    'type' => 'color',
                    'title' => '左上字体颜色',
                    'std' => '#1b71c2',
                    'depends' => array(
                        array('pro_type', '=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                    ),
                ),
                'pro57_page_top_img' => array(
                    'type' => 'media',
                    'title' => JText::_('选择默认图片'),
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/type57-chanpinzi.png',
                    'depends' => array('pro_type' => 'type57'),
                ),
                'pro57_page_top_right_title_text_color' => array(
                    'title' => '右上字体颜色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type57'),
                    ),
                ),
                'pro57_page_top_right_bg_color' => array(
                    'title' => '右上栏目背景色',
                    'type' => 'color',
                    'std' => '#1b71c2',
                    'depends' => array(
                        array('pro_type', '=', 'type57'),
                    ),
                ),
                'pro57_page_li_left_bg_color' => array(
                    'title' => '列表圆点背景色',
                    'type' => 'color',
                    'std' => '#1b71c2',
                    'depends' => array(
                        array('pro_type', '=', 'type57'),
                    ),
                ),
                // 46结束

                // 布局6新增划过翻转背景
                'pro6_open_bgfz' => array(
                    'title' => '开启背景翻转样式',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type6'),
                    ),
                ),
                'pro6_bottom_px' => array(
                    'title' => '箭头距离底部的距离(%)',
                    'type' => 'slider',
                    'std' => '30',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type6'),
                        array('pro6_open_bgfz', '=', '1'),
                    ),
                ),

                //

                // 产品布局58

                'pro58_margin' => array(
                    'title' => '列表间距',
                    'type' => 'slider',
                    'std' => '30',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type58'),
                    ),
                ),
                // 58结束
                // 62
                'pro62_fcolor' => array(
                    'title' => '立即咨询颜色',
                    'type' => 'color',
                    'std' => '#b4945b',
                    'depends' => array(
                        array('pro_type', '=', 'type62'),
                    ),
                ),
                'pro62_link' => array(
                    'title' => 'PC咨询跳转链接',
                    'type' => 'text',
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type62'),
                    ),
                ),
                'pro62_tel' => array(
                    'title' => '手机咨询电话',
                    'type' => 'text',
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type62'),
                    ),
                ),
                // 结束
                'fix_img_height' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否固定图片高度'),
                    'depends' => array(
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => 0,
                ),
                'fix_img_height_input' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置pc端图片高度'),
                    'depends' => array(
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type22'),
                        // array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('fix_img_height', '=', 1),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type69'),

                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                ),

                'fix_img_height_input_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置手机端图片高度'),
                    'depends' => array(
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type22'),
                        // array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('fix_img_height', '=', 1),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type69'),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => 100,
                ),

                /* 产品69开始 */
                // 产品69配置选项卡
                'settings_69' => array(
                    'title' => '产品69配置选项卡',
                    'type' => 'buttons',
                    'values' => array(
                        array(
                            'label' => '轮播',
                            'value' => 'swiper'
                        ),
                        array(
                            'label' => '介绍',
                            'value' => 'intro'
                        )
                    ),
                    'std' => 'swiper',
                    'depends' => array(
                        array('pro_type', '=', 'type69')
                    )
                ),
                // 轮播配置项
                'swiper_settings_69' => array(
                    'title' => '轮播',
                    'type' => 'separator',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    )
                ),
                // 一屏显示的产品数量
                'swiper_slides_69' => array(
                    'type' => 'slider',
                    'title' => '一屏显示的产品数量',
                    'std' => array('md' => '2', 'sm' => '1', 'xs' => '1'),
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 产品69图片高度
                "img_height_69" => array(
                    "title" => "产品69图片高度",
                    "type" => "slider",
                    "max" => 1000,
                    "min" => 0,
                    "std" => 500,
                    'responsive' => true,
                    "depends" => array(
                        array("pro_type", "=", "type69"),
                        array("fix_img_height", "=", 1),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 轮播宽度占比
                'swiper_width_69' => array(
                    'type' => 'slider',
                    'title' => '轮播宽度占比',
                    'std' => array('md' => '60', 'sm' => '60', 'xs' => '100'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 轮播项圆角
                'swiper_border_radius_69' => array(
                    'type' => 'slider',
                    'title' => '轮播项圆角',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 图片展示方式
                'img_show_type_69' => array(
                    'title' => '图片展示方式',
                    'type' => 'select',
                    'std' => 'cover',
                    'values' => array(
                        'cover' => '超出裁剪',
                        'contain' => '保持宽高比留白',
                        'fill' => '占满拉伸'
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 按钮配置项
                'button_settings_69' => array(
                    'title' => '按钮',
                    'type' => 'separator',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    )
                ),
                // 按钮外边距
                'button_margin_69' => array(
                    'title' => '按钮外边距',
                    'type' => 'margin',
                    'std' => array('md' => '30px 0 0 0', 'sm' => '30px 0 0 0', 'xs' => '30px 0 0 0'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 使用图片还是图标做翻页按钮
                'button_type_69' => array(
                    'title' => '使用图片还是图标做翻页按钮',
                    'type' => 'select',
                    'std' => 'icon',
                    'values' => array(
                        'icon' => '图标',
                        'img' => '图片'
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 按钮圆角
                'button_border_radius_69' => array(
                    'title' => '按钮圆角（%）',
                    'type' => 'slider',
                    'std' => '50',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 是否开启阴影
                'button_box_shadow_69' => array(
                    'title' => '是否开启阴影',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 阴影颜色
                'button_box_shadow_color_69' => array(
                    'title' => '阴影颜色',
                    'type' => 'color',
                    'std' => 'rgba(90, 90, 90, 0.56)',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 阴影大小
                'button_box_shadow_size_69' => array(
                    'title' => '阴影大小',
                    'type' => 'slider',
                    'std' => '5',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 阴影水平偏移
                'button_box_shadow_x_69' => array(
                    'title' => '阴影水平偏移',
                    'type' => 'slider',
                    'std' => '0',
                    'max' => 100,
                    'min' => -100,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 阴影垂直偏移
                'button_box_shadow_y_69' => array(
                    'title' => '阴影垂直偏移',
                    'type' => 'slider',
                    'std' => '5',
                    'max' => 100,
                    'min' => -100,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 按钮背景色
                'button_bg_69' => array(
                    'title' => '按钮背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 按钮大小
                'button_size_69' => array(
                    'title' => '按钮大小',
                    'type' => 'slider',
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 按钮箭头颜色
                'button_arrow_color_69' => array(
                    'title' => '按钮箭头颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 按钮箭头大小
                'button_arrow_size_69' => array(
                    'title' => '按钮箭头大小',
                    'type' => 'slider',
                    'std' => array('md' => 22, 'sm' => 22, 'xs' => 22),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 上一页箭头图标选择
                'button_prev_arrow_69' => array(
                    'title' => '上一页箭头图标',
                    'type' => 'icon',
                    'std' => 'fa-angle-left',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 下一页箭头图标选择
                'button_next_arrow_69' => array(
                    'title' => '下一页箭头图标',
                    'type' => 'icon',
                    'std' => 'fa-angle-right',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'icon'),
                    ),
                ),
                // 按钮上一页图片
                'button_prev_img_69' => array(
                    'title' => '上一页按钮图片',
                    'type' => 'media',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'img'),
                    ),
                ),
                // 按钮下一页图片
                'button_next_img_69' => array(
                    'title' => '下一页按钮图片',
                    'type' => 'media',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                        array('button_type_69', '=', 'img'),
                    ),
                ),
                // 按钮间距
                'button_margin_69' => array(
                    'title' => '按钮间距',
                    'type' => 'slider',
                    'std' => array('md' => 20, 'sm' => 20, 'xs' => 20),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'swiper'),
                    ),
                ),
                // 介绍容器配置
                'info_container_69' => array(
                    'title' => '介绍容器配置',
                    'type' => 'separator',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍宽度
                'info_width_69' => array(
                    'title' => '介绍宽度（%）',
                    'type' => 'slider',
                    'std' => array('md' => '40', 'sm' => '40', 'xs' => '90'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍和轮播间距
                'info_margin_69' => array(
                    'title' => '介绍和轮播间距',
                    'type' => 'margin',
                    'std' => array('md' => '40px 0 40px -60px', 'sm' => '40px 0 40px -60px', 'xs' => '30px auto 0 auto'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍背景色
                'info_bg_69' => array(
                    'title' => '介绍背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍圆角
                'info_radius_69' => array(
                    'title' => '介绍圆角',
                    'type' => 'slider',
                    'std' => '30',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍内边距
                'info_padding_69' => array(
                    'title' => '介绍内边距',
                    'type' => 'margin',
                    'std' => array('md' => '60px 40px 50px 40px', 'sm' => '60px 40px 50px 40px', 'xs' => '60px 40px 50px 40px'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍是否开启阴影
                'info_shadow_69' => array(
                    'title' => '介绍是否开启阴影',
                    'type' => 'checkbox',
                    'std' => '1',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍阴影颜色
                'info_shadow_color_69' => array(
                    'title' => '介绍阴影颜色',
                    'type' => 'color',
                    'std' => 'rgba(90, 90, 90, 0.56)',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('info_shadow_69', '=', '1'),
                    ),
                ),
                // 介绍阴影水平偏移
                'info_shadow_x_69' => array(
                    'title' => '介绍阴影水平偏移',
                    'type' => 'slider',
                    'std' => '0',
                    'max' => 100,
                    'min' => -100,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('info_shadow_69', '=', '1'),
                    ),
                ),
                // 介绍阴影垂直偏移
                'info_shadow_y_69' => array(
                    'title' => '介绍阴影垂直偏移',
                    'type' => 'slider',
                    'std' => '0',
                    'max' => 100,
                    'min' => -100,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('info_shadow_69', '=', '1'),
                    ),
                ),
                // 介绍阴影模糊半径
                'info_shadow_blur_69' => array(
                    'title' => '介绍阴影大小',
                    'type' => 'slider',
                    'std' => '10',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('info_shadow_69', '=', '1'),
                    ),
                ),
                // 介绍内容配置
                'info_content_69' => array(
                    'title' => '介绍内容配置（介绍的标题使用客户端的外部链接）',
                    'type' => 'separator',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍右上角图片
                'info_right_top_img_69' => array(
                    'title' => '介绍右上角图片',
                    'type' => 'media',
                    'std' => str_replace("administrator/", "", JURI::base()) .
                        "components/com_jwpagefactory/addons/product_list/assets/images/img_69_1.png",
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍右上角图片宽度
                'info_right_top_img_width_69' => array(
                    'title' => '介绍右上角图片宽度',
                    'type' => 'slider',
                    'std' => array('md' => 100, 'sm' => 100, 'xs' => 100),
                    'max' => 600,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array("info_right_top_img_69", "!=", ''),
                    ),
                ),
                // 介绍右上角图片上边距
                'info_right_top_img_top_69' => array(
                    'title' => '介绍右上角图片上偏移',
                    'type' => 'slider',
                    'std' => array('md' => -37, 'sm' => -37, 'xs' => -37),
                    'max' => 500,
                    'min' => -500,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array("info_right_top_img_69", "!=", ''),
                    ),
                ),
                // 介绍右上角图片右边距
                'info_right_top_img_right_69' => array(
                    'title' => '介绍右上角图片右偏移',
                    'type' => 'slider',
                    'std' => '20',
                    'max' => 500,
                    'min' => -500,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array("info_right_top_img_69", "!=", ''),
                    ),
                ),
                // 介绍标题字号
                'info_title_font_size_69' => array(
                    'title' => '介绍标题字号',
                    'type' => 'slider',
                    'std' => array('md' => '26', 'sm' => '26', 'xs' => '20'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍标题显示行数
                'info_title_line_69' => array(
                    'title' => '介绍标题显示行数',
                    'type' => 'slider',
                    'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'max' => 10,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍标题行高
                'info_title_line_height_69' => array(
                    'title' => '介绍标题行高',
                    'type' => 'slider',
                    'std' => array('md' => 47, 'sm' => 47, 'xs' => 47),
                    'max' => 100,
                    'min' => 12,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍标题字体样式
                'info_title_font_style_69' => array(
                    'title' => '介绍标题字体样式',
                    'type' => 'fontstyle',
                    'std' => array('weight' => 'bold'),
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 介绍标题字体颜色
                'info_title_font_color_69' => array(
                    'title' => '介绍标题字体颜色',
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 标题外边距
                'info_title_margin_69' => array(
                    'title' => '介绍标题外边距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 30px 0', 'sm' => '0 0 30px 0', 'xs' => '0 0 30px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 简介字号
                'info_font_size_69' => array(
                    'title' => '简介字号',
                    'type' => 'slider',
                    'std' => array('md' => 18, 'sm' => 18, 'xs' => 16),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 简介行高
                'info_intro_line_height_69' => array(
                    'title' => '简介行高',
                    'type' => 'slider',
                    'std' => array('md' => 33, 'sm' => 33, 'xs' => 24),
                    'max' => 100,
                    'min' => 12,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 简介显示行数
                'info_intro_line_69' => array(
                    'title' => '简介显示行数',
                    'type' => 'slider',
                    'std' => array('md' => 3, 'sm' => 3, 'xs' => 3),
                    'max' => 50,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 简介字体颜色
                'info_intro_font_color_69' => array(
                    'title' => '简介字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 简介外边距
                'info_intro_margin_69' => array(
                    'title' => '简介外边距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息姓名外边距
                'name_margin_69' => array(
                    'title' => '个人信息姓名外边距',
                    'type' => 'margin',
                    'std' => array('md' => '40px 0 30px 0', 'sm' => '40px 0 30px 0', 'xs' => '40px 0 30px 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息姓名字体样式
                'name_font_style_69' => array(
                    'title' => '个人信息姓名字体样式',
                    'type' => 'fontstyle',
                    'std' => array('weight' => 'bold'),
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息姓名字号
                'name_font_size_69' => array(
                    'title' => '个人信息姓名字号',
                    'type' => 'slider',
                    'std' => array('md' => '26', 'sm' => '26', 'xs' => '20'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息行高
                'name_lineheight_69' => array(
                    'title' => '个人信息行高',
                    'type' => 'slider',
                    'std' => array('md' => '47', 'sm' => '47', 'xs' => '47'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息显示行数
                'name_line_69' => array(
                    'title' => '个人信息显示行数',
                    'type' => 'slider',
                    'std' => array('md' => '1', 'sm' => '1', 'xs' => '1'),
                    'max' => 10,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息姓名字体颜色
                'name_font_color_69' => array(
                    'title' => '个人信息姓名字体颜色',
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 个人信息列表间距
                'list_margin_69' => array(
                    'title' => '个人信息列表间距',
                    'type' => 'margin',
                    'std' => array('md' => '0 0 0 30px', 'sm' => '10px 0 0 0', 'xs' => '10px 0 0 0'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 列表字号
                'list_font_size_69' => array(
                    'title' => '个人信息列表字号',
                    'type' => 'slider',
                    'std' => array('md' => 16, 'sm' => 16, 'xs' => 16),
                    'max' => 30,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 列表字体颜色
                'list_font_color_69' => array(
                    'title' => '个人信息列表字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 使用图标还是图片
                'list_font_type_69' => array(
                    'title' => '列表使用图标还是图片',
                    'type' => 'select',
                    'std' => 'icon',
                    'values' => array(
                        'icon' => '图标',
                        'img' => '图片',
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 列表标签1图标
                'label_1_font_icon_69' => array(
                    'title' => '列表标签1图标',
                    'type' => 'icon',
                    'std' => 'fa-weixin',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('list_font_type_69', '=', 'icon'),
                    ),
                ),
                // 标签2图标
                'label_2_font_icon_69' => array(
                    'title' => '列表标签1图标',
                    'type' => 'icon',
                    'std' => 'fa-phone-alt',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('list_font_type_69', '=', 'icon'),
                    ),
                ),
                // 列表标签显示行数
                'label_line_69' => array(
                    'title' => '列表标签显示行数',
                    'type' => 'slider',
                    'std' => array('md' => 1, 'sm' => 1, 'xs' => 1),
                    'min' => 1,
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 列表标签行高
                'label_line_height_69' => array(
                    'title' => '列表标签行高',
                    'type' => 'slider',
                    'std' => array('md' => 33, 'sm' => 33, 'xs' => 24),
                    'min' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 列表标签字体颜色
                'label_font_color_69' => array(
                    'title' => '列表标签字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('list_font_type_69', '=', 'icon'),
                    ),
                ),
                // 标签1图片
                'label_1_font_img_69' => array(
                    'title' => '列表标签1图片',
                    'type' => 'media',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('list_font_type_69', '=', 'img'),
                    ),
                ),
                // 标签2图片
                'label_2_font_img_69' => array(
                    'title' => '列表标签2图片',
                    'type' => 'media',
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                        array('list_font_type_69', '=', 'img'),
                    ),
                ),
                // 标签图标/图片大小
                'label_font_size_69' => array(
                    'title' => '列表标签图标/图片大小',
                    'type' => 'slider',
                    'std' => array('md' => '20', 'sm' => '20', 'xs' => '18'),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                // 图标右边距
                'label_font_size_69_right' => array(
                    'title' => '图标右边距',
                    'type' => 'slider',
                    'std' => array('md' => 10, 'sm' => 10, 'xs' => 10),
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type69'),
                        array("settings_69", "=", 'intro'),
                    ),
                ),
                /* 产品69结束 */

                'img_check_2_ani' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启移入阴影动画'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                    ),
                    'std' => 0,
                ),
                'img_check_2_line' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启底部线条'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                    ),
                    'std' => 0,
                ),
                'check2_zdy' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启自定义标题'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                    ),
                    'std' => 0,
                ),
                'zdy_tit1' => array(
                    'type' => 'text',
                    'title' => JText::_('自定义标题1(数据对应后台 标题 字段)'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('check2_zdy', '=', '1'),
                    ),
                    'std' => '停车场名称',
                ),
                'zdy_tit2' => array(
                    'type' => 'text',
                    'title' => JText::_('自定义标题2(数据对应后台 标签1 字段)'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('check2_zdy', '=', '1'),
                    ),
                    'std' => '停车场类型',
                ),
                'zdy_tit3' => array(
                    'type' => 'text',
                    'title' => JText::_('自定义标题3(数据对应后台 标签2 字段)'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('check2_zdy', '=', '1'),
                    ),
                    'std' => '停车位',
                ),


                'img_check_2_line_color' => array(
                    'type' => 'color',
                    'title' => JText::_('底部线条颜色'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'std' => '#fff',
                ),
                'img_check_2_line_colorhover' => array(
                    'type' => 'color',
                    'title' => JText::_('底部线条划过的颜色'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'std' => '#fff',
                ),
                'img_check_2_line_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的宽度'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 500,
                    'min' => 0,
                    'std' => 8,
                ),
                'img_check_2_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的高度'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 8,
                ),
                'img_check_2_line_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的圆角'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 300,
                    'min' => 0,
                    'std' => 4,
                ),
                'img_check_2_line_position' => array(
                    'type' => 'select',
                    'title' => JText::_('底部线条中间点的位置'),
                    'depends' => array(
                        array('img_check_2_line', '=', '1'),
                    ),
                    'values' => array(
                        'end' => '上',
                        'center' => '中',
                        'normal' => '下',
                    ),
                    'std' => 'center',
                ),
                'img_check_13' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启移入图片'),
                    'depends' => array(
                        array('pro_type', '=', 'type13'),
                    ),
                    'std' => 0,
                ),
                'fix_img_title_tc' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('设置只滑过标题弹出'),
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                    ),
                ),
                // type4
                'fix_img_height_input_type4' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置主图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                        array('fix_img_height', '=', 1),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => 300,
                ),
                'fix_img_height_input_type4_sec' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置副图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                        array('fix_img_height', '=', 1),
                    ),
                    'max' => 300,
                    'min' => 0,
                    'std' => 120,
                ),

                'img_zwidth_type4' => array(
                    'type' => 'slider',
                    'title' => JText::_('PC主图宽度占比(总宽12份，如设置为6，则主图宽占6/12)'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'max' => 12,
                    'min' => 0,
                    'std' => 6,
                ),


                'font_text_type27' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'depends' => array('pro_type' => 'type27'),
                    'std' => '查看更多',
                ),

                'font_color_type27' => array(
                    'type' => 'color',
                    'title' => '标题默认字体颜色',
                    'depends' => array('pro_type' => 'type27'),

                    'std' => '#000000',
                ),

                'font_color_hr_type27' => array(
                    'type' => 'color',
                    'title' => '标题滑过字体颜色',
                    'depends' => array('pro_type' => 'type27'),
                    'std' => '#ff0000',
                ),

                'btn_color_type27' => array(
                    'type' => 'color',
                    'title' => '按钮默认字体颜色',
                    'depends' => array('pro_type' => 'type27'),
                    'std' => '#000000',
                ),

                'btn_color_hr_type27' => array(
                    'type' => 'color',
                    'title' => '按钮滑过字体颜色',
                    'depends' => array('pro_type' => 'type27'),
                    'std' => '#ff0000',
                ),

                'font_size_type27' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'max' => 100,
                    'min' => 1,
                    'depends' => array('pro_type' => 'type27'),
                    'std' => 14,
                ),

                'btn_size_type27' => array(
                    'type' => 'slider',
                    'title' => '按钮字体大小',
                    'max' => 100,
                    'min' => 1,
                    'depends' => array('pro_type' => 'type27'),
                    'std' => 14,
                ),

                'fix_img_height_input_type16_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机设置图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type16'),

                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                    'placeholder' => 300,
                ),
                'fix_an_height_input_type16_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机设置按钮位置'),
                    'depends' => array(
                        array('pro_type', '=', 'type16'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 33,
                    'placeholder' => 33,
                ),
                'fix_img_height_input_type14_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机设置图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type14'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 200,
                    'placeholder' => 200,
                ),
                'fix_img_jl_input_type14_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机图片标题间距'),
                    'depends' => array(
                        array('pro_type', '=', 'type14'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 200,
                    'placeholder' => 200,
                ),
                'fix_an_height_input_type14_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机设置按钮位置'),
                    'depends' => array(
                        array('pro_type', '=', 'type14'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 130,
                    'placeholder' => 130,
                ),
                'fix_img_height_input_type16_pb' => array(
                    'type' => 'slider',
                    'title' => JText::_('平板设置图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type16'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 400,
                    'placeholder' => 400,
                ),
                'fix_an_height_input_type16_pb' => array(
                    'type' => 'slider',
                    'title' => JText::_('平板设置按钮位置'),
                    'depends' => array(
                        array('pro_type', '=', 'type16'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 40,
                    'placeholder' => 40,
                ),
                'fix_img_height_input_type4_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 14,
                ),
                'font_color_type4' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'std' => '#fff',
                ),
                'font_color_type4_active' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标进入字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'std' => '#fff',
                ),
                'bg_color_type4' => array(
                    'type' => 'color',
                    'title' => JText::_('字体背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'std' => '#000',
                ),
                'font_posi_type4' => array(
                    'type' => 'select',
                    'title' => '字体位置',
                    'depends' => array('pro_type' => 'type4'),
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右',
                    ),
                    'std' => 'center',
                ),
                'bg_color_type4_active' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标进入字体背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'std' => '#000',
                ),
                'bg_height_type4' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体背景高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type4'),
                    ),
                    'max' => 600,
                    'min' => 0,
                    'std' => 44,
                ),
                'img_animated_type4' => array(
                    'type' => 'select',
                    'title' => '选择图片动画',
                    'desc' => '图片动画',
                    'depends' => array('pro_type' => 'type4'),
                    'values' => array(
                        'animated1' => '无',
                        'animated2' => '放大',

                    ),
                    'std' => 'animated1',
                ),
                'font_type_type4' => array(
                    'type' => 'select',
                    'title' => '选择字体显示方式',
                    'depends' => array('pro_type' => 'type4'),
                    'values' => array(
                        'type1' => '固定显示',
                        'type2' => '鼠标进入显示',
                        'type3' => '左上角显示',
                    ),
                    'std' => 'type1',
                ),

                'font_type4_top' => array(
                    'type' => 'slider',
                    'title' => '标题距离上面位置(px)',
                    'depends' => array('font_type_type4' => 'type3'),
                    'max' => 500,
                    'min' => 0,
                    'std' => 20,
                ),
                // type33
                'type33_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                    'std' => '#000',
                ),
                'type33_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                    'std' => '#000',
                ),
                'type33_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                    'std' => '#fff',
                ),
                'type33_button_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                    'std' => '#5ea1cb',
                ),
                'type33_button_content' => array(
                    'type' => 'text',
                    'title' => JText::_('按钮文字内容'),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                    'std' => '查看更多',
                ),
                'tz_page_type' => array(
                    'type' => 'select',
                    'title' => JText::_('跳转方式'),
                    'desc' => JText::_('跳转方式'),
                    'values' => array(
                        'Internal_pages' => JText::_('内部页面'),
                        'external_links' => JText::_('外部链接'),
                        'wx_links' => JText::_('微信'),
                    ),
                    'std' => 'Internal_pages',
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                    ),
                ),
                'tz_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择跳转页面',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                        array('tz_page_type', '=', 'Internal_pages'),
                    ),
                ),

                'tz_detail_page' => array(
                    'type' => 'text',
                    'title' => '跳转链接',
                    'depends' => array(
                        array('pro_type', '=', 'type33'),
                        array('tz_page_type', '=', 'external_links'),
                    ),
                ),


                //type22 变量
                'type22_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入背景色'),
                    'std' => 'rgba(105, 23, 18, 0.9)',
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_content_p' => array(
                    'type' => 'padding',
                    'title' => JText::_('遮罩层内边距'),
                    'std' => array(
                        'md' => '10px',
                        'sm' => '10px',
                        'xs' => '10px',
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_content_icon' => array(
                    'type' => 'media',
                    'title' => '移入中心图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/baroque_part04/assets/images/part04_1.png',
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_before_icon' => array(
                    'type' => 'media',
                    'title' => '移入左上角图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/baroque_part04/assets/images/part04_2.png',
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_after_icon' => array(
                    'type' => 'media',
                    'title' => '移入右下角图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/baroque_part04/assets/images/part04_3.png',
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_after_icon_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入对角图标宽度'),
                    'std' => array(
                        'md' => 49,
                        'sm' => 49,
                        'xs' => 49,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_after_icon_h' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入对角图标高度'),
                    'std' => array(
                        'md' => 90,
                        'sm' => 90,
                        'xs' => 90,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_content_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('文字大小'),
                    'std' => array(
                        'md' => 16,
                        'sm' => 16,
                        'xs' => 16,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_content_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文字颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'type22_content_lineHeight' => array(
                    'type' => 'slider',
                    'title' => JText::_('文字行高'),
                    'std' => array(
                        'md' => 32,
                        'sm' => 32,
                        'xs' => 32,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type22'),
                    ),
                ),
                'gl_show' => array(
                    'type' => 'slider',
                    'title' => JText::_('不隐藏显示数量'),
                    'max' => 100,
                    'min' => 1,
                    'std' => 5,
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'mr_dh_color' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧导航字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'mr_dh_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧导航背景颜色'),
                    'std' => '#608ad8',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'mr_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧标题字体颜色'),
                    'std' => '#0a4a9b',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'mr_title_color_gl' => array(
                    'type' => 'color',
                    'title' => JText::_('左侧隐藏标题字体颜色'),
                    'std' => '#f7941d',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'right_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧标题字体颜色'),
                    'std' => '#0a4a9b',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'right_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧内容字体颜色'),
                    'std' => '#8193aa',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                'right_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧按钮字体颜色'),
                    'std' => '#0a4a9b',
                    'depends' => array(
                        array('pro_type', '=', 'type29'),
                    ),
                ),
                // 2022.2.25 关闭按钮 标题,添加标签
                'type8_title_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭标题'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_button_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭按钮'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_button_content' => array(
                    'type' => 'text',
                    'title' => JText::_('按钮文字'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_button_close', '=', 0),
                    ),
                    'std' => '了解更多',
                ),
                'type8_border_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭边框'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_background_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭背景'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_label_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示标签(数据取产品标签1字段)'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_label_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签颜色'),
                    'std' => '#555',
                    'depends' => array(
                        array('type8_label_open', '=', '1'),
                        array('pro_type', '=', 'type8'),
                    ),
                ),
                'type8_label_fize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签字体大小'),
                    'max' => 50,
                    'min' => 12,
                    'std' => 12,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_label_open', '=', '1'),
                    ),
                ),
                'type8_jianjie_close' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭简介的超出隐藏'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_intro_br' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启简介换行（仅支持回车换行）'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_zhujiang_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示主讲人(数据取产品标签1字段)'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'std' => 0,
                ),
                'type8_zhujiang_color' => array(
                    'type' => 'color',
                    'title' => JText::_('主讲人字体颜色'),
                    'std' => '#555',
                    'depends' => array(
                        array('type8_zhujiang_open', '=', '1'),
                        array('pro_type', '=', 'type8'),
                    ),
                ),
                'type8_zhujiang_fize' => array(
                    'type' => 'slider',
                    'title' => JText::_('主讲人字体大小'),
                    'max' => 30,
                    'min' => 12,
                    'std' => 12,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_zhujiang_open', '=', '1'),
                    ),
                ),
                'type8_zhujiang_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('type8_zhujiang_open', '=', '1'),
                        array('pro_type', '=', 'type8'),
                    ),
                ),
                'type8_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内间距'),
                    'std' => array('md' => '0px 30px 0px 30px', 'sm' => '0px 20px 0px 20px', 'xs' => '0px 10px 0px 10px'),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_zhujiang_open', '=', '1'),

                    ),
                ),
                // 布局8 标题相关配置
                'type8_title_padding' => array(
                    'type' => 'padding',
                    'title' => '标题内边距',
                    'std' => array(
                        'md' => '10px 0px 0px 0px',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),
                'type8_title_height' => array(
                    'type' => 'slider',
                    'title' => '标题高度',
                    'std' => array(
                        'md' => '60',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),
                'type8_title_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'std' => array(
                        'md' => '60',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),
                'type8_title_mb' => array(
                    'type' => 'slider',
                    'title' => '标题下边距',
                    'std' => array(
                        'md' => '',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),
                'type8_desc_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '简介行高',
                    'std' => array(
                        'md' => '',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),
                'type8_desc_line' => array(
                    'type' => 'slider',
                    'title' => '简介显示行数',
                    'std' => array(
                        'md' => '2',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('type8_title_close', '!=', '1'),
                    ),
                ),

                // 布局61配置项
                'type61_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'std' => '16',
                    'depends' => array(
                        array('pro_type', '=', 'type61'),
                    ),
                ),
                'type61_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type61'),
                    ),
                ),
                'type61_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体行高'),
                    'std' => '50',
                    'depends' => array(
                        array('pro_type', '=', 'type61'),
                    ),
                ),
                'type61_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#f3f3f3',
                    'depends' => array(
                        array('pro_type', '=', 'type61'),
                    ),
                ),
                'type61_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('间距'),
                    'std' => '5px 5px 0px 5px',
                    'depends' => array(
                        array('pro_type', '=', 'type61'),
                    ),
                ),


                //
                'hover_border_pub' => array(
                    'type' => 'buttons',
                    'title' => '边框状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type5'),
                        array('pro_type', '!=', 'type6'),
                        array('pro_type', '!=', 'type7'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                //2023.1.4布局66
                'type66_leftimg' => array(
                    'type' => 'media',
                    'title' => JText::_('左侧箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230104/5f84c9fbfe98e0546c7ab26810b40755.png',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),

                    ),
                ),
                'type66_rightimg' => array(
                    'type' => 'media',
                    'title' => JText::_('右侧箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230104/bc67a5596c507135a0c37edcbbf5aeac.png',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),

                    ),
                ),
                'type66_leftimg_hover' => array(
                    'type' => 'media',
                    'title' => JText::_('划过左侧箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230104/72f94c0853c64f1b3285a3d9b588eb6a.png',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                ),
                'type66_rightimg_hover' => array(
                    'type' => 'media',
                    'title' => JText::_('划过右侧箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230104/b16670a67f8388bab84267db3a187e3a.png',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                ),

                //
                'normal_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('正常边框宽度'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'normal'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type5'),
                        array('pro_type', '!=', 'type6'),
                        array('pro_type', '!=', 'type7'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => 1,
                ),
                'normal_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常边框颜色'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'normal'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type5'),
                        array('pro_type', '!=', 'type6'),
                        array('pro_type', '!=', 'type7'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#fff',
                ),
                'need_img_border_type2' => array(
                    'title' => '是否开启图片边框',
                    'type' => 'checkbox',
                    'std' => 0,
                    'depends' => array(
                        array('hover_border_pub', '=', 'normal'),
                        array('pro_type', '=', 'type2'),
                    )
                ),
                'img_normal_border_color_type2' => array(
                    'type' => 'color',
                    'std' => '',
                    'title' => JText::_('图片边框色'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'normal'),
                        array('pro_type', '=', 'type2'),
                        array('need_img_border_type2', '=', 1),
                    )
                ),
                'img_normal_border_size_type2' => array(
                    'type' => 'margin',
                    'std' => '0 0 0 0',
                    'title' => JText::_('图片边框宽度'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'normal'),
                        array('pro_type', '=', 'type2'),
                        array('need_img_border_type2', '=', 1),
                    )
                ),
                'img_hover_border_color_type2' => array(
                    'type' => 'color',
                    'std' => '',
                    'title' => JText::_('图片边框色'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'hover'),
                        array('pro_type', '=', 'type2'),
                        array('need_img_border_type2', '=', 1),
                    )
                ),
                // 产品布局63
                'type63_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => array('md' => 580, 'sm' => 386, 'xs' => 400),
                    'responsive' => true,
                    'max' => 800,
                ),
                'type63_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容顶部距离(%)'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => array('md' => 15, 'sm' => 15, 'xs' => 20),
                    'responsive' => true,
                    'max' => 100,

                ),
                'type63_logo' => array(
                    'type' => 'media',
                    'title' => JText::_('PC端logo图'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220817/4460cad80ddf6c8f085185012dd8ad20.png',
                ),
                'type63_phonewz' => array(
                    'type' => 'text',
                    'title' => JText::_('手机端logo文字'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => 'modern·1906',
                ),
                'type63_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签1/标签2 字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => array('md' => 48, 'sm' => 36, 'xs' => 28),
                    'responsive' => true,
                    'max' => '50',
                ),
                'type63_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签1/标签2 PC字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => '#fceecf',
                ),
                'type63_sjtitle_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签1/标签2 手机字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type63'),
                    ),
                    'std' => '#03594b',
                ),
                //产品58
                'type58_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type58'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '18',
                    'max' => '40',

                ),
                'type58_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type58'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#333',
                ),
                'type58_label_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type58'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '12',
                    'max' => '30',
                ),
                'type58_label_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标签字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type58'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#a5a5a5',
                ),
                //
                //2022.03.18 type8背景色
                'type8_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'type8_hover_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过背景色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                //
                'hover_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入后边框宽度'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'hover'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => 1,
                ),
                'hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后边框颜色'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'hover'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#fff',
                ),
                'hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后字体背景颜色'),
                    'depends' => array(
                        array('hover_border_pub', '=', 'hover'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type8'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#000',
                ),
                'pro_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type1'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#ffffff',
                ),
                'pro8_btnborder_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮边框颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#ccc',
                ),
                'pro8_btnbg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro8_btnfont_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#000',
                ),
                'pro8_btnborder_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮边框颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#ccc',
                ),
                'pro8_btnbg_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#ec5a41',
                ),
                'pro8_btnfont_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro10_btnborder_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮边框颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#ccc',
                ),
                'pro10_btnbg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro10_btnfont_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常按钮字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#000',
                ),
                'pro10_btnborder_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮边框颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#ccc',
                ),
                'pro10_btnbg_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#ec5a41',
                ),
                'pro10_btnfont_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入按钮字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro8_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#333',
                ),
                'pro8_font_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#333',
                ),
                'pro8_jianjie_hvcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('划过简介字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#666',
                ),
                'pro8_bot' => array(
                    'type' => 'slider',
                    'title' => JText::_('正常标题加粗'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 1000,
                    'min' => 400,
                    'std' => 400,
                ),
                'pro8_hover_bot' => array(
                    'type' => 'slider',
                    'title' => JText::_('划过标题加粗'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'max' => 1000,
                    'min' => 400,
                    'std' => 400,
                ),
                'type8fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '16',
                ),
                'pro10_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#333',
                ),
                'type10fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '16',
                ),
                'pro13_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type13'),
                    ),
                    'std' => '#fff',
                ),
                'pro13_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type13'),
                    ),
                    'std' => 'rgba(0, 0, 0, 0.5)',
                ),
                'type13fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type13'),
                    ),
                    'std' => '16',
                ),

                // type34
                'type34_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'max' => 50,
                    'min' => 0,
                    'std' => '14',
                ),
                'type34_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'std' => '#333',
                ),
                'type34_link_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => '40',
                    'max' => 200,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type34'),

                    ),
                ),
                'type34__bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'std' => '#f5f6fb',
                ),
                'type34_img_w' => array(
                    'type' => 'slider',
                    'title' => '图片的宽度设置',
                    'desc' => '宽',
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 242,
                ),

                'type34_img_h' => array(
                    'type' => 'slider',
                    'title' => '图片的高度设置',
                    'desc' => '高',
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                ),

                'type34_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('外间距'),
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                'type34_padding' => array(
                    'type' => 'slider',
                    'title' => '右内间距',
                    'desc' => '间距',
                    'depends' => array(
                        array('pro_type', '=', 'type34'),
                    ),
                    'max' => 50,
                    'min' => 0,
                    'std' => 15,
                ),
                //结束
                //type35
                'type35_img_w' => array(
                    'type' => 'slider',
                    'title' => '图片缩小的宽度',
                    'desc' => '未选中时的宽',
                    'depends' => array(
                        array('pro_type', '=', 'type35'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 240,
                ),
                'type35_img_wd' => array(
                    'type' => 'slider',
                    'title' => '图片放大的宽度',
                    'desc' => '鼠标划过时的宽',
                    'depends' => array(
                        array('pro_type', '=', 'type35'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 600,
                ),
                'type35_img_h' => array(
                    'type' => 'slider',
                    'title' => '图片的高度设置',
                    'desc' => '高',
                    'depends' => array(
                        array('pro_type', '=', 'type35'),
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 450,
                ),
                //type37
                'type37_top_status' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启选项上方模块'),
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                    'std' => 1,
                ),
                'type37_top_bj' => array(
                    'type' => 'slider',
                    'title' => JText::_('分类顶部距离'),
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                ),
                'type37_bg_img' => array(
                    'type' => 'media',
                    'title' => '选项上方背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211230/c7a139f065f25541e8819ec128fbaba4.jpg',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                        array('type37_top_status', '=', 1),
                    ),
                ),
                'type37_top_font_color' => array(
                    'type' => 'color',
                    'title' => '选项上方字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                        array('type37_top_status', '=', 1),
                    ),
                ),
                'type37_top_font_content' => array(
                    'type' => 'text',
                    'title' => '选项上方文字内容',
                    'std' => '产品介绍',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_dangqian_font_content' => array(
                    'type' => 'text',
                    'title' => '当前位置文字内容',
                    'std' => '当前位置:',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_shouye_font_content' => array(
                    'type' => 'text',
                    'title' => '当前位置首页文字内容',
                    'std' => '首页',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_color_xz' => array(
                    'type' => 'color',
                    'title' => '选项字体颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_padding' => array(
                    'type' => 'padding',
                    'title' => '选项字体内边距',
                    'std' => '0px 0px 0px 20px',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_lineheight' => array(
                    'type' => 'slider',
                    'title' => '选项字体行间距',
                    'max' => 100,
                    'min' => 1,
                    'std' => 32,
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_color_hg' => array(
                    'type' => 'color',
                    'title' => '选项滑过字体颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_bgcolor_xz' => array(
                    'type' => 'color',
                    'title' => '选项选中背景颜色',
                    'std' => '#8CB9A4',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_title_big_bgcolor' => array(
                    'type' => 'color',
                    'title' => '选项整体背景颜色',
                    'std' => '#F2F2F2',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_right_title_color' => array(
                    'type' => 'color',
                    'title' => '右侧标题字体颜色',
                    'std' => '#639F83',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_right_time_content' => array(
                    'type' => 'text',
                    'title' => '右侧栏目时间前面展示信息',
                    'std' => '信息来源:长春凯希环保有限责任公司',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_right_time_color' => array(
                    'type' => 'color',
                    'title' => '右侧时间栏目字体颜色',
                    'std' => '#999',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                'type37_right_time_bgcolor' => array(
                    'type' => 'color',
                    'title' => '右侧时间栏目背景颜色',
                    'std' => '#F6F6F6',
                    'depends' => array(
                        array('pro_type', '=', 'type37'),
                    ),
                ),
                //
                'type18fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type18'),
                    ),
                    'std' => '16',
                ),
                'pro18_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type18'),
                    ),
                    'std' => '#fff',
                ),
                'pro18_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type18'),
                    ),
                    'std' => '#4a4a4a',
                ),
                'pro18_fontcon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type18'),
                    ),
                    'std' => '#fff',
                ),
                'pro18_bgcon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type18'),
                    ),
                    'std' => 'rgba(0, 0, 0, 0.5)',
                ),
                'type9fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                    ),
                    'std' => '16',
                ),
                'pro9_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#666',
                ),
                'pro9_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro9_font_con_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常简介字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#666',
                ),
                'pro9_font_con_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入简介字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro9_font_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('正常背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro9_font_bg_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入背景颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type9'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#cfa268',
                ),
                'type3fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入标题字体大小'),
                    'depends' => array(
                        array('pro_type', '!=', 'type1'),
                        array('pro_type', '!=', 'type2'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type8'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '22',
                ),
                'type3fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '!=', 'type1'),
                        array('pro_type', '!=', 'type2'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type8'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#ffffff',
                ),
                'type3bgclolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入背景颜色'),
                    'depends' => array(
                        array('pro_type', '!=', 'type1'),
                        array('pro_type', '!=', 'type2'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type8'),
                        array('pro_type', '!=', 'type9'),
                        array('pro_type', '!=', 'type10'),
                        array('pro_type', '!=', 'type12'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type18'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type21'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => 'rgba(0, 0, 0, 0.5)',
                ),
                'type5_icon' => array(
                    'type' => 'media',
                    'title' => '移入图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png',
                    'depends' => array(
                        array('pro_type', '=', 'type5'),
                    ),
                ),
                'type11_icon' => array(
                    'type' => 'media',
                    'title' => '移入图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/works_more.png',
                    'depends' => array(
                        array('pro_type', '=', 'type11'),
                    ),
                ),
                'type5_icon_mgB' => array(
                    'type' => 'slider',
                    'title' => '移入图标距文字的距离',
                    'std' => '20',
                    'depends' => array(
                        array('pro_type', '=', 'type5'),
                    ),
                ),
                'pro_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type1'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#ffffff',
                ),
                'pro_font_color_bg_type2' => array(
                    'type' => 'color',
                    'title' => JText::_('字体背景颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'pro_font_color_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('字体背景颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type1'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'pro_font_color_type2_title' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro_font_color_type2_title_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后标题字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro_font_color_type2_intext' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '=', 'type2'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro_font_color_type2_intext_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后简介字体颜色'),
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '=', 'type2'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro_font_title_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 200,
                    'min' => 0,
                ),
                'pro_title_height_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题高度'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 300,
                    'std' => '50',
                ),
                'pro_title_height_type2_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('标题间距'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => '10px 0px 10px 0px',
                ),
                //布局66
                'pro66_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 100,
                    'std' => '24',
                ),
                'pro66_fontColor' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#255699',
                ),
                'pro66_bqfontsize' => array(
                    'type' => 'slider',
                    'title' => '标签文字大小',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 100,
                    'std' => '20',
                ),
                'pro66_bqfontColor' => array(
                    'type' => 'color',
                    'title' => '标签文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#255699',
                ),
                'pro66_bgColor' => array(
                    'type' => 'color',
                    'title' => '内容背景颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type66'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#f9f9f9',
                ),
                // 布局38 配置
                'pro38_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 300,
                    'std' => '18',
                ),
                'pro38_fontColor' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => '#333',
                ),
                'pro38_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '标题文字行高',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 300,
                    'std' => '40',
                ),
                'pro38_pad_left' => array(
                    'type' => 'slider',
                    'title' => '标题文字左间距',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 300,
                    'std' => '20',
                ),
                'pro38_pad_right' => array(
                    'type' => 'slider',
                    'title' => '标题文字右间距',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 300,
                    'std' => '30',
                ),
                'pro38_mg_bot' => array(
                    'type' => 'slider',
                    'title' => '列表间距',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'max' => 300,
                    'std' => '3',
                ),
                'pro38_icon' => array(
                    'type' => 'media',
                    'title' => '标题前小图标',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20211229/55a6e44608d9786e70b88d71f19c0be9.png',
                ),
                'pro38_fontColor_hover' => array(
                    'type' => 'color',
                    'title' => '移入标题文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                        array('hover_border_pub', '=', 'hover'),
                    ),
                    'std' => 'rgb(125, 38, 38)',
                ),
                'pro38_page_type' => array(
                    'type' => 'select',
                    'title' => '跳转方式',
                    'values' => array(
                        '_blank' => '新窗口',
                        '_self' => '本窗口',
                    ),
                    'std' => '_blank',
                    'depends' => array(
                        array('pro_type', '=', 'type38'),
                    ),
                ),
                'pro2_page_type' => array(
                    'type' => 'select',
                    'title' => '跳转方式',
                    'values' => array(
                        '_blank' => '新窗口',
                        '_self' => '本窗口',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                    ),
                ),
                // 布局41 配置
                'pro41_main_color' => array(
                    'type' => 'color',
                    'title' => '主题色',
                    'depends' => array(
                        array('pro_type', '=', 'type41'),
                    ),
                    'std' => '#00aea4',
                ),
                'pro41_img_h' => array(
                    'type' => 'slider',
                    'title' => 'pc及平板图片高度',
                    'depends' => array(
                        array('pro_type', '=', 'type41'),
                    ),
                    'max' => 1000,
                    'std' => '274',
                ),
                'pro41_img_h_xs' => array(
                    'type' => 'slider',
                    'title' => '手机端图片高度',
                    'depends' => array(
                        array('pro_type', '=', 'type41'),
                    ),
                    'max' => 1000,
                    'std' => '180',
                ),
                // 布局42 配置
                'pro42_img_h' => array(
                    'type' => 'slider',
                    'title' => 'pc及平板图片高度',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '320',
                ),
                'pro42_img_h_xs' => array(
                    'type' => 'slider',
                    'title' => '手机端图片高度',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '180',
                ),
                'pro42_item_p' => array(
                    'type' => 'slider',
                    'title' => '列横向间距',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '16',
                ),
                'pro42_item_m' => array(
                    'type' => 'slider',
                    'title' => '列横竖向间距',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '20',
                ),
                'pro42_item_t_h' => array(
                    'type' => 'slider',
                    'title' => '标题部分高度',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '62',
                ),
                'pro42_item_s_p' => array(
                    'type' => 'slider',
                    'title' => '简介部分上下内边距',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'max' => 1000,
                    'std' => '10',
                ),
                'pro42_item_s' => array(
                    'type' => 'text',
                    'title' => '简介前文字',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'std' => '简介:',
                ),
                'pro42_item_label1' => array(
                    'type' => 'text',
                    'title' => '标签一前文字',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'std' => '从业年限:',
                ),
                'pro42_style_pub' => array(
                    'type' => 'buttons',
                    'title' => '列表状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                    ),
                    'tabs' => true,
                ),
                'pro42_main_color' => array(
                    'type' => 'color',
                    'title' => '标题及简介部分背景色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'std' => '#fff',
                ),
                'pro42_item_t_f' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'max' => 1000,
                    'std' => '20',
                ),
                'pro42_item_t_c' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'std' => '#222',
                ),
                'pro42_item_s_f' => array(
                    'type' => 'slider',
                    'title' => '简介文字大小',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'max' => 1000,
                    'std' => '14',
                ),
                'pro42_item_s_c' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'std' => '#666',
                ),
                'pro42_item_l_h' => array(
                    'type' => 'slider',
                    'title' => '标题部分底部线条高度',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'max' => 1000,
                    'std' => '3',
                ),
                'pro42_item_l_c' => array(
                    'type' => 'color',
                    'title' => '标题部分底部线条颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                    'std' => 'rgba(0, 0, 0, 0)',
                ),
                'pro42_box_color' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'std' => 'rgba(155, 155, 155, 0.12)',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                ),
                'pro42_box_h_shadow' => array(
                    'type' => 'slider',
                    'title' => '水平偏移',
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                ),
                'pro42_box_v_shadow' => array(
                    'type' => 'slider',
                    'title' => '垂直偏移',
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                ),
                'pro42_box_blur' => array(
                    'type' => 'slider',
                    'title' => '模糊',
                    'max' => 100,
                    'min' => 0,
                    'std' => 8,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                ),
                'pro42_box_spread' => array(
                    'type' => 'slider',
                    'title' => '扩展',
                    'max' => 100,
                    'min' => 0,
                    'std' => 2,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'normal'),
                    ),
                ),
                'pro42_main_color_hover' => array(
                    'type' => 'color',
                    'title' => '标题及简介部分背景色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                    'std' => '#b8d972',
                ),
                'pro42_item_t_c_hover' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro42_item_s_c_hover' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro42_item_l_c_hover' => array(
                    'type' => 'color',
                    'title' => '标题部分底部线条颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                    'std' => '#fff',
                ),
                'pro42_box_color_hover' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                ),
                'pro42_box_h_shadow_hover' => array(
                    'type' => 'slider',
                    'title' => '水平偏移',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                ),
                'pro42_box_v_shadow_hover' => array(
                    'type' => 'slider',
                    'title' => '垂直偏移',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                ),
                'pro42_box_blur_hover' => array(
                    'type' => 'slider',
                    'title' => '模糊',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                ),
                'pro42_box_spread_hover' => array(
                    'type' => 'slider',
                    'title' => '扩展',
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type42'),
                        array('pro42_style_pub', '=', 'hover'),
                    ),
                ),
                // 产品45
                'pro45_title_size' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '36',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_title_color' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),

                'pro45_title_margin' => array(
                    'type' => 'slider',
                    'title' => '标题下间距',
                    'std' => '0',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),


                'pro45_txt02_size' => array(
                    'type' => 'slider',
                    'title' => '【标签一】文字大小(标题下方文字)',
                    'max' => 100,
                    'min' => 0,
                    'std' => '20',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_txt02_color' => array(
                    'type' => 'color',
                    'title' => '【标签一】文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_txt03_color' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_txt03_clear' => array(
                    'type' => 'checkbox',
                    'title' => '开启简介文字HTML标签过滤',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_img_color' => array(
                    'type' => 'color',
                    'title' => '图片选中背景颜色',
                    'std' => '#b9d876',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_txt03_a_color' => array(
                    'type' => 'color',
                    'title' => '<详情>文字颜色',
                    'std' => '#f58430',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_open_dw' => array(
                    'type' => 'checkbox',
                    'title' => '按钮开启定位',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_bottom' => array(
                    'type' => 'slider',
                    'title' => '按钮底部距离',
                    'std' => '20',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_open_dw', '=', '1'),

                    ),
                ),
                'pro45_btn01_text' => array(
                    'type' => 'text',
                    'title' => '【我要预约】按钮文字',
                    'std' => '我要预约 >',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_btn01_bgColor' => array(
                    'type' => 'color',
                    'title' => '【我要预约】按钮颜色',
                    'std' => '#f58430',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn01_text', '!=', ''),
                    ),
                ),
                'pro45_btn01_color' => array(
                    'type' => 'color',
                    'title' => '【我要预约】按钮文字颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn01_text', '!=', ''),
                    ),
                ),
                'pro45_btn01_page_id' => array(
                    'type' => 'select',
                    'title' => '【我要预约】跳转页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn01_text', '!=', ''),
                    ),
                ),
                'pro45_btn01_section_id' => array(
                    'type' => 'text',
                    'title' => '【我要预约】绑定章节ID',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn01_text', '!=', ''),
                    ),
                ),
                'pro45_btn02_text' => array(
                    'type' => 'text',
                    'title' => '【更多】按钮文字',
                    'std' => '更多 >',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'pro45_btn02_bgColor' => array(
                    'type' => 'color',
                    'title' => '【我要预约】按钮颜色',
                    'std' => '#abacad',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn02_text', '!=', ''),
                    ),
                ),
                'pro45_btn02_color' => array(
                    'type' => 'color',
                    'title' => '【我要预约】按钮文字颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn02_text', '!=', ''),
                    ),
                ),
                'pro45_btn02_page_id' => array(
                    'type' => 'select',
                    'title' => '【更多】跳转页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn02_text', '!=', ''),
                    ),
                ),
                'pro45_btn02_section_id' => array(
                    'type' => 'text',
                    'title' => '【更多】绑定章节ID',
                    'depends' => array(
                        array('pro_type', '=', 'type45'),
                        array('pro45_btn02_text', '!=', ''),
                    ),
                ),
                // 布局48
                'type48_separator_01' => array(
                    'type' => 'separator',
                    'title' => '图片配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_text_icon' => array(
                    'type' => 'media',
                    'title' => '产品图上方图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220224/4a01975bdb6042d14124f68d39fe615f.png',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_text_top' => array(
                    'type' => 'slider',
                    'title' => '产品图上方图片上边距',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '40',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_text_left' => array(
                    'type' => 'slider',
                    'title' => '产品图上方图片左边距',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '20',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_separator_02' => array(
                    'type' => 'separator',
                    'title' => '内容底部背景色块配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_bg_color' => array(
                    'type' => 'color',
                    'title' => '内容底部背景颜色',
                    'std' => 'rgba(60, 63, 78)',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_bg_width' => array(
                    'type' => 'slider',
                    'title' => '内容底部背景宽度(百分比)',
                    'max' => 100,
                    'min' => 0,
                    'std' => '70',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_bg_height' => array(
                    'type' => 'slider',
                    'title' => '内容底部背景高度(px)',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '580',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_separator_04' => array(
                    'type' => 'separator',
                    'title' => '内容部分--图片配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_img_width' => array(
                    'type' => 'slider',
                    'title' => '内容图片宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '605',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_img_height' => array(
                    'type' => 'slider',
                    'title' => '内容图片高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '374',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_separator_03' => array(
                    'type' => 'separator',
                    'title' => '内容部分--文字配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_num_size' => array(
                    'type' => 'slider',
                    'title' => '数字文字大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '34',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_num_color' => array(
                    'type' => 'color',
                    'title' => '数字文字颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_num_mgB' => array(
                    'type' => 'slider',
                    'title' => '数字文字下边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => '30',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_title_size' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '36',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_title_color' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_title_mgB' => array(
                    'type' => 'slider',
                    'title' => '标题文字下边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => '29',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_titles_color' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'std' => '#E5E5E5',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_separator_05' => array(
                    'type' => 'separator',
                    'title' => '内容部分--按钮配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_btn_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭按钮',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_btn_text' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'std' => '了解更多',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_width' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => '176',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_height' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'max' => 300,
                    'min' => 0,
                    'std' => '50',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_size' => array(
                    'type' => 'slider',
                    'title' => '按钮文字大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '14',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_border_size' => array(
                    'type' => 'slider',
                    'title' => '按钮边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '2',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_model' => array(
                    'type' => 'buttons',
                    'title' => JText::_('按钮状态'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                    ),
                ),
                'type48_btn_border_color' => array(
                    'type' => 'color',
                    'title' => '按钮边框颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'normal'),
                    ),
                ),
                'type48_btn_bg_color' => array(
                    'type' => 'color',
                    'title' => '按钮背景颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'normal'),
                    ),
                ),
                'type48_btn_color' => array(
                    'type' => 'color',
                    'title' => '按钮文字颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'normal'),
                    ),
                ),
                'type48_btn_border_color_hover' => array(
                    'type' => 'color',
                    'title' => '移入按钮边框颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'hover'),
                    ),
                ),
                'type48_btn_bg_color_hover' => array(
                    'type' => 'color',
                    'title' => '移入按钮背景颜色',
                    'std' => 'rgba(255, 255, 255)',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'hover'),
                    ),
                ),
                'type48_btn_color_hover' => array(
                    'type' => 'color',
                    'title' => '移入按钮文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_btn_hide', '!=', '1'),
                        array('type48_btn_model', '=', 'hover'),
                    ),
                ),
                'type48_separator_06' => array(
                    'type' => 'separator',
                    'title' => '内容部分--切换控制配置',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_page_model' => array(
                    'type' => 'buttons',
                    'title' => '控制器选项',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '切换箭头',
                            'value' => 'el',
                        ),
                        array(
                            'label' => '切换点',
                            'value' => 'pg',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type48_el_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭切换箭头',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'el'),
                    ),
                ),
                'type48_el_prev' => array(
                    'type' => 'media',
                    'title' => '上翻页按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220224/9f3aa43984fe8057884589c04942e4d4.png',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'el'),
                        array('type48_el_hide', '!=', '1'),
                    ),
                ),
                'type48_el_next' => array(
                    'type' => 'media',
                    'title' => '下翻页按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220224/88b97d77f022e0635217fcf703d9f218.png',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'el'),
                        array('type48_el_hide', '!=', '1'),
                    ),
                ),
                'type48_pg_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭切换点',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                    ),
                ),
                'type48_pg_mg' => array(
                    'type' => 'slider',
                    'title' => '切换点间距',
                    'max' => 100,
                    'min' => 0,
                    'std' => '10',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                    ),
                ),
                'type48_pg_model' => array(
                    'type' => 'buttons',
                    'title' => JText::_('切换点状态'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                    ),
                ),
                'type48_pg_width' => array(
                    'type' => 'slider',
                    'title' => '切换点宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '13',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'normal'),
                    ),
                ),
                'type48_pg_height' => array(
                    'type' => 'slider',
                    'title' => '切换点高度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '13',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'normal'),
                    ),
                ),
                'type48_pg_color' => array(
                    'type' => 'color',
                    'title' => '切换点颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'normal'),
                    ),
                ),
                'type48_pg_border_width' => array(
                    'type' => 'slider',
                    'title' => '切换点边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '2',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'normal'),
                    ),
                ),
                'type48_pg_border_color' => array(
                    'type' => 'color',
                    'title' => '切换点边框颜色',
                    'std' => '#999',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'normal'),
                    ),
                ),
                'type48_pg_width_active' => array(
                    'type' => 'slider',
                    'title' => '切换点宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'active'),
                    ),
                ),
                'type48_pg_height_active' => array(
                    'type' => 'slider',
                    'title' => '选中切换点高度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'active'),
                    ),
                ),
                'type48_pg_color_active' => array(
                    'type' => 'color',
                    'title' => '选中切换点颜色',
                    'std' => 'rgba(255, 255, 255)',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'active'),
                    ),
                ),
                'type48_pg_border_width_active' => array(
                    'type' => 'slider',
                    'title' => '选中切换点边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'active'),
                    ),
                ),
                'type48_pg_border_color_active' => array(
                    'type' => 'color',
                    'title' => '选中切换点边框颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                        array('type48_page_model', '=', 'pg'),
                        array('type48_pg_hide', '!=', '1'),
                        array('type48_pg_model', '=', 'active'),
                    ),
                ),

                'tz_type' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('跳转后台设置好的链接'),
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type5'),
                        array('tz_type_status', '=', 0),
                    ),
                ),
                'open_type' => array(
                    'type' => 'select',
                    'title' => '选择链接打开类型',
                    'desc' => '',
                    'values' => array(
                        '_top' => '当前页打开',
                        '_blank' => '新页面打开',
                    ),
                    'std' => '_top',
                    'depends' => array(
                        array('tz_type', '=', 1),
                        array('tz_type_status', '=', 0),
                    ),
                ),

                // type56
                'type65_close' => array(
                    'type' => 'checkbox',
                    'title' => '关闭滚动',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_cont_top' => array(
                    'type' => 'margin',
                    'title' => '整体外边距',
                    'std' => '80px 0px 0px 0px',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_pcclums' => array(
                    'type' => 'slider',
                    'title' => 'PC每行个数',
                    'std' => '4',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                        array('type65_close', '=', '1'),
                    ),
                ),
                'type65_sjclums' => array(
                    'type' => 'slider',
                    'title' => '手机每行个数',
                    'std' => '3',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                        array('type65_close', '=', '1'),
                    ),
                ),
                'type65_width_bai' => array(
                    'type' => 'checkbox',
                    'title' => '图片宽度开启百分百',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_width' => array(
                    'type' => 'slider',
                    'title' => '图片宽',
                    'std' => '211',
                    'max' => '500',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                        array('type65_width_bai', '!=', '1'),

                    ),
                ),
                'type65_height' => array(
                    'type' => 'slider',
                    'title' => '图片高',
                    'std' => '211',
                    'max' => '500',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_radius' => array(
                    'type' => 'slider',
                    'title' => '图片圆角',
                    'std' => '100',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_padding' => array(
                    'type' => 'slider',
                    'title' => '边框与图片间距',
                    'std' => '0',
                    'max' => '50',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),

                'title_textalign65' => array(
                    'type' => 'select',
                    'title' => '文字对齐方式',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                    'std' => 'center',
                ),
                'type65_margintop' => array(
                    'type' => 'slider',
                    'title' => '标题上间距',
                    'std' => '35',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_tit1' => array(
                    'type' => 'text',
                    'title' => '名称1（取值标题）',
                    'std' => '品牌:',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_tit2' => array(
                    'type' => 'text',
                    'title' => '名称2（取值标签1）',
                    'std' => '主营:',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_tit3' => array(
                    'type' => 'text',
                    'title' => '名称3（取值标签2）',
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_titcolor' => array(
                    'type' => 'color',
                    'title' => '名称颜色',
                    'std' => '#8e754e',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_titfontsize' => array(
                    'type' => 'slider',
                    'title' => '名称字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_color' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'std' => '16',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_labcolor' => array(
                    'type' => 'color',
                    'title' => '标签颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                'type65_labfontsize' => array(
                    'type' => 'slider',
                    'title' => '标签字体大小',
                    'std' => '14',
                    'depends' => array(
                        array('pro_type', '=', 'type65'),
                        array('hover_border_pub', '=', 'normal'),
                    ),
                ),
                // 内容配置项
                'content_settings' => array(
                    'type' => 'separator',
                    'title' => '内容配置项',
                ),
                //
                'text_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('标题显示字数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT_DESC'),
                    'std' => '20',
                    'depends' => array(
                        //                        array('show_intro', '=', 1),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        //                        array('pro_type', '=', 'type19'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type69'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'title_textalign8' => array(
                    'type' => 'select',
                    'title' => '标题文字对齐方式',
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                    'std' => 'center',
                ),
                'button_textalign8' => array(
                    'type' => 'select',
                    'title' => '按钮对齐方式',
                    'depends' => array(
                        array('pro_type', '=', 'type8'),
                    ),
                    'values' => array(
                        'flex-start' => '居左',
                        'center' => '居中',
                        'flex-end' => '居右',
                    ),
                    'std' => 'center',
                ),
                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'depends' => array(
                        array('pro_type', '!=', 'type1'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => 1,
                ),

                'same_show' => array(
                    'type' => 'checkbox',
                    'title' => '是否原样展示',
                    'std' => 0,
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '=', 'type10'),
                    )
                ),

                'intro_textalign' => array(
                    'type' => 'select',
                    'title' => '简介文字对齐方式',
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '!=', 'type1'),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),

                    ),
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                    'std' => 'left',
                ),
                'intro_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('简介显示字数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT_DESC'),
                    'std' => '',
                    'depends' => array(
                        array('show_intro', '=', 1),
                        //  array('pro_type', '=', 'type5'),
                        //                        array('pro_type', '=', 'type19'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                        array('same_show', '=', 0)
                    ),
                ),

                'pro_font_intext_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '!=', 'type3'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type22'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type34'),
                        array('pro_type', '!=', 'type35'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'max' => 200,
                    'min' => 0,
                ),

                'show_more_button' => array(
                    'title' => '是否展示查看更多按钮',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type10'),
                    )
                ),

                'box_font8_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'depends' => array('pro_type' => 'type8'),
                    'std' => '#666',
                ),
                'box_font6_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'depends' => array('pro_type' => 'type6'),
                    'std' => '#fff',
                ),
                'type53_jinjiecolor' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'depends' => array(
                        array('show_intro', '=', 1),
                        array('pro_type', '=', 'type53'),
                    ),
                    'std' => '#555',
                ),
                'box_font6_genduo' => array(
                    'type' => 'color',
                    'title' => JText::_('+颜色'),
                    'depends' => array('pro_type' => 'type6'),
                    'std' => '#fff',
                ),
                'box_font21_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'depends' => array('pro_type' => 'type21'),
                    'std' => '#333',
                ),
                'img_animated' => array(
                    'type' => 'select',
                    'title' => '选择图片动画',
                    'desc' => '图片动画',
                    'depends' => array('pro_type' => 'type2'),
                    'values' => array(
                        'animated1' => '无',
                        'animated2' => '放大',
                        'animated3' => '遮罩',
                    ),
                    'std' => 'animated1',
                ),
                'an3_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('遮罩上标题颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('img_animated', '=', 'animated3'),
                    ),
                ),
                'box_type2_shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('盒子阴影颜色'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => '#ffffff',
                ),
                'box_type2_shadow_x' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影水平偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type2_shadow_Y' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影垂直偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type2_shadow_mh' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影模糊'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type2_shadow_kz' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影扩展'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type1_shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('盒子阴影颜色'),
                    'depends' => array('pro_type' => 'type1'),
                    'std' => '#ffffff',
                ),
                'box_type1_shadow_x' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影水平偏移'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type1_shadow_Y' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影垂直偏移'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type1_shadow_mh' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影模糊'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 100,
                    'min' => 0,
                ),
                'box_type1_shadow_kz' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影扩展'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 100,
                    'min' => 0,
                ),

                'pro_font_color_bg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体背景高度'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 2000,
                    'min' => 0,
                ),

                'show_title' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('标题是否固定显示'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'depends' => array('pro_type' => 'type1'),
                    'std' => '0',
                ),
                'type31_right_title' => array(
                    'type' => 'text',
                    'title' => JText::_('右侧文字'),
                    'desc' => JText::_('右侧文字'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '专   业   软   硬   件   研   发   集   成   厂   家',
                ),
                'type31_right_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧文字颜色'),
                    'desc' => JText::_('右侧文字颜色'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '#000000',
                ),
                'type31_right_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧按钮字体颜色'),
                    'desc' => JText::_('右侧按钮字体颜色'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '#fff',
                ),
                'type31_right_button_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧按钮背景颜色'),
                    'desc' => JText::_('右侧按钮背景颜色'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '#0c2e5c',
                ),
                'type31_right_button_bgcolor_yr' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧按钮滑过背景颜色'),
                    'desc' => JText::_('右侧按钮滑过背景颜色'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '#0c2e5c',
                ),
                'type31_right_button_color_yr' => array(
                    'type' => 'color',
                    'title' => JText::_('右侧按钮滑过字体颜色'),
                    'desc' => JText::_('右侧按钮滑过字体颜色'),
                    'depends' => array('pro_type' => 'type31'),
                    'std' => '#fff',
                ),
                'type32_button_bgcolor_zt' => array(
                    'type' => 'color',
                    'title' => JText::_('标题整体背景颜色'),
                    'desc' => JText::_('标题整体背景颜色'),
                    'depends' => array('pro_type' => 'type32'),
                    'std' => '#f4f6f9',
                ),
                'type32_button_bgcolor_xz' => array(
                    'type' => 'color',
                    'title' => JText::_('标题选中背景颜色'),
                    'desc' => JText::_('标题选中背景颜色'),
                    'depends' => array('pro_type' => 'type32'),
                    'std' => '#053478',
                ),
                'type32_button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'desc' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type32'),
                    'std' => '#000',
                ),
                'type32_button_color_xz' => array(
                    'type' => 'color',
                    'title' => JText::_('标题选中字体颜色'),
                    'desc' => JText::_('标题选中字体颜色'),
                    'depends' => array('pro_type' => 'type32'),
                    'std' => '#FFFFFF',
                ),
                /** 产品布局 64 配置项 start*/
                'type64_img_height' => array(
                    'type' => 'slider',
                    'title' => '封面图片高度',
                    'std' => array(
                        'md' => '240',
                        // 'sm' => '240',
                        // 'xs' => '240'
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_num' => array(
                    'type' => 'slider',
                    'title' => '一列显示个数',
                    'std' => array(
                        'md' => '4',
                        'sm' => '3',
                        'xs' => '2'
                    ),
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_title_line' => array(
                    'type' => 'slider',
                    'title' => '标题显示行数',
                    'std' => '2',
                    'min' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_title' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'std' => array(
                        'md' => '16',
                        // 'sm' => '3',
                        // 'xs' => '2'
                    ),
                    'min' => 12,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_price_i' => array(
                    'type' => 'slider',
                    'title' => '价格前价格图标大小',
                    'std' => array(
                        'md' => '12',
                        // 'sm' => '3',
                        // 'xs' => '2'
                    ),
                    'min' => 12,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_price' => array(
                    'type' => 'slider',
                    'title' => '价格数字大小',
                    'std' => array(
                        'md' => '20',
                        // 'sm' => '3',
                        // 'xs' => '2'
                    ),
                    'min' => 12,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_bg_color' => array(
                    'type' => 'color',
                    'title' => '列表背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_title_color' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_list_price_color' => array(
                    'type' => 'color',
                    'title' => '价格文字颜色',
                    'std' => '#ff2b02',
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                    ),
                ),
                'type64_out_link' => array(
                    'type' => 'checkbox',
                    'title' => '跳转客户端外部链接',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type64'),
                        array('tz_type_status', '=', '0'),
                    )
                ),
                /** 产品布局 64 配置项 end */
                // 产品70配置项
                // 整体高度
                'height_type70' => array(
                    'type' => 'slider',
                    'title' => '整体高度',
                    'std' => array(
                        'md' => '920',
                        'sm' => '680',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                    ),
                ),
                // 左侧背景图
                'left_bg_type70' => array(
                    'type' => 'media',
                    'title' => '左侧背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250731/33f13466bb30479db114b5c300f47a24.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                    ),
                ),
                // 左侧文字距离顶部距离
                'left_top_type70' => array(
                    'type' => 'slider',
                    'title' => '左侧文字部分距顶部距离',
                    'std' => array(
                        'md' => '150',
                        'sm' => '80',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                    ),
                ),
                // 左侧文字部分内边距
                'left_content_padding_type70' => array(
                    'type' => 'padding',
                    'title' => '左侧文字部分内边距',
                    'std' => array(
                        'md' => '40px 60px 40px 40px',
                        'sm' => '0px 30px 0px 30px',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                    ),
                ),
                // buttons
                'left_font_buttons_type70' => array(
                    'type' => 'buttons',
                    'title' => '相关配置',
                    'std' => 'title',
                    'values' => array(
                        array(
                            'label' => '固定文字',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'subtitle'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'info'
                        ),
                        array(
                            'label' => '翻页按钮',
                            'value' => 'btn'
                        ),
                        array(
                            'label' => '底部',
                            'value' => 'bottom'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                    ),
                ),
                // 左侧固定文字
                'left_text_type70' => array(
                    'type' => 'text',
                    'title' => '左侧固定文字',
                    'std' => 'STORE STYLE',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'title'),
                    ),
                ),
                // 左侧文字粗细
                'left_fontweight_type70' => array(
                    'type' => 'select',
                    'title' => '左侧固定文字粗细',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'std' => 'bold',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_text_type70', '!=', ''),
                        array('left_font_buttons_type70', '=', 'title'),
                    ),
                ),
                // 左侧文字字体
                'left_font_family_type70' => array(
                    'type' => 'fonts',
                    'title' => '左侧固定文字字体',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_text_type70', '!=', ''),
                        array('left_font_buttons_type70', '=', 'title'),
                    )
                ),
                // 左侧固定文字大小
                'left_font_size_type70' => array(
                    'type' => 'slider',
                    'title' => '左侧固定文字大小',
                    'std' => array(
                        'md' => '60',
                        'sm' => '40',
                        'xs' => ''
                    ),
                    'max' => 100,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_text_type70', '!=', ''),
                        array('left_font_buttons_type70', '=', 'title'),
                    ),
                ),
                // 左侧固定文字颜色
                'left_font_color_type70' => array(
                    'type' => 'color',
                    'title' => '左侧固定文字颜色',
                    'std' => '#E7BF75',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_text_type70', '!=', ''),
                        array('left_font_buttons_type70', '=', 'title'),
                    ),
                ),
                // 左侧固定文字下边距
                'left_font_marginbottom_type70' => array(
                    'type' => 'slider',
                    'title' => '左侧固定文字下边距',
                    'std' => array(
                        'md' => '20',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'max' => 100,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_text_type70', '!=', ''),
                        array('left_font_buttons_type70', '=', 'title'),
                    ),
                ),
                // 标题文字粗细
                'left_title_fontweight_type70' => array(
                    'type' => 'select',
                    'title' => '标题文字粗细',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'std' => 'bold',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'subtitle'),
                    ),
                ),
                // 标题文字大小
                'left_title_fontsize_type70' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'std' => array(
                        'md' => '60',
                        'sm' => '40',
                        'xs' => ''
                    ),
                    'max' => 100,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'subtitle'),
                    ),
                ),
                // 标题文字颜色
                'left_title_color_type70' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'subtitle'),
                    ),
                ),
                // 标题文字下边距
                'left_title_marginbottom_type70' => array(
                    'type' => 'slider',
                    'title' => '标题文字下边距',
                    'std' => array(
                        'md' => '40',
                        'sm' => '',
                        'xs' => ''
                    ),
                    'max' => 100,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'subtitle'),
                    ),
                ),
                // 内容文字粗细
                'left_info_fontweight_type70' => array(
                    'type' => 'select',
                    'title' => '内容文字粗细',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '加粗',
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'info'),
                    ),
                ),
                // 内容文字大小
                'left_info_fontsize_type70' => array(
                    'type' => 'slider',
                    'title' => '内容文字大小',
                    'std' => array(
                        'md' => '24',
                        'sm' => '18',
                        'xs' => ''
                    ),
                    'max' => 100,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'info'),
                    ),
                ),
                // 内容文字颜色
                'left_info_color_type70' => array(
                    'type' => 'color',
                    'title' => '内容文字颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'info'),
                    ),
                ),
                // 翻页按钮宽度
                'btn_width_type70' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮宽度',
                    'std' => array(
                        'md' => '60',
                        'sm' => '46',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 翻页按钮高度
                'btn_height_type70' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮高度',
                    'std' => array(
                        'md' => '60',
                        'sm' => '46',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 翻页按钮距底部距离
                'btn_marginbottom_type70' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮距底部距离',
                    'std' => array(
                        'md' => '220',
                        'sm' => '230',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 翻页按钮间距
                'btn_margin_type70' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮间距',
                    'std' => array(
                        'md' => '20',
                        'sm' => '20',
                        'xs' => '20'
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 翻页按钮上一页图片
                'btn_prev_img_type70' => array(
                    'type' => 'media',
                    'title' => '翻页按钮上一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250801/87c02e10b2c4d28e70e81cd7108f1a01.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 翻页按钮下一页图片
                'btn_next_img_type70' => array(
                    'type' => 'media',
                    'title' => '翻页按钮下一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250801/58fb77de414d7d2d36042529f0a93e46.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 移入翻页按钮上一页图片
                'btn_prev_hover_img_type70' => array(
                    'type' => 'media',
                    'title' => '移入翻页按钮上一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250801/0e883ab9a1f5f7120cfdf5e70c268954.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 移入翻页按钮下一页图片
                'btn_next_hover_img_type70' => array(
                    'type' => 'media',
                    'title' => '移入翻页按钮下一页图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250801/020cc74299ae57629f4183630427d78c.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'btn'),
                    ),
                ),
                // 底部弧形颜色
                'bottom_bgcolor_type70' => array(
                    'type' => 'color',
                    'title' => '底部弧形颜色',
                    'std' => '#C30000',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'bottom'),
                    ),
                ),
                // 底部logo背景宽高
                'bottom_logo_bg_size_type70' => array(
                    'type' => 'slider',
                    'title' => '底部logo背景宽高',
                    'std' => array(
                        'md' => '220',
                        'sm' => '160',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'bottom'),
                    ),
                ),
                // 底部logo背景颜色
                'bottom_logo_bg_color_type70' => array(
                    'type' => 'color',
                    'title' => '底部logo背景颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'bottom'),
                    ),
                ),
                // 底部logo宽高
                'bottom_logo_size_type70' => array(
                    'type' => 'slider',
                    'title' => '底部logo宽高',
                    'std' => array(
                        'md' => '138',
                        'sm' => '100',
                        'xs' => ''
                    ),
                    'max' => 1000,
                    'min' => 1,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'bottom'),
                    ),
                ),
                // 底部logo图片
                'bottom_logo_img_type70' => array(
                    'type' => 'media',
                    'title' => '底部logo图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250731/a9d9a2d56ba430c5fc8a9f41be47deeb.png',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('left_font_buttons_type70', '=', 'bottom'),
                    ),
                ),

                // 跳转方式
                'target_type70' => array(
                    'type' => 'select',
                    'title' => '跳转方式',
                    'desc' => '跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('pro_type', '=', 'type70'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                /* 产品71 */
                // 容器设置
                'wrap_type71' => array(
                    'type' => 'separator',
                    'title' => '容器设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                    ),
                ),
                // 轮播高度
                'height_type71' => array(
                    'type' => 'slider',
                    'title' => '轮播高度',
                    'std' => ['md' => 700, 'sm' => 700, 'xs' => 410],
                    'responsive' => true,
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                    ),
                ),
                // 内容设置
                'content_type71' => array(
                    'type' => 'separator',
                    'title' => '内容设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                    ),
                ),
                // 内容右边距
                'content_right_type71' => array(
                    'type' => 'slider',
                    'title' => '内容右边距',
                    'std' => ['md' => 260, 'sm' => 260, 'xs' => 0],
                    'responsive' => true,
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                    ),
                ),
                // 内容设置
                'content_settings_type71' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'title' => '容器设置',
                    'values' => [
                        [
                            'label' => '左',
                            'value' => 'left'
                        ],
                        [
                            'label' => '右',
                            'value' => 'right'
                        ]
                    ],
                    'std' => 'left',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                    ),
                ),
                // 左侧内边距
                'content_padding_type71' => array(
                    'type' => 'padding',
                    'title' => '左侧内边距',
                    'std' => ['md' => '0 60px 0 0', 'sm' => '0 60px 0 0', 'xs' => '0 0 0 0'],
                    'responsive' => true,
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 标题设置
                'title_type71' => array(
                    'type' => 'separator',
                    'title' => '标题设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_padding_type71', '=', 'left'),
                    ),
                ),
                // 标题外边距
                'title_margin_type71' => array(
                    'type' => 'margin',
                    'title' => '标题外边距',
                    'std' => ['md' => '0 0 139px 0', 'sm' => '0 0 139px 0', 'xs' => '0 0 100px 0'],
                    'responsive' => true,
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 标题字号
                'title_size_type71' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std' => ['md' => 45, 'sm' => 45, 'xs' => 30],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 标题行高
                'title_lineheight_type71' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std' => ['md' => 50, 'sm' => 50, 'xs' => 35],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 显示行数
                'title_line_type71' => array(
                    'type' => 'slider',
                    'title' => '显示行数',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 标题颜色
                'title_color_type71' => array(
                    'title' => '标题颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 岗位设置
                'position_type71' => array(
                    'type' => 'separator',
                    'title' => '岗位设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_padding_type71', '=', 'left'),
                    ),
                ),
                // 岗位外边距
                'position_margin_type71' => array(
                    'type' => 'margin',
                    'title' => '岗位外边距',
                    'std' => ['md' => '0 0 36px 0', 'sm' => '0 0 36px 0', 'xs' => '0 0 36px 0'],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 岗位字号
                'position_size_type71' => array(
                    'type' => 'slider',
                    'title' => '岗位字号',
                    'std' => ['md' => 20, 'sm' => 20, 'xs' => 18],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 岗位行高
                'position_lineheight_type71' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'std' => ['md' => 48, 'sm' => 48, 'xs' => 44],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 显示行数
                'position_line_type71' => array(
                    'type' => 'slider',
                    'title' => '显示行数',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 岗位颜色
                'position_color_type71' => array(
                    'title' => '岗位颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 岗位下方分界线设置
                'line_type71' => array(
                    'type' => 'separator',
                    'title' => '岗位下方分界线设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_padding_type71', '=', 'left'),
                    ),
                ),
                // 岗位下方分界线颜色
                'line_color_type71' => array(
                    'title' => '岗位下方分界线颜色',
                    'type' => 'color',
                    'std' => '#ccc',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 分界线宽度
                'line_width_type71' => array(
                    'type' => 'slider',
                    'title' => '分界线宽度(%)',
                    'std' => 90,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 分界线外边距
                'line_margin_type71' => array(
                    'type' => 'margin',
                    'title' => '分界线外边距',
                    'std' => ['md' => '0 0 42px 0', 'sm' => '0 0 42px 0', 'xs' => '0 0 42px 0'],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 简介设置
                'intro_type71' => array(
                    'type' => 'separator',
                    'title' => '简介设置',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_padding_type71', '=', 'left'),
                    ),
                ),
                // 简介外边距
                'intro_margin_type71' => array(
                    'type' => 'margin',
                    'title' => '简介外边距',
                    'std' => ['md' => '0 0 50px 0', 'sm' => '0 0 50px 0', 'xs' => '0 0 50px 0'],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 简介字号
                'intro_size_type71' => array(
                    'type' => 'slider',
                    'title' => '简介字号',
                    'std' => ['md' => 16, 'sm' => 16, 'xs' => 14],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 简介行高
                'intro_lineheight_type71' => array(
                    'type' => 'slider',
                    'title' => '简介行高',
                    'std' => ['md' => 36, 'sm' => 36, 'xs' => 32],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 显示行数
                'intro_line_type71' => array(
                    'type' => 'slider',
                    'title' => '显示行数',
                    'std' => 2,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 简介文字颜色
                'intro_color_type71' => array(
                    'title' => '简介文字颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 右侧图片宽度
                'right_width_type71' => array(
                    'type' => 'slider',
                    'title' => '右侧图片宽度',
                    'std' => ['md' => 500, 'sm' => 500, 'xs' => 200],
                    'responsive' => true,
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'right'),
                    ),
                ),
                // 查看更多
                'more_type71' => array(
                    'type' => 'separator',
                    'title' => '查看更多按钮',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 查看更多文字
                'more_text_tyep71' => array(
                    'title' => '查看更多文字',
                    'type' => 'text',
                    'std' => '查看更多',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    )
                ),
                // 查看更多外边距
                'more_margin_type71' => array(
                    'type' => 'margin',
                    'title' => '查看更多按钮外边距',
                    'std' => ['md' => '0 0 0 0', 'sm' => '0 0 0 0', 'xs' => '0 0 0 0'],
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 查看更多字号
                'more_size_type71' => array(
                    'type' => 'slider',
                    'title' => '查看更多字号',
                    'std' => ['md' => 16, 'sm' => 16, 'xs' => 14],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 是否展示图标
                'icon_status_type71' => array(
                    'type' => 'checkbox',
                    'title' => '是否展示图标',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '1'),
                    )
                ),
                // 图标大小
                'icon_size_type71' => array(
                    'type' => 'slider',
                    'title' => '查看更多字号',
                    'std' => ['md' => 40, 'sm' => 40, 'xs' => 30],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 图标右边距
                'icon_right_type71' => array(
                    'type' => 'slider',
                    'title' => '查看更多字号',
                    'std' => ['md' => 17, 'sm' => 17, 'xs' => 10],
                    'responsive' => true,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 图标颜色
                'icon_color_type71' => array(
                    'title' => '图标颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 文字颜色
                'more_color_type71' => array(
                    'title' => '按钮文字颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 轮播配置
                'page_type71' => array(
                    'type' => 'separator',
                    'title' => '分页器配置(移动端隐藏)',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图容器宽度
                'page_box_width_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图容器宽度(移动端隐藏)',
                    'std' => 160,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 分页器一屏显示个数
                'page_num_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图一屏显示个数(移动端隐藏)',
                    'std' => 3,
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 分页器间隔
                'page_gap_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图间隔(移动端隐藏)',
                    'std' => 10,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图右边距
                'page_right_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图右边距(移动端隐藏)',
                    'std' => 40,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图下边距
                'page_img_gap_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图图片下边距(移动端隐藏)',
                    'std' => 14,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图宽度
                'page_img_w_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图图片宽度(移动端隐藏)',
                    'std' => 100,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图图片高度
                'page_img_h_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图图片高度(移动端隐藏)',
                    'std' => 150,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 缩略图文字字号
                'page_font_size_type71' => array(
                    'type' => 'slider',
                    'title' => '缩略图文字字号(移动端隐藏)',
                    'std' => 18,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                // 文字颜色
                'page_font_color_type71' => array(
                    'title' => '文字颜色',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 分页器进度槽颜色
                'progress_color_type71' => array(
                    'title' => '分页器进度空状态颜色(移动端隐藏)',
                    'type' => 'color',
                    'std' => '#E8E8E8',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 分页器进度槽颜色
                'progress_bar_color_type71' => array(
                    'title' => '分页器进度颜色(移动端隐藏)',
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('content_settings_type71', '=', 'left'),
                    ),
                ),
                // 详情页跳转方式
                'detail_target_type71' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'desc' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('pro_type', '=', 'type71'),
                        array('tz_type_status', '=', '0'),
                    ),
                ),
                /* 产品71结束 */
                'tz_type_status' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('不跳转任何页面'),
                    'std' => '0',
                    'depends' => array(
                        //array('pro_type', '!=', 'type1'),//原本写样式1的人留了bug,样式短时间不能重写,先关掉这个选项
                        array('pro_type', '!=', 'type15'),
                        array('pro_type', '!=', 'type25'),
                        //array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),

                    ),
                ),
                'type48_link_type' => array(
                    'type' => 'checkbox',
                    'title' => '跳转客户端设置链接',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pro_type', '!=', 'type66'),
                    ),
                ),
                'detail_link_style' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('tz_type_status', '!=', 1),
                        array('pro_type', '=', 'type45'),
                    ),
                ),
                'detail_link_style_type53' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_blank',
                    'depends' => array(
                        array('pro_type', '=', 'type53'),
                    ),
                ),
                'type48_detail_link_style' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('tz_type_status', '!=', 1),
                        array('pro_type', '=', 'type48'),
                    ),
                ),
                'type68_detail_link_style' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_self',
                    'depends' => array(
                        array('tz_type_status', '!=', 1),
                        array('pro_type', '=', 'type68'),
                    ),
                ),
                'detail_link_style_69' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                    ),
                    'std' => '_blank',
                    'depends' => array(
                        array('tz_type_status', '!=', 1),
                        array('pro_type', '=', 'type69'),
                    ),
                ),

                'detail_page_title_id' => array(
                    'type' => 'select',
                    'title' => '右侧标题跳转页面',
                    'desc' => '显示内部页面模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type41'),
                        array('pro_type', '!=', 'type42'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type47'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),

                'resource' => array(
                    'type' => 'select',
                    'title' => '选择产品资源',
                    'desc' => '选择产品资源',
                    'values' => array(
                        'article' => '产品资源',
                    ),
                    'std' => 'article',
                    'depends' => array(
                        array('pro_type', '!=', 'type26'),
                    ),
                ),

                'show_pro_data' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('获取分类下所有数据'),
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type59'),
                    ),
                ),
                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                    'depends' => array(
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                    ),
                ),

                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'sortdesc' => JText::_('排序id倒序'),
                        'sortasc' => JText::_('排序id正序'),
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'sortdesc',
                    'depends' => array(
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                    ),
                ),

                // 产品列表67 （国气联首页下载列表）
                'download_type67' => array(
                    'title' => '是否启用下载',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'padding_type67' => array(
                    'title' => '列表项内边距',
                    'type' => 'padding',
                    'std' => '24px 9px 24px 9px',
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'border_color_type67' => array(
                    'title' => '列表项下边框颜色',
                    'type' => 'color',
                    'std' => '#dedede',
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'img_type67' => array(
                    'title' => '列表项图标',
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20230524/1dc0e1bfbdab4cfeb9b9cbe825567776.png',
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'img_width_type67' => array(
                    'title' => '列表项图标宽度',
                    'type' => 'slider',
                    'max' => 200,
                    'std' => 40,
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'img_right_type67' => array(
                    'title' => '列表项图标右边距',
                    'type' => 'slider',
                    'max' => 100,
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'font_size_type67' => array(
                    'title' => '标题字号',
                    'type' => 'slider',
                    'max' => 50,
                    'std' => 16,
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),
                'font_color_type67' => array(
                    'title' => '标题字体颜色',
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type67'),
                    )
                ),

                // 布局68 国气联视频封面列表
                'settings_type68' => array(
                    'title' => '布局68配置项',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'total',
                    'values' => array(
                        array(
                            'label' => '整体',
                            'value' => 'total',
                        ),
                        array(
                            'label' => '封面',
                            'value' => 'video',
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                    )
                ),
                'download_type68' => array(
                    'title' => '是否开启下载',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'default_poster_type68' => array(
                    'title' => '封面占位图',
                    'type' => 'media',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/default-jzt.png',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'columns_type68' => array(
                    'title' => '展示列数',
                    'type' => 'slider',
                    'max' => 5,
                    'responsive' => true,
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'item_margin_right_type68' => array(
                    'title' => '列表项右边距',
                    'responsive' => true,
                    'type' => 'slider',
                    'max' => 100,
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'item_margin_bottom_type68' => array(
                    'title' => '列表项下边距',
                    'responsive' => true,
                    'type' => 'slider',
                    'max' => 100,
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'item_padding_type68' => array(
                    'title' => '列表项内边距',
                    'type' => 'padding',
                    'std' => '8px 0 8px 0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'item_border_color_type68' => array(
                    'title' => '列表项下边框色',
                    'type' => 'color',
                    'std' => '#dedede',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'init_add_active_type68' => array(
                    'title' => '是否有默认选中',
                    'type' => 'checkbox',
                    'std' => '1',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'init_all_active_type68' => array(
                    'title' => '是否默认展示封面',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'need_group_type68' => array(
                    'title' => '是否开启分组',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                    )
                ),
                'group_num_type68' => array(
                    'title' => '几个一组',
                    'type' => 'slider',
                    'std' => '5',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'line_color_type68' => array(
                    'title' => '分割线颜色',
                    'type' => 'color',
                    'std' => '#E1E9EC',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'line_width_type68' => array(
                    'title' => '分割线宽度（%）',
                    'type' => 'slider',
                    'std' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'line_height_type68' => array(
                    'title' => '分割线高度（px）',
                    'type' => 'slider',
                    'std' => '1',
                    'max' => '20',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'line_style_type68' => array(
                    'title' => '分割线样式',
                    'type' => 'selected',
                    'std' => 'dashed',
                    'value' => array(
                        'dashed' => '虚线',
                        'solid' => '实线',
                        'dotted' => '点'
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'line_margin_type68' => array(
                    'title' => '分割线外边距',
                    'type' => 'margin',
                    'std' => '15px auto 15px auto',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'total'),
                        array('need_group_type68', '=', '1'),
                    )
                ),
                'video_need_poster_type68' => array(
                    'title' => '是否使用封面',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                    )
                ),
                'video_poster_type68' => array(
                    'title' => '是否使用视频作为封面（视频链接在客户端的产品管理->特殊插件设置->可跳转的外部链接处填写）',
                    'desc' => '视频链接在客户端的产品管理->特殊插件设置->可跳转的外部链接处填写',
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'video'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_settings_type68' => array(
                    'title' => '视频配置项',
                    'type' => 'buttons',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'selected'
                        ),
                    ),
                    'std' => 'normal',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'video'),
                        array('video_need_poster_type68', '=', '1'),
                        array('video_poster_type68', '=', '1'),
                    )
                ),
                'video_padding_type68' => array(
                    'title' => '视频盒子内边距',
                    'type' => 'padding',
                    'std' => '30px 33px 26px 33px',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_height_type68' => array(
                    'title' => '封面和视频高度',
                    'type' => 'slider',
                    'responsive' => true,
                    'std' => '392',
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_object_fill_type68' => array(
                    'title' => '封面和视频展示方式',
                    'type' => 'select',
                    'std' => 'scale-down',
                    'values' => array(
                        'scale-down' => '留白保留宽高比',
                        'cover' => '超出剪裁',
                        'fill' => '填充拉伸',
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_play_icon_type68' => array(
                    'title' => '播放按钮图标',
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20230526/5489bbe86d0db41bc5a7a21071233bc9.png',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_play_icon_width_type68' => array(
                    'title' => '视频播放按钮宽度',
                    'type' => 'slider',
                    'std' => '89',
                    'max' => '500',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_radius_type68' => array(
                    'title' => '视频圆角',
                    'type' => 'margin',
                    'std' => '0 0 0 0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_bg_type68' => array(
                    'title' => '视频背景色',
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'normal'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_bg_hover_type68' => array(
                    'title' => '视频背景色',
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'hover'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'video_bg_selected_type68' => array(
                    'title' => '视频背景色',
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('video_poster_type68', '=', 1),
                        array('settings_type68', '=', 'video'),
                        array('video_settings_type68', '=', 'selected'),
                        array('video_need_poster_type68', '=', '1'),
                    )
                ),
                'title_setting_type68' => array(
                    'title' => '标题设置项',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'selected'
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                    )
                ),
                'title_setting_range_type68' => array(
                    'type' => 'separator',
                    'title' => '布局68标题配置',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('title_setting_type68', '=', 'title'),
                    ),
                ),
                'title_padding_type68' => array(
                    'title' => '标题内边距',
                    'type' => 'padding',
                    'std' => '16px 26px 16px 48px',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_color_type68' => array(
                    'title' => '标题颜色',
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_bg_type68' => array(
                    'title' => '标题背景颜色',
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_radius_type68' => array(
                    'title' => '标题圆角',
                    'type' => 'margin',
                    'std' => '2px 2px 2px 2px',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_size_type68' => array(
                    'title' => '标题字号',
                    'type' => 'slider',
                    'max' => '50',
                    'std' => '16',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_dot_show_type68' => array(
                    'title' => '是否开启标题前圆点',
                    'type' => 'checkbox',
                    'std' => '1',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'title_dot_size_type68' => array(
                    'title' => '标题前圆点大小',
                    'type' => 'slider',
                    'max' => '30',
                    'std' => '10',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'title_dot_top_type68' => array(
                    'title' => '标题圆点纵向偏移（%）',
                    'type' => 'slider',
                    'max' => '100',
                    'std' => '42',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'title_dot_left_type68' => array(
                    'title' => '标题圆点水平偏移（px）',
                    'type' => 'slider',
                    'max' => '300',
                    'std' => '26',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'title_dot_color_type68' => array(
                    'title' => '标题圆点颜色',
                    'type' => 'color',
                    'std' => '#d4060b',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'title_color_hover_type68' => array(
                    'title' => '标题颜色',
                    'type' => 'color',
                    'std' => '#d4060b',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'hover'),
                    )
                ),
                'title_bg_hover_type68' => array(
                    'title' => '标题背景颜色',
                    'type' => 'color',
                    'std' => '#f7f8fa',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'hover'),
                    )
                ),
                'title_bg_selected_type68' => array(
                    'title' => '标题背景颜色',
                    'type' => 'color',
                    'std' => '#f7f8fa',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'selected'),
                    )
                ),
                'title_dot_color_hover_type68' => array(
                    'title' => '标题圆点颜色',
                    'type' => 'color',
                    'std' => '#d4060b',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'hover'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'title_color_selected_type68' => array(
                    'title' => '标题颜色',
                    'type' => 'color',
                    'std' => '#d4060b',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'selected'),
                    )
                ),
                'title_bg_selected_type68' => array(
                    'title' => '标题背景颜色',
                    'type' => 'color',
                    'std' => '#f7f8fa',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'selected'),
                    )
                ),
                'title_dot_color_selected_type68' => array(
                    'title' => '标题圆点颜色',
                    'type' => 'color',
                    'std' => '#d4060b',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'selected'),
                        array('title_dot_show_type68', '=', '1'),
                    )
                ),
                'date_setting_range_type68' => array(
                    'type' => 'separator',
                    'title' => '布局68日期配置',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('title_setting_type68', '=', 'title'),
                    ),
                ),
                'date_show_type68' => array(
                    'title' => '是否展示日期',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                    )
                ),
                'date_color_type68' => array(
                    'title' => '日期颜色',
                    'type' => 'color',
                    'std' => '#a5a6a7',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('date_show_type68', '=', '1'),
                    )
                ),
                'date_color_hover_type68' => array(
                    'title' => '日期颜色',
                    'type' => 'color',
                    'std' => '#a5a6a7',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'hover'),
                        array('date_show_type68', '=', '1'),
                    )
                ),
                'date_color_selected_type68' => array(
                    'title' => '日期颜色',
                    'type' => 'color',
                    'std' => '#a5a6a7',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'selected'),
                        array('date_show_type68', '=', '1'),
                    )
                ),
                'icon_range_type68' => array(
                    'type' => 'separator',
                    'title' => '布局68下载图标配置',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    ),
                ),
                'icon_type68' => array(
                    'type' => 'media',
                    'title' => '布局68下载图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20230531/cf8b8b748059796b013c70031f1f71d1.png',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    ),
                ),
                'icon_color_type68' => array(
                    'title' => '文件类型标签颜色',
                    'type' => 'color',
                    'std' => '#0064bf',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_border_color_type68' => array(
                    'title' => '文件类型标签边框颜色',
                    'type' => 'color',
                    'std' => '#0064bf',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_size_type68' => array(
                    'title' => '文件类型标签字号',
                    'type' => 'slider',
                    'std' => '16',
                    'max' => '50',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_radius_type68' => array(
                    'title' => '文件类型标签圆角',
                    'type' => 'slider',
                    'std' => '2',
                    'max' => '10',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_padding_type68' => array(
                    'title' => '文件类型标签内边距',
                    'type' => 'padding',
                    'std' => '0 2px 0 2px',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_left_type68' => array(
                    'title' => '文件类型标签左边距',
                    'type' => 'slider',
                    'std' => '10',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('date_show_type68', '=', '1'),
                    )
                ),
                'icon_img_width_type68' => array(
                    'title' => '文件类型标签图标大小',
                    'type' => 'slider',
                    'std' => '18',
                    'max' => '100',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'icon_img_left_type68' => array(
                    'title' => '文件类型标签图标左边距',
                    'type' => 'slider',
                    'std' => '3',
                    'max' => '50',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                        array('settings_type68', '=', 'title'),
                        array('title_setting_type68', '=', 'normal'),
                        array('download_type68', '=', '1'),
                    )
                ),
                'over_type68' => array(
                    'type' => 'separator',
                    'title' => '布局68结束',
                    'depends' => array(
                        array('pro_type', '=', 'type68'),
                    ),
                ),



                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('每页显示产品数量'),
                    'std' => '10',
                    'depends' => array(
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type36'),
                    ),
                ),

                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('PC及平板列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                    'depends' => array(
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type19'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type4'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'columns_xs' => array(
                    'type' => 'number',
                    'title' => JText::_('手机列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                    'depends' => array(
                        array('pro_type', '!=', 'type16'),
                        array('pro_type', '!=', 'type13'),
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type19'),
                        array('pro_type', '!=', 'type20'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type27'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type31'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type46'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type52'),
                        array('pro_type', '!=', 'type53'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type55'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type63'),
                        array('pro_type', '!=', 'type64'),
                        array('pro_type', '!=', 'type65'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),

                // 49列数
                'column_type49' => array(
                    'title' => '列数设置',
                    'type' => 'slider',
                    'max' => 10,
                    'std' => array(
                        'md' => 2,
                        'sm' => 2,
                        'xs' => 2
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type49'),
                    )
                ),
                'right_type49' => array(
                    'title' => '列表项左右间距',
                    'type' => 'slider',
                    'max' => 300,
                    'std' => array(
                        'md' => 0,
                        'sm' => 0,
                        'xs' => 0
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type49'),
                    )
                ),
                'bottom_type49' => array(
                    'title' => '列表项上下间距',
                    'type' => 'slider',
                    'max' => 300,
                    'std' => array(
                        'md' => 0,
                        'sm' => 0,
                        'xs' => 0
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type49'),
                    )
                ),

                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('是否显示翻页码'),
                    'std' => '0',
                    'depends' => array(
                        array('pro_type', '!=', 'type17'),
                        array('pro_type', '!=', 'type23'),
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type25'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type28'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type33'),
                        array('pro_type', '!=', 'type36'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type62'),
                        array('pro_type', '!=', 'type66'),
                        array('pro_type', '!=', 'type69'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'page_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页左右位置调整'),
                    'std' => 0,
                    'max' => 300,
                    'min' => -300,
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'page_top_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页上下位置调整'),
                    'std' => 0,
                    'max' => 300,
                    'min' => -300,
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'page_style_selector' => array(
                    'type' => 'select',
                    'title' => '翻页样式',
                    'values' => array(
                        'page01' => '翻页样式一',
                        'page02' => '翻页样式二',
                        'page03' => '翻页样式三',
                    ),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => 'page01',
                ),
                'page_margin_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页左右间距'),
                    'std' => 0,
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                        array('page_style_selector', '=', 'page02'),

                    ),
                ),
                'page_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页上下间距'),
                    'std' => 0,
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                'page1_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                    ),
                    'std' => '#1e1e1e',
                ),
                'page1_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#fff',
                ),
                'page1_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#2a68a7',
                ),
                'page1_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('show_page', '=', true),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type45'),
                        array('pro_type', '!=', 'type48'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type59'),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                    'std' => '#2a68a7',
                ),

                'link_catid' => array(
                    'type' => 'category',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'depends' => array(
                        array('resource', '=', 'article'),
                        array('link_articles', '=', '1'),
                    ),
                ),

                'link_k2catid' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID_DESC'),
                    'depends' => array(
                        array('resource', '=', 'k2'),
                        array('link_articles', '=', '1'),
                    ),
                    'values' => JwPageFactoryBase::k2CatList(),
                ),

                'all_articles_btn_text' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ALL_ARTICLES_BUTTON_TEXT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ALL_ARTICLES_BUTTON_TEXT_DESC'),
                    'std' => 'See all posts',
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_FAMILY'),
                    'depends' => array('link_articles' => '1'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-btn { font-family: "{{ VALUE }}"; }',
                    ),
                ),

                'all_articles_btn_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px',
                    ),
                    'std' => '0',
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
                    'values' => array(
                        'default' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
                        'primary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
                        'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
                        'success' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
                        'info' => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
                        'warning' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
                        'danger' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
                        'dark' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
                        'link' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
                    ),
                    'std' => 'default',
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_appearance' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
                        'outline' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
                        '3d' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
                    ),
                    'std' => 'flat',
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_background_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_DESC'),
                    'std' => '#444444',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom'),
                    ),
                ),

                'all_articles_btn_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'std' => '#fff',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom'),
                    ),
                ),

                'all_articles_btn_background_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_COLOR_HOVER_DESC'),
                    'std' => '#222',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom'),
                    ),
                ),

                'all_articles_btn_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_HOVER_DESC'),
                    'std' => '#fff',
                    'depends' => array(
                        array('link_articles', '=', '1'),
                        array('all_articles_btn_type', '=', 'custom'),
                    ),
                ),

                'all_articles_btn_size' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
                        'lg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
                        'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
                        'sm' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
                        'xs' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
                    ),
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_icon' => array(
                    'type' => 'icon',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_icon_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
                    'values' => array(
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'depends' => array('link_articles' => '1'),
                ),

                'all_articles_btn_block' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
                    'values' => array(
                        '' => JText::_('JNO'),
                        'jwpf-btn-block' => JText::_('JYES'),
                    ),
                    'depends' => array('link_articles' => '1'),
                ),

                //布局25
                'def_img' => array(
                    'type' => 'media',
                    'title' => JText::_('选择默认图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/3f33ff0e8ab211eb91b2fa163ed39cbd/image/20210618/fac4dc78933aef8e6c84ce0eef9d1f1a.jpeg',
                    'depends' => array('pro_type' => 'type25'),
                ),
                'type25_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type25'),
                    'std' => '#fec456',
                ),
                'type25_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('列表字体颜色'),
                    'depends' => array('pro_type' => 'type25'),
                    'std' => '#fec456',
                ),
                'type25_title_underscore_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题下划线颜色'),
                    'depends' => array('pro_type' => 'type25'),
                    'std' => '#e5ec3c',
                ),
                'type25_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type25'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 14,
                ),
                'type25_text_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表字体大小'),
                    'depends' => array('pro_type' => 'type25'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 14,
                ),
                'location' => array(
                    'type' => 'select',
                    'title' => '二级列表位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                        //    'page03' => '翻页样式三',
                    ),
                    'depends' => array('pro_type' => 'type25'),
                    'std' => 'center',
                ),
                'type25_list_quantity' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级列表数量'),
                    'depends' => array('pro_type' => 'type25'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 8,
                ),
                //布局26
                'type26_title' => array(
                    'type' => 'text',
                    'title' => JText::_('标题'),
                    'std' => 'Product',
                    'depends' => array('pro_type' => 'type26'),
                ),
                'vice_title' => array(
                    'type' => 'text',
                    'title' => JText::_('简介'),
                    'std' => 'Product',
                    'depends' => array('pro_type' => 'type26'),
                ),
                'type26_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type26'),
                    'std' => '#e5ec3c',
                ),
                'type26_vice_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array('pro_type' => 'type26'),
                    'std' => '#e5ec3c',
                ),
                'type26_title_underscore_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题下划线颜色'),
                    'depends' => array('pro_type' => 'type26'),
                    'std' => '#e5ec3c',
                ),
                'type26_list_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('列表字体颜色'),
                    'depends' => array('pro_type' => 'type26'),
                    'std' => '#e5ec3c',
                ),
                'type26_left_icon' => array(
                    'type' => 'media',
                    'title' => '标题左侧图标',
                    'std' => 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/images/icon-13.jpg',
                    'depends' => array('pro_type' => 'type26'),
                ),
                'type26_goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                    'depends' => array('pro_type' => 'type26'),
                ),
                'type26_show_pro_data' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('获取分类下所有数据'),
                    'std' => '0',
                    'depends' => array('pro_type' => 'type26'),
                ),
                'type26_list_quantity' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表数量'),
                    'depends' => array('pro_type' => 'type26'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 10,
                ),
                'img_style' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'img_type1' => JText::_('自适应显示'),
                        'img_type2' => JText::_('占满不切割显示'),
                        'img_type3' => JText::_('占满切割显示'),
                    ),
                    'std' => 'img_type1',
                    'depends' => array('pro_type' => 'type26'),
                ),
                'img_style_type7' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('pro_type', '!=', 'type24'),
                        array('pro_type', '!=', 'type26'),
                        array('pro_type', '!=', 'type29'),
                        array('pro_type', '!=', 'type32'),
                        array('pro_type', '!=', 'type37'),
                        array('pro_type', '!=', 'type38'),
                        array('pro_type', '!=', 'type39'),
                        array('pro_type', '!=', 'type40'),
                        array('pro_type', '!=', 'type43'),
                        array('pro_type', '!=', 'type44'),
                        array('pro_type', '!=', 'type49'),
                        array('pro_type', '!=', 'type50'),
                        array('pro_type', '!=', 'type51'),
                        array('pro_type', '!=', 'type54'),
                        array('pro_type', '!=', 'type56'),
                        array('pro_type', '!=', 'type57'),
                        array('pro_type', '!=', 'type58'),
                        array('pro_type', '!=', 'type59'),
                        array('pro_type', '!=', 'type60'),
                        array('pro_type', '!=', 'type61'),
                        array('pro_type', '!=', 'type67'),
                        array("pro_type", "!=", "type68"),
                        array("pro_type", "!=", "type69"),
                        array("pro_type", "!=", "type70"),
                        array("pro_type", "!=", "type71"),                        array("pro_type", "!=", "type71"),
                        array("pro_type", "!=", "type72"),
                    ),
                ),
                //布局28
                'type28bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('背景颜色'),
                    'depends' => array('pro_type' => 'type28'),
                    'std' => '#b7b7b7',
                ),
                'type28text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'depends' => array('pro_type' => 'type28'),
                    'std' => '#fff',
                ),
                'type26_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'depends' => array('pro_type' => 'type28'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 16,
                ),
                'text_location' => array(
                    'type' => 'select',
                    'title' => '文本位置',
                    'values' => array(
                        'start' => '左',
                        'center' => '中',
                        'end' => '右',
                    ),
                    'depends' => array('pro_type' => 'type28'),
                    'std' => 'center',
                ),
                'type26_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('宽度设置'),
                    'depends' => array('pro_type' => 'type28'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 17,
                ),
                'type26_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('高度设置'),
                    'depends' => array('pro_type' => 'type28'),
                    'max' => 500,
                    'min' => 0,
                    'std' => 200,
                ),
                'type26_speed' => array(
                    'type' => 'slider',
                    'title' => JText::_('上下移动速度设置(数值越小速度越快)'),
                    'depends' => array('pro_type' => 'type28'),
                    'max' => 500,
                    'min' => 0,
                    'std' => 50,
                ),

                //                布局43
                'pro43_li_model' => array(
                    'type' => 'buttons',
                    'title' => JText::_('列表项状态'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'select',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                    ),
                ),
                /*'pro43_li_width' => array(
                'type' => 'slider',
                'title' => JText::_('列表项宽度'),
                'max' => 500,
                'std' => 160,
                'depends' => array(
                array('pro_type', '=', 'type43'),
                array('pro43_li_model', '=', 'normal'),
                ),
                ),*/
                'pro43_li_nums' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项一行展示个数'),
                    'max' => 10,
                    'std' => array('md' => 6, 'sm' => 3, 'xs' => 2),
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'normal'),
                    ),
                    'responsive' => true,
                ),
                'pro43_li_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项高度'),
                    'max' => 500,
                    'std' => 160,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'normal'),
                    ),
                ),
                'pro43_li_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项下边距'),
                    'max' => 200,
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'normal'),
                    ),
                ),
                'pro43_li_margin_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项右边距'),
                    'max' => 200,
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'normal'),
                    ),
                ),
                'pro43_img_style' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'normal'),
                    ),
                ),
                'pro43_mask_color' => array(
                    'type' => 'color',
                    'title' => JText::_('未被选中的列表项遮罩颜色'),
                    'std' => 'rgba(255,255,255,.4)',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('详情所占列表项个数'),
                    'std' => array('md' => 3, 'sm' => 2, 'xs' => 1),
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                    'responsive' => true,
                ),
                'pro43_details_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('详情背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_box_shadow' => array(
                    'type' => 'checkbox',
                    'title' => '盒阴影',
                    'std' => 1,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_box_shadow_x' => array(
                    'type' => 'slider',
                    'title' => '盒阴影横向偏移量',
                    'std' => 0,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                        array('pro43_details_box_shadow', '=', 1),
                    ),
                ),
                'pro43_details_box_shadow_y' => array(
                    'type' => 'slider',
                    'title' => '盒阴影纵向偏移量',
                    'std' => 0,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                        array('pro43_details_box_shadow', '=', 1),
                    ),
                ),
                'pro43_details_box_shadow_blur' => array(
                    'type' => 'slider',
                    'title' => '盒阴影模糊距离',
                    'std' => 10,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                        array('pro43_details_box_shadow', '=', 1),
                    ),
                ),
                'pro43_details_box_shadow_color' => array(
                    'type' => 'color',
                    'title' => '盒阴影模糊距离',
                    'std' => 'rgba(100,100,100,0.5)',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                        array('pro43_details_box_shadow', '=', 1),
                    ),
                ),
                'pro43_details_padding' => array(
                    'type' => 'padding',
                    'title' => '详情内边距',
                    'std' => '25px 20px 25px 20px',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_title_font_size' => array(
                    'type' => 'slider',
                    'title' => '标题字号',
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_title_color' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_sub_title_font_size' => array(
                    'type' => 'slider',
                    'title' => '简介字号',
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_sub_title_color' => array(
                    'type' => 'color',
                    'title' => '简介颜色',
                    'std' => '#313131',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_sub_title_margin_top' => array(
                    'type' => 'slider',
                    'title' => '简介上边距',
                    'std' => 30,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_text_top' => array(
                    'type' => 'slider',
                    'title' => '详情内容上边距',
                    'std' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_text_font_size' => array(
                    'type' => 'slider',
                    'title' => '内容字号',
                    'std' => 16,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_text_color' => array(
                    'type' => 'color',
                    'title' => '内容颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                'pro43_details_text_line_height' => array(
                    'type' => 'slider',
                    'title' => '内容行高',
                    'std' => 24,
                    'depends' => array(
                        array('pro_type', '=', 'type43'),
                        array('pro43_li_model', '=', 'select'),
                    ),
                ),
                //                布局44
                'pro44_li_model' => array(
                    'type' => 'buttons',
                    'title' => JText::_('列表项状态'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                    ),
                ),
                'pro44_li_column_num' => array(
                    'type' => 'slider',
                    'title' => JText::_('一行有几个列表项'),
                    'max' => 10,
                    'std' => array('md' => 3, 'sm' => 2, 'xs' => 1),
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                    ),
                    'responsive' => true,
                ),
                'pro44_li_margin_right' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项右边距'),
                    'max' => 500,
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                    ),
                ),
                'pro44_li_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('列表项下边距'),
                    'max' => 500,
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                    ),
                ),
                'pro44_li_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('列表项背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                    ),
                ),
                'pro44_li_bg_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入列表项背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                    ),
                ),
                'pro44_li_display_item' => array(
                    'type' => 'buttons',
                    'title' => JText::_('列表展示项'),
                    'std' => 'img',
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'img',
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content',
                        ),
                        array(
                            'label' => '箭头',
                            'value' => 'arrow',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                    ),
                ),
                'pro44_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'std' => 210,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'img'),
                    ),
                ),
                'pro44_img_style' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'img'),
                    ),
                ),
                'pro44_img_hover' => array(
                    'type' => 'text',
                    'title' => JText::_('鼠标移入图片放大倍数'),
                    'std' => '1.1',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'img'),
                    ),
                ),
                'pro44_content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容内边距'),
                    'std' => '15px 60px 15px 20px',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容下边框颜色'),
                    'std' => 'rgba(0, 0, 0, 0.1)',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_content_border_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入内容下边框颜色'),
                    'std' => 'rgba(0, 0, 0, 0.4)',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字号'),
                    'std' => 15,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_title_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入标题颜色'),
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_title_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => 24,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_intro_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介上边距'),
                    'std' => 0,
                    'max' => 500,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_intro_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字号'),
                    'std' => 15,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_intro_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'std' => '#b7b7b7',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_intro_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标移入简介字体颜色'),
                    'std' => '#b7b7b7',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_intro_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介行高'),
                    'std' => 24,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'content'),
                    ),
                ),
                'pro44_arrow_left_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头左边框颜色'),
                    'std' => 'transparent',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'arrow'),
                    ),
                ),
                'pro44_arrow_left_border_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头左边框颜色'),
                    'std' => 'rgba(0, 0, 0, 0.1)',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'arrow'),
                    ),
                ),
                'pro44_arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#b7b7b7',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'normal'),
                        array('pro44_li_display_item', '=', 'arrow'),
                    ),
                ),
                'pro44_arrow_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#b7b7b7',
                    'depends' => array(
                        array('pro_type', '=', 'type44'),
                        array('pro44_li_model', '=', 'hover'),
                        array('pro44_li_display_item', '=', 'arrow'),
                    ),
                ),

                //                产品56
                'pro56_tips' => array(
                    'type' => 'separator',
                    'title' => '请开启区块全屏',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                    ),
                ),
                'pro56_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }',
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                    ),
                ),
                'pro56_width' => array(
                    'type' => 'slider',
                    'title' => '插件宽度(单位:%,移动端无效)',
                    'std' => array('md' => 70, 'sm' => 100, 'xs' => 100),
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                    ),
                    'responsive' => true,
                ),
                'pro56_settings' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '轮播图',
                            'value' => 'img',
                        ),
                        array(
                            'label' => '文字内容',
                            'value' => 'text',
                        ),
                        array(
                            'label' => '箭头',
                            'value' => 'arrow',
                        ),
                    ),
                    'std' => 'img',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                    ),
                ),
                'pro56_nums' => array(
                    'type' => 'slider',
                    'title' => '一行展示几个轮播项(移动端无效)',
                    'std' => array('md' => 5, 'sm' => 3, 'xs' => 1),
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                    'responsive' => true,
                ),
                'pro56_select_item' => array(
                    'type' => 'slider',
                    'title' => '默认选中的列表项(移动端无效)',
                    'std' => 1,
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                ),
                'pro56_line_color' => array(
                    'type' => 'color',
                    'title' => '长线条和名称下圆点颜色',
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                ),
                'pro56_img_height' => array(
                    'type' => 'slider',
                    'title' => '图片高度',
                    'std' => array('md' => '316', 'sm' => '216', 'xs' => ''),
                    'max' => 1000,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                    'responsive' => true,
                ),
                'pro56_img_style' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                ),
                'pro56_name_font_size' => array(
                    'type' => 'slider',
                    'title' => '未选中轮播项姓名字号(移动端无效)',
                    'std' => 15,
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                ),
                'pro56_name_color' => array(
                    'type' => 'color',
                    'title' => '未选中轮播项姓名字体颜色',
                    'std' => '#8c8c8c',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'img'),
                    ),
                ),
                'pro56_info_top' => array(
                    'type' => 'slider',
                    'title' => '文字内容上边距',
                    'std' => array('md' => 22, 'sm' => 22, 'xs' => 30),
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_settings' => array(
                    'type' => 'buttons',
                    'title' => '文字内容设置',
                    'values' => array(
                        array(
                            'label' => '姓名',
                            'value' => 'name',
                        ),
                        array(
                            'label' => '职位',
                            'value' => 'position',
                        ),
                        array(
                            'label' => '介绍',
                            'value' => 'desc',
                        ),
                    ),
                    'std' => 'name',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                    ),
                ),
                'pro56_info_title_size' => array(
                    'type' => 'slider',
                    'title' => '姓名字号',
                    'std' => array('md' => 26, 'sm' => 26, 'xs' => 22),
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'name'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_title_color' => array(
                    'type' => 'color',
                    'title' => '姓名字体颜色',
                    'std' => '#404040',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'name'),
                    ),
                ),
                'pro56_info_title_hover_color' => array(
                    'type' => 'color',
                    'title' => '鼠标移入姓名字体颜色',
                    'std' => '#e6ab43',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'name'),
                    ),
                ),
                'pro56_info_position_size' => array(
                    'type' => 'slider',
                    'title' => '职位字号(移动端无效)',
                    'std' => array('md' => 18, 'sm' => 18, 'xs' => 14),
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'position'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_position_line_height' => array(
                    'type' => 'slider',
                    'title' => '职位字号(移动端无效)',
                    'std' => array('md' => 18, 'sm' => 18, 'xs' => 14),
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'position'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_position_color' => array(
                    'type' => 'color',
                    'title' => '职位字体颜色',
                    'std' => '#737373',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'position'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_desc_top' => array(
                    'type' => 'slider',
                    'title' => '移动端介绍上边距',
                    'std' => array('md' => 16, 'sm' => 16, 'xs' => 24),
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_desc_size' => array(
                    'type' => 'slider',
                    'title' => '介绍字号',
                    'std' => array('md' => 16, 'sm' => 16, 'xs' => 14),
                    'max' => 50,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_desc_color' => array(
                    'type' => 'color',
                    'title' => '介绍字体颜色',
                    'std' => '#737373',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                ),
                'pro56_info_desc_mobile_color' => array(
                    'type' => 'color',
                    'title' => '移动端介绍字体颜色',
                    'std' => 'grey',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                ),
                'pro56_info_desc_height' => array(
                    'type' => 'slider',
                    'title' => '介绍高度',
                    'std' => array('md' => 64, 'sm' => 64, 'xs' => 67),
                    'max' => 200,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_hide' => array(
                    'type' => 'slider',
                    'title' => '移动端介绍显示行数',
                    'std' => array('md' => 2, 'sm' => 2, 'xs' => 3),
                    'max' => 10,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_line_height' => array(
                    'type' => 'slider',
                    'title' => '移动端介绍行高',
                    'std' => array('md' => 32, 'sm' => 32, 'xs' => 24),
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                    'responsive' => true,
                ),
                'pro56_info_arrow' => array(
                    'type' => 'media',
                    'title' => '箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/right-bottom-pro56.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                ),
                'pro56_info_arrow_hover' => array(
                    'type' => 'media',
                    'title' => '鼠标移入箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/right-bottom-pro56-1.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'text'),
                        array('pro56_info_settings', '=', 'desc'),
                    ),
                ),
                'pro56_arrow_settings' => array(
                    'type' => 'buttons',
                    'title' => '箭头设置',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_arrow_left' => array(
                    'type' => 'media',
                    'title' => '左箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/left-arrow-pro56.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                        array('pro56_arrow_settings', '=', 'normal'),
                    ),
                ),
                'pro56_arrow_right' => array(
                    'type' => 'media',
                    'title' => '右箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/right-arrow-pro56.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                        array('pro56_arrow_settings', '=', 'normal'),
                    ),
                ),
                'pro56_arrow_left_hover' => array(
                    'type' => 'media',
                    'title' => '鼠标移入左箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/left-arrow-pro56-1.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                        array('pro56_arrow_settings', '=', 'hover'),
                    ),
                ),
                'pro56_arrow_right_hover' => array(
                    'type' => 'media',
                    'title' => '鼠标移入右箭头',
                    'std' => '/components/com_jwpagefactory/addons/product_list/assets/images/right-arrow-pro56-1.png',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                        array('pro56_arrow_settings', '=', 'hover'),
                    ),
                ),
                'pro56_arrow_size' => array(
                    'type' => 'slider',
                    'title' => '箭头按钮大小(px)',
                    'std' => 48,
                    'max' => 200,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_arrow_img_size' => array(
                    'type' => 'slider',
                    'title' => '箭头图片大小(%)',
                    'std' => 50,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_arrow_margin' => array(
                    'type' => 'slider',
                    'title' => '左(右)箭头离左(右)容器的距离(%)',
                    'std' => 6,
                    'max' => 100,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_page_dot_size' => array(
                    'title' => '移动端分页大小',
                    'type' => 'slider',
                    'std' => 6,
                    'max' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_page_dot_bg' => array(
                    'title' => '分页背景色',
                    'type' => 'color',
                    'std' => '#d9d9d9',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'pro56_page_dot_border' => array(
                    'title' => '选中分页边框色',
                    'type' => 'color',
                    'std' => '#e6ab43',
                    'depends' => array(
                        array('pro_type', '=', 'type56'),
                        array('pro56_settings', '=', 'arrow'),
                    ),
                ),
                'class_type_options' => array(
                    'type' => 'separator',
                    'title' => '插件设置',
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                ),
                'pro59_title_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#333',
                ),
                'pro59_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'min' => 10,
                    'max' => 40,
                    'std' => 17,
                ),

                'pro59_content_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#999',
                ),
                'pro59_content_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'min' => 10,
                    'max' => 40,
                    'std' => 13,
                ),
                'pro59_more_url' => array(
                    'type' => 'text',
                    'title' => JText::_('MORE更多跳转链接'),
                    'std' => '#',
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                ),
                'pro59_more_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('MORE更多字体颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#616161',
                ),
                'pro59_more_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('MORE更多字体大小'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'min' => 10,
                    'max' => 40,
                    'std' => 16,
                ),
                'pro59_more_hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('MORE更多字体鼠标移入颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#000',
                ),
                'pro59_more_arrow_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#616161',
                ),
                'pro59_more_arrow_hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头字体鼠标移入颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type59'),
                    ),
                    'std' => '#000',
                ),
            ),
        ),
    )
);
