<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input      = $app->input;
$layout_id  = $input->get('layout_id', '');
$site_id    = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(

    array(
        'type'       => 'general', //插件
        'addon_name' => 'JZT_ADDON_LSGA_4D2L2F02N8_productInfo',
        'title'      => '产品详情',
        'desc'       => '',
        'category'   => '产品', //插件分组
        'attr'       => array(
            //配置项主体
            'general' => array(
                //【基本】选项卡
                'art_type_selector_proinfo' => array(
                    'type'   => 'select',
                    'title'  => '选择详情页布局',
                    'values' => array(
                        'type1' => '布局1',
                        'type2' => '布局2',
                        'type3' => '布局3',
                        'type4' => '布局4',
                        'type5' => '布局5',
                        'type6' => '布局6',
                        'type7' => '布局7',
                        'type8' => '布局8',
                        'type9' => '布局9（带图片）',
                    ),
                    'std'    => 'type1',
                ),
                // 动态生成的配置项
                'tdk_button'           => array(
                    'type'        => 'select',
                    'desc'        => JText::_(''),
                    'placeholder' => JText::_(''),
                    'title'       => '动态TDK设置',
                    'std'         => 'yes',
                    'values'      => array(
                        'yes' => '启用',
                        'no'  => '不启用',
                    ),
                ),
                // 'title_h2_button'           => array(
                //     'type'        => 'select',
                //     'desc'        => JText::_('产品详情的标题设置为网页标题'),
                //     'placeholder' => JText::_('产品详情的标题设置为网页标题'),
                //     'title'       => '动态标题设置',
                //     'std'         => 'yes',
                //     'values'      => array(
                //         'yes' => '启用',
                //         'no'  => '不启用',
                //     ),
                // ),
                // 'keywords_h2_button' => array(
	            //     'type' => 'select',
                //     'desc' => JText::_('文章详情的简介设置为网页关键字'),
                //     'placeholder' => '文章详情的简介设置为网页关键字',
	            //     'title' => '动态关键词设置',
	            //     'std' => 'yes',
                //     'values' => array(
                //         'yes' => '启用',
                //         'no' => '不启用',
                //     ),
                // ),
                // 'description_h2_button' => array(
	            //     'type' => 'select',
                //     'desc' => JText::_('文章详情的详情设置为网页描述'),
                //     'placeholder' => '文章详情的详情设置为网页描述',
	            //     'title' => '动态描述设置',
	            //     'std' => 'yes',
                //     'values' => array(
                //         'yes' => '启用',
                //         'no' => '不启用',
                //     ),
                // ),
                'biaoqian_type' => array(
	                'type' => 'select',
                    'desc' => JText::_('动态标签设置'),
	                'title' => '动态标签设置',
	                'std' => 'no',
                    'values' => array(
                        'yes' => '启用',
                        'no' => '不启用',
                    ),
                ),
                'biaoqian_peizhi' => array(
                    'title' => JText::_('动态标签内容设置'),
                    'std' => array(
                        array(
                            'title' => '首页',
                            'title_color' => '',
                        ),
                        array(
                            'title' => '产品中心',
                            'title_color' => '',
                        ),
                        array(
                            'title' => '文章详情',
                            'title_color' => '',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('选项的名称'),
                            'std' => '',
                        ),
                        'title_color' => array(
                            'type' => 'color',
                            'title' => '文字颜色',
                            'std' => '',
                        ),
                    ),
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_bg_color' => array(
                    'type' => 'color',
                    'title' => '标签背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签行高'),
                    'max' => 500,
                    'min' => 10,
                    'std' => 10,
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_size' => array(
	                'type' => 'slider',
                    'title' => JText::_('标签字体大小'),
                    'max' => 500,
                    'min' => 10,
                    'std' => 14,
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_type' => array(
	                'type' => 'select',
                    'desc' => JText::_('标签最后是否展示产品标题'),
	                'title' => '标签最后是否展示产品标题',
	                'std' => 'no',
                    'values' => array(
                        'yes' => '展示',
                        'no' => '不展示',
                    ),
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_color' => array(
	                'type' => 'color',
                    'desc' => JText::_('标签最后产品标题字体颜色'),
	                'title' => '标签最后产品标题字体颜色',
	                'std' => '',
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                        array('biaoqian_title_type', '=', 'yes'),
                    ),
                ),
                'ordering_select' => array(
	                'type' => 'select',
                    'desc' => JText::_('排序方式，请和文章列表排序方式一致'),
                    'placeholder' => '排序方式',
	                'title' => '排序方式',
	                'std' => '',
                    'values' => array(
                        'orderdesc' => '排序id倒序',
                        'orderasc' => '排序id正序',
                        'timeasc' => '添加时间正序',
                        'timedesc' => '添加时间倒序',
                    ),
                ),
                'color1615443385649'        => array(
                    'type'    => 'color',
                    'title'   => '正文颜色',
                    'desc'    => '',
                    'std'     => '#555555',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type1'),
                    ),
                ),
                'select1615452416171'       => array(
                    'type'    => 'select',
                    'title'   => '标题布局',
                    'desc'    => '',
                    'std'     => 'left',
                    'values'  => array(
                        'left'   => '居左',
                        'center' => '居中',
                        //type3' =>'样式3',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type1'),
                    ),
                ),
                'title_bold' => array(
	                'type' => 'checkbox',
                    'desc' => JText::_('标题加粗'),
                    'placeholder' => '标题加粗',
	                'title' => '标题加粗',
	                'std' => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type3'),
                    ),
                ),
                'color1615452944411'        => array(
                    'type'    => 'color',
                    'title'   => '标题颜色',
                    'desc'    => '',
                    'std'     => '#333333',
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type2'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type5'),
                        array('art_type_selector_proinfo', '!=', 'type6'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type8'),
                    ),
                ),
                'font_size'                 => array(
                    'type'    => 'slider',
                    'title'   => JText::_('标题字体大小'),
                    'max'     => 100,
                    'min'     => 12,
                    'std'     => '28',
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type2'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type5'),
                        array('art_type_selector_proinfo', '!=', 'type6'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type8'),
                    ),
                ),
                'font_size_date'            => array(
                    'type'    => 'slider',
                    'title'   => JText::_('编辑日期字体大小'),
                    'max'     => 100,
                    'min'     => 10,
                    'std'     => '14',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type1'),
                    ),
                ),
                'color1615453171634'        => array(
                    'type'    => 'color',
                    'title'   => '时间颜色',
                    'desc'    => '',
                    'std'     => '#888888',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type1'),
                    ),
                ),
                // 样式5
                'type5_title'               => array(
                    'type'    => 'separator',
                    'title'   => '标题部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_title_align'         => array(
                    'type'    => 'select',
                    'title'   => '标题布局',
                    'desc'    => '',
                    'std'     => 'center',
                    'values'  => array(
                        'left'   => '居左',
                        'center' => '居中',
                        'right'  => '居右',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_font_size'           => array(
                    'type'    => 'slider',
                    'title'   => JText::_('标题字体大小'),
                    'max'     => 100,
                    'min'     => 12,
                    'std'     => '28',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_title_color'         => array(
                    'type'    => 'color',
                    'title'   => '标题文字颜色',
                    'desc'    => '',
                    'std'     => '#202020',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_title_l'             => array(
                    'type'    => 'checkbox',
                    'title'   => '关闭标题两侧横线',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_title_l_w'           => array(
                    'type'    => 'slider',
                    'title'   => JText::_('标题两侧横线宽度'),
                    'max'     => 100,
                    'min'     => 10,
                    'std'     => '32',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_title_l', '!=', '1'),
                    ),
                ),
                'type5_title_l_h'           => array(
                    'type'    => 'slider',
                    'title'   => JText::_('标题两侧横线高度'),
                    'max'     => 100,
                    'min'     => 10,
                    'std'     => '2',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_title_l', '!=', '1'),
                    ),
                ),
                'type5_title_l_m'           => array(
                    'type'    => 'slider',
                    'title'   => JText::_('标题两侧横线与标题间距'),
                    'max'     => 100,
                    'min'     => 10,
                    'std'     => '10',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_title_l', '!=', '1'),
                    ),
                ),
                'type5_date'                => array(
                    'type'    => 'separator',
                    'title'   => '日期&页面导航部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_date_align'          => array(
                    'type'    => 'select',
                    'title'   => '日期布局',
                    'std'     => 'center',
                    'values'  => array(
                        'left'   => '居左',
                        'center' => '居中',
                        'right'  => '居右',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_font_size_date'      => array(
                    'type'    => 'slider',
                    'title'   => JText::_('日期&页面导航字体大小'),
                    'max'     => 100,
                    'min'     => 10,
                    'std'     => '14',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_date_color'          => array(
                    'type'    => 'color',
                    'title'   => '时间&页面导航颜色',
                    'desc'    => '',
                    'std'     => '#888888',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_home'                => array(
                    'type'    => 'checkbox',
                    'title'   => '关闭页面导航',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_home_align'          => array(
                    'type'    => 'select',
                    'title'   => '页面导航布局',
                    'std'     => 'left',
                    'values'  => array(
                        'left'  => '居左',
                        'right' => '居右',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                'type5_home_color'          => array(
                    'type'    => 'color',
                    'title'   => '页面导航标题颜色',
                    'desc'    => '',
                    'std'     => '#1237ce',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                'type5_home_icon'           => array(
                    'type'    => 'media',
                    'format'  => 'image',
                    'title'   => '页面导航图标',
                    'std'     => 'https://oss.lcweb01.cn/joomla/20220111/74b47c0034f7fabc07cc8d9be94e9fee.png',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                'type5_home_text'           => array(
                    'type'    => 'text',
                    'title'   => '页面导航栏目名称文字',
                    'std'     => '栏目名称',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                'type5_home_title_limit_open'          => array(
                    'type'    => 'checkbox',
                    'title'   => '是否开启页面导航标题字数限制',
                    'desc'    => '',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                'type5_home_title_limit'          => array(
                    'type'    => 'slider',
                    'title'   => '页面导航标题字数限制',
                    'desc'    => '',
                    'std'     => 10,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                        array('type5_home_title_limit_open', '=', '1'),
                    ),
                ),
                'type5_home_title_inline'          => array(
                    'type'    => 'checkbox',
                    'title'   => '页面导航是否和日期等同在一行',
                    'desc'    => '',
                    'std'     => 1,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home', '!=', '1'),
                    ),
                ),
                // 关闭日期显示
                'type5_date_close'=> array(
                    'type'=> 'checkbox',
                    'title'=> '关闭日期显示',
                    'std'=> 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                // 关闭浏览量显示
                'type5_views_close' => array(
                    'type'=> 'checkbox',
                    'title'=> '关闭浏览量显示',
                    'std'=> 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_home_author_data'    => array(
                    'type'    => 'checkbox',
                    'title'   => '作者从后台数据获取（标签2）',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_home_author'         => array(
                    'type'    => 'text',
                    'title'   => '页面导航作者文字',
                    'std'     => 'admin',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_home_author_data', '!=', '1'),
                    ),
                ),
                // 关闭作者显示
                'type5_author_close' => array(
                    'type'=> 'checkbox',
                    'title'=> '关闭作者显示',
                    'std'=> 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_line'                => array(
                    'type'    => 'separator',
                    'title'   => '内容上方横线设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_titleL_color'        => array(
                    'type'    => 'color',
                    'title'   => '内容上方横线颜色',
                    'desc'    => '',
                    'std'     => '#dee2e6',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_content'             => array(
                    'type'    => 'separator',
                    'title'   => '内容部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_content_top'         => array(
                    'type'    => 'slider',
                    'title'   => JText::_('内容上边距'),
                    'max'     => 1000,
                    'min'     => 0,
                    'std'     => '30',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_content_img'         => array(
                    'type'    => 'checkbox',
                    'title'   => '关闭内容部分左侧封面图显示',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                'type5_content_img_w'       => array(
                    'type'    => 'slider',
                    'title'   => JText::_('内容部分图片宽度'),
                    'max'     => 1000,
                    'min'     => 0,
                    'std'     => '320',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('type5_content_img', '!=', '1'),
                    ),
                ),
                'type5_content_text_m'      => array(
                    'type'    => 'slider',
                    'title'   => JText::_('内容部分图片与文字间距'),
                    'max'     => 1000,
                    'min'     => 0,
                    'std'     => '30',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                    ),
                ),
                //布局6
                'timg_width06'              => array(
                    'type'    => 'slider',
                    'title'   => JText::_('图片宽度'),
                    'max'     => 500,
                    'min'     => 10,
                    'std'     => '380',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'intro_fontsize06'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('简介字体大小'),
                    'max'     => 30,
                    'min'     => 10,
                    'std'     => '13',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'force_intro_font_size_type6' => array(
                    'type' => 'checkbox',
                    'title' => '是否需要覆盖简介字体大小',
                    'desc' => '设置后会覆盖掉客户端设置的原来字号',
                    'std' => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    )
                ),
                'intro_color06'             => array(
                    'type'    => 'color',
                    'title'   => '简介字体颜色',
                    'std'     => '#888',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'footer_title_font_size'             => array(
                    'type'    => 'slider',
                    'title'   => '底部标题字号',
                    'std'     => 16,
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'footer_title_color'             => array(
                    'type'    => 'color',
                    'title'   => '底部标题字体颜色',
                    'std'     => '',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'footer_label_font_size'             => array(
                    'type'    => 'slider',
                    'title'   => '底部标签字号',
                    'std'     => array('md' => '', 'sm' => '14', 'xs' => '14',),
                    'max' => 50,
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),
                'footer_label_color'             => array(
                    'type'    => 'color',
                    'title'   => '底部标题字体颜色',
                    'std'     => '',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type6'),
                    ),
                ),

                //  样式七
                'type7_theme_color'         => array(
                    'type'    => 'color',
                    'title'   => '主题颜色',
                    'desc'    => '包括字体和背景颜色',
                    'std'     => '#909744',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_nav' => array(
                    'type'    => 'separator',
                    'title'   => '导航部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_nav_padding' => array(
                    'type' => 'padding',
                    'title' => '内边距',
                    'std' => '20px 20px 20px 20px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'nav_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '导航字号',
                    'std'     => '12',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'nav_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '导航字体颜色',
                    'std'     => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_settings' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'left',
                    'values'  => array(
                        array(
                            'label' => '左侧',
                            'value' => 'left',
                        ),
                        array(
                            'label' => '右侧',
                            'value' => 'right',
                        ),
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7')
                    ),
                ),
                'type7_left_width'                => array(
                    'type'    => 'slider',
                    'title'   => '左侧部分宽度(%)',
                    'std' => '66',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'type7_img'                => array(
                    'type'    => 'separator',
                    'title'   => '图片部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'img_width_type7' => array(
                    'title' => '图片宽度',
                    'type' => 'slider',
                    'std' => '612',
                    'max' => 1000,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'img_height_type7' => array(
                    'title' => '图片高度',
                    'type' => 'slider',
                    'std' => '410',
                    'max' => 1000,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'img_padding_bottom_type7' => array(
                    'title' => '图片内下边距',
                    'type' => 'slider',
                    'std' => '100',
                    'max' => 1000,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'img_margin_type7' => array(
                    'title' => '图片外边距',
                    'type' => 'margin',
                    'std' => '40px auto 0 auto',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'img_fill_mode_type7' => array(
                    'title' => '图片显示方式',
                    'type' => 'select',
                    'std' => 'auto',
                    'values' => array(
                        'scale-down' => '占满不切割',
                        'cover' => '占满切割',
                        'fill' => '占满变形',
                        'auto' => '默认'
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'type7_content'                => array(
                    'type'    => 'separator',
                    'title'   => '内容部分',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_font_size_cover_type07' => array(
                    'type'    => 'checkbox',
                    'title'   => '是否覆盖原有内容字号',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '内容字号',
                    'std'     => '12',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '内容字体颜色',
                    'std'     => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_line_height_type07' => array(
                    'type'    => 'slider',
                    'title'   => '内容行高',
                    'std'     => '24',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_padding_top_type07' => array(
                    'type'    => 'slider',
                    'title'   => '内容上内边距',
                    'std'     => '10',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'content_margin_type07' => array(
                    'type'    => 'margin',
                    'title'   => '内容外边距',
                    'std'     => '0 4% 0 4%',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'left'),
                    ),
                ),
                'type7_right_width'                => array(
                    'type'    => 'slider',
                    'title'   => '右侧部分宽度(%)',
                    'std' => '25',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_title'                => array(
                    'type'    => 'separator',
                    'title'   => '标题部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'title_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '标题字号',
                    'std'     => '16',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'title_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '标题字体颜色',
                    'std'     => '#444',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'title_line_height_type07' => array(
                    'type'    => 'slider',
                    'title'   => '标题行高',
                    'std'     => '20',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_label1'                => array(
                    'type'    => 'separator',
                    'title'   => '标签一设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'label1_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '标签一字号',
                    'std'     => '12',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'label1_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '标签一字体颜色',
                    'std'     => '#999',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'label1_line_height_type07' => array(
                    'type'    => 'slider',
                    'title'   => '标签一行高',
                    'std'     => '14',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'label1_margin_top_type07' => array(
                    'type'    => 'slider',
                    'title'   => '标签一上边距',
                    'std'     => '4',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_time'                => array(
                    'type'    => 'separator',
                    'title'   => '时间设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'time_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '时间字号',
                    'std'     => '13',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'time_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '时间字体颜色',
                    'std'     => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'time_line_height_type07' => array(
                    'type'    => 'slider',
                    'title'   => '时间行高',
                    'std'     => '24',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'time_margin_top_type07' => array(
                    'type'    => 'slider',
                    'title'   => '时间上边距',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_description'                => array(
                    'type'    => 'separator',
                    'title'   => '简介设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'des_font_size_cover_type07' => array(
                    'type'    => 'checkbox',
                    'title'   => '是否覆盖原有简介字号',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'des_font_size_type07' => array(
                    'type'    => 'slider',
                    'title'   => '简介字号',
                    'std'     => '13',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'desc_font_color_type07' => array(
                    'type'    => 'color',
                    'title'   => '简介字体颜色',
                    'std'     => '#888',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'desc_line_height_type07' => array(
                    'type'    => 'slider',
                    'title'   => '简介行高',
                    'std'     => '24',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'desc_margin_type07' => array(
                    'type'    => 'margin',
                    'title'   => '简介外边距',
                    'std'     => '20px 0 24px 0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                // 样式七按钮
                'button_type7' => array(
                    'title' => '按钮设置',
                    'type' => 'separator',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_taobao_button'       => array(
                    'type'        => 'select',
                    'desc'        => JText::_('跳转淘宝按钮'),
                    'placeholder' => JText::_('跳转淘宝按钮'),
                    'title'       => '跳转淘宝按钮',
                    'std'         => 'yes',
                    'values'      => array(
                        'yes' => '启用',
                        'no'  => '不启用',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_taobao_button_url'   => array(
                    'type'  => 'text',
                    'title' => JText::_('跳转淘宝店铺地址'),
                    'desc'  => JText::_('跳转淘宝店铺地址'),
                    'std'   => 'https://www.taobao.com',
                    'depends' => array(
                        array('type7_taobao_button', '=', 'yes'),
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),

                    ),
                ),
                'type7_weidian_button'      => array(
                    'type'        => 'select',
                    'desc'        => JText::_('跳转微店按钮'),
                    'placeholder' => JText::_('跳转微店按钮'),
                    'title'       => '跳转微店按钮',
                    'std'         => 'yes',
                    'values'      => array(
                        'yes' => '启用',
                        'no'  => '不启用',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_weidian_button_url'  => array(
                    'type'  => 'text',
                    'title' => JText::_('跳转微店店铺地址'),
                    'desc'  => JText::_('跳转微店店铺地址'),
                    'std'   => 'https://www.weidian.com',
                    'depends' => array(
                        array('type7_weidian_button', '=', 'yes'),
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),

                    ),
                ),
                'button_item_type7' => array(
                    'title' => '按钮组',
                    'std' => array(),
                    'attr' => array(
                        'btn_txt' => array(
                            'title' => '按钮文字',
                            'type' => 'text'
                        ),
                        'link' => array(
                            'title' => '跳转链接',
                            'type' => 'text'
                        ),
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'open_way_type7' => array(
                    'title' => '打开方式',
                    'type' => 'select',
                    'std' => '_blank',
                    'values' => array(
                        '_blank' => '新窗口',
                        '_self' => '本窗口'
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_height_type7' => array(
                    'title' => '按钮高度',
                    'type' => 'slider',
                    'std' => 40,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_padding_type7' => array(
                    'title' => '按钮内边距',
                    'type' => 'padding',
                    'std' => '0 30px 0 30px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_border_color_type7' => array(
                    'title' => '按钮边框色',
                    'type' => 'color',
                    'std' => '#dbdbdb',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_bg_color_type7' => array(
                    'title' => '按钮背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_color_type7' => array(
                    'title' => '按钮字体色',
                    'type' => 'color',
                    'std' => '#555',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_margin_top_type7' => array(
                    'title' => '按钮上边距',
                    'type' => 'slider',
                    'std' => '20',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_type7' => array(
                    'title' => '相关内容设置',
                    'type' => 'separator',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_size_type7' => array(
                    'title' => '相关内容标题字号',
                    'type' => 'slider',
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_color_type7' => array(
                    'title' => '相关内容标题颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_padding_type7' => array(
                    'title' => '相关内容标题内边距',
                    'type' => 'padding',
                    'std' => '30px 0 30px 0',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_margin_bottom_type7' => array(
                    'title' => '分类标签下边距',
                    'type' => 'slider',
                    'std' => 30,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_padding_type7' => array(
                    'title' => '分类标签内边距',
                    'type' => 'padding',
                    'std' => '0 14px 0 14px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_height_type7' => array(
                    'title' => '分类标签高度',
                    'type' => 'slider',
                    'std' => 26,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_border_color_type7' => array(
                    'title' => '分类标签边框色',
                    'type' => 'color',
                    'std' => 'rgba(170,170,170,0.2)',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_bg_color_type7' => array(
                    'title' => '分类标签背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_color_type7' => array(
                    'title' => '分类标签字体色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'button_about_margin_right_type7' => array(
                    'title' => '分类标签右边距',
                    'type' => 'slider',
                    'std' => '10',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_type7' => array(
                    'title' => '相关内容列表设置',
                    'type' => 'separator',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_col_type7' => array(
                    'title' => '相关内容列表列数',
                    'type' => 'slider',
                    'std' => '2',
                    'max' => 5,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_col_margin_type7' => array(
                    'title' => '相关内容列表外边距',
                    'type' => 'margin',
                    'std' => '0 10px 20px 0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_col_border_color_type7' => array(
                    'title' => '相关内容列表边框色',
                    'type' => 'color',
                    'std' => 'rgba(170,170,170,0.2)',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_title_padding_type7' => array(
                    'title' => '相关内容列表标题内边距',
                    'type' => 'padding',
                    'std' => '20px 5px 20px 5px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_title_size_type7' => array(
                    'title' => '相关内容列表标题字号',
                    'type' => 'slider',
                    'std' => '12',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_title_color_type7' => array(
                    'title' => '相关内容列表标题字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_subtitle_size_type7' => array(
                    'title' => '相关内容列表副标题字号',
                    'type' => 'slider',
                    'std' => '12',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_subtitle_color_type7' => array(
                    'title' => '相关内容列表副标题字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'about_list_subtitle_margin_top_type7' => array(
                    'title' => '相关内容列表副标题上边距',
                    'type' => 'slider',
                    'std' => '',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                // 小屏配置
                'type7_small_screen' => array(
                    'type'    => 'separator',
                    'title'   => '小屏配置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_box_margin' => array(
                    'type'    => 'margin',
                    'title'   => '文字外边距',
                    'std'     => '20px 2% 0 4%',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 标题
                'type7_small_screen_title' => array(
                    'type'    => 'separator',
                    'title'   => '标题配置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_title_size' => array(
                    'type'    => 'slider',
                    'title'   => '标题字号',
                    'std' => '16',
                    'max' => '50',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_title_color' => array(
                    'type'    => 'color',
                    'title'   => '标题颜色',
                    'std' => '#444',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_title_line_height' => array(
                    'type'    => 'slider',
                    'title'   => '标题行高',
                    'std' => '20',
                    'max' => '100',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_title_margin_top' => array(
                    'type'    => 'slider',
                    'title'   => '标题上边距',
                    'std' => '48',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 标签一
                'type7_small_screen_label1' => array(
                    'type'    => 'separator',
                    'title'   => '标签一配置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_label1_size' => array(
                    'type'    => 'slider',
                    'title'   => '标签一字号',
                    'std' => '12',
                    'max' => '50',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_label1_color' => array(
                    'type'    => 'color',
                    'title'   => '标签一颜色',
                    'std' => '#999',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_label1_line_height' => array(
                    'type'    => 'slider',
                    'title'   => '标签一行高',
                    'std' => '14',
                    'max' => '100',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_label1_margin_top' => array(
                    'type'    => 'slider',
                    'title'   => '标签一上边距',
                    'std' => '4',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 简介
                'type77_small_screen_description'                => array(
                    'type'    => 'separator',
                    'title'   => '简介设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_small_screen_des_font_size_cover' => array(
                    'type'    => 'checkbox',
                    'title'   => '是否覆盖原有简介字号',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_des_font_size' => array(
                    'type'    => 'slider',
                    'title'   => '简介字号',
                    'std'     => '14',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_des_color' => array(
                    'type'    => 'color',
                    'title'   => '简介颜色',
                    'std'     => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_desc_line_height' => array(
                    'type'    => 'slider',
                    'title'   => '简介行高',
                    'std'     => '',
                    'max' => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_desc_margin_top' => array(
                    'type'    => 'slider',
                    'title'   => '简介上边距',
                    'std'     => '20',
                    'max' => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 店铺跳转链接
                'type77_small_screen_button'                => array(
                    'type'    => 'separator',
                    'title'   => '跳转按钮',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_small_screen_button_height' => array(
                    'title' => '按钮高度',
                    'type' => 'slider',
                    'std' => 48,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_padding' => array(
                    'title' => '按钮内边距',
                    'type' => 'padding',
                    'std' => '0 32px 0 32px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_border_color' => array(
                    'title' => '按钮边框色',
                    'type' => 'color',
                    'std' => '#d6d6d6',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_bg_color' => array(
                    'title' => '按钮背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_color' => array(
                    'title' => '按钮字体色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_margin' => array(
                    'title' => '按钮边距',
                    'type' => 'margin',
                    'std' => '16px 0 16px 0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 正文
                'type77_small_screen_content'                => array(
                    'type'    => 'separator',
                    'title'   => '跳转按钮',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_small_screen_content_font_size_cover' => array(
                    'type'    => 'checkbox',
                    'title'   => '是否覆盖原有正文字号',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_content_font_size' => array(
                    'type'    => 'slider',
                    'title'   => '正文字号',
                    'std'     => '13',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_content_color' => array(
                    'type'    => 'color',
                    'title'   => '简介颜色',
                    'std'     => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_content_line_height' => array(
                    'type'    => 'slider',
                    'title'   => '简介行高',
                    'std'     => '',
                    'max' => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 相关内容
                'type7_small_screen_about'                => array(
                    'type'    => 'separator',
                    'title'   => '相关内容',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                        array('type7_settings', '=', 'right'),
                    ),
                ),
                'type7_small_screen_about_size' => array(
                    'title' => '相关内容标题字号',
                    'type' => 'slider',
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_margin_top' => array(
                    'title' => '相关内容标题上边距',
                    'type' => 'slider',
                    'std' => '20',
                    'max' => 500,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_color' => array(
                    'title' => '相关内容标题颜色',
                    'type' => 'color',
                    'std' => '#444',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 分类标签
                'type7_small_screen_button_about_margin' => array(
                    'title' => '分类标签外边距',
                    'type' => 'margin',
                    'std' => '32px 3px 32px 3px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_about_padding' => array(
                    'title' => '分类标签内边距',
                    'type' => 'padding',
                    'std' => '4px 12px 4px 12px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_about_border_color' => array(
                    'title' => '分类标签边框色',
                    'type' => 'color',
                    'std' => '#f0f0f0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_about_bg_color' => array(
                    'title' => '分类标签背景色',
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_about_color' => array(
                    'title' => '分类标签字体色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_button_about_margin_right' => array(
                    'title' => '分类标签右边距',
                    'type' => 'slider',
                    'std' => '10',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 相关内容列表
                'type7_small_screen_about_list' => array(
                    'title' => '相关内容列表设置',
                    'type' => 'separator',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_col' => array(
                    'title' => '相关内容列表列数',
                    'type' => 'slider',
                    'std' => '2',
                    'max' => 5,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_col_margin' => array(
                    'title' => '相关内容列表外边距',
                    'type' => 'margin',
                    'std' => '1% 1% 1% 1%',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_col_border_color' => array(
                    'title' => '相关内容列表边框色',
                    'type' => 'color',
                    'std' => 'rgba(170,170,170,0.2)',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_title_padding' => array(
                    'title' => '相关内容列表标题内边距',
                    'type' => 'padding',
                    'std' => '20px 20px 20px 20px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_title_size' => array(
                    'title' => '相关内容列表标题字号',
                    'type' => 'slider',
                    'std' => '12',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_title_color' => array(
                    'title' => '相关内容列表标题字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                // 副标题
                'type7_small_screen_about_list_subtitle_size' => array(
                    'title' => '相关内容列表副标题字号',
                    'type' => 'slider',
                    'std' => '12',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_subtitle_color' => array(
                    'title' => '相关内容列表副标题字体颜色',
                    'type' => 'color',
                    'std' => '#666',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),
                'type7_small_screen_about_list_subtitle_margin_top' => array(
                    'title' => '相关内容列表副标题上边距',
                    'type' => 'slider',
                    'std' => '',
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type7'),
                    ),
                ),


                // 轮播部分
                'type8_banner'                => array(
                    'type'    => 'separator',
                    'title'   => '轮播部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_banopen'               => array(
                    'type'    => 'checkbox',
                    'title'   => '关闭轮播展示',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_banner_bili'               => array(
                    'type'    => 'checkbox',
                    'title'   => '设置轮播图片比例',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                        array('type8_banopen', '=', '0'),
                    ),
                ),
                'type8_ban_img'               => array(
                    'type'    => 'text',
                    'title'   => '图片宽高比(如：2/1)',
                    'std'     => '2/1',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                        array('type8_banner_bili', '=', '1'),
                        array('type8_banopen', '=', '0'),

                    ),
                ),
                'type8_price_size'          => array(
                    'type'    => 'slider',
                    'title'   => '价格字体大小(后台取值：标签1)',
                    'std'     => '24',
                    'max'     => '100',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_price_color'          => array(
                    'type'    => 'color',
                    'title'   => '价格字体颜色',
                    'std'     => '#fa230a',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_title_size'          => array(
                    'type'    => 'slider',
                    'title'   => '标题字体大小',
                    'std'     => '16',
                    'max'     => '50',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_title_color'          => array(
                    'type'    => 'color',
                    'title'   => '标题字体颜色',
                    'std'     => '#252525',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),
                'type8_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('标题内间距'),
                    'std' => array('md' => '18px 18px 18px 18px', 'sm' => '18px 18px 18px 18px', 'xs' => '18px 18px 18px 18px'),
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type8'),
                    ),
                ),

                // 是否启用图片位置处理
                'img_position' => array(
                    'type' => 'checkbox',
                    'title' => '是否启用图片位置处理',
                    'std' => '0'
                ),

                // 布局9
                'settings_type9' => array(
                    'title' => '布局9配置项',
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'img',
                    'values' => array(
                        array(
                            'label' => '图片',
                            'value' => 'img'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'detail'
                        ),
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                    ),
                ),
                'show_banner_type9' => array(
                    'title' => '是否显示图片（使用多图上传的第一张作为pc端图片，第二张作为移动端图片，第三张为平板图片，如果没有，默认显示封面图）',
                    'type' => 'checkbox',
                    'std' => '1',
                    'desc' => '使用多图上传的第一张作为pc端图片，第二张作为移动端图片，第三张为平板图片',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'img'),
                    )
                ),
                'img_width_type9' => array(
                    'title' => '图片宽度（%）',
                    'type' => 'slider',
                    'min' => 0,
                    'max' => 100,
                    'std' => 100,
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'img'),
                    )
                ),
                'set_img_height_type9' => array(
                    'title' => '是否设置图片高度',
                    'type' => 'checkbox',
                    'std' => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'img'),
                    )
                ),
                'img_height_type9' => array(
                    'title' => '图片高度',
                    'type' => 'slider',
                    'std' => '',
                    'min' => 0,
                    'max' => 1500,
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'img'),
                        array('set_img_height_type9', '=', '1'),
                    )
                ),
                'img_fit_type9' => array(
                    'title' => '图片填充方式',
                    'type' => 'select',
                    'values' => array(
                        'fill' => '拉伸填充',
                        'cover' => '超出裁剪',
                        'contain' => '留白'
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'img'),
                        array('set_img_height_type9', '=', '1'),
                    )
                ),
                'detail_top_type9' => array(
                    'title' => '详情上边距',
                    'type' => 'slider',
                    'std' => 24,
                    'min' => -1000,
                    'max' => 1000,
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'detail'),
                    )
                ),
                'detail_width_type9' => array(
                    'title' => '内容宽度（%）',
                    'type' => 'slider',
                    'std' => 63,
                    'min' => 0,
                    'max' => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('settings_type9', '=', 'detail'),
                    )
                ),
                // 布局9

                // 翻页部分
                'type5_page'                => array(
                    'type'    => 'separator',
                    'title'   => '翻页部分设置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                    ),
                ),
                'page_button'               => array(
                    'type'    => 'checkbox',
                    'title'   => '翻页按钮是否关闭',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                    ),
                ),
                'page_dttitle'              => array(
                    'type'    => 'checkbox',
                    'title'   => '翻页开启动态标题',
                    'std'     => 0,
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('art_type_selector_proinfo', '!=', 'type2'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type6'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                    ),
                ),
                'type5_page_style'          => array(
                    'type'    => 'select',
                    'title'   => '翻页布局样式',
                    'desc'    => '',
                    'std'     => 'left',
                    'values'  => array(
                        'page01' => '布局01',
                        'page02' => '布局02',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'type5_page_top'            => array(
                    'type'    => 'slider',
                    'title'   => JText::_('翻页上边距'),
                    'max'     => 1000,
                    'min'     => 0,
                    'std'     => '30',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'next_page_text'            => array(
                    'type'    => 'text',
                    'title'   => '下一页按钮文字',
                    'std'     => '下一页',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type6'),
                    ),
                ),
                'up_page_text'              => array(
                    'type'    => 'text',
                    'title'   => '上一页按钮文字',
                    'std'     => '上一页',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type6'),
                    ),
                ),
                'type5_page_font_size'      => array(
                    'type'    => 'slider',
                    'title'   => '翻页字体大小',
                    'max'     => 100,
                    'min'     => 12,
                    'std'     => 14,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                        array('type5_page_style', '=', 'page02'),
                    ),
                ),
                'type5_page_lineHeight'     => array(
                    'type'    => 'slider',
                    'title'   => '翻页文字行高',
                    'max'     => 100,
                    'min'     => 12,
                    'std'     => 42,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                        array('type5_page_style', '=', 'page02'),
                    ),
                ),
                'type5_page_line'           => array(
                    'type'    => 'select',
                    'title'   => '翻页中间线样式',
                    'std'     => 'dashed',
                    'values'  => array(
                        'none'   => '无边框',
                        'solid'  => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge'  => '3D 垄状边框',
                        'inset'  => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                        array('type5_page_style', '=', 'page02'),
                    ),
                ),
                'type5_page_line_color'     => array(
                    'type'    => 'color',
                    'title'   => '下划线颜色',
                    'std'     => '#f0f0f0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type5'),
                        array('page_button', '=', '0'),
                        array('type5_page_style', '=', 'page02'),
                    ),
                ),
                'pageState'                 => array(
                    'type'    => 'buttons',
                    'title'   => '翻页状态',
                    'std'     => 'normal',
                    'values'  => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal',
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover',
                        ),
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('page_button', '=', 0),
                    ),
                    'tabs'    => true,
                ),
                'pageColor'                 => array(
                    'type'    => 'color',
                    'title'   => '正常翻页文字颜色',
                    'desc'    => '',
                    'std'     => '#1237ce',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageBorderColor'           => array(
                    'type'    => 'color',
                    'title'   => '正常翻页边框颜色',
                    'desc'    => '',
                    'std'     => '#1237ce',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type9'),
                        array('type5_page_style', '!=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageBgColor'               => array(
                    'type'    => 'color',
                    'title'   => '正常翻页背景颜色',
                    'desc'    => '',
                    'std'     => '#fff',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '!=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'page_prev'                 => array(
                    'type'    => 'media',
                    'format'  => 'image',
                    'title'   => '正常上一页图标',
                    'std'     => 'https://oss.lcweb01.cn/joomla/20220112/67bdf2ce34fa38f92dbe02e46d8e3db4.png',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'page_next'                 => array(
                    'type'    => 'media',
                    'format'  => 'image',
                    'title'   => '正常下一页图标',
                    'std'     => 'https://oss.lcweb01.cn/joomla/20220112/881d250021a360a075c84269b29b7e5d.png',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageColorhover'            => array(
                    'type'    => 'color',
                    'title'   => '移入翻页文字颜色',
                    'desc'    => '',
                    'std'     => '#1237ce',
                    'depends' => array(
                        array('pageState', '=', 'hover'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageBorderColorhover'      => array(
                    'type'    => 'color',
                    'title'   => '移入翻页边框颜色',
                    'desc'    => '',
                    'std'     => '#1237ce',
                    'depends' => array(
                        array('pageState', '=', 'hover'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('art_type_selector_proinfo', '!=', 'type9'),
                        array('type5_page_style', '!=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageBgColorhover'          => array(
                    'type'    => 'color',
                    'title'   => '移入翻页背景颜色',
                    'desc'    => '',
                    'std'     => '#fff',
                    'depends' => array(
                        array('pageState', '=', 'hover'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '!=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'page_prev_hover'           => array(
                    'type'    => 'media',
                    'format'  => 'image',
                    'title'   => '移入上一页图标',
                    'std'     => 'https://oss.lcweb01.cn/joomla/20220112/d257b4631b529f078b86ce241546c95a.png',
                    'depends' => array(
                        array('pageState', '=', 'hover'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                'page_next_hover'           => array(
                    'type'    => 'media',
                    'format'  => 'image',
                    'title'   => '移入下一页图标',
                    'std'     => 'https://oss.lcweb01.cn/joomla/20220112/abf3c037292df291c97947c085ac3741.png',
                    'depends' => array(
                        array('pageState', '=', 'hover'),
                        array('art_type_selector_proinfo', '!=', 'type3'),
                        array('art_type_selector_proinfo', '!=', 'type4'),
                        array('art_type_selector_proinfo', '!=', 'type7'),
                        array('type5_page_style', '=', 'page02'),
                        array('page_button', '=', 0),
                    ),
                ),
                // type9
                'pageTopType9'                 => array(
                    'type'    => 'slider',
                    'title'   => '翻页按钮上边距',
                    'desc'    => '',
                    'std'     => '50',
                    'min'     => -500,
                    'max'     => 500,
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageColumnType9'                 => array(
                    'type'    => 'checkbox',
                    'title'   => '翻页按钮是否不在同一行',
                    'std'    => '1',
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('page_button', '=', 0),
                    ),
                ),
                'pageMarginType9'                 => array(
                    'type'    => 'slider',
                    'title'   => '翻页按钮间间隔',
                    'desc'    => '',
                    'std'     => '25',
                    'min'     => 0,
                    'max'     => 500,
                    'depends' => array(
                        array('pageState', '=', 'normal'),
                        array('art_type_selector_proinfo', '=', 'type9'),
                        array('page_button', '=', 0),
                    ),
                ),
                // type9
                //type2
                'type2_settings_separator' => array(
                    'type'    => 'separator',
                    'title'   => '详情配置',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                    ),
                ),
                'type2_settings' => array(
                    'type' => 'buttons',
                    'tabs' => true,
                    'title' => '样式二配置项',
                    'std' => 'content',
                    'values' => array(
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '轮播',
                            'value' => 'swiper'
                        )
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                    ),
                ),
                'tit_size'                  => array(
                    'type'    => 'slider',
                    'title'   => '标题文字大小',
                    'std'     => 42,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'tit_cle_f_size'            => array(
                    'type'    => 'slider',
                    'title'   => '副标题字体大小',
                    'std'     => 14,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                /* 'jj_size'                   => array(
                    'type'    => 'slider',
                    'title'   => '简介大小',
                    'std'     => 14,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ), */
                'tit_cle'                   => array(
                    'type'    => 'color',
                    'title'   => '标题颜色',
                    'desc'    => '',
                    'std'     => '#000',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'tit_cle_f'                 => array(
                    'type'    => 'color',
                    'title'   => '副标题颜色',
                    'desc'    => '',
                    'std'     => '#000',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'tit_cle_line_color'                   => array(
                    'type'    => 'color',
                    'title'   => '短线颜色',
                    'desc'    => '',
                    'std'     => '#fecf41',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'tit_cle_line_margin' => array(
                    'type'    => 'margin',
                    'title'   => '短线外边距',
                    'desc'    => '',
                    'std'     => '25px 0 0 0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                /* 'text_cle_j'                => array(
                    'type'    => 'color',
                    'title'   => '简介颜色',
                    'desc'    => '',
                    'std'     => '#ff5500',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                    ),
                ), */
                'img_w'                     => array(
                    'type'    => 'slider',
                    'title'   => '图片宽度',
                    'std'     => 215,
                    'max'     => 600,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'img_h'                     => array(
                    'type'    => 'slider',
                    'title'   => '图片高度',
                    'std'     => 315,
                    'max'     => 600,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'img_mode_type2' => array(
                    'type'    => 'select',
                    'title'   => '图片展示方式',
                    'std'     => 'cover',
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'unset' => JText::_('占满不切割显示'),
                        'cover'  => JText::_('占满切割显示'),
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'content'),
                    ),
                ),
                'pro_title'                 => array(
                    'type'    => 'text',
                    'title'   => JText::_('产品展示标题'),
                    'std'     => '教师风采',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'pro_vice_title'            => array(
                    'type'    => 'text',
                    'title'   => JText::_('产品展示副标题'),
                    'std'     => 'teacher style',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'pro_vice_title_color'            => array(
                    'type'    => 'color',
                    'title'   => JText::_('产品展示副标题颜色'),
                    'std'     => '#fed561',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'pro_vice_title_line_color'            => array(
                    'type'    => 'color',
                    'title'   => JText::_('产品展示标题左侧短线颜色'),
                    'std'     => '#fee800',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'pro_vice_title_padding'            => array(
                    'type'    => 'padding',
                    'title'   => JText::_('产品展示标题内边距'),
                    'std'     => '0 0 0 10px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'nav_back_col'              => array(
                    'type'    => 'checkbox',
                    'title'   => '开启轮播图宽高设置',
                    'std'     => 0,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'img_carousel_w'            => array(
                    'type'    => 'slider',
                    'title'   => '轮播图片宽度',
                    'std'     => 215,
                    'max'     => 600,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('nav_back_col', '=', 1),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'img_carousel_h'            => array(
                    'type'    => 'slider',
                    'title'   => '轮播图片高度',
                    'std'     => 315,
                    'max'     => 600,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('nav_back_col', '=', 1),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                'img_style'                 => array(
                    'type'    => 'select',
                    'title'   => JText::_('图片填充方式'),
                    'values'  => array(
                        'cover' => JText::_('自适应显示'),
                        'unset' => JText::_('占满不切割显示'),
                        'none'  => JText::_('占满切割显示'),
                    ),
                    'std'     => 'fill',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type2'),
                        array('nav_back_col', '=', 1),
                        array('type2_settings', '=', 'swiper'),
                    ),
                ),
                //样式3
                'type3_title'               => array(
                    'type'    => 'separator',
                    'title'   => JText::_('分类标题设置'),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_class_title'         => array(
                    'type'    => 'text',
                    'title'   => JText::_('分类标题'),
                    'std'     => '产品展示',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_class_vice_title'    => array(
                    'type'    => 'text',
                    'title'   => JText::_('分类副标题'),
                    'std'     => 'Products',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_bg_col'              => array(
                    'type'    => 'color',
                    'title'   => '分类背景颜色',
                    'desc'    => '',
                    'std'     => '#09a694',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_text_col'            => array(
                    'type'    => 'color',
                    'title'   => '分类标题字体颜色',
                    'desc'    => '',
                    'std'     => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_list'                => array(
                    'type'    => 'separator',
                    'title'   => JText::_('列表设置'),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_goods_catid'         => array(
                    'type'    => 'select',
                    'title'   => JText::_('选择产品分类(请与产品列表的分类保持一致)'),
                    'desc'    => '请与产品列表的分类保持一致',
                    'values'  => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'ordering'                  => array(
                    'type'   => 'select',
                    'title'  => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc'   => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                    ),
                    'std'    => 'latest',
                ),
                'type3_bumber'              => array(
                    'type'    => 'slider',
                    'title'   => '列表数量',
                    'std'     => 10,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_img_carousel_h'      => array(
                    'type'    => 'slider',
                    'title'   => '列表高度',
                    'std'     => 50,
                    'max'     => 150,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type_screen_setting' => array(
                    'type' => 'buttons',
                    'title' => '屏幕设置',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常屏幕',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '小屏',
                            'value' => 'small'
                        )
                    ),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_img_carousel_w'      => array(
                    'type'    => 'slider',
                    'title'   => '列表宽度(%)',
                    'std'     => 100,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type_screen_setting', '=', 'normal'),
                    ),
                ),
                'type3_small_img_carousel_w'      => array(
                    'type'    => 'slider',
                    'title'   => '小屏列表展示个数',
                    'std'     => 3,
                    'max'     => 10,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type_screen_setting', '=', 'small'),
                    ),
                ),
                'type3_small_img_carousel_margin_right'      => array(
                    'type'    => 'slider',
                    'title'   => '小屏列表右边距',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type_screen_setting', '=', 'small'),
                    ),
                ),
                'type3_small_img_carousel_margin_bottom'      => array(
                    'type'    => 'slider',
                    'title'   => '小屏列表下边距',
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type_screen_setting', '=', 'small'),
                    ),
                ),
                'type3_text_number'         => array(
                    'type'    => 'slider',
                    'title'   => '列表字数限制',
                    'std'     => 10,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_text_size'           => array(
                    'type'    => 'slider',
                    'title'   => '列表字体大小',
                    'std'     => 16,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_list_bg_col_setting'         => array(
                    'type'    => 'buttons',
                    'title'   => '列表背景颜色设置',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'select'
                        )
                    ),
                    'std'     => 'normal',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_list_bg_col'         => array(
                    'type'    => 'color',
                    'title'   => '列表背景颜色',
                    'desc'    => '',
                    'std'     => '#cdb7a3',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type3_list_bg_col_setting', '=', 'normal'),
                    ),
                ),
                'type3_list_bg_col_select'         => array(
                    'type'    => 'color',
                    'title'   => '选中项列表背景颜色',
                    'desc'    => '',
                    'std'     => '#cdb7a3',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                        array('type3_list_bg_col_setting', '=', 'select'),
                    ),
                ),
                'type3_list_text_col'       => array(
                    'type'    => 'color',
                    'title'   => '列表字体颜色',
                    'desc'    => '',
                    'std'     => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_content'             => array(
                    'type'    => 'separator',
                    'title'   => JText::_('内容设置'),
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_vertical_location'   => array(
                    'type'    => 'slider',
                    'title'   => '内容垂直位置调整',
                    'std'     => 16,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                'type3_left_location'       => array(
                    'type'    => 'slider',
                    'title'   => '内容左右位置调整',
                    'std'     => 16,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type3'),
                    ),
                ),
                // type4
                'type4_settings_separator' => array(
                    'type'    => 'separator',
                    'title'   => '布局4配置项',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
                'type4_bg_color' => array(
                    'type' => 'color',
                    'title' => '背景色',
                    'std' => '#f2f5fa',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
                'type4_settings' => array(
                    'type'    => 'buttons',
                    'values'  => array(
                        array(
                            'label' => '列表',
                            'value' => 'list'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                    ),
                    'std'     => 'list',
                    'title'   => '列表配置项',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
                'gl_show'     => array(
                    'type'    => 'slider',
                    'title'   => JText::_('不高亮显示数量'),
                    'max'     => 100,
                    'min'     => 1,
                    'std'     => 5,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'mr_title_color'            => array(
                    'type'    => 'color',
                    'title'   => JText::_('左侧标题字体颜色'),
                    'std'     => '#333333',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'mr_title_color_gl'         => array(
                    'type'    => 'color',
                    'title'   => JText::_('左侧高亮标题字体颜色'),
                    'std'     => '#0a4a9b',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'type4_list_padding'         => array(
                    'type'    => 'padding',
                    'title'   => JText::_('左侧列表内边距'),
                    'std'     => '0 0 0 40px',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'type4_list_bg_color'         => array(
                    'type'    => 'color',
                    'title'   => JText::_('左侧列表背景色'),
                    'std'     => '#f8f8f8',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'type4_list_light_bg_color'         => array(
                    'type'    => 'color',
                    'title'   => JText::_('左侧列表高亮背景色'),
                    'std'     => '#e2e3e4',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'list'),
                    ),
                ),
                'right_title_color'         => array(
                    'type'    => 'color',
                    'title'   => JText::_('右侧标题字体颜色'),
                    'std'     => '#0a4a9b',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_title_size'         => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧标题字体大小'),
                    'std'     => 25,
                    'max'     => 100,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_desc_color'          => array(
                    'type'    => 'color',
                    'title'   => JText::_('右侧内容字体颜色'),
                    'std'     => '#626262',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_desc_size'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧内容字体大小'),
                    'std'     => 12,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_desc_size'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧内容字体大小'),
                    'std'     => 12,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_img_width'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧图片宽度(%)'),
                    'std'     => array('md' => 30, 'sm' => 100, 'xs' => 100),
                    'responsive' => true,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_img_title_size'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧图片标题字体大小'),
                    'std'     => 16,
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_img_title_height'          => array(
                    'type'    => 'slider',
                    'title'   => JText::_('右侧图片标题字体大小高度'),
                    'std'     => 35,
                    'max' => 50,
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_img_bg_color'          => array(
                    'type'    => 'color',
                    'title'   => JText::_('右侧图片标题背景颜色'),
                    'std'     => '#798ca4',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_img_title_color'          => array(
                    'type'    => 'color',
                    'title'   => JText::_('右侧图片标题颜色'),
                    'std'     => '#fff',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'right_arrow_color'          => array(
                    'type'    => 'color',
                    'title'   => JText::_('右侧图片标题箭头颜色'),
                    'std'     => '#f7941d',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                        array('type4_settings', '=', 'content'),
                    ),
                ),
                'show_pro_data'             => array(
                    'type'    => 'checkbox',
                    'title'   => JText::_('获取分类下所有数据'),
                    'std'     => '0',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
                'goods_catid'               => array(
                    'type'    => 'select',
                    'title'   => JText::_('选择产品分类'),
                    'desc'    => '产品分类',
                    'values'  => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
                'ordering'                  => array(
                    'type'    => 'select',
                    'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values'  => array(
                        'sortdesc'      => JText::_('排序id倒序'),
                        'sortasc'       => JText::_('排序id正序'),
                        'latest'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits'          => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured'      => JText::_('COM_JWPAGEFAC阴影水平偏移TORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc'  => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std'     => 'sortdesc',
                    'depends' => array(
                        array('art_type_selector_proinfo', '=', 'type4'),
                    ),
                ),
            ),
        ),
    )
);
