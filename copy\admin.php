<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_copy',
		'title' => JText::_('一键复制'),
		'desc' => JText::_('一键复制'),
		'category' => '常用插件',
		'attr' => array(

			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'bgimage' => array(
					'type' => 'media',
					'title' => JText::_('背景图'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT_DESC'),
					'show_input' => true,
					'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
				),

				'ewmtop' => array(
					'type' => 'padding',
					'title' => JText::_('背景内间距'),
					'std' => array('md' => '150px 20px 50px 20px', 'sm' => '130px 20px 30px 20px', 'xs' => '200px 20px 50px 20px'),
					'responsive' => true,
				),

				'ewmimage1' => array(
					'type' => 'media',
					'title' => JText::_('二维码1'),
					'std' => 'https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png',
				),
				'copy_cont1' => array(
					'type' => 'text',
					'title' => JText::_('复制内容1'),
					'std' => '',
				),
				'ewmimage2' => array(
					'type' => 'media',
					'title' => JText::_('二维码2'),
					'std' => 'https://oss.lcweb01.cn/joomla/20221213/4755c4c0114eee30de80ceb8044beeff.png',
				),
				'copy_cont2' => array(
					'type' => 'text',
					'title' => JText::_('复制内容2'),
					'std' => '',
				),

				'ewm_background' => array(
					'type' => 'color',
					'title' => JText::_('二维码背景色'),
					'std' => '#fff',
				),

				'ewmwidth' => array(
					'type' => 'slider',
					'title' => JText::_('二维码宽度'),
					'std' => array(
                        'md' => 220,
                        'sm' => 150,
                        'xs' => 130
                    ),
					'max' => 500,
					'responsive' => true
				),

				'ewmheight' => array(
					'type' => 'slider',
					'title' => JText::_('二维码高度'),
					'std' => array(
                        'md' => 250,
                        'sm' => 180,
                        'xs' => 130
                    ),
					'max' => 500,
					'responsive' => true
				),
				'ewmmargin' => array(
					'type' => 'slider',
					'title' => JText::_('二维码左右间距'),
					'std' => array(
                        'md' => 50,
                        'sm' => 10,
                        'xs' => 10
                    ),
					'max' => 200,
					'responsive' => true
				),

				'cpy_color' => array(
					'type' => 'color',
					'title' => JText::_('一键复制字体颜色'),
					'std' => '#333',
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),

			),
		),
	)
);
