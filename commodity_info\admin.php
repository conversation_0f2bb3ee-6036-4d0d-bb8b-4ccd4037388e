<?php
 /**
  * <AUTHOR> /用户名|ID/
  * @email        #EMAIL#
  * @url          #URL#
  * @copyright    Copyright (c) 2021 - #YEAR# 建站通
  * @license      GNU General Public License version 2 or later
  * @date         #DATETIME#
  * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
  */
 //no direct accees
 defined('_JEXEC') or die('Restricted access');

 JwAddonsConfig::addonConfig(

     array(
         'type' => 'general', //插件 
         'addon_name' => 'commodity_info',
         'title' => '商品详情',
         'desc' => '',
         'category' => '商品', //插件分组
         'attr' => array(
             //配置项主体
             'general' => array(
                 //【基本】选项卡
                 'title_h2_button' => array(
	                'type' => 'select',
                    'desc' => JText::_('产品详情的标题设置为网页标题'),
                    'placeholder' => JText::_('产品详情的标题设置为网页标题'),
	                'title' => '动态标题设置',
	                'std' => 'yes',
                    'values' => array(
                        'yes' => '启用',
                        'no' => '不启用',
                    ),
                ),
                 'title_position' => array(
                     'type' => 'select',
                     'title' => '标题布局',
                     'desc' => '',
                     'std' => 'left',
                     'values' => array(
                         'left' => '居左',
                         'center' => '居中',
                         'right' => '居右'
                     ),
                 ),
                 'title_color' => array(
                     'type' => 'color',
                     'title' => '标题颜色',
                     'desc' => '',
                     'std' => '#333333',
                 ),
                 'title_font_size' => array(
                     'type' => 'slider',
                     'title' => JText::_('标题字体大小'),
                     'max' => 100,
                     'min' => 12,
                     'std' => array('md' => '39','sm' => '28','xs' => '28'),
                     'responsive' => true,
                 ),
                 'img_margin_right' => array(
                     'type' => 'slider',
                     'title' => JText::_('商品图片和信息之间的距离'),
                     'max' => 1000,
                     'min' => 0,
                     'std' => '10',
                 ),
                 'price_margin_top' => array(
                     'type' => 'slider',
                     'title' => JText::_('价格模块距离标题距离'),
                     'max' => 1000,
                     'min' => 0,
                     'std' => array('md' => '10','sm' => '10','xs' => '10'),
                     'responsive' => true,
                 ),
                 'price_margin_bottom' => array(
                     'type' => 'slider',
                     'title' => JText::_('价格模块距离按钮距离'),
                     'max' => 1000,
                     'min' => 0,
                     'std' => array('md' => '10','sm' => '10','xs' => '10'),
                     'responsive' => true,
                 ),
                 'price_box_bgcolor' => array(
                     'type' => 'color',
                     'title' => '价格模块背景颜色',
                     'desc' => '',
                     'std' => '#ccc',
                 ),
                 'now_price_size' => array(
                     'type' => 'slider',
                     'title' => JText::_('现价字体大小'),
                     'max' => 100,
                     'min' => 12,
                     'std' => '14'
                 ),
                 'now_price_color' => array(
                     'type' => 'color',
                     'title' => '现价字体颜色',
                     'desc' => '',
                     'std' => '#333333',
                 ),
                 'old_price_size' => array(
                     'type' => 'slider',
                     'title' => JText::_('原价字体大小'),
                     'max' => 100,
                     'min' => 12,
                     'std' => '12'
                 ),
                 'old_price_color' => array(
                     'type' => 'color',
                     'title' => '原价字体颜色',
                     'desc' => '',
                     'std' => '#333333',
                 ),
                 'price_margin_left' => array(
                     'type' => 'slider',
                     'title' => JText::_('价格距边框左边距'),
                     'max' => 1000,
                     'min' => 0,
                     'std' => '10',
                 ),

                 'old_now_margin' => array(
                     'type' => 'slider',
                     'title' => JText::_('原价现价的间距'),
                     'max' => 1000,
                     'min' => 0,
                     'std' => '10',
                 ),

                 'show_btn' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示咨询二维码按钮（取值：客户端商品详情中的二维码字段）'),
                    'desc' => JText::_(''),
                    'std' => 1,
                 ),
                 'btn_text' => array(
                     'type' => 'text',
                     'title' => JText::_('按钮文字内容'),
                     'std' => '马上咨询',
                     'depends' => array('show_btn' => 1),
                 ),
                 'btn_bgcolor' => array(
                     'type' => 'color',
                     'title' => JText::_('按钮的背景颜色'),
                     'desc' => JText::_(''),
                     'std' => '#fd8516',
                     'depends' => array('show_btn' => 1),
                 ),
                 'btn_color' => array(
                     'type' => 'color',
                     'title' => JText::_('按钮的文字颜色'),
                     'desc' => JText::_(''),
                     'std' => '#fff',
                     'depends' => array('show_btn' => 1),
                 ),
                 'btn_border_radius' => array(
                     'type' => 'slider',
                     'title' => JText::_('按钮的圆角'),
                     'max' => 100,
                     'min' => 0,
                     'std' => '14',
                     'depends' => array('show_btn' => 1),
                 ),
                // 2023.3.30
                'show_phonebtn' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示一键电话按钮（取值：客户端商品详情中的咨询电话字段）'),
                    'std' => 0,
                ),
                'phonebtn_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮左侧距离'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '0',
                    'depends' => array('show_phonebtn' => 1),
                ),
                'phonebtn_text' => array(
                    'type' => 'text',
                    'title' => JText::_('按钮文字内容'),
                    'std' => '一键电话',
                    'depends' => array('show_phonebtn' => 1),
                ),
                'phonebtn_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮的背景颜色'),
                    'desc' => JText::_(''),
                    'std' => '#fd8516',
                    'depends' => array('show_phonebtn' => 1),
                ),
                'phonebtn_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮的文字颜色'),
                    'desc' => JText::_(''),
                    'std' => '#fff',
                    'depends' => array('show_phonebtn' => 1),
                ),
                'phonebtn_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮的圆角'),
                    'max' => 100,
                    'min' => 0,
                    'std' => '14',
                    'depends' => array('show_phonebtn' => 1),
                ),
                //
                'show_desc_btn' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介按钮'),
                    'desc' => JText::_(''),
                    'std' => 1,
                ),
                'show_desc_color' => array(
                    'type' => 'color',
                    'title' => JText::_('简介的文字颜色'),
                    'desc' => JText::_(''),
                    'std' => '#000',
                    'depends' => array('show_desc_btn' => 1),
                ),
                'show_desc_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介的文字大小'),
                    'desc' => JText::_(''),
                    'max' => 100,
                     'min' => 0,
                     'std' => 14,
                    'depends' => array('show_desc_btn' => 1),
                ),
                'show_desc_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介上边距'),
                    'desc' => JText::_(''),
                    'max' => 200,
                     'min' => 0,
                     'std' => 0,
                    'depends' => array('show_desc_btn' => 1),
                ),
                'show_desc_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介下边距'),
                    'desc' => JText::_(''),
                    'max' => 200,
                     'min' => 0,
                     'std' => 0,
                    'depends' => array('show_desc_btn' => 1),
                ),
                'show_content_btn' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示详情按钮'),
                    'desc' => JText::_(''),
                    'std' => 1,
                ),
                 'content_top' => array(
                     'type' => 'slider',
                     'title' => JText::_('详情内容上边距'),
                     'desc' => JText::_(''),
                     'max' => 1000,
                     'min' => 0,
                     'std' => 50,
                     'depends' => array('show_content_btn' => 1),
                 ),
                 'content_title_color' => array(
                     'type' => 'color',
                     'title' => JText::_('详情标题文字颜色'),
                     'desc' => JText::_(''),
                     'std' => '#333',
                     'depends' => array('show_content_btn' => 1),
                 ),
                 'content_title_fontsize' => array(
                     'type' => 'slider',
                     'title' => JText::_('详情标题文字大小'),
                     'desc' => JText::_(''),
                     'max' => 100,
                     'min' => 0,
                     'std' => 18,
                     'depends' => array('show_content_btn' => 1),
                 ),
             ),
         ),
     )
 );