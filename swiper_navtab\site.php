<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonSwiper_navtab extends JwpagefactoryAddons
{

    public function render()
    {

        $settings = $this->addon->settings;
        $is_swiper_button = (isset($settings->is_swiper_button) && $settings->is_swiper_button) ? $settings->is_swiper_button : 0;
        $is_swiper_pagination = (isset($settings->is_swiper_pagination) && $settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 0;
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $jw_snt_tab_item = (isset($settings->jw_snt_tab_item) && $settings->jw_snt_tab_item) ? $settings->jw_snt_tab_item : array(
            (object)array(
                'types' => 'video',
                'video' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/img/video.mp4',
            ),
            (object)array(
                'types' => 'text',
                'title' => '连续六年蝉联',
                'title_s' => '中国互联网百强',
                'title_ss' => '龙采科技集团',
                'bg_img' => 'https://oss.lcweb01.cn/joomla/20220513/d1dc8b73e761c063b3debab6df9ae58b.jpg',
            ),
        );
        $output = '<style>';
        $output .= $addonId . ' .ind114 img {
            opacity: 0.5;
            transition: 0.5s;
        }';
        $output .= $addonId . ' .ind114:hover img {
            opacity: 1;
            transition: 0.5s;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper {
            height: 100% !important;
        }';
        $output .= $addonId . ' video#video111 {
            width: 100%;
        }';
        $output .= $addonId . ' .swiper-container {
            width: 100%;
            height: 920px;
        }';
        $output .= $addonId . ' .swiper-slide {
            text-align: center;
            font-size: 18px;
            background: #fff;
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            width: 100% !important;
            overflow: hidden;
        }';
        $output .= $addonId . ' .swiper-button-next:after, .swiper-container-rtl .swiper-button-prev:after {
            display: none !important;
        }';
        $output .= $addonId . ' .swiper-button-prev:after, .swiper-container-rtl .swiper-button-next:after {
            display: none !important;
        }';
        $output .= $addonId . ' .swiper-container  .swiper-wrapper .swiper-slide .ind1-a3 {
            width: 100%;
            height: 100%;
            position: relative;
        }';
        $output .= $addonId . ' .swiper-container  .swiper-wrapper .swiper-slide .ind1-a3 img {
            min-height: 100%;
            width: 100%;
        }';
        $output .= $addonId . ' .swiper-container  .swiper-wrapper .swiper-slide .ind1-a5 {
            position: absolute;
            top: 310px;
            left:calc(50% - 1560px/2);
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
            height: 76px;
            margin-bottom: 32px;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6>div {
            height: 76px;
            padding: 0 14px;
            background: #881a16;
            font-size: 3rem;
            line-height: 76px;
            color: #fff0eb;
            float: left;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
            font-size: 4rem;
            line-height: 90px;
            color: #fefaf9;
            font-weight: bold;
            margin-bottom: 36px;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
            width: 54px;
            height: 5px;
            background: #fff;
            margin-bottom: 44px;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
            font-size: 3.4rem;
            line-height: 54px;
            color: #fefaf9;
            font-weight: lighter;
            text-align: left;
        }';
        $output .= $addonId . ' .ind1-a10 {
            position: absolute;
            bottom: 30px;
            right: calc(50% - 1560px/2);
            z-index: 1;
            animation: scroll1 3s linear infinite;
        }';
        $output .= $addonId . ' .ind1-a10>div:nth-child(1) {
            font-size: 14px;
            line-height: 14px;
            color: #f6f0f0;
            font-weight: bold;
            position: relative;
        }';
        $output .= $addonId . ' .ind1-a10>div:nth-child(2) {
            width: 2px;
            height: 34px;
            background: #fff;
            position: absolute;
            bottom: 20px;
            left: calc(50% - 1px);
        }';
        $output .= '@keyframes scroll1 {
            0%{
                transform:translateY(0px);
            }
            50%{
                transform:translateY(-20px);
            }
            100%{
                transform:translateY(0px);
            }
        }';
        $output .= $addonId . ' .swiper-container .swiper-button-prev,'
        . $addonId . ' .swiper-container .swiper-button-next {
            background: none;
            margin: 0;
            padding: 0;
            top: 50%;
            bottom: auto;
        }';
        $output .= $addonId . ' .ind113 {
            left: calc(50% - 1560px/2 + 40px) !important;
        }'
        . $addonId . ' .ind112 {
            left: calc(50% - 1560px/2) !important;
        }';
        $output .= $addonId . ' .swiper-container .ind114 {
            width: 12px;
            height: 22px;
            position: absolute;
            padding: 0;
            margin: 0;
            background: none;
            top: 844px !important;
            right: auto;
            opacity: 1;
        }';
        $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 {
            opacity: 0;
        }';
        $output .= '@media only screen and (max-width:1920px) and (min-width:1600px){';
            $output .= '.swiper-slide video {
                width: 120%!important;
                height: 120%!important;
                position: absolute;
                top: -10%;
            }';
        $output .= '}';
        $output .= '@media only screen and (max-width: 1599px) and (min-width: 1400px) {';
            $output .= $addonId . ' .swiper-container  .swiper-wrapper .swiper-slide .ind1-a5 {
                top: 310px;
                left: calc(50% - 1360px/2);
            }';
            $output .= $addonId . ' .ind113 {
                left: calc(50% - 1360px/2 + 40px) !important;
            }'
                . $addonId . ' .ind112 {
                left: calc(50% - 1360px/2) !important;
            }';
            $output .= '.swiper-slide video {
                width: 100%!important;
                height: 100%!important;
                position: absolute;
                top: 0;
                left: 0;
            }';
            $output .= $addonId . ' .ind1-a10 {
                right: calc(50% - 1360px/2);
            }';
        $output .= '}';
        $output .= '@media only screen and (max-width: 1399px) and (min-width: 1024px) {';
            $output .= $addonId . ' .swiper-container {
                height: 690px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                top: 240px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
                height: 56px;
                margin-bottom: 24px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6>div {
                height: 56px;
                padding: 0 10px;
                font-size: 44px;
                line-height: 56px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
                font-size: 72px;
                line-height: 72px;
                margin-bottom: 28px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
                width: 44px;
                margin-bottom: 32px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
                font-size: 44px;
                line-height: 44px;
            }';
            $output .= $addonId . ' .swiper-container .ind114 {
                top: 620px !important;
            }';
            $output .= '.swiper-slide video {
                width: 100%!important;
                height: 100%!important;
                position: absolute;
                top: 0;
                left: 0;
            }';
        $output .= '}';
        $output .= '@media only screen and (max-width: 1399px) and (min-width: 1200px) {';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                left: calc(50% - 1160px/2);
            }';
            $output .= $addonId . ' .ind113 {
                left: calc(50% - 1160px/2 + 40px) !important;
            }'
            . $addonId . ' .ind112 {
                left: calc(50% - 1160px/2) !important;
            }';
            $output .= $addonId . ' .ind1-a10 {
                right: calc(50% - 1160px/2);
            }';
        $output .= '}';
        $output .= '@media only screen and (max-width: 1199px) and (min-width: 1024px) {';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                left: calc(50% - 960px/2);
            }';
            $output .= $addonId . ' .ind113 {
                left: calc(50% - 960px/2 + 40px) !important;
            }'
            . $addonId . ' .ind112 {
                left: calc(50% - 960px/2) !important;
            }';
            $output .= $addonId . ' .ind1-a10 {
                right: calc(50% - 960px/2);
            }';
        $output .= '}';
        $output .= '@media only screen and (max-width:1023px){';
            $output .= $addonId . ' .swiper-container {
                height: auto !important;
            } ';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide {
                padding-bottom: calc(1334/750*100%);
            } ';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a3 {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                padding: calc(300/1334*100%) calc(30/750*100%) 0;
                z-index: 2;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 img {
                width: 100%;
                height: 100%;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
                margin-bottom: 20px;
                height: 56px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 div {
                font-size: 44px;
                padding: 0 15px;
                width: auto;
                height: 56px;
                line-height: 54px;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
                font-size: 3rem;
                font-family: "siyuan";
                line-height: 100%;
                color: #fefaf9;
                font-weight: bolder;
                margin-bottom: 1rem;
                text-align: left;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
                width: 1.5rem;
                height: .2rem;
                background: #fff;
                margin-bottom: 1rem;
                margin-left: .1rem;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
                font-size: 2rem;
                color: #fefaf9;
                font-weight: lighter;
                line-height: 2rem;
                text-align: left;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 {
                opacity: 1;
                width: 100%;
                padding-right: .3rem;
            }';
            $output .= $addonId . ' .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 img {
                width: calc(490/670*100%);
                display: block;
                margin: 10% 9%;
            }';
            $output .= $addonId . ' .swiper-button-prev {
                opacity: 0.5;
            }';
            $output .= $addonId . ' .swiper-button-next {
                opacity: 0.5;
            }';
        $output .= '}';

        $output .= '@media (max-width: 767px) {';
            $output .= $addonId . ' .swiper-button-prev { 
                opacity: 0;
                right: 10px !important;
            }';
            $output .= $addonId . ' .swiper-button-prev {
                opacity: 0;
                background-image: none;
                right: 10px !important;
                top: 47.5% !important;
            }';
            $output .= $addonId . ' .swiper-button-next { 
                opacity: 0;
                left: 10px;
                background-image: none;
                margin-top: 0px;
            }';
        $output .= '}';
        $output .= '</style>';
        $output .= '<!-- Swiper -->';
        $output .= '<div class="swiper-container ind111">';
            $output .= '<div class="swiper-wrapper">';
            foreach ($jw_snt_tab_item as $key => $item) {
                if ($item->types == 'video') {
                    $output .= '<div class="swiper-slide">';
                    $output .= ' <video src="' . $item->video . '" id="video111" playsinline="true" muted="true" preload="auto" autoplay loop></video>';
                    $output .= '</div>';
                } else {
                    $output .= '<div class="swiper-slide">';
                    $output .= '<div class="ind1-a3 i100"><img src="' . $item->bg_img . '"></div>';
                    $output .= '<div class="ind1-a5" swiper-animate-effect="fadeInLeft" swiper-animate-duration="1.5s" swiper-animate-delay="0.3s">';
                    $output .= '<div class="ind1-a6 clear">';
                    $output .= '<div>' . $item->title . '</div>';
                    $output .= '</div>';
                    $output .= '<div class="ind1-a7">' . $item->title_s . '</div>';
                    $output .= '<div class="ind1-a8"></div>';
                    $output .= '<div class="ind1-a9">' . $item->title_ss . '</div>';
                    $output .= '<div class="ind1-a11"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/z04.png" alt=""></div>';
                    $output .= '</div>';
                    $output .= '</div>';
                }
            }
            $output .= '</div>';
            $output .= '<div class="ind1-a10">';
                $output .= '<div>Scroll</div>';
                $output .= '<div></div>';
            $output .= '</div>';
            $output .= '<!-- Add Arrows -->';
            $output .= '<div class="swiper-button-next ind112 ind114">';
            $output .= '<img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/a23.png">';
            $output .= '</div>';
            $output .= '<div class="swiper-button-prev ind113 ind114">';
            $output .= '<img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/a24.png">';
            $output .= '</div>';
        $output .= '</div>';
        $output .= '<!-- Swiper JS --> ';
        //$output .= '<script src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'js/jquery.min.js"></script> ';
        //$output .= '<script src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'js/swiper.min.js"></script> ';
        $output .= '<script>';
//        $output .= 'var swiper = new Swiper("' . $addonId . ' .swiper-container", {';
//        $output .= 'navigation: {';
//        $output .= 'nextEl: "' . $addonId . ' .swiper-button-next",';
//        $output .= 'prevEl: "' . $addonId . ' .swiper-button-prev",';
//        $output .= '},';
//        $output .= 'autoplay: false,';
//        $output .= '});';
//        $output .= 'swiper.el.onmouseover = function() {';
//        $output .= 'swiper.autoplay.stop();  ';
//        $output .= '}';
//        $output .= 'swiper.el.onmouseout = function() {';
//        $output .= 'var v_len = $("#swiper").find("video").length';
//        $output .= '         for(var i = 0;i<v_len;i++){';
//        $output .= '$("#swiper .swiper-slide").find("video").get(i).pause();';
//        $output .= '}';
//        $output .= 'swiper.autoplay.start();';
//        $output .= '}';
//        $output .= '$("#video111").on("click", function() {';
//        $output .= 'var isPaused = $(this).get(0).paused();';
//        $output .= 'if(isPaused){ ';
//        $output .= '$(this).get(0).play();';
//        $output .= '}else{';
//        $output .= '$(this).get(0).pause(); ';
//        $output .= '}';
//        $output .= '});';
        $output .= '</script>';

        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = '';
        $style_sheet .= array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $css = '';
        return $css;
    }

    public function scripts()
    {
        $js = '';
        $js .= array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;


        $js = 'jQuery(function($){';
        $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: false,
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
                loop: true,
            });';
        $js .= '})';

        return $js;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item;
        // 是否开启切换按钮
        var is_swiper_button = data.is_swiper_button || 0;
        // 上翻页按钮
        var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png";
        // 下翻页按钮
        var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png";
        // 切换按钮宽度
        var swiper_button_width = data.swiper_button_width || 24;
        // 切换按钮高度
        var swiper_button_height = data.swiper_button_height || 24;
        // 切换按钮上边距（百分比）
        var swiper_button_top = data.swiper_button_top || 48;
        // 切换按钮两侧边距（px）
        var swiper_button_left = data.swiper_button_left || 10;
        // 是否开启轮播点
        var is_swiper_pagination = data.is_swiper_pagination || 0;
        // 轮播点宽度
        var swiper_p_width = data.swiper_p_width || 8;
        // 轮播点高度
        var swiper_p_height = data.swiper_p_height || 8;
        // 轮播点间距
        var swiper_p_margin = data.swiper_p_margin || 5;
        // 轮播点颜色
        var swiper_p_color = data.swiper_p_color || "#f0f0f0";
        // 选中轮播点宽度
        var swiper_p_width_a = data.swiper_p_width_a;
        // 选中轮播点高度
        var swiper_p_height_a = data.swiper_p_height_a;
        // 选中轮播点颜色
        var swiper_p_color_a = data.swiper_p_color_a || "#007aff";


        var jw_snt_tab_item = data.jw_snt_tab_item || "";
        #>

        <style>
            {{addonId}} .ind114 img {
                opacity: 0.5;
                transition: 0.5s;
            }
            {{addonId}} .ind114:hover img {
                opacity: 1;
                transition: 0.5s;
            }
            {{addonId}} .swiper-container .swiper-wrapper {
                height: 100% !important;
            }
            {{addonId}} video#video111 {
                width: 100%;
            }
            {{addonId}} .swiper-container {
                width: 100%;
                height: 920px;
            }
            {{addonId}} .swiper-slide {
                text-align: center;
                font-size: 18px;
                background: #fff;
                display: -webkit-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                -webkit-box-pack: center;
                -ms-flex-pack: center;
                -webkit-justify-content: center;
                justify-content: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                -webkit-align-items: center;
                align-items: center;
                width: 100% !important;
                overflow: hidden;
            }
            {{addonId}} .swiper-button-next:after, .swiper-container-rtl .swiper-button-prev:after {
                display: none !important;
            }
            {{addonId}} .swiper-button-prev:after, .swiper-container-rtl .swiper-button-next:after {
                display: none !important;
            }
            {{addonId}} .swiper-container  .swiper-wrapper .swiper-slide .ind1-a3 {
                width: 100%;
                height: 100%;
                position: relative;
            }
            {{addonId}} .swiper-container  .swiper-wrapper .swiper-slide .ind1-a3 img {
                min-height: 100%;
                width: 100%;
            }
            {{addonId}} .swiper-container  .swiper-wrapper .swiper-slide .ind1-a5 {
                position: absolute;
                top: 310px;
                left:calc(50% - 1560px/2);
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
                height: 76px;
                margin-bottom: 32px;
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6>div {
                height: 76px;
                padding: 0 14px;
                background: #881a16;
                font-size: 3rem;
                line-height: 76px;
                color: #fff0eb;
                float: left;
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
                font-size: 4rem;
                line-height: 90px;
                color: #fefaf9;
                font-weight: bold;
                margin-bottom: 36px;
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
                width: 54px;
                height: 5px;
                background: #fff;
                margin-bottom: 44px;
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
                font-size: 3.4rem;
                line-height: 54px;
                color: #fefaf9;
                font-weight: lighter;
                text-align: left;
            }
            {{addonId}} .ind1-a10 {
                position: absolute;
                bottom: 30px;
                right: calc(50% - 1560px/2);
                z-index: 1;
                animation: scroll1 3s linear infinite;
            }
            {{addonId}} .ind1-a10>div:nth-child(1) {
                font-size: 14px;
                line-height: 14px;
                color: #f6f0f0;
                font-weight: bold;
                position: relative;
            }
            {{addonId}} .ind1-a10>div:nth-child(2) {
                width: 2px;
                height: 34px;
                background: #fff;
                position: absolute;
                bottom: 20px;
                left: calc(50% - 1px);
            }
            @keyframes scroll1 {
                0%{
                    transform:translateY(0px);
                }
                50%{
                    transform:translateY(-20px);
                }
                100%{
                    transform:translateY(0px);
                }
            }
            {{addonId}} .swiper-container .swiper-button-prev,{{addonId}} .swiper-container .swiper-button-next {
                background: none;
                margin: 0;
                padding: 0;
                top: 50%;
                bottom: auto;
            }
            {{addonId}} .ind113 {
                left: calc(50% - 1560px/2 + 40px) !important;
            }
            {{addonId}} .ind112 {
                left: calc(50% - 1560px/2) !important;
            }
            {{addonId}} .swiper-container .ind114 {
                width: 12px;
                height: 22px;
                position: absolute;
                padding: 0;
                margin: 0;
                background: none;
                top: 844px !important;
                right: auto;
                opacity: 1;
            }
            {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 {
                opacity: 0;
            }
            @media only screen and (max-width:1920px) and (min-width:1600px){
                {{addonId}} .swiper-slide video {
                    width: 120%!important;
                    height: 120%!important;
                    position: absolute;
                    top: -10%;
                }
            }
            @media only screen and (max-width: 1599px) and (min-width: 1400px) {
                {{addonId}} .swiper-container  .swiper-wrapper .swiper-slide .ind1-a5 {
                    top: 310px;
                    left: calc(50% - 1360px/2);
                }
                {{addonId}} .ind113 {
                    left: calc(50% - 1360px/2 + 40px) !important;
                }
                {{addonId}} .ind112 {
                    left: calc(50% - 1360px/2) !important;
                }
                {{addonId}} .swiper-slide video {
                    width: 100%!important;
                    height: 100%!important;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                {{addonId}} .ind1-a10 {
                    right: calc(50% - 1360px/2);
                }
            }
            @media only screen and (max-width: 1399px) and (min-width: 1024px) {
                {{addonId}} .swiper-container {
                    height: 690px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                    top: 240px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
                    height: 56px;
                    margin-bottom: 24px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6>div {
                    height: 56px;
                    padding: 0 10px;
                    font-size: 44px;
                    line-height: 56px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
                    font-size: 72px;
                    line-height: 72px;
                    margin-bottom: 28px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
                    width: 44px;
                    margin-bottom: 32px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
                    font-size: 44px;
                    line-height: 44px;
                }
                {{addonId}} .swiper-container .ind114 {
                    top: 620px !important;
                }
                {{addonId}} .swiper-slide video {
                    width: 100%!important;
                    height: 100%!important;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
            }
            @media only screen and (max-width: 1399px) and (min-width: 1200px) {
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                    left: calc(50% - 1160px/2);
                }
                {{addonId}} .ind113 {
                    left: calc(50% - 1160px/2 + 40px) !important;
                }
                {{addonId}} .ind112 {
                    left: calc(50% - 1160px/2) !important;
                }
                {{addonId}} .ind1-a10 {
                    right: calc(50% - 1160px/2);
                }
            }
            @media only screen and (max-width: 1199px) and (min-width: 1024px) {
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                    left: calc(50% - 960px/2);
                }
                {{addonId}} .ind113 {
                    left: calc(50% - 960px/2 + 40px) !important;
                }
                {{addonId}} .ind112 {
                    left: calc(50% - 960px/2) !important;
                }
                {{addonId}} .ind1-a10 {
                    right: calc(50% - 960px/2);
                }
            }
            @media only screen and (max-width:1023px){
                {{addonId}} .swiper-container {
                    height: auto !important;
                } 
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide {
                    padding-bottom: calc(1334/750*100%);
                } 
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a3 {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    padding: calc(300/1334*100%) calc(30/750*100%) 0;
                    z-index: 2;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a5 img {
                    width: 100%;
                    height: 100%;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 {
                    margin-bottom: 20px;
                    height: 56px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a6 div {
                    font-size: 44px;
                    padding: 0 15px;
                    width: auto;
                    height: 56px;
                    line-height: 54px;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a7 {
                    font-size: 3rem;
                    font-family: "siyuan";
                    line-height: 100%;
                    color: #fefaf9;
                    font-weight: bolder;
                    margin-bottom: 1rem;
                    text-align: left;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a8 {
                    width: 1.5rem;
                    height: .2rem;
                    background: #fff;
                    margin-bottom: 1rem;
                    margin-left: .1rem;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a9 {
                    font-size: 2rem;
                    color: #fefaf9;
                    font-weight: lighter;
                    line-height: 2rem;
                    text-align: left;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 {
                    opacity: 1;
                    width: 100%;
                    padding-right: .3rem;
                }
                {{addonId}} .swiper-container .swiper-wrapper .swiper-slide .ind1-a11 img {
                    width: calc(490/670*100%);
                    display: block;
                    margin: 10% 9%;
                }
                {{addonId}} .swiper-button-prev {
                    opacity: 0.5;
                }
                {{addonId}} .swiper-button-next {
                    opacity: 0.5;
                }
            }

            @media (max-width: 767px) {
                {{addonId}} .swiper-button-prev { 
                    opacity: 0;
                    right: 10px !important;
                }
                {{addonId}} .swiper-button-prev {
                    opacity: 0;
                    background-image: none;
                    right: 10px !important;
                    top: 47.5% !important;
                }
                {{addonId}} .swiper-button-next { 
                    opacity: 0;
                    left: 10px;
                    background-image: none;
                    margin-top: 0px;
                }
            }
        </style>


		    <div class="swiper-container ind111">
                <div class="swiper-wrapper">

                    <# _.each(data.jw_snt_tab_item, function(item, key){    
                    #>
                        <# if (item.types == "video") { #>
                            <div class="swiper-slide">
                                <video src="{{item.video}}" id="video111" playsinline="true" muted="true" preload="auto" autoplay loop></video>
                            </div>
                        <# } else { #>
                            <div class="swiper-slide">
                                <div class="ind1-a3 i100"><img src=\'{{item.bg_img}}\'></div>
                                <div class="ind1-a5" swiper-animate-effect="fadeInLeft" swiper-animate-duration="1.5s" swiper-animate-delay="0.3s">
                                    <div class="ind1-a6 clear">
                                    <div>{{item.title}}</div>
                                </div>
                                <div class="ind1-a7">{{item.title_s}}</div>
                                <div class="ind1-a8"></div>
                                <div class="ind1-a9">{{item.title_ss}}</div>
                                <div class="ind1-a11"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/z04.png" alt=""></div>
                                </div>
                            </div>
                        <# } #>
                    <# }); #>
                </div>
                <div class="ind1-a10">
                    <div>Scroll</div>
                    <div></div>
                </div>
                
                <div class="swiper-button-next ind112 ind114">
                    <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/a23.png">
                </div>
                <div class="swiper-button-prev ind113 ind114">
                    <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_navtab/assets/' . 'img/a24.png">
                </div>
            </div>
		';

        return $output;
    }

}
