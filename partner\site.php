<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonPartner extends JwpagefactoryAddons
{

    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

				$adv_typeid = (isset($settings->adv_typeid) && $settings->adv_typeid) ? $settings->adv_typeid : '0';
				$img_zt = (isset($settings->img_zt) && $settings->img_zt) ? $settings->img_zt : 'qx';
		$dizz = (isset($settings->dizz) && $settings->dizz) ? $settings->dizz : '0';
		$sj_zong = (isset($settings->sj_zong) && $settings->sj_zong) ? $settings->sj_zong : '5';

		// 获取广告列表
		$hzhb_list = JwPageFactoryBase::getAdvLists($site_id, $company_id,$adv_typeid);

		if (isset($settings->img_colm) && $settings->img_colm) {
		    if (is_object($settings->img_colm)) {
		        $img_colm_md = $settings->img_colm->md;
		        $img_colm_sm = $settings->img_colm->sm;
		        $img_colm_xs = $settings->img_colm->xs;
		    } else {
		        $img_colm_md = $settings->img_colm;
		        $img_colm_sm = $settings->img_colm_sm;
		        $img_colm_xs = $settings->img_colm_xs;
		    }
		} else {
		    $img_colm_md = '7';
		    $img_colm_sm = '7';
		    $img_colm_xs = '3';
		}
		$output = '';
		$output .= '
			<style>';
				if($dizz!=1){

					$output .= ''.$addon_id.' .ind6-a4:nth-child(1) {
						animation: ind666 linear 4s infinite;
						animation-delay: -1.583629850744975s;
					}';
				}
			    $output .= ''.$addon_id.' .i300 {
			        overflow: hidden;
			    }
			    '.$addon_id.' .i300>img {
			        width: 100%;
			        height: 100%;
					object-fit: contain;
			    }


					';
					if($dizz==1){
						$output .= '
							'.$addon_id.' .ind6-a1:after {
							    width: 100%;
							    height: 45%;
							    position: absolute;
							    right: 0;
							    bottom: 0;
							    content: "";
							    background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
							    z-index: 1;
							    pointer-events: none;
							}
						';
					}

			        $output .= $addon_id.' .ind6-a1 {
			            width: 100%;';
			            if($dizz!=1){
				            $output .='padding-bottom: 80px;';
				        }
			            $output .='
			            position: relative;
			            overflow: hidden;
			        }
			        '.$addon_id.' .ind6-a2 {
			            width: 100%;
			            padding: 0;
			            position: relative;
			            perspective: 1000;
			            -webkit-perspective: 1000;
			        }
			        '.$addon_id.' .ind6-a1::after {
			            width: 100%;
			            position: absolute;
			            right: 0;
			            bottom: 0;
			            content: "";
			            background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
			            z-index: 1;
			            pointer-events: none;
			        }
			        '.$addon_id.' .ind6-a3 {
			            display: flex;
			            position: relative;
			            flex-wrap: wrap;';
			            if($img_zt=='qx'){
			            	$output .= '
			            		transform: perspective(1000px) rotateX(52deg);
			            		transform-style: preserve-3d;
			            	';
			            }
			            $output .= '
			        }
			        '.$addon_id.' .ind6-a4 {
			            width: calc( 100% / '.$img_colm_md.' - (1.5% * ('.$img_colm_md.' - 1) / '.$img_colm_md.') - 2px);
			            margin-right: 1.5%;
			            // height: 0;
			            // padding-bottom: calc(13%/245*130);
						';
						if($dizz==1){
							$output .='margin-bottom: 12px;';
						}else{
							$output .='margin-bottom: 20px;';
						}

			            $output .='background-color: #fff;
			            border: 1px solid #eee;
			            border-radius: 5px;
			            overflow: hidden;
			            -webkit-transition: all 0.5s;
			        }
			        '.$addon_id.' .ind6-a4:nth-child('.$img_colm_md.'n) {
			            margin-right: 0;
			        }
			        '.$addon_id.' .ind6-a5 {
			            background: center center no-repeat;
			            background-size: cover;
						height:100%;
			        }
			    @keyframes ind666{
			        0%{transform: translateZ(0px);}
			        50%{transform: translateZ(20px);}
			        100%{transform: translateZ(0px);}
			    }';

					foreach ($hzhb_list as $key => $value) {
						$output .= $addon_id.' .ind6-a4:nth-child(' . ($key + 1) .'){
							animation: ind666 linear 4s infinite;
							animation-delay: -'.rand(1,2).'.'.rand(1,100000000000).'s;
						}';
					}

                $output .= $addon_id.' .hzsj{display:none;}
			    /*手机*/
			    @media only screen and (max-width: 1023px){';
			    	if($dizz==1){
						$output .=
							$addon_id.' .ind6-a1:after {
							    width: 100%;
							    height: 45%;
							    position: absolute;
							    right: 0;
							    bottom: 0;
							    content: "";
							    background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
							    z-index: 1;
							    pointer-events: none;
							}

						';
					}

					if($img_zt=='zc'){
						$output .= $addon_id.' .hzpc{display:blcok;}
							'.$addon_id.' .hzsj{display:none;}

							'.$addon_id.' .ind6-a4 {
					            width: calc( 100% / '.$img_colm_xs.' - (1.5% * ('.$img_colm_xs.' - 1) / '.$img_colm_xs.') - 2px);
					            margin-right: 1.5%;
					            margin-bottom: 20px;
					            background-color: #fff;
					            border: 1px solid #eee;
					            overflow: hidden;
					            -webkit-transition: all 0.5s;
					        }
							'.$addon_id.' .ind6-a4:nth-child('.$img_colm_xs.'n) {
								margin-right: 0!important;
							}
							'.$addon_id.' .ind6-a4:nth-child('.$img_colm_md.'n) {
								margin-right: 1.5%!important;
							}
					        '.$addon_id.' .ind6-a3 {
							    display: flex;
							    position: relative;
							    flex-wrap: wrap;
							}

				        ';

					}else{
						$output .= $addon_id.' .hzpc{display:none;}
						'.$addon_id.' .hzsj{display:block;background:#fff;}';
					}


			        $output .=$addon_id.' .p-index-a5 {
			            position: relative;
			            width: 100%;
			            padding-top: 1rem;
			            padding-bottom: 0.75rem;
			        }
			        '.$addon_id.' .p-index-a5-line {
			            width: 100%;
			            position: relative;
			            overflow: hidden;
			        }
			        '.$addon_id.' .p-index-a5-line1 {
			            width: 100%;
			            margin: 0 auto 0.1rem;
			            overflow: hidden;
			            position: relative;
			            height: 70px;
			        }
			        '.$addon_id.' .p-index-a5-line img {
			            /*width: 2.88rem;*/
			            height: auto;
			            /*margin-right: .52rem;*/
			            /*display: inline-block;*/
			            /*float:left;*/
			            margin: 1px;
			            float: left;
			            width: 100%;
			            /*width:98px;*/
			        }
			        '.$addon_id.' .p-index-a5-line li {
			            width: 132px;
			            height: 70px;
			            float: left;
			            list-style: none;
			            overflow:hidden;
			        }
			        '.$addon_id.' .p-index-a5-line2 {
			            /*display: flex;*/
			            width: 9999px;
			            left: 0px;
			            right: 0px;
			            position: absolute;
			            /*animation:move 10s linear infinite;*/
			            /*-webkit-animation:move 10s linear infinite;*/
			        }
			        '.$addon_id.' .p-index-a5-line3 {
			            width: 100%;
			            margin-bottom: .1rem;
			            height: 70px;
			        }

			        '.$addon_id.' .p-index-a5-line3:before {
			            content: "";
			            display: inline-block;
			            width: 0.6rem;
			            height: 100%;
			            background: linear-gradient(to right, rgba(255, 255, 255, .95), rgba(255, 255, 255, 0));
			            position: absolute;
			            top: 0;
			            left: 0;
			            z-index: 6;
			        }

			        '.$addon_id.' .p-index-a5-line3:after {
			            content: "";
			            display: inline-block;
			            width: .6rem;
			            height: 100%;
			            background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, .95));
			            position: absolute;
			            top: 0;
			            right: 0;
			            z-index: 6;
			        }

			        @keyframes move {
			            0% {
			                transform: translateX(0px);
			            }
			            100% {
			                transform: translateX(-450px);
			            }
			        }
			    }

			</style>
		';


        if (!count($hzhb_list)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            return $output;
        }

        if($hzhb_list){
        	$zond=count($hzhb_list);
        	$suz=[];
	        $i=0;

					if($img_zt == 'scroll'){
                $row_md = ceil($zond / $img_colm_md);
                $row_sm = ceil($zond / $img_colm_sm);
                $row_xs = ceil($zond / $img_colm_xs);
                $output .= '<div class="scroll-container pc">';
									$output.=$this->getItemHtmlStr($row_md, $img_colm_md, $hzhb_list);
                $output.='</div>';
                $output .= '<div class="scroll-container labtop">';
									$output.=$this->getItemHtmlStr($row_sm, $img_colm_sm, $hzhb_list);
                $output.='</div>';
								$output .= '<div class="scroll-container phone">';
									$output.=$this->getItemHtmlStr($row_xs, $img_colm_xs, $hzhb_list);
                $output.='</div>';
        	}else{
                foreach ($hzhb_list as $keys => $values) {
                    if($i<3){
                        $suz[$i][]=$values;
                        if(($keys+1)%$sj_zong==0){
                            $i=$i+1;
                        }
                    }

                }
                $output .= '
                <div class=" hzpc ind6-a1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                            <div class="ind6-a2">
                                    <div class="ind6-a3 clear">';
                                        foreach ($hzhb_list as $key => $value) {
                                            $output .= '
                                    <div class="ind6-a4">
                                                            <div class="ind6-a5 i300">
                                                                <img src="'.$value->images.'">
                                                            </div>
                                                    </div>
                                            ';
                                        }

                                    $output .= '</div>
                            </div>
                    </div>

                    <div class="hzsj p-index-a5">
                            <div class="p-index-a5-line">';
                                foreach ($suz as $key1 => $value1) {
                                        $output .= '<div class="';if($key1==0){ $output .= 'p-index-a5-line1'; }elseif($key1==1){ $output .= 'p-index-a5-line3'; }else{ $output .= 'p-index-a5-line1'; } $output .= '" id="roll'.$key1.'">
                                                <ul class="p-index-a5-line2 clear" style=" '; if($key1==0){ $output .= 'width: 2376px; left: -939px;';}elseif($key1==1){ $output .= 'width: 2376px; left: -939px;'; }else{  $output .= 'width: 2376px; left: -939px;';} $output .= '">';
                                    foreach ($value1 as $key2 => $value2) {
                                                                    $output .= '<li><img src="'.$value2->images.'" alt="" oncontextmenu="return false;"></li>';
                                    }
                                                        $output .= '</ul>
                                        </div>';
                                }
                        $output .= '</div>
                </div>
                <script type="text/javascript">

                    var oDiv = document.getElementById("roll0");
                        var oUl = oDiv.getElementsByTagName("ul")[0];
                        var aLi = oUl.getElementsByTagName("li");
                        var str = oUl.innerHTML + oUl.innerHTML;
                        oUl.innerHTML = str;
                        oUl.style.width = aLi[0].offsetWidth * aLi.length + "px";
                        var speed = -1;
                        timer = setInterval(move0, 30);

                        var oDiv1 = document.getElementById("roll1");
                        var oUl1 = oDiv1.getElementsByTagName("ul")[0];
                        var aLi1 = oUl1.getElementsByTagName("li");
                        var str1 = oUl1.innerHTML + oUl1.innerHTML;
                        oUl1.innerHTML = str1;
                        oUl1.style.width = aLi1[0].offsetWidth * aLi1.length + "px";
                        var speed1 = -1;
                        timer1 = setInterval(move1, 30);

                        var oDiv2 = document.getElementById("roll2");
                        var oUl2 = oDiv2.getElementsByTagName("ul")[0];
                        var aLi12 = oUl2.getElementsByTagName("li");
                        var str2 = oUl2.innerHTML + oUl2.innerHTML;
                        oUl2.innerHTML = str2;
                        oUl2.style.width = aLi12[0].offsetWidth * aLi12.length + "px";
                        var speed2 = -1;
                        timer2 = setInterval(move2, 30);

                        function move0() {
                                oUl.style.left = oUl.offsetLeft + speed + "px";
                                if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                                        oUl.style.left = "0px";
                                }
                                else if (oUl.offsetLeft >= 0) {
                                        oUl.style.left = "-" + oUl.offsetWidth / 2 + "px";
                                }
                        }
                        function move1() {
                                oUl1.style.left = oUl1.offsetLeft + speed1 + "px";
                                // ul向作滚动，所以是负数，即比较的时候用负数进行比较
                                //这里的比较是：在滚动到一半的时候其实就是重复的开始，将所有内容拉到最开始，避免出现空白
                                if (oUl1.offsetLeft < -oUl1.offsetWidth / 2) {
                                        oUl1.style.left = "0px";
                                }
                                //当left=0，右边会出现百边，将内容拉到自身宽度的一半
                                else if (oUl1.offsetLeft >= 0) {
                                        oUl1.style.left = "-" + oUl1.offsetWidth / 2 + "px";
                                }
                        }
                        function move2() {
                                oUl2.style.left = oUl2.offsetLeft + speed2 + "px";
                                // ul向作滚动，所以是负数，即比较的时候用负数进行比较
                                //这里的比较是：在滚动到一半的时候其实就是重复的开始，将所有内容拉到最开始，避免出现空白
                                if (oUl2.offsetLeft < -oUl2.offsetWidth / 2) {
                                        oUl2.style.left = "0px";
                                }
                                //当left=0，右边会出现百边，将内容拉到自身宽度的一半
                                else if (oUl2.offsetLeft >= 0) {
                                        oUl2.style.left = "-" + oUl2.offsetWidth / 2 + "px";
                                }
                        }
                </script>

                ';
            }
        }

        return $output;
    }

		function getItemHtmlStr($row, $img_colm, $list){
			$output = '';
			if($row){
				for($i=0;$i<$row;$i++){
					$direction = $i % 2 === 0 ? '' : 'reverse';
					$output.='<div class="wall" data-direction="'.$direction.'">';
					for($j=$i*$img_colm;$j<$i*$img_colm + $img_colm;$j++){
							$output.='<div class="wall-item">
								<img src="'.$list[$j]->images.'" alt="">
							</div>';
					}
					$output.='</div>';
				}
			}
			return $output;
		}

    public function scripts()
    {
    }

    public  function js()
		{
			$output='';
    }

    public function css()
    {
			$addonId = $this->addon->id;
			$addon_id = '#jwpf-addon-' . $this->addon->id;
			$settings = $this->addon->settings;

			$img_zt = (isset($settings->img_zt) && $settings->img_zt) ? $settings->img_zt : 'qx';

			$output = '';
			$img_colm = $this->getResponsiveValues('img_colm', ['md' => '7', 'sm' => '7', 'xs' => '3']);
			$img_width_scroll = $this->getResponsiveValues('img_width_scroll', ['md' => '160', 'sm' => '160', 'xs' => '160']);
			$img_height_scroll = $this->getResponsiveValues('img_height_scroll', ['md' => '90', 'sm' => '90', 'xs' => '90']);
			$img_time_scroll = $this->getResponsiveValues('img_time_scroll', ['md' => '16', 'sm' => '16', 'xs' => '10']);
			$img_row_gap_scroll = $this->getResponsiveValues('img_row_gap_scroll', ['md' => '30', 'sm' => '30', 'xs' => '20']);
			$img_mask_scroll = isset($settings->img_mask_scroll) ? $settings->img_mask_scroll : 1;
			$img_show_type_scroll = isset($settings->img_show_type_scroll) ? $settings->img_show_type_scroll : 'cover';

			if($img_zt == 'scroll'){
				$output.=$addon_id .' * {
						margin: 0;
						padding: 0;
						box-sizing: border-box;
				}
				{{addonId}} {
						--wall-item-height: ' . $img_height_scroll['md'] . 'px;
						--wall-item-width: ' . $img_width_scroll['md'] . 'px;
						--wall-item-number: ' . $img_colm['md'] . ';
						--duration: ' . $img_time_scroll['md'] . 's;
						--row-gap: ' . $img_row_gap_scroll['md'] . 'px;
				}
				{{addonId}} .scroll-container.pc{
						display: block;
				}
				{{addonId}} .scroll-container.labtop,
				{{addonId}} .scroll-container.phone{
						display: none;
				}
				@media (max-width: 992px)  and (min-width: 768px) {
						{{addonId}} {
								--wall-item-number: ' . $img_colm['sm'] . ';
								--wall-item-height: ' . $img_height_scroll['sm'] . 'px;
								--wall-item-width: ' . $img_width_scroll['sm'] . 'px;
								--duration: ' . $img_time_scroll['sm'] . 's;
								--row-gap: ' . $img_row_gap_scroll['sm'] . 'px;
						}
						{{addonId}} .scroll-container.labtop{
								display: block;
						}
						{{addonId}} .scroll-container.pc,
						{{addonId}} .scroll-container.phone{
								display: none;
						}
				}
				@media (max-width: 767px) {
						{{addonId}} {
								--wall-item-number: ' . $img_colm['xs'] . ';
								--wall-item-height: ' . $img_height_scroll['xs'] . 'px;
								--wall-item-width: ' . $img_width_scroll['xs'] . 'px;
								--duration: ' . $img_time_scroll['xs'] . 's;
								--row-gap: ' . $img_row_gap_scroll['xs'] . 'px;
						}
						{{addonId}} .scroll-container.phone{
								display: block;
						}
						{{addonId}} .scroll-container.pc,
						{{addonId}} .scroll-container.labtop{
								display: none;
						}
				}
				{{addonId}} .scroll-container{
						overflow: hidden;
				}
				@keyframes scroll_partner {
						to {
								transform: translateX(calc(var(--wall-item-width) * -1));
						}
				}
				{{addonId}} .wall {
								margin: var(--row-gap) auto;
								height: var(--wall-item-height);
								width: 100%;
								position: relative;';
								if($img_mask_scroll){
										$output.='mask-image: linear-gradient(90deg, hsl(0 0% 0% / 0),
										hsl(0 0% 0% / 1) 20%,
										hsl(0 0% 0% / 1) 80%,
										hsl(0 0% 0% / 0));';
								}
				$output.='}
				{{addonId}} .wall .wall-item {
						position: absolute;
						top: 0;
						left: 0;
						transform: translateX(calc(var(--wall-item-width) * var(--wall-item-number)));
						height: var(--wall-item-height);
						width: var(--wall-item-width);
						background: #fff;
						animation: scroll_partner var(--duration) linear infinite;
						cursor: pointer;
				}
				{{addonId}} .wall .wall-item img{
					width: 100%;
					height: 100%;
					object-fit: ' . $img_show_type_scroll . ';
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item {
						animation-direction: reverse;
						background: #fff;
				}
				{{addonId}} .wall .wall-item:nth-child(1) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 1) * -1);
						z-index: 9;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(1) {
						z-index: 1;
				}
				{{addonId}} .wall .wall-item:nth-child(2) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 2) * -1);
						z-index: 8;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(2) {
						z-index: 2;
				}
				{{addonId}} .wall .wall-item:nth-child(3) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 3) * -1);
						z-index: 7;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(3) {
						z-index: 3;
				}
				{{addonId}} .wall .wall-item:nth-child(4) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 4) * -1);
						z-index: 6;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(4) {
						z-index: 4;
				}
				{{addonId}} .wall .wall-item:nth-child(5) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 5) * -1);
						z-index: 5;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(5) {
						z-index: 5;
				}
				{{addonId}} .wall .wall-item:nth-child(6) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 6) * -1);
						z-index: 4;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(6) {
						z-index: 6;
				}
				{{addonId}} .wall .wall-item:nth-child(7) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 7) * -1);
						z-index: 3;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(7) {
						z-index: 7;
				}
				{{addonId}} .wall .wall-item:nth-child(8) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 8) * -1);
						z-index: 2;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(8) {
						z-index: 8;
				}
				{{addonId}} .wall .wall-item:nth-child(9) {
						animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 9) * -1);
						z-index: 1;
				}
				{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(9) {
						z-index: 9;
				}
				{{addonId}} .wall:has(.wall-item:hover) .wall-item {
						animation-play-state: paused;
				}';
			}

			return $output;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
				var addonId = "#jwpf-addon-"+data.id;
				var img_zt = data.img_zt || "qx";
				#>
				<# if (img_zt === "scroll"){ #>
					<#
						var img_height_scroll = {md: 90, sm: 90, xs: 90};
						if(_.isObject(data.img_height_scroll)){
							img_height_scroll = data.img_height_scroll;
						}else{
							img_height_scroll = {md: data.img_height_scroll, sm: data.img_height_scroll, xs: data.img_height_scroll};
						}
						var img_width_scroll = {md: 160, sm: 160, xs: 160};
						if(_.isObject(data.img_width_scroll)){
							img_width_scroll = data.img_width_scroll;
						}else{
							img_width_scroll = {md: data.img_width_scroll, sm: data.img_width_scroll, xs: data.img_width_scroll};
						}
						var img_show_type_scroll = data.img_show_type_scroll || "cover";
						var img_mask_scroll = data.img_mask_scroll;
						var img_time_scroll = {md: 16, sm: 16, xs: 10};
						if(_.isObject(data.img_time_scroll)){
							img_time_scroll = data.img_time_scroll;
						}else{
							img_time_scroll = {md: data.img_time_scroll, sm: data.img_time_scroll, xs: data.img_time_scroll};
						}
						var img_row_gap_scroll = {md: 30, sm: 30, xs: 20};
						if(_.isObject(data.img_row_gap_scroll)){
							img_row_gap_scroll = data.img_row_gap_scroll;
						}else{
							img_row_gap_scroll = {md: data.img_row_gap_scroll, sm: data.img_row_gap_scroll, xs: data.img_row_gap_scroll};
						}
						var img_colm = {md: 7, sm: 7, xs: 3};
						if(_.isObject(data.img_colm)){
							img_colm = data.img_colm;
						}else{
							img_colm = {md: data.img_colm, sm: data.img_colm, xs: data.img_colm};
						}
					#>
					<style>
						{{addonId}} * {
							margin: 0;
							padding: 0;
							box-sizing: border-box;
						}
						{{addonId}} {
								--wall-item-height: {{img_height_scroll.md}}px;
								--wall-item-width: {{img_width_scroll.md}}px;
								--wall-item-number: {{img_colm.md}};
								--duration: {{img_time_scroll.md}}s;
								--row-gap: {{img_row_gap_scroll.md}}px;
						}
						{{addonId}} .scroll-container.pc{
								display: block;
						}
						{{addonId}} .scroll-container.labtop,
						{{addonId}} .scroll-container.phone{
								display: none;
						}
						@media (max-width: 992px)  and (min-width: 768px) {
								{{addonId}} {
										--wall-item-number: {{img_colm.sm}};
										--wall-item-height: {{img_height_scroll.sm}}px;
										--wall-item-width: {{img_width_scroll.sm}}px;
										--duration: {{img_time_scroll.sm}}s;
										--row-gap: {{img_row_gap_scroll.sm}}px;
								}
								{{addonId}} .scroll-container.labtop{
										display: block;
								}
								{{addonId}} .scroll-container.pc,
								{{addonId}} .scroll-container.phone{
										display: none;
								}
						}
						@media (max-width: 767px) {
								{{addonId}} {
										--wall-item-number: {{img_colm.xs}};
										--wall-item-height: {{img_height_scroll.xs}}px;
										--wall-item-width: {{img_width_scroll.xs}}px;
										--duration: {{img_time_scroll.xs}}s;
										--row-gap: {{img_row_gap_scroll.xs}}px;
								}
								{{addonId}} .scroll-container.phone{
										display: block;
								}
								{{addonId}} .scroll-container.pc,
								{{addonId}} .scroll-container.labtop{
										display: none;
								}
						}
						{{addonId}} .scroll-container{
								overflow: hidden;
						}
						@keyframes scroll_partner {
								to {
										transform: translateX(calc(var(--wall-item-width) * -1));
								}
						}
						{{addonId}} .wall {
								margin: var(--row-gap) auto;
								height: var(--wall-item-height);
								width: 100%;
								position: relative;
								<# if(img_mask_scroll){ #>
										mask-image: linear-gradient(90deg, hsl(0 0% 0% / 0),
										hsl(0 0% 0% / 1) 20%,
										hsl(0 0% 0% / 1) 80%,
										hsl(0 0% 0% / 0));
								<# } #>
						}
						{{addonId}} .wall .wall-item {
								position: absolute;
								top: 0;
								left: 0;
								transform: translateX(calc(var(--wall-item-width) * var(--wall-item-number)));
								height: var(--wall-item-height);
								width: var(--wall-item-width);
								background: #fff;
								animation: scroll_partner var(--duration) linear infinite;
								cursor: pointer;
						}
						{{addonId}} .wall .wall-item img{
							width: 100%;
							height: 100%;
							object-fit: {{img_show_type_scroll}};
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item {
								animation-direction: reverse;
								background: #fff;
						}
						{{addonId}} .wall .wall-item:nth-child(1) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 1) * -1);
								z-index: 9;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(1) {
								z-index: 1;
						}
						{{addonId}} .wall .wall-item:nth-child(2) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 2) * -1);
								z-index: 8;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(2) {
								z-index: 2;
						}
						{{addonId}} .wall .wall-item:nth-child(3) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 3) * -1);
								z-index: 7;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(3) {
								z-index: 3;
						}
						{{addonId}} .wall .wall-item:nth-child(4) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 4) * -1);
								z-index: 6;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(4) {
								z-index: 4;
						}
						{{addonId}} .wall .wall-item:nth-child(5) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 5) * -1);
								z-index: 5;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(5) {
								z-index: 5;
						}
						{{addonId}} .wall .wall-item:nth-child(6) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 6) * -1);
								z-index: 4;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(6) {
								z-index: 6;
						}
						{{addonId}} .wall .wall-item:nth-child(7) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 7) * -1);
								z-index: 3;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(7) {
								z-index: 7;
						}
						{{addonId}} .wall .wall-item:nth-child(8) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 8) * -1);
								z-index: 2;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(8) {
								z-index: 8;
						}
						{{addonId}} .wall .wall-item:nth-child(9) {
								animation-delay: calc((var(--duration) / var(--wall-item-number)) * (var(--wall-item-number) - 9) * -1);
								z-index: 1;
						}
						{{addonId}} .wall[data-direction="reverse"] .wall-item:nth-child(9) {
								z-index: 9;
						}
						{{addonId}} .wall:has(.wall-item:hover) .wall-item {
								animation-play-state: paused;
						}
					</style>
					<# for(let k=0; k<3; k++){ #>
						<#
							let type = "pc";
							if(k == 1){
								type = "labtop";
							}else if(k == 2){
								type = "phone";
							}
						#>
						<div class="scroll-container {{type}}">
							<# for(let j=0; j<3; j++){ #>
								<# var reverse = j % 2 == 0 ? "reverse" : ""; #>
								<div class="wall" data-direction="{{reverse}}">
									<# for(let i=0; i<img_colm.md; i++){ #>
										<div class="wall-item">
												<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="">
										</div>
									<# } #>
								</div>
							<# } #>
						</div>
					<# } #>
				<# } else { #>
					<style>
						{{addonId}} .ind6-a4:nth-child(1) {
									animation: ind666 linear 4s infinite;
									animation-delay: -1.583629850744975s;
							}
							{{addonId}} .i300 {
									overflow: hidden;
							}
							{{addonId}} .i300>img {
									width: 100%;
									height: 100%;
							object-fit: contain;
							}

							/*@media only screen and (max-width: 1920px) and (min-width: 1024px){*/


									{{addonId}} .ind6-a1 {
											width: 100%;
											padding-bottom: 80px;
											position: relative;
											overflow: hidden;
									}
									{{addonId}} .ind6-a2 {
											width: 100%;
											padding: 0;
											position: relative;
											perspective: 1000;
											-webkit-perspective: 1000;
									}
									{{addonId}} .ind6-a1::after {
											width: 100%;
											position: absolute;
											right: 0;
											bottom: 0;
											content: "";
											background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
											z-index: 1;
											pointer-events: none;
									}
									{{addonId}} .ind6-a3 {
											display: flex;
											position: relative;
											flex-wrap: wrap;
											<# if(img_zt=="qx"){ #>
											transform: perspective(1000px) rotateX(52deg);
											transform-style: preserve-3d;
											<# } #>

									}
									{{addonId}} .ind6-a4 {
											width: calc( 100% / 7 - (1.5% * (7 - 1) / 7) - 2px);
											margin-right: 1.5%;
											// height: 0;
											// padding-bottom: calc(13%/245*130);
											margin-bottom: 20px;
											background-color: #fff;
											border: 1px solid #eee;
											border-radius: 5px;
											overflow: hidden;
											-webkit-transition: all 0.5s;
									}
									{{addonId}} .ind6-a4:nth-child(7n) {

											margin-right: 0!important;
									}
									{{addonId}} .ind6-a5 {
											background: center center no-repeat;
											background-size: cover;
								height:100%;
									}
							/*}*/
							@keyframes ind666{
									0%{transform: translateZ(0px);}
									50%{transform: translateZ(20px);}
									100%{transform: translateZ(0px);}
										}
										{{addonId}} .ind6-a4:nth-child(1){animation: ind666 linear 4s infinite;animation-delay: -1.583629850744975s;}
							{{addonId}} .ind6-a4:nth-child(2){animation: ind666 linear 4s infinite;animation-delay: -3.636640124933237s;}
							{{addonId}} .ind6-a4:nth-child(3){animation: ind666 linear 4s infinite;animation-delay: -0.3415892575310622s;}
							{{addonId}} .ind6-a4:nth-child(4){animation: ind666 linear 4s infinite;animation-delay: -2.1592213945914214s;}
							{{addonId}} .ind6-a4:nth-child(5){animation: ind666 linear 4s infinite;animation-delay: -3.812456332286752s;}
							{{addonId}} .ind6-a4:nth-child(6){animation: ind666 linear 4s infinite;animation-delay: -3.761445778337417s;}
							{{addonId}} .ind6-a4:nth-child(7){animation: ind666 linear 4s infinite;animation-delay: -1.4740311448861378s;}
							{{addonId}} .ind6-a4:nth-child(8){animation: ind666 linear 4s infinite;animation-delay: -0.3597797042163995s;}
							{{addonId}} .ind6-a4:nth-child(9){animation: ind666 linear 4s infinite;animation-delay: -3.0055658985744014s;}
							{{addonId}} .ind6-a4:nth-child(10){animation: ind666 linear 4s infinite;animation-delay: -0.9179019590600355s;}
							{{addonId}} .ind6-a4:nth-child(11){animation: ind666 linear 4s infinite;animation-delay: -3.026364436914257s;}
							{{addonId}} .ind6-a4:nth-child(12){animation: ind666 linear 4s infinite;animation-delay: -3.821782617898635s;}
							{{addonId}} .ind6-a4:nth-child(13){animation: ind666 linear 4s infinite;animation-delay: -0.9411883522668303s;}
							{{addonId}} .ind6-a4:nth-child(14){animation: ind666 linear 4s infinite;animation-delay: -1.6709697886051078s;}
							{{addonId}} .ind6-a4:nth-child(15){animation: ind666 linear 4s infinite;animation-delay: -1.0926285146328505s;}
							{{addonId}} .ind6-a4:nth-child(16){animation: ind666 linear 4s infinite;animation-delay: -1.5688435053121568s;}
							{{addonId}} .ind6-a4:nth-child(17){animation: ind666 linear 4s infinite;animation-delay: -1.4607250633915374s;}
							{{addonId}} .ind6-a4:nth-child(18){animation: ind666 linear 4s infinite;animation-delay: -3.198819141151179s;}
							{{addonId}} .ind6-a4:nth-child(19){animation: ind666 linear 4s infinite;animation-delay: -3.503310892640034s;}
							{{addonId}} .ind6-a4:nth-child(20){animation: ind666 linear 4s infinite;animation-delay: -2.0989392601940757s;}
							{{addonId}} .ind6-a4:nth-child(21){animation: ind666 linear 4s infinite;animation-delay: -0.645072607642482s;}
							{{addonId}} .ind6-a4:nth-child(22){animation: ind666 linear 4s infinite;animation-delay: -2.0460234543970746s;}
							{{addonId}} .ind6-a4:nth-child(23){animation: ind666 linear 4s infinite;animation-delay: -3.6653057358842522s;}
							{{addonId}} .ind6-a4:nth-child(24){animation: ind666 linear 4s infinite;animation-delay: -0.9619905165684051s;}
							{{addonId}} .ind6-a4:nth-child(25){animation: ind666 linear 4s infinite;animation-delay: -1.543627600765796s;}
							{{addonId}} .ind6-a4:nth-child(26){animation: ind666 linear 4s infinite;animation-delay: -0.6773664727978881s;}
							{{addonId}} .ind6-a4:nth-child(27){animation: ind666 linear 4s infinite;animation-delay: -1.3555055248111065s;}
							{{addonId}} .ind6-a4:nth-child(28){animation: ind666 linear 4s infinite;animation-delay: -3.4946053665972068s;}
							{{addonId}} .ind6-a4:nth-child(29){animation: ind666 linear 4s infinite;animation-delay: -1.6704873508861118s;}
							{{addonId}} .ind6-a4:nth-child(30){animation: ind666 linear 4s infinite;animation-delay: -3.944176333286312s;}
							{{addonId}} .ind6-a4:nth-child(31){animation: ind666 linear 4s infinite;animation-delay: -3.742350326900147s;}
							{{addonId}} .ind6-a4:nth-child(32){animation: ind666 linear 4s infinite;animation-delay: -3.3905700651291744s;}
							{{addonId}} .ind6-a4:nth-child(33){animation: ind666 linear 4s infinite;animation-delay: -0.7581577449455175s;}
							{{addonId}} .ind6-a4:nth-child(34){animation: ind666 linear 4s infinite;animation-delay: -3.033386786190248s;}
							{{addonId}} .ind6-a4:nth-child(35){animation: ind666 linear 4s infinite;animation-delay: -3.2567125014841585s;}
							{{addonId}} .ind6-a4:nth-child(36){animation: ind666 linear 4s infinite;animation-delay: -0.9918442614476062s;}
							{{addonId}} .ind6-a4:nth-child(37){animation: ind666 linear 4s infinite;animation-delay: -1.2375333907862922s;}
							{{addonId}} .ind6-a4:nth-child(38){animation: ind666 linear 4s infinite;animation-delay: -1.7620601664275215s;}
							{{addonId}} .ind6-a4:nth-child(39){animation: ind666 linear 4s infinite;animation-delay: -3.3862342374502363s;}
							{{addonId}} .ind6-a4:nth-child(40){animation: ind666 linear 4s infinite;animation-delay: -1.9401997706297722s;}
							{{addonId}} .ind6-a4:nth-child(41){animation: ind666 linear 4s infinite;animation-delay: -3.5766836413823935s;}
							{{addonId}} .ind6-a4:nth-child(42){animation: ind666 linear 4s infinite;animation-delay: -0.15812519724330265s;}

						{{addonId}} .hzsj{display:none;}

							/*手机*/
							@media only screen and (max-width: 1023px){

							{{addonId}} .hzpc{display:none;}
							{{addonId}} .hzsj{display:block;background:#fff;}

									{{addonId}} .p-index-a5 {
											position: relative;
											width: 100%;
											padding-top: 1rem;
											padding-bottom: 0.75rem;
									}
									{{addonId}} .p-index-a5-line {
											width: 100%;
											position: relative;
											overflow: hidden;
									}
									{{addonId}} .p-index-a5-line1 {
											width: 100%;
											margin: 0 auto 0.1rem;
											overflow: hidden;
											position: relative;
											height: 70px;
									}
									{{addonId}} .p-index-a5-line img {
											/*width: 2.88rem;*/
											height: auto;
											/*margin-right: .52rem;*/
											/*display: inline-block;*/
											/*float:left;*/
											margin: 1px;
											float: left;
											width: 100%;
											/*width:98px;*/
									}
									{{addonId}} .p-index-a5-line li {
											width: 132px;
											height: 70px;
											float: left;
											list-style: none;
											overflow:hidden;
									}
									{{addonId}} .p-index-a5-line2 {
											/*display: flex;*/
											width: 9999px;
											left: 0px;
											right: 0px;
											position: absolute;
								padding-left:20px;
											/*animation:move 10s linear infinite;*/
											/*-webkit-animation:move 10s linear infinite;*/
									}
									{{addonId}} .p-index-a5-line3 {
											width: 100%;
											margin-bottom: .1rem;
											height: 70px;
									}

									{{addonId}} .p-index-a5-line3:before {
											content: "";
											display: inline-block;
											width: 0.6rem;
											height: 100%;
											background: linear-gradient(to right, rgba(255, 255, 255, .95), rgba(255, 255, 255, 0));
											position: absolute;
											top: 0;
											left: 0;
											z-index: 6;
									}

									{{addonId}} .p-index-a5-line3:after {
											content: "";
											display: inline-block;
											width: .6rem;
											height: 100%;
											background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, .95));
											position: absolute;
											top: 0;
											right: 0;
											z-index: 6;
									}

									@keyframes move {
											0% {
													transform: translateX(0px);
											}
											100% {
													transform: translateX(-450px);
											}
									}
							}

					</style>

					<div class="hzpc ind6-a1 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
							<div class="ind6-a2">
									<div class="ind6-a3 clear">
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
											<div class="ind6-a4">
													<div class="ind6-a5 i300" style="background-image: url(/static/index/img/b15.jpg);">
														<img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg">
													</div>
											</div>
									</div>
							</div>
					</div>

					<div class="p-index-a5 hzsj">
							<div class="p-index-a5-line">
									<div class="p-index-a5-line1" id="roll">
											<ul class="p-index-a5-line2 clear" style="width: 2376px; ">
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
											</ul>
									</div>
									<div class="p-index-a5-line3" id="box1">
											<ul class="p-index-a5-line2 clear" style="width: 2376px;">
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
											</ul>
									</div>
									<div class="p-index-a5-line1" id="box2">
											<ul class="p-index-a5-line2 clear" style="width: 2640px; ">
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>
													<li><img src="https://oss.lcweb01.cn/joomla/20220514/63d8348c52ecd2b8289895f57474bd37.jpg" alt="" oncontextmenu="return false;"></li>

											</ul>
									</div>
							</div>
					</div>
				<# } #>
		';

        return $output;
    }
    // 处理适配的值
    public function getResponsiveValues($baseName, $defaults = null) {

        $settings = $this->addon->settings;

        // 设置默认值（支持完整覆盖或部分覆盖）
        $finalDefaults = array_merge([
            'md' => '',
            'sm' => '',
            'xs' => ''
        ], (array)$defaults);

        // 动态构建属性名
        $value = $baseName;
        $valueSm = $baseName . '_sm';
        $valueXs = $baseName . '_xs';

        // 检查主属性是否存在
        if (isset($settings->$value)) {
            $mainValue = $settings->$value;

            if ($mainValue && is_object($mainValue)) {
                // 对象处理：从对象属性获取值
                return [
                    'md' => $mainValue->md ?? $finalDefaults['md'],
                    'sm' => $mainValue->sm ?? $finalDefaults['sm'],
                    'xs' => $mainValue->xs ?? $finalDefaults['xs']
                ];
            } elseif ($mainValue) {
                // 标量值处理：从后缀属性获取响应值
                return [
                    'md' => $mainValue,
                    'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
                    'xs' => $settings->$valueXs ?? $finalDefaults['xs']
                ];
            }
        }

        // 当主属性存在但为假值时（如0、空字符串等），返回默认值
        return $finalDefaults;
    }
}
