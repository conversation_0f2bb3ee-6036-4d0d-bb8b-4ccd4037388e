<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonArticle_detail_m extends JwpagefactoryAddons
{
    //在预览页面中渲染
    public function render()
    {
        $settings = $this->addon->settings;

        $company_id = $_GET['company_id'] ?? '';
        $site_id = $_GET['site_id'] ?? '';
        $catid_id = $_GET['catid_id'] ?? '';
        $layout_id = $_GET['layout_id'] ?? '';


        //获取文章详情的数据源
        $app = JFactory::getApplication();
        $input = $app->input;
        $article_id = $input->get('detail');
        $detail_id = base64_decode($input->get('id'));
	    //浏览次数增加
	    JwPageFactoryBase::setrticleLookNum($article_id, $catid_id);
//	    var_dump($add);
//	    exit();
	    //读取文章数据
        $article = JwPageFactoryBase::getArticleById2($article_id, $catid_id);
        if ($catid_id && is_array($catid_id)) {
            $catid_id = implode(',', $catid_id);
        }
//        $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->on . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid_id . , $absolute = true);
	    $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->on."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);
//	    $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_id) . "&detail=" . $article->down . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id . '&catid_id=' . $catid_id . '&catid=' . $catid_id, $absolute = true);
	    $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->down.'&Itemid=0&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&company_id='.$company_id,$absolute=true);

	    $addon_id = '#jwpf-addon-' . $this->addon->id;
        $addonId = $this->addon->id;

	    //页面背景图
        $content_bg = (isset($settings->content_bg) && $settings->content_bg) ? $settings->content_bg : 'https://oss.lcweb01.cn/joomla/20210804/9800279eb39ba5445d228b1b38ec77b3.jpg';
        //名字下方文字
        $content_text = (isset($settings->content_text) && $settings->content_text) ? $settings->content_text : '之墓';

        $output = '';

        $output .= '<audio id="myaudio' . $addonId . '" src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/music.mp3" controls="" loop="" hidden="true">
            </audio>
            <div class="rel"><img src="' . $content_bg . '">
                <div class="text_01">
                    <!-- <span class="text_01_top">慈母</span> -->';

                    if(strstr($article->title, '&'))
                    {
                        $array = explode('&',$article->title);
                        $output .= '<div class="text_name">';
                        foreach($array as $k => $v)
                        {
                            $output .= '<span class="text_02_top">'.$v.'</span>';
                        }
                        $output .='</div>';
                    }
                    else
                    {
                        $output .='<span class="text_01_top">' . $article->title . '</span>';
                    }
                    
            $output.='
                    <span class="text_01_bot">' . $content_text . '</span>
                </div>
                <div class="text_01_sj"></div>
                <div class="text_01_siysj"></div>
                <div class="text_01_lbz"></div>
                <div class="candle_left_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/candle.png" id="dianzhu' . $addonId . '"></div>
                <div class="candle_right_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/candle.png" id="dianzhu1' . $addonId . '"></div>
                <div class="wine_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/wine.png" id="jijiu' . $addonId . '"></div>
                <div class="wine_right_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/wine.png" id="jijiu1' . $addonId . '"></div>
                <div class="incense_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/incense.png" id="shangxiang' . $addonId . '"></div>
                <div class="flowers_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/flowers.png" id="xianhua' . $addonId . '"></div>
                <div class="sao' . $addonId . '" id="sao' . $addonId . '"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/sao.png"></div>
            </div>
            <div class="menu">
                <ul>
                    <li><a href="javascript:void(0);" onclick="xh' . $addonId . '()">献花</a></li>
                    <li><a href="javascript:void(0);" onclick="sao' . $addonId . '()">扫墓</a></li>
                    <li><a href="javascript:void(0);" onclick="sx' . $addonId . '()">上香</a></li>
                    <li><a href="javascript:void(0);" onclick="autoPlay' . $addonId . '()">点歌</a></li>
                    <li><a href="javascript:void(0);" onclick="jj' . $addonId . '()">祭酒</a></li>
                    <li><a href="javascript:void(0);" onclick="dz' . $addonId . '()">点烛</a></li>
                    <div class="cl"></div>
                </ul>
            </div>
            <div class="btn_back"><a href="Javascript:history.go(-1);void(0);">
                <img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/btn_back.png">返回上一页</a>
            </div>';


        return $output;
    }

    //引用js文件，同时作用于编辑器和预览页
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.countdown.min.js'

        );
        return $scripts;
    }

    //引用css文件，同时作用于编辑器和预览页
    public function stylesheets()
    {
        $style_sheet = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css'

        );
        return $style_sheet;
    }

    //在预览页面中使用的JS脚本
    public function js()
    {
        $addon_id = $this->addon->id;
        $js = 'function sx' . $addon_id . '() {
				document.getElementById(\'shangxiang' . $addon_id . '\').style.display = \'block\';
			}

			function xh' . $addon_id . '() {
				document.getElementById(\'xianhua' . $addon_id . '\').style.display = \'block\';
			}
			var timer' . $addon_id . ';
			function sao' . $addon_id . '() {
				var saoEle' . $addon_id . ' = document.getElementById(\'sao' . $addon_id . '\');
				saoEle' . $addon_id . '.className = "sao' . $addon_id . ' active";
				clearTimeout(timer' . $addon_id . ');
				timer' . $addon_id . ' = setTimeout(function() {
					saoEle' . $addon_id . '.className = "sao' . $addon_id . '";
				}, 2e3)
			}
			function dz' . $addon_id . '() {
				document.getElementById(\'dianzhu' . $addon_id . '\').style.display = \'block\';
				document.getElementById(\'dianzhu1' . $addon_id . '\').style.display = \'block\';
			}

			function jj' . $addon_id . '() {
				document.getElementById(\'jijiu' . $addon_id . '\').style.display = \'block\';
				document.getElementById(\'jijiu1' . $addon_id . '\').style.display = \'block\';
			}
			function autoPlay' . $addon_id . '() {
				var myAuto' . $addon_id . ' = document.getElementById(\'myaudio' . $addon_id . '\');
				myAuto' . $addon_id . '.play();
			}';
        return $js;
    }

    //在预览页面中使用的css样式
    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $addon_id = $this->addon->id;
        $settings = $this->addon->settings;
        //菜单背景色
        $menu_bg_color = (isset($settings->menu_bg_color) && $settings->menu_bg_color) ? $settings->menu_bg_color : '#257d17';
        //菜单边框色
        $menu_bg_borderColor = (isset($settings->menu_bg_borderColor) && $settings->menu_bg_borderColor) ? $settings->menu_bg_borderColor : '#86c260';
        //菜单文字颜色
        $menu_bg_fontColor = (isset($settings->menu_bg_fontColor) && $settings->menu_bg_fontColor) ? $settings->menu_bg_fontColor : '#fff';
        //中间文字上边距(百分比)
        $content_text_top = (isset($settings->content_text_top) && $settings->content_text_top) ? $settings->content_text_top : 24;

        $css = $addonId .' * {
                margin: 0;
                padding: 0;
            }
            ' . $addonId . ' #dianzhu' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #dianzhu1' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #xianhua' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #shangxiang' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #sao' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #jijiu' . $addon_id . ' {
				display: none;
			}

			' . $addonId . ' #jijiu1' . $addon_id . ' {
				display: none;
			}
            ' . $addonId . ' img {
                border: 0;
                vertical-align: top;
                width: 100%;
            }
            
            ' . $addonId . ' ul,
            ' . $addonId . ' li {
                list-style: none;
            }
            
            ' . $addonId . ' a {
                color: #000;
                text-decoration: none;
            }
            
            ' . $addonId . ' a:hover {
                text-decoration: none;
            }
            
            ' . $addonId . ' .cl {
                clear: both;
            }
            ' . $addonId . ' .rel {
                position: relative;
            }
            
            /*网上祭祀三级页*/
            ' . $addonId . ' .menu {
                width: 100%;
                background: ' . $menu_bg_color . ';
            }
            
            ' . $addonId . ' .menu li {
                float: left;
                width: 16.6%;
                text-align: center;
                border-right: solid 1px ' . $menu_bg_borderColor . ';
                padding: 4.5% 0;
                font-size: 14px;
                box-sizing: border-box;
            }
            
            ' . $addonId . ' .menu ul li:last-child {
                border: none;
            }
            
            ' . $addonId . ' .menu a {
                color: ' . $menu_bg_fontColor . ';
            }
            
            ' . $addonId . ' .btn_back {
                width: 100%;
                background: #ebebeb;
                text-align: center;
                padding: 5% 0;
                font-size: 15px;
            }
            
            ' . $addonId . ' .btn_back img {
                width: 5%;
                margin: 1% 3% 0 0;
                display: inline-block;
            }
            
            ' . $addonId . ' .btn_js {
                width: 40%;
                height: 28px;
                background: #257d17;
                border: none;
                border-radius: 20px;
                color: #fff;
                font-family: "微软雅黑";
                margin: 0 4%;
            }
            
            /*后加*/
            ' . $addonId . ' .candle_left_01 {
                width: 3.5%;
                position: absolute;
                left: 29.5%;
                bottom: 33%;
                z-index: 1;
            }
            
            ' . $addonId . ' .candle_right_01 {
                width: 3.5%;
                position: absolute;
                right: 29.5%;
                bottom: 33%;
                z-index: 1;
            }
            
            ' . $addonId . ' .wine_01 {
                width: 3%;
                position: absolute;
                left: 39%;
                bottom: 33%;
                z-index: 1;
            }
            
            ' . $addonId . ' .wine_right_01 {
                width: 3%;
                position: absolute;
                right: 39%;
                bottom: 33%;
                z-index: 1;
            }
            
            ' . $addonId . ' .incense_01 {
                width: 12%;
                position: absolute;
                left: 44%;
                bottom: 33%;
                z-index: 1;
            }
            
            ' . $addonId . ' .flowers_01 {
                width: 14%;
                position: absolute;
                left: 43%;
                bottom: 25%;
                z-index: 1;
            }
            
            ' . $addonId . ' .text_01 {
                width: 75px;
                text-align: center;
                line-height: 18px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: ' . $content_text_top . '%;
                left: 0;
                right: 0;
                margin: auto;
                font-size: 18px;
                letter-spacing: 5px;
            }
            
            ' . $addonId . ' .text_01 .text_01_top {
                padding-bottom: 1px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
                width: 22%;
                margin-left: 40%;
            }
            ' . $addonId . ' .text_01 .text_name {
                display: flex;
                justify-content: space-around;
            }
            ' . $addonId . ' .text_01 .text_name .text_02_top {

                text-align: center;
                height: 78px;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy {
                padding-bottom: 1px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy .yonl {
                display: block;
                margin-left: .5rem;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy .yonr {
                display: block;
                margin-left: 1.3rem;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy .yon3l {
                display: block;
                margin-left: .2rem;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy .yon3z {
                display: block;
                margin-left: .5rem;
            }
            
            ' . $addonId . ' .text_01 .text_01_topy .yon3r {
                display: block;
                margin-left: .5rem;
            }
            
            ' . $addonId . ' .text_01 .text_01_bot {
                padding-top: 5px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
                width: 22%;
                margin-left: 40%;
            }
            
            ' . $addonId . ' .text_01_sj {
                width: 8%;
                text-align: center;
                line-height: 12px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: 17%;
                right: 54%;
            }
            
            ' . $addonId . ' .text_01_siysj {
                width: 8%;
                text-align: center;
                line-height: 12px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: 17%;
                right: 37%;
            }
            
            ' . $addonId . ' .text_01_lbz {
                width: 3%;
                text-align: center;
                line-height: 16px; 
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                bottom: 46%;
                left: 40%;
            }
            
            ' . $addonId . ' .sao' . $addon_id . ' {
                width: 40%;
                position: absolute;
                left: 0%;
                bottom: 18%;
                z-index: 1;
            }
            
            ' . $addonId . ' #sao' . $addon_id . '.active {
                -webkit-animation: mymove 2s linear 0s 1 normal both;
                -o-animation: mymove 2s linear 0s 1 normal both;
                animation: mymove 2s linear 0s 1 normal both;
                display: block !important;
            }
            
            /* Chrome, Safari, Opera */
            @-webkit-keyframes mymove {
                0% {
                    left: 0;
                }
            
                50% {
                    left: 60%;
                }
            
                100% {
                    left: 0;
                }
            }
            
            /* Standard syntax */
            @keyframes mymove {
                0% {
                    left: 0;
                }
            
                50% {
                    left: 60%;
                }
            
                100% {
                    left: 0;
                }
            }';
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {


            $output = '';
            $output .= '
            <#
            var addonId = "#jwpf-addon-"+data.id;
            var menu_bg_color = data.menu_bg_color ? data.menu_bg_color : "#257d17";
            var menu_bg_borderColor = data.menu_bg_borderColor ? data.menu_bg_borderColor : "#86c260";
            var menu_bg_fontColor = data.menu_bg_fontColor ? data.menu_bg_fontColor : "#fff";content_bg
            //页面背景图
            var content_bg = data.content_bg ? data.content_bg : "https://oss.lcweb01.cn/joomla/20210804/9800279eb39ba5445d228b1b38ec77b3.jpg";
            // 名字下方文字
            var content_text = data.content_text ? data.content_text : "之墓";
            // 中间文字上边距(百分比)
            var content_text_top = data.content_text_top ? data.content_text_top : 24;
            #>
            <style>
            {{addonId}} * {
                margin: 0;
                padding: 0;
            }
            {{addonId}} img {
                border: 0;
                vertical-align: top;
                width: 100%;
            }

            {{addonId}} ul,
            {{addonId}} li {
                list-style: none;
            }

            {{addonId}} a {
                color: #000;
                text-decoration: none;
            }

            {{addonId}} a:hover {
                text-decoration: none;
            }

            {{addonId}} .cl {
                clear: both;
            }
            {{addonId}} .rel {
                position: relative;
            }

            /*网上祭祀三级页*/
            {{addonId}} .menu {
                width: 100%;
                background: {{menu_bg_color}};
            }

            {{addonId}} .menu li {
                float: left;
                width: 16.6%;
                text-align: center;
                border-right: solid 1px {{menu_bg_borderColor}};
                padding: 4.5% 0;
                font-size: 14px;
                box-sizing: border-box;
            }

            {{addonId}} .menu ul li:last-child {
                border: none;
            }

            {{addonId}} .menu a {
                color: {{menu_bg_fontColor}};
            }

            {{addonId}} .btn_back {
                width: 100%;
                background: #ebebeb;
                text-align: center;
                padding: 5% 0;
                font-size: 15px;
            }

            {{addonId}} .btn_back img {
                width: 5%;
                margin: 1% 3% 0 0;
                display: inline-block;
            }

            {{addonId}} .btn_js {
                width: 40%;
                height: 28px;
                background: #257d17;
                border: none;
                border-radius: 20px;
                color: #fff;
                font-family: "微软雅黑";
                margin: 0 4%;
            }

            /*后加*/
            {{addonId}} .candle_left_01 {
                width: 3.5%;
                position: absolute;
                left: 29.5%;
                bottom: 33%;
                z-index: 1;
            }

            {{addonId}} .candle_right_01 {
                width: 3.5%;
                position: absolute;
                right: 29.5%;
                bottom: 33%;
                z-index: 1;
            }

            {{addonId}} .wine_01 {
                width: 3%;
                position: absolute;
                left: 39%;
                bottom: 33%;
                z-index: 1;
            }

            {{addonId}} .wine_right_01 {
                width: 3%;
                position: absolute;
                right: 39%;
                bottom: 33%;
                z-index: 1;
            }

            {{addonId}} .incense_01 {
                width: 12%;
                position: absolute;
                left: 44%;
                bottom: 33%;
                z-index: 1;
            }

            {{addonId}} .flowers_01 {
                width: 14%;
                position: absolute;
                left: 43%;
                bottom: 25%;
                z-index: 1;
            }

            {{addonId}} .text_01 {
                width: 75px;
                text-align: center;
                line-height: 18px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: {{content_text_top}}%;
                left: 0;
                right: 0;
                margin: auto;
                font-size: 18px;
                letter-spacing: 5px;
            }

            {{addonId}} .text_01 .text_01_top {
                padding-bottom: 1px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
                width: 22%;
                margin-left: 40%;
            }
            
            {{addonId}} .text_01 .text_name {
                display: flex;
                justify-content: space-around;
            }
            {{addonId}} .text_01 .text_name .text_02_top {
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
                text-align: center;
                height: 78px;
            }

            {{addonId}} .text_01 .text_01_topy {
                padding-bottom: 1px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
            }

            {{addonId}} .text_01 .text_01_topy .yonl {
                display: block;
                margin-left: .5rem;
            }

            {{addonId}} .text_01 .text_01_topy .yonr {
                display: block;
                margin-left: 1.3rem;
            }

            {{addonId}} .text_01 .text_01_topy .yon3l {
                display: block;
                margin-left: .2rem;
            }

            {{addonId}} .text_01 .text_01_topy .yon3z {
                display: block;
                margin-left: .5rem;
            }

            {{addonId}} .text_01 .text_01_topy .yon3r {
                display: block;
                margin-left: .5rem;
            }

            {{addonId}} .text_01 .text_01_bot {
                padding-top: 5px;
                display: block;
                -webkit-writing-mode: vertical-lr;
                writing-mode: vertical-lr;
                width: 22%;
                margin-left: 40%;
            }

            {{addonId}} .text_01_sj {
                width: 8%;
                text-align: center;
                line-height: 12px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: 17%;
                right: 54%;
            }

            {{addonId}} .text_01_siysj {
                width: 8%;
                text-align: center;
                line-height: 12px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                top: 17%;
                right: 37%;
            }

            {{addonId}} .text_01_lbz {
                width: 3%;
                text-align: center;
                line-height: 16px;
                color: #9c814c;
                font-weight: bold;
                position: absolute;
                bottom: 46%;
                left: 40%;
            }

            {{addonId}} .sao {
                width: 40%;
                position: absolute;
                left: 0%;
                bottom: 18%;
                z-index: 1;
            }

            {{addonId}} #sao.active {
                -webkit-animation: mymove 2s linear 0s 1 normal both;
                -o-animation: mymove 2s linear 0s 1 normal both;
                animation: mymove 2s linear 0s 1 normal both;
                display: block !important;
            }

            /* Chrome, Safari, Opera */
            @-webkit-keyframes mymove {
                0% {
                    left: 0;
                }

                50% {
                    left: 60%;
                }

                100% {
                    left: 0;
                }
            }

            /* Standard syntax */
            @keyframes mymove {
                0% {
                    left: 0;
                }

                50% {
                    left: 60%;
                }

                100% {
                    left: 0;
                }
            }
            </style>


            <audio id="myaudio" src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/music.mp3" controls="" loop="" hidden="true">
            </audio>
            <div class="rel"><img src=\'{{content_bg}}\'>
                <div class="text_01">
                    <!-- <span class="text_01_top">慈母</span> -->
                    <span class="text_02_top">某某某</span>
                    <!--<div class="text_name">
                        <span class="text_02_top">某某某</span>
                        <span class="text_02_top">某某某</span>
                    </div>-->
                    <span class="text_01_bot">{{content_text}}</span>
                </div>
                <div class="text_01_sj"></div>
                <div class="text_01_siysj"></div>
                <div class="text_01_lbz"></div>
                <div class="candle_left_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/candle.png" id="dianzhu"></div>
                <div class="candle_right_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/candle.png" id="dianzhu1"></div>
                <div class="wine_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/wine.png" id="jijiu"></div>
                <div class="wine_right_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/wine.png" id="jijiu1"></div>
                <div class="incense_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/incense.png" id="shangxiang"></div>
                <div class="flowers_01"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/flowers.png" id="xianhua"></div>
                <div class="sao" id="sao"><img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/sao.png"></div>
            </div>
            <div class="menu">
                <ul>
                    <li><a href="">献花</a></li>
                    <li><a href="">扫墓</a></li>
                    <li><a href="">上香</a></li>
                    <li><a href="">点歌</a></li>
                    <li><a href="">祭酒</a></li>
                    <li style="border:none;"><a href="">点烛</a></li>
                    <div class="cl"></div>
                </ul>
            </div>
            <div class="btn_back"><a href="">
                <img src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/article_detail_m/assets/images/btn_back.png">返回上一页</a>
            </div>
            ';

        return $output;
    }

}