<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'custom_text_box',
        'title' => JText::_('自定义文本框'),
        'desc' => JText::_('自定义文本框'),
        'category' => '常用插件',
        'attr' => array(
            'art_type_selector_ctb' => array(
                'type' => 'select',
                'title' => '选择文本框布局',
                'values' => array(
                    'type1' => '布局1',
                    'type2' => '布局2',
                    'type3' => '布局3',
                    'type4' => '布局4',
                    'type5' => '布局5',
                    'type6' => '布局6',
                ),
                'std' => 'type1'
            ),
            // 布局1 配置项
            'type1_explain_00' => array(
                'type' => 'separator',
                'title' => '总体配置',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_item_icon' => array(
                'type' => 'media',
                'title' => '上方图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20210927/bc1ad484e5e249a3bc3d1387047ae4e4.png',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_item_title' => array(
                'type' => 'text',
                'title' => '图标下方标题',
                'std' => '推广账户搭建',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'icon' => array(
                        'type' => 'media',
                        'title' => '文本前图标',
                        'std' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => '建立账户机构'
                    ),
                ),
                'std' => array(
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '建立账户机构'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '选择关键词'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '设置匹配方式'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '物料创意撰写'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '设置URL'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '设定价格'
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                        'title' => '选择推广地域'
                    ),
                    array(
                        'icon' => '',
                        'title' => '……'
                    )
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_explain_01' => array(
                'type' => 'separator',
                'title' => '样式配置',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_title_fontsize' => array(
                'type' => 'slider',
                'title' => '标题文字大小',
                'min' => 0,
                'max' => 100,
                'std' => 24,
                'depends' => array(
                    array('art_type_selector_ctb' ,'=', 'type1'),
                )
            ),
            'type1_title_margin' => array(
                'type' => 'margin',
                'title' => '标题文字外边距',
                'std' => '30px 0 15px 0',
                'depends' => array(
                    array('art_type_selector_ctb' ,'=', 'type1'),
                )
            ),
            'type1_title_color' => array(
                'type' => 'color',
                'title' => '标题文字颜色',
                'desc' => '',
                'std' => '#454545',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            'type1_item_fontsize' => array(
                'type' => 'slider',
                'title' => '列表文字大小',
                'min' => 0,
                'max' => 100,
                'std' => 16,
                'depends' => array(
                    array('art_type_selector_ctb' ,'=', 'type1'),
                )
            ),
            'type1_item_lineHeight' => array(
                'type' => 'slider',
                'title' => '列表文字行高',
                'min' => 0,
                'max' => 100,
                'std' => 44,
                'depends' => array(
                    array('art_type_selector_ctb' ,'=', 'type1'),
                )
            ),
            'type1_item_color' => array(
                'type' => 'color',
                'title' => '列表文字颜色',
                'desc' => '',
                'std' => '#454545',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type1'),
                )
            ),
            // 布局2 布局3 布局4 配置项
            'type2_explain_00' => array(
                'type' => 'separator',
                'title' => '总体配置',
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                )
            ),
            'type2_container_height' => array(
                'type' => 'slider',
                'title' => '整个容器的高度',
                'min' => 0,
                'max' => 2000,
                'std' => 538,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),
                )
            ),
            'type2_img' => array(
                'type' => 'media',
                'title' => '中间图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20210927/f5da9024182f6c12c9c004be5bb1c294.png',
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),

                )
            ),
            'type6_img' => array(
                'type' => 'media',
                'title' => '背景图',
                'std' => 'https://oss.lcweb01.cn/joomla/20220523/dfd5e47c602106503295e85eea4185fb.png',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type6'),
                )
            ),
            'type2_img_width' => array(
                'type' => 'slider',
                'title' => '中间图标宽度（百分比）',
                'min' => 0,
                'max' => 100,
                'std' => 50,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),

                )
            ),
            
            'type2_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'icon' => array(
                        'type' => 'media',
                        'title' => '标题背景图',
                        'std' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                    ),
                    'icon_width' => array(
                        'type' => 'slider',
                        'title' => '标题背景图宽度',
                        'min' => 0,
                        'max' => 2000,
                        'std' => 182,
                    ),
                    'icon_height' => array(
                        'type' => 'slider',
                        'title' => '标题背景图高度',
                        'min' => 0,
                        'max' => 2000,
                        'std' => 52,
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => '渐进式框架'
                    ),
                    's_title' => array(
                        'type' => 'text',
                        'title' => '简介',
                        'std' => '采用业内公认的主流框架作为基础，程序响应速度更快'
                    ),
                ),
                'std' => array(
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                        'icon_width' => 182,
                        'icon_height' => 52,
                        'title' => '渐进式框架',
                        's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/d4a3efb8ec69cd115da669ab7c4f1173.png',
                        'icon_width' => 182,
                        'icon_height' => 52,
                        'title' => '多端覆盖',
                        's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                        'icon_width' => 182,
                        'icon_height' => 52,
                        'title' => '安全控制机制',
                        's_title' => '支持数据备份自定义安全规则，最大化加强安全系数',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210927/d4a3efb8ec69cd115da669ab7c4f1173.png',
                        'icon_width' => 182,
                        'icon_height' => 52,
                        'title' => '高效运行支撑',
                        's_title' => '负载均衡可支撑亿级数据访问量，高效运转不卡顿 满足庞大业务运转量',
                    ),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type2'),
                )
            ),
            'type3_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'icon' => array(
                        'type' => 'media',
                        'title' => '标题前小图标',
                        'std' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                    ),
                    'icon_bg' => array(
                        'type' => 'color',
                        'title' => '标题前小图标背景色',
                        'std' => '#6e2df6',
                    ),
                    'background_gradient' => array(
                        'type' => 'gradient',
                        'title' => '标题背景渐变色',
                        'std' => array(
                            "color" => "#8a6eff",
                            "color2" => "#874eff",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                    ),
                    'title_shadow' => array(
                        'type' => 'boxshadow',
                        'title' => '标题背景投影',
                        'std' => '0px 7px 18px rgb(138, 110, 255, .35)',
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => '不断升级的购物体验'
                    ),
                    's_title' => array(
                        'type' => 'text',
                        'title' => '简介',
                        'std' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅'
                    ),
                ),
                'std' => array(
                    array(
                        'background_gradient' => array(
                            "color" => "#8a6eff",
                            "color2" => "#874eff",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                        'icon_bg' => '#6e2df6',
                        'title_shadow' => '0px 7px 18px rgb(138, 110, 255, .35)',
                        'title' => '不断升级的购物体验',
                        's_title' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#ff7389",
                            "color2" => "#fe5872",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/a1bb894ee3fc741fe0719556fa958252.png',
                        'icon_bg' => '#ec2e4d',
                        'title_shadow' => '0px 7px 18px rgb(255, 115, 137, .35)',
                        'title' => '更高效拓客增长',
                        's_title' => '帮你做好营销+裂变，从而实现客户指数增长精准会员营销，多种花样玩法，抓住更多客户团长二级分销，刺激团长推荐更多新团长加入会员二级分销，老客带新客，会员源源不断来',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#34a7ff",
                            "color2" => "#138cff",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/1c85a71c4ebc7c8ec389d6f3e0c0f8c2.png',
                        'icon_bg' => '#0072de',
                        'title_shadow' => '0px 7px 18px rgb(18, 139, 255, .35)',
                        'title' => '私域流量+沉浸式体验',
                        's_title' => '持续保障平台转化及留存，精选商品，商品动态展示，激发购买欲客服系统增强客户信任，实时答疑，提升下单率',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#ffb11a",
                            "color2" => "#ffa324",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/e432e817b856c70633caaa687dcb65e7.png',
                        'icon_bg' => '#f16e04',
                        'title_shadow' => '0px 7px 18px rgb(255, 177, 26, .35)',
                        'title' => '软件技术+运营指导',
                        's_title' => '不仅提供系统，还为你的社区团购运营指明方向 全流程管理，数据统计分析，实现精细化运营 提供完整运营指导+经验分享，快速实现盈收 提供源码，支持定制开发，售后无忧',
                    ),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type3'),
                )
            ),
            'type4_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'icon' => array(
                        'type' => 'media',
                        'title' => '标题上方小图标',
                        'std' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                    ),
                    'background_gradient' => array(
                        'type' => 'gradient',
                        'title' => '上方小图标背景渐变色',
                        'std' => array(
                            "color" => "#ff4258",
                            "color2" => "#ff6b84",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                    ),
                    'title_shadow' => array(
                        'type' => 'boxshadow',
                        'title' => '上方小图标背景投影',
                        'std' => '0px 7px 18px rgb(255, 65, 86, .35)',
                    ),
                    'title_color' => array(
                        'type' => 'color',
                        'title' => '标题文字颜色',
                        'std' => '#ff4a60',
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => '会员数据'
                    ),
                    's_title' => array(
                        'type' => 'text',
                        'title' => '简介',
                        'std' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。'
                    ),
                ),
                'std' => array(
                    array(
                        'background_gradient' => array(
                            "color" => "#ff4258",
                            "color2" => "#ff6b84",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                        'title_color' => '#ff4a60',
                        'title_shadow' => '0px 7px 18px rgb(255, 65, 86, .35)',
                        'title' => '会员数据',
                        's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#ffc40a",
                            "color2" => "#ffde13",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/6e98a33f83fb12de05b02a972331ed0e.png',
                        'title_color' => '#ffde13',
                        'title_shadow' => '0px 7px 18px rgb(255, 200, 11, .35)',
                        'title' => '商城流量数据',
                        's_title' => '掌握店铺流量变化，直观评估推广效果。更可分析出高销售转化率产品，赋予高曝光度，获取更高的流量，达成更高的成交。',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#00c998",
                            "color2" => "#00e0be",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/a1d5ae8a5ceb6626d76d303d281be41a.png',
                        'title_color' => '#00debb',
                        'title_shadow' => '0px 7px 18px rgb(0, 201, 152, .35)',
                        'title' => '会员数据',
                        's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                    ),
                    array(
                        'background_gradient' => array(
                            "color" => "#3287ff",
                            "color2" => "#57b4ff",
                            "deg" => "-35",
                            "type" => "linear"
                        ),
                        'icon' => 'https://oss.lcweb01.cn/joomla/20210928/d0f0f2339843d6abf3c31945c000fd98.png',
                        'title_color' => '#52adff',
                        'title_shadow' => '0px 7px 18px rgb(54, 140, 255, .35)',
                        'title' => '商城流量数据',
                        's_title' => '掌握店铺流量变化，直观评估推广效果。更可分析出高销售转化率产品，赋予高曝光度，获取更高的流量，达成更高的成交。',
                    ),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type4'),
                )
            ),
            'type6_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'icon' => array(
                        'type' => 'media',
                        'title' => '标题背景图',
                        'std' => 'https://oss.lcweb01.cn/joomla/20220523/1423e5b9643c3cb0117dfd9da4ec80e8.png',
                    ),
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => ' 支持视频、音频、word、excel、ppt、'
                    ),

                ),
                'std' => array(
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20220523/1423e5b9643c3cb0117dfd9da4ec80e8.png',
                        'title' => '支持视频、音频、word、excel、ppt、<br> pdf等各种格式的资源播放',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20220523/3ca093e8022a06cb5b93315f1472d955.png',
                        'title' => '支持章节目录、模拟考试、课后练习 <br> 课后作业、章节测验、批改作业',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20220523/f3d710c224ae4c26e5aac4f1f5107fce.png',
                        'title' => '课程评论、课件资料下载，重点内容收藏',
                    ),
                    array(
                        'icon' => 'https://oss.lcweb01.cn/joomla/20220523/ff792e3d204153541cdec7f088f28e39.png',
                        'title' => '窗口聊天、试听购买、微信分享、 <br> 学习进度跟踪、数据统计',
                    ),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type6'),
                )
            ),
            'type2_explain_01' => array(
                'type' => 'separator',
                'title' => '样式配置',
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                )
            ),
            'type2_item_width' => array(
                'type' => 'slider',
                'title' => '每列宽度（百分比）',
                'min' => 0,
                'max' => 100,
                'std' => 40,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),
                )
            ),
            'type2_title_fontsize' => array(
                'type' => 'slider',
                'title' => '标题文字大小',
                'min' => 0,
                'max' => 100,
                'std' => 20,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                )
            ),
            'type2_title_lineHeight' => array(
                'type' => 'slider',
                'title' => '标题文字行高',
                'min' => 0,
                'max' => 100,
                'std' => 42,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                )
            ),
            'type2_title_color' => array(
                'type' => 'color',
                'title' => '标题文字颜色',
                'desc' => '',
                'std' => '#fff',
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                )
            ),
            'type2_sTitle_fontsize' => array(
                'type' => 'slider',
                'title' => '简介文字大小',
                'min' => 0,
                'max' => 100,
                'std' => 14,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),

                )
            ),
            'type2_sTitle_lineHeight' => array(
                'type' => 'slider',
                'title' => '简介文字行高',
                'min' => 0,
                'max' => 100,
                'std' => 22,
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),

                )
            ),
            'type2_sTitle_color' => array(
                'type' => 'color',
                'title' => '简介文字颜色',
                'desc' => '',
                'std' => '#454545',
                'depends' => array(
                    array('art_type_selector_ctb', '!=', 'type1'),
                    array('art_type_selector_ctb', '!=', 'type5'),
                    array('art_type_selector_ctb', '!=', 'type6'),

                )
            ),
            //布局5 配置
            'type5_explain_00' => array(
                'type' => 'separator',
                'title' => '总体配置',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_container_height' => array(
                'type' => 'slider',
                'title' => '整个容器的高度',
                'min' => 0,
                'max' => 2000,
                'std' => 600,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_item_icon' => array(
                'type' => 'media',
                'title' => '中间图标',
                'std' => 'https://oss.lcweb01.cn/joomla/20210928/0fe675cbdf6a78537c0a6ae3d7186827.png',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_img_width' => array(
                'type' => 'slider',
                'title' => '中间图标宽度（百分比）',
                'min' => 0,
                'max' => 100,
                'std' => 60,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_item_text' => array(
                'title' => '列表项目',
                'attr' => array(
                    'title' => array(
                        'type' => 'text',
                        'title' => '标题',
                        'std' => '爱好者生态'
                    ),
                    'left' => array(
                        'type' => 'slider',
                        'title' => '距X轴距离',
                        'min' => 0,
                        'max' => 100,
                        'std' => 22,
                    ),
                    'top' => array(
                        'type' => 'slider',
                        'title' => '距Y轴距离',
                        'min' => 0,
                        'max' => 100,
                        'std' => 22,
                    ),
                ),
                'std' => array(
                    array(
                        'title' => '爱好者生态',
                        'left' => 186,
                        'top' => 100
                    ),
                    array(
                        'title' => '明星生态',
                        'left' => 838,
                        'top' => 74
                    ),
                    array(
                        'title' => '场馆生态',
                        'left' => 130,
                        'top' => 338
                    ),
                    array(
                        'title' => '从业者生态',
                        'left' => 914,
                        'top' => 320
                    ),
                    array(
                        'title' => '厂商生态',
                        'left' => 538,
                        'top' => 518
                    ),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_fontsize' => array(
                'type' => 'slider',
                'title' => '标题文字大小',
                'min' => 0,
                'max' => 100,
                'std' => 18,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_lineHeight' => array(
                'type' => 'slider',
                'title' => '标题文字行高',
                'min' => 0,
                'max' => 100,
                'std' => 42,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_color' => array(
                'type' => 'color',
                'title' => '标题文字颜色',
                'desc' => '',
                'std' => '#2a66e4',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_border_width' => array(
                'type' => 'slider',
                'title' => '标题边框宽度',
                'std' => 1,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_border_color' => array(
                'type' => 'color',
                'std' => '#2a66e4',
                'title' => '标题边框颜色',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_border_style' => array(
                'type' => 'select',
                'title' => '标题边框样式',
                'std' => 'solid',
                'values' => array(
                    'none' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_NONE'),
                    'solid' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_SOLID'),
                    'double' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOUBLE'),
                    'dotted' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DOTTED'),
                    'dashed' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_STYLE_DASHED'),
                ),
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type5_title_border_radius' => array(
                'type' => 'slider',
                'title' => '标题边框圆角',
                'std' => 40,
                'max' => 500,
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type5'),
                )
            ),
            'type6_bgcolor' => array(
                'type' => 'color',
                'title' => '划过背景色',
                'std' => '#195bff',
                'depends' => array(
                    array('art_type_selector_ctb', '=', 'type6'),
                )
            ),

        )
    )
);