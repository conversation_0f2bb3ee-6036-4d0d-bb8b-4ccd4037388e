<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
//$addonId = $this->addon->id;
//$addonId = '111';
//print_r($app);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'articles_list_tab',
        'title' => '文章分类选项卡',
        'desc' => '文章分类选项卡',
        'category' => '文章',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'separator_options' => array(
                    'type' => 'separator',
                    'title' => '插件选项'
                ),
                'item_type' => array(
                    'type' => 'buttons',
                    'title' => '选择配置项',
                    'std' => 'tab',
                    'values' => array(
                        array(
                            'label' => '选项卡',
                            'value' => 'tab'
                        ),
                        array(
                            'label' => '文章列表',
                            'value' => 'list'
                        ),
                        array(
                            'label' => '数据相关',
                            'value' => 'data'
                        ),
                    ),
                    'tabs' => true,
                ),
                // 选项卡 配置部分
                'type_parent_show' => array(
                    'type' => 'select',
                    'title' => '选择选项卡布局',
                    'values' => array(
                        'type1' => '布局01',
                        'type2' => '布局02',
                        'type3' => '布局03',
                        'type4' => '布局04',
                    ),
                    'std' => 'type1',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                    ),
                ),
                // 选项卡 切换事件设置
                'separator_tab_01' => array(
                    'type' => 'separator',
                    'title' => '选项卡事件设置',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'!=', 'type1'),
                        array('type_parent_show' ,'!=', 'type2'),
                    ),
                ),
                'tab_change' => array(
                    'type' => 'select',
                    'title' => '选择选项卡切换方式',
                    'values' => array(
                        'onclick' => '点击',
                        'onmouseover' => '移入',
                    ),
                    'std' => 'onclick',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'!=', 'type1'),
                        array('type_parent_show' ,'!=', 'type2'),
                    ),
                ),
                'is_tab_click' => array(
                    'type' => 'checkbox',
                    'title' => '开启选项卡点击跳转页面',
                    'std' => '0',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'!=', 'type1'),
                        array('type_parent_show' ,'!=', 'type2'),
                        array('tab_change' ,'=', 'onmouseover'),
                    ),
                ),
                'tab_link_way' => array(
                    'type' => 'select',
                    'title' => '选择选项卡跳转页面方式',
                    'values' => array(
                        '_self' => '当前窗口',
                        '_blank' => '新窗口',
                    ),
                    'std' => '_blank',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'!=', 'type1'),
                        array('type_parent_show' ,'!=', 'type2'),
                        array('tab_change' ,'=', 'onmouseover'),
                        array('is_tab_click' ,'=', 1),
                    ),
                ),
                'tab_click_page_id' => array(
                    'type' => 'select',
                    'title' => '选项卡点击跳转详情页模版',
                    'desc' => '页面需要使用本插件且分类对应',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'!=', 'type1'),
                        array('type_parent_show' ,'!=', 'type2'),
                        array('tab_change' ,'=', 'onmouseover'),
                        array('is_tab_click' ,'=', 1),
                    ),
                ),
                // 选项卡样式配置 在这里写 加上 'depends' =>array(array('item_type' ,'=', 'tab'))
                'separator_tab' => array(
                    'type' => 'separator',
                    'title' => '选项卡样式配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                    ),
                ),
                'tab2_width' => array(
                    'type' => 'slider',
                    'title' => '左侧选项卡宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 245,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part01_height' => array(
                    'type' => 'slider',
                    'title' => '上方栏目的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part01_bgColor' => array(
                    'type' => 'color',
                    'title' => '上方栏目背景颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part01_name' => array(
                    'type' => 'text',
                    'title' => '上方栏目标题',
                    'std' => '新闻中心',
                    'depends' =>array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part01_fontsize' => array(
                    'type' => 'slider',
                    'title' => '上方栏目文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 28,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part01_color' => array(
                    'type' => 'color',
                    'title' => '上方栏目文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part02_height' => array(
                    'type' => 'slider',
                    'title' => '下方分类的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 500,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_part02_bgColor' => array(
                    'type' => 'color',
                    'title' => '下方分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab_state' => array(
                    'type' => 'buttons',
                    'title' => '选项卡状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                    ),
                ),
                'tab2_li_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '分类行度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 60,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_fontsize' => array(
                    'type' => 'slider',
                    'title' => '分类文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 14,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_color' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bgColor' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_color_hover' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bColor_hover' => array(
                    'type' => 'color',
                    'title' => '分类前竖线颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bWidth_hover' => array(
                    'type' => 'slider',
                    'title' => '分类前竖线宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 3,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_color_active' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bgColor_active' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bColor_active' => array(
                    'type' => 'color',
                    'title' => '分类前竖线颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab2_li_bWidth_active' => array(
                    'type' => 'slider',
                    'title' => '分类前竖线宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 3,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type2'),
                    ),
                ),
                'tab3_font_color' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_font_color_y' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体移入颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_font_color_x' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体选中颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_font_border_color_y' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体下划线移入颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_font_border_color_x' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体下划线选中颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_left_font_color' => array(
                    'type' => 'color',
                    'title' => '左侧栏目字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_left_bg_color' => array(
                    'type' => 'color',
                    'title' => '左侧栏目背景颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_left_font_text' => array(
                    'type' => 'text',
                    'title' => '左侧栏目文字内容',
                    'std' => '人才建设',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab3_font_list_num' => array(
                    'type' => 'slider',
                    'title' => '选项卡与列表间距',
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type3'),
                    ),
                ),
                'tab4_font_color' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_color_y' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体移入颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_color_x' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体选中颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_bg_color_y' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体背景移入颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'hover'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_bg_color_x' => array(
                    'type' => 'color',
                    'title' => '右侧选项字体背景选中颜色',
                    'std' => '#d80006',
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'active'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_padding_num' => array(
                    'type' => 'slider',
                    'title' => '右侧选项字体左右内边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => 5,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_after_num' => array(
                    'type' => 'slider',
                    'title' => '右侧选项字体分割线左侧间距',
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),
                'tab4_font_list_num' => array(
                    'type' => 'slider',
                    'title' => '选项卡与列表间距',
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('item_type' ,'=', 'tab'),
                        array('tab_state' ,'=', 'normal'),
                        array('type_parent_show' ,'=', 'type4'),
                    ),
                ),

                // 选项卡样式配置 结束
                // 文章列表样式配置 在这里写 加上 'depends' =>array(array('item_type' ,'=', 'list'))
                'art_type_selector' => array(
                    'type' => 'select',
                    'title' => '选择文章布局',
                    'values' => array(
//                        'art01' => '布局01',
                        'art24' => '布局24',
                    ),
                    'std' => 'art24',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'separator_list_01' => array(
                    'type' => 'separator',
                    'title' => '文章列表样式配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'separator_list_02' => array(
                    'type' => 'separator',
                    'title' => '总体样式配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'font_state' => array(
                    'type' => 'buttons',
                    'title' => '文字状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'art_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 16,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 42,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_color' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_time_fontsize' => array(
                    'type' => 'slider',
                    'title' => '日期文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 16,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_time_color' => array(
                    'type' => 'color',
                    'title' => '日期文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_border_width' => array(
                    'type' => 'slider',
                    'title' => '下划线高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 1,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_border_color' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#eee',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_border_type' => array(
                    'type' => 'select',
                    'title' => '下划线类型',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                    'values' => array(
                        'none ' => '无',
                        'dashed ' => '虚线',
                        'solid' => '实线',
                        'dotted' => '点线',
                    ),
                    'std' => 'dashed',
                ),
                'separator_list_04' => array(
                    'type' => 'separator',
                    'title' => '列表前图标配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'show_title_icon' => array(
                    'type' => 'checkbox',
                    'title' => '开启列表前图标',
                    'std' => '0',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                    ),
                ),
                'art_title_icon' => array(
                    'type' => 'media',
                    'title' => '列表前图标',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                        array('show_title_icon' ,'=', 1),
                    ),
                ),
                'art_title_icon_width' => array(
                    'type' => 'slider',
                    'title' => '列表前图标宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 12,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                        array('show_title_icon' ,'=', 1),
                    ),
                ),
                'art_title_icon_height' => array(
                    'type' => 'slider',
                    'title' => '列表前图标高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 12,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'normal'),
                        array('show_title_icon' ,'=', 1),
                    ),
                ),
                // 移入样式 array('font_state' ,'=', 'hover'),
                'art_title_color_hover' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'hover'),
                    ),
                ),
                'art_time_color_hover' => array(
                    'type' => 'color',
                    'title' => '日期文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'hover'),
                    ),
                ),
                'art_title_border_color_hover' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'hover'),
                    ),
                ),
                'art_title_border_type_hover' => array(
                    'type' => 'select',
                    'title' => '下划线类型',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'hover'),
                    ),
                    'values' => array(
                        'none ' => '无',
                        'dashed ' => '虚线',
                        'solid' => '实线',
                        'dotted' => '点线',
                    ),
                ),
                'art_title_icon_hover' => array(
                    'type' => 'media',
                    'title' => '列表前图标',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('font_state' ,'=', 'hover'),
                        array('show_title_icon' ,'=', 1),
                    ),
                ),
                'separator_list_06' => array(
                    'type' => 'separator',
                    'title' => '列表部分中间线',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'show_list_border' => array(
                    'type' => 'checkbox',
                    'title' => '开启列表部分中间线',
                    'std' => '0',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'list_border_num' => array(
                    'type' => 'slider',
                    'title' => '第几条数据下面加中间线',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 6,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_border' ,'=', '1'),
                    ),
                ),
                'list_border_width' => array(
                    'type' => 'slider',
                    'title' => '线高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 1,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_border' ,'=', '1'),
                    ),
                ),
                'list_border_color' => array(
                    'type' => 'color',
                    'title' => '线颜色',
                    'std' => '#eee',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_border' ,'=', '1'),
                    ),
                ),
                'list_border_type' => array(
                    'type' => 'select',
                    'title' => '线类型',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_border' ,'=', '1'),
                    ),
                    'values' => array(
                        'none ' => '无',
                        'dashed ' => '虚线',
                        'solid' => '实线',
                        'dotted' => '点线',
                    ),
                    'std' => 'dashed',
                ),
                'separator_list_05' => array(
                    'type' => 'separator',
                    'title' => '列表部分导航配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'show_list_nav' => array(
                    'type' => 'checkbox',
                    'title' => '开启列表部分导航',
                    'std' => '0',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'list_nav_style' => array(
                    'type' => 'select',
                    'title' => '列表部分导航布局',
                    'values' => array(
                        'nav01' => '布局01',
                    ),
                    'std' => 'nav01',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_name' => array(
                    'type' => 'text',
                    'title' => '列表部分导航标题',
                    'std' => '新闻中心',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                        array('show_list_nav' ,'=', '1'),
                        array('list_nav_style' ,'=', 'nav01'),
                    ),
                ),
                'separator_list_03' => array(
                    'type' => 'separator',
                    'title' => '第一条数据样式配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'art_list_first' => array(
                    'type' => 'select',
                    'title' => '第一条数据布局',
                    'values' => array(
                        'art01' => '正常（同其他）',
                        'art02' => '布局02',
                        'art03' => '布局03',
                    ),
                    'std' => 'art01',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'first_img_width' => array(
                    'type' => 'slider',
                    'title' => '图片宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'=', 'art03'),
                    ),
                ),
                'first_img_height' => array(
                    'type' => 'slider',
                    'title' => '图片高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 200,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'=', 'art03'),
                    ),
                ),
                'list_first_state' => array(
                    'type' => 'buttons',
                    'title' => '第一条文字状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                    ),
                ),
                'first_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 18,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                    ),
                ),
                'first_title_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '标题行高',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 24,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                    ),
                ),
                'first_title_color' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                    ),
                ),
                'first_desc_fontsize' => array(
                    'type' => 'slider',
                    'title' => '简介文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 14,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_desc_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '简介文字行高',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 22,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_desc_color' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'std' => '#555',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_more_fontsize' => array(
                    'type' => 'slider',
                    'title' => '时间/更多文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 14,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_more_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '时间/更多文字行高',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 18,
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_more_color' => array(
                    'type' => 'color',
                    'title' => '时间/更多文字颜色',
                    'std' => '#999',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'normal'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                // 第一条 移入
                'first_title_color_hover' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'hover'),
                    ),
                ),
                'first_desc_color_hover' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'hover'),
                        array('show_intro' ,'=', '1'),
                    ),
                ),
                'first_more_color_hover' => array(
                    'type' => 'color',
                    'title' => '时间/更多文字颜色',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                        array('art_list_first' ,'!=', 'art01'),
                        array('list_first_state' ,'=', 'hover'),
                    ),
                ),

                // 文章列表 翻页
                'separator_page_data' => array(
                    'type' => 'separator',
                    'title' => '翻页配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                    'depends' => array(
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'page_style_selector' => array(
                    'type' => 'select',
                    'title' => '翻页样式',
                    'values' => array(
                        'page01' => '翻页样式一',
                        'page02' => '翻页样式二',
                        'page03' => '翻页样式三',
                    ),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => 'page01'
                ),
                'page1_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#1e1e1e',
                ),
                'page1_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#ffffff',
                ),
                'page1_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#2a68a7',
                ),
                'page1_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                    'std' => '#2a68a7',
                ),
                'page_top' => array(
                    'type' => 'slider',
                    'title' => '分页上边距',
                    'std' => 10,
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                    ),
                ),
                'page3_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('上下翻页背景色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                        array('page_style_selector' ,'=', 'page03'),
                    ),
                    'std' => '#2a68a7',
                ),
                'page3_cur_color' => array(
                    'type' => 'color',
                    'title' => JText::_('上下翻页文字颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                        array('page_style_selector' ,'=', 'page03'),
                    ),
                    'std' => '#ffffff',
                ),
                'page3_none_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('上下翻页不可点击背景色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                        array('page_style_selector' ,'=', 'page03'),
                    ),
                    'std' => '#b3b3b3',
                ),
                'page3_none_color' => array(
                    'type' => 'color',
                    'title' => JText::_('上下翻页不可点击文字颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                        array('page_style_selector' ,'=', 'page03'),
                    ),
                    'std' => '#ffffff',
                ),
                'page3_num' => array(
                    'type' => 'slider',
                    'title' => '最多显示分页数',
                    'min' => 3,
                    'std' => 4,
                    'depends' => array(
                        array('show_page' ,'=', true),
                        array('item_type' ,'=', 'list'),
                        array('page_style_selector' ,'=', 'page03'),
                    ),
                ),
                // 数据配置 部分
                // 文章列表数据部分
                'separator_tab_data' => array(
                    'type' => 'separator',
                    'title' => '选项卡数据配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'type_parent' => array(
                    'type' => 'select',
                    'title' => '分类显示',
                    'desc' => '选择展示几级分类',
                    'values' => array(
                        'type1' => '一级分类',
                        'type2' => '二级分类',
                        'type3' => '混合显示',
                    ),
                    'std' => 'type1',
                    'depends' =>array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'multiple' => true,
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                        array('type_parent' ,'=', 'type3'),
                    ),
                ),
                // 文章列表数据部分
                'separator_list_data' => array(
                    'type' => 'separator',
                    'title' => '文章列表数据配置',
                    'depends' =>array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' =>array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'detail_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('详情关联插件(开启后详情页模板需选择本页面)'),
                    'desc' => JText::_(''),
                    'std' => 0,
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'detail_addon' => array(
                    'type' => 'number',
                    'title' => '详情页模版文章分类选项卡插件id',
                    'std' => '',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'order_desc' => JText::_('排序id降序'),
                        'order_asc' => JText::_('排序id升序'),
                        'time_desc' => JText::_('添加时间降序'),
                        'time_asc' => JText::_('添加时间升序'),
                    ),
                    'std' => 'order_desc',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                        array('art_type_selector', '!=', 'art24'),
                    ),
                ),
                'columns_xs' => array(
                    'type' => 'number',
                    'title' => JText::_('手机列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                        array('art_type_selector', '!=', 'art24'),
                    ),
                ),
                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => 1,
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                    ),
                ),
                'intro_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT_DESC'),
                    'std' => '200',
                    'depends' => array(
                        array('item_type' ,'=', 'data'),
                        array('show_intro', '=', 1),
                    ),
                ),
            )
        ),
    )
);
