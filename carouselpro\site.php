<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */

//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonCarouselpro extends JwpagefactoryAddons
{

	public function render()
	{

		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
		$company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
		//Addons option
		$autoplay = (isset($settings->autoplay) && $settings->autoplay) ? 1 : 0;
		$controllers = (isset($settings->controllers) && $settings->controllers) ? $settings->controllers : 0;
		$arrows = (isset($settings->arrows) && $settings->arrows) ? $settings->arrows : 0;
		$alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : '';
		$carousel_autoplay = ($autoplay) ? ' data-jwpf-ride="jwpf-carousel"' : '';
		$interval = (isset($settings->interval) && $settings->interval) ? ((int)$settings->interval * 1000) : 5000;
		if ($autoplay == 0) {
			$interval = 'false';
		}
		//Container & Column
		$full_container = (isset($settings->full_container) && $settings->full_container) ? $settings->full_container : '';
		$content_column = (isset($settings->content_column) && $settings->content_column) ? $settings->content_column : '';
		$textColumn = '';
		$imageColumn = '';
		if ($content_column) {
			$textColumn = $content_column;
			$imageColumn = (12 - $content_column);
		} else {
			$textColumn = 6;
			$imageColumn = 6;
		}
		//Arrow style
		$arrow_position = (isset($settings->arrow_position)) ? $settings->arrow_position : 'default';
		$arrow_icon = (isset($settings->arrow_icon)) ? $settings->arrow_icon : 'chevron';
		$left_arrow = '';
		$right_arrow = '';
		if ($arrow_icon == 'angle_dubble') {
			$left_arrow = 'fa-angle-double-left';
			$right_arrow = 'fa-angle-double-right';
		} elseif ($arrow_icon == 'arrow') {
			$left_arrow = 'fa-arrow-left';
			$right_arrow = 'fa-arrow-right';
		} elseif ($arrow_icon == 'arrow_circle') {
			$left_arrow = 'fa-arrow-circle-o-left';
			$right_arrow = 'fa-arrow-circle-o-right';
		} elseif ($arrow_icon == 'long_arrow') {
			$left_arrow = 'fa-long-arrow-left';
			$right_arrow = 'fa-long-arrow-right';
		} elseif ($arrow_icon == 'angle') {
			$left_arrow = 'fa-angle-left';
			$right_arrow = 'fa-angle-right';
		} else {
			$left_arrow = 'fa-chevron-left';
			$right_arrow = 'fa-chevron-right';
		}
		//Item Height
		$carousel_height = (isset($settings->carousel_height) && $settings->carousel_height) ? $settings->carousel_height : '';

		//Output start
		$output = '';
		$output .= '<div id="jwpf-carousel-' . $this->addon->id . '" data-interval="' . $interval . '" class="jwpf-carousel jwpf-carousel-pro jwpf-slide' . $class . '"' . $carousel_autoplay . '>';

		if ($controllers) {
			$output .= '<ol class="jwpf-carousel-indicators  jwpf-carousel-indicators_preview">';
			foreach ($settings->jw_carouselpro_item as $key1 => $value) {
				$output .= '<li data-jwpf-target="#jwpf-carousel-' . $this->addon->id . '" ' . (($key1 == 0) ? ' class="active"' : '') . '  data-jwpf-slide-to="' . $key1 . '"></li>' . "\n";
			}
			$output .= '</ol>';
		}

		$output .= '<div class="jwpf-carousel-inner ' . $alignment . '">';

		if (isset($settings->jw_carouselpro_item) && count((array)$settings->jw_carouselpro_item)) {
			foreach ($settings->jw_carouselpro_item as $key => $value) {


				// 2021.09.16
                // 轮播来源
                $text_from = (isset($value->text_from) && $value->text_from) ? $value->text_from : 0;
                // 轮播内容
                $text_id = (isset($value->text_id) && $value->text_id) ? $value->text_id : null;
                // 2021.09.16
                //选择轮播类型
                $hqtype = (isset($value->hqtype) && $value->hqtype) ? $value->hqtype : 'dan';
                $banner_typeid = (isset($value->banner_typeid) &&  $value->banner_typeid) ? $value->banner_typeid : null;

                if($hqtype=='zu' && $banner_typeid){
                    if($key==0){
                        $bantype = JwPageFactoryBase::getBannerLists($site_id, $company_id,$banner_typeid);
                        if($bantype){
                        	foreach ($bantype as $keyd => $valued) {
                                
                                if($valued['image_intro']){
                                    $valued['image_intro']=$valued['image_intro'];
                                }else{
                                    $valued['image_intro']='https://oss.lcweb01.cn/joomla/20220323/700056256b286fc6af9b9c60ea355642.jpg';
                                }

	                        	if($value->text_from && $value->text_from == 1 && $value->text_id) {
				                	$infos = JwPageFactoryBase::getBannerById($text_id);
				                	if($infos){	
										$value->bg=$infos->image_intro;
										$value->image=$infos->qrcode;
										$value->title=$infos->banner_title;
										$value->content=$infos->introtext;
				                	}
								}

								$bg_class = (isset($valued['image_intro']) && $valued['image_intro']) ? ' jwpf-item-has-bg' : '';
								$image = (isset($valued['qrcode']) && $valued['qrcode']) ? $valued['qrcode'] : '';
								$bg = '';
								$bg_image = '';
								if (isset($valued['image_intro']) && $valued['image_intro']) {
									
									$bg = (isset($valued['image_intro']) && $valued['image_intro']) ? $valued['image_intro'] : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg';
									$bg_image = (isset($valued['image_intro']) && $valued['image_intro']) ? 'style="background-image: url(' . $valued['image_intro'] . '); background-repeat: no-repeat; background-position: center center; background-size: cover;"' : '';
									
								}
								$alt_text = (isset($valued['banner_title']) && $valued['banner_title']) ? $valued['banner_title'] : '';

								$output .= '<div id="jwpf-item-' . $this->addon->id . $keyd . '" class="jwpf-item' . $bg_class . (($keyd == 0) ? ' active' : '') . ' carousel-item-' . ($keyd + 1) . '" ' . ($carousel_height ? $bg_image : '') . '
								>';

								if (!$carousel_height) {
									$output .= ($bg) ? '<img src="' . $bg . '" alt="' . $alt_text . '">' : '';
								}
								if ($valued['link']){
				                   $output .='<a href="'.$valued['link'].'" target="'.$value->media_target.'">';//20210630 添加设置链接
				                }
								$output .= '<div class="jwpf-carousel-item-inner">';
								$output .= '<div class="jwpf-carousel-pro-inner-content">';
								$output .= '<div>';
								if (!$full_container) {
									$output .= '<div class="jwpf-container">';
								}
								$output .= '<div class="jwpf-row">';
								$output .= '<div class="jwpf-col-sm-' . $textColumn . ' jwpf-col-xs-12">';
								$output .= '<div class="jwpf-carousel-pro-text">';

								if ($valued['banner_title'] && $valued['introtext']) {
									$output .= (isset($valued['banner_title']) && $valued['banner_title']) ? '<h2>' . $valued['banner_title'] . '</h2>' : '';
									$output .= (isset($valued['introtext']) && $valued['introtext']) ? '<div class="jwpf-carousel-pro-content">' . $valued['introtext'] . '</div>' : '';
									if (isset($value->button_text) && $value->button_text) {
										$button_class = (isset($value->button_type) && $value->button_type) ? ' jwpf-btn-' . $value->button_type : ' jwpf-btn-default';
										$button_class .= (isset($value->button_size) && $value->button_size) ? ' jwpf-btn-' . $value->button_size : '';
										$button_class .= (isset($value->button_shape) && $value->button_shape) ? ' jwpf-btn-' . $value->button_shape : ' jwpf-btn-rounded';
										$button_class .= (isset($value->button_appearance) && $value->button_appearance) ? ' jwpf-btn-' . $value->button_appearance : '';
										$button_class .= (isset($value->button_block) && $value->button_block) ? ' ' . $value->button_block : '';
										$button_icon = (isset($value->button_icon) && $value->button_icon) ? $value->button_icon : '';
										$button_icon_position = (isset($value->button_icon_position) && $value->button_icon_position) ? $value->button_icon_position : 'left';
										$button_target = (isset($value->button_target) && $value->button_target) ? $value->button_target : '_self';
										$button_url = $valued['link'];

										$icon_arr = array_filter(explode(' ', $button_icon));
										if (count($icon_arr) === 1) {
											$button_icon = 'fa ' . $button_icon;
										}

										if ($button_icon_position == 'left') {
											$value->button_text = ($button_icon) ? '<i aria-hidden="true" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_BUTTON_TEXT') . '" class="' . $button_icon . '" aria-hidden="true"></i> ' . $value->button_text : $value->button_text;
										} else {
											$value->button_text = ($button_icon) ? $value->button_text . ' <i aria-hidden="true" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_BUTTON_TEXT') . '" class="' . $button_icon . '" aria-hidden="true"></i>' : $value->button_text;
										}

										$output .= (isset($value->button_text)) ? '<a href="' . $button_url . '"  target="' . $button_target . '" ' . ($button_target === '_blank' ? 'rel="noopener noreferrer"' : '') . ' id="btn-' . ($this->addon->id + $key) . '" class="jwpf-btn' . $button_class . '">' . $value->button_text . '</a>' : '';
									}
								}

								$output .= '</div>';
								$output .= '</div>';
								$output .= '<div class="jwpf-col-sm-' . $imageColumn . ' jwpf-col-xs-12">';
								$output .= '<div class="jwpf-text-right">';

								$output .= ($image) ? '<img class="jwpf-img-reponsive" src="' . $image . '" alt="' . $valued['banner_title'] . '">' : '';
								
								$output .= '</div>';//.jwpf-text-right
								$output .= '</div>';//.jwpf-col-xs-12
								$output .= '</div>';//.jwpf-row
								if (!$full_container) {
									$output .= '</div>';//.jwpf-container
								}

								$output .= '</div>';//no class
								$output .= '</div>';//.jwpf-carousel-pro-inner-content

								$output .= '</div>';//.jwpf-carousel-item-inner
								if ($valued['link']){
						             $output .= '</a>';  //A标签结束
						        }
								$output .= '</div>';//.jwpf-item
							}
                        }else{
                            $output .= '<div style="height:50px;line-height:80px;text-align:center;">请在客户端对应分类下添加轮播图</div>';
                        }
                    }

                }else{


					if($value->text_from && $value->text_from == 1 && $value->text_id) {
	                	$infos = JwPageFactoryBase::getBannerById($text_id);
	                	if($infos){	
							$value->bg=$infos->image_intro;
							$value->image=$infos->qrcode;
							$value->title=$infos->banner_title;
							$value->content=$infos->introtext;
	                	}
					}

					$bg_class = (isset($value->bg) && $value->bg) ? ' jwpf-item-has-bg' : '';
					$video = (isset($value->video) && $value->video) ? $value->video : '';
					$image = (isset($value->image) && $value->image) ? $value->image : '';
					$bg = '';
					$bg_image = '';
					if (isset($value->bg) && $value->bg) {
						if (strpos($value->bg, "http://") !== false || strpos($value->bg, "https://") !== false) {
							$bg = (isset($value->bg) && $value->bg) ? $value->bg : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg';
							$bg_image = (isset($value->bg) && $value->bg) ? 'style="background-image: url(' . $value->bg . '); background-repeat: no-repeat; background-position: center center; background-size: cover;"' : '';
						} else {
							$bg = (isset($value->bg) && $value->bg) ? JURI::base() . $value->bg : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg';
							$bg_image = (isset($value->bg) && $value->bg) ? 'style="background-image: url(' . JURI::base() . '/' . $value->bg . '); background-repeat: no-repeat; background-position: center center; background-size: cover;"' : '';
						}
					}
					$alt_text = (isset($value->title) && $value->title) ? $value->title : '';

					$output .= '<div id="jwpf-item-' . $this->addon->id . $key . '" class="jwpf-item' . $bg_class . (($key == 0) ? ' active' : '') . ' carousel-item-' . ($key + 1) . '" ' . ($carousel_height ? $bg_image : '') . '
					>';

					if (!$carousel_height) {
						$output .= ($bg) ? '<img src="' . $bg . '" alt="' . $alt_text . '">' : '';
					}
					if ($value->media_url_show && $value->media_url && !$value->button_text){
	                   $output .='<a href="'.$value->media_url.'" target="'.$value->media_target.'">';//20210630 添加设置链接
	                }
					$output .= '<div class="jwpf-carousel-item-inner">';
					$output .= '<div class="jwpf-carousel-pro-inner-content">';
					$output .= '<div>';
					if (!$full_container) {
						$output .= '<div class="jwpf-container">';
					}
					$output .= '<div class="jwpf-row">';
					$output .= '<div class="jwpf-col-sm-' . $textColumn . ' jwpf-col-xs-12">';
					$output .= '<div class="jwpf-carousel-pro-text">';

					if ((isset($value->title) && $value->title) || (isset($value->content) && $value->content)) {
						$output .= (isset($value->title) && $value->title) ? '<h2>' . $value->title . '</h2>' : '';
						$output .= (isset($value->content) && $value->content) ? '<div class="jwpf-carousel-pro-content">' . $value->content . '</div>' : '';
						if (isset($value->button_text) && $value->button_text) {
							$button_class = (isset($value->button_type) && $value->button_type) ? ' jwpf-btn-' . $value->button_type : ' jwpf-btn-default';
							$button_class .= (isset($value->button_size) && $value->button_size) ? ' jwpf-btn-' . $value->button_size : '';
							$button_class .= (isset($value->button_shape) && $value->button_shape) ? ' jwpf-btn-' . $value->button_shape : ' jwpf-btn-rounded';
							$button_class .= (isset($value->button_appearance) && $value->button_appearance) ? ' jwpf-btn-' . $value->button_appearance : '';
							$button_class .= (isset($value->button_block) && $value->button_block) ? ' ' . $value->button_block : '';
							$button_icon = (isset($value->button_icon) && $value->button_icon) ? $value->button_icon : '';
							$button_icon_position = (isset($value->button_icon_position) && $value->button_icon_position) ? $value->button_icon_position : 'left';
							$button_target = (isset($value->button_target) && $value->button_target) ? $value->button_target : '_self';
							$button_url = (isset($value->button_url) && $value->button_url) ? $value->button_url : '';

							$icon_arr = array_filter(explode(' ', $button_icon));
							if (count($icon_arr) === 1) {
								$button_icon = 'fa ' . $button_icon;
							}

							if ($button_icon_position == 'left') {
								$value->button_text = ($button_icon) ? '<i aria-hidden="true" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_BUTTON_TEXT') . '" class="' . $button_icon . '" aria-hidden="true"></i> ' . $value->button_text : $value->button_text;
							} else {
								$value->button_text = ($button_icon) ? $value->button_text . ' <i aria-hidden="true" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_BUTTON_TEXT') . '" class="' . $button_icon . '" aria-hidden="true"></i>' : $value->button_text;
							}

							$output .= (isset($value->button_text)) ? '<a href="' . $button_url . '"  target="' . $button_target . '" ' . ($button_target === '_blank' ? 'rel="noopener noreferrer"' : '') . ' id="btn-' . ($this->addon->id + $key) . '" class="jwpf-btn' . $button_class . '">' . $value->button_text . '</a>' : '';
						}
					}

					$output .= '</div>';
					$output .= '</div>';
					$output .= '<div class="jwpf-col-sm-' . $imageColumn . ' jwpf-col-xs-12">';
					$output .= '<div class="jwpf-text-right">';

					if ($video) {

						$video = parse_url($video);

						switch ($video['host']) {
							case 'youtu.be':
								$id = trim($video['path'], '/');
								$src = '//www.youtube.com/embed/' . $id;
								break;

							case 'www.youtube.com':
							case 'youtube.com':
								parse_str($video['query'], $query);
								$id = $query['v'];
								$src = '//www.youtube.com/embed/' . $id;
								break;

							case 'vimeo.com':
							case 'www.vimeo.com':
								$id = trim($video['path'], '/');
								$src = "//player.vimeo.com/video/{$id}";
						}

						$output .= '<div class="jwpf-embed-responsive jwpf-embed-responsive-16by9">';
						$output .= '<iframe class="jwpf-embed-responsive-item" src="' . $src . '" webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>';
						$output .= '</div>';

					} else {
						$output .= ($image) ? '<img class="jwpf-img-reponsive" src="' . $image . '" alt="' . (isset($value->title) ? $value->title : '') . '">' : '';
					}


					$output .= '</div>';//.jwpf-text-right
					$output .= '</div>';//.jwpf-col-xs-12
					$output .= '</div>';//.jwpf-row
					if (!$full_container) {
						$output .= '</div>';//.jwpf-container
					}

					$output .= '</div>';//no class
					$output .= '</div>';//.jwpf-carousel-pro-inner-content

					$output .= '</div>';//.jwpf-carousel-item-inner
					if ($value->media_url_show && $value->media_url && !$value->button_text){
			             $output .= '</a>';  //A标签结束
			        }
					$output .= '</div>';//.jwpf-item
                }

			}
		}

		$output .= '</div>';//.jwpf-carousel-inner
		
		if ($arrows) {
			if ($arrow_position !== 'default') {
				$output .= '<div class="jwpf-container jwpf-carousel-pro-arrow-' . $arrow_position . '">';
				$output .= '<div class="jwpf-row">';
				$output .= '<div class="jwpf-col-sm-12">';
			}
			$output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow left jwpf-carousel-control" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa ' . $left_arrow . '" aria-hidden="true"></i></a>';
			$output .= '<a href="#jwpf-carousel-' . $this->addon->id . '" class="jwpf-carousel-arrow right jwpf-carousel-control" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa ' . $right_arrow . '" aria-hidden="true"></i></a>';
			if ($arrow_position !== 'default') {
				$output .= '</div>';
				$output .= '</div>';
				$output .= '</div>';
			}
		}

		$output .= '</div>';//.jwpf-carousel-pro

		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
		$css = '';

		//Item Height
		$carousel_height = (isset($settings->carousel_height) && $settings->carousel_height) ? "height: " . $settings->carousel_height . "px;" : "";

		if ($carousel_height) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-pro .jwpf-item {';
			$css .= $carousel_height;
			$css .= '}';
		}

		//Arrow Style
		$arrow_style = '';
		$arrow_style .= (isset($settings->arrow_height) && $settings->arrow_height) ? "height: " . $settings->arrow_height . "px;" : "";
		$arrow_style .= (isset($settings->arrow_height) && $settings->arrow_height) ? "line-height: " . (($settings->arrow_height) - ($settings->arrow_border_width) - ($settings->arrow_border_width)) . "px;" : "";
		$arrow_style .= (isset($settings->arrow_width) && $settings->arrow_width) ? "width: " . $settings->arrow_width . "px;" : "";
		$arrow_style .= (isset($settings->arrow_background) && $settings->arrow_background) ? "background-color: " . $settings->arrow_background . ";" : "";
		$arrow_style .= (isset($settings->arrow_color) && $settings->arrow_color) ? "color: " . $settings->arrow_color . ";" : "";
		$arrow_style .= (isset($settings->arrow_margin) && trim($settings->arrow_margin)) ? "margin: " . $settings->arrow_margin . ";" : "";
		$arrow_style .= (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? "font-size: " . $settings->arrow_font_size . "px;" : "";
		$arrow_style .= (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? "border-width: " . $settings->arrow_border_width . "px;" : "";
		$arrow_style .= (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? "border-style: solid;" : "";
		$arrow_style .= (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? "border-color: " . $settings->arrow_border_color . ";" : "";
		$arrow_style .= (isset($settings->arrow_border_radius) && $settings->arrow_border_radius) ? "border-radius: " . $settings->arrow_border_radius . "px;" : "";

		if ($arrow_style) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-pro .jwpf-carousel-control{';
			$css .= $arrow_style;
			$css .= '}';
		}

		//Arrow hover style
		$arrow_hover_style = '';
		$arrow_hover_style .= (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? "background-color: " . $settings->arrow_hover_background . ";" : "";
		$arrow_hover_style .= (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? "color: " . $settings->arrow_hover_color . ";" : "";
		$arrow_hover_style .= (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? "border-color: " . $settings->arrow_hover_border_color . ";" : "";

		if ($arrow_hover_style) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-pro .jwpf-carousel-control:hover{';
			$css .= $arrow_hover_style;
			$css .= '}';
		}


		// Buttons style
		foreach ($settings->jw_carouselpro_item as $key => $value) {

			$uniqid = '#jwpf-item-' . $this->addon->id . $key . ' ';

			if (isset($value->button_text)) {
				$css_path = new JLayoutFile('addon.css.button', $layout_path);
				$css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $value, 'id' => 'btn-' . ($this->addon->id + $key)));
			}

			// Title
			$title_css = (isset($value->title_fontsize) && $value->title_fontsize) ? 'font-size: ' . $value->title_fontsize . 'px;' : '';
			$title_css .= (isset($value->title_lineheight) && $value->title_lineheight) ? 'line-height: ' . $value->title_lineheight . 'px;' : '';
			$title_css .= (isset($value->title_margin) && trim($value->title_margin)) ? 'margin: ' . $value->title_margin . ';' : '';
			$title_css .= (isset($value->title_color) && $value->title_color) ? 'color: ' . $value->title_color . ';' : '';
			$title_css .= (isset($value->title_letterspace) && $value->title_letterspace) ? 'letter-spacing: ' . $value->title_letterspace . ';' : '';
			$title_css .= (isset($value->title_font_family) && $value->title_font_family) ? 'font-family: ' . $value->title_font_family . ';' : '';

			$title_fontstyle = (isset($value->title_fontstyle) && $value->title_fontstyle) ? $value->title_fontstyle : '';
			if (isset($title_fontstyle->underline) && $title_fontstyle->underline) {
				$title_css .= 'text-decoration:underline;';
			}
			if (isset($title_fontstyle->italic) && $title_fontstyle->italic) {
				$title_css .= 'font-style:italic;';
			}
			if (isset($title_fontstyle->uppercase) && $title_fontstyle->uppercase) {
				$title_css .= 'text-transform:uppercase;';
			}
			if (isset($title_fontstyle->weight) && $title_fontstyle->weight) {
				$title_css .= 'font-weight:' . $title_fontstyle->weight . ';';
			}

			if ($title_css) {
				$css .= $uniqid . '.jwpf-carousel-pro-text h2 {' . $title_css . '}';
			}

			$title_css_sm = (isset($value->title_fontsize_sm) && $value->title_fontsize_sm) ? 'font-size: ' . $value->title_fontsize_sm . 'px;' : '';
			$title_css_sm .= (isset($value->title_lineheight_sm) && $value->title_lineheight_sm) ? 'line-height: ' . $value->title_lineheight_sm . 'px;' : '';
			$title_css_sm .= (isset($value->title_margin_sm) && $value->title_margin_sm) ? 'margin: ' . $value->title_margin_sm . ';' : '';

			if ($title_css_sm) {
				$css .= '@media (min-width: 768px) and (max-width: 991px) {';
				$css .= $uniqid . '.jwpf-carousel-pro-text h2 {' . $title_css_sm . '}';
				$css .= '}';
			}

			$title_css_xs = (isset($value->title_fontsize_xs) && $value->title_fontsize_xs) ? 'font-size: ' . $value->title_fontsize_xs . 'px;' : '';
			$title_css_xs .= (isset($value->title_lineheight_xs) && $value->title_lineheight_xs) ? 'line-height: ' . $value->title_lineheight_xs . 'px;' : '';
			$title_css_xs .= (isset($value->title_margin_xs) && $value->title_margin_xs) ? 'margin: ' . $value->title_margin_xs . ';' : '';

			if ($title_css_xs) {
				$css .= '@media (max-width: 767px) {';
				$css .= $uniqid . '.jwpf-carousel-pro-text h2 {' . $title_css_xs . '}';
				$css .= '}';
			}

			// Content
			$content_css = (isset($value->content_fontsize) && $value->content_fontsize) ? 'font-size: ' . $value->content_fontsize . 'px;' : '';
			$content_css .= (isset($value->content_lineheight) && $value->content_lineheight) ? 'line-height: ' . $value->content_lineheight . 'px;' : '';
			$content_css .= (isset($value->content_margin) && trim($value->content_margin)) ? 'margin: ' . $value->content_margin . ';' : '';
			$content_css .= (isset($value->content_fontweight) && $value->content_fontweight) ? 'font-weight: ' . $value->content_fontweight . ';' : '';
			$content_css .= (isset($value->content_font_family) && $value->content_font_family) ? 'font-family: ' . $value->content_font_family . ';' : '';
			$content_css .= (isset($value->content_color) && $value->content_color) ? 'color: ' . $value->content_color .';' : '';

			if ($content_css) {
				$css .= $uniqid . '.jwpf-carousel-pro-text .jwpf-carousel-pro-content {' . $content_css . '}';
			}

			$content_css_sm = (isset($value->content_fontsize_sm) && $value->content_fontsize_sm) ? 'font-size: ' . $value->content_fontsize_sm . 'px;' : '';
			$content_css_sm .= (isset($value->content_lineheight_sm) && $value->content_lineheight_sm) ? 'line-height: ' . $value->content_lineheight_sm . 'px;' : '';
			$content_css_sm .= (isset($value->content_margin_sm) && trim($value->content_margin_sm)) ? 'margin: ' . $value->content_margin_sm . ';' : '';

			if ($content_css_sm) {
				$css .= '@media (min-width: 768px) and (max-width: 991px) {';
				$css .= $uniqid . '.jwpf-carousel-pro-text .jwpf-carousel-pro-content {' . $content_css_sm . '}';
				$css .= '}';
			}

			$content_css_xs = (isset($value->content_fontsize_xs) && $value->content_fontsize_xs) ? 'font-size: ' . $value->content_fontsize_xs . 'px;' : '';
			$content_css_xs .= (isset($value->content_lineheight_xs) && $value->content_lineheight_xs) ? 'line-height: ' . $value->content_lineheight_xs . 'px;' : '';
			$content_css_xs .= (isset($value->content_margin_xs) && trim($value->content_margin_xs)) ? 'margin: ' . $value->content_margin_xs . ';' : '';

			if ($content_css_xs) {
				$css .= '@media (max-width: 767px) {';
				$css .= $uniqid . '.jwpf-carousel-pro-text .jwpf-carousel-pro-content {' . $content_css_xs . '}';
				$css .= '}';
			}

		}
		//Item Style
		$item_padding = (isset($settings->item_padding) && trim($settings->item_padding)) ? $settings->item_padding : '';
		$speed = (isset($settings->speed) && $settings->speed) ? $settings->speed : 600;
		$css .= $addon_id . ' .jwpf-carousel-inner > .jwpf-item{';
		$css .= '-webkit-transition-duration: ' . $speed . 'ms;';
		$css .= 'transition-duration: ' . $speed . 'ms;';
		if ($item_padding) {
			$css .= 'padding:' . $item_padding . ';';
		}
		$css .= '}';

		//Item Tablet Style
		$carousel_height_sm = (isset($settings->carousel_height_sm) && $settings->carousel_height_sm) ? "height: " . $settings->carousel_height_sm . "px;" : "";
		$item_padding_sm = (isset($settings->item_padding_sm) && trim($settings->item_padding_sm)) ? $settings->item_padding_sm : '';
		if ($item_padding_sm || $carousel_height_sm) {
			$css .= '@media (min-width: 768px) and (max-width: 991px) {';
			$css .= $addon_id . ' .jwpf-carousel-inner > .jwpf-item{';
			$css .= 'padding:' . $item_padding_sm . ';';
			$css .= '}';
			if ($carousel_height_sm) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-pro .jwpf-item {';
				$css .= $carousel_height_sm;
				$css .= '}';
			}
			$css .= '}';
		}
		//Item Mobile style
		$carousel_height_xs = (isset($settings->carousel_height_xs) && $settings->carousel_height_xs) ? "height: " . $settings->carousel_height_xs . "px;" : "";
		$item_padding_xs = (isset($settings->item_padding_xs) && trim($settings->item_padding_xs)) ? $settings->item_padding_xs : '';
		if ($item_padding_xs || $carousel_height_xs) {
			$css .= '@media (max-width: 767px) {';
			$css .= $addon_id . ' .jwpf-carousel-inner > .jwpf-item{';
			$css .= 'padding:' . $item_padding_xs . ';';
			$css .= '}';
			if ($carousel_height_xs) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-pro .jwpf-item {';
				$css .= $carousel_height_xs;
				$css .= '}';
			}
			$css .= '}';
		}

		return $css;
	}

	public static function getTemplate()
	{
		$company_id             = $_GET['company_id'] ?? 0;
        $site_id                = $_GET['site_id'] ?? 0;
        $infoss = JwPageFactoryBase::getBannerLists($site_id, $company_id);
        $atts=json_encode($infoss);

		$output = '
		<#
        var infoss = ' . $atts . ';
	
		var interval = data.interval ? parseInt(data.interval) * 1000 : 5000;
		var autoplay = data.autoplay ? \'data-jwpf-ride="jwpf-carousel"\' : "";
		if(data.autoplay==0){
			interval = "false";
		}
		#>
		<style type="text/css">
			#jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
				-webkit-transition-duration: {{ data.speed }}ms;
				transition-duration: {{ data.speed }}ms;
			}

			#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .jwpf-item {
				<# if(_.isObject(data.carousel_height)){ #>
					height: {{data.carousel_height.md}}px;
				<# } else { #>
					height: {{data.carousel_height}}px;
				<# } #>
			}
			#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .jwpf-carousel-control{
				width: {{data.arrow_width}}px;
				height: {{data.arrow_height}}px;
				background-color: {{data.arrow_background}};
				color: {{data.arrow_color}};
				<# if(data.arrow_margin){ #>
					margin: {{data.arrow_margin}};
				<# } #>
				font-size: {{data.arrow_font_size}}px;
				<# if(data.arrow_height){ #>
					line-height: {{data.arrow_height-data.arrow_border_width-data.arrow_border_width}}px;
				<# } #>
				border-width: {{data.arrow_border_width}}px;
				<# if(data.arrow_border_width){ #>
					border-style: solid;
				<# } #>
				border-color: {{data.arrow_border_color}};
				border-radius: {{data.arrow_border_radius}}px;
			}
			#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .jwpf-carousel-control:hover{
				background-color: {{data.arrow_hover_background}};
				color: {{data.arrow_hover_color}};
				border-color: {{data.arrow_hover_border_color}};
			}
			<# _.each(data.jw_carouselpro_item, function (carousel_item, key){ #>
				<#
					var button_fontstyle = carousel_item.button_fontstyle || "";

					var modern_font_style = false;

					var margin = window.getMarginPadding(carousel_item.title_margin, "margin");
					var content_margin = window.getMarginPadding(carousel_item.content_margin, "margin");
				#>
				#jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-{{ carousel_item.button_type }}{
					letter-spacing: {{ carousel_item.button_letterspace }};

					<# if(_.isObject(carousel_item.button_font_style) && carousel_item.button_font_style.underline) { #>
						text-decoration: underline;
						<# modern_font_style = true #>
					<# } #>

					<# if(_.isObject(carousel_item.button_font_style) && carousel_item.button_font_style.italic) { #>
						font-style: italic;
						<# modern_font_style = true #>
					<# } #>

					<# if(_.isObject(carousel_item.button_font_style) && carousel_item.button_font_style.uppercase) { #>
						text-transform: uppercase;
						<# modern_font_style = true #>
					<# } #>

					<# if(_.isObject(carousel_item.button_font_style) && carousel_item.button_font_style.weight) { #>
						font-weight: {{ carousel_item.button_font_style.weight }};
						<# modern_font_style = true #>
					<# } #>

					<# if(modern_font_style && _.isArray(button_fontstyle)) { #>
						<# if(button_fontstyle.indexOf("underline") !== -1){ #>
							text-decoration: underline;
						<# } #>
						<# if(button_fontstyle.indexOf("uppercase") !== -1){ #>
							text-transform: uppercase;
						<# } #>
						<# if(button_fontstyle.indexOf("italic") !== -1){ #>
							font-style: italic;
						<# } #>
						<# if(button_fontstyle.indexOf("lighter") !== -1){ #>
							font-weight: lighter;
						<# } else if(button_fontstyle.indexOf("normal") !== -1){#>
							font-weight: normal;
						<# } else if(button_fontstyle.indexOf("bold") !== -1){#>
							font-weight: bold;
						<# } else if(button_fontstyle.indexOf("bolder") !== -1){#>
							font-weight: bolder;
						<# } #>
					<# } #>
				}

				#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text h2{
					<# if(_.isObject(carousel_item.title_fontsize)){ #>
						font-size: {{ carousel_item.title_fontsize.md }}px;
					<# } else { #>
						font-size: {{ carousel_item.title_fontsize }}px;
					<# } #>

					<# if(_.isObject(carousel_item.title_lineheight)){ #>
						line-height: {{ carousel_item.title_lineheight.md }}px;
					<# } else { #>
						line-height: {{ carousel_item.title_lineheight }}px;
					<# } #>

					<# if(_.isObject(margin)){ #>
						{{ margin.md }}
					<# } else { #>
						{{ margin }}
					<# } #>

					color: {{ carousel_item.title_color }};
					letter-spacing: {{ carousel_item.title_letterspace }};
					font-family: {{ carousel_item.title_font_family }};

					<# if(_.isObject(carousel_item.title_fontstyle)){
						if(carousel_item.title_fontstyle.underline){ #>
							text-decoration:underline;
						<# }
						if(carousel_item.title_fontstyle.italic){ #>
							font-style:italic;
						<# }
						if(carousel_item.title_fontstyle.uppercase){ #>
							text-transform:uppercase;
						<# }
						if(carousel_item.title_fontstyle.weight){ #>
							font-weight:{{carousel_item.title_fontstyle.weight}};
						<# }
					} #>
				}

				#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text .jwpf-carousel-pro-content{
					<# if(_.isObject(carousel_item.content_fontsize)){ #>
						font-size: {{ carousel_item.content_fontsize.md }}px;
					<# } else { #>
						font-size: {{ carousel_item.content_fontsize }}px;
					<# } #>
                    color: {{ carousel_item.content_color }};
					<# if(_.isObject(carousel_item.content_lineheight)){ #>
						line-height: {{ carousel_item.content_lineheight.md }}px;
					<# } else { #>
						line-height: {{ carousel_item.content_lineheight }}px;
					<# } #>

					<# if(_.isObject(content_margin)){ #>
						{{ content_margin.md }}
					<# } else { #>
						{{ content_margin }}
					<# } #>
					
					<# if(carousel_item.content_font_family){ #>
						font-family: {{carousel_item.content_font_family}};
					<# } #>
					<# if(carousel_item.content_fontweight){ #>
						font-weight: {{carousel_item.content_fontweight}};
					<# } #>
				}
	
				<# if(carousel_item.button_type == "custom"){ #>
					#jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom{
						color: {{ carousel_item.button_color }};
						<# if(carousel_item.button_appearance == "outline"){ #>
							border-color: {{ carousel_item.button_background_color }};
						<# } else if(carousel_item.button_appearance == "3d"){ #>
							border-bottom-color: {{ carousel_item.button_background_color_hover }};
							background-color: {{ carousel_item.button_background_color }};
						<# } else if(carousel_item.button_appearance == "gradient"){ #>
							border: none;
							<# if(typeof carousel_item.button_background_gradient.type !== "undefined" && carousel_item.button_background_gradient.type == "radial"){ #>
								background-image: radial-gradient(at {{ carousel_item.button_background_gradient.radialPos || "center center"}}, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
							<# } else { #>
								background-image: linear-gradient({{ carousel_item.button_background_gradient.deg || 0}}deg, {{ carousel_item.button_background_gradient.color }} {{ carousel_item.button_background_gradient.pos || 0 }}%, {{ carousel_item.button_background_gradient.color2 }} {{ carousel_item.button_background_gradient.pos2 || 100 }}%);
							<# } #>
						<# } else { #>
							background-color: {{ carousel_item.button_background_color }};
						<# }
						if(_.isObject(carousel_item.fontsize)){ 
						#>
							font-size: {{carousel_item.fontsize.md}}px;
						<# }
						if(_.isObject(carousel_item.button_padding)){ 
						#>
							padding: {{carousel_item.button_padding.md}};
						<# } else { #>
							padding: {{carousel_item.button_padding}};
						<# } #>
					}
	
					#jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom:hover{
						color: {{ carousel_item.button_color_hover }};
						background-color: {{ carousel_item.button_background_color_hover }};
						<# if(carousel_item.button_appearance == "outline"){ #>
							border-color: {{ carousel_item.button_background_color_hover }};
						<# } else if(carousel_item.button_appearance == "gradient"){ #>
							<# if(typeof carousel_item.button_background_gradient_hover.type !== "undefined" && carousel_item.button_background_gradient_hover.type == "radial"){ #>
								background-image: radial-gradient(at {{ carousel_item.button_background_gradient_hover.radialPos || "center center"}}, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
							<# } else { #>
								background-image: linear-gradient({{ carousel_item.button_background_gradient_hover.deg || 0}}deg, {{ carousel_item.button_background_gradient_hover.color }} {{ carousel_item.button_background_gradient_hover.pos || 0 }}%, {{ carousel_item.button_background_gradient_hover.color2 }} {{ carousel_item.button_background_gradient_hover.pos2 || 100 }}%);
							<# } #>
						<# } #>
					}
				<# } #>

				@media (min-width: 768px) and (max-width: 991px) {
					#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text h2{
						<# if(_.isObject(carousel_item.title_fontsize)){ #>
							font-size: {{ carousel_item.title_fontsize.sm }}px;
						<# } #>
						<# if(_.isObject(carousel_item.title_lineheight)){ #>
							line-height: {{ carousel_item.title_lineheight.sm }}px;
						<# } #>
						<# if(_.isObject(margin)){ #>
							{{ margin.sm }}
						<# } #>
					}

					#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text .jwpf-carousel-pro-content{
						<# if(_.isObject(carousel_item.content_fontsize)){ #>
							font-size: {{ carousel_item.content_fontsize.sm }}px;
						<# } #>
	
						<# if(_.isObject(carousel_item.content_lineheight)){ #>
							line-height: {{ carousel_item.content_lineheight.sm }}px;
						<# } #>
	
						<# if(_.isObject(content_margin)){ #>
							{{ content_margin.sm }}
						<# } #>
					}
					<# if(carousel_item.button_type == "custom"){ #>
						#jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom{
							<# if(_.isObject(carousel_item.fontsize)){ #>
								font-size: {{carousel_item.fontsize.sm}}px;
							<# }
							if(_.isObject(carousel_item.button_padding)){ 
							#>
								padding: {{carousel_item.button_padding.sm}};
							<# } #>
						}
					<# } #>
				}

				@media (max-width: 767px) {
					#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text h2{
						<# if(_.isObject(carousel_item.title_fontsize)){ #>
							font-size: {{ carousel_item.title_fontsize.xs }}px;
						<# } #>
						<# if(_.isObject(carousel_item.title_lineheight)){ #>
							line-height: {{ carousel_item.title_lineheight.xs }}px;
						<# } #>
						<# if(_.isObject(margin)){ #>
							{{ margin.xs }}
						<# } #>
					}

					#jwpf-item-{{  data.id  }}{{ key }} .jwpf-carousel-pro-text .jwpf-carousel-pro-content{
						<# if(_.isObject(carousel_item.content_fontsize)){ #>
							font-size: {{ carousel_item.content_fontsize.xs }}px;
						<# } #>
	
						<# if(_.isObject(carousel_item.content_lineheight)){ #>
							line-height: {{ carousel_item.content_lineheight.xs }}px;
						<# } #>
	
						<# if(_.isObject(content_margin)){ #>
							{{ content_margin.xs }}
						<# } #>
					}
					<# if(carousel_item.button_type == "custom"){ #>
						#jwpf-addon-{{ data.id }} #btn-{{ data.id + "" + key }}.jwpf-btn-custom{
							<# if(_.isObject(carousel_item.fontsize)){ #>
								font-size: {{carousel_item.fontsize.xs}}px;
							<# }
							if(_.isObject(carousel_item.button_padding)){ 
							#>
								padding: {{carousel_item.button_padding.xs}};
							<# } #>
						}
					<# } #>
				}
			<# }); #>

			<# if(_.isObject(data.item_padding)){ #>
				#jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
					padding:{{data.item_padding.md}};
				}
			<# } #>
			@media (min-width: 768px) and (max-width: 991px) {
				<# if(_.isObject(data.item_padding) && data.item_padding.sm){ #>
					#jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
						padding:{{data.item_padding.sm}};
					}
				<# } #>
				<# if(_.isObject(data.carousel_height)){ #>
					#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .jwpf-item {
						height: {{data.carousel_height.sm}}px;
					}
				<# } #>
			}
			@media (max-width: 767px) {
				<# if(_.isObject(data.item_padding) && data.item_padding.xs){ #>
					#jwpf-addon-{{ data.id }} .jwpf-carousel-inner > .jwpf-item{
						padding:{{data.item_padding.xs}};
					}
				<# } #>
				<# if(_.isObject(data.carousel_height)){ #>
					#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .jwpf-item {
						height: {{data.carousel_height.xs}}px;
					}
				<# } #>
			}
			<# if(!_.isEmpty(data.carousel_height) && data.carousel_height.md !== ""){
				_.each (data.jw_carouselpro_item, function(carousel_item, key) { 
					console.log(carousel_item.text_id);
					console.log(carousel_item.bg+88);

				#>
					#jwpf-addon-{{ data.id }} .jwpf-carousel-pro .item-{{ data.id }}-{{ key }} {
							
						<# if(carousel_item.text_from && carousel_item.text_from == 1 && carousel_item.text_id) {
	                        let newArr = infoss.filter(item => item.id == carousel_item.text_id)
	                        var item1_ = ""
	                        if(newArr.length > 0) {
	                            item1_ = newArr[0]
	                        	carousel_item.bg = item1_.image_intro
	                        }
	                    } #>

						<# if(carousel_item.bg){
							if(carousel_item.bg.indexOf("http://") == 0 || carousel_item.bg.indexOf("https://") == 0){ #>
								background: url({{ carousel_item.bg }});
							<# } else { #>
								background: url({{ pagefactory_base + carousel_item.bg }});
							<# }
						} #>
						background-repeat: no-repeat;
						background-size: cover;
						background-position: center center;
					}
				<# })
			} #>

		</style>
		<#
			let content_column = (!_.isEmpty(data.content_column) && data.content_column) ? data.content_column : "";
			let textColumn = "";
			let imageColumn = "";
			if(content_column){
				textColumn = content_column;
				imageColumn = (12-content_column);
			} else {
				textColumn = 6;
				imageColumn = 6;
			} 
			let arrow_icon = (!_.isEmpty(data.arrow_icon)) ? data.arrow_icon : "angle";
			let left_arrow ="";
			let right_arrow = "";
			if(arrow_icon=="angle_dubble"){
				left_arrow ="fa-angle-double-left";
				right_arrow = "fa-angle-double-right";
			} else if(arrow_icon=="arrow"){
				left_arrow ="fa-arrow-left";
				right_arrow = "fa-arrow-right";
			} else if(arrow_icon=="arrow_circle"){
				left_arrow ="fa-arrow-circle-o-left";
				right_arrow = "fa-arrow-circle-o-right";
			} else if(arrow_icon=="long_arrow"){
				left_arrow ="fa-long-arrow-left";
				right_arrow = "fa-long-arrow-right";
			} else if(arrow_icon=="angle"){
				left_arrow ="fa-angle-left";
				right_arrow = "fa-angle-right";
			} else{
				left_arrow ="fa-chevron-left";
				right_arrow = "fa-chevron-right";
			}
			if(!data.arrow_position){
				data.arrow_position = "default"
			}
		#>
		<div id="jwpf-carousel-{{data.id}}" class="jwpf-carousel jwpf-carousel-pro jwpf-slide {{ data.class }}" data-interval="{{ interval }}" {{{ autoplay }}}>
			<# if(data.controllers){ #>
				<ol class="jwpf-carousel-indicators  jwpf-carousel-indicators_preview">
				<# _.each(data.jw_carouselpro_item, function (carousel_item, key){ #>
					<# var active = (key == 0) ? "active" : ""; #>
					<li data-jwpf-target="#jwpf-carousel-{{ data.id }}"  class="{{ active }}"  data-jwpf-slide-to="{{ key }}"></li>
				<# }); #>
				</ol>
			<# } #>
			<div class="jwpf-carousel-inner {{ data.alignment }}">
				<# _.each(data.jw_carouselpro_item, function (carousel_item, key){ #>
					
					<# if(carousel_item.text_from && carousel_item.text_from == 1 && carousel_item.text_id) {
                        let newArr = infoss.filter(item => item.id == carousel_item.text_id)
                        // console.log(newArr)
                        var item_ = ""
                        if(newArr.length > 0) {
                            item_ = newArr[0]
	                        carousel_item.title = item_.banner_title
	                        carousel_item.bg = item_.image_intro
	                        carousel_item.content = item_.introtext
	                        carousel_item.image = item_.image_small
							// console.log(carousel_item.bg);
                        }

                    } #>
                    <#
						var classNames = (key == 0) ? "active" : "";
						classNames += (carousel_item.bg) ? " jwpf-item-has-bg" : "";

					#>

					<div class="jwpf-item {{ classNames }} item-{{ data.id }}-{{ key }}" id="jwpf-item-{{  data.id  }}{{ key }}" >
					<# if(_.isObject(data.carousel_height) && _.isEmpty(data.carousel_height.md)){
						if(carousel_item.bg){ #>
							<img src=\'{{ carousel_item.bg }}\' alt="{{ carousel_item.title }}">
						<# }
					} #>
						<div class="jwpf-carousel-item-inner">
							<div>
								<div>
								<# if(!data.full_container){ #>
									<div class="jwpf-container">
								<# } #>
									<div class="jwpf-row">
										<div class="jwpf-col-sm-{{textColumn}} jwpf-col-xs-12">
											<div class="jwpf-carousel-pro-text">
												<# if(carousel_item.title || carousel_item.content) { #>
													<# if(carousel_item.title) { #>
														<h2 class="jw-editable-content" id="addon-title-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carouselpro_item-{{key}}-title">{{ carousel_item.title }}</h2>
													<# } #>
													<# if(carousel_item.content) { #>
														<div class="jwpf-carousel-pro-content jw-editable-content" id="addon-content-{{data.id}}-{{key}}" data-id={{data.id}} data-fieldName="jw_carouselpro_item-{{key}}-content">{{{ carousel_item.content }}}</div>
													<# } #>
													<# if(carousel_item.button_text) { #>
														<#
															var btnClass = "";
															btnClass += carousel_item.button_type ? " jwpf-btn-"+carousel_item.button_type : " jwpf-btn-default" ;
															btnClass += carousel_item.button_size ? " jwpf-btn-"+carousel_item.button_size : "" ;
															btnClass += carousel_item.button_shape ? " jwpf-btn-"+carousel_item.button_shape : " jwpf-btn-rounded" ;
															btnClass += carousel_item.button_appearance ? " jwpf-btn-"+carousel_item.button_appearance : "" ;
															btnClass += carousel_item.button_block ? " "+carousel_item.button_block : "" ;
															var button_text = carousel_item.button_text;
					
															let icon_arr = (typeof carousel_item.button_icon !== "undefined" && carousel_item.button_icon) ? carousel_item.button_icon.split(" ") : "";
															let icon_name = icon_arr.length === 1 ? "fa "+carousel_item.button_icon : carousel_item.button_icon;
					
															if(carousel_item.button_icon_position == "left"){
																button_text = (carousel_item.button_icon) ? \'<i class="\'+icon_name+\'"></i> \'+carousel_item.button_text : carousel_item.button_text ;
															}else{
																button_text = (carousel_item.button_icon) ? carousel_item.button_text+\' <i class="\'+icon_name+\'"></i>\' : carousel_item.button_text ;
															}
														#>
														<a href=\'{{ carousel_item.button_url }}\' target="{{ carousel_item.button_target }}" id="btn-{{ data.id + "" + key}}" class="jwpf-btn{{ btnClass }}">{{{ button_text }}}</a>
													<# } #>
												<# } #>
											</div>
										</div>
										<div class="jwpf-col-sm-{{imageColumn}} jwpf-col-xs-12">
											<div class="jwpf-text-right">
											<# if(carousel_item.video) { #>
												<#
													var video = parseUrl(carousel_item.video),
														src = "";

													if (video.host == "youtu.be") {
														var id = video["path"].replace("/", "");
														src = "//www.youtube.com/embed/"+id;
													} else if(video.host == "www.youtube.com" || video.host == "youtube.com"){
														var id = video["query"].replace("v=", "");
														src = "//www.youtube.com/embed/"+id;
													} else if (video.host == "vimeo.com" || video.host == "www.vimeo.com") {
														var id = video["path"].replace("/", "");
														src = "//player.vimeo.com/video/"+id;
													}
												#>
												<div class="jwpf-embed-responsive jwpf-embed-responsive-16by9">
													<iframe class="jwpf-embed-responsive-item" src=\'{{ src }}\' webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>
												</div>
											<# } else {
												if(carousel_item.image){
													if(carousel_item.image && carousel_item.image.indexOf("https://") == -1 && carousel_item.image.indexOf("http://") == -1){ #>
														<img class="jwpf-img-reponsive" src=\'{{ pagefactory_base + carousel_item.image }}\' alt="{{ carousel_item.title }}">
													<# } else if(carousel_item.image){ #>
														<img class="jwpf-img-reponsive" src=\'{{ carousel_item.image }}\' alt="{{ carousel_item.title }}">
													<# }
												}
											} #>
											</div>
										</div>
										</div>
									<# if(!data.full_container){ #>
									</div>
									<# } #>
								</div>
							</div>
						</div>
					</div>
					
				<# }); #>
			</div>
			
			<# if(data.arrows) {
				if(data.arrow_position!=="default") { #>
					<div class="jwpf-container jwpf-carousel-pro-arrow-{{data.arrow_position}}">
					<div class="jwpf-row">
					<div class="jwpf-col-sm-12">
				<# } #>
					<a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow left jwpf-carousel-control" data-slide="prev"><i class="fa {{left_arrow}}
					"></i></a>
					<a href="#jwpf-carousel-{{ data.id }}" class="jwpf-carousel-arrow right jwpf-carousel-control" data-slide="next"><i class="fa {{right_arrow}}"></i></a>
				<# if(data.arrow_position!=="default") { #>
					</div>
					</div>
					</div>
				<# }
			} #>
		</div>
		';

		return $output;
	}

}
