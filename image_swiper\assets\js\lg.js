$(function(){
	var wow = new WOW({
	    boxClass: 'wow',
	    animateClass: 'animated',
	    offset: 0,
	    mobile: true,
	    live: true
	});
	wow.init();
	$('.z-menu2-d5').eq(0).show();
	$('.z-menu2-d3').mouseover(function(){
		if($('.z-menu2-d5').eq($(this).index()).css('display')=='none'){
			$('.z-menu2-d5').hide();
			$('.z-menu2-d5').eq($(this).index()).fadeIn(500);
		}
	})
	$('.z-hd-bt1').click(function(){
		if($(this).hasClass('z-hd-bt1on')){
			$(this).removeClass('z-hd-bt1on');
			if($(window).width()<1024){
				$('.z-menu1-d1').slideUp(500);
				$('.z-menu1-d3 ul').slideUp(500);
				$('.z-menu1-d3').removeClass('z-menu1-d3on');
			}else{
				$('.z-menu2-d1').slideUp(500);
				$('.z-menu2-d5').hide();
				$('.z-menu2-d5').eq(0).show();
			}
		}else{
			$(this).addClass('z-hd-bt1on');
			if($(window).width()<1024){
				$('.z-menu1-d1').slideDown(500);
			}else{
				$('.z-menu2-d1').slideDown(500);
			}
		}
	})
	$('.z-menu1-d4').click(function(){
		if($(this).parents('.z-menu1-d3').hasClass('z-menu1-d3on')){
			$(this).parents('.z-menu1-d3').removeClass('z-menu1-d3on');
			$(this).parents('.z-menu1-d3').find('ul').slideUp(500);
		}else{
			$('.z-menu1-d3 ul').slideUp(500);
			$('.z-menu1-d3').removeClass('z-menu1-d3on');
			$(this).parents('.z-menu1-d3').addClass('z-menu1-d3on');
			$(this).parents('.z-menu1-d3').find('ul').slideDown(500);
		}
	})
	$('.z-menu1-d3 ul li').click(function(){
		$('.z-menu1-d1').slideUp(500);
		$('.z-menu1-d3 ul').slideUp(500);
		$('.z-menu1-d3').removeClass('z-menu1-d3on');
		$('.z-hd-bt1').removeClass('z-hd-bt1on');
	})
	$('.z-menu2-d3 a').click(function(){
		$('.z-menu2-d1').slideUp(500);
		$('.z-menu2-d5').hide();
		$('.z-menu2-d5').eq(0).show();
		$('.z-hd-bt1').removeClass('z-hd-bt1on');
	})
	$('.z-menu2-d6 a').click(function(){
		$('.z-menu2-d1').slideUp(500);
		$('.z-menu2-d5').hide();
		$('.z-menu2-d5').eq(0).show();
		$('.z-hd-bt1').removeClass('z-hd-bt1on');
	})
	$('.z-lan-d3').mouseenter(function(){
		$(this).find('.z-lan-k1').fadeIn(500);
	})
	$('.z-lan-d3').mouseleave(function(){
		$(this).find('.z-lan-k1').fadeOut(500);
	})
	$('.z-lan-q1').click(function(){
		$('html,body').animate({'scrollTop':0},300,'linear');
	})
	$(window).scroll(function(){
		if($(window).width()<1024){
			if($(window).scrollTop()>800){
				$('.z-bt1').fadeIn(500);
			}else{
				$('.z-bt1').fadeOut(500);
			}
		}
	})
	$('.z-bt1').click(function(){
		$('html,body').animate({'scrollTop':0},300,'linear');
	})
})
function del(a,b,c){var t1=a;for( var i=0 ; i < c.length ; i++ ){var t2=(t1 * b) + 's';t1++;c.eq(i).attr('data-wow-delay',t2);}}
function dur(a1,b1){$(a1).attr('data-wow-duration',(b1+'s'));}
function super1(a1,a2,t1,t2){var swi1='.'+a1;var pag1='.'+a2;var mySwiper = new Swiper (swi1, {loop: true,pagination: {el: pag1,clickable :true,},autoplay:{delay: t1,disableOnInteraction: false,},speed: t2,effect : 'fade',fadeEffect: {crossFade: true,},on:{init: function(){swiperAnimateCache(this);swiperAnimate(this);},slideChangeTransitionEnd: function(){swiperAnimate(this);},},});}
function number(a,b,c,d){var i=c;var j=setInterval(function(){i++;a.html(i);if(i >= b){clearInterval(j);}},d);}
function threedot(a1,b1,b2){if(a1.html().length>b2){var h1=a1.html().slice(b1,b2);a1.html(h1);a1.append('...');}}
function width1(a1,a2,b1){var w1=0;for(var i=0;i<$(a1).length;i++){var w2=$(a1).eq(i).width() + parseFloat($(a1).css('padding-left')) * 2;w1+=w2;}w1*=b1;$(a2).width(w1);}
function navloop1(a1,b1,b2,b3,c1,c2,c3,d1,d2,e1,e2){$(b2).width($(b3).width()/e1);$(c1).width($(b1).length * $(b2).width() + e2);$(c2).width($(c1).width());$(c3).width($(c1).width());$(c2).html($(c1).html());$(c3).html($(c1).html());$(c1).css({'left':(-a1 * $(b2).width())});$(c2).css({'left':(-(a1 + $(b1).length) * $(b2).width())});$(c3).css({'left':(($(b1).length - a1) * $(b2).width())});$(d1).click(function(){var mm1=parseInt($(c1).css('left'))/parseInt($(b2).width()) - 1;$(c1).animate({'left':(mm1 * $(b2).width())},300,'linear');$(c2).animate({'left':(-($(b1).length - mm1) * $(b2).width())},300,'linear');$(c3).animate({'left':(($(b1).length + mm1) * $(b2).width())},300,'linear');time11=setTimeout(function(){if((-mm1)==$(b1).length){$(c1).css({'left':0});$(c2).css({'left':(-$(b1).length * $(b2).width())});$(c3).css({'left':($(b1).length* $(b2).width())});clearTimeout(time11);}},310);});$(d2).click(function(){var mm1=parseInt($(c1).css('left'))/parseInt($(b2).width()) + 1;$(c1).animate({'left':(mm1 * $(b2).width())},300,'linear');$(c2).animate({'left':(-($(b1).length - mm1) * $(b2).width())},300,'linear');$(c3).animate({'left':(($(b1).length + mm1) * $(b2).width())},300,'linear');time22=setTimeout(function(){if(mm1==$(b1).length){$(c1).css({'left':0});$(c2).css({'left':(-$(b1).length * $(b2).width())});$(c3).css({'left':($(b1).length* $(b2).width())});clearTimeout(time22);}},310);})}
function loopnews2(a1,b1,b2,c1){setInterval(function(){for(var i=0;i<$(a1).length;i++){if($(a1).eq(i).hasClass(b1)){if(i==($(a1).length-1)){$(a1).eq(i).removeClass(b1);$(a1).eq(i).addClass(b2);$(a1).eq(0).addClass(b1);}else{$(a1).eq(i).removeClass(b1);$(a1).eq(i).addClass(b2);$(a1).eq(i+1).addClass(b1);}if(i==0){$(a1).eq($(a1).length-2).removeClass(b2);}else if(i==1){$(a1).eq($(a1).length-1).removeClass(b2);}else{$(a1).eq(i-2).removeClass(b2);}break;}}},c1);}
function classify1(a1,a2,b1,b2,c1){var h1=$(b1).height();var h2=$(b2).height();$(a1).click(function(){if($(this).hasClass(a2)){$(this).removeClass(a2);$(b1).animate({'height':h1},c1,'linear');}else{$(this).addClass(a2);$(b1).animate({'height':h2},c1,'linear');}})}
function alink3(a1,a2,a3,b1,c1){if($(a1).first().hasClass(b1)){$(a3).prevAll().hide();}if($(a1).last().hasClass(b1)){$(a3).nextAll().hide();}for(var i=0;i<$(a1).length;i++){if($(a1).length>(2*c1+3)){if($(a1).eq(i).hasClass(b1)){$(a1).hide();if($(a2).index()<(c1+2)){for(var j=0;j<($(a2).index()+(c1+1));j++){$(a1).eq(j).show();}$(a1).last().show();$(a1).last().before('<span>...</span>');}else if($(a2).index()>($(a1).length-(c1+3))){for(var j=($(a1).length-1);j>($(a2).index()-(c1+1));j--){$(a1).eq(j).show();}$(a1).first().show();$(a1).first().after('<span>...</span>');}else{for(var j=($(a2).index()+c1);j>($(a2).index()-(c1+1));j--){$(a1).eq(j).show();}$(a1).last().show();$(a1).last().before('<span>...</span>');$(a1).first().show();$(a1).first().after('<span>...</span>');}break;}}else{break;}}}
function threedot2(a1,a2,b1,b2){for(var i=0;i<$(a1).length;i++){if($(a1).eq(i).find(a2).html().length>b2){var h1=$(a1).eq(i).find(a2).html().slice(b1,b2);$(a1).eq(i).find(a2).html(h1);$(a1).eq(i).find(a2).append('...');}}}