<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'icon_bg_enlarge',
        'title' => '图标光圈放大',
        'desc' => '',
        'category' => '图标',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'iconImg' => array(
                    'type' => 'media',
                    'title' => '图标',
                    'desc' => '',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210802/48a9be5887a8fbd7242cf6fb676c18bb.png',
                ),
                'content_text' => array(
                    'type' => 'text',
                    'title' => '标题文字',
                    'std' => '拒绝用户体验差',
                ),
                'is_link' => array(
                    'type' => 'checkbox',
                    'title' => '开启链接',
                    'std' => 0,
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'desc' => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('is_link', '=', 1)
                    )
                ),
                'content_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 800,
                    'min' => 0,
                    'std' => 14,
                ),
                'icon_bg_width' => array(
                    'type' => 'slider',
                    'title' => '背景宽度',
                    'max' => 800,
                    'min' => 0,
                    'std' => 84,
                ),
                'icon_bg_height' => array(
                    'type' => 'slider',
                    'title' => '背景高度',
                    'max' => 800,
                    'min' => 0,
                    'std' => 84,
                ),
                'icon_aperture_width' => array(
                    'type' => 'slider',
                    'title' => '背景光圈宽度',
                    'max' => 800,
                    'min' => 0,
                    'std' => 100,
                ),
                'icon_aperture_height' => array(
                    'type' => 'slider',
                    'title' => '背景光圈高度',
                    'max' => 800,
                    'min' => 0,
                    'std' => 100,
                ),
                'icon_style' => array(
                    'type' => 'buttons',
                    'title' => '图标状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        )
                    ),
                    'tabs' => true,
                ),
                'contentColor' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#258ffc',
                    'depends' => array(
                        array('icon_style', '=', 'normal'),
                    )
                ),
                'icon_bg_style' => array(
                    'type' => 'buttons',
                    'title' => '图标背景选项',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '颜色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('icon_style', '=', 'normal')
                    )
                ),
                'bgColor' => array(
                    'type' => 'color',
                    'title' => '图标背景颜色',
                    'std' => '#2464F5',
                    'depends' => array(
                        array('icon_style', '=', 'normal'),
                        array('icon_bg_style', '=', 'color')
                    )
                ),
                'bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '图标渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('icon_style', '=', 'normal'),
                        array('icon_bg_style', '=', 'gradient')
                    ),
                ),
                'apertureColor' => array(
                    'type' => 'color',
                    'title' => '光圈颜色',
                    'std' => 'rgba(37,143,252,0.3)',
                    'depends' => array(
                        array('icon_style', '=', 'normal'),
                    )
                ),
                'contentColor_hover' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'std' => '#258ffc',
                    'depends' => array(
                        array('icon_style', '=', 'hover'),
                    )
                ),
                'bgGradient_hover' => array(
                    'type' => 'gradient',
                    'title' => '图标渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('icon_style', '=', 'hover'),
                        array('icon_bg_style', '=', 'gradient')
                    ),
                ),
                'bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '图标背景颜色',
                    'std' => '#F7810C',
                    'depends' => array(
                        array('icon_style', '=', 'hover'),
                        array('icon_bg_style', '=', 'color')
                    )
                ),
                'apertureColor_hover' => array(
                    'type' => 'color',
                    'title' => '光圈颜色',
                    'std' => 'rgba(251,167,8,0.3)',
                    'depends' => array(
                        array('icon_style', '=', 'hover'),
                    )
                ),
            ),
        ),
    )
);
