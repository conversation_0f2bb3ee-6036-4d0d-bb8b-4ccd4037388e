<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'baroque_index_part02',
        'title' => '首页出行锦囊',
        'desc' => '',
        'category' => '巴洛克',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                // 切换item
                'section_tab_item' => array(
                    'title' => '选项卡列表',
                    'attr' => array(
                        'name' => array(
                            'type' => 'text',
                            'title' => '选项卡名称',
                            'std' => '游记攻略',
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => '选项卡图标',
                            'desc' => '',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210918/42d9fa270bf8e0a5b432445e6c804ac1.png',
                        ),
                        'bgColor' => array(
                            'type' => 'color',
                            'title' => '选项卡背景颜色',
                            'std' => '#585656',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                        'title_id'       => array(
                            'type'    => 'text',
                            'title'   => JText::_('标题跳转ID（容器章节ID）'),
                            'std'     => '这里可以填写跳转ID',
                        ),
                    ),
                    'std' => array(
                        array(
                            'name'=> '游记攻略',
                            'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/42d9fa270bf8e0a5b432445e6c804ac1.png',
                            'bgColor' => '#585656'
                        ),
                        array(
                            'name'=> '民宿展示',
                            'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/f3e6baa0d037c2334e0beebcf3f0d4f1.png',
                            'bgColor' => '#773936'
                        ),
                        array(
                            'name'=> '景区内外交通',
                            'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/5ea41192c5c40d5f6f32e4a45e6d46f8.png',
                            'bgColor' => '#BBA766'
                        ),
                        array(
                            'name'=> '景区地图',
                            'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/f6ac81dcc51586cb75f467d4693d1390.png',
                            'bgColor' => '#464F69'
                        ),
                        array(
                            'name'=> '联系方式',
                            'icon'=> 'https://oss.lcweb01.cn/joomla/20210918/39c619920778edfe0e23c5d22393643c.png',
                            'bgColor' => '#74A099'
                        ),
                    ),
                ),
                'section_tab_height' => array(
                    'type' => 'slider',
                    'title' => '选项卡高度',
                    'max' => 600,
                    'min' => 150,
                    'responsive' => true,
                    'std' => array(
                        'md' => 256,
                        'sm' => 240,
                        'xs' => 158,
                    ),
                ),
                'section_tab_fontsize' => array(
                    'type' => 'slider',
                    'title' => '文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 18,
                        'sm' => 18,
                        'xs' => 16,
                    ),
                ),
                'section_tab_fontcolor' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#ffffff',
                ),
                'section_tab_icon_size' => array(
                    'type' => 'slider',
                    'title' => '图标大小',
                    'max' => 500,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 100,
                        'sm' => 80,
                        'xs' => 50,
                    ),
                ),
                'section_tab_num' => array(
                    'type' => 'slider',
                    'title' => '选项卡一行显示个数',
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 5,
                        'sm' => 5,
                        'xs' => 3
                    ),
                ),

//                'section_tab_active_color' => array(
//                    'type' => 'color',
//                    'title' => '选项卡选中文字颜色',
//                    'std' => '#fff',
//                    'depends' => array(
//                        array('content_style', '=', 'tab_style'),
//                    ),
//                ),
//                'section_tab_mgB' => array(
//                    'type' => 'slider',
//                    'title' => '选项卡外下边距',
//                    'std' => array(
//                        'md' => 20,
//                        'sm' => 20,
//                        'xs' => 10,
//                    ),
//                    'depends' => array(
//                        array('content_style', '=', 'tab_style'),
//                    ),
//                    'responsive' => true,
//                    'max' => 1000,
//                ),
//
//                'section_map_city' => array(
//                    'type' => 'text',
//                    'title' => '地图中心点所在城市',
//                    'desc' => '地图中心点城市需要与填写的地图中心点经纬度一致',
//                    'std' => '太原',
//                    'depends' => array(
//                        array('content_style', '=', 'map_style'),
//                    ),
//                ),
//
//                'section_map_centerIcon' => array(
//                    'type' => 'media',
//                    'title' => '地图中心点图标',
//                    'std' => '/components/com_jwpagefactory/addons/amap_nearby/assets/images/location.png',
//                    'depends' => array(
//                        array('content_style', '=', 'map_style'),
//                    ),
//                ),

            ),
        ),
    )
);
