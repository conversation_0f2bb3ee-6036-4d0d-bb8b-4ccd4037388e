<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonButton_events extends JwpagefactoryAddons
{
    public function render()
	{
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $text = (isset($settings->text) && $settings->text) ? $settings->text : '';
        $number_text = (isset($settings->number_text) && $settings->number_text) ? $settings->number_text : '';
        $target = (isset($settings->target) && $settings->target) ? $settings->target : '';
        $tz_page_type = (isset($settings->tz_page_type)) ? $settings->tz_page_type : 'Internal_pages';
        $attribs = (isset($settings->target) && $settings->target) ? ' rel="noopener noreferrer" target="' . $settings->target . '"' : '';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $detail_page = (isset($settings->detail_page)) ? $settings->detail_page : '';
        $thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        $output ='';

        //样式 
        $button_height = (isset($settings->button_height) && $settings->button_height) ? $settings->button_height : 50;
        $button_width = (isset($settings->button_width) && $settings->button_width) ? $settings->button_width : 150;
        $button_border_width = (isset($settings->button_border_width) && $settings->button_border_width) ? $settings->button_border_width : 1;
        $button_font_size = (isset($settings->button_font_size) && $settings->button_font_size) ? $settings->button_font_size : 15;
        $button_color = (isset($settings->button_color) && $settings->button_color) ? $settings->button_color : '#000000';
        $button_color_hr = (isset($settings->button_color_hr) && $settings->button_color_hr) ? $settings->button_color_hr : '#000000';
        $button_bgcolor = (isset($settings->button_bgcolor) && $settings->button_bgcolor) ? $settings->button_bgcolor : '#ffffff';
        $button_bgcolor_hr = (isset($settings->button_bgcolor_hr) && $settings->button_bgcolor_hr) ? $settings->button_bgcolor_hr : '#ffffff';
        $button_border_color = (isset($settings->button_border_color) && $settings->button_border_color) ? $settings->button_border_color : '#000000';
        $button_border_color_hr = (isset($settings->button_border_color_hr) && $settings->button_border_color_hr) ? $settings->button_border_color_hr : '#000000';

        $button_weizhi = (isset($settings->button_weizhi) && $settings->button_weizhi) ? $settings->button_weizhi : 'left';
        $button_border_radius = isset($settings->button_border_radius) ? $settings->button_border_radius : '0';
        $button_hverm = isset($settings->button_hverm) ? $settings->button_hverm : '0';
        $ewm_img = isset($settings->ewm_img) ? $settings->ewm_img : 'https://oss.lcweb01.cn/joomla/20220330/104a593789c277a9bc97e031ed9ac93b.png';
        if (isset($settings->ewm_width) && $settings->ewm_width) {
            if (is_object($settings->ewm_width)) {
                $ewm_width_md = $settings->ewm_width->md;
                $ewm_width_sm = $settings->ewm_width->sm;
                $ewm_width_xs = $settings->ewm_width->xs;
            } else {
                $ewm_width_md = $settings->ewm_width;
                $ewm_width_sm = $settings->ewm_width_sm;
                $ewm_width_xs = $settings->ewm_width_xs;
            }
        } else {
            $ewm_width_md = '160';
            $ewm_width_sm = '160';
            $ewm_width_xs = '120';
        }

        $output.='
            <style>
                '.$addon_id.' .input_ {
                    outline: none;
                    border: 0px;
                    color: rgba(0, 0, 0, 0.0);
                    position: absolute;
                    left: -20000px;
                    background-color: transparent;
                }
                '.$addon_id.' .div_ {
                    position: absolute;
                    left: -20000px;
                    color: rgba(0, 0, 0, 0);
                    background-color: transparent;
                }
                '.$addon_id.' .buttonevens button {
                    background-color:'.$button_bgcolor.';
                    width:'.$button_width.'px;
                    height:'.$button_height.'px;
                    color:'.$button_color.';
                    font-size: '.$button_font_size.'px;
                    border: '.$button_border_width.'px solid '.$button_border_color.';
                    border-radius:'.$button_border_radius.'px;
                }
                '.$addon_id.' .buttonevens:hover button {
                    background-color:'.$button_bgcolor_hr.';
                    color:'.$button_color_hr.';
                    border: '.$button_border_width.'px solid '.$button_border_color_hr.';
                }
                '.$addon_id.' {
                    text-align:'.$button_weizhi.';
                }
                '.$addon_id.' .code {
                    width: '.$ewm_width_md.'px;
                    height: '.$ewm_width_md.'px;
                    position: absolute;
                    display: none;
                    bottom: 80px;
                    left: calc( 50% - ( '.$ewm_width_md.'px / 2 ) );
                    margin:0 auto;
                    transition:all 0.3s;
                }
                @media (max-width: 768px) {
                    '.$addon_id.' .code {
                        width: '.$ewm_width_xs.'px;
                        height: '.$ewm_width_xs.'px;
                        position: absolute;
                        display: none;
                        bottom: 70px;
                        left: calc( 50% - ( '.$ewm_width_xs.'px / 2 ) );
                        margin:0 auto;
                        transition:all 0.3s;
                    }
                    
                }
            </style>
        ';
        if($tz_page_type=='Internal_pages')
        {
            $output .= '<a class="buttonevens" target="'.$target.'" href="' . $thisUrl . '"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='wx_links')
        {
            $output .='<input readOnly="true" class="input_" id="biao1" value="'.$number_text.'"/>';
            $output .='<div id="biaoios" class="div_">'.$number_text.'</div>';
            $output .= '<a class="buttonevens" onclick="openWx()"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='QQ_links')
        {
            $output .='<input readOnly="true" class="input_" id="biao2" value="'.$number_text.'"/>';
            $output .='<div id="biaoios1" class="div_">'.$number_text.'</div>';
            $output .= '<a class="buttonevens" onclick="openQq()"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='email_links')
        {
            $output .='<a class="buttonevens" href="mailto:' . $number_text . '"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='tel')
        {
            $output .='<a class="buttonevens" href="tel:' . $number_text . '"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='external_links')
        {
            $output .= '<a class="buttonevens" target="'.$target.'" href="' . $detail_page . '"><button>' . $text . '</button></a>';
        }
        else if($tz_page_type=='none'){
            $output .= '<a class="buttonevens" href="javascript:;"><button>' . $text . '</button></a>';
        }

        if($tz_page_type=='wx_links')
		{
			$output.='<script>
				var browser = {
				versions: function () {
					var u = navigator.userAgent,
							app = navigator.appVersion;
					return {
						trident: u.indexOf(\'Trident\') > -1, /*IE内核*/
						presto: u.indexOf(\'Presto\') > -1, /*opera内核*/
						webKit: u.indexOf(\'AppleWebKit\') > -1, /*苹果、谷歌内核*/
						gecko: u.indexOf(\'Gecko\') > -1 && u.indexOf(\'KHTML\') == -1, /*火狐内核*/
						mobile: !!u.match(/AppleWebKit.*Mobile.*/), /*是否为移动终端*/
						ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), /*ios终端*/
						android: u.indexOf(\'Android\') > -1 || u.indexOf(\'Linux\') > -1, /*android终端或者uc浏览器*/
						iPhone: u.indexOf(\'iPhone\') > -1, /*是否为iPhone或者QQHD浏览器*/
						iPad: u.indexOf(\'iPad\') > -1, /*是否iPad*/
						webApp: u.indexOf(\'Safari\') == -1, /*是否web应该程序，没有头部与底部*/
						souyue: u.indexOf(\'souyue\') > -1,
						superapp: u.indexOf(\'superapp\') > -1,
						weixin: u.toLowerCase().indexOf(\'micromessenger\') > -1,
						Safari: u.indexOf(\'Safari\') > -1
					};
				}(),
				language: (navigator.browserLanguage || navigator.language).toLowerCase()
			};
		
				function openWx(url){
					if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
						var text = document.getElementById(\'biaoios\');
						//获取隐藏的input，并写入text内容，在进行复制
						var input = document.getElementById("biao1");
						input.value = text.innerHTML;
						input.select();
						input.setSelectionRange(0, input.value.length);   //兼容ios
						document.execCommand("Copy");
						input.blur();
					}else{
						var Url2=document.getElementById("biao1");//要复制文字的节点
						Url2.select(); // 选择对象
						//document.execCommand("Copy"); // 执行浏览器复制命令
						$("#biao1").blur();
						if(document.execCommand(\'copy\', false, null)){
						var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
						}
					}
					jQuery(" .white_content").fadeIn(500);
					window.location.href = "weixin://";
				}

				</script>
				';
		}
        else if($tz_page_type=='QQ_links')
        {
            $output.='<script>
            function openQq(url){
                if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
                    var text = document.getElementById(\'biaoios1\');
                    //获取隐藏的input，并写入text内容，在进行复制
                    var input = document.getElementById("biao2");
                    input.value = text.innerHTML;
                    input.select();
                    input.setSelectionRange(0, input.value.length);   //兼容ios
                    document.execCommand("Copy");
                    input.blur();
                }else{
                    var Url2=document.getElementById("biao2");//要复制文字的节点
                    Url2.select(); // 选择对象
                    //document.execCommand("Copy"); // 执行浏览器复制命令
                    $("#biao2").blur();
                    if(document.execCommand(\'copy\', false, null)){
                      var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
                    }
                }
                jQuery(" .white_content").fadeIn(500);
                window.location.href = "https://wpa.qq.com/msgrd?v=3&uin=1169296508&site=qq&menu=yes";
            }
            </script>
				';
        }

        if($button_hverm==1){
            $output .='<img src="'.$ewm_img.'" class="code"  >
                <script>
                    $("'.$addon_id.' .buttonevens").hover(function(){
                        $(this).next(".code").css("display","block");
                    },function(){
                        $(this).next(".code").css("display","none");
                    })
                </script>
            ';
        }
		return $output;
	}



}