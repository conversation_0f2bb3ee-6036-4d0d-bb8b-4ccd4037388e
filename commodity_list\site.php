<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonCommodity_list extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'panel-default';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

        // Addon options
        $resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';
        $art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : '';
        $shops_catid = (isset($settings->shop_catid) && $settings->shop_catid) ? $settings->shop_catid : 0;
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $k2catid = (isset($settings->k2catid) && $settings->k2catid) ? $settings->k2catid : 0;
        // print_r($shops_catid);
        $pro_font_color = (isset($settings->pro_font_color)) ? $settings->pro_font_color : '#fff';
        $pro_font_color_bg_type2 = (isset($settings->pro_font_color_bg_type2)) ? $settings->pro_font_color_bg_type2 : '#fff';
        $pro_font_color_bg = (isset($settings->pro_font_color_bg)) ? $settings->pro_font_color_bg : '#000';
        $pro_font_color_bg_height = (isset($settings->pro_font_color_bg_height)) ? $settings->pro_font_color_bg_height : 40;
        $pro_font_color_type2_title = (isset($settings->pro_font_color_type2_title)) ? $settings->pro_font_color_type2_title : '#000';
        $pro_font_title_size_type2 = (isset($settings->pro_font_title_size_type2)) ? $settings->pro_font_title_size_type2 : 14;
        $pro_font_color_type2_intext = (isset($settings->pro_font_color_type2_intext)) ? $settings->pro_font_color_type2_intext : '#000';
        $pro_font_intext_size_type2 = (isset($settings->pro_font_intext_size_type2)) ? $settings->pro_font_intext_size_type2 : 14;
        $img_animated = (isset($settings->img_animated)) ? $settings->img_animated : 'animated1';
        $box_type2_shadow_color = (isset($settings->box_type2_shadow_color)) ? $settings->box_type2_shadow_color : '#fff';
        $box_type2_shadow_x = (isset($settings->box_type2_shadow_x)) ? $settings->box_type2_shadow_x : 0;
        $box_type2_shadow_Y = (isset($settings->box_type2_shadow_Y)) ? $settings->box_type2_shadow_Y : 0;
        $box_type2_shadow_mh = (isset($settings->box_type2_shadow_mh)) ? $settings->box_type2_shadow_mh : 0;
        $box_type2_shadow_kz = (isset($settings->box_type2_shadow_kz)) ? $settings->box_type2_shadow_kz : 0;


        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        $intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
        $hide_thumbnail = (isset($settings->hide_thumbnail)) ? $settings->hide_thumbnail : 0;
        $show_author = (isset($settings->show_author)) ? $settings->show_author : 1;
        $show_category = (isset($settings->show_category)) ? $settings->show_category : 1;
        $show_date = (isset($settings->show_date)) ? $settings->show_date : 1;
        $show_readmore = (isset($settings->show_readmore)) ? $settings->show_readmore : 1;
        $readmore_text = (isset($settings->readmore_text) && $settings->readmore_text) ? $settings->readmore_text : 'Read More';
        $link_articles = (isset($settings->link_articles)) ? $settings->link_articles : 0;
        $link_catid = (isset($settings->catid)) ? implode(',', array_filter($settings->catid)) : '';
        $company_id = $company_id ? $company_id : ((isset($settings->company_id)) ? $settings->company_id : 0);
        $site_id = $site_id ? $site_id : ((isset($settings->site_id)) ? $settings->site_id : 0);
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
        $show_title = (isset($settings->show_title) && $settings->show_title) ? $settings->show_title : false;
        $show_price = (isset($settings->show_price) && $settings->show_price) ? $settings->show_price : 0;
        $price_new_value_color = (isset($settings->price_new_value_color) && $settings->price_new_value_color) ? $settings->price_new_value_color : '#000000';
        $price_new_value_size = (isset($settings->price_new_value_size) && $settings->price_new_value_size) ? $settings->price_new_value_size : '14';
        $price_old_value_color = (isset($settings->price_old_value_color) && $settings->price_old_value_color) ? $settings->price_old_value_color : '#000000';
        $price_old_value_size = (isset($settings->price_old_value_size) && $settings->price_old_value_size) ? $settings->price_old_value_size : '14';
        $price_position = (isset($settings->price_position) && $settings->price_position) ? $settings->price_position : 'flex-start';
        $price_new_old_margin = (isset($settings->price_new_old_margin) && $settings->price_new_old_margin) ? $settings->price_new_old_margin : '0'; 


        $all_articles_btn_text = (isset($settings->all_articles_btn_text) && $settings->all_articles_btn_text) ? $settings->all_articles_btn_text : 'See all posts';
        $all_articles_btn_class = (isset($settings->all_articles_btn_size) && $settings->all_articles_btn_size) ? ' jwpf-btn-' . $settings->all_articles_btn_size : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? ' jwpf-btn-' . $settings->all_articles_btn_type : ' jwpf-btn-default';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_shape) && $settings->all_articles_btn_shape) ? ' jwpf-btn-' . $settings->all_articles_btn_shape : ' jwpf-btn-rounded';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? ' jwpf-btn-' . $settings->all_articles_btn_appearance : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_block) && $settings->all_articles_btn_block) ? ' ' . $settings->all_articles_btn_block : '';
        $all_articles_btn_icon = (isset($settings->all_articles_btn_icon) && $settings->all_articles_btn_icon) ? $settings->all_articles_btn_icon : '';
        $all_articles_btn_icon_position = (isset($settings->all_articles_btn_icon_position) && $settings->all_articles_btn_icon_position) ? $settings->all_articles_btn_icon_position : 'left';
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        //include k2 helper
        $k2helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/k2.php';
        // $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/shops.php';
        $isk2installed = self::isComponentInstalled('com_k2');
// if($art_type_selector=='art01'){  
        $output = "<style>
  *{
      padding: 0;
      margin: 0;
  }
  {$addon_id} .page_plug{
      width: 90%;
      margin: 5px auto;
      text-align: center;
  }
  {$addon_id} .page_plug a{
      padding: 3px 8px;
      border: 1px solid #2a68a7;
      margin-right: 5px;
      text-decoration: none;
      color: #2a68a7;
  }
  {$addon_id} .jwpf-addon-article{
    position: relative;
    overflow: hidden;
  }
{$addon_id} .jwpf-addon-article:hover .jwpf-article-info-wrap{
    bottom: 0px;
}
{$addon_id} .introtext_type2{
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
";
        //布局1
if($pro_type=='type1'){
    if(!$show_title) {
        $output .= "  
    {$addon_id} .pr_list_id .jwpf-article-info-wrap{
        text-align: center;
        transition: bottom 0.3s;
        position: absolute;
        bottom: -{$pro_font_color_bg_height}px;
        height: {$pro_font_color_bg_height}px;
        line-height: {$pro_font_color_bg_height}px;
        width:100%;
      }";
    }else {
        $output .= "
    {$addon_id} .pr_list_id .jwpf-article-info-wrap{
        height: {$pro_font_color_bg_height}px;
        line-height: {$pro_font_color_bg_height}px;   
        text-align: center;       
        width:100%;
        position: absolute;
        bottom:0;
    }";
    }
}
//布局2
if($pro_type=='type2'){
    if($img_animated=='animated2'){
        $output .= "
             {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover img{
                    transform:scale(1.2);
                    -ms-transform:scale(1.2);
                   -moz-transform:scale(1.2);
                   -webkit-transform:scale(1.2);
                   -o-transform:scale(1.2);
              }
        ";
    }
    $output .= "
    {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2{
           padding: 0 10px 15px 10px;
    }
    {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .title_type2{
        color: {$pro_font_color_type2_title};
        font-size:{$pro_font_title_size_type2}px;
        font-weight: 700;
    }
    {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .introtext_type2{
        color: {$pro_font_color_type2_intext};
        font-size:{$pro_font_intext_size_type2}px;
        text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
    }
   {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap{
       transition: all .5s;
       -ms-transition: all .5s;
       -moz-transition: all .5s;
       -webkit-transition: all .5s;
       -o-transition: all .5s;
        display: block;
    }  
    {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap .img_box_type2{
        overflow: hidden;
    }  
    {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap img{
        transition: all .5s;
         -ms-transition: all .5s;
       -moz-transition: all .5s;
       -webkit-transition: all .5s;
       -o-transition: all .5s;
    }
    {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover{
           box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
           -moz-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
           -webkit-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
    }


    {$addon_id} .priceBox {
        display:flex;
        justify-content:${price_position};
        align-items:flex-end;
    }
    {$addon_id} .priceBox .now_price {
        color: ${price_new_value_color};
        font-size:${price_new_value_size}px;
        margin-right:${price_new_old_margin}px;
    }
    {$addon_id} .priceBox .old_price {
        color: ${price_old_value_color};
        font-size:${price_old_value_size}px;
        text-decoration:line-through;
    }
    
    ";
}


  $output .= "</style>";
        if ($resource == 'k2') {
            if ($isk2installed == 0) {
                $output .= '<p class="alert alert-danger">' . JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_ERORR_K2_NOTINSTALLED') . '</p>';
                return $output;
            } elseif (!file_exists($k2helper)) {
                $output .= '<p class="alert alert-danger">' . JText::_('COM_JWPAGEFACTORY_ADDON_K2_HELPER_FILE_MISSING') . '</p>';
                return $output;
            } else {
                require_once $k2helper;
            }
            $items = JwpagefactoryHelperK2::getItemsList($limit, $ordering, $k2catid, $include_subcat, $page);
            $items_count = JwpagefactoryHelperK2::getItemsCount($ordering, $k2catid, $include_subcat);
        } else {
            require_once $article_helper;
            $items = JwpagefactoryHelperShops::getShopsList($limit, $ordering, $shops_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
            $items_count = JwpagefactoryHelperShops::getShopsCount($ordering, $shops_catid, $include_subcat, $company_id, $site_id);
        }
        // echo "<pre>";
        // var_dump($items);
        // exit();
        if (!count($items)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            return $output;
        }

        // print_r($items);die;

        if (count((array)$items)) {
            if($pro_type=='type1'){
                $output .= '<div class="pr_list_id jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        if ($resource == 'k2') {
                            if (isset($item->image_medium) && $item->image_medium) {
                                $image = $item->image_medium;
                            } elseif (isset($item->image_large) && $item->image_large) {
                                $image = $item->image_medium;
                            }
                        } else {
                            $image = $item->image_thumbnail;
                        }

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this->get_image_placeholder($item->image_thumbnail);
                                $output .= '<a href="' . $item->link . '" itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                                $output .= '<a class="jwpf-article-img-wrap pr_box" href="' . $item->link . '" itemprop="url"><img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                $output .= '<div style="background:' . $pro_font_color_bg . '" class="jwpf-article-info-wrap pr_active">';
                                $output .= '<p style="margin:0;font-size: 100%;color:' . $pro_font_color . ';">' . $item->title . '</p>';
                                $output .= '</div>';
                                $output .= '</a>';
                            }
                        }
                    }


                    $output .= '</div>';
                    $output .= '</div>';
                }

                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div class="page_plug">';
                    for ($i = 1; $i <= $all_page; $i++) {
                        if ($page == $i) {
                            $output .= '<a>' . $i . '</a>';
                        } else {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                            $output .= '<a href="' . $url . '&page=' . $i . '">' . $i . '</a>';
                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
//                    $output .= '</div>';
                }


                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }

                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
            }
            if($pro_type=='type2'){
                $output .= '<div class="pr_list_id_type2 jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        if ($resource == 'k2') {
                            if (isset($item->image_medium) && $item->image_medium) {
                                $image = $item->image_medium;
                            } elseif (isset($item->image_large) && $item->image_large) {
                                $image = $item->image_medium;
                            }
                        } else {
                            $image = $item->image_thumbnail;
                        }

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this->get_image_placeholder($item->image_thumbnail);
                                $output .= '<a href="' . $item->link . '" itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                                $output .= '<a class="jwpf-article-img-wrap pr_box" href="' . $item->link . '" itemprop="url">';
                                $output .='<div class="img_box_type2">';
                                 $output .= '<img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                $output .='</div>';
                                $output .= '<div style="background:' . $pro_font_color_bg_type2 . '" class="jwpf-article-info-wrap-type2 pr_active">';
                                $output .= '<p class="title_type2" style="text-align: center; padding: 14px 0;margin:0;">' . $item->title . '</p>';
                                // 显示简介
                                if ($show_intro) {
                                    $output .= '<div class="introtext_type2">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</div>';
                                }
                                // 显示价格
                                if($show_price) {
                                    $output .= '<div class="priceBox">';
                                    $output .= '<span class="now_price">￥'. $item->sale_price .'</span><span class="old_price">￥'. $item->original_price .'</span>';
                                    $output .= '</div>';
                                }
                                $output .= '</div>';
                                $output .= '</a>';
//                                introtext
////                                alias
                            }
                        }
                    }


                    $output .= '</div>';
                    $output .= '</div>';
                }

                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div class="page_plug">';
                    for ($i = 1; $i <= $all_page; $i++) {
                        if ($page == $i) {
                            $output .= '<a>' . $i . '</a>';
                        } else {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                            $output .= '<a href="' . $url . '&page=' . $i . '">' . $i . '</a>';
                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
//                    $output .= '</div>';
                }


                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }

                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
            }

        }
        return $output;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';

        return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
    }


    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

}