<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonZihai_show extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : 'layout1';
        $jw_image_carousel_item = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';
        $title1_type1 = (isset($settings->title1_type1) && $settings->title1_type1) ? $settings->title1_type1 : '';
        $title2_type1 = (isset($settings->title2_type1) && $settings->title2_type1) ? $settings->title2_type1 : '';
        $content_type1 = (isset($settings->content_type1) && $settings->content_type1) ? $settings->content_type1 : '';
        $image_bg1_type1 = (isset($settings->image_bg1_type1) && $settings->image_bg1_type1) ? $settings->image_bg1_type1 : '';
        $image_bg2_type1 = (isset($settings->image_bg2_type1) && $settings->image_bg2_type1) ? $settings->image_bg2_type1 : '';
        $image1_type1 = (isset($settings->image1_type1) && $settings->image1_type1) ? $settings->image1_type1 : '';
        $image2_type1 = (isset($settings->image2_type1) && $settings->image2_type1) ? $settings->image2_type1 : '';


        $jw_image_carousel_item2 = (isset($settings->jw_image_carousel_item2) && $settings->jw_image_carousel_item2) ? $settings->jw_image_carousel_item2 : '';
        $bg_color_type2 = (isset($settings->bg_color_type2) && $settings->bg_color_type2) ? $settings->bg_color_type2 : '';
        $text_color_type2 = (isset($settings->text_color_type2) && $settings->text_color_type2) ? $settings->text_color_type2 : '';




        $output = '';
        if($image_carousel_layout =='layout1')
        {
            $output.='
                <style>
                    @media only screen and (min-width: 1440px){
                        '.$addon_id.' .z-h5-c1 {
                            width: 100%;
                            height: 0;
                            padding-bottom: 45.15%;
                            position: relative;
                            overflow: hidden;
                        }
                        '.$addon_id.' .z-h5-c1>img:nth-child(1) {
                            display: block;
                        }
                        '.$addon_id.' .z-h5-c1>img {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                        }
                        '.$addon_id.' .z-h5-c1>img:nth-child(2) {
                            display: none;
                        }
                        '.$addon_id.' .z-h5-c1>img {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                        }
                        '.$addon_id.' .z-h5-c1 .z-h5-t1 {
                            position: absolute;
                            left: 57.55%;
                            top: 13.72%;
                        }
                        '.$addon_id.' .z-h5-t1 {
                            width: 68px;
                            height: 2px;
                            background: #d51920;
                            position: absolute;
                        }
                        '.$addon_id.' .z-h5-c1 .z-h5-t2 {
                            position: absolute;
                            left: 57.55%;
                            top: 17.53%;
                        }
                        '.$addon_id.' .z-h5-t2 {
                            font-size: 43px;
                            line-height: 43px;
                            color: #000;
                            position: absolute;
                            font-weight: bold;
                        }
                        '.$addon_id.' .z-h5-t2 span {
                            color: #d51920;
                        }
                        '.$addon_id.' .z-h5-c1 .z-h5-t3 {
                            position: absolute;
                            left: 57.55%;
                            top: 23.99%;
                            color: #000;
                        }
                        '.$addon_id.' .z-h5-t3 {
                            font-size: 26px;
                            line-height: 44px;
                            color: #333333;
                            font-family: "yuanjian";
                            position: absolute;
                        }
                        '.$addon_id.' .z-h5-c2 {
                            width: 31.875%;
                            position: absolute;
                            top: 17.99%;
                            left: 11.92%;
                        }
                        '.$addon_id.' .i100>img {
                            width: 100%;
                        }
                        '.$addon_id.' .z-h5-c3 {
                            width: 22.4%;
                            position: absolute;
                            top: 51.9%;
                            left: 36.15%;
                        }
                        '.$addon_id.' .z-h5-c4 {
                            position: absolute;
                            left: 61.04%;
                            top: 32.26%;
                            width: 495px;
                        }
                        '.$addon_id.' .z-h5-c4>div:first-child {
                            width: 1px;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                            background: #977cf0;
                        }
                        '.$addon_id.' .z-h5-c4>div:last-child {
                            position: relative;
                            width: 100%;
                        }
                        '.$addon_id.' .z-h5-c5 {
                            width: 100%;
                            margin-bottom: 20px;
                            width: 100%;
                            height: 86px;
                            position: relative;
                        }
                
                        '.$addon_id.' .z-h5-c5:hover{
                            visibility: visible;
                            animation-duration: 1.5s;
                            animation-delay: 0.5s;
                            animation-name: fadeInRight;
                        }
                        '.$addon_id.' .z-h5-c6 {
                            width: 5px;
                            height: 10px;
                            background: #977cf0;
                            transition: 0.5s;
                            position: absolute;
                            top: calc(50% - 5px);
                            left: -2px;
                        }
                        '.$addon_id.' .z-h5-c7 {
                            width: calc(100% - 6px);
                            height: 100%;
                            border: 1px solid rgba(248,44,78,0);
                            border-top-right-radius: 20px;
                            border-bottom-right-radius: 20px;
                            box-sizing: border-box;
                            background: rgba(172,15,119,0);
                            transition: 0.5s;
                            margin-left: 6px;
                        }
                        '.$addon_id.' .z-h5-c7>div:first-child {
                            font-size: 28px;
                            line-height: 28px;
                            color: #fff;
                            position: absolute;
                            top: 18px;
                            left: 66px;
                            transition: 0.5s;
                        }
                        '.$addon_id.' .z-h5-c7>div:last-child {
                            font-size: 20px;
                            line-height: 20px;
                            color: #fff;
                            position: absolute;
                            top: 50px;
                            left: 66px;
                            font-weight: lighter;
                            transition: 0.5s;
                        }
                
                        @keyframes fadeInRight{
                            0% {
                                opacity: 0;
                                -webkit-transform: translate3d(100%, 0, 0);
                                transform: translate3d(100%, 0, 0);
                            }
                            100% {
                                opacity: 1;
                                -webkit-transform: none;
                                transform: none;
                            }
                        }
                
                        '.$addon_id.' .z-h5-c5:hover .z-h5-c7 {
                            border: 1px solid rgba(248,44,78,0.82);
                            background: rgba(172,15,119,1);
                            transition: 0.5s;
                        }
                        '.$addon_id.' .z-h5-c5:hover .z-h5-c6 {
                            background: #e50127;
                            top: calc(50% - 40px);
                            height: 80px;
                        }
                        '.$addon_id.' .z-h5-c5:hover .z-h5-c7>div:first-child {
                            left: 40px;
                            transition: 0.5s;
                        }
                        '.$addon_id.' .z-h5-c5:hover .z-h5-c7>div:last-child {
                            left: 40px;
                            transition: 0.5s;
                        }
                        
                
                    }
            
            </style>
            ';
            $output.='<div class="z-h5-c1">
            <img src="'.$image_bg1_type1.'">
            <img src="'.$image_bg2_type1.'">
            <div class="z-h5-t1 wow fadeInRight animated" data-wow-delay="0.5s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.5s; animation-name: fadeInRight;"></div>
            <div class="z-h5-t2 wow fadeInRight animated" data-wow-delay="0.7s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.7s; animation-name: fadeInRight;">'.$title1_type1.'<span>'.$title2_type1.'</span></div>
            <div class="z-h5-t3 wow fadeInRight animated" data-wow-delay="0.9s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.9s; animation-name: fadeInRight;">'.$content_type1.'</div>
            <div class="z-h5-c2 i100 wow bounceInLeft animated" data-wow-delay="0.5s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.5s; animation-name: bounceInLeft;"><img src="'.$image1_type1.'"></div>
            <div class="z-h5-c3 i100 wow bounceInLeft animated" data-wow-delay="0.7s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.7s; animation-name: bounceInLeft;"><img src="'.$image2_type1.'"></div>
            <div class="z-h5-c4">
                <div></div>
                <div>';

                foreach ($jw_image_carousel_item as $key => $item) {
                    $output.=' <div class="z-h5-c5 wow fadeInRight animated" data-wow-duration="1.5s" data-wow-delay="0.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.5s; animation-name: fadeInRight;">';
                    $output.=' <div class="z-h5-c6"></div>';
                    $output.=' <div class="z-h5-c7">';
                    $output.=' <div>'.$item->title.'</div><div>'.$item->content.'</div>';
                    $output.=' </div>';
                    $output.=' </div>';
                }

      $output.=' </div>
            </div>
        </div>';
        }
        if($image_carousel_layout =='layout2')
        {
                $output.=' 
                    <style>
                    @media only screen and (min-width: 1440px){
                        '.$addon_id.' .z-sof-g1 {
                            width: 75%;
                            margin: 0 auto;
                        }  
                        ';
                        $num = 0;
                        foreach ($jw_image_carousel_item2 as $key => $item) {
                            $num = $key+1;
                            $output.= $addon_id.' .z-sof-g2:nth-child('.$num.') {';
                            $output.=' padding-bottom: 29.1667%;';
                            $output.='}';
                        }
                $output.='        
                        '.$addon_id.' .z-sof-g2:nth-child(odd) {
                            float: left;
                        }
                        '.$addon_id.' .z-sof-g2:nth-child(even) {
                            float: right;
                        }
                        '.$addon_id.' .z-sof-g2 {
                            width: calc(50% - 8px);
                            position: relative;
                            height: 0;
                            border: 1px solid #e6e6e6;
                            border-radius: 10px;
                            box-sizing: border-box;
                            transition: 0.8s;
                            margin-bottom: 16px;
                            overflow: hidden;
                        }
                        '.$addon_id.' .z-sof-g3 {
                            height: 0;
                            position: absolute;
                            top: 0;
                            right: 0;
                            opacity: 0;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover {
                            background: '.$bg_color_type2.';
                            border: 1px solid '.$bg_color_type2.';
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover .z-sof-g6 {
                            color: '.$text_color_type2.';
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover .z-sof-g5 {
                            color: '.$text_color_type2.';
                            border-left: 4px solid '.$text_color_type2.';
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover .z-sof-g3 {
                            opacity: 0.3;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g4:nth-child(2) {
                            width: 50.2817%;
                        }
                        '.$addon_id.' .z-sof-g4 {
                            position: absolute;
                            bottom: 0;
                            right: 14px;
                            opacity: 0.8;
                        }
                        '.$addon_id.' .z-sof-g5 {
                            font-size: 30px;
                            line-height: 30px;
                            color: #585858;
                            font-weight: lighter;
                            text-indent: 15px;
                            border-left: 4px solid #d51920;
                            position: absolute;
                            top: 15.2381%;
                            left: 5.9%;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g6 {
                            font-size: 16px;
                            line-height: 36px;
                            color: #666666;
                            font-weight: lighter;
                            width: calc(100% - 11.8%);
                            position: absolute;
                            top: 32.381%;
                            left: 5.9%;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g4 img {
                            position: absolute;
                            bottom: 0;
                            right: 0;
                            width: 100%;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover .z-sof-g4 img:nth-child(2) {
                            opacity: 1;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g2:hover .z-sof-g4 img:nth-child(1) {
                            opacity: 0;
                            transition: 0.8s;
                        }
                        '.$addon_id.' .z-sof-g4 img:nth-child(2) {
                            opacity: 0;
                        }
                        '.$addon_id.' .z-sof-g4 img:nth-child(1) {
                            opacity: 1;
                        }
                        
                    }
                
                </style>
                ';
                $output.='<div class="z-sof-g1 clear wow fadeInUp animated" data-wow-delay="0.5s" data-wow-duration="1.5s" style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.5s; animation-name: fadeInUp;">';
                foreach ($jw_image_carousel_item2 as $key => $item) {
                    $output.=' <div class="z-sof-g2">';
                    $output.=' <div class="z-sof-g3"><img src="'.$item->bg_img.'"></div>';
                    $output.=' <div class="z-sof-g4"><img src="'.$item->bg_img_m1.'"><img src="'.$item->bg_img_m2.'"></div>';
                    $output.=' <div class="z-sof-g5">'.$item->title.'</div>';
                    $output.=' <div class="z-sof-g6">'.$item->content.'</div>';
                    $output.=' </div>';
                }
                 $output.='</div>';
        }


        return $output;

    }

    public function stylesheets()
    {

    }

    public function scripts()
    {

    }

    public function js()
    {

    }

    public function css()
    {
        
    }

    static function isComponentInstalled($component_name)
    {

    }
}
