<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'perpetual_calendar',
        'title' => JText::_('万年历'),
        'desc' => JText::_('万年历'),
        'category' => '常用插件',
        'attr' => array(
            'admin_label' => array(
                'type' => 'text',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                'std' => ''
            ),
            'site_id' => array(
                'std' => $site_id,
            ),
            'company_id' => array(
                'std' => $company_id,
            ),

        ),
    )
);