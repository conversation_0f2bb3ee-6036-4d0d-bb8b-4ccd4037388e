<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonSwiper_piclistitem extends JwpagefactoryAddons
{

    public function render()
    {

        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $output = ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'css/swiper.min.css">';
        $output .= ' <style>';
        $output .= $addonId . ' dd,' . $addonId . ' dl,' . $addonId . ' li,' . $addonId . ' ul{list-style:none}';
        $output .= $addonId . ' .swiper-container{height: 20rem;}';
        $output .= $addonId . ' html,body{position:relative;height:100%}';
        $output .= $addonId . ' body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}';
        $output .= $addonId . ' a{color:inherit;text-decoration:none}';
        $output .= $addonId . ' .hidden{overflow:hidden}';
        $output .= $addonId . ' .swiper-wrapper{left:-41px !important}';
        $output .= $addonId . ' .wj6 li:hover .fangwen{display:block}';
        $output .= $addonId . ' .bigimg img{transition:all 0.6s;-webkit-transition:all 0.6s}';
        $output .= $addonId . ' .bigimg:hover img{transform:scale(1.1);-webkit-transform:scale(1.1)}';
        $output .= $addonId . ' .wj6{margin:0 0 3%;margin-top: 2rem;}';
        $output .= $addonId . ' .wj6 .swiper-button-prev,' . $addonId . ' .wj6 .swiper-button-next{width:46px;height:46px;opacity:.8;top:40%;outline:none}';
        $output .= $addonId . ' .wj6 .swiper-button-next{background:none;right:29.5%;top:40%;}';
        $output .= $addonId . ' .wj6 .swiper-button-next:before{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar6.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transform:scale(0);transition:0.8s}';
        $output .= $addonId . ' .wj6 .swiper-button-next::after{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar5.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transition:0.8s}';
        $output .= $addonId . ' .wj6 .swiper-button-next:hover::after{transform:scale(0)}';    //111111111
        $output .= $addonId . ' .wj6 .swiper-button-next:hover::before{transform:scale(1)}';
        $output .= $addonId . ' .wj6 .swiper-button-prev{background:none;left:29.5%;top:40%;}';
        $output .= $addonId . ' .wj6 .swiper-button-prev:before{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar7.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transform:scale(0);transition:0.8s}';
        $output .= $addonId . ' .wj6 .swiper-button-prev::after{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar4.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transition:0.8s}';
        $output .= $addonId . ' .wj6 .swiper-button-prev:hover::after{transform:scale(0)}';
        $output .= $addonId . ' .wj6 .swiper-button-prev:hover::before{transform:scale(1)}';
        $output .= $addonId . ' .wj6_con{margin:5% 0 0;text-align:center}';
        $output .= $addonId . ' .wj6_con h3{font-size:24px;color:#333}';
        $output .= $addonId . ' .wj6_con p{color:#666;font-size:18px}';
        $output .= $addonId . ' .wj6 .swiper-slide .wj6_con{display:none}';
        $output .= $addonId . ' .wj6 .swiper-slide-active .wj6_con{display:block}';
        $output .= $addonId . ' .wj6_pic{border-radius:10px;overflow:hidden;position:relative;}';
        $output .= $addonId . ' .wj6_pic img{width:100%}';
        $output .= $addonId . ' .wj6_pic::after{width:100%;content:"";bottom:-14%;left:0;position:absolute;display:none}';
        $output .= $addonId . ' .wj6 .swiper-slide-active .wj6_pic::after{display:block}';
        $output .= $addonId . ' .wj6_box{padding-bottom:5%}';
        $output .= $addonId . ' .wj6 .fangwen{width:80px;height:30px;position:absolute;background:rgba(255,255,255,.9);border-radius:30px;right:30px;bottom:30px;line-height:30px;color:#868686;text-align:center;font-size:14px;z-index:99;display:none}';
        $output .= $addonId . ' .wj6 .fangwen img{width:16px;display:inline-block;margin-right:5px;vertical-align:middle}';
        $output .= $addonId . ' .swiper-slide-next .fangwen{bottom:140px}';
        $output .= '@media only screen and (max-width:1024px){';
        $output .= $addonId . ' .wj6 .swiper-button-next::after{background: none !important;}';
        $output .= $addonId . ' .wj6 .swiper-button-prev::after{background: none !important;}';
        $output .= $addonId . ' .wj6 .bigimg{background: none !important;}';

        $output .= $addonId . ' .wj6 .swiper-slide-next .wj6_con { display: none !important;}';
        $output .= $addonId . ' .wj6 .swiper-slide-active .wj6_con { display: block !important;}';
        
        $output .= $addonId . ' .wj6_con h3{font-size:18px;color:#333}';
        $output .= $addonId . ' .wj6_con p{color:#666;font-size:14px}';
        $output .= $addonId . ' .wj6_pic img{
            width: 100%;
            min-height: 175px;
            object-fit: cover;
            border-radius: 10px;
        }
        '.$addonId . ' .wj6 .swiper-slide-active .wj6_con{display:block!important}
        '.$addonId . ' .wj6 .swiper-slide-next .wj6_con{display:none!important}
        '.$addonId . ' .wj6 .fangwen img{
            min-height: 0!important;
            border-radius: 0px;
        }';
        $output .= ' }';
        $output .= $addonId . ' .mySwiper{overflow: hidden;}';
        $output .= ' </style>';
        $output .= '<div class="swiper  mySwiper wj6 wow fadeInUp swiper-initialized swiper-horizontal swiper-pointer-events animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;">';
        $output .= '  <ul class="swiper-wrapper">';
        foreach ($settings->jw_spiclist_item as $ke2y => $jwsplisti) {
            $output .= '    <li class="swiper-slide bigimg hidden "> <a href="' . $jwsplisti->a_url . '" target="_blank">';
            $output .= '      <div class="rel wj6_pic"><img src="' . $jwsplisti->bg_img . '" oncontextmenu="return false;"> <span class="fangwen abs"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_icon35.png" oncontextmenu="return false;">访问</span> </div>';
            $output .= '      <div class="tc wj6_con">';
            $output .= '        <h3>' . $jwsplisti->title . '</h3>';
            $output .= '        <p>' . $jwsplisti->title_ss . '</p>';
            $output .= '      </div>';
            $output .= '      </a> </li>';
        }
        $output .= '  </ul>';
        $output .= '  <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide" aria-controls="swiper-wrapper-e5104bfec109986037"></div>';
        $output .= '  <div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide" aria-controls="swiper-wrapper-e5104bfec109986037"></div>';
        $output .= '  <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>';
        $output .= '</div>';
        $output .= '<script>';
        $output .= '
        // var swiper = new Swiper("' . $addonId . ' .mySwiper", {
        //   slidesPerView: 3,
        //   spaceBetween:120,
        //   loop: true,
        //   pagination: {
        //       el: ".swiper-pagination",
        //       clickable: true,
        //     },
        //      navigation: {
        //       nextEl: ".swiper-button-next",
        //       prevEl: ".swiper-button-prev",
        //     },
        // });
        var swiper = new Swiper("' . $addonId . ' .mySwiper", {
            slidesPerView: 1.3,
            spaceBetween: 10,
            autoplay: true,
            centeredSlides: true,
            loop: true,
            navigation: {
                nextEl: "' . $addonId . ' .swiper-button-next",
                prevEl: "' . $addonId . ' .swiper-button-prev",
            },
            breakpoints: {
                1024: {
                    slidesPerView: 2.7,
                    spaceBetween: 106,
                    autoplay: false,
                },
            },
        });
    ';
        $output .= '</script>';
        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function css()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        // 上翻页按钮
        $swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
        $swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;

        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';

        $css = '';

        return $css;
    }

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;

        $js = '';

        return $js;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_spiclist_item = data.jw_spiclist_item;
        
        #>
        
        <style type="text/css">

            {{ addonId }} dd,{{ addonId }} dl,{{ addonId }} li,{{ addonId }} ul{list-style:none}
            {{ addonId }} .swiper-container{height: 20rem;}
            {{ addonId }} html,body{position:relative;height:100%}
            {{ addonId }} body{background:#eee;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:14px;color:#000;margin:0;padding:0}
            {{ addonId }} a{color:inherit;text-decoration:none}
            {{ addonId }} .hidden{overflow:hidden}
            {{ addonId }} .swiper-wrapper{left:-41px !important;height:400px;}
            {{ addonId }} .wj6 li:hover .fangwen{display:block}
            {{ addonId }} .bigimg img{transition:all 0.6s;-webkit-transition:all 0.6s}
            {{ addonId }} .bigimg:hover img{transform:scale(1.1);-webkit-transform:scale(1.1)}
            {{ addonId }} .wj6{margin:0 0 3%;margin-top: 2rem;}
            {{ addonId }} .wj6 .swiper-button-prev,{{ addonId }} .wj6 .swiper-button-next{width:46px;height:46px;opacity:.8;top:50%;outline:none}
            {{ addonId }} .wj6 .swiper-button-next{background:none;right:29.5%;top:50%;}
            {{ addonId }} .wj6 .swiper-button-next:before{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar6.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transform:scale(0);transition:0.8s}
            {{ addonId }} .wj6 .swiper-button-next::after{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar5.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transition:0.8s}
            {{ addonId }} .wj6 .swiper-button-next:hover::after{transform:scale(0)}
            {{ addonId }} .wj6 .swiper-button-next:hover::before{transform:scale(1)}
            {{ addonId }} .wj6 .swiper-button-prev{background:none;left:29.5%;top:50%;}
            {{ addonId }} .wj6 .swiper-button-prev:before{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar7.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transform:scale(0);transition:0.8s}
            {{ addonId }} .wj6 .swiper-button-prev::after{width:100%;height:100%;background:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_nar4.png) no-repeat;display:block;content:"";left:0;content:"";position:absolute;transition:0.8s}
            {{ addonId }} .wj6 .swiper-button-prev:hover::after{transform:scale(0)}
            {{ addonId }} .wj6 .swiper-button-prev:hover::before{transform:scale(1)}
            {{ addonId }} .wj6_con{margin:5% 0 0;text-align:center}
            {{ addonId }} .wj6_con h3{font-size:24px;color:#333}
            {{ addonId }} .wj6_con p{color:#666;font-size:18px}
            {{ addonId }} .wj6 .swiper-slide .wj6_con{display:none}
            {{ addonId }} .wj6 .swiper-slide-active .wj6_con{display:block}
            {{ addonId }} .wj6_pic{border-radius:10px;overflow:hidden}
            {{ addonId }} .wj6_pic img{width:100%;height: 100%;
                object-fit: scale-down;}
            {{ addonId }} .wj6_pic::after{width:100%;content:"";bottom:-14%;left:0;position:absolute;display:none}
            {{ addonId }} .wj6 .swiper-slide-active .wj6_pic::after{display:block}
            {{ addonId }} .wj6_box{padding-bottom:5%}
            {{ addonId }} .wj6 .fangwen{width:80px;height:30px;position:absolute;background:rgba(255,255,255,.9);border-radius:30px;right:30px;bottom:30px;line-height:30px;color:#868686;text-align:center;font-size:14px;z-index:99;display:none}
            {{ addonId }} .wj6 .fangwen img{width:16px;display:inline-block;margin-right:5px;vertical-align:middle}
            {{ addonId }} .swiper-slide-next .fangwen{bottom:140px}
            @media only screen and (max-width:1024px){
                {{ addonId }} .wj6 .swiper-button-next::after{background: none !important;}
                {{ addonId }} .wj6 .swiper-button-prev::after{background: none !important;}
                {{ addonId }} .wj6 .bigimg{background: none !important;}

                {{ addonId }} .wj6 .swiper-slide-next .wj6_con { display: none !important;}
                {{ addonId }} .wj6 .swiper-slide-active .wj6_con { display: block !important;}
            
                {{ addonId }} .wj6_con h3{font-size:18px;color:#333}
                {{ addonId }} .wj6_con p{color:#666;font-size:14px}
                {{ addonId }} .wj6_pic img{
                    width: 100%;
                    min-height: 175px;
                    object-fit: cover;
                    border-radius: 10px;
                }
                {{ addonId }} .wj6 .swiper-slide-active .wj6_con{display:block!important}
                {{ addonId }} .wj6 .swiper-slide-next .wj6_con{display:none!important}
                {{ addonId }} .wj6 .fangwen img{
                    min-height: 0!important;
                    border-radius: 0px;
                }
            
            }
        </style>

        <div class="swiper  mySwiper wj6 swiper-initialized swiper-horizontal swiper-pointer-events " >
            <ul class="swiper-wrapper">
                
                    <# if(_.isArray(data.jw_spiclist_item)){
                        _.each(data.jw_spiclist_item, function(jwsplisti,key){ #>
                        <li class="swiper-slide bigimg hidden1 " <# if(key==0){ #> style="width:28%;margin:0px 1%;" <# } #> <# if(key==1){ #> style="width:34%;margin:0px 3% 0px 3%;" <# } #> <# if(key==2){ #> style="width:28%;margin:0px 1%;" <# } #> > 
                            <a href="{{jwsplisti.a_url}}" target="_blank" style="display:inline-block;height:100%;">
                                <div class="rel wj6_pic" style="height:100%;">
                                    <img src=\'{{jwsplisti.bg_img}}\' oncontextmenu="return false;"> <span class="fangwen abs"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_piclistitem/assets/' . 'img/f_icon35.png" oncontextmenu="return false;">访问</span> 
                                </div>
                                <div class="tc wj6_con">
                                    <h3>{{jwsplisti.title}}</h3>
                                    <p>{{jwsplisti.title_ss}}</p>
                                </div>
                            </a> 
                        </li>
                    <#
                        })
                    } #>
            </ul>
            <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide" aria-controls="swiper-wrapper-e5104bfec109986037"></div>
            <div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide" aria-controls="swiper-wrapper-e5104bfec109986037"></div>
            <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
        </div>
        
        ';

        return $output;
    }

}
