<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined("_JEXEC") or die("Restricted access");

class JwpagefactoryAddonImage extends JwpagefactoryAddons
{
    public function render()
    {
        $settings = $this->addon->settings;
        $class =
            isset($settings->class) && $settings->class ? $settings->class : "";
        $title =
            isset($settings->title) && $settings->title ? $settings->title : "";
        $title_position =
            isset($settings->title_position) && $settings->title_position
                ? $settings->title_position
                : "top";
        $heading_selector =
            isset($settings->heading_selector) && $settings->heading_selector
                ? $settings->heading_selector
                : "h3";

        $image_layout =
            isset($settings->image_layout) && $settings->image_layout
                ? $settings->image_layout
                : "layout1";

        //开启遮罩
        $isMask =
            isset($settings->isMask) && $settings->isMask
                ? $settings->isMask
                : 0;
        $mask_title =
            isset($settings->mask_title) && $settings->mask_title
                ? $settings->mask_title
                : "图片标题";
        $mask_title_fontsize =
            isset($settings->mask_title_fontsize) &&
            $settings->mask_title_fontsize
                ? $settings->mask_title_fontsize
                : "20";
        $mask_title_font_color =
            isset($settings->mask_title_font_color) &&
            $settings->mask_title_font_color
                ? $settings->mask_title_font_color
                : "#fff";
        $mask_intro =
            isset($settings->mask_intro) && $settings->mask_intro
                ? $settings->mask_intro
                : "图片简介";
        $mask_intro_fontsize =
            isset($settings->mask_intro_fontsize) &&
            $settings->mask_intro_fontsize
                ? $settings->mask_intro_fontsize
                : "14";
        $mask_intro_font_color =
            isset($settings->mask_intro_font_color) &&
            $settings->mask_intro_font_color
                ? $settings->mask_intro_font_color
                : "#fff";
        $mask_bg_color =
            isset($settings->mask_bg_color) && $settings->mask_bg_color
                ? $settings->mask_bg_color
                : "rgba(15, 63, 119, 0.7)";
        $button_title =
            isset($settings->button_title) && $settings->button_title
                ? $settings->button_title
                : "了解更多";
        $button_bg_color =
            isset($settings->button_bg_color) && $settings->button_bg_color
                ? $settings->button_bg_color
                : "#89c400";
        $mask_title_lineheight =
            isset($settings->mask_title_lineheight) &&
            $settings->mask_title_lineheight
                ? $settings->mask_title_lineheight
                : "65";
        $mask_button_color =
            isset($settings->mask_button_color) && $settings->mask_button_color
                ? $settings->mask_button_color
                : "#fff";

        $isudown =
            isset($settings->isudown) && $settings->isudown
                ? $settings->isudown
                : 0; //开启图片上下跳动
        $image_open_bfb =
            isset($settings->image_open_bfb) && $settings->image_open_bfb
                ? $settings->image_open_bfb
                : 0;
        $image_width_bfb =
            isset($settings->image_width_bfb) && $settings->image_width_bfb
                ? $settings->image_width_bfb
                : "100";

        $zzfont_family =
            isset($settings->zzfont_family) && $settings->zzfont_family
                ? $settings->zzfont_family
                : "";
        $zz_animt =
            isset($settings->zz_animt) && $settings->zz_animt
                ? $settings->zz_animt
                : "bot";
        $zz_padding = isset($settings->zz_padding)
            ? $settings->zz_padding
            : "35% 1% 10% 1%";

        // 后台获取广告图数据  2021.11.16
        $aboutcase =
            isset($settings->aboutcase) && $settings->aboutcase
                ? $settings->aboutcase
                : 0;
        $adv_id =
            isset($settings->adv_id) && $settings->adv_id
                ? $settings->adv_id
                : "";
        // 后台获取图片管理数据  2021.11.16
        $ht_image_data =
            isset($settings->ht_image_data) && $settings->ht_image_data
                ? $settings->ht_image_data
                : 0;
        $ht_image_id =
            isset($settings->ht_image_id) && $settings->ht_image_id
                ? $settings->ht_image_id
                : "";

        if ($aboutcase && $aboutcase == 1 && $adv_id) {
            $infos = JwPageFactoryBase::getAdvById($adv_id);
            if ($infos) {
                if ($infos->state == 1) {
                    $image = $infos->images ? $infos->images : "";
                    $alt_text = $infos->title ? $infos->title : "";
                    $mask_title = $infos->title ? $infos->title : "";
                    $mask_intro = $infos->introtext ? $infos->introtext : "";
                }
            }
        } elseif ($ht_image_data && $ht_image_data == 1 && $ht_image_id) {
            $infos = JwPageFactoryBase::getimageById($ht_image_id);
            if ($infos) {
                if ($infos->state == 1) {
                    $image = $infos->image_intro ? $infos->image_intro : "";
                    $alt_text = $infos->title ? $infos->title : "";
                    $mask_title = $infos->title ? $infos->title : "";
                    $mask_intro = $infos->introtext ? $infos->introtext : "";
                }
            }
        } else {
            $image =
                isset($settings->image) && $settings->image
                    ? $settings->image
                    : "";
            $alt_text =
                isset($settings->alt_text) && $settings->alt_text
                    ? $settings->alt_text
                    : "";
            $mask_title =
                isset($settings->mask_title) && $settings->mask_title
                    ? $settings->mask_title
                    : "图片标题";
            $mask_intro =
                isset($settings->mask_intro) && $settings->mask_intro
                    ? $settings->mask_intro
                    : "图片简介";
        }

        //Options

        $position =
            isset($settings->position) && $settings->position
                ? $settings->position
                : "";
        $link =
            isset($settings->link) && $settings->link ? $settings->link : "";
        $target =
            isset($settings->target) && $settings->target
                ? 'target="' . $settings->target . '" rel="noopener noreferrer"'
                : "";
        $open_lightbox =
            isset($settings->open_lightbox) && $settings->open_lightbox
                ? $settings->open_lightbox
                : 0;
        $image_overlay =
            isset($settings->overlay_color) && $settings->overlay_color ? 1 : 0;
        $detail_page_id = isset($settings->detail_page_id)
            ? $settings->detail_page_id
            : 0;
        $link_target =
            isset($settings->link_new_tab) && $settings->link_new_tab
                ? 'target="_blank" rel="noopener noreferrer"'
                : "";
        $wx_image_code2 = isset($settings->wx_image_code2)
            ? $settings->wx_image_code2
            : "";
        $wx_image_code2_w = isset($settings->wx_image_code2_w)
            ? $settings->wx_image_code2_w
            : 0;
        $wx_image_code2_h = isset($settings->wx_image_code2_h)
            ? $settings->wx_image_code2_h
            : 0;
        $wx_image_code2_x = isset($settings->wx_image_code2_x)
            ? $settings->wx_image_code2_x
            : 0;
        $wx_image_code2_y = isset($settings->wx_image_code2_y)
            ? $settings->wx_image_code2_y
            : 0;
        $fix_img_height = isset($settings->fix_img_height)
            ? $settings->fix_img_height
            : 0;
        $image_fangda = isset($settings->image_fangda)
            ? $settings->image_fangda
            : 0;
        $layout1_open_link = isset($settings->layout1_open_link)
            ? $settings->layout1_open_link
            : 0;

        // 开启二维码
        $is_hover =
            isset($settings->is_hover) && $settings->is_hover
                ? $settings->is_hover
                : "0";
        $erweim =
            isset($settings->erweim) && $settings->erweim
                ? $settings->erweim
                : "https://oss.lcweb01.cn/joomla/20220818/dbab5d8110af6fe79939930cbc6aac5b.png";
        $erweim_bg =
            isset($settings->erweim_bg) && $settings->erweim_bg
                ? $settings->erweim_bg
                : "https://oss.lcweb01.cn/joomla/20220818/74ca67254c87c948346935fb67d22fc1.png";
        $erweim_width =
            isset($settings->erweim_width) && $settings->erweim_width
                ? $settings->erweim_width
                : "100";
        $erweim_rele =
            isset($settings->erweim_rele) && $settings->erweim_rele
                ? $settings->erweim_rele
                : "bot";
        $erweim_top =
            isset($settings->erweim_top) && $settings->erweim_top
                ? $settings->erweim_top
                : "30";
        $erweim_left =
            isset($settings->erweim_left) && $settings->erweim_left
                ? $settings->erweim_left
                : "0";

        //Lazyload image
        $dimension = $this->get_image_dimension($image);
        $dimension = implode(" ", $dimension);
        $attribs = "";
        $company_id = $_GET["company_id"] ?? 0;
        $site_id = $_GET["site_id"] ?? 0;
        $layout_id = $_GET["layout_id"] ?? 0;
        $addon_id = "#jwpf-addon-" . $this->addon->id;
        if ($detail_page_id) {
            $id = base64_encode($detail_page_id);
            $link =
                "component/jwpagefactory/?view=page&id=" .
                $id .
                "&company_id=" .
                $company_id .
                "&site_id=" .
                $site_id .
                "&layout_id=" .
                $layout_id;
        }
        $placeholder =
            $image == "" ? false : $this->get_image_placeholder($image);
        if (
            strpos($image, "http://") !== false ||
            strpos($image, "https://") !== false
        ) {
            $image = $image;
        } else {
            $image = JURI::base(true) . "/" . $image;
        }

        $output = "";

        if ($image_layout == "layout2") {
            $pc_img = isset($settings->pc_img)
                ? $settings->pc_img
                : "https://oss.lcweb01.cn/joomla/20220723/1b7d1451d17a22abb1dd4b7d0debeb5c.jpg";
            $phone_img = isset($settings->phone_img)
                ? $settings->phone_img
                : "https://oss.lcweb01.cn/joomla/20220723/bc5fe0ea84872eee9803013b2b58e8bb.jpg";
            $open_text = isset($settings->open_text) ? $settings->open_text : 1;
            $content = isset($settings->content)
                ? $settings->content
                : "经典系列";

            if (
                isset($settings->content_fontsize) &&
                $settings->content_fontsize
            ) {
                if (is_object($settings->content_fontsize)) {
                    $content_fontsize_md = $settings->content_fontsize->md;
                    $content_fontsize_sm = $settings->content_fontsize->sm;
                    $content_fontsize_xs = $settings->content_fontsize->xs;
                } else {
                    $content_fontsize_md = $settings->content_fontsize;
                    $content_fontsize_sm = $settings->content_fontsize_sm;
                    $content_fontsize_xs = $settings->content_fontsize_xs;
                }
            } else {
                $content_fontsize_md = "52";
                $content_fontsize_sm = "35";
                $content_fontsize_xs = "28";
            }

            $content_color = isset($settings->content_color)
                ? $settings->content_color
                : "#fff";
            $open_img = isset($settings->open_img) ? $settings->open_img : 1;
            $bg_img = isset($settings->bg_img)
                ? $settings->bg_img
                : "https://oss.lcweb01.cn/joomla/20220723/635e913d671e56cfda28915753d4eddd.png";
            if (isset($settings->bg_img_height) && $settings->bg_img_height) {
                if (is_object($settings->bg_img_height)) {
                    $bg_img_height_md = $settings->bg_img_height->md;
                    $bg_img_height_sm = $settings->bg_img_height->sm;
                    $bg_img_height_xs = $settings->bg_img_height->xs;
                } else {
                    $bg_img_height_md = $settings->bg_img_height;
                    $bg_img_height_sm = $settings->bg_img_height_sm;
                    $bg_img_height_xs = $settings->bg_img_height_xs;
                }
            } else {
                $bg_img_height_md = "68";
                $bg_img_height_sm = "50";
                $bg_img_height_xs = "35";
            }

            $open_button = isset($settings->open_button)
                ? $settings->open_button
                : 1;
            if (isset($settings->button_width) && $settings->button_width) {
                if (is_object($settings->button_width)) {
                    $button_width_md = $settings->button_width->md;
                    $button_width_sm = $settings->button_width->sm;
                    $button_width_xs = $settings->button_width->xs;
                } else {
                    $button_width_md = $settings->button_width;
                    $button_width_sm = $settings->button_width_sm;
                    $button_width_xs = $settings->button_width_xs;
                }
            } else {
                $button_width_md = "171";
                $button_width_sm = "130";
                $button_width_xs = "110";
            }

            $button_img = isset($settings->button_img)
                ? $settings->button_img
                : "https://oss.lcweb01.cn/joomla/20220723/e7ada5b4ef717d3cf6e46bdf7905c3cb.png";
            $button_imghv = isset($settings->button_imghv)
                ? $settings->button_imghv
                : "https://oss.lcweb01.cn/joomla/20220723/0756cbcc1dcd81b4e7c2fe20736db15f.png";
            $button_text = isset($settings->button_text)
                ? $settings->button_text
                : "—— read more";

            if (isset($settings->cont_left) && $settings->cont_left) {
                if (is_object($settings->cont_left)) {
                    $cont_left_md = $settings->cont_left->md;
                    $cont_left_sm = $settings->cont_left->sm;
                    $cont_left_xs = $settings->cont_left->xs;
                } else {
                    $cont_left_md = $settings->cont_left;
                    $cont_left_sm = $settings->cont_left_sm;
                    $cont_left_xs = $settings->cont_left_xs;
                }
            } else {
                $cont_left_md = "150";
                $cont_left_sm = "120";
                $cont_left_xs = "18";
            }

            if (isset($settings->cont_top) && $settings->cont_top) {
                if (is_object($settings->cont_top)) {
                    $cont_top_md = $settings->cont_top->md;
                    $cont_top_sm = $settings->cont_top->sm;
                    $cont_top_xs = $settings->cont_top->xs;
                } else {
                    $cont_top_md = $settings->cont_top;
                    $cont_top_sm = $settings->cont_top_sm;
                    $cont_top_xs = $settings->cont_top_xs;
                }
            } else {
                $cont_top_md = "48";
                $cont_top_sm = "48";
                $cont_top_xs = "48";
            }

            $open_intro = isset($settings->open_intro)
                ? $settings->open_intro
                : 0;
            $intro_text = isset($settings->intro_text)
                ? $settings->intro_text
                : "";
            $fontbg_family = isset($settings->fontbg_family)
                ? $settings->fontbg_family
                : "";

            $open_link = isset($settings->open_link) ? $settings->open_link : 0;
            $sec_link = isset($settings->sec_link)
                ? $settings->sec_link
                : "nei";
            $link_w = isset($settings->link_w) ? $settings->link_w : "";
            $ndetail_page_id = isset($settings->ndetail_page_id)
                ? $settings->ndetail_page_id
                : 0;
            $likns = "";
            $targets = "";
            if ($open_link == 1) {
                $targets =
                    isset($settings->targets) && $settings->targets
                        ? 'target="_blank" rel="noopener noreferrer"'
                        : "";

                if ($sec_link == "nei") {
                    if ($ndetail_page_id) {
                        $id = base64_encode($ndetail_page_id);
                        $likns =
                            "component/jwpagefactory/?view=page&id=" .
                            $id .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $likns = $link_w;
                }
            } else {
                $likns = "javascript:;";
            }

            $output .=
                '
                <style>
                    ' .
                $addon_id .
                ' .i100 {
                      overflow: hidden;
                    }
                    ' .
                $addon_id .
                ' .i200 > img {
                      height: 100%;
                    }
                    ' .
                $addon_id .
                ' .i200 {
                      overflow: hidden;
                    }
                    ' .
                $addon_id .
                ' .a1 > a {
                      width: 100%;
                      height: 100%;
                      position: absolute;
                      top: 0;
                      left: 0;
                      display: block;
                    }
                    @media only screen and (min-width: 1480px) {
                        ' .
                $addon_id .
                ' .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-corner{background: none;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                        ' .
                $addon_id .
                ' .icecream111{width: 1920px!important;height: 100%!important;margin-left: calc(50% - 1920px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-wrapper{display: block;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 1920px/2);}
                        ' .
                $addon_id .
                ' .icecream1-a2{width: 100%;height: 100%;position: relative;}

                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(1){display: block;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(2){display: none;}
                        ' .
                $addon_id .
                ' .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                        ' .
                $addon_id .
                " .icecream1-a3{position: absolute;top: " .
                $cont_top_md .
                "%;left: " .
                $cont_left_md .
                'px;}
                        ' .
                $addon_id .
                " .icecream1-a4{font-size: " .
                $content_fontsize_md .
                "px;line-height: " .
                $content_fontsize_md .
                "px;color: " .
                $content_color .
                ';font-weight: bold;margin-bottom: 20px;}
                        ' .
                $addon_id .
                " .icecream1-a5{height: " .
                $bg_img_height_md .
                'px;margin-bottom: 45px;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}

                        ' .
                $addon_id .
                " .bt2-a1{width: " .
                $button_width_md .
                'px;position: relative;}
                        ' .
                $addon_id .
                ' .bt2-a2{width: 100%;position: relative;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(1){position: relative;opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a1:hover .bt2-a2 img:nth-child(1){opacity: 0;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a1:hover .bt2-a2 img:nth-child(2){opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a3{width: 125px;position: absolute;top: calc(50% - 24px/2 + 4px);left: calc(50% - 125px/2);}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(1){width: 24px;height: 1px;background: #fff;position: relative;float: left;top: 6px;margin-right: 12px}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(2){font-size: 14px;line-height: 14px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                    }
                    @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                        ' .
                $addon_id .
                ' .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-corner{background: none;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                        ' .
                $addon_id .
                ' .icecream111{width: 1920px!important;height: 100%!important;margin-left: calc(50% - 1920px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-wrapper{display: block;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 1920px/2);}
                        ' .
                $addon_id .
                ' .icecream1-a2{width: 100%;height: 100%;position: relative;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(1){display: block;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(2){display: none;}
                        ' .
                $addon_id .
                ' .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                        ' .
                $addon_id .
                " .icecream1-a3{position: absolute;top: " .
                $cont_top_sm .
                "%;left: " .
                $cont_left_sm .
                'px;}
                        ' .
                $addon_id .
                " .icecream1-a4{font-size: " .
                $content_fontsize_sm .
                "px;line-height: " .
                $content_fontsize_sm .
                "px;color: " .
                $content_color .
                ';font-weight: bold;margin-bottom: 16px;}
                        ' .
                $addon_id .
                " .icecream1-a5{height: " .
                $bg_img_height_sm .
                'px;margin-bottom: 30px;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                " .bt2-a1{width: " .
                $button_width_md .
                'px;position: relative;}
                        ' .
                $addon_id .
                ' .bt2-a2{width: 100%;position: relative;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(1){position: relative;opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a1:hover .bt2-a2 img:nth-child(1){opacity: 0;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a1:hover .bt2-a2 img:nth-child(2){opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .bt2-a3{width: 125px;position: absolute;top: calc(50% - 24px/2 + 4px);left: calc(50% - 125px/2);}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(1){width: 24px;height: 1px;background: #fff;position: relative;float: left;top: 6px;margin-right: 12px}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(2){font-size: 14px;line-height: 14px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                    }
                    @media only screen and (max-width: 1023px) {
                        ' .
                $addon_id .
                ' .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-corner{background: none;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                        ' .
                $addon_id .
                ' .icecream111{width: 100%!important;height: 100%!important;z-index: 1;overflow: hidden;overflow-y: auto;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-wrapper{display: block;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: 0;}
                        ' .
                $addon_id .
                ' .icecream1-a2{width: 100%;height: 100%;position: relative;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(1){display: none;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(2){display: block;}
                        ' .
                $addon_id .
                ' .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                        ' .
                $addon_id .
                " .icecream1-a3{position: absolute;top: " .
                $cont_top_xs .
                "%;left: " .
                $cont_left_xs .
                'px;}
                        ' .
                $addon_id .
                " .icecream1-a4{font-size: " .
                $content_fontsize_xs .
                "px;line-height: " .
                $content_fontsize_xs .
                "px;color: " .
                $content_color .
                ';font-weight: bold;margin-bottom: 10px;}
                        ' .
                $addon_id .
                " .icecream1-a5{height: " .
                $bg_img_height_xs .
                'px;margin-bottom: 23px;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a1{display: block;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream112{width: 10px;position: absolute;top: calc(50% - 10px*7/2 - 20px*3);right: 20px;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet{width: 10px;height: 10px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 20px;position: relative;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{width: 25px;height: 28px;position: absolute;top: calc(50% - 28px/2);left: calc(50% - 25px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                        ' .
                $addon_id .
                " .bt2-a1{width:" .
                $button_width_xs .
                'px;position: relative;display: none;}
                        ' .
                $addon_id .
                ' .bt2-a2{width: 100%;position: relative;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(1){position: relative;opacity: 1;}
                        ' .
                $addon_id .
                ' .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;}
                        ' .
                $addon_id .
                ' .bt2-a3{width: 104px;position: absolute;top: calc(50% - 12px/2 + 2px);left: calc(50% - 104px/2 + 5px);}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(1){width: 12px;height: 1px;background: #fff;position: relative;float: left;top: 3px;margin-right:6px;}
                        ' .
                $addon_id .
                ' .bt2-a3>div:nth-child(2){font-size: 11px;line-height: 11px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                    }
                    @media only screen and (min-width: 1921px) {
                        ' .
                $addon_id .
                ' .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-corner{background: none;}
                        ' .
                $addon_id .
                ' .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                        ' .
                $addon_id .
                ' .icecream111{width: 2560px!important;height: 100%!important;margin-left: calc(50% - 2560px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-wrapper{display: block;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                        ' .
                $addon_id .
                ' .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 2560px/2);}
                        ' .
                $addon_id .
                ' .icecream1-a2{width: 100%;height: 100%;position: relative;}

                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(1){display: block;}
                        ' .
                $addon_id .
                ' .icecream1-a2 img:nth-child(2){display: none;}
                        ' .
                $addon_id .
                ' .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                        ' .
                $addon_id .
                " .icecream1-a3{position: absolute;top: " .
                $cont_top_md .
                "%;left: " .
                $cont_left_md .
                'px;}
                        ' .
                $addon_id .
                " .icecream1-a4{font-size: " .
                $content_fontsize_md .
                "px;line-height: " .
                $content_fontsize_md .
                "px;color: " .
                $content_color .
                ';font-weight: bold;margin-bottom: 20px;}
                        ' .
                $addon_id .
                " .icecream1-a5{height: " .
                $bg_img_height_md .
                'px;margin-bottom: 45px;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                        ' .
                $addon_id .
                ' .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                        ' .
                $addon_id .
                ' .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                    }

                    ' .
                $addon_id .
                ' .icecream1-a2.i100 img{
                        animation-name: small;
                        animation-duration: 10s;
                    }

                    @keyframes small {
                        0%{
                            transform: scale(1.2);
                        }
                        100%{
                            transform: scale(1);
                        }
                    }

                    ' .
                $addon_id .
                ' .fadeInUp {
                      -webkit-animation-name: fadeInUp;
                      animation-name: fadeInUp;
                    }
                    ' .
                $addon_id .
                ' .animated {
                      -webkit-animation-duration: 1s;
                      animation-duration: 1s;
                      -webkit-animation-fill-mode: both;
                      animation-fill-mode: both;
                    }
                    @-webkit-keyframes fadeInUp {
                      0% {
                        opacity: 0;
                        -webkit-transform: translate3d(0, 100%, 0);
                                transform: translate3d(0, 100%, 0);
                      }

                      100% {
                        opacity: 1;
                        -webkit-transform: none;
                                transform: none;
                      }
                    }

                    @keyframes fadeInUp {
                      0% {
                        opacity: 0;
                        -webkit-transform: translate3d(0, 100%, 0);
                                transform: translate3d(0, 100%, 0);
                      }

                      100% {
                        opacity: 1;
                        -webkit-transform: none;
                                transform: none;
                      }
                    }
                </style>
            ';

            $output .=
                '
                <div class="" style="height: 100%;">
                    <div class="icecream1-a2 i100">
                        <img src=\'' .
                $pc_img .
                '\'>
                        <img src=\'' .
                $phone_img .
                '\'>
                    </div>
                    <div class="icecream1-a3 ani fadeInUp animated" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.3s" style="visibility: visible; animation-duration: 1s; animation-delay: 0.3s;animation-duration:1s;-webkit-animation-duration:1s;animation-delay:0.3s;-webkit-animation-delay:0.3s;" swiper-animate-style-cache="visibility: visible;animation-duration:1s;-webkit-animation-duration:1s;animation-delay:0.3s;-webkit-animation-delay:0.3s;">';
            if ($open_text == 1) {
                $output .= '<div class="icecream1-a4">' . $content . "</div>";
            }
            if ($open_img == 1) {
                $output .=
                    '<div class="icecream1-a5 i200"><img src=\'' .
                    $bg_img .
                    '\'></div>';
            }
            if ($open_button == 1) {
                $output .=
                    '
                            <div class="bt2-a1 a1">
                                <div class="bt2-a2 i100">
                                    <img src=\'' .
                    $button_img .
                    '\'>
                                    <img src=\'' .
                    $button_imghv .
                    '\'>
                                </div>
                                <div class="bt2-a3"><div></div><div>' .
                    $button_text .
                    '</div></div>
                                <a href="' .
                    $likns .
                    '" ' .
                    $targets .
                    '></a>
                            </div>';
            }
            if ($open_intro == 1) {
                $output .=
                    '<div style="font-family:' .
                    $fontbg_family .
                    ';">' .
                    $intro_text .
                    "</div>";
            }
            $output .= '</div>
                </div>
            ';
        } elseif ($image_layout == "layout3") {
            $type3_img = isset($settings->type3_img)
                ? $settings->type3_img
                : "https://oss.lcweb01.cn/joomla/20230105/288455b29b142e19a02b268e2cf25360.jpg";
            $type3_title1 = isset($settings->type3_title1)
                ? $settings->type3_title1
                : "护助时代";
            $type3_title1_open = isset($settings->type3_title1_open)
                ? $settings->type3_title1_open
                : "0";
            $tz1_fs = isset($settings->tz1_fs) ? $settings->tz1_fs : "nei";
            $link1 = isset($settings->link1) ? $settings->link1 : "";
            $detail_page_id1 = isset($settings->detail_page_id1)
                ? $settings->detail_page_id1
                : "";
            $target1 = isset($settings->target1) ? $settings->target1 : "_self";

            $type3_title2 = isset($settings->type3_title2)
                ? $settings->type3_title2
                : "参访交流";
            $type3_title2_open = isset($settings->type3_title2_open)
                ? $settings->type3_title2_open
                : "0";
            $tz2_fs = isset($settings->tz2_fs) ? $settings->tz2_fs : "nei";
            $link2 = isset($settings->link2) ? $settings->link2 : "";
            $detail_page_id2 = isset($settings->detail_page_id2)
                ? $settings->detail_page_id2
                : "";
            $target2 = isset($settings->target2) ? $settings->target2 : "_self";

            $type3_title3 = isset($settings->type3_title3)
                ? $settings->type3_title3
                : "介护教育";
            $type3_title3_open = isset($settings->type3_title3_open)
                ? $settings->type3_title3_open
                : "0";
            $tz3_fs = isset($settings->tz3_fs) ? $settings->tz3_fs : "nei";
            $link3 = isset($settings->link3) ? $settings->link3 : "";
            $detail_page_id3 = isset($settings->detail_page_id3)
                ? $settings->detail_page_id3
                : "";
            $target3 = isset($settings->target3) ? $settings->target3 : "_self";

            $type3_title4 = isset($settings->type3_title4)
                ? $settings->type3_title4
                : "日语培训";
            $type3_title4_open = isset($settings->type3_title4_open)
                ? $settings->type3_title4_open
                : "0";
            $tz4_fs = isset($settings->tz4_fs) ? $settings->tz4_fs : "nei";
            $link4 = isset($settings->link4) ? $settings->link4 : "";
            $detail_page_id4 = isset($settings->detail_page_id4)
                ? $settings->detail_page_id4
                : "";
            $target4 = isset($settings->target4) ? $settings->target4 : "_self";

            $type3_title5 = isset($settings->type3_title5)
                ? $settings->type3_title5
                : "留学合作";
            $type3_title5_open = isset($settings->type3_title5_open)
                ? $settings->type3_title5_open
                : "0";
            $tz5_fs = isset($settings->tz5_fs) ? $settings->tz5_fs : "nei";
            $link5 = isset($settings->link5) ? $settings->link5 : "";
            $detail_page_id5 = isset($settings->detail_page_id5)
                ? $settings->detail_page_id5
                : "";
            $target5 = isset($settings->target5) ? $settings->target5 : "_self";

            $output .=
                '
                <style>
                    ' .
                $addon_id .
                ' img{
                        max-width:inherit;
                    }
                    ' .
                $addon_id .
                ' .banner{
                        position: relative;
                    }

                    ' .
                $addon_id .
                ' .banner .b-wrap{
                        width: 100%;
                        position: relative;
                        overflow: hidden;
                        max-height: 1000px;
                    }

                    ' .
                $addon_id .
                ' .banner .b-wrap img{
                        width: 108%;
                        -webkit-transition: -webkit-transform 0.5s;
                        -moz-transition: -moz-transform 0.5s;
                        -ms-transition: -ms-transform 0.5s;
                        -o-transition: -o-transform 0.5s;
                        transition: transform 0.5s;
                    }

                    ' .
                $addon_id .
                ' .banner .b-wrap b{
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0,0,0,0.6);
                        top: 0;
                        left: 0;
                    }

                    ' .
                $addon_id .
                ' .banner .pwrap{
                        position: absolute;
                        width: 80%;
                        height: 100%;
                        top: 0;
                        left: 50%;
                        margin-left: -40%;
                    }


                    @keyframes mousemove{

                        0% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                        25% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        50% {
                            -webkit-transform: translateY(5px);
                            transform: translateY(5px);
                        }

                        75% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        100% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }
                    }
                    @-webkit-keyframes mousemove{

                        0% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                        25% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        50% {
                            -webkit-transform: translateY(5px);
                            transform: translateY(5px);
                        }


                        75% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        100% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }
                    }

                    @keyframes rotate{

                        0% {
                            -webkit-transform: rotate(0);
                            transform: rotate(0);
                        }

                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                    }
                    @-webkit-keyframes rotate{

                        0% {
                            -webkit-transform: rotate(0);
                            transform: rotate(0);
                        }

                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }
                    }

                    @keyframes dot{

                        25% {
                            border-color: transparent;
                            background-color: transparent;
                        }

                        50% {
                            border-right-color: transparent;
                            background-color: transparent;
                        }

                        75% {
                            border-right-color: transparent;
                        }

                    }
                    @-webkit-keyframes dot{

                        25% {
                            border-color: transparent;
                            background-color: transparent;
                        }

                        50% {
                            border-right-color: transparent;
                            background-color: transparent;
                        }

                        75% {
                            border-right-color: transparent;
                        }
                    }

                    @keyframes rotates{

                        0% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                        50% {
                            -webkit-transform: rotate(180deg);
                            transform: rotate(180deg);
                        }

                        100% {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }
                    }
                    @-webkit-keyframes rotates{

                        0% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                        50% {
                            -webkit-transform: rotate(180deg);
                            transform: rotate(180deg);
                        }

                        100% {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }
                    }
                    ' .
                $addon_id .
                ' .b-anim{
                        width: 1200px;
                        background-size: cover;
                        height: 950px;
                        position: absolute;
                        left: 50%;
                        margin-left: -600px;
                        z-index: 7;
                    }

                    ' .
                $addon_id .
                ' .b-anim .outer-cir{

                        width: 1110px;
                        height: 1100px;
                        border: 28px dashed #fff;
                        opacity: 0.12;
                        border-radius: 640px;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -560px;
                        margin-top: -578px;
                        z-index: 2;
                        -webkit-animation: rotate 80s linear 0s infinite;
                        animation: rotate 80s linear 0s infinite;

                    }

                    ' .
                $addon_id .
                ' .b-anim .outer-nc{

                        width: 900px;
                        height:900px;
                        border: 10px dotted #fff;
                        opacity: 0.12;
                        border-radius: 640px;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -460px;
                        margin-top: -460px;
                        z-index: 2;
                        -webkit-animation: rotates 80s linear 0s infinite;
                        animation: rotates 80s linear 0s infinite;

                    }

                    ' .
                $addon_id .
                ' .line-box{

                        width: 500px;
                        height: 500px;
                        position: absolute;
                        left: 50%;
                        margin-left: -250px;
                        top: 50%;
                        margin-top: -256px;
                        z-index: 8;

                    }

                    ' .
                $addon_id .
                ' .line-box .line-fir {

                        width: 500px;
                        height: 500px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        -webkit-animation: rotate 30s linear 0s infinite;
                        animation: rotate 30s linear 0s infinite;

                    }

                    ' .
                $addon_id .
                ' .line-box .tit {

                        display: inline-block;
                        position: absolute;
                        width: 24px;
                        height: 24px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/730fb41b5061c39460f83df4ec1de871.png) no-repeat center;
                        background-size: cover;
                        top: 50%;
                        margin-top: -12px;

                    }

                    ' .
                $addon_id .
                ' .line-box .line-sec .tit {
                        left: -11px;
                    }

                    ' .
                $addon_id .
                ' .line-box .line-fir .tit {
                        right: -11px;
                    }

                    ' .
                $addon_id .
                ' .line-box .line-sec {
                        width: 360px;
                        height: 360px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        position: absolute;
                        left: 50%;
                        margin-left: -180px;
                        top: 50%;
                        margin-top: -180px;
                        border-radius: 50%;
                        -webkit-animation: rotate 15s linear 0s infinite;
                        animation: rotate 15s linear 0s infinite;

                    }

                    ' .
                $addon_id .
                ' .line-box .point-box {
                        display: block;
                        width:100px;
                        height:100px;
                        position: absolute;
                        opacity: 0;
                        font-size: 18px;
                        color: #fff;
                        text-align: center;
                        line-height: 20px;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-box span{
                        display: block;
                        width: 50%;
                        left:25%;
                        position: absolute;
                        top:50%;
                        margin-top:-20px;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-fir {
                        background-size: cover;
                        left: 50%;
                        top: -40px;
                        margin-left: -50px;
                    }
                    ' .
                $addon_id .
                ' .line-box .point-box i.a1,.line-box .point-box i.a3{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/911a47083beffb6c13a1550c178e4726.png) no-repeat center;
                        background-size: cover;
                    }
                    ' .
                $addon_id .
                ' .line-box .point-box i.a2,.line-box .point-box i.a5{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/4ee850d7f28c310c517978701f165606.png) no-repeat center;
                        background-size: cover;
                    }
                    ' .
                $addon_id .
                ' .line-box .point-box i.a4{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/fb2a88a02ef84b8bcc5537299ae870ac.png) no-repeat center;
                        background-size: cover;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-box i {
                        display: block;
                        width: 100px;
                        height:100px;
                        position: absolute;9
                        left: 0;
                        top: 0;
                        -webkit-animation: rotate 5s linear 0s infinite;
                        animation: rotate 5s linear 0s infinite;

                    }

                    ' .
                $addon_id .
                ' .line-box .point-sec {
                        left: -45px;
                        top: 30%;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-thi {
                        right: -38px;
                        top: 30%;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-fou {
                        right: 30px;
                        bottom: 5%;
                    }

                    ' .
                $addon_id .
                ' .line-box .point-tou {
                        left: 30px;
                        bottom: 5%;
                    }

                    ' .
                $addon_id .
                ' .more-box {
                        position: absolute;
                        width: 200px;
                        text-align: center;
                        left: 50%;
                        margin-left: -100px;
                        top: 50%;
                        margin-top: -60px;
                        z-index: 8;
                        opacity: 0;
                    }

                    ' .
                $addon_id .
                ' .more-box .ex-more {

                        display: block;
                        width: 200px;
                        height: 50px;
                        line-height: 50px;
                        text-align: center;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/7a065c4df51571d859a9d79d4ecb5441.png) no-repeat center;
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        text-transform: uppercase;
                        -webkit-transition: opacity 0.3s ease 0s;
                        transition: opacity 0.3s ease 0s;

                    }

                    ' .
                $addon_id .
                ' .mouse-ico {

                        display: block;
                        width: 60px;
                        height: 105px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/1d34b82f2b383ace26dedb4b026d9873.png) no-repeat center top;
                        position: absolute;
                        left: 50%;
                        margin-left: -15px;
                        top: 50%;
                        margin-top: 200px;
                        z-index: 5;
                        opacity: 0;

                    }

                    ' .
                $addon_id .
                ' .mouse-ico i {

                        display: block;
                        width: 100%;
                        height: 60px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/1a0fc97b907dca0f19de48991ee3d2a3.png) no-repeat center;
                        background-size: cover;
                        -webkit-animation: mousemove 3s linear 0s infinite;
                        animation: mousemove 3s linear 0s infinite;
                        position: relative;
                        top: 100px;

                    }

                    @media (max-width: 1200px){
                        ' .
                $addon_id .
                ' .banner .pwrap {
                            width: 90%;
                            margin-left: -45%;
                        }
                        ' .
                $addon_id .
                ' .bn-con h4{
                            line-height: 39px;
                        }

                        ' .
                $addon_id .
                ' .b-news{
                            bottom: 10px;
                        }

                        ' .
                $addon_id .
                ' .mouse-ico{
                            margin-top: 120px;
                        }
                        ' .
                $addon_id .
                ' .line-box{
                            top:40%!important;
                        }
                    }
                    @media (max-width: 1080px){
                        ' .
                $addon_id .
                ' .pwrap {
                            width: 90%;
                        }
                        ' .
                $addon_id .
                ' .mouse-ico{
                            display: none;
                        }

                        ' .
                $addon_id .
                ' .b-news{
                            display: none;
                        }
                    }
                    @media (max-width: 880px){

                        ' .
                $addon_id .
                ' .b-anim .outer-cir {
                            width: 600px!important;
                            height: 600px!important;
                            margin-left: -318px!important;
                            margin-top: -358px!important;
                        }
                        ' .
                $addon_id .
                ' .b-anim .outer-nc {
                            width: 400px!important;;
                            height: 400px!important;;
                            margin-left: -201px!important;
                            margin-top: -233px!important;
                        }
                        ' .
                $addon_id .
                ' .line-box .point-box,.line-box .point-box i{
                            width: 80px!important;
                            height: 80px!important;
                            font-size: 15px!important;
                        }
                        ' .
                $addon_id .
                ' .line-box .point-fir{
                            margin-left: -40px!important;
                        }
                        ' .
                $addon_id .
                ' .b-anim{

                            width: 100%!important;
                            height: 625px!important;
                            margin-left:0px!important;
                            left: 0!important;

                        }

                        ' .
                $addon_id .
                ' .line-box{

                            width: 260px!important;
                            height: 260px!important;
                            margin-left: -130px!important;
                            top: 23%!important;
                            margin-top: 0px!important;

                        }

                        ' .
                $addon_id .
                ' .line-box .line-fir{
                            width: 260px!important;
                            height: 260px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .line-sec{
                            width: 160px!important;
                            height: 160px!important;
                            margin-left: -80px!important;
                            margin-top: -80px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .point-thi{
                            right: -40px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .point-tou{
                            left: 26px!important;
                            bottom: -10%!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .point-fou{
                            right: 8px!important;
                            bottom: -10%!important;
                        }
                    }
                    @media (max-width: 768px){
                        ' .
                $addon_id .
                ' .banner .b-wrap img {
                            width: 120%!important;
                        }
                    }
                    @media (max-width: 636px){
                        ' .
                $addon_id .
                ' .banner .b-wrap img {
                            width: 135%!important;
                        }
                        ' .
                $addon_id .
                ' .banner .pwrap {
                            width: 100%!important;
                            margin-left: 0!important;
                            left: 0!important;
                        }
                    }

                    @media (max-width:500px){

                        ' .
                $addon_id .
                ' .b-anim{
                            background-image: none!important;
                        }

                        ' .
                $addon_id .
                ' .banner .b-wrap{
                            height: 500px!important;
                            background-size: cover!important;
                            background-repeat: no-repeat!important;
                            background-position: center!important;
                        }

                        ' .
                $addon_id .
                ' .banner .b-wrap img{
                            display: none!important;
                        }
                    }

                    @media (max-width: 414px){
                        ' .
                $addon_id .
                ' .line-box{
                            width: 220px!important;
                            height: 220px!important;
                            margin-left: -110px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .line-fir{
                            width: 220px!important;
                            height: 220px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .line-sec{
                            width: 100px!important;
                            height: 100px!important;
                            margin-left: -50px!important;
                            margin-top: -50px!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .point-tou{
                            left: 4px!important;
                            bottom: -17%!important;
                        }

                        ' .
                $addon_id .
                ' .line-box .point-fou{
                            right: -4px!important;
                            bottom: -17%!important;
                        }
                    }

                </style>
            ';
            $output .=
                '<script src="/components/com_jwpagefactory/addons/image/assets/js/jquery.transit.js"></script>';
            $output .=
                '
                <div class="banner dj">
                    <div class="b-wrap" style="background-image: url(' .
                $type3_img .
                ');">
                        <img class="bimg" src="' .
                $type3_img .
                '" >
                        <b class="b-mask"></b>
                        <div class="pwrap">
                            <!--浮动动画-->
                            <div class="b-anim">
                                <span class="outer-cir"></span>
                                <span class="outer-nc"></span>
                                <div class="line-box" style="opacity: 1;">
                                    <div class="line-fir">
                                        <span class="tit"></span>
                                    </div>
                                    <div class="line-sec">
                                        <span class="tit"></span>
                                    </div>';
            $lk1 = "javascript:;";
            if ($type3_title1_open == 1) {
                if ($tz1_fs == "nei") {
                    if ($detail_page_id1) {
                        $id1 = base64_encode($detail_page_id1);
                        $lk1 =
                            "component/jwpagefactory/?view=page&id=" .
                            $id1 .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $lk1 = $link1;
                }
            }
            $output .=
                '<a class="point-fir point-box" href="' .
                $lk1 .
                '" target="' .
                $target1 .
                '" style="opacity: 1;">
                                        <span>' .
                $type3_title1 .
                '</span>
                                        <i class="a1"></i>
                                    </a>';

            $lk2 = "javascript:;";
            if ($type3_title2_open == 1) {
                if ($tz2_fs == "nei") {
                    if ($detail_page_id2) {
                        $id2 = base64_encode($detail_page_id2);
                        $lk2 =
                            "component/jwpagefactory/?view=page&id=" .
                            $id2 .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $lk2 = $link2;
                }
            }
            $output .=
                '<a class="point-sec point-box" href="' .
                $lk2 .
                '" target="' .
                $target2 .
                '" style="opacity: 1;">
                                        <span>' .
                $type3_title2 .
                '</span>
                                        <i class="a2"></i>
                                    </a>';

            $lk3 = "javascript:;";
            if ($type3_title3_open == 1) {
                if ($tz3_fs == "nei") {
                    if ($detail_page_id3) {
                        $id3 = base64_encode($detail_page_id3);
                        $lk3 =
                            "component/jwpagefactory/?view=page&id=" .
                            $id3 .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $lk3 = $link3;
                }
            }
            $output .=
                '<a class="point-thi point-box" href="' .
                $lk3 .
                '" target="' .
                $target3 .
                '" style="opacity: 1;">
                                        <span>' .
                $type3_title3 .
                '</span>
                                        <i class="a3"></i>
                                    </a>';

            $lk4 = "javascript:;";
            if ($type3_title4_open == 1) {
                if ($tz4_fs == "nei") {
                    if ($detail_page_id4) {
                        $id4 = base64_encode($detail_page_id4);
                        $lk4 =
                            "component/jwpagefactory/?view=page&id=" .
                            $id4 .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $lk4 = $link4;
                }
            }
            $output .=
                '<a class="point-fou point-box" href="' .
                $lk4 .
                '" target="' .
                $target4 .
                '" style="opacity: 1;">
                                        <span>' .
                $type3_title4 .
                '</span>
                                        <i class="a4"></i>
                                    </a>';

            $lk5 = "javascript:;";
            if ($type3_title5_open == 1) {
                if ($tz5_fs == "nei") {
                    if ($detail_page_id5) {
                        $id5 = base64_encode($detail_page_id5);
                        $lk5 =
                            "component/jwpagefactory/?view=page&id=" .
                            $id5 .
                            "&company_id=" .
                            $company_id .
                            "&site_id=" .
                            $site_id .
                            "&layout_id=" .
                            $layout_id;
                    }
                } else {
                    $lk5 = $link5;
                }
            }
            $output .=
                '<a class="point-tou point-box" href="' .
                $lk5 .
                '" target="' .
                $target5 .
                '" style="opacity: 1;">
                                        <span>' .
                $type3_title5 .
                '</span>
                                        <i class="a5"></i>
                                    </a>
                                    <i class="a6"></i>
                                    <i class="a7"></i>
                                </div>
                                <span class="mouse-ico" style="opacity: 1;"> <i></i> </span>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    $(function(){

                        $("' .
                $addon_id .
                ' .b-wrap").mousemove(function(e){
                            var $ea= $(this).find(".bimg"),
                                X= e.clientX,
                                Y= e.clientY;
                            $ea.stop().transition({
                                x : -X / 40,
                                y : -Y / 40
                            }, 600)
                        });

                        window.onscroll=function(){
                        }

                        // contentWayPoint();
                        var w=$(window).width();
                        if(w<768){
                            $(window).unbind("scroll");
                        }
                    });
                    </script>
            ';
        } else {
            if ($fix_img_height != 0) {
                $output .= "
                <style>
                    {$addon_id} * {
                        margin: 0;
                        padding: 0;
                    }";
                $output .= "
                    {$addon_id} img {
                        display: inline-block;
                    }
                ";

                $output .= "
                    {$addon_id} .goods {
                        width: 50%;
                        text-align: center;
                    }
                ";
                $output .= "
                    {$addon_id} .goods img {
                        animation: myfirst 2s infinite;
                        animation-timing-function:ease;
                    }
                ";

                $output .= "
                    @keyframes myfirst { /*动画*/
                        0% {
                            transform: translate(0px, 0px);
                        }
                        50% {
                            transform: translate(0px, -10px);
                        }
                        100% {
                            transform: translate(0px, 0px);
                        }
                    }

                </style>
                ";
            }
            if ($image_fangda != 0) {
                $output .= "<style>
                {$addon_id} .jwpf-addon-single-image-container{
                    ";
                if ($image_open_bfb == 1) {
                    $output .= "width:100%;overflow:hidden;";
                }

                $output .= " }
                {$addon_id} .jwpf-addon-content:hover img{
                    transform: scale(1.2);
                    transition: all 0.5s;
                };
                </style>";
            }
            if ($isudown != 0) {
                $output .= "<style>

                        {$addon_id} .jwpf-addon-content img {
                            animation: myfirst 2s infinite;
                            animation-timing-function:ease;
                        }

                        @keyframes myfirst {
                            0% {
                                transform: translate(0px, 0px);
                            }
                            50% {
                                transform: translate(0px, -10px);
                            }
                            100% {
                                transform: translate(0px, 0px);
                            }
                        }

                </style>";
            }
            if ($image && $image != "/") {
                $output .=
                    '<div class="jwpf-addon jwpf-addon-single-image ' .
                    $position .
                    " " .
                    $class .
                    '">';
                $output .=
                    $title && $title_position != "bottom"
                        ? "<" .
                            $heading_selector .
                            ' class="jwpf-addon-title">' .
                            $title .
                            "</" .
                            $heading_selector .
                            ">"
                        : "";
                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-addon-single-image-container" ';
                if ($is_hover == 1) {
                    $output .= 'style="position:relative;"';
                }
                $output .= ">";
                if ($fix_img_height != 0) {
                    $output .=
                        '<div class="goods" style="width:' .
                        $wx_image_code2_w .
                        "px;height:" .
                        $wx_image_code2_h .
                        "px;position: relative;top: " .
                        $wx_image_code2_y .
                        "px;left: " .
                        $wx_image_code2_x .
                        'px ">';
                    $output .=
                        '    <img style=" width: 100%;height: 100%;" src="' .
                        $wx_image_code2 .
                        '" />';
                    $output .= "</div>";
                }

                if (empty($alt_text)) {
                    if (!empty($title)) {
                        $alt_text = $title;
                    } else {
                        $alt_text = basename($image);
                    }
                }

                if ($image_overlay && $open_lightbox) {
                    $output .= '<div class="jwpf-addon-image-overlay">';
                    $output .= "</div>";
                }

                if ($open_lightbox && $settings->isMask == 0) {
                    $output .=
                        '<a class="jwpf-magnific-popup jwpf-addon-image-overlay-icon" data-popup_type="image" data-mainclass="mfp-no-margins mfp-with-zoom" href="' .
                        $image .
                        '">+</a>';
                }

                if (
                    !$open_lightbox &&
                    $settings->isMask == 0 &&
                    $layout1_open_link != 1
                ) {
                    $output .= $link
                        ? "<a " . $target . ' href="' . $link . '">'
                        : "";
                }

                if ($is_hover == 1 && $erweim_rele == "top") {
                    $output .=
                        '<div class="ewe" style="padding:5px;width:' .
                        $erweim_width .
                        "px;background:url(" .
                        $erweim_bg .
                        ")no-repeat center center/100% 100%;position:absolute;left:" .
                        $erweim_left .
                        "px;top:-" .
                        $erweim_top .
                        'px;">
                            <img src="' .
                        $erweim .
                        '" style="width:100%;height:100%;margin-bottom:5px;">
                        </div>';
                }

                $output .=
                    '<img class="jwpf-img-responsive image222' .
                    ($placeholder ? " jwpf-element-lazy" : "") .
                    '" src="' .
                    ($placeholder ? $placeholder : $image) .
                    '" ' .
                    ($placeholder ? 'data-large="' . $image . '"' : "") .
                    ' alt="' .
                    $alt_text .
                    '" title="' .
                    $title .
                    '" ' .
                    ($dimension ? $dimension : "") .
                    ' loading="lazy">';

                if ($is_hover == 1 && $erweim_rele == "bot") {
                    $output .=
                        '<div class="ewe" style="padding:5px;width:' .
                        $erweim_width .
                        "px;background:url(" .
                        $erweim_bg .
                        ")no-repeat center center/100% 100%;position:absolute;left:" .
                        $erweim_left .
                        "px;top:" .
                        $erweim_top .
                        'px;">
                            <img src="' .
                        $erweim .
                        '" style="width:100%;height:100%;margin-top:5px;">
                        </div>';
                }

                if ($isMask == 1) {
                    if ($zz_animt == "center") {
                        $output .=
                            '
                            <div class="ind3-b3"></div>
                            <div class="ind3-b4">
                                <div>
                                    <div class="ind3-b5">' .
                            $mask_title .
                            '</div>
                                    <div class="ind3-b6">' .
                            $mask_intro .
                            '</div>
                                </div>
                            </div>
                            <a target=" ' .
                            $target .
                            ' " href=" ' .
                            $link .
                            ' "></a>
                        ';
                    } else {
                        $output .=
                            "<h3>" .
                            $mask_title .
                            '</h3>
                        <div class="pic_con">
                            <div class="pic_txt">' .
                            $mask_intro .
                            '</div>
                            <a target=" ' .
                            $target .
                            ' " href=" ' .
                            $link .
                            ' " class="pic_more">
                                ' .
                            $button_title .
                            '
                            </a>
                        </div>';
                    }
                }

                if (!$open_lightbox && $settings->isMask == 0) {
                    $output .= $link ? "</a>" : "";
                }

                $output .= "</div>";
                $output .=
                    $title && $title_position == "bottom"
                        ? "<" .
                            $heading_selector .
                            ' class="jwpf-addon-title">' .
                            $title .
                            "</" .
                            $heading_selector .
                            ">"
                        : "";
                $output .= "</div>";
                $output .= "</div>";

                if ($is_hover == 1) {
                    $output .=
                        '
                        <script>
                            $("' .
                        $addon_id .
                        ' .jwpf-addon-single-image-container").hover(function(){
                                $(this).find(".ewe").show();
                            },function(){
                                $(this).find(".ewe").hide();
                            })
                        </script>
                    ';
                }
            }
        }

        // 点击按钮出弹窗
        $dialog_addon_id =
            isset($settings->dialog_addon_id) && $settings->dialog_addon_id
                ? $settings->dialog_addon_id
                : "";

        if ($dialog_addon_id && $layout1_open_link) {
            $output .=
                '<script>
                $("' .
                $addon_id .
                ' .jwpf-addon-single-image-container").on("click", function(){
                    $("#dialog-' .
                $dialog_addon_id .
                '").show();
                })
            </script>';
        }
        // 点击按钮出弹窗结束

        // 点击弹出商桥
        $business_bridge_show = isset($settings->business_bridge_show)
            ? $settings->business_bridge_show
            : 0;
        if ($business_bridge_show && $layout1_open_link) {
            $output .=
                '<script>
                (function(){
                    $("' . $addon_id . ' .jwpf-addon-single-image-container").on("click", function(){
                        $("#aff-im-root .embed-messageboard-header-close.embed-messageboard-header-max").trigger("click");
                        $("#aff-im-root .embed-icon-default").trigger("click");
                        $("#aff-im-root .embed-icon-content").trigger("click");
                        setTimeout(function(){
                            $("#aff-im-root .embed-icon-content").trigger("click");
                            $("#aff-im-root .embed-icon-default").trigger("click");
                        }, 1000);
                        $("#k_s_ol_inviteWin").show();
                    });
                })()
            </script>';
        }
        // 点击弹出商桥结束

        return $output;
    }

    public function scripts()
    {
        return [
            "/components/com_jwpagefactory/assets/js/jquery.magnific-popup.min.js",
        ];
    }

    public function stylesheets()
    {
        return ["/components/com_jwpagefactory/assets/css/magnific-popup.css"];
    }

    public function css()
    {
        $addon_id = "#jwpf-addon-" . $this->addon->id;
        $settings = $this->addon->settings;

        $image_layout =
            isset($settings->image_layout) && $settings->image_layout
                ? $settings->image_layout
                : "layout1";

        $zzfont_family =
            isset($settings->zzfont_family) && $settings->zzfont_family
                ? $settings->zzfont_family
                : "";
        $zz_animt =
            isset($settings->zz_animt) && $settings->zz_animt
                ? $settings->zz_animt
                : "bot";
        $zz_padding = isset($settings->zz_padding)
            ? $settings->zz_padding
            : "35% 1% 10% 1%";

        $open_lightbox =
            isset($settings->open_lightbox) && $settings->open_lightbox
                ? $settings->open_lightbox
                : 0;
        $style =
            isset($settings->overlay_color) && $settings->overlay_color
                ? "background-color: " . $settings->overlay_color . ";"
                : "";
        $title_padding =
            isset($settings->title_padding) && trim($settings->title_padding)
                ? $settings->title_padding
                : "";
        $title_padding_sm =
            isset($settings->title_padding_sm) &&
            trim($settings->title_padding_sm)
                ? $settings->title_padding_sm
                : "";
        $title_padding_xs =
            isset($settings->title_padding_xs) &&
            trim($settings->title_padding_xs)
                ? $settings->title_padding_xs
                : "";

        // 开启百分比
        $image_open_bfb =
            isset($settings->image_open_bfb) && $settings->image_open_bfb
                ? $settings->image_open_bfb
                : 0;
        $image_width_bfb =
            isset($settings->image_width_bfb) && $settings->image_width_bfb
                ? $settings->image_width_bfb
                : 100;

        $style_img = "";
        $style_img_sm = "";
        $style_img_xs = "";
        $style_img =
            isset($settings->border_radius) && $settings->border_radius
                ? "border-radius: " . $settings->border_radius . "px;"
                : "";
        if ($image_open_bfb == 1) {
            $style_img .=
                isset($settings->image_width_bfb) && $settings->image_width_bfb
                    ? "width:" . $settings->image_width_bfb . "%;"
                    : "";
        } else {
            $style_img .=
                isset($settings->image_width) && $settings->image_width
                    ? "width:" . $settings->image_width . "px;"
                    : "";
            $style_img .=
                isset($settings->image_width) && $settings->image_width
                    ? "max-width:" . $settings->image_width . "px;"
                    : "";
        }
        $style_img .=
            isset($settings->image_height) && $settings->image_height
                ? "height:" . $settings->image_height . "px;"
                : "";

        $style_img_sm .=
            isset($settings->image_width_sm) && $settings->image_width_sm
                ? "max-width:" . $settings->image_width_sm . "px;"
                : "max-width:100%;";
        $style_img_sm .=
            isset($settings->image_height_sm) && $settings->image_height_sm
                ? "height:" . $settings->image_height_sm . "px;"
                : "";
        $style_img_sm .=
            isset($settings->image_width_sm) && $settings->image_width_sm
                ? "width:" . $settings->image_width_sm . "px;"
                : "width:100%;";

        $style_img_xs .=
            isset($settings->image_width_xs) && $settings->image_width_xs
                ? "max-width:" . $settings->image_width_xs . "px;"
                : "max-width:100%;";
        $style_img_xs .=
            isset($settings->image_height_xs) && $settings->image_height_xs
                ? "height:" . $settings->image_height_xs . "px;"
                : "height:auto;";
        $style_img_xs .=
            isset($settings->image_width_xs) && $settings->image_width_xs
                ? "width:" . $settings->image_width_xs . "px;"
                : "width:100%;";

        $image_width =
            isset($settings->image_width) && $settings->image_width
                ? $settings->image_width
                : 200;
        $image_width_sm =
            isset($settings->image_width_sm) && $settings->image_width_sm
                ? $settings->image_width_sm
                : "100%";
        $image_width_xs =
            isset($settings->image_width_xs) && $settings->image_width_xs
                ? $settings->image_width_xs
                : "100%";
        $image_height =
            isset($settings->image_height) && $settings->image_height
                ? $settings->image_height
                : 200;
        $image_height_sm =
            isset($settings->image_height_sm) && $settings->image_height_sm
                ? $settings->image_height_sm
                : 200;
        $image_height_xs =
            isset($settings->image_height_xs) && $settings->image_height_xs
                ? $settings->image_height_xs
                : 200;
        $isFixed =
            isset($settings->isFixed) && $settings->isFixed
                ? $settings->isFixed
                : 0;
        $fixed_style =
            isset($settings->fixed_style) && $settings->fixed_style
                ? $settings->fixed_style
                : "left";
        $fixed_top =
            isset($settings->fixed_top) && $settings->fixed_top
                ? $settings->fixed_top
                : 40;
        $fixed_right =
            isset($settings->fixed_right) && $settings->fixed_right
                ? $settings->fixed_right
                : 0;
        $fixed_bottom =
            isset($settings->fixed_bottom) && $settings->fixed_bottom
                ? $settings->fixed_bottom
                : 0;
        $fixed_left =
            isset($settings->fixed_left) && $settings->fixed_left
                ? $settings->fixed_left
                : 0;

        //开启遮罩
        $isMask =
            isset($settings->isMask) && $settings->isMask
                ? $settings->isMask
                : 0;
        $mask_title =
            isset($settings->mask_title) && $settings->mask_title
                ? $settings->mask_title
                : "图片标题";
        $mask_title_fontsize =
            isset($settings->mask_title_fontsize) &&
            $settings->mask_title_fontsize
                ? $settings->mask_title_fontsize
                : "20";
        $mask_title_font_color =
            isset($settings->mask_title_font_color) &&
            $settings->mask_title_font_color
                ? $settings->mask_title_font_color
                : "#fff";
        $mask_intro =
            isset($settings->mask_intro) && $settings->mask_intro
                ? $settings->mask_intro
                : "图片简介";
        $mask_intro_fontsize =
            isset($settings->mask_intro_fontsize) &&
            $settings->mask_intro_fontsize
                ? $settings->mask_intro_fontsize
                : "14";
        $mask_intro_font_color =
            isset($settings->mask_intro_font_color) &&
            $settings->mask_intro_font_color
                ? $settings->mask_intro_font_color
                : "#fff";
        $mask_bg_color =
            isset($settings->mask_bg_color) && $settings->mask_bg_color
                ? $settings->mask_bg_color
                : "rgba(15, 63, 119, 0.7)";
        $button_title =
            isset($settings->button_title) && $settings->button_title
                ? $settings->button_title
                : "了解更多";
        $button_bg_color =
            isset($settings->button_bg_color) && $settings->button_bg_color
                ? $settings->button_bg_color
                : "#89c400";
        $mask_title_lineheight =
            isset($settings->mask_title_lineheight) &&
            $settings->mask_title_lineheight
                ? $settings->mask_title_lineheight
                : "65";
        $mask_button_color =
            isset($settings->mask_button_color) && $settings->mask_button_color
                ? $settings->mask_button_color
                : "#fff";

        $zz_buju =
            isset($settings->zz_buju) && $settings->zz_buju
                ? $settings->zz_buju
                : "quan";
        $zz_width =
            isset($settings->zz_width) && $settings->zz_width
                ? $settings->zz_width
                : "100";
        $mask_title_borderradius =
            isset($settings->mask_title_borderradius) &&
            $settings->mask_title_borderradius
                ? $settings->mask_title_borderradius
                : "0px 0px 0px 0px";
        $mask_title_font_hvcolor =
            isset($settings->mask_title_font_hvcolor) &&
            $settings->mask_title_font_hvcolor
                ? $settings->mask_title_font_hvcolor
                : "#ffffff";
        $mask_bg_hvcolor =
            isset($settings->mask_bg_hvcolor) && $settings->mask_bg_hvcolor
                ? $settings->mask_bg_hvcolor
                : "rgba(255, 255, 255, .6)";

        $css = "";
        if ($image_layout == "layout1") {
            if ($isFixed == 1) {
                $css .=
                    $addon_id .
                    ' .jwpf-addon {
                    position: fixed;
                    z-index: 9999;
                    width: ' .
                    $image_width .
                    'px;
                    height: ' .
                    $image_height .
                    'px;
                    ';
                switch ($fixed_style) {
                    case "left":
                        $css .= '
                            left: 0;
                            top: 120px;
                        ';
                        break;
                    case "right":
                        $css .= '
                            right: 0;
                            top: 120px;
                        ';
                        break;
                    case "right_bottom":
                        $css .= '
                            right: 0;
                            bottom: 0;
                        ';
                        break;
                    case "left-center":
                        $css .= '
                            left: 0;
                            top: 0;
                            bottom: 0;
                             margin: auto;
                        ';
                        break;
                    case "right-center":
                        $css .= '
                            right: 0;
                            top: 0;
                            bottom: 0;
                            margin: auto;
                        ';
                        break;
                    case "custom":
                        $css .=
                            '
                            top: ' .
                            $fixed_top .
                            'px;
                            right: ' .
                            $fixed_right .
                            'px;
                            bottom: ' .
                            $fixed_bottom .
                            'px;
                            left: ' .
                            $fixed_left .
                            'px;
                        ';
                        break;
                }
                $css .= "}";
            }
            if ($open_lightbox && $style) {
                $css .= $addon_id . " .jwpf-addon-image-overlay{";
                $css .= $style;
                $css .= $style_img;
                $css .= "}";
            }

            if ($settings->image_fit) {
                $style_img .= "object-fit: " . $settings->image_fit . ";";
            }

            $css .= $addon_id . " img{transition:all 0.3s;" . $style_img . "}";
            if ($title_padding) {
                $css .=
                    $addon_id .
                    " .jwpf-addon-title{padding: " .
                    $title_padding .
                    "}";
            }

            if ($image_open_bfb == 1) {
                $css .= $addon_id . " .jwpf-addon-single-image-container{";
                $css .= "width:100%;";
                $css .= "}";
            }

            $css .= $addon_id . " .ewe{display:none;}";
            $css .= $addon_id . " .ewe img{max-widith:inherit;}";

            if ($isMask == 1) {
                $css .=
                    $addon_id .
                    ' .jwpf-addon-single-image-container{
                        overflow:hidden;';
                if ($image_open_bfb == 1) {
                    $css .= "width:100%;";
                }
                $css .= "}";
                $css .=
                    $addon_id .
                    " .jwpf-addon-single-image-container img {transition:all 03s;";
                if ($image_height) {
                    $css .= " height: " . $image_height . "px;";
                } else {
                    $css .= " height: auto!important;";
                }
                if ($image_open_bfb == 1) {
                    $css .= " width: " . $image_width_bfb . "%;";
                } else {
                    if ($image_width) {
                        $css .= " width: " . $image_width . "px;";
                        $css .= "max-width: " . $image_width . "px;";
                    }
                }

                $css .= "}";

                $css .=
                    $addon_id .
                    ' .jwpf-addon-single-image-container h3 {
                  width: ' .
                    $zz_width .
                    '%;
                  bottom: 0;
                  margin-bottom:0px;
                  left: 0;
                  font-family:' .
                    $zzfont_family .
                    ';
                  font-size: ' .
                    $mask_title_fontsize .
                    'px;
                  color:' .
                    $mask_title_font_color .
                    ';
                  background: ' .
                    $mask_bg_color .
                    ';
                  position: absolute;
                  line-height: ' .
                    $mask_title_lineheight .
                    'px;
                  padding: 0 20px;';
                if ($zz_buju == "biao") {
                    $css .=
                        "z-index:0;border-radius:" . $mask_title_borderradius;
                }
                $css .= "}";
                $css .=
                    $addon_id .
                    ' .jwpf-addon-single-image-container .pic_con {
                  width: 100%;
                  height: 0;
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  background: ' .
                    $mask_bg_color .
                    ';
                  transition: all 0.3s;
                }';
                $css .=
                    $addon_id .
                    ' .jwpf-addon-single-image-container .pic_con .pic_txt {
                    line-height: 30px;
                    padding: ' .
                    $zz_padding .
                    ';
                    font-size: ' .
                    $mask_intro_fontsize .
                    'px;
                    color:' .
                    $mask_intro_font_color .
                    ";";
                if ($image_height) {
                    $css .= " max-height: " . $image_height . "px;";
                }
                $css .= "}";

                if ($zz_animt == "center") {
                    $css .=
                        $addon_id .
                        ' .ind3-b3 {
                        width: 100%;
                        height: 100%;
                        background: ' .
                        $mask_bg_color .
                        ';
                        position: absolute;
                        top: 0;
                        left: 0;
                        transform: scale(0);
                        transition: 0.5s;
                    }
                    ' .
                        $addon_id .
                        ' .jwpf-addon-single-image-container:hover .ind3-b3 {
                        transform: scale(1);
                        transition: 0.5s;
                    }
                    ' .
                        $addon_id .
                        ' .ind3-b4 {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        display: table;
                    }
                    ' .
                        $addon_id .
                        ' .ind3-b4>div {
                        width: 100%;
                        display: table-cell;
                        vertical-align: middle;
                    }
                    ' .
                        $addon_id .
                        ' .ind3-b5 {
                        font-size: ' .
                        $mask_title_fontsize .
                        'px;
                        line-height: ' .
                        $mask_title_lineheight .
                        'px;
                        color: ' .
                        $mask_title_font_color .
                        ';
                        text-align: center;
                        font-family: ' .
                        $zzfont_family .
                        ';

                    }
                    ' .
                        $addon_id .
                        ' .ind3-b6 {
                        font-size: ' .
                        $mask_intro_fontsize .
                        'px;
                        line-height: 36px;
                        color: ' .
                        $mask_intro_font_color .
                        ';
                        text-align: center;
                        font-weight: bold;
                    }
                    ' .
                        $addon_id .
                        ' .jwpf-addon-single-image-container>a {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        display: block;
                    }

                    ';
                }

                $css .=
                    $addon_id .
                    ' .jwpf-addon-single-image-container .pic_con .pic_more {
                      padding:10px 20px;
                      background: ' .
                    $button_bg_color .
                    ';
                      margin: 0 auto;
                      text-align:center;
                      font-size: ' .
                    $mask_intro_fontsize .
                    'px;
                      color:' .
                    $mask_button_color .
                    ';
                }

                ';

                if ($zz_buju == "biao") {
                    $css .=
                        $addon_id .
                        ' .jwpf-addon-single-image-container:hover h3 {
                        color:' .
                        $mask_title_font_hvcolor .
                        ';
                    }';

                    $css .=
                        $addon_id .
                        ' .jwpf-addon-single-image-container h3::after {
                        width: 0;
                        height: 100%;
                        background:' .
                        $mask_bg_hvcolor .
                        ';
                        position: absolute;
                        left: 0;
                        top: 0;
                        display: block;
                        content: "";
                        transition: all .6s;
                        z-index: -1;';
                    if ($zz_buju == "biao") {
                        $css .= "border-radius:" . $mask_title_borderradius;
                    }

                    $css .= "}";
                    $css .=
                        $addon_id .
                        ' .jwpf-addon-single-image-container:hover h3::after {
                        width: 100%;
                    }';
                } else {
                    $css .=
                        $addon_id .
                        ' .jwpf-addon-single-image-container:hover h3 {
                          display: none;
                    }';
                    $css .=
                        $addon_id .
                        ' .jwpf-addon-single-image-container:hover .pic_con {
                          height: 100%;
                    }';
                }
            }

            $css .= "@media (min-width: 768px) and (max-width: 991px) {";
            if ($isMask == 1) {
                $css .=
                    $addon_id .
                    " .jwpf-addon-single-image-container img {transition:all 03s;";
                if ($image_height) {
                    $css .= " height: " . $image_height_sm . "px;";
                }
                if ($image_open_bfb == 1) {
                    $css .= " width: " . $image_width_bfb . "%;";
                } else {
                    if ($image_width) {
                        $css .= " width: " . $image_width . "px;";
                        $css .= "max-width: " . $image_width . "px;";
                    }
                }

                $css .= "}";
            }
            if ($isFixed == 1) {
                $css .=
                    $addon_id .
                    ' .jwpf-addon {
                    width: ' .
                    $image_width_sm .
                    ';
                    height: ' .
                    $image_height_sm .
                    'px;
                }';
            }
            if ($title_padding_sm) {
                $css .=
                    $addon_id .
                    " .jwpf-addon-title{padding: " .
                    $title_padding_sm .
                    "}";
            }
            $css .= $addon_id . " img{" . $style_img_sm . "}";
            $css .= "}";
            $css .= "@media (max-width: 767px) {";

            if ($isMask == 1) {
                $css .=
                    $addon_id .
                    " .jwpf-addon-single-image-container img {transition:all 03s;";
                if ($image_height) {
                    $css .= " height: " . $image_height_xs . "px;";
                } else {
                    $css .= " height: auto;!important";
                }
                if ($image_open_bfb == 1) {
                    $css .= " width: " . $image_width_bfb . "%;";
                } else {
                    if ($image_width) {
                        $css .= " width: " . $image_width . "px;";
                        $css .= "max-width: " . $image_width . "px;";
                    }
                }

                $css .= "}";
            }
            if ($isFixed == 1) {
                $css .=
                    $addon_id .
                    ' .jwpf-addon {
                    width: ' .
                    $image_width_xs .
                    ';
                    height: ' .
                    $image_height_xs .
                    'px;
                }';
            }
            if ($title_padding_xs) {
                $css .=
                    $addon_id .
                    " .jwpf-addon-title{padding: " .
                    $title_padding_xs .
                    "}";
            }
            $css .= $addon_id . " img{" . $style_img_xs . "}";
            $css .= "}";

            // 点击按钮出弹窗
            $dialog_addon_id =
                isset($settings->dialog_addon_id) && $settings->dialog_addon_id
                    ? $settings->dialog_addon_id
                    : "";
            $layout1_open_link = isset($settings->layout1_open_link)
                ? $settings->layout1_open_link
                : 0;
            if ($dialog_addon_id && $layout1_open_link) {
                $css .=
                    $addon_id .
                    ' img{
                    cursor: pointer;
                }';
            }
            // 点击按钮出弹窗结束
        }

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <#
            var aboutcase = data.aboutcase || "0";
            var adv_id = data.adv_id || "";

            var alt_text = data.alt_text;

            var image_overlay = 0;
            if(!_.isEmpty(data.overlay_color)){
                image_overlay = 1;
            }
            var open_lightbox = parseInt(data.open_lightbox);
            var title_font_style = data.title_fontstyle || "";

            var image_layout = data.image_layout || "layout1";


            if(_.isEmpty(alt_text)){
                if(!_.isEmpty(data.title)){
                    alt_text = data.title;
                }
            }
        #>
        <#
        if(image_layout=="layout2"){

            var open_intro = data.open_intro || "0";
            var intro_text = data.intro_text || "";
            var fontbg_family = data.fontbg_family || "";

            #>

            <style>
                #jwpf-addon-{{ data.id }} .i100 {
                  overflow: hidden;
                }
                #jwpf-addon-{{ data.id }} .i200 > img {
                  height: 100%;
                }
                #jwpf-addon-{{ data.id }} .i200 {
                  overflow: hidden;
                }
                #jwpf-addon-{{ data.id }} .a1 > a {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 0;
                  left: 0;
                  display: block;
                }
                @media only screen and (min-width: 1480px) {
                    #jwpf-addon-{{ data.id }} .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-corner{background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111{width: 1920px!important;height: 100%!important;margin-left: calc(50% - 1920px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-wrapper{display: block;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 1920px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a2{width: 100%;height: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img{min-height: 100%;transform: scale(1.2);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(1){display: block;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(2){display: none;}
                    #jwpf-addon-{{ data.id }} .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3{position: absolute;top: calc(50% - 80px);left: calc(50% - 1440px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a4{font-size: 52px;line-height: 52px;color: #fff;font-weight: bold;margin-bottom: 20px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a5{height: 68px;margin-bottom: 45px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}

                    #jwpf-addon-{{ data.id }} .bt2-a1{width: 171px;position: relative;}
                    #jwpf-addon-{{ data.id }} .bt2-a2{width: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(1){position: relative;opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1:hover .bt2-a2 img:nth-child(1){opacity: 0;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1:hover .bt2-a2 img:nth-child(2){opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a3{width: 125px;position: absolute;top: calc(50% - 24px/2 + 4px);left: calc(50% - 125px/2);}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(1){width: 24px;height: 1px;background: #fff;position: relative;float: left;top: 6px;margin-right: 12px}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(2){font-size: 14px;line-height: 14px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                }
                @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                    #jwpf-addon-{{ data.id }} .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-corner{background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111{width: 1920px!important;height: 100%!important;margin-left: calc(50% - 1920px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-wrapper{display: block;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 1920px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a2{width: 100%;height: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img{min-height: 100%;transform: scale(1.2);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(1){display: block;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(2){display: none;}
                    #jwpf-addon-{{ data.id }} .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3{position: absolute;top: calc(50% - 80px);left: calc(50% - 960px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a4{font-size: 40px;line-height: 40px;color: #fff;font-weight: bold;margin-bottom: 16px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a5{height: 56px;margin-bottom: 30px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1{width: 171px;position: relative;}
                    #jwpf-addon-{{ data.id }} .bt2-a2{width: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(1){position: relative;opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1:hover .bt2-a2 img:nth-child(1){opacity: 0;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1:hover .bt2-a2 img:nth-child(2){opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a3{width: 125px;position: absolute;top: calc(50% - 24px/2 + 4px);left: calc(50% - 125px/2);}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(1){width: 24px;height: 1px;background: #fff;position: relative;float: left;top: 6px;margin-right: 12px}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(2){font-size: 14px;line-height: 14px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                }
                @media only screen and (max-width: 1023px) {
                    #jwpf-addon-{{ data.id }} .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-corner{background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111{width: 100%!important;height: 100%!important;z-index: 1;overflow: hidden;overflow-y: auto;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-wrapper{display: block;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: 0;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2{width: 100%;height: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img{height: 100%;width: auto;/*margin-left: -100%;*/transform: scale(1.2);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(1){display: none;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(2){display: block;}
                    #jwpf-addon-{{ data.id }} .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3{position: absolute;top: calc(50% - 104px);left: 18px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a4{font-size: 27px;line-height: 27px;color: #fff;font-weight: bold;margin-bottom: 10px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a5{height: 35px;margin-bottom: 23px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a1{display: block;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream112{width: 10px;position: absolute;top: calc(50% - 10px*7/2 - 20px*3);right: 20px;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet{width: 10px;height: 10px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 20px;position: relative;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{width: 25px;height: 28px;position: absolute;top: calc(50% - 28px/2);left: calc(50% - 25px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .bt2-a1{width: 124px;position: relative;display: none;}
                    #jwpf-addon-{{ data.id }} .bt2-a2{width: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(1){position: relative;opacity: 1;}
                    #jwpf-addon-{{ data.id }} .bt2-a2 img:nth-child(2){position: absolute;top: 0;left: 0;opacity: 0;}
                    #jwpf-addon-{{ data.id }} .bt2-a3{width: 104px;position: absolute;top: calc(50% - 12px/2 + 2px);left: calc(50% - 104px/2 + 5px);}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(1){width: 12px;height: 1px;background: #fff;position: relative;float: left;top: 3px;margin-right:6px;}
                    #jwpf-addon-{{ data.id }} .bt2-a3>div:nth-child(2){font-size: 11px;line-height: 11px;color: #fff;font-family: Arial;text-transform: uppercase;font-style: italic;float: left;}
                }
                @media only screen and (min-width: 1921px) {
                    #jwpf-addon-{{ data.id }} .icecream1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 1;overflow: hidden;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar{width : 0;height: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-thumb{background: none;border-radius: 0;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-corner{background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111::-webkit-scrollbar-track{border-radius: 0;background: none;}
                    #jwpf-addon-{{ data.id }} .icecream111{width: 2560px!important;height: 100%!important;margin-left: calc(50% - 2560px/2);z-index: 1;overflow: hidden;overflow-y: auto;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-wrapper{display: block;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide{width: 100%!important;/*height: 100%!important;*/position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream111 .swiper-slide.on1{position: fixed;top: 0;left: calc(50% - 2560px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a2{width: 100%;height: 100%;position: relative;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img{min-height: 100%;transform: scale(1.2);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(1){display: block;}
                    #jwpf-addon-{{ data.id }} .icecream1-a2 img:nth-child(2){display: none;}
                    #jwpf-addon-{{ data.id }} .swiper-slide-active .icecream1-a2 img{transform: scale(1);transition: 10s;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3{position: absolute;top: calc(50% - 80px);left: calc(50% - 1440px/2);}
                    #jwpf-addon-{{ data.id }} .icecream1-a4{font-size: 52px;line-height: 52px;color: #fff;font-weight: bold;margin-bottom: 20px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a5{height: 68px;margin-bottom: 45px;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(1){background: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream1-a3 .bt2-a3>div:nth-child(2){color: #b3a36b;}
                    #jwpf-addon-{{ data.id }} .icecream112{width: 8px;position: absolute;top: calc(50% - 8px*7/2 - 25px*3);right: 50px;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet{width: 8px;height: 8px;background: #fff;border-radius: 50%;opacity: 1;display: block;margin-bottom: 25px;position: relative;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active{background: none;transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{width: 24px;height: 27px;position: absolute;top: calc(50% - 27px/2);left: calc(50% - 24px/2);opacity: 0;transform: scale(0);transition: 0.5s;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet div{background-size: 100% 100%;}
                    #jwpf-addon-{{ data.id }} .icecream112 .swiper-pagination-bullet-active div{transform: scale(1);opacity: 1;transition: 0.5s;}
                }

                #jwpf-addon-{{ data.id }} .icecream1-a2.i100 img{
                    animation-name: small;
                    animation-duration: 10s;
                }

                @keyframes small {
                    0%{
                        transform: scale(1.2);
                    }
                    100%{
                        transform: scale(1);
                    }
                }


                #jwpf-addon-{{ data.id }} .fadeInUp {
                  -webkit-animation-name: fadeInUp;
                  animation-name: fadeInUp;
                }
                #jwpf-addon-{{ data.id }} .animated {
                  -webkit-animation-duration: 1s;
                  animation-duration: 1s;
                  -webkit-animation-fill-mode: both;
                  animation-fill-mode: both;
                }
                @-webkit-keyframes fadeInUp {
                  0% {
                    opacity: 0;
                    -webkit-transform: translate3d(0, 100%, 0);
                            transform: translate3d(0, 100%, 0);
                  }

                  100% {
                    opacity: 1;
                    -webkit-transform: none;
                            transform: none;
                  }
                }

                @keyframes fadeInUp {
                  0% {
                    opacity: 0;
                    -webkit-transform: translate3d(0, 100%, 0);
                            transform: translate3d(0, 100%, 0);
                  }

                  100% {
                    opacity: 1;
                    -webkit-transform: none;
                            transform: none;
                  }
                }
            </style>

            <div class="swiper-slide swiper-slide-active" style="height: 100%;">
                <div class="icecream1-a2 i100">
                    <img src=\'{{data.pc_img}}\'>
                    <img src=\'{{data.phone_img}}\'>
                </div>
                <div class="icecream1-a3 ani fadeInUp animated" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.3s" style="visibility: visible; animation-duration: 1s; animation-delay: 0.3s;animation-duration:1s;-webkit-animation-duration:1s;animation-delay:0.3s;-webkit-animation-delay:0.3s;" swiper-animate-style-cache="visibility: visible;animation-duration:1s;-webkit-animation-duration:1s;animation-delay:0.3s;-webkit-animation-delay:0.3s;">
                <# if(data.open_text==1){ #>
                    <div class="icecream1-a4">{{data.content}}</div>
                <# } #>
                <# if(data.open_img==1){ #>
                    <div class="icecream1-a5 i200"><img src=\'{{data.bg_img}}\'></div>
                <# } #>
                <# if(data.open_button==1){ #>
                    <div class="bt2-a1 a1">
                        <div class="bt2-a2 i100">
                            <img src=\'{{data.button_img}}\'>
                            <img src=\'{{data.button_imghv}}\'>
                        </div>
                        <div class="bt2-a3"><div></div><div>{{data.button_text}}</div></div>
                        <a href="javascript:;"></a>
                    </div>
                <# } #>
                <# if(data.open_intro==1){ #>
                    <div style="font-family:{{data.fontbg_family}};" >{{{data.intro_text}}}</div>
                <# } #>

                </div>
            </div>

        <# }else if(image_layout=="layout3"){
            var type3_img = (!_.isEmpty(data.type3_img) && data.type3_img) ? data.type3_img : "https://oss.lcweb01.cn/joomla/20230105/288455b29b142e19a02b268e2cf25360.jpg";
            var type3_title1 = (!_.isEmpty(data.type3_title1) && data.type3_title1) ? data.type3_title1 : "护助时代";
            var type3_title2 = (!_.isEmpty(data.type3_title2) && data.type3_title2) ? data.type3_title2 : "参访交流";
            var type3_title3 = (!_.isEmpty(data.type3_title3) && data.type3_title3) ? data.type3_title3 : "介护教育";
            var type3_title4 = (!_.isEmpty(data.type3_title4) && data.type3_title4) ? data.type3_title4 : "日语培训";
            var type3_title5 = (!_.isEmpty(data.type3_title5) && data.type3_title5) ? data.type3_title5 : "留学合作";


        #>
            <style>
                    #jwpf-addon-{{ data.id }} .banner{
                        position: relative;
                    }

                    #jwpf-addon-{{ data.id }} .banner .b-wrap{
                        width: 100%;
                        position: relative;
                        overflow: hidden;
                        max-height: 1000px;
                        background-image:url({{type3_img}});
                    }

                    #jwpf-addon-{{ data.id }} .banner .b-wrap img{
                        width: 108%;
                        -webkit-transition: -webkit-transform 0.5s;
                        -moz-transition: -moz-transform 0.5s;
                        -ms-transition: -ms-transform 0.5s;
                        -o-transition: -o-transform 0.5s;
                        transition: transform 0.5s;
                    }

                    #jwpf-addon-{{ data.id }} .banner .b-wrap b{
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0,0,0,0.6);
                        top: 0;
                        left: 0;
                    }

                    #jwpf-addon-{{ data.id }} .banner .pwrap{
                        position: absolute;
                        width: 80%;
                        height: 100%;
                        top: 0;
                        left: 50%;
                        margin-left: -40%;
                    }


                    @keyframes mousemove{

                        0% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                        25% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        50% {
                            -webkit-transform: translateY(5px);
                            transform: translateY(5px);
                        }

                        75% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        100% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                    }
                    @-webkit-keyframes mousemove{

                        0% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                        25% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        50% {
                            -webkit-transform: translateY(5px);
                            transform: translateY(5px);
                        }


                        75% {
                            -webkit-transform: translateY(0px);
                            transform: translateY(0px);
                        }

                        100% {
                            -webkit-transform: translateY(-5px);
                            transform: translateY(-5px);
                        }

                    }


                    @keyframes rotate{

                        0% {
                            -webkit-transform: rotate(0);
                            transform: rotate(0);
                        }

                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                    }
                    @-webkit-keyframes rotate{

                        0% {
                            -webkit-transform: rotate(0);
                            transform: rotate(0);
                        }

                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                    }

                    @keyframes dot{

                        25% {
                            border-color: transparent;
                            background-color: transparent;
                        }

                        50% {
                            border-right-color: transparent;
                            background-color: transparent;
                        }

                        75% {
                            border-right-color: transparent;
                        }

                    }
                    @-webkit-keyframes dot{

                        25% {
                            border-color: transparent;
                            background-color: transparent;
                        }

                        50% {
                            border-right-color: transparent;
                            background-color: transparent;
                        }

                        75% {
                            border-right-color: transparent;
                        }

                    }


                    @keyframes rotates{

                        0% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                        50% {
                            -webkit-transform: rotate(180deg);
                            transform: rotate(180deg);
                        }

                        100% {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }

                    }
                    @-webkit-keyframes rotates{

                        0% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }

                        50% {
                            -webkit-transform: rotate(180deg);
                            transform: rotate(180deg);
                        }

                        100% {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }

                    }
                    #jwpf-addon-{{ data.id }} .b-anim{
                        width: 1200px;
                        background-size: cover;
                        height: 950px;
                        position: absolute;
                        left: 50%;
                        margin-left: -600px;
                        z-index: 7;
                    }

                    #jwpf-addon-{{ data.id }} .b-anim .outer-cir{

                        width: 1110px;
                        height: 1100px;
                        border: 28px dashed #fff;
                        opacity: 0.12;
                        border-radius: 640px;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -568px;
                        margin-top: -578px;
                        z-index: 2;
                        -webkit-animation: rotate 80s linear 0s infinite;
                        animation: rotate 80s linear 0s infinite;

                    }

                    #jwpf-addon-{{ data.id }} .b-anim .outer-nc{

                        width: 900px;
                        height:900px;
                        border: 10px dotted #fff;
                        opacity: 0.12;
                        border-radius: 640px;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -460px;
                        margin-top: -460px;
                        z-index: 2;
                        -webkit-animation: rotates 80s linear 0s infinite;
                        animation: rotates 80s linear 0s infinite;

                    }

                    #jwpf-addon-{{ data.id }} .line-box{

                        width: 500px;
                        height: 500px;
                        position: absolute;
                        left: 50%;
                        margin-left: -250px;
                        top: 50%;
                        margin-top: -256px;
                        z-index: 8;

                    }

                    #jwpf-addon-{{ data.id }} .line-box .line-fir {

                        width: 500px;
                        height: 500px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        -webkit-animation: rotate 30s linear 0s infinite;
                        animation: rotate 30s linear 0s infinite;

                    }

                    #jwpf-addon-{{ data.id }} .line-box .tit {

                        display: inline-block;
                        position: absolute;
                        width: 24px;
                        height: 24px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/730fb41b5061c39460f83df4ec1de871.png) no-repeat center;
                        background-size: cover;
                        top: 50%;
                        margin-top: -12px;

                    }

                    #jwpf-addon-{{ data.id }} .line-box .line-sec .tit {
                        left: -11px;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .line-fir .tit {
                        right: -11px;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .line-sec {
                        width: 360px;
                        height: 360px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        position: absolute;
                        left: 50%;
                        margin-left: -180px;
                        top: 50%;
                        margin-top: -180px;
                        border-radius: 50%;
                        -webkit-animation: rotate 15s linear 0s infinite;
                        animation: rotate 15s linear 0s infinite;

                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-box {
                        display: block;
                        width:100px;
                        height:100px;
                        position: absolute;
                        opacity: 0;
                        font-size: 18px;
                        color: #fff;
                        text-align: center;
                        line-height: 20px;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-box span{
                        display: block;
                        width: 50%;
                        left:25%;
                        position: absolute;
                        top:50%;
                        margin-top:-20px;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-fir {
                        background-size: cover;
                        left: 50%;
                        top: -40px;
                        margin-left: -50px;
                    }
                    #jwpf-addon-{{ data.id }} .line-box .point-box i.a1,.line-box .point-box i.a3{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/911a47083beffb6c13a1550c178e4726.png) no-repeat center;
                        background-size: cover;
                    }
                    #jwpf-addon-{{ data.id }} .line-box .point-box i.a2,.line-box .point-box i.a5{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/4ee850d7f28c310c517978701f165606.png) no-repeat center;
                        background-size: cover;
                    }
                    #jwpf-addon-{{ data.id }} .line-box .point-box i.a4{
                        background: url(https://oss.lcweb01.cn/joomla/20230105/fb2a88a02ef84b8bcc5537299ae870ac.png) no-repeat center;
                        background-size: cover;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-box i {
                        display: block;
                        width: 100px;
                        height:100px;
                        position: absolute;9
                        left: 0;
                        top: 0;
                        -webkit-animation: rotate 5s linear 0s infinite;
                        animation: rotate 5s linear 0s infinite;

                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-sec {
                        left: -45px;
                        top: 30%;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-thi {
                        right: -38px;
                        top: 30%;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-fou {
                        right: 30px;
                        bottom: 5%;
                    }

                    #jwpf-addon-{{ data.id }} .line-box .point-tou {
                        left: 30px;
                        bottom: 5%;
                    }

                    #jwpf-addon-{{ data.id }} .more-box {
                        position: absolute;
                        width: 200px;
                        text-align: center;
                        left: 50%;
                        margin-left: -100px;
                        top: 50%;
                        margin-top: -60px;
                        z-index: 8;
                        opacity: 0;
                    }

                    #jwpf-addon-{{ data.id }} .more-box .ex-more {

                        display: block;
                        width: 200px;
                        height: 50px;
                        line-height: 50px;
                        text-align: center;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/7a065c4df51571d859a9d79d4ecb5441.png) no-repeat center;
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        text-transform: uppercase;
                        -webkit-transition: opacity 0.3s ease 0s;
                        transition: opacity 0.3s ease 0s;

                    }

                    #jwpf-addon-{{ data.id }} .mouse-ico {

                        display: block;
                        width: 60px;
                        height: 105px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/1d34b82f2b383ace26dedb4b026d9873.png) no-repeat center top;
                        position: absolute;
                        left: 50%;
                        margin-left: -15px;
                        top: 50%;
                        margin-top: 200px;
                        z-index: 5;
                        opacity: 0;

                    }

                    #jwpf-addon-{{ data.id }} .mouse-ico i {

                        display: block;
                        width: 100%;
                        height: 60px;
                        background: url(https://oss.lcweb01.cn/joomla/20230105/1a0fc97b907dca0f19de48991ee3d2a3.png) no-repeat center;
                        background-size: cover;
                        -webkit-animation: mousemove 3s linear 0s infinite;
                        animation: mousemove 3s linear 0s infinite;
                        position: relative;
                        top: 100px;

                    }

                    @media (max-width: 1200px){
                        #jwpf-addon-{{ data.id }} .banner .pwrap {
                            width: 90%;
                            margin-left: -45%;
                        }
                        #jwpf-addon-{{ data.id }} .bn-con h4{
                            line-height: 39px;
                        }

                        #jwpf-addon-{{ data.id }} .b-news{
                            bottom: 10px;
                        }

                        #jwpf-addon-{{ data.id }} .mouse-ico{
                            margin-top: 120px;
                        }
                        #jwpf-addon-{{ data.id }} .line-box{
                            top:40%!important;
                        }
                    }
                    @media (max-width: 1080px){
                        #jwpf-addon-{{ data.id }} .pwrap {
                            width: 90%;
                        }
                        #jwpf-addon-{{ data.id }} .mouse-ico{
                            display: none;
                        }
                        #jwpf-addon-{{ data.id }} .b-news{
                            display: none;
                        }
                    }
                    @media (max-width: 880px){

                        #jwpf-addon-{{ data.id }} .b-anim .outer-cir {
                            width: 600px!important;
                            height: 600px!important;
                            margin-left: -318px!important;
                            margin-top: -358px!important;
                        }
                        #jwpf-addon-{{ data.id }} .b-anim .outer-nc {
                            width: 400px!important;;
                            height: 400px!important;;
                            margin-left: -210px!important;
                            margin-top: -244px!important;
                        }
                        #jwpf-addon-{{ data.id }} .line-box .point-box,.line-box .point-box i{
                            width: 80px!important;
                            height: 80px!important;
                            font-size: 15px!important;
                        }
                        #jwpf-addon-{{ data.id }} .line-box .point-fir{
                            margin-left: -40px!important;
                        }
                        #jwpf-addon-{{ data.id }} .b-anim{

                            width: 100%!important;
                            height: 625px!important;
                            margin-left:0px!important;
                            left: 0!important;

                        }

                        #jwpf-addon-{{ data.id }} .line-box{

                            width: 260px!important;
                            height: 260px!important;

                            margin-left: -130px!important;

                            top: 23%!important;

                            margin-top: 0px!important;

                        }

                        #jwpf-addon-{{ data.id }} .line-box .line-fir{
                            width: 260px!important;
                            height: 260px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .line-sec{
                            width: 160px!important;
                            height: 160px!important;
                            margin-left: -80px!important;
                            margin-top: -80px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .point-thi{
                            right: -40px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .point-tou{
                            left: 26px!important;
                            bottom: -10%!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .point-fou{
                            right: 8px!important;
                            bottom: -10%!important;
                        }
                    }
                    @media (max-width: 768px){
                        #jwpf-addon-{{ data.id }} .banner .b-wrap img {
                            width: 120%!important;
                        }
                    }
                    @media (max-width: 636px){
                        #jwpf-addon-{{ data.id }} .banner .b-wrap img {
                            width: 135%!important;
                        }
                        #jwpf-addon-{{ data.id }} .banner .pwrap {
                            width: 100%!important;
                            margin-left: 0!important;
                            left: 0!important;
                        }
                    }

                    @media (max-width:500px){

                        #jwpf-addon-{{ data.id }} .b-anim{
                            background-image: none!important;
                        }

                        #jwpf-addon-{{ data.id }} .banner .b-wrap{
                            height: 500px!important;
                            background-size: cover!important;
                            background-repeat: no-repeat!important;
                            background-position: center!important;
                        }

                        #jwpf-addon-{{ data.id }} .banner .b-wrap img{
                            display: none!important;
                        }
                    }

                    @media (max-width: 414px){
                        #jwpf-addon-{{ data.id }} .line-box{
                            width: 220px!important;
                            height: 220px!important;
                            margin-left: -110px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .line-fir{
                            width: 220px!important;
                            height: 220px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .line-sec{
                            width: 100px!important;
                            height: 100px!important;
                            margin-left: -50px!important;
                            margin-top: -50px!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .point-tou{
                            left: 4px!important;
                            bottom: -17%!important;
                        }

                        #jwpf-addon-{{ data.id }} .line-box .point-fou{
                            right: -4px!important;
                            bottom: -17%!important;
                        }
                    }
            </style>
            <div class="banner dj">
                <div class="b-wrap" >
                    <img class="bimg" src=\'{{type3_img}}\' >
                    <b class="b-mask"></b>
                    <div class="pwrap">
                        <!--浮动动画-->
                        <div class="b-anim">
                            <span class="outer-cir"></span>
                            <span class="outer-nc"></span>
                            <div class="line-box" style="opacity: 1;">
                                <div class="line-fir">
                                    <span class="tit"></span>
                                </div>
                                <div class="line-sec">
                                    <span class="tit"></span>
                                </div>
                                <a class="point-fir point-box"  style="opacity: 1;">
                                    <span>{{type3_title1}}</span>
                                    <i class="a1"></i>
                                </a>
                                <a class="point-sec point-box" style="opacity: 1;">
                                    <span>{{type3_title2}}</span>
                                    <i class="a2"></i>
                                </a>
                                <a class="point-thi point-box"  style="opacity: 1;">
                                    <span>{{type3_title3}}</span>
                                    <i class="a3"></i>
                                </a>
                                <a class="point-fou point-box"  style="opacity: 1;">
                                    <span>{{type3_title4}}</span>
                                    <i class="a4"></i>
                                </a>
                                <a class="point-tou point-box" style="opacity: 1;">
                                    <span>{{type3_title5}}</span>
                                    <i class="a5"></i>
                                </a>
                                <i class="a6"></i>
                                <i class="a7"></i>
                            </div>
                            <span class="mouse-ico" style="opacity: 1;"> <i></i> </span>
                        </div>
                    </div>
                </div>
            </div>
        <# }else{ #>
            <#
                //开启浮动
                var image_width = (!_.isEmpty(data.image_width) && data.image_width) ? data.image_width : 200;
                var image_height = (!_.isEmpty(data.image_height) && data.image_height) ? data.image_height : 200;
                var isFixed = (!_.isEmpty(data.isFixed) && data.isFixed) ? data.isFixed : 0;
                var fixed_style = (!_.isEmpty(data.fixed_style) && data.fixed_style) ? data.fixed_style : "left";
                var fixed_top = (!_.isEmpty(data.fixed_top) && data.fixed_top) ? data.fixed_top : 40;
                var fixed_right = (!_.isEmpty(data.fixed_right) && data.fixed_right) ? data.fixed_right : 0;
                var fixed_bottom = (!_.isEmpty(data.fixed_bottom) && data.fixed_bottom) ? data.fixed_bottom : 0;
                var fixed_left = (!_.isEmpty(data.fixed_left) && data.fixed_left) ? data.fixed_left : 0;

                var zz_animt = (!_.isEmpty(data.zz_animt) && data.zz_animt) ? data.zz_animt : "bot";
                var image = (!_.isEmpty(data.image) && data.image) ? data.image : "https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg";

                //开启遮罩
                var isMask = (!_.isEmpty(data.isMask) && data.isMask) ? data.isMask : 0;
                var mask_title = (!_.isEmpty(data.mask_title) && data.mask_title) ? data.mask_title : "图片标题";
                var mask_title_fontsize = (!_.isEmpty(data.mask_title_fontsize) && data.mask_title_fontsize) ? data.mask_title_fontsize : "20";
                var mask_title_font_color = (!_.isEmpty(data.mask_title_font_color) && data.mask_title_font_color) ? data.mask_title_font_color : "#fff";
                var mask_intro = (!_.isEmpty(data.mask_intro) && data.mask_intro) ? data.mask_intro : "图片简介";
                var mask_intro_fontsize = (!_.isEmpty(data.mask_intro_fontsize) && data.mask_intro_fontsize) ? data.mask_intro_fontsize : "14";
                var mask_intro_font_color = (!_.isEmpty(data.mask_intro_font_color) && data.mask_intro_font_color) ? data.mask_intro_font_color : "#fff";
                var mask_bg_color = (!_.isEmpty(data.mask_bg_color) && data.mask_bg_color) ? data.mask_bg_color : "rgba(15, 63, 119, 0.7)";
                var button_title = (!_.isEmpty(data.button_title) && data.button_title) ? data.button_title : "了解更多";
                var button_bg_color = (!_.isEmpty(data.button_bg_color) && data.button_bg_color) ? data.button_bg_color : "#89c400";
                var mask_title_lineheight = (!_.isEmpty(data.mask_title_lineheight) && data.mask_title_lineheight) ? data.mask_title_lineheight : "65";
                var mask_button_color = (!_.isEmpty(data.mask_button_color) && data.mask_button_color) ? data.mask_button_color : "#fff";

                // 开启百分比
                var image_open_bfb = (!_.isEmpty(data.image_open_bfb) && data.image_open_bfb) ? data.image_open_bfb : 0;
                var image_width_bfb = (!_.isEmpty(data.image_width_bfb) && data.image_width_bfb) ? data.image_width_bfb : "100";

                // 遮罩标题
                var zz_buju = (!_.isEmpty(data.zz_buju) && data.zz_buju) ? data.zz_buju : "quan";
                var zz_width = (!_.isEmpty(data.zz_width) && data.zz_width) ? data.zz_width : "100";
                var mask_title_borderradius = (!_.isEmpty(data.mask_title_borderradius) && data.mask_title_borderradius) ? data.mask_title_borderradius : "0px 0px 0px 0px";
                var mask_title_font_hvcolor = (!_.isEmpty(data.mask_title_font_hvcolor) && data.mask_title_font_hvcolor) ? data.mask_title_font_hvcolor : "#fff";
                var mask_bg_hvcolor = (!_.isEmpty(data.mask_bg_hvcolor) && data.mask_bg_hvcolor) ? data.mask_bg_hvcolor : "rgba(255, 255, 255, .6)";

                var is_hover = (!_.isEmpty(data.is_hover) && data.is_hover) ? data.is_hover : "0";
                var erweim = (!_.isEmpty(data.erweim) && data.erweim) ? data.erweim : "https://oss.lcweb01.cn/joomla/20220818/dbab5d8110af6fe79939930cbc6aac5b.png";
                var erweim_bg = (!_.isEmpty(data.erweim_bg) && data.erweim_bg) ? data.erweim_bg : "https://oss.lcweb01.cn/joomla/20220818/74ca67254c87c948346935fb67d22fc1.png";
                var erweim_width = (!_.isEmpty(data.erweim_width) && data.erweim_width) ? data.erweim_width : "100";
                var erweim_rele = (!_.isEmpty(data.erweim_rele) && data.erweim_rele) ? data.erweim_rele : "bot";
                var erweim_top = (!_.isEmpty(data.erweim_top) && data.erweim_top) ? data.erweim_top : "30";
                var erweim_left = (!_.isEmpty(data.erweim_left) && data.erweim_left) ? data.erweim_left : "0";

            #>
            <style>
                <# if(data.isFixed == 1){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon {
                        position: fixed;
                        z-index: 9999;
                        <# if(_.isObject(data.image_width)) { #>
                            width: {{data.image_width.md}}px;
                        <# } else { #>
                            width: {{data.image_width}}px;
                        <# } #>
                        <# if(_.isObject(data.image_height)) { #>
                            height: {{data.image_height.md}}px;
                        <# } else { #>
                            height: {{data.image_height}}px;
                        <# } #>
                        <# switch (data.fixed_style) {
                            case \'left\': #>
                                left: 0;
                                top: 120px;
                                <# break;
                            case \'right\': #>
                                right: 0;
                                top: 120px;
                                <# break;
                            case \'right_bottom\': #>
                                right: 0;
                                bottom: 0;
                                <# break;
                            case \'left-center\': #>
                                left: 0;
                                top: 0;
                                bottom: 0;
                                margin: auto;
                                <# break;
                            case \'right-center\': #>
                                right: 0;
                                top: 0;
                                bottom: 0;
                                margin: auto;
                                <# break;
                            case \'custom\': #>
                                top: {{data.fixed_top}}px;
                                right: {{data.fixed_right}}px;
                                bottom: {{data.fixed_bottom}}px;
                                left: {{data.fixed_left}}px;
                                <# break;
                        } #>
                    }
                <# } #>

                <# if(open_lightbox && data.overlay_color){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-image-overlay{
                        background-color: {{ data.overlay_color }};
                        border-radius: {{ data.border_radius }}px;
                    }
                <# } #>

                #jwpf-addon-{{ data.id }} img{
                    border-radius: {{ data.border_radius }}px;
                    <# if (data.image_fit) { #>
                    object-fit: {{data.image_fit}};
                    <# } #>
                    <# if(_.isObject(data.image_height)) { #>
                        height: {{data.image_height.md}}px;
                    <# } else { #>
                        height: {{data.image_height}}px;
                    <# } #>
                    <# if(data.image_open_bfb==1){ #>
                        width: {{data.image_width_bfb}}%;
                    <# }else{ #>
                        <# if(_.isObject(data.image_width)) { #>
                            width: {{data.image_width.md}}px;
                        <# } else { #>
                            width: {{data.image_width}}px;
                        <# } #>
                        <# if(_.isObject(data.image_width)) { #>
                            max-width: {{data.image_width.md}}px;
                        <# } else { #>
                            max-width: {{data.image_width}}px;
                        <# } #>
                    <# } #>
                }
                <# if(data.image_fangda){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-content:hover img{
                        transform: scale(1.2);
                        transition: all 0.8s;
                    };
                <# } #>
                #jwpf-addon-{{ data.id }} .jwpf-addon-title{
                    <# if(_.isObject(data.title_padding)) { #>
                        padding:{{data.title_padding.md}};
                    <# } else { #>
                        padding:{{data.title_padding}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .ewe{
                    <# if(data.is_hover == 1){ #>
                        padding:5px;width:{{data.erweim_width}}px;background:url({{erweim_bg}})no-repeat;
                        background-size:100% 100%;position:absolute;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .ewe img{
                    <# if(data.is_hover == 1){ #>
                        max-width:inherit;
                    <# } #>
                }

                #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container{
                    <# if(data.image_open_bfb == 1){ #>
                        width:100%;
                    <# } #>
                }


                <# if(data.isMask == 1){ #>

                    <# if(data.zz_animt=="center"){ #>

                    #jwpf-addon-{{ data.id }} .ind3-b3 {
                        width: 100%;
                        height: 100%;
                        background: {{data.mask_bg_color}};
                        position: absolute;
                        top: 0;
                        left: 0;
                        transform: scale(0);
                        transition: 0.5s;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container:hover .ind3-b3 {
                        transform: scale(1);
                        transition: 0.5s;
                    }
                    #jwpf-addon-{{ data.id }} .ind3-b4 {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        display: table;
                    }
                    #jwpf-addon-{{ data.id }} .ind3-b4>div {
                        width: 100%;
                        display: table-cell;
                        vertical-align: middle;
                    }
                    #jwpf-addon-{{ data.id }} .ind3-b5 {
                        font-size: {{data.mask_title_fontsize}}px;
                        line-height: {{data.mask_title_lineheight}}px;
                        color: {{data.mask_title_font_color}};
                        text-align: center;
                        font-family: {{data.zzfont_family}};
                    }
                    #jwpf-addon-{{ data.id }} .ind3-b6 {
                        font-size: {{data.mask_intro_fontsize}}px;
                        line-height: 36px;
                        color: {{data.mask_intro_font_color}};
                        text-align: center;
                        font-weight: bold;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container>a {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        display: block;
                    }


               <# } #>


                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container{
                        overflow:hidden;
                        <# if(data.image_open_bfb == 1){ #>
                            width:100%;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container img {
                        <# if(_.isObject(data.image_height)) { #>
                            height: {{data.image_height.md}}px;
                        <# } else { #>
                            height: {{data.image_height}}px;
                        <# } #>
                        <# if(data.image_open_bfb==1){ #>
                            width: {{data.image_width_bfb}}%;
                        <# }else{ #>
                            <# if(_.isObject(data.image_width)) { #>
                                width: {{data.image_width.md}}px;
                            <# } else { #>
                                width: {{data.image_width}}px;
                            <# } #>
                            <# if(_.isObject(data.image_width)) { #>
                                max-width: {{data.image_width.md}}px;
                            <# } else { #>
                                max-width: {{data.image_width}}px;
                            <# } #>
                        <# } #>

                    }

                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container h3 {
                      width: {{zz_width}}%;
                      bottom: 0;
                      margin-bottom:0px;
                      left: 0;
                      font-size: {{mask_title_fontsize}}px;
                      color:{{mask_title_font_color}};
                      background: {{mask_bg_color}};
                      position: absolute;
                      line-height: {{mask_title_lineheight}}px;
                      padding: 0 20px;
                        <# if(zz_buju=="biao"){ #>
                            z-index:0;
                            border-radius:{{mask_title_borderradius}};
                        <# } #>
                    }

                    <# if(zz_buju=="biao"){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container:hover h3 {
                            color:{{mask_title_font_hvcolor}};
                        }

                    <# } #>


                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container .pic_con {
                      width: 100%;
                      height: 0;
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      background: {{mask_bg_color}};
                      transition: all 0.3s;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container .pic_con .pic_txt {
                      line-height: 30px;
                      padding: 35% 1% 10%;
                      font-size: {{mask_intro_fontsize}}px;
                      color:{{mask_intro_font_color}};
                        <# if(_.isObject(data.image_height)) { #>
                            max-height: {{data.image_height.md}}px;
                        <# } else { #>
                            max-height: {{data.image_height}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-single-image-container .pic_con .pic_more {
                      padding:10px 20px;
                      background: {{button_bg_color}};
                      margin: 0 auto;
                      text-align:center;
                      font-size: {{mask_intro_fontsize}}px;
                      color:{{mask_button_color}};
                    }
                <# } #>
                @media (min-width: 768px) and (max-width: 991px) {
                    <# if (data.isFixed == 1) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-addon {
                            <# if(_.isObject(data.image_height)) { #>
                                height: {{data.image_height.sm}}px;
                            <# } #>
                            <# if(_.isObject(data.image_width)) { #>
                                width: {{data.image_width.sm}}px;
                            <# } #>
                        }
                    <# } #>
                    <# if(_.isObject(data.title_padding)) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-addon-title{
                            padding: {{data.title_padding.sm}};
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} img{
                        <# if(_.isObject(data.image_height)) { #>
                            height: {{data.image_height.sm}}px!important;
                        <# } #>

                        <# if(data.image_open_bfb==1){ #>
                            width: {{data.image_width_bfb}}%;
                            max-width: 100%;
                        <# }else{ #>

                            <# if(_.isObject(data.image_width)) { #>
                                <# if(data.image_width.sm){ #>
                                    width: {{data.image_width.sm}}px;
                                <# }else{ #>
                                    width: 100%;
                                <# } #>
                            <# } #>
                            <# if(_.isObject(data.image_width)) { #>
                                <# if(data.image_width.sm){ #>
                                    max-width: {{data.image_width.sm}}px;
                                <# }else{ #>
                                    max-width: 100%;
                                <# } #>
                            <# } #>
                        <# } #>

                    }
                }
                @media (max-width: 767px) {
                    <# if (data.isFixed == 1) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-addon {
                            <# if(_.isObject(data.image_height)) { #>
                                height: {{data.image_height.xs}}px;
                            <# } #>
                            <# if(_.isObject(data.image_width)) { #>
                                <# if(data.image_width.xs){ #>
                                    width: {{data.image_width.xs}}px;
                                <# }else{ #>
                                    width: 100%;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    <# if(_.isObject(data.title_padding)) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-addon-title{
                            padding: {{data.title_padding.xs}};
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} img{
                        <# if(_.isObject(data.image_height)) { #>
                            height: {{data.image_height.xs}}px!important;
                        <# }else{ #>
                            height:auto;
                        <# } #>

                        <# if(data.image_open_bfb==1){ #>
                            width: {{data.image_width_bfb}}%;
                            max-width: 100%;
                        <# }else{ #>

                            <# if(_.isObject(data.image_width)) { #>
                                <# if(data.image_width.xs){ #>
                                    width: {{data.image_width.xs}}px;
                                <# }else{ #>
                                    width: 100%;
                                <# } #>

                            <# } #>
                            <# if(_.isObject(data.image_width)) { #>
                                <# if(data.image_width.xs){ #>
                                    max-width: {{data.image_width.xs}}px;
                                <# }else{ #>
                                    max-width: 100%;
                                <# } #>
                            <# } #>
                        <# } #>

                    }
                }
            </style>

                <div class="jwpf-addon jwpf-addon-single-image {{ data.position }} {{ data.class }}">
                    <# if( !_.isEmpty( data.title ) && data.title_position != "bottom" ){ #><{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{ data.title }}</{{ data.heading_selector }}><# } #>
                    <div class="jwpf-addon-content">
                        <div class="jwpf-addon-single-image-container" <# if(is_hover==1){ #> style="position:relative;" <# } #> >
                            <# if(image_overlay && open_lightbox) { #>
                                <div class="jwpf-addon-image-overlay"></div>
                            <# } #>
                            <# if(open_lightbox && data.isMask==0) { #>
                                <a class="jwpf-magnific-popup jwpf-addon-image-overlay-icon" data-popup_type="image" data-mainclass="mfp-no-margins mfp-with-zoom" href=\'{{ image }}\'>+</a>
                            <# } #>

                            <# if(!open_lightbox && data.isMask==0) { #>
                                <a target="{{ data.target }}" href=\'{{ data.link }}\'>
                            <# } #>

                            <# if(data.is_hover==1 && data.erweim_rele=="top") { #>
                                <div class="ewe" style="left:{{erweim_left}}px;top:-{{erweim_top}}px;">
                                    <img src=\'{{erweim}}\' style="width:100%;height:100%;margin-bottom:5px;">
                                </div>
                            <# } #>

                            <# if(data.aboutcase || data.ht_image_data){ #>
                                <img class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20250117/68fb7a470f0e7c39ddb49c169d56fbf9.png" alt="{{ alt_text }}" title="{{ data.title }}" style="object-fit: contain;">
                            <# } else { #>
                                <# if(image.indexOf("http://") == -1 && image.indexOf("https://") == -1){ #>
                                    <img class="jwpf-img-responsive" src=\'{{ pagefactory_base + image }}\' alt="{{ alt_text }}" title="{{ data.title }}">
                                <# } else { #>
                                    <img class="jwpf-img-responsive" src=\'{{ image }}\' alt="{{ alt_text }}" title="{{ data.title }}">
                                <# } #>
                            <# } #>

                            <# if(data.is_hover==1 && data.erweim_rele=="bot") { #>
                                <div class="ewe" style="left:{{erweim_left}}px;top:{{erweim_top}}px;">
                                    <img src=\'{{erweim}}\' style="width:100%;height:100%;margin-top:5px;">
                                </div>
                            <# } #>

                            <# if(data.isMask==1) {
                                if(data.zz_animt=="center"){ #>

                                    <div class="ind3-b3"></div>
                                    <div class="ind3-b4">
                                        <div>
                                            <div class="ind3-b5">{{ mask_title }}</div>
                                            <div class="ind3-b6">{{ mask_intro }}</div>
                                        </div>
                                    </div>
                                    <a href=""></a>

                                <# }else{ #>
                                    <h3>{{ mask_title }}</h3>
                                    <div class="pic_con">
                                        <div class="pic_txt">{{ mask_intro }}</div>
                                        <a target="{{ data.target }}" href=\'{{ data.link }}\' class="pic_more">
                                            {{ button_title }}
                                        </a>
                                    </div>
                                <# } #>

                            <# } #>


                            <# if(!open_lightbox && data.isMask==0) { #>
                                </a>
                            <# } #>

                        </div>
                        <# if( !_.isEmpty( data.title ) && data.title_position == "bottom" ){ #><{{ data.heading_selector }} class="jwpf-addon-title">{{ data.title }}</{{ data.heading_selector }}><# } #>
                    </div>
                </div>

            <# if (data.isFixed == 1) { #>
            <div>
               <div style="margin:100px">此位置仅在编辑器中站位使用,不影响预览效果</div>
            </div>
            <# } #>

        <# } #>
        ';

        return $output;
    }
}
