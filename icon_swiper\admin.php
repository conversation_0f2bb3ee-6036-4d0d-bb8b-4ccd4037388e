<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'icon_swiper',
        'title' => JText::_('图标轮播'),
        'desc' => JText::_('图标轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'std' => array(
                        array(
                            'icon'=>'https://oss.lcweb01.cn/joomla/20221021/0ffbd213c130aa46c5c605021be6bd5f.png',
                            'icon_active'=>'https://oss.lcweb01.cn/joomla/20221021/79b48ce563dcd24067ea5c1c0197fa4b.png',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/fe5fc460f1c1ade29316b6cc2effee24.png',
                            'text'=>'公司名称',
                        ),
                        array(
                            'icon'=>'https://oss.lcweb01.cn/joomla/20221021/0ffbd213c130aa46c5c605021be6bd5f.png',
                            'icon_active'=>'https://oss.lcweb01.cn/joomla/20221021/79b48ce563dcd24067ea5c1c0197fa4b.png',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/fe5fc460f1c1ade29316b6cc2effee24.png',
                            'text'=>'公司名称',
                        ),
                        array(
                            'icon'=>'https://oss.lcweb01.cn/joomla/20221021/0ffbd213c130aa46c5c605021be6bd5f.png',
                            'icon_active'=>'https://oss.lcweb01.cn/joomla/20221021/79b48ce563dcd24067ea5c1c0197fa4b.png',
                            'img'=>'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/fe5fc460f1c1ade29316b6cc2effee24.png',
                            'text'=>'公司名称',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'swiper_settings' => array(
                            'type' => 'separator',
                            'title' => '轮播项设置',
                        ),
                        'text'=>array(
                            'type' => 'text',
                            'title' => '轮播项标题',
                            'desc' => '轮播项标题',
                            'std'=>'龙云正元'
                        ),
                        'icon_style'=>array(
                            'type' => 'buttons',
                            'title' => '轮播项图标',
                            'desc' => '轮播项图标',
                            'std'=>'normal',
                            'tabs'=>true,
                            'values' => array(
                                array(
                                    'label' => '正常状态',
                                    'value' => 'normal'
                                ),
                                array(
                                    'label' => '激活状态',
                                    'value' => 'active'
                                ),
                            ),
                        ),
                        'icon'=>array(
                            'type' => 'media',
                            'title' => '轮播项图标',
                            'desc' => '轮播项图标',
                            'std'=>'https://oss.lcweb01.cn/joomla/20221021/0ffbd213c130aa46c5c605021be6bd5f.png',
                            'depends'=>array(
                                array(
                                    'icon_style','=','normal'
                                )
                            )
                        ),
                        'icon_active'=>array(
                            'type' => 'media',
                            'title' => '轮播项图标',
                            'desc' => '轮播项图标',
                            'std'=>'https://oss.lcweb01.cn/joomla/20221021/79b48ce563dcd24067ea5c1c0197fa4b.png',
                            'depends'=>array(
                                array(
                                    'icon_style','=','active'
                                )
                            )
                        ),
                        'display_settings' => array(
                            'type' => 'separator',
                            'title' => '展示设置',
                        ),
                        'img'=> array(
                            'type' => 'media',
                            'title' => JText::_('展示图片'),
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220608/fe5fc460f1c1ade29316b6cc2effee24.png',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '链接地址',
                            'desc' => '链接地址',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),
                        'logo'=> array(
                            'type' => 'media',
                            'title' => JText::_('展示logo'),
                            'std' => 'https://oss.lcweb01.cn/joomla/20221019/6d9962b059473fc35736436e46c3b7eb.png',
                        ),
                        'company'=>array(
                            'type' => 'text',
                            'title' => '企业名称',
                            'desc' => '企业名称',
                            'std'=>'龙云正元'
                        ),
                        'url'=>array(
                            'type' => 'text',
                            'title' => '企业网址',
                            'desc' => '企业网址',
                            'std'=>'https://qianzhenglong.cn/'
                        ),
                        'introduce'=>array(
                            'type' => 'text',
                            'title' => '企业介绍',
                            'desc' => '企业介绍',
                            'std'=>'https://qianzhenglong.cn/'
                        ),
                    ),
                ),

                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CAROUSEL_OPTIONS'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播元素样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'carousel_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播宽度占比'),
                    'desc' => JText::_('轮播宽度占比'),
                    'min' => 0,
                    'max' => 100,
                    'std' =>67,
                    'depends'=>array(
                        array('carousel_options','=','elements')
                    ),
                    'responsive'=>true
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播高度'),
                    'desc' => JText::_('轮播高度'),
                    'min' => 0,
                    'max' => 500,
                    'std' =>array('md'=>140,'sm'=>105,'xs'=>52),
                    'depends'=>array(
                        array('carousel_options','=','elements')
                    ),
                    'responsive'=>true
                ),
                'carousel_item_number' => array(
                    'type' => 'slider',
                    'title' => JText::_('一次显示的slides'),
                    'desc' => JText::_('一次显示的slides'),
                    'min' => 1,
                    'max' => 6,
                    'std' => array(
                        'md'=>5,
                        'sm'=>3,
                        'xs'=>2
                    ),
                    'depends'=>array(
                        array('carousel_options','=','elements')
                    ),
                    'responsive'=>true
                ),

                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends'=>array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 2500
                ),
                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => '控制器设置',
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),

                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '激活状态',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends'=>array(
                        array('carousel_options','=','elements')
                    )
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                    ),
                    'std' => array('md'=>50,'sm'=>44,'xs'=>22),
                    'responsive'=>true,
                ),

                'arrow_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),

                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#ccc',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                'arrow_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => 22,
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','normal_arrow'),
                    )
                ),
                //Arrow hover
                'arrow_hover_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                    )
                ),
                'arrow_hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                    )
                ),
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#c2322c',
                    'depends' => array(
                        array('carousel_options','=','elements'),
                        array('arrow_style','=','hover_arrow'),
                    )
                ),


                'swiper_setting' => array(
                    'type' => 'separator',
                    'title' => '轮播区域设置',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'item_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播元素样式'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '激活状态',
                            'value' => 'active'
                        )
                    ),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    )
                ),
                'swiper_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('轮播项内边距'),
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),

                    'std' => array(
                        'md' => '20px 0 20px 0',
                        'sm' => '10px 0 10px 0',
                        'xs' => '5px 0 5px 0'
                    ),
                    'responsive'=>true
                ),
                'swiper_img_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播项图标高度（宽度自适应）'),
                    'std' => array('md'=>50,'sm'=>44,'xs'=>22),
                    'max'=>200,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),
                    'responsive'=>true
                ),
                'swiper_title_font' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播项标题字号'),
                    'std' => array('md'=>16,'sm'=>14,'xs'=>12),
                    'max'=>50,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),
                    'responsive'=>true
                ),
                'swiper_title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播项标题上边距'),
                    'std' => array('md'=>30,'sm'=>0,'xs'=>0),
                    'max'=>50,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),
                    'responsive'=>true
                ),
                'swiper_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('轮播标题颜色'),
                    'std' => '#454343',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),
                ),
                'swiper_title_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('轮播标题颜色'),
                    'std' => '#fff',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','active')
                    ),
                ),
                'swiper_background' => array(
                    'type' => 'color',
                    'title' => JText::_('轮播项背景色'),
                    'std' => '#fff',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','normal')
                    ),
                ),
                'swiper_background_active' => array(
                    'type' => 'color',
                    'title' => JText::_('轮播项背景色'),
                    'std' => '#c2322c',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                        array('item_style','=','active')
                    ),
                ),
                'content_setting' => array(
                    'type' => 'separator',
                    'title' => '内容区域设置',
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容高度'),
                    'std' => 720,
                    'max'=> 1500,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_company_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('企业名称上边距'),
                    'std' => 50,
                    'max'=> 1000,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_company_bottom'=> array(
                    'type' => 'slider',
                    'title' => JText::_('企业名称下边距'),
                    'std' => 30,
                    'max'=> 1000,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_img_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('logo宽度（高度自适应）'),
                    'std' => '',
                    'max'=> 1000,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_img_margin_right'=> array(
                    'type' => 'slider',
                    'title' => JText::_('logo右边距'),
                    'std' => 20,
                    'max'=> 1000,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                ),
                'content_company_font'=> array(
                    'type' => 'slider',
                    'title' => JText::_('企业名称字号'),
                    'std' => 42,
                    'max'=> 60,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_url_font'=> array(
                    'type' => 'slider',
                    'title' => JText::_('企业网址字号'),
                    'std' => 16,
                    'max'=> 40,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_introduce_font'=> array(
                    'type' => 'slider',
                    'title' => JText::_('企业介绍字号'),
                    'std' => 18,
                    'max'=> 40,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_introduce_width'=> array(
                    'type' => 'slider',
                    'title' => JText::_('企业介绍宽度占比'),
                    'std' => 33,
                    'max'=> 100,
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
                'content_color'=> array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends'=>array(
                        array('carousel_options','=','item_style'),
                    ),
                    'responsive'=>true
                ),
            ),
        ),
    )
);
