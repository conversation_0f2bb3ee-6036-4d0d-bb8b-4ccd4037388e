<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonText_block extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$addon_ids = 'jwpf-addona-' . $this->addon->id;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		//Options
		$text = (isset($settings->text) && $settings->text) ? $settings->text : '';
		$alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : '';
		$dropcap = (isset($settings->dropcap) && $settings->dropcap) ? $settings->dropcap : 0;



        // 2021.08.26
        // 文本块来源
        $text_from = (isset($settings->text_from) && $settings->text_from) ? $settings->text_from : 0;
        // 文本块内容
        $text_id = (isset($settings->text_id) && $settings->text_id) ? $settings->text_id : null;
        // 2021.08.26

		$dropcapCls = '';
		if ($dropcap) {
			$dropcapCls = 'jwpf-dropcap';
		}

		// 首行缩进
		$indent = (isset($settings->indent) && $settings->indent) ? $settings->indent : 0;
		$indentCls = '';
		if ($indent) {
			$indentCls = 'jwpf-indent';
		}


		//Output
        $output = '';
			if($text_from == 1 && $text_id) {
				$infos = JwPageFactoryBase::getInfoById($text_id);
				if($infos){
					$text=$infos->fulltext;
				}
			}

            $output .= '<div  class="jwpf-addon jwpf-addon-text-block ' . $dropcapCls . ' ' . $alignment . ' '. $indentCls .'" style=" ' . $class . '">';
            $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
            $output .= '<div class="jwpf-addon-content" >';
			if(strstr($text,'ijzt.china9.cn'))
			{
				$text = '<span style="color:red;">跳转链接请勿直接放入预览地址</span>';
			}
            $output .= $text;
            $output .= '</div>';
            $output .= '</div>';


		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$css = '';
		$dropcap_style = '';
		$dropcap_style .= (isset($settings->dropcap_color) && $settings->dropcap_color) ? "color: " . $settings->dropcap_color . ";" : "";
		$dropcap_style .= (isset($settings->dropcap_font_size) && $settings->dropcap_font_size) ? "font-size: " . $settings->dropcap_font_size . "px;" : "";
		$dropcap_style .= (isset($settings->dropcap_font_size) && $settings->dropcap_font_size) ? "line-height: " . $settings->dropcap_font_size . "px;" : "";
		$dropcap_style_sm = (isset($settings->dropcap_font_size_sm) && $settings->dropcap_font_size_sm) ? "font-size: " . $settings->dropcap_font_size_sm . "px;" : "";
		$dropcap_style_sm .= (isset($settings->dropcap_font_size_sm) && $settings->dropcap_font_size_sm) ? "line-height: " . $settings->dropcap_font_size_sm . "px;" : "";
		$dropcap_style_xs = (isset($settings->dropcap_font_size_xs) && $settings->dropcap_font_size_xs) ? "font-size: " . $settings->dropcap_font_size_xs . "px;" : "";
		$dropcap_style_xs .= (isset($settings->dropcap_font_size_xs) && $settings->dropcap_font_size_xs) ? "line-height: " . $settings->dropcap_font_size_xs . "px;" : "";

		$style = '';
		$style_sm = '';
		$style_xs = '';

		$style .= (isset($settings->text_fontsize) && $settings->text_fontsize) ? "font-size: " . $settings->text_fontsize . "px;" : "";
		$style .= (isset($settings->text_fontweight) && $settings->text_fontweight) ? "font-weight: " . $settings->text_fontweight . ";" : "";
		$style_sm .= (isset($settings->text_fontsize_sm) && $settings->text_fontsize_sm) ? "font-size: " . $settings->text_fontsize_sm . "px;" : "";
		$style_xs .= (isset($settings->text_fontsize_xs) && $settings->text_fontsize_xs) ? "font-size: " . $settings->text_fontsize_xs . "px;" : "";

		$style .= (isset($settings->text_lineheight) && $settings->text_lineheight) ? "line-height: " . $settings->text_lineheight . "px;" : "";
		$style_sm .= (isset($settings->text_lineheight_sm) && $settings->text_lineheight_sm) ? "line-height: " . $settings->text_lineheight_sm . "px;" : "";
		$style_xs .= (isset($settings->text_lineheight_xs) && $settings->text_lineheight_xs) ? "line-height: " . $settings->text_lineheight_xs . "px;" : "";

        /*纵向排列*/
        $column = (isset($settings->column) && ($settings->column || $settings->column==0)) ? $settings->column: 0;
        $writing_mode = (isset($settings->writing_mode) && $settings->writing_mode) ? $settings->writing_mode: '';
        $column_title_margin = (isset($settings->column_title_margin) && $settings->column_title_margin) ? $settings->column_title_margin: '0 0 0 0';

//        字体
        $css.='@font-face {
              font-family: "宋体";
              src: url("/components/com_jwpagefactory/addons/text_block/assets/font/SimSun-01.ttf");
            }';

		//解决图片不居中问题
        $css .= '#jwpf-addon-' . $this->addon->id . ' img {
            display: inline-block;
        }';

				$css.='#jwpf-addon-'. $this->addon->id.' .jwpf-addon-content{
          word-wrap: break-word;
    			word-break: break-all;
				}';

		// 首行缩进
		if (isset($settings->indent) && $settings->indent ) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-indent .jwpf-addon-content{text-indent:2em}';
		}


		if (isset($settings->dropcap) && $settings->dropcap && !empty($dropcap_style)) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-dropcap .jwpf-addon-content:first-letter{ ' . $dropcap_style . ' }';
		}

		if ($style) {
			$css .= '#jwpf-addon-' . $this->addon->id . '{ ' . $style . ' }';
		}

//      文本纵向排列
        if($column==1){
            $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title{
                margin: '.$column_title_margin.';
            }';
            if($writing_mode=='vertical-lr'){
                $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
                #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{
                    float: left;
                }';
            }elseif($writing_mode=='vertical-rl'){
                $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
                #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{
                    float: right;
                }';
            }
            $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-title,
            #jwpf-addon-' . $this->addon->id . ' .jwpf-addon-content{
                writing-mode: '.$writing_mode.';
            }';
        }

		$css .= '@media (min-width: 768px) and (max-width: 991px) {';
		if ($style_sm) {
			$css .= '#jwpf-addon-' . $this->addon->id . '{';
			$css .= $style_sm;
			$css .= '}';
		}
		if ($dropcap_style_sm) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-dropcap .jwpf-addon-content:first-letter {';
			$css .= $dropcap_style_sm;
			$css .= '}';
		}
		$css .= '}';

		$css .= '@media (max-width: 767px) {';
		if ($style_xs) {
			$css .= '#jwpf-addon-' . $this->addon->id . '{ ' . $style_xs . ' }';
		}
		if ($dropcap_style_xs) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-dropcap .jwpf-addon-content:first-letter {';
			$css .= $dropcap_style_xs;
			$css .= '}';
		}
		$css .= '}';

		return $css;
	}

	public static function getTemplate()
	{

		$output = '
		<#


			var dropcap = "";

			if(data.dropcap){
				dropcap = "jwpf-dropcap";
			}



			var indent = "";
			if(data.indent){
				indent = "jwpf-indent";
			}


			if(!data.heading_selector){
				data.heading_selector = "h3";
			}

			// 2021.08.26
			// 文本块来源
			var text_from = data.text_from || 0;
			// 文本块内容
			var text_id = data.text_id;


			// 2021.08.26


//			纵向排列
			let column=data.column||0;
			let writing_mode = data.writing_mode||"";
		    let column_title_margin = data.column_title_margin|| "0 0 0 0";
		#>
		<style type="text/css">
			#jwpf-addon-{{ data.id }}{
				<# if(_.isObject(data.text_fontsize)){ #>
					font-size: {{ data.text_fontsize.md }}px;
				<# } else { #>
					font-size: {{ data.text_fontsize }}px;
				<# } #>

				<# if(_.isObject(data.text_lineheight)){ #>
					line-height: {{ data.text_lineheight.md }}px;
				<# } else { #>
					line-height: {{ data.text_lineheight }}px;
				<# } #>
				font-weight:{{data.text_fontweight}};
			}
			#jwpf-addon-{{ data.id }} .jwpf-dropcap .jwpf-addon-content:first-letter {
				color: {{ data.dropcap_color }};
				<# if(_.isObject(data.dropcap_font_size)){ #>
					font-size: {{data.dropcap_font_size.md}}px;
					line-height: {{data.dropcap_font_size.md}}px;
				<# } else { #>
					font-size: {{data.dropcap_font_size}}px;
					line-height: {{data.dropcap_font_size}}px;
				<# } #>
			}

			#jwpf-addon-{{ data.id }}.jwpf-addon-content{
				word-wrap: break-word;
				word-break: break-all;
			}
			#jwpf-addon-{{ data.id }} .jwpf-indent .jwpf-addon-content{
				text-indent:2em;
			}

			<# if(column==1){ #>
			    #jwpf-addon-{{ data.id }} .jwpf-addon-title{
                    margin: {{column_title_margin}};
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                    writing-mode: {{writing_mode}};
                }
                <# if(writing_mode=="vertical-lr"){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                    #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                        float: left;
                    }
                <# }else if(writing_mode=="vertical-rl"){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-addon-title,
                    #jwpf-addon-{{ data.id }} .jwpf-addon-content{
                        float: right;
                    }
                <# } #>
			<# } #>

			@media (min-width: 768px) and (max-width: 991px) {
				#jwpf-addon-{{ data.id }}{
					<# if(_.isObject(data.text_fontsize)){ #>
						font-size: {{ data.text_fontsize.sm }}px;
					<# } #>

					<# if(_.isObject(data.text_lineheight)){ #>
						line-height: {{ data.text_lineheight.sm }}px;
					<# } #>
				}
				#jwpf-addon-{{ data.id }} .jwpf-dropcap .jwpf-addon-content:first-letter {
					<# if(_.isObject(data.dropcap_font_size)){ #>
						font-size: {{data.dropcap_font_size.sm}}px;
						line-height: {{data.dropcap_font_size.sm}}px;
					<# } #>
				}
			}
			@media (max-width: 767px) {
				#jwpf-addon-{{ data.id }}{
					<# if(_.isObject(data.text_fontsize)){ #>
						font-size: {{ data.text_fontsize.xs }}px;
					<# } #>

					<# if(_.isObject(data.text_lineheight)){ #>
						line-height: {{ data.text_lineheight.xs }}px;
					<# } #>
				}
				#jwpf-addon-{{ data.id }} .jwpf-dropcap .jwpf-addon-content:first-letter {
					<# if(_.isObject(data.dropcap_font_size)){ #>
						font-size: {{data.dropcap_font_size.xs}}px;
						line-height: {{data.dropcap_font_size.xs}}px;
					<# } #>
				}
			}
		</style>
        <# if(text_from == 1 && text_id) { #>
            <div class="jwpf-addon jwpf-addon-text-block">
                <#
                let heading_selector = data.heading_selector || "h3";
                if( !_.isEmpty( data.title ) ){
                #>
                    <{{ heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{{ data.title }}}</{{ heading_selector }}>
                <# } #>
                <# if(text_id) { #>
                <div class="jwpf-addon-content jw-editable-content" style="padding:15px 0px;">客户端数据请在预览页查看</div>
                <# } else { #>
                <div id="addon-text-{{data.id}}" class="jwpf-addon-content jw-editable-content" data-id={{data.id}} data-fieldName="text">{{{ data.text }}}</div>
                <# } #>
            </div>
        <# } else { #>
            <div class="jwpf-addon jwpf-addon-text-block {{ dropcap }} {{ data.alignment }} {{ data.class }} {{indent}}">
                <#
                let heading_selector = data.heading_selector || "h3";
                if( !_.isEmpty( data.title ) ){
                #>
                    <{{ heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{{ data.title }}}</{{ heading_selector }}>
                <# } #>
                <div id="addon-text-{{data.id}}" class="jwpf-addon-content jw-editable-content" data-id={{data.id}} data-fieldName="text">{{{ data.text }}}</div>
            </div>
        <# } #>
		';
		return $output;
	}
}
