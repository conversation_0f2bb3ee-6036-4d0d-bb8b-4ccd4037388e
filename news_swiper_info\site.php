<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonNews_swiper_info extends JwpagefactoryAddons
{
    //在预览页面中渲染
    public function render()
    {
        $settings = $this->addon->settings;

        //此处载入所有配置项变量
        //$class = (isset($settings->class) && $settings->class) ? $settings->class : '';

        //配置项变量
        $color1615443385649 = (isset($settings->color1615443385649) && $settings->color1615443385649) ? $settings->color1615443385649 : '';
        $select1615452416171 = (isset($settings->select1615452416171) && $settings->select1615452416171) ? $settings->select1615452416171 : '';
        $color1615452944411 = (isset($settings->color1615452944411) && $settings->color1615452944411) ? $settings->color1615452944411 : '';
        $color1615453171634 = (isset($settings->color1615453171634) && $settings->color1615453171634) ? $settings->color1615453171634 : '';
        $font_size = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '';
        $font_size_date = (isset($settings->font_size_date) && $settings->font_size_date) ? $settings->font_size_date : '';
        $pageColor = (isset($settings->pageColor) && $settings->pageColor) ? $settings->pageColor : '';
        $pageBorderColor = (isset($settings->pageBorderColor) && $settings->pageBorderColor) ? $settings->pageBorderColor : '';
        $pageBgColor = (isset($settings->pageBgColor) && $settings->pageBgColor) ? $settings->pageBgColor : '';
        $pageColorhover = (isset($settings->pageColorhover) && $settings->pageColorhover) ? $settings->pageColorhover : '';
        $pageBorderColorhover = (isset($settings->pageBorderColorhover) && $settings->pageBorderColorhover) ? $settings->pageBorderColorhover : '';
        $pageBgColorhover = (isset($settings->pageBgColorhover) && $settings->pageBgColorhover) ? $settings->pageBgColorhover : '';

        $company_id = $_GET['company_id'] ?? '';
        $site_id = $_GET['site_id'] ?? '';
        $catid_id = $_GET['catid_id'] ?? '';
        $layout_id = $_GET['layout_id'] ?? '';
        //获取文章详情的数据源


        $app = JFactory::getApplication();
        $input = $app->input;

        $article_id = $input->get('detail');
        $detail_id = base64_decode($input->get('id'));
        $article = JwPageFactoryBase::getNoticeById($article_id,$catid_id);
//        $on = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->on."&Itemid=0&company_id=".$company_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&layout_id='.$layout_id,$absolute=true);
//        $down = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".base64_encode($detail_id)."&detail=".$article->down."&Itemid=0&company_id=".$company_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&layout_id='.$layout_id,$absolute=true);
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $output = '';
        $output .= '';
        $output .= '<style>';
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT { color:' . $color1615452944411 . '; }';
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img{ display:inline-block !important;}';
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT h2{ font-size:' . $font_size . 'px; }';
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT p{ font-size:' . $font_size_date . 'px; }';
        if ($article->on) {
            $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox{  display:flex;justify-content:space-between;padding:20px; }';
        }else {
            $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox{  display:flex;justify-content:flex-end;padding:20px; }';
        }
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a{     width: 120px; height: 40px; border: 1px solid '.$pageBorderColor.';color: '.$pageColor.';font-size: 14px;background:'.$pageBgColor.';text-align: center;line-height: 40px;text-decoration: none;}' ;
        $output .= '	' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT .btnBox a:hover{     width: 120px; height: 40px; border: 1px solid '.$pageBorderColorhover.';color: '.$pageColorhover.';font-size: 14px;background:'.$pageBgColorhover.';text-align: center;line-height: 40px;text-decoration: none;}' ;

        $output .= '</style>';
        $output .= '<style>
                            @media (max-width: 991px) {
                             ' . $addon_id . ' #JZT_ADDON_LSGA_4D2L2F02N8_TXT img{ display:inline-block !important;width:100% !important;height:100% !important;}
                            }
                    </style>';
        $output .= '<div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">';
        $output .= '	<h2 style="text-align:' . $select1615452416171 . '">' . $article->title . '</h2>';
        $output .= '   <hr />';
        $output .= '	<div style="color:' . $color1615443385649 . '">';
        $output .= '    ' . $article->fulltext . '';
        $output .= '	</div>';
//        $output .= '	<div class="btnBox">';
//        if ($article->on) {
//            $output .= '<a href="'.$on.'">上一篇</a>';
//        }
//        if ($article->down) {
//            $output .= '<a href="'.$down.'">下一篇</a>';
//        }
//        $output .= '	</div>';
        $output .= '</div>';
        $output .= '<script>';
        $output .= '';
        $output .= '</script>';
        $output .= '';


        return $output;
    }

    //在预览页面中使用
    public function scripts()
    {
        //可以逗号分隔以引用多个js文件
        $scripts = array(//JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.countdown.min.js'

        );
        return $scripts;
    }

    //在预览页面中使用的css样式
    public function css()
    {
        $css = '';
        return $css;
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output = '';
        $output .= '
        <#
        var addonId = "jwpf-addon-"+data.id;
        #>';
        $output .= '<style>';
        $output .= '	#{{ addonId }} #JZT_ADDON_LSGA_4D2L2F02N8_TXT { color:{{{ data.color1615452944411 }}}; }';
        $output .= '	#{{ addonId }} .btnBox { display:flex;justify-content:space-between;padding:20px;}';
        $output .= '	#{{ addonId }} .btnBox a {     width: 120px; height: 40px; border: 1px solid {{data.pageBorderColor}};color: {{data.pageColor}};font-size: 14px;background:{{data.pageBgColor}}; text-align: center;line-height: 40px;text-decoration: none;}';
        $output .= '	#{{ addonId }} .btnBox a:hover {     width: 120px; height: 40px; border: 1px solid {{data.pageBorderColorhover}};color: {{data.pageColorhover}};font-size: 14px;background:{{data.pageBgColorhover}};text-align: center;line-height: 40px;text-decoration: none;}';
        $output .= '</style>';
        $output .= '<div id="JZT_ADDON_LSGA_4D2L2F02N8_TXT">';
        $output .= '	<h2 style="text-align:{{{ data.select1615452416171 }}};font-size: {{data.font_size}}px;">示例文章标题</h2>';
        $output .= '   <hr />';
        $output .= '	<p style="text-align:right; display:block; padding-right:10px; color:{{{ data.color1615453171634 }}};;font-size: {{data.font_size_date}}px;">编辑：2021年3月11日 16:59</p>';
        $output .= '	<div style="color:{{{ data.color1615443385649 }}}">';
        $output .= '    示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文，示例文章正文。';
        $output .= '	</div>';
//        $output .= '<div class="btnBox">';
//        $output .= '<a href="#">上一篇</a>';
//        $output .= '<a href="#">下一篇</a>';
//        $output .= '</div>';
        $output .= '</div>';
        $output .= '<script>';
        $output .= '';
        $output .= '</script>';
        $output .= '';


        return $output;
    }

}