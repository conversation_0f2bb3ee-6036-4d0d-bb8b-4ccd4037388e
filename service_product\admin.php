<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'service_product', 
        'title' => JText::_('自定义产品服务'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '产品',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'service_product_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                        'type2' => '布局2',
                        'type3' => '布局3',
                        'type4' => '布局4',
                        'type5' => '布局5',
                        'type6' => '布局6',
                        'type7' => '布局7',
                        'type8' => '布局8',
                        'type9' => '布局9',
                        'type10' => '手机布局10',
                        'type11' => '布局11',
                        'type12' => '布局12',
                        'type13' => '布局13',
                        'type14' => '布局14',
                        'type15' => '布局15',
                        'type16' => '布局16',
                    ),
                    'std' => 'type1',
                ),
                // Repeatable Item
                'jw_tab_item_goods' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题',
                        ),
                        'number_img' => array(
                            'type' => 'media',
                            'title' => JText::_('序号图标'),
                            'desc' => JText::_('序号图标'),
                            'std' => '',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        'goods_desc' => array(
                            'title' => JText::_('自定义小标题列表'),
                            'attr' => array(
                                'title_list' => array(
                                    'type' => 'text',
                                    'title' => JText::_('小标题'),
                                    'desc' => JText::_('小标题'),
                                    'std' => '小标题',
                                ),
                                'd_page_type' => array(
                                    'type' => 'select',
                                    'title' => JText::_('跳转方式'),
                                    'desc' => JText::_('跳转方式'),
                                    'values' => array(
                                        'Internal_pages' => JText::_('内部页面'),
                                        'external_links' => JText::_('外部链接'),
                                    ),
                                    'std' => 'Internal_pages',
                                ),
                                'd_page_id' => array(
                                    'type' => 'select',
                                    'title' => '选择跳转页面',
                                    'desc' => '',
                                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                                    'depends' => array(array('d_page_type', '=', 'Internal_pages')),
                                ),
                                'd_page' => array(
                                    'type' => 'text',
                                    'title' => '跳转链接',
                                    'depends' => array(array('d_page_type', '=', 'external_links')),
                                ),
                                'd_target' => array(
                                    'type' => 'select',
                                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                                    'values' => array(
                                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                                    ),
                                ),
                            ),
                            'std' => '',
                        ),
                        'hover_content_top' => array(
                            'type' => 'slider',
                            'title' => '鼠标移入内容顶部距离（%）',
                            'max' => 100,
                            'min' => 0,
                            'std' => '',
                        )
                    ),
                    'std' => array(
                        array(
                            'title' => '网页视觉',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/00e867888885d52104ece012914587fa.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/51276cabac8932cea5d24e69a6b804fb.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '品牌官网',
                                ),
                                array(
                                    'title_list' => '企业网站',
                                ),
                                array(
                                    'title_list' => 'HTML5响应式',
                                ),
                                array(
                                    'title_list' => '电商详情页',
                                ),
                            ),
                        ),
                        array(
                            'title' => 'APP开发',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/51d8c22fc2db311b1b2248e627d7ed31.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/8b8613acfe2fc6329ff2ad89c54c55b0.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => 'IOS端',
                                ),
                                array(
                                    'title_list' => 'Android端',
                                ),
                            ),
                        ),
                        array(
                            'title' => '小程序开发',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/683baa6f3dc4b5499ef91f6695fbf6d7.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/a85aa9f9db91d8df9c182722ba25c33d.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '微信小程序',
                                ),
                                array(
                                    'title_list' => '百度小程序',
                                ),
                            ),
                        ),
                        array(
                            'title' => '行业解决方案',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'number_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/590d18b5f5621eff4938b16ef0a066bc.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220513/7deabcb256b9af0a2261b0b8841b8c43.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '电商管理系统解决方案',
                                ),
                                array(
                                    'title_list' => '无感知考勤系统解决方案',
                                ),
                                array(
                                    'title_list' => '人力资源系统解决方案',
                                ),
                                array(
                                    'title_list' => '网上教学系统解决方案',
                                ),
                                array(
                                    'title_list' => '医疗挂号系统解决方案',
                                ),
                            ),
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_two' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题',
                        ),
                        'goods_desc' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介'),
                            'desc' => JText::_('产品简介'),
                            'std' => '简介',
                        ),
                        'button_title' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮标题'),
                            'desc' => JText::_('按钮标题'),
                            'std' => '按钮标题',
                        ),
                        'button_img' => array(
                            'type' => 'media',
                            'title' => JText::_('按钮图标'),
                            'desc' => JText::_('按钮图标'),
                            'std' => '',
                        ),
                        'big_img_pc' => array(
                            'type' => 'media',
                            'title' => JText::_('pc背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                        'big_img_sj' => array(
                            'type' => 'media',
                            'title' => JText::_('手机背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '百度APP',
                            'button_title' => '百度APP',
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/1034b487132013a2a16895a33bbc1188.png',
                            'big_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/020e9beb9434dde5ba1698de612eaedf.jpeg',
                            'big_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/62ded3beb5b2586d190a47a1133843c8.jpeg',
                            'goods_desc' => '您的广告将会展现在百度APP信息流资讯当中',
                        ),
                        array(
                            'title' => '百度贴吧',
                            'button_title' => '百度贴吧',
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/1d9f71d4157848683cd41e7fd29dea5c.png',
                            'big_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/ef0b11a26bcfd9a1f70a6d712b6b2580.jpeg',
                            'big_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/c445d574a3a0b87680d42124f582d2d9.jpeg',
                            'goods_desc' => '您的广告将会展现在贴吧推荐信息流和帖子信息流当中',
                        ),
                        array(
                            'title' => '手机网页版百度首页',
                            'button_title' => '手机网页版',
                            'button_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/1034b487132013a2a16895a33bbc1188.png',
                            'big_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/05132c6100566d8ef308bb03ebcc01cb.jpeg',
                            'big_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/4a9960aed987dd2646d6ab1d59141440.jpeg',
                            'goods_desc' => '您的广告将会展现在移动端百度首页推荐信息流当中',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type2')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_three' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题',
                        ),
                        'goods_desc' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介'),
                            'desc' => JText::_('产品简介'),
                            'std' => '简介',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => JText::_('图标'),
                            'desc' => JText::_('图标'),
                            'std' => '',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '影院场景',
                            'goods_desc' => '社区电子屏 | 门禁屏 | 超市屏',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/0d134878b813c1656caf0218a1e9afb4.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/276b68d54dae10c368d246983f02857f.png',
                        ),
                        array(
                            'title' => '楼宇场景',
                            'goods_desc' => '社区电子屏 | 门禁屏 | 超市屏',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/affbd71394dbd664a74ee8844937ccfd.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/16c0400c37d2d44d5b3dc879d38e66a4.png',
                        ),
                        array(
                            'title' => '生活服务场景',
                            'goods_desc' => '社区电子屏 | 门禁屏 | 超市屏',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/53a69534547390059bd271d9b2c3247c.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/55ae543f0ba9a34e42440ef00b67f3fe.png',
                        ),
                        array(
                            'title' => '出行场景',
                            'goods_desc' => '社区电子屏 | 门禁屏 | 超市屏',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/9772f686145b73ac0fa53153953a1629.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/e948d9b5e80a7d2031668bdf6bed92fc.png',
                        ),
                        array(
                            'title' => '家庭场景',
                            'goods_desc' => '社区电子屏 | 门禁屏 | 超市屏',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/ab04a7490f9247db5baf4dd7f0798416.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/fa1318f868217166f57894c445efc2da.png',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type3')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_four' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title_one' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题1'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题1',
                        ),
                        'title_two' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题2'),
                            'desc' => JText::_('产品简介'),
                            'std' => '标题2',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'desc' => JText::_('图片'),
                            'std' => '',
                        ),
                        'title_three' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题3'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题3',
                        ),
                        'title_four' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题4'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题4',
                        ),
                        'title_five' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题5'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题5',
                        ),
                        'title_six' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题6'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题6',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title_one' => '游戏/APP',
                            'title_two' => '行业媒体包',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/8fe1cc07dce1e2d3166d06ed8022e03d.jpeg',
                            'title_three' => '优化目标：',
                            'title_four' => '激活/下载提升',
                            'title_five' => '建议出价范围：',
                            'title_six' => '0.28~0.91',
                        ),
                        array(
                            'title_one' => '金融行业',
                            'title_two' => '媒体包',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/a67b78e437acc8a0dc6688e9365b996a.jpeg',
                            'title_three' => '优化目标：',
                            'title_four' => '激活/下载提升',
                            'title_five' => '建议出价范围：',
                            'title_six' => '0.25~0.91',
                        ),
                        array(
                            'title_one' => '教育行业',
                            'title_two' => '媒体包',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/061025e2a343b27a8ec305d2e5d56b84.jpeg',
                            'title_three' => '优化目标：',
                            'title_four' => '激活/下载提升',
                            'title_five' => '建议出价范围：',
                            'title_six' => '0.2~0.61',
                        ),
                        array(
                            'title_one' => '家居行业',
                            'title_two' => '媒体包',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/4d10c24acac7d65dbe8322bc5627cccf.jpeg',
                            'title_three' => '优化目标：',
                            'title_four' => '激活/下载提升',
                            'title_five' => '建议出价范围：',
                            'title_six' => '0.25~0.91',
                        ),
                        array(
                            'title_one' => '女性优质',
                            'title_two' => '媒体包',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/820bb63a76f7a76e5c8c0d07d23c7214.jpeg',
                            'title_three' => '优化目标：',
                            'title_four' => '激活/下载提升',
                            'title_five' => '建议出价范围：',
                            'title_six' => '0.22~0.71',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type4')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_five' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'content' => array(
                            'type' => 'text',
                            'title' => '产品简介（换行用<br>）',
                            'desc' => JText::_('产品简介'),
                            'std' => '产品简介',
                        ),
                        'icon_pc' => array(
                            'type' => 'media',
                            'title' => JText::_('pc图标'),
                            'desc' => JText::_('pc图标'),
                            'std' => '',
                        ),
                        'icon_sj' => array(
                            'type' => 'media',
                            'title' => JText::_('手机端图片'),
                            'desc' => JText::_('手机端图片'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '代码规范',
                            'content' => '可减少bug，易于团队合作，易于<br>二次开发，降低维护成本',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/4ebba91351cfecc9aefa5b6fda3961ee.png',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/a5d2608b50246934467f7828ceb796ba.png',
                        ),
                        array(
                            'title' => '开发模式',
                            'content' => '畅快的用户体验，性能稳定，上手流畅，<br>访问本地资源，带来APP愉悦体验',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/dd544eb90f2aa02298f0b37d16b4bf28.png',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/dccc5e37a67b45aeb1c3b178c325297a.png',
                        ),
                        array(
                            'title' => '开发效率高',
                            'content' => '模板化，可扩展、维护、协作开<br>发，一套代码适用IOS和Android开发',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/868249afafefa82fe3af5f47bab3ecb6.png',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/942228a6a9b9fe96ee9d069d79a514a7.png',
                        ),
                        array(
                            'title' => '完善的商城API',
                            'content' => '移动端应用提供很好的接口，更好的支持<br>系统扩展，利于二次开发',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/bffddb4643cb75baf2a59983f790f0d5.png',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/363f22bff24d3112ffd0d88366a38a61.png',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type5')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_six' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => JText::_('图标'),
                            'desc' => JText::_('图标'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '客户体验',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/484f0f47eb52133769946c559d8e3fee.png',
                        ),
                        array(
                            'title' => '视觉设计',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/5d7ded1d2de2da0568ef01e8f2033577.png',
                        ),
                        array(
                            'title' => '产品研发',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/29c992aae302af0bca29ecc1e1e57f9e.png',
                        ),
                        array(
                            'title' => '软件测试',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/9f3319b1e862dca7721c2bab27363686.png',
                        ),
                        array(
                            'title' => '上线运营',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/b4d2371c57d34b4c41179a8d5cfd54e3.png',
                        ),
                        array(
                            'title' => '合同签订',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/d1c3c1a7d3648d65c7cf5747efa8aebb.png',
                        ),
                        array(
                            'title' => '资讯',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/1be9bee1d7699579e4237ac6b12640c6.png',
                        ),
                        array(
                            'title' => '原型设计',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/a61b4148457f1698e394c3cede2056d7.png',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type6')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_seven' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'icon_img_pc' => array(
                            'type' => 'media',
                            'title' => JText::_('pc图片'),
                            'desc' => JText::_('图片'),
                            'std' => '',
                        ),
                        'icon_img_sj' => array(
                            'type' => 'media',
                            'title' => JText::_('手机端图片'),
                            'desc' => JText::_('图片'),
                            'std' => '',
                        ),
                        'content' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介'),
                            'desc' => JText::_('产品简介'),
                            'std' => '产品简介',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '互联网媒体管理',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/349df03ebffc33ca4cb5f3a4042388d8.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/5a19e67c70f0af175e92a2eda4fcd144.png',
                            'content' => '绑定企业公众号、百家号等平台，实现内容一站分发。',
                        ),
                        array(
                            'title' => '网站建设',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/2532166fd3f26c63a7477964de8ac489.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/fdf84e6c7c7445a1e67f0946951ce714.png',
                            'content' => '海量模板，快速实现网站搭建。',
                        ),
                        array(
                            'title' => '域名及IDC管理',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/ce82e17a7390b501cd6d151de5c3b054.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/e580a7fa8e0825b3c73c241b296315f0.png',
                            'content' => '全球ICANN、CNNIC顶级域名注册服务机构，拥有多个数据存储中心。',
                        ),
                        array(
                            'title' => '素材库智能管理',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/31f352fe273e635af7e8471e68e76bf5.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/1239d6115fb53092cc8e99370cff9247.png',
                            'content' => '支持文档、图片、视频等多类素材文件的备份、查看、编辑等操作。',
                        ),
                        array(
                            'title' => '云经理APP内容分享',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/9849a53d79b7b1c31f22288c44e27159.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/b0d386a8a6af01c5dfb0281ca07ee142.png',
                            'content' => '通过云经理随时查看素材库资源，一键分享。',
                        ),
                        array(
                            'title' => '多端同步',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/66de3a749423307216d23536e5e17a23.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/4ece7d3c6791d7399ea14a28c940f1c8.png',
                            'content' => '一站多屏，一站多端，全网布局，即刻实现。',
                        ),
                        array(
                            'title' => '网站内容发布管理',
                            'icon_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/467a8c981c8f855c3ffc40b0b74ee1d9.jpeg',
                            'icon_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/14806f68ae9f835849214067007ed7f1.png',
                            'content' => '支持国内/国际服务器内容部署，自动化部署更灵活迅速。',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type7')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_eight' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题',
                        ),
                        'icon_img' => array(
                            'type' => 'media',
                            'title' => JText::_('图标'),
                            'desc' => JText::_('图标'),
                            'std' => '',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('按钮跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择按钮跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '按钮跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        'goods_desc' => array(
                            'title' => JText::_('自定义小标题列表'),
                            'attr' => array(
                                'title_list' => array(
                                    'type' => 'text',
                                    'title' => JText::_('小标题'),
                                    'desc' => JText::_('小标题'),
                                    'std' => '小标题',
                                ),
                                'tz_page_type_s' => array(
                                    'type' => 'select',
                                    'title' => JText::_('图标跳转方式'),
                                    'desc' => JText::_('跳转方式'),
                                    'values' => array(
                                        'Internal_pages' => JText::_('内部页面'),
                                        'external_links' => JText::_('外部链接'),
                                    ),
                                    'std' => 'Internal_pages',
                                ),
                                'detail_page_id_s' => array(
                                    'type' => 'select',
                                    'title' => '选择图标跳转页面',
                                    'desc' => '',
                                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                                    'depends' => array(array('tz_page_type_s', '=', 'Internal_pages')),
                                ),
                                'detail_page_s' => array(
                                    'type' => 'text',
                                    'title' => '图标跳转链接',
                                    'depends' => array(array('tz_page_type_s', '=', 'external_links')),
                                ),
                                'target_s' => array(
                                    'type' => 'select',
                                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                                    'values' => array(
                                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                                    ),
                                ),
                            ),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '功能文档',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '',
                            'detail_page_id' => 0,
                            'icon_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/66f1f90efc7c5e3de4c1e03653d71ff6.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '综合医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '妇科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '儿童医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '男科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '美容整形功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                            ),
                        ),
                        array(
                            'title' => 'PPT介绍',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '',
                            'detail_page_id' => 0,
                            'icon_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/38db7712460375fd06477fef11ca3af5.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '综合医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '妇科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '儿童医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '男科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '美容整形功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                            ),
                        ),
                        array(
                            'title' => '医疗DM单',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '',
                            'detail_page_id' => 0,
                            'icon_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/4561e75cff9d6961bc6d0b03b66fec1a.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '综合医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '妇科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '儿童医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '男科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '美容整形功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                            ),
                        ),
                        array(
                            'title' => '宣传册',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '',
                            'detail_page_id' => 0,
                            'icon_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/daef6f00df2360fa1b190e5b2d5e8566.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '综合医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '妇科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '儿童医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '男科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '美容整形功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                            ),
                        ),
                        array(
                            'title' => '报价表',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                            'target' => '',
                            'detail_page_id' => 0,
                            'icon_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/c2eb61585d6d76f165baa71b944ee678.png',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '综合医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '妇科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '儿童医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '男科医院功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                                array(
                                    'title_list' => '美容整形功能文档',
                                    'tz_page_type_s' => 'external_links',
                                    'detail_page_id_s' => 0,
                                    'detail_page_s' => '#',
                                    'target_s' => '',
                                ),
                            ),
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type8')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_nine' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'content' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介'),
                            'desc' => JText::_('产品简介'),
                            'std' => '产品简介',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '技术方面',
                            'content' => 'his挂号管理系统是基于云服务器部署，采用互联网、B/S架构，支持业界公认关系型数据库MySQL，具有安全性高、稳定性强、支持数据仓库等特点，保证了医疗机构数据的安全性问题，公司提供专业的售后服务支持，协助医疗机构快速实现信息化管理，全程一对一安装培训，系统服务热线7×12小时，无需再担心技术培训问题。',
                        ),
                        array(
                            'title' => '数据方面',
                            'content' => '系统整合了基层医疗机构常见疾病的治疗经验，对包括主诉、简要病史、诊断结果、处方药品、医嘱等进行大数据提取和分析，导入系统经典处方模板库、药品知识库等，让基层医生在使用电子处方、病历系统过程中，一方面可以学习专家经验，降低风险；另一方面可以勾选操作，减少打字输入，从而缩短看诊等待时间，提升用户体验。',
                        ),
                        array(
                            'title' => '业务管理方面',
                            'content' => '从挂号登记到药房发药一站式服务，整个流程紧密衔接，数据资料传递及时；每个功能模块处设有访问权限，不同权限的医务人员可查询不同的财务数据；客户回访功能可维护医患关系，通过对客户做不定期回访，避免丢失患者；多维度报表分析，助力中医馆实现数据化、精细化管理。',
                        ),
                        array(
                            'title' => '宣传方面',
                            'content' => '官网宣传，品牌推广宣传、落地页宣传。',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type9')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_eleven' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                        'goods_desc' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品简介',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '公司注册',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/3e8ce5cba3d2c11c8760073f85923749.jpeg',
                            'goods_desc' => '免费代办公司',
                        ),
                        array(
                            'title' => '代理记账',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/0d24340c0c6ceb7018145056bc842e06.jpeg',
                            'goods_desc' => '专业会计，安心代账',
                        ),
                        array(
                            'title' => '工商变更',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/b67e30a714a14fe11f5cedece9a69b6a.jpeg',
                            'goods_desc' => '急速办理，便捷省心',
                        ),
                        array(
                            'title' => '公司注销',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/930c2948b714b8be2c10c895bdbc816a.jpeg',
                            'goods_desc' => '专人专办，省时放心',
                        ),
                        array(
                            'title' => '行政审批',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/f34ea37b7178a7714173af38de1f1768.jpeg',
                            'goods_desc' => '专业办理，方便快捷',
                        ),
                        array(
                            'title' => '建筑资质',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220627/caa566ae34b8a71e2a6472af2bb67c07.jpeg',
                            'goods_desc' => '省心/省钱的全程服务',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type11')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_twelve' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题'),
                            'desc' => JText::_('产品标题'),
                            'std' => '产品标题',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('背景图片'),
                            'desc' => JText::_('背景图片'),
                            'std' => '',
                        ),
                        'title_one' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题1'),
                            'desc' => JText::_('产品标题1'),
                            'std' => '产品标题1',
                        ),
                        'goods_desc_one' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介1'),
                            'desc' => JText::_('产品简介1'),
                            'std' => '产品简介1',
                        ),
                        'title_two' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题2'),
                            'desc' => JText::_('产品标题2'),
                            'std' => '产品标题2',
                        ),
                        'goods_desc_two' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介2'),
                            'desc' => JText::_('产品简介2'),
                            'std' => '产品简介2',
                        ),
                        'title_three' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题3'),
                            'desc' => JText::_('产品标题3'),
                            'std' => '产品标题3',
                        ),
                        'goods_desc_three' => array(
                            'type' => 'text',
                            'title' => JText::_('产品简介3'),
                            'desc' => JText::_('产品简介3'),
                            'std' => '产品简介3',
                        ),
                        'title_four' => array(
                            'type' => 'text',
                            'title' => JText::_('产品标题4'),
                            'desc' => JText::_('产品标题4'),
                            'std' => '产品标题4',
                        ),
                        'number_one' => array(
                            'type' => 'text',
                            'title' => JText::_('服务数量1'),
                            'desc' => JText::_('服务数量1'),
                            'std' => '服务数量1',
                        ),
                        'number_desc_one' => array(
                            'type' => 'text',
                            'title' => JText::_('服务数量简介1'),
                            'desc' => JText::_('服务数量简介1'),
                            'std' => '服务数量简介1',
                        ),
                        'number_two' => array(
                            'type' => 'text',
                            'title' => JText::_('服务数量2'),
                            'desc' => JText::_('服务数量2'),
                            'std' => '服务数量2',
                        ),
                        'number_desc_two' => array(
                            'type' => 'text',
                            'title' => JText::_('服务数量2'),
                            'desc' => JText::_('服务数量2'),
                            'std' => '服务数量2',
                        ),
                        'button_text' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮文字'),
                            'desc' => JText::_('按钮文字'),
                            'std' => '按钮文字',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '安平县德祥瑞网业有限公司',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/db747229016b768302a6e36964eb877f.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '生产、销售：不锈钢网、金刚网、护栏网、六角网、冲孔网、钢板网、电焊网、格宾网、钢格板、刺绳等。',
                            'title_four' => '企业数据',
                            'number_one' => '5000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '100+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '山西省平遥牛肉集团有限公司',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/8c45d7522ed613e2ff1abe4a9e598c72.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '平遥牛肉、冠云牛肉、冠云、平遥冠云牛肉、定制牛肉、熟食、牛肉食材等。',
                            'title_four' => '企业数据',
                            'number_one' => '4000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '200+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '忻州安康眼科医院',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/0ef71a792d6a44fd097c5db523c39fd9.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '医院、眼科、医疗设备等。',
                            'title_four' => '企业数据',
                            'number_one' => '3000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '100+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '帝仪科技',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/f7581bdc4d51c92d8d6bc2c35765bad7.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '帝仪科技、人工智能、脑电、脑机接口、智慧养老、智能交通、智能硬件、生物信息等。',
                            'title_four' => '企业数据',
                            'number_one' => '6000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '200+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '汾力特酒业',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/e8b453a1e5d9bd7d6514473023f1c7a3.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '杏花酒、汾力特、清香型白酒等。',
                            'title_four' => '企业数据',
                            'number_one' => '3000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '100+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '春秋晋国城旅游',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220628/88fa6ee4ecc0e2b0292d142af0ebcf99.png',
                            'title_one' => '购买产品',
                            'goods_desc_one' => '年费会员',
                            'title_two' => '入驻诉求',
                            'goods_desc_two' => '产品展示与销售',
                            'title_three' => '主营业务',
                            'goods_desc_three' => '春秋晋国城、山西曲沃、晋国故地、浍河湖畔、春秋五霸、历代兵家之地、旅游开发、历史影视宫、清王府建筑群等。',
                            'title_four' => '企业数据',
                            'number_one' => '1000+',
                            'number_desc_one' => '平台日展现',
                            'number_two' => '50+',
                            'number_desc_two' => '商品日点击',
                            'button_text' => '了解更多',
                            'tz_page_type' => 'external_links',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type12')),
                ),
                // Repeatable Item
                'jw_tab_item_goods_thirteen' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'desc' => JText::_('图片'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/73d3f2bf59075bee96b6ee436ebe417d.jpeg',
                        ),
                        array(
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/9bfaa2188a455fa31f9bcb73c85c86ee.jpeg',
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type13')),
                ),
                'toufang_font' => array(
                    'type' => 'text',
                    'title' => JText::_('投放按钮文字'),
                    'desc' => JText::_('投放按钮文字'),
                    'std' => '开始投放',
                    'depends' => array(array('service_product_type', '=', 'type2')),
                ),
                'toufang_url' => array(
                    'type' => 'text',
                    'title' => JText::_('投放路径'),
                    'desc' => JText::_('投放路径'),
                    'std' => 'https://p.qiao.baidu.com/cps/chat?siteId=689930&amp;userId=2816398&amp;siteToken=593d428f846f036119c10b4fd588d20a',
                    'depends' => array(array('service_product_type', '=', 'type2')),
                ),
                'hover_bgcolor_type3' => array(
                    'type' => 'color',
                    'title' => JText::_('滑入背景颜色'),
                    'depends' => array(array('service_product_type', '=', 'type3')),
                    'std' => '#d9012a'
                ),
                'toufang_font_color_type3' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'depends' => array(array('service_product_type', '=', 'type3')),
                    'std' => '#fff'
                ),
                'toufang_border_color_type3' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'depends' => array(array('service_product_type', '=', 'type3')),
                    'std' => '#fff'
                ),
                'toufang_font_type3' => array(
                    'type' => 'text',
                    'title' => JText::_('投放按钮文字'),
                    'desc' => JText::_('投放按钮文字'),
                    'std' => '开始投放',
                    'depends' => array(array('service_product_type', '=', 'type3')),
                ),
                'background_img' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/05171d7d80a2933fec9dec6f751a7424.png',
                    'depends' => array(array('service_product_type', '=', 'type5')),
                ),
                'background_img_type6' => array(
                    'type' => 'media',
                    'title' => JText::_('pc背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/f594ac3fd9eee028864699943d38839f.png',
                    'depends' => array(array('service_product_type', '=', 'type6')),
                ),
                'background_img_sj_type6' => array(
                    'type' => 'media',
                    'title' => JText::_('手机端背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/338dacfd82e09bd12308b5193efb5693.png',
                    'depends' => array(array('service_product_type', '=', 'type6')),
                ),
                'background_img_type7' => array(
                    'type' => 'media',
                    'title' => JText::_('pc背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/aeffa7d711b34ccfd624ddfea2318c12.jpeg',
                    'depends' => array(array('service_product_type', '=', 'type7')),
                ),
                'background_img_sj_type7' => array(
                    'type' => 'media',
                    'title' => JText::_('手机端背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/82ca2ccb4f16c202fdb5f8fdb1074930.jpeg',
                    'depends' => array(array('service_product_type', '=', 'type7')),
                ),
                'background_img_type9' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220523/69994cf9a6ff1c56ed8a665a4426b8e5.jpeg',
                    'depends' => array(array('service_product_type', '=', 'type9')),
                ),
                'background_img_type13' => array(
                    'type' => 'media',
                    'title' => JText::_('背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220708/97841dd58c91a88de58c4478f1f7df72.png',
                    'depends' => array(array('service_product_type', '=', 'type5')),
                ),
                'title_type13' => array(
                    'type' => 'text',
                    'title' => JText::_('标题'),
                    'desc' => JText::_('标题'),
                    'std' => '门店',
                    'depends' => array(array('service_product_type', '=', 'type13')),
                ),
                'content_type13' => array(
                    'type' => 'text',
                    'title' => JText::_('简介'),
                    'desc' => JText::_('简介'),
                    'std' => '线上线下一体化售卖，在全国省会城市打造2-3家直营店，内设咖啡区、冷饮区，提升品牌形象。打造样板市场树立马迭尔形象、弘扬马迭尔文化。',
                    'depends' => array(array('service_product_type', '=', 'type13')),
                ),
                'buju_style_type13' => array(
                    'type' => 'select',
                    'title' => JText::_('样式布局'),
                    'values' => array(
                        'type1' => '图片在左',
                        'type2' => '图片在右',
                    ),
                    'std' => 'type1',
                    'depends' => array(array('service_product_type', '=', 'type13')),
                ),
                // 布局11配置项
                'background_img_tian' => array(
                    'type' => 'select',
                    'title' => JText::_('背景图片填充方式'),
                    'values' => array(
                        'z1' => '占满不切割',
                        'z2' => '占满切割显示',
                    ),
                    'std' => 'z1',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                ),
                'background_img_round_type11' => array(
                    'type' => 'slider',
                    'title' => '背景图圆角',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                ),
                'background_img_height_type11' => array(
                    'type' => 'slider',
                    'title' => '背景图高度(单位px)',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                    'max' => 1000,
                    'min' => 0,
                    'std' => array('md' => 560, 'sm' => 450, 'xs' => 0),
                    'responsive' => true,
                ),
                'box_gap_type11' => array(
                    'type' => 'slider',
                    'title' => '每项左右间距(单位px)',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                ),
                'text_position_type11' => array(
                    'type' => 'select',
                    'title' => '文字显示位置',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                    'values' => array(
                        'cover_image_bottom' => '覆盖于图片下方',
                        'image_bottom' => '图片下方'
                    ),
                    'std' => 'cover_image_bottom'
                ),
                'text_padding_type11' => array(
                    'type' => 'padding',
                    'title' => '文字内间距(单位px)',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                    'std' => '0 0 0 25px'
                ),
                'list_item_status_type11' => array(
                    'type' => 'buttons',
                    'title' => '列表项状态',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'std' => 'normal',
                    'depends' => array(array('service_product_type', '=', 'type11')),
                ),
                // 文字距离底部距离
                'text_bottom_type11' => array(
                    'type' => 'slider',
                    'title' => '内容距底部距离(单位px)',
                    'max' => 1000,
                    'min' => -1000,
                    'std' => array('md' => 30, 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('text_position_type11', '=', 'cover_image_bottom'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 移入内容底部距离
                'hover_text_bottom_type11' => array(
                    'type' => 'slider',
                    'title' => '移入内容底部距离(单位px)',
                    'max' => 1000,
                    'min' => -1000,
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('text_position_type11', '=', 'cover_image_bottom'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                'cover_bgcolor_type11' => array(
                    'type' => 'color',
                    'title' => '遮罩颜色',
                    'std' => 'rgba(0,0,0,0.7)',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                'hover_cover_bgcolor_type11' => array(
                    'type' => 'color',
                    'title' => '鼠标移入遮罩颜色',
                    'std' => 'rgba(0,0,0,0.4)',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    )
                ),
                'active_box_width_type11' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入元素宽度(单位%)',
                    'max' => 100,
                    'min' => 0,
                    'std' => array('md' => 43, 'sm' => 25, 'xs' => 25),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                'text_align_type11' => array(
                    'type' => 'select',
                    'title' => '文字对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中对齐',
                        'right' => '右对齐',
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                'hover_text_align_type11' => array(
                    'type' => 'select',
                    'title' => '鼠标移入文字对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中对齐',
                        'right' => '右对齐',
                    ),
                    'std' => '',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                // 标题字体大小
                'fontsize_type11' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => 200,
                    'min' => 0,
                    'std' => array('md' => '30', 'sm' => '25', 'xs' => '20'),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                'hover_title_fontsize_type11' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入标题字体大小',
                    'max' => 200,
                    'min' => 0,
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                'color_type11' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                'hover_title_color_type11' => array(
                    'type' => 'color',
                    'title' => '鼠标移入标题字体颜色',
                    'std' => '',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                'title_fontweight_type11' => array(
                    'type' => 'select',
                    'title' => '标题字体粗细',
                    'values' => array(
                        'normal' => '正常',
                        'bold' => '粗体',
                        'bolder' => '更粗体',
                        'lighter' => '更细体',
                    ),
                    'std' => 'bold',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 隐藏简介
                'hide_desc_type11' => array(
                    'type' => 'checkbox',
                    'title' => '简介正常隐藏移入显示',
                    'std' => 0,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 简介字体大小
                'jjfontsize_type11' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'max' => 50,
                    'min' => 0,
                    'std' => array('md' => '18', 'sm' => '15', 'xs' => '14'),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                'jjhover_fontsize_type11' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入简介字体大小',
                    'max' => 50,
                    'min' => 0,
                    'std' => array('md' => '', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                'jjcolor_type11' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    )
                ),
                'jjhover_color_type11' => array(
                    'type' => 'color',
                    'title' => '鼠标移入简介字体颜色',
                    'std' => '',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'hover'),
                    ),
                ),
                // 更多按钮
                'more_type11' => array(
                    'type' => 'checkbox',
                    'title' => '显示更多按钮',
                    'std' => 0,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 更多按钮隐藏
                'hide_more_type11' => array(
                    'type' => 'checkbox',
                    'title' => '更多按钮正常隐藏移入显示',
                    'std' => 0,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('more_type11', '=', 1),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 更多按钮图片
                'more_img_type11' => array(
                    'type' => 'media',
                    'title' => '更多按钮图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20250729/2221be08ef946ec274d616b40f59790b.png',
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('more_type11', '=', 1),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 更多按钮图片宽度
                'more_img_width_type11' => array(
                    'type' => 'slider',
                    'title' => '更多按钮图片宽度',
                    'max' => 50,
                    'min' => 0,
                    'std' => array('md' => 40, 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('more_type11', '=', 1),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),
                // 更多按钮外边距
                'more_margin_type11' => array(
                    'type' => 'padding',
                    'title' => '更多按钮外边距',
                    'max' => 50,
                    'min' => 0,
                    'std' => array('md' => '30px 0 0 0', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('service_product_type', '=', 'type11'),
                        array('more_type11', '=', 1),
                        array('list_item_status_type11', '=', 'normal'),
                    ),
                ),

                // Repeatable Item
                'jw_tab_item_goods_fourteen' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => '木工区',
                        ),
                        'fu_title' => array(
                            'type' => 'text',
                            'title' => JText::_('副标题'),
                            'std' => '展柜不变形',
                        ),
                        'jianjie' => array(
                            'type' => 'text',
                            'title' => JText::_('简介'),
                            'std' => '制作木工师傅注重展柜整体结构合理性，优良品率≥98.6%。',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'std' => 'https://oss.lcweb01.cn/joomla/20220728/33f9680061b242aebbbb08ef3c11e3ea.jpg',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        
                    ),
                    'std' => array(
                        array(
                            'title' => '木工区',
                            'fu_title' => '展柜不变形',
                            'jianjie' => '制作木工师傅注重展柜整体结构合理性，优良品率≥98.6%。',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'big_img' => 'https://oss.lcweb01.cn/joomla/20220728/65578d3b2d6b79a8229d55c5b1c38d5e.jpg',
                            
                        ),
                        array(
                            'title' => '批灰打磨区',
                            'fu_title' => '展柜不褪色',
                            'jianjie' => '展柜制作烤漆师傅360°精心打磨，YJPY丝滑漆面，MQ品控，不会出现漆面起泡、流油、橘皮、颗粒等问题。',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'big_img' => 'https://oss.lcweb01.cn/joomla/20220728/33f9680061b242aebbbb08ef3c11e3ea.jpg',
                            
                        ),
                        array(
                            'title' => '亚克力制作区',
                            'fu_title' => '坚固耐用',
                            'jianjie' => '展柜制作加工师傅做到胶水拼接位的平滑、无气泡、无凹凸不平，亚克力切割面后期打磨抛光到钻石抛光的工艺。',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'big_img' => 'https://oss.lcweb01.cn/joomla/20220728/4ee0d34a78b61e127a611a8a09fe78d8.jpg',
                            
                        ),
                        array(
                            'title' => '五金工艺区',
                            'fu_title' => '尺寸精度0.3mm',
                            'jianjie' => '宜佳展柜设计让五金焊接工艺严密，焊点不会过大影响美观，焊接位平整无突起状，不锈钢平面打磨可达镜钢水平，光滑平整。',
                            'detail_page' => '',
                            'target' => '',
                            'detail_page_id' => 0,
                            'big_img' => 'https://oss.lcweb01.cn/joomla/20220728/2323b9bbaab82da9634c1b5c5833a5c1.jpg',
                            
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                ),
                'qywidth_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('区域宽'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '1080',
                    'max' => '1200'
                ),
                'qyheight_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('区域高'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '374',
                    'max' => '800'
                ),
                'qycolor_type14' => array(
                    'type' => 'color',
                    'title' => JText::_('区域背景色'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '#dcdcdc',
                ),
                'liwidth_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('模块宽'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '251',
                    'max' => '500'
                ),
                'imgheight_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '259',
                    'max' => '500'
                ),
                'bjcolor_type14' => array(
                    'type' => 'color',
                    'title' => JText::_('划过背景色'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '#b4945b'
                ),
                'fontcolor_type14' => array(
                    'type' => 'color',
                    'title' => JText::_('划过字体颜色'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '#171717'
                ),
                'fonttit_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '20'
                ),
                'fontjj_type14' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => '16'
                ),

                'gdimg_type14' => array(
                    'type' => 'media',
                    'title' => JText::_('查看更多图'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220728/ab10fc783ae71bd3115597d4594c4743.gif'
                ),
                'gdimgs_type14' => array(
                    'type' => 'media',
                    'title' => JText::_('划过查看更多图'),
                    'depends' => array(array('service_product_type', '=', 'type14')),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220728/4f99012b74fe6814af84554eda8abf94.gif'
                ),

                // Repeatable Item
                'jw_tab_item_goods_fiveteen' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title_img_pc' => array(
                            'type' => 'media',
                            'title' => JText::_('pc端标题图片'),
                            'std' => '',
                        ),
                        'title_img_sj' => array(
                            'type' => 'media',
                            'title' => JText::_('移动端标题图片'),
                            'std' => '',
                        ),
                        'icon_pc' => array(
                            'type' => 'media',
                            'title' => JText::_('pc端图标'),
                            'std' => '',
                        ),
                        'icon_sj' => array(
                            'type' => 'media',
                            'title' => JText::_('移动端图标'),
                            'std' => '',
                        ),
                        'big_img' => array(
                            'type' => 'media',
                            'title' => JText::_('内容图片'),
                            'std' => '',
                        ),
                        'content_pc' => array(
                            'type' => 'text',
                            'title' => JText::_('pc端简介'),
                            'std' => '',
                        ),
                        'content_sj' => array(
                            'type' => 'text',
                            'title' => JText::_('移动端简介'),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/02dc4bd0afe450e0367da3e4289491f1.png',
                            'title_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/8575939971c11cbf535c172a967f91b3.png',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/b6679863a5e872c30ed5321f589ba1d8.gif',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/f12c1e805f3bf77d36efe721fdcebc85.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/b89aa9200ac060b86952d871c361ed02.jpeg',
                            'content_pc' => '精心选材，成就匠心之作。在展柜制作材质上经过严格挑选，采用高密度的中纤板，阻燃板等，品牌亚克力，钢化玻璃等，从源头上保障柜台品质。',
                            'content_sj' => '各种材质经过严格挑选，采用高密度的中纤板，阻燃板等，品牌亚克力，钢化玻璃等，从源头上保障产品品质。',
                            
                        ),
                        array(
                            'title_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/6b2e54e2333b2545e3697c965622ec83.png',
                            'title_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/607ff326254cab686000b1adc84f7f8d.png',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/4cb0b5f13d07074731a40b409ad8d08c.gif',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/27f3718938708d07e15a659fb430e3c2.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/18f49d5c3c120949d7483db1c5fec59a.jpeg',
                            'content_pc' => '大型进口数控加工设备，展柜制作各环节自主完成，可以保质保量完成不同类型的订单。',
                            'content_sj' => '各个生产制作环节自主完成，可以保质保量完成不同类型的订单。',
                            
                        ),
                        array(
                            'title_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/aee2a1a80939885bf969f589b16f1d9e.png',
                            'title_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/d44406c439ca8528e44e3e6e97670c75.png',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/59b9fc1aeed1cdd388d331324f78f9d3.gif',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/0e01bc2c55e11cdf4c1ab06f59b82e56.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/2c5167489b07ad1e66728665f95fc2e9.jpeg',
                            'content_pc' => '设有大型的除尘车间，360°精心打磨，凸显品牌魅力，防指纹工艺，对品牌展柜制作要求得到解决，实力厂家，专属定制。',
                            'content_sj' => '大型的除尘烤漆车间，有工艺品烤漆的水平，漆面光滑平整，产品表面处理细致，能让您对品牌展柜的制作要求得到解决。',
                            
                        ),
                        array(
                            'title_img_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/448f7cbde4eac77be731d71e80fe8b10.png',
                            'title_img_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/e7cb839c564747185aaf656e35da9de6.png',
                            'icon_pc' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/77f472a891a54a6676f48663fba047ff.gif',
                            'icon_sj' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/8b6e01993c88baf52cb0ce8e030e39f5.png',
                            'big_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220730/89376c3effbcabe100664f0c99cb26cb.jpeg',
                            'content_pc' => 'MQ品控，每道工序层层把关，保障各项流程可以监控，能及时发现质量问题，把质量问题控制和解决在生产环节！',
                            'content_sj' => '各个生产环节都设立专职的质检人员，保障各项流程可以监控，能及时发现质量问题，把质量问题控制和解决在生产环节！',
                            
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type15')),
                ),
                'contentcolor_type15_pc' => array(
                    'type' => 'color',
                    'title' => JText::_('pc端简介字体颜色'),
                    'depends' => array(array('service_product_type', '=', 'type15')),
                    'std' => '#616161'
                ),
                'contentcolor_type15_sj' => array(
                    'type' => 'color',
                    'title' => JText::_('移动端简介字体颜色'),
                    'depends' => array(array('service_product_type', '=', 'type15')),
                    'std' => '#000000'
                ),
                'fgxcolor_type15_hr' => array(
                    'type' => 'color',
                    'title' => JText::_('pc端分割线颜色'),
                    'depends' => array(array('service_product_type', '=', 'type15')),
                    'std' => '#a4802f'
                ),
                // Repeatable Item
                'jw_tab_item_goods_sixteen' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题'),
                            'std' => '',
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => JText::_('图标'),
                            'std' => '',
                        ),
                        'icon_hg' => array(
                            'type' => 'media',
                            'title' => JText::_('选中图标'),
                            'std' => '',
                        ),
                        'content_text' => array(
                            'type' => 'text',
                            'title' => JText::_('简介'),
                            'std' => '',
                        ),
                        'button_text' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮文字'),
                            'std' => '',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                            'std' => 'javascript:;',
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '体育考试及认证服务',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/8f25b91828134fd09682be6d1927f92d.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/b0efa6c64eda7a5611f01d4432d05414.png',
                            'content_text' => '龙采体育考试及认证服务以赋能认证考试为核心，依托大数据、人…',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '体育赛事管理及运营服务',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/97f1d54521ea8dc3bd788a83c78fb2fd.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/3f9fbf6195671cb9e59585c6bfdb4af5.png',
                            'content_text' => '龙采体育深耕大型体育赛事管理及运营服务…',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '协会网站维护运营',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/0e581578b6617c787580ef9712d63f06.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/3d6cfdabbd52748f1792f47936f31c35.png',
                            'content_text' => '在互联网全面渗透社会生活的时代背景下…',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '教材出版及研发服务',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/b842a7d0149dcf91896d9ded62bde259.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/b74a775b4de2ae16b7f4be61901bcfec.png',
                            'content_text' => '龙采体育力求给广大体育爱好者更专业…',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '场馆智能建设服务',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/26d51e94480391591055b08cd5ccee1f.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/584d35f3ffc68965ca0bf2f3583373f4.png',
                            'content_text' => '龙采体育场馆智能建设服务，打造体育领域…',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title' => '体育特许商品开发ip孵化服务',
                            'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/3edd2bd95c9e946a435ac0f56aa0b3e6.png',
                            'icon_hg' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/f3b51c7d9712b086993b9adfa989901f.png',
                            'content_text' => '龙采体育深挖体育IP资产的商业拓展，围绕',
                            'button_text' => '了解更多>>',
                            'tz_page_type' => 'external_links',
                            'detail_page' => 'javascript:;',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                    ),
                    'depends' => array(array('service_product_type', '=', 'type16')),
                ),
                'bg_img_type16' => array(
                    'type' => 'media',
                    'title' => JText::_('选中背景图'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220801/0dd7d76a4f0f88bbf80feb8c33b55d6f.png',
                    'depends' => array(array('service_product_type', '=', 'type16')),

                ),
                'button_bottom_type16' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc端按钮下边距'),
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(array('service_product_type', '=', 'type16')),
                ),
                'button_bottom_type16_sj' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机端按钮下边距'),
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(array('service_product_type', '=', 'type16')),
                ),
            ),
        ),
    )
);
