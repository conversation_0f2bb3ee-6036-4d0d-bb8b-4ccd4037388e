<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonEvent_list extends JwpagefactoryAddons
{

	// public static $this_obj;

	// public function __construct($addon)
	// {
	// 	parent::__construct($addon);
	// 	self::$this_obj = $this;
	// }

	public function render()
	{
		$company_id             = $_GET['company_id'] ?? 0;
		$site_id                = $_GET['site_id'] ?? 0;
		$layout_id              = $_GET['layout_id'] ?? 0;
		$settings               = $this->addon->settings;

		// 判断页面样式
        $bjstyle = (isset($settings->bjstyle) && $settings->bjstyle) ? $settings->bjstyle : 'list1';
        //指定更多跳转页ID
        $gengduo = (isset($settings->gengduo)) ? $settings->gengduo : 0;
		
		if($bjstyle=='list1'){
			// 赛事列表
			// 分类样式
			if ($settings->title_fontsize) {

				$title_fontsize = (isset($settings->title_fontsize) && $settings->title_fontsize) ? $settings->title_fontsize : '16';
				$title_fontsize_sm = (isset($settings->title_fontsize_sm) && $settings->title_fontsize_sm) ? $settings->title_fontsize_sm : '16';
				$title_fontsize_xs = (isset($settings->title_fontsize_xs) && $settings->title_fontsize_xs) ? $settings->title_fontsize_xs : '16';
	        } else {
	            
	            $title_fontsize = (isset($settings->title_fontsize) && $settings->title_fontsize) ? $settings->title_fontsize : '';
				$title_fontsize_sm = (isset($settings->title_fontsize_sm) && $settings->title_fontsize_sm) ? $settings->title_fontsize_sm : '';
				$title_fontsize_xs = (isset($settings->title_fontsize_xs) && $settings->title_fontsize_xs) ? $settings->title_fontsize_xs : '';
	        }

			$title_text_color                  = (isset($settings->title_text_color) && $settings->title_text_color) ? $settings->title_text_color : '#4a4a4a';
			$title_active_color                  = (isset($settings->title_active_color) && $settings->title_active_color) ? $settings->title_active_color : '#0E893B';
			$seach_color       = (isset($settings->seach_color) && $settings->seach_color) ? $settings->seach_color : 'rgba(14,137,59,1)';
			// 赛事列表样式
			if ($settings->bttitle_fontsize) {

				$bttitle_fontsize = (isset($settings->bttitle_fontsize) && $settings->bttitle_fontsize) ? $settings->bttitle_fontsize : '18';
				$bttitle_fontsize_sm = (isset($settings->bttitle_fontsize_sm) && $settings->bttitle_fontsize_sm) ? $settings->bttitle_fontsize_sm : '16';
				$bttitle_fontsize_xs = (isset($settings->bttitle_fontsize_xs) && $settings->bttitle_fontsize_xs) ? $settings->bttitle_fontsize_xs : '16';
	        } else {
	            
	           	$bttitle_fontsize = (isset($settings->bttitle_fontsize) && $settings->bttitle_fontsize) ? $settings->bttitle_fontsize : '';
				$bttitle_fontsize_sm = (isset($settings->bttitle_fontsize_sm) && $settings->bttitle_fontsize_sm) ? $settings->bttitle_fontsize_sm : '';
				$bttitle_fontsize_xs = (isset($settings->bttitle_fontsize_xs) && $settings->bttitle_fontsize_xs) ? $settings->bttitle_fontsize_xs : '';
	        }
	        if ($settings->xqtitle_fontsize) {

				$xqtitle_fontsize = (isset($settings->xqtitle_fontsize) && $settings->xqtitle_fontsize) ? $settings->xqtitle_fontsize : '18';
				$xqtitle_fontsize_sm = (isset($settings->xqtitle_fontsize_sm) && $settings->xqtitle_fontsize_sm) ? $settings->xqtitle_fontsize_sm : '16';
				$xqtitle_fontsize_xs = (isset($settings->xqtitle_fontsize_xs) && $settings->xqtitle_fontsize_xs) ? $settings->xqtitle_fontsize_xs : '16';
	        } else {
	            
	           	$xqtitle_fontsize = (isset($settings->xqtitle_fontsize) && $settings->xqtitle_fontsize) ? $settings->xqtitle_fontsize : '';
				$xqtitle_fontsize_sm = (isset($settings->xqtitle_fontsize_sm) && $settings->xqtitle_fontsize_sm) ? $settings->xqtitle_fontsize_sm : '';
				$xqtitle_fontsize_xs = (isset($settings->xqtitle_fontsize_xs) && $settings->xqtitle_fontsize_xs) ? $settings->xqtitle_fontsize_xs : '';
	        }
	        if ($settings->btlink_height) {
				$btlink_height = (isset($settings->btlink_height) && $settings->btlink_height) ? $settings->btlink_height : '30';
				$btlink_height_sm = (isset($settings->btlink_height_sm) && $settings->btlink_height_sm) ? $settings->btlink_height_sm : '30';
				$btlink_height_xs = (isset($settings->btlink_height_xs) && $settings->btlink_height_xs) ? $settings->btlink_height_xs : '25';
	        } else {
	           	$btlink_height = (isset($settings->btlink_height) && $settings->btlink_height) ? $settings->btlink_height : '';
				$btlink_height_sm = (isset($settings->btlink_height_sm) && $settings->btlink_height_sm) ? $settings->btlink_height_sm : '';
				$btlink_height_xs = (isset($settings->btlink_height_xs) && $settings->btlink_height_xs) ? $settings->btlink_height_xs : '';
	        }
	        if ($settings->lblink_height) {
				$lblink_height = (isset($settings->lblink_height) && $settings->lblink_height) ? $settings->lblink_height : '28';
				$lblink_height_sm = (isset($settings->lblink_height_sm) && $settings->lblink_height_sm) ? $settings->lblink_height_sm : '28';
				$lblink_height_xs = (isset($settings->lblink_height_xs) && $settings->lblink_height_xs) ? $settings->lblink_height_xs : '25';
	        } else {
	           	$lblink_height = (isset($settings->lblink_height) && $settings->lblink_height) ? $settings->lblink_height : '';
				$lblink_height_sm = (isset($settings->lblink_height_sm) && $settings->lblink_height_sm) ? $settings->lblink_height_sm : '';
				$lblink_height_xs = (isset($settings->lblink_height_xs) && $settings->lblink_height_xs) ? $settings->lblink_height_xs : '';
	        }
			$bttitle_text_color                  = (isset($settings->bttitle_text_color) && $settings->bttitle_text_color) ? $settings->bttitle_text_color : 'rgba(14,137,59,1)';
			$xqtitle_text_color                  = (isset($settings->xqtitle_text_color) && $settings->xqtitle_text_color) ? $settings->xqtitle_text_color : '#4a4a4a';
			$limit                    = (isset($settings->limit) && $settings->limit) ? $settings->limit : 200;
			$show_page                      = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;

		}else{
			// 近期赛事样式
			
			$dtitle_fontsize_md = (isset($settings->dtitle_fontsize_md) && $settings->dtitle_fontsize_md) ? $settings->dtitle_fontsize_md : '24';
			$dtitle_fontsize_sm = (isset($settings->dtitle_fontsize_sm) && $settings->dtitle_fontsize_sm) ? $settings->dtitle_fontsize_sm : '20';
			$dtitle_fontsize_xs = (isset($settings->dtitle_fontsize_xs) && $settings->dtitle_fontsize_xs) ? $settings->dtitle_fontsize_xs : '18';

			$dtitle_text_color = (isset($settings->dtitle_text_color) && $settings->dtitle_text_color) ? $settings->dtitle_text_color : '24';

			$con_margin_md = (isset($settings->con_margin_md) && $settings->con_margin_md) ? $settings->con_margin_md : '20';
			$con_margin_sm = (isset($settings->con_margin_sm) && $settings->con_margin_sm) ? $settings->con_margin_sm : '10';
			$con_margin_xs = (isset($settings->con_margin_xs) && $settings->con_margin_xs) ? $settings->con_margin_xs : '10';

			$hr_background = (isset($settings->hr_background) && $settings->hr_background) ? $settings->hr_background : '#0E893B';

			$list_padding_md = (isset($settings->list_padding_md) && $settings->list_padding_md) ? $settings->list_padding_md : '20';
			$list_padding_sm = (isset($settings->list_padding_sm) && $settings->list_padding_sm) ? $settings->list_padding_sm : '10';
			$list_padding_xs = (isset($settings->list_padding_xs) && $settings->list_padding_xs) ? $settings->list_padding_xs : '10';

			$lbttitle_fontsize_md = (isset($settings->lbttitle_fontsize_md) && $settings->lbttitle_fontsize_md) ? $settings->lbttitle_fontsize_md : '16';
			$lbttitle_fontsize_sm = (isset($settings->lbttitle_fontsize_sm) && $settings->lbttitle_fontsize_sm) ? $settings->dtitle_fontsize_sm : '16';
			$lbttitle_fontsize_xs = (isset($settings->lbttitle_fontsize_xs) && $settings->lbttitle_fontsize_xs) ? $settings->lbttitle_fontsize_xs : '14';

			$lbttitle_text_color = (isset($settings->lbttitle_text_color) && $settings->lbttitle_text_color) ? $settings->lbttitle_text_color : '#252525';

			$lbtlink_height_md = (isset($settings->lbtlink_height_md) && $settings->lbtlink_height_md) ? $settings->lbtlink_height_md : '24';
			$lbtlink_height_sm = (isset($settings->lbtlink_height_sm) && $settings->lbtlink_height_sm) ? $settings->lbtlink_height_sm : '24';
			$lbtlink_height_xs = (isset($settings->lbtlink_height_xs) && $settings->lbtlink_height_xs) ? $settings->lbtlink_height_xs : '20';

			$class_background = (isset($settings->class_background) && $settings->class_background) ? $settings->class_background : 'rgba(123,229,186,1)';

			$lititle_fontsize_md = (isset($settings->lititle_fontsize_md) && $settings->lititle_fontsize_md) ? $settings->lititle_fontsize_md : '14';
			$lititle_fontsize_sm = (isset($settings->lititle_fontsize_sm) && $settings->lititle_fontsize_sm) ? $settings->lititle_fontsize_sm : '14';
			$lititle_fontsize_xs = (isset($settings->lititle_fontsize_xs) && $settings->lititle_fontsize_xs) ? $settings->lititle_fontsize_xs : '13';

			$lititle_text_color = (isset($settings->lititle_text_color) && $settings->lititle_text_color) ? $settings->lititle_text_color : 'rgba(44,58,88,1)';

			$lilink_height_md = (isset($settings->lilink_height_md) && $settings->lilink_height_md) ? $settings->lilink_height_md : '28';
			$lilink_height_sm = (isset($settings->lilink_height_sm) && $settings->lilink_height_sm) ? $settings->lilink_height_sm : '28';
			$lilink_height_xs = (isset($settings->lilink_height_xs) && $settings->lilink_height_xs) ? $settings->lilink_height_xs : '24';


			$item_number_md = (isset($settings->item_number_md) && $settings->item_number_md) ? $settings->item_number_md : '4';
			$item_number_sm = (isset($settings->item_number_sm) && $settings->item_number_sm) ? $settings->item_number_sm : '3';
			$item_number_xs = (isset($settings->item_number_xs) && $settings->item_number_xs) ? $settings->item_number_xs : '2';

			$dis_number_md = (isset($settings->dis_number_md) && $settings->dis_number_md) ? $settings->dis_number_md : '4';
			$dis_number_sm = (isset($settings->dis_number_sm) && $settings->dis_number_sm) ? $settings->dis_number_sm : '3';
			$dis_number_xs = (isset($settings->dis_number_xs) && $settings->dis_number_xs) ? $settings->dis_number_xs : '2';
			
		}

		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$addon_ida = 'jwpf-addon-' . $this->addon->id;

		$article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
		require_once $article_helper;

		// 获取分类
		$pids=array();
        $pids[]=2;
        $types = JwpagefactoryHelperCategories::getCatematch($pids,$company_id, $site_id);

        if($bjstyle=='list1'){

			$output = '
				<style>
					
					{$addon_id} .ks_search{
					    max-width: 1200px;
					    margin: 0 auto;
					    margin-top: 34px;
					    width:100%;
					}
					{$addon_id} .fb_area a ,.fb_area span{
					    height:40px;
					    font-size:16px;
					    color:rgba(138,138,138,1);
					    line-height:40px;
					    margin:0px 8px;
					    padding:5px 3px; 

					    font-size:'.$title_fontsize.'px;
					    color:'.$title_text_color.';
					}
					{$addon_id} .ks_active{
					    color: '.$title_active_color.' !important;
					    height:40px;
					    font-size:'.$title_fontsize.'px;
					    
					    line-height:40px;
					}
					{$addon_id} .ks_search input{
					    border:1px solid rgba(239,238,239,1);
					    font-size: 14px;
					    padding: 9px 10px;
					    margin-right: 15px;
					    width: 250px;
					}
					{$addon_id} .btn_search{
					    text-align: center;
					    width: 42px;
					    height:40px;
					    background:'.$seach_color.'
					}
					{$addon_id} .btn_search img{
					    margin-top: 10px;
					    display:inline;
					}
					{$addon_id} .rf{
					    float: right;
					}
					{$addon_id} .lf{
					    float: left;    
					}
					{$addon_id} .cl{clear:both;}
					{$addon_id} a{ text-decoration:none; }
					{$addon_id} .fb_area{line-height:40px}
					/*赛事查询*/
					{$addon_id} .game_query{
					    margin-bottom: 40px;
					}
					{$addon_id} .game_list{
					    margin-top: 30px;
					    padding-bottom: 30px;
					    border-bottom: 1px solid #EFEEEF;
					}
					{$addon_id} .game_list>img{
					    max-width:35%;
					    width:32%;
						display:inline;
					}
					{$addon_id} .marginleft20{
					    margin-left: 20px;
					    width:63%;
					}
					{$addon_id} .game_name{
					    float: left;
					    
					    overflow: hidden;
					    margin-bottom: 10px;
					    display: block;
					    float: left;
					    font-weight:600;

					    height:'.$btlink_height.'px;
					    font-size:'.$bttitle_fontsize.'px;
					    color: '.$bttitle_text_color.';
					    line-height:'.$btlink_height.'px;

					}
					{$addon_id} .game_type{
					    width: 100px;
					    margin-bottom: 10px;
					    display: block;
					    float: left;
					    text-align: center;
					    height:30px;
					    background:linear-gradient(329deg,rgba(56,202,143,1) 0%,rgba(123,229,186,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					    margin-left:10px
					}
					{$addon_id} .game_time{
					    float: left;
					    display: inline-block;
					    margin-top: 12px;

					    font-size:'.$xqtitle_fontsize.'px;
					    color: '.$xqtitle_text_color.';
					    height:'.$lblink_height.'px;
					    line-height:'.$lblink_height.'px;
					}
					{$addon_id} .game_times{
					    float: left;
					    overflow: hidden;

					    display: inline-block;
					    margin-top: 12px;
						
						font-size:'.$xqtitle_fontsize.'px;
					    color: '.$xqtitle_text_color.';
					    height:'.$lblink_height.'px;
					    line-height:'.$lblink_height.'px;
					}
					{$addon_id} .game_state{
					    float: left; 
					    margin-top: 12px;
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(147deg,rgba(102,173,255,1) 0%,rgba(76,150,235,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{$addon_id} .game_state a{
					      color:rgba(255,255,255,1);
					}
					{$addon_id} .play_game{
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(332deg,rgba(230,124,138,1) 0%,rgba(255,159,172,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{$addon_id} .see_game{
					    height:22px;
					    font-size:16px;
					    color:rgba(224,32,32,1);
					    line-height:22px;
					    margin-left: 20px;
					}
					{$addon_id} .not_play{
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{$addon_id} .play_name{
					    height:22px;
					    font-size:16px;
					    color:rgba(255,154,0,1);
					    line-height:22px;
					    margin-left: 20px;
					}
					{$addon_id} .game_right{
					    position: relative;
					    margin-top: 30px;
					}
					{$addon_id} .game_right>img{
					    width: 320px;
					    height: 190px;
					}
					{$addon_id} .game_right>div{
					    display: none;
					    position: absolute;
					    top: 0;
					    left: 0;
					    width: 100%;
					    height: 190px;
					    line-height: 190px;
					    text-align: center;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    background:linear-gradient(135deg,rgba(35,143,255,0.7) 0%,rgba(14,137,59,0.7) 100%);
					}
					{$addon_id} .game_right:hover .img_show{
					    display: inline-block;
					}
					
					{$addon_id} .fenys{text-align:center;}
					{$addon_id} .page_plug{display:inline-block;margin-left:5px}
					{$addon_id} .page_plug a{
						cursor:pointer;
						padding:0px 7px;border:1px solid #ccc;text-align:center;margin:0px 3px;display:inline-block
					}
					{$addon_id} .page_plug .act{
						color:#fff!important;
					    background: '.$title_active_color.' !important;
					}
					/*平板*/
					@media (min-width: 768px) and (max-width: 991px) {

						{$addon_id} .fb_area a ,.fb_area span{
						    height:40px;
						    font-size:'.$title_fontsize_sm.'px;
						    color:'.$title_text_color.';
						    line-height:40px;
						    margin:0px 8px;
						    padding:5px 3px; 

						}

						{$addon_id} .ks_active{
						    color: '.$title_active_color.'!important;
						    height:40px;
						    font-size:'.$title_fontsize_sm.'px;
						}
						{$addon_id} .clearfix{
							margin-top:10px
						}
						{$addon_id} .game_name{
						    float: left;
						   
						    overflow: hidden;
						    // white-space: nowrap;
						    // text-overflow: ellipsis;
						    margin-bottom: 0px;
						    display: block;
						    float: left;
						   
						    font-size:'.$bttitle_fontsize_sm.'px;

						    font-weight:600;
						    color: '.$bttitle_text_color.';

						    height:'.$btlink_height_xs.'px;
						    line-height:'.$btlink_height_sm.'px;
						    
						}
						{$addon_id} .game_type{
						    margin-bottom: 0px;
						}
						{$addon_id} .game_time{
						    float: left;

						    display: inline-block;
						    margin-top: 12px;

						    font-size:'.$xqtitle_fontsize_sm.'px;
						    color: '.$xqtitle_text_color.';

						    height:'.$lblink_height_sm.'px;
					    	line-height:'.$lblink_height_sm.'px;
						}
						{$addon_id} .game_times{
						    float: left;
						    overflow: hidden;
						    display: inline-block;
						    margin-top: 12px;
				
							height:'.$lblink_height_sm.'px;
					    	line-height:'.$lblink_height_sm.'px;
						    font-size:'.$xqtitle_fontsize_sm.'px;
						    color: '.$xqtitle_text_color.';
			
						}
					}

					/*手机*/
					@media (max-width: 767px) {
						{$addon_id} .fb_area a ,.fb_area span{
						    height:40px;
						    font-size:'.$title_fontsize_xs.'px;
						    color:'.$title_text_color.';
						    line-height:40px;
						    margin:0px 8px;
						    padding:5px 3px; 

						}

						{$addon_id} .ks_active{
						    color: '.$title_active_color.'!important;
						    height:40px;
						    font-size:'.$title_fontsize_xs.'px;
						}
						{$addon_id} .clearfix{
							margin-top:10px
						}
						{$addon_id} .game_name{
						    float: left;
						   
						    overflow: hidden;

						    margin-bottom: 0px;
						    display: block;
						    float: left;
						   
						    font-size:'.$bttitle_fontsize_xs.'px;

						    font-weight:600;
						    color: '.$bttitle_text_color.';

						    height:'.$btlink_height_xs.'px;
						    line-height:'.$btlink_height_xs.'px;
						    
						}
						{$addon_id} .game_type{
						    margin-bottom: 0px;
						}
						{$addon_id} .game_time{
						    float: left;

						    display: inline-block;
						    margin-top: 12px;

						    font-size:'.$xqtitle_fontsize_xs.'px;
						    color: '.$xqtitle_text_color.';

						    height:'.$lblink_height_xs.'px;
					    	line-height:'.$lblink_height_xs.'px;
						}
						{$addon_id} .game_times{
						    float: left;
						    overflow: hidden;
						    display: inline-block;
						    margin-top: 12px;
				
							height:'.$lblink_height_xs.'px;
					    	line-height:'.$lblink_height_xs.'px;
						    font-size:'.$xqtitle_fontsize_xs.'px;
						    color: '.$xqtitle_text_color.';
			
						}

						{$addon_id} .game_list>img{
						    width:100%;
							display:inline;
							max-width:100%
						}
						{$addon_id} .marginleft20{
						    margin-left: 0%;
						    width:100%;
						}
					}
				</style>
			';
			$output .= '<div class="ks_search" id="' . $addon_ida . '">';
				$output .= '<div class="clearfix">';
					$output .= '<div class="fb_area lf">';
						$output .= '<span>赛事类型:</span>';
						$output .= '<span><a class="ks_active quan" style="cursor:pointer">全部</a></span>';
						//分类数据
						if($types){
	                        foreach ($types as $k1 => $v1){
								$output .= '<span value="'.$v1["tag_id"].'" class="type"><a href="#">'.$v1["title"].'</a></span>';
	                        }
	                    }
					$output .= '</div>';

					$output .= '<div class="rf">';
						$output .= '<input class="lf name" lit="'.$limit.'" placeholder="输入赛事名称进行搜索">';
						$output .= '<div class="lf btn_search">';
							$output .= '<img src="/components/com_jwpagefactory/addons/event_list/assets/images/fffsearch.png">';
						$output .= '</div>';
						$output .= '<div class="cl"></div>';
					$output .= '</div>';
					$output .= '<div class="cl"></div>';
				$output .= '</div>';

				$output .= '<div class="game_query clearfix" >';
					$output .= '<div class="width824 sai num" id="pNum1">';

				    $output .= '</div>';
				$output .= '</div>';

				// 翻页
				if ($show_page)
					{
						$output .= '<div class="fenys">';
						$output .= '</div>';
					}

				//js
				$yuming=$_SERVER['HTTP_HOST'];
		        if($yuming=='jzt_dev_2.china9.cn'){
		            $urlpath='http://jzt_dev_1.china9.cn';
		        }elseif($yuming=='ijzt.china9.cn'){
		            $urlpath='https://zhjzt.china9.cn';
		        }else{
		            $config = new JConfig();
		            $urlpath = $config->jzt_url;
		        }

				$output .= '<script>
					// 进入页面查询数据
					jQuery(document).on("ready", function(){

	          			var show_page="'.$show_page.'";
						if (show_page==true){
	          				var lmt='.$limit.';
							dsj("","",1,lmt);
							ActivityCount();
						}else{
							dsj();
						}
					});
	                
					// 点击分页
	                function hqfy(ys,lit){
						jQuery("' . $addon_id . ' .page_plug a").removeClass("act");

						jQuery("' . $addon_id . ' .page_plug ").find(".nm"+ys).addClass("act");

						var keys=jQuery("' . $addon_id . ' .name ").val();//关键词
						var value = jQuery("' . $addon_id . ' .ks_active ").parent().attr("value"); // 选中值
						
						dsj(value,"",ys,lit);
	                }

	                // 点击分类切换内容
	                jQuery("' . $addon_id . ' .type ").click(function(){
						var zz=jQuery(this).attr("value");
						var keys=jQuery("' . $addon_id . ' .name ").val();//关键词

						jQuery("' . $addon_id . ' .fb_area a ").removeClass("ks_active"); 
						jQuery(this).find("a").addClass("ks_active");
						
						var show_page="'.$show_page.'";
						if (show_page==true){
	          				var lmt='.$limit.';
							dsj(zz,"",1,lmt);
							ActivityCount(zz,"");
						}else{
							dsj(zz,"");
						}
	                }) 
					// 查询全部数据
	                jQuery("' . $addon_id . ' .quan ").click(function(){
						jQuery("' . $addon_id . ' .fb_area a ").removeClass("ks_active"); 
						jQuery(this).addClass("ks_active");

	          			var show_page="'.$show_page.'";
						if (show_page==true){
	          				var lmt='.$limit.';
							dsj("","",1,lmt);
							ActivityCount();
						}else{
							dsj();
						}
	                })
					
					// 关键词搜索
					jQuery("' . $addon_id . ' .btn_search ").click(function(){
						var keys=jQuery("' . $addon_id . ' .name ").val();//关键词
						var value = jQuery("' . $addon_id . ' .ks_active ").parent().attr("value"); // 选中值

						var show_page="'.$show_page.'";
						if(keys){
							if(show_page==true){
								dsj("",keys);
								ActivityCount("",keys);
							}else{
								dsj("",keys);
							}
						}else{
							alert("请输入搜索关键词");
						}
					})

					// ajax查数据
					function dsj(catid,keys,ys,lit){
	                    jQuery.ajax({
	                        type: "POST",
	                        url: "' . $urlpath . '/api/Shuju/matchlist",
	                        dataType: "json",
	                        data: {
	                            "company_id" :' . $company_id . ',
	                            "site_id" :' . $site_id . ',
	                            "catid": catid,
	                            "keys": keys,
	                            "ys": ys,
	                            "lit": lit
	                        },
	                        success: function (res) {
	                            console.log(res.data);
	                            jQuery("' . $addon_id . ' .sai ").empty(); 
	                            var lens=res.data.length;
	                            if(lens>0){
							        var bs="";
	                                var str="";
	                                for(var i=0;i < lens; i++){

	                                	str+= \'<div class="game_list">\';
											str+= \'<img src="\'+ res.data[i]["image_intro"] + \'" alt="">\';
												str+= \'<div class="rf marginleft20">\';
							                  		str+= \'<div class="clearfix">\';
														str+= \'<div class="game_name lf">\'+ res.data[i]["title"]+ \'</div>\';
							                        	str+= \'<div class="game_type rf">\'+ res.data[i]["catitle"] +\'</div>\';
							                        str+= \'<div class="cl"></div>\';
							                    str+=\'</div>\';

							                    str+= \'<div>\';
							                        str+= \'<div class="game_time">比赛时间：</div>\';
							                        	str+= \'<div class="game_times">\'+ res.data[i]["pkstart"] +\'到\'+ res.data[i]["pkstop"] +\'</div>\';
							                        	str+= \'<div class="cl"></div>\';
							                    	str+= \'</div>\';
							                    str+= \'<div>\';
							                        str+= \'<div class="game_time">比赛地点：</div>\';
							                        str+= \'<div class="game_times">\'+ res.data[i]["address"] +\'</div>\';
							                        str+= \'<div class="cl"></div>\';

							                    str+= \'</div>\';
							                    str+= \'<div>\';
							                        str+= \'<div class="game_time">主办单位：</div>\';
							                        str+= \'<div class="game_times">\'+ res.data[i]["organizer"] +\'</div>\';
							                        str+= \'<div class="cl"></div>\';
							                    str+= \'</div>\';
							                    str+= \'<div>\';
							                        str+= \'<div class="game_time">承办单位：</div>\';
							                        str+= \'<div class="game_times">\'+ res.data[i]["undertaker"] +\'</div>\';
							                        str+= \'<div class="cl"></div>\';
							                    str+= \'</div>\';
							                    str+= \'<div>\';
							                        str+= \'<div class="game_time">赛事状态：</div>\';

								                        if(res.data[i]["zt"]=="结束"){
															bs="background:linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%)";
								                        }else if(res.data[i]["zt"]=="未开始"){
															bs="background: linear-gradient(147deg,rgba(102,173,255,1) 0%,rgba(76,150,235,1) 100%)";
								                        }else if(res.data[i]["zt"]=="进行中"){
															bs="background: linear-gradient(329deg,rgba(56,202,143,1) 0%,rgba(123,229,186,1) 100%)";
								                        }else{
															bs="background: linear-gradient(329deg,rgba(56,202,143,1) 0%,rgba(123,229,186,1) 100%)";
															
								                        }
							                        	str+= \'<div class="game_state clearfix" style="\'+bs+\'">\'+ res.data[i]["zt"] +\'</div>\';
							                        	str+= \'<div class="cl"></div>\';
							                    	str+= \'</div>\';
							                str+= \'</div>\';
							                str+= \'<div class="cl"></div>\';
							            str+= \'</div>\';
	                                }
	                                jQuery("' . $addon_id . ' .sai ").append(str);
	                            }else{
	                                var str="";
	                            	str+=\'<div style="padding:20px 0px;text-align:center;font-size:16px;">暂无资料~</div>\';
	                                jQuery("' . $addon_id . ' .sai ").append(str);

	                            }
	                        }     
	                    });
	                }


	                // ajax分页总数查询
	                function ActivityCount(catid,keys){
						jQuery.ajax({
	                        type: "POST",
	                        url: "' . $urlpath . '/api/Shuju/ActivityCount",
	                        dataType: "json",
	                        data: {
	                            "company_id" :' . $company_id . ',
	                            "site_id" :' . $site_id . ',
	                            "catid": catid
	                        },
	                        success: function (res) {
	                            // console.log(res.data);
								var cont=res.data;
	          					var all_page = 1;
	          					var lmt='.$limit.';
								if (lmt)
								{
									all_page = Math.ceil(cont / lmt);
								}

	                            jQuery("' . $addon_id . ' .fenys ").empty(); 
								
								str="";
								
								if(all_page>1 && !keys){

									str+= \'<div class="page_plug">\';
									for(var i=1;i <= all_page; i++){

										str+= \'<a class="page_num nm\'+i+\'" onclick="hqfy(\'+i+\',\'+lmt+\')" ys=\'+i+\' lit=\'+lmt+\'>\'+i+\'</a>\';
										
									}
									str+= \'</div>\';
									str+= \'<div class="page_plug">共 \'+cont+\' 条</div>\';
									str+= \'</div>\';

	                                jQuery("' . $addon_id . ' .fenys ").append(str);

								}

	                        }     
	                    });
	                }
				</script>';

        }else{

        }

		return $output;

	}


	public static function getTemplate()
	{

		$output = '
	        <#
	        	var addonId = "#jwpf-addon-"+data.id;
				
				/*布局样式*/
			    var bjstyle = (typeof data.bjstyle !== "undefined" && data.bjstyle) ? data.bjstyle : "list1";

				/*分类样式*/
      			var title_fontsize = (typeof data.title_fontsize !== "undefined" && data.title_fontsize) ? data.title_fontsize : "16";
			    var title_text_color = (typeof data.title_text_color !== "undefined" && data.title_text_color) ? data.title_text_color : "#4a4a4a";
			    var title_active_color = (typeof data.title_active_color !== "undefined" && data.title_active_color) ? data.title_active_color : "#0E893B";
			    var seach_color = (typeof data.seach_color !== "undefined" && data.seach_color) ? data.seach_color : "rgba(14,137,59,1)";
				
				var bttitle_fontsize = (typeof data.bttitle_fontsize !== "undefined" && data.bttitle_fontsize) ? data.bttitle_fontsize : "18";
			    var bttitle_text_color = (typeof data.bttitle_text_color !== "undefined" && data.bttitle_text_color) ? data.bttitle_text_color : "rgba(14,137,59,1)";
			    var xqtitle_fontsize = (typeof data.xqtitle_fontsize !== "undefined" && data.xqtitle_fontsize) ? data.xqtitle_fontsize : "18";
			    var xqtitle_text_color = (typeof data.xqtitle_text_color !== "undefined" && data.xqtitle_text_color) ? data.xqtitle_text_color : "#4a4a4a";
				var btlink_height = (typeof data.btlink_height !== "undefined" && data.btlink_height) ? data.btlink_height : "30";
			    var lblink_height = (typeof data.lblink_height !== "undefined" && data.lblink_height) ? data.lblink_height : "28";
				

				/*近期赛事*/
      			var dtitle_fontsize = (typeof data.dtitle_fontsize !== "undefined" && data.dtitle_fontsize) ? data.dtitle_fontsize : "24";
				
      			var dtitle_text_color = (typeof data.dtitle_text_color !== "undefined" && data.dtitle_text_color) ? data.dtitle_text_color : "#0E893B";
      			var con_margin = (typeof data.con_margin !== "undefined" && data.con_margin) ? data.con_margin : "30";
      			var hr_background = (typeof data.hr_background !== "undefined" && data.hr_background) ? data.hr_background : "#0E893B";
      			
      			if(hr_background=="#0E893B"){
					hr_background="linear-gradient(92deg,rgba(35,143,255,1) 0%,rgba(14,137,59,1) 100%)";
      			}

      			var list_padding = (typeof data.list_padding !== "undefined" && data.list_padding) ? data.list_padding : "20px 20px 20px 20px";
      			var lbttitle_fontsize = (typeof data.lbttitle_fontsize !== "undefined" && data.lbttitle_fontsize) ? data.lbttitle_fontsize : "24";
      			if(lbttitle_fontsize.md== "undefined"){
					lbttitle_fontsize.md=lbttitle_fontsize;
				}
      			var lbttitle_text_color = (typeof data.lbttitle_text_color !== "undefined" && data.lbttitle_text_color) ? data.lbttitle_text_color : "#252525";
      			var lbtlink_height = (typeof data.lbtlink_height !== "undefined" && data.lbtlink_height) ? data.lbtlink_height : "24";

      			var class_background = (typeof data.class_background !== "undefined" && data.class_background) ? data.class_background : "rgba(123,229,186,1)";

      			var lititle_fontsize = (typeof data.lititle_fontsize !== "undefined" && data.lititle_fontsize) ? data.lititle_fontsize : "14";
      			if(lititle_fontsize.md== "undefined"){
					lititle_fontsize.md=lititle_fontsize;
				}
      			var lititle_text_color = (typeof data.lititle_text_color !== "undefined" && data.lititle_text_color) ? data.lititle_text_color : "rgba(44,58,88,1)";
      			var lilink_height = (typeof data.lilink_height !== "undefined" && data.lilink_height) ? data.lilink_height : "28";
      			var item_number = (typeof data.item_number !== "undefined" && data.item_number) ? data.item_number : "4";
      			var dis_number = (typeof data.dis_number !== "undefined" && data.dis_number) ? data.dis_number : "2";
				

			#>

			<style>
				<# if(bjstyle == "list1"){ 
					//赛事列表
				#>
		            {{addonId}} .ks_search{
					    max-width: 1200px;
					    margin: 0 auto;
					    margin-top: 34px;
					    width:100%;
					}

					{{addonId}} .fb_area a ,.fb_area span{
					    height:40px;
					    font-size:{{ title_fontsize.md }}px;
					    color:{{ title_text_color }};
					    line-height:40px;
					    margin:0px 8px;
					    padding:5px 3px; 

					}

					{{addonId}} .ks_active{
					    color: {{ title_active_color }}!important;
					    height:40px;
					    font-size:{{ title_fontsize.md }}px;
					}
					{{addonId}} .ks_search input{
					    border:1px solid rgba(239,238,239,1);
					    font-size: 14px;
					    padding: 9px 10px;
					    margin-right: 15px;
					    width: 250px;
					}
					{{addonId}} .btn_search{
					    text-align: center;
					    width: 42px;
					    height:40px;
					    background:{{ seach_color }}
					}
					{{addonId}} .btn_search img{
					    margin-top: 10px;
					    display:inline;
					}
					{{addonId}} .rf{
					    float: right;
					}
					{{addonId}} .lf{
					    float: left;    
					}
					{{addonId}} .cl{clear:both;}
					{{addonId}} a{ text-decoration:none; }

					/*赛事查询*/
					{{addonId}} .game_query{
					    margin-bottom: 40px;
					}
					{{addonId}} .game_list{
					    margin-top: 30px;
					    padding-bottom: 30px;
					    border-bottom: 1px solid #EFEEEF;
					}
					{{addonId}} .game_list>img{
					    max-width:35%;
					    width:32%;
						display:inline;
					}
					{{addonId}} .marginleft20{
					    margin-left: 20px;
					    width:63%;
					}
					{{addonId}} .game_name{
					    float: left;
					   
					    overflow: hidden;

					    margin-bottom: 10px;
					    display: block;
					    float: left;

					    height:{{ btlink_height.md }}px;
					    font-size:{{ bttitle_fontsize.md }}px;

					    font-weight:600;
					    color: {{ bttitle_text_color }};

					    line-height:{{ btlink_height.md }}px;
					}
					{{addonId}} .game_type{
					    width: 100px;
					    margin-bottom: 10px;
					    display: block;
					    float: left;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(329deg,rgba(56,202,143,1) 0%,rgba(123,229,186,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{{addonId}} .game_time{
					    float: left;

					    display: inline-block;
					    margin-top: 12px;
					    font-size:{{ xqtitle_fontsize.md }}px;
					    color: {{ xqtitle_text_color }};

					    height:{{ lblink_height.md }}px;
					    line-height:{{ lblink_height.md }}px;
					    
					}
					{{addonId}} .game_times{
					    float: left;
					    overflow: hidden;
					    white-space: nowrap;
					    text-overflow: ellipsis;
					    display: inline-block;
					    margin-top: 12px;
					    

					    font-size:{{ xqtitle_fontsize.md }}px;
					    color: {{ xqtitle_text_color }};

					    height:{{ lblink_height.md }}px;
					    line-height:{{ lblink_height.md }}px;
					}
					{{addonId}} .game_state{
					    float: left; 
					    margin-top: 12px;
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(147deg,rgba(102,173,255,1) 0%,rgba(76,150,235,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{{addonId}} .game_state a{
					      color:rgba(255,255,255,1);
					}
					{{addonId}} .play_game{
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(332deg,rgba(230,124,138,1) 0%,rgba(255,159,172,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{{addonId}} .see_game{
					    height:22px;
					    font-size:16px;
					    color:rgba(224,32,32,1);
					    line-height:22px;
					    margin-left: 20px;
					}
					{{addonId}} .not_play{
					    margin-bottom: 10px;
					    display: inline-block;
					    text-align: center;
					    width:80px;
					    height:30px;
					    background:linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%);
					    border-radius:16px;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    line-height:30px;
					}
					{{addonId}} .play_name{
					    height:22px;
					    font-size:16px;
					    color:rgba(255,154,0,1);
					    line-height:22px;
					    margin-left: 20px;
					}
					{{addonId}} .game_right{
					    position: relative;
					    margin-top: 30px;
					}
					{{addonId}} .game_right>img{
					    width: 320px;
					    height: 190px;
					}
					{{addonId}} .game_right>div{
					    display: none;
					    position: absolute;
					    top: 0;
					    left: 0;
					    width: 100%;
					    height: 190px;
					    line-height: 190px;
					    text-align: center;
					    font-size:16px;
					    color:rgba(255,255,255,1);
					    background:linear-gradient(135deg,rgba(35,143,255,0.7) 0%,rgba(14,137,59,0.7) 100%);
					}
					{{addonId}} .game_right:hover .img_show{
					    display: inline-block;
					}

					/*平板*/
					@media (min-width: 768px) and (max-width: 991px) {
						{{addonId}} .fb_area a ,.fb_area span{
						    height:40px;
						    font-size:{{ title_fontsize.sm }}px;
						    color:{{ title_text_color }};
						    line-height:40px;
						    margin:0px 3px;
						    padding:5px 3px; 

						}

						{{addonId}} .ks_active{
						    color: {{ title_active_color }}!important;
						    height:40px;
						    font-size:{{ title_fontsize.sm }}px;
						}

						{{addonId}} .game_name{
						    float: left;
						   
						    overflow: hidden;

						    margin-bottom: 10px;
						    display: block;
						    float: left;

						    font-size:{{ bttitle_fontsize.sm }}px;

						    font-weight:600;
						    color: {{ bttitle_text_color }};

						    height:{{ btlink_height.sm }}px;
						    line-height:{{ btlink_height.sm }}px;
						    
						}

						{{addonId}} .game_time{
						    float: left;

						    display: inline-block;

						    font-size:{{ xqtitle_fontsize.sm }}px;
						    color: {{ xqtitle_text_color }};

						    height:{{ lblink_height.sm }}px;
					    	line-height:{{ lblink_height.sm }}px;
						}
						{{addonId}} .game_times{
						    float: left;
						    overflow: hidden;
						    display: inline-block;

						    font-size:{{ xqtitle_fontsize.sm }}px;
						    color: {{ xqtitle_text_color }};

						    height:{{ lblink_height.sm }}px;
					    	line-height:{{ lblink_height.sm }}px;
						}
					}

					/*手机*/
					@media (max-width: 767px) {
						{{addonId}} .fb_area a ,.fb_area span{
						    height:40px;
						    font-size:{{ title_fontsize.xs }}px;
						    color:{{ title_text_color }};
						    line-height:40px;
						    margin:0px 8px;
						    padding:5px 3px; 

						}

						{{addonId}} .ks_active{
						    color: {{ title_active_color }}!important;
						    height:40px;
						    font-size:{{ title_fontsize.xs }}px;
						}
						{{addonId}} .clearfix{
							margin-top:10px
						}
						{{addonId}} .game_name{
						    float: left;
						   
						    overflow: hidden;

						    margin-bottom: 0px;
						    display: block;
						    float: left;
						   
						    font-size:{{ bttitle_fontsize.xs }}px;

						    font-weight:600;
						    color: {{ bttitle_text_color }};

						    height:{{ btlink_height.xs }}px;
						    line-height:{{ btlink_height.xs }}px;
						    
						}
						{{addonId}} .game_type{
						    margin-bottom: 0px;
						}
						{{addonId}} .game_time{
						    float: left;

						    display: inline-block;
						    margin-top: 12px;

						    font-size:{{ xqtitle_fontsize.xs }}px;
						    color: {{ xqtitle_text_color }};

						    height:{{ lblink_height.xs }}px;
					    	line-height:{{ lblink_height.xs }}px;
						}
						{{addonId}} .game_times{
						    float: left;
						    overflow: hidden;
						    display: inline-block;
						    margin-top: 12px;
				
							height:{{ lblink_height.xs }}px;
					    	line-height:{{ lblink_height.xs }}px;
						    font-size:{{ xqtitle_fontsize.xs }}px;
						    color: {{ xqtitle_text_color }};

			
						}

						{{addonId}} .game_list>img{
						    width:100%;
							display:inline;
							max-width:100%
						}
						{{addonId}} .marginleft20{
						    margin-left: 0%;
						    width:100%;
						}
					}
				<# } #>


				<# if(bjstyle == "list2"){
					// 近期赛事css
				#>
					img{max-width:100%;border:none;}
					a{text-decoration:none}
					*,ul,p{margin:0;padding:0;}
					{{addonId}} .rf{float:right}
					{{addonId}} .lf{float:left}

					{{addonId}} .newsMessage{
					    width: 100%;
					    margin: 0 auto;
					    height:45px;
					    line-height:45px;
					    border-bottom: 1px solid #d8d8d8;
					}
					{{addonId}} .newsVideo{
					    /*margin-top: 49px;*/
					}
					{{addonId}} .newsVideoTitle{
					    margin-right: 25px;
					    font-weight:600;
					    border-bottom: 2px solid {{ dtitle_text_color }};
					    line-height:43px;

					    font-size:{{ dtitle_fontsize.md }}px;
					    color:{{ dtitle_text_color }};
					}
					{{addonId}} .more_more{
					     color: #999999 !important;
					}
					{{addonId}} .core{
					    margin-top: {{con_margin.md}}px;
					    margin-bottom: {{con_margin.md}}px;
					    font-size:0px;
					}
					{{addonId}} .core li{
					    display: inline-block;
					    text-align: center;
					    padding: {{list_padding.md}};
					    width: calc(100% / {{item_number.md}});
					   	
					}
					{{addonId}} .core li:nth-child(1){
					    margin-right:0;
					    margin-bottom:10px;
					}
					{{addonId}} .textleft{
					    text-align: left;
					}
					{{addonId}} .core_title{
					    overflow: hidden;
					    height:{{lbtlink_height.md}}px;
					    font-size:{{lbttitle_fontsize.md}}px;
					    color:{{lbttitle_text_color}};
					    line-height:{{lbtlink_height.md}}px;
					    margin-top: 10px;
					    margin-bottom: 7px;
					}
					{{addonId}} .core_address{
					    width: 190px;
					}
					{{addonId}} .textcenter{
					    text-align: center;
					}
					{{addonId}} .phone_pe{
					    border-top: 1px solid #d8d8d8;
					    font-size:{{lititle_fontsize.md}}px;
					    color:{{lititle_text_color}};
					    line-height:{{lilink_height.md}}px;
					    padding-top: 5px;
					}
					{{addonId}} .address{
					    margin-top: 5px;
					   	font-size:{{lititle_fontsize.md}}px;
					    color:{{lititle_text_color}};
					    line-height:{{lilink_height.md}}px;
					}
					{{addonId}} .core li:hover{
					    background:{{hr_background}} ;
					}
					{{addonId}} .core li:hover p{
					    color: #fff;
					}
					{{addonId}} .core li:hover .address{
					    color: #fff;
					}
					{{addonId}} .eventsTypes{
					    margin-top: 5px;
					    margin-bottom: 10px;
					    display: inline-block;
					    padding: 3px 8px;
					    color: #fff;
					    background:{{class_background}};
					    border-radius:11px;
					    font-size:{{lbttitle_fontsize.md}}px;
					}
					{{addonId}} .isStart{
					    margin-top: 15px;
					    width:67px;
					    height:22px;
					    line-height: 22px;
					    color: #fff;
					    font-size: 12px;
					    background:linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%);
					    border-radius:11px;
					}
					{{addonId}} .end{
					    margin-top: 15px;
					    width:67px;
					    height:22px;
					    line-height: 22px;
					    color: #fff;
					    font-size: 12px;
					    background:linear-gradient(147deg,rgba(102,173,255,1) 0%,rgba(76,150,235,1) 100%);
					    border-radius:11px;
					}
					

					/*平板*/
					@media (min-width: 768px) and (max-width: 991px) {
						{{addonId}} .newsVideoTitle{
						    margin-right: 25px;
						    font-weight:600;
						    border-bottom: 2px solid {{ dtitle_text_color }};
						    line-height:43px;
						    font-size:{{ dtitle_fontsize.sm }}px;
						    color:{{ dtitle_text_color }};
						}

						{{addonId}} .core{
						    margin-top: {{con_margin.sm}}px;
						    margin-bottom: {{con_margin.sm}}px;
						    font-size:0px;
						}
						{{addonId}} .core li{
						    display: inline-block;
						    text-align: center;
						    padding: {{list_padding.sm}};
						    width: calc(100% / {{item_number.sm}});
						}

						{{addonId}} .core_title{
						    overflow: hidden;
						    height:{{lbtlink_height.sm}}px;
						    font-size:{{lbttitle_fontsize.sm}}px;
						    color:{{lbttitle_text_color}};
						    line-height:{{lbtlink_height.sm}}px;
						    margin-top: 10px;
						    margin-bottom: 7px;
						}
						
						{{addonId}} .phone_pe{
						    border-top: 1px solid #d8d8d8;
						    font-size:{{lititle_fontsize.sm}}px;
						    color:{{lititle_text_color}};
						    line-height:{{lilink_height.sm}}px;
						    padding-top: 5px;
						}
						{{addonId}} .address{
						    margin-top: 5px;
						   	font-size:{{lititle_fontsize.sm}}px;
						    color:{{lititle_text_color}};
						    line-height:{{lilink_height.sm}}px;
						}
						{{addonId}} .core li:hover{
						    background:{{hr_background}} ;
						}
						{{addonId}} .eventsTypes{
						    margin-top: 5px;
						    margin-bottom: 10px;
						    display: inline-block;
						    padding: 3px 8px;
						    color: #fff;
						    background:{{class_background}};
						    border-radius:11px;
						    font-size:{{lbttitle_fontsize.sm}}px;
						}
					}

					/*手机*/
					@media (max-width: 767px) {
						{{addonId}} .newsVideoTitle{
						    margin-right: 25px;
						    font-weight:600;
						    border-bottom: 2px solid {{ dtitle_text_color }};
						    line-height:43px;
						    font-size:{{ dtitle_fontsize.xs }}px;
						    color:{{ dtitle_text_color }};
						}

						{{addonId}} .core{
						    margin-top: {{con_margin.xs}}px;
						    margin-bottom: {{con_margin.xs}}px;
						    font-size:0px;
						}
						{{addonId}} .core li{
						    display: inline-block;
						    text-align: center;
						    padding: {{list_padding.xs}};
						    width: calc(100% / {{item_number.xs}});
						}

						{{addonId}} .core_title{
						    overflow: hidden;
						    height:{{lbtlink_height.xs}}px;
						    font-size:{{lbttitle_fontsize.xs}}px;
						    color:{{lbttitle_text_color}};
						    line-height:{{lbtlink_height.xs}}px;
						    margin-top: 10px;
						    margin-bottom: 7px;
						}
						
						{{addonId}} .phone_pe{
						    border-top: 1px solid #d8d8d8;
						    font-size:{{lititle_fontsize.xs}}px;
						    color:{{lititle_text_color}};
						    line-height:{{lilink_height.xs}}px;
						    padding-top: 5px;
						}
						{{addonId}} .address{
						    margin-top: 5px;
						   	font-size:{{lititle_fontsize.xs}}px;
						    color:{{lititle_text_color}};
						    line-height:{{lilink_height.xs}}px;
						}
						{{addonId}} .core li:hover{
						    background:{{hr_background}} ;
						}
						{{addonId}} .eventsTypes{
						    margin-top: 5px;
						    margin-bottom: 10px;
						    display: inline-block;
						    padding: 3px 8px;
						    color: #fff;
						    background:{{class_background}};
						    border-radius:11px;
						    font-size:{{lbttitle_fontsize.xs}}px;
						}
					}


				<# } #>
			</style>
				
			<# if(bjstyle == "list1"){
				// 赛事列表样式
			#>
				<div class="ks_search">
				    <div class="clearfix">
				        <div class="fb_area lf">
				            <span>赛事类型:</span>
				            <span><a class="ks_active" href="">全部</a></span>
				            <span value="1" class="type"><a href="#">公开赛</a></span>
		            		<span value="2" class="type"><a href="#">俱乐部联赛</a></span>
				        </div>
				        <div class="rf">
				            <input class="lf name" placeholder="输入赛事名称进行搜索">
				            <div class="lf btn_search">
				                <img src="/components/com_jwpagefactory/addons/event_list/assets/images/fffsearch.png">
				            </div>
				            <div class="cl"></div>
				        </div>
				        <div class="cl"></div>
				    </div>
				    <div class="game_query clearfix">
				        <div class="width824 sai num" id="pNum1">
				            <div class="game_list">
				                <img src="https://yoga.longcaisport.com/uploads/42c4795b378695d39cfa7bbc43a62ded.png">
				                <div class="rf marginleft20">
				                    <div class="clearfix">
				                        <div class="game_name lf">
				                            2021年全国健身瑜伽公开赛（金华站）
				                        </div>
				                        <div class="game_type rf">
				                            公开赛
				                        </div>
				                        <div class="cl"></div>
				                    </div>
				                    <div>
				                        <div class="game_time">比赛时间：</div>
				                        <div class="game_times">2021-05-22到2021-05-24</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">比赛地点：</div>
				                        <div class="game_times">浙江 金华</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">主办单位：</div>
				                        <div class="game_times">国家体育总局社会体育指导中心</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">承办单位：</div>
				                        <div class="game_times">浙江盛力体育文化发展有限公司</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">赛事状态：</div>
				                        <div class="game_state clearfix" style="background: linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%);">结束</div>
				                        <div class="cl"></div>

				                    </div>
				                </div>
				                <div class="cl"></div>
				            </div>
				            <div class="game_list">
				                <img src="https://yoga.longcaisport.com/uploads/42c4795b378695d39cfa7bbc43a62ded.png">
				                <div class="rf marginleft20">
				                    <div class="clearfix">
				                        <div class="game_name lf">
				                            2021年全国健身瑜伽公开赛（金华站）
				                        </div>
				                        <div class="game_type rf">
				                            公开赛
				                        </div>
				                        <div class="cl"></div>
				                    </div>
				                    <div>
				                        <div class="game_time">比赛时间：</div>
				                        <div class="game_times">2021-05-22到2021-05-24</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">比赛地点：</div>
				                        <div class="game_times">浙江 金华</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">主办单位：</div>
				                        <div class="game_times">国家体育总局社会体育指导中心</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">承办单位：</div>
				                        <div class="game_times">浙江盛力体育文化发展有限公司</div>
				                        <div class="cl"></div>

				                    </div>
				                    <div>
				                        <div class="game_time">赛事状态：</div>
				                        <div class="game_state clearfix" style="background: linear-gradient(332deg,rgba(247,190,42,1) 0%,rgba(244,210,122,1) 100%);">结束</div>
				                        <div class="cl"></div>

				                    </div>
				                </div>
				                <div class="cl"></div>
				            </div>
				        </div>
				    </div>
				</div>
			<# } #>

			<# if(bjstyle == "list2"){
				// 近期赛事样式
			#>
				<div class="newsVideo">
				    <div class="clearfix newsMessage">
				        <div class="lf newsVideoTitle">
				            近期赛事
				        </div>
				        <a href="index.php?p=events_show"><p class="rf more_more">更多&gt;&gt;</p></a>
				    </div>
				    <div class="clearfix ">
				        <ul class="core sai">
				            <li class="clearfix background_we">
				                <a href="index.php?p=events_show" target="_blank">
				                    <img src="https://yoga.longcaisport.com/uploads/42c4795b378695d39cfa7bbc43a62ded.png">
				                    <p class="core_title color12">2021年全国健身瑜伽公开赛（金华站）</p>
				                    <p class="color12"></p>
				                    <div class="textcenter">
				                        <p class="eventsTypes">公开赛</p>
				                    </div>
				                    <p class="textleft phone_pe">
				                        <span>比赛时间：</span>
				                        <span>2021-05-22到2021-05-24</span>
				                    </p>
				                    <div class="textleft address clearfix">
				                        <div class="lf">比赛地点：</div>
				                        <div class="lf core_address" style="width: 180px">浙江 金华</div>
				                    </div>
				                    <p class="rf isStart">结束</p>
				                </a>
				            </li>
				            <li class="clearfix background_we">
				                <a href="index.php?p=events_show" target="_blank">
				                    <img src="https://yoga.longcaisport.com/uploads/c413a9b74a061fd34f8ed479e6f471eb.jpg">
				                    <p class="core_title color12">2021年全国健身瑜伽公开赛（青岛站）</p>
				                    <p class="color12"></p>
				                    <div class="textcenter">
				                        <p class="eventsTypes">公开赛</p>
				                    </div>
				                    <p class="textleft phone_pe">
				                        <span>比赛时间：</span>
				                        <span>2021-09-10到2021-09-12</span>
				                    </p>
				                    <div class="textleft address clearfix">
				                        <div class="lf">比赛地点：</div>
				                        <div class="lf core_address" style="width: 180px">
				                            山东 青岛
				                        </div>
				                    </div>
				                    <p class="rf isStart" style="background:#66adff;">未开始</p>
				                </a>
				            </li>
				            <li class="clearfix background_we">
				                <a href="index.php?p=events_show" target="_blank">
				                    <img src="https://yoga.longcaisport.com/uploads/42c4795b378695d39cfa7bbc43a62ded.png">
				                    <p class="core_title color12">2021年全国健身瑜伽公开赛（金华站）</p>
				                    <p class="color12"></p>
				                    <div class="textcenter">
				                        <p class="eventsTypes">公开赛</p>
				                    </div>
				                    <p class="textleft phone_pe">
				                        <span>比赛时间：</span>
				                        <span>2021-05-22到2021-05-24</span>
				                    </p>
				                    <div class="textleft address clearfix">
				                        <div class="lf">比赛地点：</div>
				                        <div class="lf core_address" style="width: 180px">浙江 金华</div>
				                    </div>
				                    <p class="rf isStart">结束</p>
				                </a>
				            </li>
				            <li class="clearfix background_we">
				                <a href="index.php?p=events_show" target="_blank">
				                    <img src="https://yoga.longcaisport.com/uploads/c413a9b74a061fd34f8ed479e6f471eb.jpg">
				                    <p class="core_title color12">2021年全国健身瑜伽公开赛（青岛站）</p>
				                    <p class="color12"></p>
				                    <div class="textcenter">
				                        <p class="eventsTypes">公开赛</p>
				                    </div>
				                    <p class="textleft phone_pe">
				                        <span>比赛时间：</span>
				                        <span>2021-09-10到2021-09-12</span>
				                    </p>
				                    <div class="textleft address clearfix">
				                        <div class="lf">比赛地点：</div>
				                        <div class="lf core_address" style="width: 180px">
				                            山东 青岛
				                        </div>
				                    </div>
				                    <p class="rf isStart" style="background:#66adff;">未开始</p>
				                </a>
				            </li>
				        </ul>
				    </div>
				</div>
			<# } #>
		';
		return $output;
	}

	public function scripts()
	{
		// $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/newnav.js');
	  	//           '/components/com_jwpagefactory/addons/event_list/assets/js/jquery-1.11.3.min.js',

		// return $scripts;
	}

	static function isComponentInstalled($component_name)
	{
		$db    = JFactory::getDbo();
		$query = $db->getQuery(true);
		$query->select('a.enabled');
		$query->from($db->quoteName('#__extensions', 'a'));
		$query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
		$db->setQuery($query);
		$is_enabled = $db->loadResult();

		return $is_enabled;
	}

	//去掉url指定参数
	function removeqsvar($url, $var_names)
	{
		foreach ($var_names as $param)
		{
			$url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
			$url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
		}
		$url = trim($url, "?");
		$url = trim($url, "#");
		$url = trim($url, "&");
		$url = trim($url, "/");

		return $url;
	}


}