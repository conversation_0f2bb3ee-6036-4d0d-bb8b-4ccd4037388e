<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_audio',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_DESC'),
		'category' => '媒体',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),

				'separator_options' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS')
				),

				//音频样式  2023.5.31
				'music_list' => array(
					'type' => 'select',
					'title' => JText::_('音频样式'),
					'values' => array(
						'type1' => JText::_('样式1'),
						'type2' => JText::_('样式2'),
					),
					'std' => 'type1',
				),
				//
				'mp3_link' => array(
					'type' => 'media',
					'format' => 'audio',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_MP3_URL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_MP3_URL_DESC'),
					'format' => 'audio',
					'hide_preview' => true,
					'std' => ''
				),
				//
				'music_type2_width' => array(
					'type' => 'slider',
					'title' => JText::_('宽度'),
					'std' => 1000,
					'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
				),
				'music_type2_bg' => array(
					'type' => 'color',
					'title' => JText::_('背景色'),
					'std' => '#F5F5F5',
					'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
				),
				'music_img' => array(
                    'type' => 'media',
                    'title' => JText::_('歌曲logo'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230531/6c2010f5c6dc8de7d3e5effa92535cef.png',
                    'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
                ),
                'music_title' => array(
                    'type' => 'text',
                    'title' => JText::_('歌曲名称'),
                    'std' => '协会会歌',
                    'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
                ),
                'music_author' => array(
                    'type' => 'text',
                    'title' => JText::_('歌曲作者'),
                    'std' => 'CHQA',
                    'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
                ),
                'music_ypimg' => array(
                    'type' => 'media',
                    'title' => JText::_('音频图片'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20230531/2b6e7358c32be79a37e033d51d8e5f23.png',
                    'depends' => array(
                        array('music_list', '=', 'type2'),
                    ),
                ),
                //


				'ogg_link' => array(
					'type' => 'media',
					'format' => 'audio',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_OGG_URL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_OGG_URL_DESC'),
					'format' => 'audio',
					'hide_preview' => true,
					'std' => '',
                    'depends' => array(array('music_list', '!=', 'type2')),

				),

				'autoplay' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_AUTOPLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_AUTOPLAY_DESC'),
					'values' => array(
						'' => JText::_('JNO'),
						'autoplay' => JText::_('JYES'),
					),
					'std' => '',
                    'depends' => array(array('music_list', '!=', 'type2')),

				),

				'repeat' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_REPEAT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_AUDIO_REPEAT_DESC'),
					'values' => array(
						'' => JText::_('JNO'),
						'loop' => JText::_('JYES'),
					),
					'std' => '',
                    'depends' => array(array('music_list', '!=', 'type2')),

				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
