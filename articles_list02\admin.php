<?php
defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'articles_list02',
		'title' => '文章列表2',
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
		'category' => '文章',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),
				'title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => '',
				),
				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),
				// 'title_font_family' => array(
				// 	'type' => 'fonts',
				// 	'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
				// 	'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
				// 	'depends' => array(array('title', '!=', '')),
				// 	'selector' => array(
				// 		'type' => 'font',
				// 		'font' => '{{ VALUE }}',
				// 		'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
				// 	)
				// ),
				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),
				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),
				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),
				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'0' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),
				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),
				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),
				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'responsive' => true,
					'max' => 400,
				),
				'separator_options' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ADDON_OPTIONS')
				),
				'detail_page_id' => array(
					'type' => 'select',
					'title' => '详情页模版',
					'desc' => '显示文章详情页模版',
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),
				'detail_target' => array(
					'type' => 'select',
					'title' => '详情页跳转方式',
					'values' => array(
						'_self' => '当前页打开',
						'_blank' => '新标签打开',
						'none' => '不跳转',
					),
					'std' => '_self',
				),
				'art_type_selector' => array(
					'type' => 'thumbnail',
					'title' => '选择文章布局',
					'values' => array(
						'art80' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/articles_list02/assets/images/art80.jpg', //'布局80',
						'art81' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/articles_list02/assets/images/art81.png', //'布局81',
						'art82' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/articles_list02/assets/images/art82.png', //'布局82',
						'art83' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/articles_list02/assets/images/art83.jpg', //'布局83',
					),
					'std' => 'art80'
				),
				'separator_options03' => array(
					'type' => 'separator',
					'title' => '样式配置'
				),
				/* 布局80 样式配置 */
				'art80_layout' => array(
					'type' => 'select',
					'title' => '布局方式',
					'values' => array(
						'row' => '左图右文',
						'row-reverse' => '左文右图',
						'column' => '上图下文',
						'column-reverse' => '上文下图',
						'no-img' => '无图模式',
					),
					'std' => 'row',
					'depends' => array(
						array('art_type_selector', '=', 'art80')
					),
				),
				'art80_img_w' => array(
					'type' => 'slider',
					'title' => '图片宽度',
					'desc' => '图片宽度仅在布局方式为左图右文、左文右图中生效',
					'std' => array(
						'md' => 186,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80')
					),
					'responsive' => true,
					'max' => 2000,
				),
				'art80_img_h' => array(
					'type' => 'slider',
					'title' => '图片高度',
					'std' => array(
						'md' => 124,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80')
					),
					'responsive' => true,
					'max' => 2000,
				),
				'art80_img_style' => array(
					'type' => 'select',
					'title' => '图片填充方式',
					'values' => array(
							'scale-down' => '自适应显示',
							'fill' => '占满不切割显示',
							'cover' => '占满切割显示',
					),
					'std' => 'cover',
					'depends' => array(
						array('art_type_selector', '=', 'art80')
					),
				),
				'art80_style_state' => array(
					'type' => 'buttons',
					'title' => '状态',
					'std' => 'normal',
					'values' => array(
							array(
									'label' => '正常',
									'value' => 'normal'
							),
							array(
									'label' => '移入',
									'value' => 'hover'
							),
					),
					'tabs' => true,
					'depends' => array(
							array('art_type_selector', '=', 'art80'),
					),
				),
				'art80_item_bgcolor' => array(
					'type' => 'color',
					'title' => '背景颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
				),
				'art80_title_fontsize' => array(
					'type' => 'slider',
					'title' => '标题文字大小',
					'std' => array(
						'md' => 16,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'art80_title_line_h' => array(
					'type' => 'slider',
					'title' => '标题文字行高',
					'std' => array(
						'md' => 22,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'art80_title_color' => array(
					'type' => 'color',
					'title' => '标题文字颜色',
					'std' => '#0B5EA0',
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
				),
				'art80_time_fontsize' => array(
					'type' => 'slider',
					'title' => '日期文字大小',
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'art80_time_color' => array(
					'type' => 'color',
					'title' => '日期文字颜色',
					'std' => '#9E9E9E',
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
				),
				'art80_intro_fontsize' => array(
					'type' => 'slider',
					'title' => '简介文字大小',
					'std' => array(
						'md' => 14,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'art80_intro_line_h' => array(
					'type' => 'slider',
					'title' => '简介文字行高',
					'std' => array(
						'md' => 26,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'art80_intro_color' => array(
					'type' => 'color',
					'title' => '简介文字颜色',
					'std' => '#333333',
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'normal'),
					),
				),
				'art80_item_bgcolor_hover' => array(
					'type' => 'color',
					'title' => '移入背景颜色',
					'std' => '#E9F9FF',
					'depends' => array(
						array('art_type_selector', '=', 'art80'),
						array('art80_style_state', '=', 'hover'),
					),
				),

				// 布局81
				'show_zsy_art81' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否固定宽高开启图片填充方式'),
					'desc' => JText::_('是否固定宽高开启图片填充方式'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 0,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					)
				),
				'img_gd_height_art81' => array(
					'type' => 'slider',
					'title' => JText::_('固定图片宽高'),
					'max' => 2000,
					'min' => 1,
					'std' => 500,
					'responsive' => true,
					'depends' => array(
						array('show_zsy_art81', '=', 1),
						array('art_type_selector', '=', 'art81')
					),
				),
				'img_style_art81' => array(
					'type' => 'select',
					'title' => JText::_('图片填充方式'),
					'values' => array(
						'scale-down' => JText::_('自适应显示'),
						'fill' => JText::_('占满不切割显示'),
						'cover' => JText::_('占满切割显示'),
					),
					'std' => 'fill',
					'depends' => array(
						array('show_zsy_art81', '=', 1),
						array('art_type_selector', '=', 'art81')
					),
				),
				'part01_art81' => array(
					'type' => 'separator',
					'title' => '标题配置',
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				// 标题配置
				'title_bgcolor_art81' => array(
					'type' => 'color',
					'title' => '标题背景颜色',
					'std' => 'rgba(0, 0, 0, 0.64)',
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'title_fontsize_art81' => array(
					'type' => 'slider',
					'title' => '标题文字大小',
					'max' => 100,
					'std' => array('md' => 18, 'sm' =>'', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'title_lineHeight_art81' => array(
					'type' => 'slider',
					'title' => '标题文字行高',
					'max' => 100,
					'std' => array('md' => 52, 'sm' => '', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'title_color_art81' => array(
					'type' => 'color',
					'title' => '标题文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'title_padding_art81' => array(
					'type' => 'padding',
					'title' => '标题文字内边距',
					'std' => array('md' => '0px 200px 0px 20px', 'sm' => '', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				// 轮播相关
				'swiper_settings_art81' => array(
					'title' => '轮播设置',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				// 轮播图切换动画 适用swiper
				'animate_dh_art81' => array(
					'type' => 'select',
					'title' => '切换动画',
					'values' => array(
						'slide' => '默认',
						'fade' => '淡入',
						'cube' => '方块',
						'coverflow' => '3d流',
						'flip' => '3d翻转',
						//'cards' => '卡片式',  // Swiper 6.6.2 不支持
						//'creative' => '创意性',  // Swiper 6.6.2 不支持
					),
					'std' => 'fade',
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'autoplay_art81' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY_DESC'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 1,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				'interval_art81' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_INTERVAL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_INTERVAL_DESC'),
					'std' => 5,
					'depends' => array(
						array('autoplay', '=', 1),
						array('art_type_selector', '=', 'art81')
					)
				),
				'speed_art81' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED_DESC'),
					'std' => 600,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					)
				),
				'swiper_style_art81' => array(
					'type' => 'buttons',
					'title' => '切换选项状态',
					'std' => 'pagination_s',
					'values' => array(
						array(
							'label' => '翻页配置',
							'value' => 'button_s'
						),
						array(
							'label' => '轮播点配置',
							'value' => 'pagination_s'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81')
					),
				),
				// 箭头
				'is_swiper_button_art81' => array(
					'type' => 'checkbox',
					'title' => '开启切换按钮',
					'std' => 0,
					'depends' => array(
						array('swiper_style_art81', '=', 'button_s'),
						array('art_type_selector', '=', 'art81')
					),
				),
				'swiper_button_prev_art81' => array(
					'type' => 'media',
					'title' => '上一页',
					'std' => 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				'swiper_button_next_art81' => array(
					'type' => 'media',
					'title' => '下一页',
					'std' => 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				'swiper_button_prev_hover_art81' => array(
					'type' => 'media',
					'title' => '移入上一页',
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				'swiper_button_next_hover_art81' => array(
					'type' => 'media',
					'title' => '移入下一页',
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				'swiper_button_width_art81' => array(
					'type' => 'slider',
					'title' => '切换按钮宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 40,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				'swiper_button_height_art81' => array(
					'type' => 'slider',
					'title' => '切换按钮高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 40,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'button_s'),
						array('is_swiper_button_art81', '=', 1),
					),
				),
				// 轮播点配置
				'is_swiper_pagination_art81' => array(
					'type' => 'checkbox',
					'title' => '开启轮播点',
					'std' => 1,
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
					),
				),
				'pagination_style_art81' => array(
					'type' => 'buttons',
					'title' => '轮播点状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '选中',
							'value' => 'active'
						),
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('is_swiper_pagination_art81', '=', 1),
					),
					'tabs' => true,
				),
				'swiper_p_width_art81' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 18,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'normal'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_height_art81' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 7,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'normal'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_margin_art81' => array(
					'type' => 'slider',
					'title' => '轮播点间距',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 5,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'normal'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_border_art81' => array(
					'type' => 'slider',
					'title' => '轮播点圆角',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 1,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'normal'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_color_art81' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => 'rgba(255,255,255,0.52)',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'normal'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				//轮播点选中
				'swiper_p_width_hover_art81' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'active'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_height_hover_art81' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'active'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),
				'swiper_p_color_hover_art81' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('art_type_selector', '=', 'art81'),
						array('swiper_style_art81', '=', 'pagination_s'),
						array('pagination_style_art81', '=', 'active'),
						array('is_swiper_pagination_art81', '=', 1),
					),
				),

				// 布局82开始
				// 主题色
				'theme_color_82' => array(
					'type' => 'color',
					'title' => JText::_('主题色'),
					'std' => '#C7000B',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
					)
				),
				// 整体设置选项卡
				'layout_82' => array(
					'type' => 'buttons',
					'title' => JText::_('设置项'),
					'values' => array(
						array(
								'label' => '列表项',
								'value' => 'list_item'
						),
						array(
								'label' => '标题',
								'value' => 'title'
						),
						array(
								'label' => '简介',
								'value' => 'intro'
						),
						array(
								'label' => '查看详情',
								'value' => 'btn'
						),
					),
					'std' => 'list_item',
					'tabs' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
					)
				),
				// 每行排列列数
				'col_num_82' => array(
					'type' => 'slider',
					'title' => JText::_('每行列数'),
					'std' => array('md' => '2', 'sm' => '2', 'xs' => '1'),
					'min' => '1',
					'max' => '10',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 列间距
				'col_gap_82' => array(
					'type' => 'slider',
					'title' => JText::_('列间距'),
					'std' => array('md' => '18', 'sm' => '18', 'xs' => '18'),
					'min' => '0',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 每项高度
				'item_height_82' => array(
					'type' => 'slider',
					'title' => JText::_('每项高度'),
					'std' => array('md' => '221', 'sm' => '300', 'xs' => '300'),
					'min' => '0',
					'max' => '1000',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 行间距
				'row_gap_82' => array(
					'type' => 'slider',
					'title' => JText::_('行间距'),
					'std' => array('md' => '30', 'sm' => '30', 'xs' => '30'),
					'min' => '0',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 边框宽度
				'border_width_82' => array(
					'type' => 'margin',
					'title' => JText::_('边框宽度'),
					'std' => '3px 0 0 0',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 边框类型
				'border_style_82' => array(
					'type' => 'select',
					'title' => JText::_('边框类型'),
					'values' => array(
						'solid' => JText::_('实线'),
						'dotted' => JText::_('点状线'),
						'dashed' => JText::_('虚线'),
					),
					'std' => 'solid',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 背景色
				'bg_color_82' => array(
					'type' => 'color',
					'title' => JText::_('背景颜色'),
					'std' => '#FAFAFA',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					)
				),
				// 内边距
				'padding_82' => array(
					'type' => 'margin',
					'title' => JText::_('内边距'),
					'std' => array('md' => '34px 24px 22px 24px', 'sm' => '24px 22px 22px 24px', 'xs' => '20px 18px 18px 20px'),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'list_item')
					),
					'responsive' => true,
				),
				// 小图标配置项
				'icon_settings_82' => array(
					'title' => '小图标',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title')
					),
				),
				// 标题前是否显示小图标
				'icon_before_title' => array(
					'type' => 'checkbox',
					'title' => JText::_('标题前是否显示小图标'),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title')
					),
					'std' => 1
				),
				// 小图标
				'icon_82' => array(
					'type' => 'icon',
					'title' => JText::_('小图标'),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('icon_before_title', '=', '1'),
					)
				),
				// 小图标大小
				'icon_size_82' => array(
					'type' => 'slider',
					'title' => JText::_('小图标大小'),
					'std' => '28',
					'min' => '12',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('icon_before_title', '=', '1'),
					)
				),
				// 小图标右边距
				'icon_margin_82' => array(
					'type' => 'slider',
					'title' => JText::_('小图标右边距'),
					'std' => '16',
					'min' => '0',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('icon_before_title', '=', '1'),
					)
				),
				// 标题设置项
				'title_settings_82' => array(
					'title' => '标题设置项',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					),
				),
				// 标题字号
				'title_font_size_82' => array(
					'type' => 'slider',
					'title' => JText::_('标题字号'),
					'std' => '18',
					'min' => '12',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 字体颜色
				'title_color_82' => array(
					'type' => 'color',
					'title' => JText::_('字体颜色'),
					'std' => '#333',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 标题字重
				'title_font_weight_82' => array(
					'type' => 'slider',
					'title' => JText::_('标题字重'),
					'std' => '600',
					'min' => '100',
					'max' => '900',
					'step' => '100',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 标题字体
				'title_font_family_82' => array(
					'type' => 'fonts',
					'title' => JText::_('标题字体'),
					'std' => 'inherit',
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 标题右边距
				'title_margin_82' => array(
					'type' => 'slider',
					'title' => JText::_('标题右边距'),
					'std' => '14',
					'min' => '0',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 标题下边距
				'title_margin_bottom_82' => array(
					'type' => 'slider',
					'title' => JText::_('标题下边距'),
					'std' => '24',
					'min' => '0',
					'max' => '100',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 时间设置项
				'time_settings_82' => array(
					'title' => '时间设置项',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					),
				),
				// 是否展示时间
				'show_time_82' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否展示时间'),
					'std' => 1,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
					)
				),
				// 时间字号
				'time_font_size_82' => array(
					'type' => 'slider',
					'title' => JText::_('时间字号'),
					'std' => '16',
					'min' => '12',
					'max' => '50',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('show_time_82', '=', '1'),
					)
				),
				// 时间字体
				'time_font_family_82' => array(
					'type' => 'fonts',
					'title' => JText::_('时间字体'),
					'std' => 'inherit',
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('show_time_82', '=', '1'),
					)
				),
				// 时间字体颜色
				'time_color_82' => array(
					'type' => 'color',
					'title' => JText::_('时间字体颜色'),
					'std' => '#7F7F7F',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('show_time_82', '=', '1'),
					)
				),
				// 时间字重
				'time_font_weight_82' => array(
					'type' => 'slider',
					'title' => JText::_('时间字重'),
					'std' => '',
					'min' => '100',
					'max' => '900',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('show_time_82', '=', '1'),
					)
				),
				// 时间显示格式
				'time_format_82' => array(
					'type' => 'select',
					'title' => JText::_('时间显示格式'),
					'std' => 'Y-m-d',
					'values' => array(
						'Y-m-d' => 'Y-m-d',
						'Y/m/d' => 'Y/m/d',
						'Y年m月d日' => 'Y年m月d日',
						'm月d日' => 'm月d日',
						'd日' => 'd日',
						'H:i' => 'H:i',
						'H:i:s' => 'H:i:s',
						'Y年m月d日 H:i:s' => 'Y年m月d日 H:i:s',
						'Y年m月d日 H:i' => 'Y年m月d日 H:i',
						'Y-m-d H:i:s' => 'Y-m-d H:i:s',
						'Y-m-d H:i' => 'Y-m-d H:i',
						'm月d日 H:i' => 'm月d日 H:i',
						'd日 H:i' => 'd日 H:i',
						'm月d日 H:i:s' => 'm月d日 H:i:s',
						'd日 H:i:s' => 'd日 H:i:s',
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'title'),
						array('show_time_82', '=', '1'),
					)
				),
				// 简介设置项
				'intro_settings_82' => array(
					'title' => '简介设置项',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					),
				),
				// 简介字号
				'intro_font_size_82' => array(
					'type' => 'slider',
					'title' => JText::_('简介字号'),
					'std' => '16',
					'min' => '12',
					'max' => '50',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 简介字体
				'intro_font_family_82' => array(
					'type' => 'fonts',
					'title' => JText::_('简介字体'),
					'std' => 'inherit',
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-intro { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 简介字体颜色
				'intro_color_82' => array(
					'type' => 'color',
					'title' => JText::_('简介字体颜色'),
					'std' => '#333',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 简介字重
				'intro_font_weight_82' => array(
					'type' => 'slider',
					'title' => JText::_('简介字重'),
					'std' => '',
					'min' => '100',
					'max' => '900',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 简介行高
				'intro_line_height_82' => array(
					'type' => 'slider',
					'title' => JText::_('简介行高'),
					'std' => '34',
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 简介是否首行缩进
				'intro_first_line_indent_82' => array(
					'type' => 'checkbox',
					'title' => JText::_('简介是否首行缩进'),
					'std' => 1,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'intro'),
					)
				),
				// 查看详情设置项
				'btn_settings_82' => array(
					'title' => '查看详情设置项',
					'type' => 'separator',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
					),
				),
				// 查看详情正常和鼠标移入切换按钮
				'btn_style_82' => array(
					'type' => 'buttons',
					'title' => JText::_('查看详情样式'),
					'values' => array(
						array(
								'label' => '正常',
								'value' => 'normal'
						),
						array(
							'label' => '鼠标移入切换',
							'value' => 'hover'
						),
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
					),
				),
				// 是否显示查看详情
				'show_btn_82' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否显示查看详情'),
					'std' => 1,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('btn_style_82', '=', 'normal'),
					),
				),
				// 查看详情外边距
				'btn_margin_82' => array(
					'type' => 'margin',
					'title' => JText::_('查看详情外边距'),
					'std' => array('md' => '18px 0 0 0', 'sm' => '18px 0 0 0', 'xs' => '18px 0 0 0'),
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情文字
				'btn_text_82' => array(
					'type' => 'text',
					'title' => JText::_('查看详情文字'),
					'std' => '查看详情 >>',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮宽度
				'btn_width_82' => array(
					'type' => 'slider',
					'title' => JText::_('查看详情按钮宽度'),
					'std' => '',
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮高度
				'btn_height_82' => array(
					'type' => 'slider',
					'title' => JText::_('查看详情按钮高度'),
					'std' => '',
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮内边距
				'btn_padding_82' => array(
					'type' => 'padding',
					'title' => JText::_('查看详情按钮内边距'),
					'std' => array('md' => '', 'sm' => '', 'xs' => ''),
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮圆角
				'btn_border_radius_82' => array(
					'type' => 'margin',
					'title' => JText::_('查看详情按钮圆角'),
					'std' => '0 0 0 0',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮背景颜色
				'btn_bg_color_82' => array(
					'type' => 'color',
					'title' => JText::_('查看详情按钮背景颜色'),
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮字体颜色
				'btn_color_82' => array(
					'type' => 'color',
					'title' => JText::_('查看详情按钮字体颜色'),
					'std' => '#fff',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮字体大小
				'btn_font_size_82' => array(
					'type' => 'slider',
					'title' => JText::_('查看详情按钮字体大小'),
					'std' => '14',
					'min' => '0',
					'max' => '300',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情字体
				'btn_font_family_82' => array(
					'type' => 'fonts',
					'title' => JText::_('查看详情字体'),
					'std' => 'inherit',
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-intro { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情字重
				'btn_font_weight_82' => array(
					'type' => 'slider',
					'title' => JText::_('查看详情字重'),
					'std' => '400',
					'min' => '0',
					'max' => '1000',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情鼠标移入按钮背景颜色
				'btn_bg_hover_color_82' => array(
					'type' => 'color',
					'title' => JText::_('查看详情按钮背景颜色'),
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 查看详情按钮字体颜色
				'btn_hover_color_82' => array(
					'type' => 'color',
					'title' => JText::_('查看详情按钮字体颜色'),
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art82'),
						array('layout_82', '=', 'btn'),
						array('show_btn_82', '=', '1'),
						array('btn_style_82', '=', 'normal'),
					)
				),
				// 布局82结束
				// 布局83开始
				// 图片高度
				'img_height_art83' => array(
					'type' => 'slider',
					'title' => '图片高度',
					'std' => array('md' => '300', 'sm' => '', 'xs' => ''),
					'min' => '0',
					'max' => '1000',
					'responsive' => true,
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				// 图片上卡片信息背景颜色
				'card_bg_color_art83' => array(
					'type' => 'color',
					'title' => '图片上卡片信息背景颜色',
					'std' => '#0078d4',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				// 卡片倒三角颜色
				'card_triangle_color_art83' => array(
					'type' => 'color',
					'title' => '卡片倒三角颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				'swiper_button_prev_art83' => array(
					'type' => 'media',
					'title' => '上一页',
					'std' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMjQgMTJMMTYgMjBMMjQgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				'swiper_button_next_art83' => array(
					'type' => 'media',
					'title' => '下一页',
					'std' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMTYgMTJMMjQgMjBMMTYgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				'swiper_button_prev_hover_art83' => array(
					'type' => 'media',
					'title' => '移入上一页',
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				'swiper_button_next_hover_art83' => array(
					'type' => 'media',
					'title' => '移入下一页',
					'std' => '',
					'depends' => array(
						array('art_type_selector', '=', 'art83'),
					),
				),
				// 布局83结束
				/* 数据相关 */
				'separator_options02' => array(
					'type' => 'separator',
					'title' => '数据相关'
				),
				'catid' => array(
					'type' => 'category',
					'title' => '选择分类',
					'desc' => '从列表中选择建站通客户端添加的文章分类',
					'multiple' => true,
					'depends' => array(),
				),
				'include_subcat' => array(
					'type' => 'select',
					'title' => '包含子分类',
					'values' => array(
						1 => '包含子分类',
						0 => '不包含子分类',
					),
					'std' => 1,
				),
				'ordering' => array(
					'type' => 'select',
					'title' => '排序',
					'desc' => '从列表中选择文章的排序',
					'values' => array(
						'timedesc' => '添加时间降序',
						'timeasc' => '添加时间升序',
						'sortdesc' => '排序id倒序',
						'sortasc' => '排序id正序',
					),
					'std' => 'sortdesc',
				),
				'limit' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
					'std' => '10',
					'depends' => array()
				),
				'columns' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
					'std' => '2',
					'depends' => array(),
				),
				'columns_xs' => array(
					'type' => 'number',
					'title' => JText::_('手机列数'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
					'std' => '2',
					'depends' => array(),
				),
				'show_intro' => array(
					'type' => 'checkbox',
					'title' => JText::_('显示简介'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
					'std' => 1,
					'depends' => array(),
				),
				'use_detail_instead_intro' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否使用详情充作简介'),
					'std' => 0,
					'depends' => array(
						array('show_intro', '=', 1)
					),
				),
				'intro_limit' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_INTRO_LIMIT_DESC'),
					'std' => '200',
					'depends' => array(),
				),

				/* 翻页 */
				'show_page' => array(
					'type' => 'checkbox',
					'title' => JText::_('显示翻页码'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
					'std' => '0',
					'depends' => array(),
				),
				'page_style_selector' => array(
					'type' => 'select',
					'title' => '翻页样式',
					'values' => array(
						'page01' => '翻页样式一',
						'page02' => '翻页样式二',
						'page03' => '翻页样式三',
						'page04' => '翻页样式四',
						'page05' => '翻页样式五',
					),
					'depends' => array('show_page' => true),
					'std' => 'page01'
				),
				'page1_fontcolor' => array(
					'type' => 'color',
					'title' => JText::_('翻页字体颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#1e1e1e',
				),
				'page1_bordercolor' => array(
					'type' => 'color',
					'title' => JText::_('翻页边框颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#b3b3b3',
				),
				'page1_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('翻页背景颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#b3b3b3',
				),
				'page1_cur_fontcolor' => array(
					'type' => 'color',
					'title' => JText::_('当前页字体颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#ffffff',
				),
				'page1_cur_bordercolor' => array(
					'type' => 'color',
					'title' => JText::_('当前页边框颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#2a68a7',
				),
				'page1_cur_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('当前页背景颜色'),
					'depends' => array(
						array('show_page', '=', true),
					),
					'std' => '#2a68a7',
				),
			)
		),
	)
);
