<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'product_tab',
        'title' => '产品分类选项卡',
        'desc' => '产品分类选项卡',
        'category' => '产品',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
                    )
                ),
                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题背景颜色'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题宽度（%）'),
                    'max' => 1000,
                    'unit' => true,
                    'responsive' => true,
                    'std' => array(
                        'md' => '10',
                        'unit' => '%'
                    ),
                    'depends' => array(array('title', '!=', '')),
                ),
                'separator_options_01' => array(
                    'type' => 'separator',
                    'title' => '配置选项'
                ),
                'setting_part' => array(
                    'type' => 'buttons',
                    'title' => '配置选项',
                    'std' => 'menu',
                    'values' => array(
                        array(
                            'label' => '导航',
                            'value' => 'menu'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '翻页',
                            'value' => 'pages'
                        ),
                        array(
                            'label' => '内容数据',
                            'value' => 'content_data'
                        ),
                    ),
                    'tabs' => true
                ),
                //导航配置
                'style' => array(
                    'type' => 'select',
                    'title' => JText::_('导航样式'),
                    'desc' => JText::_('选择导航样式（内容布局6是请选择默认）'),
                    'values' => array(
                        'modern' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_MODERN'),
                        'tabs' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DEFAULT'),
                        'pills' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_PILLS'),
                        'lines' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_LINES'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_CUSTOM'),
                        'type6' => JText::_('样式6'),
                    ),
                    'std' => 'tabs',
                    'depends' =>array(
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'tab_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航设置'),
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
//                        array('style', '=', 'custom'),
                    ),
                ),
                // 内容布局6 type7 配置项
                'pro7_title' => array(
                    'type' => 'text',
                    'title' => '导航左侧标题',
                    'std' => '产品展示',
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('pro_type', '=', 'type7'),
                    ),
                ),
                'pro7_desc' => array(
                    'type' => 'text',
                    'title' => '导航左侧副标题',
                    'std' => 'Products Show',
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('pro_type', '=', 'type7'),
                    ),
                ),
                'pro7_title_color' => array(
                    'type' => 'color',
                    'title' => '导航左侧标题颜色',
                    'std' => '#393939',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'pro7_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '导航左侧标题文字大小',
                    'std' => 24,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'pro7_desc_color' => array(
                    'type' => 'color',
                    'title' => '导航左侧副标题颜色',
                    'std' => '#6a6a6a',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'pro7_desc_fontsize' => array(
                    'type' => 'slider',
                    'title' => '导航左侧副标题文字大小',
                    'std' => 20,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'pro7_nav_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('右侧导航样式'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'pro7_tab_width' => array(
                    'type' => 'slider',
                    'title' => '导航宽度',
                    'std' => 104,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_height' => array(
                    'type' => 'slider',
                    'title' => '导航高度',
                    'std' => 42,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_mgL' => array(
                    'type' => 'slider',
                    'title' => '导航间距',
                    'std' => 42,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_fontsize' => array(
                    'type' => 'slider',
                    'title' => '导航文字大小',
                    'std' => 16,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_color' => array(
                    'type' => 'color',
                    'title' => '导航文字颜色',
                    'std' => '#6a6a6a',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_bgColor' => array(
                    'type' => 'color',
                    'title' => '导航背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'normal'),
                    ),
                ),
                'pro7_tab_color_active' => array(
                    'type' => 'color',
                    'title' => '导航文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'active'),
                    ),
                ),
                'pro7_tab_bgColor_active' => array(
                    'type' => 'color',
                    'title' => '导航背景颜色',
                    'std' => '#5ea1cb',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                        array('pro7_nav_style' ,'=', 'active'),
                    ),
                ),
                //自定义标签样式选项
//                'custom_tab_style' => array(
//                    'type' => 'buttons',
//                    'title' => JText::_('导航样式选项'),
//                    'std' => 'navigation',
//                    'values' => array(
//                        array(
//                            'label' => '导航',
//                            'value' => 'navigation'
//                        ),
////                        array(
////                            'label' => '导航图标或者图片',
////                            'value' => 'icon_image'
////                        ),
//                        array(
//                            'label' => '内容',
//                            'value' => 'content'
//                        ),
//                    ),
//                    'tabs' => true,
//                    'depends' => array(
//                        array('style', '=', 'custom')
//                    ),
//                ),
                'nav_position' => array(
                    'type' => 'select',
                    'title' => JText::_('导航整体位置'),
                    'desc' => JText::_('用来设置导航整体的位置'),
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                    ),
                    'values' => array(
                        'nav-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'nav-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'nav-top' => JText::_('上')
                    ),
                ),
                // 非自定义导航块位置
                'nav_all_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '!=', 'custom'),
                        array('pro_type', '!=', 'type7'),
                    ),
                ),
                // 自定义导航块
                'nav_block_positon' => array(
                    'type' => 'select',
                    'title' => '导航横向位置',
                    'desc' => JText::_('用来设置导航整体位置选择上后横向位置'),
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                        array('nav_position', '=', 'nav-top'),

                    ),
                ),
                'nav_font_position' => array(
                    'type' => 'select',
                    'title' => '导航字体居中设置',
                    'std' => 'flex-start',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_left_position' => array(
                    'type' => 'slider',
                    'title' => '导航整体右移',
                    'std' => '0',
                    'max' => '1000',
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                        array('nav_position', '=', 'nav-top'),

                    ),
                ),
                'nav_gutter' => array(
                    'type' => 'slider',
                    'title' => JText::_('在导航和内容之间的空间'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER_DESC'),
                    'max' => 100,
                    'std' => array('md' => 15),
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                    ),
                ),
                'nav_font_width' => array(
                    'type' => 'slider',
                    'title' => '导航文字自定义位置',
                    'std' => '',
                    'max' => 200,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_gundong' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航滚动'),
                    'desc' => JText::_('是否开启导航滚动'),
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'!=', 'custom'),
                        array('pro_type' ,'!=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_wrap' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航换行'),
                    'desc' => JText::_('是否开启导航自动换行'),
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_bottom_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航底部设置部分'),
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_bottom' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启导航底部图片'),
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //导航底部图片
                'nav_bottom_img' => array(
                    'type' => 'media',
                    'title' => '导航底部图片',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_bottom', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //导航底部图片宽高
                'nav_bottom_img_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航底部图片宽度（百分比）'),
                    'std' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_bottom', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
//                'nav_bottom_img_h' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('导航底部图片高度（px）'),
//                    'std' => 100,
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_bottom', '=', 1),
//                        array('setting_part' ,'=', 'menu'),
//                    ),
//                ),
                'nav_bottom_img_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航底部图片和导航之间的空间'),
                    'std' => 0,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_bottom', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),

                'nav_cont_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航头部设置部分'),
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_cont' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航头部设置'),
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //导航头部图片
                'nav_img' => array(
                    'type' => 'media',
                    'title' => '导航头部图片',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //导航头部图片宽高
                'nav_img_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片宽度'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_img_h' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片高度'),
                    'max' => 300,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_img_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部图片和导航之间的空间'),
                    'max' => 300,
                    'std' => array('md' => 10, 'sm' => 10, 'xs' => 10),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),

//                导航头部的文字
                'nav_title_show' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否展示导航头部文字'),
                    'std'=>'0',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_title' => array(
                    'type' => 'text',
                    'title' => JText::_('导航头部文字'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_title_show' ,'=', '1'),
                    ),
                ),
                'nav_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部文字字号'),
                    'std'=>16,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_title_show' ,'=', '1'),
                    ),
                ),
                'nav_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航头部文字字体颜色'),
                    'std'=>'#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_title_show' ,'=', '1'),
                    ),
                ),
                'nav_font_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部文字左边距（%）'),
                    'std'=>2,
                    'max'=>100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_title_show' ,'=', '1'),
                    ),
                ),
                'nav_font_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航头部文字上边距（%）'),
                    'std'=>45,
                    'max'=>100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_title_show' ,'=', '1'),
                    ),
                ),

                //导航外层边框
                'nav_bor' => array(
                    'type' => 'margin',
                    'title' => '导航外层边框',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'std' => '0px 0px 0px 0px',
                ),
                //导航外层边框颜色
                'nav_bor_color' => array(
                    'type' => 'color',
                    'title' => '导航外层边框颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //导航外层背景颜色
                'nav_back_color' => array(
                    'type' => 'color',
                    'title' => '导航外层背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_cont', '=', 1),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_all_bg_color' => array(
                    'type' => 'color',
                    'title' => '导航条背景颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('style', '!=', 'custom'),
                        array('pro_type', '!=', 'type7'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('一级导航样式'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'is_nav_cate_img' => array(
                    'type' => 'checkbox',
                    'title' => '一级导航前显示分类图片',
                    'desc' => '是否开启一级导航前显示分类图片',
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_style', '=', 'normal'),
                    ),
                ),
                'is_nav_cate_img_hover' => array(
                    'type' => 'checkbox',
                    'title' => '一级导航前显示分类图片移入或选中更改',
                    'desc' => '',
                    'std' => '0',
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_style', '=', 'normal'),
                        array('is_nav_cate_img', '=', '1'),
                    ),
                ),
                'nav_cate_width' => array(
                    'type' => 'slider',
                    'title' => '一级导航前分类图片宽度',
                    'std' => array(
                        'md' => 30,
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_style', '=', 'normal'),
                        array('is_nav_cate_img', '=', '1'),
                    ),
                ),
                'nav_cate_height' => array(
                    'type' => 'slider',
                    'title' => '一级导航前分类图片高度',
                    'std' => array(
                        'md' => 30,
                        'sm' => '',
                        'xs' => ''
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('style' ,'=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                        array('nav_style', '=', 'normal'),
                        array('is_nav_cate_img', '=', '1'),
                    ),
                ),
                'is_nav_before_icon' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启一级导航前字符'),
                    'std' => '0',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
//                'nav_before_icon' => array(
//                    'type' => 'text',
//                    'title' => JText::_('一级导航前字符'),
//                    'std' => '—',
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_style', '=', 'normal'),
//                        array('setting_part' ,'=', 'menu'),
//                        array('is_nav_before_icon' ,'=', '1'),
//                    ),
//                ),
                'is_nav_after_icon' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启一级导航后圆点'),
                    'std' => '0',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
//                'nav_after_icon' => array(
//                    'type' => 'text',
//                    'title' => JText::_('一级导航前字符'),
//                    'std' => '—',
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_style', '=', 'normal'),
//                        array('setting_part' ,'=', 'menu'),
//                        array('is_nav_after_icon' ,'=', '1'),
//                    ),
//                ),
                'nav_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'max' => 100,
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_width_phone' => array(
                    'type' => 'slider',
                    'title' => JText::_('手机一级导航宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'max' => 100,
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航背景颜色'),
                    'std' => '#463f43',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_bg_img' => array(
                    'type' => 'media',
                    'title' => JText::_('一级导航背景图片'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'max' => 400,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航行高'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('一级导航字体'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('一级导航字体样式'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border' => array(
                    'type' => 'margin',
                    'title' => JText::_('一级导航边框'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航边框颜色'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航边框半径'),
                    'std' => '70',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('一级导航空白空间'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'std' => '0px 10px 5px 10px',
                ),
                'nav_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('一级导航内边距'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
//                'nav_text_align' => array(
//                    'type' => 'select',
//                    'title' => JText::_('一级导航文本与图标对齐'),
//                    'desc' => JText::_('一级导航文本与图标在导航内按照选择的对齐方式对齐'),
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_style', '=', 'normal'),
//                        array('custom_tab_style', '=', 'navigation'),
//                    ),
//                    'values' => array(
//                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
//                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
//                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
//                    ),
//                    'std' => 'left',
//                ),
                //Hover Nav Style
                'hover_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停背景色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停文字颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('导航悬停边框宽度'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航悬停边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //Active Nav Style
                'active_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('选中导航背景色'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('选中导航文本颜色'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('选中导航边框宽度'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('选中导航边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'tab_separator_2' => array(
                    'type' => 'separator',
                    'title' => JText::_('二级导航设置'),
                    'depends' => array(
                        array('setting_part' ,'=', 'menu'),
                        array('style', '=', 'custom'),
                    ),
                ),
                //二级导航
                'nav_style2' => array(
                    'type' => 'buttons',
                    'title' => JText::_('二级导航样式'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal2',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal2'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover2'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active2'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_width2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航宽度'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'max' => 100,
                    'std' => array('md' => 100, 'sm' => 100, 'xs' => 100),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#0d0d0d',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_bg_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#eeeeee',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_fontsize2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'max' => 400,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航行高'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family2' => array(
                    'type' => 'fonts',
                    'title' => JText::_('二级导航字体'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style2' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('二级导航字体样式'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border2' => array(
                    'type' => 'margin',
                    'title' => JText::_('二级导航边框'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航边框颜色'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_border_radius2' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航边框半径'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'nav_margin2' => array(
                    'type' => 'margin',
                    'title' => JText::_('二级导航空白空间'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'std' => '0px 0px 5px 0px',
                ),
                'nav_padding2' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航内边距'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'normal2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
//                'nav_text_align2' => array(
//                    'type' => 'select',
//                    'title' => JText::_('二级导航文本与图标对齐'),
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('nav_style2', '=', 'normal2'),
//                        array('custom_tab_style', '=', 'navigation'),
//                    ),
//                    'values' => array(
//                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
//                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
//                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
//                    ),
//                    'std' => 'left',
//                ),

                //Hover Nav Style
                'hover_tab_bg2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航悬停背景色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航悬停文字颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_border_width2' => array(
                    'type' => 'margin',
                    'title' => JText::_('二级导航悬停边框宽度'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'hover_tab_border_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航悬停边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'hover2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                //Active Nav Style
                'active_tab_bg2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中背景色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中文本颜色'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_border_width2' => array(
                    'type' => 'margin',
                    'title' => JText::_('二级导航选中边框宽度'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                'active_tab_border_color2' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航选中边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style2', '=', 'active2'),
                        array('setting_part' ,'=', 'menu'),
                    ),
                ),
                // 导航配置结束

                'nav_icon_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('图标位置'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'icon_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('图标字体大小'),
                    'max' => 400,
                    'std' => array('md' => 16),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('图标颜色'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('图标悬停颜色'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('图标点击颜色'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('图标外边距'),
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'std' => '',
                ),
                //Image Style
                'image_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('导航图像选项'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'nav_image_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('图像的位置'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'image_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片高度'),
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('图片宽度'),
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('外边距'),
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        // array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'std' => '',
                ),
                'pro_type' => array(
                    'type' => 'select',
                    'title' => '内容样式',
                    'desc' => '内容样式',
                    'values' => array(
                        'type1' => '标题覆盖图片',
                        'type2' => '标题在图片下方',
                        'type3' => '产品布局3',
                        'type4' => '自定义',
                        'type6' => '自定义2',
                        'type5' => '产品布局5',
                        'type7' => '产品布局6',
                        'type8' => '产品布局7',
                        'type9' => '产品布局9',
                        'type10' => '产品布局10',
                    ),
                    'std' => 'type1',
                    'depends' =>array(
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                // 2021.09.16 新增客户端获取
                //轮播内容来源
                'text_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('从客户端获取信息'),
                    'desc' => JText::_('开启后即从客户端获取信息'),
                    'std' => 0,
                    'depends' => array(
                        array('pro_type', '=', 'type6'),
                    ),
                ),
                //2022.5.10日新增轮播组选项
                'hqtype' => array(
                    'type' => 'select',
                    'title' => JText::_('获取类型'),
                    'values' => array(
                        'dan' => JText::_('单条信息'),
                        'zu' => JText::_('信息组'),
                    ),
                    'std' => 'dan',
                    'depends' => array(
                        array('text_from', '=', 1),
                    ),
                ),

                // 信息 单条内容类型
                'text_id' => array(
                    'type' => 'select',
                    'title' => JText::_('选择客户端对应的信息名称'),
                    'desc' => JText::_('对应客户端上传的信息名称，如果不选默认还显示编辑器添加的信息'),
                    'values' => JwPageFactoryBase::getinfoList($site_id, $company_id)['list'],
                    'depends' => array(
                        array('text_from', '=', 1),
                        array('hqtype', '!=', 'zu'),
                    ),
                ),

                // 信息组类型 2022.5.10
                'banner_typeid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择后台对应的信息分类'),
                    'desc' => JText::_('对应后台上传的信息分类名称，如果不选默认还显示编辑器添加的信息'),
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_info')['list'],
                    'depends' => array(
                        array('text_from', '=', 1),
                        array('hqtype', '!=', 'dan'),
                    ),
                ),
                // Repeatable Item
                'jw_tab_item' => array(
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEMS'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TITLE_DESC'),
                            'std' => '点我'
                        ),
                        'subtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_SUBTITLE_DESC'),
                            'std' => ' ',
                        ),
                        'image_or_icon' => array(
                            'type' => 'buttons',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE'),
                            'std' => 'icon',
                            'values' => array(
                                array(
                                    'label' => '图标',
                                    'value' => 'icon'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'image'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'icon' => array(
                            'type' => 'icon',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_DESC'),
                            'std' => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'icon'),
                            )
                        ),
                        'image' => array(
                            'type' => 'media',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE'),
                            'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_IMAGE_DESC'),
                            'std' => '',
                            'depends' => array(
                                array('image_or_icon', '=', 'image'),
                            )
                        ),
                        'content' => array(
                            'type' => 'editor',
                            'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT_DESC'),
                            'std' => '我们的高级生活谴责特里·理查森和鱿鱼。3狼月办公室，非丘比特滑板dolor早午餐。食品车藜麦nesciunt laborum eiusmod。早午餐3狼月tempor, sunt aliqua把一只鸟在它鱿鱼单一起源咖啡nulla假设enda shoreditch等。'
                        ),
                        'closeClick' => array(
                            'type' => 'checkbox',
                            'title' => '关闭点击事件',
                            'std' => 0
                        )
                    ),
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                        array('text_from', '!=', 1),
                    ),
                ),
                'separator_options01' => array(
                    'type' => 'separator',
                    'title' => '列表部分导航设置',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'show_list_nav' => array(
                    'type' => 'checkbox',
                    'title' => '开启列表部分导航',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'list_nav_style' => array(
                    'type' => 'select',
                    'title' => '列表部分导航样式',
                    'values' => array(
                        'nav01' => '布局01',
                        //'nav02' => '布局02',
                    ),
                    'std' => 'nav01',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_name' => array(
                    'type' => 'text',
                    'title' => '列表部分导航标题',
                    'std' => '新闻中心',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_bg_style' => array(
                    'type' => 'buttons',
                    'title' => '背景选项',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '颜色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                    'tabs' => true,
                ),
                'list_nav_bgColor' => array(
                    'type' => 'color',
                    'title' => '列表部分导航背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                        array('list_nav_bg_style' ,'=', 'color'),
                    ),
                ),
                'list_nav_bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '列表部分导航渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                        array('list_nav_bg_style', '=', 'gradient')
                    ),
                ),
                'list_nav_borderColor' => array(
                    'type' => 'color',
                    'title' => '列表部分导航描边颜色',
                    'std' => '#eee',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_border_h' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航描边宽度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_mb' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航距离底部距离',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_h' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_left_p' => array(
                    'type' => 'margin',
                    'title' => '列表部分导航左侧文字内边距',
                    'max' => 300,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    )
                ),
                'list_nav_left_title_f' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航左侧文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_left_title_h' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航左侧文字行高',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_left_title_fColor' => array(
                    'type' => 'color',
                    'title' => '列表部分导航左侧文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_left_lineColor' => array(
                    'type' => 'color',
                    'title' => '列表部分导航左侧线条颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_left_img' => array(
                    'type' => 'media',
                    'title' => '列表部分导航左侧图标','depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_right_p' => array(
                    'type' => 'margin',
                    'title' => '列表部分导航右侧文字内边距',
                    'max' => 300,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    )
                ),
                'list_nav_right_f' => array(
                    'type' => 'slider',
                    'title' => '列表部分导航右侧文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_right_Color' => array(
                    'type' => 'color',
                    'title' => '列表部分导航右侧文字颜色',
                    'std' => '#666',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_right_t_Color' => array(
                    'type' => 'color',
                    'title' => '列表部分导航右侧分类名称颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'hidden_list_nav_right_img' => array(
                    'type' => 'checkbox',
                    'title' => '关闭右侧图标显示',
                    'std' => '0',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'list_nav_right_img' => array(
                    'type' => 'media',
                    'title' => '列表部分导航右侧图标',
                    'std' => 'https://oss.lcweb01.cn/jzt/6d102551-1c08-4a57-9a29-a2d204df9298/image/20211118/d1aa16c21c434c802485343db816398a.png',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                        array('hidden_list_nav_right_img' ,'!=', '1'),
                    ),
                ),
                'list_nav_right_s' => array(
                    'type' => 'text',
                    'title' => '列表部分导航右侧文字分隔符',
                    'std' => '>',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type1'),
                        array('pro_type' ,'!=', 'type2'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content'),
                        array('show_list_nav' ,'=', '1'),
                    ),
                ),
                'separator_options' => array(
                    'type' => 'separator',
                    'title' => '内容列表设置',
                    'depends' =>array(
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'content_hidden_title' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭内容上方标题显示'),
                    'depends' => array(
                        array('pro_type', '=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 0,
                ),
                'button_check_2' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启查看更多按钮'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 0,
                ),
                'button_text' => array(
                    'type' => 'text',
                    'title' => JText::_('按钮文字'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('button_check_2', '=', '1'),
                    ),
                    'std' => '+查看详情+',
                ),
                'button_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('button_check_2', '=', '1'),
                    ),
                    'std' => '#cdcdcd',
                ),
                'button_bg_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮划过背景色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('button_check_2', '=', '1'),
                    ),
                    'std' => '#2d2c31',
                ),
                'img_check_2_line' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启底部线条'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 0,
                ),
                'img_check_2_line_color' => array(
                    'type' => 'color',
                    'title' => JText::_('底部线条颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'std' => '#fff',
                ),
                'img_check_2_line_colorhover' => array(
                    'type' => 'color',
                    'title' => JText::_('底部线条划过的颜色'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'std' => '#fff',
                ),
                'img_check_2_line_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的宽度'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 500,
                    'min' => 0,
                    'std' => 8,
                ),
                'img_check_2_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的高度'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 100,
                    'min' => 0,
                    'std' => 8,
                ),
                'img_check_2_line_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('底部线条中间点的圆角'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'max' => 300,
                    'min' => 0,
                    'std' => 4,
                ),
                'img_check_2_line_position' => array(
                    'type' => 'select',
                    'title' => JText::_('底部线条中间点的位置'),
                    'depends' => array(
                        array('pro_type', '=', 'type2'),
                        array('setting_part' ,'=', 'content'),
                        array('img_check_2_line', '=', '1'),
                    ),
                    'values' => array(
                        'end' => '上',
                        'center' => '中',
                        'normal' => '下'
                    ),
                    'std' => 'center',
                ),
                'tab2_width' => array(
                    'type' => 'slider',
                    'title' => '左侧选项卡宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 245,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part01_height' => array(
                    'type' => 'slider',
                    'title' => '上方栏目的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part01_bgColor' => array(
                    'type' => 'color',
                    'title' => '上方栏目背景颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part01_name' => array(
                    'type' => 'text',
                    'title' => '上方栏目标题',
                    'std' => '新闻中心',
                    'depends' =>array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part01_fontsize' => array(
                    'type' => 'slider',
                    'title' => '上方栏目文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 28,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part01_color' => array(
                    'type' => 'color',
                    'title' => '上方栏目文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part02_height' => array(
                    'type' => 'slider',
                    'title' => '下方分类的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 500,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_part02_bgColor' => array(
                    'type' => 'color',
                    'title' => '下方分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '分类行度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 60,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_fontsize' => array(
                    'type' => 'slider',
                    'title' => '分类文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 14,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_color' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bgColor' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_color_hover' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bColor_hover' => array(
                    'type' => 'color',
                    'title' => '分类前竖线颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bWidth_hover' => array(
                    'type' => 'slider',
                    'title' => '分类前竖线宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 3,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_color_active' => array(
                    'type' => 'color',
                    'title' => '分类文字颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bgColor_active' => array(
                    'type' => 'color',
                    'title' => '单个分类背景颜色',
                    'std' => '#f2f2f2',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bColor_active' => array(
                    'type' => 'color',
                    'title' => '分类前竖线颜色',
                    'std' => '#e50011',
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'tab2_li_bWidth_active' => array(
                    'type' => 'slider',
                    'title' => '分类前竖线宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 3,
                    'depends' => array(
                        array('pro_type' ,'=', 'type6'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),

                // 内容布局7
                // 面包屑
                'pro_type8_article' => array(
                    'type' => 'buttons',
                    'title' => '配置选项',
                    'std' => 'bread',
                    'values' => array(
                        array(
                            'label' => '面包屑',
                            'value' => 'bread'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '信息来源',
                            'value' => 'info'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                    ),
                ),

                'nav_first_title' => array(
                    'type' => 'text',
                    'title' => JText::_('一级面包屑'),
                    'std'=>"首页",
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('面包屑高度'),
                    'std' => '26',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_border_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('面包屑背景色'),
                    'std' => '',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('面包屑底部边框色'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_title_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('面包屑左侧标题字号'),
                    'std' => 18,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_title_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('面包屑左侧标题字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_position_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('面包屑右侧当前位置字号'),
                    'std' => 16,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'bread_position_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('面包屑右侧当前位置字体颜色'),
                    'std' => '#333',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'bread'),
                    ),
                ),
                'article_title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文章标题颜色'),
                    'std' => '#6aa285',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'title'),
                    ),
                ),
                'article_title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章标题字号'),
                    'std' => 20,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'title'),
                    ),
                ),
                'article_title_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章标题行高（px）'),
                    'std' => 40,
                    'max'=>300,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'title'),
                    ),
                ),
                'article_info_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文章信息来源背景色'),
                    'std' => '#f3f3f3',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'info'),
                    ),
                ),
                'article_info_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文章信息来源边框色'),
                    'std' => '#7b7b7b',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'info'),
                    ),
                ),
                'article_info_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文章信息来源颜色'),
                    'std' => '#6aa285',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'info'),
                    ),
                ),
                'article_info_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章信息来源字号'),
                    'std' => 20,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'info'),
                    ),
                ),
                'article_info_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章信息来源行高（px）'),
                    'std' => 40,
                    'max'=>300,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'info'),
                    ),
                ),
                'article_content_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文章内容颜色'),
                    'std' => '#6aa285',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'content'),
                    ),
                ),
                'article_content_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章内容字号（预览页字号由富文本编辑器的设置确定）'),
                    'desc' => JText::_('预览页字号由富文本编辑器的设置确定'),
                    'std' => 20,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'content'),
                    ),
                ),
                'article_content_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章内容行高（px）'),
                    'std' => 40,
                    'max'=>300,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'content'),
                    ),
                ),
                'article_content_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('文章内容距上边距（px）'),
                    'std' => 40,
                    'max'=>300,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type8'),
                        array('pro_type8_article' ,'=', 'content'),
                    ),
                ),
                'fix_img_height' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否固定内容图片高度'),
                    'std' => 0,
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'!=', 'type7'),
                    ),
                ),
                'img_style_type' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('fix_img_height', '=', 1),
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'!=', 'type7'),
                    ),
                ),
                'fix_img_height_input' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置pc端图片高度'),
                    'max' => 1000,
                    'min' => 0,
                    'std' => 300,
                    'depends' => array(
                        array('fix_img_height', '=', 1),
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'!=', 'type7'),
                    ),
                ),
                'fix_img_height_input_m' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置手机端图片高度'),
                    'max' => 600,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(
                        array('fix_img_height', '=', 1),
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'!=', 'type7'),
                    ),
                ),
                // 产品布局5 配置
                'type5_item_mb' => array(
                    'type' => 'slider',
                    'title' => '内容列表下边距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type5_item_border_color' => array(
                    'type' => 'color',
                    'title' => '内容列表边框颜色',
                    'std' => '#ededed',
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_border_style' => array(
                    'type' => 'select',
                    'title' => '内容列表边框样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_border_width' => array(
                    'type' => 'margin',
                    'title' => '内容列表边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),

                'type5_item_img_border_color' => array(
                    'type' => 'color',
                    'title' => '内容列表图片边框颜色',
                    'std' => '#ededed',
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_img_border_style' => array(
                    'type' => 'select',
                    'title' => '内容列表图片边框样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_img_border_width' => array(
                    'type' => 'margin',
                    'title' => '内容列表图片边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_img_p' => array(
                    'type' => 'margin',
                    'title' => '内容列表图片边框内边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    )
                ),
                'type5_item_img_scale' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启鼠标移入图片放大效果'),
                    'std' => 0,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type5_item_even' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('内容列表间距均匀分布'),
                    'std' => 0,
                    'depends' => array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type5_style' => array(
                    'type' => 'buttons',
                    'title' => '内容列表状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type5_item_bgColor' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表整体背景颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 'f2f2f2',
                ),
                'type5_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表标题文字大小'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 16,
                ),
                'type5_fontColor' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表标题文字颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#333',
                ),
                'type5_title_linHeight' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表标题文字行高'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 68,
                ),
                'type5_title_bgColor' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表标题背景颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'type5_more_bgColor' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表右下角图标背景色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#999999',
                ),
                'type5_more_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表右下角图标"+"颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'type5_more_bgWidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表右下角图标背景大小'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 36,
                ),
                'type5_more_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表右下角图标"+"大小'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 27,
                ),
                'type5_more_right' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表右下角图标右边距'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 0,
                ),
                'type5_more_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表右下角图标下边距'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => -19,
                ),
                'type5_hover_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表鼠标移入遮罩颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 'rgba(31, 53, 241, 0.7)',
                ),
                'type5_fontsize_hover' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表鼠标移入标题文字大小'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 20,
                ),
                'type5_fontColor_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表鼠标移入标题文字颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'type5_title_linHeight_hover' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表鼠标移入标题文字行高'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 52,
                ),
                'type5_lineWidth_hover' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表鼠标移入标题下方横线宽度'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 37,
                ),
                'type5_lineHeight_hover' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表鼠标移入标题下方横线高度'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 2,
                ),
                'type5_lineColor_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表鼠标移入标题下方横线颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'type5_more_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表鼠标移入右下角图标背景色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#be1d2c',
                ),
                'type5_more_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表鼠标移入右下角图标"+"颜色'),
                    'depends' =>array(
                        array('type5_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type5'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                // type5 配置结束

                // type9 配置开始
                'type9_style' => array(
                    'type' => 'buttons',
                    'title' => '内容列表状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type9_border_color' => array(
                    'type' => 'color',
                    'title' => '内容列表边框颜色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#2f5496',
                ),
                'type9_title_color' => array(
                    'type' => 'color',
                    'title' => '内容列表标题颜色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'type9_title_bgColor' => array(
                    'type' => 'color',
                    'title' => '内容列表标题背景色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#2f5496',
                ),
                'type9_icon_img' => array(
                    'type' => 'media',
                    'title' => '内容列表标题上方图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png',
                    'depends' => array(
                        array('type9_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type9_icon_bgImg' => array(
                    'type' => 'media',
                    'title' => '内容列表标题上方图标背景',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220613/d192734a26b96846918d12e0fcc256ba.png',
                    'depends' => array(
                        array('type9_style' ,'=', 'normal'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type9_border_color_hover' => array(
                    'type' => 'color',
                    'title' => '内容列表边框颜色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#dd0000',
                ),
                'type9_title_color_hover' => array(
                    'type' => 'color',
                    'title' => '内容列表鼠标移入标题颜色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '',
                ),
                'type9_title_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '内容列表鼠标移入标题背景色',
                    'depends' =>array(
                        array('type9_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#dd0000',
                ),
                'type9_icon_img_hover' => array(
                    'type' => 'media',
                    'title' => '内容列表鼠标移入标题上方图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png',
                    'depends' => array(
                        array('type9_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'type9_icon_bgImg_hover' => array(
                    'type' => 'media',
                    'title' => '内容列表鼠标移入标题上方图标背景',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220613/505cbe642c55dc01405f626b71234e1b.png',
                    'depends' => array(
                        array('type9_style' ,'=', 'hover'),
                        array('pro_type' ,'=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                // type9 配置结束
                'hover_border_pub' => array(
                    'type' => 'buttons',
                    'title' => '内容列表边框状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'normal_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表正常边框宽度'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 2,
                ),
                'normal_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表正常边框颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),

                    ),
                    'std' => 'rgba(21, 228, 116, 0)',
                ),
                'normal_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表正常字体背景颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'normal'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#000',
                ),
                'hover_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入后内容列表边框宽度'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 2,
                ),
                'hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后内容列表边框颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 'rgba(21, 228, 116, 0)',
                ),
                'hover_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后内容列表字体背景颜色'),
                    'depends' =>array(
                        array('hover_border_pub' ,'=', 'hover'),
                        array('pro_type' ,'!=', 'type3'),
                        array('pro_type' ,'!=', 'type5'),
                        array('pro_type' ,'!=', 'type7'),
                        array('pro_type' ,'!=', 'type9'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#000',
                ),
                'pro_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容列表正常字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type1'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#ffffff',
                ),
                'pro_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入后内容列表字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type1'),
                        array('hover_border_pub' ,'=','hover'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#ffffff',
                ),
                'pro_title_height_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表标题高度'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'max' => 300,
                    'std' => '50',
                ),
                'pro_title_height_type2_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容列表标题间距'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '10px 0px 10px 0px',
                ),

                'pro_font_color_type2_title' => array(
                    'type' => 'color',
                    'title' => JText::_('正常内容列表标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
                'pro_font_color_type2_title_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('移入内容列表标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','hover'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#000000',
                ),

                'pro_font_title_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容列表标题字体大小'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'max' => 200,
                    'min' => 0
                ),
                'title_font_position' => array(
                    'type' => 'select',
                    'title' => '内容列表标题位置设置',
                    'std' => 'center',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右',
                    ),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => 1,
                ),
                'pro_font_color_type2_intext' => array(
                    'type' => 'color',
                    'title' => JText::_('内容简介字体颜色'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('pro_type','=','type2'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#fff',
                ),
//                'content_margin_type2_intext' => array(
//                    'type' => 'margin',
//                    'title' => JText::_('简介间距'),
//                    'responsive' => true,
//                    'depends' => array('pro_type' => 'type2'),
//                    'std' => '',
//                ),
                'pro_font_color_type2_intext_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标进入内容简介字体颜色'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('pro_type','=','type2'),
                        array('hover_border_pub' ,'=','hover'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#000000',
                ),
                'pro_font_intext_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容简介字体大小'),
                    'depends' => array(
                        array('show_intro','=', 1),
                        array('hover_border_pub' ,'=','normal'),
                        array('pro_type','=','type2'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'max' => 200,
                    'min' => 0
                ),
                'img_animated' => array(
                    'type' => 'select',
                    'title' => '选择图片动画',
                    'desc' => '图片动画',
                    'depends' => array(
                        array('hover_border_pub' ,'=','normal'),
                        array('pro_type','=','type2'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'values' => array(
                        'animated1' => '无',
                        'animated2' => '放大',
                    ),
                    'std' => 'animated1',
                ),
//                'box_type2_shadow_color' => array(
//                    'type' => 'color',
//                    'title' => JText::_('盒子阴影颜色'),
//                    'depends' => array('pro_type' => 'type2'),
//                    'std' => '#ffffff',
//                ),
//                'box_type2_shadow_x' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('阴影水平偏移'),
//                    'depends' => array('pro_type' => 'type2'),
//                    'max' => 100,
//                    'min' => 0
//                ),
//                'box_type2_shadow_Y' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('阴影垂直偏移'),
//                    'depends' => array('pro_type' => 'type2'),
//                    'max' => 100,
//                    'min' => 0
//                ),
//                'box_type2_shadow_mh' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('阴影模糊'),
//                    'depends' => array('pro_type' => 'type2'),
//                    'max' => 100,
//                    'min' => 0
//                ),
//                'box_type2_shadow_kz' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('阴影扩展'),
//                    'depends' => array('pro_type' => 'type2'),
//                    'max' => 100,
//                    'min' => 0
//                ),
                'show_title' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('内容标题是否固定显示'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'depends' => array(
                        array('hover_border_pub' ,'=','normal'),
                        array('pro_type','=','type1'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '0'
                ),
                'pro_font_color_bg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容标题字体背景高度'),
                    'depends' => array(
                        array('hover_border_pub' ,'=','normal'),
                        array('pro_type','=','type1'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'max' => 2000,
                    'min' => 0,
                    'std' => array('md' => 40, 'sm' => 20, 'xs' => 20),
                ),
                'content_fontsize_bt' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容标题字体大小'),
                    'depends' => array(
                        array('hover_border_pub' ,'=','normal'),
                        array('pro_type','=','type1'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'max' => 100,
                    'std' => 14,
                ),

                'type3fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('移入内容标题字体大小'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                        array('hover_border_pub' ,'=','normal'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '22',
                ),
                'type3fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入内容标题字体颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                        array('hover_border_pub' ,'=','hover'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => '#ffffff',
                ),
                'type3bgclolor' => array(
                    'type' => 'color',
                    'title' => JText::_('移入内容背景颜色'),
                    'depends' => array(
                        array('pro_type' ,'=', 'type3'),
                        array('hover_border_pub' ,'=','hover'),
                        array('setting_part' ,'=', 'content'),
                    ),
                    'std' => 'rgba(0, 0, 0, 0.5)',
                ),
                //Content Style
                'content_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('内容外层设置'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),
//                'content_color' => array(
//                    'type' => 'color',
//                    'title' => JText::_('内容颜色'),
//                    'std' => '#000',
//                    'depends' => array(
//                        array('style', '=', 'custom'),
//                        array('custom_tab_style', '=', 'content'),
//                    ),
//                ),
                'content_backround' => array(
                    'type' => 'color',
                    'title' => JText::_('内容外层背景'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),

                'content_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容外层边框'),
                    'std' => 1,
                    'max' => 20,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),

                'content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('内容外层边框颜色'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),
                'content_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('内容外层边框半径'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),
                'content_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('内容字体'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-tab-custom-content > div{ font-family: {{ VALUE }}; }'
                    )
                ),
                'content_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('内容字体样式'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                ),
                'content_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('内容外部空白'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                    'std' => '',
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容内边距'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '=', 'content'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),

                'tab_separator_3' => array(
                    'type' => 'separator',
                    'title' => JText::_('外部阴影设置'),
                    'depends' => array(
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                        array('style', '=', 'custom'),
                    ),
                ),
                'show_boxshadow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('盒阴影'),
                    'desc' => JText::_('导航与内容的阴影样式'),
                    'std' => 1,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                'shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('阴影颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                'shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => JText::_('水平偏置'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                'shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => JText::_('垂直偏移'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                'shadow_blur' => array(
                    'type' => 'slider',
                    'title' => JText::_('模糊'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                'shadow_spread' => array(
                    'type' => 'slider',
                    'title' => JText::_('展开'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('setting_part', '!=', 'pages'),
                        array('setting_part', '!=', 'content_data'),
                    ),
                ),
                // 内容样式产品布局6 type7 内容部分配置
                'pro7_content_img_style_type' => array(
                    'type' => 'select',
                    'title' => JText::_('图片填充方式'),
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'fill',
                    'depends' => array(
                        array('setting_part' ,'=', 'content'),
                        array('pro_type' ,'=', 'type7'),
                    ),
                ),
                'pro7_content_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '内容标题文字大小',
                    'std' => 24,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'pro7_content_title_color' => array(
                    'type' => 'color',
                    'title' => '内容标题文字颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),
                'pro7_content_title_height' => array(
                    'type' => 'slider',
                    'title' => '内容标题文字行高',
                    'std' => 70,
                    'depends' => array(
                        array('pro_type', '=', 'type7'),
                        array('setting_part' ,'=', 'content'),
                    ),
                ),

                // 翻页配置
                'page' => array(
                    'type' => 'separator',
                    'title' => JText::_('翻页设置'),
                    'depends' =>array(
                        array('setting_part' ,'=', 'pages'),
                    )
                ),
                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('显示翻页码与相关设置'),
                    'std' => '0',
                    'depends' =>array(
                        array('setting_part' ,'=', 'pages'),
                        array('pro_type' ,'!=', 'type7'),
                    ),
                ),
                'page_count' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页隐藏数量'),
                    'std' => 0,
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                    ),
                ),
                'page_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页左右位置调整'),
                    'std' => 0,
                    'max' =>300,
                    'min' =>-300,
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                    ),
                ),
                'page_top_location' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页上下位置调整'),
                    'std' => 0,
                    'max' =>300,
                    'min' =>-300,
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                    ),
                ),
                'show_page_col' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码颜色设置'),
                    'std' => '0',
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                    ),
                ),
                'page2_tab_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#000',
                ),
                'page2_tab_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#100c0d',
                ),
                'page2_tab_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#ffffff',
                ),
                'page2_tab_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#69d640',
                ),
                'page2_tab_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#100d0d',
                ),
                'page2_tab_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#169fcb',
                ),
                'page2_tab_fontcolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过字体颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#1a22b3',
                ),
                'page2_tab_bordercolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过边框颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#0e0707',
                ),
                'page2_tab_bgcolor_hov' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标划过背景颜色'),
                    'depends' => array(
                        array('setting_part' ,'=', 'pages'),
                        array('show_page' ,'=', true),
                        array('show_page_col' ,'=', true),
                    ),
                    'std' => '#984706',
                ),
                // 数据配置
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示产品详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' =>array(
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
                'show_target' => array(
                    'type' => 'select',
                    'title' => '详情页跳转方式',
                    'values' => array(
                        '_self' => '当前页',
                        '_blank' => '新页面',
                        '#'=>'不跳转任何页面'
                    ),
                    'std' => '_self',
                    'depends' =>array(
                        array('style' ,'!=', ''),
                        array('pro_type' ,'!=', 'type4'),
                        array('pro_type' ,'!=', 'type6'),
                        array('pro_type' ,'!=', 'type8'),
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
                'type_parent' => array(
                    'type' => 'select',
                    'title' => '分类显示',
                    'desc' => '分类显示',
                    'values' => array(
                        'type1' => '一级分类',
                        'type2' => '二级分类',
                        'all' => '混合显示'
                    ),
                    'std' => 'type1',
                    'depends' =>array(
                        array('style' ,'!=', ''),
                        array('setting_part' ,'=', 'content_data'),
                        array('pro_type' ,'!=', 'type4'),
                    ),
                ),
                'catordering' => array(
                    'type' => 'select',
                    'title' => JText::_('分类排序'),
                    'desc' => JText::_('从分类中选择排序的方式'),
                    'values' => array(
                        'sortasc' => JText::_('按排序id正序'),
                        'sortdesc' => JText::_('按排序id倒序'),
                    ),
                    'std' => 'sortasc',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从第n个分类开始显示',
                    'std' => '1',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类(0为不限)',
                    'desc' => '显示n条分类',
                    'std' => '10',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
//                'k2catid' => array(
//                    'type' => 'select',
//                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID'),
//                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID_DESC'),
//                    'depends' => array('resource' => 'k2'),
//                    'values' => JwPageFactoryBase::k2CatList(),
//                    'multiple' => true,
//                ),
                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('列表排序'),
                    'desc' => JText::_('从列表中选择排序的方式'),
                    'values' => array(
                        'orderingasc' => JText::_('排序id正序'),
                        'orderingdesc' => JText::_('排序id倒序'),
                    ),
                    'std' => 'orderingdesc',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                    ),
                ),
                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('每页条数'),
                    'desc' => JText::_('分页每页显示产品数量'),
                    'std' => '10',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                        array('pro_type' ,'!=', 'type8'),
                    ),
                ),
                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('PC及平板列数'),
                    'desc' => JText::_('设置每行的产品列的数目。'),
                    'std' => '2',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                        array('pro_type' ,'!=', 'type8'),
                    ),
                ),
                'columns_xs' => array(
                    'type' => 'number',
                    'title' => JText::_('手机列数'),
                    'desc' => JText::_('设置每行的产品列的数目。'),
                    'std' => '2',
                    'depends' =>array(
                        array('pro_type' ,'!=', 'type4'),
                        array('setting_part' ,'=', 'content_data'),
                        array('pro_type' ,'!=', 'type8'),
                    ),
                ),
                // 数据配置结束
                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('css类'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);
