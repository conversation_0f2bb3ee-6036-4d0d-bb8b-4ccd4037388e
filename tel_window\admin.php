<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

 defined('_JEXEC') or die ('resticted aceess');

 $app = JFactory::getApplication();

 $input = $app->input;
 $layout_id = $input->get('layout_id', '');
 $site_id = $input->get('site_id', 0);
 $company_id = $input->get('company_id', 0);

 JwAddonsConfig::addonConfig(
     array(
         'type' => 'content',
         'addon_name' => 'tel_window',
         'title' => JText::_('悬浮插件'),
         'desc' => JText::_('悬浮插件'),
         'category' => '常用插件',
         'attr' => array(
             'general' => array(
                'addon_style' => array(
					'type' => 'select',
					'title' => JText::_('浮窗功能'),
					'desc' => JText::_('浮窗功能'),
					'values' => array(
						'ticker' => JText::_('返回上一页'),
						'scroller' => JText::_('悬浮电话'),
						// 'carousel' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_CAROUSEL'),
					),
					'std' => 'scroller',
				),

                 'tel_text' => array(
                     'type' => 'text',
                     'title' => JText::_('电话号码'),
                     'std' => '17696013033',
                     'depends' => array(
						array('addon_style', '=', 'scroller'),
					),
                 ),

                 'tz_style' => array(
                    'type' => 'select',
                    'title' => JText::_('返回方式'),
                    'values' => array(
						'tzsx' => JText::_('跳转刷新'),
						'tzbsx' => JText::_('跳转不刷新'),
						// 'carousel' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_CAROUSEL'),
					),
                    'std' => 'tzbsx',
                    'depends' => array(
                       array('addon_style', '=', 'ticker'),
                   ),
                ),

                 'tel_img' => array(
                     'type' => 'media',
                     'title' => '图片',
                     'std' => 'https://oss.lcweb01.cn/joomla/20210818/edbc585bb2d5e8c0f46532e3aea96bc7.png',
                 ),
                 'button_width' => array(
                   'type' => 'slider',
                   'title' => JText::_('按钮宽度'),
                   'max' => 300,
                   'min' => 0,
                   'std' => 70
                 ),
                 'button_height' => array(
                     'type' => 'slider',
                     'title' => JText::_('按钮高度'),
                     'max' => 300,
                     'min' => 0,
                     'std' => 50
                 ),
                 'button_top' => array(
                     'type' => 'slider',
                     'title' => JText::_('按钮上下位置'),
                     'max' => 100,
                     'min' => 0,
                     'std' => 50
                 ),
                 'button_left' => array(
                     'type' => 'slider',
                     'title' => JText::_('按钮左右位置'),
                     'max' => 100,
                     'min' => 0,
                     'std' => 100
                 ),
             ),
         ),
     )
 );
