<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'swiper_roundarrow',
		'title' => JText::_('圆形箭头轮播组件'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
		'category' => '轮播',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'jw_roundarrow_tab_item' => array(
                    'title' => '内容列表',
                    'std'   => array(
                        array(
                            'title'    => '搜索推广',
                            'title_ss' => '根据客户主动搜索，展示您的推广内容，精准定位，性价比高。',
                            'a_url'   => '/search',
                            'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/a8f82fd3c829e88dec8012b1b72aabd1.png',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                        ),
                        array(
                            'title'    => '信息流推广',
                            'title_ss' => '将您的推广信息自然融入在各类资讯、信息中，易传播，易操作。',
                            'a_url'   => '#',
                            'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/5f316af2f2f8065cbc70a8ae6b2dd4e1.png',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                        ),
                        array(
                            'title'    => '开屏推广',
                            'title_ss' => '百度开屏矩阵，是以围绕百度APP搭建的百度系开屏产品矩阵。',
                            'a_url'   => '/poly',
                            'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/355807c23967f1649abbda99f2b3dfff.png',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                        ),
                        array(
                            'title'    => '聚屏推广',
                            'title_ss' => '整合优质品牌广告流量，以开屏广告样式强势品牌曝光。搭建百度系开屏产品矩阵，整合百度优质广告资源与流量，以APP开屏广告的样式，强势品牌曝光。',
                            'a_url'   => '/search',
                            'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/c890e2b6552eac5339a932b4d2ef42f3.png',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                        ),
                        array(
                            'title'    => '百青藤推广',
                            'title_ss' => '百度系+联盟资源，碎片化场景全面覆盖用户，为您拓展更多商机。',
                            'a_url'   => '/search',
                            'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_roundarrow/assets/'.'img/e4ceaceaf7d82d2cdc41aaaf66d16099.png',
                            'tz_page_type' => 'external_links',
                            'detail_page_id' => 0,
                            'detail_page' => '',
                            'target' => '',
                        ),

                    ),
                    'attr'  => array(
                        'title'    => array(
                            'type'    => 'text',
                            'title'   => '标题',
                            'std'     => '投资创新的未来',
                        ),
                        'title_ss' => array(
                            'type'    => 'text',
                            'title'   => '副标题',
                            'std'     => '龙采科技集团',
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(array('tz_page_type', '=', 'Internal_pages')),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(array('tz_page_type', '=', 'external_links')),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                        ),
                        'bg_img'   => array(
                            'type'    => 'media',
                            'title'   => '背景图',
                            'format'  => 'image',
                            'std'     => 'https://oss.lcweb01.cn/joomla/20220513/d1dc8b73e761c063b3debab6df9ae58b.jpg',
                        ),
                    ),
                ),
                'title_fontsize' => array(
					'type' => 'slider',
					'title' => '标题字体大小',
					'std' => array('md' => 52, 'sm' => 32, 'xs' => 32),
                    'responsive' => true,
				),
                'title_color' => array(
					'type' => 'color',
					'title' => '标题字体颜色',
					'std' => '#fff',
				),
                'titles_fontsize' => array(
					'type' => 'slider',
					'title' => '副标题字体大小',
					'std' => array('md' => 18, 'sm' => 14, 'xs' => 14),
                    'responsive' => true,
				),
                'titles_color' => array(
					'type' => 'color',
					'title' => '副标题字体颜色',
					'std' => '#cfcfcf',
				),
                'bg_image' => array(
					'type' => 'media',
					'title' => '背景图',
					'std' => 'https://oss.lcweb01.cn/joomla/20220614/0b21cab17a6a618033185e35e798cdcc.jpg',
				),
                'title_image' => array(
					'type' => 'media',
					'title' => '名称后的图片',
					'std' => 'https://oss.lcweb01.cn/joomla/20220614/206a955845191fe677953bdac6233da7.png',
				),
                'close_button' => array(
					'type' => 'checkbox',
					'title' => '关闭按钮',
					'std' => 0,
				),
                'phone_height' => array(
					'type' => 'slider',
					'title' => '手机高度',
					'std' => '700',
					'max' => '1000',
				),
                'phone_imgwz' => array(
					'type' => 'slider',
					'title' => '手机图片底部位置(%)',
					'std' => '26',
					'max' => '100',
				),
			),
		),
	)
);
