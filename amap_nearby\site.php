<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonAmap_nearby extends JwpagefactoryAddons
{

	public function render()
	{
		$layout_id = $_GET['layout_id'] ?? 0;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;



		$output = '<div class="map-box" id="app-map">
			<div class="tab-box">
				<div :class="[\'item\', {\'active\':tabIndex == index}]" v-for="(item, index) in tabList" :key="index" @click="changeSearch(index)">
					<img :src="item.icon" class="icon" alt="">
					<img :src="item.iconA" class="icon active" alt="">
					<p>{{ item.name }}</p>
				</div>
			</div>
			<div class="flex main">
				<div class="left-map">
					<div id="container-' . $this->addon->id .'"></div>
				</div>
				<div class="right-panel">
					<div id="panel" style="display: none;"></div>
					<div class="address-list" style="" v-if="addressList.length > 0">
						<div class="item" v-for="(item, index) in addressList" :key="index">
							<img :src="tabList[tabIndex].locationIcon" class="icon" alt="">
							<div class="info">
								<p class="name">{{ item.name }} <span class="mi">{{ item.location.lat | getDistance(item.location.lng)}}米</span></p>
								<p class="address">{{ item.address }}</p>
							</div>
						</div>
					</div>
					<div class="nodata" v-else>
						没有找到相关地点
					</div>
				</div>
			</div>
		</div>';

		return $output;
	}

	public function scripts()
	{
		jimport('joomla.application.component.helper');
		$params = JComponentHelper::getParams('com_jwpagefactory');
		$amap_api = $params->get('amap_api', '');

		return array(
			'/components/com_jwpagefactory/assets/js/<EMAIL>',
//			'//webapi.amap.com/maps?v=1.4.15&key=' . $amap_api,
			'//webapi.amap.com/maps?v=1.4.15&key=509ed5f612c9e136d02124d1501da3eb',
			'https://cache.amap.com/lbs/static/addToolbar.js',
		);
	}

	public  function js() {
		$addonId = '#jwpf-addon-' . $this->addon->id;

		$settings = $this->addon->settings;
		//选项卡Item
		$section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? json_encode($settings->section_tab_item) : [];
		//地图中心点名称
		$section_map_name = (isset($settings->section_map_name) && $settings->section_map_name) ? $settings->section_map_name : "";
		//地图中心点所在城市
		$section_map_city = (isset($settings->section_map_city) && $settings->section_map_city) ? $settings->section_map_city : "";
		//地图中心点经度
		$section_map_lng = (isset($settings->section_map_lng) && $settings->section_map_lng) ? $settings->section_map_lng : "";
		//地图中心点纬度
		$section_map_lat = (isset($settings->section_map_lat) && $settings->section_map_lat) ? $settings->section_map_lat : "";
		//地图中心点图标
		$section_map_centerIcon = (isset($settings->section_map_centerIcon) && $settings->section_map_centerIcon) ? $settings->section_map_centerIcon : "";
		//地图中心点图标宽度
		$section_map_centerIcon_w = (isset($settings->section_map_centerIcon_w) && $settings->section_map_centerIcon_w) ? $settings->section_map_centerIcon_w : "";
		//地图中心点图标高度
		$section_map_centerIcon_h = (isset($settings->section_map_centerIcon_h) && $settings->section_map_centerIcon_h) ? $settings->section_map_centerIcon_h : "";

		$js = 'jQuery(function($){
		    var position = {lat: ' . $section_map_lat . ', lng: ' . $section_map_lng . '}
			var app = new Vue({
				el: \' ' . $addonId . ' #app-map\',
				data() {
					return {
						tabList: ' . $section_tab_item . ',
						tabIndex: 0,
						addressList: []
					}
				},
				methods: {
					changeSearch(index) {
						let self = this;
						if(index != self.tabIndex) {
							self.tabIndex = index;
							placeSearch.searchNearBy(self.tabList[self.tabIndex].name, cpoint, 1000, function(status, result) {
								console.log(result)
								if(result.info == \'OK\' && status == \'complete\') {
									let info_ = result.poiList;

									self.addressList = info_.pois;
//									console.log(self.tabList[self.tabIndex].locationIcon)
									setTimeout(function() {
										$(\'#container-' . $this->addon->id . ' .amap_lib_placeSearch_poi\').css(\'background-image\', \'url(\' + self.tabList[self.tabIndex].locationIcon +\')\')
									},100)
								}else {
									self.addressList = []
								}
							});
						}
					}
				},
				mounted() {
					console.log(this.tabList)
				},
				watch: {
					addressList(newV, oldV) {
						console.log(newV)
					}
				},
				filters: {
					/**
					* 计算两个经纬度的距离(米)
					*/
					getDistance(lat2, lng2){
						const lat1 = position.lat,
							lng1 = position.lng;
						var radLat1 = lat1 * Math.PI / 180.0;
						var radLat2 = lat2 * Math.PI / 180.0;
						var a = radLat1 - radLat2;
						var b = lng1*Math.PI / 180.0 - lng2 * Math.PI / 180.0;
						var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) +
							Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b/2),2)));
						s = s * 6378.137 ;// EARTH_RADIUS;
						s = Math.round(s * 10000 / 10);
						return s;
					}
				}
			})
			//地图开始
			var map = new AMap.Map("container-' . $this->addon->id . '", {
				resizeEnable: true
			});

			// 首先创建 AMap.Icon 实例：
			var icon = new AMap.Icon({
				size: new AMap.Size(' .$section_map_centerIcon_w . ', ' .$section_map_centerIcon_h . '), // 图标尺寸
				 //************ Icon路径 ************
				image: "' . $section_map_centerIcon . '",
				imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
				imageSize: new AMap.Size(' .$section_map_centerIcon_w . ', ' .$section_map_centerIcon_h . '), // 根据所设置的大小拉伸或压缩图片
			});
			// 将 Icon 实例添加到 marker 上:
			var marker = new AMap.Marker({
				title: \'' . $section_map_name . '\',
				snippet: \'\',
				position: new AMap.LngLat(position.lng, position.lat),// icon在地图上显示的位置，数组内对应经纬度
				offset: new AMap.Pixel(-10, -10),
				icon: icon, // 添加 Icon 实例
				zoom: 20, //缩放等级

			});
			// 将点标记添加到地图map上
			map.add(marker);

			var placeSearch, cpoint
			AMap.service(["AMap.PlaceSearch"], function() {
				//构造地点查询类
				placeSearch = new AMap.PlaceSearch({
					type: \'\', // 兴趣点类别
					pageSize: 20, // 单页显示结果条数
					pageIndex: 1, // 页码
					city: "' . $section_map_city . '", // 兴趣点城市
					citylimit: true,  //是否强制限制在设置的城市内搜索
					map: map, // 展现结果的地图实例
					panel: "panel", // 结果列表将在此容器中进行展示。
					renderStyle: \'default\',
					autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
				});

				cpoint = [position.lng, position.lat]; //中心点坐标
				placeSearch.searchNearBy(app.$data.tabList[app.$data.tabIndex].name, cpoint, 1000, function(status, result) {
					if(result.info == \'OK\') {
						let info_ = result.poiList;
						app.$data.addressList = info_.pois;
						setTimeout(function() {
							$(\'#container-' . $this->addon->id .' .amap_lib_placeSearch_poi\').css(\'background-image\', \'url(\' + app.$data.tabList[app.$data.tabIndex].locationIcon +\')\')
						},200)
					}
				});
			});';
		if($settings->icon_beat!=0){
        $js .= 'marker.setAnimation(\'AMAP_ANIMATION_BOUNCE\');';
        }
		$js .= '})';
		return $js;
	}

	public function css()
	{
		$addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;

		//选项卡背景色
		$section_tab_bgColor = (isset($settings->section_tab_bgColor) && $settings->section_tab_bgColor) ? $settings->section_tab_bgColor : "";
		//选项卡文字颜色
		$section_tab_color = (isset($settings->section_tab_color) && $settings->section_tab_color) ? $settings->section_tab_color : "";
		//选项卡文字大小
		$section_tab_fontsize = (isset($settings->section_tab_fontsize) && $settings->section_tab_fontsize) ? $settings->section_tab_fontsize : 16;
		$section_tab_fontsize_sm = (isset($settings->section_tab_fontsize_sm) && $settings->section_tab_fontsize_sm) ? $settings->section_tab_fontsize_sm : 16;
		$section_tab_fontsize_xs = (isset($settings->section_tab_fontsize_xs) && $settings->section_tab_fontsize_xs) ? $settings->section_tab_fontsize_xs : 14;
//		//选项卡选中文字大小
		$section_tab_active_fontsize = (isset($settings->section_tab_active_fontsize) && $settings->section_tab_active_fontsize) ? $settings->section_tab_active_fontsize : 16;
		$section_tab_active_fontsize_sm = (isset($settings->section_tab_active_fontsize_sm) && $settings->section_tab_active_fontsize_sm) ? $settings->section_tab_active_fontsize_sm : 14;
		$section_tab_active_fontsize_xs = (isset($settings->section_tab_active_fontsize_xs) && $settings->section_tab_active_fontsize_xs) ? $settings->section_tab_active_fontsize_xs : 14;
		//选项卡选中背景色
		$section_tab_active_bgColor = (isset($settings->section_tab_active_bgColor) && $settings->section_tab_active_bgColor) ? $settings->section_tab_active_bgColor : "";
		//选项卡选中文字颜色
		$section_tab_active_color = (isset($settings->section_tab_active_color) && $settings->section_tab_active_color) ? $settings->section_tab_active_color : "";
		//一行显示选项卡的个数
		$section_tab_num = (isset($settings->section_tab_num) && $settings->section_tab_num) ? $settings->section_tab_num : 8;
		$section_tab_num_sm = (isset($settings->section_tab_num_sm) && $settings->section_tab_num_sm) ? $settings->section_tab_num_sm : 6;
		$section_tab_num_xs = (isset($settings->section_tab_num_xs) && $settings->section_tab_num_xs) ? $settings->section_tab_num_xs : 3;
		//选项卡高度
		$section_tab_H = (isset($settings->section_tab_H) && $settings->section_tab_H) ? $settings->section_tab_H : 60;
		$section_tab_H_sm = (isset($settings->section_tab_H_sm) && $settings->section_tab_H_sm) ? $settings->section_tab_H_sm : 52;
		$section_tab_H_xs = (isset($settings->section_tab_H_xs) && $settings->section_tab_H_xs) ? $settings->section_tab_H_xs : 52;
		//选项卡外下边距
		$section_tab_mgB = (isset($settings->section_tab_mgB) && $settings->section_tab_mgB) ? $settings->section_tab_mgB : 20;
		$section_tab_mgB_sm = (isset($settings->section_tab_mgB_sm) && $settings->section_tab_mgB_sm) ? $settings->section_tab_mgB_sm : 20;
		$section_tab_mgB_xs = (isset($settings->section_tab_mgB_xs) && $settings->section_tab_mgB_xs) ? $settings->section_tab_mgB_xs : 10;
		//选项卡动画效果时间
		$section_tab_active_time = (isset($settings->section_tab_active_time) && $settings->section_tab_active_time) ? $settings->section_tab_active_time : "";
		//地图散点图标宽度
		$section_map_icon_w = (isset($settings->section_map_icon_w) && $settings->section_map_icon_w) ? $settings->section_map_icon_w : "";
		//地图散点图标高度
		$section_map_icon_h = (isset($settings->section_map_icon_h) && $settings->section_map_icon_h) ? $settings->section_map_icon_h : "";
		//左侧地图宽度
		$section_map_map_w = (isset($settings->section_map_map_w) && $settings->section_map_map_w) ? $settings->section_map_map_w : "";
		//左侧地图高度
		$section_map_map_h = (isset($settings->section_map_map_h) && $settings->section_map_map_h) ? $settings->section_map_map_h : 400;
		$section_map_map_h_sm = (isset($settings->section_map_map_h_sm) && $settings->section_map_map_h_sm) ? $settings->section_map_map_h_sm : 400;
		$section_map_map_h_xs = (isset($settings->section_map_map_h_xs) && $settings->section_map_map_h_xs) ? $settings->section_map_map_h_xs : 400;
		//右侧列表高度
		$section_map_content_h_sm = (isset($settings->section_map_content_h_sm) && $settings->section_map_content_h_sm) ? $settings->section_map_content_h_sm : 400;
		$section_map_content_h_xs = (isset($settings->section_map_content_h_xs) && $settings->section_map_content_h_xs) ? $settings->section_map_content_h_xs : 500;
		//右侧列表标题文字大小
		$section_content_title_fontsize = (isset($settings->section_content_title_fontsize) && $settings->section_content_title_fontsize) ? $settings->section_content_title_fontsize : 16;
		$section_content_title_fontsize_sm = (isset($settings->section_content_title_fontsize_sm) && $settings->section_content_title_fontsize_sm) ? $settings->section_content_title_fontsize_sm : 16;
		$section_content_title_fontsize_xs = (isset($settings->section_content_title_fontsize_xs) && $settings->section_content_title_fontsize_xs) ? $settings->section_content_title_fontsize_xs : 16;
		//右侧列表标题文字颜色
		$section_content_title_color = (isset($settings->section_content_title_color) && $settings->section_content_title_color) ? $settings->section_content_title_color : "";
		//右侧列表标题下边距
		$section_content_title_mgB = (isset($settings->section_content_title_mgB) && $settings->section_content_title_mgB) ? $settings->section_content_title_mgB : 8;
		$section_content_title_mgB_sm = (isset($settings->section_content_title_mgB_sm) && $settings->section_content_title_mgB_sm) ? $settings->section_content_title_mgB_sm : 8;
		$section_content_title_mgB_xs = (isset($settings->section_content_title_mgB_xs) && $settings->section_content_title_mgB_xs) ? $settings->section_content_title_mgB_xs : 6;
		//右侧列表地址文字大小
		$section_content_sTitle_fontsize = (isset($settings->section_content_sTitle_fontsize) && $settings->section_content_sTitle_fontsize) ? $settings->section_content_sTitle_fontsize : 14;
		$section_content_sTitle_fontsize_sm = (isset($settings->section_content_sTitle_fontsize_sm) && $settings->section_content_sTitle_fontsize_sm) ? $settings->section_content_sTitle_fontsize_sm : 14;
		$section_content_sTitle_fontsize_xs = (isset($settings->section_content_sTitle_fontsize_xs) && $settings->section_content_sTitle_fontsize_xs) ? $settings->section_content_sTitle_fontsize_xs : 12;
		//右侧列表地址文字颜色
		$section_content_sTitle_color = (isset($settings->section_content_sTitle_color) && $settings->section_content_sTitle_color) ? $settings->section_content_sTitle_color : "";

		$css =
			$addonId . ' p {
				margin: 0;
			}
			' . $addonId . ' #container-' . $this->addon->id . ',' . $addonId . ' #panel  {
				width: 100%;
				height: 100%;
				background-color: #FFFFFF;
			}
			' . $addonId . ' .map-box {
				width: 100%;
			}
			' . $addonId . ' .tab-box {
				display: flex;
				flex-wrap: wrap;
				background-color: ' . $section_tab_bgColor . ';
				width: 100%;
				margin-bottom: ' . $section_tab_mgB . 'px;
			}
			' . $addonId . ' .tab-box .item {
				width: calc(100% / ' . $section_tab_num . ');
				height: ' . $section_tab_H . 'px;
				line-height: ' . $section_tab_H . 'px;
				text-align: center;
				color: ' . $section_tab_color . ';
				cursor: pointer;
				transition: all ease-in-out ' . $section_tab_active_time . 'ms;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: ' . $section_tab_fontsize . 'px;
			}
			' . $addonId . ' .tab-box .item .icon {
				width: 20px;
				height: 20px;
				background: url() no-repeat center;
				background-size: 100%;
				margin-right: 8px;
			}
			' . $addonId . ' .tab-box .item .icon.active {
				display: none;
			}
			' . $addonId . ' .tab-box .item.active,' . $addonId . ' .tab-box .item:hover {
				background-color: ' . $section_tab_active_bgColor . ';
				color: ' . $section_tab_active_color . ';
				font-size: ' . $section_tab_active_fontsize .'px;
			}
			' . $addonId . ' .tab-box .item.active .icon,' . $addonId . ' .tab-box .item:hover .icon {
				display: none;
			}
			' . $addonId . ' .tab-box .item.active .icon.active,' . $addonId . ' .tab-box .item:hover .icon.active {
				display: block;
			}
			' . $addonId . ' .main {
				display: flex;
				align-items: center;
				justify-content: space-between;
				border: #EAEAEA solid 1px;
			}
			' . $addonId . ' .main .left-map {
				width: ' . $section_map_map_w . 'px;
				height: ' .$section_map_map_h . 'px;
			}
			' . $addonId . ' .main .right-panel {
				width: calc(100% - ' .$section_map_map_w . 'px - 20px);
				height: ' .$section_map_map_h . 'px;
			}
			' . $addonId . ' .main .right-panel .address-list {
				height: 100%;
				overflow-y: auto;
				padding: 20px 20px 20px 0;
				box-sizing: border-box;
			}
			' . $addonId . ' .main .right-panel .address-list .item {
				border-bottom: #EAEAEA solid 1px;
				padding: 20px 0;
				display: flex;/*
				align-items: center; */
			}
			' . $addonId . ' .main .right-panel .address-list .item:first-child {
				padding-top: 0;
			}
			' . $addonId . ' .main .right-panel .address-list .item:last-child {
				border-bottom: none;
			}
			' . $addonId . ' .main .right-panel .address-list .item .icon {
				width: ' .$section_map_icon_w . 'px;
				height: ' .$section_map_icon_h . 'px;
				background: url() no-repeat center;
				background-size: 100%;
				margin-right: 10px;
			}
			' . $addonId . ' .main .right-panel .address-list .item .info {
				width: calc(100% - ' .$section_map_icon_w . 'px);
			}
			' . $addonId . ' .main .right-panel .address-list .item .name {
				font-size: ' . $section_content_title_fontsize . 'px;
				font-weight: bold;
				margin-bottom: ' . $section_content_title_mgB . 'px;
				color: ' . $section_content_title_color . ';
				display: flex;
				justify-content: space-between;
			}
			' . $addonId . ' .main .right-panel .address-list .item .name span {
				font-size: 12px;
				color: #999;
				font-weight: 400;
			}
			' . $addonId . ' .main .right-panel .address-list .item .address {
				font-size: ' . $section_content_sTitle_fontsize . 'px;
				color: ' . $section_content_sTitle_color . ';
			}
			' . $addonId . ' .main .right-panel .nodata {
				text-align: center;
				font-size: 16px;
				line-height: 88px;
			}
			' . $addonId . ' #container-' . $this->addon->id . ' .amap_lib_placeSearch_poi {
				background-image: url();
				color: rgba(0, 0, 0, 0);
				width: ' .$section_map_icon_w . 'px;
				height: ' .$section_map_icon_h . 'px;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				' . $addonId . ' .tab-box {
					margin-bottom: ' . $section_tab_mgB_sm . 'px;
				}
				' . $addonId . ' .tab-box .item {
					width: calc(100% / ' . $section_tab_num_sm . ');
					height: ' . $section_tab_H_sm . 'px;
					line-height: ' . $section_tab_H_sm . 'px;
					font-size: ' . $section_tab_fontsize_sm . 'px;
				}
				' . $addonId . ' .tab-box .item.active,' . $addonId . ' .tab-box .item:hover {
					font-size: ' . $section_tab_active_fontsize_sm .'px;
				}
				' . $addonId . ' .main {
					display: block;
				}
				' . $addonId . ' .main .left-map, ' . $addonId . ' .main .right-panel {
					width: 100%;
				}
				' . $addonId . ' .main .left-map {
					height: ' .$section_map_map_h_sm . 'px;
				}
				' . $addonId . ' .main .right-panel {
					height: ' .$section_map_content_h_sm . 'px;
				}
				' . $addonId . ' .main .right-panel .address-list {
					padding-left: 20px;
				}
				' . $addonId . ' .main .right-panel .address-list .item .name {
					font-size: ' . $section_content_title_fontsize_sm . 'px;
					margin-bottom: ' . $section_content_title_mgB_sm . 'px;
				}
				' . $addonId . ' .main .right-panel .address-list .item .address {
					font-size: ' . $section_content_sTitle_fontsize_sm . 'px;
				}
			}
			@media (max-width:768px) {
				' . $addonId . ' .tab-box {
					margin-bottom: ' . $section_tab_mgB_xs . 'px;
				}
				' . $addonId . ' .tab-box .item {
					width: calc(100% / ' . $section_tab_num_xs . ');
					height: ' . $section_tab_H_xs . 'px;
					line-height: ' . $section_tab_H_xs . 'px;
					font-size: ' . $section_tab_fontsize_xs . 'px;
				}
				' . $addonId . ' .tab-box .item.active,' . $addonId . ' .tab-box .item:hover {
					font-size: ' . $section_tab_active_fontsize_xs .'px;
				}
				' . $addonId . ' .main {
					display: block;
				}
				' . $addonId . ' .main .left-map, ' . $addonId . ' .main .right-panel {
					width: 100%;
				}
				' . $addonId . ' .main .left-map {
					height: ' .$section_map_map_h_xs . 'px;
				}
				' . $addonId . ' .main .right-panel {
					height: ' .$section_map_content_h_xs . 'px;
				}
				' . $addonId . ' .main .right-panel .address-list {
					padding-left: 20px;
				}
				' . $addonId . ' .main .right-panel .address-list .item .name {
					font-size: ' . $section_content_title_fontsize_xs . 'px;
					margin-bottom: ' . $section_content_title_mgB_xs . 'px;
				}
				' . $addonId . ' .main .right-panel .address-list .item .address {
					font-size: ' . $section_content_sTitle_fontsize_xs . 'px;
				}
			}
			';

		return $css;
	}

	public static function getTemplate()
	{
		$output = '
        <div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>
		';

		return $output;
	}
}
