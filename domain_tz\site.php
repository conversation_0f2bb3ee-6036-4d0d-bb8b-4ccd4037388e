<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonDomain_tz extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$tz_peizhi = (isset($settings->tz_peizhi) && $settings->tz_peizhi) ? $settings->tz_peizhi : '';
		//output start
		$output = '';
		
		$output .= '
			<script>
			document.addEventListener("DOMContentLoaded", function() {
				var domain = document.domain;//先获取当前访问的全域名
				var person = {
		';
		foreach ($tz_peizhi as $k => $v) {
			if($k==0)
			{
				$output .= '
					"'.$v->domain.'":"'.$v->domain_ml.'"
				';
			}
			else
			{
				$output .= '
					,"'.$v->domain.'":"'.$v->domain_ml.'"
				';
			}
		}
		$output.='
				}	
				console.log(person)
				console.log(person[domain])
				var currentUrl = window.location.href;
				if(person[domain] && person[domain]!="/")
				{
                    var links = document.querySelectorAll("a");
                    links.forEach(function(link) {
                        if(link.href.includes(domain+"/"+person[domain])==false)
                        {
                            if(link.href.includes("index.html")==false)
                            {
                                link.href = link.href.replace(domain, domain+"/"+person[domain]);
                            }
                            else
                            {
                                link.href = "http://"+domain
                            }
                        }
                        
                    });
				}
			})
			</script>
		';
		return $output;
	}

	//用于设计器中显示
    public static function getTemplate()
    {
		$output .='该插件无显示效果，此文字用于占位，预览页不会显示';
		return $output;
	}
}
