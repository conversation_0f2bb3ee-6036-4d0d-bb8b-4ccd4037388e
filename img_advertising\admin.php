<?php
defined('_JEXEC') or die('resticted aceess');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'jw_img_advertising',
        'title' => JText::_('图片广告'),
        'category' => '图片',
        'attr' => array(
            'general' => array(
                'advertising_style' => array(
                    'type' => 'select',
                    'title' => JText::_('广告样式'),
                    'values' => array(
                        'type1' => '样式1',
                    ),
                    'std' => 'type1',
                ),
                'style1_img' => array(
                    'title' => JText::_('图片地址'),
                    'type' => 'media',
                    'std' => '/components/com_jwpagefactory/addons/img_advertising/assets/img/bg-style1.png',
                    'depends' => array(
                        array('advertising_style', '=', 'type1')
                    )
                ),
                'style1_fixed_img_size' => array(
                    'title' => JText::_('是否固定图片宽高'),
                    'type' => 'checkbox',
                    'std' => 0,
                    'depends' => array(
                        array('advertising_style', '=', 'type1')
                    )
                ),
                'style1_img_width' => array(
                    'title' => JText::_('图片宽度'),
                    'type' => 'slider',
                    'std' => 340,
                    'max' => 1000,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                        array('style1_fixed_img_size', '=', 1),
                    )
                ),
                'style1_img_height' => array(
                    'title' => JText::_('图片高度'),
                    'type' => 'slider',
                    'std' => 160,
                    'max' => 1000,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                        array('style1_fixed_img_size', '=', 1),
                    )
                ),
                'style1_img_style' => array(
                    'title' => JText::_('图片填充方式'),
                    'type' => 'select',
                    'values' => array(
                        'scale-down' => JText::_('自适应显示'),
                        'fill' => JText::_('占满不切割显示'),
                        'cover' => JText::_('占满切割显示'),
                    ),
                    'std' => 'cover',
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                        array('style1_fixed_img_size', '=', 1),
                    )
                ),
                'style1_img_opacity' => array(
                    'title' => JText::_('图片透明度（%）'),
                    'type' => 'slider',
                    'std' => 80,
                    'max' => 100,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'style1_img_move_speed' => array(
                    'title' => JText::_('图片移动速度（毫秒）'),
                    'type' => 'slider',
                    'std' => 50,
                    'max' => 3000,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'style1_img_offset_x' => array(
                    'title' => JText::_('图片每毫秒的水平方向位移量'),
                    'type' => 'slider',
                    'std' => 2,
                    'max' => 100,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'style1_img_offset_y' => array(
                    'title' => JText::_('图片每毫秒的垂直方向位移量'),
                    'type' => 'slider',
                    'std' => 2,
                    'max' => 100,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'style1_img_x' => array(
                    'title' => JText::_('图片初始水平方向位移'),
                    'type' => 'slider',
                    'std' => 0,
                    'max' => 2000,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'style1_img_y' => array(
                    'title' => JText::_('图片初始垂直方向位移'),
                    'type' => 'slider',
                    'std' => 0,
                    'max' => 2000,
                    'depends' => array(
                        array('advertising_style', '=', 'type1'),
                    )
                ),
                'need_link' => array(
                    'type'    => 'checkbox',
                    'title'   => JText::_('是否添加跳转链接'),
                    'std'     => 1,
                ),
                'link_page_select' => array(
                    'type'    => 'select',
                    'title'   => JText::_('跳转页面选择'),
                    'std'     => 'inner',
                    'values' => array(
                        'inner'=>'内页',
                        'outer'=>'外页',
                    ),
                    'depends' => array(
                        array('need_link', '=', 1)
                    )
                ),
                'detail_page_id' => array(
                    'type'    => 'select',
                    'title'   => JText::_('跳转内页'),
                    'std'     => '',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('need_link', '=', 1),
                        array('link_page_select', '=', 'inner'),
                    )
                ),
                'detail_page_url' => array(
                    'type'   => 'text',
                    'title'  => '跳转页面',
                    'std' => '',
                    'depends' => array(
                        array('need_link', '=', 1),
                        array('link_page_select', '=', 'outer'),
                    )
                ),
                'target' => array(
                    'type'   => 'select',
                    'title'  => '打开方式',
                    'values' => array(
                        '_self' => '默认',
                        '_blank' => '新窗口',
                    ),
                    'std' => '',
                    'depends' => array(
                        array('need_link', '=', 1),
                    )
                ),
            ),
        ),
    )
);
