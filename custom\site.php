<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonCustom extends JwpagefactoryAddons
{

    public function render()
    {
        //CSRF
        \JHtml::_('jquery.token');
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = $this->addon->id;
        $addon_ida = '#jwpf-addon-' . $this->addon->id;

        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }

        $class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
        $limit = (isset($settings->limit) && $settings->limit) ? ' ' . $settings->limit : '10';
        $show_page = (isset($settings->show_page) && $settings->show_page) ? ' ' . $settings->show_page : '0';
        $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? ' ' . $settings->goods_catid : 0;
        $classt = (isset($settings->classt) && $settings->classt) ? ' ' . $settings->classt : '';

    
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $detail_link_style = (isset($settings->detail_link_style) && $settings->detail_link_style) ? $settings->detail_link_style : '_self';

        $show_proData = (isset($settings->show_pro_data) && $settings->show_pro_data) ? $settings->show_pro_data : 0;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
        require_once $article_helper;


        $output = '';
        $output .= '<div class="' . $class . '">';

        // 产品
        $items = JwpagefactoryHelperGoods::getGoodsListProductData($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
        $items_count = JwpagefactoryHelperGoods::getGoodsListProductCount($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $show_proData);
        foreach ($items as $itemd => $items1) {
            if($items1->other){
                $bian=json_decode($items1->other,true);
                foreach($bian as $itemd2 => $items2){
                    $items[$itemd]->$itemd2=$items2;
                }
            }
        }
        

        foreach ($items as $key => $item) {
        $output .= '<div class="' . $classt . '">';

            if (isset($settings->jw_form_builder_item) && is_array($settings->jw_form_builder_item)) {
                $increasing_addon_id = $addon_id;
                $set_list = [];
                foreach ($settings->jw_form_builder_item as $item_key => $item_value) {

                    $field_type = (isset($item_value->field_type) && $item_value->field_type) ? $item_value->field_type : 'div';
                    $field_name = (isset($item_value->field_name) && $item_value->field_name) ? $item_value->field_name : '';
                    $class_name = (isset($item_value->class_name) && $item_value->class_name) ? $item_value->class_name : '';
                    
                    if($field_type=="div"){
                        $output .='<div class="'.$class_name.'">'.$item->$field_name.'</div>';
                    }elseif($field_type=="img"){
                        $output .='<img class="'.$class_name.'" src="'.$item->$field_name.'">';
                    }
                    
                } 
            }

        $output .= '</div>';

        }

        // if ($show_page) {
            
        //         $all_page = 1;
        //         if ($limit) {
        //             $all_page = ceil($items_count / $limit);
        //         }
        //         $output .= '<div class="page_plug">';
        //         // 判断是不是第一页
        //         if($page && $page != 1) {
        //             $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
        //             $output .= '<a class="page_num" href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
        //         }
        //         for ($i = 1; $i <= $all_page; $i++) {
        //             if ($page == $i) {
        //                 $output .= '<a class="curPage">' . $i . '</a>';
        //             } else {
        //                 $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
        //                 $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&zcpcatid=' . $catid[0] . '">' . $i . '</a>';
        //             }
        //         }
        //         // 判断是不是最后一页
        //         if($page < $all_page) {
        //             $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
        //             $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
        //         }

        //         $output .= '</div>';
        //         // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
           
        // }
        
        $output .= '</div>';

        return $output;
    }

    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

}