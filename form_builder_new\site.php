<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonForm_builder_new extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id    = '#jwpf-addon-' . $this->addon->id;
        $settings    = $this->addon->settings;
        $company_id = (isset($settings->company_id) && $settings->company_id) ? $settings->company_id : 0;
        $site_id = (isset($settings->site_id) && $settings->site_id) ? $settings->site_id : 0;
        $success_text = (isset($settings->success_text) && $settings->success_text) ? $settings->success_text : '了解更多代理商信息，欢迎拨打服务热线400 809 0990';
        $ys_text = (isset($settings->ys_text) && $settings->ys_text) ? $settings->ys_text : '隐私协议';
        $title_text = (isset($settings->title_text) && $settings->title_text) ? $settings->title_text : '代理商申请登记表';
        $number_type = (isset($settings->number_type) && $settings->number_type) ? $settings->number_type : 'type1';
        $output = '';
        if($number_type=='type1')
        {
            $jw_tab_item_one = (isset($settings->jw_tab_item_one) && $settings->jw_tab_item_one) ? $settings->jw_tab_item_one : '';
            $output .= '
                <style>
                    '.$addon_id.' blockquote,'.$addon_id.'  body, '.$addon_id.' button,'.$addon_id.'  dd, '.$addon_id.' div, '.$addon_id.' dl, '.$addon_id.' dt, '.$addon_id.' form, '.$addon_id.' h1, '.$addon_id.' h2, '.$addon_id.' h3, '.$addon_id.' h4, '.$addon_id.' h5, '.$addon_id.' h6, '.$addon_id.' input, '.$addon_id.' li, '.$addon_id.' ol, '.$addon_id.' p, '.$addon_id.' pre, '.$addon_id.' td, '.$addon_id.' textarea, '.$addon_id.' th, '.$addon_id.' ul {
                        margin: 0;
                        padding: 0;
                        -webkit-tap-highlight-color: rgba(0,0,0,0);
                    }
                    '.$addon_id.' .inner-title {
                        font-weight: bold;
                        font-size: 42px;
                        color: #333;
                    }
                    '.$addon_id.' .inner-line {
                        display: inline-block;
                        width: 55px;
                        height: 4px;
                        margin: 15px 0 30px;
                        background: #333;
                    }
                    '.$addon_id.' .about-wrap-first .inner-descrition {
                        width: 1000px;
                        margin: 0 auto;
                        text-align: left;
                        text-indent: 2rem;
                    }
                    '.$addon_id.' .inner-descrition {
                        font-size: 16px;
                        color: #333;
                        line-height: 1.75;
                    }
                    '.$addon_id.' .element-box {
                        position: relative;
                        z-index: 3;
                        opacity: 1;
                        margin: 5px 0 20px;
                        visibility: visible;
                        width: 100%;
                        top: 0px;
                        left: 0px;
                        transform: rotateZ(0deg);
                        animation: 1s ease 0.4s 1 normal both running fadeInLeft;
                        display: block;
                        height: auto;
                        color: #676767;
                        text-indent: 0;
                    }
                    '.$addon_id.' .element-box-contents {
                        width: 99.5%;
                        border: none;
                        border-radius: 0px;
                        text-indent: 0;
                    }
                    '.$addon_id.' .form-title-box {
                        height: auto;
                        padding-bottom: 8px;
                        text-align: left;
                        color: rgb(51,51,51);
                        line-height: 1.4;
                        font-weight: bold;
                        display: block;
                    }
                    '.$addon_id.' .head-title {
                        width: calc(100% - 0.5em);
                    }
                    '.$addon_id.' .head-title font {
                        color: #f00;
                        padding: 0 6px;
                    }
                    '.$addon_id.' .form-title-box, .form-des-box {
                        word-wrap: break-word;
                        word-break: break-all;
                    }
                    '.$addon_id.' .fill-blank {
                        width: 100%;
                        height: calc(100% - 27px);
                        color: rgb(51,51,51);
                        background-color: rgb(255,255,255);
                        text-align: left;
                        font-size: 14px;
                        line-height: 0.2;
                        z-index: 3;
                        opacity: 1;
                        transform: none;
                        box-shadow: rgb(0 0 0 / 50%) 0px 0px 0px;
                        visibility: visible;
                        font-weight: normal;
                    }
                    '.$addon_id.' .option-list {
                        box-sizing: border-box;
                        border: 1px solid rgb(204, 213, 219);
                        border-radius: 3px;
                    }
                    '.$addon_id.' .options .option-list textarea {
                        padding: 0.4rem 0.65rem !important;
                        height: 1.8rem;
                        line-height: 2;
                        word-break: break-all;
                        width: 100%;
                        box-sizing: border-box;
                        -webkit-overflow-scrolling: touch;
                        user-select: none;
                        overflow: auto;
                    }
                    '.$addon_id.' .comp_input {
                        width: 12rem;
                        height: 44.8px;
                        background: transparent;
                        border: none;
                        padding: 6.4px 10.4px !important;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #666666;
                    }
                    '.$addon_id.' textarea {
                        height: 44.8px !important;
                        resize: none;
                    }
                    '.$addon_id.' button, '.$addon_id.' input, '.$addon_id.' optgroup, '.$addon_id.' option, '.$addon_id.' select, '.$addon_id.' textarea {
                        font-family: inherit;
                        font-size: inherit;
                        font-style: inherit;
                        font-weight: inherit;
                        outline: 0;
                    }
                    '.$addon_id.' .inner-main-container img {
                        max-width: 100%;
                        vertical-align: middle;
                    }
                    '.$addon_id.' .yzm {
                        cursor: pointer;
                        width: 120px;
                        height: 46px;
                        margin: 0 0 0 20px;
                    }
                    '.$addon_id.' .new-upload-wraper {
                        position: relative;
                        width: 100%;
                        min-height: 4.5rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        flex-direction: column;
                        border: 1px solid rgb(204, 213, 219);
                        border-radius: 0.3rem;
                        line-height: 1.2;
                    }
                    '.$addon_id.' .upload-file-list {
                        margin-right: -0.3rem;
                        display: flex;
                        flex-wrap: wrap;
                    }
                    '.$addon_id.' .upload-comp-btn {
                        width: 100%;
                        display: block;
                        border: none;
                        background-color: transparent;
                        text-align: center;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        font-size: 0.700rem;
                        line-height: 2.6;
                        pointer-events: none;
                    }
                    '.$addon_id.' .upload-add {
                        width: 100%;
                        height: 100%;
                        position: relative;
                        padding-left: 0px;
                        font-size: 0.700rem;
                        overflow: hidden;
                    }
                    '.$addon_id.' .upload-add::before {
                        content: "+";
                        display: inline-block;
                        position: static;
                        font-size: 1.4em;
                        left: 0px;
                        margin-right: 0.1rem;
                    }
                    '.$addon_id.' input[type=file] {
                        position: absolute;
                        left: 0px;
                        top: 0px;
                        width: 100%;
                        height: 100%;
                        cursor: pointer;
                        opacity: 0;
                    }
                    '.$addon_id.' .upload-file-item {
                        position: relative;
                        width: 4.5rem;
                        height: 4.5rem;
                        margin-right: 0.3rem;
                        margin-bottom: 0.3rem;
                        border: 0.05rem solid #ccc;
                        background-position: center center;
                        background-repeat: no-repeat;
                        background-size: 100% auto;
                        display: flex;
                        -webkit-display: flex;
                        -moz-display: flex;
                        align-items: center;
                        justify-content: center;
                        line-height: 1.3;
                        padding: 0.3rem;
                    }
                    '.$addon_id.' .upload-file-item .del-btn {
                        width: 1.1rem;
                        height: 1.1rem;
                        position: absolute;
                        right: -0.4rem;
                        top: -0.4rem;
                        background-color: #ff296a;
                        border-radius: 50%;
                        line-height: 1.100rem;
                        text-align: center;
                        color: #fff;
                        cursor: pointer;
                    }
                    '.$addon_id.' input[type="checkbox"]:not([disabled]) {
                        position: relative;
                        overflow: initial;
                        -moz-transform: scale(1);
                        display: inline-block;
                    }
                    '.$addon_id.' #ipcheck::after {
                        content: ";
                        position: absolute;
                        left: -0.05rem;
                        top: -0.1rem;
                        height: 0.7rem;
                        width: 0.7rem;
                        background: #fff;
                        border: 0.05rem solid #ccd5db;
                        border-radius: 0.15rem;
                        cursor: pointer;
                        transform: scale(10);
                    }
                    '.$addon_id.' .privacy-verify div {
                        position: relative;
                        padding-left: 0.3rem;
                        flex: 1;
                        display: inline-block;
                        font-size: 16px;
                        color: #333;
                        line-height: 1.75;
                    }
                    '.$addon_id.' .privacy-verify div .agreement {
                        font-weight: bold;
                        color: #1593ff;
                        cursor: pointer;
                        text-decoration: underline;
                    }
                    '.$addon_id.' .privacy-verify .msg {
                        display: none;
                        background: rgba(0,0,0,0.6);
                        line-height: 0.900rem;
                        color: #fff;
                        font-size: 0.600rem;
                        padding: 0.1rem 0.3rem;
                        border-radius: 1rem;
                        position: absolute;
                        bottom: 0.1rem;
                        left: 50%;
                        transform: translateX(-50%);
                        text-indent: 0;
                    }
                    '.$addon_id.' .comp_button {
                        display: block;
                        width: 90%;
                        height: 36px;
                        background-color: rgb(21,147,255);
                        border: none;
                        opacity: 1;
                        color: rgb(255,255,255);
                        margin: 2vh auto;
                    }
                    input[type="checkbox"]:not([disabled]):checked::after {
                        background-image: url(https://res.eqh5.com/Fs2Xot0vHFPYXQWKk82715M15lLh);
                        background-color: #1593ff;
                        background-size: 86%;
                        background-position: center;
                        border-color: #1593ff;
                    }
                </style>
            ';
            $output .= '
            <div class="inner-text" style="margin-top: 20px;text-align: center;">
            <h2 class="inner-title">'.$title_text.'</h2>
            <span class="inner-line"></span>
            <div class="inner-descrition" style="width: 100%;">
    
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>公司名称：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input">
                            <div class="options">
                                <div class="option-list">
                                    <textarea name="title" id="title" placeholder="公司名称" required="true"
                                        class="element comp_input editable-text"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>联系人姓名：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input">
                            <div class="options">
                                <div class="option-list">
                                    <textarea name="name" id="name" placeholder="联系人姓名" required="true"
                                        class="element comp_input editable-text"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>联系人手机：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input">
                            <div class="options">
                                <div class="option-list">
                                    <textarea name="tel" id="tel" placeholder="联系人手机号" required="true"
                                        class="element comp_input editable-text"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>验证码：
                            </div>
                        </div>
                        <div class="fill-blank form-comp-box min input">
                            <div class="options" style="width: 50%;float: left;">
                                <div class="option-list">
                                    <textarea name="yzm" id="yzm" placeholder="验证码" required="true"
                                        class="element comp_input editable-text"></textarea>
    
                                </div>
                            </div>
                            <img src="https://zhjzt.china9.cn/api/Message/captch_code?c=c"
                                alt="看不清楚，换一张" title="看不清楚，换一张" align="absmiddle" class="yzm"
                                onclick="newgdcode(this,this.src)">
                        </div>
                    </div> 
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require"> 
                            <div class="head-title">
                                <font>*</font>营业执照：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input3">
                            <label onchange="upimg(\'zhizhao\',\'1\')" class="new-upload-wraper" id="img_item_1" for="img1">
                                <button class="upload-comp-btn">
                                    <span class="btnTitle upload-add">上传图片</span>
                                </button>
                                <div class="form-note-box">最多1个，每个10MB以内，jpg/png/gif格式</div>
                                <input type="file" id="img1" class="upload"
                                    accept="image/png,image/jpeg,image/gif,image/jpg,">
                            </label>
                            <div class="upload-file-list" id="img_box_1">
    
                            </div>
                        </div>
                    </div>
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>身份证正反面：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input3">
                            <label onchange="upimg(\'cardnum\',\'2\')" class="new-upload-wraper" id="img_item_2" for="img2">
                                <button class="upload-comp-btn">
                                    <span class="btnTitle upload-add">上传图片</span>
                                </button>
                                <div class="form-note-box">最多2个，每个10MB以内，jpg/png/gif格式</div>
                                <input type="file" id="img2" class="upload"
                                    accept="image/png,image/jpeg,image/gif,image/jpg,">
                            </label>
                            <div class="upload-file-list" id="img_box_2">
    
                            </div>
                        </div>
    
                    </div>
                </div>
                <div class="element-box">
                    <div class="element-box-contents">
                        <div class="form-title-box require">
                            <div class="head-title">
                                <font>*</font>办公环境：
                            </div>
                        </div>
                        <div class="form-des-box">
                            <div></div>
                        </div>
                        <div class="fill-blank form-comp-box min input3">
                            <label onchange="upimg(\'huanjing\',\'3\')" class="new-upload-wraper" id="img_item_3" for="img3">
                                <button class="upload-comp-btn">
                                    <span class="btnTitle upload-add">上传图片</span>
                                </button>
                                <div class="form-note-box">最多5个，每个10MB以内，jpg/png/gif格式</div>
                                <input type="file" id="img3" class="upload"
                                    accept="image/png,image/jpeg,image/gif,image/jpg,">
                            </label>
                            <div class="upload-file-list" id="img_box_3">
    
                            </div>
                        </div>
                    </div>
                </div>
                <div class="element-box-contents" style="padding: 7px 0px;">
                    <div>
                        <div class="privacy-verify">
                            <input type="checkbox" name="agreement" id="ipcheck">
                            <div>已知晓<span class="agreement">《隐私协议》</span>并授权该表单收集我的信息</div>
                            <span class="msg">请知晓隐私协议</span>
                        </div>
                        <button class="element comp_button editable-text" type="button" onclick="checkform();">提交</button>
                    </div>
                </div>
                <div class="cl"></div>
    
            </div>
        </div>
         <script src="https://zhjzt.china9.cn/joomla/H-ui.admin.Pro/lib/layer/3.1.1/layer.js"></script>
            ';
        $output .="
        <script>
            function newgdcode(obj,url) {
                obj.src = url+ '&nowtime=' + new Date().getTime();//后面传递一个随机参数，否则在IE7和火狐下，不刷新图片
            }
            function upimg(id, i) {
                var input = document.getElementById('img' + i);
                if (!input['value'].match(/.jpg|.gif|.png|.jpeg|.bmp/i)) {
                    layer.msg('上传的图片格式不正确，请重新选择');
                    return false;
                };
                var that = this,
                    file = input.files[0],
                    formData = new FormData();
                formData.append('file', file);
                formData.append('company_id', ".$company_id.");
                $.ajax({
                    type: 'POST',
                    // 数据提交类型
                    url: 'https://zhjzt.china9.cn/api/UploadFile/tpajax',
                    // 发送地址
                    data: formData,
                    //发送数据
                    contentType: false,
                    //上传文件 不可缺
                    processData: false,
                    //上传文件 不可缺
                    dataType: 'json',
                    success: function success(response) {
                        console.log(response)
                        if (response.code == 200) {
                            var h = $('".$addon_id." .img_' + id).length + 1;
                            var img = '<div class=\"upload-file-item img_' + id + '\" id=\"img_' + id + h + '\" style=background-image:url('+response.data+')><div></div><div class=\"del-btn\" onClick=\"dells(this);\" pid=\"img_' + id + h + '\" h=\"' + h + '\" ids=\"' + id + '\"  i=\"' + i + '\">-</div></div>' + '<input type=\"hidden\" name=\"' + id + '[]\" id=\"img_int_' + id + h + '\" value=\"' + response.data + '\">';
                            if (i == 2 && h < 2) {
                                $('".$addon_id." .uploadBtn' + i).remove();
                                img += '<div class=\"upload-file-item uploadBtn' + i + '\" ><span>+</span><input onchange=\"upimg2(\'' + id + '\',\'' + i + '\')\" id=\"imgcs' + i + '\" type=\"file\" class=\"upload\" ></div>';
                            }
                            if (i == 3 && h < 5) {
                                $('".$addon_id." .uploadBtn' + i).remove();
                                img += '<div class=\"upload-file-item uploadBtn' + i + '\" ><span>+</span><input onchange=\"upimg2(\'' + id + '\',\'' + i + '\')\" id=\"imgcs' + i + '\" type=\"file\" class=\"upload\" ></div>';
                            }
                            $('".$addon_id." #img_item_' + i).hide();
                            $('".$addon_id." #img_box_' + i).show();
                            $('".$addon_id." #img_box_' + i).append(img);
                        } else {
                            layer.msg('上传失败',{icon:2})
                            layer.closeAll('loading');
                        }
                    },
                    error: function error(err) { }
                });
            }
            function upimg2(id,i){
                var input = document.getElementById('imgcs'+i);
                if (!input['value'].match(/.jpg|.gif|.png|.jpeg|.bmp/i)) {
                    layer.msg('上传的图片格式不正确，请重新选择');
                    return false;
                };
                var that = this,
                    file = input.files[0],
                    formData = new FormData();
                formData.append('file', file);
                formData.append('company_id', ".$company_id.");
                $.ajax({
                    type: 'POST',
                    // 数据提交类型
                    url: 'https://zhjzt.china9.cn/api/UploadFile/tpajax',
                    // 发送地址
                    data: formData,
                    //发送数据
                    contentType: false,
                    //上传文件 不可缺
                    processData: false,
                    //上传文件 不可缺
                    dataType: 'json',
                    success: function success(response) {
                        console.log(response)
                        if (response.code == 200) {
                            var h = $('".$addon_id." .img_'+id).length+1;
                            var img = '<div class=\"upload-file-item img_'+id+'\" id=\"img_'+id+h+'\" style=background-image:url('+response.data+')><div></div><div class=\"del-btn\" onClick=\"dells(this);\" pid=\"img_'+id+h+'\" h=\"'+h+'\" ids=\"'+id+'\"  i=\"'+i+'\">-</div></div>'+'<input type=\"hidden\" name=\"'+id+'[]\" id=\"img_int_'+id+h+'\" value=\"'+response.data+'\">';
                            if(i == 2 && h<2){
                                $('".$addon_id." .uploadBtn'+i).remove();
                                img += '<div class=\"upload-file-item uploadBtn'+i+'\" ><span>+</span><input onchange=\"upimg2(\''+id+'\',\''+i+'\')\" id=\"imgcs'+i+'\" type=\"file\" class=\"upload\" ></div>';
                            }else{
                                $('".$addon_id." .uploadBtn'+i).remove();
                            }
                            if(i == 3 && h<5){
                                $('".$addon_id." .uploadBtn'+i).remove();
                                img += '<div class=\"upload-file-item uploadBtn'+i+'\" ><span>+</span><input onchange=\"upimg2(\''+id+'\',\''+i+'\')\" id=\"imgcs'+i+'\" type=\"file\" class=\"upload\" ></div>';
                            }else{
                                $('".$addon_id." .uploadBtn'+i).remove();
                            }
                            $('".$addon_id." #img_item_'+i).hide();
                            $('".$addon_id." #img_box_'+i).show();
                            $('".$addon_id." #img_box_'+i).append(img);
                        } else {
                            layer.msg('上传失败',{icon:2})
                            layer.closeAll('loading');
                        }
                        
                    },
                    error: function error(err) { }
                });
            }
            $('".$addon_id." .agreement').click(function(){
                var html= '".$ys_text."';
                var w = $(window).width();        
                if(w>=720){
                    layer.open({
                        title: '《隐私政策》',
                        type: 1,
                        area: ['60%', '420px'],
                        fixed: false, //不固定
                        maxmin: true,
                        shade: 0.01,
                        content:html
                    });
                }else{
                    layer.open({
                        title: '《隐私政策》',
                        type: 1,
                        area: ['80%', '600px'],
                        fixed: false, //不固定
                        maxmin: true,
                        shade: 0.01,
                        content:html
                    });
                }
            });
            function dells(obj) {
                var pid = $(obj).attr('pid');
                var ids = $(obj).attr('ids');
                var h = $(obj).attr('h');
                var i = $(obj).attr('i');
                var num = $('.img_' + ids).length;
                $('".$addon_id." #' + pid).remove();
                $('".$addon_id." #img_int_' + ids + h).remove();
                if (num <= 1) {
                    $('".$addon_id." #img_box_' + i).hide();
                    $('".$addon_id." #img_item_' + i).show();
                }
            }
            function checkform(){
                var title = $('".$addon_id." #title').val();
                var name = $('".$addon_id." #name').val();
                var tel = $('".$addon_id." #tel').val();
                var yzm = $('".$addon_id." #yzm').val();
                var reg = /^1[23456789]\d{9}$/;
                
                var zhizhao = new Array();
                var cardnum = new Array(); 
                var huanjing = new Array();
                if (!title) {
                    layer.msg('请填写企业名称');
                    return false;
                }
                if (!name) {
                    layer.msg('请填写联系人姓名');
                    return false;
                }
                if (!tel) {
                    layer.msg('请填写联系人电话');
                    return false;
                }
                if (!yzm) {
                    layer.msg('请填写验证码');
                    return false;
                }
                if (!reg.test(tel)) {
                    layer.msg('手机号格式错误');
                    return false;
                }
                if (!$('".$addon_id." input[name=agreement]').is(':checked')) {
                    layer.msg('需要同意隐私政策');
                    return false;
                }
                $(\"input[name^='zhizhao']\").each(function(i){
                    zhizhao[i] = this.value;
                });
                $(\"input[name^='cardnum']\").each(function(i){
                    cardnum[i] = this.value;
                });
                $(\"input[name^='huanjing']\").each(function(i){
                    huanjing[i] = this.value;
                });
                //console.log(cardnum);
                if (zhizhao.length<1) {
                    layer.msg('需要上传营业执照');
                    return false;
                }
                if (cardnum.length<1) {
                    layer.msg('需要上传身份证');
                    return false;
                }
                if (huanjing.length<1) {
                    layer.msg('需要上传办公环境');
                    return false;
                }
                var formData = new FormData();
                var cc = '{\"公司名称\":\"'+title+'\",\"联系人姓名\":\"'+name+'\",\"联系人手机\":\"'+tel+'\"}';
                formData.append('title', cc);
                formData.append('yzm', yzm);
                formData.append('site_id', ".$site_id.");
                formData.append('company_id', ".$company_id.");
                formData.append('zhizhao', zhizhao);
                formData.append('cardnum', cardnum);
                formData.append('huanjing', huanjing);
                $.ajax({
                    url: 'https://zhjzt.china9.cn/api/Message/submit_forms',
                    type: 'POST',    /*传递方法 */
                    data:formData,  /*要带的值，在这里只能带一个formdata ，不可以增加其他*/
                    dataType : 'json',  //传递数据的格式
                    async:false, //这是重要的一步，防止重复提交的
                    cache: false,  //设置为false，上传文件不需要缓存。
                    contentType: false,//设置为false,因为是构造的FormData对象,所以这里设置为false。
                    processData: false,//设置为false,因为data值是FormData对象，不需要对数据做处理。
                    beforeSend:function(){
                        index_load = layer.load(2, {shade: false}); //0代表加载的风格，支持0-2
                    },
                    success: function(data) {
                        //console.log(data);
                        layer.close(index_load);
                        //var data=JSON.parse(res);
                        if (data.code == 200) {
                            layer.msg('".$success_text."',{icon:1,time:2000,shade:0.4},function() {

                            });
                        } else if (data.code == 500) {
                            layer.msg('提交失败');
                        } else if (data.code == 400) {
                            layer.msg('验证码错误');
                        }
                    },
                    error: function() {
                        layer.close(index_load);
                        layer.msg('上传错误');
                    }
                });
        
                //$('form').submit();
            }
        </script>
        ";
        }
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
