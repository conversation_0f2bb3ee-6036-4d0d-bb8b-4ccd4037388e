<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonElectronic_Tags extends JwpagefactoryAddons
{

    public function render()
    {

        $output         = '';
        $company_id     = $_GET['company_id'] ?? 0;
        $site_id        = $_GET['site_id'] ?? 0;
        $layout_id      = $_GET['layout_id'] ?? 0;
        $page_view_name = isset($_GET['view']);
        $settings       = $this->addon->settings;
        $addon_id       = '#jwpf-addon-t' . $this->addon->id;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        $items = JwpagefactoryHelperArticles::getmerchantstong($company_id, $site_id);
        $output .= '<style type="text/css">';
        $output .= $addon_id  . '  img {';
        $output .= 'width:' . $settings->tags_width . 'px;';
        $output .= 'height:' . $settings->tags_height . 'px;';
        $output .= '}';
        $output .= '</style>';
        $output .= '<div id="' . $addon_id . '" >';
        foreach ($items as $k => $v) {
            $output .= '' . $v->electronic_tags . '';
        }
        $output .= '</div>';
        return $output;
    }

    public static function getTemplate()
    {
         return '<div>本段文字用于编辑模式下占位，预览模式下不显示</div>';
    }
}
