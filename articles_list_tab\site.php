<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonArticles_list_tab extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $addon = $_GET['addon'] ?? 0;
        $detail = $_GET['detail'] ?? 0;
        $catid_id = $_GET['catid_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $zcpcatid = $_GET['zcpcatid'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;

        if($catid_id != 0) {
            $zcpcatid = $catid_id;
        }

        // Addon options
        // 选项卡样式
        $type_parent_show = (isset($settings->type_parent_show) && $settings->type_parent_show) ? $settings->type_parent_show : 'type1';
        // 文章来源
        $resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
        // 文章列表排序
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'order_desc';
        // 文章列表 每页条数
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        // 分类id
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        // 选项卡切换方式
        $tab_change = (isset($settings->tab_change)) ? $settings->tab_change : 'onclick';
        // 开启选项卡点击跳转页面
        $is_tab_click = (isset($settings->is_tab_click)) ? $settings->is_tab_click : 0;
        // 选项卡跳转方式
        $tab_link_way = (isset($settings->tab_link_way)) ? $settings->tab_link_way : '_blank';
        // 选项卡点击跳转详情页模版
        $tab_click_page_id = (isset($settings->tab_click_page_id)) ? $settings->tab_click_page_id : 0;


        // print_r($catid);
//        if(!isset($catid)){
//            $catid = [0];
//        }
//        if($catid == 0){
//            $catid = [0];
//        }
//        if($zcpcatid != $catid[0]){
//            $page = 1;
//        };



        //include k2 helper
        $k2helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/k2.php';
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        $isk2installed = self::isComponentInstalled('com_k2');


        if($type_parent_show=='type1')
        {
            $output_tab = "<style>
                {$addon_id} .art-tab-box .art-tab {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    margin-bottom: 20px;
                }
                {$addon_id} .art-tab-box .art-tab .art-tab-item {
                    list-style: none;
                    padding: 0 10px;
                    height: 40px;
                    line-height: 40px;
                    font-size: 14px;
                    color: #333333;
                    margin-right: 20px;
                    border: #3988CA solid 1px;
                }
                {$addon_id} .art-tab-box .art-tab .art-tab-item.item-active, {$addon_id} .art-tab-box .art-tab .art-tab-item:hover {
                    cursor: pointer;
                    background-color: #3988CA;
                    color: #fff;
                }
            </style>";
        }
        elseif($type_parent_show=='type2')
        {
            // 左侧选项卡宽度
            $tab2_width = (isset($settings->tab2_width) && $settings->tab2_width) ? $settings->tab2_width : 245;
            // 上方栏目的高度
            $tab2_part01_height = (isset($settings->tab2_part01_height) && $settings->tab2_part01_height) ? $settings->tab2_part01_height : 100;
            // 上方栏目背景颜色
            $tab2_part01_bgColor = (isset($settings->tab2_part01_bgColor) && $settings->tab2_part01_bgColor) ? $settings->tab2_part01_bgColor : '#e50011';
            // 上方栏目文字大小
            $tab2_part01_fontsize = (isset($settings->tab2_part01_fontsize) && $settings->tab2_part01_fontsize) ? $settings->tab2_part01_fontsize : 28;
            // 上方栏目文字颜色
            $tab2_part01_color = (isset($settings->tab2_part01_color) && $settings->tab2_part01_color) ? $settings->tab2_part01_color : '#fff';
            // 下方分类的高度
            $tab2_part02_height = (isset($settings->tab2_part02_height) && $settings->tab2_part02_height) ? $settings->tab2_part02_height : 500;
            // 下方分类背景颜色
            $tab2_part02_bgColor = (isset($settings->tab2_part02_bgColor) && $settings->tab2_part02_bgColor) ? $settings->tab2_part02_bgColor : '#f2f2f2';
            // 分类行度
            $tab2_li_lineHeight = (isset($settings->tab2_li_lineHeight) && $settings->tab2_li_lineHeight) ? $settings->tab2_li_lineHeight : 60;
            // 分类文字大小
            $tab2_li_fontsize = (isset($settings->tab2_li_fontsize) && $settings->tab2_li_fontsize) ? $settings->tab2_li_fontsize : 14;
            // 分类文字颜色
            $tab2_li_color = (isset($settings->tab2_li_color) && $settings->tab2_li_color) ? $settings->tab2_li_color : '#333';
            // 单个分类背景颜色
            $tab2_li_bgColor = (isset($settings->tab2_li_bgColor) && $settings->tab2_li_bgColor) ? $settings->tab2_li_bgColor : '#f2f2f2';
            // 移入 分类文字颜色
            $tab2_li_color_hover = (isset($settings->tab2_li_color_hover) && $settings->tab2_li_color_hover) ? $settings->tab2_li_color_hover : '#e50011';
            // 移入 单个分类背景颜色
            $tab2_li_bgColor_hover = (isset($settings->tab2_li_bgColor_hover) && $settings->tab2_li_bgColor_hover) ? $settings->tab2_li_bgColor_hover : '#f2f2f2';
            // 移入 分类前竖线颜色
            $tab2_li_bColor_hover = (isset($settings->tab2_li_bColor_hover) && $settings->tab2_li_bColor_hover) ? $settings->tab2_li_bColor_hover : '#e50011';
            // 移入 分类前竖线宽度
            $tab2_li_bWidth_hover = (isset($settings->tab2_li_bWidth_hover) && $settings->tab2_li_bWidth_hover) ? $settings->tab2_li_bWidth_hover : 3;
            // 选中 分类文字颜色
            $tab2_li_color_active = (isset($settings->tab2_li_color_active) && $settings->tab2_li_color_active) ? $settings->tab2_li_color_active : '#e50011';
            // 选中 单个分类背景颜色
            $tab2_li_bgColor_active = (isset($settings->tab2_li_bgColor_active) && $settings->tab2_li_bgColor_active) ? $settings->tab2_li_bgColor_active : '#f2f2f2';
            // 选中 分类前竖线颜色
            $tab2_li_bColor_active = (isset($settings->tab2_li_bColor_active) && $settings->tab2_li_bColor_active) ? $settings->tab2_li_bColor_active : '#e50011';
            // 选中 分类前竖线宽度
            $tab2_li_bWidth_active = (isset($settings->tab2_li_bWidth_active) && $settings->tab2_li_bWidth_active) ? $settings->tab2_li_bWidth_active : 3;

            $output_tab = "<style>
                {$addon_id} .er_left {
                    width: {$tab2_width}px;
                    display: inline-block;
                    vertical-align: top;
                }
                {$addon_id} .art-list {
                    width: calc(100% - " . ($tab2_width + 20) . "px);
                    margin-left: 20px;
                    display: inline-block;
                    vertical-align: top;
                }
                {$addon_id} .er_left .lanmu_title {
                    width: 100%;
                    height: {$tab2_part01_height}px;
                    background: {$tab2_part01_bgColor};
                    font-size: {$tab2_part01_fontsize}px;
                    font-weight: bold;
                    color: {$tab2_part01_color};
                    text-align: left;
                    line-height: {$tab2_part01_height}px;
                    box-sizing: border-box;
                    padding: 0 20px;
                }
                {$addon_id} .er_left ul {
                    display: block;
                    width: 100%;
                    height: {$tab2_part02_height}px;
                    background: {$tab2_part02_bgColor};
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }
                {$addon_id} .er_left ul li {
                    display: block;
                    width: 100%;
                    height: {$tab2_li_lineHeight}px;
                    line-height: {$tab2_li_lineHeight}px;
                    background-color: {$tab2_li_bgColor};
                    list-style: none;
                    font-weight: bold;
                    border-left: {$tab2_li_bWidth_hover}px solid transparent;
                    transition: all ease-in-out 0.3s;
                }
                {$addon_id} .er_left ul li a {
                    display: block;
                    margin: 0 20px;
                    font-size: {$tab2_li_fontsize}px;
                    line-height: " . ($tab2_li_lineHeight - 1)."px;
                    color: {$tab2_li_color};
                    border-bottom: 1px solid #e4e4e4;
                    text-decoration: none;
                }
                {$addon_id} .er_left ul li a span {
                    float: right;
                }
                {$addon_id} .er_left ul li:hover {
                    background-color: {$tab2_li_bgColor_hover};
                    color: {$tab2_li_color_hover} !important;
                    border-left: {$tab2_li_bWidth_hover}px solid {$tab2_li_bColor_hover};
                }
                {$addon_id} .er_left ul li:hover a{
                    color: {$tab2_li_color_hover} !important;
                    transition: all ease-in-out 0.3s;
                }
                {$addon_id} .er_left ul li.item-active{
                    background-color: {$tab2_li_bgColor_active};
                    color: {$tab2_li_color_active};
                    border-left: {$tab2_li_bWidth_active}px solid {$tab2_li_bColor_active};
                }
                {$addon_id} .er_left ul li.item-active a{
                    color: {$tab2_li_color_active};
                }
            </style>";
        }
        elseif($type_parent_show=='type3')
        {
            // 右侧选项字体颜色
            $tab3_font_color = (isset($settings->tab3_font_color) && $settings->tab3_font_color) ? $settings->tab3_font_color : '#000';
            // 右侧选项字体移入颜色
            $tab3_font_color_y = (isset($settings->tab3_font_color_y) && $settings->tab3_font_color_y) ? $settings->tab3_font_color_y : '#d80006';
            // 右侧选项字体选中颜色
            $tab3_font_color_x = (isset($settings->tab3_font_color_x) && $settings->tab3_font_color_x) ? $settings->tab3_font_color_x : '#d80006';
            // 右侧选项字体移入下划线颜色
            $tab3_font_border_color_y = (isset($settings->tab3_font_border_color_y) && $settings->tab3_font_border_color_y) ? $settings->tab3_font_border_color_y : '#d80006';
            // 右侧选项字体选中下划线颜色
            $tab3_font_border_color_x = (isset($settings->tab3_font_border_color_x) && $settings->tab3_font_border_color_x) ? $settings->tab3_font_border_color_x : '#d80006';
            // 左侧栏目字体颜色
            $tab3_left_font_color = (isset($settings->tab3_left_font_color) && $settings->tab3_left_font_color) ? $settings->tab3_left_font_color : '#fff';
            // 左侧栏目背景颜色
            $tab3_left_bg_color = (isset($settings->tab3_left_bg_color) && $settings->tab3_left_bg_color) ? $settings->tab3_left_bg_color : '#d80006';
            // 左侧栏目文字内容
            $tab3_left_font_text = (isset($settings->tab3_left_font_text) && $settings->tab3_left_font_text) ? $settings->tab3_left_font_text : '人才建设';
            // 选项卡与列表间距
            $tab3_font_list_num = (isset($settings->tab3_font_list_num) && $settings->tab3_font_list_num) ? $settings->tab3_font_list_num : 20;
            $output_tab ="<style>
                {$addon_id} .list_type {
                    height: 45px;
                    border-bottom: 2px solid #e0e0e0;
                    margin-bottom:{$tab3_font_list_num}px;
                }
                {$addon_id} .list_type .buttontwo li.item-active {
                    color: {$tab3_font_color_x};
                    border-bottom: 2px solid {$tab3_font_border_color_x};
                    font-weight: bold;
                }
                {$addon_id} .list_type .buttontwo li.item-active a {
                    color: {$tab3_font_color_x};
                }
                {$addon_id} .list_type .buttontwo li {
                    width: 70px;
                    font-size: 16px;
                    display: block;
                    float: left;
                    text-align: center;
                    line-height: 45px;
                    margin-right: 30px;
                    text-decoration: none;
                    color: {$tab3_font_color};
                    cursor: pointer;
                    height: 45px;
                }
                {$addon_id} .list_type .buttontwo li a {
                    color: {$tab3_font_color};
                    display: inline-block;
                    width: 100%;
                    height: 100%;
                    line-height: 100%;
                }
                {$addon_id} .list_type .buttontwo li:hover {
                    color: {$tab3_font_color_y};
                    border-bottom: 2px solid {$tab3_font_border_color_y};
                    font-weight: bold;
                }
                 {$addon_id} .list_type .buttontwo li:hover a {
                    color: {$tab3_font_color_y};
                }
                {$addon_id} .buttontwo {
                    float: right;
                }
                {$addon_id} .buttonone {
                    width:0;
                    height:0;
                    border-right:60px solid transparent;
                    border-left:60px solid transparent;
                    border-top:60px solid {$tab3_left_bg_color};
                    z-index: 100;
                }
                {$addon_id} .kailong{
                    position: relative;
                    width: 120px;
                    height: 45px;
                    float: left;
                    font-size: 20px;
                    line-height: 45px;
                    text-align: center; 
                    background-color: {$tab3_left_bg_color};
                    color: {$tab3_left_font_color};
                    font-weight: bold;
                    z-index: 10;
                }
            </style>";
        }
        elseif($type_parent_show=='type4')
        {
             // 右侧选项字体颜色
             $tab4_font_color = (isset($settings->tab4_font_color) && $settings->tab4_font_color) ? $settings->tab4_font_color : '#000';
             // 右侧选项字体移入颜色
             $tab4_font_color_y = (isset($settings->tab4_font_color_y) && $settings->tab4_font_color_y) ? $settings->tab4_font_color_y : '#fff';
             // 右侧选项字体选中颜色
             $tab4_font_color_x = (isset($settings->tab4_font_color_x) && $settings->tab4_font_color_x) ? $settings->tab4_font_color_x : '#fff';
             // 右侧选项字体背景移入颜色
             $tab4_font_bg_color_y = (isset($settings->tab4_font_bg_color_y) && $settings->tab4_font_bg_color_y) ? $settings->tab4_font_bg_color_y : '#d80006';
             // 右侧选项字体背景选中颜色
             $tab4_font_bg_color_x = (isset($settings->tab4_font_bg_color_x) && $settings->tab4_font_bg_color_x) ? $settings->tab4_font_bg_color_x : '#d80006';
             // 右侧选项字体左右内边距
             $tab4_font_padding_num = (isset($settings->tab4_font_padding_num) && $settings->tab4_font_padding_num) ? $settings->tab4_font_padding_num : 5;
             // 右侧选项字体分割线左侧间距
             $tab4_font_after_num = (isset($settings->tab4_font_after_num) && $settings->tab4_font_after_num) ? $settings->tab4_font_after_num : 20;
             // 选项卡与列表间距
             $tab4_font_list_num = (isset($settings->tab4_font_list_num) && $settings->tab4_font_list_num) ? $settings->tab4_font_list_num : 20;
            $output_tab = "
                <style>
                    {$addon_id} .list_type {
                        height: 30px;
                        margin-bottom:{$tab4_font_list_num}px;
                    }
                    {$addon_id} .list_type .buttontwo li.item-active {
                        color: {$tab4_font_color_x};
                        /* border-bottom: 2px solid #d80006; */
                        font-weight: bold;
                        background-color: {$tab4_font_bg_color_x};
                    }
                    {$addon_id} .list_type .buttontwo li.item-active a {
                        color: {$tab4_font_color_x};
                    }
                    {$addon_id} .list_type .buttontwo li {
                        display: flex;
                        width: auto;
                        font-size: 16px;
                        display: block;
                        float: left;
                        text-align: center;
                        line-height: 30px;
                        margin-right: 30px;
                        text-decoration: none;
                        color: {$tab4_font_color};
                        cursor: pointer;
                        padding-left: {$tab4_font_padding_num}px;
                        padding-right: {$tab4_font_padding_num}px;
                    }
                    {$addon_id} .list_type .buttontwo li a {
                        color: {$tab4_font_color};
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                        line-height: 100%;
                    }
                    {$addon_id} .list_type .buttontwo li::after{
                        position: absolute;    
                        top:6px;    
                        margin-left: {$tab4_font_after_num}px;
                        content: '';    
                        width:0;    
                        height: 20px;
                        border-left: solid  #000 3px;
                    }
                    {$addon_id} .list_type .buttontwo li:last-child::after{
                        content: '';
                        display: none;
                    }
                    {$addon_id} .list_type .buttontwo li:hover {
                        color: {$tab4_font_color_y};
                        background-color: {$tab4_font_bg_color_y};
                        font-weight: bold;
                    }
                    {$addon_id} .list_type .buttontwo li:hover a {
                        color: {$tab4_font_color_y};
                    }
                    {$addon_id} .buttontwo {
                        float: right;
                    }
                </style>
            ";
        }

        require_once $article_helper;
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        // 获取分类
        $pids = array();

        if($settings->type_parent=='type1'){
            $pids[]=1;
            $types = JwpagefactoryHelperCategories::getArticlefl($pids,$company_id, $site_id);
        }
        elseif($settings->type_parent=='type2'){
            $pids=$catid;
            $types = JwpagefactoryHelperCategories::getArticlefl($pids,$company_id, $site_id);
        }
        else{
            $types = JwpagefactoryHelperCategories::getArticlefl2($company_id, $site_id);
            foreach($types as $k => $v)
            {
                if(is_array($catid))
                {
                    if($catid[0]!='')
                    {
                        if(!in_array($v['tag_id'],$catid))
                        {
                        unset($types[$k]);
                        }
                    }
                }
            }
            $types = array_merge($types);
        }
        

       
        if($type_parent_show=='type1')
        {
            $output_tab .= '<div class="art-tab-box">';
            $output_tab .= '<ul class="art-tab">';
            if($types){
                foreach ($types as $k1 => $v1){
                    $active = '';
                    if($addon == $this->addon->id) {
                        if($k1 == 0 && $zcpcatid == 0) {
                            $active .= ' item-active';
                        }else if($zcpcatid == $v1['tag_id']) {
                            $active .= ' item-active';
                        }
                    }else {
                        if($k1 == 0 && $addon != 0 && $zcpcatid == 0) {
                            $active .= ' item-active';
                        }else if($zcpcatid == $v1['tag_id']) {
                            $active .= ' item-active';
                        }
                    }
                    $output_tab .= '<li id="li'.$v1['tag_id'].'" onclick="lists'.$this->addon->id.'('.$v1["tag_id"].')" class="art-tab-item '.$active.'" value="'.$v1["tag_id"].'">'.$v1["title"].'</li>';
                }
            }else{
                $output_tab .= '<div style="color:red;">该分类暂无二级分类！</div>';
            }
            $output_tab .= '</ul>';
            $output_tab .= '</div>';
        }
        elseif($type_parent_show=='type2')
        {
            // 上方栏目文字
            $tab2_part01_name = (isset($settings->tab2_part01_name) && $settings->tab2_part01_name) ? $settings->tab2_part01_name : '新闻中心';

            $output_tab .= '<div class="er_left art-tab-box">';
            $output_tab .= '<div class="lanmu_title">' . $tab2_part01_name . '</div>';
            $output_tab .= '<ul class="art-tab">';
            foreach ($types as $k1 => $v1){
                $active = '';
                if($addon == $this->addon->id) {
                    if($k1 == 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }
                }else {
                    if($k1 == 0 && $addon != 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }
                }
                $output_tab .= '<li id="li'.$v1['tag_id'].'" onclick="lists'.$this->addon->id.'('.$v1["tag_id"].')" class="art-tab-item' . $active .'" value="'.$v1["tag_id"].'"><a href="javascript:;">'.$v1["title"].'<span>&gt;</span></a></li>';
            }
            $output_tab .= '</ul>';
            $output_tab .= '</div>';
        }
        elseif($type_parent_show=='type3')
        {
            $output_tab .= '<div class="list_type art-tab-box">';
            $output_tab .= '<div class="kailong">';
            $output_tab .= '<div class="buttonone"></div>';
            $output_tab .= '<span style="position: absolute;top: 0;left: 0;right: 0;">'.$tab3_left_font_text.'</span>';
            $output_tab .= '</div>';
            $output_tab .= '<div class="buttontwo art-tab">';
            $output_tab .= '<ul style="display: contents">';
//            print_r($types);
//            die();
            foreach ($types as $k1 => $v1){
                $active = '';
                if($addon == $this->addon->id) {
                    if($k1 == 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }
                }else {
                    if($k1 == 0 && $addon != 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }else if($k1 == 0) {
                        $active .= ' item-active';
                    }
                }
                $tab_title = '';
                if($tab_change == 'onclick'){
                    $tab_title .= $v1["title"];
                }
                if($tab_change == 'onmouseover' && $is_tab_click == 1) {
                    $page_id = base64_encode($tab_click_page_id);
                    $tab_url = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".$page_id."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&zcpcatid='.$v1['tag_id'].'&page=' . $page . '&company_id='.$company_id,$absolute=true);
                    $tab_title .= '<a href="' . $tab_url .'" target="' . $tab_link_way .'">' . $v1["title"] . '</a>';
                }
                $output_tab .= '<li id="li'.$v1['tag_id'].'" ' . $tab_change .'="lists'.$this->addon->id.'('.$v1["tag_id"].')" class="art-tab-item '.$active.'" value="'.$v1["tag_id"].'">'.$tab_title.'</li>';
            }
            $output_tab .= '</ul>';
            $output_tab .= '</div>';
            $output_tab .= '</div>';
       }
        elseif($type_parent_show=='type4')
        {
            $output_tab .= '<div class="list_type art-tab-box">';
            $output_tab .= '<div class="buttontwo art-tab">';
            $output_tab .= '<ul style="display: contents">';
            foreach ($types as $k1 => $v1){
                $active = '';
                if($addon == $this->addon->id) {
                    if($k1 == 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }
                }else {
                    if($k1 == 0 && $addon != 0 && $zcpcatid == 0) {
                        $active .= ' item-active';
                    }else if($zcpcatid == $v1['tag_id']) {
                        $active .= ' item-active';
                    }
                }
                if($k1 == 0 && $zcpcatid == 0 ) {
                    $active .= ' item-active';
                }else if($zcpcatid == $v1['tag_id'] && $addon == $this->addon->id) {
                    $active .= ' item-active';
                }
                $tab_title = '';
                if($tab_change == 'onclick'){
                    $tab_title = $v1["title"];
                }
                if($tab_change == 'onmouseover' && $is_tab_click == 1) {
                    $page_id = base64_encode($tab_click_page_id);
                    $tab_url = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".$page_id."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&zcpcatid='.$v1['tag_id'].'&page=' . $page . '&company_id='.$company_id,$absolute=true);
                    $tab_title = '<a href="' . $tab_url .'" target="' . $tab_link_way .'">' . $v1["title"] . '</a>';
                }
                $output_tab .= '<li id="li'.$v1['tag_id'].'" ' . $tab_change .'="lists'.$this->addon->id.'('.$v1["tag_id"].')" class="art-tab-item '.$active.'" value="'.$v1["tag_id"].'">'.$tab_title.'</li>';
            }
            $output_tab .= '</ul>';
            $output_tab .= '</div>';
            $output_tab .= '</div>';
        }


        $output_tab .= '<script>
                function lists'.$this->addon->id.'(id){
                    $("'.$addon_id.' .art_list_id").addClass("listhide");
                    $("'.$addon_id.' #div"+id).removeClass("listhide");
                    $("'.$addon_id.' .item-active").removeClass("item-active");
                    $("'.$addon_id.' #li"+id).addClass("item-active");
                }
        </script>';


        // 文章列表布局
        $output = '';

        // 文章列表数据
        // require_once $article_helper;
        // $items = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        // $items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $catid, $include_subcat, $company_id, $site_id, $post_type, $tagids);

        // 详情是否本插件
        $detail_from = (isset($settings->detail_from) && $settings->detail_from) ? $settings->detail_from : 0;
        if($detail != 0 && $addon == $this->addon->id) {
            //读取文章数据
            $article = JwPageFactoryBase::getArticleById2($detail, $catid_id);
            $output .= '<div class="art_list_id jwpf-addon" id="div">';
            $catid_title = '';
            foreach($types as $kk => $vv){
                if($vv['tag_id'] == $catid_id) {
                    $catid_title .= $vv['title'];
                }
            }
            // 列表导航
            $output .= $this->listNav($catid_title);
            if ($article) {
                $output .= '<style>
                   ' . $addon_id . ' .art-page {
                        border-top: 1px solid #e4e4e4;
                        padding-top: 5px;
                   }
                   ' . $addon_id . ' .art-page p {
                        line-height: 32px;
                        font-size: 14px;
                   }
                   ' . $addon_id . ' .art-page p, ' . $addon_id . ' .art-page p a {
                        color: #999;
                   }
                </style>';
                $output .= '<div class="page-header" style="text-align: center;margin-top: 20px;">';
                    $output .= '<h1 style="font-size: 18px;font-weight: bold;">'.$article->title.'</h1>';
                    $output .= '<p style="font-size: 14px;color: #666;margin-top: 5px;">发布时间：' . $article->created . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;点击量：' . $article->Look_number . '</p>';
                $output .= '</div>';
                $output .= '<div itemprop="articleBody" style="border-top: 1px solid #e4e4e4;margin-top: 20px;padding: 20px 0;">';
                    $output .=  $article->fulltext;
                $output .= '</div>';
                // 上一篇 下一篇
                $up_id = $article->on;
                $up_title = $this->pageArt($up_id);
                $down_id = $article->down;
                $down_title = $this->pageArt($down_id);

                $output .= '<div class="art-page">';
                $output .= '<p>上一篇：' . $up_title . '</p>';
                $output .= '<p>下一篇：'.$down_title.'</p>';
                $output .= '</div>';
            }
            $output .= '</div>';
        }

        foreach($types as $kk => $vv)
        {
            if($zcpcatid != 0)
            {
                if($zcpcatid != $vv['tag_id'])
                {
                   $page = 1;
                }
                else {
                    $page = $_GET['page'];
                }
            }
            if($addon != 0)
            {
                if($addon != $this->addon->id)
                {
                    $page = 1;
                }
            }
//            print_r('page: ' . $page . ',zcpcatid：' .$zcpcatid . 'tag_id: ' . $vv['tag_id'] . '</br>');
            $ccatid = [];
            $ccatid[0] = $vv['tag_id'];
            $items = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $ccatid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id,1);
            $items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $ccatid, $include_subcat, $company_id, $site_id, $post_type, $tagids);

            $tab_active = '';
            if($zcpcatid != 0)
            {
                if($addon != 0) {
                    if($addon == $this->addon->id) {
                        if($zcpcatid != $vv['tag_id'] || $detail != 0)
                        {
                            $tab_active .= ' listhide';
                        }
                    }
                    else
                    {
                        if($kk != 0) {
                            $tab_active .= ' listhide';
                        }
                    }
                }
                else
                {
                    if($zcpcatid != $vv['tag_id'])
                    {
                        $tab_active .= ' listhide';
                    }
                }
            }
            else
            {
                if($addon == $this->addon->id) {
                    if ($kk != 0 || $detail != 0) {
                        $tab_active .= ' listhide';
                    }
                }
                else
                {
                    if($kk != 0) {
                        $tab_active .= ' listhide';
                    }
                }
            }
            $output .= '<div class="art_list_id jwpf-addon' . $tab_active . '" id="div'.$vv['tag_id'].'">';
            // 列表导航
            $output .= $this->listNav($vv['title']);
            if (!count($items)) {
                $output .= '<p class="alert alert-warning" style="padding: .75rem 1.25rem;margin-top: 20px;">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            }
            else
            {
                if (count((array)$items)) {
                    $output .= $this->artListHtml($items, $vv['tag_id'], $page);
                    // 此处为 翻页 样式
                    $output .= $this->artListPage($items_count, $vv['tag_id'], $page);
                }
            }
            $output .= '</div>';
        }

        return '<div class="art-tab-list">' . $output_tab . '<div class="art-list">' . $output . '</div>' . '</div>';
    }

    // 处理文章详情翻页
    public function pageArt($art_id) {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $catid_id = $_GET['catid_id'] ?? 0;
        $page_id = $_GET['id'];
        $page = $_GET['page'] ?? 1;

        $art_title = '暂无';
        if($art_id && $art_id > 0) {
            $art_art = JwPageFactoryBase::getArticleById2($art_id, $catid_id);
            $art_url = JRoute::_("/index.php/component/jwpagefactory/?view=page&id=".$page_id."&detail=".$art_id."&Itemid=0".'&layout_id='.$layout_id.'&site_id='.$site_id.'&catid_id='.$catid_id.'&page=' . $page . '&company_id='.$company_id.'&addon=' . $this->addon->id,$absolute=true);
            $art_title = '<a href="' . $art_url .'">' . $art_art->title . '</a>';
        }
        return $art_title;
    }

    // 文章列表上方导航
    public function listNav($title) {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 列表导航部分
        // 开启列表部分导航
        $show_list_nav = (isset($settings->show_list_nav) && $settings->show_list_nav) ? $settings->show_list_nav : 0;
        // 列表部分导航布局
        $list_nav_style = (isset($settings->list_nav_style) && $settings->list_nav_style) ? $settings->list_nav_style : 'nav01';
        // 列表部分导航标题
        $list_nav_name = (isset($settings->list_nav_name) && $settings->list_nav_name) ? $settings->list_nav_name : '新闻中心';
        $output_nav = '';
        if($show_list_nav == 1) {
            if($list_nav_style == 'nav01') {
                $output_nav .= "
                    <style>
                        {$addon_id} .list-nav {
                            display: flex;
                            width: 100%;
                            align-items: center;
                            justify-content: space-between;
                            border-bottom: #eee solid 2px;
                        }
                        {$addon_id} .list-nav .nav-left {
                            font-size: 18px;
                            line-height: 44px;
                            padding: 0 20px;
                            text-align: center;
                            color: #333;
                            font-weight: bold;
                            position: relative;
                        }
                        {$addon_id} .list-nav .nav-left:before {
                            content: '';
                            width: 2px;
                            height: 18px;
                            background-color: #e50011;
                            position: absolute;
                            left: 8px;
                            top: 0;
                            bottom: 0;
                            margin: auto;
                        }
                        {$addon_id} .list-nav .nav-left:after {
                            content: '';
                            width: 100%;
                            height: 2px;
                            background-color: #e50011;
                            position: absolute;
                            left: 0;
                            bottom: -2px;
                        }
                        {$addon_id} .list-nav .nav-right {
                            display: flex;
                            align-items: center;
                            font-size: 14px;
                            color: #666;
                        }
                        {$addon_id} .list-nav .nav-right span {
                            color:  #e50011;
                        }
                        {$addon_id} .list-nav .nav-right .nav-icon {
                            width: auto;
                            height: 100%;
                            margin-right: 4px;
                        }
                    </style>
                ";
                $output_nav .= '<div class="list-nav">
                    <div class="nav-left">' . $title . '</div>
                    <div class="nav-right">
                        <img class="nav-icon" src="https://oss.lcweb01.cn/jzt/6d102551-1c08-4a57-9a29-a2d204df9298/image/20211118/d1aa16c21c434c802485343db816398a.png" alt="">
                        <p class="nav">首页 > ' . $list_nav_name .' >&nbsp;<span>' . $title . '</span></p>
                    </div>
                </div>';
            }
        }
        return $output_nav;
    }

    // 文章列表 布局样式css
    public function css() {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        // 文章列表布局
        $art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : '';

        $output = '';

        //布局24
        if($art_type_selector == 'art24') {
            // 标题文字大小
            $art_title_fontsize = (isset($settings->art_title_fontsize) && $settings->art_title_fontsize) ? $settings->art_title_fontsize : 16;
            // 标题文字行高
            $art_title_lineHeight = (isset($settings->art_title_lineHeight) && $settings->art_title_lineHeight) ? $settings->art_title_lineHeight : 42;
            // 标题文字颜色
            $art_title_color = (isset($settings->art_title_color) && $settings->art_title_color) ? $settings->art_title_color : '#333';
            // 日期文字大小
            $art_time_fontsize = (isset($settings->art_time_fontsize) && $settings->art_time_fontsize) ? $settings->art_time_fontsize : 16;
            // 日期文字颜色
            $art_time_color = (isset($settings->art_time_color) && $settings->art_time_color) ? $settings->art_time_color : '#333';
            // 下划线高度
            $art_title_border_width = (isset($settings->art_title_border_width) && $settings->art_title_border_width) ? $settings->art_title_border_width : 1;
            // 下划线颜色
            $art_title_border_color = (isset($settings->art_title_border_color) && $settings->art_title_border_color) ? $settings->art_title_border_color : '#eee';
            // 下划线类型
            $art_title_border_type = (isset($settings->art_title_border_type) && $settings->art_title_border_type) ? $settings->art_title_border_type : 'dashed';
            // 开启列表前图标
            $show_title_icon = (isset($settings->show_title_icon) && $settings->show_title_icon) ? $settings->show_title_icon : 0;
            // 列表前图标
            $art_title_icon = (isset($settings->art_title_icon) && $settings->art_title_icon) ? $settings->art_title_icon : '';
            // 列表前图标宽度
            $art_title_icon_width = (isset($settings->art_title_icon_width) && $settings->art_title_icon_width) ? $settings->art_title_icon_width : 12;
            // 列表前图标高度
            $art_title_icon_height = (isset($settings->art_title_icon_height) && $settings->art_title_icon_height) ? $settings->art_title_icon_height : 12;
            // 移入 标题文字颜色
            $art_title_color_hover = (isset($settings->art_title_color_hover) && $settings->art_title_color_hover) ? $settings->art_title_color_hover : '';
            // 移入 下划线颜色
            $art_title_border_color_hover = (isset($settings->art_title_border_color_hover) && $settings->art_title_border_color_hover) ? $settings->art_title_border_color_hover : '';
            // 移入 下划线类型
            $art_title_border_type_hover = (isset($settings->art_title_border_type_hover) && $settings->art_title_border_type_hover) ? $settings->art_title_border_type_hover : '';
            // 移入 日期文字大小
            $art_time_fontsize_hover = (isset($settings->art_time_fontsize_hover) && $settings->art_time_fontsize_hover) ? $settings->art_time_fontsize_hover : '';
            // 移入 日期文字颜色
            $art_time_color_hover = (isset($settings->art_time_color_hover) && $settings->art_time_color_hover) ? $settings->art_time_color_hover : '';
            // 移入 列表前图标
            $art_title_icon_hover = (isset($settings->art_title_icon_hover) && $settings->art_title_icon_hover) ? $settings->art_title_icon_hover : '';
            // 第一条数据 图片宽度
            $first_img_width = (isset($settings->first_img_width) && $settings->first_img_width) ? $settings->first_img_width : 300;
            // 第一条数据 图片高度
            $first_img_height = (isset($settings->first_img_height) && $settings->first_img_height) ? $settings->first_img_height : 200;
            // 第一条数据 标题文字大小
            $first_title_fontsize = (isset($settings->first_title_fontsize) && $settings->first_title_fontsize) ? $settings->first_title_fontsize : 18;
            // 第一条数据 标题行高
            $first_title_lineHeight = (isset($settings->first_title_lineHeight) && $settings->first_title_lineHeight) ? $settings->first_title_lineHeight : 24;
            // 第一条数据 标题文字颜色
            $first_title_color = (isset($settings->first_title_color) && $settings->first_title_color) ? $settings->first_title_color : '#333';
            // 第一条数据 简介文字大小
            $first_desc_fontsize = (isset($settings->first_desc_fontsize) && $settings->first_desc_fontsize) ? $settings->first_desc_fontsize : 14;
            // 第一条数据 简介文字行高
            $first_desc_lineHeight = (isset($settings->first_desc_lineHeight) && $settings->first_desc_lineHeight) ? $settings->first_desc_lineHeight : 22;
            // 第一条数据 简介文字颜色
            $first_desc_color = (isset($settings->first_desc_color) && $settings->first_desc_color) ? $settings->first_desc_color : '#555';
            // 第一条数据 时间/更多文字大小
            $first_more_fontsize = (isset($settings->first_more_fontsize) && $settings->first_more_fontsize) ? $settings->first_more_fontsize : 14;
            // 第一条数据 时间/更多文字行高
            $first_more_lineHeight = (isset($settings->first_more_lineHeight) && $settings->first_more_lineHeight) ? $settings->first_more_lineHeight : 18;
            // 第一条数据 时间/更多文字颜色
            $first_more_color = (isset($settings->first_more_color) && $settings->first_more_color) ? $settings->first_more_color : '#999';
            // 第一条数据 移入 标题文字颜色
            $first_title_color_hover = (isset($settings->first_title_color_hover) && $settings->first_title_color_hover) ? $settings->first_title_color_hover : '';
            // 第一条数据 移入 简介文字颜色
            $first_desc_color_hover = (isset($settings->first_desc_color_hover) && $settings->first_desc_color_hover) ? $settings->first_desc_color_hover : '';
            // 第一条数据 移入 时间/更多文字颜色
            $first_more_color_hover = (isset($settings->first_more_color_hover) && $settings->first_more_color_hover) ? $settings->first_more_color_hover : '';
            // 线高度
            $list_border_width = (isset($settings->list_border_width) && $settings->list_border_width) ? $settings->list_border_width : 1;
            // 线颜色
            $list_border_color = (isset($settings->list_border_color) && $settings->list_border_color) ? $settings->list_border_color : '#eee';
            // 线类型
            $list_border_type = (isset($settings->list_border_type) && $settings->list_border_type) ? $settings->list_border_type : 'dashed';

            $output .= "
                {$addon_id} .art_list_id * {
                    padding: 0;
                    margin: 0;
                }
                {$addon_id} .jsqg_news {
                    width: 100%;
                }
                {$addon_id} .jsqg_news .art24_a {
                    width: 100%;
                    text-decoration: none;
                    height: {$art_title_lineHeight}px;
                    line-height: {$art_title_lineHeight}px;
                    border-bottom: {$art_title_border_width}px {$art_title_border_type} {$art_title_border_color};
                    display: flex;
                    justify-content: space-between;
                    overflow: hidden;
                    font-size: {$art_title_fontsize}px;
                    color: {$art_title_color};
                    transition: all ease-in-out 0.3s;
                    position: relative;
                }
                ";
            if($show_title_icon == 1) {
                $output .= "{$addon_id} .jsqg_news .art24_a {
                        padding-left: calc({$art_title_icon_width}px + 5px);
                    }
                    {$addon_id} .jsqg_news .art24_a:before {
                        content: '';
                        width: {$art_title_icon_width}px;
                        height: {$art_title_icon_height}px;
                        background: url({$art_title_icon}) no-repeat center;
                        background-size: 100%;
                        transition: all ease-in-out 0.3s;
                        position: absolute;
                        left: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                    {$addon_id} .jsqg_news .art24_a:hover:before {
                        background-image: url({$art_title_icon_hover});
                    }";
            }
            $output .= "{$addon_id} .jsqg_news .art24_a .fr {
                    font-size: {$art_time_fontsize}px;
                    color: {$art_time_color};
                    transition: all ease-in-out 0.3s;
                }
                {$addon_id} .jsqg_news .art24_a .fl{
                    width: calc(100% - 110px);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                {$addon_id} .jsqg_news .art24_a:hover {
                    color: {$art_title_color_hover};
                    border-bottom-color: {$art_title_border_color_hover};
                    border-bottom-style: {$art_title_border_type_hover};
                }
                {$addon_id} .jsqg_news .art24_a:hover .fr {
                    font-size: {$art_time_fontsize_hover}px;
                    color: {$art_time_color_hover};
                }
                {$addon_id} .jsqg_news .art24_a:last-child {
                    border: none;
                }
                /* 中间线 */
                {$addon_id} .jsqg_news .list-line {
                    width: 100%;
                    height: calc({$art_title_lineHeight}px / 3);
                    border-bottom: {$list_border_width}px {$list_border_color} {$list_border_type};
                    margin-bottom: calc({$art_title_lineHeight}px / 3);
                }
                /*第一条数据样式02*/
                {$addon_id} .jsqg_news .list-02 {
                    width: 100%;
                    padding: 20px 0;
                    border-bottom: #eee solid 1px;
                }
                {$addon_id} .jsqg_news .list-02 a {
                    display: block;
                }
                {$addon_id} .jsqg_news .list-02 .list-title {
                    font-size: {$first_title_fontsize}px;
                    color: {$first_title_color};
                    line-height: {$first_title_lineHeight}px;
                    font-weight: bold;
                }
                {$addon_id} .jsqg_news .list-02 .list-more {
                    font-size: {$first_more_fontsize}px;
                    color: {$first_more_color};
                    line-height: {$first_more_lineHeight}px;
                }
                {$addon_id} .jsqg_news .list-02 .list-desc {
                    font-size: {$first_desc_fontsize}px;
                    line-height: {$first_desc_lineHeight}px;
                    color: {$first_desc_color};
                    margin-top: 10px;
                    margin-bottom: 10px;
                }
                {$addon_id} .jsqg_news .list-02:hover .list-title {
                    color: {$first_title_color_hover};
                }
                {$addon_id} .jsqg_news .list-02:hover .list-desc {
                    color: {$first_desc_color_hover};
                }
                {$addon_id} .jsqg_news .list-02:hover .list-more {
                    color: {$first_more_color_hover};
                }
                /*第一条数据样式03*/
                {$addon_id} .jsqg_news .list-03 {
                    width: 100%;
                    display: flex;
                    padding: 20px 0;
                    border-bottom: #eee solid 1px;
                    margin-bottom: 10px;
                }
                {$addon_id} .jsqg_news .list-03 .img-box {
                    width: {$first_img_width}px;
                    height: {$first_img_height}px;
                    display: inline-block;
                }
                {$addon_id} .jsqg_news .list-03 .img-box img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                {$addon_id} .jsqg_news .list-03 .content-box {
                    display: inline-block;
                    width: calc(100% - {$first_img_width}px + 20px);
                    margin-left: 20px;
                }
                {$addon_id} .jsqg_news .list-03 .list-title {
                    font-size: {$first_title_fontsize}px;
                    color: {$first_title_color};
                    line-height: {$first_title_lineHeight}px;
                    font-weight: bold;
                }
                {$addon_id} .jsqg_news .list-03 .list-time {
                    font-size: {$first_more_fontsize}px;
                    color: {$first_more_color};
                    line-height: {$first_more_lineHeight}px;
                    margin-top: 10px;
                    margin-bottom: 10px;
                }
                {$addon_id} .jsqg_news .list-03 .list-desc {
                    font-size: {$first_desc_fontsize}px;
                    line-height: {$first_desc_lineHeight}px;
                    color: {$first_desc_color};
                }
                {$addon_id} .jsqg_news .list-03:hover .list-title {
                    color: {$first_title_color_hover};
                }
                {$addon_id} .jsqg_news .list-03:hover .list-desc {
                    color: {$first_desc_color_hover};
                }
                {$addon_id} .jsqg_news .list-03:hover .list-time {
                    color: {$first_more_color_hover};
                }
                {$addon_id} .listshow {
                    display:block;
                }
                {$addon_id} .listhide {
                    display:none;
                }
            ";
        }
        return $output;
    }

    // 文章列表html
    public function artListHtml($items, $wzcatid, $page) {
        $settings = $this->addon->settings;
        $addonId = $this->addon->id;
        // 文章列表布局
        $art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : '';
        // 文章列表 pc及平板列数
        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        // 文章列表 手机列数
        $columns_xs = (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : 2;
        // 文章列表 显示简介
        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        // 文章列表 简介字数
        $intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
        // 详情是否本插件
        $detail_from = (isset($settings->detail_from) && $settings->detail_from) ? $settings->detail_from : 0;
        // 详情页模版文章分类选项卡插件id
        $detail_addon = (isset($settings->detail_addon) && $settings->detail_addon) ? $settings->detail_addon : '';

        // 选择的列数  这里的使用$columns是因为列表以pc和平板为主
        $num = round(12 / $columns);
        // 宽度
        $width = 100 / $columns_xs ;
        $widthbig =100 / $columns ;

        $output = '';

        if($art_type_selector == 'art24') {
            // 第一条数据布局
            $art_list_first = (isset($settings->art_list_first) && $settings->art_list_first) ? $settings->art_list_first : 'art01';
            // 开启文章列表中间线
            $show_list_border = (isset($settings->show_list_border) && $settings->show_list_border) ? $settings->show_list_border : 0;
            // 第几条数据下面加中间线
            $list_border_num = (isset($settings->list_border_num) && $settings->list_border_num) ? $settings->list_border_num : 6;
            // 列表中间线
            $list_line = '';
            if($show_list_border == 1) {
                $list_line .= '<div class="list-line"></div>';
            }

            $output .= '<div class="jwpf-addon-content">';
            $output .= '<div class="jsqg_news">';
            foreach ($items as $key => $item) {
                if($item->image_thumbnail != '')
                {
                    $image = $item->image_thumbnail;
                }
                else
                {
                    $image = 'https://ijzt.china9.cn/components/com_jwpagefactory/addons/articles_list/assets/images/moren.png';
                }
                $links = $item->link;
                if($detail_from == 1 || $detail_addon) {
                    if($detail_addon) {
                        $addonId = $detail_addon;
                    }
                    $links .= '&page=' . $page . '&addon=' . $addonId;
                }
                if($key == 0 && $art_list_first != 'art01') {
                    $list_desc = '';
                    // var_dump($intro_limit);
                    if($show_intro == 1) {
                        $list_desc .= '<a href="' . $links  . '" class="list-desc">';
                        $list_desc .= mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8');
                        if($this->utf8_strlen($item->introtext) > $intro_limit) {
                            $list_desc .= '...';
                        }
                        $list_desc .= '</a>';
                    }
                    if($art_list_first == 'art02') {
                        $output .= '<div class="list-02">';
                        $output .= '<a href="' . $links  . '" class="list-title" title="'. $item->title . '">';
                        $output .= $item->title;
                        $output .= '</a>';
                        $output .= $list_desc;
                        $output .= '<a href="' . $links  . '" class="list-more">';
                        $output .= '了解更多>>';
                        $output .= '</a>';
                        $output .= '</div>';
                    }
                    if($art_list_first == 'art03') {
                        $output .= '<div class="list-03">';
                        $output .= '<a class="img-box" href="' . $links . '">
                                        <img src="' . $image . '" alt=""/>
                                    </a>';
                        $output .= '<div class="content-box">';
                        $output .= '<a href="' . $links  . '" class="list-title" title="'. $item->title . '">';
                        $output .= $item->title;
                        $output .= '</a>';
                        $output .= '<p class="list-time">';
                        $output .= '发布时间：' .date('Y-m-d',strtotime($item->created));
                        $output .= '</p>';
                        $output .= $list_desc;
                        $output .= '</div>';
                        $output .= '</div>';
                    }
                }
                else {
                    $output .= '<a href="' . $links  . '" class="art24_a" title="'. $item->title . '">';
                    $output .= '<span class="fl">'. $item->title . '</span>';
                    $output .= '<span class="fr">'. date('Y-m-d',strtotime($item->created)) .'</span>';
                    $output .= '</a>';
                }
                if($key == $list_border_num - 1) {
                    $output .= $list_line;
                }
            }
            $output .= '</div>';
            $output .= '</div>';
        }
        return $output;
    }

    // 文章列表 page
    public function artListPage($items_count, $wzcatid, $page) {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $addonId = $this->addon->id;
        // 分类id
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
        // 文章列表 每页条数
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        // 文章列表 显示分页
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : 0;
        // 文章列表 显示分页
        $page_style_selector = (isset($settings->page_style_selector) && $settings->page_style_selector) ? $settings->page_style_selector : 'page01';
        // 文章列表 分页 翻页字体颜色
        $page1_fontcolor = (isset($settings->page1_fontcolor)) ? $settings->page1_fontcolor : '#ffffff';
        // 文章列表 分页 翻页边框颜色
        $page1_bordercolor = (isset($settings->page1_bordercolor)) ? $settings->page1_bordercolor : '#2a68a7';
        // 文章列表 分页 翻页背景颜色
        $page1_bgcolor = (isset($settings->page1_bgcolor)) ? $settings->page1_bgcolor : '#ffffff';
        // 文章列表 分页 当前页字体颜色
        $page1_cur_fontcolor = (isset($settings->page1_cur_fontcolor)) ? $settings->page1_cur_fontcolor : '#ffffff';
        // 文章列表 分页 当前页边框颜色
        $page1_cur_bordercolor = (isset($settings->page1_cur_bordercolor)) ? $settings->page1_cur_bordercolor : '#2a68a7';
        // 文章列表 分页 当前页背景颜色
        $page1_cur_bgcolor = (isset($settings->page1_cur_bgcolor)) ? $settings->page1_cur_bgcolor : '#ffffff';
        // 文章列表 分页 上边距
        $page_top = (isset($settings->page_top)) ? $settings->page_top : 0;

        $output_page = '';
        if ($show_page) {
            $output_page .= "<style>
                {$addon_id} .page_plug {
                    width: 90%;
                    margin: 5px auto;
                    text-align: center;
                }
                {$addon_id} .page_plug a {
                    padding: 3px 8px;
                    border: 1px solid {$page1_bordercolor};
                    margin-right: 5px;
                    text-decoration: none;
                    color: {$page1_fontcolor};
                    background:{$page1_bgcolor};
                }
                {$addon_id} .page_plug .curPage {
                  border: 1px solid {$page1_cur_bordercolor};
                  color: {$page1_cur_fontcolor};
                  background:{$page1_cur_bgcolor};
                }
                /* 省略号翻页样式 */
                {$addon_id} div.zxf_pagediv {
                    margin: 0 auto;
                }
                {$addon_id} div.zxf_pagediv a.current {
                    background: {$page1_cur_bgcolor};
                    color: {$page1_cur_fontcolor};
                    border: 1px solid {$page1_cur_bordercolor};
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                }
                {$addon_id} .zxfPagenum {
                    color:{$page1_fontcolor};
                    border: 1px solid {$page1_bordercolor};
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    background:{$page1_bgcolor}; 
                    margin: 0 5px;
                }
                {$addon_id} .nextbtn, .prebtn, {$addon_id} span.disabled{
                    color:{$page1_fontcolor};
                }
                {$addon_id} div.zxf_pagediv span{
                    color:{$page1_fontcolor};
                }
                {$addon_id} .fenys {
                    margin-top: {$page_top}px;
                }
                @media (max-width:768px) {
                    #jw-page-factory .page-content .jwpf-section:first-child  {
                        padding-top: 0;
                    }
                }";
            if($page_style_selector == 'page03') {
                // 分页 上一页/下一页 背景颜色
                $page3_cur_bgcolor = (isset($settings->page3_cur_bgcolor)) ? $settings->page3_cur_bgcolor : '#2a68a7';
                // 分页 上一页/下一页 文字颜色
                $page3_cur_color = (isset($settings->page3_cur_color)) ? $settings->page3_cur_color : '#ffffff';
                // 分页 上一页/下一页 不可点击 背景颜色
                $page3_none_bgcolor = (isset($settings->page3_none_bgcolor)) ? $settings->page3_none_bgcolor : '#b3b3b3';
                // 分页 上一页/下一页 不可点击 文字颜色
                $page3_none_color = (isset($settings->page3_none_color)) ? $settings->page3_none_color : '#ffffff';
                $output_page .= "
                    {$addon_id} .current, {$addon_id} .zxfPagenum {
                        border-radius: 0px;
                    }
                    {$addon_id} .zxf_pagediv .prebtn, {$addon_id} .zxf_pagediv .nextbtn{
                        background-color: {$page3_cur_bgcolor};
                        color: {$page3_cur_color};
                        line-height: 30px;
                        height: 30px;
                        border-radius: 0;
                        font-size: 12px;
                    }
                    {$addon_id} .zxf_pagediv .prebtn {
                        margin-right: 5px;
                    }
                    {$addon_id} .zxf_pagediv .nextbtn {
                        margin-left: 5px;
                    }
                    {$addon_id} .zxf_pagediv .none {
                        background-color: {$page3_none_bgcolor};
                        color: {$page3_none_color};
                    }
                ";
            }
            $output_page .="</style>";
            $output_page .= '<div class="fenys">';
            if ($page_style_selector == 'page01') {
                $all_page = 1;
                if ($limit) {
                    $all_page = ceil($items_count / $limit);
                }
                $output_page .= '<div class="page_plug">';
                // 判断是不是第一页
                if($page && $page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '<a class="page_num" href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '"> << </a>';
                }
                for ($i = 1; $i <= $all_page; $i++) {
                    if ($page == $i) {
                        $output_page .= '<a class="curPage">' . $i . '</a>';
                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                        $output_page .= '<a class="page_num" href="' . $url . '&page=' . $i . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '">' . $i . '</a>';
                    }
                }
                // 判断是不是最后一页
                if($page < $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '"> >> </a>';
                }

                $output_page .= '</div>';
                // $output_page .= '<div class="page_plug">共' . $items_count . '条</div>';
            }

            if ($page_style_selector == 'page02') {
                $all_page = 1;
                if ($limit) {
                    $all_page = ceil($items_count / $limit);
                }
                /* 编辑器省略号分页 */
                $output_page .= '<div class="zxf_pagediv " id="false_page" style="width:100%;display:flex;align-items: center;justify-content: center">';
                if($page != 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '    <a href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '" class="prebtn">上一页</a>';
                }
                $output_page .= ' <div class="page">';
                for ($i = 1; $i <= $all_page; $i++) {
                    if ($page == $i) {
                        $output_page .= '<a class="current">' . $i . '</a>';
                    } else {
                        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                        $output_page .= '<a class="zxfPagenum" href="' . $url . '&page=' . $i . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '">' . $i . '</a>';

                    }
                }
                $output_page .= '</div>';
                // 判断是不是最后一页
                if($page != $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '    <a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '" class="nextbtn">下一页</a>';
                }
                $output_page .= '</div>';
                $output_page .= '<script></script>';
            }
            if ($page_style_selector == 'page03') {
                // 最多显示分页数
                $page3_num = (isset($settings->page3_num)) ? $settings->page3_num : 4;
                $all_page = 1;
                $more_page = ($page3_num - 1);
                if ($limit) {
                    $all_page = ceil($items_count / $limit);
                }
                /* 编辑器省略号分页 */
                $output_page .= '<div class="zxf_pagediv " id="false_page" style="width:100%;display:flex;align-items: center;justify-content: center">';
                if($page > 1) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '    <a href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '" class="prebtn">上一页</a>';
                }
                else
                {
                    $output_page .= '<a class="prebtn none">上一页</a>';
                }
                $output_page .= ' <div class="page_three">';
                $page_list = $this->art_list_page($all_page, $page, $page3_num);
                foreach ($page_list as $key => $item) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $page_html = '<a class="zxfPagenum" href="' . $url . '&page=' . $item . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '">' . $item . '</a>';
                    if ($page == $item) {
                        $output_page .= '<a class="current">' . $item . '</a>';
                    }elseif (is_numeric($item)) {
                        $output_page .= $page_html;
                    }
                    if ($item == '...') {
                        $output_page .= '<span style="color: #555;">...</span>';
                    }
                }
                $output_page .= '</div>';
                // 判断是不是最后一页
                if($page < $all_page) {
                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid', 'detail', 'addon']);
                    $output_page .= '    <a href="' . $url . '&page=' . ($page + 1) . '&zcpcatid=' . $wzcatid . '&addon=' . $addonId . '" class="nextbtn">下一页</a>';
                }
                else
                {
                    $output_page .= '<a class="nextbtn none">下一页</a>';
                }
                $output_page .= '</div>';
                $output_page .= '<script></script>';
            }
            $output_page .= '</div>';
        }
        return $output_page;
    }

    // 处理翻页设置显示几页
    public function art_list_page($count, $page, $num)
    {
        if($count < $num) {
            $start = 1;
            $end = $count;
            $nnn = [];
            $numss = 0;
            for($i=$start; $i<=$end; $i++)
            {
                $nnn[$numss] = $i;
                $numss++;
            }
        }else {
            $show_num = $num;
            if($page + 1 >= $count)
            {
                $num = $num-1;
            }
            else
            {
                $num = $num-2;
            }
            $nnn = [];
            $numss = 2;
            $num = min($count, $num); //处理显示的页码数大于总页数的情况
            if($page > $count || $page < 1) return; //处理非法页号的情况
            $end = $page + floor($num/2) <= $count ? $page + floor($num/2) : $count; //计算结束页号
            $start = $end - $num + 1; //计算开始页号
            if($start < 2) { //处理开始页号小于1的情况
                $end -= $start - 2;
                $start = 2;
            }
            for($i=$start; $i<=$end; $i++)
            {
                //输出分页条，请自行添加链接样式
                if($i!=1 && $i!=$count)
                {
                    $nnn[$numss] = $i;
                }
                $numss++;
            }
            if(($nnn[2]-1) > 1)
            {
                $nnn[1] = '...';
            }
            // print_r($nnn);die;
            if($nnn[count($nnn)]+1 < $count && $count > $show_num)
            {
                $nnn[count($nnn)+2] = '...';
            }
            $nnn[0] = 1;
            $nnn[count($nnn)+1] = $count;
            ksort($nnn);
        }
        return $nnn;
    }

//    public function css()
//    {
//        $addon_id = '#jwpf-addon-' . $this->addon->id;
//        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
//        $css_path = new JLayoutFile('addon.css.button', $layout_path);
//        $settings = $this->addon->settings;
//
//        $options = new stdClass;
//        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
//        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
//        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
//        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
//        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
//        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
//        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
//        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
//        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';
//
//        return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
//    }

    public function scripts()
    {
        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/newnav.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $scripts;
    }


    /* 带省略号分页js */
    public function js()
    {
        $currentPage =isset($_GET['page'])  ? $_GET['page'] : 1;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $art_type_selector = (isset($settings->art_type_selector) && $settings->art_type_selector) ? $settings->art_type_selector : '';
        $js = '';
        $js .= '
            jQuery(document).on("ready", function(){
                "use strict";
                console.log("11111")
                var pLength = jQuery("' . $addon_id . ' .page a").length;
                if (pLength >= 10) {
                   jQuery("' . $addon_id . ' .page a").hide();
                   jQuery("' . $addon_id . ' .page a").eq(5).after("<span>...</span>");
                    for (let i = 0; i < pLength; i++) {
                        if (i < 5 || i > pLength - 5) {
                           jQuery("' . $addon_id . ' .page a").eq(i).show();
                        }
                    }
                }
                var curPage =  '.$currentPage.'; //当前页
                console.log(curPage,"当前页")
                console.log(pLength,"总页数")
                jQuery("' . $addon_id . ' .page a").click(function(){
                    curPageClick();

                })
                function curPageClick() {
                    jQuery("' . $addon_id . ' .page a").hide(); //先把页都隐藏
                    jQuery("' . $addon_id . ' .page span").hide(); //把省略号都隐藏
                     if (curPage >= 5 && curPage < pLength - 4 && pLength >= 10) {
                         console.log("当前页中间几页")
                        jQuery("' . $addon_id . ' .page a").eq(0).after("<span>...</span>"); //在第1页后加省略号
                        if((curPage+5) >= pLength) {
                            jQuery("' . $addon_id . ' .page a").eq(curPage + 5).after("<span>...</span>"); //在当前点击页数后五位加省略号
                        }
               
                         console.log(curPage + 5);
                         for (let i = curPage - 2; i < pLength; i++) {
                             if (i < curPage + 5 || i > pLength - 5) {
                                jQuery("' . $addon_id . ' .page a").eq(0).show();
                                jQuery("' . $addon_id . ' .page a").eq(i).show();
                             }
                         }
                     } else if (curPage <= 5 && pLength >= 10) {
                         console.log("当前页小于等于5")
                        jQuery("' . $addon_id . ' .page a").eq(5).after("<span>...</span>");
                         for (let i = 0; i < pLength; i++) {
                             if (i < 5 || i > pLength - 5) {
                                jQuery("' . $addon_id . ' .page a").eq(i).show();
                             }
                         }
                     } else if (curPage >= pLength - 4 && pLength >= 10) {
                         console.log("当前页在后面几页")
                        jQuery("' . $addon_id . ' .page a").eq(4).after("<span>...</span>");
                         for (let i = 0; i < pLength; i++) {
                             if (i < 4 || i > pLength - 5) {
                                jQuery("' . $addon_id . ' .page a").eq(i).show();
                                jQuery("' . $addon_id . ' .page a").eq(curPage - 1).show();
                                jQuery("' . $addon_id . ' .page a").eq(curPage - 2).show();
                             }
                         }
                     } else if (pLength < 10) {
                        jQuery("' . $addon_id . ' .page a").show(); //先把页都隐藏
                     }
                }
                curPageClick();
            });
        ';
        return $js;
    }

    //数组多层级 递归
    public function subTree($data, $pid = 1, $deep = 0)
    {   //用来存放数据
        $arr = [];
        //遍历数据库中的字段
        foreach ($data as $val)
        {
            //判断字段pid相等时放行
            if ($pid == $val['parent_id'])
            {
                //不同的层级
                $val['deep'] = $deep;
                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
                $val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
                //如果遇到pid==本条id时，将其存入数组
                $arr[] = $val;
            }
        }

        //返回数组
        return $arr;
    }

    /* 带省略号css */
    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/zxf_page.css',
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css'
        );
        return $style_sheet;
    }
    static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }

    //去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

    function utf8_strlen($string = null) {
        // 将字符串分解为单元
        preg_match_all("/./us", $string, $match);
        // 返回单元个数
        return count($match[0]);
    }
}
