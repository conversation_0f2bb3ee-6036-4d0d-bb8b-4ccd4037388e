$(function () {
    dur('.wow', '1.5');
    new WOW().init();

    function dur(a1, b1) {
        $(a1).attr('data-wow-duration', (b1 + 's'));
    };
    if ($(window).width() > 1023) {
        var interleaveOffset = 0.5; //瑙嗗樊姣斿€�
        var xxdgl_b = new Swiper(".xxdgl_b .swiper", {
            loop: true,
            speed: 1000,
            grabCursor: true,
            watchSlidesProgress: true,
            mousewheelControl: true,
            keyboardControl: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            on: {
                init: function () {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function (swiper) {
                    swiperAnimate(this);
                },
                slideChangeTransitionStart: function (swiper) {
                    $(".xxdgl_b .a1 .a2 .item").eq(swiper.realIndex).addClass('select').siblings().removeClass('select');
                },
                progress: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        var slideProgress = swiper.slides[i].progress;
                        var innerOffset = swiper.width * interleaveOffset;
                        var innerTranslate = slideProgress * innerOffset;
                        swiper.slides[i].querySelector(".slide-inner").style.transform =
                            "translate3d(" + innerTranslate + "px, 0, 0)";
                    }
                },
                touchStart: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = "";
                    }
                },
                setTransition: function (swiper, speed) {
                    swiperAnimate(this);
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = speed + "ms";
                        swiper.slides[i].querySelector(".slide-inner").style.transition =
                            speed + "ms";
                    }
                }
            }
        });
        var xxdgl_c = new Swiper(".xxdgl_c .swiper", {
            loop: true,
            speed: 1000,
            grabCursor: true,
            watchSlidesProgress: true,
            mousewheelControl: true,
            keyboardControl: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            on: {
                init: function () {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function (swiper) {
                    swiperAnimate(this);
                },
                progress: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        var slideProgress = swiper.slides[i].progress;
                        var innerOffset = swiper.width * interleaveOffset;
                        var innerTranslate = slideProgress * innerOffset;
                        swiper.slides[i].querySelector(".slide-inner").style.transform =
                            "translate3d(" + innerTranslate + "px, 0, 0)";
                    }
                },
                touchStart: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = "";
                    }
                },
                setTransition: function (swiper, speed) {
                    swiperAnimate(this);
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = speed + "ms";
                        swiper.slides[i].querySelector(".slide-inner").style.transition =
                            speed + "ms";
                    }
                }
            }
        });
        $(document).on('click', '.xxdgl_b .a1 .a2 .item', function () {
            let index = $(this).index();
            if ($(this).hasClass('select')) {
                return false;
            } else {
                $(this).addClass('select').siblings().removeClass('select');
                xxdgl_b.slideToLoop(index, 1000, false);
            }
        });
    
        var xxdgl_a = new Swiper('.xxdgl_a .swiper', {
            effect: 'coverflow',
            spaceBetween: 30,
            speed: 1500,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            navigation: {
                nextEl: '.xxdgl_a .next',
                prevEl: '.xxdgl_a .prev',
            },
            pagination: {
                el: '.xxdgl_a .a1',
                modifierClass: 'pagination',
                bulletClass: 'ada',
                bulletActiveClass: 'active',
                clickable: true,
                renderBullet: function (index, className) {
                    let num = index + 1
                    if (num < 10) {
                        num = '0' + num
                    }
                    return '<span class="' + className + '">' + num + '</span>';
                },
            },
            on: {
                init: function () {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function (swiper) {
                    swiperAnimate(this);
                },
                setTransition: function (swiper, speed) {
                    swiperAnimate(this);
                }
            }
        });
        var app_e = new Swiper(".app_e .swiper", {
            effect: 'coverflow',
            spaceBetween: 30,
            speed: 1500,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.app_e .pagination',
                modifierClass: 'pagination',
                bulletClass: 'ada',
                bulletActiveClass: 'active',
                type: 'custom',
                renderCustom: function (swiper, current, total) {
                    if (current < 10) {
                        current = "0" + current
                    }
                    if (total < 10) {
                        total = "0" + total
                    }
                    return current + '<em> / ' + total + '</em>';
                }
            },
        });


    } else {
        var app_e = new Swiper(".app_e .swiper", {
            effect: 'coverflow',
            spaceBetween: 30,
            speed: 1500,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.app_e .pagination',
                modifierClass: 'pagination',
                bulletClass: 'ada',
                bulletActiveClass: 'active',
                type: 'custom',
                renderCustom: function (swiper, current, total) {
                    if (current < 10) {
                        current = "0" + current
                    }
                    if (total < 10) {
                        total = "0" + total
                    }
                    return current + '<em> / ' + total + '</em>';
                }
            },
        });
        var interleaveOffset = 0.5; //瑙嗗樊姣斿€�
        var xxdgl_b = new Swiper(".xxdgl_b .swiper", {
            loop: true,
            speed: 1000,
            grabCursor: true,
            watchSlidesProgress: true,
            mousewheelControl: true,
            keyboardControl: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            on: {
                init: function () {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function (swiper) {
                    swiperAnimate(this);
                },
                slideChangeTransitionStart: function (swiper) {
                    $(".xxdgl_b .a1 .a2 .item").eq(swiper.realIndex).addClass('select').siblings().removeClass('select');
                },
                progress: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        var slideProgress = swiper.slides[i].progress;
                        var innerOffset = swiper.width * interleaveOffset;
                        var innerTranslate = slideProgress * innerOffset;
                        swiper.slides[i].querySelector(".slide-inner").style.transform =
                            "translate3d(" + innerTranslate + "px, 0, 0)";
                    }
                },
                touchStart: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = "";
                    }
                },
                setTransition: function (swiper, speed) {
                    swiperAnimate(this);
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = speed + "ms";
                        swiper.slides[i].querySelector(".slide-inner").style.transition =
                            speed + "ms";
                    }
                }
            }
        });
        var xxdgl_c = new Swiper(".xxdgl_c .swiper", {
            loop: true,
            speed: 1000,
            grabCursor: true,
            watchSlidesProgress: true,
            mousewheelControl: true,
            keyboardControl: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            on: {
                init: function () {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function (swiper) {
                    swiperAnimate(this);
                },
                progress: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        var slideProgress = swiper.slides[i].progress;
                        var innerOffset = swiper.width * interleaveOffset;
                        var innerTranslate = slideProgress * innerOffset;
                        swiper.slides[i].querySelector(".slide-inner").style.transform =
                            "translate3d(" + innerTranslate + "px, 0, 0)";
                    }
                },
                touchStart: function (swiper) {
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = "";
                    }
                },
                setTransition: function (swiper, speed) {
                    swiperAnimate(this);
                    for (var i = 0; i < swiper.slides.length; i++) {
                        swiper.slides[i].style.transition = speed + "ms";
                        swiper.slides[i].querySelector(".slide-inner").style.transition =
                            speed + "ms";
                    }
                }
            }
        });
        $(document).on('click', '.xxdgl_b .a1 .a2 .item', function () {
            let index = $(this).index();
            if ($(this).hasClass('select')) {
                return false;
            } else {
                $(this).addClass('select').siblings().removeClass('select');
                xxdgl_b.slideToLoop(index, 1000, false);
            }
        });

        var xxdgl_a = new Swiper('.xxdgl_a .swiper', {
            slidesPerView: 1.08,
            spaceBetween: 15,
            speed: 1500,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            navigation: {
                nextEl: '.xxdgl_a .next',
                prevEl: '.xxdgl_a .prev',
            },
            pagination: {
                el: '.xxdgl_a .a1',
                modifierClass: 'pagination',
                bulletClass: 'ada',
                bulletActiveClass: 'active',
                clickable: true,
                renderBullet: function (index, className) {
                    let num = index + 1
                    if (num < 10) {
                        num = '0' + num
                    }
                    return '<span class="' + className + '">' + num + '</span>';
                },
            }
        });
    }

})