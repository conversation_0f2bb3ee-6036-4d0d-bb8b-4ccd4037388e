<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'tz_button_wx', 
        'title' => JText::_('跳转微信小程序'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '产品',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'service_product_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                    ),
                    'std' => 'type1',
                ),
                'xcx_app_id' => array(
                    'type' => 'text',
                    'title' => JText::_('小程序app_id'),
                    'desc' => JText::_('小程序appid,上微信后台查看'),
                    'std' => '',
                    'depends' => array(array('service_product_type', '=', 'type1')),
                ),
                'xcx_app_secret' => array(
                    'type' => 'text',
                    'title' => JText::_('小程序app_secret'),
                    'desc' => JText::_('小程序appsecret,上微信后台查看'),
                    'std' => '',
                    'depends' => array(array('service_product_type', '=', 'type1')),
                ),
                'bgcolor_type1' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮背景颜色'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'std' => ''
                ),
                'border_color_type1' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'std' => ''
                ),
                'font_color_type1' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'std' => ''
                ),
                'font_size_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮字体大小'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'max' => 50,
                    'min' => 0,
                    'std' => 16,
                ),
                'button_width_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'max' => 500,
                    'min' => 0,
                    'std' => 100,
                ),
                'button_height_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'max' => 500,
                    'min' => 0,
                    'std' => 50,
                ),
                'border_width_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮边框宽度'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'max' => 100,
                    'min' => 0,
                    'std' => 1,
                ),
                'border_radius_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮边框弧度'),
                    'depends' => array(array('service_product_type', '=', 'type1')),
                    'max' => 500,
                    'min' => 0,
                    'std' => 0,
                ),
            ),
        ),
    )
);
