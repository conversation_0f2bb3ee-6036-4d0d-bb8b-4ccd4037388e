<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'zihai_tab',
        'title' => JText::_('官网tab'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '常用插件',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'section_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type01' => '布局01',
                        'type02' => '布局02',
                        'type03' => '布局03',
                        'type04' => '布局04',
                        'type05' => '布局05',
                        'type06' => '布局06',
                        'type07' => '布局07',
                        'type08' => '布局08',
                        'type09' => '布局09',
                        'type10' => '布局10',
                        'type11' => '布局11',
                        'type12' => '布局12',
                        'type13' => '布局13',

                    ),
                    'std' => 'type01',
                ),
                'section_title' => array(
                    'type' => 'text',
                    'title' => '版块名称',
                    'std' => '专业的电商行业解决方案',
                    'depends' => array(
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'), 
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                ),
                // 'section_title_type10' => array(
                //     'type' => 'text',
                //     'title' => '简介',
                //     'std' => '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;好的产品质量是企业立足、发展的根本，也是企业建设的源动力。如果将龙采比喻为一辆性能优越的越野车，那技术团队就是马力十足的发动机。他们用丰富的经验和精湛的功力，一次次攻克技术的高峰!',
                //     'depends' => array(
                //         array('section_type', '=', 'type10'),
                //     ),
                // ),
                // 'section_desc_type10' => array(
                //     'type' => 'text',
                //     'title' => '详情',
                //     'std' => '龙采科技集团利用先进的互联网技术，打破原有的行业运营模式，将发展过程中积累的技术和业务经验转化和应用于信息化整合平台中，为企业提供现代化营销新平台。作为网站建设与网络推广行业专家，网络建设开发团队，集团不断引进大量技术人才，短短几年时间，从十几人的小团队，发展成为北京、黑龙江、辽宁、山西四个技术中心，五百余人的强大技术支持团队。为龙采品牌在互联网行业长期保持领先地位提供了坚实的基础。',
                //     'depends' => array(
                //         array('section_type', '=', 'type10'),
                //     ),
                // ),
                'type10_active' => array(
                    'type' => 'slider',
                    'title' => '默认选中(从0开始)',
                    'depends' => array(
                        array('section_type', '=', 'type10'),
                    ),
                    'std'=>'2',
                ),
                'section_desc' => array(
                    'type' => 'text',
                    'title' => '版块简介',
                    'std' => '专业全面，多年电商服务经验，我们懂您的真正需求',
                    'depends' => array(
                        array('section_type', '!=', 'type02'),
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'),
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                ),
                'type02_section_desc' => array(
                    'type' => 'editor',
                    'title' => '版块简介',
                    'std' => '深知行业特性及功能所需网校从技术到运营<br/>多方面降低教育信息化互联网化门槛',
                    'depends' => array(
                        array('section_type', '!=', 'type01'),
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'),
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                ),
                'section_title_icon1' => array(
                    'type' => 'media',
                    'title' => '标题左侧图标',
                    'format' => 'image',
                    'depends' => array(
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'),
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210720/15dae4aa2d2c5abf3710ac8e5e5d14fe.png'
                ),
                'section_title_icon2' => array(
                    'type' => 'media',
                    'title' => '标题右侧图标',
                    'format' => 'image',
                    'depends' => array(
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'),
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210720/aba607862ae608f841c1bdc53bb93366.png'
                ),
                'jw_tab_item' => array(
                    'title' => JText::_('选项组'),
                    'std' => array(
                        array(
                            'title' => '跨境电商方案',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220520/41eaba0ad8f0465827969431d06c9316.jpg',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20220520/969a672ea761e629601affbefb76d211.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20220520/d9c8aca72a2584d5633090d2be73196a.png',
                            'icon3' => 'https://oss.lcweb01.cn/joomla/20220520/997ffd85f558cf5af31929beb4ff2833.png',
                            'title_big' => '跨境电商解决方案',
                            'content_big' => '专注解决跨境电商企业的清关效率低下、库存管理、分销发展吃力、信用问题凸显和政策支持困境',
                            'desc_big' => '一招解决5大困境 · 3大主流化运营模式 · 订单同步海关系统 · 高效率业务流程',
                            'title_sm1' => '京东自营',
                            'desc_sm1' => '自营+入驻',
                            'content_sm1' => '控制品质，同时吸纳优质供应商弥补货源 品类不足，商品存储在海关仓库。',
                            'title_sm2' => '垂直B2C模式',
                            'desc_sm2' => '(保税区直营+自采)',
                            'content_sm2' => '平台选择某个特定领域，直接参与货源组 织、物流仓储、买卖流程等。',
                            'title_sm3' => 'C2C模式',
                            'desc_sm3' => '(直销、直购、直邮)',
                            'content_sm3' => '由海外零售商直销给中国消费者。',
                        ),
                        array(
                            'title' => '零售商超方案',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220520/41eaba0ad8f0465827969431d06c9316.jpg',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20220520/969a672ea761e629601affbefb76d211.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20220520/d9c8aca72a2584d5633090d2be73196a.png',
                            'icon3' => 'https://oss.lcweb01.cn/joomla/20220520/997ffd85f558cf5af31929beb4ff2833.png',
                            'title_big' => '零售商超行业解决方案',
                            'content_big' => '结合互联网趋势与商超行业的特性，提供多销售终端，线上线下一体化，从商品销售管理、财务结算到物流配送...',
                            'desc_big' => '完善的 · 商品管理体系 · 会员管理体系 · 营销管理体系 · 商超结算体系',
                            'title_sm1' => '线上全渠道拓展',
                            'desc_sm1' => '从传统商超到电商转型，从PC到移动，从单点到多点布局，从纯电商到社交媒体，实现零售的更多可能性',
                            'content_sm1' => '从传统商超到电商转型，从PC到移动，从单点到多点布局，从纯电商到社交媒体，实现零售的更多可能性',
                            'title_sm2' => '线下体验完善',
                            'desc_sm2' => '为消费者提供更便捷的购物体验，涉及从排队买单到送货上门等环节，让实体商超变得更加“接地气”。',
                            'content_sm2' => '为消费者提供更便捷的购物体验，涉及从排队买单到送货上门等环节，让实体商超变得更加“接地气”。',
                            'title_sm3' => '线上全渠道拓展',
                            'desc_sm3' => '借助商超方案整合线下线上的大数据、流量、供应商、会员资源、物流、区域供应商等资源，互通互联。',
                            'content_sm3' => '借助商超方案整合线下线上的大数据、流量、供应商、会员资源、物流、区域供应商等资源，互通互联。',
                        ),
                        array(
                            'title' => '多供应商方案',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220520/41eaba0ad8f0465827969431d06c9316.jpg',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20220520/969a672ea761e629601affbefb76d211.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20220520/d9c8aca72a2584d5633090d2be73196a.png',
                            'icon3' => 'https://oss.lcweb01.cn/joomla/20220520/997ffd85f558cf5af31929beb4ff2833.png',
                            'title_big' => '多供应商解决方案',
                            'content_big' => '帮助企业打通上游供应链，整合已有的资源优势，结合线上全渠道销售通道，构建一个智能化的零售商业生态圈',
                            'desc_big' => '平台0库存 · 减少物流流程 · 降低运营成本 · 定点分仓发货',
                            'title_sm1' => '多供应商',
                            'desc_sm1' => '供应商在平台发布商品，设置供货价，直接对消费者发货及提供售后。',
                            'content_sm1' => '供应商在平台发布商品，设置供货价，直接对消费者发货及提供售后。',
                            'title_sm2' => '平台方',
                            'desc_sm2' => '平台审核商品，设置销售价，专心经营对外推广促销活动，实现平台0仓储。',
                            'content_sm2' => '平台审核商品，设置销售价，专心经营对外推广促销活动，实现平台0仓储。',
                            'title_sm3' => '消费者',
                            'desc_sm3' => '直接由供应商发货和售后，减少物流流程，加快收货和退/换货时间。',
                            'content_sm3' => '直接由供应商发货和售后，减少物流流程，加快收货和退/换货时间。',
                        ),
                        array(
                            'title' => '新零售方案',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220520/41eaba0ad8f0465827969431d06c9316.jpg',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20220520/969a672ea761e629601affbefb76d211.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20220520/d9c8aca72a2584d5633090d2be73196a.png',
                            'icon3' => 'https://oss.lcweb01.cn/joomla/20220520/997ffd85f558cf5af31929beb4ff2833.png',
                            'title_big' => '全渠道新零售方案',
                            'content_big' => '助力传统零售企业打通线上线下渠道，实现网店和实体门店一体化运营、突破渠道数据壁垒的电商解决方案',
                            'desc_big' => '线上下单，线下服务 · 线上发券，线下核销 · 线下缺货，线上代发 · 线下购物，线上会员',
                            'title_sm1' => '精细化运营',
                            'desc_sm1' => '1、打破空间局限，提高门店坪效；
                            2、告别混乱管理，打通进销存；
                            3、用数据驱动门店管理；',
                            'content_sm1' => '1、打破空间局限，提高门店坪效；<br>
                            2、告别混乱管理，打通进销存；<br>
                            3、用数据驱动门店管理；',
                            'title_sm2' => '高效管理',
                            'desc_sm2' => '1、全方位采集消费者数据；<br>
                            2、线上线下一套会员数据；<br>
                            3、精细化管理会员；',
                            'content_sm2' => '1、全方位采集消费者数据；
                            2、线上线下一套会员数据；
                            3、精细化管理会员；',
                            'title_sm3' => '全渠道体系',
                            'desc_sm3' => '1、为客户输出整体方案；<br>
                            2、省去多套系统购买、对接成本；<br>
                            3、兼容加盟、直营两套体系；',
                            'content_sm3' => '1、为客户输出整体方案；
                            2、省去多套系统购买、对接成本；<br>
                            3、兼容加盟、直营两套体系；',
                        ),
                        array(
                            'title' => 'O2O门店方案',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20220520/41eaba0ad8f0465827969431d06c9316.jpg',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20220520/969a672ea761e629601affbefb76d211.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20220520/d9c8aca72a2584d5633090d2be73196a.png',
                            'icon3' => 'https://oss.lcweb01.cn/joomla/20220520/997ffd85f558cf5af31929beb4ff2833.png',
                            'title_big' => '智慧门店O2O方案',
                            'content_big' => '对接三大主流O2O电商模式，线上线下无缝融合，通过门店APP即可对所有门店进行全能化管理、运营等。',
                            'desc_big' => '独立门店首页 · 移动门店APP · 无限制加门店 · 移动收款/核销POS机',
                            'title_sm1' => '合作加盟模式',
                            'desc_sm1' => '商家拥有多家合作加盟店，根据用户的位置将用户匹配到最合适的门店，由该门店为用户提供相应的服务。',
                            'content_sm1' => '· 同类案例：千惠超市、芙蓉兴盛',
                            'title_sm2' => '同城区域模式',
                            'desc_sm2' => '商家是做同城或区域电商的，每家合作门店相对独立或者经营多品类，按距离远近向用户展示最近的门店。',
                            'content_sm2' => '· 同类案例：京东到家、百度外卖',
                            'title_sm3' => '连锁直营模式',
                            'desc_sm3' => '商家总部和门店都有发货能力，线上平台是一个相对中心化的流量入口，由平台将流量分发到各个门店中。',
                            'content_sm3' => '· 同类案例：美特斯邦威、优衣库',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('选项的名称'),
                            'std' => '',
                        ),
                        'bg' => array(
                            'type' => 'media',
                            'title' => JText::_('背景'),
                            'desc' => JText::_('背景图片'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'icon1' => array(
                            'type' => 'media',
                            'title' => JText::_('图标1'),
                            'desc' => JText::_('图标1'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'icon2' => array(
                            'type' => 'media',
                            'title' => JText::_('图标2'),
                            'desc' => JText::_('图标2'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'icon3' => array(
                            'type' => 'media',
                            'title' => JText::_('图标3'),
                            'desc' => JText::_('图标3'),
                            'std' => ''
                        ),
                        'title_big' => array(
                            'type' => 'text',
                            'title' => JText::_('大标题'),
                            'desc' => JText::_('大标题'),
                            'std' => ''
                        ),
                        'content_big' => array(
                            'type' => 'text',
                            'title' => JText::_('大内容'),
                            'desc' => JText::_('大内容'),
                            'std' => ''
                        ),
                        'desc_big' => array(
                            'type' => 'text',
                            'title' => JText::_('大简介'),
                            'desc' => JText::_('大简介'),
                            'std' => ''
                        ),
                        'title_sm1' => array(
                            'type' => 'text',
                            'title' => JText::_('小标题1'),
                            'desc' => JText::_('小标题1'),
                            'std' => ''
                        ),
                        'content_sm1' => array(
                            'type' => 'text',
                            'title' => JText::_('小内容1'),
                            'desc' => JText::_('小内容1'),
                            'std' => ''
                        ),
                        'desc_sm1' => array(
                            'type' => 'text',
                            'title' => JText::_('小简介1'),
                            'desc' => JText::_('小简介1'),
                            'std' => ''
                        ),
                        'title_sm2' => array(
                            'type' => 'text',
                            'title' => JText::_('小标题2'),
                            'desc' => JText::_('小标题2'),
                            'std' => ''
                        ),
                        'content_sm2' => array(
                            'type' => 'text',
                            'title' => JText::_('小内容2'),
                            'desc' => JText::_('小内容2'),
                            'std' => ''
                        ),
                        'desc_sm2' => array(
                            'type' => 'text',
                            'title' => JText::_('小简介2'),
                            'desc' => JText::_('小简介2'),
                            'std' => ''
                        ),
                        'title_sm3' => array(
                            'type' => 'text',
                            'title' => JText::_('小标题3'),
                            'desc' => JText::_('小标题3'),
                            'std' => ''
                        ),
                        'content_sm3' => array(
                            'type' => 'text',
                            'title' => JText::_('小内容3'),
                            'desc' => JText::_('小内容3'),
                            'std' => ''
                        ),
                        'desc_sm3' => array(
                            'type' => 'text',
                            'title' => JText::_('小简介3'),
                            'desc' => JText::_('小简介3'),
                            'std' => ''
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '!=', 'type02'),
                        array('section_type', '!=', 'type03'),
                        array('section_type', '!=', 'type04'),
                        array('section_type', '!=', 'type05'),
                        array('section_type', '!=', 'type06'),
                        array('section_type', '!=', 'type07'),
                        array('section_type', '!=', 'type08'),
                        array('section_type', '!=', 'type09'),
                        array('section_type', '!=', 'type10'),
                        array('section_type', '!=', 'type11'),
                        array('section_type', '!=', 'type12'),
                        array('section_type', '!=', 'type13'),

                    ),
                ),
                //布局02选项组
                'type02_jw_tab_item' => array(
                    'title' => JText::_('选项组'),
                    'std' => array(
                        array(
                            'title' => '早教幼教',
                            's_title' => 'EARLY CHILDHOOD EDUCATION',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210804/9be65071947b5eec2f914a903f5d42c5.jpg',
                            'more_icon' => 'https://oss.lcweb01.cn/joomla/20210804/ab448aa3fadb891b1265bfbf255010c8.png',
                            'more_icon_hover' => 'https://oss.lcweb01.cn/joomla/20210804/ca5ce90ee5571bca4cebcebffa04f871.png',
                            //                            'tab_bg' => 'rgba(0, 0, 0, 0)',
                            //                            'tab_bg_hover' => '#1650fd',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20210804/47b9de4900d5cafb4e7fe70bbdaeed1d.jpg',
                        ),
                        array(
                            'title' => '早教幼教',
                            's_title' => 'EARLY CHILDHOOD EDUCATION',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210804/9be65071947b5eec2f914a903f5d42c5.jpg',
                            'more_icon' => 'https://oss.lcweb01.cn/joomla/20210804/ab448aa3fadb891b1265bfbf255010c8.png',
                            'more_icon_hover' => 'https://oss.lcweb01.cn/joomla/20210804/ca5ce90ee5571bca4cebcebffa04f871.png',
                            //                            'tab_bg' => 'rgba(0, 0, 0, 0)',
                            //                            'tab_bg_hover' => '#1650fd',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20210804/47b9de4900d5cafb4e7fe70bbdaeed1d.jpg',
                        ),
                        array(
                            'title' => '早教幼教',
                            's_title' => 'EARLY CHILDHOOD EDUCATION',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210804/9be65071947b5eec2f914a903f5d42c5.jpg',
                            'more_icon' => 'https://oss.lcweb01.cn/joomla/20210804/ab448aa3fadb891b1265bfbf255010c8.png',
                            'more_icon_hover' => 'https://oss.lcweb01.cn/joomla/20210804/ca5ce90ee5571bca4cebcebffa04f871.png',
                            //                            'tab_bg' => 'rgba(0, 0, 0, 0)',
                            //                            'tab_bg_hover' => '#1650fd',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20210804/47b9de4900d5cafb4e7fe70bbdaeed1d.jpg',
                        ),
                        array(
                            'title' => '早教幼教',
                            's_title' => 'EARLY CHILDHOOD EDUCATION',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210804/9be65071947b5eec2f914a903f5d42c5.jpg',
                            'more_icon' => 'https://oss.lcweb01.cn/joomla/20210804/ab448aa3fadb891b1265bfbf255010c8.png',
                            'more_icon_hover' => 'https://oss.lcweb01.cn/joomla/20210804/ca5ce90ee5571bca4cebcebffa04f871.png',
                            //                            'tab_bg' => 'rgba(0, 0, 0, 0)',
                            //                            'tab_bg_hover' => '#1650fd',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20210804/47b9de4900d5cafb4e7fe70bbdaeed1d.jpg',
                        ),
                        array(
                            'title' => '早教幼教',
                            's_title' => 'EARLY CHILDHOOD EDUCATION',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210804/9be65071947b5eec2f914a903f5d42c5.jpg',
                            'more_icon' => 'https://oss.lcweb01.cn/joomla/20210804/ab448aa3fadb891b1265bfbf255010c8.png',
                            'more_icon_hover' => 'https://oss.lcweb01.cn/joomla/20210804/ca5ce90ee5571bca4cebcebffa04f871.png',
                            //                            'tab_bg' => 'rgba(0, 0, 0, 0)',
                            //                            'tab_bg_hover' => '#1650fd',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20210804/47b9de4900d5cafb4e7fe70bbdaeed1d.jpg',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('选项的名称'),
                            'std' => '',
                        ),
                        's_title' => array(
                            'type' => 'text',
                            'title' => JText::_('小标题'),
                            'std' => '',
                        ),
                        'bg' => array(
                            'type' => 'media',
                            'title' => JText::_('背景'),
                            'desc' => JText::_('背景图片'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'more_icon' => array(
                            'type' => 'media',
                            'title' => JText::_('未选中选项卡右侧图标'),
                            'desc' => JText::_('未选中选项卡右侧图标'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'more_icon_hover' => array(
                            'type' => 'media',
                            'title' => JText::_('选中选项卡右侧图标'),
                            'desc' => JText::_('选中选项卡右侧图标'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        //                        'tab_bg' => array(
                        //                            'type' => 'color',
                        //                            'title' => '未选中选项卡背景',
                        //                            'std' => 'rgba(0, 0, 0, 0)',
                        //                        ),
                        //                        'tab_bg_hover' => array(
                        //                            'type' => 'color',
                        //                            'title' => '选中选项卡背景',
                        //                            'std' => '#1650fd',
                        //                        ),
                        'content_img' => array(
                            'type' => 'media',
                            'title' => JText::_('右侧图片'),
                            'desc' => JText::_('右侧图片'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('开启点击跳转对应产品页面'),
                            'desc' => '该功能只支持跳转到页面包含【三级产品分类选项卡】的对应产品',
                            'std' => '0',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面（需包含【三级产品分类选项卡】）',
                            //                            'desc' => '显示文章详情页模版',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('is_link', '=', 1),
                            ),
                        ),
                        'goods_catid' => array(
                            'type' => 'select',
                            'title' => JText::_('选择产品分类'),
                            'desc' => '对应【三级产品分类选项卡】产品分类',
                            'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
                            'depends' => array(
                                array('is_link', '=', 1),
                            ),
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type02'),
                    ),
                ),
                'type02_section_height' => array(
                    'type' => 'slider',
                    'title' => '整个版块的高度',
                    'max' => 1200,
                    'min' => 0,
                    'std' => 946,
                    'depends' => array(
                        array('section_type', '=', 'type02'),
                    ),
                ),
                //布局03选项组
                'jw_tab_item03' => array(
                    'title' => JText::_('选项组'),
                    'std' => array(
                        array(
                            'title' => 'PC独立商城端',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/8a67453bf12e20f264293a081a7148ba.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/98cad71ff9f517daac0478830277e1d9.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/8553e801c9af12c5cc1ef8f426c48708.png',
                            'title_big' => '资海e店PC独立商城',
                            'content_big' => '商品展示全面，营销内容丰富，完善用户体验！商品展示清晰详细，Banner展示灵活多样。独立后台管理，全方位掌握所有终端数据。品牌传播载体，提升消费者认知。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                        array(
                            'title' => '微信小程序端',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/980131f83b1ab28952b64f4411f686b7.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/cdc9620f2a98b5b5c11f52098a59f0f3.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/4b22734a35b710596e243e61ec32f96c.png',
                            'title_big' => '资海e店微信小程序端',
                            'content_big' => '无需关注，无需下载，一键使用，“触手可及”。下单、支付，消费更便捷。成本低，上线快，易管理。附近搜索、线下扫码、公众号导入、获取“线上客流”更轻松。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                        array(
                            'title' => 'APP商城IOS端',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/c6b0283faf4a89c455879f7513eef521.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/f22424f92df6f9d6190c96fd29168274.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/c940d8d436546deaa9ceba0dfccc4dd2.png',
                            'title_big' => 'APP商城IOS端',
                            'content_big' => '支持iOS各应用商店均可下载。用户随时随地可消费，实现“移动购物”。订单信息及时掌握，实现“移动经营” 。独立后台管理，全方位掌握所有终端数据。 可与PC端同步，无缝对接。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                        array(
                            'title' => '手机触屏商城端',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/bd77ae0fd69b86fda99faddf7df270e2.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/465965e4573a50a3283425241bc6a767.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/9e8f12d50a79de7a4722f48f1204a9a4.png',
                            'title_big' => '手机触屏商城端',
                            'content_big' => '微信超10亿活跃用户，覆盖面广、使用率高、传播有效性高。随时随地微信下单、支付，消费更便捷。成本低，上线快，易管理。用户随时随地可消费，实现“移动购物”。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                        array(
                            'title' => '商家后台',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/7c2b179f3f89fe49dd28c0ae54eee8ca.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/729c9443bfe09ca484ae66937e0ffbc8.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/f8dedaaa87ce7aaef81dbbc771286813.png',
                            'title_big' => '商家后台',
                            'content_big' => '集成多个系统管理，保证数据准确无误。为经营提供有效依据，解决运营难题。会员体系完善，会员资料、等级、折扣、等统一管理。在线申请开票，信息直达财务管理端，提高财务效率，降低管理成本。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                        array(
                            'title' => 'APP商城安卓端',
                            'bg' => 'https://oss.lcweb01.cn/joomla/20210812/b848d9135f9d1c0638a316a4b0d49c31.png',
                            'icon1' => 'https://oss.lcweb01.cn/joomla/20210812/765e5417e45b87fc4367abf7ee68afc9.png',
                            'icon2' => 'https://oss.lcweb01.cn/joomla/20210812/8efe3710c30783c0d7aa4a2d1602a99e.png',
                            'title_big' => 'APP商城安卓端',
                            'content_big' => '支持安卓系统，各应用商店均可下载。用户随时随地可消费，实现“移动购物”。订单信息及时掌握，实现“移动经营” 。独立后台管理，全方位掌握所有终端数据。 可与PC端同步，无缝对接。',
                            'desc_big' => '立即免费体验',
                            'title_sm1' => '',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('选项的名称'),
                            'std' => '',
                        ),
                        'bg' => array(
                            'type' => 'media',
                            'title' => JText::_('内容右侧图'),
                            'desc' => JText::_('内容右侧图片'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'icon1' => array(
                            'type' => 'media',
                            'title' => JText::_('左侧图标'),
                            'desc' => JText::_('选项卡左侧小图标'),
                            'format' => 'image',
                            'std' => ''
                        ),
                        'icon2' => array(
                            'type' => 'media',
                            'title' => JText::_('划过图标'),
                            'desc' => JText::_('切换选项卡时变换的图标'),
                            'format' => 'image',
                            'std' => ''
                        ),

                        'title_big' => array(
                            'type' => 'text',
                            'title' => JText::_('内容标题'),
                            'desc' => JText::_('内容标题'),
                            'std' => ''
                        ),
                        'content_big' => array(
                            'type' => 'text',
                            'title' => JText::_('内容文本'),
                            'desc' => JText::_('内容文本'),
                            'std' => ''
                        ),
                        'desc_big' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮文本'),
                            'desc' => JText::_('按钮文本'),
                            'std' => ''
                        ),
                        'title_sm1' => array(
                            'type' => 'text',
                            'title' => JText::_('按钮链接'),
                            'desc' => JText::_('填写点击按钮的跳转链接'),
                            'std' => ''
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type03'),
                    ),
                ),
                'type03_bgcolog' => array(
                    'type' => 'color',
                    'title' => 'tab划过背景色',
                    'std' => '#fd701b',
                    'depends' => array(
                        array('section_type', '=', 'type03'),
                    ),
                ),
                //布局04选项组
                'jw_tab_item04' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '速生性强',
                            'title_s' => '年酒盅、三年碗口',
                            'content' => '<em>定植1周年，胸径4-6厘米；</em> <em>定植2周年，胸径8-10厘米；</em> <em>定植3周年，胸径11-13厘米；</em> <em>定植4周年，胸径15-17厘米；</em><em>定植5周年，胸径18-20厘米；</em>',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220226/31dcd2fba58b5b54c077cbd9608cd500.png',
                        ),
                        array(
                            'title' => '养护省心',
                            'title_s' => '病虫害少，成本低',
                            'content' => '<em>泓森槐不易受杂草影响，且病虫害少、耐旱，耐贫瘠、养护成本低于其他速生品种30%</em>',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220226/0fcf53086387595fff8067071904f5c7.png',
                        ),
                        array(
                            'title' => '适生区广',
                            'title_s' => '更具市场推广潜力',
                            'content' => '<em>泓森槐抗性强、防风固沙，适应南北大跨度气候区域，除海南和黑龙江外，皆可种植。</em>',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220226/a9eaa89c71873193f557083daa0e4308.png',
                        ),
                        array(
                            'title' => '收益可观',
                            'title_s' => '经济价值、附加值高',
                            'content' => '<em>泓森槐8年即可成材，槐木价格近5年在1200-1600元/m³。</em> <em>槐花药食两用；槐花蜜属上等蜜；茎叶可以加工成优质蛋白饲料。</em>',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220226/4a76fc911fbbf34c9df3c6bf5d7ddff8.png',
                        ),
                        array(
                            'title' => '生态修复',
                            'title_s' => '净化空气 改良土壤',
                            'content' => '<em>泓森槐叶片可分泌粘膜，能有效吸收空气中的二氧化硫、粉尘等；<br>泓森槐为豆科植物，具根瘤菌，能固氮，落叶可肥土，提高土壤肥力；且耐瘠薄、耐干旱、耐盐碱，可广泛应用于土壤改良、矿山修复、荒山造林、行道绿化等领域。</em>',
                            'img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'icon' => 'https://oss.lcweb01.cn/joomla/20220226/5e333e8f259cf721be21b7d538c3aca0.png',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '速生性强',
                        ),
                        'title_s' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '年酒盅、三年碗口',
                        ),
                        'content' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std' => '<em>定植1周年，胸径4-6厘米；</em> <em>定植2周年，胸径8-10厘米；</em> <em>定植3周年，胸径11-13厘米；</em> <em>定植4周年，胸径15-17厘米；</em><em>定植5周年，胸径18-20厘米；</em>',
                        ),
                        'img' => array(
                            'type' => 'media',
                            'title' => '内容右侧图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg'
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => '标题图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220226/31dcd2fba58b5b54c077cbd9608cd500.png'
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type04'),
                    ),
                ),
                'type04_bg_color' => array(
                    'type' => 'color',
                    'title' => '背景颜色',
                    'std' => '#FFF',
                    'depends' => array(
                        array('section_type', '=', 'type04'),
                    ),
                ),
                'type04_border_color' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('section_type', '=', 'type04'),
                    ),
                ),
                'type04_ul_align' => array(
                    'type' => 'select',
                    'title' => '底部选项卡对齐方式',
                    'values' => array(
                        'flex-start' => '左对齐',
                        'center' => '居中对齐',
                        'flex-end' => '右对齐',
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('section_type', '=', 'type04'),
                    ),
                ),
                'type04_img_width' => array(
                    'type' => 'slider',
                    'title' => '图片宽度',
                    'min' => 0,
                    'max' => 1000,
                    'std' => 560,
                    'depends' => array(
                        array('section_type', '=', 'type04'),
                    )
                ),
                // 布局05
                'tab05_title' => array(
                    'type' => 'text',
                    'title' => '栏目标题（细体）',
                    'std' => '龙采体育',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_title_s' => array(
                    'type' => 'text',
                    'title' => '栏目标题（粗体）',
                    'std' => '生态',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_desc' => array(
                    'type' => 'text',
                    'title' => '栏目简介（细体）',
                    'std' => 'LONGCAI SPORTS',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_desc_s' => array(
                    'type' => 'text',
                    'title' => '栏目简介（粗体）',
                    'std' => 'ECOLOGY',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'jw_tab_item05' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '管家',
                            'title_s' => '场馆生态',
                            'mg_t' => '0',
                        ),
                        array(
                            'title' => '海健身',
                            'title_s' => '爱好者生态',
                            'mg_t' => '0',
                        ),
                        array(
                            'title' => '考试认证<br/>评价系统',
                            'title_s' => '从业者生态',
                            'mg_t' => '18',
                        ),
                        array(
                            'title' => '赛事评分<br/>管理系统',
                            'title_s' => '明星生态',
                            'mg_t' => '18',
                        ),
                        array(
                            'title' => '体育健康商城<br/>供应链系统',
                            'title_s' => '厂商生态',
                            'mg_t' => '18',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '管家',
                        ),
                        'title_s' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '场馆生态',
                        ),
                        'mg_t' => array(
                            'type' => 'slider',
                            'title' => '标题上偏移（pc）',
                            'std' => '0',
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_part01' => array(
                    'type' => 'separator',
                    'title' => '左侧内容样式配置',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_left_bg_style' => array(
                    'type' => 'buttons',
                    'title' => '左侧文字背景选项（平板及手机）',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '颜色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_left_bgColor' => array(
                    'type' => 'color',
                    'title' => '左侧文字背景颜色',
                    'std' => '#D0142E',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                        array('tab05_left_bg_style', '=', 'color')
                    )
                ),
                'tab05_left_bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '左侧文字渐变色背景',
                    'std' => array(
                        "color" => "#D0142E",
                        "color2" => "#FF6A34",
                        "deg" => "90",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                        array('tab05_left_bg_style', '=', 'gradient')
                    ),
                ),
                'tab05_title_f' => array(
                    'type' => 'slider',
                    'title' => '栏目标题文字大小',
                    'responsive' => true,
                    'std' => array(
                        'md' => '32',
                        'sm' => '26',
                        'xs' => '20',
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_title_color' => array(
                    'type' => 'color',
                    'title' => '栏目标题文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_desc_f' => array(
                    'type' => 'slider',
                    'title' => '栏目简介文字大小',
                    'responsive' => true,
                    'std' => array(
                        'md' => '26',
                        'sm' => '18',
                        'xs' => '16',
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_desc_color' => array(
                    'type' => 'color',
                    'title' => '栏目简介文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_part02' => array(
                    'type' => 'separator',
                    'title' => '右侧内容样式配置',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_right_bgColor' => array(
                    'type' => 'color',
                    'title' => '右侧选项组背景颜色',
                    'std' => '#292c2e',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_item_title_f' => array(
                    'type' => 'slider',
                    'title' => '选项组标题文字大小',
                    'responsive' => true,
                    'std' => array(
                        'md' => '22',
                        'sm' => '18',
                        'xs' => '16',
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_item_title_color' => array(
                    'type' => 'color',
                    'title' => '选项组标题文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_item_desc_f' => array(
                    'type' => 'slider',
                    'title' => '选项组简介文字大小',
                    'responsive' => true,
                    'std' => array(
                        'md' => '16',
                        'sm' => '14',
                        'xs' => '12',
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                'tab05_item_desc_color' => array(
                    'type' => 'color',
                    'title' => '选项组简介文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_item_desc_bgColor' => array(
                    'type' => 'color',
                    'title' => '选项组简介背景颜色',
                    'std' => '#414345',
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    )
                ),
                'tab05_item_desc_w' => array(
                    'type' => 'slider',
                    'title' => '选项组简介宽度',
                    'responsive' => true,
                    'std' => array(
                        'md' => '120',
                        'sm' => '',
                        'xs' => '',
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type05'),
                    ),
                ),
                //布局06选项组
                'jw_tab_item06' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '馆家',
                            'desc' => '馆家是龙采体育集团推出的场馆智能化经…',
                            'desc_hover' => '馆家是龙采体育集团推出的场馆智能化经营管理解决方案，以服务场馆为核心，深入理……',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220412/017bd205fe2ee35711eb89ea8c0a64e6.png',
                            'bg_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/922a978b479109d635f2f1a54ca8ace1.png',
                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220412/923355bc7095d9d60f108f15a3539309.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/4c615143f5d3a981f23a925ee966c4ed.png',
                        ),
                        array(
                            'title' => '海健身',
                            'desc' => '海健身app是一个为体育爱好者提供专业…',
                            'desc_hover' => '海健身app是一个为体育爱好者提供专业健身指导、考试认证、场馆服务的学习交流分…',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220412/c0be7dbe1fb6d755c3b8d365d8892de4.png',
                            'bg_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/c09a70184c3538705a46400e622a47b0.png',
                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220412/bc1ba7418d4d1cfaf65fb4fc17730fe7.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/a7800bb7ea39817cbbadc7636dafe5b8.png',
                        ),
                        array(
                            'title' => '智能终端',
                            'desc' => '“智镜”是龙采体育集团推出的一款集智能…',
                            'desc_hover' => '“智镜”是龙采体育集团推出的一款集智能硬件、课程内容、人工智能、衍生服务于一体…',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220412/a4c7542e79f0c93664f1c78be537dcd8.png',
                            'bg_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/6be8c79dfdb7167b36808e1ce8cdbb15.png',
                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220412/7ac9aac48ec39f954f9cd5c8a1a2adf5.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/63189f437f1541a81369612d6c796e2a.png',
                        ),
                        array(
                            'title' => '线上商城',
                            'desc' => '为满足用户对周边商品的需求，提升价值…',
                            'desc_hover' => '通过创新平台合作机制，将供应链平台赋能场馆，实现流通扁平化，提升场馆增收能…',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220412/ca9158be756959a6b033d76d5b3ae3e3.png',
                            'bg_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/43e8dbde0486b562a1bca27fe7c8e57e.png',
                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220412/b36e469a79cc21fd1ced90ca2cc4d186.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/dab93ef23aa2a73876552fb2d6b5f9bc.png',
                        ),
                        array(
                            'title' => '内容出版',
                            'desc' => '北京福恩文化传播有限公司是由龙采体育…',
                            'desc_hover' => '公司已开发《CBBA专业健身教练培训教程》、《CBBA东方舞级位培训教程》…',
                            'bg_img' => 'https://oss.lcweb01.cn/joomla/20220412/e35049b43a44da5f16f58f05a3a8573f.png',
                            'bg_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/96ef895708dd6e5e1aa4e33822751a32.png',
                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220412/651b2604ffd1a5e4350fdac799513c45.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220412/3b6432e0068c5eccb2812b80c826f82f.png',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '馆家',
                        ),
                        'desc' => array(
                            'type' => 'text',
                            'title' => '正常状态描述文字',
                            'std' => '馆家是龙采体育集团推出的场馆智能化经…',
                        ),
                        'desc_hover' => array(
                            'type' => 'text',
                            'title' => '鼠标移入描述文字',
                            'std' => '馆家是龙采体育集团推出的场馆智能化经营管理解决方案，以服务场馆为核心，深入理……',
                        ),
                        'bg_img' => array(
                            'type' => 'media',
                            'title' => '正常状态背景',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220412/017bd205fe2ee35711eb89ea8c0a64e6.png'
                        ),
                        'bg_img_hover' => array(
                            'type' => 'media',
                            'title' => '鼠标移入背景',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220412/c09a70184c3538705a46400e622a47b0.png'
                        ),
                        'icon_img' => array(
                            'type' => 'media',
                            'title' => '正常状态小图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220412/923355bc7095d9d60f108f15a3539309.png'
                        ),
                        'icon_img_hover' => array(
                            'type' => 'media',
                            'title' => '鼠标移入小图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220412/4c615143f5d3a981f23a925ee966c4ed.png'
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type06'),
                    ),
                ),
                'tab06_item_bg_w' => array(
                    'type' => 'slider',
                    'title' => '正常状态背景图宽度',
                    'std' => '182',
                    'depends' => array(
                        array('section_type', '=', 'type06'),
                    ),
                ),
                'tab06_item_bg_h' => array(
                    'type' => 'slider',
                    'title' => '正常状态背景图高度',
                    'std' => '248',
                    'depends' => array(
                        array('section_type', '=', 'type06'),
                    ),
                ),
                'tab06_item_bg_w_hover' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入背景图宽度(需大于或等于正常状态)',
                    'std' => '204',
                    'depends' => array(
                        array('section_type', '=', 'type06'),
                    ),
                ),
                'tab06_item_bg_h_hover' => array(
                    'type' => 'slider',
                    'title' => '鼠标移入背景图高度(需大于或等于正常状态)',
                    'std' => '328',
                    'depends' => array(
                        array('section_type', '=', 'type06'),
                    ),
                ),
                // 2022.4.14 布局07
                'section_title_red7' => array(
                    'type' => 'text',
                    'title' => '版块中文名称(加粗)',
                    'std' => '项目',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'section_title_ct7' => array(
                    'type' => 'text',
                    'title' => '版块中文名称',
                    'std' => '介绍',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'section_title_eng7' => array(
                    'type' => 'text',
                    'title' => '版块英文名称(加粗)',
                    'std' => 'PROJECT',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'section_title_engct7' => array(
                    'type' => 'text',
                    'title' => '版块英文名称',
                    'std' => 'INITRODUCTION',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'section_active_color7' => array(
                    'type' => 'color',
                    'title' => '主题色',
                    'std' => ' #DB0000',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'section_height7' => array(
                    'type' => 'slider',
                    'title' => '版块高度',
                    'std' => '380',
                    'max' => 800,
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'sessin7_jt' => array(
                    'type' => 'media',
                    'title' => '了解更多箭头图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220413/a8f51d550caab5b94e8167d833dbf5b1.png',
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                'jw_tab_item07' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '健美协会',
                            'desc' => '2016年，龙采体育集团旗下北京西美文化交流有限公司与中国健美协会签约成为战略合作伙伴，先后在全国推广专业健身健美、东方舞运动，研发培训管理系统，为培训机构和行业人群提供便捷服务。',
                            'left_img' => 'https://oss.lcweb01.cn/joomla/20220413/93c70d9f4741d9a507decabda8a9032e.png',
                            'small_title1' => '健身教练',
                            'small_intro1' => '初、中、高、国家级认证证书',
                            'small_title2' => '东方舞',
                            'small_intro2' => '少儿、成人等级认证证书',
                            'small_title3' => '东方舞',
                            'small_intro3' => '少儿、成人教练员认证证书',
                            'right_img1' => 'https://oss.lcweb01.cn/joomla/20220413/085c190b9e2e7c131d88ab5b27594e1f.png',
                            'right_img2' => 'https://oss.lcweb01.cn/joomla/20220413/1cd1560f94849a290ce4a8a111996085.png',
                            'right_img3' => 'https://oss.lcweb01.cn/joomla/20220413/f3bd8a790864f2fa98dd3866c44a6b60.png',

                        ),
                        array(
                            'title' => '健身瑜伽',
                            'desc' => '2019年，龙采体育集团与全国健身瑜伽指导委员会签约成为战略合作伙伴，结合我国国情与大众健身需求，出台全国瑜伽裁判员管理办法及全国瑜伽导师资格标准，并积极落实体育总局社体中心发布的…',
                            'left_img' => 'https://oss.lcweb01.cn/joomla/20220413/ec10204d7aed40900589d95c79f52d78.png',
                            'small_title1' => '爱好者',
                            'small_intro1' => '段前级-九段段位认证证书',
                            'small_title2' => '晋段官',
                            'small_intro2' => ' 初、中、高级认证证书',
                            'small_title3' => '教练员',
                            'small_intro3' => '初、中、高、国家级认证证书',
                            'right_img1' => 'https://oss.lcweb01.cn/joomla/20220413/047f27d84dbceaa27f8b6e5c0c57ffcc.png',
                            'right_img2' => 'https://oss.lcweb01.cn/joomla/20220413/aab7c01b0fdffed46e9fe35bf1d133e9.png',
                            'right_img3' => '',

                        ),
                        array(
                            'title' => '健身气功',
                            'desc' => '2016年，龙采体育集团旗下北京福恩文化传播有限公司与国际健身气功联合会签约成为战略合作伙伴，推广健身气功运动，为全球80多个国家和地区的1600万健身气功爱好者搭建培训、考试、认证服务…',
                            'left_img' => 'https://oss.lcweb01.cn/joomla/20220413/7bae09fc7c2f14f8b22da4968cfc0272.png',
                            'small_title1' => '培训',
                            'small_intro1' => '龙采体育集团通过与国际健身气功联合会的合作，每天向全球80多个国家和地区，累计超过100万健身气功爱好者传播健身气功知识、讲座、演练…',
                            'small_title2' => '考试',
                            'small_intro2' => ' ',
                            'small_title3' => '认证服务',
                            'small_intro3' => '',
                            'right_img1' => 'https://oss.lcweb01.cn/joomla/20220413/a3201ee2af7b5905bc459a7448967761.png',
                            'right_img2' => '',
                            'right_img3' => '',
                        ),

                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '健美协会',
                        ),
                        'desc' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'std' => '2016年，龙采体育集团旗下北京西美文化交流有限公司与中国健美协会签约成为战略合作伙伴，先后在全国推广专业健身健美、东方舞运动，研发培训管理系统，为培训机构和行业人群提供便捷服务。',
                        ),
                        'left_img' => array(
                            'type' => 'media',
                            'title' => '左侧logo',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220413/93c70d9f4741d9a507decabda8a9032e.png'
                        ),

                        'small_title1' => array(
                            'type' => 'text',
                            'title' => '小标题1',
                            'std' => '健身教练',
                        ),
                        'small_intro1' => array(
                            'type' => 'text',
                            'title' => '小简介1',
                            'std' => '初、中、高、国家级认证证书',
                        ),
                        'small_title2' => array(
                            'type' => 'text',
                            'title' => '小标题2',
                            'std' => '东方舞',
                        ),
                        'small_intro2' => array(
                            'type' => 'text',
                            'title' => '小简介2',
                            'std' => '少儿、成人等级认证证书',
                        ),
                        'small_title3' => array(
                            'type' => 'text',
                            'title' => '小标题3',
                            'std' => '东方舞',
                        ),
                        'small_intro3' => array(
                            'type' => 'text',
                            'title' => '小简介3',
                            'std' => '少儿、成人教练员认证证书',
                        ),

                        'right_img1' => array(
                            'type' => 'media',
                            'title' => '右侧图片1',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220413/085c190b9e2e7c131d88ab5b27594e1f.png'
                        ),
                        'right_img2' => array(
                            'type' => 'media',
                            'title' => '右侧图片2',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220413/1cd1560f94849a290ce4a8a111996085.png'
                        ),
                        'right_img3' => array(
                            'type' => 'media',
                            'title' => '右侧图片3',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220413/f3bd8a790864f2fa98dd3866c44a6b60.png'
                        ),

                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type07'),
                    ),
                ),
                //布局08选项组
                'jw_tab_item08' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => 'O',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg',
                        ),
                        array(
                            'title' => 'U',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/2e887954fd8678e49e3afd0b7b7a1efb.png',
                        ),
                        array(
                            'title' => 'R',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/1cc87c459c16332fcd4101808a03fdf8.jpg',
                        ),
                        array(
                            'title' => 'T',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/19e8b74f0bd85ec2a1a039715c2a556f.jpg',
                        ),
                        array(
                            'title' => 'E',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/6e77ac1e9481b67fd74a1cec8fcc3690.jpg',
                        ),
                        array(
                            'title' => 'A',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/0a3f67981b7b28400c5b22776d84a772.jpg',
                        ),
                        array(
                            'title' => 'M',
                            'hover_img' => 'https://oss.lcweb01.cn/joomla/20220512/f6ab3508a3e51751b8004cb2333de8e7.jpg',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '字母文字',
                            'std' => 'O',
                        ),
                        'hover_img' => array(
                            'type' => 'media',
                            'title' => '移入图片',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg'
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type08'),
                    ),
                ),
                'title_color_08' => array(
                    'type' => 'color',
                    'title' => '字母文字颜色',
                    'std' => ' #FFFFFF',
                    'depends' => array(
                        array('section_type', '=', 'type08'),
                    ),
                ),
                // 布局10
                'jw_tab_item10' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '黑龙江',
                            'content' => '好的产品质量是企业立足、发展的根本，也是企业建设的源动力。如果将龙采比喻为一辆性能优越的越野车，那技术团队就是马力十足的发动机。他们用丰富的经验和精湛的功力，一次次攻克技术的高峰!
                            　　龙采科技集团利用先进的互联网技术，打破原有的行业运营模式，将发展过程中积累的技术和业务经验转化和应用于信息化整合平台中，为企业提供现代化营销新平台。作为网站建设与网络推广行业专家，网络建设开发团队，集团不断引进大量技术人才，短短几年时间，从十几人的小团队，发展成为北京、黑龙江、辽宁、山西四个技术中心，五百余人的强大技术支持团队。为龙采品牌在互联网行业长期保持领先地位提供了坚实的基础。
                            ',
                            'img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/38f21fdc3b9692e114fbfa657410daa9.jpeg',
                            'img2' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/ab5b4f17809583d143f1070449fd6e51.jpeg',
                            'img_item10' => array(

                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/96ed05d6aed4d37a370dd352860a577c.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/1024d41b4251ad24c81a8387d6b8dd72.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/6c446598083db9f1c912ca184f5a261e.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a671441dfc492c88852d837c427997dd.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/57236d36a03cdd209b5b3626454c59b5.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/8aa9ed1e7a7ea7df99e82d6ba7f87e66.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/491eeb26584aae91179999d1a93b642b.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/96ed05d6aed4d37a370dd352860a577c.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/1024d41b4251ad24c81a8387d6b8dd72.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/6c446598083db9f1c912ca184f5a261e.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a671441dfc492c88852d837c427997dd.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/57236d36a03cdd209b5b3626454c59b5.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/8aa9ed1e7a7ea7df99e82d6ba7f87e66.jpeg',
                                ),
                            
                            ),
                        ),
                        array(
                            'title' => '大连',
                            'content' => '好的产品质量是企业立足、发展的根本，也是企业建设的源动力。如果将龙采比喻为一辆性能优越的越野车，那技术团队就是马力十足的发动机。他们用丰富的经验和精湛的功力，一次次攻克技术的高峰!
                            　　龙采科技集团利用先进的互联网技术，打破原有的行业运营模式，将发展过程中积累的技术和业务经验转化和应用于信息化整合平台中，为企业提供现代化营销新平台。作为网站建设与网络推广行业专家，网络建设开发团队，集团不断引进大量技术人才，短短几年时间，从十几人的小团队，发展成为北京、黑龙江、辽宁、山西四个技术中心，五百余人的强大技术支持团队。为龙采品牌在互联网行业长期保持领先地位提供了坚实的基础。
                            ',
                            'img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/02abf7e509883d7f472a8b7ae7b6dfa0.jpeg',
                            'img2' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/da7598f8e6cc9e927c6180f299ad9a8d.jpeg',
                            'img_item10' => array(

                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/ccc5d30c6ff49233b11356894e92e4a3.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/9f0702b0f224e48f9bcb50ae887cf894.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/93062e0b7f1dd8e35e8e6701d1c091ec.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/0296ed10e6ac02454849f33dfc23d8b3.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/327807a7e79f0a93ac0340b29771599f.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a8ddba175dcdf538fe1d7d4baef8145a.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/5bbfcc50b6cfc298230f37eb535c69c6.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/dbe35e5b6b23622256f6066cb350acbb.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/4c24815d861e2580ff601bb4ff5803c3.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/ccc5d30c6ff49233b11356894e92e4a3.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/9f0702b0f224e48f9bcb50ae887cf894.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/93062e0b7f1dd8e35e8e6701d1c091ec.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/0296ed10e6ac02454849f33dfc23d8b3.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/327807a7e79f0a93ac0340b29771599f.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a8ddba175dcdf538fe1d7d4baef8145a.jpeg',
                                ),
                            ),
                        ),
                        array(
                            'title' => '山西',
                            'content' => '好的产品质量是企业立足、发展的根本，也是企业建设的源动力。如果将龙采比喻为一辆性能优越的越野车，那技术团队就是马力十足的发动机。他们用丰富的经验和精湛的功力，一次次攻克技术的高峰!
                            　　龙采科技集团利用先进的互联网技术，打破原有的行业运营模式，将发展过程中积累的技术和业务经验转化和应用于信息化整合平台中，为企业提供现代化营销新平台。作为网站建设与网络推广行业专家，网络建设开发团队，集团不断引进大量技术人才，短短几年时间，从十几人的小团队，发展成为北京、黑龙江、辽宁、山西四个技术中心，五百余人的强大技术支持团队。为龙采品牌在互联网行业长期保持领先地位提供了坚实的基础。
                            ',
                            'img1' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/aca67edb99d3b526e132e4341327e99e.jpeg',
                            'img2' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/7978ac05503e8af82443e429fdf86054.jpeg',
                            'img_item10' => array(

                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/254d72b06b34eb20103ee6a431bf1a18.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a514571fc14e7d4b29c264a96ede6525.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/e88b6df710b4fd4b55f45254a42951c1.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/0426d48972a24de025c1d48e482f6c01.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/fd7dc0c3302d3d64df71efd918bc9871.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/002f12fac18b48ba84da993709fbcad7.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/dd4642ca013d0351dcd5e6975d846c4d.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/c2daad78f7e7bcbae967e8b264983b65.jpeg',
                                ),
                                array(
                                    'title' =>'营销团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/36596871c967725ab022cd69b3744f37.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/ff6a111b6d84949e7491aca1abdcb1e5.jpeg',
                                ),
                                array(
                                    'title' =>'营销团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/fdf9c3c482b03d215d7249abd145d19d.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/5b492b673386429050641c24b82832ee.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/60ef2045e1d667cda482789acab47444.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/ca42a6ac5663b3b03a35e68733792d0a.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/254d72b06b34eb20103ee6a431bf1a18.jpeg',
                                ),

                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a514571fc14e7d4b29c264a96ede6525.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/e88b6df710b4fd4b55f45254a42951c1.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/0426d48972a24de025c1d48e482f6c01.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/fd7dc0c3302d3d64df71efd918bc9871.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/002f12fac18b48ba84da993709fbcad7.jpeg',
                                ),
                                
                            ),
                        ),
                        
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '速生性强',
                        ),
                        'content' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std' => '好的产品质量是企业立足、发展的根本，也是企业建设的源动力。如果将龙采比喻为一辆性能优越的越野车，那技术团队就是马力十足的发动机。他们用丰富的经验和精湛的功力，一次次攻克技术的高峰!
                            　　龙采科技集团利用先进的互联网技术，打破原有的行业运营模式，将发展过程中积累的技术和业务经验转化和应用于信息化整合平台中，为企业提供现代化营销新平台。作为网站建设与网络推广行业专家，网络建设开发团队，集团不断引进大量技术人才，短短几年时间，从十几人的小团队，发展成为北京、黑龙江、辽宁、山西四个技术中心，五百余人的强大技术支持团队。为龙采品牌在互联网行业长期保持领先地位提供了坚实的基础。',
                        ),
                        'img1' => array(
                            'type' => 'media',
                            'title' => '内容右侧图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg'
                        ),
                        'img2' => array(
                            'type' => 'media',
                            'title' => '标题图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220226/31dcd2fba58b5b54c077cbd9608cd500.png'
                        ),
                        'img_item10' => array(
                            'title' => '轮播图片',
                            'std' => array(
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/96ed05d6aed4d37a370dd352860a577c.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/1024d41b4251ad24c81a8387d6b8dd72.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/6c446598083db9f1c912ca184f5a261e.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/a671441dfc492c88852d837c427997dd.jpeg',
                                ),
                                array(
                                    'title' =>'团队',
                                    'hover_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/57236d36a03cdd209b5b3626454c59b5.jpeg',
                                ),

                            ),
                            'attr' => array(
                                'title' => array(
                                    'type' => 'text',
                                    'title' => '图片标题',
                                    'std' => '团队',
                                ),
                                'hover_img' => array(
                                    'type' => 'media',
                                    'title' => '图片',
                                    'format' => 'image',
                                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/96ed05d6aed4d37a370dd352860a577c.jpeg'
                                ),
                            ),
                            
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type10'),
                    ),
                ),

                //布局11选项组
                'item11_content_bg' => array(
                    'type' => 'media',
                    'title' => '内容部分背景图片',
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220524/94c9d50f6991281b2af56794c34996d4.jpg',
                    'depends' => array(
                        array('section_type', '=', 'type11'),
                    ),
                ),
                'jw_tab_item11' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '录播课',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/a4f19dc4e5ec7ca3e73d2a04a1ec5637.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/3a0ae7d132ad604f80da2c0ad61578fe.png',
                            'desc' => '创建视频付费内容，学员购买后能随时在线收看。系统可跟踪学员学习进度、记录学员的完成量、观看时间。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/02b686fb2fc35fe546f63b3a443dc994.jpg',
                        ),
                        array(
                            'title' => '直播课',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/cdbbc0afe3d44b89166d91cf3f7def87.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/492cd9eab7f3a9bc8b5013c4e9b061ba.png',
                            'desc' => '视频直播支持实时视频，支持桌面共享，直播时教师和学员可以文字互动、直播提醒，课后快速生成回放视频。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/9d0a4ad5dbf7ce3cc1ee941be2e114b5.jpg',
                        ),
                        array(
                            'title' => '体验课',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/656e8e5ef7a7366a8a63c8ef50789981.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/dee10f8decbd0a1eee3d1dee14821b1c.png',
                            'desc' => '直播课 录播课均可设置免费体验课。用来做引流的招生方式。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/6add1904ba4287ed59aaada2e8bb1e47.jpg',
                        ),
                        array(
                            'title' => '音频课',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/691f647ffa6d6100d520e5ca3906eb13.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/ccb231cdf7bad39f85240aa0dfc93d64.png',
                            'desc' => '机构可以创建音频付费内容，学员购买后能随时在线收听。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/6a6dadf6c6c7a377975e4755bcad0352.png',
                        ),
                        array(
                            'title' => '考试系统',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/5e52cfa7a44a0b9df77326bd1e87b74b.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/7370cce2158bba3ea5f1fc8e3617e707.png',
                            'desc' => '检测用户学习效果的学习工具，学习效果显而易见。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/a25e679a8173661bd02754c8fd4743ba.jpg',
                        ),
                        array(
                            'title' => '营销中心',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/9172f376b55259f97680e5c5121a61e6.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/45e347f7f14ff8b2e40ea284e26a1cb9.png',
                            'desc' => '实现裂变传播的营销功能。用户自发形成刷屏式传播，促进销量提升同时可快速吸粉引流。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/4622ca74b37203c059fbd080b824b52e.jpg',
                        ),
                        array(
                            'title' => '教师管理',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/9ea1cb4e5001387d2b8e790707d056ba.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/5d46b384b9a8ee6ad27298ad14f452e2.png',
                            'desc' => '教师权限独立分配，教师管理更便捷。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/2554232e6cbe5ba65a1f28930bca56bc.jpg',
                        ),
                        array(
                            'title' => '数据统计',
                            'title_icon' => 'https://oss.lcweb01.cn/joomla/20220524/b06b35145263ad5e6c8ae8e9cee5d86b.png',
                            'title_icon_hover' => 'https://oss.lcweb01.cn/joomla/20220524/8173b95cfe09902e20e742a6c907c544.png',
                            'desc' => '学校后台可对、累计用户，今日访客，新增用户，订单管理，今日收益，课程销量排行榜等数据进行统计。',
                            'content_img' => 'https://oss.lcweb01.cn/joomla/20220524/a25e679a8173661bd02754c8fd4743ba.jpg',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '录播课',
                        ),
                        'title_icon' => array(
                            'type' => 'media',
                            'title' => '标题图片',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/a4f19dc4e5ec7ca3e73d2a04a1ec5637.jpg'
                        ),
                        'title_icon_hover' => array(
                            'type' => 'media',
                            'title' => '移入标题图片',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/3a0ae7d132ad604f80da2c0ad61578fe.jpg'
                        ),
                        'desc' => array(
                            'type' => 'text',
                            'title' => '内容简介',
                            'std' => '创建视频付费内容，学员购买后能随时在线收看。系统可跟踪学员学习进度、记录学员的完成量、观看时间。',
                        ),
                        'content_img' => array(
                            'type' => 'media',
                            'title' => '内容图片',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/02b686fb2fc35fe546f63b3a443dc994.jpg'
                        ),
                        // 选项卡点击跳转
                        'tab_click_jump' => array(
                            'type' => 'checkbox',
                            'title' => '开启选项卡点击跳转',
                            'std' => 0,
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                            'depends' => array(
                                array('tab_click_jump', '=', 1),
                            ),
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('tz_page_type', '=', 'Internal_pages'),
                                array('tab_click_jump', '=', 1),
                            ),
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(
                                array('tz_page_type', '=', 'external_links'),
                                array('tab_click_jump', '=', 1),
                            ),
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                            'depends' => array(
                                array('tab_click_jump', '=', 1),
                            ),
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type11'),
                    ),
                ),

                //布局12选项组
                'jw_tab_item12' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '馆家',
                            'tb' => 'https://oss.lcweb01.cn/joomla/20220803/02b0dcb6f4a6da598f4cf1799323a853.png',
                            'tb_act' => 'https://oss.lcweb01.cn/joomla/20220803/080c34b3bd05a4747b8901107dc46d79.png',
                            'cont_logo' => 'https://oss.lcweb01.cn/joomla/20220804/728e33506e2f25e20c48bb267c88d3c0.png',
                            'bgcolor_x' => '#ffa',
                            'cont_width' => '80',
                            
                            'cont_title' => '馆家标题',
                            'cont_jianjie' => '简介简介',
                            'cont_img' => 'https://oss.lcweb01.cn/joomla/20220804/d79c13c53c06553d0bacce3a10c6ed29.png',
                            'img_width' => '50',
                            'img_select' => 'flex-end',
                            'logo_width' => '40',
                            'cont_title_size' => '18',
                            'cont_title_color' => '#333',
                            'but_item12' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',
                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                ),
                            ),
                        ),
                        array(
                            'title' => '海健身',
                            'tb' => 'https://oss.lcweb01.cn/joomla/20220803/02b0dcb6f4a6da598f4cf1799323a853.png',
                            'tb_act' => 'https://oss.lcweb01.cn/joomla/20220803/080c34b3bd05a4747b8901107dc46d79.png',
                            'cont_logo' => 'https://oss.lcweb01.cn/joomla/20220804/728e33506e2f25e20c48bb267c88d3c0.png',
                            'bgcolor_x' => '#ffa',
                            'cont_width' => '80',

                            'cont_title' => '海健身标题',
                            'cont_jianjie' => '简介简介',
                            'cont_img' => 'https://oss.lcweb01.cn/joomla/20220804/d79c13c53c06553d0bacce3a10c6ed29.png',
                            'img_width' => '50',
                            'img_select' => 'flex-end',
                            'logo_width' => '40',
                            'cont_title_size' => '18',
                            'cont_title_color' => '#333',
                            'but_item12' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',
                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                ),
                            ),
                        ),
                        array(
                            'title' => '龙采智镜',
                            'tb' => 'https://oss.lcweb01.cn/joomla/20220803/02b0dcb6f4a6da598f4cf1799323a853.png',
                            'tb_act' => 'https://oss.lcweb01.cn/joomla/20220803/080c34b3bd05a4747b8901107dc46d79.png',
                            'cont_logo' => 'https://oss.lcweb01.cn/joomla/20220804/728e33506e2f25e20c48bb267c88d3c0.png',
                            'bgcolor_x' => '#ffa',
                            'cont_width' => '80',

                            'cont_title' => '龙采智镜标题',
                            'cont_jianjie' => '简介简介',
                            'cont_img' => 'https://oss.lcweb01.cn/joomla/20220804/d79c13c53c06553d0bacce3a10c6ed29.png',
                            'img_width' => '50',
                            'img_select' => 'flex-end',
                            'logo_width' => '40',
                            'cont_title_size' => '18',
                            'cont_title_color' => '#333',
                            'but_item12' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',
                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                ),
                            ),
                        ),
                        array(
                            'title' => '赛事管理系统',
                            'tb' => 'https://oss.lcweb01.cn/joomla/20220803/02b0dcb6f4a6da598f4cf1799323a853.png',
                            'tb_act' => 'https://oss.lcweb01.cn/joomla/20220803/080c34b3bd05a4747b8901107dc46d79.png',
                            'cont_logo' => 'https://oss.lcweb01.cn/joomla/20220804/728e33506e2f25e20c48bb267c88d3c0.png',
                            'bgcolor_x' => '#ffa',
                            'cont_width' => '80',

                            'cont_title' => '赛事管理系统标题',
                            'cont_jianjie' => '简介简介',
                            'cont_img' => 'https://oss.lcweb01.cn/joomla/20220804/d79c13c53c06553d0bacce3a10c6ed29.png',
                            'img_width' => '50',
                            'img_select' => 'flex-end',
                            'logo_width' => '40',
                            'cont_title_size' => '18',
                            'cont_title_color' => '#333',
                            'but_item12' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',
                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                ),
                            ),
                        ),
                        array(
                            'title' => '球友圈',
                            'tb' => 'https://oss.lcweb01.cn/joomla/20220803/02b0dcb6f4a6da598f4cf1799323a853.png',
                            'tb_act' => 'https://oss.lcweb01.cn/joomla/20220803/080c34b3bd05a4747b8901107dc46d79.png',
                            'cont_logo' => 'https://oss.lcweb01.cn/joomla/20220804/728e33506e2f25e20c48bb267c88d3c0.png',
                            'bgcolor_x' => '#ffa',
                            'cont_width' => '80',
                            'cont_title' => '球友圈标题',
                            'cont_jianjie' => '简介简介',
                            'cont_img' => 'https://oss.lcweb01.cn/joomla/20220804/d79c13c53c06553d0bacce3a10c6ed29.png',
                            'img_width' => '50',
                            'img_select' => 'flex-end',
                            'logo_width' => '40',
                            'cont_title_size' => '18',
                            'cont_title_color' => '#333',
                            'but_item12' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',

                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                ),
                            ),
                        ),
                        
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '模块名称',
                            'std' => '馆家',
                        ),
                        'tb' => array(
                            'type' => 'media',
                            'title' => '模块图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg'
                        ),
                        'tb_act' => array(
                            'type' => 'media',
                            'title' => '划过图标',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg'
                        ),
                        'cont_width' => array(
                            'type' => 'slider',
                            'title' => '内容区块宽度（%）',
                            'std' => '80',
                            'max' => '100'
                        ),
                        'cont_logo' => array(
                            'type' => 'media',
                            'title' => '内容logo',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg'
                        ),
                        'logo_width' => array(
                            'type' => 'slider',
                            'title' => 'logo宽度（%）',
                            'std' => '40',
                            'max' => '100'
                        ),
                        'bgcolor_x' => array(
                            'type' => 'color',
                            'title' => 'logo下横条颜色',
                            'std' => '#faa'
                        ),
                        'cont_title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '标题'
                        ),
                        'cont_title_size' => array(
                            'type' => 'slider',
                            'title' => '标题字体大小',
                            'std' => '18'
                        ),
                        'cont_title_color' => array(
                            'type' => 'color',
                            'title' => '标题字体颜色',
                            'std' => '#333'
                        ),
                        'cont_jianjie' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'std' => '简介简介'
                        ),
                        'cont_img' => array(
                            'type' => 'media',
                            'title' => '内容右侧图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220512/be326c6f667fc73f078133e347f5283c.jpg'
                        ),
                        'img_width' => array(
                            'type' => 'slider',
                            'title' => '图片宽度（%）',
                            'std' => '50'
                        ),
                        'img_select' => array(
                            'type' => 'select',
                            'title' => '图片位置',
                            'values' => array(
                                'flex-start' => '顶部',
                                'center' => '居中',
                                'flex-end' => '底部',
                            ),
                            'std' => 'flex-end',
                        ),

                        'but_item12' => array(
                            'title' => '按钮图片',
                            'std' => array(
                                array(
                                    'title' =>'按钮1',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/2e137ae7a8bec0a1505919a4a1a510a4.png',
                                    'annv_width' =>'120',
                                    'annv_top' =>'20',
                                    'annv_left' =>'0',
                                ),
                                array(
                                    'title' =>'按钮2',
                                    'but_img' => 'https://oss.lcweb01.cn/joomla/20220816/58e178e11b5b24476a2d441c5eb6b2be.png',
                                    'annv_width' =>'120',
                                    'annv_top' =>'20',
                                    'annv_left' =>'0',
                                ),
                            ),
                            'attr' => array(
                                'title' => array(
                                    'type' => 'text',
                                    'title' => '按钮标题',
                                    'std' => '按钮1',
                                ),
                                'but_img' => array(
                                    'type' => 'media',
                                    'title' => '按钮图片',
                                    'format' => 'image',
                                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220520/96ed05d6aed4d37a370dd352860a577c.jpeg'
                                ),
                                'annv_width' => array(
                                    'type' => 'slider',
                                    'title' => '按钮宽度',
                                    'std' => '120',
                                    'max' => '300',
                                ),
                                'annv_top' => array(
                                    'type' => 'slider',
                                    'title' => '按钮上间距',
                                    'std' => '20',
                                ),
                                'annv_left' => array(
                                    'type' => 'slider',
                                    'title' => '按钮左间距',
                                    'std' => '0',
                                ),


                                'button_xx' => array(
                                    'type' => 'select',
                                    'title' => JText::_('按钮触发方式'),
                                    'values' => array(
                                        'tzlj' => JText::_('跳转链接'),
                                        'hger' => JText::_('显示二维码'),
                                    ),
                                    'std' => 'tzlj',
                                ),
                                'erweim' => array(
                                    'type' => 'media',
                                    'title' => JText::_('二维码'),
                                    'std' => 'https://oss.lcweb01.cn/joomla/20220818/dbab5d8110af6fe79939930cbc6aac5b.png',
                                    'depends' => array(
                                        array('button_xx', '=', 'hger'),
                                    ),
                                ),
                                'erweim_bg' => array(
                                    'type' => 'media',
                                    'title' => JText::_('二维码背景图'),
                                    'std' => 'https://oss.lcweb01.cn/joomla/20220818/74ca67254c87c948346935fb67d22fc1.png',
                                    'depends' => array(
                                        array('button_xx', '=', 'hger'),
                                    ),
                                ),
                                'erweim_width' => array(
                                    'type' => 'slider',
                                    'title' => JText::_('背景图宽度'),
                                    'std' => '100',
                                    'max' => '200',
                                    'depends' => array(
                                        array('button_xx', '=', 'hger'),
                                    ),
                                ),
                                'erweim_top' => array(
                                    'type' => 'slider',
                                    'title' => JText::_('背景图上间距'),
                                    'std' => '30',
                                    'max' => '100',
                                    'depends' => array(
                                        array('button_xx', '=', 'hger'),
                                    ),
                                ),


                                'tz_page_type' => array(
                                    'type' => 'select',
                                    'title' => JText::_('跳转方式'),
                                    'desc' => JText::_('跳转方式'),
                                    'values' => array(
                                        'Internal_pages' => JText::_('内部页面'),
                                        'external_links' => JText::_('外部链接'),
                                    ),
                                    'std' => 'Internal_pages',
                                    'depends' => array(
                                        array('button_xx', '!=', 'hger'),
                                    ),
                                ),
                                'detail_page_id' => array(
                                    'type' => 'select',
                                    'title' => '选择跳转页面',
                                    'desc' => '',
                                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                                    'depends' => array(
                                        array('tz_page_type', '=', 'Internal_pages'),
                                        array('button_xx', '!=', 'hger'),
                                    ),
                                ),
                                'detail_page' => array(
                                    'type' => 'text',
                                    'title' => '跳转链接',
                                    'depends' => array(
                                        array('tz_page_type', '=', 'external_links'),
                                        array('button_xx', '!=', 'hger'),

                                    ),
                                ),
                                'target' => array(
                                    'type' => 'select',
                                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                                    'values' => array(
                                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                                    ),
                                    'depends' => array(
                                        array('button_xx', '!=', 'hger'),
                                    ),
                                ),
                            ),
                            
                        ),

                    ),
                    'depends' => array(
                        array('section_type', '=', 'type12'),
                    ),
                ),
                'item12_pcheight' => array(
                    'type' => 'slider',
                    'title' => 'pc端区域高',
                    'std' => '450',
                    'max' => '1000',
                    'depends' => array(
                        array('section_type', '=', 'type12'),
                    ),
                ),
                'item12_padding' => array(
                    'type' => 'padding',
                    'title' => '左侧选项上下距离',
                    'std' => '100px 0px 50px 0px',
                    'depends' => array(
                        array('section_type', '=', 'type12'),
                    ),
                ),
                'item12_leftbg' => array(
                    'type' => 'media',
                    'title' => '左侧背景图片',
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220804/bd2e94c295a76625aeef4bc16a65901b.png',
                    'depends' => array(
                        array('section_type', '=', 'type12'),
                    ),
                ),
                'item12_titactive' => array(
                    'type' => 'color',
                    'title' => '标题选中背景色',
                    'std' => '#f00',
                    'depends' => array(
                        array('section_type', '=', 'type12'),
                    ),
                ),


                //布局08选项组
                'jw_tab_item13' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => 'LED芯片',
                            'futitle' => '国星半导体',
                            'descrip' => '国星半导体为国星光电的控股子公司，致力于研发、生产可用于照明、显示、背光的氮化镓基 LED芯片。',
                            'typeimg' => 'https://oss.lcweb01.cn/joomla/20221223/7ce71976dc146dc37d00f2167ca86a56.png',
                            'tab_click_jump13' => '0',

                            'ctype_item13' => array(
                                array(
                                    'ctitle' => '显示屏',
                                    'tab_click_jump' => '0',
                                ),
                                array(
                                    'ctitle' => '倒装系列',
                                    'tab_click_jump' => '0',
                                ),
                            ),
               
                        ),
                        
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => 'LED芯片',
                        ),
                        'futitle' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => '国星半导体',
                        ),
                        
                        'descrip' => array(
                            'type' => 'editor',
                            'title' => '简介',
                            'std' => '国星半导体为国星光电的控股子公司，致力于研发、生产可用于照明、显示、背光的氮化镓基 LED芯片。',
                        ),
                        'typeimg' => array(
                            'type' => 'media',
                            'title' => '分类图片',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20221223/7ce71976dc146dc37d00f2167ca86a56.png'
                        ),
                        'tab_click_jump13' => array(
                            'type' => 'checkbox',
                            'title' => '开启点击标题跳转',
                            'std' => 0,
                        ),
                        'tz_page_type13' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                            'depends' => array(
                                array('tab_click_jump13', '=', 1),
                            ),
                        ),
                        'detail_page_id13' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('tz_page_type13', '=', 'Internal_pages'),
                                array('tab_click_jump13', '=', 1),
                            ),
                        ),
                        'detail_page13' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(
                                array('tz_page_type13', '=', 'external_links'),
                                array('tab_click_jump13', '=', 1),
                            ),
                        ),
                        'target13' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                            'depends' => array(
                                array('tab_click_jump13', '=', 1),
                            ),
                        ),

                        'ctype_item13' => array(
                            'title' => '子分类',
                            'std' => array(
                                array(
                                    'ctitle' => '显示屏',
                                    'tab_click_jump' => '0',
                                ),
                                array(
                                    'ctitle' => '倒装系列',
                                    'tab_click_jump' => '0',
                                ),
                            ),
                            'attr' => array(
                                'ctitle' => array(
                                    'type' => 'text',
                                    'title' => '子分类标题',
                                    'std' => '显示屏',
                                ),

                                'tab_click_jump' => array(
                                    'type' => 'checkbox',
                                    'title' => '开启点击跳转',
                                    'std' => 0,
                                ),
                                'tz_page_type' => array(
                                    'type' => 'select',
                                    'title' => JText::_('跳转方式'),
                                    'desc' => JText::_('跳转方式'),
                                    'values' => array(
                                        'Internal_pages' => JText::_('内部页面'),
                                        'external_links' => JText::_('外部链接'),
                                    ),
                                    'std' => 'Internal_pages',
                                    'depends' => array(
                                        array('tab_click_jump', '=', 1),
                                    ),
                                ),
                                'detail_page_id' => array(
                                    'type' => 'select',
                                    'title' => '选择跳转页面',
                                    'desc' => '',
                                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                                    'depends' => array(
                                        array('tz_page_type', '=', 'Internal_pages'),
                                        array('tab_click_jump', '=', 1),
                                    ),
                                ),
                                'detail_page' => array(
                                    'type' => 'text',
                                    'title' => '跳转链接',
                                    'depends' => array(
                                        array('tz_page_type', '=', 'external_links'),
                                        array('tab_click_jump', '=', 1),
                                    ),
                                ),
                                'target' => array(
                                    'type' => 'select',
                                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                                    'values' => array(
                                        '_self' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                        '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                                    ),
                                    'depends' => array(
                                        array('tab_click_jump', '=', 1),
                                    ),
                                ),
                            ),
                            
                        ),
                    ),
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'item13_bg' => array(
                    'type' => 'media',
                    'title' => '标题选中背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20221223/3e3b23745736532a0b153f778912f300.png',
                    'format' => 'image',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'item13_titcolor' => array(
                    'type' => 'color',
                    'title' => '标题背景色',
                    'std' => '#e6e6e5',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'item13_titlecolor' => array(
                    'type' => 'color',
                    'title' => '标题字体颜色',
                    'std' => '#858585',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'futitle_color' => array(
                    'type' => 'color',
                    'title' => '副标题字体颜色',
                    'std' => '#185bbb',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'ctybg' => array(
                    'type' => 'color',
                    'title' => '子分类背景色',
                    'std' => '#e6e6e5',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                'hvbg' => array(
                    'type' => 'color',
                    'title' => '子分类划过背景色',
                    'std' => '#185bbb',
                    'depends' => array(
                        array('section_type', '=', 'type13'),
                    ),
                ),
                
            ),
        ),
    )
);
