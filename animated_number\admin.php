<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_animated_number',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_DESC'),
		'category' => '其他',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				'number_type' => array(
					'type' => 'select',
					'title' => '样式选择',
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_FONT_WEIGHT_DESC'),
					'values' => array(
						'type1' => '样式1',
						'type2' => '样式2',
						'type3' => '样式3',
					),
				),

				'number' => array( 
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_DESC'),
					'placeholder' => '1000',
					'std' => '1000',
				),

				'number_before_after_text' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_BEF_AFT_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_BEF_AFT_TEXT_DESC'),
					'placeholder' => '+,K,$',
					'std' => '',
				),
				
				'number_before_after_text_right' => array(
					'type' => 'text',
					'title' => '右上角标识',
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_BEF_AFT_TEXT_DESC'),
					'placeholder' => '㎡',
					'std' => '',
					'depends' => array(
						array('number_type', '=', 'type2'),
					),
				),

				'number_before_after_text_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_BEF_AFT_TEXT_POS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_BEF_AFT_TEXT_POS_DESC'),
					'values' => array(
						'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BEFORE'),
						'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_AFTER'),
					),
					'std' => 'left',
					'depends' => array(
						array('number_before_after_text', '!=', ''),
						array('number_type', '!=', 'type2'),
						array('number_type', '!=', 'type3'),
					),
				),

				'duration' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_DURATION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_DURATION_DESC'),
					'placeholder' => '1000',
					'std' => '1000',
				),

				'color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_COLOR_DESC'),
				),

				'font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_SIZE_DESC'),
					'placeholder' => 36,
					'std' => array(
						'md' => 36 
					),
					'responsive' => true,
					'max' => 400,
				),

				'type3_div_width' => array(
					'type' => 'slider',
					'title' => JText::_('整体宽度'),
					'desc' => JText::_('整体宽度'),
					'placeholder' => 100,
					'std' =>  100,
					'responsive' => true,
					'max' => 100,
					'min' => 1,
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_image' => array(
					'type' => 'media',
					'title' => JText::_('图片'),
					'desc' => JText::_('图片'),
					'show_input' => true,
					'std' => '',
					'depends' => array(
						array('number_type', '=', 'type3'),
					)
				),
				'type3_image_top' => array(
					'type' => 'slider',
					'title' => JText::_('图片上边距'),
					'desc' => JText::_('图片上边距'),
					'placeholder' => 40,
					'std' =>  40,
					'responsive' => true,
					'max' => 100,
					'min' => 0,
					'depends' => array(
						array('number_type', '=', 'type3'),
						array('type3_image', '!=', ''),
					),
				),
				'type3_image_right' => array(
					'type' => 'slider',
					'title' => JText::_('图片右边距'),
					'desc' => JText::_('图片右边距'),
					'placeholder' => 20,
					'std' =>  20,
					'responsive' => true,
					'max' => 100,
					'min' => 0,
					'depends' => array(
						array('number_type', '=', 'type3'),
						array('type3_image', '!=', ''),
					),
				),
				'type3_title_left' => array(
					'type' => 'slider',
					'title' => JText::_('标题左侧边距'),
					'desc' => JText::_('标题左侧边距'),
					'placeholder' => 0,
					'std' =>  0,
					'responsive' => true,
					'max' => 200,
					'min' => 0,
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_title_height' => array(
					'type' => 'slider',
					'title' => JText::_('标题背景高度'),
					'desc' => JText::_('标题背景高度'),
					'placeholder' => 30,
					'std' =>  30,
					'responsive' => true,
					'max' => 200,
					'min' => 10,
					'depends' => array(
						array('number_type', '=', 'type3'), 
					),
				),
				'type3_title_font' => array(
					'type' => 'text',
					'title' => JText::_('标题'),
					'desc' => JText::_('标题'),
					'std' =>  '',
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_title_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('标题背景颜色'),
					'desc' => JText::_('标题背景颜色'),
					'std' =>  '#b71b28',
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_title_bgcolor_jb' => array(
					'type' => 'color',
					'title' => JText::_('标题背景渐变颜色'),
					'desc' => JText::_('标题背景渐变颜色'),
					'std' =>  '#ce3d4a',
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_title_font_color' => array(
					'type' => 'color',
					'title' => JText::_('标题字体颜色'),
					'desc' => JText::_('标题字体颜色'),
					'std' =>  '#fff', 
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_img_width' => array(
					'type' => 'slider',
					'title' => JText::_('图片宽度'),
					'desc' => JText::_('图片宽度'),
					'placeholder' => 30,
					'std' =>  30,
					'responsive' => true,
					'max' => 200,
					'min' => 10,
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_number_height' => array(
					'type' => 'slider',
					'title' => JText::_('动画数字背景高度'),
					'desc' => JText::_('动画数字背景高度'),
					'placeholder' => 50,
					'std' =>  50,
					'responsive' => true,
					'max' => 500,
					'min' => 10,
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_number_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('动画数字背景颜色'),
					'desc' => JText::_('动画数字背景颜色'),
					'std' =>  '#ffa500',
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_number_font_color' => array(
					'type' => 'color',
					'title' => JText::_('动画数字字体颜色'),
					'desc' => JText::_('动画数字字体颜色'),
					'std' =>  '#fff',
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),
				'type3_number_left' => array(
					'type' => 'slider',
					'title' => JText::_('动画数字左侧边距'),
					'desc' => JText::_('动画数字左侧边距'),
					'placeholder' => 0,
					'std' =>  0,
					'responsive' => true,
					'max' => 200,
					'min' => 0,
					'depends' => array(
						array('number_type', '=', 'type3'),
					),
				),

				'after_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('下角标大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_SIZE_DESC'),
					'placeholder' => 1,
					'std' =>  1,
					'responsive' => true,
					'max' => 400,
					'min' => 1,
				),

				'before_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('上角标大小'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_SIZE_DESC'),
					'placeholder' => 1,
					'std' =>  1,
					'responsive' => true,
					'max' => 400,
					'min' => 1,
					'depends' => array(
						array('number_type', '!=', 'type3'),
					),
				),

				'number_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_NUMBER_FONT_FAMILY_DESC'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-animated-number { font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
						array('number_type', '!=', 'type3'),
						array('number_type', '!=', 'type2'),
					),
				),

				'line_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_LINE_HEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_LINE_HEIGHT_DESC'),
					'placeholder' => 36,
					'std' => array(
						'md' => 36
					),
					'responsive' => true,
					'max' => 400,
					'depends' => array(
						array('number_type', '!=', 'type3'),
						array('number_type', '!=', 'type2'),
					),
				),

				'number_font_wight' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_FONT_WEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_FONT_WEIGHT_DESC'),
					'values' => array(
						100 => 100,
						200 => 200,
						300 => 300,
						400 => 400,
						500 => 500,
						600 => 600,
						700 => 700,
						800 => 800,
						900 => 900,
					),
					'depends' => array(
						array('number_type', '!=', 'type3'),
						array('number_type', '!=', 'type2'),
					),
				),

				'number_position' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_POSITION'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_POSITION_DESC'),
					'values' => array(
						'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
						'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
						'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
						'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
					),
					'depends' => array(
						array('number_type', '!=', 'type3'),
						array('number_type', '!=', 'type2'),
					),
				),

				'counter_separator' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_OPTIONS'),
					'depends' => array(
						array('number_type', '!=', 'type3'),
						array('number_type', '!=', 'type2'),
					),
				),

				'counter_title' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_TITLE_DESC'),
					'std' => '这里是标题',
				),

				'title_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_TITLE_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_TITLE_COLOR_DESC'),
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'title_font_size' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_NUMBER_TITLE_FONT_SIZE_DESC'),
					'placeholder' => 18,
					'std' => array(
						'md' => 18
					),
					'responsive' => true,
					'max' => 400,
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'title_font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_FONT_FAMILY'),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-animated-number-title{ font-family: "{{ VALUE }}"; }'
					),
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'title_line_height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_TITLE_LINE_HEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ANIMATED_TITLE_LINE_HEIGHT_DESC'),
					'placeholder' => 36,
					'std' => array(
						'md' => 36
					),
					'responsive' => true,
					'max' => 400,
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'title_fontstyle' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TITLE_FONT_STYLE'),
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'title_margin' => array(
					'type' => 'margin',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
					'responsive' => true,
					'std' => array('md' => '', 'sm' => '', 'xs' => ''),
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'alignment' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_CONTENT_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'jwpf-text-center',
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => '',
					'depends' => array(
					
						array('number_type', '!=', 'type2'),
					),
				),
			),
		),
	)
);
