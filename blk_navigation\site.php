<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonblk_navigation extends JwpagefactoryAddons
{

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        // $is_link    = (isset($settings->is_link)) ? $settings->is_link : 0;
        $config = new JConfig();
        $settings = $this->addon->settings;
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $a_css = '';
        if ($config->jzt_url == 'http://' . $_SERVER['HTTP_HOST']) {
            $urlpath = $config->jzt_url;
        } else {
            $urlpath = $config->ijzt_url;
        }

        if (isset($settings->title_padding) && $settings->title_padding) {
            if (trim($settings->title_padding) != "") {
                $title_padding_md = '';
                $title_paddings_md = explode(' ', $settings->title_padding);
                foreach ($title_paddings_md as $padding_md) {
                    $padding_md = trim($padding_md);
                    if (empty($padding_md)) {
                        $title_padding_md .= ' 0';
                    } else {
                        $title_padding_md .= ' ' . $padding_md;
                    }
                }
                $padding_css = "padding: " . $title_padding_md . ";\n";
            }
        }
        if (isset($settings->title_margin) && $settings->title_margin) {
            if (trim($settings->title_margin) != "") {
                $title_margin_md = '';
                $title_margins_md = explode(' ', $settings->title_margin);
                foreach ($title_margins_md as $margin_md) {
                    $margin_md = trim($margin_md);
                    if (empty($margin_md)) {
                        $title_margin_md .= ' 0';
                    } else {
                        $title_margin_md .= ' ' . $margin_md;
                    }
                }
                $margin_css = "margin: " . $title_margin_md . ";\n";
            }
        }
        $title_fontsize_css = (isset($settings->title_fontsize) && $settings->title_fontsize) ? 'font-size:' . $settings->title_fontsize . 'px;' : 'font-size:10px;';
        $title_lineheight_css = (isset($settings->title_lineheight) && $settings->title_lineheight) ? 'line-height:' . $settings->title_lineheight . 'px;' : 'line-height:30px;';
        $title_color_css = (isset($settings->title_color) && !empty($settings->title_color)) ? 'color:' . $settings->title_color . ';' : 'color:#000;';
        $output = '<style>'
            . $addonId . ' .navtop {'
            . 'width: 100%;'
            . 'height: 50px;'
            . '}'
            . $addonId . ' .navtop-left {'
            . 'width: 30%;'
            . 'height: auto;'
            . 'float: left;/*padding: 0px 0px 0px 23px;*/'
            . 'padding: 14px;
                display: flex;
                align-items: center;'
            . '}'
            . $addonId . ' .navtop-left img {'
            . 'width: ' . $settings->biao_bg_w . 'px;'
            . 'height: auto;'
            . 'vertical-align: top;'
            . 'float: left;'
            . 'padding-left: 4px;'
            . 'padding-right: 4px;'
            . '}'
            . $addonId . ' .navtop-left .navtop-left-text {'
            . 'float: left;'
            . 'font-size: 14px;'
            . '}'
            . $addonId . ' .navtop-right {'
            . 'width: 67%;'
            . 'height: auto;'
            . 'float: right;'
            . '}'
            . $addonId . ' .navtop-right .ula {'
            . 'width: 100%;'
            . 'height: 47px;'
            . 'padding: 0;
                margin: 0;'
            . 'line-height: 47px;'
            . 'text-align:right;'
            . '}'
            . $addonId . ' .navtop-right .lia {'
            . 'display: inline-block;'
            . 'padding-left: 10px;'
            . 'padding-right: 10px;'
            . 'position: relative;'
            . '}'
            . $addonId . ' .navtop-right .lia a {
                display: inline-block;'
                . $padding_css
                . $margin_css
                . $title_fontsize_css
                . $title_lineheight_css
                . $title_color_css
                . 'text-decoration: none;'
                . 'height:50px;
                transition: all ease-in-out 0.3s;'
            . '}'
            . $addonId . ' .navtop-right .lia:hover a {'
                . 'color:' . $settings->title_active_color . ';'
            . '}'
            . $addonId . ' .navtop-right .lia:before,' . $addonId . ' .navtop-right .lia:after {'
            . 'content:"";'
            . 'width:' . $settings->tx_w . 'px;'
            . 'height:' . $settings->tx_h . 'px;'
            . 'background-size: contain;'
            . 'position: absolute;'
            . 'left:0;'
            . 'right:0;'
            . 'margin:auto;
                opacity: 0;
                transition: all ease-in-out 0.3s;'
            . '}'
            . $addonId . ' .navtop-right .lia:hover::before{'
                . 'background: url(' . $settings->top_bg . ') no-repeat center;'
                . 'top:-' . $settings->tx_top_bottom . 'px;'
            . '}'
            . $addonId . ' .navtop-right .lia:hover::after{'
                . 'background: url(' . $settings->bottom_bg . ') no-repeat center;'
                . 'bottom:-' . $settings->tx_top_bottom . 'px;'
            . '}'
            . $addonId . ' .navtop-right .lia:hover:before,' . $addonId . ' .navtop-right .lia:hover:after {'
                . 'opacity: 1;'
            . '}'
            . '
            @media (min-width: 768px) and (max-width: 991px) {'
                . $addonId . ' .navtop-right .lia a {
                    font-size: ' . $settings->title_fontsize_sm .'px;
                    line-height: ' . $settings->title_lineheight_sm .'px;
                    padding: ' . $settings->title_padding_sm .';
                    margin: ' . $settings->title_margin_sm .';
                }
            }
            @media (max-width: 767px) {'
                . $addonId . ' .navtop-right .lia a {
                    font-size: ' . $settings->title_fontsize_xs .'px;
                    line-height: ' . $settings->title_lineheight_xs .'px;
                    padding: ' . $settings->title_padding_xs .';
                    margin: ' . $settings->title_margin_xs .';
                }
            }
            '

            . '</style>';
        $output .= '<div class="navtop">';
        $output .= '<div class="navtop-left" >';
        if ($settings->is_biao) {
            $output .= ' <img  src="' . $settings->biao_bg . '">';
        }
        $output .= '<div class="navtop-left-text">您所在的位置：首页 > ' . $settings->biao_title . '</div>';
        $output .= '</div>';
        $output .= '<div class="navtop-right">';
        // $output .= '<ul>';
        // //' . (($key == 0) ? ' class="active"' : ''  ) . '
        // foreach ($settings->blk_navigation_item as $key => $value) {
        //     $thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id . '&';
        //     if ($value->is_link) {
        //         $output .= '<li><a href="' . $thisUrl . '">' . $value->title . '</a></li>' . "\n";
        //     } else {
        //         $output .= '<li><a href="#' . $value->title_id . '">' . $value->title . '</a></li>' . "\n";
        //     }
        // }
        // $output .= '</ul>';

        $output .= '<div class="ula">';
        foreach ($settings->blk_navigation_item as $key => $value) {
            $thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($value->detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id . '&';
            if ($value->is_link) {
                $output .= '<div class="lia"><a href="' . $thisUrl . '">' . $value->title . '</a></div>' . "\n";
            } else {
                $output .= '<div class="lia"><a href="#' . $value->title_id . '">' . $value->title . '</a></div>' . "\n";
            }
        }
        $output .= '</div>';


        $output .= '</div>';
        $output .= '</div>';
        return $output;
    }

    public static function getTemplate()
    {
        $output = '
        <#
            var addonId = "#jwpf-addon-" + data.id;
            
            // 文字内边距
            var title_padding = data.title_padding || "0px 0px 0px 0px";
            
            // 划过图标宽高处理
            var tx_w = "";
            var tx_w_sm = "";
            var tx_w_xs = "";
            if(_.isObject(data.tx_w)){
                tx_w = data.tx_w.md
                tx_w_sm = data.tx_w.sm
                tx_w_xs = data.tx_w.xs
            }else {
                tx_w = tx_w_sm = tx_w_xs = data.tx_w
            }
            var tx_h = "";
            var tx_h_sm = "";
            var tx_h_xs = "";
            if(_.isObject(data.tx_h)){
                tx_h = data.tx_h.md
                tx_h_sm = data.tx_h.sm
                tx_h_xs = data.tx_h.xs
            }else {
                tx_h = tx_h_sm = tx_h_xs = data.tx_h
            }
            // 鼠标滑过图标间距
            var tx_top_bottom = "";
            var tx_top_bottom_sm = "";
            var tx_top_bottom_xs = "";
            if(_.isObject(data.tx_top_bottom)){
                tx_top_bottom = data.tx_top_bottom.md
                tx_top_bottom_sm = data.tx_top_bottom.sm
                tx_top_bottom_xs = data.tx_top_bottom.xs
            }else {
                tx_top_bottom = tx_top_bottom_sm = tx_top_bottom_xs = data.tx_top_bottom
            }
            
            // 文字内边距处理
            var padding = "";
            var padding_sm = "";
            var padding_xs = "";
            if(data.title_padding){
                if(_.isObject(data.title_padding)){
                    if(data.title_padding.md.trim() !== ""){
                        padding = data.title_padding.md.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_padding.sm.trim() !== ""){
                        padding_sm = data.title_padding.sm.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_padding.xs.trim() !== ""){
                        padding_xs = data.title_padding.xs.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                }
            }
            // 文字外边距处理
            var margin = "";
            var margin_sm = "";
            var margin_xs = "";
            if(data.title_margin){
                if(_.isObject(data.title_margin)){
                    if(data.title_margin.md.trim() !== ""){
                        margin = data.title_margin.md.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_margin.sm.trim() !== ""){
                        margin_sm = data.title_margin.sm.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                    if(data.title_margin.xs.trim() !== ""){
                        margin_xs = data.title_margin.xs.split(" ").map(item => {
                            if(_.isEmpty(item)){
                                return "0";
                            }
                            return item;
                        }).join(" ")
                    }
                }
            }
        #>
        <style>
            {{ addonId }} .navtop {
                width: 100%;
                height: 50px;
            }
            {{ addonId }} .navtop-left {
                width: 30%;
                height: auto;
                float: left;
                /*padding: 0px 0px 0px 23px;*/
                padding: 14px;
                display: flex;
                align-items: center;
            }
            {{ addonId }} .navtop-left img {
                width: {{data.biao_bg_w}}px;
                height: auto;
                vertical-align: top;
                float: left;
                padding-left: 4px;
                padding-right: 4px;
            }
            {{ addonId }} .navtop-left .navtop-left-text {
                float: left;
                font-size: 14px;
            }
            {{ addonId }} .navtop-right {
                width: 67%;
                height: auto;
                float: right;
            }
            {{ addonId }} .navtop-right .ula {
                width: 100%;
                height: 47px;
                padding: 0;
                margin: 0;
                line-height: 47px;
                text-align:right;
            }
            {{ addonId }} .navtop-right .lia {
                display: inline-block;
                padding-left: 10px;
                padding-right: 10px;
                position: relative;
            }
            {{ addonId }} .navtop-right .lia:before, {{ addonId }} .navtop-right .lia:after {
                content: "";
                width: {{ tx_w }}px;
                height: {{ tx_h }}px;
                background-size: contain;
                position: absolute;
                left: 0;
                right: 0;
                margin: auto;
                opacity: 0;
                transition: all ease-in-out 0.3s;
            }
            {{ addonId }} .navtop-right .lia:before {
                background: url({{data.top_bg}}) no-repeat center;
                top: -{{tx_top_bottom}}px;
            }
            {{ addonId }} .navtop-right .lia:after {
                background: url({{data.bottom_bg}}) no-repeat center;
                bottom: -{{tx_top_bottom}}px;
            }
            {{ addonId }} .navtop-right .lia:hover:before, {{ addonId }} .navtop-right .lia:hover:after {
                opacity: 1;
            }
            {{ addonId }} .navtop-right .lia a {
                display: inline-block;
                <# if(_.isObject(data.title_fontsize)){ #>
                    font-size: {{ data.title_fontsize.md }}px;
                <# } else { #>
                    font-size: {{ data.title_fontsize }}px;
                <# } #>
                <# if(_.isObject(data.title_lineheight)){ #>
                    line-height: {{ data.title_lineheight.md }}px;
                <# } else { #>
                    line-height: {{ data.title_lineheight }}px;
                <# } #>
                color: {{ data.title_color }};
                padding: {{ padding }};
                margin: {{ margin }};
                text-decoration: none;
                height: 50px;
                transition: all ease-in-out 0.3s;
            }
            {{ addonId }} .navtop-right .lia:hover a {
                color: {{data.title_active_color}};
            }
			@media (min-width: 768px) and (max-width: 991px) {
			    {{ addonId }} .navtop-right .lia a {
                    <# if(_.isObject(data.title_fontsize)){ #>
                        font-size: {{ data.title_fontsize.sm }}px;
                    <# } else { #>
                        font-size: {{ data.title_fontsize }}px;
                    <# } #>
                    <# if(_.isObject(data.title_lineheight)){ #>
                        line-height: {{ data.title_lineheight.sm }}px;
                    <# } else { #>
                        line-height: {{ data.title_lineheight }}px;
                    <# } #>
                    padding: {{ padding_sm }};
                    margin: {{ margin_sm }};
                }
			}
            @media (max-width: 767px) {
                {{ addonId }} .navtop-right .lia a {
                    <# if(_.isObject(data.title_fontsize)){ #>
                        font-size: {{ data.title_fontsize.xs }}px;
                    <# } else { #>
                        font-size: {{ data.title_fontsize }}px;
                    <# } #>
                    <# if(_.isObject(data.title_lineheight)){ #>
                        line-height: {{ data.title_lineheight.xs }}px;
                    <# } else { #>
                        line-height: {{ data.title_lineheight }}px;
                    <# } #>
                    padding: {{ padding_xs }};
                    margin: {{ margin_xs }};
                }
            }
        </style>
        <div class="navtop">
            <div class="navtop-left" >
                <# if(data.is_biao){  #>
                    <img src=\'{{data.biao_bg }}\'> 
                <# }#>
                <div class="navtop-left-text">您所在的位置：首页 > {{data.biao_title}}</div>
            </div>
            <div class="navtop-right">
                <div class="ula">
                    <# _.each(data.blk_navigation_item, function (navigation_item, key){ #>
                        <div class="lia"><a href="#{{navigation_item.title_id}}">{{navigation_item.title}}</a></div>
                    <# }); #>
                </div>
            </div>
        </div>  
        ';

        return $output;
    }

}
