<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonScale_swiper extends JwpagefactoryAddons
{
    public function render()
    {
//        $isk2installed = self::isComponentInstalled('scale_swiper');

//        if ($isk2installed === 0) {
//            return '<div>出错了</div>';
//        }
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        //指定获得产品列表的pid
        $detail = $_GET['detail'] ?? 0;
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

//        $items = (isset($settings->jw_image_carousel_item) && $settings->jw_image_carousel_item) ? $settings->jw_image_carousel_item : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';//排序
        $goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;//产品分类
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 7;//显示数量
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;//文章详情页id
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $title_c = (isset($settings->title_c)) ? $settings->title_c : 0;//标题字数控制

        //数据
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
        require_once $article_helper;
        $items = JwpagefactoryHelperGoods::getGoodsListThree($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id, $goods_catid);
//        var_dump($items);
//        exit();

        $output = '';
        if (!count($items)) {
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';
            return $output;
        }
        $output .= '<div class="swiper-box" id="swiper_' . $addonId . '">
          <div class="swiper-container">
            <div class="swiper-wrapper">';
                foreach ($items as $key => $item) {
                    $output .= '<div class="swiper-slide" data-id="' . $key . '">';
                        $output .= '<a href="' . $item->link . '" >';
                            $output .= '<img src=\' '. $item->image_thumbnail .' \' alt="">';

                            $output.='<div class="text-layout3">
                                <div class="split">
                                    <span class="dot">
                                        <span class="little"></span>
                                    </span>
                                </div>
                                <p>'.$item->title.'</p>
                            </div>';

                        $output .= '</a>';

                    $output .= '</div>';
                }
            $output .= '</div>
          </div>';
          $output .= '<!-- Add Pagination -->
                <div class="swiper-pagination"></div>';
        $output .= '</div>';


        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        return $js;
    }

    public function js()
    {
        $settings = $this->addon->settings;

        $addonId = $this->addon->id;

        $carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
        $carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;

//      能看到几个slides，手机端只能看到一个sliders
        $carousel_item_number = '';
        if (isset($settings->carousel_item_number) && $settings->carousel_item_number) {
            if (is_object($settings->carousel_item_number)) {
                $carousel_item_number = $settings->carousel_item_number->md;
            } else {
                $carousel_item_number = $settings->carousel_item_number;
            }
        } else {
            $carousel_item_number = '';
        }

        $carousel_item_number_sm = '';
        if (isset($settings->carousel_item_number) && $settings->carousel_item_number) {
            if (is_object($settings->carousel_height)) {
                $carousel_item_number_sm = $settings->carousel_item_number->sm;
            } else {
                $carousel_item_number_sm = $settings->carousel_item_number_sm;
            }
        } else {
            $carousel_item_number_sm = '';
        }



//      slide之间的距离
        $carousel_margin = (isset($settings->carousel_margin) && $settings->carousel_margin) ? $settings->carousel_margin : 0;

//        是否自动轮播
        $carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;

        if ($carousel_autoplay === 1) {
            $autoplay = '{
                delay: ' . $carousel_interval . ',
                disableOnInteraction: false,
              }';
        } else {
            $autoplay = 'false';
        }


        $script = 'jQuery(document).ready(function($){
//        初始化swiper配置项
            function initSwiper(slidesPerView,spaceBetween){
                let settings={
                    slidesPerView: slidesPerView,
                    spaceBetween: spaceBetween,
                    loop: true,
                    loopFillGroupWithBlank: true,
                    autoplay: ' . $autoplay . ',                  
                    speed: ' . $carousel_speed . ',
                    centeredSlides: true,
                    observer: true,
                    observeParents:true,
                    breakpoints: {
                        640: {
                          slidesPerView: 2,
                          spaceBetween: 20,
                        },
                        768: {
                          slidesPerView: 3,
                          spaceBetween: 40,
                        },
                        1024: {
                          slidesPerView: slidesPerView,
                          spaceBetween: spaceBetween,
                        },
                    }
                }
                let swiper = new Swiper("#swiper_' . $addonId . ' .swiper-container", settings);
                return swiper;
            }
//            根据屏幕初始化swiper
            function initSwiperByWidth(){
                if (innerWidth>991){
                    initSwiper(' . $carousel_item_number . ',' . $carousel_margin . ');
                }else if(innerWidth<991&&innerWidth>767){
                    initSwiper(' . $carousel_item_number_sm . ',' . $carousel_margin . ',);
                }else {
                    initSwiper(1,0);
                }
            }
            initSwiperByWidth();
//            屏幕改变大小再初始化一次
//            window.onresize=function (){
//                initSwiperByWidth();
//            } 
                
        })';
        return $script;
    }

    public function css()
    {
        $settings = $this->addon->settings;

//        轮播
        $addonId = '#swiper_' . $this->addon->id;


//        外部容器
        $carousel_height = '';
        if (isset($settings->carousel_height) && $settings->carousel_height) {
            if (is_object($settings->carousel_height)) {
                $carousel_height = $settings->carousel_height->md;
            } else {
                $carousel_height = $settings->carousel_height;
            }
        } else {
            $carousel_height = '';
        }
        $carousel_height_sm='';
        if (isset($settings->carousel_height_sm) && $settings->carousel_height_sm) {
            if (is_object($settings->carousel_height_sm)) {
                $carousel_height_sm = $settings->carousel_height_sm->sm;
            } else {
                $carousel_height_sm = $settings->carousel_height_sm;
            }
        } else {
            $carousel_height_sm = '';
        }
        $carousel_height_xs='';
        if (isset($settings->carousel_height_xs) && $settings->carousel_height_xs) {
            if (is_object($settings->carousel_height_xs)) {
                $carousel_height_xs = $settings->carousel_height_xs->xs;
            } else {
                $carousel_height_xs = $settings->carousel_height_xs;
            }
        } else {
            $carousel_height_xs = '';
        }




//        轮播项内容
//        对齐
        $item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : 'center';
        $item_content_verti_align = (isset($settings->item_content_verti_align) && $settings->item_content_verti_align) ? $settings->item_content_verti_align : 'center';
//          标题字体大小
        $content_title_fontsize='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize = $settings->content_title_fontsize->md;
            } else {
                $content_title_fontsize = $settings->content_title_fontsize;
            }
        } else {
            $content_title_fontsize = '';
        }

        $content_title_fontsize_sm='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_sm = $settings->content_title_fontsize->sm;
            } else {
                $content_title_fontsize_sm = $settings->content_title_fontsize_sm;
            }
        } else {
            $content_title_fontsize_sm = '';
        }

        $content_title_fontsize_xs='';
        if (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_fontsize_xs = $settings->content_title_fontsize->xs;
            } else {
                $content_title_fontsize_xs = $settings->content_title_fontsize_xs;
            }
        } else {
            $content_title_fontsize_xs = '';
        }


        $content_title_lineheight = (isset($settings->content_title_lineheight) && $settings->content_title_lineheight) ? $settings->content_title_lineheight : '60';
        $content_title_font_family = (isset($settings->content_title_font_family) && $settings->content_title_font_family) ? $settings->content_title_font_family : '';
        $title_style = '';
        $content_title_font_style = (isset($settings->content_title_font_style) && $settings->content_title_font_style) ? $settings->content_title_font_style : '';
        if (isset($content_title_font_style->underline) && $content_title_font_style->underline) {
            $title_style .= 'text-decoration:underline;';
        }
        if (isset($content_title_font_style->italic) && $content_title_font_style->italic) {
            $title_style .= 'font-style:italic;';
        }
        if (isset($content_title_font_style->uppercase) && $content_title_font_style->uppercase) {
            $title_style .= 'text-transform:uppercase;';
        }
        if (isset($content_title_font_style->weight) && $content_title_font_style->weight) {
            $title_style .= 'font-weight:' . $content_title_font_style->weight . ';';
        }
        $content_title_letterspace = (isset($settings->content_title_letterspace) && $settings->content_title_letterspace) ? $settings->content_title_letterspace : '';
        $content_title_text_color = (isset($settings->content_title_text_color) && $settings->content_title_text_color) ? $settings->content_title_text_color : '#333';

        $content_title_margin='';
        if (isset($settings->content_title_margin) && trim($settings->content_title_margin)) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_margin = $settings->content_title_margin->md;
            } else {
                $content_title_margin = $settings->content_title_margin;
            }
        } else {
            $content_title_margin = '';
        }

        $content_title_margin_sm='';
        if (isset($settings->content_title_margin) && trim($settings->content_title_margin)) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_margin_sm = $settings->content_title_margin->sm;
            } else {
                $content_title_margin_sm = $settings->content_title_margin_sm;
            }
        } else {
            $content_title_margin_sm = '';
        }
        $content_title_margin_xs='';
        if (isset($settings->content_title_margin) && trim($settings->content_title_margin)) {
            if (is_object($settings->content_title_fontsize)) {
                $content_title_margin_xs = $settings->content_title_margin->xs;
            } else {
                $content_title_margin_xs = $settings->content_title_margin_xs;
            }
        } else {
            $content_title_margin_xs = '';
        }




        $output = '
            ' . $addonId . '{
                position: relative;
                width: 100%;
                height: ' . $carousel_height . 'px;
                overflow: hidden;
                padding: 0;
            }
            ' . $addonId . ' a{
                text-decoration: none;
            }
            ' . $addonId . ' .swiper-container{
                width: 100%;
                height: 100%;
            }
            ' . $addonId . ' .swiper-slide{
                position: relative
            }
            ' . $addonId . ' .swiper-slide img{
                width: 100%;
                height: 100%;
                display: block;
                object-fit: cover;
            }
            ' . $addonId . ' .swiper-button-next.img img,
            ' . $addonId . ' .swiper-button-prev.img img{
                width: 100%;
                height: 100%;
            }
            ' . $addonId . ' .swiper-button-next.img img{
                transform: rotate(180deg);
            }
            ' . $addonId . ' .swiper-button-next.img::after,
            ' . $addonId . ' .swiper-button-prev.img::after{
                display: none;
            }
            ' . $addonId . ' .text-layout3 .split{
                width: 100%;
                text-align: center;
                margin-top: 22px;
                position: relative;
            }
            ' . $addonId . ' .text-layout3 .split .dot{
                display: inline-block;
                width: 26px;
                height: 26px;
                background: #69171233;
                border-radius: 50%;
                text-align: center;
                line-height: 26px;
            }
            ' . $addonId . ' .text-layout3 .split .dot .little{
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #691712;
                border-radius: 50%;
                opacity: 1;
            }
            ' . $addonId . ' .text-layout3 .split .dot::before,
            ' . $addonId . ' .text-layout3 .split .dot::after{
                content: "";
                display: inline-block;
                width: calc((100% - 40px) / 2);
                height: 1px;
                background: #B6B6B678;
                position: absolute;
                top: 0;
                bottom:0;
                margin: auto;
            }
            ' . $addonId . ' .text-layout3 .split .dot::before{
                left: 0px;
            }
            ' . $addonId . ' .text-layout3 .split .dot::after{
               right: 0px;
            }
            ' . $addonId . ' .text-layout3 p,
            ' . $addonId . ' .swiper-content .first-title{
                font-size: ' . $content_title_fontsize . 'px;
                font-family: ' . $content_title_font_family . ';
                color: ' . $content_title_text_color . ';
                line-height: ' . $content_title_lineheight . 'px;
                text-align: ' . $item_content_hori_align . ';
                ' . $title_style . ';
                letter-spacing: ' . $content_title_letterspace . ';
                ' . $content_title_margin . '
            }
            ' . $addonId . ' .text-layout3 p{
                overflow: hidden;
                text-overflow:ellipsis;
                white-space: nowrap;
            }

            ' . $addonId . ' .swiper-content{
                position:absolute;
                bottom: 0;
                left:0;
                right:0;
                margin:auto;
                display: flex;
                flex-direction: column;
                text-align:center;
                align-items: center;';
        if ($item_content_verti_align === 'middle') {
            $output .= '
                top: 0;
                justify-content: center;
            }';
        } else if ($item_content_verti_align === 'bottom') {
            $output .= 'justify-content: center}';
        } else {
            $output .= 'top:0;}';
        }
        $output .= '
            '.$addonId . ' .swiper-slide{
                transform: scale(.55);
            }
            ' . $addonId . ' .swiper-slide-prev,
            '. $addonId . ' .swiper-slide-next{
                transform: scale(.7);
            }
            ' . $addonId . ' .swiper-slide-active, .swiper-slide-duplicate-active{
                transform: scale(1);
            }' . $addonId . '  .swiper-slide img,
            ' . $addonId . ' .swiper-slide img{
                width: 100%;
                height: 80%;
            }
            /*' . $addonId . ' .swiper-slide-active img{
                height: 100%;
            }
            ' . $addonId . ' .swiper-slide-active .text-layout3{
                display: none;
            }*/
            @media (min-width: 768px) and (max-width: 991px){
                ' . $addonId . '{
                    height: ' . $carousel_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination{
                    display:none
                }
                ' . $addonId . ' .text-layout3 p{
                    font-size: ' . $content_title_fontsize_sm . 'px;
                    ' . $content_title_margin_sm . '
                }
            }
            @media (max-width: 767px){
                ' . $addonId . '{
                    height: ' . $carousel_height_xs . 'px;
                }
                ' . $addonId . ' .text-layout3 p{
                    font-size: ' . $content_title_fontsize_xs . 'px;
                    ' . $content_title_margin_xs . '
                }
            }';

        return $output;
    }

    public static function editCss(){
        $output='
            <# 
                let swiperId="#swiper_"+data.id;
                let carousel_height=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.md:data.carousel_height;
                let carousel_height_sm=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.sm:data.carousel_height;
                let carousel_height_xs=(_.isObject(data.carousel_height)&&data.carousel_height)?data.carousel_height.xs:data.carousel_height;
                let content_title_fontsize=(_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.md)?data.content_title_fontsize.md:data.content_title_fontsize;
                let content_title_fontsize_sm=(_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.sm)?data.content_title_fontsize.sm:data.content_title_fontsize;
                let content_title_fontsize_xs=(_.isObject(data.content_title_fontsize)&&data.content_title_fontsize.xs)?data.content_title_fontsize.xs:data.content_title_fontsize;
                let content_title_font_family=(_.isObject(data.content_title_font_family)&&data.content_title_font_family)?data.content_title_font_family:"";
                let content_title_text_color=(_.isObject(data.content_title_text_color)&&data.content_title_text_color)?data.content_title_text_color:"#333";
                let content_title_lineheight=(_.isObject(data.content_title_lineheight)&&data.content_title_lineheight)?data.content_title_lineheight:"60";
                let item_content_hori_align=(_.isObject(data.item_content_hori_align)&&data.item_content_hori_align)?data.item_content_hori_align:"center";
                let title_style="";
                let content_title_font_style = (_.isObject(data.content_title_font_style) && content_title_font_style) ? content_title_font_style : "";
                if (_.isObject(content_title_font_style.underline) && content_title_font_style.underline) {
                    title_style += "text-decoration:underline;";
                }
                if (_.isObject(content_title_font_style.italic) && content_title_font_style.italic) {
                    title_style += "font-style:italic;";
                }
                if (_.isObject(content_title_font_style.uppercase) && content_title_font_style.uppercase) {
                    title_style += "text-transform:uppercase;";
                }
                if (_.isObject(content_title_font_style.weight) && content_title_font_style.weight) {
                    title_style += "font-weight:"+content_title_font_style.weight;
                }
                let content_title_letterspace=(_.isObject(data.content_title_letterspace)&&data.content_title_letterspace)?data.content_title_letterspace:"0";
                let content_title_margin=(_.isObject(data.content_title_margin)&&data.content_title_margin.md)?data.content_title_margin.md:data.content_title_margin;
                let content_title_margin_sm=(_.isObject(data.content_title_margin)&&data.content_title_margin.sm)?data.content_title_margin.sm:data.content_title_margin;
                let content_title_margin_xs=(_.isObject(data.content_title_margin)&&data.content_title_margin.xs)?data.content_title_margin.xs:data.content_title_margin;
                let item_content_verti_align=(_.isObject(data.item_content_verti_align)&&data.item_content_verti_align)?data.item_content_verti_align:"middle";
                let carousel_item_number=(_.isObject(data.carousel_item_number)&&data.carousel_item_number.md)?data.carousel_item_number.md:"3";
                let carousel_item_number_sm=(_.isObject(data.carousel_item_number)&&data.carousel_item_number.sm)?data.carousel_item_number.sm:"2";
                let carousel_item_number_xs=(_.isObject(data.carousel_item_number)&&data.carousel_item_number.xs)?data.carousel_item_number.xs:"1";
                let carousel_margin=(_.isObject(data.carousel_margin)&&data.carousel_margin)?data.carousel_margin:"33";
                
                let active=Math.ceil(carousel_item_number/2),active_sm=Math.ceil(carousel_item_number_sm/2),active_xs=Math.ceil(carousel_item_number_xs/2);
                
            #>
            <style>
                #jwpf-addon-{{data.id}} .tips{
                    height: 40px;
                    line-height: 40px;
                    margin-bottom: 30px;
                    background: rgba(255,141,115,0.88);
                    box-sizing: border-box;
                    padding: 0 10px;
                }
                {{swiperId}}{
                    position: relative;
                    width: 100%;
                    height: {{carousel_height||830}}px;
                    overflow: hidden;
                    padding: 9%;
                }
                {{swiperId}} .scale-swiper-container{
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                }
                {{swiperId}} .swiper-slide{
                    transform: scale(.55);
                    position: relative;
                    width: calc(100% / {{carousel_item_number}} - {{carousel_margin}}px);
                    margin-right: {{carousel_margin}}px;
                }
                {{swiperId}} .swiper-slide img{
                    width: 100%;
                    height: 100%;
                    display: block;
                    object-fit: cover;
                }
                {{swiperId}} .swiper-button-next.img img,
                {{swiperId}} .swiper-button-prev.img img{
                    width: 100%;
                    height: 100%;
                }
                {{swiperId}} .swiper-button-next.img img{
                    transform: rotate(180deg);
                }
                {{swiperId}} .swiper-button-next.img::after,
                {{swiperId}} .swiper-button-prev.img::after{
                    display: none;
                }
                {{swiperId}} .text-layout3 .split{
                    width: 100%;
                    text-align: center;
                    margin-top: 22px;
                    position: relative;
                    overflow: hidden;
                }
                {{swiperId}} .text-layout3 .split .dot{
                    display: inline-block;
                    width: 26px;
                    height: 26px;
                    background: #69171233;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 26px;
                }
                {{swiperId}} .text-layout3 .split .dot .little{
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    background: #691712;
                    border-radius: 50%;
                    opacity: 1;
                }
                {{swiperId}} .text-layout3 .split .dot::before,
                {{swiperId}} .text-layout3 .split .dot::after{
                    content: "";
                    display: inline-block;
                    width: calc((100% - 40px) / 2);
                    height: 1px;
                    background: #B6B6B678;
                    position: absolute;
                    top: 0;
                    bottom:0;
                    margin: auto;
                }
                {{swiperId}} .text-layout3 .split .dot::before{
                    left: 0px;
                }
                {{swiperId}} .text-layout3 .split .dot::after{
                   right: 0px;
                }
                {{swiperId}} .text-layout3 p,
                {{swiperId}} .swiper-content .first-title{
                    font-size: {{content_title_fontsize||16}}px;
                    font-family: {{content_title_font_family}};
                    color: {{content_title_text_color}};
                    line-height: {{content_title_lineheight}}px;
                    text-align: {{item_content_hori_align}};
                    {{title_style}};
                    letter-spacing: {{content_title_letterspace}}px;
                    margin: {{content_title_margin}};
                }
                {{swiperId}} .swiper-content{
                    position:absolute;
                    bottom: 0;
                    left:0;
                    right:0;
                    margin:auto;
                    display: flex;
                    flex-direction: column;
                    text-align:center;
                    align-items: center;
                    <# if(item_content_verti_align==="middle"){ #>
                        top: 0;
                        justify-content: center;
                    <# }else if (item_content_verti_align === "bottom") { #>
                        justify-content: center;
                    <# }else { #>
                        top:0;
                    <# } #>
                }
                {{swiperId}} .swiper-slide:nth-of-type({{active+1}}),
                {{swiperId}} .swiper-slide:nth-of-type({{active-1}}){
                    transform: scale(.7);
                }
                {{swiperId}} .swiper-slide:nth-of-type({{active}}){
                    transform: scale(1);
                }
                {{swiperId}}  .swiper-slide img,
                {{swiperId}} .swiper-slide img{
                    width: 100%;
                    height: 80%;
                }
            @media (min-width: 768px) and (max-width: 991px){
                 {{swiperId}}{
                    height: {{carousel_height_sm||600}}px;
                }
                {{swiperId}} .swiper-pagination{
                    display:none;
                }
                {{swiperId}} .text-layout3 p{
                    font-size: {{content_title_fontsize_sm}}px;
                    margin: {{content_title_margin_sm}};
                }
                {{swiperId}} .swiper-slide{
                    width: calc(100% / {{carousel_item_number_sm}});
                }
            }
            @media (max-width: 767px){
                {{swiperId}}{
                    height: {{carousel_height_xs}}px;
                }
                {{swiperId}} .text-layout3 p{
                    font-size: {{content_title_fontsize_xs}}px;
                    margin: {{content_title_margin_xs}};
                }
                {{swiperId}} .swiper-slide{
                    width: calc(100% / {{carousel_item_number_xs}});
                }
            }
            </style>
        ';
        return $output;
    }
    public static function getTemplate()
    {
        $output='
        <# 
            let Id=data.id;
        #>
        <p class="alert alert-warning">本图片仅为布局样式，请在预览页面中查看该插件切换效果</p>
        <div class="swiper-box" id="swiper_{{Id}}">
            <div class="scale-swiper-container">
                <div class="swiper-wrapper">
                    <# _.each(data.jw_image_carousel_item,function(item,key){ #>
                        <div class="swiper-slide" data-id="{{key}}">
                            <img src=\'{{item.image_carousel_img}}\' alt="">
                            <div class="text-layout3">
                                <div class="split">
                                    <span class="dot">
                                        <span class="little"></span>
                                    </span>
                                </div>
                                <p>{{item.item_title}}</p>
                            </div>
                        </div>
                    <# }) #>
                </div>
            </div>
        </div>
        ';
        return self::editCss().$output;
    }
    /*static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();
        return $is_enabled;
    }*/
}
