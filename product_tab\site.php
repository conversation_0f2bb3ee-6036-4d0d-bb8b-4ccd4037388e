<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonProduct_tab extends JwpagefactoryAddons
{

    public static $this_obj;

    public function __construct($addon)
    {
        parent::__construct($addon);
        self::$this_obj = $this;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $cpcatid = $_GET['cpcatid'] ?? 0;

        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';
        $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : '';
        $nav_icon_postion = (isset($settings->nav_icon_postion) && $settings->nav_icon_postion) ? $settings->nav_icon_postion : '';
        $nav_image_postion = (isset($settings->nav_image_postion) && $settings->nav_image_postion) ? $settings->nav_image_postion : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
        $img_style_type = (isset($settings->img_style_type)) ? $settings->img_style_type : 'fill';
        $nav_text_align = (isset($settings->nav_text_align) && $settings->nav_text_align) ? $settings->nav_text_align : 'jwpf-text-left';
        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';
        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : '0';
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $catordering = isset($settings->catordering) ? $settings->catordering : 'sortasc'; //分类排序
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;

        $link_target = isset($settings->show_target) && $settings->show_target ? ' target=\'' . $settings->show_target . '\'' : '';

        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $article_helper;
        $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, $type_parent, $type_start, $type_num, $catordering);
        // print_r($categories_list);die;
        // if($catordering!='sortasc'){
        //     $last_names = array_column($categories_list,'ordering');
        //     array_multisort($last_names,SORT_ASC,$categories_list);

        // }else{
        //     $last_names = array_column($categories_list,'ordering');
        //     array_multisort($last_names,SORT_DESC,$categories_list);
        // }
        if ($type_parent == 'all') {
            $c_list = [];
            foreach ($categories_list as $key_l => $tab_l) {
                if ($tab_l['parent_id'] != 1) {
                    $c_list[$tab_l['parent_id']][] = $categories_list[$key_l];
                    unset($categories_list[$key_l]);
                }
            }
            foreach ($categories_list as $key_z => $tab_z) {
                $categories_list[$key_z]['subset'] = $c_list[$tab_z['tag_id']] ?? [];
            }
            if($type_num>0)
            {
                $dd = [];
                $num = 1;
                $nums = 1;
                foreach ($categories_list as $kk => $vv)
                {
                    if($type_start)
                    {
                        if($nums<=$type_start && $num<=$type_num)
                        {
                            $dd[] = $categories_list[$kk];
                            $num++;
                        }else{
                            if($nums>=$type_start && $num<=$type_num)
                            {
                                $dd[] = $categories_list[$kk];
                                $num++;
                            }
                        }
                    }
                    else
                    {
                        if($num<=$type_num)
                        {
                            $dd[] = $categories_list[$kk];
                            $num++;
                        }
                    }
                    $nums++;
                }
                $categories_list = $dd;
            }
        }
        $output = '<div class="jwpf-addon jwpf-addon-tab ' . $addon_id . $class . '">';
        $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
        $output .= '<div class="jwpf-addon-content minBox jwpf-tab jwpf-' . $style . '-tab jwpf-tab-' . $nav_position . '">';
        //新修改
        $nav_img = isset($settings->nav_img) ? $settings->nav_img : ''; //导航头部图片

        $nav_cont = isset($settings->nav_cont) ? $settings->nav_cont : 0; //导航头部图片
        $nav_img_w = isset($settings->nav_img_w) ? $settings->nav_img_w : ''; //导航图片宽
        $nav_img_h = isset($settings->nav_img_h) ? $settings->nav_img_h : ''; //导航图片高
        $nav_img_m = isset($settings->nav_img_m) ? $settings->nav_img_m : ''; //导航图片与导航之间的空间
        $nav_bor = isset($settings->nav_bor) ? $settings->nav_bor : '0px 0px 0px 0px'; //导航边框设置
        $nav_bor_color = isset($settings->nav_bor_color) ? $settings->nav_bor_color : '#666'; //导航边框颜色
        $nav_bor = explode(" ", $settings->nav_bor);
        $nav_bor_m = isset($settings->nav_bor_m) ? $settings->nav_bor_m : 0; //导航外层内边距
        $nav_bor_m = explode(" ", $settings->nav_bor_m);
        $nav_back_color = isset($settings->nav_back_color) ? $settings->nav_back_color : ''; //导航外层背景
        $nav_padding = isset($settings->nav_padding) ? $settings->nav_padding : 0; //导航外层背景
        $nav_padding = explode(" ", $settings->nav_padding);
        $nav_font_width = isset($settings->nav_font_width) ? $settings->nav_font_width : 0; //导航文字自定义位置

        // 新增 导航底部图片
        $nav_bottom_img = (isset($settings->nav_bottom_img) && $settings->nav_bottom_img) ? $settings->nav_bottom_img : ''; //导航底部图片
        $nav_bottom = (isset($settings->nav_bottom) && $settings->nav_bottom) ? $settings->nav_bottom : 0; //开启导航底部图片
        $nav_bottom_img_w = (isset($settings->nav_bottom_img_w) && $settings->nav_bottom_img_w) ? $settings->nav_bottom_img_w : 100; //导航底部图片宽度（百分比）
        $nav_bottom_img_h = (isset($settings->nav_bottom_img_h) && $settings->nav_bottom_img_h) ? $settings->nav_bottom_img_h : 100; //导航底部图片高度（px）
        $nav_bottom_img_m = (isset($settings->nav_bottom_img_m) && $settings->nav_bottom_img_m) ? $settings->nav_bottom_img_m : 0; //导航底部图片和导航之间的空间

        //新增 开启一级导航前字符
        $is_nav_before_icon = (isset($settings->is_nav_before_icon) && $settings->is_nav_before_icon) ? $settings->is_nav_before_icon : 0; //开启一级导航前字符
        $nav_before_icon = (isset($settings->nav_before_icon) && $settings->nav_before_icon) ? $settings->nav_before_icon : ''; //开启一级导航前字符

        $is_nav_after_icon = (isset($settings->is_nav_after_icon) && $settings->is_nav_after_icon) ? $settings->is_nav_after_icon : 0; //开启一级导航后圆点
        //新增 开启一级导航前分类图片
        $is_nav_cate_img = (isset($settings->is_nav_cate_img) && $settings->is_nav_cate_img) ? $settings->is_nav_cate_img : 0; //开启一级导航前分类图片

        $nav_beforeIcon = '';
        $nav_afterIcon = '';
        if ($style == 'custom') {
            if ($is_nav_before_icon) {
                $output .= '<style>
                     ' . $addon_id . ' .page_n .before-icon {
                        display: inline-block;
                        width: 12px;
                        height: 2px;
                        background-color: ' . $settings->nav_color . ';
                        margin-right: 3px;
                     }
                     ' . $addon_id . ' .page_n:hover .before-icon {
                        background-color: ' . $settings->hover_tab_color . ';
                     }
                     ' . $addon_id . ' .page_n.active .before-icon {
                        background-color: ' . $settings->active_tab_color . ';
                     }
                </style>';
                $nav_beforeIcon .= '<span class="before-icon"></span>';
            }
            if ($is_nav_cate_img) {
                $nav_cate_width_md = isset($settings->nav_cate_width) && $settings->nav_cate_width ? $settings->nav_cate_width : '30';
                $nav_cate_width_sm = isset($settings->nav_cate_width_sm) && $settings->nav_cate_width_sm ? $settings->nav_cate_width_sm : '';
                $nav_cate_width_xs = isset($settings->nav_cate_width_xs) && $settings->nav_cate_width_xs ? $settings->nav_cate_width_xs : '';

                $nav_cate_height_md = isset($settings->nav_cate_height) && $settings->nav_cate_height ? $settings->nav_cate_height : '30';
                $nav_cate_height_sm = isset($settings->nav_cate_height_sm) && $settings->nav_cate_height_sm ? $settings->nav_cate_height_sm : '';
                $nav_cate_height_xs = isset($settings->nav_cate_height_xs) && $settings->nav_cate_height_xs ? $settings->nav_cate_height_xs : '';
                $output .= '<style>
                    ' . $addon_id . ' .before-icon-cate {
                        width: ' . $nav_cate_width_md . 'px;
                        height: ' . $nav_cate_height_md . 'px;
                        margin-right: 5px;
                    }
                    ' . $addon_id . ' .before-icon-cate>img {
                        width: 100%;
                        height: 100%;
                        object-fit: scale-down;
                    }
                    ' . $addon_id . ' .before-icon-cate>img.hover-img {
                        display: none;
                    }';
                    if($settings->is_nav_cate_img_hover == 1) {
                        $output .= '
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a .before-icon-cate>img.hover-img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:hover .before-icon-cate>img.hover-img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:focus .before-icon-cate>img.hover-img {
                            display: block;
                        }
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a .before-icon-cate>img.img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:hover .before-icon-cate>img.img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:focus .before-icon-cate>img.img {
                            display: none;
                        }
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list > a:hover .before-icon-cate>img.hover-img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list > a:focus .before-icon-cate>img.hover-img {
                            display: block;
                        }
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list > a:hover .before-icon-cate>img.img,
                        ' . $addon_id . ' .jwpf-nav-custom li.product-list > a:focus .before-icon-cate>img.img {
                            display: none;
                        }
                        ';
                    }
                    $output .= '
                    ' . $addon_id . ' .jwpf-tab-title {
                        max-width: calc(100% - ' . $nav_cate_width_md . 'px - 5px);
                        /*white-space: nowrap;*/
                        text-overflow: ellipsis;
                        overflow: hidden;
                        display: inline-block;
                        /*height: 100%;*/
                    }
                    @media (min-width: 768px) and (max-width: 991px) {
                        ' . $addon_id . ' .before-icon-cate {
                            width: ' . $nav_cate_width_sm . 'px;
                            height: ' . $nav_cate_height_sm . 'px;
                        }
                        ' . $addon_id . ' .jwpf-tab-title {
                            max-width: calc(100% - ' . $nav_cate_width_sm . 'px - 5px);
                        }
                    }
                    @media (max-width: 767px) {
                        ' . $addon_id . ' .before-icon-cate {
                            width: ' . $nav_cate_width_xs . 'px;
                            height: ' . $nav_cate_height_xs . 'px;
                        }
                        ' . $addon_id . ' .jwpf-tab-title {
                            max-width: calc(100% - ' . $nav_cate_width_xs . 'px - 5px);
                        }
                    }
                </style>';
            }
            if ($is_nav_after_icon) {
                $output .= '<style>
                    ' . $addon_id . ' .page_n a {
                        position: relative;
                    }
                     ' . $addon_id . ' .page_n .after-icon {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        background-color: #fff;
                        border-radius: 50%;
                        position: absolute;
                        right: 10px;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                     }
                </style>';
                $nav_afterIcon .= '<span class="after-icon"></span>';
            }
        }

        if ($style == 'custom') {
            //border边框
            $output .= '<ul class="jwpf-nav minUl jwpf-nav-' . $style . '" role="tablist"  style="height:fit-content;background-color: ' . $nav_back_color . ';
            padding:' . ($nav_bor_m[0] ? $nav_bor_m[0] : 0) . ' ' . ($nav_bor_m[1] ? $nav_bor_m[1] : 0) . ' ' . ($nav_bor_m[2] ? $nav_bor_m[2] : 0) . ' ' . ($nav_bor_m[3] ? $nav_bor_m[3] : 0) . ';
            border-top-width:' . ($nav_bor[0] ? $nav_bor[0] : 0) . ';border-left-width:' . ($nav_bor[1] ? $nav_bor[1] : 0) . ';border-bottom-width:' . ($nav_bor[2] ? $nav_bor[2] : 0) . ';border-right-width:' . ($nav_bor[3] ? $nav_bor[3] : 0) . '; border-color:' . $nav_bor_color . ';border-style:solid;">';
            // 是否展示导航图片上方标题
            $nav_title = (isset($settings->nav_title) && $settings->nav_title) ? $settings->nav_title : '';
            // 导航图片上方标题
            $nav_title_show = (isset($settings->nav_title_show) && $settings->nav_title_show) ? $settings->nav_title_show : 0;
            // 导航图片上方标题字号
            $nav_font_size = (isset($settings->nav_font_size) && $settings->nav_font_size) ? $settings->nav_font_size : 16;
            // 导航图片上方标题字体颜色
            $nav_font_color = (isset($settings->nav_font_color) && $settings->nav_font_color) ? $settings->nav_font_color : '#fff';
            // 导航图片上方标题字体左边距
            $nav_font_left = (isset($settings->nav_font_left) && $settings->nav_font_left) ? $settings->nav_font_left : 2;
            // 导航图片上方标题字体上边距
            $nav_font_top = (isset($settings->nav_font_top) && $settings->nav_font_top) ? $settings->nav_font_top : 45;

            if ($pro_type != 'type8') {
                // 导航后圆点
                $output .= '<div style="position:relative;">';
                if ($nav_img != '' && $nav_cont == 1) //导航图片
                {
                    $output .= '<img style="max-width:none; width:' . $nav_img_w . '%;height: ' . $nav_img_h . 'px;margin-bottom:' . ($nav_img_m ? $nav_img_m : 0) . 'px;" src="' . $nav_img . '">';
                }
                if ($nav_title_show == 1) {
                    $output .= '<p class="nav_title" style="font-size:' . $nav_font_size . 'px;color:' . $nav_font_color . ';position:absolute;left:' . $nav_font_left . '%;top:' . $nav_font_top . '%">' . $nav_title . '</p>';
                }
                $output .= '</div>';
            }
        } else {
            $output .= '<style>
                ' . $addon_id . ' .jwpf-nav-modern>li{
                    position: relative;
                }
                ' . $addon_id . ' .er_type{
                    position: absolute;
                    z-index: 9;
                }';
            // 当样式选择为modern时选项卡样式
            if ($style == 'modern') {
                $output .= '
                    ' . $addon_id . ' .jwpf-nav-modern .page_n > a {
                        font-size: 14px;
                        font-weight: bolder;
                        line-height: 1.42857143;
                        padding: 12px 15px;
                        background: #f6fafb;
                        color: #4b5981;
                        border: 1px solid #e8eff1;
                        border-radius: 4px 4px 0 0;
                        display: block;
                        text-align: center;
                    }
                    ' . $addon_id . ' .jwpf-nav-modern > li.active > a,
                    ' . $addon_id . ' .jwpf-nav-modern > li.active > .page_n > a,
                    ' . $addon_id . ' .jwpf-nav-modern .page_n.active > a{
                        background: #fff;
                        border-bottom-color: #fff;
                    }';
            } elseif ($style == 'tabs') {
                $output .= $addon_id . ' .jwpf-nav-tabs .page_n > a {
                    font-size: 14px;
                    font-weight: bolder;
                    line-height: 1.42857143;
                    padding: 12px 15px;
                    background: #f5f5f5;
                    border: 1px solid #e5e5e5;
                    border-right-width: 0;
                    display: block;
                    text-align: center;
                }
                ' . $addon_id . ' .jwpf-nav-tabs > li.active > a,
                    ' . $addon_id . ' .jwpf-nav-tabs > li.active > .page_n > a,
                    ' . $addon_id . ' .jwpf-nav-tabs .page_n.active > a{
                        background: #fff;
                        border-bottom-color: #fff;
                    }';
            } elseif ($style == 'pills') {
                $output .= $addon_id . ' .jwpf-nav-pills .page_n > a {
                    font-size: 14px;
                    line-height: 1.42857143;
                    font-weight: bolder;
                    padding: 13px 15px;
                    border-radius: 3px;
                    color: #333;
                    display: block;
                    text-align: center;
                }';
            } elseif ($style == 'lines') {
                $output .= $addon_id . ' .jwpf-nav-lines .page_n > a {
                    padding: 13px 15px;
                    font-size: 14px;
                    color: inherit;
                    font-weight: bolder;
                    line-height: 1.42857143;
                    display: block;
                    text-align: center;
                }';
            }
            $output .= '</style>';

            //border边框
            $output .= '<ul class="jwpf-nav minUl jwpf-nav-' . $style . '" role="tablist">';

        }
        $one = 1;

        if ($pro_type == 'type4') {

            // print_r($settings->jw_tab_item);die;
            foreach ($settings->jw_tab_item as $key => $tab) {

                $icon_top = '';
                $icon_bottom = '';
                $icon_right = '';
                $icon_left = '';
                $icon_block = '';
                //Image
                $image_top = '';
                $image_bottom = '';
                $image_right = '';
                $image_left = '';
                //Lazy load image
                $dimension = $this->get_image_dimension($tab->image);
                $dimension = implode(' ', $dimension);

                $placeholder = !$tab->image ? false : $this->get_image_placeholder($tab->image);
                if (strpos($tab->image, "http://") !== false || strpos($tab->image, "https://") !== false) {
                    $tab->image = $tab->image;
                }

                $title = (isset($tab->title) && $tab->title) ? ' ' . $tab->title . ' ' : '';
                $subtitle = (isset($tab->subtitle) && $tab->subtitle) ? '<span class="jwpf-tab-subtitle">' . $tab->subtitle . '</span>' : '';

                if (isset($tab->image_or_icon) && $tab->image_or_icon == 'image') {
                    if ($tab->image && $nav_image_postion == 'top') {
                        $image_top = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'bottom') {
                        $image_bottom = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'right') {
                        $image_right = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } else {
                        $image_left = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    }
                } else {
                    if (isset($tab->icon) && $tab->icon) {
                        $icon_arr = array_filter(explode(' ', $tab->icon));
                        if (count($icon_arr) === 1) {
                            $tab->icon = 'fa ' . $tab->icon;
                        }
                        if ($tab->icon && $nav_icon_postion == 'top') {
                            $icon_top = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'bottom') {
                            $icon_bottom = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'right') {
                            $icon_right = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } else {
                            $icon_left = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        }
                    }
                }
                if ($nav_icon_postion == 'top' || $nav_icon_postion == 'bottom' || $nav_image_postion == 'top' || $nav_image_postion == 'bottom') {
                    $icon_block = 'tab-img-or-icon-block-wrap';
                }

                // 判断未点击翻页时默认选中首个分类
                if ($cpcatid == 0 || $cpcatid == '') {
                    $output .= '<li class="page_n product-list ' . (($key == 0) ? "active" : "") . '">';
                } else {
                    $output .= '<li class="page_n product-list ">';
                }
                if ($nav_font_width != 0) {
                    $output .= '<a style="text-indent:' . $nav_font_width . 'px; height:100%" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';
                } else {

                    $output .= '<a ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';
                }
                $output .= '</li>';
                $one++;
            }
            if ($nav_bottom_img != '' && $nav_bottom == 1) //导航图片
            {
                $output .= '<img style="max-width:none; width:' . $nav_bottom_img_w . '%;height: auto;margin-top:' . ($nav_bottom_img_m ? $nav_bottom_img_m : 0) . 'px;" src="' . $nav_bottom_img . '">';
            }
            //边框
            $output .= '</ul>';
            $oen2 = 1;
            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
            //列表数据的循环
            foreach ($settings->jw_tab_item as $k => $tab) {

                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($k == 0) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $tab->content . '</div>';
                //            $oen2 = $oen2 +1 ;
            }

        }
        if ($pro_type == 'type6') {
            $text_from = (isset($settings->text_from) && $settings->text_from) ? $settings->text_from : 0;
            $hqtype = (isset($settings->hqtype) && $settings->hqtype) ? $settings->hqtype : 'dan';
            $text_id = (isset($settings->text_id) && $settings->text_id) ? $settings->text_id : 0;
            $banner_typeid = (isset($settings->banner_typeid) && $settings->banner_typeid) ? $settings->banner_typeid : 0;
            // 左侧选项卡宽度
            $tab2_width = (isset($settings->tab2_width) && $settings->tab2_width) ? $settings->tab2_width : 245;
            // 上方栏目的高度
            $tab2_part01_height = (isset($settings->tab2_part01_height) && $settings->tab2_part01_height) ? $settings->tab2_part01_height : 100;
            // 上方栏目背景颜色
            $tab2_part01_bgColor = (isset($settings->tab2_part01_bgColor) && $settings->tab2_part01_bgColor) ? $settings->tab2_part01_bgColor : '#e50011';
            // 上方栏目文字大小
            $tab2_part01_fontsize = (isset($settings->tab2_part01_fontsize) && $settings->tab2_part01_fontsize) ? $settings->tab2_part01_fontsize : 28;
            // 上方栏目文字颜色
            $tab2_part01_color = (isset($settings->tab2_part01_color) && $settings->tab2_part01_color) ? $settings->tab2_part01_color : '#fff';
            // 下方分类的高度
            $tab2_part02_height = (isset($settings->tab2_part02_height) && $settings->tab2_part02_height) ? $settings->tab2_part02_height : 500;
            // 下方分类背景颜色
            $tab2_part02_bgColor = (isset($settings->tab2_part02_bgColor) && $settings->tab2_part02_bgColor) ? $settings->tab2_part02_bgColor : '#f2f2f2';
            // 分类行度
            $tab2_li_lineHeight = (isset($settings->tab2_li_lineHeight) && $settings->tab2_li_lineHeight) ? $settings->tab2_li_lineHeight : 60;
            // 分类文字大小
            $tab2_li_fontsize = (isset($settings->tab2_li_fontsize) && $settings->tab2_li_fontsize) ? $settings->tab2_li_fontsize : 14;
            // 分类文字颜色
            $tab2_li_color = (isset($settings->tab2_li_color) && $settings->tab2_li_color) ? $settings->tab2_li_color : '#333';
            // 单个分类背景颜色
            $tab2_li_bgColor = (isset($settings->tab2_li_bgColor) && $settings->tab2_li_bgColor) ? $settings->tab2_li_bgColor : '#f2f2f2';
            // 移入 分类文字颜色
            $tab2_li_color_hover = (isset($settings->tab2_li_color_hover) && $settings->tab2_li_color_hover) ? $settings->tab2_li_color_hover : '#e50011';
            // 移入 单个分类背景颜色
            $tab2_li_bgColor_hover = (isset($settings->tab2_li_bgColor_hover) && $settings->tab2_li_bgColor_hover) ? $settings->tab2_li_bgColor_hover : '#f2f2f2';
            // 移入 分类前竖线颜色
            $tab2_li_bColor_hover = (isset($settings->tab2_li_bColor_hover) && $settings->tab2_li_bColor_hover) ? $settings->tab2_li_bColor_hover : '#e50011';
            // 移入 分类前竖线宽度
            $tab2_li_bWidth_hover = (isset($settings->tab2_li_bWidth_hover) && $settings->tab2_li_bWidth_hover) ? $settings->tab2_li_bWidth_hover : 3;
            // 选中 分类文字颜色
            $tab2_li_color_active = (isset($settings->tab2_li_color_active) && $settings->tab2_li_color_active) ? $settings->tab2_li_color_active : '#e50011';
            // 选中 单个分类背景颜色
            $tab2_li_bgColor_active = (isset($settings->tab2_li_bgColor_active) && $settings->tab2_li_bgColor_active) ? $settings->tab2_li_bgColor_active : '#f2f2f2';
            // 选中 分类前竖线颜色
            $tab2_li_bColor_active = (isset($settings->tab2_li_bColor_active) && $settings->tab2_li_bColor_active) ? $settings->tab2_li_bColor_active : '#e50011';
            // 选中 分类前竖线宽度
            $tab2_li_bWidth_active = (isset($settings->tab2_li_bWidth_active) && $settings->tab2_li_bWidth_active) ? $settings->tab2_li_bWidth_active : 3;
            // 左上角文字
            $tab2_part01_name = (isset($settings->tab2_part01_name) && $settings->tab2_part01_name) ? $settings->tab2_part01_name : '关于我们';

            $output = "<style>
                {$addon_id} .er_left {
                    width: {$tab2_width}px;
                    display: inline-block;
                    vertical-align: top;
                }
                {$addon_id} .art-list {
                    width: calc(100% - " . ($tab2_width + 20) . "px);
                    margin-left: 20px;
                    display: inline-block;
                    vertical-align: top;
                }
                {$addon_id} .er_left .lanmu_title {
                    width: 100%;
                    height: {$tab2_part01_height}px;
                    background: {$tab2_part01_bgColor};
                    font-size: {$tab2_part01_fontsize}px;
                    font-weight: bold;
                    color: {$tab2_part01_color};
                    text-align: left;
                    line-height: {$tab2_part01_height}px;
                    box-sizing: border-box;
                    padding: 0 20px;
                }
                {$addon_id} .er_left ul {
                    display: block;
                    width: 100%;
                    height: {$tab2_part02_height}px;
                    background: {$tab2_part02_bgColor};
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }
                {$addon_id} .er_left ul li {
                    display: block;
                    width: 100%;
                    height: {$tab2_li_lineHeight}px;
                    line-height: {$tab2_li_lineHeight}px;
                    background-color: {$tab2_li_bgColor};
                    list-style: none;
                    font-weight: bold;
                    border-left: {$tab2_li_bWidth_hover}px solid transparent;
                    transition: all ease-in-out 0.3s;
                }
                {$addon_id} .er_left ul li a {
                    display: block;
                    margin: 0 20px;
                    font-size: {$tab2_li_fontsize}px;
                    line-height: " . ($tab2_li_lineHeight - 1) . "px;
                    color: {$tab2_li_color};
                    border-bottom: 1px solid #e4e4e4;
                    text-decoration: none;
                }
                {$addon_id} .er_left ul li a span {
                    float: right;
                }
                {$addon_id} .er_left ul li:hover {
                    background-color: {$tab2_li_bgColor_hover};
                    color: {$tab2_li_color_hover} !important;
                    border-left: {$tab2_li_bWidth_hover}px solid {$tab2_li_bColor_hover};
                }
                {$addon_id} .er_left ul li:hover a{
                    color: {$tab2_li_color_hover} !important;
                    transition: all ease-in-out 0.3s;
                }
                {$addon_id} .er_left ul li.item-active{
                    background-color: {$tab2_li_bgColor_active};
                    color: {$tab2_li_color_active};
                    border-left: {$tab2_li_bWidth_active}px solid {$tab2_li_bColor_active};
                }
                {$addon_id} .er_left ul li.item-active a{
                    color: {$tab2_li_color_active};
                }
                {$addon_id} .listhide {
                    display:none;
                }
                {$addon_id} {
                    display: -webkit-inline-box;
                }
            </style>";
            $output .= '<div class="er_left art-tab-box">';
            $output .= '<div class="lanmu_title">' . $tab2_part01_name . '</div>';
            $output .= '<ul class="art-tab">';
            if ($hqtype == 'dan') {
                $itemlists = JwPageFactoryBase::getinfoLists($site_id, $company_id, $banner_typeid, $text_id);
            } else {
                $itemlists = JwPageFactoryBase::getinfoLists($site_id, $company_id, $banner_typeid, 0);
            }
            if ($text_from) {
                foreach ($itemlists as $key => $tab) {
                    $active = '';
                    if ($key == 0) {
                        $active .= ' item-active';
                    }
                    $output .= '<li id="li' . $key . '" onclick="lists' . $this->addon->id . '(' . $key . ')" class="art-tab-item' . $active . '" value="' . $key . '"><a href="javascript:;">' . $tab['title'] . '<span>&gt;</span></a></li>';
                }
            } else {
                foreach ($settings->jw_tab_item as $key => $tab) {
                    $active = '';
                    if ($key == 0) {
                        $active .= ' item-active';
                    }
                    $output .= '<li id="li' . $key . '" onclick="lists' . $this->addon->id . '(' . $key . ')" class="art-tab-item' . $active . '" value="' . $key . '"><a href="javascript:;">' . $tab->title . '<span>&gt;</span></a></li>';
                }
            }
            $output .= '</ul>';
            $output .= '</div>';
            //边框
            $output .= '</ul>';
            $oen2 = 1;
            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content" style="margin-left:20px;width:832.5px;margin-top:0px;">';
            //列表数据的循环
            if ($text_from) {
                foreach ($itemlists as $k => $tab) {
                    if ($k == 0) {
                        $output .= '<div id="div' . $k . '" class="jwpf-big-div" style="width:832.5px">';
                    } else {
                        $output .= '<div id="div' . $k . '" class="jwpf-big-div listhide" style="width:832.5px">';
                    }
                    $output .= $this->listNav($tab['title']);
                    $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1"  class="jwpf-tab-pane jwpf-fade active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $tab['introtext'] . '</div>';
                    //            $oen2 = $oen2 +1 ;
                    $output .= '</div>';
                }
            } else {
                foreach ($settings->jw_tab_item as $k => $tab) {
                    if ($k == 0) {
                        $output .= '<div id="div' . $k . '" class="jwpf-big-div" style="width:832.5px">';
                    } else {
                        $output .= '<div id="div' . $k . '" class="jwpf-big-div listhide" style="width:832.5px">';
                    }
                    $output .= $this->listNav($tab->title);
                    $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1"  class="jwpf-tab-pane jwpf-fade active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $tab->content . '</div>';
                    //            $oen2 = $oen2 +1 ;
                    $output .= '</div>';
                }
            }
            $output .= '</div>';
            $output .= '<script>
                    function lists' . $this->addon->id . '(id){
                        $("' . $addon_id . ' .jwpf-big-div").addClass("listhide");
                        $("' . $addon_id . ' #div"+id).removeClass("listhide");
                        $("' . $addon_id . ' .item-active").removeClass("item-active");
                        $("' . $addon_id . ' #li"+id).addClass("item-active");
                    }
            </script>';
            return $output;

        }
        if ($pro_type == 'type7') {
            $article_helpes = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
            require_once $article_helpes;

            $pro7_title = (isset($settings->pro7_title) && $settings->pro7_title) ? $settings->pro7_title : '产品展示';
            $pro7_desc = (isset($settings->pro7_desc) && $settings->pro7_desc) ? $settings->pro7_desc : 'Products Show';
            $pro7_title_color = (isset($settings->pro7_title_color) && $settings->pro7_title_color) ? $settings->pro7_title_color : '#393939';
            $pro7_title_fontsize = (isset($settings->pro7_title_fontsize) && $settings->pro7_title_fontsize) ? $settings->pro7_title_fontsize : 24;
            $pro7_desc_color = (isset($settings->pro7_desc_color) && $settings->pro7_desc_color) ? $settings->pro7_desc_color : '#6a6a6a';
            $pro7_desc_fontsize = (isset($settings->pro7_desc_fontsize) && $settings->pro7_desc_fontsize) ? $settings->pro7_desc_fontsize : 20;

            $pro7_tab_width = (isset($settings->pro7_tab_width) && $settings->pro7_tab_width) ? $settings->pro7_tab_width : 104;
            $pro7_tab_height = (isset($settings->pro7_tab_height) && $settings->pro7_tab_height) ? $settings->pro7_tab_height : 42;
            $pro7_tab_mgL = (isset($settings->pro7_tab_mgL) && $settings->pro7_tab_mgL) ? $settings->pro7_tab_mgL : 42;
            $pro7_tab_fontsize = (isset($settings->pro7_tab_fontsize) && $settings->pro7_tab_fontsize) ? $settings->pro7_tab_fontsize : 16;
            $pro7_tab_color = (isset($settings->pro7_tab_color) && $settings->pro7_tab_color) ? $settings->pro7_tab_color : '#6a6a6a';
            $pro7_tab_bgColor = (isset($settings->pro7_tab_bgColor) && $settings->pro7_tab_bgColor) ? $settings->pro7_tab_bgColor : '';
            $pro7_tab_color_active = (isset($settings->pro7_tab_color_active) && $settings->pro7_tab_color_active) ? $settings->pro7_tab_color_active : '#fff';
            $pro7_tab_bgColor_active = (isset($settings->pro7_tab_bgColor_active) && $settings->pro7_tab_bgColor_active) ? $settings->pro7_tab_bgColor_active : '#5ea1cb';

            $pro7_content_img_style_type = (isset($settings->pro7_content_img_style_type) && $settings->pro7_content_img_style_type) ? $settings->pro7_content_img_style_type : 'fill';
            $pro7_content_title_fontsize = (isset($settings->pro7_content_title_fontsize) && $settings->pro7_content_title_fontsize) ? $settings->pro7_content_title_fontsize : 24;
            $pro7_content_title_color = (isset($settings->pro7_content_title_color) && $settings->pro7_content_title_color) ? $settings->pro7_content_title_color : '#000';
            $pro7_content_title_height = (isset($settings->pro7_content_title_height) && $settings->pro7_content_title_height) ? $settings->pro7_content_title_height : 70;

            $output = "<style>
                {$addon_id} .room_part {
                    width: 100%;
                    margin: 0 auto 12px;
                    height: auto;
                    overflow: hidden;
                }
                {$addon_id} .room_top {
                    margin-bottom: 32px;
                    height: auto;
                    display: flow-root;
                }
                {$addon_id} .room_title {
                    float: left;
                    color: {$pro7_desc_color};
                    font-size: {$pro7_desc_fontsize}px;
                    line-height: 2;
                    text-transform: uppercase;
                }
                {$addon_id} .room_title h4 {
                    color: {$pro7_title_color};
                    font-size: {$pro7_title_fontsize}px;
                    font-weight: normal;
                }
                {$addon_id} .room_list {
                    float: right;
                    padding: 9px 40px 0 0;
                    width: 800px;
                }
                {$addon_id} ul, li {
                    list-style: none;
                }
                {$addon_id} .room_list li {
                    display: inline-block;
                    width: {$pro7_tab_width}px;
                    height: {$pro7_tab_height}px;
                    line-height: {$pro7_tab_height}px;
                    text-align: center;
                    font-size: {$pro7_tab_fontsize}px;
                    cursor: pointer;
                    margin-left: {$pro7_tab_mgL}px;
                    color: {$pro7_tab_color};
                    background: {$pro7_tab_bgColor};
                }
                {$addon_id} .room_list li:first-child {
                    margin-left: 0px;
                }
                {$addon_id} .room_list .room_on {
                    color: {$pro7_tab_color_active};
                    background: {$pro7_tab_bgColor_active};
                }

                {$addon_id} .container {
                    margin:0 auto;
                    width:100%;
                    height: 407px;
                }
                {$addon_id} .container .div-box-content{
                    height: 407px;
                    width: 437px;
                    margin-left: 20px;
                    position:relative;
                    overflow: hidden;
                }
                {$addon_id} .container .div-box-content .div-box-img{
                    width: 100%;
                    height: 285px;
                }
                {$addon_id} .container .div-box-content .div-box-content-title{
                    color: {$pro7_content_title_color};
                    text-align:center;
                    line-height: {$pro7_content_title_height}px;
                    display:block;
                    width:100%;
                    font-size: {$pro7_content_title_fontsize}px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                }
                {$addon_id} .scroll-text {
                    width: 100%;
                    height:auto;
                    overflow: hidden;
                    padding:0;
                }
                {$addon_id} .scroll-text ul {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    margin: 0;
                    padding: 0;
                    font-size: 0;
                }
                {$addon_id} .scroll-text ul li {
                    height: 407px;
                    width: 461px;
                    display: inline-block;
                    margin: 0;
                    padding: 0;
                }
                {$addon_id} a{
                    text-decoration:none;
                    display: block;
                }
            </style>";
            $output .= '<div class="room_part wow fadeInUp animated" data-wow-delay="0.8s" style="visibility: visible; animation-delay: 0.8s; animation-name: fadeInUp;">';
            $output .= '<div class="room_top">';
            $output .= '<div class="room_title">';
            $output .= '    <h4>' . $pro7_title . '</h4>';
            $output .= $pro7_desc;
            $output .= '</div>';
            $output .= '<div class="room_list">';
            $output .= '    <ul>';
            foreach ($categories_list as $key => $tab) {
                if ($key == 0) {
                    $output .= '<li class="room_on" ccc="dddiv' . $key . '">' . $tab['title'] . '</li>';
                } else {
                    $output .= '<li class="" ccc="dddiv' . $key . '">' . $tab['title'] . '</li>';
                }
            }
            $output .= '</ul>';
            $output .= '</div>';
            $output .= '</div>';
            $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 10;
            $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
            $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
            $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
            $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
            $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
            $company_id = $_GET['company_id'] ?? 0;
            $site_id = $_GET['site_id'] ?? 0;
            $layout_id = $_GET['layout_id'] ?? 0;
            //列表数据的循环
            foreach ($categories_list as $key => $val) {
                if ($key == 0) {
                    $output .= '<div class="container" id="dddiv' . $key . '">';
                } else {
                    $output .= '<div class="container" id="dddiv' . $key . '" style="display: none;">';
                }
                $output .= '<div id="demo2" class="scroll-text">';
                $output .= '<ul style="width: 6000px;">';
                $items = JwpagefactoryHelperGoods::getGoodsList($limit, $ordering, $val['tag_id'], $include_subcat, $post_type, $tagids, $detail_page_id, 1, $company_id, $layout_id, $site_id);
                foreach ($items as $k => $v) {
                    $type7_link_target = isset($settings->show_target) && $settings->show_target ? $settings->show_target : '_blank';
                    $output .= '<li> ';
                    if ($settings->show_target == '#') {
                        $output .= '<a href="#" > ';
                    } else {
                        $output .= '<a href="' . $v->link . '" target="' . $type7_link_target . '"> ';
                    }
                    $output .= ' <div class="div-box-content">';
                    $output .= '<img style="object-fit:' . $pro7_content_img_style_type . ';" class="div-box-img" src="' . $v->image_thumbnail . '" alt="">';
                    $output .= '<div class="div-box-content-title title">' . $v->title . '</div>';
                    $output .= '</div>';
                    $output .= '</a>';
                    $output .= '</li>';
                }
                $output .= '</ul>';
                $output .= '</div>';
                $output .= '</div>';
            }
            $output .= '</div>';
            // $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script>';
            $output .= '<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.scrollbox.js"></script>';
            $output .= "
            <script type='text/javascript'>
                $(function($){
                    $(' {$addon_id} .room_list li').hover(function(){
                        $(this).addClass('room_on').siblings('li').removeClass('room_on');
                        var index = $(this).index();
                        $(' {$addon_id} .container').hide();
                        var id = $(this).attr('ccc')
                        console.log(id)
                        $('#'+id).show();
                    })
                })
            </script>
            ";
            $output .= "
            <script>
                jQuery(function($){
                    let width = ($('{$addon_id} .scroll-text ul li').length + 20) * 200;
                    $('{$addon_id} #demo2 ul').width(width);
                    $('{$addon_id} #demo2').scrollbox({
                        direction: '',
                        linear: true,
                        step: 1,
                        delay: 0,
                        speed: 20,
                        onMouseOverPause:false,
                    });
                });
            </script>
            ";
            return $output;

        } else {
            foreach ($categories_list as $key => $tab) {
                //print_r($tab);

                $icon_top = '';
                $icon_bottom = '';
                $icon_right = '';
                $icon_left = '';
                $icon_block = '';
                //Image
                $image_top = '';
                $image_bottom = '';
                $image_right = '';
                $image_left = '';
                //Lazy load image
                $dimension = $this->get_image_dimension($tab->image);
                $dimension = implode(' ', $dimension);

                $placeholder = !$tab->image ? false : $this->get_image_placeholder($tab->image);
                if (strpos($tab->image, "http://") !== false || strpos($tab->image, "https://") !== false) {
                    $tab->image = $tab->image;
                }
                $nav_beforeCateIcon = '';
                if ($settings->is_nav_cate_img == 1 && $style == 'custom' && $tab['image_intro']) {
                    $nav_beforeCateIcon = '
                    <span class="before-icon-cate">
                        <img src="' . $tab['image_intro'] . '" class="img" />';
                        if($settings->is_nav_cate_img_hover == 1 && $tab['type_qhimg']) {
                            $nav_beforeCateIcon .= '<img src="' . $tab['type_qhimg'] . '" class="hover-img" />';
                        }
                    $nav_beforeCateIcon .= '
                    </span>';
                }

                $title = (isset($tab['title']) && $tab['title']) ? '<span class="jwpf-tab-title">' . $tab['title'] . '</span>' : '';
                $subtitle = (isset($tab['subtitle']) && $tab['subtitle']) ? '<span class="jwpf-tab-subtitle">' . $tab['subtitle'] . '</span>' : '';

                if (isset($tab->image_or_icon) && $tab->image_or_icon == 'image') {
                    if ($tab->image && $nav_image_postion == 'top') {
                        $image_top = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'bottom') {
                        $image_bottom = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'right') {
                        $image_right = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } else {
                        $image_left = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    }
                } else {
                    if (isset($tab->icon) && $tab->icon) {
                        $icon_arr = array_filter(explode(' ', $tab->icon));
                        if (count($icon_arr) === 1) {
                            $tab->icon = 'fa ' . $tab->icon;
                        }
                        if ($tab->icon && $nav_icon_postion == 'top') {
                            $icon_top = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'bottom') {
                            $icon_bottom = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'right') {
                            $icon_right = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } else {
                            $icon_left = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        }
                    }
                }
                if ($nav_icon_postion == 'top' || $nav_icon_postion == 'bottom' || $nav_image_postion == 'top' || $nav_image_postion == 'bottom') {
                    $icon_block = 'tab-img-or-icon-block-wrap';
                }
                if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset'])) {
                    //二级导航为空
                    if ($settings->type_parent != 'all') {
                        // 判断未点击翻页时默认选中首个分类
                        if ($cpcatid == 0 || $cpcatid == '') {
                            $output .= '<li class="page_n product-list ' . (($one == 1) ? "active" : "") . '" >';
                        } else {
                            $output .= '<li class="page_n product-list ' . (($tab['tag_id'] == $cpcatid) ? "active" : "") . '"  dat="' . $tab['tag_id'] . '">';
                        }
                    } else {
                        $output .= '<li class="page_n product-list ' . (($one == 1) ? "active" : "") . '" >';
                    }

                    if ($nav_font_width != 0) {

                        $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $nav_beforeCateIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';
                    } else {
                        $output .= '<a ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $nav_beforeCateIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';

                    }
                    $output .= '</li>';
                } else {
                    // 有二级导航
                    $output .= '<li class="product-list ' . (($one == 1) ? "active" : "") . '">';
                    $output .= '<div class="page_n">';
                    if ($nav_font_width != 0) {
                        $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $nav_beforeCateIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';

                    } else {

                        $output .= '<a  ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $nav_beforeIcon . $nav_beforeCateIcon . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . $nav_afterIcon . '</a>';
                    }
                    $output .= '</div>';
                    $output .= '<ul class="er_type">';
                    foreach ($tab['subset'] as $ke => $tabe) {
                        $output .= '<li class="page_n product-list ' . (($one == 1) ? "active" : "") . '">';
                        if ($nav_font_width != 0) {
                            $output .= '<a style="text-indent:' . $nav_font_width . 'px;" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-' . ($ke + 1) . '" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $ke) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $tabe['title'] . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';

                        } else {
                            $output .= '<a ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-' . ($ke + 1) . '" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" class="' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $ke) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $image_top . $image_left . $icon_top . $icon_left . $tabe['title'] . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle . '</a>';

                        }
                        $output .= '</li>';
                        $one = $one + 1;
                    }
                    $output .= '</ul>';
                    $output .= '</li>';

                }
                $one++;
            }
            if ($nav_bottom_img != '' && $nav_bottom == 1) //导航图片
            {
                $output .= '<img style="max-width:none; width:' . $nav_bottom_img_w . '%;height: auto;margin-top:' . ($nav_bottom_img_m ? $nav_bottom_img_m : 0) . 'px;" src="' . $nav_bottom_img . '">';
            }
            //边框
            $output .= '</ul>';
            $oen2 = 1;
            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
            //列表数据的循环
            foreach ($categories_list as $k => $tab) {

                if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset'])) {
                    $conent = $this->render2($tab['tag_id'], $this);

                    if ($settings->type_parent != 'all') {

                        // 判断未点击翻页时默认选中首个分类数据
                        if ($cpcatid == 0 || $cpcatid == '') {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="sss jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                        } else {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="sss jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($tab['tag_id'] == $cpcatid) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                        }
                    } else {
                        $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="sss jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                    }

                    $oen2 = $oen2 + 1;
                } else {
                    foreach ($tab['subset'] as $ak => $va) {
                        $conent = $this->render2($va['tag_id'], $this);

                        if ($settings->type_parent != 'all') {
                            // 判断未点击翻页时默认选中首个分类数据
                            if ($cpcatid == 0 || $cpcatid == '') {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="sss jwpf-tab-' . ($this->addon->id + $ak) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                            } else {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="sss jwpf-tab-' . ($this->addon->id + $ak) . '" class="jwpf-tab-pane jwpf-fade " role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                            }
                        } else {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="sss jwpf-tab-' . ($this->addon->id + $ak) . '" class="jwpf-tab-pane jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                        }

                        $oen2 = $oen2 + 1;
                    }
                }
                //            $oen2 = $oen2 +1 ;
            }

        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        // 固定图片高度
        if ($fix_img_height) {
            $output .= "<style>

                     {$addon_id} .jwpf-img-responsive{
                        object-fit: {$img_style_type};
                         height:{$fix_img_height_input}px !important;
                       }
                       @media (max-width: 992px) {
                     {$addon_id} .jwpf-img-responsive{
                        height:{$fix_img_height_input_m}px !important;
                        }
                    }

             </style>";
        }
        $output .= "<style>
                     {$addon_id} li{
                         list-style-type: none;
                       }
                       {$addon_id} .er_type{
                          max-height: 0;
                          overflow: hidden;
                          transition: all 0.6s ease-out;
                       }
             </style>";

        // 二级导航在上面
        if ($nav_position == 'nav-top') {
            $output .= "<script>
//                    let nav_dhsub=jQuery('{$addon_id} .jwpf-nav-custom div.page_n').outerHeight(true);
//                    jQuery('{$addon_id} .jwpf-nav-custom .er_type').css('cssText','top:'+nav_dhsub+'px !important');


                    jQuery('{$addon_id} .jwpf-nav-custom>li').click(function (){
                        // 有二级导航
                            if(jQuery(this).find('ul').hasClass('er_type')){
                                 console.log(69697777)
                               jQuery('{$addon_id} .er_type').css('max-height','0');
//                                //展开状态
//                                console.log(jQuery(this).next().css('max-height'))
                                jQuery(this).find('.er_type').css('max-height','1000px');

                                jQuery('{$addon_id} .er_type > li').removeClass('active');
                            }else{
                                // 没有二级导航
                                console.log(6969)
                                jQuery('{$addon_id} .er_type').css('cssText','max-height:0 !important;');
                            }
                        })
                         jQuery('{$addon_id} .jwpf-nav-custom>li').mouseleave(function (){
                             console.log(5858)
                             jQuery(this).find('.er_type').css('cssText','max-height:0 !important;');
                        })

                </script>";
        } else {
            // 二级导航在两侧
            $output .= "<script>
                        jQuery('{$addon_id} div.page_n').click(function (){
                            if(jQuery(this).next().hasClass('er_type')){
                                //jQuery({$addon_id} 'div.page_n').next().css('max-height','0');
                                //展开状态
                                console.log(jQuery(this).next().css('max-height'))
                                if(jQuery(this).next().css('max-height')!='0px'){
                                     jQuery(this).next().css('max-height','0');
                                }else{
                                    jQuery(this).next().css('max-height','1000px');
                                }
                            }
                        })
                        // 开始有二级导航
                        if(jQuery('{$addon_id} .jwpf-nav-custom>li').eq(0).find('ul').length > 0){
                          jQuery('{$addon_id} .jwpf-nav-custom>li').eq(0).find('ul').css('max-height','1000px');
                        }
                        /* 20240805 导航在左侧，点击完产品分类2下的产品2（分类），再回去点击产品分类1下的产品1（分类）下的产品就点不出来了 */
                        $('{$addon_id} div.page_n + .er_type .page_n').unbind().on('click', function(e){
                            $('{$addon_id} div.page_n + .er_type .page_n').removeClass('active');
                            $(e.target).addClass('active')
                        })
             </script>";
        }
        if ($style == 'type6') {
            // print_r($categories_list);die;
            $output = '
            <style>
            @media only screen and (min-width: 1024px){
                ' . $addon_id . ' .shit202101071725001-c4 {
                    width: 1030px;
                    height: 56px;
                    line-height: 56px;
                    display: flex;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div {
                    flex: 1;
                    width: 160px;
                    height: 44px;
                    border-radius: 100px;
                    margin-top: 8px;
                    text-align: center;
                    line-height: 44px;
                    background-color: rgba(220, 220, 220, 0.88);
                    cursor: pointer;
                    border: 1px solid #fffcfc;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div a {
                    color: #000;
                    font-size: 18px;
                    text-decoration: none;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div .shit202101071725001-c5 {
                    width: 160px;
                    z-index: 5;
                    display: none;
                    z-index: 10;
                    position: relative;
                    top: -10px;
                    padding-top: 10px;
                    background: #fff;
                    border: 0;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div .shit202101071725001-c5 div {
                    border: 1px solid #dcdcdc;
                    border-radius: 100px;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div .shit202101071725001-c5 div a {
                    color: black;
                    font-size: 20px;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div:hover {
                    border: 1px solid #f9f6f1;
                    border-radius: 100px;
                    cursor: pointer;
                    background-color: #fff;
                }
                ' . $addon_id . ' .shit202101071725001-c4 div:hover .shit202101071725001-c5 {
                    display: block;
                    height: 200px;
                    margin: 0 auto;
                }
            }
            </style>
            ';
            $output .= '<div class="jwpf-addon jwpf-addon-tab ' . $addon_id . $class . '">';
            $output .= '<div class="jwpf-addon-content minBox jwpf-tab jwpf-' . $style . '-tab jwpf-tab-' . $nav_position . '">';
            $output .= '<div class="shit202101071725001-c4">';
            foreach ($categories_list as $key => $tab) {
                $ccsssc = '';
                $ccsssc = 'jwpf-tab' . ($this->addon->id + $key + 1) . '-1';
                $output .= '<div>';
                $output .= '<a onclick="changeDictionary(this,\'' . $ccsssc . '\')" ccc="jwpf-tab-' . ($this->addon->id + $key + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $key) . '" class="ss ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($one == 1) ? "true" : "false") . '">' . $tab['title'] . '</a>';
                $output .= '<div class="shit202101071725001-c5">';
                if (isset($tab['subset']) && $tab['subset'] != '') {
                    foreach ($tab['subset'] as $ke => $tabe) {
                        $ccssscs = '';
                        $ccssscs = 'jwpf-tab' . ($this->addon->id + $key + 1) . '-' . ($ke + 1);
                        $output .= '<div>';
                        // $output .='<a ccc="jwpf-tab-' . ($this->addon->id + $ke + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" >'.$tabe['title'].'</a>';
                        $output .= '<a onclick="changeDictionary(this,\'' . $ccssscs . '\')" ccc="jwpf-tab-' . ($this->addon->id + $ke + 1) . '-1" data-toggle="jwpf-tab" id="jwpf-content-' . ($this->addon->id + $ke) . '" href="#jwpf-tab-' . ($this->addon->id + $ke) . '" >' . $tabe['title'] . '</a>';
                        $output .= '</div>';
                    }
                }
                $output .= '</div>';
                $output .= '</div>';
            }
            $output .= '</div>';

            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
            //列表数据的循环
            foreach ($categories_list as $k => $tab) {

                if ((isset($tab['subset']) && empty($tab['subset'])) || !isset($tab['subset'])) {
                    $conent = $this->render2($tab['tag_id'], $this);

                    if ($settings->type_parent != 'all') {

                        // 判断未点击翻页时默认选中首个分类数据
                        if ($cpcatid == 0 || $cpcatid == '') {
                            if ($k == 0) {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . $tab['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade   active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                            } else {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . $tab['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                            }
                        } else {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . $tab['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade ' . (($tab['tag_id'] == $cpcatid) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                        }
                    } else {
                        if ($k == 0) {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . $tab['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade   active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                        } else {
                            $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-1" id="jwpf-tab-' . $tab['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $k) . '">' . $conent . '</div>';
                        }
                    }

                    $oen2 = $oen2 + 1;
                } else {
                    foreach ($tab['subset'] as $ak => $va) {
                        $conent = $this->render2($va['tag_id'], $this);

                        if ($settings->type_parent != 'all') {
                            // 判断未点击翻页时默认选中首个分类数据
                            if ($cpcatid == 0 || $cpcatid == '') {
                                if ($k == 0) {
                                    $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . $va['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade  active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                                } else {
                                    $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . $va['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                                }
                            } else {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . $va['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade " role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                            }
                        } else {
                            if ($k == 0) {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . $va['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade  active in" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                            } else {
                                $output .= '<div ccc="jwpf-tab-' . ($this->addon->id + $k + 1) . '-' . ($ak + 1) . '" id="jwpf-tab-' . $va['tag_id'] . '" class="jwpf-tab-pane ptab jwpf-fade ' . (($oen2 == 1) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $ak) . '">' . $conent . '</div>';
                            }

                        }

                        $oen2 = $oen2 + 1;
                    }
                }
                //            $oen2 = $oen2 +1 ;
            }
            $output .= '</div>';

            $output .= '</div>';
            $output .= '</div>';
            $output .= '
                <script>
                function changeDictionary(obj,ccc)
                {

                    var id = "#"+ccc;
                    console.log($(id))
                    $(".ptab").removeClass("active") ;
                    $(".ptab").removeClass("in") ;
                    $(id).addClass("active") ;
                    $(id).addClass("in") ;
                }
                </script>
            ';
        }

        return $output;

    }

    public function listNav($title)
    {

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        // 列表导航部分
        // 开启列表部分导航
        $show_list_nav = (isset($settings->show_list_nav) && $settings->show_list_nav) ? $settings->show_list_nav : 0;
        // 列表部分导航布局
        $list_nav_style = (isset($settings->list_nav_style) && $settings->list_nav_style) ? $settings->list_nav_style : 'nav01';
        // 列表部分导航标题
        $list_nav_name = (isset($settings->list_nav_name) && $settings->list_nav_name) ? $settings->list_nav_name : '新闻中心';

        // 列表部分导航右侧文字分隔符
        $list_nav_right_s = (isset($settings->list_nav_right_s) && $settings->list_nav_right_s) ? $settings->list_nav_right_s : '>';
        // 关闭右侧图标显示
        $hidden_list_nav_right_img = (isset($settings->hidden_list_nav_right_img) && $settings->hidden_list_nav_right_img) ? $settings->hidden_list_nav_right_img : 0;
        // 列表部分导航左侧图标
        $list_nav_left_img = (isset($settings->list_nav_left_img) && $settings->list_nav_left_img) ? $settings->list_nav_left_img : '';
        // 列表部分导航右侧图标
        $list_nav_right_img = (isset($settings->list_nav_right_img) && $settings->list_nav_right_img) ? $settings->list_nav_right_img : 'https://oss.lcweb01.cn/jzt/6d102551-1c08-4a57-9a29-a2d204df9298/image/20211118/d1aa16c21c434c802485343db816398a.png';

        $list_nav_bg_style = (isset($settings->list_nav_bg_style) && $settings->list_nav_bg_style) ? $settings->list_nav_bg_style : 'color';
        $list_nav_bgColor = (isset($settings->list_nav_bgColor) && $settings->list_nav_bgColor) ? $settings->list_nav_bgColor : '';
        $list_nav_bgGradient = (isset($settings->list_nav_bgGradient) && $settings->list_nav_bgGradient) ? $settings->list_nav_bgGradient : null;

        $list_nav_borderColor = (isset($settings->list_nav_borderColor) && $settings->list_nav_borderColor) ? $settings->list_nav_borderColor : '#eee';

        $list_nav_border_h_md = (isset($settings->list_nav_border_h) && $settings->list_nav_border_h) ? $settings->list_nav_border_h : 2;
        $list_nav_border_h_sm = (isset($settings->list_nav_border_h_sm) && $settings->list_nav_border_h_sm) ? $settings->list_nav_border_h_sm : '';
        $list_nav_border_h_xs = (isset($settings->list_nav_border_h_xs) && $settings->list_nav_border_h_xs) ? $settings->list_nav_border_h_xs : '';

        $list_nav_mb_md = (isset($settings->list_nav_mb) && $settings->list_nav_mb) ? $settings->list_nav_mb : '';
        $list_nav_mb_sm = (isset($settings->list_nav_mb_sm) && $settings->list_nav_mb_sm) ? $settings->list_nav_mb_sm : '';
        $list_nav_mb_xs = (isset($settings->list_nav_mb_xs) && $settings->list_nav_mb_xs) ? $settings->list_nav_mb_xs : '';

        $list_nav_left_title_f_md = (isset($settings->list_nav_left_title_f) && $settings->list_nav_left_title_f) ? $settings->list_nav_left_title_f : 18;
        $list_nav_left_title_f_sm = (isset($settings->list_nav_left_title_f_sm) && $settings->list_nav_left_title_f_sm) ? $settings->list_nav_left_title_f_sm : '';
        $list_nav_left_title_f_xs = (isset($settings->list_nav_left_title_f_xs) && $settings->list_nav_left_title_f_xs) ? $settings->list_nav_left_title_f_xs : '';

        $list_nav_left_title_h_md = (isset($settings->list_nav_left_title_h) && $settings->list_nav_left_title_h) ? $settings->list_nav_left_title_h : 44;
        $list_nav_left_title_h_sm = (isset($settings->list_nav_left_title_h_sm) && $settings->list_nav_left_title_h_sm) ? $settings->list_nav_left_title_h_sm : '';
        $list_nav_left_title_h_xs = (isset($settings->list_nav_left_title_h_xs) && $settings->list_nav_left_title_h_xs) ? $settings->list_nav_left_title_h_xs : '';

        $list_nav_h_md = (isset($settings->list_nav_h) && $settings->list_nav_h) ? $settings->list_nav_h : '';
        $list_nav_h_sm = (isset($settings->list_nav_h_sm) && $settings->list_nav_h_sm) ? $settings->list_nav_h_sm : '';
        $list_nav_h_xs = (isset($settings->list_nav_h_xs) && $settings->list_nav_h_xs) ? $settings->list_nav_h_xs : '';

        $list_nav_left_p_md = (isset($settings->list_nav_left_p) && $settings->list_nav_left_p) ? $settings->list_nav_left_p : '0 20px';
        $list_nav_left_p_sm = (isset($settings->list_nav_left_p_sm) && $settings->list_nav_left_p_sm) ? $settings->list_nav_left_p_sm : '';
        $list_nav_left_p_xs = (isset($settings->list_nav_left_p_xs) && $settings->list_nav_left_p_xs) ? $settings->list_nav_left_p_xs : '';

        $list_nav_left_title_fColor = (isset($settings->list_nav_left_title_fColor) && $settings->list_nav_left_title_fColor) ? $settings->list_nav_left_title_fColor : '#333';
        $list_nav_left_lineColor = (isset($settings->list_nav_left_lineColor) && $settings->list_nav_left_lineColor) ? $settings->list_nav_left_lineColor : '#e50011';

        $list_nav_right_p_md = (isset($settings->list_nav_right_p) && $settings->list_nav_right_p) ? $settings->list_nav_right_p : '0 20px';
        $list_nav_right_p_sm = (isset($settings->list_nav_right_p_sm) && $settings->list_nav_right_p_sm) ? $settings->list_nav_right_p_sm : '';
        $list_nav_right_p_xs = (isset($settings->list_nav_right_p_xs) && $settings->list_nav_right_p_xs) ? $settings->list_nav_right_p_xs : '';

        $list_nav_right_f_md = (isset($settings->list_nav_right_f) && $settings->list_nav_right_f) ? $settings->list_nav_right_f : 14;
        $list_nav_right_f_sm = (isset($settings->list_nav_right_f_sm) && $settings->list_nav_right_f_sm) ? $settings->list_nav_right_f_sm : '';
        $list_nav_right_f_xs = (isset($settings->list_nav_right_f_xs) && $settings->list_nav_right_f_xs) ? $settings->list_nav_right_f_xs : '';

        $list_nav_right_Color = (isset($settings->list_nav_right_Color) && $settings->list_nav_right_Color) ? $settings->list_nav_right_Color : '#666';
        $list_nav_right_t_Color = (isset($settings->list_nav_right_t_Color) && $settings->list_nav_right_t_Color) ? $settings->list_nav_right_t_Color : '#e50011';

        $output_nav = '';
        if ($show_list_nav == 1) {
            if ($list_nav_style == 'nav01') {
                $output_nav .= "
                    <style>
                        {$addon_id} .list-nav {
                            display: flex;
                            width: 100%;
                            align-items: center;
                            justify-content: space-between;
                            border-bottom: {$list_nav_borderColor} solid {$list_nav_border_h_md}px;";
                if ($list_nav_bg_style == 'color') {
                    $output_nav .= "background-color: {$list_nav_bgColor};";
                }
                if ($list_nav_bg_style == 'gradient' && $list_nav_bgGradient) {
                    $output_nav .= 'background-image: ' . ($list_nav_bgGradient->type ?: "linear") . '-gradient(';
                    if ($list_nav_bgGradient->type && $list_nav_bgGradient->type == "radial") {
                        $output_nav .= 'at ' . ($list_nav_bgGradient->radialPos ?: "center center");
                    } else {
                        $output_nav .= ($list_nav_bgGradient->deg ?: 0) . 'deg';
                    }
                    $output_nav .= ',
                                                    ' . $list_nav_bgGradient->color . ' ' . ($list_nav_bgGradient->pos ?: 0) . '%,';
                    $output_nav .= $list_nav_bgGradient->color2 . ' ' . ($list_nav_bgGradient->pos2 ?: 100) . '%);';

                }
                $output_nav .= "
                            margin-bottom: {$list_nav_mb_md}px;
                            height: {$list_nav_h_md}px;
                        }
                        {$addon_id} .list-nav .nav-left {
                            display: flex;
                            align-items: center;
                            font-size: {$list_nav_left_title_f_md}px;
                            line-height: {$list_nav_left_title_h_md}px;
                            padding: {$list_nav_left_p_md};
                            text-align: center;
                            color: {$list_nav_left_title_fColor};
                            font-weight: bold;
                            position: relative;
                        }
                        {$addon_id} .list-nav .nav-left:before {
                            content: '';
                            width: 2px;
                            height: {$list_nav_left_title_f_md}px;
                            background-color: {$list_nav_left_lineColor};
                            position: absolute;
                            left: 8px;
                            top: 0;
                            bottom: 0;
                            margin: auto;
                        }
                        {$addon_id} .list-nav .nav-left:after {
                            content: '';
                            width: 100%;
                            height: 2px;
                            background-color: {$list_nav_left_lineColor};
                            position: absolute;
                            left: 0;
                            bottom: -2px;
                        }
                        {$addon_id} .list-nav .nav-right {
                            display: flex;
                            align-items: center;
                            font-size: {$list_nav_right_f_md}px;
                            color: {$list_nav_right_Color};
                            padding: {$list_nav_right_p_md};
                        }
                        {$addon_id} .list-nav .nav-right span {
                            color: {$list_nav_right_t_Color};
                        }
                        {$addon_id} .list-nav .nav-right .nav-icon {
                            width: auto;
                            height: 100%;
                            margin-right: 4px;
                        }
                        @media (min-width: 768px) and (max-width: 991px) {
                            {$addon_id} .jwpf-tab-content .list-nav {
                                border-width: {$list_nav_border_h_sm}px;
                                margin-bottom: {$list_nav_mb_sm}px;
                                height: {$list_nav_h_sm}px;
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-left {
                                font-size: {$list_nav_left_title_f_sm}px;
                                line-height: {$list_nav_left_title_h_sm}px;
                                padding: {$list_nav_left_p_sm};
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-left:before {
                                height: {$list_nav_left_title_f_sm}px;
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-right {
                                font-size: {$list_nav_right_f_sm}px;
                                padding: {$list_nav_right_p_sm};
                            }
                        }
                        @media (max-width: 767px) {
                            {$addon_id} .jwpf-tab-content .list-nav {
                                border-width: {$list_nav_border_h_xs}px;
                                margin-bottom: {$list_nav_mb_xs}px;
                                height: {$list_nav_h_xs}px;
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-left {
                                font-size: {$list_nav_left_title_f_xs}px;
                                line-height: {$list_nav_left_title_h_xs}px;
                                padding: {$list_nav_left_p_xs};
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-left:before {
                                height: {$list_nav_left_title_f_xs}px;
                            }
                            {$addon_id} .jwpf-tab-content .list-nav .nav-right {
                                font-size: {$list_nav_right_f_xs}px;
                                padding: {$list_nav_right_p_xs};
                            }
                        }
                    </style>
                ";
                $output_nav .= '<div class="list-nav">
                    <div class="nav-left">';
                if ($list_nav_left_img) {
                    $output_nav .= '<img class="nav-icon" src="' . $list_nav_left_img . '" alt="">&nbsp;&nbsp;';
                }
                $output_nav .= $title . '
                    </div>
                    <div class="nav-right">';
                if ($list_nav_right_img && $hidden_list_nav_right_img != 1) {
                    $output_nav .= '<img class="nav-icon" src="' . $list_nav_right_img . '" alt="">';
                }
                $output_nav .= '
                        <p class="nav">首页 ' . $list_nav_right_s . ' ' . $list_nav_name . $list_nav_right_s . ' &nbsp;<span>' . $title . '</span></p>
                    </div>
                </div>';
            }
        }
        return $output_nav;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $nav_bg_img = (isset($settings->nav_bg_img) && $settings->nav_bg_img) ? $settings->nav_bg_img : '';
        $nav_width_phone = (isset($settings->nav_width_phone) && $settings->nav_width_phone) ? $settings->nav_width_phone : 20;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';
        $nav_color2 = (isset($settings->nav_color2) && $settings->nav_color2) ? 'color: ' . $settings->nav_color2 . '!important;' : '';
        $nav_color2 .= (isset($settings->nav_width2) && $settings->nav_width2) ? 'width: 100%;' : '';
        $nav_bg_color2 = (isset($settings->nav_bg_color2) && $settings->nav_bg_color2) ? 'background-color: ' . $settings->nav_bg_color2 . '!important;' : '';

        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';

        $tab_style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $style = '';
        $style .= (isset($settings->active_tab_color) && $settings->active_tab_color) ? 'color: ' . $settings->active_tab_color . ';' : '';
        $style .= (isset($settings->active_tab_border_width) && trim($settings->active_tab_border_width)) ? 'border-width: ' . $settings->active_tab_border_width . ';border-style: solid;' : '';
        $style .= (isset($settings->active_tab_border_color) && $settings->active_tab_border_color) ? 'border-color: ' . $settings->active_tab_border_color . ';' : '';
        $style2 = '';
        $style2 .= (isset($settings->active_tab_color2) && $settings->active_tab_color2) ? 'color: ' . $settings->active_tab_color2 . ';' : '';
        $style2 .= (isset($settings->active_tab_border_width2) && trim($settings->active_tab_border_width2)) ? 'border-width: ' . $settings->active_tab_border_width2 . ';border-style: solid;' : '';
        $style2 .= (isset($settings->active_tab_border_color2) && $settings->active_tab_border_color2) ? 'border-color: ' . $settings->active_tab_border_color2 . ';' : '';

        //Font style
        $font_style = '';
        $font_style .= (isset($settings->nav_fontsize) && $settings->nav_fontsize) ? 'font-size: ' . $settings->nav_fontsize . 'px;' : '';
        $font_style .= (isset($settings->nav_lineheight) && $settings->nav_lineheight) ? 'line-height: ' . $settings->nav_lineheight . 'px;' : '';
        //二级导航Font style
        $font_style2 = '';
        $font_style2 .= (isset($settings->nav_fontsize2) && $settings->nav_fontsize2) ? 'font-size: ' . $settings->nav_fontsize2 . 'px;' : '';
        $font_style2 .= (isset($settings->nav_lineheight2) && $settings->nav_lineheight2) ? 'line-height: ' . $settings->nav_lineheight2 . 'px;' : '';
        $font_style2 .= (isset($settings->nav_lineheight2) && $settings->nav_lineheight2) ? 'height: ' . $settings->nav_lineheight2 . 'px;' : '';

        //Font style object
        $nav_font_style = (isset($settings->nav_font_style) && $settings->nav_font_style) ? $settings->nav_font_style : '';
        $fontStyle = '';
        if (isset($nav_font_style->underline) && $nav_font_style->underline) {
            $fontStyle .= 'text-decoration:underline;';
        }
        if (isset($nav_font_style->italic) && $nav_font_style->italic) {
            $fontStyle .= 'font-style:italic;';
        }
        if (isset($nav_font_style->uppercase) && $nav_font_style->uppercase) {
            $fontStyle .= 'text-transform:uppercase;';
        }
        if (isset($nav_font_style->weight) && $nav_font_style->weight) {
            $fontStyle .= 'font-weight:' . $nav_font_style->weight . ';';
        }
        $nav_border = (isset($settings->nav_border) && trim($settings->nav_border)) ? $settings->nav_border : '';
        if (strpos($nav_border, 'px')) {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border-width: ' . $settings->nav_border . ';border-style:solid;' : '';
        } else {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border: ' . $settings->nav_border . 'px solid;' : '';
        }
        $font_style .= (isset($settings->nav_border_color) && $settings->nav_border_color) ? 'border-color: ' . $settings->nav_border_color . ';' : '';
        $font_style .= (isset($settings->nav_color) && $settings->nav_color) ? 'color: ' . $settings->nav_color . ';' : '';
        $font_style .= (isset($settings->nav_bg_color) && $settings->nav_bg_color) ? 'background-color: ' . $settings->nav_bg_color . ';' : '';
        $font_style .= (isset($settings->nav_border_radius) && $settings->nav_border_radius) ? 'border-radius: ' . $settings->nav_border_radius . 'px;' : '';
        $font_style .= (isset($settings->nav_padding) && trim($settings->nav_padding)) ? 'padding: ' . $settings->nav_padding . ';' : '';

        $font_style_sm = (isset($settings->nav_fontsize_sm) && $settings->nav_fontsize_sm) ? 'font-size: ' . $settings->nav_fontsize_sm . 'px;' : '';
        $font_style_sm .= (isset($settings->nav_padding_sm) && trim($settings->nav_padding_sm)) ? 'padding: ' . $settings->nav_padding_sm . ';' : '';
        $font_style_sm .= (isset($settings->nav_lineheight_sm) && $settings->nav_lineheight_sm) ? 'line-height: ' . $settings->nav_lineheight_sm . 'px;' : '';

        $font_style_xs = (isset($settings->nav_fontsize_xs) && $settings->nav_fontsize_xs) ? 'font-size: ' . $settings->nav_fontsize_xs . 'px;' : '';
        $font_style_xs .= (isset($settings->nav_padding_xs) && trim($settings->nav_padding_xs)) ? 'padding: ' . $settings->nav_padding_xs . ';' : '';
        $font_style_xs .= (isset($settings->nav_lineheight_xs) && $settings->nav_lineheight_xs) ? 'line-height: ' . $settings->nav_lineheight_xs . 'px;' : '';

        //Nav Width
        $nav_width = (isset($settings->nav_width) && $settings->nav_width) ? $settings->nav_width : 30;
        $nav_width_sm = (isset($settings->nav_width_sm) && $settings->nav_width_sm) ? $settings->nav_width_sm : 30;
        $nav_width_xs = (isset($settings->nav_width_xs) && $settings->nav_width_xs) ? $settings->nav_width_xs : 30;
        //二级导航width
        $nav_width2 = (isset($settings->nav_width2) && $settings->nav_width2) ? $settings->nav_width2 : 100;
        $nav_width2_sm = (isset($settings->nav_width2_sm) && $settings->nav_width2_sm) ? $settings->nav_width2_sm : $nav_width2;
        $nav_width2_xs = (isset($settings->nav_width2_xs) && $settings->nav_width2_xs) ? $settings->nav_width2_xs : $nav_width2;

        //        字体背景高度
        //        字体背景高度
        $pro_font_color_bg_height_sm = (isset($settings->pro_font_color_bg_height) && $settings->pro_font_color_bg_height) ? $settings->pro_font_color_bg_height : 40;
        $pro_font_color_bg_height_xs = (isset($settings->pro_font_color_bg_height) && $settings->pro_font_color_bg_height) ? $settings->pro_font_color_bg_height : 40;
        //Nav Margin
        $nav_margin = (isset($settings->nav_margin) && trim($settings->nav_margin)) ? 'padding: ' . $settings->nav_margin . ';' : 'padding: 0px 0px 5px 0px;';
        $nav_margin_kj = explode(" ", $nav_margin);
        $nav_margin_sm = (isset($settings->nav_margin_sm) && trim($settings->nav_margin_sm)) ? 'padding: ' . $settings->nav_margin_sm . ';' : '';
        $nav_margin_xs = (isset($settings->nav_margin_xs) && trim($settings->nav_margin_xs)) ? 'padding: ' . $settings->nav_margin_xs . ';' : '';
        //Nav Margin
        $nav_margin2 = (isset($settings->nav_margin2) && trim($settings->nav_margin2)) ? 'padding: ' . $settings->nav_margin2 . ';' : 'padding: 0px 0px 5px 0px;';
        $nav_margin2_sm = (isset($settings->nav_margin2_sm) && trim($settings->nav_margin2_sm)) ? 'padding: ' . $settings->nav_margin2_sm . ';' : '';
        $nav_margin2_xs = (isset($settings->nav_margin2_xs) && trim($settings->nav_margin2_xs)) ? 'padding: ' . $settings->nav_margin2_xs . ';' : '';

        //在导航和内容之间的空间
        $nav_gutter = (isset($settings->nav_gutter)) ? $settings->nav_gutter : '';

        //Font style object
        $nav_font_style2 = (isset($settings->nav_font_style2) && $settings->nav_font_style2) ? $settings->nav_font_style2 : '';

        if (isset($nav_font_style2->underline) && $nav_font_style2->underline) {
            $font_style2 .= 'text-decoration:underline;';
        }
        if (isset($nav_font_style2->italic) && $nav_font_style2->italic) {
            $font_style2 .= 'font-style:italic;';
        }
        if (isset($nav_font_style2->uppercase) && $nav_font_style2->uppercase) {
            $font_style2 .= 'text-transform:uppercase;';
        }
        if (isset($nav_font_style2->weight) && $nav_font_style2->weight) {
            $font_style2 .= 'font-weight:' . $nav_font_style2->weight . ';';
        }
        $nav_border2 = (isset($settings->nav_border2) && trim($settings->nav_border2)) ? $settings->nav_border2 : '';
        if (strpos($nav_border2, 'px')) {
            $font_style2 .= (isset($settings->nav_border2) && trim($settings->nav_border2)) ? 'border-width: ' . $settings->nav_border2 . ';border-style:solid;' : '';
        } else {
            $font_style2 .= (isset($settings->nav_border2) && trim($settings->nav_border2)) ? 'border: ' . $settings->nav_border2 . 'px solid;' : '';
        }
        $font_style2 .= (isset($settings->nav_border_color2) && $settings->nav_border_color2) ? 'border-color: ' . $settings->nav_border_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_color2) && $settings->nav_color2) ? 'color: ' . $settings->nav_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_bg_color2) && $settings->nav_bg_color2) ? 'background-color: ' . $settings->nav_bg_color2 . ';' : '';
        $font_style2 .= (isset($settings->nav_border_radius2)) ? 'border-radius: ' . $settings->nav_border_radius2 . 'px;' : '';
        $font_style2 .= (isset($settings->nav_padding2) && trim($settings->nav_padding2)) ? 'padding: ' . $settings->nav_padding2 . ';' : '';

        $font_style2_sm = (isset($settings->nav_fontsize2_sm) && $settings->nav_fontsize2_sm) ? 'font-size: ' . $settings->nav_fontsize2_sm . 'px;' : '';
        $font_style2_sm .= (isset($settings->nav_padding2_sm) && trim($settings->nav_padding2_sm)) ? 'padding: ' . $settings->nav_padding2_sm . ';' : '';
        $font_style2_sm .= (isset($settings->nav_lineheight2_sm) && $settings->nav_lineheight2_sm) ? 'line-height: ' . $settings->nav_lineheight2_sm . 'px;' : '';

        $font_style2_xs = (isset($settings->nav_fontsize2_xs) && $settings->nav_fontsize2_xs) ? 'font-size: ' . $settings->nav_fontsize2_xs . 'px;' : '';
        $font_style2_xs .= (isset($settings->nav_padding2_xs) && trim($settings->nav_padding2_xs)) ? 'padding: ' . $settings->nav_padding2_xs . ';' : '';
        $font_style2_xs .= (isset($settings->nav_lineheight2_xs) && $settings->nav_lineheight2_xs) ? 'line-height: ' . $settings->nav_lineheight2_xs . 'px;' : '';

        //Nav Gutter
        if ($nav_position == 'nav-right') {
            $nav_gutter_right = isset($settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_sm = isset($settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_xs = isset($settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';

            $nav_gutter_left = isset($settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_sm = isset($settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_xs = isset($settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';
        } else {
            $nav_gutter_right = isset($settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_sm = isset($settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_xs = isset($settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';

            $nav_gutter_left = isset($settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_sm = isset($settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_xs = isset($settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';
        }

        //Content Style
        $content_style = '';
        $content_style .= (isset($settings->content_backround) && $settings->content_backround) ? 'background-color: ' . $settings->content_backround . ';' : '';
        $content_style .= (isset($settings->content_border) && $settings->content_border) ? 'border: ' . $settings->content_border . 'px solid;' : '';
        $content_style .= (isset($settings->content_color) && $settings->content_color) ? 'color: ' . $settings->content_color . ';' : '';
        $content_style .= (isset($settings->content_border_color) && $settings->content_border_color) ? 'border-color: ' . $settings->content_border_color . ';' : '';
        $content_style .= (isset($settings->content_border_radius) && $settings->content_border_radius) ? 'border-radius: ' . $settings->content_border_radius . 'px;' : '';
        $content_style .= (isset($settings->content_margin) && trim($settings->content_margin)) ? 'margin: ' . $settings->content_margin . ';' : '';
        $content_style .= (isset($settings->content_padding) && trim($settings->content_padding)) ? 'padding: ' . $settings->content_padding . ';' : '';
        $content_style .= (isset($settings->content_lineheight) && $settings->content_lineheight) ? 'line-height: ' . $settings->content_lineheight . 'px;' : '';
        //Font style object
        $content_font_style = (isset($settings->content_font_style) && $settings->content_font_style) ? $settings->content_font_style : '';
        if (isset($content_font_style->underline) && $content_font_style->underline) {
            $content_style .= 'text-decoration:underline;';
        }
        if (isset($content_font_style->italic) && $content_font_style->italic) {
            $content_style .= 'font-style:italic;';
        }
        if (isset($content_font_style->uppercase) && $content_font_style->uppercase) {
            $content_style .= 'text-transform:uppercase;';
        }
        if (isset($content_font_style->weight) && $content_font_style->weight) {
            $content_style .= 'font-weight:' . $content_font_style->weight . ';';
        }
        //Content tablet style
        $content_style_sm = (isset($settings->content_margin_sm) && trim($settings->content_margin_sm)) ? 'margin: ' . $settings->content_margin_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_padding_sm) && $settings->content_padding_sm) ? 'padding: ' . $settings->content_padding_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_lineheight_sm) && $settings->content_lineheight_sm) ? 'line-height: ' . $settings->content_lineheight_sm . 'px;' : '';

        //Content Mobile style
        $content_style_xs = (isset($settings->content_margin_xs) && trim($settings->content_margin_xs)) ? 'margin: ' . $settings->content_margin_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_padding_xs) && $settings->content_padding_xs) ? 'padding: ' . $settings->content_padding_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_lineheight_xs) && $settings->content_lineheight_xs) ? 'line-height: ' . $settings->content_lineheight_xs . 'px;' : '';
        //Box shadow
        $show_boxshadow = (isset($settings->show_boxshadow) && $settings->show_boxshadow) ? $settings->show_boxshadow : '';
        $box_shadow = '';
        if ($show_boxshadow) {
            $box_shadow .= (isset($settings->shadow_horizontal) && $settings->shadow_horizontal) ? $settings->shadow_horizontal . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_vertical) && $settings->shadow_vertical) ? $settings->shadow_vertical . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_blur) && $settings->shadow_blur) ? $settings->shadow_blur . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_spread) && $settings->shadow_spread) ? $settings->shadow_spread . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_color) && $settings->shadow_color) ? $settings->shadow_color : 'rgba(0, 0, 0, .5)';
        }
        //Icon Style
        $icon_style = '';
        $icon_style .= (isset($settings->icon_fontsize) && $settings->icon_fontsize) ? 'font-size: ' . $settings->icon_fontsize . 'px;' : '';
        $icon_style .= (isset($settings->icon_margin) && trim($settings->icon_margin)) ? 'margin: ' . $settings->icon_margin . ';' : '';
        $icon_style .= (isset($settings->icon_color) && $settings->icon_color) ? 'color: ' . $settings->icon_color . ';' : '';

        $icon_style_sm = (isset($settings->icon_fontsize_sm) && $settings->icon_fontsize_sm) ? 'font-size: ' . $settings->icon_fontsize_sm . 'px;' : '';
        $icon_style_sm .= (isset($settings->icon_margin_sm) && trim($settings->icon_margin_sm)) ? 'margin: ' . $settings->icon_margin_sm . ';' : '';

        $icon_style_xs = (isset($settings->icon_fontsize_xs) && $settings->icon_fontsize_xs) ? 'font-size: ' . $settings->icon_fontsize_xs . 'px;' : '';
        $icon_style_xs .= (isset($settings->icon_margin_xs) && trim($settings->icon_margin_xs)) ? 'margin: ' . $settings->icon_margin_xs . ';' : '';

        //Image Style
        $image_style = '';
        $image_style .= (isset($settings->image_height) && $settings->image_height) ? 'height: ' . $settings->image_height . 'px;' : '';
        $image_style .= (isset($settings->image_width) && $settings->image_width) ? 'width: ' . $settings->image_width . 'px;' : '';
        $image_style .= (isset($settings->image_margin) && trim($settings->image_margin)) ? 'margin: ' . $settings->image_margin . ';' : '';

        $image_style_sm = '';
        $image_style_sm .= (isset($settings->image_height_sm) && $settings->image_height_sm) ? 'height: ' . $settings->image_height_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_width_sm) && $settings->image_width_sm) ? 'width: ' . $settings->image_width_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_margin_sm) && trim($settings->image_margin_sm)) ? 'margin: ' . $settings->image_margin_sm . ';' : '';

        $image_style_xs = '';
        $image_style_xs .= (isset($settings->image_height_xs) && $settings->image_height_xs) ? 'height: ' . $settings->image_height_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_width_xs) && $settings->image_width_xs) ? 'width: ' . $settings->image_width_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_margin_xs) && trim($settings->image_margin_xs)) ? 'margin: ' . $settings->image_margin_xs . ';' : '';

        // min start
        $nav_block_positon = (isset($settings->nav_block_positon) && $settings->nav_block_positon) ? $settings->nav_block_positon . ';' : '';
        $nav_font_position = (isset($settings->nav_font_position) && $settings->nav_font_position) ? $settings->nav_font_position : 'flex-start';
        $nav_left_position = (isset($settings->nav_left_position) && $settings->nav_left_position) ? $settings->nav_left_position : '0';

        // min end--------

        //Css output
        $css = '';

        /*标题新增样式*/
        // 标题背景颜色
        $title_bg_color = (isset($settings->title_bg_color) && $settings->title_bg_color) ? $settings->title_bg_color : '';
        //标题宽度
        $title_w = (isset($settings->title_w) && $settings->title_w) ? $settings->title_w : null;
        $title_w_sm = (isset($settings->title_w_sm) && $settings->title_w_sm) ? $settings->title_w_sm : null;
        $title_w_xs = (isset($settings->title_w_xs) && $settings->title_w_xs) ? $settings->title_w_xs : null;

        $css .= $addon_id . " .jwpf-addon-title {
            background-color: {$title_bg_color};
            width: {$title_w};
        }";
        $css .= '@media (min-width: 768px) and (max-width: 991px) {';
        $css .= $addon_id . " .jwpf-addon-title {
                width: {$title_w_sm};
            }";
        $css .= '}';
        $css .= '@media (max-width: 767px) {';
        $css .= $addon_id . " .jwpf-addon-title {
                width: {$title_w_xs};
            }";
        $css .= '}';

        $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
        $css .= 'display:block;';
        $css .= '}';
        if ($tab_style == 'pills') {
            $css .= '';
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            if ($style) {
//                $css .= $addon_id . ' .jwpf-nav-pills > li.active > a,' . $addon_id . ' .jwpf-nav-pills > li.active > a:hover,' . $addon_id . ' .jwpf-nav-pills > li.active > a:focus {';
                //                $css .= $style;
                //                $css .= '}';
                $css .= $addon_id . ' .jwpf-nav-pills > li.active  .page_n > a,' . $addon_id . ' .jwpf-nav-pills > li.active .page_n > a:hover,' . $addon_id . ' .jwpf-nav-pills > li.active .page_n > a:focus,' . $addon_id . ' .jwpf-nav-pills .page_n.active > a,' . $addon_id . ' .jwpf-nav-pills .page_n.active > a:hover,' . $addon_id . ' .jwpf-nav-pills .page_n.active > a:focus,' . $addon_id . ' .jwpf-nav-pills > li.active > a,
                    ' . $addon_id . ' .jwpf-nav-pills > li.active > .page_n > a,
                    ' . $addon_id . ' .jwpf-nav-pills .page_n.active > a{
//                    color: #333;
//                    background-color: #e5e5e5;
                        ' . $style . '
                }';
            }
        } else if ($tab_style == 'lines') {
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'border-bottom-color: ' . $settings->active_tab_bg . ';' : '';
            if ($style) {
//                $css .= $addon_id . ' .jwpf-nav-lines > li.active > a,' . $addon_id . ' .jwpf-nav-lines > li.active > a:hover,' . $addon_id . ' .jwpf-nav-lines > li.active > a:focus {';
                //                $css .= '.$style.';
                //                $css .= '}';

                $css .= $addon_id . ' .jwpf-nav-lines{
                    border-bottom: 2px solid #e5e5e5!important;
                }';
                $css .= $addon_id . ' .jwpf-nav-lines > li.active>.page_n > a,' . $addon_id . ' .jwpf-nav-lines > li.active .page_n > a:hover,' . $addon_id . ' .jwpf-nav-lines > li.active .page_n > a:focus,' . $addon_id . ' .jwpf-nav-lines .page_n.active > a,' . $addon_id . ' .jwpf-nav-lines .page_n.active > a:hover,' . $addon_id . ' .jwpf-nav-lines > li.active > a,' . $addon_id . ' .jwpf-nav-lines > li.active > .page_n > a,' . $addon_id . ' .jwpf-nav-lines .page_n.active > a{
                    border-bottom: #e5e5e5;
                    border-bottom: 2px solid ' . ($settings->active_tab_bg) . ';
                }';
            }
        } else if ($tab_style == 'custom') // 自定义样式
        {
            //Active Nav style
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            $style2 .= (isset($settings->active_tab_bg2) && $settings->active_tab_bg2) ? 'background-color: ' . $settings->active_tab_bg2 . ';' : '';

            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-custom li.product-list.active > a,' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:hover,' . $addon_id . ' .jwpf-nav-custom li.product-list.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
            if ($style2) {
                $css .= $addon_id . ' .jwpf-nav-custom .er_type li.product-list.active > a,' . $addon_id . ' .jwpf-nav-custom .er_type li.product-list.active > a:hover,' . $addon_id . ' .jwpf-nav-custom .er_type li.product-list.active > a:focus {';
                $css .= 'background-color: ' . $settings->active_tab_bg2 . ';';
                $css .= 'color: ' . $settings->active_tab_color2 . ';';
                $css .= 'border-width: ' . $settings->active_tab_border_width2 . ' ;';
                $css .= 'border-color: ' . $settings->active_tab_border_color2 . ' ;';
                $css .= 'border-style: solid ;';
                $css .= '}';
            }
            $css .= $addon_id . ' .jwpf-nav-custom {';
            $css .= 'width: ' . $nav_width . '%;';
            $css .= $nav_gutter_right;
            $css .= '}';
            $css .= $addon_id . ' .er_type{';
            $css .= 'width: ' . $nav_width2 . '%;';
            $css .= '}';
            $css .= $addon_id . ' .er_type li{
                width: 100%!important;
            }';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            $css .= 'width:' . (100 - $nav_width) . '%;';
            $css .= $nav_gutter_left;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style;
            $css .= 'box-shadow:' . $box_shadow . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
            $css .= $font_style;
            $css .= 'box-shadow:' . $box_shadow . ';';
            $css .= '}';
            $css .= '.jwpf-nav-custom>li>a,.jwpf-nav-custom>li>.page_n>a{
                ' . $fontStyle . '
            }';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type a {';
            $css .= $font_style2;
            $css .= $nav_color2;
            $css .= $nav_bg_color2;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li.product-list {';
            $css .= '' . $nav_margin_kj[0] . '' . $nav_margin_kj[1] . ' ' . $nav_margin_kj[2] . ' ' . $nav_margin_kj[3] . ' ' . $nav_margin_kj[4] . '';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style;
            $css .= '}';
            //Nav Hover Style
            $hover_style = '';
            $hover_style .= (isset($settings->hover_tab_color) && $settings->hover_tab_color) ? 'color: ' . $settings->hover_tab_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_border_width) && trim($settings->hover_tab_border_width)) ? 'border-width: ' . $settings->hover_tab_border_width . ';border-style: solid;' : '';
            $hover_style .= (isset($settings->hover_tab_border_color) && $settings->hover_tab_border_color) ? 'border-color: ' . $settings->hover_tab_border_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_bg) && $settings->hover_tab_bg) ? 'background-color: ' . $settings->hover_tab_bg . ';' : '';
            //Nav Hover Style
            $hover_style2 = '';
            $hover_style2 .= (isset($settings->hover_tab_color2) && $settings->hover_tab_color2) ? 'color: ' . $settings->hover_tab_color2 . ' !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_border_width2) && trim($settings->hover_tab_border_width2)) ? 'border-width: ' . $settings->hover_tab_border_width2 . ';border-style: solid !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_border_color2) && $settings->hover_tab_border_color2) ? 'border-color: ' . $settings->hover_tab_border_color2 . ' !important;' : '';
            $hover_style2 .= (isset($settings->hover_tab_bg2) && $settings->hover_tab_bg2) ? 'background-color: ' . $settings->hover_tab_bg2 . ' !important;' : '';

            if ($hover_style) {
                $css .= $addon_id . ' .jwpf-nav-custom li.product-list > a:hover,' . $addon_id . ' .jwpf-nav-custom li.product-list > a:focus {';
                $css .= $hover_style;
                $css .= '}';
            }
            if ($hover_style2) {
                $css .= $addon_id . ' .jwpf-nav-custom .er_type li.product-list a:hover,' . $addon_id . ' .jwpf-nav-custom .er_type li.product-list a:focus {';
                $css .= $hover_style2;
                $css .= '}';
            }

            //Icon hover and active color
            $icon_color_hover = (isset($settings->icon_color_hover) && $settings->icon_color_hover) ? 'color: ' . $settings->icon_color_hover . ';' : '';
            if ($icon_color_hover) {
                $css .= $addon_id . ' .jwpf-nav-custom li.product-list > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li.product-list > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_hover;
                $css .= '}';
            }
            $icon_color_active = (isset($settings->icon_color_active) && $settings->icon_color_active) ? 'color: ' . $settings->icon_color_active . ';' : '';
            if ($icon_color_active) {
                $css .= $addon_id . ' .jwpf-nav-custom li.active > a > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li.active > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom li.active > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_active;
                $css .= '}';
            }
        } else if ($tab_style == 'modern') {
            $css .= $addon_id . ' .jwpf-nav-modern > li.active  .page_n > a,' . $addon_id . ' .jwpf-nav-modern > li.active .page_n > a:focus,' . $addon_id . ' .jwpf-nav-modern .page_n.active > a,' . $addon_id . ' .jwpf-nav-modern .page_n.active > a:focus {
                    border-bottom-color: #fff;
                    background:#fff;
                }';
        } else if ($tab_style == 'tabs') {
            $css .= $addon_id . ' .jwpf-nav-tabs{
                border-bottom: 1px solid #e5e5e5!important;
            }';
            $css .= $addon_id . ' .jwpf-nav-tabs > li.active  .page_n > a,' . $addon_id . ' .jwpf-nav-tabs > li.active .page_n > a:focus,' . $addon_id . ' .jwpf-nav-tabs .page_n.active > a,' . $addon_id . ' .jwpf-nav-tabs .page_n.active > a:focus {
                    border-bottom-color: #fff;
                    background:#fff;
                }';
            $css .= $addon_id . ' .jwpf-nav-pills > li.active .page_n > a:hover,' . $addon_id . ' .jwpf-nav-pills .page_n.active > a:hover{
                    color: #f58505;
                    background-color: #eee;
            }';
        }
        if (!empty($font_style_sm) || !empty($pro_font_color_bg_height_sm) || !empty($nav_width_sm) || !empty($content_style_sm) || !empty($nav_margin_sm) || !empty($image_style_sm)) {
            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type{';
            if (!empty($nav_width2_sm)) {
                $css .= 'width: ' . $nav_width2_sm . '%;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_sm)) {
                $css .= 'width: ' . $nav_width_sm . '%;';
            }

            $css .= $nav_gutter_right_sm;
            $css .= '}';
            $css .= $addon_id . ' .pr_list_id .jwpf-article-info-wrap {';
            if (!empty($pro_font_color_bg_height_sm)) {
                $css .= 'height: ' . $pro_font_color_bg_height_sm . 'px;';
                $css .= 'line-height: ' . $pro_font_color_bg_height_sm . 'px;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_sm) && $nav_width_sm != 100) {
                $css .= 'width:' . (100 - $nav_width_sm) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
            $css .= $font_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_sm;
            $css .= '}';

            $css .= '}';
        }
        if (!empty($font_style_xs) || !empty($pro_font_color_bg_height_xs) || !empty($nav_width_xs) || !empty($content_style_xs) || !empty($nav_margin_xs) || !empty($image_style_xs)) {
            $css .= '@media (max-width: 767px) {';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type{';
            if (!empty($nav_width2_xs)) {
                $css .= 'width: ' . $nav_width2_xs . '%;';
            }
            $css .= 'position: static;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_phone)) {
                $css .= 'width: ' . $nav_width_phone . '%;';
            }
            $css .= $nav_gutter_right_xs;
            $css .= 'display: block;';
            $css .= '}';
            $css .= $addon_id . ' .pr_list_id .jwpf-article-info-wrap {';
            if (!empty($pro_font_color_bg_height_xs)) {
                $css .= 'height: ' . $pro_font_color_bg_height_xs . 'px;';
                $css .= 'line-height: ' . $pro_font_color_bg_height_xs . 'px;';
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_phone) && $nav_width_phone != 100) {
                $css .= 'width:' . (100 - $nav_width_phone) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
            $css .= $font_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .er_type li {';
            $css .= $nav_margin2_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_xs;
            $css .= '}';

            $css .= '}';
        }
        // 除自定义导航外的背景颜色 以及导航块的位置
        if ($tab_style != 'custom') {

            $style .= (isset($settings->nav_all_bg_color) && $settings->nav_all_bg_color) ? 'background-color: ' . $settings->nav_all_bg_color . ';' : '';
            $style .= (isset($settings->nav_all_positon) && $settings->nav_all_positon) ? 'justify-content: ' . $settings->nav_all_positon . ';' : '';
            $style .= (isset($settings->nav_bor_color) && $settings->nav_bor_color) ? 'border-color: ' . $settings->nav_bor_color . ';' : '';
            $nav_wrap = (isset($settings->nav_wrap) && $settings->nav_wrap) ? $settings->nav_wrap : '';
            $nav_gundong = (isset($settings->nav_gundong) && $settings->nav_gundong) ? $settings->nav_gundong : '';
            if ($style) {
                $css .= $addon_id . ' .minUl {';
                if ($nav_wrap != 0) {
                    //换行
                    $css .= 'display:block;';
                } else {
                    $css .= 'display:flex;';
                    if ($nav_gundong != 0) {
                        $css .= 'overflow: auto;';
                    }
                }

                $css .= $style;
                $css .= '}';
            }
        }
        $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
        $css .= 'width: 100%;';
        $css .= 'height: 100%;';
        $css .= 'display: flex;';
        $css .= 'justify-content: ' . $nav_font_position . ';';
        if ($nav_bg_img) {
            $css .= 'background: url(' . $nav_bg_img . ');';
            $css .= 'background-size: cover;';
        }
        $css .= 'align-items: center;';
        $css .= '}';
        if ($nav_position == 'nav-top') {
            $css .= $addon_id . ' .minUl {';
            $css .= 'flex-wrap:wrap;';
            //            $css .= 'width:' . $nav_width . '% !important;';
            $css .= 'width:100% !important;';
            if($nav_left_position){ $css.='padding-left:'.$nav_left_position.'px!important;'; }
            $css .= '}';

            $css .= $addon_id . ' .minBox {';
            $css .= 'flex-direction: column;';
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-nav-custom {';
            $css .= 'width:100%;display: flex;padding-right:0px;justify-content:' . $nav_block_positon;
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-tab-custom-content{';
            $css .= 'width:100%;padding-left:0px;';
            $css .= 'margin-top:' . $nav_gutter . 'px;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {
                    width:' . $nav_width . '%;
                ';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li .er_type{';
            $css .= 'position: absolute;';
            $css .= 'left: 0;';
            $css .= 'z-index: 99999;';
            $css .= 'max-width: 100%;';
            $css .= '}';

            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            $css .= $addon_id . ' .minBox .jwpf-nav-custom {';
            $css .= 'display: block;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li .er_type{';
            $css .= 'position: static;';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
            $css .= $font_style_sm;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'text-align: center;';
            $css .= 'justify-content: ' . $nav_font_position . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= 'width:' . $nav_width_sm . '%;';
            $css .= 'float: left';
            $css .= '}';
            $css .= '}';

            $css .= '@media (max-width: 768px) {';
            $css .= $addon_id . ' .jwpf-nav-custom .product-list a {';
            $css .= $font_style_xs;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            if($nav_font_position === 'flex-start'){
                $css .= 'text-align: left;';
            }elseif($nav_font_position === 'center'){
                $css .= 'text-align: center;';
            }elseif($nav_font_position === 'flex-end'){
                $css .= 'text-align: right;';
            }
            // $css .= 'text-align: center;';
            $css .= 'justify-content: ' . $nav_font_position . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= 'float: left;';
            $css .= 'width:' . $nav_width_phone . '%;';
            $css .= '}';
            $css .= '}';

        }

        $css .= $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
        $page2_tab_fontcolor = (isset($settings->page2_tab_fontcolor)) ? $settings->page2_tab_fontcolor : '#000';
        $page2_tab_bordercolor = (isset($settings->page2_tab_bordercolor)) ? $settings->page2_tab_bordercolor : '#2a68a7';
        $page2_tab_bgcolor = (isset($settings->page2_tab_bgcolor)) ? $settings->page2_tab_bgcolor : '#ffffff';

        $page2_tab_cur_bgcolor = (isset($settings->page2_tab_cur_bgcolor)) ? $settings->page2_tab_cur_bgcolor : '#ffffff';
        $page2_tab_cur_fontcolor = (isset($settings->page2_tab_cur_fontcolor)) ? $settings->page2_tab_cur_fontcolor : '#000';
        $page2_tab_cur_bordercolor = (isset($settings->page2_tab_cur_bordercolor)) ? $settings->page2_tab_cur_bordercolor : '#2a68a7';

        $page2_tab_bgcolor_hov = (isset($settings->page2_tab_bgcolor_hov)) ? $settings->page2_tab_bgcolor_hov : '#ffffff';
        $page2_tab_fontcolor_hov = (isset($settings->page2_tab_fontcolor_hov)) ? $settings->page2_tab_fontcolor_hov : '#000';
        $page2_tab_bordercolor_hov = (isset($settings->page2_tab_bordercolor_hov)) ? $settings->page2_tab_bordercolor_hov : '#000';

        $show_page_col = (isset($settings->show_page_col)) ? $settings->show_page_col : 0;

        if ($show_page_col == 1) {
            $css .= "
                /* 省略号翻页样式 */
                    {$addon_id} .page_plug{
                    margin: 0 auto;
                }
                {$addon_id} .page_plug a.current {
                    background: {$page2_tab_cur_bgcolor};
                    color: {$page2_tab_cur_fontcolor};
                    border: 1px solid {$page2_tab_cur_bordercolor};
                    width: 30px;
                    height: 30px;
                    line-height: 24px;
                }
                {$addon_id} .page_plug a:hover{
                background: {$page2_tab_bgcolor_hov};
                color:{$page2_tab_fontcolor_hov};
                border: 1px solid {$page2_tab_bordercolor_hov};
                }
                {$addon_id} .page_num {
                    color:{$page2_tab_fontcolor};
                    border: 1px solid {$page2_tab_bordercolor};
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    background:{$page2_tab_bgcolor};
                }


        ";
        } else {
            $css .= "
                /* 省略号翻页样式 */
                    {$addon_id} .page_plug{
                    margin: 0 auto;
                }
                {$addon_id} .page_plug a.current {
                    background: #2a68a7;
                    color: #fff;
                    border: 1px solid #2a68a7;
                    width: 30px;
                    height: 30px;
                    line-height: 24px;
                }
                {$addon_id} .page_plug a:hover{
                    background: #2a68a7;
                    color: #fff;
                    border: 1px solid #2a68a7;
                }
                {$addon_id} .page_num {
                    color:#000;
                    border: 1px solid #2a68a7;
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    background:#fff;
                }


        ";
        }
        // 内容样式部分
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';

        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        $columns_xs = (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : 2;

        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;
        $img_style_type = (isset($settings->img_style_type)) ? $settings->img_style_type : 'fill';

        // 布局5
        if ($pro_type == 'type5') {
            $type5_fontsize = (isset($settings->type5_fontsize) && $settings->type5_fontsize) ? $settings->type5_fontsize : 16;
            $type5_fontColor = (isset($settings->type5_fontColor) && $settings->type5_fontColor) ? $settings->type5_fontColor : '#333';
            $type5_title_linHeight = (isset($settings->type5_title_linHeight) && $settings->type5_title_linHeight) ? $settings->type5_title_linHeight : 68;
            $type5_title_bgColor = (isset($settings->type5_title_bgColor) && $settings->type5_title_bgColor) ? $settings->type5_title_bgColor : '#fff';
            $type5_more_bgColor = (isset($settings->type5_more_bgColor) && $settings->type5_more_bgColor) ? $settings->type5_more_bgColor : '#999999';
            $type5_more_color = (isset($settings->type5_more_color) && $settings->type5_more_color) ? $settings->type5_more_color : '#fff';
            $type5_more_bgWidth = (isset($settings->type5_more_bgWidth) && $settings->type5_more_bgWidth) ? $settings->type5_more_bgWidth : 36;
            $type5_more_fontsize = (isset($settings->type5_more_fontsize) && $settings->type5_more_fontsize) ? $settings->type5_more_fontsize : 27;
            $type5_more_right = (isset($settings->type5_more_right) && $settings->type5_more_right) ? $settings->type5_more_right : 0;
            $type5_more_bottom = (isset($settings->type5_more_bottom) && $settings->type5_more_bottom) ? $settings->type5_more_bottom : -19;
            $type5_hover_bg = (isset($settings->type5_hover_bg) && $settings->type5_hover_bg) ? $settings->type5_hover_bg : 'rgba(31, 53, 241, 0.7)';
            $type5_fontsize_hover = (isset($settings->type5_fontsize_hover) && $settings->type5_fontsize_hover) ? $settings->type5_fontsize_hover : 20;
            $type5_fontColor_hover = (isset($settings->type5_fontColor_hover) && $settings->type5_fontColor_hover) ? $settings->type5_fontColor_hover : '#fff';
            $type5_title_linHeight_hover = (isset($settings->type5_title_linHeight_hover) && $settings->type5_title_linHeight_hover) ? $settings->type5_title_linHeight_hover : 52;
            $type5_lineWidth_hover = (isset($settings->type5_lineWidth_hover) && $settings->type5_lineWidth_hover) ? $settings->type5_lineWidth_hover : 37;
            $type5_lineHeight_hover = (isset($settings->type5_lineHeight_hover) && $settings->type5_lineHeight_hover) ? $settings->type5_lineHeight_hover : 2;
            $type5_lineColor_hover = (isset($settings->type5_lineColor_hover) && $settings->type5_lineColor_hover) ? $settings->type5_lineColor_hover : '#fff';
            $type5_more_bgColor_hover = (isset($settings->type5_more_bgColor_hover) && $settings->type5_more_bgColor_hover) ? $settings->type5_more_bgColor_hover : '#be1d2c';
            $type5_more_color_hover = (isset($settings->type5_more_color_hover) && $settings->type5_more_color_hover) ? $settings->type5_more_color_hover : '#fff';
            $type5_item_bgColor = (isset($settings->type5_item_bgColor) && $settings->type5_item_bgColor) ? $settings->type5_item_bgColor : '#f2f2f2';

            $type5_item_mb_md = (isset($settings->type5_item_mb) && is_numeric($settings->type5_item_mb)) ? $settings->type5_item_mb : 50;
            $type5_item_mb_sm = (isset($settings->type5_item_mb_sm) && $settings->type5_item_mb_sm) ? $settings->type5_item_mb_sm : '';
            $type5_item_mb_xs = (isset($settings->type5_item_mb_xs) && $settings->type5_item_mb_xs) ? $settings->type5_item_mb_xs : '';

            $type5_item_border_color = (isset($settings->type5_item_border_color) && $settings->type5_item_border_color) ? $settings->type5_item_border_color : '#ededed';
            $type5_item_border_style = (isset($settings->type5_item_border_style) && $settings->type5_item_border_style) ? $settings->type5_item_border_style : 'solid';

            $type5_item_border_width_md = (isset($settings->type5_item_border_width) && $settings->type5_item_border_width) ? $settings->type5_item_border_width : '1px';
            $type5_item_border_width_sm = (isset($settings->type5_item_border_width_sm) && $settings->type5_item_border_width_sm) ? $settings->type5_item_border_width_sm : '';
            $type5_item_border_width_xs = (isset($settings->type5_item_border_width_xs) && $settings->type5_item_border_width_xs) ? $settings->type5_item_border_width_xs : '';

            $type5_item_img_border_color = (isset($settings->type5_item_img_border_color) && $settings->type5_item_img_border_color) ? $settings->type5_item_img_border_color : '';
            $type5_item_img_border_style = (isset($settings->type5_item_img_border_style) && $settings->type5_item_img_border_style) ? $settings->type5_item_img_border_style : '';

            $type5_item_img_border_width_md = (isset($settings->type5_item_img_border_width) && $settings->type5_item_img_border_width) ? $settings->type5_item_img_border_width : '';
            $type5_item_img_border_width_sm = (isset($settings->type5_item_img_border_width_sm) && $settings->type5_item_img_border_width_sm) ? $settings->type5_item_img_border_width_sm : '';
            $type5_item_img_border_width_xs = (isset($settings->type5_item_img_border_width_xs) && $settings->type5_item_img_border_width_xs) ? $settings->type5_item_img_border_width_xs : '';

            $type5_item_img_p_md = (isset($settings->type5_item_img_p) && $settings->type5_item_img_p) ? $settings->type5_item_img_p : '';
            $type5_item_img_p_sm = (isset($settings->type5_item_img_p_sm) && $settings->type5_item_img_p_sm) ? $settings->type5_item_img_p_sm : '';
            $type5_item_img_p_xs = (isset($settings->type5_item_img_p_xs) && $settings->type5_item_img_p_xs) ? $settings->type5_item_img_p_xs : '';

            $type5_item_img_scale = (isset($settings->type5_item_img_scale) && $settings->type5_item_img_scale) ? $settings->type5_item_img_scale : 0;
            $type5_item_even = (isset($settings->type5_item_even) && $settings->type5_item_even) ? $settings->type5_item_even : 0;

            $css .= "
                {$addon_id} .pro05 {
                    display: flex;
                    flex-wrap: wrap;
                }
                {$addon_id} .pro05 .pro-item {";
                    if ($type5_item_even == 1) {
                    $css .= "
                        width: calc(100% / {$columns} - " . (number_format($columns) + 1) * 20 / $columns . "px);
                        margin-left: 20px;";
                    } else {
                        $css .= "width: calc(100% / {$columns});
                        padding: 0 20px;";
                    }
                    $css .= "display: inline-block;
                    margin-bottom: {$type5_item_mb_md}px;
                }
                {$addon_id} .pro05 .pro-item a {
                    display: block;
                    position: relative;
                    /*border: solid 1px #ededed;*/
                    border-style: {$type5_item_border_style};
                    border-color: {$type5_item_border_color};
                    border-width: {$type5_item_border_width_md};
                    width: 100%;
                    height: 100%;
                }
                {$addon_id} .pro05 .pro-item a::before {
                    content: '';
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 7px;
                    left: -5px;
                    background: {$type5_item_bgColor};
                    z-index: -1;
                }
                {$addon_id} .pro05 .pro-item .img-box {
                    width: 100%;
                    overflow: hidden;
                    border-style: {$type5_item_img_border_style};
                    border-color: {$type5_item_img_border_color};
                    border-width: {$type5_item_img_border_width_md};
                    padding: {$type5_item_img_p_md};
                }
                {$addon_id} .pro05 .pro-item .img-box img {
                    transition: all 0.6s;
                }";
                if ($fix_img_height == 1) {
                    $css .= "{$addon_id} .pro05 .pro-item .img-box {
                        height: {$fix_img_height_input}px;
                    }
                    {$addon_id} .pro05 .pro-item .img-box img {
                        object-fit: {$img_style_type};
                        width: 100%;
                        height: 100%;
                    }";
                }
                $css .= "{$addon_id} .pro05 .pro-item .con {
                    line-height: {$type5_title_linHeight}px;
                    text-align: center;
                    background-color: {$type5_title_bgColor};
                    font-size: {$type5_fontsize}px;
                    color: {$type5_fontColor};
                    padding: 0 10px;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
                {$addon_id} .pro05 .pro-item .liHoverBox {
                    width: 100%;
                    height: 0;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    background: {$type5_hover_bg};
                    padding: 0 50px;
                    transition: all 0.6s;
                    z-index: -1;
                    overflow: hidden;
                }
                {$addon_id} .pro05 .pro-item .liHoverBox .hoverCon {
                    padding: 60% 0 0;
                    line-height: {$type5_title_linHeight_hover}px;
                    font-size: {$type5_fontsize_hover}px;
                    color: {$type5_fontColor_hover};
                    text-align: center;
                    position: relative;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                }
                {$addon_id} .pro05 .pro-item .liHoverBox .line {
                    width: {$type5_lineWidth_hover}px;
                    height: {$type5_lineHeight_hover}px;
                    background-color: {$type5_lineColor_hover};
                    margin: auto;
                }
                {$addon_id} .pro05 .pro-item:hover .liHoverBox {
                    height: 100%;
                    z-index: 1;
                }
                {$addon_id} .pro05 .pro-item .icon {
                    width: {$type5_more_bgWidth}px;
                    height: {$type5_more_bgWidth}px;
                    line-height: {$type5_more_bgWidth}px;
                    background: {$type5_more_bgColor};
                    border-radius: 100%;
                    display: block;
                    position: absolute;
                    right: {$type5_more_right}px;
                    bottom: {$type5_more_bottom}px;
                    font-size: {$type5_more_fontsize}px;
                    color: {$type5_more_color};
                    text-align: center;
                    z-index: 3;
                    transition: all 0.6s;
                }
                {$addon_id} .pro05 .pro-item:hover .icon {
                    background: {$type5_more_bgColor_hover};
                    color: {$type5_more_color_hover};
                }
                {{ addonId }} .pro05 .pro-item:hover .img-box img {";
                    if ($type5_item_img_scale == 1) {
                        $css .= "transform: scale(1.3);";
                    }
                $css .= "}
                @media (min-width: 768px) and (max-width: 991px) {
                    {$addon_id} .pro05 .pro-item {
                        margin-bottom: {$type5_item_mb_sm}px;
                    }
                    {$addon_id} .pro05 .pro-item a {
                        border-width: {$type5_item_border_width_sm};
                    }
                    {$addon_id} .pro05 .pro-item .img-box {
                        border-width: {$type5_item_img_border_width_sm};
                        padding: {$type5_item_img_p_sm};
                    }
                }
                @media (max-width: 767px) {
                    {$addon_id} .pro05 .pro-item {
                        width: calc(100% / {$columns_xs});
                        margin-bottom: {$type5_item_mb_xs}px;
                    }
                    {$addon_id} .pro05 .pro-item a {
                        border-width: {$type5_item_border_width_xs};
                    }
                    {$addon_id} .pro05 .pro-item .img-box {
                        border-width: {$type5_item_img_border_width_xs};
                        padding: {$type5_item_img_p_xs};
                    }";
                    if ($fix_img_height == 1) {
                        $css .= "{$addon_id} .pro05 .pro-item .img-box {
                            height: {$fix_img_height_input_m}px;
                        }";
                    }
                $css .= "}
            ";
        }
        // 布局9
        if ($pro_type == 'type9') {
            $item_m = 28;
            $item_w = $item_m * ($columns - 1) / $columns;
            $item_w_xs = $item_m * ($columns_xs - 1) / $columns_xs;

            // 正常
            $type9_border_color = isset($settings->type9_border_color) && $settings->type9_border_color ? $settings->type9_border_color : '#2f5496';
            $type9_title_color = isset($settings->type9_title_color) && $settings->type9_title_color ? $settings->type9_title_color : '#fff';
            $type9_title_bgColor = isset($settings->type9_title_bgColor) && $settings->type9_title_bgColor ? $settings->type9_title_bgColor : '#2f5496';
            // 移入
            $type9_border_color_hover = isset($settings->type9_border_color_hover) && $settings->type9_border_color_hover ? $settings->type9_border_color_hover : '#dd0000';
            $type9_title_color_hover = isset($settings->type9_title_color_hover) && $settings->type9_title_color_hover ? $settings->type9_title_color_hover : '';
            $type9_title_bgColor_hover = isset($settings->type9_title_bgColor_hover) && $settings->type9_title_bgColor_hover ? $settings->type9_title_bgColor_hover : '#dd0000';

            $css .= "
            {$addon_id} .pro09 {
                display: flex;
                flex-wrap: wrap;
            }
            {$addon_id} .pro09 .pro-item {
                width: calc(100% / {$columns} - {$item_w}px);
                margin-right: {$item_m}px;
                margin-bottom: 20px;
            }
            {$addon_id} .pro09 .pro-item:nth-child({$columns}n) {
                margin-right: 0;
            }
            {$addon_id} .pro09 .pro-item a {
                display: block;
                border: solid {$type9_border_color} 1px;
                /*transition: all 0.6s;*/
            }";
            if ($fix_img_height == 1) {
                $css .= "{$addon_id} .pro09 .pro-item .img-box {
                    height: {$fix_img_height_input}px;
                }
                {$addon_id} .pro09 .pro-item .img-box img.proImg {
                    object-fit: {$img_style_type};
                    width: 100%;
                    height: 100%;
                }";
            }
            $css .= "
            {$addon_id} .pro09 .pro-item .img-box {
                position: relative;
                overflow: hidden;
            }
            {$addon_id} .pro09 .pro-item .img-box .icon-bg-box {
                position: absolute;
                width: 100%;
                height: 52px;
                left: 0;
                bottom: 0;
                z-index: 2;
            }
            {$addon_id} .pro09 .pro-item .img-box .icon-bg-box .icon {
                width: 100%;
                height: 100%;
            }
            {$addon_id} .pro09 .pro-item .img-box .icon-box {
                position: absolute;
                width: 100%;
                height: 52px;
                left: 0;
                bottom: 0;
                z-index: 3;
            }
            {$addon_id} .pro09 .pro-item .img-box .icon-box .icon,
            {$addon_id} .pro09 .pro-item .img-box .icon-bg-box .icon {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                margin: auto;
                /*transition: all 0.6s;*/
            }
            {$addon_id} .pro09 .pro-item .img-box .icon-box .icon.icon-h,
            {$addon_id} .pro09 .pro-item .img-box .icon-bg-box .icon.icon-h {
                opacity: 0;
            }
            {$addon_id} .pro09 .pro-item .con {
                background: {$type9_title_bgColor};
                color: {$type9_title_color};
                font-size: 18px;
                height: 46px;
                line-height: 1.5;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 0 10px;
                text-align: center;
                /*transition: all 0.6s;*/
            }
            {$addon_id} .pro09 .pro-item:hover a {
                border-color: {$type9_border_color_hover};
            }
            {$addon_id} .pro09 .pro-item:hover .img-box .icon-box .icon,
            {$addon_id} .pro09 .pro-item:hover .img-box .icon-bg-box .icon {
                opacity: 0;
            }
            {$addon_id} .pro09 .pro-item:hover .img-box .icon-box .icon.icon-h,
            {$addon_id} .pro09 .pro-item:hover .img-box .icon-bg-box .icon.icon-h {
                opacity: 1;
            }
            {$addon_id} .pro09 .pro-item:hover .con {
                background: #dd0000;
            }
            @media (min-width: 768px) and (max-width: 991px) {

            }
            @media (max-width: 767px) {
                {$addon_id} .pro09 .pro-item {
                    width: calc(100% / {$columns_xs} - {$item_w_xs}px);
                }
                {{ addonId }} .pro09 .pro-item:nth-child({$columns}n) {
                    margin-right: {$item_m}px !important;
                }
                {{ addonId }} .pro09 .pro-item:nth-child({$columns_xs}n) {
                    margin-right: 0px !important;
                }";
            if ($fix_img_height == 1) {
                $css .= "
                    {$addon_id} .pro09 .pro-item .img-box {
                        height: {$fix_img_height_input_m}px;
                    }";
            }
            $css .= "
            }
            ";
        }

        if($pro_type == 'type10') {
            $css .= "
            {$addon_id} .pro10 {
                width: 100%;
                padding-top: 50px;
                padding-bottom: 50px;
            }
            {$addon_id} .pro10 .swiper-slide {
                background-position: center;
                background-size: cover;
                width: calc(100% / 3);
            }";
            if ($fix_img_height == 1) {
                $css .= "{$addon_id} .pro10 .swiper-slide .img-box {
                    height: {$fix_img_height_input}px;
                }
                {$addon_id} .pro10 .swiper-slide img {
                    object-fit: {$img_style_type};
                    width: 100%;
                    height: 100%;
                }";
            }
            $css .= "
            {$addon_id} .pro10 .swiper-slide .con {
                font-size: 18px;
                line-height: 18px;
                color: #333333;
                text-align: center;
                font-weight: bold;
                margin-top: 30px;
                opacity: 0;
                transition: all ease-in-out 0.3s;
            }
            {$addon_id} .pro10 .swiper-slide.swiper-slide-active .con {
                opacity: 1;
            }
            @media (max-width: 767px) {";
                if ($fix_img_height == 1) {
                    $css .= "
                        {$addon_id} .pro10 .swiper-slide .img-box {
                            height: {$fix_img_height_input_m}px;
                        }";
                }
                $css .= "
            }
            ";
        }

        return $css;
    }

    public static function getTemplate()
    {
        // $settings = self::getTemplate->addon->settings;
        // echo "<pre>";
        // var_dump($settings);
        // exit();
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        };

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $article_helper;

        $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, "type1", 1, 10);
        $html_list = [];
        foreach ($categories_list as $key => $val) {
            $this_model = new JwpagefactoryAddonProduct_tab(self::$this_obj);
            $html_list[] = ['content' => $this_model->render2($val['tag_id'], self::$this_obj)];
        };
        $html_json = json_encode($html_list);

        $output = '
        <#
            var type_parent = (typeof data.type_parent !== "undefined" && data.type_parent) ? data.type_parent : "type1";
            var navPosition = data.nav_position || "nav-left";
            var box_shadow = "";
            var jw_tab_item = ' . json_encode($categories_list) . ';
            //console.log(jw_tab_item)
            var jw_tab_content = ' . $html_json . ';
            var type = (typeof data.type !== "undefined" && data.type) ? data.type : "nav";

            //调活布局2 开启线条样式
            var img_check_2_line = data.img_check_2_line || 0;
            if(img_check_2_line == 1) {
                var img_check_2_line_color = data.img_check_2_line_color || "#fff";
                var img_check_2_line_width = data.img_check_2_line_width || "8";
                var img_check_2_line_height = data.img_check_2_line_height || "8";
                var img_check_2_line_border = data.img_check_2_line_border || "4";
                var img_check_2_line_position = data.img_check_2_line_position || "center";
                var img_check_2_line_colorhover = data.img_check_2_line_colorhover || "#fff";
            }

            // 标题宽度
            // 标题宽度 单位
            var title_w_unit = typeof data.title_w !== "undefined" && typeof data.title_w.unit !== "undefined" ? data.title_w.unit : "";
            // 标题宽度
            var title_w_md = typeof data.title_w !== "undefined" && typeof data.title_w.md !== "undefined" ? data.title_w.md : "";
            var title_w_sm = typeof data.title_w !== "undefined" && typeof data.title_w.sm !== "undefined" ? data.title_w.sm : "";
            var title_w_xs = typeof data.title_w !== "undefined" && typeof data.title_w.xs !== "undefined" ? data.title_w.xs : "";

            // 开启列表部分导航
            var show_list_nav = data.show_list_nav || 0;
            // 列表部分导航布局
            var list_nav_style = data.list_nav_style || "nav01";
            // 列表部分导航标题
            var list_nav_name = data.list_nav_name || "";
            // 列表部分导航右侧文字分隔符
            var list_nav_right_s = data.list_nav_right_s || ">";
            // 关闭右侧图标显示
            var hidden_list_nav_right_img = data.hidden_list_nav_right_img || 0;
            // 列表部分导航左侧图标
            var list_nav_left_img = data.list_nav_left_img || "";
            // 列表部分导航右侧图标
            var list_nav_right_img = data.list_nav_right_img || "https://oss.lcweb01.cn/jzt/6d102551-1c08-4a57-9a29-a2d204df9298/image/20211118/d1aa16c21c434c802485343db816398a.png";

            // 内容上方导航
            function listNav() {
                var html = "";
                if(show_list_nav == 1) {
                    switch(list_nav_style) {
                        case "nav01":
                            html += "<div class=list-nav>";
                            html += "<div class=nav-left>";
                            if(list_nav_left_img) {
                                html += "<img class=\'nav-icon\' src=\'" + list_nav_left_img + "\' alt=>&nbsp;&nbsp;";
                            }
                            html += "产品分类</div>";
                            html += "<div class=nav-right>";
                            if(list_nav_right_img && hidden_list_nav_right_img != 1) {
                                html += "<img class=\'nav-icon\' src=\'" + list_nav_right_img + "\' alt=\'\' />";
                            }
                            html += "<p class=nav>首页 " + list_nav_right_s + " " + list_nav_name + " " + list_nav_right_s + "&nbsp;<span>产品分类</span></p>";
                            html += "</div>";
                            html += "</div>";
                            break;
                    }
                }
                return html;
            }
            // 内容上方导航HTML
            var navHtml = listNav();
            var addonId = "#jwpf-addon-" + data.id;
            function trimBlank(str){
                return str.replace(/(^\s*)|(\s*$)/g, "");
            }
        #>
        <style type="text/css">
            /* 内容上方 导航样式 start */
            <#

                var list_nav_bg_style = data.list_nav_bg_style || "color";
                var list_nav_bgColor = data.list_nav_bgColor || "";
                var list_nav_bgGradient = data.list_nav_bgGradient || null;

                var list_nav_borderColor = data.list_nav_borderColor || "#eee";

                var list_nav_border_h_md = data.list_nav_border_h && data.list_nav_border_h.md ? data.list_nav_border_h.md : 2;
                var list_nav_border_h_sm = data.list_nav_border_h && data.list_nav_border_h.sm ? data.list_nav_border_h.sm : "";
                var list_nav_border_h_xs = data.list_nav_border_h && data.list_nav_border_h.xs ? data.list_nav_border_h.xs : "";

                var list_nav_mb_md = data.list_nav_mb && data.list_nav_mb.md ? data.list_nav_mb.md : "";
                var list_nav_mb_sm = data.list_nav_mb && data.list_nav_mb.sm ? data.list_nav_mb.sm : "";
                var list_nav_mb_xs = data.list_nav_mb && data.list_nav_mb.xs ? data.list_nav_mb.xs : "";

                var list_nav_left_title_f_md = data.list_nav_left_title_f && data.list_nav_left_title_f.md ? data.list_nav_left_title_f.md : 18;
                var list_nav_left_title_f_sm = data.list_nav_left_title_f && data.list_nav_left_title_f.sm ? data.list_nav_left_title_f.sm : "";
                var list_nav_left_title_f_xs = data.list_nav_left_title_f && data.list_nav_left_title_f.xs ? data.list_nav_left_title_f.xs : "";

                var list_nav_left_title_h_md = data.list_nav_left_title_h && data.list_nav_left_title_h.md ? data.list_nav_left_title_h.md : 44;
                var list_nav_left_title_h_sm = data.list_nav_left_title_h && data.list_nav_left_title_h.sm ? data.list_nav_left_title_h.sm : "";
                var list_nav_left_title_h_xs = data.list_nav_left_title_h && data.list_nav_left_title_h.xs ? data.list_nav_left_title_h.xs : "";

                var list_nav_h_md = data.list_nav_h && data.list_nav_h.md ? data.list_nav_h.md : "";
                var list_nav_h_sm = data.list_nav_h && data.list_nav_h.sm ? data.list_nav_h.sm : "";
                var list_nav_h_xs = data.list_nav_h && data.list_nav_h.xs ? data.list_nav_h.xs : "";

                var list_nav_left_p_md = data.list_nav_left_p && trimBlank(data.list_nav_left_p.md) ? data.list_nav_left_p.md : "0 20px";
                var list_nav_left_p_sm = data.list_nav_left_p && data.list_nav_left_p.sm ? data.list_nav_left_p.sm : "";
                var list_nav_left_p_xs = data.list_nav_left_p && data.list_nav_left_p.xs ? data.list_nav_left_p.xs : "";

                var list_nav_left_title_fColor = data.list_nav_left_title_fColor || "#333";

                var list_nav_left_lineColor = data.list_nav_left_lineColor || "#e50011";

                var list_nav_right_p_md = data.list_nav_right_p && data.list_nav_right_p.md ? data.list_nav_right_p.md : "";
                var list_nav_right_p_sm = data.list_nav_right_p && data.list_nav_right_p.sm ? data.list_nav_right_p.sm : "";
                var list_nav_right_p_xs = data.list_nav_right_p && data.list_nav_right_p.xs ? data.list_nav_right_p.xs : "";

                var list_nav_right_f_md = data.list_nav_right_f && data.list_nav_right_f.md ? data.list_nav_right_f.md : 14;
                var list_nav_right_f_sm = data.list_nav_right_f && data.list_nav_right_f.sm ? data.list_nav_right_f.sm : "";
                var list_nav_right_f_xs = data.list_nav_right_f && data.list_nav_right_f.xs ? data.list_nav_right_f.xs : "";

                var list_nav_right_Color = data.list_nav_right_Color || "#666";

                var list_nav_right_t_Color = data.list_nav_right_t_Color || "#e50011";
                switch(list_nav_style) {
                    case "nav01":
            #>
            {{ addonId }} .jwpf-tab-content .list-nav {
                display: flex;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                border-bottom: {{list_nav_borderColor}} solid {{list_nav_border_h_md}}px;
                <# if(list_nav_bg_style == "color") { #>
                    background-color: {{ list_nav_bgColor }};
                <# } #>
                <# if(list_nav_bg_style == "gradient" && list_nav_bgGradient && _.isObject(list_nav_bgGradient)) { #>
                    background-image: {{list_nav_bgGradient.type || "linear"}}-gradient(
                    <# if(list_nav_bgGradient.type && list_nav_bgGradient.type == "radial") { #>
                    at {{list_nav_bgGradient.radialPos || "center center"}}
                    <# } else { #>
                    {{list_nav_bgGradient.deg || 0}}deg
                    <# } #>
                    ,
                        {{list_nav_bgGradient.color}} {{list_nav_bgGradient.pos || 0}}%,
                        {{list_nav_bgGradient.color2}} {{list_nav_bgGradient.pos2 || 100}}%);
                <# } #>
                margin-bottom: {{list_nav_mb_md}}px;
                height: {{list_nav_h_md}}px;
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-left {
                display: flex;
                align-items: center;
                font-size: {{list_nav_left_title_f_md}}px;
                line-height: {{list_nav_left_title_h_md}}px;
                padding: {{list_nav_left_p_md}};
                text-align: center;
                color: {{list_nav_left_title_fColor}};
                font-weight: bold;
                position: relative;
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-left:before {
                content: "";
                width: 2px;
                height: {{list_nav_left_title_f_md}}px;
                background-color: {{list_nav_left_lineColor}};
                position: absolute;
                left: 8px;
                top: 0;
                bottom: 0;
                margin: auto;
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-left:after {
                content: "";
                width: 100%;
                height: 2px;
                background-color: {{list_nav_left_lineColor}};
                position: absolute;
                left: 0;
                bottom: -2px;
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-right {
                display: flex;
                align-items: center;
                font-size: {{list_nav_right_f_md}}px;
                color: {{list_nav_right_Color}};
                padding: {{list_nav_right_p_md}};
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-right span {
                color: {{list_nav_right_t_Color}};
            }
            {{ addonId }} .jwpf-tab-content .list-nav .nav-right .nav-icon {
                width: auto;
                height: 100%;
                margin-right: 4px;
            }
            @media (min-width: 768px) and (max-width: 991px) {
                {{ addonId }} .jwpf-tab-content .list-nav {
                    border-width: {{list_nav_border_h_sm}}px;
                    margin-bottom: {{list_nav_mb_sm}}px;
                    height: {{list_nav_h_sm}}px;
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-left {
                    font-size: {{list_nav_left_title_f_sm}}px;
                    line-height: {{list_nav_left_title_h_sm}}px;
                    padding: {{list_nav_left_p_sm}};
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-left:before {
                    height: {{list_nav_left_title_f_sm}}px;
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-right {
                    font-size: {{list_nav_right_f_sm}}px;
                    padding: {{list_nav_right_p_sm}};
                }
            }
            @media (max-width: 767px) {
                {{ addonId }} .jwpf-tab-content .list-nav {
                    border-width: {{list_nav_border_h_xs}}px;
                    margin-bottom: {{list_nav_mb_xs}}px;
                    height: {{list_nav_h_xs}}px;
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-left {
                    font-size: {{list_nav_left_title_f_xs}}px;
                    line-height: {{list_nav_left_title_h_xs}}px;
                    padding: {{list_nav_left_p_xs}};
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-left:before {
                    height: {{list_nav_left_title_f_xs}}px;
                }
                {{ addonId }} .jwpf-tab-content .list-nav .nav-right {
                    font-size: {{list_nav_right_f_xs}}px;
                    padding: {{list_nav_right_p_xs}};
                }
            }
            <#
                    break;
                }
            #>
            /* 内容上方 导航样式 end */
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a{
                align-items: center;
            }
            #jwpf-addon-{{ data.id }} .jwpf-addon-title {
                background-color: {{ data.title_bg_color }};
                width: {{ title_w_md }}{{title_w_unit}};
            }
            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} .jwpf-addon-title {
                     width: {{ title_w_sm }}{{title_w_unit}};
                }
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} .jwpf-addon-title {
                     width: {{ title_w_xs }}{{title_w_unit}};
                }
            }
            <#
                if(data.show_boxshadow){
                    box_shadow += (!_.isEmpty(data.shadow_horizontal) && data.shadow_horizontal) ?  data.shadow_horizontal + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_vertical) && data.shadow_vertical) ?  data.shadow_vertical + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_blur) && data.shadow_blur) ?  data.shadow_blur + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_spread) && data.shadow_spread) ?  data.shadow_spread + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_color) && data.shadow_color) ?  data.shadow_color : "rgba(0, 0, 0, .5)";
                }
                if(data.style == "pills"){
            #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:focus{
                    color: {{ data.active_tab_color||"#333" }};
                    background-color: {{ data.active_tab_bg||"#e5e5e5" }};
                    border-width: 0;
                    border-width: {{data.active_tab_border_width}};
                    border-style: solid;
                    border-color: {{data.active_tab_border_color}};
                }
            <# } #>
            <# if(data.nav_position != "nav-top"){ #>
                /*#jwpf-addon-{{ data.id }} .minUl {
                    display:block !important;
                }*/
            <# } #>
            <# if(data.nav_position == "nav-top"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                    position: absolute;
                    left: 0;
                    top:36px;
                    max-height:1000px;
                    z-index: 999;
                    overflow: hidden;
                    width:100%;
                }
                #jwpf-addon-{{ data.id }} .page_n{
                    width:100%;
                }
                #jwpf-addon-{{ data.id }} .minUl {
                    display:flex !important;
                    flex-wrap:wrap !important;
                    justify-content:{{data.nav_block_positon}} !important;
                    padding-left:{{data.nav_left_position}}px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top {
                    display:flex;
                    flex-direction:column;
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top .jwpf-tab-custom-content {
                    width:100%;
                    <#if(data.nav_position=="nav-top"){#>
                        padding-top:{{data.nav_gutter}}px;
                    <#}#>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    display:flex;
                    padding-right:0px !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                    display:flex;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                    width:100%;
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width.sm}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        width:100%;
                        display:flex;
                    }
                }
                @media (max-width: 768px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width.xs}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {

                        width:100%;
                        display:flex;
                    }
                }
            <# } #>
            <# if(data.style == "lines"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-bottom: 2px solid {{ data.active_tab_bg }};
                }
            <# } #>

            #jwpf-addon-{{ data.id }} li{
                list-style-type: none;
            }
            <# if(data.fix_img_height=="1"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-img-responsive{
                    height:{{data.fix_img_height_input}}px !important;
                }
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .jwpf-img-responsive{
                        height:{{data.fix_img_height_input_m}}px !important;
                    }
                }
            <# } #>
            #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap{
                background:{{data.normal_font_color}} !important;
            }
            #jwpf-addon-{{ data.id }} .jwpf-addon-article {
                position: relative;
                overflow: hidden;
                border:{{data.normal_border_width}}px solid {{data.normal_border_color}};
            }
            <# if (data.style == "custom") { #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:focus{
                    color: {{ data.hover_tab_color }};
                    border-width: {{ data.hover_tab_border_width }};
                    border-color: {{ data.hover_tab_border_color }};
                    background-color: {{ data.hover_tab_bg }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type  li > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li > a:focus{
                    color: {{ data.hover_tab_color2 }};
                    border-width: {{ data.hover_tab_border_width2 }};
                    border-color: {{ data.hover_tab_border_color2 }};
                    background-color: {{ data.hover_tab_bg2 }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-width: {{ data.active_tab_border_width }};
                    border-color: {{ data.active_tab_border_color }};
                    background-color: {{ data.active_tab_bg }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li.active > a:focus{
                    color: {{ data.active_tab_color2 }};
                    border-width: {{ data.active_tab_border_width2 }};
                    border-color: {{ data.active_tab_border_color2 }};
                    background-color: {{ data.active_tab_bg2 }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_hover }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom  li.active > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_active }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                    <#if(data.nav_position=="nav-top"){#>
                        width:{{data.nav_width}}%;
                    <#}else{#>
                        width:100%;
                    <#}#>
                    <# if(_.isObject(data.nav_margin)){ #>
                        padding: {{data.nav_margin.md}};
                    <# } else { #>
                        padding: {{data.nav_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                    <# if(_.isObject(data.nav_margin2)){ #>
                        padding: {{data.nav_margin2}};
                    <# } else { #>
                        padding: {{data.nav_margin2}};
                    <# } #>
                        width:{{data.nav_width2}}%;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                    <#if(data.nav_lineheight!=0){#>
                        height:{{data.nav_lineheight}}px;
                    <#}#>
                    <# if(_.isObject(data.nav_fontsize)){ #>
                        font-size: {{data.nav_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_fontsize}}px;
                    <# } #>
                    <# if(_.endsWith(data.nav_border, "x")) { #>
                        border-width: {{data.nav_border}};
                        border-style: solid;
                    <# } else { #>
                        border: {{_.trim(data.nav_border, " ")}}px solid;
                    <# } #>
                    border-color: {{data.nav_border_color}};
                    color: {{data.nav_color}};
                    background-color: {{data.nav_bg_color}};
                    border-radius: {{data.nav_border_radius}}px;
                    display:flex;
                    justify-content:{{data.nav_font_position}};
                    <# if(_.isObject(data.nav_padding)){ #>
                        padding: {{data.nav_padding.md}};
                    <# } else { #>
                        padding: {{data.nav_padding}};
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.nav_font_family}};
                    <# if(_.isObject(data.nav_lineheight)){ #>
                        line-height:{{data.nav_lineheight.md}}px;
                    <# }else{ #>
                        line-height:{{data.nav_lineheight}}px;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom>li>a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom>li>.page_n>a{
                    <# if(_.isObject(data.nav_font_style)){
                            if(data.nav_font_style.underline){
                        #>
                            text-decoration:underline;
                        <# }
                            if(data.nav_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                            if(data.nav_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                            if(data.nav_font_style.weight){
                        #>
                            font-weight:{{data.nav_font_style.weight}};
                        <# }
                    } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li a {
                    <# if(_.isObject(data.nav_fontsize2)){ #>
                        font-size: {{data.nav_fontsize2.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_fontsize2}}px;
                    <# } #>
                    <# if(_.endsWith(data.nav_border2, "x")) { #>
                        border-width: {{data.nav_border2}};
                        border-style: solid;
                    <# } else { #>
                        border: {{_.trim(data.nav_border2, " ")}}px solid;
                    <# } #>
                    border-color: {{data.nav_border_color2}};
                    color: {{data.nav_color2}};
                    padding:{{data.nav_padding2}};
                    background-color: {{data.nav_bg_color2}};
                    border-radius: {{data.nav_border_radius2}}px;
                     display:flex;
                    justify-content:{{data.nav_font_position2}};
                    <# if(_.isObject(data.nav_padding2)){ #>

                    <# } else { #>

                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.nav_font_family2}};
                    <# if(_.isObject(data.nav_lineheight2)){ #>
                        line-height:{{data.nav_lineheight2.md}}px;
                        height:{{data.nav_lineheight2.md}}px;
                    <# }else{ #>
                        line-height:{{data.nav_lineheight2}}px;
                        height:{{data.nav_lineheight2}}px;
                    <# }
                    if(_.isObject(data.nav_font_style2)){
                        if(data.nav_font_style2.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.nav_font_style2.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.nav_font_style2.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.nav_font_style2.weight){
                        #>
                            font-weight:{{data.nav_font_style2.weight}};
                        <# }
                    } #>
                }

                #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                    <# if(data.icon_color){ #>
                        color:{{data.icon_color}};
                    <# } #>
                    <# if(_.isObject(data.icon_fontsize)){ #>
                        font-size: {{data.icon_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.icon_fontsize}}px;
                    <# } #>
                    <# if(_.isObject(data.icon_margin)){ #>
                        margin: {{data.icon_margin.md}};
                    <# } else { #>
                        margin: {{data.icon_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                    <# if(_.isObject(data.image_height)){ #>
                        height: {{data.image_height.md}}px;
                    <# } else { #>
                        height: {{data.image_height}}px;
                    <# }
                    if(_.isObject(data.image_width)){
                    #>
                        width: {{data.image_width.md}}px;
                    <# } else { #>
                        width: {{data.image_width}}px;
                    <# }
                    if(_.isObject(data.image_margin)){
                    #>
                        margin: {{data.image_margin.md}};
                    <# } else { #>
                        margin: {{data.image_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    <# if(_.isObject(data.nav_width)){ #>
                        width: 100%;
                    <# } else { #>
                        <#if(data.nav_position=="nav-top"){#>
                            width:100%;
                        <#}else{#>
                            width: {{data.nav_width}}%;
                        <#}#>
                    <# }
                    if(navPosition == "nav-right") {
                    #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-left: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-left: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else {
                        if(_.isObject(data.nav_gutter)){
                    #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# }
                    } #>
                }


                 #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                  <# if(_.isObject(data.nav_width2)){ #>
                        width: {{data.nav_width2.md}}%;
                 <# } else { #>
                        width: {{data.nav_width2}}%;
                 <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                    <#
                    //console.log(data.nav_width)
                    if(_.isObject(data.nav_width)){ #>
                        width: {{100 - data.nav_width.md}}%;
                    <# } else if(data.nav_width) { #>
                        width: {{100 - data.nav_width}}%;
                    <# } else { #>
                        width: 100%;
                    <# } #>
                    <# if(navPosition == "nav-right"){ #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else { #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            <# if(data.nav_position=="nav-left"){ #>
                                padding-left: {{data.nav_gutter.md}}px;
                            <# } #>
                        <# } else { #>
                            <# if(data.nav_position=="nav-left"){ #>
                                padding-left: {{data.nav_gutter.md}}px;
                            <# } #>
                        <# } #>
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                    background-color: {{data.content_backround}};
                    border: {{data.content_border}}px solid;
                    border-color: {{data.content_border_color}};
                    border-radius: {{data.content_border_radius}}px;
                    color: {{data.content_color}};
                    <# if(_.isObject(data.content_padding)){ #>
                        padding: {{data.content_padding.md}};
                    <# } else { #>
                        padding: {{data.content_padding}};
                    <# } #>
                    <# if(_.isObject(data.content_margin)){ #>
                        margin: {{data.content_margin.md}};
                    <# } else { #>
                        margin: {{data.content_margin}};
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.content_font_family}};
                    <# if(_.isObject(data.content_fontsize)){ #>
                        font-size:{{data.content_fontsize.md}}px;
                    <# }
                    if(_.isObject(data.content_lineheight)){ #>

                    <# }
                    if(_.isObject(data.content_font_style)){
                        if(data.content_font_style.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.content_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.content_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.content_font_style.weight){
                        #>
                            font-weight:{{data.content_font_style.weight}};
                        <# }
                    } #>
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.sm}};

                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                        <# if(_.isObject(data.nav_margin2)){ #>
                            padding: {{data.nav_margin2.sm}};
                        <# } #>
                        width:{{data.nav_width2}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.sm}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.sm}};
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: 100%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                                <# if(_.isObject(data.nav_width2)) { #>
                                   width: {{data.nav_width2.sm}}%;
                                <# } else { #>
                                  width: {{data.nav_width2}}%;
                                <# } #>


                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.sm !== "100"){ #>
                                width: {{100-data.nav_width.sm}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.sm}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.sm}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>

                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.sm}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.sm}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.sm}};
                        <# } #>
                    }
                }
                @media (max-width: 767px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.xs}};
                        <# } #>
                        <#if(data.nav_position=="nav-top"){#>
                            <# if(_.isObject(data.nav_width_phone)){ #>
                            width: {{data.nav_width_phone.xs}}%;
                            <# } else if(data.nav_width_phone) { #>
                                width: {{data.nav_width_phone}}%;
                            <# } else { #>
                            <# } #>
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type li {
                        <# if(_.isObject(data.nav_margin2)){ #>
                            padding: {{data.nav_margin2.xs}};
                        <# } #>
                        width:{{data.nav_width2}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        <# if (data.nav_font_position === "flex-start") { #>
                            text-align: left;
                        <# } else if (data.nav_font_position === "center") { #>
                            text-align: center;
                        <# } else if (data.nav_font_position === "flex-end") { #>
                            text-align: right;
                        <# } #>
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.xs}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.xs}};
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                        <#if(data.nav_position=="nav-top"){#>
                            width: 100%;
                        <#}else{#>
                            <# if(_.isObject(data.nav_width_phone)){ #>
                            width: {{data.nav_width_phone.xs}}%;
                            <# } else if(data.nav_width_phone) { #>
                                width: {{data.nav_width_phone}}%;
                            <# } else { #>
                            <# } #>
                        <#}#>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                        <# if(_.isObject(data.nav_width_phone)){ #>
                            width: {{100 - data.nav_width_phone.xs}}%;
                        <# } else if(data.nav_width_phone) { #>
                            width: {{100 - data.nav_width_phone}}%;
                        <# } else { #>
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: 100%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom .er_type{
                            <# if(_.isObject(data.nav_width2)) { #>
                                width: {{data.nav_width2.xs}}%;
                            <# } else { #>
                                width: {{data.nav_width2}}%;
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.xs !== "100"){ #>
                                width: {{100-data.nav_width.xs}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } #>
                                <# if(navPosition == "nav-top") { #>
                                    padding-top: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.xs}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.xs}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>

                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.xs}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.xs}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.xs}};
                        <# } #>
                    }
                }
            <# } #>

            <# if (data.style != "custom") { #>
                #jwpf-addon-{{ data.id }} .minUl {
                    display:flex;
                    justify-content:{{data.nav_all_positon}};
                    background-color:{{data.nav_all_bg_color}};
                }
            <# } #>
            *{
                padding: 0;
                margin: 0;
            }
            #jwpf-addon-{{ data.id }} .page_plug{
                width: 90%;
                margin: 5px auto;
                text-align: center;
                display: flex;
                justify-content: center;
            }
            #jwpf-addon-{{ data.id }} .page_plug a{
                width: 30px;
                line-height: 21px;
                height: 30px;
                padding: 3px 8px;
                border: 1px solid #2a68a7;
                margin-right: 5px;
                text-decoration: none;
                color: #2a68a7;
            }
            //翻页
            #jwpf-addon-{{ data.id }} .page_plug .current:hover{
                color:{{data.page2_tab_fontcolor_hov}};
                background:{{data.page2_tab_bgcolor_hov}};
                border-color:{{data.page2_tab_bordercolor_hov}};
            }
            #jwpf-addon-{{ data.id }} .page_plug .page_num{
                color:{{data.page2_tab_fontcolor}};
                background:{{data.page2_tab_bgcolor}};
                border-color:{{data.page2_tab_bordercolor}};
            }
            #jwpf-addon-{{ data.id }} .page_plug .page_num:hover{
                color:{{data.page2_tab_fontcolor_hov}};
                background:{{data.page2_tab_bgcolor_hov}};
                border-color:{{data.page2_tab_bordercolor_hov}};
            }
            #jwpf-addon-{{ data.id }} .jwpf-addon-article {
                position: relative;
                overflow: hidden;
                border:{{data.normal_border_width}}px solid {{data.normal_border_color}};
            }
            #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover {
                border:{{data.hover_border_width}}px solid {{data.hover_border_color}};
            }

            /*#jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap{
                background:{{data.normal_font_color}} !important;
            }*/
            #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap{
                bottom: 0px;
            }
            #jwpf-addon-{{ data.id }} .introtext_type2{
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }
            <# if(data.is_nav_cate_img == 1) {
                var nav_cate_width_md = data.nav_cate_width && data.nav_cate_width.md ? data.nav_cate_width.md : "30";
                var nav_cate_width_sm = data.nav_cate_width && data.nav_cate_width.sm ? data.nav_cate_width.sm : "";
                var nav_cate_width_xs = data.nav_cate_width && data.nav_cate_width.xs ? data.nav_cate_width.xs : "";

                var nav_cate_height_md = data.nav_cate_height && data.nav_cate_height.md ? data.nav_cate_height.md : "30";
                var nav_cate_height_sm = data.nav_cate_height && data.nav_cate_height.sm ? data.nav_cate_height.sm : "";
                var nav_cate_height_xs = data.nav_cate_height && data.nav_cate_height.xs ? data.nav_cate_height.xs : "";
            #>
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                width: {{ nav_cate_width_md }}px;
                height: {{ nav_cate_height_md }}px;
                margin-right: 5px;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate>img {
                width: 100%;
                height: 100%;
                object-fit: scale-down;
            }
            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                    width: {{ nav_cate_width_sm }}px;
                    height: {{ nav_cate_height_sm }}px;
                }
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                    width: {{ nav_cate_width_xs }}px;
                    height: {{ nav_cate_height_xs }}px;
                }
            }
            <# } #>
            <# if(data.pro_type=="type1"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap {
                    background:{{data.normal_font_color}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap {
                    background:{{data.hover_font_color}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article .jwpf-article-info-wrap p {
                    color:{{data.pro_font_color}} !important;
                    font-size:{{data.content_fontsize_bt}}px !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap p {
                    color:{{data.pro_font_color_hover}} !important;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id a{
                    display:block;
                }
                <# if(!data.show_title) { #>
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                        text-align: center;
                        transition: bottom 0.3s;
                        position: absolute;
                        <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                            bottom: -{{data.pro_font_color_bg_height.md}}px;
                            height: {{data.pro_font_color_bg_height.md}}px;
                            line-height: {{data.pro_font_color_bg_height.md}}px;
                        <# } else { #>
                            bottom: -{{data.pro_font_color_bg_height}}px;
                            height: {{data.pro_font_color_bg_height}}px;
                            line-height: {{data.pro_font_color_bg_height}}px;
                        <# } #>
                        width:100%;
                    }
                <# }else { #>
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                        <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                            height: {{data.pro_font_color_bg_height.md}}px;
                            line-height: {{data.pro_font_color_bg_height.md}}px;
                        <# } else { #>
                            height: {{data.pro_font_color_bg_height}}px;
                            line-height: {{data.pro_font_color_bg_height}}px;
                        <# } #>
                        text-align: center;
                        width:100%;
                        position: absolute;
                        bottom:0;
                    }
                <# } #>
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap{
                        bottom:0;
                    }
                }
                @media (max-width: 768px) {
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap {
                        <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                        height:{{data.pro_font_color_bg_height.xs}}px !important;
                        line-height:{{data.pro_font_color_bg_height.xs}}px !important;
                        <# } else { #>
                            height:{{data.pro_font_color_bg_height}}px !important;
                            line-height:{{data.pro_font_color_bg_height}}px !important;
                       <# } #>
                    }
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .pr_list_id .jwpf-article-info-wrap {
                    <# if(_.isObject(data.pro_font_color_bg_height)){ #>
                        height:{{data.pro_font_color_bg_height.sm}}px !important;
                        line-height:{{data.pro_font_color_bg_height.sm}}px !important;
                    <# } else { #>
                        height:{{data.pro_font_color_bg_height}}px !important;
                        line-height:{{data.pro_font_color_bg_height}}px !important;
                   <# } #>
                }
            <# } #>
            <# if(data.pro_type=="type2"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap {
                    background:{{data.normal_font_color}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 {
                    background:{{data.normal_font_color}} !important;
                }
                <# if(data.button_check_2==1){ #>

                    #jwpf-addon-{{ data.id }} .an_but a:hover {
                        background: {{data.button_bg_hover}};
                    }
                    #jwpf-addon-{{ data.id }} .an_but a {
                        color: #fff;
                        width: 122px;
                        line-height: 28px;
                        background: {{data.button_bg}};
                        text-align: center;
                        margin: 0 auto;
                        font-size:14px;
                    }
                    #jwpf-addon-{{ data.id }} .an_but a {
                        display: block;
                    }
                <# } #>
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .jwpf-article-info-wrap-type2 {
                    background:{{data.hover_font_color}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 .title_type2 {
                    color:{{data.pro_font_color_type2_title}};
                }
                #jwpf-addon-{{ data.id }} .jwpf-article-info-wrap-type2 .introtext_type2 {
                    color:{{data.pro_font_color_type2_intext}};
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .title_type2 {
                    color:{{data.pro_font_color_type2_title_hover}} !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .introtext_type2 {
                    color:{{data.pro_font_color_type2_intext_hover}} !important;
                }
                <# if(data.img_animated=="animated2"){ #>
                    #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap:hover img{
                        transform:scale(1.2);
                        -ms-transform:scale(1.2);
                        -moz-transform:scale(1.2);
                        -webkit-transform:scale(1.2);
                        -o-transform:scale(1.2);
                    }
                <# } #>
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2{
                    padding: 0 10px 15px 10px;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .title_type2{
                    color: {{data.pro_font_color_type2_title}};
                    font-size:{{data.pro_font_title_size_type2}}px;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .introtext_type2{
                    color: {{data.pro_font_color_type2_intext}};
                    font-size:{{data.pro_font_intext_size_type2}}px;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap{
                    transition: all .5s;
                    -ms-transition: all .5s;
                    -moz-transition: all .5s;
                    -webkit-transition: all .5s;
                    -o-transition: all .5s;
                    display: block;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap .img_box_type2{
                    overflow: hidden;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap img{
                    transition: all .5s;
                    -ms-transition: all .5s;
                    -moz-transition: all .5s;
                    -webkit-transition: all .5s;
                    -o-transition: all .5s;
                }
                #jwpf-addon-{{ data.id }} .pr_list_id_type2 .jwpf-article-img-wrap:hover{
                    box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                    -moz-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                    -webkit-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                }
                <# if(data.img_check_2_line == 1){ #>
                    #jwpf-addon-{{ data.id }} .pr_list_id_type2 .lineBox{
                        display:flex;
                        align-items:{{img_check_2_line_position}};
                        justify-content: space-between;
                        margin-top:10px;
                        width:100%;
                        height:4px;
                    }
                    #jwpf-addon-{{ data.id }} .pr_list_id_type2 .lineBox .lineBox_left{
                        width:47%;
                        height:2px;
                        background:{{img_check_2_line_color}};
                    }
                    #jwpf-addon-{{ data.id }} .pr_list_id_type2 .lineBox .lineBox_right{
                        width:47%;
                        height:2px;
                        background:{{ img_check_2_line_color}};
                    }
                    #jwpf-addon-{{ data.id }} .pr_list_id_type2 .lineBox .lineBox_center{
                        width:{{ img_check_2_line_width}}px;
                        height:{{ img_check_2_line_height}}px;
                        border-radius:{{ img_check_2_line_border}}px;
                        background:{{ img_check_2_line_color}};
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .lineBox_left{
                        background:{{ img_check_2_line_colorhover}}!important;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .lineBox .lineBox_right{
                        background:{{ img_check_2_line_colorhover}}!important;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-addon-article:hover .lineBox .lineBox_center{
                        background:{{ img_check_2_line_colorhover}}!important;
                    }
                <# } #>
            <# } #>
            <# if(data.pro_type=="type3"){ #>
                * {
                    margin: 0;
                    padding: 0;
                }

                #jwpf-addon-{{ data.id }} .pro3Ul {
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                }

                #jwpf-addon-{{ data.id }} .pro3li {
                    list-style: none;
                    width: {{100/data.columns}}%;
                    height: 400px;
                    position: relative;
                }

                #jwpf-addon-{{ data.id }} .pro3li .proImg {
                    width: 100%;
                    height: 100%;
                }

                #jwpf-addon-{{ data.id }} .liHoverBox {
                    display: none;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    background: {{data.type3bgclolor}};
                    text-align: center;
                    left: 0;
                    top: 0;
                    box-sizing: border-box;
                }

                #jwpf-addon-{{ data.id }} .hoverIcon {
                    margin: 0px auto 40px auto;
                    width: 45px;
                    height: 45px;
                }

                #jwpf-addon-{{ data.id }} .hoverCon {
                    font-size: {{data.type3fontsize}}px;
                    line-height: 50px;
                    padding-top: 15px;
                    margin-top: 0;
                    color: {{data.type3fontcolor}};
                    text-align: center;
                    background: url("/components/com_jwpagefactory/addons/product_list/assets/images/11_1103.jpg") no-repeat center top;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                #jwpf-addon-{{ data.id }} .pro3li:hover .liHoverBox {
                    display: flex;
                    justify-content: center;
                    flex-flow: column;
                }
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .liHoverBox {
                        display: none;
                        flex-flow: column;
                        justify-content: center;
                    }
                }
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .hoverCon {
                         line-height: initial;在导航和内容之间的空间
                    }
                }
                @media (max-width: 992px) {
                    #jwpf-addon-{{ data.id }} .hoverIcon {
                        margin: 0px auto 8px auto;
                        width: 20px;
                        height: 20px;
                    }
                }

                @media (max-width:768px) {
                    #jwpf-addon-{{ data.id }} .pro3li {
                        width: {{100/data.columns_xs}}%;
                        margin-bottom: 10px;
                    }
                }
                <# if(data.fix_img_height){ #>
                    #jwpf-addon-{{ data.id }} .pro3li{
                        height:{{data.fix_img_height_input}}px !important;
                    }
                    @media (max-width: 768px) {
                        #jwpf-addon-{{ data.id }} .pro3li{
                            height:{{data.fix_img_height_input_m}}px !important;
                        }
                    }
                <# } #>
            <# } #>
            <# if(data.pro_type =="type5") {
                var addonId = "#jwpf-addon-" + data.id;
                var columns = data.columns || 2;
                var columns_xs = data.columns_xs || 2;
                var fix_img_height = data.fix_img_height;
                var img_style_type = data.img_style_type;
                var fix_img_height_input = data.fix_img_height_input;
                var fix_img_height_input_m = data.fix_img_height_input_m;

                var type5_fontsize = data.type5_fontsize || 16;
                var type5_fontColor = data.type5_fontColor || "#333";
                var type5_title_linHeight = data.type5_title_linHeight || 68;
                var type5_title_bgColor = data.type5_title_bgColor || "#fff";
                var type5_more_bgColor = data.type5_more_bgColor || "#999999";
                var type5_more_color = data.type5_more_color || "#fff";
                var type5_more_bgWidth = data.type5_more_bgWidth || 36;
                var type5_more_fontsize = data.type5_more_fontsize || 27;
                var type5_more_right = data.type5_more_right || 0;
                var type5_more_bottom = data.type5_more_bottom || -19;
                var type5_hover_bg = data.type5_hover_bg || "rgba(31, 53, 241, 0.7)";
                var type5_fontsize_hover = data.type5_fontsize_hover || 20;
                var type5_fontColor_hover = data.type5_fontColor_hover || "#fff";
                var type5_title_linHeight_hover = data.type5_title_linHeight_hover || 52;
                var type5_lineWidth_hover = data.type5_lineWidth_hover || 37;
                var type5_lineHeight_hover = data.type5_lineHeight_hover || 2;
                var type5_lineColor_hover = data.type5_lineColor_hover || "#fff";
                var type5_more_bgColor_hover = data.type5_more_bgColor_hover || "#be1d2c";
                var type5_more_color_hover = data.type5_more_color_hover || "#fff";

                var type5_item_bgColor = data.type5_item_bgColor || "#f2f2f2";

                var type5_item_mb_md = data.type5_item_mb && data.type5_item_mb.md ? data.type5_item_mb.md : 50;
                var type5_item_mb_sm = data.type5_item_mb && data.type5_item_mb.sm ? data.type5_item_mb.sm : "";
                var type5_item_mb_xs = data.type5_item_mb && data.type5_item_mb.xs ? data.type5_item_mb.xs : "";

                var type5_item_border_color = data.type5_item_border_color || "#ededed";
                var type5_item_border_style = data.type5_item_border_style || "solid";

                var type5_item_border_width_md = data.type5_item_border_width && data.type5_item_border_width.md ? data.type5_item_border_width.md : "1px";
                var type5_item_border_width_sm = data.type5_item_border_width && data.type5_item_border_width.sm ? data.type5_item_border_width.sm : "";
                var type5_item_border_width_xs = data.type5_item_border_width && data.type5_item_border_width.xs ? data.type5_item_border_width.xs : "";

                var type5_item_img_border_color = data.type5_item_img_border_color || "";
                var type5_item_img_border_style = data.type5_item_img_border_style || "";

                var type5_item_img_border_width_md = data.type5_item_img_border_width && data.type5_item_img_border_width.md ? data.type5_item_img_border_width.md : "";
                var type5_item_img_border_width_sm = data.type5_item_img_border_width && data.type5_item_img_border_width.sm ? data.type5_item_img_border_width.sm : "";
                var type5_item_img_border_width_xs = data.type5_item_img_border_width && data.type5_item_img_border_width.xs ? data.type5_item_img_border_width.xs : "";

                var type5_item_img_p_md = data.type5_item_img_p && data.type5_item_img_p.md ? data.type5_item_img_p.md : "";
                var type5_item_img_p_sm = data.type5_item_img_p && data.type5_item_img_p.sm ? data.type5_item_img_p.sm : "";
                var type5_item_img_p_xs = data.type5_item_img_p && data.type5_item_img_p.xs ? data.type5_item_img_p.xs : "";


            #>
                {{ addonId }} .pro05 {
                    display: flex;
                    flex-wrap: wrap;
                }
                {{ addonId }} .pro05 .pro-item {
                    <# if(data.type5_item_even == 1) { #>
                    width: calc(100% / {{columns}} - {{ (Number(columns) + 1) * 20 / columns}}px);
                    margin-left: 20px;
                    <# } else { #>
                    width: calc(100% / {{columns}});
                    padding: 0 20px;
                    <# } #>
                    display: inline-block;
                    margin-bottom: {{type5_item_mb_md}}px;
                }
                {{ addonId }} .pro05 .pro-item a {
                    display: block;
                    position: relative;
                    border-style: {{type5_item_border_style}};
                    border-color: {{type5_item_border_color}};
                    <# if (trimBlank(type5_item_border_width_md)) { #>
                        border-width: {{type5_item_border_width_md}};
                    <# } else { #>
                        border-width: 1px;
                    <# } #>
                    width: 100%;
                    height: 100%;
                }
                {{ addonId }} .pro05 .pro-item a::before {
                    content: "";
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 7px;
                    left: -5px;
                    background: {{type5_item_bgColor}};
                    z-index: -1;
                }
                {{ addonId }} .pro05 .pro-item .img-box {
                    width: 100%;
                    overflow: hidden;
                    border-style: {{type5_item_img_border_style}};
                    border-color: {{type5_item_img_border_color}};
                    <# if (trimBlank(type5_item_img_border_width_md)) { #>
                        border-width: {{type5_item_img_border_width_md}};
                    <# } #>
                    <# if (trimBlank(type5_item_img_p_md)) { #>
                        padding: {{type5_item_img_p_md}};
                    <# } #>
                }
                {{ addonId }} .pro05 .pro-item .img-box img {
                    transition: all 0.6s;
                }
                <# if(fix_img_height == 1) { #>
                    {{ addonId }} .pro05 .pro-item .img-box {
                        height: {{ fix_img_height_input }}px;
                    }
                    {{ addonId }} .pro05 .pro-item .img-box img {
                        object-fit: {{ img_style_type }};
                        width: 100%;
                        height: 100%;
                    }
                <# } #>
                {{ addonId }} .pro05 .pro-item .con {
                    line-height: {{ type5_title_linHeight }}px;
                    text-align: center;
                    background-color: {{ type5_title_bgColor }};
                    font-size: {{ type5_fontsize }}px;
                    color: {{ type5_fontColor }};
                    padding: 0 10px;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
                {{ addonId }} .pro05 .pro-item .liHoverBox {
                    width: 100%;
                    height: 0;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    background: {{ type5_hover_bg }};
                    padding: 0 50px;
                    transition: all 0.6s;
                    overflow: hidden;
                }
                {{ addonId }} .pro05 .pro-item .liHoverBox .hoverCon {
                    padding: 60% 0 0;
                    line-height: {{ type5_title_linHeight_hover }}px;
                    font-size: {{ type5_fontsize_hover }}px;
                    color: {{ type5_fontColor_hover }};
                    text-align: center;
                    position: relative;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                }
                {{ addonId }} .pro05 .pro-item .liHoverBox .line {
                    width: {{ type5_lineWidth_hover }}px;
                    height: {{ type5_lineHeight_hover }}px;
                    background-color: {{ type5_lineColor_hover }};
                    margin: auto;
                }
                {{ addonId }} .pro05 .pro-item:hover .liHoverBox {
                    height: 100%;
                }
                {{ addonId }} .pro05 .pro-item .icon {
                    width: {{ type5_more_bgWidth }}px;
                    height: {{ type5_more_bgWidth }}px;
                    line-height: {{ type5_more_bgWidth }}px;
                    background: {{ type5_more_bgColor }};
                    border-radius: 100%;
                    display: block;
                    position: absolute;
                    right: {{ type5_more_right }}px;
                    bottom: {{ type5_more_bottom }}px;
                    font-size: {{ type5_more_fontsize }}px;
                    color: {{ type5_more_color }};
                    text-align: center;
                    z-index: 3;
                    transition: all 0.6s;
                }
                {{ addonId }} .pro05 .pro-item:hover .icon {
                    background: {{ type5_more_bgColor_hover }};
                    color: {{ type5_more_color_hover }};
                }
                {{ addonId }} .pro05 .pro-item:hover .img-box img {
                    <# if(data.type5_item_img_scale == 1) { #>
                        transform: scale(1.3);
                    <# } #>
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    {{ addonId }} .pro05 .pro-item {
                        margin-bottom: {{type5_item_mb_sm}}px;
                    }
                    {{ addonId }} .pro05 .pro-item a {
                        <# if (trimBlank(type5_item_border_width_sm)) { #>
                            border-width: {{type5_item_border_width_sm}};
                        <# } #>
                    }
                    {{ addonId }} .pro05 .pro-item .img-box {
                        <# if (trimBlank(type5_item_img_border_width_sm)) { #>
                            border-width: {{type5_item_img_border_width_sm}};
                        <# } #>
                        <# if (trimBlank(type5_item_img_p_sm)) { #>
                            padding: {{type5_item_img_p_sm}};
                        <# } #>
                    }
                }
                @media (max-width: 767px) {
                    {{ addonId }} .pro05 .pro-item {
                        <# if(data.type5_item_even == 1) { #>
                        width: calc(100% / {{columns_xs}} - {{ (Number(columns_xs) + 1) * 20 / columns_xs}}px);
                        <# } else { #>
                        width: calc(100% / {{columns_xs}});
                        <# } #>
                        margin-bottom: {{type5_item_mb_xs}}px;
                    }
                    {{ addonId }} .pro05 .pro-item a {
                        <# if (trimBlank(type5_item_border_width_xs)) { #>
                            border-width: {{type5_item_border_width_xs}};
                        <# } #>
                    }
                    {{ addonId }} .pro05 .pro-item .img-box {
                        <# if (trimBlank(type5_item_img_border_width_xs)) { #>
                            border-width: {{type5_item_img_border_width_xs}};
                        <# } #>
                        <# if (trimBlank(type5_item_img_p_xs)) { #>
                            padding: {{type5_item_img_p_xs}};
                        <# } #>
                    }
                    <# if(fix_img_height == 1) { #>
                        {{ addonId }} .pro05 .pro-item .img-box {
                            height: {{ fix_img_height_input_m }}px;
                        }
                    <# } #>
                }
            <# } #>
            <# if(data.pro_type =="type9") {
                var addonId = "#jwpf-addon-" + data.id;
                var columns = data.columns || 2;
                var columns_xs = data.columns_xs || 2;
                var fix_img_height = data.fix_img_height;
                var img_style_type = data.img_style_type;
                var fix_img_height_input = data.fix_img_height_input;
                var fix_img_height_input_m = data.fix_img_height_input_m;

                var item_m = 28;
                var item_w = item_m * (columns - 1) / columns;
                var item_w_xs = item_m * (columns_xs - 1) / columns_xs;

                // 正常
                var type9_border_color = data.type9_border_color || "#2f5496";
                var type9_title_color = data.type9_title_color || "#fff";
                var type9_title_bgColor = data.type9_title_bgColor || "#2f5496";
                // 移入
                var type9_border_color_hover = data.type9_border_color_hover || "#dd0000";
                var type9_title_color_hover = data.type9_title_color_hover || "";
                var type9_title_bgColor_hover = data.type9_title_bgColor_hover || "#dd0000";
            #>
                {{ addonId }} .pro09 {
                    display: flex;
                    flex-wrap: wrap;
                }
                {{ addonId }} .pro09 .pro-item {
                    width: calc(100% / {{columns}} - {{item_w}}px);
                    margin-right: {{item_m}}px;
                    margin-bottom: 20px;
                }
                {{ addonId }} .pro09 .pro-item:nth-child({{columns}}n) {
                    margin-right: 0;
                }
                {{ addonId }} .pro09 .pro-item a {
                    display: block;
                    border: solid {{ type9_border_color }} 1px;
                    /*transition: all 0.6s;*/
                }
                <# if(fix_img_height == 1) { #>
                    {{ addonId }} .pro09 .pro-item .img-box {
                        height: {{ fix_img_height_input }}px;
                    }
                    {{ addonId }} .pro09 .pro-item .img-box img.proImg {
                        object-fit: {{ img_style_type }};
                        width: 100%;
                        height: 100%;
                    }
                <# } #>
                {{ addonId }} .pro09 .pro-item .img-box {
                    position: relative;
                    overflow: hidden;
                }
                {{ addonId }} .pro09 .pro-item .img-box .icon-bg-box {
                    position: absolute;
                    width: 100%;
                    height: 52px;
                    left: 0;
                    bottom: 0;
                    z-index: 2;
                }
                {{ addonId }} .pro09 .pro-item .img-box .icon-bg-box .icon {
                    width: 100%;
                    height: 100%;
                }
                {{ addonId }} .pro09 .pro-item .img-box .icon-box {
                    position: absolute;
                    width: 100%;
                    height: 52px;
                    left: 0;
                    bottom: 0;
                    z-index: 3;
                }
                {{ addonId }} .pro09 .pro-item .img-box .icon-box .icon,
                {{ addonId }} .pro09 .pro-item .img-box .icon-bg-box .icon {
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    /*transition: all 0.6s;*/
                }
                {{ addonId }} .pro09 .pro-item .img-box .icon-box .icon.icon-h,
                {{ addonId }} .pro09 .pro-item .img-box .icon-bg-box .icon.icon-h {
                    opacity: 0;
                }
                {{ addonId }} .pro09 .pro-item .con {
                    background: {{ type9_title_bgColor }};
                    color: {{ type9_title_color }};
                    font-size: 18px;
                    height: 46px;
                    line-height: 1.5;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    padding: 0 10px;
                    text-align: center;
                    /*transition: all 0.6s;*/
                }
                {{ addonId }} .pro09 .pro-item:hover a {
                    border-color: {{ type9_border_color_hover }};
                }
                {{ addonId }} .pro09 .pro-item:hover .img-box .icon-box .icon,
                {{ addonId }} .pro09 .pro-item:hover .img-box .icon-bg-box .icon {
                    opacity: 0;
                }
                {{ addonId }} .pro09 .pro-item:hover .img-box .icon-box .icon.icon-h,
                {{ addonId }} .pro09 .pro-item:hover .img-box .icon-bg-box .icon.icon-h {
                    opacity: 1;
                }
                {{ addonId }} .pro09 .pro-item:hover .con {
                    background: {{ type9_title_bgColor_hover }};
                    color: {{ type9_title_color_hover }};
                }
                @media (min-width: 768px) and (max-width: 991px) {

                }
                @media (max-width: 767px) {
                    {{ addonId }} .pro09 .pro-item {
                        width: calc(100% / {{columns_xs}} - {{item_w_xs}}px);
                    }
                    {{ addonId }} .pro09 .pro-item:nth-child({{columns}}n) {
                        margin-right: {{item_m}}px;
                    }
                    {{ addonId }} .pro09 .pro-item:nth-child({{columns_xs}}n) {
                        margin-right: 0px;
                    }
                    <# if(fix_img_height == 1) { #>
                        {{ addonId }} .pro09 .pro-item .img-box {
                            height: {{ fix_img_height_input_m }}px;
                        }
                    <# } #>
                }
            <# } #>
            #jwpf-addon-{{ data.id }} .jwpf-article-img-wrap{
                position:relative;
                cursor: pointer;
                display:block;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a{
                align-items: center;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li>a{
                align-items: center;
            }

            #jwpf-addon-{{ data.id }} .jwpf-addon-article{
                height: max-content;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon {
                display: inline-block;
                width: 12px;
                height: 2px;
                background-color: {{ data.nav_color }};
                margin-right: 3px;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li>a:hover .before-icon {
                background-color: {{ data.hover_tab_color }};
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li.active>a .before-icon {
                background-color: {{ data.active_tab_color }};
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom li>a {
                position: relative;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .after-icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                background-color: #fff;
                border-radius: 50%;
                position: absolute;
                right: 10px;
                top: 0;
                bottom: 0;
                margin: auto;
            }
            <# if(data.is_nav_cate_img == 1) {
                var nav_cate_width_md = data.nav_cate_width && data.nav_cate_width.md ? data.nav_cate_width.md : "30";
                var nav_cate_width_sm = data.nav_cate_width && data.nav_cate_width.sm ? data.nav_cate_width.sm : "";
                var nav_cate_width_xs = data.nav_cate_width && data.nav_cate_width.xs ? data.nav_cate_width.xs : "";

                var nav_cate_height_md = data.nav_cate_height && data.nav_cate_height.md ? data.nav_cate_height.md : "30";
                var nav_cate_height_sm = data.nav_cate_height && data.nav_cate_height.sm ? data.nav_cate_height.sm : "";
                var nav_cate_height_xs = data.nav_cate_height && data.nav_cate_height.xs ? data.nav_cate_height.xs : "";
            #>
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                width: {{ nav_cate_width_md }}px;
                height: {{ nav_cate_height_md }}px;
                margin-right: 5px;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate>img {
                width: 100%;
                height: 100%;
                object-fit: scale-down;
            }
            #jwpf-addon-{{ data.id }} .jwpf-nav-custom .jwpf-tab-title {
                max-width: calc(100% - {{ nav_cate_width_md }}px - 5px);
                /*white-space: nowrap;*/
                text-overflow: ellipsis;
                overflow: hidden;
                display: inline-block;
                /*height: 100%;*/
            }
            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                    width: {{ nav_cate_width_sm }}px;
                    height: {{ nav_cate_height_sm }}px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .jwpf-tab-title {
                    max-width: calc(100% - {{ nav_cate_width_sm }}px - 5px);
                }
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .before-icon-cate {
                    width: {{ nav_cate_width_xs }}px;
                    height: {{ nav_cate_height_xs }}px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom .jwpf-tab-title {
                    max-width: calc(100% - {{ nav_cate_width_xs }}px - 5px);
                }
            }
            <# } #>
        </style>
        <# if (data.pro_type !="type4" && data.pro_type !="type7") { #>
            <div class="jwpf-addon jwpf-addon-tab {{ data.class }}">
                <# if( !_.isEmpty( data.title )){ #>
                    <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
                <# }
                let icon_postion = (data.nav_icon_postion == \'top\' || data.nav_icon_postion == \'bottom\') ? \'tab-icon-block\' : \'\';

                #>
                <div class="jwpf-addon-content  jwpf-tab jwpf-{{data.style}}-tab jwpf-tab-{{navPosition}}">

                    <# if(jw_tab_item.length == 0) { #>
                        <p class="alert alert-warning">请先去客户端添加产品分类和产品内容</p>
                    <# } else { #>
                    <#if(data.nav_cont==1 && data.style==="custom"){
                        if(data.nav_bor!=0 ||data.nav_back_color!=""){
                            var str = data.nav_bor.split(" "); #>
                            <ul style="border:solid  {{data.nav_bor_color}}; border-top-width: {{str[0]}};border-left-width: {{str[1]}};border-bottom-width: {{str[2]}};border-right-width:{{str[3]}};background-color:{{data.nav_back_color}};" class="jwpf-nav minUl jwpf-nav-{{ data.style }}">
                                <!--头部图片-->
                                <# if(data.nav_img && data.nav_cont==1 && data.nav_title_show != 1){ #>
                                    <img src=\'{{data.nav_img}}\' style="width:{{data.nav_img_w}}%;height:{{data.nav_img_h}}px;margin-bottom:{{data.nav_img_m}}px">
                                <# } #>

                                <!--头部图片上的文字-->
                                <# if(data.nav_title_show == 1){ #>
                                    <div style="position:relative;">
                                        <img src=\'{{data.nav_img}}\' style="width:{{data.nav_img_w}}%;height:{{data.nav_img_h}}px;margin-bottom:{{data.nav_img_m}}px;">
                                        <p class="nav_title" style="font-size:{{data.nav_font_size}}px;color:{{data.nav_font_color}};position:absolute;left:{{data.nav_font_left}}%;top:{{data.nav_font_top}}%">{{data.nav_title}}</p>
                                    </div>
                                <# } #>
                        <# } else { #>
                            <ul class="jwpf-nav minUl jwpf-nav-{{ data.style }}">
                        <# }
                    } else { #>
                        <ul class="jwpf-nav minUl jwpf-nav-{{ data.style }}">
                    <# } #>
                        <#
                            _.each(jw_tab_item, function(tab, key){
                                var active = "";
                                if(key == 0){
                                    active = "active";
                                }

                                var title = "";
                                var subtitle = "";
                                var icon_top ="";
                                var icon_bottom = "";
                                var icon_right = "";
                                var icon_left = "";
                                var icon_block = "";

                                var image_top ="";
                                var image_bottom = "";
                                var image_right = "";
                                var image_left = "";


                                if(tab.title){
                                    title = "<span class=\"jwpf-tab-title\">" + tab.title + "</span>";
                                }
                                if(tab.subtitle){
                                    subtitle = `<span class="jwpf-tab-subtitle">${tab.subtitle}</span>`;
                                }

                                if(data.nav_icon_postion == "top" || data.nav_icon_postion == "bottom" || data.nav_image_postion == "top" || data.nav_image_postion == "bottom"){
                                    icon_block = "tab-img-or-icon-block-wrap";
                                }
                                var nav_before_icon = "";
                                var nav_after_icon = "";
                                if(data.style == "custom"){
                                    if(data.is_nav_before_icon == 1) {
                                        nav_before_icon += "<span class=\"before-icon\"></span>";
                                    }
                                    if(data.is_nav_cate_img == 1 && tab.image_intro) {
                                        nav_before_icon += "<span class=\"before-icon-cate\"><img src=\"" + tab.image_intro + "\" /></span>";
                                    }
                                    if(data.is_nav_after_icon == 1) {
                                        nav_after_icon += "<span class=\"after-icon\"></span>";
                                    }
                                }
                        #>


                            <# if(data.style=="custom"&&data.type_parent!="type1"&&data.type_parent!="type2"){ #>
                                 <li class="{{ active }}">
                                    <a style="text-indent: {{data.nav_font_width}}px;" data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} {{{ nav_before_icon }}} {{{title}}} {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}} {{{ nav_after_icon }}}</a>
                                </li>
                                <li>
                                    <div class="page_n">
                                        <a style="text-indent: {{data.nav_font_width}}px;" class="nav_n" href="javascript:void(0)">{{{ nav_before_icon }}} {{{title}}} {{{ nav_after_icon }}}</a>
                                    </div>
                                    <ul class="er_type">
                                        <li class="page_n">
                                            <a style="text-indent: {{data.nav_font_width}}px;" data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} 我是二级导航 {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}</a>
                                        </li>
                                    </ul>
                               </li>

                            <# }else{ #>
                               <li class="{{ active }}">
                                    <a style="text-indent: {{data.nav_font_width}}px;" data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} {{{ nav_before_icon }}}  {{{title}}} {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}} {{{ nav_after_icon }}}</a>
                                </li>
                            <# } #>

                        <# }); #>
                        <!--底部图片-->
                        <# if(data.nav_bottom_img && data.nav_bottom==1){ #>
                            <img src=\'{{data.nav_bottom_img}}\' style="width:{{data.nav_bottom_img_w}}%;height:auto;margin-top:{{data.nav_bottom_img_m}}px">
                        <# } #>
                    </ul>
                    <# } #>
                    <# if(data.pro_type=="type1"){ #>
                        <div class="pr_list_id jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                    <# } #>
                    <# if(data.pro_type=="type2"){ #>
                        <div class="pr_list_id_type2 jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                    <# } #>

                    <# if(data.pro_type=="type3"){ #>
                        <div class=" jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                    <# } #>

                    <# if(data.pro_type == "type5"){ #>
                        <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content pr_list_05">
                    <# } #>

                    <# if(data.pro_type == "type8"){ #>
                        <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content pr_list_08">
                    <# } #>

                    <# if(data.pro_type == "type9"){ #>
                        <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content pr_list_09">
                    <# } #>

                    <# if(data.pro_type == "type10"){ #>
                        <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content pr_list_10">
                    <# } #>

                        <# _.each(jw_tab_content, function(tab, key){ #>
                            <#
                                var active = "";
                                if(key == 0){
                                    active = "active in";
                                }
                            #>
                            <div id="jwpf-tab-{{ data.id }}{{ key }}" class="jwpf-tab-pane jwpf-fade {{ active }}">
                                <# if(data.pro_type=="type1"){ #>
                                    <div style="display: flex !important;justify-content: space-between;">
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                    <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                </div>
                                            </a>
                                        </div>
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                    <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                </div>
                                            </a>
                                        </div>
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap pr_active">
                                                    <p style="margin:0;font-size: 100%;color:{{data.pro_font_color}};">我是标题</p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                <# } #>
                                <# if(data.pro_type=="type2"){ #>
                                    <div style="display: flex !important;justify-content: space-between;">
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <div class="img_box_type2">
                                                    <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                </div>
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                    <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                    <# if (data.show_intro) { #>
                                                        <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                    <# } #>
                                                    <# if (data.button_check_2) { #>
                                                        <div class="an_but"><a title="{{data.button_text}}">{{data.button_text}}</a></div>
                                                    <# } #>
                                                    <# if(img_check_2_line == 1) {#>
                                                        <div class="lineBox">
                                                            <div class="lineBox_left"></div>
                                                            <div class="lineBox_center"></div>
                                                            <div class="lineBox_right"></div>
                                                        </div>
                                                    <# } #>
                                                </div>
                                            </a>
                                        </div>
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <div class="img_box_type2">
                                                    <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                </div>
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                    <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                    <# if (data.show_intro) { #>
                                                        <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                    <# } #>
                                                    <# if (data.button_check_2) { #>
                                                        <div class="an_but"><a title="{{data.button_text}}">{{data.button_text}}</a></div>
                                                    <# } #>
                                                    <# if(img_check_2_line == 1) {#>
                                                        <div class="lineBox">
                                                            <div class="lineBox_left"></div>
                                                            <div class="lineBox_center"></div>
                                                            <div class="lineBox_right"></div>
                                                        </div>
                                                    <# } #>
                                                </div>
                                            </a>
                                        </div>
                                        <div style="width:32% !important" class="jwpf-addon-article">
                                            <a class="jwpf-article-img-wrap pr_box">
                                                <div class="img_box_type2">
                                                    <img style="width:100%"  class="jwpf-img-responsive" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg">
                                                </div>
                                                <div style="background:{{data.normal_font_color}}" class="jwpf-article-info-wrap-type2 pr_active">
                                                    <p class="title_type2" style="padding: {{data.pro_title_height_type2_padding}};height: {{data.pro_title_height_type2}}px;text-align: {{data.title_font_position}};margin:0;color:{{data.pro_font_color_type2_title}};">我是标题</p>
                                                    <# if (data.show_intro) { #>
                                                        <div style="color:{{data.pro_font_color_type2_intext}}" class="introtext_type2">我是简介</div>
                                                    <# } #>
                                                    <# if (data.button_check_2) { #>
                                                        <div class="an_but"><a title="{{data.button_text}}">{{data.button_text}}</a></div>
                                                    <# } #>
                                                    <# if(img_check_2_line == 1) {#>
                                                        <div class="lineBox">
                                                            <div class="lineBox_left"></div>
                                                            <div class="lineBox_center"></div>
                                                            <div class="lineBox_right"></div>
                                                        </div>
                                                    <# } #>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                <# } #>
                                <# if(data.pro_type=="type3"){ #>
                                    <div class="pro3">
                                        <ul class="pro3Ul">
                                            <li class="pro3li">
                                                <a>
                                                    <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                    <div class="liHoverBox">
                                                        <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                        <div class="hoverCon">我是标题</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="pro3li">
                                                <a>
                                                    <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                    <div class="liHoverBox">
                                                        <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                        <div class="hoverCon">我是标题</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="pro3li">
                                                <a>
                                                    <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                    <div class="liHoverBox">
                                                        <img class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">
                                                        <div class="hoverCon">我是标题</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                <# } #>
                                <# if(data.pro_type == "type5") {
                                    var limit = data.limit || 10;
                                #>
                                    <# if( !_.isEmpty( data.title ) && data.content_hidden_title != 1){ #>
                                        <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
                                    <# } #>
                                    {{{ navHtml }}}
                                    <div class="pro05">
                                        <# for(var i = 0; i < limit; i++) { #>
                                            <div class="pro-item">
                                                <a>
                                                    <div class="img-box">
                                                        <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                    </div>
                                                    <div class="con">我是标题</div>
                                                    <div class="liHoverBox">
                                                        <div class="hoverCon">我是标题</div>
                                                        <p class="line"></p>
                                                    </div>
                                                    <div class="icon">+</div>
                                                </a>
                                            </div>
                                        <# } #>
                                    </div>
                                <# } #>


                                <# if(data.pro_type == "type8"){ #>
                                    <style>
                                        #jwpf-addon-{{ data.id }} .type8{
                                            width:100%;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 .breadcrumb1{
                                            overflow:hidden;
                                            background: {{data.bread_border_bg}};
                                            border-bottom: 2px solid {{data.bread_border_color}};
                                            height:{{data.bread_height}}px;
                                            line-height:{{data.bread_height}}px;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 .breadcrumb1 .title{
                                            float:left;
                                            font-size: {{data.bread_title_font_size}}px;
                                            color:{{data.bread_title_font_color}};
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 .breadcrumb1 .crumb{
                                            float:right;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 .breadcrumb1 .crumb .position{
                                            font-weight:blod;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 .breadcrumb1 .crumb span{
                                            color:{{data.bread_position_font_color}};
                                            font-size:{{data.bread_position_font_size}}px;
                                            cursor: pointer;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 article h1{
                                            color:{{data.article_title_color}};
                                            font-size:{{data.article_title_size}}px;
                                            line-height: {{data.article_title_line_height}}px;
                                            text-align:center;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 article .info{
                                            background:{{data.article_info_bg_color}};
                                            color:{{data.article_info_color}};
                                            border-top:1px solid {{data.article_info_border_color}};
                                            border-bottom:1px solid {{data.article_info_border_color}};
                                            font-size:{{data.article_info_size}}px;
                                            line-height: {{data.article_info_line_height}}px;
                                            text-align:center;
                                        }
                                        #jwpf-addon-{{ data.id }} .type8 article .content{
                                            color:{{data.article_content_color}};
                                            font-size:{{data.article_content_size}}px;
                                            line-height: {{data.article_content_line_height}}px;
                                            margin-top:{{data.article_content_top}}px;
                                        }
                                    </style>
                                    <div class="type8">
                                        <div class="breadcrumb1">
                                            <div class="title">我是一级导航</div>
                                            <div class="crumb">
                                            <span class="position">当前位置：</span>
                                            <# if(data.nav_first_title!==""){ #>
                                                <span>{{data.nav_first_title}}</span> &nbsp; > &nbsp;
                                            <# } #>
                                            <# if(data.nav_title){ #>
                                                <span>{{data.nav_title}}</span> &nbsp; > &nbsp;
                                            <# } #>
                                                <span>我是一级导航</span> &nbsp; > &nbsp;
                                                <span>我是二级导航</span>
                                            </div>
                                        </div>
                                        <article>
                                            <h1>我是标题</h1>
                                            <div class="info">信息来源：xxx &nbsp; 发布时间：2015-05-25</div>
                                            <div class="content">我是内容</div>
                                        </article>
                                    </div>
                                <# } #>
                                <# if(data.pro_type == "type9") {
                                    var limit = data.limit || 10;
                                    var type9_icon_img = data.type9_icon_img || "https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png";
                                    var type9_icon_bgImg = data.type9_icon_bgImg || "https://oss.lcweb01.cn/joomla/20220613/d192734a26b96846918d12e0fcc256ba.png";
                                    var type9_icon_img_hover = data.type9_icon_img_hover || "https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png";
                                    var type9_icon_bgImg_hover = data.type9_icon_bgImg_hover || "https://oss.lcweb01.cn/joomla/20220613/505cbe642c55dc01405f626b71234e1b.png";
                                #>
                                    <# if( !_.isEmpty( data.title ) && data.content_hidden_title != 1){ #>
                                        <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
                                    <# } #>
                                    {{{ navHtml }}}
                                    <div class="pro09">
                                        <# for(var i = 0; i < limit; i++) { #>
                                            <div class="pro-item">
                                                <a>
                                                    <div class="img-box">
                                                        <img class="proImg" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"  loading="lazy">
                                                        <div class="icon-bg-box">
                                                            <img src=\'{{ type9_icon_bgImg }}\' class="icon" />
                                                            <img src=\'{{ type9_icon_bgImg_hover }}\' class="icon icon-h" />
                                                        </div>
                                                        <div class="icon-box">
                                                            <img src=\'{{ type9_icon_img }}\' class="icon" />
                                                            <img src=\'{{ type9_icon_img_hover }}\' class="icon icon-h" />
                                                        </div>
                                                    </div>
                                                    <div class="con">我是标题</div>
                                                </a>
                                            </div>
                                        <# } #>
                                    </div>
                                <# } #>
                                <# if(data.pro_type == "type10") { #>
                                布局10
                                <# } #>
                                <style>
                                    //翻页
                                    #jwpf-addon-{{ data.id }} .page_plug .current{
                                        width: 30px;

                                    }
                                    #jwpf-addon-{{ data.id }} .page_plug .currentss{
                                        color:{{data.page2_tab_cur_fontcolor}};
                                        background:{{data.page2_tab_cur_bgcolor}};
                                        width: 30px;
                                        height: 30px;
                                        line-height: 30px;
                                        border: 1px solid ;
                                        border-color:{{data.page2_tab_cur_bordercolor}};
                                        margin-right:5px;
                                    }
                                    #jwpf-addon-{{ data.id }} .page_plug .currentss:hover{
                                        color:{{data.page2_tab_fontcolor_hov}};
                                        background:{{data.page2_tab_bgcolor_hov}};
                                        width: 30px;
                                        height: 30px;
                                        line-height: 30px;
                                        border: 1px solid ;
                                        border-color:{{data.page2_tab_bordercolor_hov}};
                                        margin-right:5px;
                                    }
                                    #jwpf-addon-{{ data.id }} .page_plug .page_num{
                                        color:{{data.page2_tab_fontcolor}};
                                        background:{{data.page2_tab_bgcolor}};
                                        border-color:{{data.page2_tab_bordercolor}};
                                    }
                                    #jwpf-addon-{{ data.id }} .page_plug .page_num:hover{
                                        color:{{data.page2_tab_fontcolor_hov}};
                                        background:{{data.page2_tab_bgcolor_hov}};
                                        border-color:{{data.page2_tab_bordercolor_hov}};
                                    }
                                </style>
                                <# if(data.show_page==1){ #>
                                    <div style="position: relative;left: {{data.page_location}}px; top:{{data.page_top_location}}px;background-color:none;border-color:none;">
                                        <div class="page_plug">
                                            <p class="currentss">1</p>
                                            <a class="page_num"
                                               href="/index.php/component/jwpagefactory/?view=page&amp;id=Mzc2OTg=&amp;company_id=2&amp;layout_id=487&amp;site_id=249&amp;page=2&amp;cpcatid=1391"
                                            >2</a>
                                            <# if(data.page_count<4){ #>
                                                <a class="page_num"
                                                    href="/index.php/component/jwpagefactory/?view=page&amp;id=Mzc2OTg=&amp;company_id=2&amp;layout_id=487&amp;site_id=249&amp;page=3&amp;cpcatid=1391"
                                                >3</a>
                                                <# if(data.page_count<3){ #>
                                                    <a class="page_num"
                                                        href="/index.php/component/jwpagefactory/?view=page&amp;id=Mzc2OTg=&amp;company_id=2&amp;layout_id=487&amp;site_id=249&amp;page=4&amp;cpcatid=1391"
                                                    >4</a>
                                                    <# if(data.page_count<2){ #>
                                                        <a class="page_num"
                                                            href="/index.php/component/jwpagefactory/?view=page&amp;id=Mzc2OTg=&amp;company_id=2&amp;layout_id=487&amp;site_id=249&amp;page=5&amp;cpcatid=1391"
                                                        >5</a>
                                                        <# if(data.page_count<1){ #>
                                                            <a class="page_num"
                                                                href="/index.php/component/jwpagefactory/?view=page&amp;id=Mzc2OTg=&amp;company_id=2&amp;layout_id=487&amp;site_id=249&amp;page=6&amp;cpcatid=1391"
                                                            >6</a>
                                                        <# } #>
                                                    <# } #>
                                                <# } #>
                                            <# } #>
                                            <# if(data.page_count!=0){ #>
                                                <p>...</p>
                                            <# } #>
                                        </div>
                                        <div class="page_plug">共6条</div>
                                    </div>
                                <# }#>
                            </div>
                        <# }); #>
                    </div>
                </div>
            </div>
        <# } else if(data.pro_type == "type4") { #>
            <div class="jwpf-addon jwpf-addon-tab {{ data.class }}">
                <# if( !_.isEmpty( data.title )){ #>
                    <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
                <# }
                let icon_postion = (data.nav_icon_postion == \'top\' || data.nav_icon_postion == \'bottom\') ? \'tab-icon-block\' : \'\';
                let nav_image_position = data.nav_image_position ? data.nav_image_position : "left";
                let nav_icon_postion = data.nav_icon_postion ? data.nav_icon_postion : "left";
                #>
                <div class="jwpf-addon-content  jwpf-tab jwpf-{{data.style}}-tab jwpf-tab-{{navPosition}}">

                    <ul class="jwpf-nav minUl jwpf-nav-{{ data.style }}">

                            <!--头部图片-->
                        <# if(data.nav_img){ #>
                            <img src=\'{{data.nav_img}}\' style="width:{{data.nav_img_w}}%;height:{{data.nav_img_h}}px;margin-bottom:{{data.nav_img_m}}px">
                        <# } #>

                        <#
                            _.each(data.jw_tab_item, function(tab, key){
                                var active = "";
                                if(key == 0){
                                    active = "active";
                                }

                                var title = "";
                                var subtitle = "";
                                var icon_top ="";
                                var icon_bottom = "";
                                var icon_right = "";
                                var icon_left = "";
                                var icon_block = "";

                                var image_top ="";
                                var image_bottom = "";
                                var image_right = "";
                                var image_left = "";


                                if(tab.title){
                                    title = tab.title;
                                }
                                if(tab.subtitle){
                                    subtitle = `<span class="jwpf-tab-subtitle">${tab.subtitle}</span>`;
                                }

                                if(tab.image_or_icon && tab.image_or_icon==="image"){
                                    if(tab.image && nav_image_position==="top"){
                                        image_top=`<img class="jwpf-tab-image tab-image-block" src=\'${tab.image}\'>`;
                                    }else if (tab.image && nav_image_position==="bottom") {
                                        image_bottom = `<img class="jwpf-tab-image tab-image-block" src=\'${tab.image}\'>`;
                                    } else if (tab.image && nav_image_position==="right") {
                                        image_right = `<img class="jwpf-tab-image tab-image-block" src=\'${tab.image}\'>`;
                                    }else {
                                        image_left = `<img class="jwpf-tab-image tab-image-block" src=\'${tab.image}\'>`;
                                    }
                                }else{
                                    if (tab.icon && nav_icon_postion==="top") {
                                        icon_top=`<span class="jwpf-tab-icon tab-icon-block" "><i class="${tab.icon}" aria-hidden="true"></i></span>`
                                    }else if (tab.icon && nav_icon_postion==="bottom") {
                                        icon_bottom=`<span class="jwpf-tab-icon tab-icon-block" "><i class="${tab.icon}" aria-hidden="true"></i></span>`
                                    }else if (tab.icon && nav_icon_postion==="right") {
                                        icon_right=`<span class="jwpf-tab-icon tab-icon-block" "><i class="${tab.icon}" aria-hidden="true"></i></span>`
                                    }else {
                                        icon_left=`<span class="jwpf-tab-icon tab-icon-block" "><i class="${tab.icon}" aria-hidden="true"></i></span>`
                                    }
                                }

                                if(data.nav_icon_postion == "top" || data.nav_icon_postion == "bottom" || data.nav_image_postion == "top" || data.nav_image_postion == "bottom"){
                                    icon_block = "tab-img-or-icon-block-wrap";
                                }
                        #>

                        <li class="{{ active }}">
                            <a data-toggle="jwpf-tab" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" href="#jwpf-tab-{{ data.id }}{{ key }}">{{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} {{tab.title}} {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}</a>
                        </li>

                        <# }); #>
                    </ul>
                    <style>
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                            background-color: {{data.content_backround}};
                            border: 1px solid;
                            color: #000;
                            border-color: {{data.content_border_color||"#e5e5e5"}};
                            padding: {{data.content_padding}};
                            box-shadow: {{data.shadow_horizontal||0}}px {{data.shadow_vertical||0}}px {{data.shadow_blur||0}}px {{data.shadow_spread||0}}px {{data.shadow_color||"#000"}};
                        }
                    </style>

                    <div class="jwpf-tab-custom-content">
                        <# _.each(data.jw_tab_item, function(tab, key){ #>
                            <div ccc="jwpf-tab-{{data.id}}-{{key+1}}" id="jwpf-tab{{data.id}}-{{key+1}}" class="jwpf-tab-pane jwpf-fade  active in" role="tabpanel" aria-labelledby="jwpf-{{data.id}}-{{key+1}}">{{tab.content}}</div>
                        <# }) #>
                    </div>

                </div>
            </div>
        <# } else if(data.pro_type == "type7") {
            var addonId = "#jwpf-addon-" + data.id;
            var pro7_title = data.pro7_title || "产品展示";
            var pro7_desc = data.pro7_desc || "Products Show";
            var pro7_title_color = data.pro7_title_color || "#393939";
            var pro7_title_fontsize = data.pro7_title_fontsize || 24;
            var pro7_desc_color = data.pro7_desc_color || 20;

            var pro7_tab_width = data.pro7_tab_width || 104;
            var pro7_tab_height = data.pro7_tab_height || 42;
            var pro7_tab_mgL = data.pro7_tab_mgL || 42;
            var pro7_tab_fontsize = data.pro7_tab_fontsize || 16;
            var pro7_tab_color = data.pro7_tab_color || "#6a6a6a";
            var pro7_tab_bgColor = data.pro7_tab_bgColor || "";
            var pro7_tab_color_active = data.pro7_tab_color_active || "#fff";
            var pro7_tab_bgColor_active = data.pro7_tab_bgColor_active || "#5ea1cb";

            var pro7_content_img_style_type = data.pro7_content_img_style_type || "fill";
            var pro7_content_title_fontsize = data.pro7_content_title_fontsize || 24;
            var pro7_content_title_color = data.pro7_content_title_color || "#000";
            var pro7_content_title_height = data.pro7_content_title_height || 70;
        #>
            <style>
                {{ addonId }} .room_part {
                    width: 100%;
                    margin: 0 auto 12px;
                    height: auto;
                    overflow: hidden;
                }
                {{ addonId }} .room_top {
                    margin-bottom: 32px;
                    height: auto;
                    display: flow-root;
                }
                {{ addonId }} .room_title {
                    float: left;
                    color: {{pro7_desc_color}};
                    font-size: {$pro7_desc_fontsize}px;
                    line-height: 2;
                    text-transform: uppercase;
                }
                {{ addonId }} .room_title h4 {
                    color: {{ pro7_title_color }};
                    font-size: {{ pro7_title_fontsize }}px;
                    font-weight: normal;
                }
                {{ addonId }} .room_list {
                    float: right;
                    padding: 9px 40px 0 0;
                    width: 800px;
                }
                {{ addonId }} ul, li {
                    list-style: none;
                }
                {{ addonId }} .room_list li {
                    display: inline-block;
                    width: {{ pro7_tab_width }}px;
                    height: {{ pro7_tab_height }}px;
                    line-height: {{ pro7_tab_height }}px;
                    text-align: center;
                    font-size: {{ pro7_tab_fontsize }}px;
                    cursor: pointer;
                    margin-left: {{ pro7_tab_mgL }}px;
                    color: {{ pro7_tab_color }};
                    background: {{ pro7_tab_bgColor }};
                }
                {{ addonId }} .room_list li:first-child {
                    margin-left: 0px;
                }
                {{ addonId }} .room_list .room_on {
                    color: {{ pro7_tab_color_active }};
                    background: {{ pro7_tab_bgColor_active }};
                }

                {{ addonId }} .container {
                    margin:0 auto;
                    width:100%;
                    height: 407px;
                }
                {{ addonId }} .container .div-box-content{
                    height: 407px;
                    width: 437px;
                    margin-left: 20px;
                    position:relative;
                    overflow: hidden;
                }
                {{ addonId }} .container .div-box-content .div-box-img{
                    width: 100%;
                    height: 285px;
                }
                {{ addonId }} .container .div-box-content .div-box-content-title{
                    color: {{ pro7_content_title_color }};
                    text-align:center;
                    line-height: {{ pro7_content_title_height }}px;
                    display:block;
                    width:100%;
                    font-size: {{ pro7_content_title_fontsize }}px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                {{ addonId }} .scroll-text {
                    width: 100%;
                    height:auto;
                    overflow: hidden;
                    padding:0;
                }
                {{ addonId }} .scroll-text ul {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    margin: 0;
                    padding: 0;
                    font-size: 0;
                }
                {{ addonId }} .scroll-text ul li {
                    height: 407px;
                    width: 461px;
                    display: inline-block;
                    margin: 0;
                    padding: 0;
                }
            </style>
            <div class="room_part" style="visibility: visible;">
                <div class="room_top">
                    <div class="room_title">
                        <h4>{{pro7_title}}</h4>
                        {{pro7_desc}}
                    </div>
                    <div class="room_list">
                        <ul>
                            <# for(var i = 0; i < 3; i++){
                                var pro7_class = "";
                                if(i == 0) {
                                    pro7_class = "room_on";
                                }
                            #>
                                <li class="{{pro7_class}}">分类</li>
                            <# } #>
                        </ul>
                    </div>
                </div>
                <div class="container">
                    <div id="demo2" class="scroll-text">
                        <ul style="width: 6000px;">
                            <# for(var i = 0; i < 3; i++){ #>
                                <li>
                                    <div class="div-box-content">
                                        <img style="object-fit:{{ pro7_content_img_style_type }};" class="div-box-img" src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg"/>
                                        <div class="div-box-content-title title">我是标题</div>
                                    </div>
                                </li>
                            <# } #>
                        </ul>
                    </div>
                </div>
            </div>
        <# } #>
        ';

        return $output;
    }

    public function render2($goods_catid, $this_obj)
    {
        $settings = $this_obj->addon->settings;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $cpcatid = $_GET['cpcatid'] ?? 0;
        $page_view_name = isset($_GET['view']);
        $addon_id = '#jwpf-addon-' . $this_obj->addon->id;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

        // Addon options
        $resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        if ($cpcatid == $goods_catid) {
            $page = $_GET['page'];
        } else {
            $page = 1;
        }
        $pro_font_color = (isset($settings->pro_font_color)) ? $settings->pro_font_color : '#fff';
        $img_style_type = (isset($settings->img_style_type)) ? $settings->img_style_type : 'fill';
        $pro_font_color_hover = (isset($settings->pro_font_color_hover)) ? $settings->pro_font_color_hover : '#fff';
        $pro_font_color_bg_height = (isset($settings->pro_font_color_bg_height)) ? $settings->pro_font_color_bg_height : 40;
        $pro_font_color_type2_title = (isset($settings->pro_font_color_type2_title)) ? $settings->pro_font_color_type2_title : '#000';
        $pro_font_color_type2_title_hover = (isset($settings->pro_font_color_type2_title_hover)) ? $settings->pro_font_color_type2_title_hover : '#000';
        $pro_font_title_size_type2 = (isset($settings->pro_font_title_size_type2)) ? $settings->pro_font_title_size_type2 : 14;
        $pro_font_color_type2_intext = (isset($settings->pro_font_color_type2_intext)) ? $settings->pro_font_color_type2_intext : '#000';
        $pro_font_color_type2_intext_hover = (isset($settings->pro_font_color_type2_intext_hover)) ? $settings->pro_font_color_type2_intext_hover : '#000';
        $pro_font_intext_size_type2 = (isset($settings->pro_font_intext_size_type2)) ? $settings->pro_font_intext_size_type2 : 14;
        $img_animated = (isset($settings->img_animated)) ? $settings->img_animated : 'animated1';
        $box_type2_shadow_color = (isset($settings->box_type2_shadow_color)) ? $settings->box_type2_shadow_color : '#fff';
        $box_type2_shadow_x = (isset($settings->box_type2_shadow_x)) ? $settings->box_type2_shadow_x : 0;
        $box_type2_shadow_Y = (isset($settings->box_type2_shadow_Y)) ? $settings->box_type2_shadow_Y : 0;
        $box_type2_shadow_mh = (isset($settings->box_type2_shadow_mh)) ? $settings->box_type2_shadow_mh : 0;
        $box_type2_shadow_kz = (isset($settings->box_type2_shadow_kz)) ? $settings->box_type2_shadow_kz : 0;
        $title_font_position = (isset($settings->title_font_position)) ? $settings->title_font_position : 'center';

        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'orderingdesc';

        if ($pro_type === 'type8') {
            $limit = 1;
        } else {
            $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 3;
        }

        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        $columns_xs = (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : 2;
        $content_fontsize_bt = (isset($settings->content_fontsize_bt) && $settings->content_fontsize_bt) ? $settings->content_fontsize_bt : 14;

        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        $intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 200;
        $hide_thumbnail = (isset($settings->hide_thumbnail)) ? $settings->hide_thumbnail : 0;
        $link_articles = (isset($settings->link_articles)) ? $settings->link_articles : 0;
        $link_catid = (isset($settings->catid)) ? implode(',', array_filter($settings->catid)) : '';
        $company_id = $company_id ? $company_id : ((isset($settings->company_id)) ? $settings->company_id : 0);
        $site_id = $site_id ? $site_id : ((isset($settings->site_id)) ? $settings->site_id : 0);
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
        $show_title = (isset($settings->show_title) && $settings->show_title) ? $settings->show_title : false;
        $pro_title_height_type2 = (isset($settings->pro_title_height_type2)) ? $settings->pro_title_height_type2 : 50;
        $pro_title_height_type2_padding = (isset($settings->pro_title_height_type2_padding) && trim($settings->pro_title_height_type2_padding)) ? 'padding:' . $settings->pro_title_height_type2_padding . ';' : '';

        $button_check_2 = (isset($settings->button_check_2)) ? $settings->button_check_2 : 0;
        $button_text = (isset($settings->button_text)) ? $settings->button_text : '+查看详情+';
        $button_bg = (isset($settings->button_bg)) ? $settings->button_bg : '#cdcdcd';
        $button_bg_hover = (isset($settings->button_bg_hover)) ? $settings->button_bg_hover : '#2d2c31';

        // 选择的列数  这里的使用$columns是因为列表以pc和平板为主
        $num = round(12 / $columns);
        $widthbig = 100 / $columns;
        // 宽度
        $width = 100 / $columns_xs;

        $all_articles_btn_text = (isset($settings->all_articles_btn_text) && $settings->all_articles_btn_text) ? $settings->all_articles_btn_text : 'See all posts';
        $all_articles_btn_class = (isset($settings->all_articles_btn_size) && $settings->all_articles_btn_size) ? ' jwpf-btn-' . $settings->all_articles_btn_size : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? ' jwpf-btn-' . $settings->all_articles_btn_type : ' jwpf-btn-default';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_shape) && $settings->all_articles_btn_shape) ? ' jwpf-btn-' . $settings->all_articles_btn_shape : ' jwpf-btn-rounded';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? ' jwpf-btn-' . $settings->all_articles_btn_appearance : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_block) && $settings->all_articles_btn_block) ? ' ' . $settings->all_articles_btn_block : '';
        $all_articles_btn_icon = (isset($settings->all_articles_btn_icon) && $settings->all_articles_btn_icon) ? $settings->all_articles_btn_icon : '';
        $all_articles_btn_icon_position = (isset($settings->all_articles_btn_icon_position) && $settings->all_articles_btn_icon_position) ? $settings->all_articles_btn_icon_position : 'left';
        $hover_border_width = isset($settings->hover_border_width) ? $settings->hover_border_width : 2;
        $hover_border_color = (isset($settings->hover_border_color) && $settings->hover_border_color) ? $settings->hover_border_color : 'rgba(21, 228, 116, 0)';
        $normal_border_width = isset($settings->normal_border_width) ? $settings->normal_border_width : 2;
        $normal_border_color = (isset($settings->normal_border_color) && $settings->normal_border_color) ? $settings->normal_border_color : 'rgba(21, 228, 116, 0)';
        $hover_font_color = (isset($settings->hover_font_color) && $settings->hover_font_color) ? $settings->hover_font_color : '#666';
        $normal_font_color = (isset($settings->normal_font_color) && $settings->normal_font_color) ? $settings->normal_font_color : '#666';
        $type3fontsize = (isset($settings->type3fontsize)) ? $settings->type3fontsize : '22';
        $type3fontcolor = (isset($settings->type3fontcolor)) ? $settings->type3fontcolor : '#fff';
        $type3bgclolor = (isset($settings->type3bgclolor)) ? $settings->type3bgclolor : 'rgba(0, 0, 0, 0.5)';
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;
        $page_location = (isset($settings->page_location)) ? $settings->page_location : 0;
        $page_top_location = (isset($settings->page_top_location)) ? $settings->page_top_location : 0;

        $img_check_2_line = (isset($settings->img_check_2_line)) ? $settings->img_check_2_line : '0';
        // 调活布局2 开启线条样式
        if ($img_check_2_line == 1) {
            $img_check_2_line_color = (isset($settings->img_check_2_line_color) && $settings->img_check_2_line_color) ? $settings->img_check_2_line_color : '#fff';
            $img_check_2_line_width = (isset($settings->img_check_2_line_width) && $settings->img_check_2_line_width) ? $settings->img_check_2_line_width : '8';
            $img_check_2_line_height = (isset($settings->img_check_2_line_height) && $settings->img_check_2_line_height) ? $settings->img_check_2_line_height : '8';
            $img_check_2_line_border = (isset($settings->img_check_2_line_border) && $settings->img_check_2_line_border) ? $settings->img_check_2_line_border : '4';
            $img_check_2_line_position = (isset($settings->img_check_2_line_position) && $settings->img_check_2_line_position) ? $settings->img_check_2_line_position : 'center';
            $img_check_2_line_colorhover = (isset($settings->img_check_2_line_colorhover) && $settings->img_check_2_line_colorhover) ? $settings->img_check_2_line_colorhover : '#fff';
        }

        $page2_tab_fontcolor = (isset($settings->page2_tab_fontcolor)) ? $settings->page2_tab_fontcolor : '#ffffff';
        $page2_tab_bordercolor = (isset($settings->page2_tab_bordercolor)) ? $settings->page2_tab_bordercolor : '#2a68a7';
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';

        $output = "<style>
            *{
                padding: 0;
                margin: 0;
            }
            {$addon_id} .jwpf-addon-article {
                position: relative;
                overflow: hidden;
                border:{$normal_border_width}px solid {$normal_border_color};
            }
            {$addon_id} .jwpf-addon-article:hover {
                border:{$hover_border_width}px solid {$hover_border_color};
            }

            {$addon_id} .jwpf-addon-article:hover  .jwpf-article-info-wrap{
                background:{$normal_font_color} !important;
            }
            {$addon_id} .jwpf-addon-article:hover .jwpf-article-info-wrap{
                bottom: 0px;
            }
            {$addon_id} .introtext_type2{
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }
            {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} {
                    max-width:{$widthbig}%;
                    flex: 0 0 {$widthbig}%;
                }
            @media (max-width: 768px) {
                 {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} {
                            max-width:{$width}%;
                        flex: 0 0 {$width}%;
                    }
            }

        ";
        $show_page_col = (isset($settings->show_page_col)) ? $settings->show_page_col : 0;
        if ($show_page_col == 1) {
            $output .= "{$addon_id} .page_plug{
                width: 90%;
                margin: 5px auto;
                text-align: center;
                color: #000;
                display: flex;
                text-align: center;
                flex-wrap: wrap;
                align-items: center;
                justify-content: center;
            }
            {$addon_id} .page_plug a{
                width:auto;
                padding: 3px 8px;
                border: 1px solid {$page2_tab_bordercolor};
                margin-right: 5px;
                text-decoration: none;
                color:{$page2_tab_fontcolor};
                line-height:24px;
                margin-bottom: 5px;
            }";
        } else {
            $output .= "
                {$addon_id} .page_plug{
                    width: 90%;
                    margin: 5px auto;
                    text-align: center;
                    color: #000;
                    display: flex;
                    text-align: center;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: center;
                }
                {$addon_id} .page_plug a{
                    padding: 3px 8px;
                    border: 1px solid #2a68a7;
                    margin-right: 5px;
                    text-decoration: none;
                    color:#000;
                    width:auto;
                    line-height:24px;
                    margin-bottom: 5px;
                }
            ";
        }

        //布局1
        if ($pro_type == 'type1') {
            $output .= "
            {$addon_id}  .jwpf-article-info-wrap {
                background:{$normal_font_color} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap {
                background:{$hover_font_color} !important;
            }

            {$addon_id}  .jwpf-addon-article .jwpf-article-info-wrap p {
                color:{$pro_font_color} !important;
            }

            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap p {
                color:{$pro_font_color_hover} !important;
            }


            ";
            if (!$show_title) {
                $output .= "
                {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                    text-align: center;
                    transition: bottom 0.3s;
                    position: absolute;
                    bottom: -{$pro_font_color_bg_height}px;
                    height: {$pro_font_color_bg_height}px;
                    line-height: {$pro_font_color_bg_height}px;
                    width:100%;
                  }";
            } else {
                $output .= "
                {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                    height: {$pro_font_color_bg_height}px;
                    line-height: {$pro_font_color_bg_height}px;
                    text-align: center;
                    width:100%;
                    position: absolute;
                    bottom:0;
                }";
            }
            $output .= "
            @media (max-width: 992px) {
                  {$addon_id} .pr_list_id .jwpf-article-info-wrap{
                     bottom:0;
                }
            }";
            //            $output .= "
            //            @media (min-width: 768px) and (max-width: 991px){
            //            {$addon_id} .pr_list_id .jwpf-article-info-wrap{
            //                height: ' . $pro_font_color_bg_height_sm . 'px !important;';
            //                }
            //            }";

        }
        //布局2
        $button_check_2 = (isset($settings->button_check_2)) ? $settings->button_check_2 : 0;
        $button_text = (isset($settings->button_text)) ? $settings->button_text : '+查看详情+';
        $button_bg = (isset($settings->button_bg)) ? $settings->button_bg : '#cdcdcd';
        $button_bg_hover = (isset($settings->button_bg_hover)) ? $settings->button_bg_hover : '#2d2c31';

        if ($pro_type == 'type2') {
            $output .= "
            {$addon_id}  .jwpf-article-info-wrap {
                background:{$normal_font_color} !important;
            }";
            if($button_check_2==1){

                $output .= "{$addon_id} .an_but a:hover {
                    background: {$button_bg_hover};
                }
                {$addon_id} .an_but a {
                    color: #fff;
                    width: 122px;
                    line-height: 28px;
                    background: {$button_bg};
                    text-align: center;
                    margin: 0 auto;
                    font-size:14px;
                }
                {$addon_id} .an_but a {
                    display: block;
                }";
            }

            $output .= "{$addon_id}  .jwpf-article-info-wrap-type2 {
                background:{$normal_font_color} !important;
            }
            {$addon_id}  .jwpf-article-info-wrap-type2 .title_type2 {
                color:{$pro_font_color_type2_title};
            }
            {$addon_id}  .jwpf-article-info-wrap-type2 .introtext_type2 {
                color:{$pro_font_color_type2_intext};
            }
            {$addon_id}  .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .title_type2 {
                color:{$pro_font_color_type2_title_hover} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover  .jwpf-article-info-wrap-type2 .introtext_type2 {
                color:{$pro_font_color_type2_intext_hover} !important;
            }
            {$addon_id}  .jwpf-addon-article:hover .jwpf-article-info-wrap-type2  {
                background:{$hover_font_color} !important;
            }
            ";
            if ($img_animated == 'animated2') {
                $output .= "
                    {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover img{
                        transform:scale(1.2);
                        -ms-transform:scale(1.2);
                       -moz-transform:scale(1.2);
                       -webkit-transform:scale(1.2);
                       -o-transform:scale(1.2);
                    }";
            }
            $output .= "
            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2{
                   padding: 0 10px 15px 10px;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .title_type2{
                color: {$pro_font_color_type2_title};
                font-size:{$pro_font_title_size_type2}px;
                 height:{$pro_title_height_type2}px;
                 overflow: hidden;
                 {$pro_title_height_type2_padding};
            }

            {$addon_id} .pr_list_id_type2 .jwpf-article-info-wrap-type2 .introtext_type2{
                color: {$pro_font_color_type2_intext};
                font-size:{$pro_font_intext_size_type2}px;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap{
                transition: all .5s;
                -ms-transition: all .5s;
                -moz-transition: all .5s;
                -webkit-transition: all .5s;
                -o-transition: all .5s;
                display: block;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap .img_box_type2{
                overflow: hidden;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap img{
                transition: all .5s;
                -ms-transition: all .5s;
                -moz-transition: all .5s;
                -webkit-transition: all .5s;
                -o-transition: all .5s;
            }
            {$addon_id} .pr_list_id_type2 .jwpf-article-img-wrap:hover{
                box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                -moz-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
                -webkit-box-shadow: {$box_type2_shadow_x}px {$box_type2_shadow_Y}px {$box_type2_shadow_mh}px {$box_type2_shadow_kz}px {$box_type2_shadow_color};
            }";

            if ($img_check_2_line) {
                $output .= "
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} .lineBox{
                    display:flex;
                    align-items:{$img_check_2_line_position};
                    justify-content: space-between;
                    margin-top:10px;
                    width:100%;
                    height:4px;
                }
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} .lineBox .lineBox_left{
                    width:47%;
                    height:2px;
                    background:{$img_check_2_line_color};
                }
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} .lineBox .lineBox_right{
                    width:47%;
                    height:2px;
                    background:{$img_check_2_line_color};
                }
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} .lineBox .lineBox_center{
                    width:{$img_check_2_line_width}px;
                    height:{$img_check_2_line_height}px;
                    border-radius:{$img_check_2_line_border}px;
                    background:{$img_check_2_line_color};
                }
                {$addon_id} .jwpf-addon-article:hover .lineBox_left{
                    background:{$img_check_2_line_colorhover}!important;
                }
                {$addon_id} .jwpf-addon-article:hover .lineBox .lineBox_right{
                    background:{$img_check_2_line_colorhover}!important;
                }
                {$addon_id} .jwpf-addon-article:hover .lineBox .lineBox_center{
                    background:{$img_check_2_line_colorhover}!important;
                }
                @media (max-width:768px) {
                    {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} .lineBox .lineBox_center{
                        width:4px;
                        height:4px;
                        border-radius:2px;
                        background:{$img_check_2_line_color};
                    }
                }
                ";
            }
        }
        if ($pro_type == 'type3') {
            $output .= "
            * {
                margin: 0;
                padding: 0;
            }

            {$addon_id} .pro3Ul {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
            }

            {$addon_id} .pro3li {
                list-style: none;
                width: 100%;
                height: 400px;
                position: relative;
            }

            {$addon_id} .pro3li .proImg {
                width: 100%;
                height: 100%;
            }

            {$addon_id} .liHoverBox {
                display: none;
                position: absolute;
                width: 100%;
                height: 100%;
                background: {$type3bgclolor};
                text-align: center;
                left: 0;
                top: 0;
                box-sizing: border-box;
            }

            {$addon_id} .hoverIcon {
                margin: 0px auto 40px auto;
                width: 45px;
                height: 45px;
            }

            {$addon_id} .hoverCon {
                font-size: {$type3fontsize}px;
                line-height: 50px;
                padding-top: 15px;
                margin-top: 0;
                color: {$type3fontcolor};
                text-align: center;
                background: url('/components/com_jwpagefactory/addons/product_list/assets/images/11_1103.jpg') no-repeat center top;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            {$addon_id} .pro3li:hover .liHoverBox {
                display: flex;
                justify-content: center;
                flex-flow: column;
            }
                @media (max-width: 992px) {
                    {$addon_id} .liHoverBox {
                        display: flex;
                        flex-flow: column;
                        justify-content: center;
                }
            }
            @media (max-width: 992px) {
                    {$addon_id} .hoverCon {
                     line-height: initial;
                }
            }
            @media (max-width: 992px) {
                {$addon_id} .hoverIcon {
                   margin: 0px auto 8px auto;
                    width: 20px;
                    height: 20px;
                }
            }
            @media (max-width: 768px) {
                {$addon_id} .jwpf-addon-articles .jwpf-row .jwpf-col-sm-{$num} {
                    max-width:{$width}%;
                flex: 0 0 {$width}%;
                }
            }
            @media (max-width:768px) {
                {$addon_id} .pro3li {
                    width: 100%;
                    margin-bottom: 10px;

                }
            }
            {$addon_id} .jwpf-addon-article{
                border: none;
            }
            ";
            // 固定图片高度
            if ($fix_img_height) {
                $output .= "
                    {$addon_id} .pro3li{
                         height:{$fix_img_height_input}px !important;
                    }
                    @media (max-width: 768px) {
                        {$addon_id} .pro3li{
                            height:{$fix_img_height_input_m}px !important;
                        }
                    }
                ";
            }

        }

        $output .= "</style>";

        require_once $article_helper;
        $items = JwpagefactoryHelperGoods::getGoodsList($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        $items_count = JwpagefactoryHelperGoods::getGoodsCount($ordering, $goods_catid, $include_subcat, $company_id, $site_id);

        // 获取分类名称
        $goods_catInfo = JwpagefactoryHelperCategories::getTitle($goods_catid);

        if (!count($items)) {
            if ($pro_type == 'type5' || $pro_type == 'type9') {
                $output .= $this->listNav($goods_catInfo->title);
            }
            $output .= '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUND') . '</p>';

            return $output;
        }

        if (count((array)$items)) {

            $link_target = isset($settings->show_target) && $settings->show_target ? ' target=\'' . $settings->show_target . '\'' : '';

            if ($pro_type == 'type1') {
                $output .= '<div class="pr_list_id jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        if ($resource == 'k2') {
                            if (isset($item->image_medium) && $item->image_medium) {
                                $image = $item->image_medium;
                            } elseif (isset($item->image_large) && $item->image_large) {
                                $image = $item->image_medium;
                            }
                        } else {
                            $image = $item->image_thumbnail;
                        }

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img style="object-fit:' . $img_style_type . ';" src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img style="object-fit:' . $img_style_type . ';" src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this_obj->get_image_placeholder($item->image_thumbnail);
                                $output .= '<a href="' . $item->link . '" itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this_obj->get_image_placeholder($image);

                                if ($settings->show_target == '#') {
                                    $output .= '<a class="jwpf-article-img-wrap pr_box"  href="#"  itemprop="url"><img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                } else {
                                    $output .= '<a class="jwpf-article-img-wrap pr_box" ' . $link_target . ' href="' . $item->link . '" itemprop="url"><img style=width:100%  class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';

                                }
                                $output .= '<div style="background:' . $normal_font_color . '" class="jwpf-article-info-wrap pr_active">';
                                $output .= '<p style="margin:0;font-size: ' . $content_fontsize_bt . 'px;color:' . $pro_font_color . ';">' . $item->title . '</p>';
                                $output .= '</div>';
                                $output .= '</a>';
                            }
                        }
                    }

                    $output .= '</div>';
                    $output .= '</div>';
                }

                //                    $output .= '</div>';

                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if (!empty($link_catid)) {
                        $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this_obj->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                    }
                }

                $output .= '</div>';
                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }

                    $output .= '<div style="position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug" >';
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num " rel="external"  href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';
                    $output .= "
                    <script>
                    </script>
                    ";
                }
                $output .= '</div>';
                $output .= '</div>';
            }

            if ($pro_type == 'type2') {
                $output .= '<div class="pr_list_id_type2 jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $image = '';
                        $image = $item->image_thumbnail;

                        if ($resource != 'k2' && $item->post_format == 'gallery') {
                            if (count((array)$item->imagegallery->images)) {
                                $output .= '<div class="jwpf-carousel jwpf-slide" data-jwpf-ride="jwpf-carousel">';
                                $output .= '<div class="jwpf-carousel-inner">';
                                foreach ($item->imagegallery->images as $key => $gallery_item) {
                                    $active_class = '';
                                    if ($key == 0) {
                                        $active_class = ' active';
                                    }
                                    if (isset($gallery_item['thumbnail']) && $gallery_item['thumbnail']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img style="object-fit:' . $img_style_type . ';" src="' . $gallery_item['thumbnail'] . '" alt="">';
                                        $output .= '</div>';
                                    } elseif (isset($gallery_item['full']) && $gallery_item['full']) {
                                        $output .= '<div class="jwpf-item' . $active_class . '">';
                                        $output .= '<img style="object-fit:' . $img_style_type . ';" src="' . $gallery_item['full'] . '" alt="">';
                                        $output .= '</div>';
                                    }
                                }
                                $output .= '</div>';

                                $output .= '<a class="left jwpf-carousel-control" role="button" data-slide="prev" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_PREVIOUS') . '"><i class="fa fa-angle-left"></i></a>';
                                $output .= '<a class="right jwpf-carousel-control" role="button" data-slide="next" aria-label="' . JText::_('COM_JWPAGEFACTORY_ARIA_NEXT') . '"><i class="fa fa-angle-right"></i></a>';

                                $output .= '</div>';

                            } elseif (isset($item->image_thumbnail) && $item->image_thumbnail) {
                                //Lazyload image
                                $placeholder = $item->image_thumbnail == '' ? false : $this_obj->get_image_placeholder($item->image_thumbnail);
                                if ($settings->show_target == '#') {
                                    $output .= '<a href="#"  itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                                } else {
                                    $output .= '<a href="' . $item->link . '" ' . $link_target . ' itemprop="url"><img style=width:100% class="jwpf-img-responsive' . ($placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder && $page_view_name != 'form' ? $placeholder : $item->image_thumbnail) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . '  loading="lazy"></a>';
                                }

                            }
                        } elseif ($resource != 'k2' && $item->post_format == 'video' && isset($item->video_src) && $item->video_src) {
                            $output .= '<div class="entry-video embed-responsive embed-responsive-16by9">';
                            $output .= '<object class="embed-responsive-item" style="width:100%;height:100%;" data="' . $item->video_src . '">';
                            $output .= '<param name="movie" value="' . $item->video_src . '">';
                            $output .= '<param name="wmode" value="transparent" />';
                            $output .= '<param name="allowFullScreen" value="true">';
                            $output .= '<param name="allowScriptAccess" value="always"></param>';
                            $output .= '<embed src="' . $item->video_src . '" type="application/x-shockwave-flash" allowscriptaccess="always"></embed>';
                            $output .= '</object>';
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'audio' && isset($item->audio_embed) && $item->audio_embed) {
                            $output .= '<div class="entry-audio embed-responsive embed-responsive-16by9">';
                            $output .= $item->audio_embed;
                            $output .= '</div>';
                        } elseif ($resource != 'k2' && $item->post_format == 'link' && isset($item->link_url) && $item->link_url) {
                            $output .= '<div class="entry-link">';
                            $output .= '<a target="_blank" rel="noopener noreferrer" href="' . $item->link_url . '"><h4>' . $item->link_title . '</h4></a>';
                            $output .= '</div>';
                        } else {
                            if (isset($image) && $image) {
                                //Lazyload image
                                $default_placeholder = $image == '' ? false : $this_obj->get_image_placeholder($image);

                                if ($settings->show_target == '#') {
                                    $output .= '<a class="jwpf-article-img-wrap pr_box"  href="#" itemprop="url">';
                                } else {
                                    $output .= '<a class="jwpf-article-img-wrap pr_box" ' . $link_target . ' href="' . $item->link . '" itemprop="url">';

                                }

                                $output .= '<div class="img_box_type2">';
                                $output .= '<img style="object-fit:' . $img_style_type . ';width:100% " class="jwpf-img-responsive' . ($default_placeholder && $page_view_name != 'form' ? ' jwpf-element-lazy' : '') . '" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '" itemprop="thumbnailUrl" ' . ($default_placeholder && $page_view_name != 'form' ? 'data-large="' . $image . '"' : '') . ' loading="lazy">';
                                $output .= '</div>';
                                $output .= '<div style="background:' . $normal_font_color . '" class="jwpf-article-info-wrap-type2 pr_active">';
                                $output .= '<p class="title_type2" style="text-align: ' . $title_font_position . ';margin:0;">' . $item->title . '</p>';
                                // 显示简介
                                if ($show_intro) {
                                    $output .= '<div class="introtext_type2">' . mb_substr(strip_tags($item->introtext), 0, $intro_limit, 'UTF-8') . '...</div>';
                                }
                                //显示按钮
                                if ($button_check_2) {
                                    $output .= '
                                        <div class="an_but"><a href="' . $item->link . '" target="_blank" title="'.$button_text.'">'.$button_text.'</a></div>
                                    ';
                                }
                                if ($img_check_2_line) {
                                    $output .= '<div class="lineBox">';
                                    $output .= '<div class="lineBox_left">';
                                    $output .= '</div>';
                                    $output .= '<div class="lineBox_center">';
                                    $output .= '</div>';
                                    $output .= '<div class="lineBox_right">';
                                    $output .= '</div>';
                                    $output .= '</div>';
                                }
                                $output .= '</div>';
                                $output .= '</a>';
                            }
                        }
                    }

                    $output .= '</div>';
                    $output .= '</div>';
                }

                // See all link
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if (!empty($link_catid)) {
                        $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this_obj->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                    }
                }

                $output .= '</div>';

                if ($show_page) {
                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }

                    $output .= '<div style="position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug">';
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    $output .= '</div>';
                    $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';
                }
                $output .= '</div>';
                $output .= '</div>';
            }

            if ($pro_type == 'type3') {
                $output .= '<div class="pr_list_id_type2 jwpf-addon jwpf-addon-articles ' . $class . '">';

                if ($title) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }

                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div class="jwpf-row">';

                foreach ($items as $key => $item) {
                    $output .= '<div class="jwpf-col-sm-' . round(12 / $columns) . '">';
                    $output .= '<div class="jwpf-addon-article">';
                    if (!$hide_thumbnail) {
                        $output .= ' <div class="pro3">';
                        $output .= ' <ul class="pro3Ul">';
                        $output .= '<li class="pro3li">';
                        if ($settings->show_target == '#') {
                            $output .= '<a href="#">';
                        } else {
                            $output .= '<a href="' . $item->link . '"' . $link_target . ' >';
                        }

                        $image = $item->image_thumbnail;
                        if (isset($image) && $image) {
                            $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                            $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
                        }
                        $output .= '<div class="liHoverBox">';
                        $output .= '<img style="object-fit:' . $img_style_type . ';" class="hoverIcon" src="/components/com_jwpagefactory/addons/product_list/assets/images/p10_1103.png" alt="">';
                        $output .= '<div class="hoverCon">' . $item->title . '</div>';
                        $output .= '</div>';
                        $output .= '</a>';
                        $output .= '</li>';
                        $output .= '</ul>';
                        $output .= '</div>';

                    }
                    $output .= '</div>';
                    $output .= '</div>';
                }
                if ($show_page) {

                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div style="width: 100%; position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug">';
                    // 判断是不是第一页
                    //    if($page != 1) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
                    //    }
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    // 判断是不是最后一页
                    //    if($page != $all_page) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
                    //    }

                    $output .= '</div>';
                    // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';

                }
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }

                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
            }

            if ($pro_type == 'type5') {
                if ($title && $settings->content_hidden_title != 1) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }
                $output .= $this->listNav($goods_catInfo->title);
                $output .= '<div class="jwpf-addon-content pro05">';
                foreach ($items as $key => $item) {
                    $output .= '<div class="pro-item">';
                    if ($settings->show_target == '#') {
                        $output .= '<a href="#" >';
                    } else {
                        $output .= '<a href="' . $item->link . '"' . $link_target . ' >';
                    }
                    $output .= '<div class="img-box">';
                    $image = $item->image_thumbnail;
                    if (isset($image) && $image) {
                        $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                        $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
                    }
                    $output .= '</div>';
                    $output .= '<div class="con">' . $item->title . '</div>';
                    $output .= '<div class="liHoverBox">';
                    $output .= '<div class="hoverCon">' . $item->title . '</div>';
                    $output .= '<p class="line"></p>';
                    $output .= '</div>';
                    $output .= '<div class="icon">+</div>';
                    $output .= '</a>';
                    $output .= '</div>';
                }

                $output .= '</div>';

                if ($show_page) {

                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div style="width: 100%; position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug">';
                    // 判断是不是第一页
                    //    if($page != 1) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
                    //    }
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    // 判断是不是最后一页
                    //    if($page != $all_page) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
                    //    }

                    $output .= '</div>';
                    // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';

                }
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }
            }

            if ($pro_type == 'type8') {
                $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
                $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
                $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : '0';
                $catordering = isset($settings->catordering) ? $settings->catordering : 'sortasc'; //分类排序
                $categories_list = JwpagefactoryHelperCategories::getCategoriesList('com_goods', $site_id, $type_parent, $type_start, $type_num, $catordering);

                $bread_border_color = (isset($settings->bread_border_color) && $settings->bread_border_color) ? $settings->bread_border_color : '#e5e5e5';
                $bread_height = (isset($settings->bread_height) && $settings->bread_height) ? $settings->bread_height : 26;
                $bread_title_font_size = (isset($settings->bread_title_font_size) && $settings->bread_title_font_size) ? $settings->bread_title_font_size : 18;
                $bread_title_font_color = (isset($settings->bread_title_font_color) && $settings->bread_title_font_color) ? $settings->bread_title_font_color : '#333';
                $bread_position_font_size = (isset($settings->bread_position_font_size) && $settings->bread_position_font_size) ? $settings->bread_position_font_size : 16;
                $bread_position_font_color = (isset($settings->bread_position_font_color) && $settings->bread_position_font_color) ? $settings->bread_position_font_color : '#333';
                $article_title_color = (isset($settings->article_title_color) && $settings->article_title_color) ? $settings->article_title_color : '#6aa285';
                $article_title_size = (isset($settings->article_title_size) && $settings->article_title_size) ? $settings->article_title_size : 20;
                $article_title_line_height = (isset($settings->article_title_line_height) && $settings->article_title_line_height) ? $settings->article_title_line_height : 40;
                $article_info_color = (isset($settings->article_info_color) && $settings->article_info_color) ? $settings->article_info_color : '#6aa285';
                $article_info_size = (isset($settings->article_info_size) && $settings->article_info_size) ? $settings->article_info_size : 20;
                $article_info_line_height = (isset($settings->article_info_line_height) && $settings->article_info_line_height) ? $settings->article_info_line_height : 40;
                $article_info_border_color = (isset($settings->article_info_border_color) && $settings->article_info_border_color) ? $settings->article_info_border_color : '#7b7b7b';
                $article_info_bg_color = (isset($settings->article_info_bg_color) && $settings->article_info_bg_color) ? $settings->article_info_bg_color : '#f6f6f6';
                $article_content_color = (isset($settings->article_content_color) && $settings->article_content_color) ? $settings->article_content_color : '#333';
                $article_content_size = (isset($settings->article_content_size) && $settings->article_content_size) ? $settings->article_content_size : 16;
                $article_content_line_height = (isset($settings->article_content_line_height) && $settings->article_content_line_height) ? $settings->article_content_line_height : 40;
                $article_content_top = (isset($settings->article_content_top) && $settings->article_content_top) ? $settings->article_content_top : 40;
                $nav_first_title = (isset($settings->nav_first_title) && $settings->nav_first_title) ? $settings->nav_first_title : '首页';
                $bread_border_bg = (isset($settings->bread_border_bg) && $settings->bread_border_bg) ? $settings->bread_border_bg : 'transparent';
                $nav_title = (isset($settings->nav_title) && $settings->nav_title) ? $settings->nav_title : '';

//                var_dump($categories_list[1]->title);
                //                var_dump($categories_list[1]);
                $output .= '
                    <style>
                        ' . $addon_id . ' .type8{
                            width:100%;
                        }
                        ' . $addon_id . ' .type8 .breadcrumb1{
                            overflow:hidden;
                            background: ' . $bread_border_bg . ';
                            border-bottom: 2px solid ' . $bread_border_color . ';
                            height:' . $bread_height . 'px;
                            line-height:' . $bread_height . 'px;
                        }
                        ' . $addon_id . ' .type8 .breadcrumb1 .title{
                            float:left;
                            font-size: ' . $bread_title_font_size . 'px;
                            color:' . $bread_title_font_color . ';
                        }
                        ' . $addon_id . ' .type8 .breadcrumb1 .crumb{
                            float:right;
                        }
                        ' . $addon_id . ' .type8 .breadcrumb1 .crumb .position{
                            font-weight:blod;
                        }
                        ' . $addon_id . ' .type8 .breadcrumb1 .crumb span{
                            color:' . $bread_position_font_color . ';
                            font-size:' . $bread_position_font_size . 'px;
                            cursor: pointer;
                        }
                        ' . $addon_id . ' .type8 article h1{
                            color:' . $article_title_color . ';
                            font-size:' . $article_title_size . 'px;
                            line-height: ' . $article_title_line_height . 'px;
                            text-align:center;
                        }
                        ' . $addon_id . ' .type8 article .info{
                            background:' . $article_info_bg_color . ';
                            color:' . $article_info_color . ';
                            font-size:' . $article_info_size . 'px;
                            line-height: ' . $article_info_line_height . 'px;
                            text-align:center;
                            border-top:1px solid ' . $article_info_border_color . ';
                            border-bottom:1px solid ' . $article_info_border_color . ';
                        }
                        ' . $addon_id . ' .type8 article .content{
                            color:' . $article_content_color . ';
                            font-size:' . $article_content_size . 'px;
                            line-height: ' . $article_content_line_height . 'px;
                            margin-top:' . $article_content_top . 'px;
                        }
                    </style>
                ';
                $output .= '<div class="jwpf-addon-content type8">';

                foreach ($items as $key => $item) {
                    $output .= '
                        <div class="type8">
                            <div class="breadcrumb1">
                                <div class="title">' . $categories_list[0]['title'] . '</div>
                                <div class="crumb">
                                    <span class="position">当前位置：</span>';
                    if ($nav_first_title !== "") {
                        $output .= '<span>' . $nav_first_title . '</span> &nbsp; &gt; &nbsp';
                    }
                    if ($nav_title !== "") {
                        $output .= '<span>' . $nav_title . '</span> &nbsp; &gt; &nbsp;';
                    }

                    $output .= '<span class="bread_first">' . $categories_list[0]['title'] . '</span>';

                    $show_second_bread = '';
                    if ($type_parent === "all") {
                        $show_second_bread = $categories_list[1]->parent_id === $categories_list[0]->tag_id ? 'inline' : 'none';
                    } else {
                        $show_second_bread = 'inline';
                    }

                    $output .= '<span class="bread_second" style="display:' . $show_second_bread . ';">
                                        &nbsp; &gt; &nbsp;
                                        <span>' . ($categories_list[1]['title']) . '</span>
                                    </span>';

                    $output .= '</div>
                            </div>
                            <article>
                                <h1>' . $item->title . '</h1>
                                <div class="info">发布时间：' . $item->publish_up . '</div>
                                <div class="content">' . $item->introtext . '</div>
                            </article>
                        </div>
                    ';
                }

                $output .= '</div>';

                $output .= '<script>
                        jQuery(function($){
                            $("' . $addon_id . ' .jwpf-nav").on("click",function(e){
                                if(e.target.tagName==="A"){
                                    if($(e.target).parent().parent("ul").hasClass("er_type")){
                                        let bread_conent=$(e.target).parent().parent("ul").prev(".page_n").find("a").html();
                                        $("' . $addon_id . ' .type8 .title").html(bread_conent);
                                        $("' . $addon_id . ' .type8 .bread_first").html(bread_conent);
                                        $("' . $addon_id . ' .type8 .bread_second span").html($(e.target).html());
                                        $("' . $addon_id . ' .type8 .bread_second").show();
                                    }else if($(e.target).parent("li.page_n")){
                                        let bread_conent=$(e.target).parent("li.page_n").find("a").html();
                                        $("' . $addon_id . ' .type8 .title").html(bread_conent);
                                        $("' . $addon_id . ' .type8 .bread_first").html(bread_conent);
                                        $("' . $addon_id . ' .type8 .bread_second span").html("");
                                        $("' . $addon_id . ' .type8 .bread_second").hide();
                                    }
                                }
                            })
                        });
                    </script>';

                if ($show_page) {

                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div style="width: 100%; position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug">';
                    // 判断是不是第一页
                    //    if($page != 1) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
                    //    }
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    // 判断是不是最后一页
                    //    if($page != $all_page) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
                    //    }

                    $output .= '</div>';
                    // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';

                }
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }
            }
            if ($pro_type == 'type9') {
                $type9_icon_img = isset($settings->type9_icon_img) && $settings->type9_icon_img ? $settings->type9_icon_img : 'https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png';
                $type9_icon_bgImg = isset($settings->type9_icon_bgImg) && $settings->type9_icon_bgImg ? $settings->type9_icon_bgImg : 'https://oss.lcweb01.cn/joomla/20220613/d192734a26b96846918d12e0fcc256ba.png';
                $type9_icon_img_hover = isset($settings->type9_icon_img_hover) && $settings->type9_icon_img_hover ? $settings->type9_icon_img_hover : 'https://oss.lcweb01.cn/joomla/20220613/8de55642fd3c5e9d8531d789f8dbf4ae.png';
                $type9_icon_bgImg_hover = isset($settings->type9_icon_bgImg_hover) && $settings->type9_icon_bgImg_hover ? $settings->type9_icon_bgImg_hover : 'https://oss.lcweb01.cn/joomla/20220613/505cbe642c55dc01405f626b71234e1b.png';
                if ($title && $settings->content_hidden_title != 1) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }
                $output .= $this->listNav($goods_catInfo->title);
                $output .= '<div class="jwpf-addon-content pro09">';
                foreach ($items as $key => $item) {
                    $output .= '<div class="pro-item">';
                    if ($settings->show_target == '#') {
                        $output .= '<a href="#" >';
                    } else {
                        $output .= '<a href="' . $item->link . '"' . $link_target . ' >';
                    }
                    $output .= '<div class="img-box">';
                    $image = $item->image_thumbnail;
                    if (isset($image) && $image) {
                        $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                        $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
                    }
                    $output .= '
                        <div class="icon-bg-box">
                            <img src="' . $type9_icon_bgImg . '" class="icon" />
                            <img src="' . $type9_icon_bgImg_hover . '" class="icon icon-h" />
                        </div>
                        <div class="icon-box">
                            <img src="' . $type9_icon_img . '" class="icon" />
                            <img src="' . $type9_icon_img_hover . '" class="icon icon-h" />
                        </div>
                    ';
                    $output .= '</div>';
                    $output .= '<div class="con">' . $item->title . '</div>';
                    $output .= '</a>';
                    $output .= '</div>';
                }

                $output .= '</div>';

                if ($show_page) {

                    $all_page = 1;
                    if ($limit) {
                        $all_page = ceil($items_count / $limit);
                    }
                    $output .= '<div style="width: 100%; position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                    $output .= '<div class="page_plug">';
                    // 判断是不是第一页
                    //    if($page != 1) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&zcpcatid=' . $catid[0] . '"> << </a>';
                    //    }
                    $page_count = $settings->page_count ?? 0;
                    for ($i = 1; $i <= $all_page; $i++) //循环翻页按钮
                    {
                        if ($page == $i) {
                            $output .= '<a class="current">' . $i . '</a>';
                        } else {
                            $url = $this_obj->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'cpcatid', 'zcpcatid']);
                            if ($i > ($page + 1) && $i <= ($page + $page_count + 1)) {
                                if ($i == ($page + 2)) {
                                    $output .= '<span>...</span>';
                                }
                                $output .= '<a style="display:none" class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            } else {
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }

                        }
                    }
                    // 判断是不是最后一页
                    //    if($page != $all_page) {
                    //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                    //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page+1) . '&zcpcatid=' . $catid[0] . '"> >> </a>';
                    //    }

                    $output .= '</div>';
                    // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                    $output .= '</div>';

                }
                if ($link_articles) {

                    $icon_arr = array_filter(explode(' ', $all_articles_btn_icon));
                    if (count($icon_arr) === 1) {
                        $all_articles_btn_icon = 'fa ' . $all_articles_btn_icon;
                    }

                    if ($all_articles_btn_icon_position == 'left') {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? '<i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i> ' . $all_articles_btn_text : $all_articles_btn_text;
                    } else {
                        $all_articles_btn_text = ($all_articles_btn_icon) ? $all_articles_btn_text . ' <i class="' . $all_articles_btn_icon . '" aria-hidden="true"></i>' : $all_articles_btn_text;
                    }

                    if ($resource == 'k2') {
                        if (!empty($link_k2catid)) {
                            $output .= '<a href="' . urldecode(JRoute::_(K2HelperRoute::getCategoryRoute($link_k2catid))) . '" " id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    } else {
                        if (!empty($link_catid)) {
                            $output .= '<a href="' . JRoute::_(ContentHelperRoute::getCategoryRoute($link_catid)) . '" id="btn-' . $this->addon->id . '" class="jwpf-btn' . $all_articles_btn_class . '">' . $all_articles_btn_text . '</a>';
                        }
                    }
                }
            }
            if ($pro_type == 'type10') {
                if ($title && $settings->content_hidden_title != 1) {
                    $output .= '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>';
                }
                $output .= $this->listNav($goods_catInfo->title);
                $output .= '<div class="jwpf-addon-content swiper-container pro10">';
                $output .= '<div class="swiper-wrapper">';
                foreach ($items as $key => $item) {
                    $output .= '<div class="swiper-slide">';
                    if ($settings->show_target == '#') {
                        $output .= '<a href="#" >';
                    } else {
                        $output .= '<a href="' . $item->link . '"' . $link_target . ' >';
                    }
                    $output .= '<div class="img-box">';
                    $image = $item->image_thumbnail;
                    if (isset($image) && $image) {
                        $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
                        $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
                    }

                    $output .= '</div>';
                    $output .= '<div class="con">' . $item->title . '</div>';
                    $output .= '</a>';
                    $output .= '</div>';
                }

                $output .= '</div>';
                $output .= '</div>';
            }
        }

        return $output;
    }

    public function js()
    {
        $settings = $this->addon->settings;
        $addonId = $this->addon->id;
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';

        $js = '';

        if($pro_type == 'type10') {
            $js .= '
            jQuery(document).ready(function($){
                var swiper' . $addonId . ' = new Swiper(".pro10", {
                    effect: "coverflow",
                    slidesPerView: 3,
                    slidesPerGroup: 1,
                    centeredSlides: true,
                    centeredSlidesBounds: true,
                    slideToClickedSlide: true,
                    watchSlidesProgress: true,
                    loop: true,
                    coverflowEffect: {
                        rotate: 50,
                        stretch: 0,
                        depth: 80,
                        modifier: 1,
                        slideShadows : false
                    },
                    speed:800,
                    autoplay: {
                        delay: 2000,
                    },
			        observer: true,
				    observeParents: true,
                });
            })
            ';
        }

        return $js;
    }

    public function scripts()
    {
        $settings = $this->addon->settings;
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';

        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/newnav.js');

        if($pro_type == 'type10') {
            array_push($scripts, JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js');
        }

        return $scripts;
    }

    public function stylesheets()
    {
        $settings = $this->addon->settings;
        $pro_type = (isset($settings->pro_type) && $settings->pro_type) ? $settings->pro_type : 'type1';

        $style_sheet = array();
        if($pro_type == 'type10') {
            array_push($style_sheet, JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        }

        return $style_sheet;
    }

    public static function isComponentInstalled($component_name)
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true);
        $query->select('a.enabled');
        $query->from($db->quoteName('#__extensions', 'a'));
        $query->where($db->quoteName('a.name') . " = " . $db->quote($component_name));
        $db->setQuery($query);
        $is_enabled = $db->loadResult();

        return $is_enabled;
    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");

        return $url;
    }

}
