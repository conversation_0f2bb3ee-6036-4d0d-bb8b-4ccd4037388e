<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonSearch_box extends JwpagefactoryAddons
{
	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$layout_id = $_GET['layout_id'] ?? 0;
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$config = new JConfig();
		$search_type = (isset($settings->search_type)) ? $settings->search_type : 'type1';
		$detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        $search_button_font_size = (isset($settings->search_button_font_size)) ? $settings->search_button_font_size : 16;

		if ('jzt_dev_2.china9.cn' == $_SERVER['HTTP_HOST']) 
    {
			$urlpath = 'http://jzt_dev_1.china9.cn/';
		} else {
			$urlpath = 'https://zhjzt.china9.cn/';
		}
    $thisUrl1 = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';
		$thisUrl = $urlpath . "/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0" . '&site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';
		$postUrl = '/html/' . base64_encode($detail_page_id) . '.html?site_id=' . $site_id . '&company_id='.$company_id.'&site_id='.$site_id.'&layout_id='.$layout_id.'&';

		// 指定文章详情页ID
		$output = '';
		if($search_type=='type3')
        {
          $output .= '<style>
                            ' . $addon_id . '  .input_serch{
                              width: 679px;
                              height: 58px;
                              background: #FFFFFF;
                              border-left: 5px solid #B3B3B3;
                              border-top: 5px solid #B3B3B3;
                              border-bottom: 5px solid #B3B3B3;
                              border-right: none;
                          }
                          ' . $addon_id . '.input_submit{
                              width: 96px;
                              height: 70px;
                              background-color: #272831;
                              background-image: url("/components/com_jwpagefactory/addons/product_list/assets/images/serch.png");
                              background-repeat:no-repeat; 
                              background-position-x:center;
                              background-position-y:center;
                              border-right: 5px solid #B3B3B3;
                              border-top: 5px solid #B3B3B3;
                              border-bottom: 5px solid #B3B3B3;
                              border-left: none;
                          }
                          ' . $addon_id . '.box_serch{
                              display:flex;
                              
                          }
                      
                          ' . $addon_id . '.box_content{
                              margin-top: 63px;
                              width: 556px;
                              height: 107px;
                             
                              
                          }
                          ' . $addon_id . '.box_content_title{
                              font-size: 18px;
                              font-family: Microsoft YaHei;
                              font-weight: 400;
                              color: #FFFFFF;
                              line-height: 30px;
                              
                          }
                          ' . $addon_id . '.box_content_desc{
                              margin-top: 40px;
                              font-size: 16px;
                              font-family: Microsoft YaHei;
                              font-weight: 400;
                              color: #B2B2B2;
                              line-height: 30px;
                              
                          }
                      
                          ' . $addon_id . '.white_content { 
                              display: none;  
                              position: absolute;  
                              width:100%;
                              height: 100%;
                              background-color:rgba(0,0,0,0.1);
                              z-index:1002;  
                              overflow: auto;
                              top:0;
                              left:0;  
                          } 
                      
                          ' . $addon_id . '.white_content_div { 
                              position: absolute;  
                              left: 25%;
                              top: 25%; 
                              width:50%;
                              height: 50%;
                              z-index:1003;  
                              overflow: auto;  
                          } 
                      
                          ' . $addon_id . '.div-close { 
                              position: absolute;  
                              right: 50px;
                              top: 50px;  
                          }    
                        </style>';
    		$output .= '<a href="javascript:void(0)" onclick=document.getElementById(ligh").style.display="block";document.getElementById(fade).style.display="block"><img src="/components/com_jwpagefactory/addons/product_list/assets/images/serch.png" alt=""></a>
            <div id="light" class="white_content">
                <div class="white_content_div">
                    <form class="box_serch">
                        <input class="input_serch" type="text">
                        <input class="input_submit" type="submit" value="">
                    </form>
                    <div class="box_content">
                        <div class="box_content_title">其他人都在搜</div>
                        <div class="box_content_desc">巴洛克建筑 巴洛克音乐 巴洛克风格三天特征 巴洛克建筑艺术 巴洛克时期</div>
                    </div>
                </div>
                <a href="javascript:void(0)"';
                $output .= 'onclick="document.getElementById(light).style.display="none";document.getElementById(fade).style.display="none" class="div-close">
                    <img src="/components/com_jwpagefactory/addons/product_list/assets/images/close.png" alt=""></a>
            </div>';
            //        搜索详情页
    	
        }
        else
        {
        $output .= '<style>
                        ' . $addon_id . ' .search_box{
                          border: 1px solid;
                          outline-color: transparent;
                          border-color: ' . $settings->border_color . ';
                          winth:' . $settings->input_width . 'px;
                          padding-left: 12px;
                          background:' . $settings->input_bg . ';
                          color:' . $settings->input_color . ';
                        }
                         ' . $addon_id . ' .search_box::-webkit-input-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                       ' . $addon_id . ' .search_box::-moz-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                         ' . $addon_id . ' .search_box::-ms-input-placeholder{
                                color: ' . $settings->input_color_pla . ' !important;
                        }
                        ' . $addon_id . ' .search_btn{
                          font-size: '.$search_button_font_size.'px;
                          padding: 0 10px;
                          border: 1px solid ' . $settings->button_border_color . ';
                          cursor: pointer;
                          outline-color: transparent;
                          background:' . $settings->button_color . ';
                          color:' . $settings->button_font_color . ';
                        }
                    </style>';
		$output .= '<div style="display:flex;justify-content: ' . $settings->search_wz . ';height:' . $settings->input_height . 'px ">';
		$output .= '<input placeholder="' . $settings->search_input_text . '" style="width:' . $settings->input_width . 'px;border-radius:' . $settings->search_border_radius . 'px;" class="search_box" value="" onkeydown="ev' . $this->addon->id . '()" name="key" id="key">';
		$output .= '<input class="subBuootm" type="hidden" value="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($detail_page_id) . "&Itemid=0", $absolute = true) . '&site_id='.$site_id.'">';
		$output .= '<a style="text-align:center;width:' . $settings->button_width . 'px;border-radius:' . $settings->button_border_radius . 'px;margin-left:' . $settings->search_button_num . 'px;line-height:' . $settings->input_height . 'px;"';
    if ($search_type == 'type1') {
      $output .= 'href="'.$thisUrl.'"';
      // href="'.$thisUrl1.'"
    }
    $output .= 'class="search_btn tzd" >' . $settings->search_button_text . '</a>';
		$output .= "</div>";
        //        搜索详情页
		if($search_type=='type1'){
			$output .= '<script>
                           var hostname =\'http://\'+ window.location.hostname;
                           var hostnames =\'https://\'+ window.location.hostname;
                           var jzt_url = "'.$config->jzt_url.'"; 
                           var ijzt_url = "'.$config->ijzt_url.'";
                            jQuery("' . $addon_id . ' .search_btn").click(function(){
                              var search_name=jQuery("' . $addon_id . ' .search_box").val()
                              console.log(search_name)
                              if(search_name==""){
                                  alert("请输入要搜索的内容")
                              }else{

                                var aa=$("' . $addon_id . ' .search_box").val();
                                a = encodeURI(encodeURI(aa));
   
                                if(a){
                                  if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                    var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                                  } else {
                                    var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                                  }
                                    
                                    $("' . $addon_id . ' .tzd").attr("href",ff);
                                }
                                
                              }
                            });
                            $("' . $addon_id . ' .search_box").bind("input propertychange change",function(){
                                var aa=$("' . $addon_id . ' .search_box").val();
                                a = encodeURI(encodeURI(aa));
   
                                if(a){
                                  if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                    var ff="'.$thisUrl.'search_name="+a+"&ymid='.$detail_page_id.'";
                                  } else {
                                    var ff="'.$thisUrl.'?search_name="+a+"&ymid='.$detail_page_id.'";
                                  }
                                    
                                    $("' . $addon_id . ' .tzd").attr("href",ff);
                                }

                            })

                            function ev' . $this->addon->id . '()
                            {
                                var event = window.event || arguments.callee.caller.arguments[0];
                                if (event.keyCode == 13)
                                {
                                    var search_na=jQuery("' . $addon_id . ' #key").val();
                                    search_name = encodeURI(encodeURI(search_na));
                                    if(search_name==""){
                                        alert("请输入搜索内容")
                                    }else{ 

                                        if (hostname == jzt_url || hostname == ijzt_url || hostnames == jzt_url || hostnames == ijzt_url) {
                                            window.location.href = "' . $thisUrl . 'search_name=" + search_name;
                                        } else {
                                            window.location.href = "' . $thisUrl . '?search_name=" + search_name;
                                        }
                                      
                                    }
                                }
                            }
                    </script>';
		}
        //        咨询
		if($search_type=='type2'){
			$output .= '
        <script>
          jQuery("' . $addon_id . ' .search_btn").click(function(){
            var search_name=jQuery("' . $addon_id . ' .search_box").val()
            let myreg = /^[1][3-9][0-9]{9}$/;
            var tel = /^0\d{2,3}-?\d{7,8}$/;
            var emailreg = new RegExp("^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"); //正则表达式
            if (myreg.test(search_name) || tel.test(search_name) || emailreg.test(search_name)) {
              jQuery.ajax({
                type: "POST",
                url: "' . $urlpath . '/api/Consult/create",
                data: {
                  mobile: search_name,
                  "company_id" :"' . $company_id . '",
                  "site_id" :"' . $site_id . '"
                },
                success: function (e) {
                  if (e.code == 200) {
                    alert("提交成功")
                  }else {
                    alert(e.msg)
                  }
                }
              });
            } else {
              alert("格式不正确");
            }
        });
      </script>';
		}
    }
		return $output;
	}

	public function css()
	{

	}

	//用于设计器中显示
	public static function getTemplate()
	{

		$output = '';
		$output .= '<style>
<#
                        var addonId = "jwpf-addon-"+data.id;
#>
                         #{{ addonId }} .search_box{
                          border: 1px solid;
                          outline-color: transparent;
                          border-color: {{data.border_color}};
                          winth:{{data.input_width}}px;
                          padding-left: 12px;
                          background:{{data.input_bg}};
                          color:{{data.input_color}};
                          border-radius: {{data.search_border_radius}}px;
                        }
                         #{{ addonId }} .search_box::-webkit-input-placeholder{
                                color: {{data.input_color_pla}} !important;
                        }
                        #{{ addonId }} .search_box::-moz-placeholder{
                                color: {{data.input_color_pla}} !important;
                        }
                         #{{ addonId }} .search_box::-ms-input-placeholder{
                                color: {{data.input_color_pla}} !important;
                        }
                         #{{ addonId }} .search_btn{
                          font-size: {{data.search_button_font_size}}px;
                          padding: 0 10px;
                          border: 1px solid {{data.button_border_color}};
                          cursor: pointer;
                          outline-color: transparent;
                          background:{{data.button_color}};
                          color:{{data.button_font_color}};
                          border-radius: {{data.button_border_radius}}px;
                          margin-left: {{data.search_button_num}}px;
                        }
                    </style>';
		$output .= '<div style="display:flex;justify-content: {{data.search_wz}};height:{{data.input_height}}px ">';
		$output .= '<input placeholder="{{data.search_input_text}}" style="width:{{data.input_width}}px" class="search_box" value="">';
		$output .= '<button style="width:{{data.button_width}}px" class="search_btn">{{data.search_button_text}}</button>';
		$output .= "</div>";
		return $output;
	}
}