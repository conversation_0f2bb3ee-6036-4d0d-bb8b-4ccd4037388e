<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonSupport_window extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $title_desc = (isset($settings->title_desc) && $settings->title_desc) ? $settings->title_desc : 'Hi，我是你的客服龙龙，有需要随时找我哦~';
        $img_cw = (isset($settings->img_cw) && $settings->img_cw) ? $settings->img_cw : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220516/b881f40c559a7f021cd56f1e1cf2f7ff.gif';
        $tz_page_type = (isset($settings->tz_page_type) && $settings->tz_page_type) ? $settings->tz_page_type : 'external_links';
        $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
        $detail_page = (isset($settings->detail_page) && $settings->detail_page) ? $settings->detail_page : '';
        $target = (isset($settings->target) && $settings->target) ? $settings->target : '';

        $type_select = (isset($settings->type_select) && $settings->type_select) ? $settings->type_select : 'type01';
        $img_ewm = (isset($settings->img_ewm) && $settings->img_ewm) ? $settings->img_ewm : 'https://oss.lcweb01.cn/joomla/20220607/9071976a458df5e8fce7651c26a07907.jpg';
        $weibo_url = (isset($settings->weibo_url) && $settings->weibo_url) ? $settings->weibo_url : 'https://weibo.com/p/1006062488214857/home?from=page_100606&mod=TAB#place';
        $type2_top = (isset($settings->type2_top) && $settings->type2_top) ? $settings->type2_top : '200';

        $output = '';
        if($type_select=='type02'){
            $output .= '
                <style>
                    @media only screen and (min-width: 1480px) {
                        ' . $addon_id . ' .lqt_right1-a1 {
                            width: 90px;
                            position: fixed;
                            top: '.$type2_top. 'px;
                            right: calc(50% - 1440px/2);
                            z-index:900;
                        }
                        ' . $addon_id . ' .lqt_right1-a2 {
                            width: 100%;
                            position: relative;
                            margin-bottom: 28px;
                        }
                        ' . $addon_id . ' .lqt_right1-a3 {
                            font-size: 16px;
                            line-height: 16px;
                            color: #535353;
                            text-align: center;
                            margin-bottom: 16px;
                        }
                        ' . $addon_id . ' .lqt_right1-a4 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .lqt_right1-a5 {
                            width: 100%;
                            height: 66px;
                            position: relative;
                            display: block;
                        }
                        ' . $addon_id . ' .lqt_right1-a6 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                        }
                        ' . $addon_id . ' .lqt_right1-a6 img {
                            max-width: 29px;
                            max-height: 26px;
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                        }
                        ' . $addon_id . ' .lqt_right1-a7 {
                            width: 110px;
                            height: 110px;
                            position: absolute;
                            top: calc(50% - 110px/2);
                            right: calc(100% + 10px);
                            transform: scale(0);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .lqt_right1-a5:hover .lqt_right1-a7 {
                            transform: scale(1);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .lqt_right1-a7 img {
                            opacity: 1!important;
                        }
                        ' . $addon_id . ' .imgshow1 img {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(1) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(2) {
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1:hover img:nth-child(1) {
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1:hover img:nth-child(2) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                    }

                    @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                        ' . $addon_id . ' .imgshow1 img {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(1) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(2) {
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1:hover img:nth-child(1) {
                            opacity: 0;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .imgshow1:hover img:nth-child(2) {
                            opacity: 1;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .lqt_right1-a1 {
                            width: 70px;
                            position: fixed;
                            top: calc( '.$type2_top. ' - 40px );
                            right: calc(50% - 960px/2);
                            z-index:9999;
                        }
                        ' . $addon_id . ' .lqt_right1-a2 {
                            width: 100%;
                            position: relative;
                            margin-bottom: 20px;
                        }
                        ' . $addon_id . ' .lqt_right1-a3 {
                            font-size: 14px;
                            line-height: 14px;
                            color: #535353;
                            text-align: center;
                            margin-bottom: 14px;
                        }
                        ' . $addon_id . ' .lqt_right1-a4 {
                            width: 100%;
                        }
                        ' . $addon_id . ' .lqt_right1-a5 {
                            width: 100%;
                            height: 40px;
                            position: relative;
                            display: block;

                        }
                        ' . $addon_id . ' .lqt_right1-a6 {
                            width: 100%;
                            height: 100%;
                            position: relative;
                        }
                        ' . $addon_id . ' .lqt_right1-a6 img {
                            max-width: 26px;
                            max-height: 24px;
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                        }
                        ' . $addon_id . ' .lqt_right1-a7 {
                            width: 90px;
                            height: 90px;
                            position: absolute;
                            top: calc(50% - 90px/2);
                            right: calc(100% + 10px);
                            transform: scale(0);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .lqt_right1-a5:hover .lqt_right1-a7 {
                            transform: scale(1);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .lqt_right1-a7 img {
                            opacity: 1!important;
                        }
                    }

                    @media only screen and (max-width: 1023px) {
                        ' . $addon_id . ' .lqt_right1-a1 {
                            display: none;
                        }
                        ' . $addon_id . ' .imgshow1 img {
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(1) {
                            opacity: 1;
                        }
                        ' . $addon_id . ' .imgshow1 img:nth-child(2) {
                            opacity: 0;
                        }
                    }

                </style>
            ';

            if ($tz_page_type == 'Internal_pages') {
                $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                $arrray = explode('&', $idurl);
                foreach ($arrray as $key => $value) {
                    if (substr($value, 0, 3) == 'id=') {
                        $arrray[$key] = 'id=' . base64_encode($detail_page_id);
                    }
                }

                $return = implode('&', $arrray);
            } else {
                $return = $detail_page;
            }

            $output .= '
                <div class="lqt_right1-a1">
                    <div class="lqt_right1-a2 i100"><a href="'.$return.'" target="'.$target.'"><img src="'.$img_cw.'"></a></div>
                    <div class="lqt_right1-a3">分享</div>
                    <div class="lqt_right1-a4">
                        <div class="lqt_right1-a5 imgshow1">
                            <div class="lqt_right1-a6">
                                <img src="https://oss.lcweb01.cn/joomla/20220607/c8f8b7b8bc7562567b93b69311e092b7.png">
                                <img src="https://oss.lcweb01.cn/joomla/20220607/67d55e11341735d48f9bf783fd8f2738.png">
                            </div>
                            <div class="lqt_right1-a7 i100"><img src="'.$img_ewm.'"></div>
                        </div>
                        <a href="'.$weibo_url.'" target="_blank" class="lqt_right1-a5 imgshow1">
                            <div class="lqt_right1-a6">
                                <img src="https://oss.lcweb01.cn/joomla/20220607/a900b70908bea4f91242610094563351.png">
                                <img src="https://oss.lcweb01.cn/joomla/20220607/b3467b34f650d410684a19d219ed2aff.png">
                            </div>
                        </a>
                    </div>
                </div>
            ';
        }else{

            $output .= '
                <style>
                    ' . $addon_id . ' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    @media only screen and (max-width: 1399px) and (min-width: 1023px){
                        ' . $addon_id . ' .right1-a1 {
                            width: 105px;
                            position: fixed;
                            bottom: 50px;
                            right: -35px;
                            z-index: 7;
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a2 {
                            width: 97px;
                            height: 114px;
                            overflow: hidden;
                            transform: rotate(-30deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a2 div {
                            width: 125px;
                            height: 137px;
                            position: absolute;
                            top: -11px;
                            left: -11px;
                        }
                        ' . $addon_id . ' .i100 {
                            overflow: hidden;
                        }
                        ' . $addon_id . ' .right1-a3 {
                            width: 200px;
                            height: 66px;
                            border-radius: 30px 30px 0 30px;
                            background: #d9012a;
                            padding: 7px 12px 0 20px;
                            font-size: 14px;
                            line-height: 26px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: 50px;
                            right: 100%;
                            transform: rotateX(90deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a1:hover {
                            right: -10px;
                            transition: 0.5s;
                            z-index: 8;
                        }
                        ' . $addon_id . ' .right1-a1:hover .right1-a2 {
                            transform: rotate(0deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a1:hover .right1-a3 {
                            transform: rotateX(0deg);
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (min-width: 1399px){
                        ' . $addon_id . ' .right1-a1:hover {
                            right: -10px;
                            transition: 0.5s;
                            z-index: 8;
                        }
                        ' . $addon_id . ' .right1-a3 {
                            width: 230px;
                            height: 78px;
                            border-radius: 30px 30px 0 30px;
                            background: #d9012a;
                            padding: 9px 16px 0 34px;
                            font-size: 16px;
                            line-height: 30px;
                            color: #fff;
                            font-weight: bold;
                            position: absolute;
                            top: 50px;
                            right: 100%;
                            transform: rotateX(90deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a1:hover .right1-a3 {
                            transform: rotateX(0deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a1 {
                            width: 105px;
                            position: fixed;
                            top: auto;
                            bottom: 50px;
                            right: -35px;
                            z-index: 7;
                            transition: 0.5s;
                            display: block;
                            /* display: none; */
                            opacity: 1;
                        }
                        ' . $addon_id . ' .right1-a2 {
                            width: 97px;
                            height: 114px;
                            overflow: hidden;
                            transform: rotate(-30deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a1:hover .right1-a2 {
                            transform: rotate(0deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a2 div {
                            width: 125px;
                            height: 137px;
                            position: absolute;
                            top: -11px;
                            left: -11px;
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        ' . $addon_id . ' .right1-a1 {
                            width: 105px;
                            position: fixed;
                            bottom: 25px;
                            right: -40px !important;
                            z-index: 7;
                            transition: 0.5s;
                            transform: scale(0.6);
                            /* display: none; */
                        }
                        ' . $addon_id . ' .right1-a2 {
                            width: 90px;
                            height: 114px;
                            overflow: hidden;
                            transform: rotate(-30deg);
                            transition: 0.5s;
                        }
                        ' . $addon_id . ' .right1-a2 div {
                            width: 125px;
                            height: 132px;
                            position: absolute;
                            top: -11px;
                            left: -11px;
                        }
                        ' . $addon_id . ' .right1-a3 {
                            display: none;
                        }
                    }
                    ' . $addon_id . ' a>img {
                        width: 100%;
                    }
                    ' . $addon_id . ' a {
                        color: inherit;
                        text-decoration: none;
                    }
                </style>
            ';
            if ($tz_page_type == 'Internal_pages') {
                $idurl = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page']);
                $arrray = explode('&', $idurl);
                foreach ($arrray as $key => $value) {
                    if (substr($value, 0, 3) == 'id=') {
                        $arrray[$key] = 'id=' . base64_encode($detail_page_id);
                    }
                }

                $return = implode('&', $arrray);
            } else {
                $return = $detail_page;
            }
            $output .= '
                <div class="right1-a1" style="opacity: 1; display: block;">
                    <div class="right1-a2">
                        <div class="i100">
                            <a href="' . $return . '"
                                target="'.$target.'">
                                <img src="' . $img_cw . '"
                                    oncontextmenu="return false;"></a>
                        </div>
                    </div>
                    <div class="right1-a3">
                        <a href="' . $return . '"
                            target="_blank">' . $title_desc . '</a>
                    </div>
                </div>
                <script>
                    window.addEventListener("wheel", function(e) {
                        if ($(window).scrollTop() > $(window).height() / 3 * 2) {
                            //$("' . $addon_id . ' .right1-a1").css("opacity", "1");
                            $("' . $addon_id . ' .right1-a1").css("display", "block");
                        } else {
                            //$("' . $addon_id . ' .right1-a1").css("opacity", "0");
                            $("' . $addon_id . ' .right1-a1").css("display", "none");
                        }
                    }, {
                        passive: false
                    });
                </script>
            ';
        }

        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
        $scripts = '';
        return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
