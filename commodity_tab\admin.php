<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'commodity_tab',
        'title' => '商品分类选项卡',
        'desc' => '商品分类选项卡',
        'category' => '商品',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
                    )
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_margin_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_margin_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
                    'placeholder' => '10',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'style' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DESC'),
                    'values' => array(
                        'modern' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_MODERN'),
                        'tabs' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_DEFAULT'),
                        'pills' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_PILLS'),
                        'lines' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_LINES'),
                        'custom' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_CUSTOM'),
                    ),
                    'std' => 'tabs'
                ),

                // 非自定义导航块位置
                'nav_all_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'nav_all_bg_color' => array(
                    'type' => 'color',
                    'title' => '导航条背景颜色',
                    'std' => '#000',
                    'depends' => array(array('style', '!=', 'custom')),
                ),
                'type_parent' => array(
                    'type' => 'select',
                    'title' => '分类显示',
                    'desc' => '分类显示',
                    'values' => array(
                        'type1' => '一级分类',
                        'type2' => '二级分类',
                        'all' => '混合显示'
                    ),
                    'std' => 'type1',
                ),
                'type_start' => array(
                    'type' => 'number',
                    'title' => '从第n个分类开始显示',
                    'desc' => '从第n个分类开始显示',
                    'std' => '1'
                ),
                'type_num' => array(
                    'type' => 'number',
                    'title' => '显示n条分类(0为不限)',
                    'desc' => '显示n条分类',
                    'std' => '10'
                ),
                'separator_options' => array(
                    'type' => 'separator',
                    'title' => '内容插件选项'
                ),

                'pro_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                        'type2' => '布局2',
                    ),
                    'std' => 'type1',
                ),
                'pro_font_color_type2_title' => array(
                    'type' => 'color',
                    'title' => JText::_('标题字体颜色'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => '#000000',
                ),
                'pro_font_title_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 200,
                    'min' => 0
                ),
                'show_intro' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示简介'),
                    'depends' => array('pro_type' => 'type2'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => 1,
                ),
                'pro_font_color_type2_intext' => array(
                    'type' => 'color',
                    'title' => JText::_('简介字体颜色'),
                    'depends' => array('show_intro' => 1),
                    'std' => '#000000',
                ),
                'pro_font_intext_size_type2' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'depends' => array('show_intro' => 1),
                    'max' => 200,
                    'min' => 0
                ),
                'show_price' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示价格'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => 1
                ),
                'price_position' => array(
                    'type' => 'select',
                    'title' => '价格位置',
                    'depends' => array('show_price' => '1'),
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右'
                    ),
                    'std' => 'flex-start',
                ),
                'price_new_old_margin' => array(
                    'type' => 'slider',
                    'title' => JText::_('原价现价之间的距离'),
                    'depends' => array('show_price' => 1),
                    'max' => 100,
                    'min' => 0
                ),
                'price_new_value_color' => array(
                    'type' => 'color',
                    'title' => JText::_('现价字体颜色'),
                    'depends' => array('show_price' => 1),
                    'std' => '#000000',
                ),
                'price_new_value_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('现价字体大小'),
                    'depends' => array('show_price' => 1),
                    'max' => 200,
                    'min' => 0
                ),
                'price_old_value_color' => array(
                    'type' => 'color',
                    'title' => JText::_('原价字体颜色'),
                    'depends' => array('show_price' => 1),
                    'std' => '#000000',
                ),
                'price_old_value_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('原价字体大小'),
                    'depends' => array('show_price' => 1),
                    'max' => 200,
                    'min' => 0
                ),
                'pro_font_color_bg_type2' => array(
                    'type' => 'color',
                    'title' => JText::_('字体背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array('pro_type' => 'type2'),
                ),
                'img_animated' => array(
                    'type' => 'select',
                    'title' => '选择图片动画',
                    'desc' => '图片动画',
                    'depends' => array('pro_type' => 'type2'),
                    'values' => array(
                        'animated1' => '无',
                        'animated2' => '放大',
                    ),
                    'std' => 'animated1',
                ),
                'box_type2_shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('盒子阴影颜色'),
                    'depends' => array('pro_type' => 'type2'),
                    'std' => '#ffffff',
                ),
                'box_type2_shadow_x' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影水平偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_Y' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影垂直偏移'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_mh' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影模糊'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'box_type2_shadow_kz' => array(
                    'type' => 'slider',
                    'title' => JText::_('阴影扩展'),
                    'depends' => array('pro_type' => 'type2'),
                    'max' => 100,
                    'min' => 0
                ),
                'pro_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'depends' => array('pro_type' => 'type1'),
                    'std' => '#ffffff',
                ),
                'pro_font_color_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('字体背景颜色'),
                    'std' => '#000000',
                    'depends' => array('pro_type' => 'type1'),
                ),
                'pro_font_color_bg_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体背景高度'),
                    'depends' => array('pro_type' => 'type1'),
                    'max' => 2000,
                    'min' => 0
                ),

                'show_title' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('标题是否固定显示'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'depends' => array('pro_type' => 'type1'),
                    'std' => '0'
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),

                'resource' => array(
                    'type' => 'select',
                    'title' => '选择商品资源',
                    'desc' => '选择商品资源',
                    'values' => array(
                        'article' => '商品资源',
                    ),
                    'std' => 'article',
                ),

                'shop_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择商品分类'),
                    'desc' => '商品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_shop')['list'],
                ),

                'k2catid' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_K2_CATID_DESC'),
                    'depends' => array('resource' => 'k2'),
                    'values' => JwPageFactoryBase::k2CatList(),
                    'multiple' => true,
                ),

                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),

                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10'
                ),

                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                ),

                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                ),

                // 自定义导航块
                'nav_block_positon' => array(
                    'type' => 'select',
                    'title' => '导航块位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(array('style', '=', 'custom'),array('nav_position', '=', 'nav-top')),
                ),
                //Custom Tab Style
                'custom_tab_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_STYLE_OPTIONS'),
                    'std' => 'navigation',
                    'values' => array(
                        array(
                            'label' => '导航',
                            'value' => 'navigation'
                        ),
                        array(
                            'label' => '导航图标或者图片',
                            'value' => 'icon_image'
                        ),
                        array(
                            'label' => '内容',
                            'value' => 'content'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom')
                    ),
                ),

                'tab_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_SEPERATOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_position' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_POSITION_DESC'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'nav-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'nav-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'nav-top' => JText::_('上')
                    ),
                ),
                'nav_gutter' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_GUTTER_DESC'),
                    'responsive' => true,
                    'max' => 100,
                    'std' => array('md' => 15),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_STYLE_DESC'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '日常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_NAV_WIDTH_DESC'),
                    'responsive' => true,
                    'max' => 100,
                    'std' => array('md' => 30, 'sm' => 30, 'xs' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_COLOR'),
                    'std' => '#fff',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BG_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'max' => 400,
                    'responsive' => true,
                    'std' => array('md' => 16),
                ),
                'nav_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'nav_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-nav-custom a{ font-family: {{ VALUE }}; }'
                    )
                ),
                'nav_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_FONT_STYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER'),
                    'std' => '1px 1px 1px 1px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'nav_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_MARGIN'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '0px 0px 5px 0px',
                ),
                'nav_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_NAV_PADDING'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                'nav_text_align' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_TEXT_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                    'values' => array(
                        'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                        'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
                        'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                    ),
                    'std' => 'left',
                ),
                //Hover Nav Style
                'hover_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BG'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'hover_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_HOVER_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Active Nav Style
                'active_tab_bg' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_COLOR'),
                    'std' => '#333333',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_width' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                'active_tab_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ACTIVE_BORDER_COLOR'),
                    'std' => '',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'navigation'),
                    ),
                ),
                //Icon or Image style
                'image_or_icon_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_ICON_OR_IMAGE_STYLE'),
                    'std' => 'icon_style',
                    'values' => array(
                        array(
                            'label' => '图标样式',
                            'value' => 'icon_style'
                        ),
                        array(
                            'label' => '图片样式',
                            'value' => 'image_style'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                    ),
                ),
                //Icon Style
                'icon_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ICON_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'nav_icon_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_POSITION'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'icon_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_SIZE'),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => 16),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_HOVER'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'hover'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_color_active' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_COLOR_ACTIVE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'active'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                ),
                'icon_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_ICON_MARGIN'),
                    'responsive' => true,
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'icon_style'),
                    ),
                    'std' => '',
                ),
                //Image Style
                'image_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_IMG_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'nav_image_postion' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_ALIGNMENT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('COM_JWPAGEFACTORY_GLOBAL_TOP'),
                        'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
                        'bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
                        'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
                    ),
                    'std' => 'left',
                ),
                'image_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_HEIGHT'),
                    'responsive' => true,
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_JS_SLIDER_ITEM_IMG_WIDTH'),
                    'responsive' => true,
                    'max' => 200,
                    'std' => array('md' => 30),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                ),
                'image_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'responsive' => true,
                    'std' => '0px',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('nav_style', '=', 'normal'),
                        array('custom_tab_style', '=', 'icon_image'),
                        array('image_or_icon_style', '=', 'image_style'),
                    ),
                    'std' => '',
                ),
                //Content Style
                'content_separator' => array(
                    'type' => 'separator',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_OPTIONS'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_backround' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_BG'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_FONT_SIZE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'max' => 400,
                    'responsive' => true,
                    'std' => array('md' => ''),
                ),
                'content_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_LINEHEIGHT'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                    'std' => array('md' => ''),
                ),
                'content_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-tab-custom-content > div{ font-family: {{ VALUE }}; }'
                    )
                ),
                'content_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONTSTYLE'),
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER'),
                    'std' => 1,
                    'max' => 20,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTEN_BORDER_RADIUS'),
                    'std' => '',
                    'max' => 400,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_BORDER_COLOR'),
                    'std' => '#e5e5e5',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'show_boxshadow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_SHOW_DESC'),
                    'std' => 1,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_BOXSHADOW_COLOR'),
                    'std' => '#000',
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_horizontal' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_HORIZONTAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_vertical' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_VERTICAL'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_blur' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_BLUR'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'shadow_spread' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_SHADOW_SPREAD'),
                    'std' => '',
                    'max' => 100,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('show_boxshadow', '=', 1),
                        array('custom_tab_style', '=', 'content'),
                    ),
                ),
                'content_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_MARGIN'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '',
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('COM_JWPAGEFACTORY_TAB_CONTENT_PADDING'),
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '10px 10px 10px 10px',
                ),
                'content_item_margin' => array(
                    'type' => 'margin',
                    'title' => '内容项外边距',
                    'responsive' => true,
                    'depends' => array(
                        array('style', '=', 'custom'),
                        array('custom_tab_style', '=', 'content'),
                    ),
                    'std' => '0 0 30px 0',
                ),
                'class' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);
