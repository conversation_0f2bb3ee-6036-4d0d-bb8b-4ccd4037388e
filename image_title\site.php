<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonImage_title extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$title_position = (isset($settings->title_position) && $settings->title_position) ? $settings->title_position : 'top';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		//Options
		$image = (isset($settings->image) && $settings->image) ? $settings->image : '';
		$alt_text = (isset($settings->alt_text) && $settings->alt_text) ? $settings->alt_text : '';
		$position = (isset($settings->position) && $settings->position) ? $settings->position : '';
		$link = (isset($settings->link) && $settings->link) ? $settings->link : '';
		$target = (isset($settings->target) && $settings->target) ? 'target="' . $settings->target . '" rel="noopener noreferrer"' : '';
		$open_lightbox = (isset($settings->open_lightbox) && $settings->open_lightbox) ? $settings->open_lightbox : 0;
		$image_overlay = (isset($settings->overlay_color) && $settings->overlay_color) ? 1 : 0;
		$detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
		$link_target = (isset($settings->link_new_tab) && $settings->link_new_tab) ? 'target="_blank" rel="noopener noreferrer"' : '';

		


		//Lazyload image
		$dimension = $this->get_image_dimension($image);
		$dimension = implode(' ', $dimension);
		$attribs='';
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$layout_id = $_GET['layout_id'] ?? 0;

		if($detail_page_id){
			$id=base64_encode($detail_page_id);
			$link = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;

		}
		$placeholder = $image == '' ? false : $this->get_image_placeholder($image);
		if (strpos($image, "http://") !== false || strpos($image, "https://") !== false) {
			$image = $image;
		} else {
			$image = JURI::base(true) . '/' . $image;
		}

		$output = '';



		if ($image) {
			$output .= '<div class="jwpf-addon jwpf-addon-single-image ' . $position . ' ' . $class . '">';
			$output .= '<div class="xtb_zt">';
			
			if (empty($alt_text)) {
				if (!empty($title)) {
					$alt_text = $title;
				} else {
					$alt_text = basename($image);
				}
			}

			if ($open_lightbox) {
				$output .= '<a class="jwpf-magnific-popup jwpf-addon-image-overlay-icon" data-popup_type="image" data-mainclass="mfp-no-margins mfp-with-zoom" href="' . $image . '">+</a>';
			}

			if (!$open_lightbox) {
				$output .= ($link) ? '<a ' . $target . ' href="' . $link . '">' : '';
			}
			
			$output .='<div class="xtb_img">';
				
				$output .= '<img class="jwpf-img-responsive' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $image) . '" ' . ($placeholder ? 'data-large="' . $image . '"' : '') . ' alt="' . $alt_text . '" title="' . $title . '" ' . ($dimension ? $dimension : '') . ' loading="lazy">';
		   	$output .= '</div>';
		    $output .= '<div class="xtb_tit">
					        '.$title.'
					    </div>';


			if (!$open_lightbox) {
				$output .= ($link) ? '</a>' : '';
			}

			$output .= '</div>';

			$output .= '</div>';

		}

		return $output;
	}

	public function scripts()
	{
		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.magnific-popup.min.js');
	}

	public function stylesheets()
	{
		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/magnific-popup.css');
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
		$open_lightbox = (isset($settings->open_lightbox) && $settings->open_lightbox) ? $settings->open_lightbox : 0;
		$style = (isset($settings->overlay_color) && $settings->overlay_color) ? 'background-color: ' . $settings->overlay_color . ';' : '';


		$style_img = '';
		$style_img_sm = '';
		$style_img_xs = '';
		$style_img = (isset($settings->border_radius) && $settings->border_radius) ? 'border-radius: ' . $settings->border_radius . 'px;' : '';
		$style_img .= (isset($settings->image_width) && $settings->image_width) ? 'width:' . $settings->image_width . 'px;' : '';
		$style_img .= (isset($settings->image_width) && $settings->image_width) ? 'max-width:' . $settings->image_width . 'px;' : '';
		$style_img .= (isset($settings->image_height) && $settings->image_height) ? 'height:' . $settings->image_height . 'px;' : '';

		$style_img_sm .= (isset($settings->image_width_sm) && $settings->image_width_sm) ? 'max-width:' . $settings->image_width_sm . 'px;' : '';
		$style_img_sm .= (isset($settings->image_height_sm) && $settings->image_height_sm) ? 'height:' . $settings->image_height_sm . 'px;' : '';

		$style_img_xs .= (isset($settings->image_width_xs) && $settings->image_width_xs) ? 'max-width:' . $settings->image_width_xs . 'px;' : '';
		$style_img_xs .= (isset($settings->image_height_xs) && $settings->image_height_xs) ? 'height:' . $settings->image_height_xs . 'px;' : '';

		$image_width = (isset($settings->image_width) && $settings->image_width) ? $settings->image_width : 200;
		$image_width_sm = (isset($settings->image_width_sm) && $settings->image_width_sm) ? $settings->image_width_sm : 200;
		$image_width_xs = (isset($settings->image_width_xs) && $settings->image_width_xs) ? $settings->image_width_xs : 200;
		$image_height = (isset($settings->image_height) && $settings->image_height) ? $settings->image_height : 200;
		$image_height_sm = (isset($settings->image_height_sm) && $settings->image_height_sm) ? $settings->image_height_sm : 200;
		$image_height_xs = (isset($settings->image_height_xs) && $settings->image_height_xs) ? $settings->image_height_xs : 200;
		$isFixed = (isset($settings->isFixed) && $settings->isFixed) ? $settings->isFixed : 0;
		$fixed_style = (isset($settings->fixed_style) && $settings->fixed_style) ? $settings->fixed_style : 'left';
		$fixed_top = (isset($settings->fixed_top) && $settings->fixed_top) ? $settings->fixed_top : 40;
		$fixed_right = (isset($settings->fixed_right) && $settings->fixed_right) ? $settings->fixed_right : 0;
		$fixed_bottom = (isset($settings->fixed_bottom) && $settings->fixed_bottom) ? $settings->fixed_bottom : 0;
		$fixed_left = (isset($settings->fixed_left) && $settings->fixed_left) ? $settings->fixed_left : 0;

		// css
		$title_fontsize_md = (isset($settings->title_fontsize) && $settings->title_fontsize) ? $settings->title_fontsize : '14';
		$title_fontsize_sm = (isset($settings->title_fontsize_sm) && $settings->title_fontsize_sm) ? $settings->title_fontsize_sm : '14';
		$title_fontsize_xs = (isset($settings->title_fontsize_xs) && $settings->title_fontsize_xs) ? $settings->title_fontsize_xs : '14';
		
		$title_lineheight_md = (isset($settings->title_lineheight) && $settings->title_lineheight) ? $settings->title_lineheight : '40';
		$title_lineheight_sm = (isset($settings->title_lineheight_sm) && $settings->title_lineheight_sm) ? $settings->title_lineheight_sm : '30';
		$title_lineheight_xs = (isset($settings->title_lineheight_xs) && $settings->title_lineheight_xs) ? $settings->title_lineheight_xs : '30';



		$title_text_color = (isset($settings->title_text_color) && $settings->title_text_color) ? $settings->title_text_color : '#333';
		$title_hg_color = (isset($settings->title_hg_color) && $settings->title_hg_color) ? $settings->title_hg_color : '#555';
		$title_margin_md = (isset($settings->title_margin) && $settings->title_margin) ? $settings->title_margin : '0px 0px 0px 0px';
		$title_margin_sm = (isset($settings->title_margin_sm) && trim($settings->title_margin_sm)) ? $settings->title_margin_sm : '0px 0px 0px 0px';
		$title_margin_xs = (isset($settings->title_margin_xs) && trim($settings->title_margin_xs)) ? $settings->title_margin_xs : '5px 0px 5px 0px';

		$title_padding_md = (isset($settings->title_padding) && $settings->title_padding) ? $settings->title_padding : '20px 20px 20px 20px';
		$title_padding_sm = (isset($settings->title_padding_sm) && trim($settings->title_padding_sm)) ? $settings->title_padding_sm : '10px 10px 10px 10px';
		$title_padding_xs = (isset($settings->title_padding_xs) && trim($settings->title_padding_xs)) ? $settings->title_padding_xs : '10px 10px 10px 10px';

		$wbk_color = (isset($settings->wbk_color) && $settings->wbk_color) ? $settings->wbk_color : '#eee';
		$bg_color = (isset($settings->bg_color) && $settings->bg_color) ? $settings->bg_color : '#fff';
		$hgbg_color = (isset($settings->hgbg_color) && $settings->hgbg_color) ? $settings->hgbg_color : '#fff';


		$css = '';

		$css .= 
			$addon_id . ' .xtb_zt{
				border:1px solid '.$wbk_color.';
				border-radius:3px;
				background: '.$bg_color.';
				overflow: hidden;
				padding:'.$title_padding_md.';
				margin:'.$title_margin_md.';
				text-align:center;
				cursor:pointer;
			}'.
			$addon_id . ' .xtb_zt a{
				color:'.$title_text_color.';
				font-size:'.$title_fontsize_md.'px;
			}'.
		    $addon_id . ' .xtb_img{
		    	width:30px;
		    	height:30px;
		    	margin:0 auto;
		    	vertical-align: middle;
		    }'.
		    $addon_id . ' .xtb_tit{
		    	line-height:'.$title_lineheight_md.'px;
		    	font-size:'.$title_fontsize_md.'px;
		    	height:'.$title_lineheight_md.'px;
		    	margin-top:5px;
		    	color:'.$title_text_color.';
		    	transition:0.5s;
		    }'.
		    $addon_id . ' .xtb_zt:hover{
				background: '.$hgbg_color.';
			}'.
			$addon_id . '{
		    	transition:all 0.6s;
			}'.
			$addon_id . ' .xtb_zt:hover a{
				color: '.$title_hg_color.';

			}'.

			$addon_id . ' .xtb_img img{
		    	transition:all 0.6s;
			}'.
			$addon_id . ' .xtb_zt:hover .xtb_tit{
				color: '.$title_hg_color.';
			}';


		if($isFixed == 1) {
			$css .= $addon_id . ':hover{
			
				';
			switch ($fixed_style) {
				case 'top':
					$css .= '
						transform: translateY(-10px);
						-webkit-transform:translateY(-10px);
						transition: All 0.6s ease-in-out;
		            	-webkit-transition: All 0.6s ease-in-out;
		           	 	-moz-transition: All 0.6s ease-in-out;
		            	-o-transition: All 0.6s ease-in-out;
					';
					break;
				case 'fanzhuan':
					$css .= '
						transform: rotateY(360deg);
			            -webkit-transform: rotateY(360deg);
			            -moz-transform: rotateY(360deg);
			            -o-transform: rotateY(360deg);
			            -ms-transform: rotateY(360deg);

			            transition: All 0.7s ease-in-out;
				        -webkit-transition: All 0.7s ease-in-out;
				        -moz-transition: All 0.7s ease-in-out;
				        -o-transition: All 0.7s ease-in-out;
					';
					break;

			}

			$css .= '}';


			if($fixed_style == 'huaguo') {
				$css .= $addon_id . ':hover .xtb_img img{
					transform: scaleX(1.3) scaleY(1.3);
			        transition: All 0.6s ease-in-out;
			        -webkit-transition: All 0.6s ease-in-out;
			        -moz-transition: All 0.6s ease-in-out;
			        -o-transition: All 0.6s ease-in-out';
				$css .= '}';
			}

			
			$css .='.jwpf-animated{
				animation-fill-mode:none!important;
				-webkit-animation-fill-mode:none!important';
			$css .= '}';


		}
		if ($open_lightbox && $style) {
			$css .= $addon_id . ' .jwpf-addon-image-overlay{';
			$css .= $style;
			$css .= $style_img;
			$css .= '}';
		}

		$css .= $addon_id . ' img{' . $style_img . '}';
		if ($title_padding) {
			$css .= $addon_id . ' .jwpf-addon-title{padding: ' . $title_padding . '}';
		}

		$css .= '@media (min-width: 768px) and (max-width: 991px) {';
		if($isFixed == 1) {
			$css .= $addon_id . ' .jwpf-addon {
				width: ' . $image_width_sm . 'px;
				height: ' . $image_height_sm . 'px;
			}';
		}
		if ($title_padding_sm) {
			$css .= $addon_id . ' .jwpf-addon-title{padding: ' . $title_padding_sm . '}';
		}
		$css .= $addon_id . ' img{' . $style_img_sm . '}';
		$css .= '}';
		$css .= '@media (max-width: 767px) {';
		if($isFixed == 1) {
			$css .= $addon_id . ' .jwpf-addon {
				width: ' . $image_width_xs . 'px;
				height: ' . $image_height_xs . 'px;
			}';
		}
		if ($title_padding_xs) {
			$css .= $addon_id . ' .jwpf-addon-title{padding: ' . $title_padding_xs . '}';
		}
		$css .= $addon_id . ' img{' . $style_img_xs . '}';
		$css .= '}';

		return $css;

	}

	public static function getTemplate()
	{
		$output = '
		<#
			var image_overlay = 0;
			if(!_.isEmpty(data.overlay_color)){
				image_overlay = 1;
			}
			var open_lightbox = parseInt(data.open_lightbox);
			var title_font_style = data.title_fontstyle || "";

			var alt_text = data.alt_text;

			if(_.isEmpty(alt_text)){
				if(!_.isEmpty(data.title)){
					alt_text = data.title;
				}
			}

		#>
		<#
		//开启浮动
		var image_width = (!_.isEmpty(data.image_width) && data.image_width) ? data.image_width : 50;
		var image_height = (!_.isEmpty(data.image_height) && data.image_height) ? data.image_height : 50;
		var isFixed = (!_.isEmpty(data.isFixed) && data.isFixed) ? data.isFixed : 0;
		var fixed_style = (!_.isEmpty(data.fixed_style) && data.fixed_style) ? data.fixed_style : "left";
		var fixed_top = (!_.isEmpty(data.fixed_top) && data.fixed_top) ? data.fixed_top : 40;
		var fixed_right = (!_.isEmpty(data.fixed_right) && data.fixed_right) ? data.fixed_right : 0;
		var fixed_bottom = (!_.isEmpty(data.fixed_bottom) && data.fixed_bottom) ? data.fixed_bottom : 0;
		var fixed_left = (!_.isEmpty(data.fixed_left) && data.fixed_left) ? data.fixed_left : 0;
		
		#>
		<style>
			
			#jwpf-addon-{{ data.id }} .xtb_zt{
				border:1px solid {{data.wbk_color}};
				border-radius:2px;
				background: {{data.bg_color}};
				overflow: hidden;
				<# if(_.isObject(data.title_padding)) { #>
					padding:{{data.title_padding.md}};
				<# } else { #>
					padding:{{data.title_padding}};
				<# } #>
				text-align:center;
				<# if(_.isObject(data.title_margin)) { #>
					margin:{{data.title_margin.md}};
				<# } else { #>
					margin:{{data.title_margin}};
				<# } #>

			}
			#jwpf-addon-{{ data.id }} .xtb_zt a{color:{{data.title_text_color}};font-size:{{data.title_fontsize.md}}px;}
		    #jwpf-addon-{{ data.id }} .xtb_img{width:30px;height:30px;margin:0 auto;vertical-align: middle;}
		    #jwpf-addon-{{ data.id }} .xtb_tit{line-height:{{data.title_lineheight.md}}px;font-size:{{data.title_fontsize.md}}px;height:{{data.title_lineheight.md}}px;margin-top:5px;}
			#jwpf-addon-{{ data.id }} .xtb_zt:hover{
				background: {{data.hgbg_color}};
			}
			#jwpf-addon-{{ data.id }} .xtb_zt:hover a{
				color: {{data.title_hg_color}};
			}

			#jwpf-addon-{{ data.id }} img{
				border-radius: {{ data.border_radius }}px;
				<# if(_.isObject(data.image_height)) { #>
					height: {{data.image_height.md}}px;
				<# } else { #>
					height: {{data.image_height}}px;
				<# } #>
				<# if(_.isObject(data.image_width)) { #>
					width: {{data.image_width.md}}px;
				<# } else { #>
					width: {{data.image_width}}px;
				<# } #>
				<# if(_.isObject(data.image_width)) { #>
					max-width: {{data.image_width.md}}px;
				<# } else { #>
					max-width: {{data.image_width}}px;
				<# } #>
			}
			#jwpf-addon-{{ data.id }} .jwpf-addon-title{
				<# if(_.isObject(data.title_padding)) { #>
					padding:{{data.title_padding.md}};
				<# } else { #>
					padding:{{data.title_padding}};
				<# } #>
			}

			<# if(data.isFixed==1) { #>
				<# if(_.isObject(data.fixed_style)=="top") { #>
					
					#jwpf-addon-{{ data.id }} .jwpf-addon {
						position: fixed;
						z-index: 9999;
					}

					#jwpf-addon-{{ data.id }} .xtb_zt {
						margin-top:-20px;
					}
				<# } #>
			<# } #>
			

			@media (min-width: 768px) and (max-width: 991px) {
				#jwpf-addon-{{ data.id }} .xtb_zt{
					
					<# if(_.isObject(data.title_padding)) { #>
						padding:{{data.title_padding.sm}};
					<# } else { #>
						padding:{{data.title_padding}};
					<# } #>
					text-align:center;
					<# if(_.isObject(data.title_margin)) { #>
						margin:{{data.title_margin.sm}};
					<# } else { #>
						margin:{{data.title_margin}};
					<# } #>

				}
				#jwpf-addon-{{ data.id }} .xtb_zt a{color:{{data.title_text_color}};font-size:{{data.title_fontsize.sm}}px;}
			    #jwpf-addon-{{ data.id }} .xtb_img{margin:0 auto;vertical-align: middle;}
			    #jwpf-addon-{{ data.id }} .xtb_tit{line-height:{{data.title_lineheight.sm}}px;font-size:{{data.title_fontsize.sm}}px;height:{{data.title_lineheight.sm}}px;margin-top:5px;}
				#jwpf-addon-{{ data.id }} img{
					<# if(_.isObject(data.image_height)) { #>
						height: {{data.image_height.sm}}px;
					<# } #>
					<# if(_.isObject(data.image_width)) { #>
						width: {{data.image_width.sm}}px;
					<# } #>
					<# if(_.isObject(data.image_width)) { #>
						max-width: {{data.image_width.sm}}px;
					<# } #>
				}
			}
			@media (max-width: 767px) {
				#jwpf-addon-{{ data.id }} .xtb_zt{
					
					<# if(_.isObject(data.title_padding)) { #>
						padding:{{data.title_padding.xs}};
					<# } else { #>
						padding:{{data.title_padding}};
					<# } #>
					text-align:center;
					<# if(_.isObject(data.title_margin)) { #>
						margin:{{data.title_margin.xs}};
					<# } else { #>
						margin:{{data.title_margin}};
					<# } #>

				}
				#jwpf-addon-{{ data.id }} .xtb_zt a{color:{{data.title_text_color}};font-size:{{data.title_fontsize.xs}}px;}
			    #jwpf-addon-{{ data.id }} .xtb_tit{line-height:{{data.title_lineheight.xs}}px;font-size:{{data.title_fontsize.xs}}px;height:{{data.title_lineheight.xs}}px;margin-top:5px;}
				#jwpf-addon-{{ data.id }} img{
					<# if(_.isObject(data.image_height)) { #>
						height: {{data.image_height.xs}}px;
					<# } #>
					<# if(_.isObject(data.image_width)) { #>
						width: {{data.image_width.xs}}px;
					<# } #>
					<# if(_.isObject(data.image_width)) { #>
						max-width: {{data.image_width.xs}}px;
					<# } #>
				}
			}
		</style>
		
		<div class="jwpf-addon jwpf-addon-single-image ">
			<div class="xtb_zt">
				<# if(open_lightbox) { #>
					<a class="jwpf-magnific-popup jwpf-addon-image-overlay-icon" data-popup_type="image" data-mainclass="mfp-no-margins mfp-with-zoom" href=\'{{ data.image }}\'>+</a>
				<# } #>
	
				<# if(!open_lightbox) { #>
					<a target="{{ data.target }}" href=\'{{ data.link }}\'>
				<# } #>

				    <div class="xtb_img">

			    		<# if(image_overlay && open_lightbox) { #>
							<div class="jwpf-addon-image-overlay"></div>
						<# } #>
						
						<# if(data.image.indexOf("http://") == -1 && data.image.indexOf("https://") == -1){ #>
							<img class="jwpf-img-responsive" src=\'{{ pagefactory_base + data.image }}\' alt="{{ alt_text }}" title="{{ data.title }}">
						<# } else { #>
							<img class="jwpf-img-responsive" src=\'{{ data.image }}\' alt="{{ alt_text }}" title="{{ data.title }}">
						<# } #>

				    </div>
				    <div class="xtb_tit">
				        {{ data.title }}
				    </div>

			    <# if(!open_lightbox) { #>
					</a>
				<# } #>
			</div>
		</div>
	
		
		';

		return $output;
	}

}
