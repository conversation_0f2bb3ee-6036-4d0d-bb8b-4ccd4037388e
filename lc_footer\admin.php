<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type'       => 'repeatable',
        'addon_name' => 'lc_footer',
        'title'      => JText::_('多功能页脚底部组件'),
        'desc'       => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
        'category'   => '龙采官网插件',
        'attr'       => array(
            'general' => array(
                'admin_label'   => array(
                    'type'  => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc'  => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std'   => '',
                ),
                'bgColor' => array(
                    'type' => 'color',
                    'title' => '背景颜色设置',
                    'std' => '#1e1e1e'
                ),
                'bot_borderColor' => array(
                    'type' => 'color',
                    'title' => '底部线条颜色',
                    'std' => '#393939'
                ),
                'bot_l_color' => array(
                    'type' => 'color',
                    'title' => '底部主链接文字颜色',
                    'std' => '#777777'
                ),
                'bot_l_color_h' => array(
                    'type' => 'color',
                    'title' => '鼠标移入底部主链接文字颜色',
                    'std' => '#ffffff'
                ),
                'bot_s_color' => array(
                    'type' => 'color',
                    'title' => '底部子链接文字颜色',
                    'std' => '#7a7a7a'
                ),
                'bot_s_color_h' => array(
                    'type' => 'color',
                    'title' => '鼠标移入底部子链接文字颜色',
                    'std' => '#ffffff'
                ),
                'bot_left_color' => array(
                    'type' => 'color',
                    'title' => '底部线条下方左侧文字颜色',
                    'std' => '#7a7a7a'
                ),
                'bot_right_color' => array(
                    'type' => 'color',
                    'title' => '底部线条下方右侧文字颜色',
                    'std' => '#ffffff'
                ),
                'media_title'   => array(
                    'type'  => 'text',
                    'title' => '第三方平台栏目名称',
                    'std'   => '官方社交媒体',
                ),
                'shouqian_url'  => array(
                    'type'  => 'text',
                    'title' => '售前客服链接',
                    'std'   => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                ),
                'shouhou_url'   => array(
                    'type'  => 'text',
                    'title' => '售后客服链接',
                    'std'   => 'https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c',
                ),
                'beianhao'      => array(
                    'type'  => 'text',
                    'title' => '备案号',
                    'std'   => '黑B2-20101198-1',
                ),
                'beianhao_url'   => array(
                    'type'  => 'text',
                    'title' => '备案号跳转链接',
                    'std'   => 'https://beian.miit.gov.cn/#/Integrated/index',
                ),
                'youbian'       => array(
                    'type'  => 'text',
                    'title' => '邮编',
                    'std'   => '15000',
                ),
                'ybh_img'       => array(
                    'type'  => 'checkbox',
                    'title' => '不显示小图标',
                    'std'   => '0',
                ),
                'youbian_img'   => array(
                    'type'  => 'media',
                    'title' => '邮编后小图标',
                    'std'   => 'https://oss.lcweb01.cn/joomla/20220812/f9fd0eb548f5aee1a910dd0d7df0190a.png',
                    'depends' => array(
                        array('ybh_img', '!=', '1'),
                    ),
                ),
                'wanganbeian'   => array(
                    'type'  => 'text',
                    'title' => '公网安备',
                    'std'   => '黑公网安备 23010202010007号',
                ),
                'wanganbeian_url'   => array(
                    'type'  => 'text',
                    'title' => '公网安备跳转链接',
                    'std'   => 'https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=%2023010202010007',
                ),
                'zhuceren'   => array(
                    'type'  => 'text',
                    'title' => '注册人',
                    'std'   => '注册人权利与责任',
                ),
                'jw_media_item' => array(
                    'title' => '第三方平台栏目列表',
                    'desc'  => '第三方平台数量最大四个',
                    'std'   => array(
                        array(
                            'qr_code'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/9df26cca949feeb5253c41383943a3e8.jpg',
                            'icon_url'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a04.png',
                            'icon_url2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a05.png',
                            'icon_url3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/z24.png',
                            'title'     => '龙采官方微博',
                            'a_url'     => 'https://weibo.com/p/1006062488214857/home?from=page_100606&amp;mod=TAB#place',
                            'content'   => '扫码关注龙采科技官方微博，了解更多企业资讯！',
                            'on_link'     => 0,
                        ),
                        array(
                            'qr_code'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/263868ccbfdf729b44eb265f11964f1d.jpg',
                            'icon_url'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a06.png',
                            'icon_url2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a07.png',
                            'icon_url3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/z25.png',
                            'title'     => '龙采官方微信',
                            'a_url'     => 'https://weibo.com/p/1006062488214857/home?from=page_100606&amp;mod=TAB#place',
                            'content'   => '扫码关注龙采科技官方微信，了解更多企业资讯！',
                            'on_link'     => 0,
                        ),
                        array(
                            'qr_code'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/3949371911a6b99dc84ee2c8011b07eb.jpg',
                            'icon_url'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a08.png',
                            'icon_url2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a09.png',
                            'icon_url3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/z26.png',
                            'title'     => '龙采官方抖音',
                            'a_url'     => 'https://weibo.com/p/1006062488214857/home?from=page_100606&amp;mod=TAB#place',
                            'content'   => '扫码关注龙采科技官方抖音，了解更多企业资讯！',
                            'on_link'     => 0,
                        ),
                        array(
                            'qr_code'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/18b1da154be7da670bb3aacca50de9a3.jpg',
                            'icon_url'  => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a10.png',
                            'icon_url2' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a11.png',
                            'icon_url3' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/z27.png',
                            'title'     => '龙采官方百家号',
                            'a_url'     => 'https://weibo.com/p/1006062488214857/home?from=page_100606&amp;mod=TAB#place',
                            'content'   => '扫码关注龙采科技百家号，了解更多龙采',
                            'on_link'     => 0,
                        ),
                    ),
                    'attr'  => array(
                        'qr_code'   => array(
                            'type'  => 'media',
                            'title' => '二维码',
                            'std'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/18b1da154be7da670bb3aacca50de9a3.jpg',
                        ),
                        'icon_url'  => array(
                            'type'  => 'media',
                            'title' => '图标',
                            'std'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a10.png',
                        ),
                        'icon_url2' => array(
                            'type'  => 'media',
                            'title' => '选中图标',
                            'std'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a11.png',
                        ),
                        'icon_url3' => array(
                            'type'  => 'media',
                            'title' => '手机版图标',
                            'std'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lc_footer/assets/img/a11.png',
                        ),
                        'title'     => array(
                            'type'  => 'text',
                            'title' => '平台名称',
                            'std'   => '龙采官方百家号',
                        ),
                        'content'   => array(
                            'type'  => 'editor',
                            'title' => '平台简介',
                            'desc'  => '简介',
                            'std'   => '扫码关注龙采科技百家号，了解更多龙采',
                        ),
                        'on_link'     => array(
                            'type'  => 'checkbox',
                            'title' => '使用内部页面链接',
                            'std'   => 0,
                        ),
                        'a_url'     => array(
                            'type'  => 'text',
                            'title' => '跳转链接',
                            'std'   => '',
                            'depends' => array(
                                array('on_link', '!=', '1')
                            ),
                        ),
                        'detail_page_ida' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'depends' => array(
                                array('on_link', '=', '1')
                            ),
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                    ),
                ),
                'jw_link_item'  => array(
                    'title' => '链接列表',
                    'std'   => array(
                        array(
                            'title'         => '关于龙采',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '公司简介',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '720°全景',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '办公环境',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '集团大事记',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '合作伙伴',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),

                            ),
                        ),
                        array(
                            'title'         => '数字经济',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '数字政府平台',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '数字企业平台',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '数字体育平台',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),
                        array(
                            'title'         => '百度营销',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '搜索推广',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '信息流推广',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '聚屏推广',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '百青藤推广',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),
                        array(
                            'title'         => '产品与服务',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '网站建设',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => 'APP开发',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '小程序开发',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '行业解决方案',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),
                        array(
                            'title'         => '龙采资讯',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '行业资讯',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '企业资讯',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '党建新闻',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),
                        array(
                            'title'         => '案例展示',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => 'case案例',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),
                        array(
                            'title'         => '联系我们',
                            'title_url' => 'https://www.longcai0351.com/about#jianjie',
                            'jw_link_data' => array(
                                array(
                                    'title' => '黑龙江',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                                array(
                                    'title' => '山西',
                                    'a_url' => 'https://www.longcai0351.com/about#quanjing',
                                ),
                                array(
                                    'title' => '大连',
                                    'a_url' => 'https://www.longcai0351.com/about#jianjie',
                                ),
                            ),
                        ),

                    ),
                    'attr'  => array(
                        'title'         => array(
                            'type'  => 'text',
                            'title' => '链接名称',
                            'std'   => '',
                        ),
                        'link_selet' => array(
                            'type' => 'select',
                            'title' => '跳转页面',
                            'values' => array(
                                'ww' => '外部链接',
                                'nn' => '内部页面'
                            ),
                            'std' => 'ww',
                        ),
                        'title_url'  => array(
                            'type'  => 'text',
                            'title' => '链接地址',
                            'std'   => '',
                            'depends' => array(
                                array('link_selet', '!=', 'nn')
                            ),
                        ),
                        'wdetail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'depends' => array(
                                array('link_selet', '=', 'nn')
                            ),
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                        'jw_link_data' => array(
                            'title' => '子链接列表',
                            'attr'  => array(
                                'title' => array(
                                    'type'  => 'text',
                                    'title' => '链接题目',
                                    'std'   => '龙采官方百家号',
                                ),
                                'link_nw' => array(
                                    'type' => 'checkbox',
                                    'title' => '跳转内部页面',
                                    'std' => '0',
                                ),
                                'a_url' => array(
                                    'type'  => 'text',
                                    'title' => '链接地址',
                                    'std'   => '',
                                    'depends' => array(
                                        array('link_nw', '!=', '1')
                                    ),
                                ),

                                'detail_page_id' => array(
                                    'type' => 'select',
                                    'title' => '内部页面链接',
                                    'desc' => '',
                                    'depends' => array(
                                        array('link_nw', '=', '1')
                                    ),
                                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                                ),


                            ),
                        ),
                    ),
                ),
            ),
        ),
    )
);