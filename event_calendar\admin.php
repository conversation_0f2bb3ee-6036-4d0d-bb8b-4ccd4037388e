<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
   array(
       'type' => 'content',
       'addon_name' => 'event_calendar',
       'title' => '活动日历',
       'desc' => '',
       'category' => '活动',
       'attr' => array(
           'general' => array(
               'admin_label' => array(
                   'type' => 'text',
                   'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                   'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                   'std' => ''
               ),
               'site_id' => array(
                   'std' => $site_id,
               ),
               'company_id' => array(
                   'std' => $company_id,
               ),

               'gengduo' => array(
					'type' => 'select',
					'title' => '点击更多跳转页面',
					'desc' => '显示页面模版',
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				),

                'title' => array(
					'type' => 'text',
					'title' => '标题',
					'desc' => '标题',
					'std' => '赛事活动',
				),

                'k_status_title' => array(
					'type' => 'text',
					'title' => '弹窗无数据提示',
					'desc' => '弹窗无数据提示',
					'std' => '暂无赛事信息',
				),


//                'section_tab_active_color' => array(
//                    'type' => 'color',
//                    'title' => '选项卡选中文字颜色',
//                    'std' => '#fff',
//                    'depends' => array(
//                        array('content_style', '=', 'tab_style'),
//                    ),
//                ),
//                'section_tab_mgB' => array(
//                    'type' => 'slider',
//                    'title' => '选项卡外下边距',
//                    'std' => array(
//                        'md' => 20,
//                        'sm' => 20,
//                        'xs' => 10,
//                    ),
//                    'depends' => array(
//                        array('content_style', '=', 'tab_style'),
//                    ),
//                    'responsive' => true,
//                    'max' => 1000,
//                ),
//
//                'section_map_city' => array(
//                    'type' => 'text',
//                    'title' => '地图中心点所在城市',
//                    'desc' => '地图中心点城市需要与填写的地图中心点经纬度一致',
//                    'std' => '太原',
//                    'depends' => array(
//                        array('content_style', '=', 'map_style'),
//                    ),
//                ),
//
//                'section_map_centerIcon' => array(
//                    'type' => 'media',
//                    'title' => '地图中心点图标',
//                    'std' => '/components/com_jwpagefactory/addons/amap_nearby/assets/images/location.png',
//                    'depends' => array(
//                        array('content_style', '=', 'map_style'),
//                    ),
//                ),

           ),
       ),
   )
);
