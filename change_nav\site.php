<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-24 18:15:57
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-05-30 14:39:28
 * @FilePath: \addons\change_nav\assets\site.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON>Worker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonChange_nav extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
		
		$output='';

		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$nav_decline_normal = (isset($settings->nav_decline_normal) && $settings->nav_decline_normal) ? $settings->nav_decline_normal : '';
		$section_id_decline = (isset($settings->section_id_decline) && $settings->section_id_decline) ? $settings->section_id_decline : '';
		$phone_open = (isset($settings->phone_open) && ($settings->phone_open || $settings->phone_open == 0)) ? $settings->phone_open : '0';
		$gradient = (isset($settings->gradient) && ($settings->gradient || $settings->gradient == 0)) ? $settings->gradient : '0';
		$gradient_time = (isset($settings->gradient_time) && ($settings->gradient_time || $settings->gradient_time == 0)) ? $settings->gradient_time : '3';
		$fixed = (isset($settings->fixed) && ($settings->fixed || $settings->fixed == 0)) ? $settings->fixed : 1;
		
		$css = '#'.$nav_decline_normal.'{';
			if($fixed == 1){
				$css.='position: fixed;';
			}
			$css.='opacity: 1;
			z-index: 2000 !important;
			margin-top: 0;';
			if($gradient == 1){
				$css.='transition: opacity '.$gradient_time.'s;';
			}
		$css.='}
		#'.$section_id_decline.'{';
			if($fixed == 1){
				$css.='position: fixed;';
			}
			$css.='opacity: 0;
			margin-top: 0;';
			if($gradient == 1){
				$css.='transition: opacity '.$gradient_time.'s;';
			}
		$css.='}
		#jwpf-addon-wrapper-'.$this->addon->id.',
		'.$addon_id.'{
			display: none;
			height: 0;
			margin: 0;
			padding: 0;
		}';
		
		return $css;
	}

	public function js()
	{
		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$nav_decline_normal = (isset($settings->nav_decline_normal) && $settings->nav_decline_normal) ? $settings->nav_decline_normal : '';
		$section_id_decline = (isset($settings->section_id_decline) && $settings->section_id_decline) ? $settings->section_id_decline : '';
		$decline_distance = (isset($settings->decline_distance) && ($settings->decline_distance || $settings->decline_distance == 0)) ? $settings->decline_distance : '0';
		$phone_open = (isset($settings->phone_open) && ($settings->phone_open || $settings->phone_open == 0)) ? $settings->phone_open : '0';
		$fixed = (isset($settings->fixed) && ($settings->fixed || $settings->fixed == 0)) ? $settings->fixed : 1;
		
		$js = '';

		$js .='jQuery(document).ready(function($){';
			if($fixed == 0){
				$js.='$("#'.$section_id_decline.'").css("margin-top", -Number($("#'.$section_id_decline.'").outerHeight()));
				';
			}
			
			$js .= '
				$("#'.$nav_decline_normal.'").css("cssText", "z-index: 2000 !important;");
				';
			$js .= '$(window).on("scroll",function(){
				if ($(window).scrollTop() > '.$decline_distance.') {
					$("#'.$section_id_decline.'").css("cssText", "z-index: 2000 !important;")
					$("#'.$section_id_decline.'").css({"opacity":"1"});
					$("#'.$nav_decline_normal.'").css("cssText", "z-index: 1000 !important;")
					$("#'.$nav_decline_normal.'").css({"opacity":"0"});
				} else {
					$("#'.$nav_decline_normal.'").css("cssText", "z-index: 2000 !important;")
					$("#'.$nav_decline_normal.'").css({"opacity":"1"});
					$("#'.$section_id_decline.'").css("cssText", "z-index: 1000 !important;")
					$("#'.$section_id_decline.'").css({"opacity":"0"});
				}
			})
		})';
		return $js;
	}

	
    public static function getTemplate(){
		$output = '<p class="alert alert-warning" style="margin-bottom: 10px;">编辑页仅为占位样式，请在预览页面中查看该导航切换效果</p>';
		return $output;
	}
}
