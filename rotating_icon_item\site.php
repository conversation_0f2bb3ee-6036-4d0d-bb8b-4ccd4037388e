<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonRotating_icon_item extends JwpagefactoryAddons
{

    public function render()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $jw_rit_tab_item == (isset($settings->jw_rit_tab_item) && $settings->jw_rit_tab_item) ? $settings->jw_rit_tab_item : array(

            (Object) array(
                'title'    => '1.技术方面',
                'content'  => '技术基础薄弱，部分基层个体诊所医技水平不高、培训渠道匮乏，诊疗范围受限。',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k1.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l1.png',
            ),
            (Object) array(
                'title'    => '2.人员方面',
                'content'  => '缺少专业的医疗信息化建设相关人才；专业的中医大夫匮',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k2.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l2.png',
            ),
            (Object) array(
                'title'    => '3.管理方面',
                'content'  => '医患关系弱，工作人员无法在企业信息系统中对客户资料进行系统管理和深入分析，患者就诊结束后无渠道能做诊后服务，丢失患者；无法对坐诊医生进行精细化分析、考核。',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k3.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l3.png',
            ),
            (Object) array(
                'title'    => '4.业务方面',
                'content'  => '无法实现流程化管理，各部门工作脱节，对移动应用和互联网的扩展支持差，导致沟通不及时，工作效率低，如：开处方单时，相关饮片库存、价格、效期等重要信息不匹配。',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k4.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l4.png',
            ),
            (Object) array(
                'title'    => '5.数据方面',
                'content'  => '管理混乱，数据无法电子化，患者历史病历/处方多，无法有效管理，纸质处方办公效率低，数据维护成本高；职能混乱，人员流动性高，相应责任无法追踪。',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k5.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l5.png',
            ),
            (Object) array(
                'title'    => '6.宣传方面',
                'content'  => '没有宣传载体，品牌知名度低，特色诊疗项目无人知晓。',
                'icon_img' => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/k6.png',
                'bg_img'   => str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/rotating_icon_item/assets/images/l6.png',
            ),
        );
        $bgcolor = (isset($settings->bgcolor) && $settings->bgcolor) ? $settings->bgcolor : '#f3f3f3';

        $output .= '<style>';
        $output .= $addonId . ' .z-f10-d2{width:100%;height:678px;position:relative;background:'.$bgcolor.';overflow:hidden}';
        $output .= $addonId . ' .z-f10-d3{width:1420px;position:absolute;top:50px;left:calc(50% - 710px)}';
        $output .= $addonId . ' .z-f10-d4{width:452px;height:264px;position:relative;float:left;margin-right:32px;margin-bottom:40px;border:0;border-radius:6px;background:#fff;overflow:hidden}';
        $output .= $addonId . ' .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}';
        $output .= $addonId . ' .z-f10-d2{width:100%;height:678px;position:relative;background:'.$bgcolor.';overflow:hidden}';
        $output .= $addonId . ' .z-f10-d3{width:1420px;position:absolute;top:50px;left:calc(50% - 710px)}';
        $output .= $addonId . ' .z-f10-d4{width:452px;height:264px;position:relative;float:left;margin-right:32px;margin-bottom:40px;border:0;border-radius:6px;background:#fff;overflow:hidden}';
        $output .= $addonId . ' .z-f10-d4:nth-child(3n){margin-right:0}';
        $output .= $addonId . ' .z-f10-i1{width:100%;height:123px;position:absolute;top:0;left:0}';
        $output .= $addonId . ' .z-f10-i2{width:92px;height:92px;position:absolute;top:16px;left:96px;border:0;border-radius:50%;transition:0.5s}';
        $output .= $addonId . ' .z-f10-d4:hover .z-f10-i2{transform:rotateY(360deg);transition:0.5s}';
        $output .= $addonId . ' .z-f10-p3{font-size:20px;line-height:22px;color:#fff;position:absolute;top:44px;left:225px}';
        $output .= $addonId . ' .z-f10-p3 span{font-family:"din";font-size:36px;line-height:22px;color:#fff;position:relative;top:4px}';
        $output .= $addonId . ' .z-f10-p4{width:396px;font-size:15px;line-height:26px;color:#434343;position:absolute;top:150px;left:calc((100% - 396px) / 2)}';
        $output .= $addonId . ' .z-f10-d2{animation:fadenum 2s}';
        $output .= ' @keyframes fadenum{';
        $output .= $addonId . ' 0%{transform:translateY(-10px);opacity:0}';
        $output .= $addonId . ' 20%{transform:translateY(-20px);opacity:0.1}';
        $output .= $addonId . ' 40%{transform:translateY(-30px);opacity:0.2}';
        $output .= $addonId . ' 60%{transform:translateY(-40px);opacity:0.3}';
        $output .= $addonId . ' 100%{transform:translateY(-60px);opacity:1}';
        $output .= ' }';
        $output .=' @media only screen and (max-width: 1023px)';
        $output .= ' {';
        $output .= $addonId . ' .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}';
        $output .= $addonId . ' .z-f10-d2{width:100%;position:relative;background:'.$bgcolor.';overflow:hidden}';
        $output .= $addonId . ' .z-f10-d3{width:100%;position:absolute;top:1rem;left:1rem}';
        $output .= $addonId . ' .z-f10-d4{width:44%;height:13rem;position:relative;float:left;margin-right:16px;margin-bottom:16px;border:0;border-radius:6px;background:#fff;overflow:hidden}';
        $output .= $addonId . ' .z-f10-i1{width:100%;height:4rem;position:absolute;top:0;left:0}';
        $output .= $addonId . ' .z-f10-i1 img{width:100%;height:4rem;}';
        $output .= $addonId . ' .z-f10-i2{width:2.8rem;height:2.8rem;position:absolute;top:0.6rem;left:0.5rem;border:0;border-radius:50%;transition:0.5s}';
        $output .= $addonId . ' .z-f10-d4:hover .z-f10-i2{transform:rotateY(360deg);transition:0.5s}';
        $output .= $addonId . ' .z-f10-p3{font-size:1rem;line-height:4rem;color:#fff;position:absolute;top:0;left:4.8rem}';
        $output .= $addonId . ' .z-f10-p3 span{font-family:"din";font-size:3rem;line-height:2rem;color:#fff;position:relative;top:4px}';
        $output .= $addonId . ' .z-f10-p4{width:98%;font-size:0.6rem;line-height:20px;color:#434343;position:absolute;top:5.5rem;left:6px;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(1){height: 9.64rem;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(2){height: 9.64rem;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(3){height: 14.2rem;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(4){height: 14.2rem;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(5){height: 12.64rem;margin-bottom: 0;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(6){height: 12.64rem;margin-bottom: 0;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(3n) {margin-right:1rem;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(odd){float: left;}';
        $output .= $addonId . ' .z-f10-d4:nth-child(even){float: left;}';
    
        $output .=  ' }';

        $output .= '</style>';
        $output .= '<div class="z-f10-d2">';
        $output .= '  <div class="z-f10-d3 clear">';
        foreach ($settings->jw_rit_tab_item as $key => $item) {
            $output .= '    <div class="z-f10-d4 wow fadeInUp animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">';
            $output .= '      <div class="z-f10-i1 i100"><img src="' . $item->bg_img . '"></div>';
            $output .= '      <div class="z-f10-i2 i100"><img src="' . $item->icon_img . '"></div>';
            $output .= '      <div class="z-f10-p3">' . $item->title . '</div>';
            $output .= '      <div class="z-f10-p4">' . $item->content . '</div>';
            $output .= '    </div>';
        }
        $output .= '  </div>';
        $output .= '</div>';

        return $output;

    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

    public function css()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        // 上翻页按钮
        $swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
        $swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;

        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';

        $css = '
            /* 组件盒子 */
            ' . $addonId . ' .swiper-box-main {
                position: relative;
            }
            /* 切换 配置样式 */
            ' . $addonId . ' .swiper-button {
                width: auto;
                height: auto;
                top: ' . $swiper_button_top_md . '%;
            }
            ' . $addonId . ' .swiper-button:after {
                content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
            }
            ' . $addonId . ' .swiper-button-prev {
                left: ' . $swiper_button_left_md . 'px;
            }
            ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }
            ' . $addonId . ' .swiper-button-next {
                right: ' . $swiper_button_left_md . 'px;
            }
            ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
                background-image: url(' . $swiper_button_next . ');
            }
            /*轮播点*/
            ' . $addonId . ' .swiper-pagination {
                width: 100%;
            }
            ' . $addonId . ' .swiper-pagination-bullet {
                margin-right: ' . $swiper_p_margin_md . 'px;
                width: ' . $swiper_p_width_md . 'px;
                height: ' . $swiper_p_height_md . 'px;
                background: ' . $swiper_p_color . ';
                opacity: 1;
            }
            ' . $addonId . ' .swiper-pagination-bullet-active {
                width: ' . $swiper_p_width_a_md . 'px;
                height: ' . $swiper_p_height_a_md . 'px;
                background: ' . $swiper_p_color_a . ';
            }
            ' . $addonId . ' .swiper-pagination-bullet:last-child {
                margin-right: 0px;
            }
            @media (min-width: 768px) and (max-width: 991px) {
                /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' . $swiper_p_margin_sm . 'px;
                    width: ' . $swiper_p_width_sm . 'px;
                    height: ' . $swiper_p_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_sm . 'px;
                    height: ' . $swiper_p_height_a_sm . 'px;
                }
            }
            @media (max-width: 767px) {
                /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' . $swiper_p_margin_xs . 'px;
                    width: ' . $swiper_p_width_xs . 'px;
                    height: ' . $swiper_p_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_xs . 'px;
                    height: ' . $swiper_p_height_a_xs . 'px;
                }
            }';

        return $css;
    }

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public function js()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;

        $js = 'jQuery(function($){';
        $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: true,
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
            });';
        $js .= '})';

        return $js;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        var jw_rit_tab_item = data.jw_rit_tab_item || 0;
        var bgcolor = data.bgcolor || "#f3f3f3";

        #>
        <style type="text/css">
            {{ addonId }} .swiper-box-main {
                position: relative;
            }
               
            {{ addonId }} .z-f10-d2{width:100%;height:678px;position:relative;background:{{bgcolor}};overflow:hidden}
            {{ addonId }} .z-f10-d3{width:1420px;position:absolute;top:50px;left:calc(50% - 710px)}
            {{ addonId }} .z-f10-d4{width:452px;height:264px;position:relative;float:left;margin-right:32px;margin-bottom:40px;border:0;border-radius:6px;background:#fff;overflow:hidden}
            {{ addonId }} .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}
            {{ addonId }} .z-f10-d2{width:100%;height:678px;position:relative;background:{{bgcolor}};overflow:hidden}
            {{ addonId }} .z-f10-d3{width:1420px;position:absolute;top:50px;left:calc(50% - 710px)}
            {{ addonId }} .z-f10-d4{width:452px;height:264px;position:relative;float:left;margin-right:32px;margin-bottom:40px;border:0;border-radius:6px;background:#fff;overflow:hidden}
            {{ addonId }} .z-f10-d4:nth-child(3n){margin-right:0}
            {{ addonId }} .z-f10-i1{width:100%;height:123px;position:absolute;top:0;left:0}
            {{ addonId }} .z-f10-i2{width:92px;height:92px;position:absolute;top:16px;left:96px;border:0;border-radius:50%;transition:0.5s}
            {{ addonId }} .z-f10-d4:hover .z-f10-i2{transform:rotateY(360deg);transition:0.5s}
            {{ addonId }} .z-f10-p3{font-size:20px;line-height:22px;color:#fff;position:absolute;top:44px;left:225px}
            {{ addonId }} .z-f10-p3 span{font-family:"din";font-size:36px;line-height:22px;color:#fff;position:relative;top:4px}
            {{ addonId }} .z-f10-p4{width:396px;font-size:15px;line-height:26px;color:#434343;position:absolute;top:150px;left:calc((100% - 396px) / 2)}
            {{ addonId }} .z-f10-d2{animation:fadenum 2s}
            @keyframes fadenum{
                {{ addonId }} 0%{transform:translateY(-10px);opacity:0}
                {{ addonId }} 20%{transform:translateY(-20px);opacity:0.1}
                {{ addonId }} 40%{transform:translateY(-30px);opacity:0.2}
                {{ addonId }} 60%{transform:translateY(-40px);opacity:0.3}
                {{ addonId }} 100%{transform:translateY(-60px);opacity:1}
            }
            @media only screen and (max-width: 1023px) {
                {{ addonId }} .fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}
                {{ addonId }} .z-f10-d2{width:100%;position:relative;background:{{bgcolor}};overflow:hidden}
                {{ addonId }} .z-f10-d3{width:100%;position:absolute;top:1rem;left:1rem}
                {{ addonId }} .z-f10-d4{width:44%;height:13rem;position:relative;float:left;margin-right:16px;margin-bottom:16px;border:0;border-radius:6px;background:#fff;overflow:hidden}
                {{ addonId }} .z-f10-i1{width:100%;height:4rem;position:absolute;top:0;left:0}
                {{ addonId }} .z-f10-i1 img{width:100%;height:4rem;}
                {{ addonId }} .z-f10-i2{width:2.8rem;height:2.8rem;position:absolute;top:0.6rem;left:0.5rem;border:0;border-radius:50%;transition:0.5s}
                {{ addonId }} .z-f10-d4:hover .z-f10-i2{transform:rotateY(360deg);transition:0.5s}
                {{ addonId }} .z-f10-p3{font-size:1rem;line-height:4rem;color:#fff;position:absolute;top:0;left:4.8rem}
                {{ addonId }} .z-f10-p3 span{font-family:"din";font-size:3rem;line-height:2rem;color:#fff;position:relative;top:4px}
                {{ addonId }} .z-f10-p4{width:98%;font-size:0.6rem;line-height:20px;color:#434343;position:absolute;top:5.5rem;left:6px;}
                {{ addonId }} .z-f10-d4:nth-child(1){height: 9.64rem;}
                {{ addonId }} .z-f10-d4:nth-child(2){height: 9.64rem;}
                {{ addonId }} .z-f10-d4:nth-child(3){height: 14.2rem;}
                {{ addonId }} .z-f10-d4:nth-child(4){height: 14.2rem;}
                {{ addonId }} .z-f10-d4:nth-child(5){height: 12.64rem;margin-bottom: 0;}
                {{ addonId }} .z-f10-d4:nth-child(6){height: 12.64rem;margin-bottom: 0;}
                {{ addonId }} .z-f10-d4:nth-child(3n) {margin-right:1rem;}
                {{ addonId }} .z-f10-d4:nth-child(odd){float: left;}
                {{ addonId }} .z-f10-d4:nth-child(even){float: left;}
            }

        </style>
        
        <div class="z-f10-d2">
            <div class="z-f10-d3 clear">
            
                <# _.each(jw_rit_tab_item, function(item, key){ #>
                    <div class="z-f10-d4 wow fadeInUp animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">
                        <div class="z-f10-i1 i100"><img src=\'{{item.bg_img}}\'></div>
                        <div class="z-f10-i2 i100"><img src=\'{{item.icon_img}}\'></div>
                        <div class="z-f10-p3">{{item.title}}</div>
                        <div class="z-f10-p4">{{item.content}}</div>
                    </div>
                <# }) #>
                    
            </div>
        </div>

        ';

        return $output;
    }

}
