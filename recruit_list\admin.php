<?php
/**
* <AUTHOR>
* @email        <EMAIL>
* @url          http://www.joomla.work
* @copyright    Copyright (c) 2010 - 2019 JoomWorker
* @license      GNU General Public License version 2 or later
* @date         2019/01/01 09:30
*/
//no direct accees
defined('_JEXEC') or die ('Restricted access');

$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
 	array(
 		'type' => 'content',
 		'addon_name' => 'recruit_list',
 		'title' => '招聘列表',
 		'desc' => '',
 		'category' => '招聘',
 		'attr' => array(
 			'general' => array(
 				'admin_label' => array(
 					'type' => 'text',
 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
 					'std' => ''
 				),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'recruit_type_selector' => array(
                    'type' => 'select',
                    'title' => '选择列表布局',
                    'values' => array(
                        'type01' => '布局01',
                        'type02' => '布局02',
                    ),
                    'std' => 'type01'
                ),
                'zp_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择招聘分类'),
                    'desc' => '招聘分类',
                    'values' => JwPageFactoryBase::getRecruitList($site_id, $company_id, 'com_recruit')['list'],

                ),
                'table_mg' => array(
                    'type' => 'checkbox',
                    'title' => '开启单元格间距',
                    'std' => 0,
                    'depends' => array(
                        //array('recruit_item', '=', 'th'),
                    ),
                ),
                'table_border_r' => array(
                    'type' => 'slider',
                    'title' => '单元格列间距',
                    'std' => '0',
                    'depends' => array(
                        array('table_mg', '=', 1),
                    ),
                ),
                'table_border_c' => array(
                    'type' => 'slider',
                    'title' => '单元格行间距',
                    'std' => '0',
                    'depends' => array(
                        array('table_mg', '=', 1),
                    ),
                ),
                'recruit_item' => array(
                    'type' => 'buttons',
                    'title' => '表格配置',
                    'std' => 'th',
                    'values' => array(
                        array(
                            'label' => '表头',
                            'value' => 'th'
                        ),
                        array(
                            'label' => '表格内容',
                            'value' => 'td'
                        ),
                    ),
                    'tabs' => true,
                ),
                'table_th' => array(
                    'type' => 'checkbox',
                    'title' => '显示表格头',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                    ),
                ),
                'table_th_01' => array(
                    'type' => 'checkbox',
                    'title' => '显示【职位名称】',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title01' => array(
                    'type' => 'text',
                    'title' => '【职位名称】标题文字',
                    'std' => '职位名称',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_01', '=', 1),
                    ),
                ),
                'table_th_02' => array(
                    'type' => 'checkbox',
                    'title' => '显示【招聘人数】',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title02' => array(
                    'type' => 'text',
                    'title' => '【招聘人数】标题文字',
                    'std' => '招聘人数',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_02', '=', 1),
                    ),
                ),
                'table_th_03' => array(
                    'type' => 'checkbox',
                    'title' => '显示【职位说明】',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title03' => array(
                    'type' => 'text',
                    'title' => '【职位说明】标题文字',
                    'std' => '职位说明',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_03', '=', 1),
                    ),
                ),
                'table_th_04' => array(
                    'type' => 'checkbox',
                    'title' => '关闭【应聘】',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title04' => array(
                    'type' => 'text',
                    'title' => '【应聘】标题文字',
                    'std' => '应聘',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_04', '!=', 1),
                    ),
                ),
                'table_th_05' => array(
                    'type' => 'checkbox',
                    'title' => '显示【最低学历】',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title05' => array(
                    'type' => 'text',
                    'title' => '【最低学历】标题文字',
                    'std' => '最低学历',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_05', '=', 1),
                    ),
                ),
                'table_th_06' => array(
                    'type' => 'checkbox',
                    'title' => '显示【工作经验】',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title06' => array(
                    'type' => 'text',
                    'title' => '【工作经验】标题文字',
                    'std' => '工作经验',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_06', '=', 1),
                    ),
                ),
                'table_th_07' => array(
                    'type' => 'checkbox',
                    'title' => '显示【薪资待遇】',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_title07' => array(
                    'type' => 'text',
                    'title' => '【薪资待遇】标题文字',
                    'std' => '薪资待遇',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_07', '=', 1),
                    ),
                ),
                'table_th_08' => array(
                    'type' => 'checkbox',
                    'title' => '显示【岗位详情】按钮',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'table_th_title08' => array(
                    'type' => 'text',
                    'title' => '【岗位详情】标题文字',
                    'std' => '岗位详情',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                        array('table_th_08', '=', 1),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'table_th_height' => array(
                    'type' => 'slider',
                    'title' => '表格头部高度',
                    'std' => 30,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_bgColor' => array(
                    'type' => 'color',
                    'title' => '表格头背景颜色',
                    'std' => 'rgba(255, 255, 255, 0)',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_border' => array(
                    'type' => 'margin',
                    'title' => '表格头部圆角',
                    'std' => '0',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_align' => array(
                    'type' => 'select',
                    'title' => '表格头文字位置',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右'
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_fontsize' => array(
                    'type' => 'slider',
                    'title' => '表格头文字大小',
                    'std' => 14,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_color' => array(
                    'type' => 'color',
                    'title' => '表格头文字颜色',
                    'std' => 'rgba(0, 0, 0, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_th_fontWeight' => array(
                    'type' => 'checkbox',
                    'title' => '表格头文字加粗',
                    'std' => 1,
                    'depends' => array(
                        array('recruit_item', '=', 'th'),
                        array('table_th', '=', 1),
                    ),
                ),
                'table_td_height' => array(
                    'type' => 'slider',
                    'title' => '表格内容高度',
                    'std' => 30,
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_border' => array(
                    'type' => 'checkbox',
                    'title' => '开启行外边框',
                    'std' => 0,
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_border_color' => array(
                    'type' => 'color',
                    'title' => '行外边框颜色',
                    'std' => '#eee',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('table_td_border', '=', '1'),
                    ),
                ),
                'table_td_color_active' => array(
                    'type' => 'color',
                    'title' => '选中表格内容颜色',
                    'std' => '#0065ff',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'action_icon_w' => array(
                    'type' => 'slider',
                    'title' => '【岗位详情】内容部分按钮宽度',
                    'std' => 20,
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('table_th_08', '=', '1'),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'action_icon_h' => array(
                    'type' => 'slider',
                    'title' => '【岗位详情】内容部分按钮高度',
                    'std' => 20,
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('table_th_08', '=', '1'),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'action_icon' => array(
                    'type' => 'media',
                    'title' => '【岗位详情】内容部分按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220329/3fd0b8508d083a6ad3f029af19561837.png',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('table_th_08', '=', '1'),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'action_icon_active' => array(
                    'type' => 'media',
                    'title' => '【岗位详情】内容部分选中按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220329/41f1a566a2a664c2b83d455f53cec8e5.png',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                        array('table_th_08', '=', '1'),
                        array('recruit_type_selector', '=', 'type02'),
                    ),
                ),
                'table_td_align' => array(
                    'type' => 'select',
                    'title' => '表格内容文字位置',
                    'values' => array(
                        'left' => '左',
                        'center' => '中',
                        'right' => '右'
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_fontsize' => array(
                    'type' => 'slider',
                    'title' => '表格内容文字大小',
                    'std' => 14,
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_color' => array(
                    'type' => 'color',
                    'title' => '表格内容文字颜色',
                    'std' => 'rgba(0, 0, 0, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_zwsm' => array(
                    'type' => 'text',
                    'title' => '职位说明的显示字数',
                    'std' => '30',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_btn' => array(
                    'type' => 'text',
                    'title' => '【应聘】文字',
                    'std' => '应聘',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'table_td_btnColor' => array(
                    'type' => 'color',
                    'title' => '【应聘】文字颜色',
                    'std' => 'rgba(0, 0, 0, 1)',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                ),
                'title_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '【职位名称】跳转详情页模板',
                    'desc' => '',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                ),
                'title_target' => array(
                    'type' => 'select',
                    'title' => '【职位名称】跳转方式',
                    'desc' => '',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                    'std' => '_self',
                    'values' => array(
                        '_blank' => '空页面',
                        '_self' => '当前页'
                    ),
                ),
                'action_detail_page_id' => array(
 					'type' => 'select',
 					'title' => '【应聘】跳转详情页模板',
 					'desc' => '',
 					'depends' => array(
                        array('recruit_item', '=', 'td'),
 					),
 					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
 				),
                'action_target' => array(
                    'type' => 'select',
                    'title' => '【应聘】跳转方式',
                    'desc' => '',
                    'depends' => array(
                        array('recruit_item', '=', 'td'),
                    ),
                    'std' => '_self',
                    'values' => array(
                        '_blank' => '空页面',
                        '_self' => '当前页'
                    ),
                ),
 			),
 		),
 	)
);
