<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(

    array(
        'type' => 'general', //插件
        'addon_name' => 'news_swiper_info',
        'title' => '新闻播报详情',
        'desc' => '',
        'category' => '文章', //插件分组
        'attr' => array(
            //配置项主体
            'general' => array(
                //【基本】选项卡

                // 动态生成的配置项

                'color1615443385649' => array(
                    'type' => 'color',
                    'title' => '正文颜色',
                    'desc' => '',
                    'std' => '#555555',
                ),
                'select1615452416171' => array(
                    'type' => 'select',
                    'title' => '标题布局',
                    'desc' => '',
                    'std' => 'left',
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                    ),
                ),
                'color1615452944411' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'desc' => '',
                    'std' => '#333333',
                ),
                'font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '28'
                ),
                'font_size_date' => array(
                    'type' => 'slider',
                    'title' => JText::_('编辑日期字体大小'),
                    'max' => 100,
                    'min' => 10,
                    'std' => '14'
                ),
                'color1615453171634' => array(
                    'type' => 'color',
                    'title' => '时间颜色',
                    'desc' => '',
                    'std' => '#888888',
                ),
                'pageState' => array(
					'type' => 'buttons',
					'title' => '翻页状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '移入',
							'value' => 'hover'
						),
					),
					'tabs' => true
				),
                'pageColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页文字颜色',
                    'desc' => '',
                    'std' => '#1237ce',  
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal')
                    ),
                ),
                'pageBorderColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页边框颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal')
                    ),
                ),
                'pageBgColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页背景颜色',
                    'desc' => '',
                    'std' => '#fff',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal')
                    ),
                ),
                'pageColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页文字颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover')
                    ),
                ),
                'pageBorderColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页边框颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover')
                    ),
                ),
                'pageBgColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页背景颜色',
                    'desc' => '',
                    'std' => '#fff',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover')
                    ),
                ),


            ),
        ),
    )
);