<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input      = $app->input;
$layout_id  = $input->get('layout_id', '');
$site_id    = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type'       => 'repeatable',
        'addon_name' => 'three_news_class',
        'title'      => '选项卡带新闻列表展示插件',
        'desc'       => '选项卡带新闻列表展示插件',
        'category'   => '文章',
        'attr'       => array(
            'class_type_options'          => array(
                'type'  => 'separator',
                'title' => '左侧栏目分类设置',
            ),
            'item_type'                   => array(
                'type'   => 'buttons',
                'title'  => '选择配置项',
                'std'    => 'tab',
                'values' => array(
                    array(
                        'label' => '选项卡',
                        'value' => 'tab',
                    ),
                    array(
                        'label' => '内容列表',
                        'value' => 'list',
                    ),
                    array(
                        'label' => 'CSS效果',
                        'value' => 'data',
                    ),

                ),
                'tabs'   => true,
            ),
            'class_top_cn_title'          => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(中文)'),
                'desc'    => JText::_('选卡导航分类名称(中文)'),
                'std'     => '关于我们',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_top_us_title'          => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(英文)'),
                'desc'    => JText::_('选卡导航分类名称(英文)'),
                'std'     => 'about',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_type'                  => array(
                'type'    => 'select',
                'title'   => '选择所属分类 ',
                'desc'    => '选择所属分类',
                'values'  => array(
                    'com_content' => '文章',
                    'com_goods'   => '产品',
                    // 'com_shop'    => '商品',
                ),
                'std'     => 'com_goods',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_type_parent'           => array(
                'type'    => 'select',
                'title'   => '分类显示',
                'desc'    => '分类显示',
                'values'  => array(
                    'type1' => '一级分类',
                    'type2' => '二级分类',
                    'all'   => '混合显示',
                ),
                'std'     => 'all',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_type_start'            => array(
                'type'    => 'number',
                'title'   => '从第n个分类开始显示',
                'desc'    => '从第n个分类开始显示',
                'std'     => '1',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_type_num'              => array(
                'type'    => 'number',
                'title'   => '显示n条分类(0为不限)',
                'desc'    => '显示n条分类',
                'std'     => '10',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'class_top_bg'                => array(
                'type'    => 'color',
                'title'   => JText::_('栏目背景颜色'),
                'std'     => '#73a91c',
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
            ),
            'class_top_father_font_color' => array(
                'type'    => 'color',
                'title'   => JText::_('父标题颜色'),
                'std'     => '#ffffff',
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
            ),
            'class_top_father_font_size'  => array(
                'type'    => 'slider',
                'title'   => JText::_('父标题字体大小'),
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
                'min'     => 10,
                'max'     => 40,
                'std'     => 20,
            ),
            'class_top_child_font_color'  => array(
                'type'    => 'color',
                'title'   => JText::_('栏目子标题颜色'),
                'std'     => '#fff',
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
            ),
            'class_top_child_font_size'   => array(
                'type'    => 'slider',
                'title'   => JText::_('子标题字体大小'),
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
                'min'     => 10,
                'max'     => 40,
                'std'     => 16,
            ),
            'class_top_font_hover_color'  => array(
                'type'    => 'color',
                'title'   => JText::_('鼠标移入/选中字体颜色'),
                'depends' => array(
                    array('item_type', '=', 'data'),
                ),
                'std'     => '#73a91c',
            ),
            // 'content_catid'       => array(
            //     'type'    => 'select',
            //     'title'   => JText::_('选择文章分类'),
            //     'desc'    => '文章分类',
            //     'values'  => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_content')['list'],
            //     'depends' => array(array('class_type', '=', 'com_content')),
            //     'depends' => array(
            //         array('item_type', '=', 'list'),
            //     ),
            // ),
            // 'goods_catid'         => array(
            //     'type'    => 'select',
            //     'title'   => JText::_('选择产品分类'),
            //     'desc'    => '产品分类',
            //     'values'  => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_goods')['list'],
            //     'depends' => array(array('class_type', '=', 'com_goods')),
            //     'depends' => array(
            //         array('item_type', '=', 'list'),
            //     ),
            // ),
            // 'shop_catid'          => array(
            //     'type'    => 'select',
            //     'title'   => JText::_('选择商品分类'),
            //     'desc'    => '商品分类',
            //     'values'  => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_shop')['list'],
            //     'depends' => array(array('class_type', '=', 'com_shop')),
            //     'depends' => array(
            //         array('item_type', '=', 'list'),
            //     ),
            // ),
            'catid'                       => array(
                'type'     => 'category',
                'title'    => '选择分类',
                'desc'     => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                'multiple' => true,
                'depends'  => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'ordering'                    => array(
                'type'    => 'select',
                'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                'values'  => array(
                    'latest'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                    'oldest'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                    'timedesc'      => JText::_('添加时间降序'),
                    'timeasc'       => JText::_('添加时间升序'),
                    'sortdesc'      => JText::_('排序id倒序'),
                    'sortasc'       => JText::_('排序id正序'),
                    'hits'          => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                    'featured'      => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                    'alphabet_asc'  => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                    'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                    'random'        => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                ),
                'std'     => 'sortdesc',
                'depends' => array(
                    array('item_type', '=', 'list'),
                ),
            ),
            'limit'                       => array(
                'type'    => 'number',
                'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                'std'     => '10',
                'depends' => array(
                    array('item_type', '=', 'list'),
                ),
            ),
            'include_subcat'              => array(
                'type'    => 'select',
                'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES'),
                'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES_DESC'),
                'values'  => array(
                    1 => JText::_('COM_JWPAGEFACTORY_YES'),
                    0 => JText::_('COM_JWPAGEFACTORY_NO'),
                ),
                'std'     => 1,
                'depends' => array(
                    array('item_type', '=', 'list'),
                ),

            ),
            'post_type'                   => array(
                'type'    => 'select',
                'title'   => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE'),
                'desc'    => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_DESC'),
                'values'  => array(
                    ''         => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_ALL'),
                    'standard' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STANDARD'),
                    'audio'    => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_AUDIO'),
                    'video'    => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_VIDEO'),
                    'gallery'  => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_GALLERY'),
                    'link'     => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_LINK'),
                    'quote'    => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_QUOTE'),
                    'status'   => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STATUS'),
                ),
                'std'     => '',
                'depends' => array(
                    array('item_type', '=', 'list'),
                ),
            ),
            'detail_page_id'              => array(
                'type'    => 'select',
                'title'   => '详情页模版',
                'desc'    => '显示文章详情页模版',
                'values'  => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('item_type', '=', 'list'),
                ),
            ),
            'class_type_options2'         => array(
                'type'    => 'separator',
                'title'   => '左侧新闻栏目设置',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'news_item_type'              => array(
                'type'    => 'buttons',
                'title'   => '选择配置项',
                'std'     => 'newslist',
                'values'  => array(
                    array(
                        'label' => '新闻列表相关',
                        'value' => 'newslist',
                    ),
                    array(
                        'label' => 'CSS效果',
                        'value' => 'newsdata',
                    ),
                ),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
                'tabs'    => true,
            ),
            'news_top_cn_title'           => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(中文)'),
                'desc'    => JText::_('选卡导航分类名称(中文)'),
                'std'     => '最新新闻',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('news_item_type', '=', 'newslist'),
                ),
            ),
            'news_top_us_title'           => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(英文)'),
                'desc'    => JText::_('选卡导航分类名称(英文)'),
                'std'     => 'News',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('news_item_type', '=', 'newslist'),
                ),
            ),
            'news_top_font_size'          => array(
                'type'    => 'slider',
                'title'   => JText::_('字体大小'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('news_item_type', '=', 'newsdata'),
                ),
                'min'     => 10,
                'max'     => 40,
                'std'     => 14,
            ),
            'news_top_font_hover_color'   => array(
                'type'    => 'color',
                'title'   => JText::_('鼠标移入/选中字体圆点颜色'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('news_item_type', '=', 'newsdata'),
                ),
                'std'     => '#73a91c',
            ),
            'news_top_dot_color'          => array(
                'type'    => 'color',
                'title'   => JText::_('圆点颜色'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('news_item_type', '=', 'newsdata'),
                ),
                'std'     => '#484040',
            ),
            'class_type_options3'         => array(
                'type'    => 'separator',
                'title'   => '左侧联系方式栏目设置',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),
            ),
            'show_contact'                => array(
                'type'    => 'checkbox',
                'title'   => JText::_('显示联系方式'),
                'std'     => '0',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                ),

            ),
            'contact_item_type'           => array(
                'type'    => 'buttons',
                'title'   => '选择配置项',
                'std'     => 'contactlist',
                'values'  => array(
                    array(
                        'label' => '联系方式相关',
                        'value' => 'contactlist',
                    ),
                    array(
                        'label' => 'CSS效果',
                        'value' => 'contactdata',
                    ),
                ),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('show_contact', '=', '1'),
                ),
                'tabs'    => true,
            ),
            'contact_top_cn_title'        => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(中文)'),
                'desc'    => JText::_('选卡导航分类名称(中文)'),
                'std'     => '联系我们',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_top_us_title'        => array(
                'type'    => 'text',
                'title'   => JText::_('选卡导航分类名称(英文)'),
                'desc'    => JText::_('选卡导航分类名称(英文)'),
                'std'     => 'Contact Us',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_name'                => array(
                'type'    => 'text',
                'title'   => JText::_('内容、企业名称'),
                'std'     => '安徽国平苗木有限公司【官网】',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_phone'               => array(
                'type'    => 'text',
                'title'   => JText::_('联系方式电话号码'),
                'std'     => '138-6519-8228',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_mail'                => array(
                'type'    => 'text',
                'title'   => JText::_('联系方式邮箱'),
                'std'     => '<EMAIL>',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_address'             => array(
                'type'    => 'text',
                'title'   => JText::_('联系方式地址'),
                'std'     => '安徽省铜陵市枞阳县麒麟镇新安村',
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactlist'),
                    array('show_contact', '=', '1'),
                ),
            ),
            'contact_font_size'           => array(
                'type'    => 'slider',
                'title'   => JText::_('公司名称字体大小'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactdata'),
                    array('show_contact', '=', '1'),
                ),
                'min'     => 10,
                'max'     => 40,
                'std'     => 16,
            ),
            'contact_font_color'          => array(
                'type'    => 'color',
                'title'   => JText::_('公司名称颜色'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactdata'),
                    array('show_contact', '=', '1'),
                ),
                'std'     => '#000000',
            ),
            'contact_content_font_size'           => array(
                'type'    => 'slider',
                'title'   => JText::_('内容字体大小'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactdata'),
                    array('show_contact', '=', '1'),
                ),
                'min'     => 10,
                'max'     => 40,
                'std'     => 14,
            ),
            'contact_content_font_color'          => array(
                'type'    => 'color',
                'title'   => JText::_('内容颜色'),
                'depends' => array(
                    array('item_type', '=', 'tab'),
                    array('contact_item_type', '=', 'contactdata'),
                    array('show_contact', '=', '1'),
                ),
                'std'     => '#2f2f2f',
            ),
        ),
    )
);
