<?php
defined('_JEXEC') or die ('resticted aceess');
JwAddonsConfig::addonConfig(
array(
	'type'=>'content',
	'addon_name'=>'cq_ty_list00010',
	'title'=>JText::_('滑入显示二维码'),
	'desc'=>JText::_('滑入显示二维码'),
	'category'=>'常用插件',
	'attr'=>array(
		'general'=>array(
            'a_link_open' => array(
                'type' => 'checkbox',
                'title' => JText::_('是否开启链接'),
                'std' => 0,
            ),

            'separator_options1'=>array(
                'type'=>'separator',
                'title'=>'第一组设置',
            ),
            'image1' => array(
                'type' => 'media',
                'title' => JText::_('第一组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title1'=>array(
                'type'=>'text',
                'title'=>'第一组标题',
                'std'=>'标题',
            ),
            'dec1'=>array(
                'type'=>'text',
                'title'=>'第一组描述',
                'std'=>'描述',
            ),
            'keyword1_1'=>array(
                'type'=>'text',
                'title'=>'第一组关键字1',
                'std'=>'关键字1',
            ),
            'keyword1_2'=>array(
                'type'=>'text',
                'title'=>'第一组关键字2',
                'std'=>'关键字2',
            ),
            'image1_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第一组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link1' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id1' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target1' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),

            'separator_options2'=>array(
                'type'=>'separator',
                'title'=>'第二组设置',
            ),
            'image2' => array(
                'type' => 'media',
                'title' => JText::_('第二组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title2'=>array(
                'type'=>'text',
                'title'=>'第二组标题',
                'std'=>'标题',
            ),
            'dec2'=>array(
                'type'=>'text',
                'title'=>'第二组描述',
                'std'=>'描述',
            ),
            'keyword2_1'=>array(
                'type'=>'text',
                'title'=>'第二组关键字1',
                'std'=>'关键字1',
            ),
            'keyword2_2'=>array(
                'type'=>'text',
                'title'=>'第二组关键字2',
                'std'=>'关键字2',
            ),
            'image2_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第二组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link2' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id2' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target2' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options3'=>array(
                'type'=>'separator',
                'title'=>'第三组设置',
            ),
            'image3' => array(
                'type' => 'media',
                'title' => JText::_('第三组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title3'=>array(
                'type'=>'text',
                'title'=>'第三组标题',
                'std'=>'标题',
            ),
            'dec3'=>array(
                'type'=>'text',
                'title'=>'第三组描述',
                'std'=>'描述',
            ),
            'keyword3_1'=>array(
                'type'=>'text',
                'title'=>'第三组关键字1',
                'std'=>'关键字1',
            ),
            'keyword3_2'=>array(
                'type'=>'text',
                'title'=>'第三组关键字2',
                'std'=>'关键字2',
            ),
            'image3_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第三组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link3' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id3' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target3' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options4'=>array(
                'type'=>'separator',
                'title'=>'第四组设置',
            ),
            'image4' => array(
                'type' => 'media',
                'title' => JText::_('第四组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title4'=>array(
                'type'=>'text',
                'title'=>'第四组标题',
                'std'=>'标题',
            ),
            'dec4'=>array(
                'type'=>'text',
                'title'=>'第四组描述',
                'std'=>'描述',
            ),
            'keyword4_1'=>array(
                'type'=>'text',
                'title'=>'第四组关键字1',
                'std'=>'关键字1',
            ),
            'keyword4_2'=>array(
                'type'=>'text',
                'title'=>'第四组关键字2',
                'std'=>'关键字2',
            ),
            'image4_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第四组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link4' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id4' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target4' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options5'=>array(
                'type'=>'separator',
                'title'=>'第五组设置',
            ),
            'image5' => array(
                'type' => 'media',
                'title' => JText::_('第五组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title5'=>array(
                'type'=>'text',
                'title'=>'第五组标题',
                'std'=>'标题',
            ),
            'dec5'=>array(
                'type'=>'text',
                'title'=>'第五组描述',
                'std'=>'描述',
            ),
            'keyword5_1'=>array(
                'type'=>'text',
                'title'=>'第五组关键字1',
                'std'=>'关键字1',
            ),
            'keyword5_2'=>array(
                'type'=>'text',
                'title'=>'第五组关键字2',
                'std'=>'关键字2',
            ),
            'image5_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第五组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link5' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id5' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target5' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options6'=>array(
                'type'=>'separator',
                'title'=>'第六组设置',
            ),
            'image6' => array(
                'type' => 'media',
                'title' => JText::_('第六组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title6'=>array(
                'type'=>'text',
                'title'=>'第六组标题',
                'std'=>'标题',
            ),
            'dec6'=>array(
                'type'=>'text',
                'title'=>'第六组描述',
                'std'=>'描述',
            ),
            'keyword6_1'=>array(
                'type'=>'text',
                'title'=>'第六组关键字1',
                'std'=>'关键字1',
            ),
            'keyword6_2'=>array(
                'type'=>'text',
                'title'=>'第六组关键字2',
                'std'=>'关键字2',
            ),
            'image6_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第六组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link6' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id6' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target6' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options7'=>array(
                'type'=>'separator',
                'title'=>'第七组设置',
            ),
            'image7' => array(
                'type' => 'media',
                'title' => JText::_('第七组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title7'=>array(
                'type'=>'text',
                'title'=>'第七组标题',
                'std'=>'标题',
            ),
            'dec7'=>array(
                'type'=>'text',
                'title'=>'第七组描述',
                'std'=>'描述',
            ),
            'keyword7_1'=>array(
                'type'=>'text',
                'title'=>'第七组关键字1',
                'std'=>'关键字1',
            ),
            'keyword7_2'=>array(
                'type'=>'text',
                'title'=>'第七组关键字2',
                'std'=>'关键字2',
            ),
            'image7_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第七组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link7' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id7' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target7' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options8'=>array(
                'type'=>'separator',
                'title'=>'第八组设置',
            ),
            'image8' => array(
                'type' => 'media',
                'title' => JText::_('第八组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'title8'=>array(
                'type'=>'text',
                'title'=>'第八组标题',
                'std'=>'标题',
            ),
            'dec8'=>array(
                'type'=>'text',
                'title'=>'第八组描述',
                'std'=>'描述',
            ),
            'keyword8_1'=>array(
                'type'=>'text',
                'title'=>'第八组关键字1',
                'std'=>'关键字1',
            ),
            'keyword8_2'=>array(
                'type'=>'text',
                'title'=>'第八组关键字2',
                'std'=>'关键字2',
            ),
            'image8_hover' => array(
                'type' => 'media',
                'title' => JText::_('鼠标进入第八组图片'),
                'show_input' => true,
                'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg'
            ),
            'link8' => array(
                'type' => 'text',
                'title' => JText::_('外部链接->http(https)开头'),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'detail_page_id8' => array(
                'type' => 'select',
                'title' => '内部页面链接',
                'desc' => '',
                'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'target8' => array(
                'type' => 'select',
                'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
                'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
                'values' => array(
                    '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                    '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                ),
                'depends' => array(
                    array('a_link_open', '=', 1),
                ),
            ),
            'separator_options10'=>array(
                'type'=>'separator',
                'title'=>'样式设置',
            ),
			'attr_1'=>array(
				'type'=>'number',
				'title'=>'标题字体大小',
				'std'=>' 16',
			),
			'attr_2'=>array(
				'type'=>'number',
				'title'=>'标题字体高度',
				'std'=>' 16',
			),
			'attr_3'=>array(
				'type'=>'color',
				'title'=>'标题字体颜色',
				'std'=>' #60667E',
			),
			'attr_4'=>array(
				'type'=>'color',
				'title'=>'横线颜色',
				'std'=>' #60667E',
			),
			'attr_5'=>array(
				'type'=>'number',
				'title'=>'描述字体大小',
				'std'=>' 17',
			),
			'attr_6'=>array(
				'type'=>'number',
				'title'=>'描述字体高度',
				'std'=>' 17',
			),
			'attr_7'=>array(
				'type'=>'color',
				'title'=>'描述字体颜色',
				'std'=>' #AFB2C0',
			),
			'attr_8'=>array(
				'type'=>'number',
				'title'=>'关键词字体大小',
				'std'=>' 14',
			),
			'attr_9'=>array(
				'type'=>'number',
				'title'=>'关键词字体高度',
				'std'=>' 24',
			),
			'attr_10'=>array(
				'type'=>'color',
				'title'=>'关键词字体颜色',
				'std'=>' #9797A1',
			),
            'attr_11'=>array(
                'type'=>'color',
                'title'=>'二维码背景颜色',
                'std'=>'lightcoral',
            ),

		),
	),
));