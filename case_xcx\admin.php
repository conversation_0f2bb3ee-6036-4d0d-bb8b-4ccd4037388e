<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_case_xcx',
		'title' => JText::_('二维码案例'),
		'desc' => JText::_('二维码案例'),
		'category' => '常用插件',
		'attr' => array(
			'general' => array(

				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),

				// Title
				'title' => array(
					'type' => 'textarea',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
					'std' => ''
				),

				'heading_selector' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
					'values' => array(
						'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
						'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
						'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
						'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
						'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
						'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
						'p' => 'p',
						'span' => 'span',
						'div' => 'div'
					),
					'std' => 'h3',
					'depends' => array(array('title', '!=', '')),
				),

				'font_family' => array(
					'type' => 'fonts',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
					'depends' => array(array('title', '!=', '')),
					'selector' => array(
						'type' => 'font',
						'font' => '{{ VALUE }}',
						'css' => '.jwpf-addon-title { font-family: "{{ VALUE }}"; }'
					)
				),

				'title_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
					'std' => '',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_font_style' => array(
					'type' => 'fontstyle',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_letterspace' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
					'values' => array(
						'-10px' => '-10px',
						'-9px' => '-9px',
						'-8px' => '-8px',
						'-7px' => '-7px',
						'-6px' => '-6px',
						'-5px' => '-5px',
						'-4px' => '-4px',
						'-3px' => '-3px',
						'-2px' => '-2px',
						'-1px' => '-1px',
						'0px' => 'Default',
						'1px' => '1px',
						'2px' => '2px',
						'3px' => '3px',
						'4px' => '4px',
						'5px' => '5px',
						'6px' => '6px',
						'7px' => '7px',
						'8px' => '8px',
						'9px' => '9px',
						'10px' => '10px'
					),
					'std' => '0',
					'depends' => array(array('title', '!=', '')),
				),

				'title_text_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'depends' => array(array('title', '!=', '')),
				),

				'title_margin_top' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_TOP_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				'title_margin_bottom' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_MARGIN_BOTTOM_DESC'),
					'placeholder' => '10',
					'depends' => array(array('title', '!=', '')),
					'max' => 400,
					'responsive' => true
				),

				// Content
				'phone_open' => array(
					'type' => 'checkbox',
					'title' => JText::_('手机开启滚动'),
					'std' => 0
				),
				// 图片：
				'imgs' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否固定图片高度'),
					'desc' => JText::_('是否固定图片高度'),
					'std' => 0
				),
				'img_height' => array(
					'type' => 'slider',
					'title' => JText::_('设置图片高度'),
					'std' => array('md' => '225', 'sm' => '200', 'xs' => '180'),
					'max' => 1000,
					'depends' => array(array('imgs', '=', '1')),
					'responsive' => true
				),
				'img_tcfs' => array(
					'type' => 'select',
					'title' => JText::_('图片填充方式'),
					'depends' => array(array('imgs', '=', '1')),
					'values' => array(
                        'fill' => '占满不切割',
                        'contain' => '自适应显示',
                        'cover' => '占满切割',
                    ),
                    'std' => 'fill'
				),
				// 项目名称
				'text_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('项目名称字体大小'),
					'std' => array('md' => '20', 'sm' => '16', 'xs' => '16'),
					
					'max' => 400,
					'responsive' => true
				),
				'text_color' => array(
					'type' => 'color',
					'title' => JText::_('项目名称颜色'),
					'std' => '#3c3c50',

					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
				),

				// 简介
				'wbtext_height' => array(
					'type' => 'slider',
					'title' => JText::_('文本区域高度'),
					'std' => array('md' => '128', 'sm' => '120', 'xs' => '80'),
					'max' => 200,
					'responsive' => true
				),
				'wbtext_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('文本字体大小'),

					'std' => array('md' => '14', 'sm' => '14', 'xs' => '12'),

					'max' => 400,
					'responsive' => true
				),
				'wbtext_color' => array(
					'type' => 'color',
					'title' => JText::_('文本字体颜色'),
					'std' => '#3c3c50',
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
				),

				'wbtext_lineheight' => array(
					'type' => 'slider',
					'title' => JText::_('文本行高'),
					'std' => array('md' => '30', 'sm' => '25', 'xs' => '25'),
					
					'max' => 400,
					'responsive' => true
				),
				// 标签
				'bqtext_fontsize' => array(
					'type' => 'slider',
					'title' => JText::_('标签字体大小'),
					'std' => array('md' => '15', 'sm' => '14', 'xs' => '12'),
					'max' => 400,
					'responsive' => true
				),
				'bqtext_color' => array(
					'type' => 'color',
					'title' => JText::_('标签字体颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'std' => '#fd701b',

				),
				'bqbg_color' => array(
					'type' => 'color',
					'title' => JText::_('标签背景颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'std' => 'rgba(234,112,27,.1)',

				),
				'border_color' => array(
					'type' => 'color',
					'title' => JText::_('标签边框颜色'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
					'std' => 'rgba(255,75,75,.1)',

				),

				'resource' => array(
                    'type' => 'select',
                    'title' => '选择产品资源',
                    'desc' => '选择产品资源',
                    'values' => array(
                        'article' => '产品资源',
                    ),
                    'std' => 'article',
                ),

                'goods_catid' => array(
                    'type' => 'select',
                    'title' => JText::_('选择产品分类'),
                    'desc' => '产品分类',
                    'values' => JwPageFactoryBase::getTypeList($site_id,$company_id,'com_goods')['list'],
                ),
                // 'detail_page_id' => array(
                //     'type' => 'select',
                //     'title' => '详情页模版',
                //     'desc' => '显示文章详情页模版',
                //     'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                // ),
                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFAC阴影水平偏移TORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),
                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '6'
                ),

                'columns' => array(
                    'type' => 'number',
                    'title' => JText::_('PC及平板列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '3',
                    
                ),
                'columns_xs' => array(
                    'type' => 'number',
                    'title' => JText::_('手机列数'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_COLUMNS_DESC'),
                    'std' => '2',
                    
                ),

                'show_page' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示翻页码'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_SHOW_INTRO_DESC'),
                    'std' => '0',
                    'depends' => array(
                        array('pro_type' ,'!=', 'type17'),
                    ),
                ),

                'page_style_selector' => array(
                    'type' => 'select',
                    'title' => '翻页样式',
                    'values' => array(
                        'page01' => '翻页样式一',
                        'page02' => '翻页样式二',
                        //    'page03' => '翻页样式三',
                    ),
                    'depends' => array('show_page' => true),
                    'std' => 'page01'
                ),

                'page1_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#1e1e1e',
                ),
                'page1_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('翻页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#b3b3b3',
                ),
                'page1_cur_fontcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页字体颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#fff',
                ),
                'page1_cur_bordercolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页边框颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#2a68a7',
                ),
                'page1_cur_bgcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('当前页背景颜色'),
                    'depends' => array(
                        array('show_page' ,'=', true),
                      
                    ),
                    'std' => '#2a68a7',
                ),

                'link_catid' => array(
                    'type' => 'category',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'depends' => array(
                        array('resource', '=', 'article'),
                        array('link_articles', '=', '1')
                    )
                ),

				
			),
		),
	)
);
