<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonBrand_story extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $config = new JConfig();
        $imgurl = $config->img_url;
        $brand_type = (isset($settings->brand_type)) ? $settings->brand_type : 'type1';

        $output = '';

        $output .= '
            <style>
                '.$addon_id.' .i300 > img {
                  width: 100%;
                  height: 100%;
                }
                '.$addon_id.' .i100 {
                  overflow: hidden;
                }
                '.$addon_id.' .i100 > img {
                  width: 100%;
                }
                @media only screen and (min-width: 1480px) {
                    '.$addon_id.' .story1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;overflow: hidden;z-index: 0;}
                    '.$addon_id.' .story1-a2{height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-a3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .story1-a4{width: calc(165/9600*100%);position: absolute;top: calc(230/925*100%);left: calc(1260/9600*100%);animation: story1 6s linear infinite;animation-delay: -1s;}
                    '.$addon_id.' .story1-a5{width: calc(340/9600*100%);position: absolute;top: calc(630/925*100%);left: calc(920/9600*100%);animation: story2 6s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a6{width: calc(328/9600*100%);position: absolute;top: calc(135/925*100%);left: calc(1790/9600*100%);animation: story3 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a7{width: calc(180/9600*100%);position: absolute;top: calc(665/925*100%);left: calc(710/9600*100%);animation: story4 10s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a8{width: calc(80/9600*100%);position: absolute;top: calc(555/925*100%);left: calc(1360/9600*100%);animation: story5 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a9{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-b1{position: absolute;width: calc(570/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(1){top: calc(305/925*100%);left: calc(2100/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2){top: calc(110/925*100%);left: calc(2650/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3){top: calc(280/925*100%);left: calc(3220/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4){top: calc(135/925*100%);left: calc(3865/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5){top: calc(315/925*100%);left: calc(4540/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6){top: calc(150/925*100%);left: calc(5150/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7){top: calc(305/925*100%);left: calc(5805/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8){top: calc(120/925*100%);left: calc(6375/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9){top: calc(320/925*100%);left: calc(7045/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10){top: calc(120/925*100%);left: calc(7720/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11){top: calc(320/925*100%);left: calc(8385/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12){top: calc(165/925*100%);left: calc(8985/9600*100%);}
                    '.$addon_id.' .story1-b2{width: 100%;position: relative;}
                    '.$addon_id.' .story1-b3{font-size: 18px;line-height: 36px;color: #3d3215;position: absolute;}
                    '.$addon_id.' .story1-b1:nth-child(1) .story1-b3{width: calc(390/570*100%);bottom: 100%;left: calc(130/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2) .story1-b3{width: calc(390/570*100%);top: 100%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3) .story1-b3{width: calc(390/570*100%);top: 9%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4) .story1-b3{width: calc(440/570*100%);top: 100%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5) .story1-b3{width: calc(450/570*100%);bottom: 100%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6) .story1-b3{width: calc(480/570*100%);top: 86%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7) .story1-b3{width: calc(420/570*100%);bottom: 100%;left: calc(40/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8) .story1-b3{width: calc(410/570*100%);top: 102%;left: calc(95/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9) .story1-b3{width: calc(470/570*100%);bottom: 88%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10) .story1-b3{width: calc(490/570*100%);top: 106%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11) .story1-b3{width: calc(435/570*100%);bottom: 104%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12) .story1-b3{width: calc(490/570*100%);top: 96%;left: calc(70/570*100%);}
                    '.$addon_id.' .story1-c1{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-c2{width: calc(186/9600*100%);position: absolute;animation: story11 72s linear infinite;transform-origin: 50% 50%;}
                    @keyframes story1{0%{top: calc(230/925*100%);}50%{top: calc(160/925*100%);}100%{top: calc(230/925*100%);}}
                    @keyframes story2{0%{left: calc(920/9600*100%);}50%{left: calc(1000/9600*100%);}100%{left: calc(920/9600*100%);}}
                    @keyframes story3{0%{top: calc(85/925*100%);}50%{top: calc(165/925*100%);}100%{top: calc(85/925*100%);}}
                    @keyframes story4{0%{transform: rotate(-8deg);}50%{transform: rotate(8deg);}100%{transform: rotate(-8deg);}}
                    @keyframes story5{0%{transform: rotateY(0deg);}10%{transform: rotateY(360deg);}100%{transform: rotateY(360deg);}}
                    @keyframes story11{
                        0%{top: calc(710/925*100%);left: calc(1410/9600*100%);transform: rotate(0deg);}
                        7.5%{top: calc(660/925*100%);left: calc(1880/9600*100%);transform: rotate(-6deg);}
                        15%{top: calc(730/925*100%);left: calc(2580/9600*100%);transform: rotate(6deg);}
                        22.5%{top: calc(660/925*100%);left: calc(3275/9600*100%);transform: rotate(-6deg);}
                        30%{top: calc(730/925*100%);left: calc(3935/9600*100%);transform: rotate(6deg);}
                        37.5%{top: calc(660/925*100%);left: calc(4650/9600*100%);transform: rotate(-6deg);}
                        45%{top: calc(730/925*100%);left: calc(5335/9600*100%);transform: rotate(6deg);}
                        52.5%{top: calc(660/925*100%);left: calc(6070/9600*100%);transform: rotate(-6deg);}
                        60%{top: calc(730/925*100%);left: calc(6730/9600*100%);transform: rotate(6deg);}
                        67.5%{top: calc(660/925*100%);left: calc(7460/9600*100%);transform: rotate(-6deg);}
                        75%{top: calc(730/925*100%);left: calc(8175/9600*100%);transform: rotate(6deg);}
                        82.5%{top: calc(660/925*100%);left: calc(8820/9600*100%);transform: rotate(-6deg);}
                        90%{top: calc(730/925*100%);left: calc(9475/9600*100%);transform: rotate(6deg);}
                        100%{top: calc(660/925*100%);left: calc(9800/9600*100%);transform: rotate(0deg);}
                    }
                }
                @media only screen and (max-width: 1479px) and (min-width: 1024px) {
                    '.$addon_id.' .story1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;overflow: hidden;z-index: 0;}
                    '.$addon_id.' .story1-a2{height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-a3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .story1-a4{width: calc(165/9600*100%);position: absolute;top: calc(230/925*100%);left: calc(1260/9600*100%);animation: story1 6s linear infinite;animation-delay: -1s;}
                    '.$addon_id.' .story1-a5{width: calc(340/9600*100%);position: absolute;top: calc(630/925*100%);left: calc(920/9600*100%);animation: story2 6s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a6{width: calc(328/9600*100%);position: absolute;top: calc(135/925*100%);left: calc(1790/9600*100%);animation: story3 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a7{width: calc(180/9600*100%);position: absolute;top: calc(665/925*100%);left: calc(710/9600*100%);animation: story4 10s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a8{width: calc(80/9600*100%);position: absolute;top: calc(555/925*100%);left: calc(1360/9600*100%);animation: story5 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a9{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-b1{position: absolute;width: calc(570/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(1){top: calc(305/925*100%);left: calc(2100/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2){top: calc(110/925*100%);left: calc(2650/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3){top: calc(280/925*100%);left: calc(3220/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4){top: calc(135/925*100%);left: calc(3865/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5){top: calc(315/925*100%);left: calc(4540/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6){top: calc(150/925*100%);left: calc(5150/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7){top: calc(305/925*100%);left: calc(5805/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8){top: calc(120/925*100%);left: calc(6375/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9){top: calc(320/925*100%);left: calc(7045/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10){top: calc(120/925*100%);left: calc(7720/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11){top: calc(320/925*100%);left: calc(8385/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12){top: calc(165/925*100%);left: calc(8985/9600*100%);}
                    '.$addon_id.' .story1-b2{width: 100%;position: relative;}
                    '.$addon_id.' .story1-b3{font-size: 14px;line-height: 28px;color: #3d3215;position: absolute;}
                    '.$addon_id.' .story1-b1:nth-child(1) .story1-b3{width: calc(390/570*100%);bottom: 100%;left: calc(130/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2) .story1-b3{width: calc(390/570*100%);top: 100%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3) .story1-b3{width: calc(390/570*100%);top: 9%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4) .story1-b3{width: calc(440/570*100%);top: 100%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5) .story1-b3{width: calc(450/570*100%);bottom: 100%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6) .story1-b3{width: calc(480/570*100%);top: 86%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7) .story1-b3{width: calc(420/570*100%);bottom: 100%;left: calc(40/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8) .story1-b3{width: calc(410/570*100%);top: 102%;left: calc(95/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9) .story1-b3{width: calc(470/570*100%);bottom: 88%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10) .story1-b3{width: calc(490/570*100%);top: 106%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11) .story1-b3{width: calc(435/570*100%);bottom: 104%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12) .story1-b3{width: calc(490/570*100%);top: 96%;left: calc(70/570*100%);}
                    '.$addon_id.' .story1-c1{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-c2{width: calc(186/9600*100%);position: absolute;animation: story11 72s linear infinite;transform-origin: 50% 50%;}
                    @keyframes story1{0%{top: calc(230/925*100%);}50%{top: calc(160/925*100%);}100%{top: calc(230/925*100%);}}
                    @keyframes story2{0%{left: calc(920/9600*100%);}50%{left: calc(1000/9600*100%);}100%{left: calc(920/9600*100%);}}
                    @keyframes story3{0%{top: calc(85/925*100%);}50%{top: calc(165/925*100%);}100%{top: calc(85/925*100%);}}
                    @keyframes story4{0%{transform: rotate(-8deg);}50%{transform: rotate(8deg);}100%{transform: rotate(-8deg);}}
                    @keyframes story5{0%{transform: rotateY(0deg);}10%{transform: rotateY(360deg);}100%{transform: rotateY(360deg);}}
                    @keyframes story11{
                        0%{top: calc(710/925*100%);left: calc(1410/9600*100%);transform: rotate(0deg);}
                        7.5%{top: calc(660/925*100%);left: calc(1880/9600*100%);transform: rotate(-6deg);}
                        15%{top: calc(730/925*100%);left: calc(2580/9600*100%);transform: rotate(6deg);}
                        22.5%{top: calc(660/925*100%);left: calc(3275/9600*100%);transform: rotate(-6deg);}
                        30%{top: calc(730/925*100%);left: calc(3935/9600*100%);transform: rotate(6deg);}
                        37.5%{top: calc(660/925*100%);left: calc(4650/9600*100%);transform: rotate(-6deg);}
                        45%{top: calc(730/925*100%);left: calc(5335/9600*100%);transform: rotate(6deg);}
                        52.5%{top: calc(660/925*100%);left: calc(6070/9600*100%);transform: rotate(-6deg);}
                        60%{top: calc(730/925*100%);left: calc(6730/9600*100%);transform: rotate(6deg);}
                        67.5%{top: calc(660/925*100%);left: calc(7460/9600*100%);transform: rotate(-6deg);}
                        75%{top: calc(730/925*100%);left: calc(8175/9600*100%);transform: rotate(6deg);}
                        82.5%{top: calc(660/925*100%);left: calc(8820/9600*100%);transform: rotate(-6deg);}
                        90%{top: calc(730/925*100%);left: calc(9475/9600*100%);transform: rotate(6deg);}
                        100%{top: calc(660/925*100%);left: calc(9800/9600*100%);transform: rotate(0deg);}
                    }
                }
                @media only screen and (max-width: 1080px) and (min-width: 1024px) {
                    '.$addon_id.' .story1-a1{overflow: hidden;overflow-x: auto;}
                    '.$addon_id.' .story1-a1::-webkit-scrollbar{width: 0;height: 0;}
                    '.$addon_id.' .story1-a1::-webkit-scrollbar-thumb{border-radius: 0;background: none;}
                    '.$addon_id.' .story1-a1::-webkit-scrollbar-track{border-radius: 0;background: none;}
                }
                @media only screen and (max-width: 1023px) {
                    '.$addon_id.' .story1-a1{width: 100%;height: 100%;position: fixed;top: 0;left: 0;overflow: hidden;overflow-x: auto;z-index: 0;}
                    '.$addon_id.' .story1-a2{height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-a3{width: 100%;height: 100%;position: relative;}
                    '.$addon_id.' .story1-a4{width: calc(165/9600*100%);position: absolute;top: calc(230/925*100%);left: calc(1260/9600*100%);animation: story1 6s linear infinite;animation-delay: -1s;}
                    '.$addon_id.' .story1-a5{width: calc(340/9600*100%);position: absolute;top: calc(630/925*100%);left: calc(920/9600*100%);animation: story2 6s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a6{width: calc(328/9600*100%);position: absolute;top: calc(135/925*100%);left: calc(1790/9600*100%);animation: story3 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a7{width: calc(180/9600*100%);position: absolute;top: calc(665/925*100%);left: calc(710/9600*100%);animation: story4 10s linear infinite;animation-delay: -3s;}
                    '.$addon_id.' .story1-a8{width: calc(80/9600*100%);position: absolute;top: calc(555/925*100%);left: calc(1360/9600*100%);animation: story5 8s linear infinite;animation-delay: -2s;}
                    '.$addon_id.' .story1-a9{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-b1{position: absolute;width: calc(570/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(1){top: calc(305/925*100%);left: calc(2100/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2){top: calc(110/925*100%);left: calc(2650/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3){top: calc(280/925*100%);left: calc(3220/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4){top: calc(135/925*100%);left: calc(3865/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5){top: calc(315/925*100%);left: calc(4540/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6){top: calc(150/925*100%);left: calc(5150/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7){top: calc(305/925*100%);left: calc(5805/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8){top: calc(120/925*100%);left: calc(6375/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9){top: calc(320/925*100%);left: calc(7045/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10){top: calc(120/925*100%);left: calc(7720/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11){top: calc(320/925*100%);left: calc(8385/9600*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12){top: calc(165/925*100%);left: calc(8985/9600*100%);}
                    '.$addon_id.' .story1-b2{width: 100%;position: relative;}
                    '.$addon_id.' .story1-b3{font-size: 14px;line-height: 28px;color: #3d3215;position: absolute;}
                    '.$addon_id.' .story1-b1:nth-child(1) .story1-b3{width: calc(390/570*100%);bottom: 100%;left: calc(130/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(2) .story1-b3{width: calc(390/570*100%);top: 100%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(3) .story1-b3{width: calc(390/570*100%);top: 9%;left: calc(90/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(4) .story1-b3{width: calc(440/570*100%);top: 100%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(5) .story1-b3{width: calc(450/570*100%);bottom: 100%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(6) .story1-b3{width: calc(480/570*100%);top: 86%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(7) .story1-b3{width: calc(420/570*100%);bottom: 100%;left: calc(40/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(8) .story1-b3{width: calc(410/570*100%);top: 102%;left: calc(95/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(9) .story1-b3{width: calc(470/570*100%);bottom: 88%;left: calc(55/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(10) .story1-b3{width: calc(490/570*100%);top: 106%;left: calc(50/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(11) .story1-b3{width: calc(435/570*100%);bottom: 104%;left: calc(80/570*100%);}
                    '.$addon_id.' .story1-b1:nth-child(12) .story1-b3{width: calc(490/570*100%);top: 96%;left: calc(70/570*100%);}
                    '.$addon_id.' .story1-c1{width: 100%;height: 100%;position: absolute;top: 0;left: 0;}
                    '.$addon_id.' .story1-c2{width: calc(186/9600*100%);position: absolute;animation: story11 72s linear infinite;transform-origin: 50% 50%;}
                    @keyframes story1{0%{top: calc(230/925*100%);}50%{top: calc(160/925*100%);}100%{top: calc(230/925*100%);}}
                    @keyframes story2{0%{left: calc(920/9600*100%);}50%{left: calc(1000/9600*100%);}100%{left: calc(920/9600*100%);}}
                    @keyframes story3{0%{top: calc(85/925*100%);}50%{top: calc(165/925*100%);}100%{top: calc(85/925*100%);}}
                    @keyframes story4{0%{transform: rotate(-8deg);}50%{transform: rotate(8deg);}100%{transform: rotate(-8deg);}}
                    @keyframes story5{0%{transform: rotateY(0deg);}10%{transform: rotateY(360deg);}100%{transform: rotateY(360deg);}}
                    @keyframes story11{
                        0%{top: calc(710/925*100%);left: calc(1410/9600*100%);transform: rotate(0deg);}
                        7.5%{top: calc(660/925*100%);left: calc(1880/9600*100%);transform: rotate(-6deg);}
                        15%{top: calc(730/925*100%);left: calc(2580/9600*100%);transform: rotate(6deg);}
                        22.5%{top: calc(660/925*100%);left: calc(3275/9600*100%);transform: rotate(-6deg);}
                        30%{top: calc(730/925*100%);left: calc(3935/9600*100%);transform: rotate(6deg);}
                        37.5%{top: calc(660/925*100%);left: calc(4650/9600*100%);transform: rotate(-6deg);}
                        45%{top: calc(730/925*100%);left: calc(5335/9600*100%);transform: rotate(6deg);}
                        52.5%{top: calc(660/925*100%);left: calc(6070/9600*100%);transform: rotate(-6deg);}
                        60%{top: calc(730/925*100%);left: calc(6730/9600*100%);transform: rotate(6deg);}
                        67.5%{top: calc(660/925*100%);left: calc(7460/9600*100%);transform: rotate(-6deg);}
                        75%{top: calc(730/925*100%);left: calc(8175/9600*100%);transform: rotate(6deg);}
                        82.5%{top: calc(660/925*100%);left: calc(8820/9600*100%);transform: rotate(-6deg);}
                        90%{top: calc(730/925*100%);left: calc(9475/9600*100%);transform: rotate(6deg);}
                        100%{top: calc(660/925*100%);left: calc(9800/9600*100%);transform: rotate(0deg);}
                    }
                }
            </style>
        ';
        $output .= '
            <div class="story1-a1">
                <div class="story1-a2" style="width: 8759px; left:0px;">
                    <div class="story1-a3 i300"><img src="https://oss.lcweb01.cn/joomla/20220725/283d505ffc32ab5591047526a7aa5f89.jpg"></div>
                    <div class="story1-a4 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/1d60809be2b5fac24cf583fda487cff0.png"></div>
                    <div class="story1-a5 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/90bcec8765701423ad69a236a59306fd.png"></div>
                    <div class="story1-a6 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/a222feffdcd3b94697a122fb1f92b6d8.png"></div>
                    <div class="story1-a7 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/cc0ac696f15ca00139652bd4fdc971c2.png"></div>
                    <div class="story1-a8 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/1b9c175ac5914bd1f04212137a645116.png"></div>
                    <div class="story1-a9">
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/d645c8700c1630a2e4dd366b722ccc62.png"></div>
                            <div class="story1-b3">1903年，中东铁路正式通车，大批俄国人到被誉为东方小巴黎的哈尔滨兴建实业，法（俄）籍犹太人约瑟·开斯普便是其中的翘楚。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/39e19c665c210194fc8205ca0acc2524.png"></div>
                            <div class="story1-b3">约瑟·开斯普年少参军，日俄战争爆发后，他义无反顾投身战役，与战友约瑟沃里·托夫斯基结为生死之交。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/051daf9aba8b7e3b7dca23ec68f4caaf.png"></div>
                            <div class="story1-b3">战争结束后，约瑟·开斯普来到哈尔滨，先后从事过修理钟表、经营珠宝等生意。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/40bc56e231769723a693ed85e7413943.png"></div>
                            <div class="story1-b3">取得资本原始积累后，约瑟·开斯普在中央大街建造了当时远东地区最豪华，同时也是中国最早的涉外旅馆——马迭尔旅馆。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/0e9198cdc1e9b5ef471bff97ee1d4861.png"></div>
                            <div class="story1-b3">一时间，各界名流、达官显贵汇集于此，香鬓俪影、轻歌曼舞、灯红酒绿，马迭尔旅馆成为当时上流社会主要的社交场所。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/941e7dc41dc3831018950744859d76d0.png"></div>
                            <div class="story1-b3">他的战友约瑟沃里·托夫斯基因具有家族畜牧业经验，选择在世界三大优质碱草带之一的安达市建立卧里屯牧场，并从俄国引进中国第一头黑白花奶牛，这便是中国第一家乳品企业的雏形。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/adeaade56313905be9150d94aa9fdc33.png"></div>
                            <div class="story1-b3">约瑟·开斯普意识到卧里屯牧场的奶源十分优质，于是他令人到冬季封冻的松花江上取冰，在马迭尔旅馆下层砌成冰窖，作为夏季制冰的冷源。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/b26a884177b1c59ddab966a5c51b86d5.png"></div>
                            <div class="story1-b3">将冻好的奶坨从距离哈尔滨100多里以外的卧里屯牧场运回，置于冰窖以备夏季食用。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/c5144039ec787535101c134bbfefa64d.png"></div>
                            <div class="story1-b3">每至7、8月份，约瑟·开斯普就会令人打开密闭的冰窖，取出奶坨用于制作冰品。</div>
                        </div>
                                    <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/13cb72cb0056e787f963b47faffd507b.png"></div>
                            <div class="story1-b3">将奶坨打碎后放入锅中熬制，根据欧洲宫廷古法制作冰棍的秘方，加入特定比例的糖、鸡蛋清以及纯天然香料，再倒入冰棍模具中运回冰窖冷冻。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/5b5fbf97447d0ff16c329551e90ac9b2.png"></div>
                            <div class="story1-b3">冰窖一分为二，一部分储存冰坨，另一部分用于冷冻完成灌模的冰棍。</div>
                        </div>
                        <div class="story1-b1">
                            <div class="story1-b2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/5cc2637f353cb37df87492bf2d56067d.png"></div>
                            <div class="story1-b3">一经上市风靡了当时整个上流社会、此后流传了超越一个世纪之久的马迭尔冰棍就此诞生！它的出现开创了哈尔滨冷饮业的先河，马迭尔也因此成为中国最早的冷饮企业！</div>
                        </div>
                    </div>
                    <div class="story1-c1">
                        <div class="story1-c2 i100"><img src="https://oss.lcweb01.cn/joomla/20220725/86b7ae571faf9ac4dc2fa22fc6a8b9b5.png"></div>
                    </div>
                </div>
            </div>

            <script type="text/javascript">
                jQuery(function($){
        
                    $("'.$addon_id.' .story1-a2").width(Math.floor($(window).height()/925*9600));
                    $("'.$addon_id.' .story1-a1").mousemove(function(e){
                        if($(window).width()>1080){
                            $("'.$addon_id.' .story1-a2").css("left",($(window).width()-$(".story1-a2").width())*e.clientX/$(window).width());
                        }
                    });
          
                })
            </script>
        ';

        return $output;
    }

    public function css()
    {

    }
    //用于设计器中显示
//    public static function getTemplate()
//    {
//        $output.='占位';
//        return $output;
//    }
//        $config = new JConfig();
//        $imgurl = $config->img_url;
//        $output = "";
//        $output .= "
//                <#
//                    var addonId = 'jwpf-addon-'+data.id;
//                     var padding_tb = (typeof data.padding_tb !== 'undefined' && data.padding_tb) ? data.padding_tb : 22;
//                     var padding_lr = (typeof data.padding_lr !== 'undefined' && data.padding_lr) ? data.padding_lr : 22;
//                 #>
//
//       <style type='text/css'>
//       <# if (data.art_type_selector == 'type1'){ #>
//
//            #{{addonId}} .box{
//              background:{{data.bg_color}};
//              border-radius:{{data.fc_border_radius}}px;
//              <# if (data.fix_img_height != 0) { #>
//              /*position: fixed;*/
//              <# }else{ #>
//              position: fixed;
//              <#}#>
//
//              width:{{data.fc_width}}px;
//              padding-top:{{padding_tb}}px;
//              padding-bottom:{{padding_tb}}px;
//              padding-left:{{padding_lr}}px;
//              padding-right:{{padding_lr}}px;
//              <# if(data.theme=='location1') { #>
//                  bottom:0;
//                  left:0;
//               <# } #>
//                <# if(data.theme=='location2') { #>
//                  bottom:0;
//                  right:0;
//               <# } #>
//               <# if(data.theme=='location3') { #>
//                  top:0;
//                  left:0;
//               <# } #>
//               <# if(data.theme=='location4') { #>
//                  top:0;
//                  right:0;
//               <# } #>
//            }
//             #{{addonId}} .box .box_img_wx{
//                    text-align: center;
//             }
//             #{{addonId}} .box .box_img_wx img{
//                height: {{data.image_code_height}}px;
//                display: inline-block;
//
//             }
//             #{{addonId}} .box .box_zx_text{
//                text-align: center;
//                color: {{data.zx_text_color}};
//                font-size: {{data.zx_font_size}}px;
//             }
//             #{{addonId}} .box .phone_zx{
//                text-align: center;border-bottom: 1px solid #fff;
//                padding: 12px;
//             }
//             #{{addonId}} .box .phone_zx img{
//                display: inline-block;
//             }
//              #{{addonId}} .box .phone_zx span{
//                color: {{data.phone_font_color}};
//                font-size: {{data.phone_font_size}}px;
//             }
//             #{{addonId}} .box .phone_wx{
//                text-align: center;
//                border-bottom: 1px solid #fff;
//                padding: 12px;
//             }
//             #{{addonId}} .box .phone_wx img{
//                display: inline-block;
//             }
//             #{{addonId}} .box .phone_wx span{
//                color: {{data.wx_font_color}};
//                font-size: {{data.wx_font_size}}px;
//             }
//             #{{addonId}} .arrow{
//                 text-align: center;
//                 padding-top: 12px;
//                 color: {{data.wx_font_color}};
//             }
//        <#}elseif(data.art_type_selector == 'type2'){#>
//
//        /*右侧悬浮菜单*/
//        .slide{
//
//            width: 50px;
//            height: 250px;
//            position: fixed;
//            top: 50%;
//            margin-top: -126px;
//            margin-right: 26px;
//            background: #d7012a;
//            right: 0;
//            border-radius: 30px;
//            z-index: 999;
//        }
//        .slide ul{
//            list-style: none;
//        }
//        .slide .icon li{
//            width: 49px;
//            height: 50px;
//            background: url('/components/com_jwpagefactory/assets/img/index_cy.png') no-repeat;
//        }
//        .slide .icon .up{
//            background-position:-330px -120px ;
//        }
//        .slide .icon li.qq{
//            background-position:-385px -73px ;
//            margin-top: 30px;
//        }
//        .slide .icon li.tel{
//            background-position:-385px -160px ;
//        }
//        .slide .icon li.wx{
//            background-position:-385px -120px ;
//        }
//        .slide .icon li.down{
//            background-position:-330px -160px ;
//        }
//        .slide .info{
//            top: 50%;
//            height: 147px;
//            position: absolute;
//            right: 100%;
//            background: #018D75;
//            width: 0px;
//            overflow: hidden;
//            margin-top: -73.5px;
//            transition:0.5s;
//            border-radius:4px 0 0 4px ;
//        }
//        .slide .info.hover{
//            width: 145px;
//
//        }
//        .slide .info li{
//            width: 145px;
//            color: #CCCCCC;
//            text-align: center;
//        }
//        .slide .info li p{
//            font-size: 1.1em;
//            line-height: 2em;
//            padding: 15px;
//            text-align: left;
//        }
//        .slide .info li.qq p a{
//            display: block;
//            margin-top: 12px;
//            width: 100px;
//            height: 32px;
//            line-height: 32px;
//            color: #00DFB9;
//            font-size: 16px;
//            text-align: center;
//            text-decoration: none;
//            border: 1px solid #00DFB9;
//            border-radius: 5px;
//        }
//        .slide .info li.qq p a:hover{
//            color: #FFFFFF;
//            border: none;
//            background: #00E0DB;
//        }
//        .slide .info li div.img{
//            height: 100%;
//            background: #DEFFF9;
//            margin: 15px;
//        }
//        .slide .info li div.img img{
//            width: 100%;
//            height: 100%;
//        }
//        /*控制菜单的按钮*/
//        .index_cy{
//            width: 30px;
//            height: 30px;
//            background: url('/components/com_jwpagefactory/assets/img/index_cy.png');
//            position: fixed;
//            right: 0;
//            top: 50%;
//            margin-top: 140px;
//            background-position: 62px 0;
//            cursor: pointer;
//        }
//        .index_cy2{
//            width: 30px;
//            height: 30px;
//            background: url('/components/com_jwpagefactory/assets/img/index_cy.png');
//            position: fixed;
//            right: 0;
//            top: 50%;
//            margin-top: 140px;
//            background-position: 30px 0;
//            cursor: pointer;
//        }
//
//        <#}#>
//         </style>
//        <div class='box'>
//            <div class='box_img_wx'>
//                <img src='{{data.image_code}}'>
//            </div>
//            <div class='box_zx_text'>{{data.zx_text}}</div>
//            <div class='phone_zx'>
//                <img src='{$imgurl}phone_zx.png'>
//                <span>{{data.phone_font_text}}</span>
//            </div>
//            <div class='phone_wx'>
//                <img src='{$imgurl}phone_wx.png'>
//                <span>{{data.wx_font_text}}</span>
//            </div>
//            <div class='arrow'>∧</div>
//        </div>
//
//         <div class='slide'>
//	        <ul class='icon'>
//		        <li class='qq'></li>
//		        <li class='tel'></li>
//		        <li class='wx'></li>
//	        </ul>
//        </div>
//
//    ";
//
//
//        return $output;
//    }

}