<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonSwiper_item extends JwpagefactoryAddons
{

	public function render()
	{

		$settings = $this->addon->settings;
        $is_swiper_button = (isset($settings->is_swiper_button) && $settings->is_swiper_button) ? $settings->is_swiper_button : 0;
        $is_swiper_pagination = (isset($settings->is_swiper_pagination) && $settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 0;

		//Output
		$output = '<div class="swiper-box-main">';
        $output .= '<div class="swiper-container">';
        $output .= '<div class="swiper-wrapper">';
		//swiper_item Contnet
		foreach ($settings->jw_swiper_item_item as $key => $tab) {
			$output .= '<div class="swiper-slide">' . $tab->content . '</div>';
		}
		$output .= '</div>';
		$output .= '</div>';
		if($is_swiper_button == 1) {
            $output .= '<div class="swiper-button swiper-button-prev"></div><!--左箭头。如果放置在swiper-container外面，需要自定义样式。-->
            <div class="swiper-button swiper-button-next"></div><!--右箭头。如果放置在swiper-container外面，需要自定义样式。-->';
        }
        if($is_swiper_pagination == 1) {
            $output .= '<div class="swiper-pagination"></div><!--分页器。如果放置在swiper-container外面，需要自定义样式。-->';
        }
		$output .= '</div>';


		return $output;

	}

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

	public function css()
	{
		$addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;

        $is_hoverimg = (isset($settings->is_hoverimg) && $settings->is_hoverimg) ? $settings->is_hoverimg : 0;
		$hv_button_prev = (isset($settings->hv_button_prev) && $settings->hv_button_prev) ? $settings->hv_button_prev : 'https://oss.lcweb01.cn/joomla/20220530/c50b0b3cfdfe92ba84bebbaf1df690c2.png';
		$hv_button_next = (isset($settings->hv_button_next) && $settings->hv_button_next) ? $settings->hv_button_next : 'https://oss.lcweb01.cn/joomla/20220530/f2634a62cdd9d537f21e041c96a81a73.png';


        // 上翻页按钮
		$swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
		$swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;


        // 轮播点底部距离
        $swiper_pagination_bottom_md = (isset($settings->swiper_pagination_bottom) && $settings->swiper_pagination_bottom) ? $settings->swiper_pagination_bottom : '';
        $swiper_pagination_bottom_sm = (isset($settings->swiper_pagination_bottom_sm) && $settings->swiper_pagination_bottom_sm) ? $settings->swiper_pagination_bottom_sm : '';
        $swiper_pagination_bottom_xs = (isset($settings->swiper_pagination_bottom_xs) && $settings->swiper_pagination_bottom_xs) ? $settings->swiper_pagination_bottom_xs : '';
        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';

        // 动画方式
        $animate_dh = (isset($settings->animate_dh) && $settings->animate_dh) ? $settings->animate_dh : 'slide';


        $css = '
		    /* 组件盒子 */
		    ' . $addonId . ' .swiper-box-main {
		        position: relative;
		    }
		    /* 切换 配置样式 */
	        ' . $addonId . ' .swiper-button {
	            width: auto;
	            height: auto;
                top: ' . $swiper_button_top_md . '%;
                outline: none;
	        }
	        ' . $addonId . ' .swiper-button:after {
	            content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev {
	            left: ' . $swiper_button_left_md . 'px;
	        }
            
	        ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }';
            if($animate_dh == 'narrowIn') {// 轮播项自动切换的时间间隔
                $swiper_autoplay_delay = isset($settings->swiper_autoplay_delay) && $settings->swiper_autoplay_delay ? $settings->swiper_autoplay_delay : '3000';
                $css .= "
                    {$addonId} .swiper-slide {
                        overflow: hidden;
                    }
                    {$addonId} .swiper-slide .jwpf-addon-wrapper {
                        transform: scale(1.2);
                        transition: {$swiper_autoplay_delay}ms;
                    }
                    {$addonId} .swiper-slide.swiper-slide-active .jwpf-addon-wrapper {
                        transform: scale(1);
                    }
                ";
            }
            if($is_hoverimg==1){
                $css .=  $addonId . ' .swiper-button-prev:hover:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:hover:after{
                        background-image: url(' . $hv_button_prev . ');
                    }
                    ' . $addonId . ' .swiper-button-next:hover:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:hover:after {
                        background-image: url(' . $hv_button_next . ');
                    }
                    ' . $addonId . ' .swiper-button-prev, .swiper-container-rtl .swiper-button-next{background-image:url("");}
                    ' . $addonId . ' .swiper-button-next, .swiper-container-rtl .swiper-button-prev{background-image:url("");}
                ';
            }

            $css .= $addonId . ' .swiper-button-next {
	            right: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url(' . $swiper_button_next . ');
	        }
	        /*轮播点*/
	        ' . $addonId . ' .swiper-pagination {
	            width: 100%;
	            bottom: ' . $swiper_pagination_bottom_md . 'px;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet {
	            margin-right: ' .$swiper_p_margin_md . 'px;
	            width: ' . $swiper_p_width_md . 'px;
                height: ' . $swiper_p_height_md . 'px;
                background: ' . $swiper_p_color . ';
	            opacity: 1;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet-active {
	            width: ' . $swiper_p_width_a_md . 'px;
                height: ' . $swiper_p_height_a_md . 'px;
                background: ' . $swiper_p_color_a . ';
	        }
	        ' . $addonId . ' .swiper-pagination-bullet:last-child {
	            margin-right: 0px;
	        }
	        @media (min-width: 768px) and (max-width: 991px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination {
                    bottom: ' . $swiper_pagination_bottom_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_sm . 'px;
                    width: ' . $swiper_p_width_sm . 'px;
                    height: ' . $swiper_p_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_sm . 'px;
                    height: ' . $swiper_p_height_a_sm . 'px;
                }
	        }
	        @media (max-width: 767px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination {
                    bottom: ' . $swiper_pagination_bottom_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_xs . 'px;
                    width: ' . $swiper_p_width_xs . 'px;
                    height: ' . $swiper_p_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_xs . 'px;
                    height: ' . $swiper_p_height_a_xs . 'px;
                }
	        }';

		return $css;
	}

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
         //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;
        $is_xunhuan = (isset($settings->is_xunhuan) && $settings->is_xunhuan) ? $settings->is_xunhuan : 0;
        $animate_dh = (isset($settings->animate_dh) && $settings->animate_dh) ? $settings->animate_dh : 'slide';

        // 轮播项切换速度
        $swiper_speed = isset($settings->swiper_speed) && $settings->swiper_speed ? $settings->swiper_speed : '300';
        // 轮播项自动切换的时间间隔
        $swiper_autoplay_delay = isset($settings->swiper_autoplay_delay) && $settings->swiper_autoplay_delay ? $settings->swiper_autoplay_delay : '3000';

        $outplay = '';
        if($is_swiper_autoplay) {
            $outplay = '
                autoplay: {
                    delay: ' . $swiper_autoplay_delay .'
                },
            ';
        }

        if($animate_dh == 'narrowIn') {
            $animate_dh = 'fade';
        }

        $js = 'jQuery(function($){';
        $js .= '
            var swiper' . $this->addon->id .' = new Swiper(\'' . $addonId . ' .swiper-container\',{
                loop: ' . ($is_xunhuan == 1 ? 'true' : 'false') . ',
                autoHeight: true,
                speed: ' . $swiper_speed . ',
                effect: "'.$animate_dh.'",
                fadeEffect: {
                    crossFade: true,
                },
                ' . $outplay . '
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
            });';
        $js .= '})';

        return $js;
    }

	public static function getTemplate()
	{
		$output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item;
        // 是否开启切换按钮
        var is_swiper_button = data.is_swiper_button || 0;
        // 上翻页按钮
        var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png";
        // 下翻页按钮
        var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png";
        // 切换按钮宽度
        var swiper_button_width = data.swiper_button_width || 24;
        // 切换按钮高度
        var swiper_button_height = data.swiper_button_height || 24;
        // 切换按钮上边距（百分比）
        var swiper_button_top = data.swiper_button_top || 48;
        // 切换按钮两侧边距（px）
        var swiper_button_left = data.swiper_button_left || 10;
        // 是否开启轮播点
        var is_swiper_pagination = data.is_swiper_pagination || 0;
        // 轮播点底部间距
        var swiper_pagination_bottom = data.swiper_pagination_bottom || "";
        // 轮播点宽度
        var swiper_p_width = data.swiper_p_width || 8;
        // 轮播点高度
        var swiper_p_height = data.swiper_p_height || 8;
        // 轮播点间距
        var swiper_p_margin = data.swiper_p_margin || 5;
        // 轮播点颜色
        var swiper_p_color = data.swiper_p_color || "#f0f0f0";
        // 选中轮播点宽度
        var swiper_p_width_a = data.swiper_p_width_a;
        // 选中轮播点高度
        var swiper_p_height_a = data.swiper_p_height_a;
        // 选中轮播点颜色
        var swiper_p_color_a = data.swiper_p_color_a || "#007aff";
        #>
		<style type="text/css"> 
		    {{ addonId }} .swiper-box-main {
		        position: relative;
		    } 
            /* 切换 配置样式 */
	        {{addonId}} .swiper-button {
	            width: auto;
	            height: auto;
                top: {{swiper_button_top.md}}%;
                outline: none;
	        }
	        {{addonId}} .swiper-button:after {
	            content: "";
	            background: url() no-repeat center;
                background-size: cover;
                width: {{swiper_button_width.md}}px;
                height: {{swiper_button_height.md}}px;
	        }
	        {{addonId}} .swiper-button-prev,{{addonId}} .swiper-container-rtl .swiper-button-next {
	            left: {{swiper_button_left.md}}px;
	            background-image: none;
	        }
	        {{addonId}} .swiper-button-prev:after,{{addonId}} .swiper-container-rtl .swiper-button-next:after {
                background-image: url({{swiper_button_prev}});
            }
            {{addonId}} .swiper-button-next,{{addonId}} .swiper-container-rtl .swiper-button-prev {
	            right: {{swiper_button_left.md}}px;
	            background-image: none;
	        }
	        {{addonId}} .swiper-button-next:after,{{addonId}} .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url({{swiper_button_next}});
	        }
	        /*轮播点*/
	        {{addonId}} .swiper-pagination {
	            width: 100%;
	            <#if (swiper_pagination_bottom && swiper_pagination_bottom.md) {#>
	            bottom: {{swiper_pagination_bottom.md}}px;
                <#}#>
	        }
	        {{addonId}} .swiper-pagination-bullet {
	            <#if (swiper_p_margin && swiper_p_margin.md) {#>
	            margin-right: {{swiper_p_margin.md}}px;
	            <#}#>
	            <#if (swiper_p_width && swiper_p_width.md) {#>
	            width: {{swiper_p_width.md}}px;
                <#}#>
                <#if (swiper_p_height && swiper_p_height.md) {#>
	            height: {{swiper_p_height.md}}px;
	            <#}#>
	            background: {{swiper_p_color}};
	            opacity: 1;
	        }
	        {{addonId}} .swiper-pagination-bullet-active {
	            <#if (swiper_p_width_a && swiper_p_width_a.md) {#>
	            width: {{swiper_p_width_a.md}}px;
	            <#}#>
	            <#if (swiper_p_height_a && swiper_p_height_a.md) {#>
	            height: {{swiper_p_height_a.md}}px;
	            <#}#>
	            background: {{swiper_p_color_a}};
	        }
	        {{addonId}} .swiper-pagination-bullet:last-child {
	            margin-right: 0px;
	        }
            @media (min-width: 768px) and (max-width: 991px) {
                /* 切换 配置样式 */
                {{addonId}} .swiper-button {
                    top: {{swiper_button_top.sm}}%;
                }
                {{addonId}} .swiper-button:after {
                    width: {{swiper_button_width.sm}}px;
                    height: {{swiper_button_height.sm}}px;
                }
                {{addonId}} .swiper-button-prev {
                    left: {{swiper_button_left.sm}}px;
                }
                {{addonId}} .swiper-button-next {
                    right: {{swiper_button_left.sm}}px;
                }
                /*轮播点*/
                {{addonId}} .swiper-pagination {
                    <#if (swiper_pagination_bottom && swiper_pagination_bottom.sm) {#>
                    bottom: {{swiper_pagination_bottom.sm}}px;
                    <#}#>
                }
                {{addonId}} .swiper-pagination-bullet {
                    <#if (swiper_p_margin && swiper_p_margin.sm) {#>
                    margin-right: {{swiper_p_margin.sm}}px;
                    <#}#>
                    <#if (swiper_p_width && swiper_p_width.sm) {#>
                    width: {{swiper_p_width.sm}}px;
                    <#}#>
                    <#if (swiper_p_height && swiper_p_height.sm) {#>
                    height: {{swiper_p_height.sm}}px;
                    <#}#>
                }
                {{addonId}} .swiper-pagination-bullet-active {
                    <#if (swiper_p_width_a && swiper_p_width_a.sm) {#>
                    width: {{swiper_p_width_a.sm}}px;
                    <#}#>
                    <#if (swiper_p_height_a && swiper_p_height_a.sm) {#>
                    height: {{swiper_p_height_a.sm}}px;
                    <#}#>
                }
            }
            @media (max-width: 767px) {
                /* 切换 配置样式 */
                {{addonId}} .swiper-button {
                    top: {{swiper_button_top.xs}}%;
                }
                {{addonId}} .swiper-button:after {
                    width: {{swiper_button_width.xs}}px;
                    height: {{swiper_button_height.xs}}px;
                }
                {{addonId}} .swiper-button-prev {
                    left: {{swiper_button_left.xs}}px;
                }
                {{addonId}} .swiper-button-next {
                    right: {{swiper_button_left.xs}}px;
                }
                /*轮播点*/
                {{addonId}} .swiper-pagination {
                    <#if (swiper_pagination_bottom && swiper_pagination_bottom.xs) {#>
                    bottom: {{swiper_pagination_bottom.xs}}px;
                    <#}#>
                }
                {{addonId}} .swiper-pagination-bullet {
                    <#if (swiper_p_margin && swiper_p_margin.xs) {#>
                    margin-right: {{swiper_p_margin.xs}}px;
                    <#}#>
                    <#if (swiper_p_width && swiper_p_width.xs) {#>
                    width: {{swiper_p_width.xs}}px;
                    <#}#>
                    <#if (swiper_p_height && swiper_p_height.xs) {#>
                    height: {{swiper_p_height.xs}}px;
                    <#}#>
                }
                {{addonId}} .swiper-pagination-bullet-active {
                    <#if (swiper_p_width_a && swiper_p_width_a.xs) {#>
                    width: {{swiper_p_width_a.xs}}px;
                    <#}#>
                    <#if (swiper_p_height_a && swiper_p_height_a.xs) {#>
                    height: {{swiper_p_height_a.xs}}px;
                    <#}#>
                }
            }
		</style>
		<p class="alert alert-warning" style="margin-bottom: 10px;">编辑页仅为布局样式，请在预览页面中查看该插件切换效果</p>
		<div class="swiper-box-main">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <# _.each(data.jw_swiper_item_item, function(tab, key){ 
                        //console.log(tab.content);
                    #>
                    <div class="swiper-slide">
                        <#
                            var htmlContent = "";
                            _.each(tab.content, function(content){
                                htmlContent += content;
                            });
                        #>
                        {{{ htmlContent }}}
                    </div>
                    <# }); #>
                </div>
            </div> 
            <# if(is_swiper_button == 1) { #>
            <div class="swiper-button swiper-button-prev"></div><!--左箭头。如果放置在swiper-container外面，需要自定义样式。-->
            <div class="swiper-button swiper-button-next"></div><!--右箭头。如果放置在swiper-container外面，需要自定义样式。-->
            <# } #>
            <# if(is_swiper_pagination == 1) { #>
            <div class="swiper-pagination swiper-pagination-bullets">
                <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                <span class="swiper-pagination-bullet"></span>
                <span class="swiper-pagination-bullet"></span>
            </div><!--分页器。如果放置在swiper-container外面，需要自定义样式。-->
            <# } #>
        </div>
		';

		return $output;
	}

}
