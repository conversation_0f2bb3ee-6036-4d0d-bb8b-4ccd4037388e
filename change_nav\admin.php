<?php
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-25 09:04:16
 * @LastEditors: she<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-05-30 14:50:22
 * @FilePath: \Joomla-v3.9.27\components\com_jwpagefactory\addons\change_nav\assets\admin.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'jw_change_nav',
        'title' => JText::_('导航下滑变色'),
        'desc' => JText::_('导航下滑变色'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'nav_decline_normal' => array(
                    'type' => 'text',
                    'title' => JText::_('未下滑时显示的导航区块id'),
                    'std' => '',
                ),
                'section_id_decline' => array(
                    'type' => 'text',
                    'title' => JText::_('下滑后显示的导航区块id'),
                    'std' => '',
                ),
                'decline_distance' => array(
                    'type' => 'slider',
                    'title' => JText::_('下滑距离'),
                    'desc' => JText::_('下滑距离大于该值时切换导航'),
                    'std' => '0',
                    'max' => 2000,
                ),
                'gradient' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启渐变'),
                    'std' => '0',
                ),
                'gradient_time' => array(
                    'type' => 'slider',
                    'title' => JText::_('渐变时长（单位：秒）'),
                    'std' => '3',
                    'max' => 10,
                    'depends' => array(
                        array('gradient', '=', 1)
                    )
                ),
                'fixed' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('导航是否固定'),
                    'desc' => JText::_('导航区块“选择区块位置”设置为“顶端固定”或是“底部固定”时，请将该项设置为开启'),
                    'std' => '1',
                ),
                /* 'phone_open' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('移动端是否开启切换'),
                    'std' => '0',
                ), */
				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
            )
        ),
    )
);
