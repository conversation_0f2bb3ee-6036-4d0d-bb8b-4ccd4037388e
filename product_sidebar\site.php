<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonProduct_sidebar extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $zcpcatid = $_GET['zcpcatid'] ?? 1000000000000;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;

        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'all';
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : '1';
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : '0';

        //背景颜色
        $tit_col1 = (isset($settings->tit_col1) && $settings->tit_col1) ? $settings->tit_col1 : '#7ad0d0';
        //滑过背景颜色
        $over_col2 = (isset($settings->over_col2) && $settings->over_col2) ? $settings->over_col2 : '#666';
        //字体颜色
        $tit_col2 = (isset($settings->tit_col2) && $settings->tit_col2) ? $settings->tit_col2 : '#000';
        // 鼠标滑过 文字颜色
        $tit_col2_hover = (isset($settings->tit_col2_hover) && $settings->tit_col2_hover) ? $settings->tit_col2_hover : '#fff';
        //字体大小
        $text_size = (isset($settings->text_size) && $settings->text_size) ? $settings->text_size : 16;
        //列表宽度
        $list_width = (isset($settings->list_width) && $settings->list_width) ? $settings->list_width : 10;
        //列表高度
        $list_height = (isset($settings->list_height) && $settings->list_height) ? $settings->list_height : 55;
        //列表左右位置
        $list_pos_left = (isset($settings->list_pos_left) && $settings->list_pos_left) ? $settings->list_pos_left : 0;
        //列表垂直位置
        $list_pos_up = (isset($settings->list_pos_up) && $settings->list_pos_up) ? $settings->list_pos_up : 0;
        // qq
        $qq = (isset($settings->qq) && $settings->qq) ? $settings->qq : 0;

        // 关闭在线客服
        $show_qq = (isset($settings->show_qq) && $settings->show_qq) ? $settings->show_qq : 0;
        // 关闭返回顶部
        $show_top = (isset($settings->show_top) && $settings->show_top) ? $settings->show_top : 0;
        // 返回顶部图标
        $top_icon = (isset($settings->top_icon) && $settings->top_icon) ? $settings->top_icon : "https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png";
        // 鼠标滑过返回顶部图标
        $top_icon_hover = (isset($settings->top_icon_hover) && $settings->top_icon_hover) ? $settings->top_icon_hover : "https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png";

        // 锚点窗口位置
        $list_position = (isset($settings->list_position) && $settings->list_position) ? $settings->list_position : 'left_top';
        $list_position_arr = explode("_", $list_position);
        $list_position_css = $list_position_arr[0] . ':' . $list_pos_left . 'px;';
        $list_position_css .= $list_position_arr[1] . ':' . $list_pos_up . 'px;';

//        var_dump($list_width);
        $list_width_css = '';
        switch(gettype($list_width)) {
            case 'string':
                $list_width_css .= $list_width;
                break;
            case 'integer':
                $list_width_css .= $list_width .'%';
                break;
        }

        $output = '
        <style>
            ' . $addon_id . ' ul {
                padding: 0;
                margin: 0;
            }
            ' . $addon_id . ' .center {
                margin: 0 auto;
                width: ' . $list_width_css . ';
                position: fixed;
                ' . $list_position_css . '
            }
            ' . $addon_id . ' .sli {
                width: 80%;
                display: flex;
                justify-content: center;
                line-height: 21px;
                overflow: hidden;
                text-overflow: ellipsis;
                color: ' . $tit_col2 . ';
                font-size: ' . $text_size . 'px;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            ' . $addon_id . ' .sli a {
                color: ' . $tit_col2 . ';
            }
            ' . $addon_id . ' li img {
                height: 20px;
                margin: auto;
            }
            ' . $addon_id . ' li img.hover {
                display: none;
            }
            ' . $addon_id . ' li:hover {
                background-color: ' . $over_col2 . ';
            }
            ' . $addon_id . ' li:hover img.normal {
                display: none;
            }
            ' . $addon_id . ' li:hover img.hover {
                display: block;
            }
            ' . $addon_id . ' li {
                background-color: ' . $tit_col1 . ';
                margin-bottom: 1px;
                list-style-type: none;
                width: 100%;
                height: ' . $list_height . 'px;
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                transition: all ease-in-out 0.3s;
            }
            ' . $addon_id . ' li:hover .sli a {
                color: ' . $tit_col2_hover . ';
            }
        </style>
        ';

        $imgUrl = str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_sidebar/assets/images/top.png';
        $output .= '
        <div class="center">
            <ul>';
            if($show_qq !== 1){
                $output .= '<li>
                    <div class="sli">
                        <a href="http://wpa.qq.com/msgrd?v=3&uin=' . $qq . '&site=qq&menu=yes" target="_blank">在线客服</a>
                    </div>
                </li>';
            }
            foreach ($settings->ps_tab_item_2 as $k => $v) {
                $output .= '<li>
                                <div class="sli">
                                    <a href="#' . $v->text8 . '">' . $v->title . '</a>
                                </div> 
                            </li>';
            }
            if($show_top !== 1){
                $output .= '<li id="test">
                                <div class="sli">
                                    <img src="' . $top_icon . '" alt="" class="normal">
                                    <img src="' . $top_icon_hover . '" alt="" class="hover">
                                    <a>顶部</a>
                                </div>
                            </li>';
            }
        $output .= '</ul>
        </div>
        ';

        return $output;
    }

    public function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $js = 'jQuery(function($){
            var test = $("' . $addonId . ' #test")
            test = test[0]
            test.onclick = function(){//回到最顶部
                var currentPosition, timer;
                var speed = 10;
                timer = setInterval(function(){
                    currentPosition = document.documentElement.scrollTop || document.body.scrollTop;
                    currentPosition -= speed; //speed变量
                    if(currentPosition > 0){
                        window.scrollTo(0, currentPosition);
                    }else{
                        window.scrollTo(0, 0);
                        clearInterval(timer);
                    }
                }, 1);
            }
        })';
        return $js;
    }

    public static function getTemplate()
    {

        $imgUrl = str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/product_sidebar/assets/images/top.png';
        $output = '
            <#
                var addonId = "#jwpf-addon-" + data.id;
                // 锚点窗口位置
                var list_position = data.list_position || "left_top";
                // 关闭在线客服
                var show_qq = data.show_qq || 0;
                // 关闭返回顶部
                var show_top = data.show_top || 0;
                // 返回顶部图标
                var top_icon = data.top_icon || "https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png";
                // 鼠标滑过返回顶部图标
                var top_icon_hover = data.top_icon_hover || "https://oss.lcweb01.cn/joomla/20210914/444d283b938e47ec7f96a931f36dc14b.png";
                // 鼠标滑过 文字颜色
                var tit_col2_hover = data.tit_col2_hover;
                // 锚点窗口宽度 单位
                let list_width_unit = typeof data.list_width !== "undefined" && typeof data.list_width.unit !== "undefined" ? data.list_width.unit : "%";
                // 锚点窗口宽度
                let list_width_md = typeof data.list_width !== "undefined" && typeof data.list_width.md !== "undefined" ? data.list_width.md : "10";
                
                //  锚点窗口位置处理
                var list_position_arr = list_position.split("_");
                var list_position_css = "";
                list_position_css += list_position_arr[0] + ":" + data.list_pos_left + "px;";
                list_position_css += list_position_arr[1] + ":" + data.list_pos_up + "px;";
            #>
            <style>
                {{ addonId }} ul {
                    padding: 0;
                    margin: 0;
                }
                {{ addonId }} .center {
                    margin: 0 auto;
                    width: {{list_width_md}}{{list_width_unit}};
                    /*margin-top: 50px;*/
                    position: fixed;
                    {{ list_position_css }}
                }
                {{ addonId }} .sli {
                    width: 80%;
                    display: flex;
                    justify-content: center;
                    line-height: 21px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: {{data.tit_col2}};
                    font-size: {{data.text_size}}px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
                {{ addonId }} .sli a {
                    color:{{data.tit_col2}};
                }
                {{ addonId }} li img {
                    height: 20px;
                    margin: auto;
                }
                {{ addonId }} li img.hover {
                    display: none;
                }
                {{ addonId }} li:hover {
                    background-color: {{data.over_col2}};
                }
                {{ addonId }} li:hover img.normal {
                    display: none;
                }
                {{ addonId }} li:hover img.hover {
                    display: block;
                }
                {{ addonId }} li {
                    background-color: {{data.tit_col1}};
                    margin-bottom: 1px;
                    list-style-type: none;
                    width: 100%;
                    height: {{data.list_height}}px;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    transition: all ease-in-out 0.3s;
                }
                {{ addonId }} li:hover .sli a {
                    color: {{ tit_col2_hover }};
                }
            </style>
            <div style=" height:100px">此区域为锚点侧边栏占位，不影响预览页展示</div>
            <div class="center">
                <ul>
                    <# if(show_qq !== 1) { #>
                    <li>
                        <div class="sli">
                            <a href="http://wpa.qq.com/msgrd?v=3&amp;uin={{data.qq}}&amp;site=qq&amp;menu=yes">在线客服</a>
                        </div>
                    </li>
                    <# } #>
                    <# _.each(data.ps_tab_item_2, function(accordion_item, key){ #>
                        <li>
                            <div class="sli">
                                <a href="#{{accordion_item.text8}}">{{accordion_item.title}}</a>
                            </div>
                        </li>
                    <# }); #>
                    <# if(show_top !== 1) { #>
                    <li id="test">
                        <div class="sli">
                            <img src=\'{{ top_icon }}\' alt="" class="normal">
                            <img src=\'{{ top_icon_hover }}\' alt="" class="hover">
                            <a>顶部</a>
                       </div>
                    </li>
                    <# } #>
                </ul>
            </div>
        ';
        return $output;
    }
}