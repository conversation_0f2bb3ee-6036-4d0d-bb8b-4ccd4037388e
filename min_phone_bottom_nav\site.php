<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonMin_phone_bottom_nav extends JwpagefactoryAddons
{
    // 渲染html
    public function addItem($image, $title, $btn_link, $btn_link_from, $btn_link_, $index) {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $html = "";
        $className = "";
        $link = "javascript:;";
        $a_html = "";
        if ($index == 3) {
            $className = " min_phone_bottom_nav_item3";
        }
        if($btn_link_from == 1) {
            $link = '';
            if($btn_link_){
                $id = base64_encode($btn_link_);
                $link .= '/index.php/component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
            }
        } else {
            if (strstr($btn_link,'wx')) {
                $link .= "' onclick='openWx()";
                $a_html .='<input readOnly="true" class="input_" id="biao1" value="'.substr($btn_link,3).'"/>';
                $a_html .='<div id="biaoios" class="div_">'.substr($btn_link,3).'</div>';
            }  elseif (strstr($btn_link, 'qq')) {
                $link .= "' onclick='openQq()";
                $a_html .='<input readOnly="true" class="input_" id="biao2" value="'.substr($btn_link,3).'"/>';
                $a_html .='<div id="biaoios1" class="div_">'.substr($btn_link,3).'</div>';
            } else {
                $link = $btn_link;
            }
        }

        $html .= "<a class='min_phone_bottom_nav_item" . $className . "' href='" . $link . "'>";
        $html .= $a_html;
        $html .= "<img src='" . $image . "' alt=''>";
        $html .= "<div>" . $title . "</div>";
        $html .= "</a>";
        return $html;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $showBtnCenter = (isset($settings->showBtnCenter) && $settings->showBtnCenter) ? $settings->showBtnCenter : false; 
        $bg_color = (isset($settings->bg_color) && $settings->bg_color) ? $settings->bg_color : '#000';
        $bg_image = (isset($settings->bg_image) && $settings->bg_image) ? $settings->bg_image : '';
        $font_color = (isset($settings->font_color) && $settings->font_color) ? $settings->font_color : '';
        $font_color_center = (isset($settings->font_color_center) && $settings->font_color_center) ? $settings->font_color_center : '';
        $font_bgcolor_center = (isset($settings->font_bgcolor_center) && $settings->font_bgcolor_center) ? $settings->font_bgcolor_center : '';
        $btn1link = (isset($settings->btn1link) && $settings->btn1link) ? $settings->btn1link : '';
        $btn2link = (isset($settings->btn2link) && $settings->btn2link) ? $settings->btn2link : '';
        $btncenterlink = (isset($settings->btncenterlink) && $settings->btncenterlink) ? $settings->btncenterlink : '';
        $btn4link = (isset($settings->btn4link) && $settings->btn4link) ? $settings->btn4link : '';
        $btn5link = (isset($settings->btn5link) && $settings->btn5link) ? $settings->btn5link : '';
        $btn1_image = (isset($settings->btn1_image) && $settings->btn1_image) ? $settings->btn1_image : '';
        $btn2_image = (isset($settings->btn2_image) && $settings->btn2_image) ? $settings->btn2_image : '';
        $btn3_image = (isset($settings->btn3_image) && $settings->btn3_image) ? $settings->btn3_image : '';
        $btn4_image = (isset($settings->btn4_image) && $settings->btn4_image) ? $settings->btn4_image : '';
        $btn5_image = (isset($settings->btn5_image) && $settings->btn5_image) ? $settings->btn5_image : '';
        $btn1font = (isset($settings->btn1font) && $settings->btn1font) ? $settings->btn1font : '产品';
        $btn2font = (isset($settings->btn2font) && $settings->btn2font) ? $settings->btn2font : '案例';
        $btncenterfont = (isset($settings->btncenterfont) && $settings->btncenterfont) ? $settings->btncenterfont : '首页';
        $btn4font = (isset($settings->btn4font) && $settings->btn4font) ? $settings->btn4font : '地图';
        $btn5font = (isset($settings->btn5font) && $settings->btn5font) ? $settings->btn5font : '电话';
        $center_btn_w = (isset($settings->center_btn_w) && $settings->center_btn_w) ? $settings->center_btn_w : 80; //中间导航宽度
        $center_btn_h = (isset($settings->center_btn_h) && $settings->center_btn_h) ? $settings->center_btn_h : 80; //中间导航高度

        $font_size = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '16';
        $font_padding = (isset($settings->font_padding) && $settings->font_padding) ? $settings->font_padding : '10px 10px 10px 10px';
        $img_width = (isset($settings->img_width)) ? $settings->img_width : '30';
        $img_height = (isset($settings->img_height)) ? $settings->img_height : '30';

        $btn_item_custom = isset($settings->btn_item_custom) ? $settings->btn_item_custom : 0;
        $showBtn1 = 1;
        $showBtn2 = 1;
        $showBtn4 = 1;
        $showBtn5 = 1;
        if($btn_item_custom == 1) {
            $showBtn1 = isset($settings->showBtn1) ? $settings->showBtn1 : null;
            $showBtn2 = isset($settings->showBtn2) ? $settings->showBtn2 : null;
            $showBtn4 = isset($settings->showBtn4) ? $settings->showBtn4 : null;
            $showBtn5 = isset($settings->showBtn5) ? $settings->showBtn5 : null;
        }
        $btn1link_from = isset($settings->btn1link_from) ? $settings->btn1link_from : 0;
        $btn2link_from = isset($settings->btn2link_from) ? $settings->btn2link_from : 0;
        $btn3link_from = isset($settings->btn3link_from) ? $settings->btn3link_from : 0;
        $btn4link_from = isset($settings->btn4link_from) ? $settings->btn4link_from : 0;
        $btn5link_from = isset($settings->btn5link_from) ? $settings->btn5link_from : 0;

        $btn1link_ = isset($settings->btn1link_) ? $settings->btn1link_ : null;
        $btn2link_ = isset($settings->btn2link_) ? $settings->btn2link_ : null;
        $btn3link_ = isset($settings->btn3link_) ? $settings->btn3link_ : null;
        $btn4link_ = isset($settings->btn4link_) ? $settings->btn4link_ : null;
        $btn5link_ = isset($settings->btn5link_) ? $settings->btn5link_ : null;


        $output = '';
        $output .= "<style>
        ${addon_id} .min_phone_bottom_nav {
            display: none;
            width: 100%;
            justify-content: space-around;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 999999999999999999999999999;
            background-color:${bg_color};
            background-image: url(${bg_image});
            background-repeat:no-rapeat;
            background-size:100% 100%;
            box-sizing: border-box;
            padding: 5px;
        }
        ${addon_id} .min_phone_bottom_nav_item .input_ {
            outline: none;
            border: 0px;
            color: rgba(0, 0, 0, 0.0);
            position: absolute;
            left: -200px;
            background-color: transparent;
        }
        ${addon_id} .min_phone_bottom_nav_item .div_ {
            position: absolute;
            left: -200px;
            color: rgba(0, 0, 0, 0);
            background-color: transparent;
        }

        ${addon_id} .min_phone_bottom_nav_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            flex: 1;
            ";
            if($font_padding){
                $output .= "padding:{$font_padding};";
            }else{
                $output .= "padding: 10px;";
            }
        $output .= "}



        ${addon_id} .min_phone_bottom_nav_item img {
            margin-bottom: 5px;
            ";
            if($img_width!=''){
                $output .= "width:{$img_width}px;";
            }else{
                $output .= "width: 30px;";
            }
            if($img_height!=''){
                $output .= "height:{$img_height}px;";
            }else{
                $output .= "height: 30px;";
            }
        $output .= "
        }
        ${addon_id} .min_phone_bottom_nav_item div {
            color:${font_color};
            ";
            if($font_size){
                $output .= "font-size:{$font_size}px;";
            }else{
                $output .= "font-size: 16px;";
            }
        $output .= "}
        ${addon_id} .min_phone_bottom_nav_item3 {
            display:flex;
            border-radius: 50%;
            background-color: ${font_bgcolor_center};
            width: {$center_btn_w}px;
            height: {$center_btn_h}px;
            flex: none;
        }
        ${addon_id} .min_phone_bottom_nav_item3 div{
            color:${font_color_center};
        }

        @media (max-width:768px) {
            ${addon_id} .min_phone_bottom_nav {
                display: flex;
            }
        }
        </style>";

        $output .= "<div class='min_phone_bottom_nav'>";
        if($showBtn1 == 1) {
            $output .= $this->addItem($btn1_image, $btn1font, $btn1link, $btn1link_from, $btn1link_ , 1);
        }
        if($showBtn2 == 1) {
            $output .= $this->addItem($btn2_image, $btn2font, $btn2link, $btn2link_from, $btn2link_ , 2);
        }
        if($showBtnCenter) {
            $output .= $this->addItem($btn3_image, $btncenterfont, $btncenterlink, $btn3link_from, $btn3link_ , 3);
        }
        if($showBtn4 == 1) {
            $output .= $this->addItem($btn4_image, $btn4font, $btn4link, $btn4link_from, $btn4link_ , 4);
        }
        if($showBtn5 == 1) {
            $output .= $this->addItem($btn5_image, $btn5font, $btn5link, $btn5link_from, $btn5link_ , 5);
        }

        $output .="  </div>";
//        $output.='<script src="/components/com_jwpagefactory/addons/tal_button/assets/js/jquery.min.js"></script>';
        $output.='<script>
         var browser = {
          versions: function () {
              var u = navigator.userAgent,
                      app = navigator.appVersion;
              return {
                  trident: u.indexOf(\'Trident\') > -1, /*IE内核*/
                  presto: u.indexOf(\'Presto\') > -1, /*opera内核*/
                  webKit: u.indexOf(\'AppleWebKit\') > -1, /*苹果、谷歌内核*/
                  gecko: u.indexOf(\'Gecko\') > -1 && u.indexOf(\'KHTML\') == -1, /*火狐内核*/
                  mobile: !!u.match(/AppleWebKit.*Mobile.*/), /*是否为移动终端*/
                  ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), /*ios终端*/
                  android: u.indexOf(\'Android\') > -1 || u.indexOf(\'Linux\') > -1, /*android终端或者uc浏览器*/
                  iPhone: u.indexOf(\'iPhone\') > -1, /*是否为iPhone或者QQHD浏览器*/
                  iPad: u.indexOf(\'iPad\') > -1, /*是否iPad*/
                  webApp: u.indexOf(\'Safari\') == -1, /*是否web应该程序，没有头部与底部*/
                  souyue: u.indexOf(\'souyue\') > -1,
                  superapp: u.indexOf(\'superapp\') > -1,
                  weixin: u.toLowerCase().indexOf(\'micromessenger\') > -1,
                  Safari: u.indexOf(\'Safari\') > -1
              };
          }(),
          language: (navigator.browserLanguage || navigator.language).toLowerCase()
      };
  
         function openWx(url){
            if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
                var text = document.getElementById(\'biaoios\');
                //获取隐藏的input，并写入text内容，在进行复制
                var input = document.getElementById("biao1");
                input.value = text.innerHTML;
                input.select();
                input.setSelectionRange(0, input.value.length);   //兼容ios
                document.execCommand("Copy");
                input.blur();
            }else{
                var Url2=document.getElementById("biao1");//要复制文字的节点
                Url2.select(); // 选择对象
                //document.execCommand("Copy"); // 执行浏览器复制命令
                $("#biao1").blur();
                if(document.execCommand(\'copy\', false, null)){
                  var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
                }
            }
            jQuery(" .white_content").fadeIn(500);
            window.location.href = "weixin://";
        }

        function openQq(url){
            if (navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)) {//区分iPhone设备
                var text = document.getElementById(\'biaoios1\');
                //获取隐藏的input，并写入text内容，在进行复制
                var input = document.getElementById("biao2");
                input.value = text.innerHTML;
                input.select();
                input.setSelectionRange(0, input.value.length);   //兼容ios
                document.execCommand("Copy");
                input.blur();
            }else{
                var Url2=document.getElementById("biao2");//要复制文字的节点
                Url2.select(); // 选择对象
                //document.execCommand("Copy"); // 执行浏览器复制命令
                $("#biao2").blur();
                if(document.execCommand(\'copy\', false, null)){
                  var successful = document.execCommand(\'copy\');// 执行浏览器复制命令
                }
            }
            jQuery(" .white_content").fadeIn(500);
            window.location.href = "http://wpa.qq.com/msgrd?v=3&uin=1169296508&site=qq&menu=yes";
        }
           
        </script>
        ';
        return $output;
    }

    public function css()
    {
    }


    public static function getTemplate()
    {
        $output = '
        <#
            var addonId = "#jwpf-addon-"+data.id;
            var bg_color = (!_.isEmpty(data.bg_color) && data.bg_color) ? data.bg_color : "#000";
            var bg_image = (!_.isEmpty(data.bg_image) && data.bg_image) ? data.bg_image : "";
            var font_color = (!_.isEmpty(data.font_color) && data.font_color) ? data.font_color : "";
            var font_color_center = (!_.isEmpty(data.font_color_center) && data.font_color_center) ? data.font_color_center : "";
            var font_bgcolor_center = (!_.isEmpty(data.font_bgcolor_center) && data.font_bgcolor_center) ? data.font_bgcolor_center : "";
            var btn1_image = (!_.isEmpty(data.btn1_image) && data.btn1_image) ? data.btn1_image : "";
            var btn2_image = (!_.isEmpty(data.btn2_image) && data.btn2_image) ? data.btn2_image : "";
            var btn3_image = (!_.isEmpty(data.btn3_image) && data.btn3_image) ? data.btn3_image : "";
            var btn4_image = (!_.isEmpty(data.btn4_image) && data.btn4_image) ? data.btn4_image : "";
            var btn5_image = (!_.isEmpty(data.btn5_image) && data.btn5_image) ? data.btn5_image : "";
            var showBtnCenter =  (!_.isEmpty(data.showBtnCenter) && data.showBtnCenter) ? data.showBtnCenter : false;
            var btn1font =  (!_.isEmpty(data.btn1font) && data.btn1font) ? data.btn1font : "产品";
            var btn2font =  (!_.isEmpty(data.btn2font) && data.btn2font) ? data.btn2font : "案例";
            var btncenterfont =  (!_.isEmpty(data.btncenterfont) && data.btncenterfont) ? data.btncenterfont : "首页";
            var btn4font =  (!_.isEmpty(data.btn4font) && data.btn4font) ? data.btn4font : "地图";
            var btn5font =  (!_.isEmpty(data.btn5font) && data.btn5font) ? data.btn5font : "电话";
            var center_btn_w =  (!_.isEmpty(data.center_btn_w) && data.center_btn_w) ? data.center_btn_w : 80;
            var center_btn_h =  (!_.isEmpty(data.center_btn_h) && data.center_btn_h) ? data.center_btn_h : 80;
            var font_size = (!_.isEmpty(data.font_size) && data.font_size) ? data.font_size : "16";
            var font_padding = (!_.isEmpty(data.font_padding) && data.font_padding) ? data.font_padding : "10px 10px 10px 10px";
            var img_width = (!_.isEmpty(data.img_width) && data.img_width) ? data.img_width : "30";
            var img_height = (!_.isEmpty(data.img_height) && data.img_height) ? data.img_height : "30";
            
            var btn_item_custom = data.btn_item_custom
            var showBtn1 = 1
            var showBtn2 = 1
            var showBtn4 = 1
            var showBtn5 = 1
            if(btn_item_custom == 1) {
                showBtn1 = data.showBtn1
                showBtn2 = data.showBtn2
                showBtn4 = data.showBtn4
                showBtn5 = data.showBtn5
            }
        #>
        <style>
            {{addonId}} .min_phone_bottom_nav {
                display: none;
                width: 100%;
                position: fixed;
                bottom: 0;
                left: 0;
                z-index: 999999999999999999999999999;
                justify-content: space-around;
                background-color:{{bg_color}};
                background-image: url({{bg_image}});
                background-repeat:no-rapeat;
                background-size:100% 100%;
                box-sizing:border-box;
                padding: 5px;
            }

            {{addonId}} .min_phone_bottom_nav_item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-decoration: none;
                flex: 1;
                
                <# if(font_padding){ #>
                    padding: {{font_padding}};
                <# }else{ #>
                    padding: 10px;
                <# } #>
                 
            }
            {{addonId}} .min_phone_bottom_nav_item img {
                margin-bottom: 5px;
                <# if(img_width){ #>
                    width: {{img_width}}px;
                <# }else{ #>
                    width: 30px;
                <# } #>
                
                <# if(img_height){ #>
                    height: {{img_height}}px;
                <# }else{ #>
                    height: 30px;
                <# } #>

            } 
            {{addonId}} .min_phone_bottom_nav_item div {
                color:{{font_color}};
                <# if(font_size){ #>
                    font-size: {{font_size}}px;
                <# } #>
            }

            {{addonId}} .min_phone_bottom_nav_item3 {
                display:flex;
                border-radius: 50%;
                background-color: {{font_bgcolor_center}};
                width: {{center_btn_w}}px;
                height: {{center_btn_h}}px;
                flex: none;
            }
            {{addonId}} .min_phone_bottom_nav_item3 div{
                color:{{font_color_center}};
            }

            @media (max-width:768px) {
                {{addonId}} .min_phone_bottom_nav {
                    display: flex;
                }
            }
 
        </style>
        <#                 
            function addItem(image, title, index){
                var html = "",
                    className = "";
                if (index == 3) {
                    className = " min_phone_bottom_nav_item3";
                }
                html += "<a class=\'min_phone_bottom_nav_item" + className + "\' href=\'#\'>";
                html += "<img src=\'" + image + "\' alt=\'\'>";
                html += "<div>" + title + "</div>";
                html += "</a>";
                return html;
            }
        #>
        <div class="min_phone_bottom_nav">
            <# if(showBtn1 == 1){ #>
                {{{ addItem(btn1_image, btn1font, 1) }}}
            <# } #>
            <# if(showBtn2 == 1){ #>
                {{{ addItem(btn2_image, btn2font, 2) }}}
            <# } #>
            <# if(data.showBtnCenter){ #>
                {{{ addItem(btn3_image, btncenterfont, 3) }}}
            <# } #>
            <# if(showBtn4 == 1){ #>
                {{{ addItem(btn4_image, btn4font, 4) }}}
            <# } #>
            <# if(showBtn5 == 1){ #>
                {{{ addItem(btn5_image, btn5font, 5) }}}
            <# } #>
        </div>
        <div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,不影响预览页面结构</div>
        </div>
        ';
        return $output;
    }
}
