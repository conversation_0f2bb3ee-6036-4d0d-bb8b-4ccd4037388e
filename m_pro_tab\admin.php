<?php

// defined('_JEXEC') or die ('resticted aceess');

// $app = JFactory::getApplication();

// $input = $app->input;
// $layout_id = $input->get('layout_id', '');
// $site_id = $input->get('site_id', 0);
// $company_id = $input->get('company_id', 0);

// JwAddonsConfig::addonConfig(
//     array(
//         'type' => 'content',
//         'addon_name' => 'm_pro_tab',
//         'title' => JText::_('产品列表1'),
//         'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
//         'category' => '常用插件',
//         'attr' => array(
//             'general' => array(
//                 'admin_label' => array(
//                     'type' => 'text',
//                     'title' => JText::_('左侧标题内容'),
//                     'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
//                     'std' => '产品分类'
//                 ),
//                 'bg' => array(
// 					'type' => 'buttons',
// 					'title' => '左侧标题设置',
// 					'std' => 'color',
// 					'values' => array(
// 						array(
// 							'label' => '背景颜色',
// 							'value' => 'color'
// 						),
// 						array(
// 							'label' => '背景图片',
// 							'value' => 'img'
// 						),
// 					),
// 					'tabs' => true,
// 				),
//                 'bg_color' => array(
// 					'type' => 'color',
// 					'title' => '标题背景颜色',
// 					'std' => '#000',
//                     'depends' => array(
//                         array('bg', '=', 'color'),
//                     )
// 				),
//                 'left_title_color' => array(
// 					'type' => 'color',
// 					'title' => '标题字体颜色',
// 					'std' => '#fff',
//                     'depends' => array(
//                         array('bg', '=', 'color'),
//                     )
// 				),
//                 'left_title_fontSize' => array(
// 					'type' => 'slider',
// 					'title' => '标题字体大小',
// 					'std' => '18',
//                     'depends' => array(
//                         array('bg', '=', 'color'),
//                     )
// 				),
//                 'bg_image' => array(
// 					'type' => 'media',
// 					'title' => '标题背景图片',
// 					'std' => '/components/com_jwpagefactory/addons/m_pro_tab/assets/titlebg.jpg',
//                     'depends' => array(
//                         array('bg', '=', 'img'),
//                     )
// 				),
//                 'leftTab' => array(
// 					'type' => 'buttons',
// 					'title' => '左侧tab设置',
// 					'std' => 'normal',
// 					'values' => array(
// 						array(
// 							'label' => '正常',
// 							'value' => 'normal'
// 						),
// 						array(
// 							'label' => '移入',
// 							'value' => 'hover'
// 						),
// 					),
// 					'tabs' => true,
// 				),
//                 'left_tab_bgcolor' => array(
// 					'type' => 'color',
// 					'title' => '左侧tab正常背景颜色',
// 					'std' => '#dfdfdf',
//                     'depends' => array(
//                         array('leftTab', '=', 'normal'),
//                     )
// 				),
//                 'left_tab_bgcolor_hover' => array(
// 					'type' => 'color',
// 					'title' => '左侧tab移入背景颜色',
// 					'std' => '#dfdfdf',
//                     'depends' => array(
//                         array('leftTab', '=', 'hover'),
//                     )
// 				),
//                 'left_tab_fontcolor' => array(
// 					'type' => 'color',
// 					'title' => '左侧tab正常字体颜色',
// 					'std' => '#000',
//                     'depends' => array(
//                         array('leftTab', '=', 'normal'),
//                     )
// 				),
//                 'left_tab_fontcolor_hover' => array(
// 					'type' => 'color',
// 					'title' => '左侧tab移入字体颜色',
// 					'std' => '#fff',
//                     'depends' => array(
//                         array('leftTab', '=', 'hover'),
//                     )
// 				),
//                 'rightCon' => array(
// 					'type' => 'buttons',
// 					'title' => '右侧内容设置',
// 					'std' => 'normal',
// 					'values' => array(
// 						array(
// 							'label' => '正常',
// 							'value' => 'normal'
// 						),
// 						array(
// 							'label' => '移入',
// 							'value' => 'hover'
// 						),
// 					),
// 					'tabs' => true,
// 				),
//                 'right_con_fontSize' => array(
// 					'type' => 'slider',
// 					'title' => '右侧标题字体大小',
// 					'std' => '16',
//                     'depends' => array(
//                         array('rightCon', '=', 'normal'),
//                     )
// 				),
//                 'right_con_bgcolor' => array(
// 					'type' => 'color',
// 					'title' => '右侧标题正常背景颜色',
// 					'std' => '#e1e1e1',
//                     'depends' => array(
//                         array('rightCon', '=', 'normal'),
//                     )
// 				),
//                 'right_con_bgcolor_hover' => array(
// 					'type' => 'color',
// 					'title' => '右侧标题移入背景颜色',
// 					'std' => '#0a53b8',
//                     'depends' => array(
//                         array('rightCon', '=', 'hover'),
//                     )
// 				),
//                 'right_con_bordercolor' => array(
// 					'type' => 'color',
// 					'title' => '右侧边框颜色',
// 					'std' => '#e1e1e1',
//                     'depends' => array(
//                         array('rightCon', '=', 'normal'),
//                     )
// 				),
//                 'right_con_bordercolor_hover' => array(
// 					'type' => 'color',
// 					'title' => '右侧边框移入颜色',
// 					'std' => '#0a53b8',
//                     'depends' => array(
//                         array('rightCon', '=', 'hover'),
//                     )
// 				),
//                 'right_con_fontcolor' => array(
// 					'type' => 'color',
// 					'title' => '右侧标题正常字体颜色',
// 					'std' => '#000',
//                     'depends' => array(
//                         array('rightCon', '=', 'normal'),
//                     )
// 				),
//                 'right_con_fontcolor_hover' => array(
// 					'type' => 'color',
// 					'title' => '右侧标题移入字体颜色',
// 					'std' => '#fff',
//                     'depends' => array(
//                         array('rightCon', '=', 'hover'),
//                     )
// 				),
//             ),
//         ),
//     )
// );
