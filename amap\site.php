<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonAmap extends JwpagefactoryAddons
{

	public function render()
	{

		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

		//Options
        //地图展示形式
        $map_type = (isset($settings->map_type) && $settings->map_type) ? $settings->map_type : 'type01';

        $map = (isset($settings->map) && $settings->map) ? $settings->map : '';
		//新增两项设置：经度map_lng和纬度map_lat
		$map_lng = (isset($settings->map_lng) && $settings->map_lng) ? $settings->map_lng : '';
		$map_lat = (isset($settings->map_lat) && $settings->map_lat) ? $settings->map_lat : '';

		$marker = (isset($settings->marker) && $settings->marker) ? $settings->marker : '';
		$infowindow = (isset($settings->infowindow) && $settings->infowindow) ? $settings->infowindow : '';

		// 2021.09.17 默认是否显示信息窗
        $show_infowindow = (isset($settings->show_infowindow) && $settings->show_infowindow) ? $settings->show_infowindow : 0;

		$lang = (isset($settings->lang) && $settings->lang) ? $settings->lang : '';
		$style = (isset($settings->style) && $settings->style) ? $settings->style : '';
		$type = (isset($settings->type) && $settings->type) ? $settings->type : '';
		$zoom = (isset($settings->zoom) && $settings->zoom) ? $settings->zoom : '';
		$mousescroll = (isset($settings->mousescroll) && $settings->mousescroll) ? $settings->mousescroll : '';


		$duotu_location = (isset($settings->duotu_location) && $settings->duotu_location) ? $settings->duotu_location : 0;
		$libg_color = (isset($settings->libg_color) && $settings->libg_color) ? $settings->libg_color : '#F8F8F8';
		$litit_color = (isset($settings->litit_color) && $settings->litit_color) ? $settings->litit_color : '#265FAA';
		$lixq_color = (isset($settings->lixq_color) && $settings->lixq_color) ? $settings->lixq_color : '#9A9A9A';
		$pc_table = (isset($settings->pc_table) && $settings->pc_table) ? $settings->pc_table : 'wz1';
		$sj_table = (isset($settings->sj_table) && $settings->sj_table) ? $settings->sj_table : 'wza1';

		$multi_location = (isset($settings->multi_location) && $settings->multi_location) ? $settings->multi_location : 0;

		if($multi_location == 1) {
            $show_infowindow = 0;
        }

		$location_addr = [];
		if (isset($settings->multi_location_items) && $multi_location !== 0) {
			foreach ($settings->multi_location_items as $key => $item) {
			//				$lat_long = explode(',', $item->location_item);
				$location_addr[] = array(
					'address' => $item->location_popup_text,
					'longitude' => $item->longitude,
					'latitude' => $item->latitude,
					'icon' => $item->location_marker
				);
			}
		}
		$location_json = json_encode($location_addr);
        $output = '';
		//修改判断：如果自定义经纬度，则使用经纬度，如未设置或者未全部录入，则使用默认北京坐标
		if ($map || ($map_lng != '' && $map_lat != '')) {
			if ($map_lng != '' && $map_lat != '') {
				$map = explode(',', $map_lng . ',' . $map_lat);
			} else {
				$map = explode(',', $map);
			}
			if($map_type == 'type01'){
                $output .= '<div id="jwpf-addon-map-' . $this->addon->id . '" class="jwpf-addon jwpf-addon-amap ' . $class . '">';
                $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
                $output .= '<div class="jwpf-addon-content">';
                $output .= '<div id="jwpf-addon-amap-' . $this->addon->id . '" class="jwpf-addon-amap-canvas" data-showinfo="' . $show_infowindow . '" data-lng="' . trim($map[0]) . '" data-lat="' . trim($map[1]) . '" data-location=\'' . base64_encode($location_json) . '\' data-maplang="' . $lang . '" data-mapstyle="' . $style . '" data-maptype="' . $type . '" data-mapzoom="' . $zoom . '" data-mousescroll="' . $mousescroll . '" data-marker="' . $marker . '" data-infowindow="' . base64_encode($infowindow) . '"></div>';
                $output .= '</div>';
                $output .= '</div>';
            }
			if($map_type == 'type02' || $map_type == 'type03'){

                if($map_type=='type02' && $duotu_location==1){

                	if($settings->duotu_location_items && $duotu_location == 1){
	            		foreach ($settings->duotu_location_items as $keys1 => $item) {
	            			if($keys1==0){
	            				//经度
								$map_lng = $item->longitude ? $item->longitude : "116.397464";
								//纬度
								$map_lat = $item->latitude ? $item->latitude :  "39.908696";
								// type02 标点位置名称
								$map_name = $item->title ? $item->title : "北京市天安门";
								$map_coordinate = $item->zbtype ? $item->zbtype : "gaode";
	            			}
	            		}


		                $pc_link = '';
		                $mobile_link = '';

		                $pc_link .= '//uri.amap.com/marker?position=' . trim($map_lng) . ',' . trim($map_lat) . '&name=' . $map_name;

		                $pc_link .= '&src=建站通&coordinate=' . $map_coordinate . '&callnative=0';
		                $mobile_link .= $pc_link . "&platform=mobile";

		                $output .= '<script>
							jQuery(function($){

								$("'.$addon_id .' .gdmap_ul li").eq(0).addClass("active");
								var pc_link = "";
		                		var mobile_link = "";

								$("'.$addon_id .' .gdmap_ul li").click(function(){
									$("'.$addon_id .' .gdmap_ul li").removeClass("active");
									$(this).addClass("active");
									var map_lng=$(this).attr("lng");
									var map_lat=$(this).attr("lat");
									var map_name=$(this).attr("title");
									var map_coordinate=$(this).attr("map_coordinate");
									map_lng= map_lng.trim();
									map_lat= map_lat.trim();

									pc_link = "//uri.amap.com/marker?position=" +map_lng+ "," +map_lat+ "&name="+map_name;
					                pc_link += "&src=建站通&coordinate="+map_coordinate+"&callnative=0";

					                mobile_link = pc_link+"&platform=mobile";
					                $("'.$addon_id .' .pc-map").attr("src",pc_link);
					                $("'.$addon_id .' .mobile-map").attr("src",mobile_link);
								})
							})
	            		</script>';

	            	}

				    $output .= '
						<div class="gd_map">';


								if($pc_table=="wz1"){
									if($sj_table=="wza1"){
										$output .= '<div class="gd_map_left fl">
				                    		<iframe class="map-iframe pc-map" src="' . $pc_link . '" width="100%" height="320"></iframe>
											<iframe class="map-iframe mobile-map" src="' . $mobile_link . '" width="100%" height="320"></iframe>
								        </div>';
									 }else{
										$output .= '<div class="gd_map_left fl">
				                    		<iframe class="map-iframe pc-map" src="' . $pc_link . '" width="100%" height="320"></iframe>
								        </div>';
									 }
								}


							if($pc_table=="wz2" && $sj_table=="wza1"){
								$output .= '<div class="gd_map_left fl sj">
									<iframe class="map-iframe mobile-map" src="' . $mobile_link . '" width="100%" height="320"></iframe>
						        </div>';
							}

					        $output .= '<div class="gd_map_right fl">
					            <ul class="gdmap_ul">';
					            	if($settings->duotu_location_items && $duotu_location == 1){
					            		foreach ($settings->duotu_location_items as $keys => $item) {

											//经度
											$map_lng = $item->longitude ? $item->longitude : "116.397464";

											//纬度
											$map_lat = $item->latitude ? $item->latitude :  "39.908696";
											// type02 标点位置名称
											$map_name = $item->title ? $item->title : "北京市天安门";
											$zbtype = $item->zbtype ? $item->zbtype : "gaode";

											$left_img = $item->left_img ? $item->left_img : "https://oss.lcweb01.cn/joomla/20211213/152d1b1790b18bb7bcee4c93717e0d5b.png";
											$descrip = $item->descrip ? $item->descrip :  "北京市天安门";

											$dtname = $item->dtname ? $item->dtname :  "联系人：李经理";
											$dttel = $item->dttel ? $item->dttel :  "电话：010-6666666";
											$dtadd = $item->dtadd ? $item->dtadd :  "地址：北京市东城区长安街";


											$output .= '<li lng="'.$map_lng.'" lat="'.$map_lat.'" title="'.$map_name.'" map_coordinate="'.$zbtype.'">
								                    <div class="gdli_left fl">
							                            <img src="'.$left_img.'" alt="'.$map_name.'">
								                    </div>
								                    <div class="gdli_right fl">
								                        <div class="gdli_title">
								                            <span>
								                                '.$map_name.'
								                            </span>
								                            <span>
								                                '.$descrip.'
								                            </span>
								                        </div>
								                        <div class="gdcont">
								                            <div class="gdli_name">
								                                '.$dtname.'
								                            </div>
								                            <div class="gdli_tel">
								                                '.$dttel.'
								                            </div>
								                            <div class="gdli_address">
								                                '.$dtadd.'
								                            </div>
								                        </div>

								                    </div>
								                    <div class="cl"></div>
								                </li>';
										}
									}
					            $output .= '</ul>
					        </div>';

					        if($pc_table=="wz1" && $sj_table=="wza2"){
								$output .= '<div class="gd_map_left fl sj">
									<iframe class="map-iframe mobile-map" src="' . $mobile_link . '" width="100%" height="320"></iframe>
						        </div>';
							}


							if($pc_table=="wz2"){
								if($sj_table=="wza2"){
									$output .= '<div class="gd_map_left fl">
							            <iframe class="map-iframe pc-map" src="' . $pc_link . '" width="100%" height="320"></iframe>
										<iframe class="map-iframe mobile-map" src="' . $mobile_link . '" width="100%" height="320"></iframe>
							        </div>';
								}else{
									$output .= '<div class="gd_map_left fl">
				                    	<iframe class="map-iframe pc-map" src="' . $pc_link . '" width="100%" height="320"></iframe>
							        </div>';
								}
							}

					        $output .= '<div class="cl"></div>
					    </div>';
                }else{
                	$map_name = (isset($settings->map_name) && $settings->map_name) ? $settings->map_name : '北京市天安门';
	                $map_coordinate = (isset($settings->map_coordinate) && $settings->map_coordinate) ? $settings->map_coordinate : 'gaode';
	                $pc_link = '';
	                $mobile_link = '';

	                switch ($map_type) {
	                    case 'type02':
	                        $pc_link .= '//uri.amap.com/marker?position=' . trim($map[0]) . ',' . trim($map[1]) . '&name=' . $map_name;
	                        break;
	                    case 'type03':
	                        $pc_link .= '//uri.amap.com/navigation?&to=' . trim($map[0]) . ',' . trim($map[1]) . ',' . $map_name;
	                        break;
	                }

	                $pc_link .= '&src=建站通&coordinate=' . $map_coordinate . '&callnative=0';
	                $mobile_link .= $pc_link . "&platform=mobile";
				    $output .= '
	                    <iframe class="map-iframe pc-map" src="' . $pc_link . '" width="100%" height="320"></iframe>
				        <iframe class="map-iframe mobile-map" src="' . $mobile_link . '" width="100%" height="320"></iframe>';
                }

            }
			if($map_type == 'type04'){
				$map_add = (isset($settings->map_add) && $settings->map_add) ? $settings->map_add : '哈尔滨市道里区爱建路13号';
				$map_email = (isset($settings->map_email) && $settings->map_email) ? $settings->map_email : '<EMAIL>';
				$map_tel = (isset($settings->map_tel) && $settings->map_tel) ? $settings->map_tel : '************';
				$map_tubiao = (isset($settings->map_tubiao) && $settings->map_tubiao) ? $settings->map_tubiao : '/components/com_jwpagefactory/addons/amap/assets/images/jump.png';


				$output .= '
					<div class="d_main" id="address">
						<div class="d_add_area">
							<div class="d_add_t">
								<div class="d_map wow fadeInUp animated animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">
									<div id="map" class=" bmap-container" style="overflow: hidden; position: relative; background-image: url(&quot;https://webmap0.bdimg.com/image/api/bg.png&quot;); text-align: left; touch-action: none;"><div id="platform" style="overflow: visible; position: absolute; z-index: 5; left: 0px; top: 0px; cursor: url(&quot;https://webmap0.bdimg.com/image/api/openhand.cur&quot;) 8 8, default;"><div id="mask" class=" BMap_mask" style="position: absolute; left: 0px; top: 0px; z-index: 9; overflow: hidden; user-select: none; width: 697px; height: 360px; opacity: 0; background: rgb(0, 0, 0); transition: opacity 0.4s ease 0s;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 200;"><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 800;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 700;"><div class="BMap_Marker BMap_noprint" unselectable="on" ""="" style="position: absolute; padding: 0px; margin: 0px; border: 0px; cursor: pointer; width: 23px; height: 32px; left: 339px; top: 155px; z-index: -9154088; transform-origin: 10px 25px;" title=""></div></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 600;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 500;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 400;"></div></div></div><div class="click-ripple-container"><div class="click-ripple"></div></div><div class=" anchorBL" style="position: absolute; z-index: 10; inset: auto auto 15px 5px;"><img src="https://api.map.baidu.com/images/logo_hd.png" style="height:21px;width:62px;"></div><canvas width="697" height="360" style="position: absolute; left: 0px; top: 0px; width: 697px; height: 360px; z-index: 0;"></canvas><div unselectable="on" class=" BMap_cpyCtrl anchorBL" style="cursor: default; white-space: nowrap; text-size-adjust: none; inset: auto auto 2px 2px; position: absolute; z-index: 10;"><span _cid="1" style="display: none;"><span style="background: rgba(255, 255, 255, 0.701961);padding: 0px 1px;line-height: 16px;display: inline;height: 16px;">©&nbsp;2022 Baidu - GS(2021)6026号 - 甲测资字11111342 - 京ICP证030173号 - Data © 长地万方</span></span></div></div>
									</div>
									<div class="d_add_right wow fadeInUp animated animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
									<div class="d_add">
										<ul>';
										foreach ($settings->sduotu_location_items as $keys => $item) {

											//经度
											$map_lng = $item->longitude ? $item->longitude : "126.60804";
											$latitude = $item->latitude ? $item->latitude : "45.770448";
											$left_img = $item->left_img ? $item->left_img : "https://oss.lcweb01.cn/joomla/20220519/3d7ec151c6a6042bc13a358aa562e954.png";
											$title = $item->title ? $item->title : "龙采科技集团有限责任公司（黑龙江总部）";
											$descrip = $item->descrip ? $item->descrip : "************（转1）";
											$dtname = $item->dtname ? $item->dtname : "";
											$dtadd = $item->dtadd ? $item->dtadd : "哈尔滨市道里区爱建路13号";
											$dtdomain = $item->dtdomain ? $item->dtdomain : "https://www.longcai.com";

											$output .= '<li class="dt-bg">
												<img src="'.$left_img.'">
												<div class="d_add_r">
													<h4>'.$title.'</h4>
													热线电话：'.$descrip.'<br>';
													if($dtname!=""){
														$output .='客户服务及投诉：'.$dtname.'<br>';
													}
													$output .='地址：<span class="dizhi">'.$dtadd.'</span><br>
													网址：<a href="'.$dtdomain.'" target="_blank">'.$dtdomain.'</a>
												</div>
												<div class="cl"></div>
												<div class="jwd" style="display: none;">'.$map_lng.','.$latitude.'</div>
												<div class="tname" style="display: none;">'.$title.'</div><br>
												<div class="jj" style="display: none;">'.$dtadd.'</div><br>
											</li>';
										}

										$output .= '</ul>
										<div class="cl"></div>
									</div>
								</div>
								<div class="cl"></div>
							</div>
							<div class="d_con">
								<ul>
									<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="0.75s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.75s;">
										<img src="https://oss.lcweb01.cn/joomla/20220519/9f401325b21ffbff81277bcb2db1570d.png">
										'.$map_add.'
										<div class="cl"></div>
									</li>
									<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="0.8999999999999999s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.9s;">
										<img src="https://oss.lcweb01.cn/joomla/20220519/edb57501edb6208c881c6331d5716aa5.png">
										'.$map_email.'
										<div class="cl"></div>
									</li>
									<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="1.05s" style="visibility: visible; animation-duration: 2s; animation-delay: 1.05s;">
										<img src="https://oss.lcweb01.cn/joomla/20220519/d69be296f060764f94ba1d791799b5fd.png">
										'.$map_tel.'
										<div class="cl"></div>
									</li>
								</ul><div class="cl"></div>
							</div>
						</div>
					</div>
					<script type="text/javascript" src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=C4CUiSxlKGyEIOwWlMsgIS1kCp7l1FYX"></script>
					<script>
						jQuery(function($){
							$("'.$addon_id .' .d_add li").eq(0).addClass("dt-bg");
							$("'.$addon_id .' .d_add li").click(function(){
								$(this).addClass("dt-bg");
								$(this).siblings().removeClass("dt-bg");
							})
						})
					</script>
					<script>

						// GL版命名空间为BMapGL
						// 按住鼠标右键，修改倾斜角和角度
						var map = new BMapGL.Map("map");    // 创建Map实例
						';
						foreach ($settings->sduotu_location_items as $keys => $item) {
							if($keys==0){
								$map_lng = $item->longitude ? $item->longitude : "126.60804";
								$latitude = $item->latitude ? $item->latitude : "45.770448";
								$output .= '
									var point = new BMapGL.Point("'.$map_lng.'","'.$latitude.'");
									map.centerAndZoom(new BMapGL.Point("'.$map_lng.'","'.$latitude.'"), 19);  // 初始化地图,设置中心点坐标和地图级别
								';
							}

						}
						$output .= 'map.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
						map.setHeading("64.5");
						map.setTilt("73");
						var myIcon = new BMapGL.Icon("'.$map_tubiao.'", new BMapGL.Size("23", "32"), {
							// 指定定位位置。
							// 当标注显示在地图上时，其所指向的地理位置距离图标左上
							// 角各偏移10像素和25像素。您可以看到在本例中该位置即是
							// 图标中央下端的尖角位置。
							anchor: new BMapGL.Size("10", "25"),
							// 设置图片偏移。
							// 当您需要从一幅较大的图片中截取某部分作为标注图标时，您
							// 需要指定大图的偏移位置，此做法与css sprites技术类似。
							imageOffset: new BMapGL.Size("0", "0" )   // 设置图片偏移
						});
						// 创建标注对象并添加到地图
						var marker = new BMapGL.Marker(point, {icon: myIcon});
						map.addOverlay(marker);

						$("'.$addon_id .' .d_add>ul>li").click(function(){
							var h1=$(this).find(".jwd").html();
							var tname=$(this).find(".tname").html();
							var jj=$(this).find(".jj").html();
							var h2=h1.split(",");
							var h3=parseFloat(h2[0]);
							var h4=parseFloat(h2[1]);
							var map1 = new BMapGL.Map("map");
							var point = new BMapGL.Point(h3,h4);
							map1.centerAndZoom(new BMapGL.Point(h3,h4), 19);  // 初始化地图,设置中心点坐标和地图级别
							map1.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
							map1.setHeading("64.5");
							map1.setTilt("73");
							var myIcon = new BMapGL.Icon("'.$map_tubiao.'", new BMapGL.Size("23", "32"), {
								// 指定定位位置。
								// 当标注显示在地图上时，其所指向的地理位置距离图标左上
								// 角各偏移10像素和25像素。您可以看到在本例中该位置即是
								// 图标中央下端的尖角位置。
								anchor: new BMapGL.Size("10", "25"),
								// 设置图片偏移。
								// 当您需要从一幅较大的图片中截取某部分作为标注图标时，您
								// 需要指定大图的偏移位置，此做法与css sprites技术类似。
								imageOffset: new BMapGL.Size("0", "0" )   // 设置图片偏移
							});
							// 创建标注对象并添加到地图
							var marker = new BMapGL.Marker(point, {icon: myIcon});
							map1.addOverlay(marker);

						})
					</script>
				';
            }

		}

		return $output;
	}

	public function scripts()
	{
		$map_type = (isset($settings->map_type) && $settings->map_type) ? $settings->map_type : 'type01';
		jimport('joomla.application.component.helper');
		$params = JComponentHelper::getParams('com_jwpagefactory');
		$amap_api = $params->get('amap_api', '');

		if($map_type == 'type01'){
			$amap_api = 'd1f15ad3fdf0c704434eaf23c7c18698';
		}
		return array(
			'//webapi.amap.com/maps?v=1.4.12&key=' . $amap_api. '&&plugin=AMap.Scale,AMap.HawkEye,AMap.ToolBar,AMap.ControlBar',
			'//webapi.amap.com/maps?v=1.4.12&key=' . $amap_api,
			/* 'https://webapi.amap.com/maps?v=2.0&key='.$amap_api.'&plugin=AMap.Adaptor,AMap.Scale,AMap.HawkEye,AMap.ToolBar,AMap.ControlBar', */
			JURI::base(true) . '/components/com_jwpagefactory/assets/js/amap.js'
		);
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
		$height = (isset($settings->height) && $settings->height) ? $settings->height : 0;
		$height_sm = (isset($settings->height_sm) && $settings->height_sm) ? $settings->height_sm : 0;
		$height_xs = (isset($settings->height_xs) && $settings->height_xs) ? $settings->height_xs : 0;

		$mobile_full = (isset($settings->mobile_full) && $settings->mobile_full) ? $settings->mobile_full : 0;

		$libg_color = (isset($settings->libg_color) && $settings->libg_color) ? $settings->libg_color : '#F8F8F8';
		$litit_color = (isset($settings->litit_color) && $settings->litit_color) ? $settings->litit_color : '#265FAA';
		$lixq_color = (isset($settings->lixq_color) && $settings->lixq_color) ? $settings->lixq_color : '#9A9A9A';
		$pc_table = (isset($settings->pc_table) && $settings->pc_table) ? $settings->pc_table : 'wz1';
		$sj_table = (isset($settings->sj_table) && $settings->sj_table) ? $settings->sj_table : 'wza1';

		$map_type = (isset($settings->map_type) && $settings->map_type) ? $settings->map_type : 'type01';

		$css = '';
		if ($height) {
			$css .= $addon_id . ' .jwpf-addon-amap-canvas , ' . $addon_id . ' .map-iframe {';
			$css .= 'height:' . (int)$height . 'px;';
			$css .= '}';
		}
		$css .=  $addon_id . ' .map-iframe {
		    border: none;
		}';
        $css .=  $addon_id . ' .mobile-map {
		    display: none;
		}';

        $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            if ($height_sm) {
                $css .= $addon_id . ' .jwpf-addon-amap-canvas , ' . $addon_id . ' .map-iframe {';
                $css .= 'height:' . (int)$height_sm . 'px;';
                $css .= '}';
            }
        $css .= '}';
        $css .= '@media (max-width: 767px) {';
            if ($height_xs) {
                $css .= $addon_id . ' .jwpf-addon-amap-canvas, ' . $addon_id . ' .map-iframe {';
                $css .= 'height:' . (int)$height_xs . 'px;';
                $css .= '}';
            }
            $css .=  $addon_id . ' .pc-map {
                display: none;
            }';
            $css .=  $addon_id . ' .mobile-map {
                display: block;
            }';
            if($mobile_full == 1) {
                $css .=  $addon_id . ' .mobile-map {
                    height: 100vh;
                }';
            }
        $css .= '}';


        $css .= '.amap-info-window-content{padding:8px;}';

        /*多图切换样式*/

	    $css .= $addon_id . ' .fl{float:left;}';
	    $css .= $addon_id . ' .cl{clear:both;}';
	    $css .= $addon_id . ' ul{margin:0;padding:0;}';
	    $css .= $addon_id . ' li{
	        list-style-type:none
	    }';
	    $css .= $addon_id . ' .gd_map{
	    	border:1px solid #ccc;
			height: '. (int)$height .'px;
	    }';
	    $css .= $addon_id . ' .gd_map_left{width:65%;height:100%;';
	    	if($pc_table=="wz1"){
	    		$css .='border-right:1px solid #ccc; ';
	    	}else{
				$css .='border-left:1px solid #ccc; ';
	    	}

	    $css .='}';
	    $css .= $addon_id . ' .sj{display:none;}';

	    $css .= $addon_id . ' .gd_map_right{width:35%;height:100%;max-height:100%;overflow-y:auto; }';
	    $css .= $addon_id . ' .gdmap_ul{width:100%;padding:10px 5%;}';
	    $css .= $addon_id . ' .gdmap_ul li{width:100%;padding:5px 3%;border-top:1px solid #ccc;background: #fff;cursor: pointer;}';
	   	$css .= $addon_id . ' .gdmap_ul li:first-child{border-top:none;}';
	    $css .= $addon_id . ' .gdli_left{width:10%;height:50px;line-height:50px;}';
	    $css .= $addon_id . ' .gdli_left img{max-width:85%;display: inline-block;vertical-align:middle;}';
	    $css .= $addon_id . ' .gdli_right{width:90%;}';
	    $css .= $addon_id . ' .gdli_title span{font-size:16px;color:#333;display: block;line-height:25px;}';
	    $css .= $addon_id . ' .gdli_name,.gdli_tel,.gdli_address{font-size:14px;color:#9a9a9a;line-height:25px;}';
	    $css .= $addon_id . ' .gdcont{
	        padding:5px 0px;
	    }';
	    $css .= $addon_id . ' .gdmap_ul li:hover{
	        background: '.$libg_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul li:hover .gdli_title span{
	        color: '.$litit_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul li:hover .gdli_name{
	        color: '.$lixq_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul li:hover .gdli_tel{
	        color: '.$lixq_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul li:hover .gdli_address{
	        color: '.$lixq_color.';
	    }';

	    $css .= $addon_id . ' .gdmap_ul .active{
	        background: '.$libg_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul .active .gdli_title span{
	        color: '.$litit_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul .active .gdli_name{
	        color: '.$lixq_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul .active .gdli_tel{
	        color: '.$lixq_color.';
	    }';
	    $css .= $addon_id . ' .gdmap_ul .active .gdli_address{
	        color: '.$lixq_color.';
	    }';

	    $css .= '@media (max-width: 767px) {';
	    	$css .= $addon_id . ' .gdmap_ul{width:100%;padding:10px 3%;}';

	    	$css .= $addon_id . ' .sj{display:block;}';

	    	$css .=  $addon_id . ' .pc-map {
                display: none;
            }';
            $css .=  $addon_id . ' .mobile-map {
                display: block;
            }';

            $css .= $addon_id . ' .gd_map{
		    	border:1px solid #ccc;height:auto;
		    }';


            $css .= $addon_id . ' .gd_map_left{width:100%;';
            	if ($height_xs) {
	            	$css .='height:'. (int)$height_xs .'px;';
	            }
	            $css .='border:none;
	        }';
	    	$css .= $addon_id . ' .gd_map_right{width:100%;border:none; }';

        $css .= '}';

		if($map_type=="type04"){
			$css .= $addon_id . ' img{display:blcok;}
			'. $addon_id .' .d_add_right::-webkit-scrollbar{width: 6px;height: 20px;}
			'. $addon_id .' .d_add_right::-webkit-scrollbar-thumb{border-radius: 3px;background: #d9012a;cursor: pointer;}
			'. $addon_id .' .d_add_right::-webkit-scrollbar-track{border-radius: 3px;background: none;}
			'. $addon_id .' .anchorBL{display: none;}
			'. $addon_id .' .d_main {
				width: 100%;
				margin: 0 auto;
				overflow: hidden;
			}
			'. $addon_id .' #map{width: 100%;height: 100%;overflow: hidden;margin:0;}
			'. $addon_id .' .d_add_area{border: solid 1px #e2e2e2; overflow: hidden;}
			'. $addon_id .' .d_add_t{border-bottom: solid 1px #e2e2e2; padding-right: 1%; overflow: hidden;}
			'. $addon_id .' .d_map{float: left; width: 49%; height:360px; border-right: solid 1px #e2e2e2; overflow: hidden;}
			'. $addon_id .' .d_map img{width: 100%; height: 100%;}
			'. $addon_id .' .d_add_right{float: right; width: 48%; height:330px; margin: 15px 0; overflow-y: scroll; -webkit-overflow-scrolling: touch;}
			'. $addon_id .' .d_add{width: 90%;}
			'. $addon_id .' .d_add li{border-bottom: dashed 1px #eee; padding: 10px 0;}
			'. $addon_id .' .d_add img{float: left; width: 4.5%;margin-left: 3%;}
			'. $addon_id .' .d_add_r{float: right; width: 90%; color: #999; font-size: 14px; line-height: 24px;}
			'. $addon_id .' .d_add_r a{color: #999; font-size: 14px; line-height: 24px;}
			'. $addon_id .' .d_add_r h4{color: #2d2d2d; font-size: 16px; line-height: 34px; font-weight: normal;}
			'. $addon_id .' .d_con{width: 100%; padding: 65px 0;}
			'. $addon_id .' .d_con li{float: left; width: 31.333%; padding: 0 1%; text-align: center; color: #666; font-size: 16px;}
			'. $addon_id .' .d_con img{width: 60px; height: 60px; border-radius: 50%; background: #c4c4c4; margin: 0 auto 6%; transition:all 0.8s;}
			'. $addon_id .' .d_con li:hover img{background: #b40025;}
			'. $addon_id .' .d_con li:nth-child(2){font-size: 20px;}
			'. $addon_id .' .d_con li:nth-child(3){font-size: 20px;}
			@media only screen and (max-width: 1269px) and (min-width: 1024px){
				'. $addon_id .' .d_main {
					width: 100%;
				}
			}
			@media only screen and (max-width: 1023px){
				'. $addon_id .' .d_main {
					width: 100%;
				}
				'. $addon_id .' .d_map{width: 48%; height:280px;}
				'. $addon_id .' .d_add_right{width: 48%; height:250px; margin: 15px 0;}
				'. $addon_id .' .d_add img{margin-top: 2%;}
				'. $addon_id .' .d_add_r{width: 90%;}
				'. $addon_id .' .d_con{padding: 50px 0 60px 0;}
				'. $addon_id .' .d_con img{width: 50px; height: 50px;}
			}

			@media only screen and (max-width: 750px){
				'. $addon_id .' .d_main {
					width: 100%;
				}
				'. $addon_id .' .d_add_t{padding: 0;}
				'. $addon_id .' .d_map{float: none; width: 100%; height:220px; border: none;}
				'. $addon_id .' .d_add_right{float: none; width: 98%; height:250px; margin: 15px auto;}
				'. $addon_id .' .d_add{width: 90%; margin: 0 auto;}
				'. $addon_id .' .d_add img{width: 6%; margin-top: 0;}
				'. $addon_id .' .d_add_r{width: 90%; font-size: 12px; line-height: 22px;}
				'. $addon_id .' .d_add_r h4{font-size: 14px; line-height: 26px;}
				'. $addon_id .' .d_con{width: 95%; padding: 4% 0 6% 0; margin: 0 auto;}
				'. $addon_id .' .d_con li{float: none; width: 100%; text-align: left; font-size: 14px; line-height: 22px; margin-bottom: 5%;}
				'. $addon_id .' .d_con li:nth-child(2){font-size: 14px; line-height: 30px;}
				'. $addon_id .' .d_con li:nth-child(3){font-size: 14px; line-height: 30px;}
				'. $addon_id .' .d_con li:last-child{margin: 0;}
				'. $addon_id .' .d_con img{float: left; width: 30px; height: 30px; margin-right: 3%;}
			}';

		}
		return $css;
	}

	public static function getTemplate()
	{
		$output = '
		<#
			var map = data.map.split(",");

			var ConvertToBaseSixFour = {
				_keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
				encode: function(e) {
					var t = "";
					var n, r, i, s, o, u, a;
					var f = 0;
					e = ConvertToBaseSixFour._utf8_encode(e);
					while (f < e.length) {
						n = e.charCodeAt(f++);
						r = e.charCodeAt(f++);
						i = e.charCodeAt(f++);
						s = n >> 2;
						o = (n & 3) << 4 | r >> 4;
						u = (r & 15) << 2 | i >> 6;
						a = i & 63;
						if (isNaN(r)) {
							u = a = 64
						} else if (isNaN(i)) {
							a = 64
						}
						t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
					}
					return t
				},
				_utf8_encode: function(e) {
					e = e.replace(/rn/g, "n");
					var t = "";
					for (var n = 0; n < e.length; n++) {
						var r = e.charCodeAt(n);
						if (r < 128) {
							t += String.fromCharCode(r)
						} else if (r > 127 && r < 2048) {
							t += String.fromCharCode(r >> 6 | 192);
							t += String.fromCharCode(r & 63 | 128)
						} else {
							t += String.fromCharCode(r >> 12 | 224);
							t += String.fromCharCode(r >> 6 & 63 | 128);
							t += String.fromCharCode(r & 63 | 128)
						}
					}
					return t
				}
			};
			var infoText = (!_.isEmpty(data.infowindow)) ? ConvertToBaseSixFour.encode(data.infowindow) : "";

			let location_addr = {
				address: data.infowindow,
				longitude: data.map_lng,
				latitude: data.map_lat,
				icon: data.marker
			};
			if(_.isObject(data.multi_location_items) && data.multi_location !== 0){
				_.each(data.multi_location_items, function(item){
				//					let latLong = _.split(item.location_item, ",");
					let mainObj = {
						address: item.location_popup_text,
						latitude: item.latitude,
						longitude: item.longitude,
						icon: item.location_marker,
					}
					location_addr = _.concat(location_addr, mainObj);
				})
			}



			let location_json = JSON.stringify(location_addr);

			//新增参数
			var addonId = "#jwpf-addon-" + data.id;
			//地图类型
			var map_type = data.map_type || "type01";
			//经度
			var map_lng = data.map_lng || "116.397464";
			//纬度
			var map_lat = data.map_lat || "39.908696";
			// type02 标点位置名称
			var map_name = data.map_name || "北京市天安门";
			// 手机端是否全屏
			var mobile_full = data.mobile_full || 0;

			//开启多点切换
			var duotu_location = data.duotu_location || 0;
			var libg_color = data.libg_color || "#F8F8F8";
			var litit_color = data.litit_color || "#265FAA";
			var lixq_color = data.lixq_color || "#9A9A9A";
			var pc_table = data.pc_table || "wz1";
			var sj_table = data.sj_table || "wza1";

		#>
		<style type="text/css">
		{{ addonId }} .jwpf-addon-amap-canvas, {{ addonId }} .map-iframe {
			<# if(_.isObject(data.height)){ #>
				height: {{ data.height.md }}px;
			<# } else { #>
				height: {{ data.height }}px;
			<# } #>
		}
		/*新增样式*/
		{{ addonId }} .map-iframe {
		    border: none;
		}
		{{ addonId }} .mobile-map {
		    display: none;
		}

		/*多图切换样式*/

	    {{ addonId }} .fl{float:left;}
	    {{ addonId }} .cl{clear:both;}
	    {{ addonId }} ul{margin:0;padding:0;}
	    {{ addonId }} li{
	        list-style-type:none
	    }
	    {{ addonId }} .gd_map{
	    	border:1px solid #ccc;
			<# if(_.isObject(data.height)){ #>
				height: {{ data.height.md }}px;
			<# } else { #>
				height: {{ data.height }}px;
			<# } #>
	    }
	    {{ addonId }} .gd_map_left{width:65%;height:100%;}
	    {{ addonId }} .gd_map_right{width:35%;height:100%;max-height:100%;overflow-y:auto;
	    	<# if(pc_table=="wz1"){ #>
	    		border-left:1px solid #ccc;
	    	<# }else{ #>
				border-right:1px solid #ccc;
	    	<# } #>
	    }
	    {{ addonId }} .gdmap_ul{width:100%;padding:10px 5%;}
	    {{ addonId }} .gdmap_ul li{width:100%;padding:5px 2%;border-top:1px solid #ccc;background: #fff;cursor: pointer;}
	   	{{ addonId }} .gdmap_ul li:first-child{border-top:none;}
	    {{ addonId }} .gdli_left{width:10%;height:50px;line-height:50px;}
	    {{ addonId }} .gdli_left img{max-width:85%;display: inline-block;vertical-align:middle;}
	    {{ addonId }} .gdli_right{width:90%;}
	    {{ addonId }} .gdli_title span{font-size:16px;color:#333;display: block;line-height:25px;}
	    {{ addonId }} .gdli_name,.gdli_tel,.gdli_address{font-size:14px;color:#9a9a9a;line-height:25px;}
	    {{ addonId }} .gdcont{
	        padding:5px 0px;
	    }
	    {{ addonId }} .gdmap_ul li:hover{
	        background: {{ libg_color }};
	    }
	    {{ addonId }} .gdmap_ul li:hover .gdli_title span{
	        color: {{ litit_color }};
	    }
	    {{ addonId }} .gdmap_ul li:hover .gdli_name{
	        color: {{ lixq_color }};
	    }
	    {{ addonId }} .gdmap_ul li:hover .gdli_tel{
	        color: {{ lixq_color }};
	    }
	    {{ addonId }} .gdmap_ul li:hover .gdli_address{
	        color: {{ lixq_color }};
	    }
		{{ addonId }} .sj{display:none;}
		@media (min-width: 768px) and (max-width: 991px) {
			{{ addonId }} .jwpf-addon-amap-canvas, {{ addonId }} .map-iframe {
				<# if(_.isObject(data.height)){ #>
					height: {{ data.height.sm }}px;
				<# } #>
			}
		}
		@media (max-width: 767px) {
			{{ addonId }} .sj{display:block;}
			{{ addonId }} .gd_map_right{border:none;}
			{{ addonId }} .jwpf-addon-amap-canvas, {{ addonId }} .map-iframe {
				<# if(_.isObject(data.height)){ #>
					height: {{ data.height.xs }}px;
				<# } #>
			}
			{{ addonId }} .pc-map {
                display: none;
            }
			{{ addonId }} .mobile-map {
                display: block;
            }
            <# if(mobile_full == 1){ #>
                {{ addonId }} .mobile-map {
                    height: 100vh;
                }
            <# } #>

			<# if(duotu_location == 1){ #>
            	{{ addonId }} .gd_map_left{width:100%;}
	    		{{ addonId }} .gd_map_right{width:100%; }
	    		{{ addonId }} .gd_map{height:auto;}
	    	<# } #>
		}
		</style>
		<# if(map_type == "type01") { #>
			<p class="alert alert-warning" style="margin-bottom: 10px;">请在预览页面中查看效果</p>
			<div id="jwpf-addon-map-{{ data.id }}" class="jwpf-addon jwpf-addon-amap {{ data.class }}">
				<# if( !_.isEmpty( data.title ) ){ #>
				    <{{ data.heading_selector }} class="jwpf-addon-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="title" contenteditable="true">{{ data.title }}</{{ data.heading_selector }}>
				<# } #>
				<div class="jwpf-addon-content">
					<div id="jwpf-addon-amap-{{ data.id }}" class="jwpf-addon-amap-canvas" data-location=\'{{ConvertToBaseSixFour.encode(location_json)}}\' data-lng="{{ data.map_lng }}" data-lat="{{ data.map_lat }}" data-maptype="{{ data.type }}" data-mapzoom="{{ data.zoom }}" data-mousescroll="{{ data.mousescroll }}" data-marker="{{ data.marker }}" data-infowindow="{{ infoText }}"></div>
				</div>
			</div>
		<# } #>
		<# if(map_type == "type02" || map_type == "type03") {
		    var pc_link = "",
		        mobile_link = "",
		        map_coordinate = data.map_coordinate || "gaode";
		    switch(map_type){
		        case "type02":
		            pc_link = "//uri.amap.com/marker?position=" + map_lng + "," + map_lat + "&name=" + map_name;
		            break;
		        case "type03":
		            //var a = "from=116.478346,39.997361,startpoint";
		            pc_link = "//uri.amap.com/navigation?&to=" + map_lng + "," + map_lat + "," + map_name;
		            break;
		    }
		    pc_link += "&src=建站通&coordinate=" + map_coordinate + "&callnative=0";
            mobile_link = pc_link + "&platform=mobile";
		#>
            <# if(map_type == "type02" && duotu_location == "1") { #>
				<div class="gd_map">
					<#
						if(pc_table=="wz1"){
							if(sj_table=="wza1"){ #>
								<div class="gd_map_left fl">
						            <iframe class="map-iframe pc-map" src="{{ pc_link }}" width="100%" height="320"></iframe>
						            <iframe class="map-iframe mobile-map" src="{{ mobile_link }}" width="100%" height="320"></iframe>
						        </div>
							<# }else{ #>
								<div class="gd_map_left fl">
						            <iframe class="map-iframe pc-map" src="{{ pc_link }}" width="100%" height="320"></iframe>
						        </div>
							<# }
						}
					#>

					<# if(pc_table=="wz2" && sj_table=="wza1"){ #>
						<div class="gd_map_left fl sj">
				            <iframe class="map-iframe mobile-map" src="{{ mobile_link }}" width="100%" height="320"></iframe>
				        </div>
					<# } #>

			        <div class="gd_map_right fl">
			            <ul class="gdmap_ul">
			            	<# if(_.isObject(data.duotu_location_items) && data.duotu_location !== 0){
								_.each(data.duotu_location_items, function(item){
									//经度
									var map_lng = item.longitude || "116.397464";
									//纬度
									var map_lat = item.latitude || "39.908696";
									// type02 标点位置名称
									var map_name = item.title || "北京市天安门";

									var left_img = item.left_img || "https://oss.lcweb01.cn/joomla/20211213/152d1b1790b18bb7bcee4c93717e0d5b.png";
									var descrip = item.descrip || "北京市天安门";
									var dtname = item.dtname || "联系人：李经理";
									var dttel = item.dttel || "电话：010-6666666";
									var dtadd = item.dtadd || "地址：北京市东城区长安街";

									#>
										<li>
						                    <div class="gdli_left fl">
							                    <# if(left_img && left_img.indexOf("http://") == -1 && left_img.indexOf("https://") == -1){ #>
					                                <img src=\'{{ left_img }}\' alt="{{ map_name }}">
					                            <# } else if(left_img){ #>
					                                <img src=\'{{ left_img }}\' alt="{{ map_name }}">
					                            <# } #>

						                    </div>
						                    <div class="gdli_right fl">
						                        <div class="gdli_title">
						                            <span>
						                                {{ map_name }}
						                            </span>
						                            <span>
						                                {{ descrip }}
						                            </span>
						                        </div>
						                        <div class="gdcont">
						                            <div class="gdli_name">
						                                {{ dtname }}
						                            </div>
						                            <div class="gdli_tel">
						                                {{ dttel }}
						                            </div>
						                            <div class="gdli_address">
						                                {{ dtadd }}
						                            </div>
						                        </div>

						                    </div>
						                    <div class="cl"></div>
						                </li>
								<# })
							} #>

			            </ul>
			        </div>

					<# if(pc_table=="wz1" && sj_table=="wza2"){ #>
						<div class="gd_map_left fl sj">
				            <iframe class="map-iframe mobile-map" src="{{ mobile_link }}" width="100%" height="320"></iframe>
				        </div>
					<# } #>

			        <#
						if(pc_table=="wz2"){
							if(sj_table=="wza2"){ #>
								<div class="gd_map_left fl">
						            <iframe class="map-iframe pc-map" src="{{ pc_link }}" width="100%" height="320"></iframe>
						            <iframe class="map-iframe mobile-map" src="{{ mobile_link }}" width="100%" height="320"></iframe>
						        </div>
							<# }else{ #>
								<div class="gd_map_left fl">
						            <iframe class="map-iframe pc-map" src="{{ pc_link }}" width="100%" height="320"></iframe>
						        </div>
							<# }
						}
					#>
			        <div class="cl"></div>
			    </div>
            <# }else{ #>
				<iframe class="map-iframe pc-map" src="{{ pc_link }}" width="100%" height="320"></iframe>
				<iframe class="map-iframe mobile-map" src="{{ mobile_link }}" width="100%" height="320"></iframe>
			<# } #>

		<# } #>

		<# if(map_type == "type04") {
				var map_add = data.map_add || "哈尔滨市道里区爱建路13号";
				var map_email = data.map_email || "<EMAIL>";
				var map_tel = data.map_tel || "************";
				var map_tubiao = data.map_tubiao || "/components/com_jwpagefactory/addons/amap/assets/images/jump.png";
				var addon_id = "#jwpf-addon-" + data.id;
			#>
			<style type="text/css">
				{{addon_id}} img{display:blcok;}
				{{addon_id}} .d_add_right::-webkit-scrollbar{width: 6px;height: 20px;}
				{{addon_id}} .d_add_right::-webkit-scrollbar-thumb{border-radius: 3px;background: #d9012a;cursor: pointer;}
				{{addon_id}} .d_add_right::-webkit-scrollbar-track{border-radius: 3px;background: none;}
				{{addon_id}} .anchorBL{display: none;}
				{{addon_id}} .d_main {
					width: 100%;
					margin: 0 auto;
					overflow: hidden;
				}
				{{addon_id}} #map{width: 100%;height: 100%;overflow: hidden;margin:0;}
				{{addon_id}} .d_add_area{border: solid 1px #e2e2e2; overflow: hidden;}
				{{addon_id}} .d_add_t{border-bottom: solid 1px #e2e2e2; padding-right: 1%; overflow: hidden;}
				{{addon_id}} .d_map{float: left; width: 49%; height:360px; border-right: solid 1px #e2e2e2; overflow: hidden;}
				{{addon_id}} .d_map img{width: 100%; height: 100%;}
				{{addon_id}} .d_add_right{float: right; width: 48%; height:330px; margin: 15px 0; overflow-y: scroll; -webkit-overflow-scrolling: touch;}
				{{addon_id}} .d_add{width: 90%;}
				{{addon_id}} .d_add li{border-bottom: dashed 1px #eee; padding: 10px 0;}
				{{addon_id}} .d_add img{float: left; width: 4.5%;margin-left: 3%;}
				{{addon_id}} .d_add_r{float: right; width: 90%; color: #999; font-size: 14px; line-height: 24px;}
				{{addon_id}} .d_add_r a{color: #999; font-size: 14px; line-height: 24px;}
				{{addon_id}} .d_add_r h4{color: #2d2d2d; font-size: 16px; line-height: 34px; font-weight: normal;}
				{{addon_id}} .d_con{width: 100%; padding: 65px 0;}
				{{addon_id}} .d_con li{float: left; width: 31.333%; padding: 0 1%; text-align: center; color: #666; font-size: 16px;}
				{{addon_id}} .d_con img{width: 60px; height: 60px; border-radius: 50%; background: #c4c4c4; margin: 0 auto 6%; transition:all 0.8s;}
				{{addon_id}} .d_con li:hover img{background: #b40025;}
				{{addon_id}} .d_con li:nth-child(2){font-size: 20px;}
				{{addon_id}} .d_con li:nth-child(3){font-size: 20px;}
				@media only screen and (max-width: 1269px) and (min-width: 1024px){
					{{addon_id}} .d_main {
						width: 100%;
					}
				}
				@media only screen and (max-width: 1023px){
					{{addon_id}} .d_main {
						width: 100%;
					}
					{{addon_id}} .d_map{width: 48%; height:280px;}
					{{addon_id}} .d_add_right{width: 48%; height:250px; margin: 15px 0;}
					{{addon_id}} .d_add img{margin-top: 2%;}
					{{addon_id}} .d_add_r{width: 90%;}
					{{addon_id}} .d_con{padding: 50px 0 60px 0;}
					{{addon_id}} .d_con img{width: 50px; height: 50px;}
				}

				@media only screen and (max-width: 750px){
					{{addon_id}} .d_main {
						width: 100%;
					}
					{{addon_id}} .d_add_t{padding: 0;}
					{{addon_id}} .d_map{float: none; width: 100%; height:220px; border: none;}
					{{addon_id}} .d_add_right{float: none; width: 98%; height:250px; margin: 15px auto;}
					{{addon_id}} .d_add{width: 90%; margin: 0 auto;}
					{{addon_id}} .d_add img{width: 6%; margin-top: 0;}
					{{addon_id}} .d_add_r{width: 90%; font-size: 12px; line-height: 22px;}
					{{addon_id}} .d_add_r h4{font-size: 14px; line-height: 26px;}
					{{addon_id}} .d_con{width: 95%; padding: 4% 0 6% 0; margin: 0 auto;}
					{{addon_id}} .d_con li{float: none; width: 100%; text-align: left; font-size: 14px; line-height: 22px; margin-bottom: 5%;}
					{{addon_id}} .d_con li:nth-child(2){font-size: 14px; line-height: 30px;}
					{{addon_id}} .d_con li:nth-child(3){font-size: 14px; line-height: 30px;}
					{{addon_id}} .d_con li:last-child{margin: 0;}
					{{addon_id}} .d_con img{float: left; width: 30px; height: 30px; margin-right: 3%;}
				}
			</style>

			<div class="d_main" id="address">
				<div class="d_add_area">
					<div class="d_add_t">
						<div class="d_map wow fadeInUp animated animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s;">
							<div id="map" class=" bmap-container" style="overflow: hidden; position: relative; background-image: url(&quot;https://webmap0.bdimg.com/image/api/bg.png&quot;); text-align: left; touch-action: none;"><div id="platform" style="overflow: visible; position: absolute; z-index: 5; left: 0px; top: 0px; cursor: url(&quot;https://webmap0.bdimg.com/image/api/openhand.cur&quot;) 8 8, default;"><div id="mask" class=" BMap_mask" style="position: absolute; left: 0px; top: 0px; z-index: 9; overflow: hidden; user-select: none; width: 697px; height: 360px; opacity: 0; background: rgb(0, 0, 0); transition: opacity 0.4s ease 0s;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 200;"><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 800;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 700;"><div class="BMap_Marker BMap_noprint" unselectable="on" ""="" style="position: absolute; padding: 0px; margin: 0px; border: 0px; cursor: pointer; width: 23px; height: 32px; left: 339px; top: 155px; z-index: -9154088; transform-origin: 10px 25px;" title=""></div></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 600;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 500;"></div><div style="position: absolute; height: 0px; width: 0px; left: 0px; top: 0px; z-index: 400;"></div></div></div><div class="click-ripple-container"><div class="click-ripple"></div></div><div class=" anchorBL" style="position: absolute; z-index: 10; inset: auto auto 15px 5px;"><img src="https://api.map.baidu.com/images/logo_hd.png" style="height:21px;width:62px;"></div><canvas width="697" height="360" style="position: absolute; left: 0px; top: 0px; width: 697px; height: 360px; z-index: 0;"></canvas><div unselectable="on" class=" BMap_cpyCtrl anchorBL" style="cursor: default; white-space: nowrap; text-size-adjust: none; inset: auto auto 2px 2px; position: absolute; z-index: 10;"><span _cid="1" style="display: none;"><span style="background: rgba(255, 255, 255, 0.701961);padding: 0px 1px;line-height: 16px;display: inline;height: 16px;">©&nbsp;2022 Baidu - GS(2021)6026号 - 甲测资字11111342 - 京ICP证030173号 - Data © 长地万方</span></span></div></div>
							</div>
							<div class="d_add_right wow fadeInUp animated animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
							<div class="d_add">
								<ul>

								<# if(_.isObject(data.sduotu_location_items)){
									_.each(data.sduotu_location_items, function(item){
										//经度
										var map_lng = item.longitude || "126.60804";
										//纬度
										var latitude = item.latitude || "45.770448";
										var title = item.title || "龙采科技集团有限责任公司（黑龙江总部）";
										var left_img = item.left_img || "https://oss.lcweb01.cn/joomla/20220519/3d7ec151c6a6042bc13a358aa562e954.png";
										var descrip = item.descrip || "************（转1）";
										var dtname = item.dtname || "";
										var dtdomain = item.dtdomain || "https://www.longcai.com";
										var dtadd = item.dtadd || "哈尔滨市道里区爱建路13号";

										#>


									<li class="dt-bg">
										<img src=\'{{left_img}}\'>
										<div class="d_add_r">
											<h4>{{title}}</h4>
											热线电话：{{descrip}}<br>
											<# if(dtname!=""){ #>
												客户服务及投诉：{{dtname}}<br>
											<# } #>
											地址：<span class="dizhi">{{dtadd}}</span><br>
											网址：<a href="{{dtdomain}}" target="_blank">{{dtdomain}}</a>
										</div>
										<div class="cl"></div>
										<div class="jwd" style="display: none;">{{map_lng}},{{latitude}}</div>
										<div class="tname" style="display: none;">{{title}}</div><br>
										<div class="jj" style="display: none;">{{dtadd}}</div><br>
									</li>
								<#
									})
									} #>

								</ul>
								<div class="cl"></div>
							</div>
						</div>
						<div class="cl"></div>
					</div>
					<div class="d_con">
						<ul>
							<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="0.75s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.75s;">
								<img src="https://oss.lcweb01.cn/joomla/20220519/9f401325b21ffbff81277bcb2db1570d.png">
								{{data.map_add}}
								<div class="cl"></div>
							</li>
							<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="0.8999999999999999s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.9s;">
								<img src="https://oss.lcweb01.cn/joomla/20220519/edb57501edb6208c881c6331d5716aa5.png">
								{{data.map_email}}
								<div class="cl"></div>
							</li>
							<li class="wow fadeInUp animated animated" data-wow-duration="2s" data-wow-delay="1.05s" style="visibility: visible; animation-duration: 2s; animation-delay: 1.05s;">
								<img src="https://oss.lcweb01.cn/joomla/20220519/d69be296f060764f94ba1d791799b5fd.png">
								{{data.map_tel}}
								<div class="cl"></div>
							</li>
						</ul><div class="cl"></div>
					</div>
				</div>
			</div>


		<# } #>

		';
		return $output;
	}
}
