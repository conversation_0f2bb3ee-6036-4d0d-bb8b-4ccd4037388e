<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonFull_page03 extends JwpagefactoryAddons
{
    public function render() {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $output = '';
        return $output;
    }
    public function css() {
        $settings = $this->addon->settings;

        // 导航部分
        $nav_section_id = (isset($settings->nav_section_id) && $settings->nav_section_id) ? $settings->nav_section_id : '';
        $nav_section_bg = (isset($settings->nav_section_bg) && $settings->nav_section_bg) ? $settings->nav_section_bg : '';
        $nav_section_p = (isset($settings->nav_section_p) && $settings->nav_section_p) ? $settings->nav_section_p : '';
        if (isset($settings->nav_section_p) && $settings->nav_section_p) {
            if (is_object($settings->nav_section_p)) {
                $nav_section_p = $settings->nav_section_p->md;
                $nav_section_p_sm = $settings->nav_section_p->sm;
                $nav_section_p_xs = $settings->nav_section_p->xs;
            } else {
                $nav_section_p = $settings->nav_section_p;
                $nav_section_p_sm = $settings->nav_section_p_sm;
                $nav_section_p_xs = $settings->nav_section_p_xs;
            }
        } else {
            $nav_section_p = '';
            $nav_section_p_sm = '';
            $nav_section_p_xs = '';
        }
        // 导航变色
        $change_nav = (isset($settings->change_nav) && $settings->change_nav) ? $settings->change_nav : '0';
        $nav_section_id2 = (isset($settings->nav_section_id2) && $settings->nav_section_id2) ? $settings->nav_section_id2 : '';
        $nav_section_bg2 = (isset($settings->nav_section_bg2) && $settings->nav_section_bg2) ? $settings->nav_section_bg2 : '';

        // 分页器样式
        $pagination_r = (isset($settings->pagination_r) && $settings->pagination_r) ? $settings->pagination_r . 'px' : '';
        $pagination_m = (isset($settings->pagination_m) && $settings->pagination_m) ? $settings->pagination_m . 'px' : 'auto';
        $pagination_w = (isset($settings->pagination_w) && $settings->pagination_w) ? $settings->pagination_w . 'px' : '20px';
        $pagination_h = (isset($settings->pagination_h) && $settings->pagination_h) ? $settings->pagination_h . 'px' : '20px';
        $pagination_color = (isset($settings->pagination_color) && $settings->pagination_color) ? $settings->pagination_color : 'rgba(255, 255, 255, 0.3)';
        $pagination_color_a = (isset($settings->pagination_color_a) && $settings->pagination_color_a) ? $settings->pagination_color_a : 'rgba(255, 255, 255, 0.8)';
        $pagination_bColor_a = (isset($settings->pagination_bColor_a) && $settings->pagination_bColor_a) ? $settings->pagination_bColor_a : 'rgba(255, 255, 255, 0.2)';
        $pagination_b_w = (isset($settings->pagination_b_w) && $settings->pagination_b_w) ? $settings->pagination_b_w . 'px' : '0px';

        // 下翻页按钮宽度
        $navigation_img_w = (isset($settings->navigation_img_w) && $settings->navigation_img_w) ? $settings->navigation_img_w . 'px' : 'max-content';
        // 分式指示器样式
        $pagination_fraction_color = (isset($settings->pagination_fraction_color) && $settings->pagination_fraction_color) ? $settings->pagination_fraction_color : '#fff';
        $pagination_fraction_font_size = (isset($settings->pagination_fraction_font_size) && $settings->pagination_fraction_font_size) ? $settings->pagination_fraction_font_size : 18;
        $pagination_fraction_left = (isset($settings->pagination_fraction_left) && $settings->pagination_fraction_left) ? $settings->pagination_fraction_left : 35;
        $pagination_fraction_top = (isset($settings->pagination_fraction_top) && $settings->pagination_fraction_top) ? $settings->pagination_fraction_top : -24;

        $first_include = (isset($settings->first_include) && ($settings->first_include || $settings->first_include == 0)) ? $settings->first_include : 1;
        $set_footer_section_id = (isset($settings->set_footer_section_id) && ($settings->set_footer_section_id || $settings->set_footer_section_id == 0)) ? $settings->set_footer_section_id : 0;
        $footer_section_id = (isset($settings->footer_section_id) && ($settings->footer_section_id || $settings->footer_section_id == 0)) ? $settings->footer_section_id : 0;
        if (isset($settings->footer_padding) && $settings->footer_padding) {
            if (is_object($settings->footer_padding)) {
                $footer_padding = $settings->footer_padding->md;
                $footer_padding_sm = $settings->footer_padding->sm;
                $footer_padding_xs = $settings->footer_padding->xs;
            } else {
                $footer_padding = $settings->footer_padding;
                $footer_padding_sm = $settings->footer_padding_sm;
                $footer_padding_xs = $settings->footer_padding_xs;
            }
        } else {
            $footer_padding = '50px 0 50px 0';
            $footer_padding_sm = '50px 0 50px 0';
            $footer_padding_xs = '50px 0 50px 0';
        }
        $footer_bg_color = (isset($settings->footer_bg_color) && $settings->footer_bg_color) ? $settings->footer_bg_color : '';
        $footer_bg_img = (isset($settings->footer_bg_img) && $settings->footer_bg_img) ? $settings->footer_bg_img : '';
        $footer_z_index = (isset($settings->footer_z_index) && $settings->footer_z_index) ? $settings->footer_z_index : 0;
        $footer_poistion = (isset($settings->footer_poistion) && $settings->footer_poistion) ? $settings->footer_poistion : 'fixed';
        $footer_margin = (isset($settings->footer_margin) && $settings->footer_margin) ? $settings->footer_margin : '0 0 0 0';
        if (isset($settings->footer_height) && $settings->footer_height) {
            if (is_object($settings->footer_height)) {
                $footer_height = $settings->footer_height->md;
                $footer_height_sm = $settings->footer_height->sm;
                $footer_height_xs = $settings->footer_height->xs;
            } else {
                $footer_height = $settings->footer_height;
                $footer_height_sm = $settings->footer_height_sm;
                $footer_height_xs = $settings->footer_height_xs;
            }
        } else {
            $footer_height = 'auto';
            $footer_height_sm = 'auto';
            $footer_height_xs = 'auto';
        }

        $css = '';
        if($nav_section_id) {
            $css .= '#' . $nav_section_id . ' {
                background-color: ' . $nav_section_bg . ' !important;
                padding: ' . $nav_section_p . ' !important;
            }
            @media (max-width: 991px) and (min-width: 768px){
                padding: ' . $nav_section_p_sm . ' !important;
            }
            @media (max-width: 767px){
                padding: ' . $nav_section_p_xs . ' !important;
            }';
        }
        if($change_nav == 1){
            if($nav_section_id2) {
                $css .= '#' . $nav_section_id2 . ' {
                    background-color: ' . $nav_section_bg2 . ' !important;
                    padding: ' . $nav_section_p . ' !important;
                    opacity: 0;
                }';
            }
        }
        $css .= '.jw-page-factory {
            width: 100%;
            height: 100vh;
        }
        /* 下一页按钮 */
        .jw-page-factory .full-swiper-button-next {
            left: 0;
            right: 0;
            top: auto;
            margin: auto;
            bottom: 30px;
            /*transform: rotateZ(90deg);*/
            -webkit-animation: fade 2s infinite linear;
            animation: fade 3s infinite linear;
            color: #fff;
            position: fixed;
            z-index: 9999;
            width: ' . $navigation_img_w . ';
            cursor: pointer;
        }
        .jw-page-factory .full-swiper-button-next::after {
            content: \'\';
        }
        .jw-page-factory .full-swiper-button-next.swiper-button-disabled {
            display: none;
        }
        /* 切换点 */
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets {
            right: ' . $pagination_r . ' !important;
            position: fixed;
            z-index: 9999;
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
            width: ' . $pagination_w . ';
            height: ' . $pagination_h . ';
            background-color: rgba(255, 255, 255, 0);
            border: rgba(255, 255, 255, 0) solid ' . $pagination_b_w . ';
            opacity: 1;
            margin: ' . $pagination_m . ' 0;
            position: relative;
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet::after {
            content: \'\';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: ' . $pagination_color . ';
            margin: auto;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet i{
            font-style: normal;
            color: '.$pagination_fraction_color.';
            font-size: '.$pagination_fraction_font_size.'px;
            position: absolute;
            top: 50%;
            left: '.$pagination_fraction_left.'px;
            height: ' . $pagination_h . ';
            transform: translateY('.(-50 + $pagination_fraction_top).'%);
            display: none;
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet-active i{
            display: block;
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.full-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
            border: ' . $pagination_bColor_a . ' solid ' . $pagination_b_w . ';
        }
        .jw-page-factory.full-swiper-container.swiper-container-vertical>.full-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
            background-color: ' . $pagination_color_a . ';
        }
        #jw-page-factory.full-swiper-container .page-content .jwpf-section.swiper-slide, #jw-page-factory .jwpf-section.swiper-slide{
            overflow: hidden;
        }
        ';
        if($first_include == 0){
            $css.='
                .full-swiper-pagination .swiper-pagination-bullet:first-of-type{
                    display: none;
                }
            ';
        }

        $css.='@-webkit-keyframes fade {
            0% {
                opacity: 1;
                bottom: 20px;
            }

            50% {
                opacity: 0.3;
                bottom: 10px;
            }

            100% {
                opacity: 1;
                bottom: 20px;
            }
        }

        @keyframes fade {
            0% {
                opacity: 1;
                bottom: 20px;
            }

            50% {
                opacity: 0.3;
                bottom: 10px;
            }

            100% {
                opacity: 1;
                bottom: 20px;
            }
        }
        @-webkit-keyframes scale {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes scale {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }
        /*隐藏滚动条*/
        ::-webkit-scrollbar {
          display: none;
        }
        #jw-page-factory{
            transition: all 0.5s;
            height: auto;
        }
        .swiper-container.full-swiper-container{
            height: 100vh!important;
        }
        ';
        // footer内边距
        if($set_footer_section_id == 1){
            if($footer_section_id){
                $css .= '#'.$footer_section_id.'{
                    padding: '.$footer_padding.';
                    background: '.$footer_bg_color.' url(\''.$footer_bg_img.'\') no-repeat center/cover;
                    z-index: 0;
                    height: '.$footer_height.'px;';
                if($footer_poistion === 'static'){
                    $css.='position: '.$footer_poistion.';
                        margin-top: '.explode(' ', $footer_margin)[0].';
                        margin-right: '.explode(' ', $footer_margin)[1].';
                        margin-bottom: '.explode(' ', $footer_margin)[2].';
                        margin-left: '.explode(' ', $footer_margin)[3].';';
                }else{
                    $css.='position: '.$footer_poistion.';
                        top: '.explode(' ', $footer_margin)[0].';
                        right: '.explode(' ', $footer_margin)[1].';
                        bottom: '.explode(' ', $footer_margin)[2].';
                        left: '.explode(' ', $footer_margin)[3].';';
                }
                $css.='}
                @media (max-width: 991px) and (min-width:768px){
                    #'.$footer_section_id.'{
                        padding: '.$footer_padding_sm.'px;
                        height: '.$footer_height_sm.'px;
                    }
                }
                @media (max-width: 767){
                    #'.$footer_section_id.'{
                        padding: '.$footer_padding_xs.';
                        height: '.$footer_height_xs.'px;
                    }
                }';
            }
        }

        // 切换翻页
        $change_pagination = (isset($settings->change_pagination) && ($settings->change_pagination || $settings->change_pagination == 0)) ? $settings->change_pagination : 0;
        $pagenation_items = (isset($settings->pagenation_items) && $settings->pagenation_items) ? $settings->pagenation_items : array();

        if($change_pagination == 1){
            foreach($pagenation_items as $key => $value){
                $css.='
                        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet[data-index="' . $key . '"]{
                            border: rgba(255, 255, 255, 0) solid ' . $value->pagination_b_w . 'px;
                        }
                        .jw-page-factory.full-swiper-container.swiper-container-vertical>.full-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active[data-index="' . $key . '"]{
                            border: ' . $value->pagination_bColor_a . ' solid ' . $value->pagination_b_w . 'px;
                        }
                        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet[data-index="' . $key . '"]::after {
                            background-color: ' . $value->pagination_color . ';
                        }
                        .jw-page-factory.full-swiper-container.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet[data-index="' . $key . '"] i{
                            color: '.$value->pagination_fraction_color.';
                        }
                        .jw-page-factory.full-swiper-container.swiper-container-vertical>.full-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active[data-index="' . $key . '"]::after {
                            background-color: ' . $value->pagination_color_a . ';
                        }
                    ';
            }
        }
        return $css;
    }
    public function js() {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;

        // 导航部分
        $nav_section_id = (isset($settings->nav_section_id) && $settings->nav_section_id) ? $settings->nav_section_id : '';

        // 导航变色
        $change_nav = (isset($settings->change_nav) && $settings->change_nav) ? $settings->change_nav : '0';
        $nav_section_id2 = (isset($settings->nav_section_id2) && $settings->nav_section_id2) ? $settings->nav_section_id2 : '';

        // 是否关闭分页控制器
        $pagination_hide = (isset($settings->pagination_hide) && $settings->pagination_hide) ? $settings->pagination_hide : 0;
        // 是否关闭箭头控制器
        $navigation_hide = (isset($settings->navigation_hide) && $settings->navigation_hide) ? $settings->navigation_hide : 0;
        // 下翻页按钮
        $navigation_img = (isset($settings->navigation_img) && $settings->navigation_img) ? $settings->navigation_img : 'https://oss.lcweb01.cn/joomla/20211207/a6cc6a4bda51f3259dffc9c4aa3dd9bd.png';

        // 适配
        $pc_hide = (isset($settings->pc_hide) && $settings->pc_hide) ? $settings->pc_hide : 0;
        $pad_hide = (isset($settings->pad_hide) && $settings->pad_hide) ? $settings->pad_hide : 0;
        $wap_hide = (isset($settings->wap_hide) && $settings->wap_hide) ? $settings->wap_hide : 0;

        // 是否开启分式指示器
        $pagination_fraction_hide = (isset($settings->pagination_fraction_hide) && $settings->pagination_fraction_hide) ? $settings->pagination_fraction_hide : 0;
        // 首屏是否展示分页器
        $first_include = (isset($settings->first_include) && ($settings->first_include || $settings->first_include == 0)) ? $settings->first_include : 1;

        // 底部不计入轮播
        $last_include = (isset($settings->last_include) && ($settings->last_include || $settings->last_include == 0)) ? $settings->last_include : 1;
        $up_height = (isset($settings->up_height) && ($settings->up_height || $settings->up_height == 0)) ? $settings->up_height : 1000;
        $up_height_offset = (isset($settings->up_height_offset) && ($settings->up_height_offset || $settings->up_height_offset == 0)) ? $settings->up_height_offset : 0;
        $footer_bottom = (isset($settings->footer_bottom) && ($settings->footer_bottom || $settings->footer_bottom == 0)) ? $settings->footer_bottom : 0;
        $editTotal = (isset($settings->editTotal) && ($settings->editTotal || $settings->editTotal == 0)) ? $settings->editTotal : 0;
        $totalPage = (isset($settings->totalPage) && ($settings->totalPage || $settings->totalPage == 0)) ? $settings->totalPage : 0;
        $last_section_id = (isset($settings->last_section_id) && ($settings->last_section_id || $settings->last_section_id == 0)) ? $settings->last_section_id : "";
        $has_animate = (isset($settings->has_animate) && ($settings->has_animate || $settings->has_animate == 0)) ? $settings->has_animate : 1;
        $set_footer_section_id = (isset($settings->set_footer_section_id) && ($settings->set_footer_section_id || $settings->set_footer_section_id == 0)) ? $settings->set_footer_section_id : 0;
        $footer_section_id = (isset($settings->footer_section_id) && ($settings->footer_section_id || $settings->footer_section_id == 0)) ? $settings->footer_section_id : 0;

        // 间隔切换导航
        $interval_change_nav = (isset($settings->interval_change_nav) && ($settings->interval_change_nav || $settings->interval_change_nav == 0)) ? $settings->interval_change_nav : 0;
        $nav_section_interval1 = (isset($settings->nav_section_interval1) && ($settings->nav_section_interval1 || $settings->nav_section_interval1 == 0)) ? $settings->nav_section_interval1 : '1,3';
        $nav_section_interval2 = (isset($settings->nav_section_interval2) && ($settings->nav_section_interval2 || $settings->nav_section_interval2 == 0)) ? $settings->nav_section_interval2 : '2,4';


        // 切换翻页
        $change_pagination = (isset($settings->change_pagination) && ($settings->change_pagination || $settings->change_pagination == 0)) ? $settings->change_pagination : 0;
        $pagenation_items = (isset($settings->pagenation_items) && $settings->pagenation_items) ? $settings->pagenation_items : array();
        $need_resize_listener = (isset($settings->need_resize_listener) && ($settings->need_resize_listener || $settings->need_resize_listener == 0)) ? $settings->need_resize_listener : 1;

        // 底部区块的层级
        $footer_z_index = (isset($settings->footer_z_index) && $settings->footer_z_index) ? $settings->footer_z_index : 0;

        $js_swiper = '
            $(window).on("load",function(){
                $("body,html").scrollTop(0);
                $(".jwpf-addon-amap").addClass("swiper-no-swiping");
            })
            var container = $(".jw-page-factory");
            var page = $(".page-content");
            var section = $(".page-content>.jwpf-section");
            var row = $(".jwpf-wow");
            container.addClass("swiper-container").addClass("full-swiper-container");
            page.addClass("swiper-wrapper");
            section.addClass("swiper-slide");
            var nav_section1_arr="'.$nav_section_interval1.'".split(",");
            var nav_section2_arr="'.$nav_section_interval2.'".split(",");
            ';
        if ($need_resize_listener){
            $js_swiper.='
                $(window).on("resize", function(){
                    window.location.reload();
                });
            ';
        }
        if($has_animate == 1) {
            $js_swiper.='
                    $(".swiper-slide .wow").each(function(i,v){
                        saveAnimateClass(i,v,"wow");
                    })
                    $(".swiper-slide .jwpf-wow").each(function(i,v){
                        saveAnimateClass(i,v,"jwpf-wow");
                    })
                ';
        }
        if($last_include == 0){
            $js_swiper.='
                    // section.addClass("swiper-no-swiping");
                    var footer_section;
                ';
            if($set_footer_section_id == 1 && $footer_section_id){
                $js_swiper.='footer_section = $("#'.$footer_section_id.'");';
            }else{
                $js_swiper.='footer_section = section.eq(section.length - 1);';
            }
            $js_swiper.= '
                footer_section.css({"opacity": 0, "z-index": 0});
                var footer_id = footer_section.attr("id");
                $("#"+footer_id).removeClass("swiper-slide");
                $("#"+footer_id).on("mousewheel DOMMouseScroll wheel", function(e) {
                    var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1));
                    if (delta > 0) {
                        $("#jw-page-factory").css({
                            "margin-top":"0"
                        });
                        footer_section.css({
                            opacity: 0, "z-index": 0
                        })
                        $(".full-swiper-pagination").show();
                    }
                    return false;
                });
                $("#jw-page-factory").parent().append(footer_section);';
                if($last_section_id){
                    $js_swiper.='$("#'.$last_section_id.'").on("mousewheel DOMMouseScroll wheel", function(e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1));
                        if (delta < 0) {
                            var footer_height = footer_section.height();
                            var up_height_offset = '.$up_height_offset.';
                            $("#jw-page-factory").css({
                                "margin-top":"-"+(footer_height + (up_height_offset || 0))+"px"
                            });
                            footer_section.css({
                                opacity: 1, "z-index": '.$footer_z_index. '
                            });
                            $(".full-swiper-pagination").hide();
                        }else{
                            if(Number($("#jw-page-factory").css("margin-top").replace("px","")) < 0){
                                $("#jw-page-factory").css({
                                    "margin-top":"0"
                                });
                                footer_section.css({
                                    opacity: 0, "z-index": 0
                                })
                            }else{
                                swiper.slidePrev();
                            }
                            $(".full-swiper-pagination").show();
                        }
                        return false;
                    });';
                }
        }else{
            $js_swiper.='';
        }

        if($nav_section_id && $nav_section_id){
            $js_swiper .= '
            $("#' . $nav_section_id . '").removeClass("swiper-slide");
            $("#' . $nav_section_id . '").removeClass("swiper-no-swiping");
            var nav = $("#' . $nav_section_id . '");';
            if($change_nav == 0){
                $js_swiper .= '
                container.prepend(nav);';
            }else{
                $js_swiper .= '
                container.parent().prepend(nav);';
            }
        }
        if($change_nav == 1){
            if($nav_section_id2){
                $js_swiper .= '
                    $("#' . $nav_section_id2 . '").removeClass("swiper-slide");
                    $("#' . $nav_section_id2 . '").removeClass("swiper-no-swiping");
                    var nav2 = $("#' . $nav_section_id2 . '");
                    // $("#nav").remove();
                    container.parent().prepend(nav2);';
            }
        }

        $js_swiper .= '// 处理加动画不显示问题
//                row.addClass("ani");
//                $(".wow").addClass("ani");

                var pagination = "<!-- Add Pagination --><div class=\'full-swiper-pagination\'></div>";
                var navigation = "<!-- Add Arrows --><div class=\'full-swiper-button-next\'><img src=\'' . $navigation_img . '\'></div>";

                var pagination_hide = ' . $pagination_hide . ';
                var navigation_hide = ' . $navigation_hide . ';
                if(pagination_hide == 0) {
                    container.append(pagination);
                }
                if(navigation_hide == 0) {
                    container.append(navigation);
                }';

                if($editTotal == 1){
                    $js_swiper.='var totalPagination = '.$totalPage.';';
                }else{
                    $js_swiper.='var totalPagination = $(".page-content > .jwpf-section.swiper-slide").length;';
                }

        $js_swiper .= 'var swiper = new Swiper(\'.jw-page-factory\', {
                    direction: \'vertical\',
                    slidesPerView: 1,
                    mousewheel: true,
                    noSwiping: true,
                    pagination: {
                        el: \'.full-swiper-pagination\',
                        clickable: true,';
                        if($pagination_fraction_hide == 1){
                            if($first_include == 1){
                                if($change_pagination == 1){
                                    $js_swiper.='
                                        renderBullet: function (index, className) {
                                            return "<span class=\"swiper-pagination-bullet\" data-index=\"0\" tabindex=\"0\" role=\"button\"><i>" + (index + 1) + "/" + totalPagination + "</i></span>";
                                        }
                                    ';
                                }else{
                                    $js_swiper.='renderBullet: function (index, className) {
                                        return "<span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\"><i>" + (index + 1) + "/" + totalPagination + "</i></span>";
                                    },';
                                }
                            }else{
                                if($change_pagination == 1){
                                    $js_swiper.='
                                        renderBullet: function (index, className) {
                                            return "<span class=\"swiper-pagination-bullet\" data-index=\"0\" tabindex=\"0\" role=\"button\"><i>" + (index) + "/" + totalPagination + "</i></span>";
                                        }
                                    ';
                                }else{
                                    $js_swiper.='renderBullet: function (index, className) {
                                        return "<span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\"><i>" + (index) + "/" + totalPagination + "</i></span>";
                                    },';
                                }
                            }
                        }else{
                            if($change_pagination == 1){
                                $js_swiper.='
                                    renderBullet: function (index, className) {
                                        return "<span class=\"swiper-pagination-bullet\" data-index=\"0\" tabindex=\"0\" role=\"button\"></span>";
                                    }
                                ';
                            }else{

                            }
                        }
                    $js_swiper.='},
                    navigation: {
                        nextEl: \'.full-swiper-button-next\',
                    },
                    on: {
                        init: function() {
                            swiperAnimateCache(this); //隐藏动画元素
                            swiperAnimate(this); //初始化完成开始动画
                            ';
                            // 如果首屏隐藏轮播点
                            if($first_include == 0){
                                $js_swiper.='
                                if(this.realIndex === 0){
                                    $(".full-swiper-pagination").hide();
                                }else{
                                    $(".full-swiper-pagination").show();
                                }';
                            }
                            if($has_animate == 1) {
                                $js_swiper.='
                                    $(".swiper-slide .wow").each(function(i,v){
                                        saveAnimateClass(i,v,"wow");
                                    })
                                    $(".swiper-slide .jwpf-wow").each(function(i,v){
                                        saveAnimateClass(i,v,"jwpf-wow");
                                    })
                                ';
                            }

                            // 间隔切换导航
                            if($interval_change_nav == 1){
                                $js_swiper.='
                                $("#'.$nav_section_id.'").css("opacity", "1");
                                $("#'.$nav_section_id2.'").css("opacity", "0");
                                ';
                            }
                        $js_swiper.='
                        },
                        slideChangeTransitionEnd: function() {
                            swiperAnimateCache(this); //隐藏动画元素
                            swiperAnimate(this); //每个slide切换结束时也运行当前slide动画
                            ';
                            // 如果首屏隐藏轮播点
                            if($first_include == 0){
                                $js_swiper.='
                                if(this.realIndex === 0){
                                    $(".full-swiper-pagination").hide();
                                }else{
                                    $(".full-swiper-pagination").show();
                                }';
                            }
                            // 添加动画
                            if($has_animate == 1) {
                                $js_swiper.='
                                     $(".swiper-slide .wow").each(function(i,v){
                                        saveAnimateClass(i,v,"wow");
                                    })
                                    $(".swiper-slide .jwpf-wow").each(function(i,v){
                                        saveAnimateClass(i,v,"jwpf-wow");
                                    })

                                ';
                            }
                            // 间隔切换导航
                            if($interval_change_nav == 1){
                                $js_swiper.='
                                if(nav_section1_arr.includes(""+(this.realIndex + 1))){
                                    $("#'.$nav_section_id.'").css("opacity", "1");
                                    $("#'.$nav_section_id2.'").css("opacity", "0");
                                }else if(nav_section2_arr.includes(""+(this.realIndex + 1))){
                                    $("#'.$nav_section_id2.'").css("opacity", "1");
                                    $("#'.$nav_section_id.'").css("opacity", "0");
                                }else{
                                    $("#'.$nav_section_id2.'").css("opacity", "0");
                                    $("#'.$nav_section_id.'").css("opacity", "0");
                                }
                                ';
                            }

                            if($change_pagination == 1){
                                $js_swiper.='
                                    var index = this.realIndex;
                                    $(".jw-page-factory.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet").each(function(i,v){
                                        $(v).attr("data-index",index);
                                    })
                                ';
                            }
                        $js_swiper.='
                        },';
                        if($last_include == 0){
                            $js_swiper.='touchEnd: function(swiper,event){
                                if($(swiper.slides[this.realIndex]).attr("id") === "'.$last_section_id.'"){
                                    if(this.touches.diff < 0){
                                        var footer_height = footer_section.height();
                                        var up_height_offset = '.$up_height_offset.';
                                        $("#jw-page-factory").css({
                                            "margin-top":"-"+(footer_height + (up_height_offset || 0))+"px"
                                        });
                                        footer_section.css({
                                            opacity: 1
                                        });
                                        $(".full-swiper-pagination").hide();
                                    }else{
                                        if(Number($("#jw-page-factory").css("margin-top").replace("px","")) < 0){
                                            $("#jw-page-factory").css({
                                                "margin-top":"0"
                                            });
                                            footer_section.css({
                                                opacity: 0
                                            });
                                            swiper.slideTo(this.realIndex);
                                        }
                                        $(".full-swiper-pagination").show();
                                    }
                                }
                            },';
                        }
                    $js_swiper.='}
                });
            ';
        $js_info = '';
        if($pc_hide != 1) {
            $js_info .= '
                if(windowWidth > 991){
                    ' . $js_swiper . ';
                }
            ';
        }else{
            $js_info .= '';
        }
        if($pad_hide != 1) {
            $js_info .= '
                if(windowWidth >= 768 && windowWidth <= 991){
                    ' . $js_swiper . ';
                }
            ';
        }else{
            $js_info .= '';
        }
        if($wap_hide != 1) {
            $js_info .= '
                if(windowWidth < 768){
                    ' . $js_swiper . ';
                }
            ';
        }else{
            $js_info .= '';
        }

        $js = '
            function saveAnimateClass(i,v,className){
                let classArr = $(v).attr("class").split(" ");
                $(v).data("class-orginal",$(v).attr("class"));
                classArr.forEach(function(value,index,arr){
                    if(value === className){
                        $(v).attr("swiper-animate-effect",arr[index + 1]);
                        $(v).attr("swiper-animate-duration",$(v).css("animation-duration"));
                        $(v).attr("swiper-animate-delay",$(v).css("animation-delay"));
                        $(v).attr("swiper-animate-delay",$(v).css("animation-delay"));
                        $(v).addClass("ani");
                        $(v).removeClass("wow");
                        $(v).removeClass("jwpf-wow");
                        $(v).css({"visibility": "",
                        "animation-delay": "",
                        "animation-name": "",
                        "animation-duration": ""});
                        $(v).removeClass("jwpf-");
                        $(v).removeAttr("data-jwpf-wow-duration");
                        $(v).removeAttr("data-jwpf-wow-delay");
                    }
                })
            }
            function saveAnimateClass1(i,v,className){
                let classArr = $(v).attr("class").split(" ");
                $(v).data("class-orginal",$(v).attr("class"));
                classArr.forEach(function(value,index,arr){
                    if(value === className){
                        $(v).data("class",className).data("animateName",arr[index + 1]);
                    }
                })
            }
            function removeAnimateClass(i,v,className){
                $(v).css({"animation-name":"none"});
            }
            function addAnimateClass(i,v,className){
                let orginalClass = $(v).data("class-orginal").split(" ").filter(function(v1){
                    return v1!=="";
                });
                let nowClass = $(v).attr("class").split(" ").filter(function(v2){
                    return v2!==""
                });
                if(orginalClass.length > 0){
                    nowClass.map(function(v3){
                        if(orginalClass.indexOf(v3) === -1){
                            $(v).addClass(v3);
                        }
                    })
                }
                $(v).css({"animation-name":$(v).data("animateName")}).css("visibility", "visible");
            }
            jQuery(document).ready(function($){
                var windowWidth = document.body.clientWidth;
                if(windowWidth > 991){
                    $(".jwpf-section.jwpf-hidden-md").remove();
                }else if(windowWidth >= 768 && windowWidth <= 991){
                    $(".jwpf-section.jwpf-hidden-sm").remove();
                }else if(windowWidth < 768){
                    $(".jwpf-section.jwpf-hidden-xs").remove();
                }

                ' . $js_info . '
            })
        ';

        return $js;
    }
    public function scripts() {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js',
            JURI::base(true) . '//webapi.amap.com/maps?v=2.0&&key=509ed5f612c9e136d02124d1501da3eb',
        );
        return $js;
    }
    public function stylesheets() {
        $style_sheet = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
            JURI::base(true) . '/components/com_jwpagefactory/addons/full_page/assets/css/animate.min.css',
        );

        return $style_sheet;
    }
    public static function getTemplate() {
        $output = '
        <#

        #>
            <div style="height: 100px">本段文字用于智能全屏滚动插件编辑模式下占位，预览模式下不显示。<strong>如果在预览页面发现有插件不显示，有可能是插件中包含有动画，请开启“是否包含动画”开关，以便页面正常显示</strong></div>
        ';
        return $output;
    }
}
