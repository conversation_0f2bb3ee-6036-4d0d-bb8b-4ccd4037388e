<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 14:00:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonBusiness_bridge extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $yuming=$_SERVER['HTTP_HOST'];
        if($yuming=='jzt_dev_2.china9.cn'){
            $urlpath='http://jzt_dev_1.china9.cn';
        }elseif($yuming=='ijzt.china9.cn'){
            $urlpath='https://zhjzt.china9.cn';
        }else{
            $config = new JConfig();
            $urlpath = $config->jzt_url;
        }


        // 指定文章详情页ID
        $url='';
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        //include k2 helper
        $k2helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/k2.php';
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        // $isk2installed = self::isComponentInstalled('com_k2');

//  if ($resource == 'k2') {
//  if ($isk2installed == 0) {
//    $output .= '<p class="alert alert-danger">' . JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_ERORR_K2_NOTINSTALLED') . '</p>';
//    return $output;
//  } elseif (!file_exists($k2helper)) {
//    $output .= '<p class="alert alert-danger">' . JText::_('COM_JWPAGEFACTORY_ADDON_K2_HELPER_FILE_MISSING') . '</p>';
//    return $output;
//  } else {
//    require_once $k2helper;
//  }
//  $items = JwpagefactoryHelperK2::getItemsList($limit, $ordering, $k2catid, $include_subcat, $page);
//  $items_count = JwpagefactoryHelperK2::getItemsCount($ordering, $k2catid, $include_subcat);
//  } else {
//  require_once $article_helper;
//  $items = JwpagefactoryHelperArticles::getArticlesList($limit, $ordering, $catid, $include_subcat, $post_type, $tagids,$detail_page_id, $page, $company_id, $layout_id,$site_id);
//        $items_count = JwpagefactoryHelperArticles::getArticlesCount($ordering, $k2catid, $include_subcat, $company_id, $site_id);
//  }


//  if (count((array)$items)) {
//  }
//        $output .='<script src="https://hm.baidu.com/hm.js?e0f423dda76a514cd2c4eff74b55ff96"></script>';

        $output .= '<script>
                                
                                           
                        jQuery.ajax({
                            type: "POST",

                            url: "' . $urlpath . '/api/Shangqiao/index",
                            dataType: "json",
                            data: {
                                "company_id" :' . $company_id . ',
                                "site_id" :' . $site_id . ',
                                //"company_id" :"167",
                                // "site_id" :"338",
                            },
                            success: function (res) {
                                let stri = res.data
                                if(stri){
                                    console.log(stri)
                                    let strpath = stri.indexOf("https") == -1 ? "" :stri.substring((stri.indexOf("https")), stri.indexOf("var s")-5);
                                    console.log(strpath)
                                    var hm= document.createElement("script");
                                    hm.onload = hm.onreadystatechange = function() {
                                        if (!this.readyState || this.readyState == "loaded" || this.readyState == "complete" ) {
                                            hm.onload = hm.onreadystatechange = null;
                                        }
                                    };
                                    hm.src = strpath;
                                    jQuery("' . $addon_id . '").append(stri);
                                }
                               
                            }     
                        });
        </script>';

        return $output;
    }

    public function css()
    {
        $css = '';
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $prod_id = '#jwpf-addon-wrapper-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        // $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';
        $css .= $prod_id . '{ ';
        $css .= 'margin:0px !important';
        $css .= '}';
        return $css;
        // return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        return '<div>本段文字用于编辑模式下占位，预览模式下不显示</div>';
    }
}