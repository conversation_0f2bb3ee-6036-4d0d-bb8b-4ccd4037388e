<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonCard extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $items=$settings->item;
        $card_icon=(isset($settings->card_icon) && $settings->card_icon)?$settings->card_icon:'/components/com_jwpagefactory/addons/card/assets/images/img.png';
        $card_icon_active=(isset($settings->card_icon_active) && $settings->card_icon_active)?$settings->card_icon_active:'/components/com_jwpagefactory/addons/card/assets/images/img_1.png';

        $output='<div class="tab">';
        for ($i=0;$i<count($items);$i++){
            if ($i===0){
                $output.='<div class="topitem active" id="'.$i.'">';
            }else{
                $output.='<div class="topitem" id="'.$i.'">';
            }


            $output.='
            <div class="choosetop">';
            if ($i===0){
                $output.='<img src="'.$card_icon_active.'" class="choose">';
            }else{
                $output.='<img src="'.$card_icon.'" class="choose">';
            }
            $output.='
                  <text>'.$items[$i]->title.'</text>
                </div>
                <div class="choosecont" >
                  '.$items[$i]->content.'
                </div>
              </div>';
        }



        return $output;

    }

    public function css()
    {
        $Id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $card_width = (isset($settings->card_width) && $settings->card_width)?$settings->card_width:'228';
        $card_width_sm = (isset($settings->card_width_sm) && $settings->card_width_sm)?$settings->card_width_sm:'228';
        $card_width_xs = (isset($settings->card_width_xs) && $settings->card_width_xs)?$settings->card_width_xs:'228';
        $card_height = (isset($settings->card_height) && $settings->card_height)?$settings->card_height:'308';
        $card_height_sm = (isset($settings->card_height_sm) && $settings->card_height_sm)?$settings->card_height_sm:'308';
        $card_height_xs = (isset($settings->card_height_xs) && $settings->card_height_xs)?$settings->card_height_xs:'308';
        $card_padding = (isset($settings->card_padding) && $settings->card_padding)?$settings->card_padding:'24px 28px 24px 28px';
        $card_padding_sm = (isset($settings->card_padding_sm) && $settings->card_padding_sm)?$settings->card_padding_sm:'24px 28px 24px 28px';
        $card_padding_xs = (isset($settings->card_padding_xs) && $settings->card_padding_xs)?$settings->card_padding_xs:'24px 28px 24px 28px';
        $card_radius = (isset($settings->card_radius) && $settings->card_radius)?$settings->card_radius:'8';
        $card_margin_right = (isset($settings->card_margin_right) && $settings->card_margin_right)?$settings->card_margin_right:'22';
        $card_margin_right_sm = (isset($settings->card_margin_right_sm) && $settings->card_margin_right_sm)?$settings->card_margin_right_sm:'22';
        $card_margin_right_xs = (isset($settings->card_margin_right_xs) && $settings->card_margin_right_xs)?$settings->card_margin_right_xs:'22';
        $card_title_size = (isset($settings->card_title_size) && $settings->card_title_size)?$settings->card_title_size:'16';
        $card_title_size_sm = (isset($settings->card_title_size_sm) && $settings->card_title_size_sm)?$settings->card_title_size_sm:'16';
        $card_title_size_xs = (isset($settings->card_title_size_xs) && $settings->card_title_size_xs)?$settings->card_title_size_xs:'16';
        $card_title_margin_bottom = (isset($settings->card_title_margin_bottom) && $settings->card_title_margin_bottom)?$settings->card_title_margin_bottom:'5';
        $card_title_margin_bottom_sm = (isset($settings->card_title_margin_bottom_sm) && $settings->card_title_margin_bottom_sm)?$settings->card_title_margin_bottom_sm:'5';
        $card_title_margin_bottom_xs = (isset($settings->card_title_margin_bottom_xs) && $settings->card_title_margin_bottom_xs)?$settings->card_title_margin_bottom_xs:'5';
        $card_font_size = (isset($settings->card_font_size) && $settings->card_font_size)?$settings->card_font_size:'12';
        $card_font_size_sm = (isset($settings->card_font_size_sm) && $settings->card_font_size_sm)?$settings->card_font_size_sm:'12';
        $card_font_size_xs= (isset($settings->card_font_size_xs) && $settings->card_font_size_xs)?$settings->card_font_size_xs:'12';
        $card_background= (isset($settings->card_background) && $settings->card_background)?$settings->card_background:'rgb(229, 232, 236)';
        $card_background_active= (isset($settings->card_background_active) && $settings->card_background_active)?$settings->card_background_active:'rgb(238, 120, 0)';
        $card_title_color= (isset($settings->card_title_color) && $settings->card_title_color)?$settings->card_title_color:'rgba(64,64,64,1)';
        $card_title_color_active= (isset($settings->card_title_color_active) && $settings->card_title_color_active)?$settings->card_title_color_active:'white';
        $card_content_color= (isset($settings->card_content_color) && $settings->card_content_color)?$settings->card_content_color:'rgba(64,64,64,1)';
        $card_content_color_active= (isset($settings->card_content_color_active) && $settings->card_content_color_active)?$settings->card_content_color_active:'white';
        $card_icon_size= (isset($settings->card_icon_size) && $settings->card_icon_size)?$settings->card_icon_size:'16';
        $card_icon_size_sm= (isset($settings->card_icon_size_sm) && $settings->card_icon_size_sm)?$settings->card_icon_size_sm:'16';
        $card_icon_size_xs= (isset($settings->card_icon_size_xs) && $settings->card_icon_size_xs)?$settings->card_icon_size_xs:'16';
        $card_left_margin = (isset($settings->card_left_margin) && $settings->card_left_margin)?$settings->card_left_margin:'10';
        $card_left_margin_sm = (isset($settings->card_left_margin_sm) && $settings->card_left_margin_sm)?$settings->card_left_margin_sm:'10';
        $card_left_margin_xs = (isset($settings->card_left_margin_xs) && $settings->card_left_margin_xs)?$settings->card_left_margin_xs:'5';



        $css=''.$Id.' .tab{
              width: 100%;
              overflow: hidden;
            }
            '.$Id.' .topitem{
              margin: 0;
              width: '.$card_width.'px;
              height: '.$card_height.'px;
              padding: '.$card_padding.';
              border-radius: '.$card_radius.'px;
              float: left;
              margin-right: '.$card_margin_right.'px;
              cursor: pointer;
              color: white;
              background: '.$card_background.';
              overflow: hidden;
            }
            '.$Id.' .topitem *{
                pointer-events: none;
            }
            '.$Id.' .topitem.active{
              background: '.$card_background_active.';
            }
            '.$Id.' .topitem.active text{
              color: '.$card_title_color_active.';
            }
            '.$Id.' .topitem.active .choosecont{
              color: '.$card_content_color_active.';
            }
            '.$Id.' .choosecont{
              height:calc(100% - '.$card_title_size.'px - '.$card_title_margin_bottom.'px);
              font-size: '.$card_font_size.'px;
              color: '.$card_content_color.';
              overflow:hidden;
            }
            '.$Id.' .choosetop{
                margin-bottom: '.$card_title_margin_bottom.'px;
                display:flex;
                align-items:center;
            }
            '.$Id.' text {
                cursor: pointer;
                font-size: '.$card_title_size.'px;
                font-weight: 400;
                color: '.$card_title_color.';
                margin-left: '.$card_left_margin.'px;
            }
            '.$Id.' .choose{
                width: '.$card_icon_size.'px;
                display: inline-block;
            }
            @media (min-width: 767px) and (max-width: 991px) {
                '.$Id.' .topitem{
                  width: '.$card_width_sm.'px;
                  height: '.$card_height_sm.'px;
                  padding: '.$card_padding_sm.';
                  margin-right: '.$card_margin_right_sm.'px;
                }
                '.$Id.' text {
                    cursor: pointer;
                    font-size: '.$card_title_size_sm.'px;
                    font-weight: 400;
                    color: white;
                    margin-left: '.$card_left_margin_sm.'px;
                }
                '.$Id.' .choosetop{
                    margin-bottom: '.$card_title_margin_bottom_sm.'px;
                }
                '.$Id.' .choosecont{
                    font-size: '.$card_font_size_sm.'px;
                }
                '.$Id.' .choose{
                    width: '.$card_icon_size_sm.'px;
                    height: '.$card_icon_size_sm.'px;
                    display: inline-block;
                }
            }
            @media (max-width: 768px) {
                '.$Id.' .topitem{
                  width: '.$card_width_xs.'px;
                  height: '.$card_height_xs.'px;
                  padding: '.$card_padding_xs.';
                  margin-right: '.$card_margin_right_xs.'px;
                }
                '.$Id.' text {
                    cursor: pointer;
                    font-size: '.$card_title_size_xs.'px;
                    font-weight: 400;
                    color: white;
                    margin-bottom: '.$card_title_margin_bottom_xs.'px;
                    margin-left: '.$card_left_margin_xs.'px;
                }
                '.$Id.' .choosecont{
                    font-size: '.$card_font_size_xs.'px;
                }
                '.$Id.' .choose{
                    width: '.$card_icon_size_xs.'px;
                    height: '.$card_icon_size_xs.'px;
                    display: inline-block;
                }
                '.$Id.' .choosetop{
                    margin-bottom: '.$card_title_margin_bottom_xs.'px;
                }
            }';
        return $css;
    }

    public static function style(){
        $output='
        <#
            let Id = "#jwpf-addon-"+data.id;
//            卡片宽
            let card_width=(_.isObject(data.card_width)&&data.card_width)?data.card_width.md:data.card_width;
            let card_width_sm=(_.isObject(data.card_width)&&data.card_width)?data.card_width.sm:data.card_width;
            let card_width_xs=(_.isObject(data.card_width)&&data.card_width)?data.card_width.xs:data.card_width;
//            卡片高
            let card_height=(_.isObject(data.card_height)&&data.card_height)?data.card_height.md:data.card_height;
            let card_height_sm=(_.isObject(data.card_height)&&data.card_height)?data.card_height.sm:data.card_height;
            let card_height_xs=(_.isObject(data.card_height)&&data.card_height)?data.card_height.xs:data.card_height;
//            内边距
            let card_padding=(_.isObject(data.card_padding)&&data.card_padding)?data.card_padding.md:data.card_padding;
            let card_padding_sm=(_.isObject(data.card_padding)&&data.card_padding)?data.card_padding.sm:data.card_padding;
            let card_padding_xs=(_.isObject(data.card_padding)&&data.card_padding)?data.card_padding.xs:data.card_padding;          
//            圆角
            let card_radius=(_.isObject(data.card_radius)&&data.card_radius)?data.card_radius:data.card_radius;
//            右边距
            let card_margin_right=(_.isObject(data.card_margin_right)&&data.card_margin_right)?data.card_margin_right.md:data.card_margin_right;
            let card_margin_right_sm=(_.isObject(data.card_margin_right)&&data.card_margin_right)?data.card_margin_right.sm:data.card_margin_right;
            let card_margin_right_xs=(_.isObject(data.card_margin_right)&&data.card_margin_right)?data.card_margin_right.xs:data.card_margin_right;
//            标题字体大小
            let card_title_size=(_.isObject(data.card_title_size)&&data.card_title_size)?data.card_title_size.md:data.card_title_size;
            let card_title_size_sm=(_.isObject(data.card_title_size)&&data.card_title_size)?data.card_title_size.sm:data.card_title_size;
            let card_title_size_xs=(_.isObject(data.card_title_size)&&data.card_title_size)?data.card_title_size.xs:data.card_title_size;
//            标题下边距
            let card_title_margin_bottom=(_.isObject(data.card_title_margin_bottom)&&data.card_title_margin_bottom)?data.card_title_margin_bottom.md:data.card_title_margin_bottom;
            let card_title_margin_bottom_sm=(_.isObject(data.card_title_margin_bottom)&&data.card_title_margin_bottom)?data.card_title_margin_bottom.sm:data.card_title_margin_bottom;
            let card_title_margin_bottom_xs=(_.isObject(data.card_title_margin_bottom)&&data.card_title_margin_bottom)?data.card_title_margin_bottom.xs:data.card_title_margin_bottom;
//          卡片标题左边距
            let card_left_margin=(_.isObject(data.card_left_margin)&&data.card_left_margin)?data.card_left_margin.md:data.card_left_margin;
            let card_left_margin_sm=(_.isObject(data.card_left_margin)&&data.card_left_margin)?data.card_left_margin.sm:data.card_left_margin;
            let card_left_margin_xs=(_.isObject(data.card_left_margin)&&data.card_left_margin)?data.card_left_margin.xs:data.card_left_margin;
//             正文字体
            let card_font_size=(_.isObject(data.card_font_size)&&data.card_font_size)?data.card_font_size.md:data.card_font_size;
            let card_font_size_sm=(_.isObject(data.card_font_size)&&data.card_font_size)?data.card_font_size.sm:data.card_font_size;
            let card_font_size_xs=(_.isObject(data.card_font_size)&&data.card_font_size)?data.card_font_size.xs:data.card_font_size;
//            图标
            let card_icon_size=(_.isObject(data.card_icon_size)&&data.card_icon_size)?data.card_icon_size.md:data.card_icon_size;
            let card_icon_size_sm=(_.isObject(data.card_icon_size)&&data.card_icon_size)?data.card_icon_size.sm:data.card_icon_size;
            let card_icon_size_xs=(_.isObject(data.card_icon_size)&&data.card_icon_size)?data.card_icon_size.xs:data.card_icon_size;
//            正常状态
//            背景色
            let card_background=(_.isObject(data.card_background)&&data.card_background)?data.card_background:data.card_background;
//            标题字体色
            let card_title_color=(_.isObject(data.card_title_color)&&data.card_title_color)?data.card_title_color:data.card_title_color;
//            正文字体色
            let card_content_color=(_.isObject(data.card_content_color)&&data.card_content_color)?data.card_content_color:data.card_content_color;
//          选中状态
//            背景色
            let card_background_active=(_.isObject(data.card_background_active)&&data.card_background_active)?data.card_background_active:data.card_background_active;
//            标题字体色
            let card_title_color_active=(_.isObject(data.card_title_color_active)&&data.card_title_color_active)?data.card_title_color_active:data.card_title_color_active;
//            正文字体色
            let card_content_color_active=(_.isObject(data.card_content_color_active)&&data.card_content_color_active)?data.card_content_color_active:data.card_content_color_active;
        #>
        <style>
            {{Id}} .tips{
                height: 40px;
                line-height: 40px;
                margin-bottom: 30px;
                background: rgba(255,141,115,0.88);
                box-sizing: border-box;
                padding: 0 10px;
            }
            {{Id}} .tab{
              width: 100%;
              overflow: hidden;
            }
            {{Id}} .topitem{
              margin: 0;
              width: {{card_width}}px;
              height: {{card_height}}px;
              padding: {{card_padding}};
              border-radius: {{card_radius}}px;
              float: left;
              margin-right: {{card_margin_right}}px;
              cursor: pointer;
              color: white;
              background: {{card_background}};
              overflow: hidden;
            }
            {{Id}} .topitem *{
                pointer-events: none;
            }
            {{Id}} .topitem.active{
              background: {{card_background_active}};
            }
            {{Id}} .topitem.active text{
              color: {{card_title_color_active}};
            }
            {{Id}} .topitem.active .choosecont{
              color: {{card_content_color_active}};
            }
            {{Id}} .choosecont{
              height:calc(100% - {{card_title_size}}px - {{card_title_margin_bottom}}px);
              font-size: {{card_font_size}}px;
              color: {{card_content_color}};
              overflow:hidden;
            }
            {{Id}} .choosetop{
                margin-bottom: {{card_title_margin_bottom}}px;
                display:flex;
                align-items:center;
            }
            {{Id}} text {
                cursor: pointer;
                font-size: {{card_title_size}}px;
                font-weight: 400;
                color: {{card_title_color}};
                margin-left: {{card_left_margin}}px;
            }
            {{Id}} .choose{
                width: {{card_icon_size}}px;
                display: inline-block;
                height:{{card_icon_size}}px
            }
            @media (min-width: 767px) and (max-width: 991px) {
                {{Id}} .topitem{
                  width: {{card_width_sm}}px;
                  height: {{card_height_sm}}px;
                  padding: {{card_padding_sm}};
                  margin-right: {{card_margin_right_sm}}px;
                }
                {{Id}} text {
                    cursor: pointer;
                    font-size: {{card_title_size_sm}}px;
                    font-weight: 400;
                    color: white;
                    margin-left: {{card_left_margin_sm}}px;
                }
                {{Id}} .choosetop{
                    margin-bottom: {{card_title_margin_bottom_sm}}px;
                }
                {{Id}} .choosecont{
                    font-size: {{card_font_size_sm}}px;
                }
                {{Id}} .choose{
                    width: {{card_icon_size_sm}}px;
                    height: {{card_icon_size_sm}}px;
                    display: inline-block;
                }
            }
            @media (max-width: 768px) {
                {{Id}} .topitem{
                  width: {{card_width_xs}}px;
                  height: {{card_height_xs}}px;
                  padding: {{card_padding_xs}};
                  margin-right: {{card_margin_right_xs}}px;
                }
                {{Id}} text {
                    cursor: pointer;
                    font-size: {{card_title_size_xs}}px;
                    font-weight: 400;
                    color: white;
                    margin-bottom: {{card_title_margin_bottom_xs}}px;
                    margin-left: {{card_left_margin_xs}}px;
                }
                {{Id}} .choosecont{
                    font-size: {{card_font_size_xs}}px;
                }
                {{Id}} .choose{
                    width: {{card_icon_size_xs}}px;
                    height: {{card_icon_size_xs}}px;
                    display: inline-block;
                }
                {{Id}} .choosetop{
                    margin-bottom: {{card_title_margin_bottom_xs}}px;
                }
            }
        </style>';
        return $output;
    }
    public function js()
    {
        $Id = '#jwpf-addon-' . $this->addon->id;
        $card_icon=(isset($settings->card_icon) && $settings->card_icon)?$settings->card_icon:'/components/com_jwpagefactory/addons/card/assets/images/chooseout.png';
        $card_icon_active=(isset($settings->card_icon_active) && $settings->card_icon_active)?$settings->card_icon_active:'/components/com_jwpagefactory/addons/card/assets/images/img_1.png';

        $js = "jQuery(function($){
            $('".$Id." .tab').click(function(e){
                let img=$(e.target).find('.choose')[0];
                $('".$Id." .topitem').each(function(i,v){
                    $(v).removeClass('active');
                    $('".$Id." .choose').attr('src','".$card_icon."');
                })
                if($(e.target).is('.topitem')){
                    $(e.target).addClass('active');
                    $(img).attr('src','".$card_icon_active."');
                };
            })
        })";
        return $js;
    }
    public static function getTemplate()
    {
        $output='
            <# 
                let card_icon=(_.isObject(data.card_icon)&&data.card_icon)?data.card_icon:data.card_icon;
                let card_icon_active=(_.isObject(data.card_icon_active)&&data.card_icon_active)?data.card_icon_active:data.card_icon_active;
            #>
            <p class="tips">该样式仅为布局样式，请在预览页面查看切换效果</p>
            <div class="tab">
            <# _.each(data.item,function(item,key){ #>
                <div class="topitem {{key===0?"active":""}}" id="{{key}}">
                <div class="choosetop">
                  <img src=\'{{card_icon}}\' class="choose">
                  <text>{{item.title}}</text>
                </div>
                <div class="choosecont" >
                  {{item.content}}
                </div>
              </div>
            <# }) #>
            
              <!--<div class="topitem active">
                <div class="choosetop">
                  <img src=\'{{card_icon}}\' class="choose">
                  <text>馆+综合版</text>
                </div>
                <div class="choosecont" >
                  垂直于跆拳道行业更完善的CRM管理系统，
                  提高潜在用户付费转化率 *********统计馆内的日常运营状态，
                  会员消课状况，日常营业，
                  及客户转化 完整的会员课程表，
                  学员日常数据更加清晰明确 移动端和PC端协作办公，
                  时刻管理你的员工和会员动态
                </div>
              </div>
              <div class="topitem">
                <div class="choosetop">
                  <img src=\'{{card_icon}}\' class="choose">
                  <text>馆+综合版</text>
                </div>
                <div class="choosecont" >
                  垂直于跆拳道行业更完善的CRM管理系统，
                  提高潜在用户付费转化率 *********统计馆内的日常运营状态，
                  会员消课状况，日常营业，
                  及客户转化 完整的会员课程表，
                  学员日常数据更加清晰明确 移动端和PC端协作办公，
                  时刻管理你的员工和会员动态
                </div>
              </div>
              <div class="topitem">
                <div class="choosetop">
                  <img src=\'{{card_icon}}\' class="choose">
                  <text>馆+综合版</text>
                </div>
                <div class="choosecont" >
                  垂直于跆拳道行业更完善的CRM管理系统，
                  提高潜在用户付费转化率 *********统计馆内的日常运营状态，
                  会员消课状况，日常营业，
                  及客户转化 完整的会员课程表，
                  学员日常数据更加清晰明确 移动端和PC端协作办公，
                  时刻管理你的员工和会员动态
                </div>
              </div>
            </div>-->
';

        return self::style().$output;
    }

}