<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'jw_carousel',
		'category' => '轮播',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_DESC'),
		'attr' => array(
			'general' => array(

				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),

				'lbys' => array(
					'type' => 'select',
					'title' => JText::_('轮播布局'),
					'values' => array(
						'lb1' => '常用轮播',
						'lb2' => '进场动画轮播',
						'lb3' => '缩小动画轮播',
						'lb4' => '百叶窗动画轮播',
						'lb5' => '布局5',
					),
					'std' => 'lb1'
				),

				//repeatable
				'jw_carousel_item' => array(
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
					'attr' => array(
						'equipment' => array(
							'type' => 'buttons',
							'title' => '轮播项设置',
							'std' => 'img_title',
							'values' => array(
								array(
									'label' => '图片/标题',
									'value' => 'img_title'
								),
								array(
									'label' => '内容',
									'value' => 'ban_cont'
								),
								array(
									'label' => '按钮',
									'value' => 'ban_but'
								),
							),
							'tabs' => true,
						),

						// 2021.09.16 新增客户端获取
						//轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端获取'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'zu'),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),

						// 2021.09.16 新增客户端获取

						'title' => array(
							'type' => 'text',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE'),
							'std' => '这里可以填写一个名称',
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'title_font_family' => array(
							'type' => 'fonts',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_FONT_FAMILY'),
							'depends' => array(
								array('title', '!=', ''),
								array('equipment', '=', 'img_title'),
							),
							'selector' => array(
								'type' => 'font',
								'font' => '{{ VALUE }}',
								'css' => ' h2 { font-family: "{{ VALUE }}"; }'
							)
						),

						'title_fontsize' => array(
							'type' => 'slider',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_FONTSIZE'),
							'max' => 100,
							'std' => array('md' => 46, 'sm' => 36, 'xs' => 16),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'title_lineheight' => array(
							'type' => 'slider',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_LINEHEIGHT'),
							'max' => 100,
							'std' => array('md' => 56, 'sm' => 46, 'xs' => 20),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'title_color' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_COLOR'),
							'std' => '#fff',
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'title_padding' => array(
							'type' => 'padding',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_PADDING'),
							'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'title_margin' => array(
							'type' => 'margin',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE_MARGIN'),
							'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						'bg' => array(
							'type' => 'media',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
								array('media_video', '!=', 1),
							)
						),
						// 添加视频
						'media_video' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用视频'),
							'desc' => JText::_('开启即可上传banner视频,不会再显示banner图片'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),
						'slider_video' => array(
							'type' => 'media',
							'format' => 'video',
							'title' => JText::_('上传banner视频'),
							'desc' => JText::_('上传banner视频'),
							'depends' => array(
								array('media_video', '=', 1),
								array('equipment', '=', 'ban_cont'),

							),
						),
						//						 'video_height' => array(
						//						 	'type' => 'slider',
						//						 	'title' => JText::_('视频高度'),
						//						 	'max' => 2000,
						//						 	'std' => array('md' => 600, 'sm' => 500, 'xs' => 300),
						//						 	'responsive' => true,
						//						 	'depends' => array(
						//						 		array('media_video', '=', 1),
						//						 	),
						//						 ),
						'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'desc' => JText::_('开启可设置图片点击跳转地址'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 新加跳转的链接地址
						'media_url' => array(
							'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'external_links'),
							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),
						// 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => JText::_('链接跳转方式'),
							'desc' => JText::_('链接跳转方式'),
							'values' => array(
								'' => JText::_('当前页面'),
								'_blank' => JText::_('新窗口'),
							),
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),

							)
						),

						'content' => array(
							'type' => 'editor',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_DESC'),
							'std' => '这里你可以随意填写一些内容',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'ban_cont'),

							)
						),

						'content_font_family' => array(
							'type' => 'fonts',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_FONT_FAMILY'),
							'depends' => array(
								array('content', '!=', ''),
								array('equipment', '=', 'ban_cont'),

							),
							'selector' => array(
								'type' => 'font',
								'font' => '{{ VALUE }}',
								'css' => ' .jwpf-carousel-pro-text .jwpf-carousel-content { font-family: "{{ VALUE }}"; }'
							)
						),

						'content_fontsize' => array(
							'type' => 'slider',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_FONTSIZE'),
							'max' => 100,
							'std' => array('md' => 16, 'sm' => 14, 'xs' => 12),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),

						'content_lineheight' => array(
							'type' => 'slider',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_LINEHEIGHT'),
							'max' => 100,
							'std' => array('md' => 24, 'sm' => 22, 'xs' => 16),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),

						'content_color' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_COLOR'),
							'std' => '#fff',
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),

						'content_padding' => array(
							'type' => 'padding',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_CONTENT_PADDING'),
							'std' => array('md' => '20px 0px 30px 0px', 'sm' => '15px 0px 20px 0px', 'xs' => '10px 0px 10px 0px'),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),

						'content_margin' => array(
							'type' => 'margin',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CONTENT_MARGIN'),
							'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
							'responsive' => true,
							'depends' => array(
								array('equipment', '=', 'ban_cont'),
							),
						),

						// button
						'button_text' => array(
							'type' => 'text',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_TEXT'),
							'desc' => JText::_('按钮显示的前提是必须设置轮播标题或内容'),
							'std' => '按钮',
							'depends' => array(
								array('equipment', '=', 'ban_but'),
							)
						),

						'button_font_family' => array(
							'type' => 'fonts',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_BUTTON_FONT_FAMILY'),
							'selector' => array(
								'type' => 'font',
								'font' => '{{ VALUE }}',
								'css' => '.jwpf-carousel-pro-text .jwpf-btn { font-family: "{{ VALUE }}"; }'
							),
							'depends' => array(
								array('equipment', '=', 'ban_but'),
								array('button_text', '!=', ''),
							)
						),

						'button_font_style' => array(
							'type' => 'fontstyle',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_FONT_STYLE'),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_letterspace' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_LETTER_SPACING'),
							'values' => array(
								'0' => 'Default',
								'1px' => '1px',
								'2px' => '2px',
								'3px' => '3px',
								'4px' => '4px',
								'5px' => '5px',
								'6px' => '6px',
								'7px' => '7px',
								'8px' => '8px',
								'9px' => '9px',
								'10px' => '10px'
							),
							'std' => '0',
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),
							)
						),

						'tz_page_type2' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'ban_but'),
							),
						),

						'button_url' => array(
							'type' => 'media',
							'format' => 'attachment',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_URL_DESC'),
							'placeholder' => 'http://',
							'hide_preview' => true,
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),
								array('tz_page_type2', '=', 'external_links'),
							)
						),
						'detail_page_id2' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),

						'button_target' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
							'values' => array(
								'' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
								'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
							),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),
							)
						),

						'button_type' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_STYLE_DESC'),
							'values' => array(
								'default' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DEFAULT'),
								'primary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_PRIMARY'),
								'secondary' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SECONDARY'),
								'success' => JText::_('COM_JWPAGEFACTORY_GLOBAL_SUCCESS'),
								'info' => JText::_('COM_JWPAGEFACTORY_GLOBAL_INFO'),
								'warning' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WARNING'),
								'danger' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DANGER'),
								'dark' => JText::_('COM_JWPAGEFACTORY_GLOBAL_DARK'),
								'link' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK'),
								'custom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CUSTOM'),
							),
							'std' => 'success',
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_appearance' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_DESC'),
							'values' => array(
								'' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_FLAT'),
								'gradient' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_GRADIENT'),
								'outline' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_OUTLINE'),
								'3d' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_APPEARANCE_3D'),
							),
							'std' => 'flat',
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_status' => array(
							'type' => 'buttons',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ENABLE_BACKGROUND_OPTIONS'),
							'std' => 'normal',
							'values' => array(
								array(
									'label' => 'Normal',
									'value' => 'normal'
								),
								array(
									'label' => 'Hover',
									'value' => 'hover'
								),
							),
							'tabs' => true,
							'depends' => array(
								array('button_type', '=', 'custom'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_background_color' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_DESC'),
							'std' => '#444444',
							'depends' => array(
								array('button_appearance', '!=', 'gradient'),
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'normal'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_background_gradient' => array(
							'type' => 'gradient',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
							'std' => array(
								"color" => "#B4EC51",
								"color2" => "#429321",
								"deg" => "45",
								"type" => "linear"
							),
							'depends' => array(
								array('button_appearance', '=', 'gradient'),
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'normal'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_color' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_DESC'),
							'std' => '#fff',
							'depends' => array(
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'normal'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_background_color_hover' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BACKGROUND_COLOR_HOVER_DESC'),
							'std' => '#222',
							'depends' => array(
								array('button_appearance', '!=', 'gradient'),
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'hover'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_background_gradient_hover' => array(
							'type' => 'gradient',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BACKGROUND_GRADIENT'),
							'std' => array(
								"color" => "#429321",
								"color2" => "#B4EC51",
								"deg" => "45",
								"type" => "linear"
							),
							'depends' => array(
								array('button_appearance', '=', 'gradient'),
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'hover'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_color_hover' => array(
							'type' => 'color',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_COLOR_HOVER_DESC'),
							'std' => '#fff',
							'depends' => array(
								array('button_type', '=', 'custom'),
								array('button_status', '=', 'hover'),
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_size' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DESC'),
							'values' => array(
								'' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_DEFAULT'),
								'lg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_LARGE'),
								'xlg' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_XLARGE'),
								'sm' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_SMALL'),
								'xs' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SIZE_EXTRA_SAMLL'),
							),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_shape' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_DESC'),
							'values' => array(
								'rounded' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUNDED'),
								'square' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_SQUARE'),
								'round' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_SHAPE_ROUND'),
							),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_block' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_BLOCK_DESC'),
							'values' => array(
								'' => JText::_('JNO'),
								'jwpf-btn-block' => JText::_('JYES'),
							),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_icon' => array(
							'type' => 'icon',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON'),
							'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_DESC'),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

						'button_icon_position' => array(
							'type' => 'select',
							'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BUTTON_ICON_POSITION'),
							'values' => array(
								'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
								'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
							),
							'depends' => array(
								array('button_text', '!=', ''),
								array('equipment', '=', 'ban_but'),

							)
						),

					),
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					)
				),
				// 布局4
				'jw_carousel_item04' => array(
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
					'attr' => array(
						'equipment' => array(
							'type' => 'buttons',
							'title' => '轮播项设置',
							'std' => 'img_title',
							'values' => array(
								array(
									'label' => '图片',
									'value' => 'img_title'
								),
							),
							'tabs' => true,
						),
						// 2021.09.16 新增客户端获取
						// 轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端获取'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),
						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'zu'),
								array('equipment', '=', 'img_title'),
							),
						),
						'bg' => array(
							'type' => 'media',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							)
						),
						'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'desc' => JText::_('开启可设置图片点击跳转地址'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						// 新加跳转的链接地址
						'media_url' => array(
							'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'external_links'),
							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),
						// 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => '链接跳转方式',
							'desc' => '如开启客户端获取并且获取类型为轮播组时，跳转链接为客户端设置的外部链接',
							'std' => '_self',
							'values' => array(
								'_self' => '当前页面',
								'_blank' => '新窗口',
							),
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),

							)
						),
					),
					'std' => array(
						array(
							'text_from' => 0,
							'bg' => 'https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg',
							'media_url_show' => 0,
						)
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
					)
				),
				// 布局5
				'jw_carousel_item05' => array(
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
					'attr' => array(
						'equipment' => array(
							'type' => 'buttons',
							'title' => '轮播项设置',
							'std' => 'img_title',
							'values' => array(
								array(
									'label' => '图片',
									'value' => 'img_title'
								),
							),
							'tabs' => true,
						),
						// 2021.09.16 新增客户端获取
						// 轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端获取'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),
						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '=', 'zu'),
								array('equipment', '=', 'img_title'),
							),
						),
						'title' => array(
							'type' => 'text',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_TITLE'),
							'std' => '这里可以填写一个名称',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							),
						),
						'bg' => array(
							'type' => 'media',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							)
						),
						'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'desc' => JText::_('开启可设置图片点击跳转地址'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						// 新加跳转的链接地址
						'media_url' => array(
							'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'external_links'),
							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=', 1),
								array('hqtype', '=', 'dan'),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),
						// 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => '链接跳转方式',
							'desc' => '如开启客户端获取并且获取类型为轮播组时，跳转链接为客户端设置的外部链接',
							'std' => '_self',
							'values' => array(
								'_self' => '当前页面',
								'_blank' => '新窗口',
							),
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),

							)
						),
					),
					'std' => array(
						array(
							'text_from' => 0,
							'bg' => 'https://oss.lcweb01.cn/joomla/20230505/4c631108c5fd6d355b04b242c4dcd5da.jpg',
							'media_url_show' => 0,
						)
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
					)
				),
				'autoplay' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_AUTOPLAY_DESC'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 1,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),

					)
				),

				'interval' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_INTERVAL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_INTERVAL_DESC'),
					'std' => 5,
					'depends' => array(
						array('autoplay', '=', 1),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),

					)
				),
				'speed' => array(
					'type' => 'number',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SPEED_DESC'),
					'std' => 600,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),

					)
				),

				// 显示圆点控制
				'controllers' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_CONTROLLERS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_CONTROLLERS_DESC'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 1,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
					)
				),

				// 显示箭头控制
				'arrows' => array(
					'type' => 'checkbox',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_ARROWS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_SHOW_ARROWS_DESC'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 1,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
					)
				),

				'arrows_site' => array(
					'type' => 'select',
					'title' => '导航箭头位置',
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTENT_ALIGNMENT_DESC'),
					'values' => array(
						'left' => '左下角',
						'center' => '中间',
						'right' => '右下角',
					),
					'std' => 'center',
					'depends' => array(
						array('arrows', '=', 1),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					),
				),

				'alignment' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTENT_ALIGNMENT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_CONTENT_ALIGNMENT_DESC'),
					'values' => array(
						'jwpf-text-left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'jwpf-text-center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'jwpf-text-right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'jwpf-text-center',
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					)
				),


				'show_zsy' => array(
					'type' => 'checkbox',
					'title' => JText::_('是否固定宽高开启图片填充方式'),
					'desc' => JText::_('是否固定宽高开启图片填充方式'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 0,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),

					)
				),

				'img_gd_height' => array(
					'type' => 'slider',
					'title' => JText::_('固定图片宽高'),
					'max' => 2000,
					'min' => 1,
					'std' => 500,
					'responsive' => true,
					'depends' => array(
						array('show_zsy', '=', 1),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
					),
				),
				'img_style_type7' => array(
					'type' => 'select',
					'title' => JText::_('图片填充方式'),
					'values' => array(
						'scale-down' => JText::_('自适应显示'),
						'fill' => JText::_('占满不切割显示'),
						'cover' => JText::_('占满切割显示'),
					),
					'std' => 'fill',
					'depends' => array(
						array('show_zsy', '=', 1),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),

					),
				),

				'show_bubble' => array(
					'type' => 'checkbox',
					'title' => JText::_('显示气泡遮罩(气泡效果请到预览页查看)'),
					'desc' => JText::_('开启后轮播图上方会显示气泡遮罩效果'),
					'values' => array(
						1 => JText::_('JYES'),
						0 => JText::_('JNO'),
					),
					'std' => 0,
					'depends' => array(
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					)
				),
				'bubble_color_set' => array(
					'type' => 'select',
					'title' => '气泡颜色样式',
					'values' => array(
						'custom' => '自定义',
						'random' => '随机',
					),
					'std' => 'custom',
					'depends' => array(
						array('show_bubble', '=', 1),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					),
				),
				'bubble_color' => array(
					'type' => 'color',
					'title' => '气泡颜色',
					'std' => 'rgba(255,255,255, .4)',
					'depends' => array(
						array('show_bubble', '=', 1),
						array('bubble_color_set', '=', 'custom'),
						array('lbys', '!=', 'lb2'),
						array('lbys', '!=', 'lb3'),
						array('lbys', '!=', 'lb4'),
						array('lbys', '!=', 'lb5'),
					),
				),


				// 布局2
				'jw_carousel_item2' => array(
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
					'attr' => array(
						'equipment' => array(
							'type' => 'buttons',
							'title' => '轮播项设置',
							'std' => 'img_title',
							'values' => array(
								array(
									'label' => '图片/标题',
									'value' => 'img_title'
								),

							),
							'tabs' => true,
						),

						// 2021.09.16 新增客户端获取
						// 轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端获取'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'zu'),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						'bg' => array(
							'type' => 'media',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							)
						),
						'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'desc' => JText::_('开启可设置图片点击跳转地址'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 新加跳转的链接地址
						'media_url' => array(
							'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'external_links'),
							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),
						// 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => JText::_('链接跳转方式'),
							'desc' => JText::_('链接跳转方式'),
							'values' => array(
								'' => JText::_('当前页面'),
								'_blank' => JText::_('新窗口'),
							),
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),

							)
						),


					),
					'depends' => array(
						array('lbys', '=', 'lb2'),
					)
				),
				'lb2_topheight' => array(
					'type' => 'slider',
					'title' => '头部距离',
					'std' => '0',
					'max' => '200',
					'depends' => array(
						array('lbys', '=', 'lb2'),
					),
				),
				'lb2_title' => array(
					'type' => 'text',
					'title' => '标题',
					'std' => 'WELCOME TO EVOLUTION DESIGN',
					'depends' => array(
						array('lbys', '=', 'lb2'),
					),
				),
				'lb2_fontsize' => array(
					'type' => 'slider',
					'title' => '字体大小',
					'std' => '38',
					'max' => '60',
					'depends' => array(
						array('lbys', '=', 'lb2'),
					),
				),
				'lb2_image_fit' => array(
					'type' => 'select',
					'title' => '图片填充方式',
					'values' => array(
						'cover' => '占满切割显示',
						'fill' => '占满不切割显示',
					),
					'std' => 'cover',
					'depends' => array(
						array('lbys', '=', 'lb2'),
					),
				),

				'jw_carousel_item3' => array(
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEMS'),
					'attr' => array(
						'equipment' => array(
							'type' => 'buttons',
							'title' => '轮播项设置',
							'std' => 'img_title',
							'values' => array(
								array(
									'label' => '图片/标题',
									'value' => 'img_title'
								),

							),
							'tabs' => true,
						),

						// 2021.09.16 新增客户端获取
						// 轮播内容来源
						'text_from' => array(
							'type' => 'checkbox',
							'title' => JText::_('从客户端获取（注：PC端图片取后台"轮播图"字段、手机端图片取后台"内嵌图"字段）'),
							'desc' => JText::_('开启后即从后台获取轮播信息'),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),
						//2022.5.10日新增轮播组选项
						'hqtype' => array(
							'type' => 'select',
							'title' => JText::_('获取类型'),
							'values' => array(
								'dan' => JText::_('单张轮播'),
								'zu' => JText::_('轮播组'),
							),
							'std' => 'dan',
							'depends' => array(
								array('text_from', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播 单张内容类型
						'text_id' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播名称'),
							'desc' => JText::_('对应后台上传的轮播名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getBannerList($site_id, $company_id)['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'zu'),
								array('equipment', '=', 'img_title'),
							),
						),

						// 轮播组id 2022.5.10
						'banner_typeid' => array(
							'type' => 'select',
							'title' => JText::_('选择后台对应的轮播分类'),
							'desc' => JText::_('对应后台上传的轮播分类名称，如果不选默认还显示编辑器添加的信息'),
							'values' => JwPageFactoryBase::getTypeList($site_id, $company_id, 'com_banner')['list'],
							'depends' => array(
								array('text_from', '=', 1),
								array('hqtype', '!=', 'dan'),
								array('equipment', '=', 'img_title'),
							),
						),
						'bg' => array(
							'type' => 'media',
							'title' => 'PC轮播图',
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/joomla/20220711/56fa646cff29309488d6602246694d27.jpg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							)
						),
						'phone_bg' => array(
							'type' => 'media',
							'title' => '手机轮播图',
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CAROUSEL_ITEM_IMAGE_DESC'),
							'format' => 'image',
							'std' => 'https://oss.lcweb01.cn/joomla/20220711/0b472b1eb9db57d7557bc97b093b0b70.jpg',
							'depends' => array(
								array('text_from', '!=', '1'),
								array('equipment', '=', 'img_title'),
							)
						),
						'media_url_show' => array(
							'type' => 'checkbox',
							'title' => JText::_('是否启用链接跳转'),
							'desc' => JText::_('开启可设置图片点击跳转地址'),
							'values' => array(
								1 => JText::_('JYES'),
								0 => JText::_('JNO'),
							),
							'std' => 0,
							'depends' => array(
								array('equipment', '=', 'img_title'),
							),
						),

						'tz_page_type' => array(
							'type' => 'select',
							'title' => JText::_('跳转方式'),
							'desc' => JText::_('跳转方式'),
							'values' => array(
								'Internal_pages' => JText::_('内部页面'),
								'external_links' => JText::_('外部链接'),
							),
							'std' => 'external_links',
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
							),
						),

						// 新加跳转的链接地址
						'media_url' => array(
							'type' => 'text',
							'title' => JText::_('链接跳转地址'),
							'desc' => JText::_('链接必须以http://或https://开始'),
							'placeholder' => 'http://',
							'std' => '',
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'external_links'),
							)
						),
						'detail_page_id' => array(
							'type' => 'select',
							'title' => '选择跳转页面',
							'desc' => '',
							'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
							'depends' => array(
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),
								array('tz_page_type', '=', 'Internal_pages')
							),
						),
						// 新加跳转方式
						'media_target' => array(
							'type' => 'select',
							'title' => JText::_('链接跳转方式'),
							'desc' => JText::_('链接跳转方式'),
							'values' => array(
								'' => JText::_('当前页面'),
								'_blank' => JText::_('新窗口'),
							),
							'depends' => array(
								// array('bg', '!=', ''),
								array('media_url_show', '=', 1),
								array('equipment', '=', 'img_title'),

							)
						),
					),
					'depends' => array(
						array('lbys', '=', 'lb3'),
					)
				),

				'qx_height' => array(
					'type' => 'checkbox',
					'title' => '高度自适应',
					'std' => 0,
					'depends' => array(
						array('lbys', '=', 'lb3'),
					),
				),
				'qx_height3' => array(
					'type' => 'slider',
					'title' => '高度',
					'std' => array('md' => 930, 'sm' => 1326, 'xs' => 720),
					'max' => 1000,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb3'),
						array('qx_height', '!=', '1'),
					),
				),
				'img_lunb3' => array(
					'type' => 'select',
					'title' => JText::_('图片填充方式'),
					'values' => array(
						'fill' => JText::_('占满不切割显示'),
						'scale-down' => JText::_('自适应显示'),
						'cover' => JText::_('占满切割显示'),
					),
					'std' => 'fill',
					'depends' => array(
						array('lbys', '=', 'lb3'),
					),
				),
				'lb3_zsq' => array(
					'type' => 'select',
					'title' => '指示器样式',
					'values' => array(
						'ysang1' => JText::_('圆点'),
						'ysang2' => JText::_('数字'),
					),
					'std' => 'ysang1',
					'depends' => array(
						array('lbys', '=', 'lb3'),
					),
				),
				/* 布局4 */
				'img_height04' => array(
					'type' => 'slider',
					'title' => '高度',
					'std' => array('md' => 602, 'sm' => 334, 'xs' => 186),
					'max' => 2000,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb4'),
					),
				),
				'img_fit_04' => array(
					'type' => 'select',
					'title' => '图片填充方式',
					'values' => array(
						'fill' => '占满不切割显示',
						'scale-down' => '自适应显示',
						'cover' => '占满切割显示',
					),
					'std' => 'cover',
					'depends' => array(
						array('lbys', '=', 'lb4'),
					),
				),
				'swiper_style04' => array(
					'type' => 'buttons',
					'title' => '切换选项状态',
					'std' => 'button_s',
					'values' => array(
						array(
							'label' => '翻页配置',
							'value' => 'button_s'
						),
						array(
							'label' => '轮播点配置',
							'value' => 'pagination_s'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						// array('is_swiper', '=', 1)
					),
				),
				'is_swiper_button' => array(
					'type' => 'checkbox',
					'title' => '开启切换按钮',
					'std' => 1,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
					),
				),
				'swiper_button_prev' => array(
					'type' => 'media',
					'title' => '上一页',
					'std' => 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png',
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_next' => array(
					'type' => 'media',
					'title' => '下一页',
					'std' => 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png',
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_prev_hover' => array(
					'type' => 'media',
					'title' => '移入上一页',
					'std' => '',
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_next_hover' => array(
					'type' => 'media',
					'title' => '移入下一页',
					'std' => '',
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_width' => array(
					'type' => 'slider',
					'title' => '切换按钮宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 40,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_height' => array(
					'type' => 'slider',
					'title' => '切换按钮高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 40,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_top' => array(
					'type' => 'slider',
					'title' => '切换按钮上边距（百分比）',
					'max' => 1000,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 48,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'is_swiper_button_top_px' => array(
					'type' => 'checkbox',
					'title' => '切换按钮上边距使用px单位',
					'std' => 0,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				'swiper_button_left' => array(
					'type' => 'slider',
					'title' => '切换按钮两侧边距（px）',
					'max' => 800,
					'min' => -100,
					'responsive' => true,
					'std' => array(
						'md' => 10,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'button_s'),
						array('is_swiper_button', '=', 1),
					),
				),
				// 轮播点配置
				'is_swiper_pagination' => array(
					'type' => 'checkbox',
					'title' => '开启轮播点',
					'std' => 1,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
					),
				),
				'swiper_pagination_bottom' => array(
					'type' => 'slider',
					'title' => '轮播点底部距离（px）',
					'responsive' => true,
					'std' => array(
						'md' => 20,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'pagination_style' => array(
					'type' => 'buttons',
					'title' => '轮播点状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '选中',
							'value' => 'active'
						),
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('is_swiper_pagination', '=', 1),
					),
					'tabs' => true,
				),
				'swiper_p_width' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 23,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'normal'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'swiper_p_height' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 5,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'normal'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'swiper_p_margin' => array(
					'type' => 'slider',
					'title' => '轮播点间距',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 10,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'normal'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'swiper_p_color' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => '#fff',
					'depends' => array(
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'normal'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				//轮播点选中
				'swiper_p_width_a' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'active'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'swiper_p_height_a' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'active'),
						array('is_swiper_pagination', '=', 1),
					),
				),
				'swiper_p_color_a' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => '#007aff',
					'depends' => array(
						array('lbys', '=', 'lb4'),
						array('swiper_style04', '=', 'pagination_s'),
						array('pagination_style', '=', 'active'),
						array('is_swiper_pagination', '=', 1),
					),
				),

				// 'pagination_bColor_a' => array(
				// 	'type' => 'color',
				// 	'title' => '轮播点边框颜色',
				// 	'desc' => '',
				// 	'std' => 'rgba(255, 255, 255, 0.2)',
				// 	'depends' => array(
				// 		array('swiper_style04', '=', 'pagination_s'),
				// 		array('is_swiper_pagination', '=', 1),
				// 		array('pagination_tab', '=', 'active'),
				// 	)
				// ),
				// 'pagination_b_w' => array(
				// 	'type' => 'slider',
				// 	'title' => '轮播点边框宽度',
				// 	'desc' => '',
				// 	'max' => 1000,
				// 	'min' => 0,
				// 	'std' => 3,
				// 	'depends' => array(
				// 		array('swiper_style04', '=', 'pagination_s'),
				// 		array('is_swiper_pagination', '=', 1),
				// 		array('pagination_tab', '=', 'active'),
				// 	)
				// ),
				/* 布局5 */
				'type05_part01' => array(
					'type' => 'separator',
					'title' => '标题配置',
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				// 标题配置
				'title_bgcolor05' => array(
					'type' => 'color',
					'title' => '标题背景颜色',
					'std' => 'rgba(0, 0, 0, 0.64)',
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				'title_fontsize05' => array(
					'type' => 'slider',
					'title' => '标题文字大小',
					'max' => 100,
					'std' => array('md' => 18, 'sm' =>'', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				'title_lineHeight05' => array(
					'type' => 'slider',
					'title' => '标题文字行高',
					'max' => 100,
					'std' => array('md' => 52, 'sm' => '', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				'title_color05' => array(
					'type' => 'color',
					'title' => '标题文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				'title_padding05' => array(
					'type' => 'padding',
					'title' => '标题文字内边距',
					'std' => array('md' => '0px 200px 0px 20px', 'sm' => '', 'xs' => ''),
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				// 轮播图切换动画 适用swiper
				'animate_dh' => array(
					'type' => 'select',
					'title' => '切换动画',
					'values' => array(
						'slide' => '默认',
						'fade' => '淡入',
						'cube' => '方块',
						'coverflow' => '3d流',
						'flip' => '3d翻转',
						//'cards' => '卡片式',  // Swiper 6.6.2 不支持
						//'creative' => '创意性',  // Swiper 6.6.2 不支持
					),
					'std' => 'fade',
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				'swiper_style05' => array(
					'type' => 'buttons',
					'title' => '切换选项状态',
					'std' => 'pagination_s',
					'values' => array(
						// array(
						// 	'label' => '翻页配置',
						// 	'value' => 'button_s'
						// ),
						array(
							'label' => '轮播点配置',
							'value' => 'pagination_s'
						)
					),
					'tabs' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
					),
				),
				// 'is_swiper_button05' => array(
				// 	'type' => 'checkbox',
				// 	'title' => '开启切换按钮',
				// 	'std' => 1,
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 	),
				// ),
				// 'swiper_button_prev05' => array(
				// 	'type' => 'media',
				// 	'title' => '上一页',
				// 	'std' => 'https://oss.lcweb01.cn/joomla/20230505/2b54b3bee6d92a1894fe0303e02734d4.png',
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 'swiper_button_next05' => array(
				// 	'type' => 'media',
				// 	'title' => '下一页',
				// 	'std' => 'https://oss.lcweb01.cn/joomla/20230505/9bef579ce5b3ffe7a2ada712628df7c4.png',
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb6'),
				// 		array('swiper_style06', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 'swiper_button_prev_hover05' => array(
				// 	'type' => 'media',
				// 	'title' => '移入上一页',
				// 	'std' => '',
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 'swiper_button_next_hover05' => array(
				// 	'type' => 'media',
				// 	'title' => '移入下一页',
				// 	'std' => '',
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 'swiper_button_width05' => array(
				// 	'type' => 'slider',
				// 	'title' => '切换按钮宽度',
				// 	'max' => 100,
				// 	'min' => 0,
				// 	'responsive' => true,
				// 	'std' => array(
				// 		'md' => 40,
				// 		'sm' => '',
				// 		'xs' => ''
				// 	),
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 'swiper_button_height05' => array(
				// 	'type' => 'slider',
				// 	'title' => '切换按钮高度',
				// 	'max' => 100,
				// 	'min' => 0,
				// 	'responsive' => true,
				// 	'std' => array(
				// 		'md' => 40,
				// 		'sm' => '',
				// 		'xs' => ''
				// 	),
				// 	'depends' => array(
				// 		array('lbys', '=', 'lb5'),
				// 		array('swiper_style05', '=', 'button_s'),
				// 		array('is_swiper_button05', '=', 1),
				// 	),
				// ),
				// 轮播点配置
				'is_swiper_pagination05' => array(
					'type' => 'checkbox',
					'title' => '开启轮播点',
					'std' => 1,
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
					),
				),
				'pagination_style05' => array(
					'type' => 'buttons',
					'title' => '轮播点状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '选中',
							'value' => 'active'
						),
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('is_swiper_pagination05', '=', 1),
					),
					'tabs' => true,
				),
				'swiper_p_width05' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 18,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'normal'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_height05' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 7,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'normal'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_margin05' => array(
					'type' => 'slider',
					'title' => '轮播点间距',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 5,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'normal'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_border05' => array(
					'type' => 'slider',
					'title' => '轮播点圆角',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'std' => array(
						'md' => 1,
						'sm' => '',
						'xs' => ''
					),
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'normal'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_color05' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => 'rgba(255,255,255,0.52)',
					'depends' => array(
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'normal'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				//轮播点选中
				'swiper_p_width_a05' => array(
					'type' => 'slider',
					'title' => '轮播点宽度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'active'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_height_a05' => array(
					'type' => 'slider',
					'title' => '轮播点高度',
					'max' => 100,
					'min' => 0,
					'responsive' => true,
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'active'),
						array('is_swiper_pagination05', '=', 1),
					),
				),
				'swiper_p_color_a05' => array(
					'type' => 'color',
					'title' => '轮播点颜色',
					'std' => '#FFFFFF',
					'depends' => array(
						array('lbys', '=', 'lb5'),
						array('swiper_style05', '=', 'pagination_s'),
						array('pagination_style05', '=', 'active'),
						array('is_swiper_pagination05', '=', 1),
					),
				),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
			),
		),
	)
);
