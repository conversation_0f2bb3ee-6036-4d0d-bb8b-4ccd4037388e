<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonThree_lv_product_class extends JwpagefactoryAddons
{

    public static $this_obj;

    public function __construct($addon)
    {
        parent::__construct($addon);
        self::$this_obj = $this;
    }

    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $cpcatid = $_GET['cpcatid'] ?? 0;

        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $addonss_id = $this->addon->id;
        $cjid = $_GET['cjid'] ?? $addonss_id;
        $tab_cid = $_GET['tab_cid'] ?? 0;
        if($cjid == $addonss_id)
        {
            $cpcatid = $_GET['cpcatid'] ?? 0;
            $page = $_GET['page'] ?? 1;
        }
        else
        {
            $cpcatid = 0;
            $page = 1;
        }
        $id = $_GET['id'] ?? 0;
        // print_r($tab_cid);die;

        $settings = $this->addon->settings;
        $style_type = (isset($settings->style_type)) ? $settings->style_type : 'type1';
        //从第几个分类开始显示
        $type_start = (isset($settings->type_start)) ? $settings->type_start : 0;
        //显示几条分类
        $type_num = (isset($settings->type_num)) ? $settings->type_num : 10;
        //每条分类显示几条数据
        $limit = (isset($settings->limit)) ? $settings->limit : 10;
        //详情页id
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;
        //一级导航宽度
        $nav_width_1 = (isset($settings->nav_width_1)) ? $settings->nav_width_1 : 100;
        $nav_max_width = (isset($settings->nav_max_width)) ? $settings->nav_max_width : 15;
        //一级导航高度
        $nav_height_1 = (isset($settings->nav_height_1)) ? $settings->nav_height_1 : 50;
        //一级导航背景颜色
        $nav_bg_color_1 = (isset($settings->nav_bg_color_1)) ? $settings->nav_bg_color_1 : '#1768b7';
        //一级导航字体颜色
        $nav_color_1 = (isset($settings->nav_color_1)) ? $settings->nav_color_1 : '#ffe1de';
        $nav_1_size = (isset($settings->nav_1_size)) ? $settings->nav_1_size : 18;
        //一级导航左边距
        $nav_margin_left_1 = (isset($settings->nav_margin_left_1)) ? $settings->nav_margin_left_1 : 0;
        //一级导航文字位置
        $nav_align_1 = (isset($settings->nav_align_1)) ? $settings->nav_align_1 : 'center';
        //二级导航文字位置
        $nav_align_2 = (isset($settings->nav_align_2)) ? $settings->nav_align_2 : 'center';
        //三级导航文字位置
        $nav_align_3 = (isset($settings->nav_align_3)) ? $settings->nav_align_3 : 'center';
        //二级导航宽度
        $nav_width_2 = (isset($settings->nav_width_2)) ? $settings->nav_width_2 : 100;
        //二级导航背景颜色
        $nav_bg_color_2 = (isset($settings->nav_bg_color_2)) ? $settings->nav_bg_color_2 : '#ffffff';
        //二级导航字体颜色
        $nav_color_2 = (isset($settings->nav_color_2)) ? $settings->nav_color_2 : '#333333';
        $nav_2_size = (isset($settings->nav_2_size)) ? $settings->nav_2_size : 16;
        //二级导航左边距
        $nav_margin_left_2 = (isset($settings->nav_margin_left_2)) ? $settings->nav_margin_left_2 : 0;

        //三级导航宽度
        $nav_width_3 = (isset($settings->nav_width_3)) ? $settings->nav_width_3 : 100;
        //三级导航背景颜色
        $nav_bg_color_3 = (isset($settings->nav_bg_color_3)) ? $settings->nav_bg_color_3 : '#ffffff';
        //三级导航字体颜色
        $nav_color_3 = (isset($settings->nav_color_3)) ? $settings->nav_color_3 : '#666666';
        $nav_3_size = (isset($settings->nav_3_size)) ? $settings->nav_3_size : 14;
        //三级导航左边距
        $nav_margin_left_3 = (isset($settings->nav_margin_left_3)) ? $settings->nav_margin_left_3 : 0;
        $title_font_height = (isset($settings->title_font_height)) ? $settings->title_font_height : 0;
        $title_font_height_yd = (isset($settings->title_font_height_yd)) ? $settings->title_font_height_yd : 60;
        /*分页*/
        //翻页显示数量
        $page_dis = (isset($settings->show_page)) ? $settings->show_page : 0;
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
        $page_count = (isset($settings->page_count)) ? $settings->page_count : 5;
        //翻页左右位置调整
        $page_location = (isset($settings->page_location)) ? $settings->page_location : 0;
        //翻页上下位置调整
        $page_top_location = (isset($settings->page_top_location)) ? $settings->page_top_location : 0;
        //翻页字体颜色
        $page2_tab_fontcolor = (isset($settings->page2_tab_fontcolor)) ? $settings->page2_tab_fontcolor : '#737373';
        //翻页边框颜色
        $page2_tab_bordercolor = (isset($settings->page2_tab_bordercolor)) ? $settings->page2_tab_bordercolor : '#e5e5e5';
        //翻页背景颜色
        $page2_tab_bgcolor = (isset($settings->page2_tab_bgcolor)) ? $settings->page2_tab_bgcolor : '#e5e5e5';
        //当前页字体颜色
        $page2_bom_col = (isset($settings->page2_bom_col)) ? $settings->page2_bom_col : '#fff';
        //当前页边框颜色
        $page2_bom_bor_col = (isset($settings->page2_bom_bor_col)) ? $settings->page2_bom_bor_col : '#016729';
        //当前页背景颜色
        $page2_bom_bgcolor = (isset($settings->page2_bom_bgcolor)) ? $settings->page2_bom_bgcolor : '#016729';
        //上下页字体颜色
        $page2_ud_col = (isset($settings->page2_ud_col)) ? $settings->page2_ud_col : '#fff';
        //上下页边框颜色
        $page2_ud_bor_col = (isset($settings->page2_ud_bor_col)) ? $settings->page2_ud_bor_col : '#e5e5e5';
        //上下页背景颜色
        $page2_ud_bgcolor = (isset($settings->page2_ud_bgcolor)) ? $settings->page2_ud_bgcolor : '#000';
        //首尾页字体颜色
        $page2_be_col = (isset($settings->page2_be_col)) ? $settings->page2_be_col : '#737373';
        //首尾页边框颜色
        $page2_be_bor_col = (isset($settings->page2_be_bor_col)) ? $settings->page2_be_bor_col : '#e5e5e5';
        //首尾页背景颜色
        $page2_be_bgcolor = (isset($settings->page2_be_bgcolor)) ? $settings->page2_be_bgcolor : '#e5e5e5';

        /*内容*/
        //内容垂直位置调整
        $vertical_position = (isset($settings->vertical_position)) ? $settings->vertical_position : 0;
        //内容左右位置调整
        $left_position = (isset($settings->left_position)) ? $settings->left_position : 0;
        //默认不显示二三级导航
        $show_nav = (isset($settings->show_nav)) ? $settings->show_nav : 0;
        $show_nav_two = (isset($settings->show_nav_two)) ? $settings->show_nav_two : 0;
        $show_one_nav = (isset($settings->show_one_nav)) ? $settings->show_one_nav : 0;
        $nav_max_widthss = 95-$nav_max_width;
        //图片填充方式
        $img_style = (isset($settings->img_style)) ? $settings->img_style : 'cover';
        //内容背景颜色
        $page2_cont_bgcolor = (isset($settings->page2_cont_bgcolor)) ? $settings->page2_cont_bgcolor : '';
        //图片高度
        $img_height = (isset($settings->img_height)) ? $settings->img_height : 'auto';
        if($img_height!='auto')
        {
            $img_height  = $img_height.'px';
        }

        $phoneimg_height = (isset($settings->phoneimg_height)) ? $settings->phoneimg_height : 'auto';
        if($phoneimg_height!='auto')
        {
            $phoneimg_height  = $phoneimg_height.'px';
        }
        $left_phonecont_width = (isset($settings->left_phonecont_width)) ? $settings->left_phonecont_width : 43;

        $link_tz_fs = (isset($settings->link_tz_fs)) ? $settings->link_tz_fs : 0;

        //列表背景颜色
        $page2_list_bgcolor = (isset($settings->page2_list_bgcolor)) ? $settings->page2_list_bgcolor : '#666';
        //一级导航展开按钮
        $nav_icon_1 = (isset($settings->nav_icon_1)) ? $settings->nav_icon_1 : '+';
        //二级导航展开按钮
        $nav_icon_2 = (isset($settings->nav_icon_2)) ? $settings->nav_icon_2 : '+';
        //一级导航展开按钮字体大小
        $nav_icon_1_size = (isset($settings->nav_icon_1_size)) ? $settings->nav_icon_1_size : '+';
        //二级导航展开按钮字体大小
        $nav_icon_2_size = (isset($settings->nav_icon_2_size)) ? $settings->nav_icon_2_size : '+';
        //列表内边距
        $page2_list_padding = (isset($settings->page2_list_padding)) ? $settings->page2_list_padding : '0px 0px 0px 0px';
        //列表文字颜色
        $page2_list_color = (isset($settings->page2_list_color)) ? $settings->page2_list_color : '#000';
        //内容字体大小
        $page2_list_size = (isset($settings->page2_list_size)) ? $settings->page2_list_size : 16;
        //内容列表宽度
        $left_cont_width = (isset($settings->left_cont_width)) ? $settings->left_cont_width : 45;
        $left_cont_width_jg = (isset($settings->left_cont_width_jg)) ? $settings->left_cont_width_jg : 0.5;
        //导航整体背景
        $nav_bg_color = (isset($settings->nav_bg_color)) ? $settings->nav_bg_color : 16;
        //获取分类下所有数据
        $show_pro_data = (isset($settings->show_pro_data)) ? $settings->show_pro_data : 1;
        $page1_fontcolor = (isset($settings->page1_fontcolor)) ? $settings->page1_fontcolor : '#2a68a7';
        $page1_bordercolor = (isset($settings->page1_bordercolor)) ? $settings->page1_bordercolor : '#2a68a7';
        $page1_bgcolor = (isset($settings->page1_bgcolor)) ? $settings->page1_bgcolor : '#ffffff';
        $page1_cur_fontcolor = (isset($settings->page1_cur_fontcolor)) ? $settings->page1_cur_fontcolor : '#2a68a7';
        $page1_cur_bordercolor = (isset($settings->page1_cur_bordercolor)) ? $settings->page1_cur_bordercolor : '#2a68a7';
        $page1_cur_bgcolor = (isset($settings->page1_cur_bgcolor)) ? $settings->page1_cur_bgcolor : '#ffffff';
        //排序
        $ordering = (isset($settings->ordering)) ? $settings->ordering : 'orderingdesc';
        $nav_ordering = (isset($settings->nav_ordering)) ? $settings->nav_ordering : 'DESC';
        $shiyong_type = (isset($settings->shiyong_type)) ? $settings->shiyong_type : 'pc';
        $page_style_selector = (isset($settings->page_style_selector)) ? $settings->page_style_selector : 'page01';
        $page_style_current_bg_color = (isset($settings->page_style_current_color)) ? $settings->page_style_current_color : '#f00';
        $page_style_font_color = (isset($settings->page_style_font_color)) ? $settings->page_style_font_color : '#000';
        $nav_max_widthss = 95-$nav_max_width;
        $page1_min_width=(isset($settings->page1_min_width)) ? $settings->page1_min_width : '';
        $page1_margin_bottom=(isset($settings->page1_margin_bottom)) ? $settings->page1_margin_bottom : '';

        $nav_bg_check = (isset($settings->nav_bg_check)) ? $settings->nav_bg_check : 0;
        $nav_bgxz_color = (isset($settings->nav_bgxz_color)) ? $settings->nav_bgxz_color : "#baab9e";
        $nav_font_color = (isset($settings->nav_font_color)) ? $settings->nav_font_color : "#fff";

        //翻页接口
        $config = new JConfig();
        //这里是动态获取域名的方法
        //        if ($config->jzt_url == 'http://'.$_SERVER['HTTP_HOST']) {
        //            $urlpath = $config->jzt_url;
        //        } else {
        //            $urlpath = $config->ijzt_url;
        //        }

                // if($show_pro_data == 0){
                // $urlpath = 'http://jzt_dev_2.china9.cn/index.php/component/jwpagefactory/?view=page&id='.$id.'&company_id='.$company_id.'&layout_id='.$layout_id.'&site_id='.$site_id.'&page=1&cpcatid=';
                // $urlpath = 'https://zhjzt.china9.cn/api/Shuju/product_list_data';
        //            $urlpath = $config->jzt_url.'/api/Shuju/product_list_data';
                // }else{
                    // $urlpath = 'https://zhjzt.china9.cn/api/Shuju/product_list_data';
        //            $urlpath = $config->jzt_url.'/api/Shuju/product_list_data2';
        // }
        $urlpath = '/index.php/component/jwpagefactory/?view=page&id='.$id.'&company_id='.$company_id.'&layout_id='.$layout_id.'&site_id='.$site_id.'&page=1&cjid='.$addonss_id.'&cpcatid=';
        $output ='';
        $output .="
        <style>
            {$addon_id} .content{
                width: 100%;
            }
            ";
            if($nav_bg_check){
                $output .="{$addon_id} .aat{
                    background:{$nav_bgxz_color}!important;
                    color:{$nav_font_color}!important;
                }";
            }
            
        $output .="@media (min-width: 1200px) {
            {$addon_id} .content .sec-content{
                width: {$nav_max_width}%;
                float: left;
                margin-right: 20px;
                padding-bottom: 60px;
                background:{$nav_bg_color};
        
            }
            {$addon_id} .content .sec-content .sec-content-box{
                width: 100%;
            }
            {$addon_id} .content .sec-content .sec-content-box-t{
                cursor: pointer;
                position: relative;
                width: {$nav_width_1}%;
                height: {$nav_height_1}px;
                background: {$nav_bg_color_1};
                font-size: {$nav_1_size}px;
                line-height: 50px;
                color: {$nav_color_1};
                text-align: {$nav_align_1};
                margin-bottom: 10px;
                margin-left: {$nav_margin_left_1}px;
            }
        }
            {$addon_id} .content .sec-content .sec-content-box-t .sec_dj div{
                position: absolute;
                top: 0;
                right: 10px;
                bottom: 0;
                margin: auto;
                font-size: 25px;
                line-height: 46px;
                color: #fff;
            }
            {$addon_id} .sec-content-box-b{
        
                width: calc(100% - 20px);
                margin: 0 auto;
            }
            {$addon_id} .sec-content-box-bli{
                transition: .3s;
                display: block;
                text-decoration: none;
                width: 100%;
                box-sizing: border-box;
                margin-bottom: 10px;
            }
            {$addon_id} .sec-content-box-bli-t{
                display: block;
                text-decoration: none;
                transition: .3s;
                width: {$nav_width_2}%;
                margin-left: {$nav_margin_left_1}px;
                text-align: {$nav_align_2};
                font-size: {$nav_2_size}px;
                color: {$nav_color_2};
                line-height: 46px;
                background: {$nav_bg_color_2};
                margin-bottom: 10px;
                box-sizing: border-box;
            }
            {$addon_id} .sec-content-box-bli-b{
                width: {$nav_width_3}%;
                background: {$nav_bg_color_3};
                margin-left: {$nav_margin_left_3}px;
            }
            {$addon_id} .sec-content-box-bli-bli{
                transition: .3s;
                display: block;
                text-decoration: none;
                width: 100%;
                font-size: {$nav_3_size}px;
                line-height: 40px;
                background: {$nav_bg_color_3};
                color: {$nav_color_3};
                text-align: {$nav_align_3};
                border-bottom: 1px dashed #ddd;
            }
            {$addon_id} .sec-content-box-t .sec_dj div:last-child{
                display: none;
                font-size: 35px;
                position: absolute;
                top: 0;
                right: 10px;
                bottom: 0;
                margin: auto;
                line-height: 46px;
                color: #fff;
            }
            {$addon_id} .sec-content-box-t a{
                color: {$nav_color_1};
                text-decoration: none;
            }
            {$addon_id} .sec-content-box-bli-t span{
                font-size: 20px;
                float:left
            }
            {$addon_id} .sec-content-box-bli-t a{
                color: {$nav_color_2};
                text-decoration: none;
                text-align: {$nav_align_2};
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
            }
        
            /*内容列表*/
            {$addon_id} .cqr-tulisu-xianzhi{
                width: 50%;
                margin: 0;
                float: left;
                position: relative;
                margin-top: {$vertical_position}px;
                margin-left: {$left_position}px;
            }
            {$addon_id} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3{
                clear: both;
                text-align: left;
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                background: {$page2_cont_bgcolor};
            }
            {$addon_id} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3 .box{
                width: {$left_cont_width}%;
                overflow: hidden;
                box-sizing: border-box;
                margin: {$left_cont_width_jg}%;
                position: relative;
                display: inline-block;
                text-align: center;
                padding: {$page2_list_padding};
            }
            {$addon_id} .cqr-tulisu-xianzhi .imgBox img{
                width: 100%;
                height: {$img_height} !important;
                transition: all 0.6s linear;
                display: block;
            }
            {$addon_id} .cqr-tulisu-xianzhi .box a{
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                color: #535353;
                text-decoration: none;
            }
            {$addon_id} .cqr-tulisu-xianzhi .title{
                padding: 20px 0;
                width: 100%;
                height: {$title_font_height}px;
                text-align: center;
                font-size: {$page2_list_size}px;
                background: {$page2_list_bgcolor};
                color: {$page2_list_color};
            }
            {$addon_id} .pages-box{
                display: flex;
                width: 856px;
                justify-content: center;
            }
            {$addon_id} .pages-c1{
                display: inline-block;
            }
            {$addon_id} .pages-c2{
                text-align: center;
            }
            {$addon_id} .pages-c2>a{
                display: inline-block;
                width: 55px;
                height: 30px;
                border-radius: 4px;
                box-sizing: border-box;
                font-size: 14px;
                line-height: 30px;
                color: #737373;
                text-align: center;
                transition: .3s;
                margin: 0 2px;
                background: #E5E5E5;
                text-decoration: none;
            }
            {$addon_id} .pages-c3{
                display: inline-block;
                margin: 0 5px;
            }
            {$addon_id} .pages-c3>a{
                font-size: 14px;
                line-height: 30px;
                color: #737373;
                transition: 0.5s;
                margin: 0 3px;
                display: inline-block;
                width: 30px;
                background: #E5E5E5;
                border-radius: 3px;
                text-decoration: none;
            }
            {$addon_id} .pages-sel{
                color: #fff!important;
                transition: 0.5s!important;
                background: #016729!important;
            }
            
            
            @media (max-width: 768px) {
                {$addon_id} .cqr-tulisu-xianzhi .imgBox img{
                    width: 100%;
                    height: {$phoneimg_height} !important;
                    transition: all 0.6s linear;
                    display: block;
                }
                {$addon_id} .content{
                    width: 100%;
                    margin: 0 auto;
                }
                {$addon_id} .content .sec-content{
                    width: 100%;
                    background: #fff;
                    display: flex;
                    flex-wrap: wrap;
                    z-index: 1000;
                    background:{$nav_bg_color};
                }
                {$addon_id} .content .sec-content .sec-content-box{
                    width: {$nav_width_1}%;
                    margin-left: {$nav_margin_left_1}px;
                }
                {$addon_id} .content .sec-content .sec-content-box-t{
                    cursor: pointer;
                    position: relative;
                    width: 100%;
                    height: 50px;
                    background: {$nav_bg_color_1};
                    font-size: {$nav_1_size}px;
                    line-height: 50px;
                    color: {$nav_color_1};
                    text-align: {$nav_align_1};
                    margin-bottom: 10px;
                    
                }
                {$addon_id} .sec-content-box-t a{
                    color: {$nav_color_1};
                    text-decoration: none;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin: auto;
                }
                {$addon_id} .cqr-tulisu-xianzhi{
                    width: 100%;
                    margin: 0;
                    float: left;
                    position: relative;
                }
                {$addon_id} .cqr-tulisu-xianzhi .imgBox{
                    width:100%
                }
                {$addon_id} .cqr-tulisu-xianzhi .imgBox img{
                    width: 100%;
                    height: 100px;
                    object-fit:{$img_style};
                }
                {$addon_id} .cqr-tulisu-xianzhi .title{
                    height: {$title_font_height_yd}px;
                    background:{$page2_list_bgcolor};
                    color:$page2_list_color;
                    font-size: {$page2_list_size}px;
                }
                {$addon_id} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3{
                    clear: both;
                    text-align: left;
                    width:100%;
                    display: flex;
                    flex-wrap: wrap;
                    background: {$page2_cont_bgcolor};
                }
                {$addon_id} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3 .box{
                    width: {$left_phonecont_width}%;
                    overflow: hidden;
                    box-sizing: border-box;
                    position: relative;
                    display: inline-block;
                    text-align: center;
                    margin: 12px;
                    padding:{$page2_list_padding};
                }
                {$addon_id} .pages-box-dis{
                    width: 100%!important;
                }
                {$addon_id} .sec-content-box-b{
                    display: none;
                }
                {$addon_id} .content .sec-content .sec-content-box-t{
                    height: 45px;
                    line-height: 42px;
                }
                
                {$addon_id} .sec-content-box-bli-t{
                    margin-left:0;
                    height:50px;
                    text-align: {$nav_align_2};
                    display: flex;
                    background: {$nav_bg_color_2};
                    color: {$nav_color_2};
                    justify-content: center;
                }
                {$addon_id} .sec-content-box-bli-t span{
                    float: left;
                }
                {$addon_id} .sec-content-box-bli-t a{
                    color: {$nav_color_2};
                    text-decoration: none;
                    text-align: {$nav_align_2};
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                {$addon_id} .sec-content-box-bli-b{
                    display: block;
                }
                {$addon_id} .sec-content-box-bli-bli{
                    transition: .3s;
                    display: block;
                    text-decoration: none;
                    width: 100%;
                    font-size: 14px;
                    line-height: 40px;
                    background: {$nav_bg_color_3};
                    color: {$nav_color_3};
                    text-align: {$nav_align_3};
                    border-bottom: 1px dashed #ddd;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                {$addon_id} .anv_li{
                    cursor:pointer;
                    
                }
               
            }
        </style>
        ";
        //分页
        $output .="
                <style>
                    {$addon_id} #pageGro{ text-align: center; height:25px; margin:0 auto; padding-top:30px;}
                    {$addon_id} #pageGro div{list-style: none; font-size:12px; line-height:23px; float:left; margin-left:5px;}
                    {$addon_id} #pageGro div ul li{list-style: none; font-size:12px; color:#999; line-height:23px; float:left; margin-left:5px;}
                    {$addon_id} #pageGro .pageList{margin-left: 0}
                    {$addon_id} #pageGro div ul{padding-left: 0}
                    {$addon_id} #pageGro div ul li{ width:22px; text-align:center; border:1px solid {$page2_tab_bordercolor}; color:{$page2_be_col}; background:{$page2_be_bgcolor};cursor:pointer;}
                    {$addon_id} #pageGro div ul li.on{ color:{$page2_tab_fontcolor}; background:{$page2_tab_bgcolor}; border:1px solid {$page2_tab_bordercolor};}
                    {$addon_id} #pageGro .pageUp,#pageGro .pageDown,#pageGro .pagestart,#pageGro .pageend{ width:63px; border:1px solid #999; cursor:pointer;}
                    {$addon_id} .pages-box-dis{width: 900px;display: flex;justify-content: center;}
                    {$addon_id} .page_content{background-color: {$page2_bom_bgcolor}!important;color: {$page2_bom_col}!important;border: 1px {$page2_bom_bor_col} solid!important;}
                    {$addon_id} .pagese{color:{$page2_be_col}; background:{$page2_be_bgcolor}; border:1px solid {$page2_be_bor_col};}
                    {$addon_id} .pageUp,.pageDown{color:{$page2_ud_col}; background:{$page2_ud_bgcolor}; border:1px solid {$page2_ud_bor_col};}
                    {$addon_id} .pages-box-dis{position: relative;left: {$page_location}px;top {$page_top_location}px;}
                </style>  
                ";
        //获取产品数据
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $item = JwpagefactoryHelperCategories::GetCategoriesthree('com_goods', $site_id, $detail_page_id, $layout_id, $company_id);
        $item = $this->subTree($item);

        if($nav_ordering!='DESC'){
            $last_names = array_column($item,'ordering');
            array_multisort($last_names,SORT_ASC,$item);
            foreach ($item as $k=>$v){
                $last_names = array_column($item[$k]['sub'],'ordering');
                array_multisort($last_names,SORT_ASC,$item[$k]['sub']);
                foreach ($item[$k]['sub'] as $k2=>$v2){
                    $last_names = array_column($item[$k]['sub'][$k2],'ordering');
                    array_multisort($last_names,SORT_ASC,$item[$k]['sub'][$k2]);
                }
            }

        }else{
            $item = $this->Sort($item,'ordering','DESC');
            foreach ($item as $k=>$v){
                $item[$k]['sub'] = $this->Sort($item[$k]['sub'],'ordering','DESC');
                foreach ($item[$k]['sub'] as $k2=>$v2){
                    $item[$k]['sub'][$k2]['sub'] = $this->Sort($item[$k]['sub'][$k2]['sub'],'ordering','DESC');
                }
            }
        }
        if($tab_cid)
        {
            $items = [];
             foreach ($item as $k=>$v)
             {
                if($v['tag_id'] == $tab_cid)
                {
                    $items[] = $v;
                }
                else
                {
                    foreach ($item[$k]['sub'] as $k2=>$v2)
                    {
                        if($v2['tag_id'] == $tab_cid)
                        {
                            $v2['parent_id'] = 1;
                            $items[] = $v2;
                        }
                        else
                        {
                            foreach ($v2['sub'] as $k3=>$v3)
                            {
                                if($v3['tag_id'] == $tab_cid)
                                {
                                    $v3['parent_id'] = 1;
                                    $items[] = $v3;
                                }
                            }
                        }
                    }
                }
            }
            $item = $items;
            // print_r($item);die;
        }
//        var_dump($item);
//        exit();
        //获取列表数据
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
        require_once $article_helper;
        //数据处理从第几条开始

        foreach ($item as $ke1 => $va1)
        {
            if ($ke1 < $type_start - 1 || $ke1 > $type_num)
            {
                unset($item[$ke1]);
            }
        }
        $onesub = 0;
        $twosub = 0;
        $threesub = 0;
        if($cpcatid!=0)
        {
            foreach($item as $k => $v)
            {
                if($cpcatid==$v['tag_id'])
                {
                    $onesub = $cpcatid;
                }
                else
                {
                    foreach($v['sub'] as $kk => $vv)
                    {
                        if($cpcatid==$vv['tag_id'])
                        {
                            $onesub = $v['tag_id'];
                            $twosub = $cpcatid;
                        }
                        else
                        {
                            foreach($vv['sub'] as $kkk => $vvv)
                            {
                                if($cpcatid==$vvv['tag_id'])
                                {
                                    $onesub = $v['tag_id'];
                                    $twosub = $vv['tag_id'];
                                    $threesub = $cpcatid;
                                }
                            }
                        }
                    }
                }
            }
        }

        //外层div
        if($style_type=='type1')
        {
            $output .="
            <div class=\"content\">
                <div class=\"sec-content\">
                ";
            $item = array_merge($item);

            $goods_arr=[];
            $goodslists =[];
            $ccccc=0;
            foreach ($item as $k=>$v){
                $goodslists[] = $v;
                    // $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v['tag_id'], $limit,$ordering);
                    //     if(!empty($Don_item)){
                    //     $Don_item['superior_id']=$Don_item[0]['pid'];
                    //     array_push($goods_arr, $Don_item);
                    //     }

                    //一个一二三级的整体
                    $output .="<div class=\"sec-content-box\">";
                        if($onesub==0)
                        {
                            if($k==0){

                                if($cpcatid==0)
                                {
                                    $cpcatid = $v['tag_id'];
                                }
                                //一级导航
                                $output .="
                                        <div class=\"sec-content-box-t\">";
                                if(isset($page) && $page>1)
                                {
                                    $output .='<a class=\'anv_li one_nav_0\' value=\''.$v['tag_id'].'\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                                }
                                else
                                {
                                    if($link_tz_fs==1)
                                    {
                                        $output .='<a class=\'anv_li one_nav_0\' value=\''.$v['tag_id'].'\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                                    }
                                    else
                                    {
                                        $output .='<a class=\'anv_li one_nav_0\' value=\''.$v['tag_id'].'\' data-id=\'list_tl_'.$v['tag_id'].'\' href="javascript:;" onclick="ajaxcatList'.$shiyong_type.$addonss_id.$v['tag_id'].'('.$v['tag_id'].')">'.$v['title'].'</a>';
                                    }
                                }
                                if($show_one_nav)
                                {
                                    $output .="<div class=\"sec_dj sec_check\" style='display:none;'>";
                                    $show_nav=1;
                                }
                                else
                                {
                                    $output .="<div class=\"sec_dj sec_check\">";
                                }

                                if(!empty($v['sub']))
                                {
                                    $output.="
                                                <div style='font-size:".$nav_icon_1_size."px'>".$nav_icon_1."</div>
                                                <div>-</div>";
                                }

                                $output.="
                                            </div>
                                        </div>";
                                if($tab_cid==0)
                                {
                                    $ccccc=$v['tag_id'];
                                }
                                else
                                {
                                    $ccccc=$tab_cid;
                                }

                            }else{

                                //一级导航
                                $output .="
                                        <div class=\"sec-content-box-t\">";
                                if(isset($page) && $page>1)
                                {
                                    $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                                }
                                else
                                {
                                    if($link_tz_fs==1)
                                    {
                                        $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                                    }
                                    else
                                    {
                                        $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="javascript:;" onclick="ajaxcatList'.$shiyong_type.$addonss_id.$v['tag_id'].'('.$v['tag_id'].')">'.$v['title'].'</a>';
                                    }
                                }
                                if($show_one_nav)
                                {
                                    $output .="
                                            <div class=\"sec_dj sec_check\" style='display:none;'>";
                                    $show_nav=1;
                                }
                                else
                                {
                                    $output .="
                                            <div class=\"sec_dj sec_check\">";
                                }

                                if(!empty($v['sub']))
                                {
                                    $output.="
                                                <div style='font-size:".$nav_icon_1_size."px'>".$nav_icon_1."</div>
                                                <div>-</div>";
                                }
                                 $output.="            </div>
                                        </div>";
                            }
                        }
                        else
                        {
                            //一级导航
                            $output .="
                            <div class=\"sec-content-box-t\">";
                            if(isset($page) && $page>1)
                            {
                                $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                            }
                            else
                            {
                                if($link_tz_fs==1)
                                {
                                    $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="'.$urlpath.$v['tag_id'].'">'.$v['title'].'</a>';
                                }
                                else
                                {
                                    $output .='<a class=\'anv_li\' data-id=\'list_tl_'.$v['tag_id'].'\' href="javascript:;" onclick="ajaxcatList'.$shiyong_type.$addonss_id.$v['tag_id'].'('.$v['tag_id'].')">'.$v['title'].'</a>';
                                }
                            }
                            $output .="
                                        <div class=\"sec_dj sec_check\" >";
                            if(!empty($v['sub']))
                            {
                                $output.="
                                            <div style='font-size:".$nav_icon_1_size."px'>".$nav_icon_1."</div>
                                            <div>-</div>";
                            }
                                $output.="            </div>
                                    </div>";
                        }


                        // 二级导航
                        if(!empty($v['sub'])){
                            foreach ($v['sub'] as $k2=>$v2){
                                $goodslists[] = $v2;
                                if($twosub==0)
                                {
                                    if($onesub==0)
                                    {
                                        if($k==0){
                                            if($show_nav==0){
                                                $output .="<div class=\"sec-content-box-b\" style=\"display: block;\">";
                                            }else{
                                                $output .="<div class=\"sec-content-box-b\" style=\"display: none;\">";
                                            }
                                        }else{
                                            $output .="<div class=\"sec-content-box-b\" style=\"display: none;\">";
                                        }
                                    }
                                    else
                                    {
                                        if($onesub==$v['tag_id'])
                                        {
                                            $output .="<div class=\"sec-content-box-b\" style=\"display: block;\">";
                                        }
                                        else
                                        {
                                            $output .="<div class=\"sec-content-box-b\" style=\"display: none;\">";
                                        }

                                    }
                                }
                                else
                                {
                                    if($onesub==$v['tag_id'])
                                    {
                                        $output .="<div class=\"sec-content-box-b\" style=\"display: block;\">";
                                    }
                                    else
                                    {
                                        $output .="<div class=\"sec-content-box-b\" style=\"display: none;\">";
                                    }
                                }
                                $output .=" <div class=\"sec-content-box-bli\">";
                                // $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v2['tag_id'], $limit);

                                // if(!empty($Don_item)){
                                //     $Don_item['superior_id']=$Don_item[0]['pid'];
                                //     array_push($goods_arr, $Don_item);
                                // }
                                $output .='<div class="sec-content-box-bli-t">';
                                if(!empty($v2['sub']))
                                {
                                    $output.='<span style="font-size:'.$nav_icon_2_size.'px">'.$nav_icon_2.'</span> ';
                                }
                                if(isset($page) && $page>1)
                                {
                                    $output .='<a class=\'anv_li\' style="cursor:pointer;" data-id=\'list_tl_'.$v2['tag_id'].'\' href="'.$urlpath.$v2['tag_id'].'">'.$v2['title'].'</a></div>';
                                }
                                else
                                {
                                    if($link_tz_fs==1)
                                    {
                                        $output .='<a class=\'anv_li\' style="cursor:pointer;" data-id=\'list_tl_'.$v2['tag_id'].'\' href="'.$urlpath.$v2['tag_id'].'">'.$v2['title'].'</a></div>';
                                    }
                                    else
                                    {
                                        $output .='<a class=\'anv_li\' style="cursor:pointer;" data-id=\'list_tl_'.$v2['tag_id'].'\' href="javascript:;" onclick="ajaxcatList'.$shiyong_type.$addonss_id.$v2['tag_id'].'('.$v2['tag_id'].')">'.$v2['title'].'</a></div>';
                                    }
                                }


                                if(!empty($v2['sub'])){
                                    foreach ($v2['sub'] as $k3=>$v3){
                        //                 $goodslists[] = $v3;
                        //                 $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v3['tag_id'], $limit);
                        //                 if(!empty($Don_item)){
                        //                     $Don_item['superior_id']=$Don_item[0]['pid'];
                        //                     array_push($goods_arr, $Don_item);
                        //                 }
                                    if($threesub == 0)
                                    {
                                        if($twosub==0)
                                        {
                                            if($onesub==0)
                                            {
                                                if($show_nav_two==0){
                                                    if($k3==0)
                                                    {
                                                        $output .="<div class=\"sec-content-box-bli-b\" style=\"display: block;\">";
                                                    }
                                                    else
                                                    {
                                                        $output .="<div class=\"sec-content-box-bli-b\" style=\"display: block;\">";
                                                    }
                                                }else{
                                                    $output .="<div class=\"sec-content-box-bli-b\" style=\"display: none;\">";
                                                }

                                            }
                                            else
                                            {
                                                $output .="<div class=\"sec-content-box-bli-b\" style=\"display: none;\">";
                                            }
                                        }
                                        else
                                        {
                                            if($twosub==$v2['tag_id'])
                                            {
                                                $output .="<div class=\"sec-content-box-bli-b\" style=\"display: block;\">";
                                            }
                                            else
                                            {
                                                $output .="<div class=\"sec-content-box-bli-b\" style=\"display: none;\">";
                                            }

                                        }

                                    }
                                    else
                                    {
                                        if($twosub==$v2['tag_id'])
                                        {
                                            $output .="<div class=\"sec-content-box-bli-b\" style=\"display: block;\">";
                                        }
                                        else
                                        {
                                            $output .="<div class=\"sec-content-box-bli-b\" style=\"display: none;\">";
                                        }
                                    }
                        //                 //三级导航
                                        if(isset($page) && $page>1)
                                        {
                                            $output .='<a data-id=\'list_tl_'.$v3['tag_id'].'\' href="'.$urlpath.$v3['tag_id'].'"  class="sec-content-box-bli-bli anv_li" style="cursor:pointer;">'.$v3['title'].'</a>';
                                        }
                                        else
                                        {
                                            if($link_tz_fs==1)
                                            {
                                                $output .='<a data-id=\'list_tl_'.$v3['tag_id'].'\' href="'.$urlpath.$v3['tag_id'].'"  class="sec-content-box-bli-bli anv_li" style="cursor:pointer;">'.$v3['title'].'</a>';
                                            }
                                            else
                                            {
                                                $output .='<a data-id=\'list_tl_'.$v3['tag_id'].'\' href="javascript:;" onclick="ajaxcatList'.$shiyong_type.$addonss_id.$v3['tag_id'].'('.$v3['tag_id'].')" class="sec-content-box-bli-bli anv_li" style="cursor:pointer;">'.$v3['title'].'</a>';
                                            }
                                        }

                                    $output .="</div>";
                                    }
                                }
                                $output .="</div>";
                                $output .="</div>";
                            }
                        }
                    $output .="</div>";
            }
            $output .="</div>";


        }
        else
        {
            $output .="
                <style>
                {$addon_id} #nav li:hover ul ul,#nav li.sfhover ul ul,#nav li:hover ul ul ul{left: -999em;}
                {$addon_id} #nav li:hover ul, #nav li li:hover ul,#nav li.sfhover ul, #nav li li.sfhover ul,#nav li li li:hover ul{left: auto;}
                {$addon_id} #nav li:hover ul,#nav li.sfhover ul{left: 233px;top: 0px;}
                {$addon_id} .m_type_box {width: 231px;height: auto;float: left;position: relative;z-index: 999990;border-style: none;border-color: inherit;border-width: 0px;padding: 0;list-style-type: none;margin-left: auto;margin-top: 0;margin-bottom: 0;}
                {$addon_id} div, ul, li, dt, dl, dd, h1, h2, h3, h4, h5, h6, h7, img, p, span {border-style: none;border-color: inherit;border-width: 0px;padding: 0;list-style-type: none;margin-left: auto;margin-top: 0;margin-bottom: 0;}
                {$addon_id} #nav li {position: relative;}
                {$addon_id} #nav a.selected {width: 231px;height: 47px;float: left;background: url(http://cowayscaster.cn/templates/default/images/hov2.jpg) no-repeat left top;font-family: 微软雅黑;font-size: 18px;color: #fff;text-align: left;font-weight: normal;line-height: 47px;}
                {$addon_id} #nav li a {margin-top: -1px;width: 231px;height: 47px;float: left;margin-left: 1px;background: url(http://cowayscaster.cn/templates/default/images/hov1.jpg) no-repeat left top;padding-left: 18px;font-family: 微软雅黑;font-size: 16px;color: #333333;text-align: left;font-weight: normal;line-height: 47px;}
                {$addon_id} #nav li ul {position: absolute;z-index: 999;left: -999em;width: 291px;height: auto!important;min-height: 230px;font-weight: normal;margin: 0px;padding: 0px;}
                {$addon_id} #nav li li {width: 291px;}
                {$addon_id} #nav li {position: relative;}
                {$addon_id} #nav ul li {list-style: none;padding: 0px;margin: 0px;width: 233px;text-align: left;}
                {$addon_id} #nav li li a {width: 291px;height: 36px;float: left;background: url(http://cowayscaster.cn/templates/default/images/hov21.png) no-repeat left top;padding-left: 14px;font-family: 微软雅黑;font-size: 14px;color: #333333;text-align: left;font-weight: normal;line-height: 36px;}
                {$addon_id} #nav li li li a:hover {width: 202px;height: 36px;float: left;background: #d4d4d4;font-family: 微软雅黑;font-size: 14px;color: #333333;text-align: center;font-weight: normal;line-height: 36px;}
                {$addon_id} #nav li li li a {width: 202px;height: 36px;float: left;padding: 0;background: #f1f1f1;font-family: 微软雅黑;font-size: 14px;color: #333333;text-align: center;font-weight: normal;line-height: 36px;}
                {$addon_id} a:link {text-decoration: none;}
                {$addon_id} #nav ul li {list-style: none;padding: 0px;margin: 0px;width: 233px;text-align: left;}
                {$addon_id} #nav li li a:hover {width: 291px;height: 36px;float: left;background: url(http://cowayscaster.cn/templates/default/images/hov22.png) no-repeat left top;font-family: 微软雅黑;font-size: 14px;color: #333333;text-align: left;font-weight: normal;line-height: 36px;}
                {$addon_id} #nav li a:hover {width: 231px;height: 47px;float: left;background: url(http://cowayscaster.cn/templates/default/images/hov2.jpg) no-repeat left top;font-family: 微软雅黑;font-size: 16px;color: #fff;text-align: left;font-weight: normal;line-height: 47px;}
                {$addon_id} #nav li ul ul {margin: 0px 0 0 298px;height: auto!important;min-height: 230px;}
                {$addon_id} #nav li ul {position: absolute;z-index: 999;left: -999em;width: 291px;height: auto!important;min-height: 230px;font-weight: normal;margin: 0px;padding: 0px;}

                </style>
            ";
            $output .="
            <div class=\"content\">
            <div class=\"sec-content\">
            <div class='m_type_box'>
            <ul id='nav'>
            ";
            $goods_arr=[];
            $goodslists =[];
            foreach ($item as $k=>$v)
            {
                $goodslists[] = $v;
                $output .="
                <li>
                <a class='' href='javascript:;' onclick='ajaxcatList".$shiyong_type.$addonss_id."(".$v['tag_id'].")'>".$v['title']."</a>
                ";

                $Don_item = JwpagefactoryHelperGoods::getGoodsListTwo($detail_page_id, $company_id, $layout_id, $site_id, $v['tag_id'], $limit,$ordering);
                if(!empty($Don_item)){
                    $Don_item['superior_id']=$Don_item[0]['pid'];
                    array_push($goods_arr, $Don_item);
                }
                if(!empty($v['sub']))
                {
                    $output .="
                        <ul style='margin-top:0;'>
                    ";
                    foreach ($v['sub'] as $k2=>$v2){
                        $goodslists[] = $v2;
                        $output .="
                        <li>
                        <a href='javascript:;' onclick='ajaxcatList".$shiyong_type.$addonss_id."(".$v2['tag_id'].")'>".$v2['title']."</a>
                        ";
                        if(!empty($v2['sub'])){
                            $output .="
                            <ul style='margin-top:0;'>
                            ";
                            foreach ($v2['sub'] as $k3=>$v3){
                                $goodslists[] = $v3;
                                $output .="
                                <li>
                                <a href='javascript:;' onclick='ajaxcatList".$shiyong_type.$addonss_id."(".$v3['tag_id'].")'>".$v3['title']."</a>
                                </li>
                                ";
                            }
                            $output .="
                            </ul>
                            ";
                        }
                        $output .="
                        </li>
                        ";

                    }
                    $output .="
                            </ul>
                            ";
                }
                $output .="
                </li>
                ";
            }
            $output .="
            </ul>
            </div>
            </div>
            ";
        }

        //内容部分
        $output .="
            <style>
            
            @media (min-width: 1200px) {
                {$addon_id} .cqr-tulisu-xianzhi{
                    display: none;
                }
                {$addon_id} .cqr-tulisu-xianzhi.active{
                    display: block;
                    width: {$nav_max_widthss}% !important;
                }
            }
            @media (max-width: 1200px) {
                {$addon_id} .cqr-tulisu-xianzhi{
                    display: none;
                }
                {$addon_id} .cqr-tulisu-xianzhi.active{
                    display: block;
                }
            }
            {$addon_id}  .page_plug{
                width: 90%;
                margin: 5px auto;
                text-align: center;
                display:flex;
                justify-content:center;
                flex-wrap:wrap;
            }
            {$addon_id}  .downlist{
                display: none !important;
            }
            {$addon_id}  .page_plug a{
                padding: 3px 8px;
                border: 1px solid {$page1_bordercolor};
                margin-right: 5px;
                text-decoration: none;
                color: {$page1_fontcolor};
                background:{$page1_bgcolor};
                min-width:{$page1_min_width}px;
                margin-bottom:{$page1_margin_bottom}px;
            }
            {$addon_id}  .page_plug .curPage {
              border: 1px solid {$page1_cur_bordercolor};
              color: {$page1_cur_fontcolor};
              background:{$page1_cur_bgcolor};
            }
            </style>
        ";

        // print_r($goodslists);die;
        // foreach ($goodslists as $k => $v){

            // if($k==0){//与导航相对应的id
            //     $output .="<div class=\"cqr-tulisu-xianzhi active\" id='listss".$v['tag_id']."' style=\"background:url() no-repeat ;\">";
            // }else{
            //     $output .="<div class=\"cqr-tulisu-xianzhi\" id='listss".$v['tag_id']."' style=\"background:url() no-repeat ;\">";
            // }
            $catiditem = [];
            $nnbb = 0;
            foreach($item as $iik => $iiv)
            {
                $catiditem[] = $iiv['tag_id'];
                foreach($iiv['sub'] as $iik1 => $iiv1)
                {
                    $catiditem[] = $iiv1['tag_id'];
                    foreach($iiv1['sub'] as $iik2 => $iiv2)
                    {
                        $catiditem[] = $iiv2['tag_id'];
                    }
                }
            }
            foreach ($catiditem as $kk1=>$vv1)
            {
                if(isset($cpcatid) && $cpcatid==$vv1)
                {
                    $output .="<div class=\"cqr-tulisu-xianzhi active listslist\" id='listss".$vv1."' style=\"background:url() no-repeat ;\">";
                    $nnbb = 1;
                }
                else
                {
                    if($kk1 == 0 && $cpcatid==0)
                    {
                        if($nnbb==0)
                        {
                            $output .="<div class=\"cqr-tulisu-xianzhi active listslist\" id='listss".$vv1."' style=\"background:url() no-repeat ;\">";
                        }
                        else
                        {
                            $output .="<div class=\"cqr-tulisu-xianzhi active listslist downlist\" id='listss".$vv1."' style=\"background:url() no-repeat ;\">";
                        }
                    }
                    else
                    {
                        $output .="<div class=\"cqr-tulisu-xianzhi active listslist downlist\" id='listss".$vv1."' style=\"background:url() no-repeat ;\">";
                    }
                }

                //一个分类下的内容
                $output .="<div id='cont-list' class=\"cqr-tulisu-xianzhi-1 cqr-tulisu-xianzhi-2 cqr-tulisu-xianzhi-3\">";
                // $goodsitem = JwpagefactoryHelperGoods::getGoodsList($limit, $ordering, $vv1, 1, '','', $detail_page_id, $page, $company_id, $layout_id, $site_id);
                $goodsitem = JwpagefactoryHelperGoods::getGoodsListProductData($limit, $ordering, $vv1, 1, '', '', $detail_page_id, $page, $company_id, $layout_id, $site_id,$show_pro_data);
                $items_count = JwpagefactoryHelperGoods::getGoodsListProductCount($limit, $ordering, $vv1, 1, '', '', $detail_page_id, $page, $company_id, $layout_id, $site_id,$show_pro_data);
                // $itemd = array_merge($goodsitem);

                // $items_count = JwpagefactoryHelperGoods::getGoodsCount($ordering, $vv1, 1, $company_id, $site_id);
                if($goodsitem)
                {
                    foreach($goodsitem as $k => $v)
                    {
                        $output .='<div class="box" style="display: block">';
                        $output .='<a href="'.$v->link.'">';
                        $output .='<div class="imgBox" style="width: 100%;">';
                        $output .='<img src="'.$v->image_thumbnail.'" alt="">';
                        $output .='</div>';
                        $output .='<h2 class="title" >'.$v->title.'</h2>';
                        $output .='</a>';
                        $output .='</div>';
                    }
                    if ($show_page) {
                        if ($page_style_selector == 'page01') {

                            $all_page = 1;
                            if ($limit) {
                                $all_page = ceil($items_count / $limit);
                            }
                            $output .= '<div class="page_plug" style="position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px;">';
                            // 判断是不是第一页
                            //    if($page != 1) {
                            //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                            //        $output .= '<a class="page_num"  href="' . $url . '&page=' . ($page-1) . '&cpcatid=' .$goods_catid . '"> << </a>';
                            //    }
                            for ($i = 1; $i <= $all_page; $i++) {
                //                            echo $i;
                //                            echo $page;
                                if ($page == $i) {
                                    $output .= '<a class="curPage">' . $i . '</a>';
                                } else {
                                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid','cjid']);
                                    $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $vv1 . '&cjid='.$addonss_id.'">' . $i . '</a>';
                                }
                            }
                            //                    // 判断是不是最后一页
                            //    if($page != $all_page) {
                            //        $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page','zcpcatid','cpcatid']);
                            //        $output .= '<a class="page_num" href="' . $url . '&page=' . ($page+1) . '&cpcatid=' .$goods_catid . '"> >> </a>';
                            //    }

                            $output .= '</div>';
                            // $output .= '<div class="page_plug">共' . $items_count . '条</div>';
                        }


                        if ($page_style_selector == 'page02') {
                            $all_page = 1;
                            if ($limit) {
                                $all_page = ceil($items_count / $limit);
                            }

                            /* 编辑器省略号分页 */
                            $output .= '<div class="zxf_pagediv " id="false_page" style=" position: relative;left: ' . $page_location . 'px; top:' . $page_top_location . 'px; width:100%;display:flex;align-items: center;justify-content: center;opic">';
                            if ($page != 1) {
                                $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid','cjid']);
                                $output .= '    <a href="' . $url . '&page=' . ($page - 1) . '&cpcatid=' . $vv1 . '&cjid='.$addonss_id.'" class="prebtn">上一页</a>';
                            }

                            $output .= ' <div class="page">';
                            for ($i = 1; $i <= $all_page; $i++) {

                                if ($page == $i) {
                                    $output .= '<a class="current">' . $i . '</a>';
                                } else {
                                    $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid','cjid']);
                                    $output .= '<a class="zxfPagenum" href="' . $url . '&page=' . $i . '&cpcatid=' . $vv1 . '&cjid='.$addonss_id.'">' . $i . '</a>';

                                }
                            }
                            $output .= '</div>';
                            // 判断是不是最后一页
                            if ($page != $all_page) {
                                $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid','cjid']);
                                $output .= '    <a href="' . $url . '&page=' . ($page + 1) . '&cpcatid=' . $vv1 . '&cjid='.$addonss_id.'" class="nextbtn">下一页</a>';
                            }

                            $output .= '</div>';
                            //下面是假的

                            $output .= '<script>
                        
                            </script>';
                        }
                    }
                }
                else
                {
                    $output .='<p class="alert alert-warning">暂无数据</p>';
                }

                $output .="</div>";
                //二级数据结尾div
                $output .="</div>";
                $output .= '
                    <script>
                    //点击分类时触发事件
                    function ajaxcatList'.$shiyong_type.$addonss_id.$vv1.'(catid)
                    {
    
                        var lid="list_tl_"+catid;
                        $("'.$addon_id.' .anv_li").parent().removeClass("aat");
                        $("'.$addon_id.' .anv_li").removeClass("aat");

                        $("'.$addon_id.' .anv_li").each(function(){
                            var sd=$(this).attr("data-id");
                            
                            if(sd==lid){
                                $(this).parent().addClass("aat");
                                $(this).addClass("aat");
                            }

                        })
                        
                        $("'.$addon_id.' .listslist").addClass("downlist");
                        $("'.$addon_id.' #listss"+catid).removeClass("downlist");
                    }
                    </script>
                ';
                if($link_tz_fs){
                    if($cpcatid && $cjid){
                        $output .= '
                            <script>
                                var lid="list_tl_"+'.$cpcatid.';

                                $("#jwpf-addon-'.$cjid.' .anv_li").each(function(){
                                    var sd=$(this).attr("data-id");
                                    if(sd==lid){
                                        $(this).parent().addClass("aat");
                                        $(this).addClass("aat");
                                    }

                                })

                            </script>
                        ';
                    }
                }

            }

        // }

        $output .="</div>";

        //分页
        $output .= "
                <script>
                    function ajaxList".$shiyong_type.$addonss_id."(page,catid){
                        setCookie('page',page);
                        setCookie('tlpc_catid',catid);
                        var cat_id_page;
                        if(getCookie(\"tlpc_catid\")!=null)
                        {
                        cat_id_page = getCookie(\"tlpc_catid\");
                        }else{
                        cat_id_page = catid;//分类id
                        }
                        //var cat_id_page = jQuery('{$addon_id} .one_nav_0').attr('value');//分类id
                        var s='';
                        var pagenumber = {$page_count};
                        pagenumber = Number(pagenumber)-2;
                        jQuery.ajax({
                            type: \"POST\",
                            url: \"{$urlpath}\",
                            async: true,
                            dataType: 'json',
                            data: {
                                'page':page,//分页id
                                'cat_id_page':cat_id_page,//分类id
                                'page_number':{$limit},//每页几条
                                'company_id':{$company_id},
                                'site_id':{$site_id},
                                'layout_id':{$layout_id},
                                'detail_page_id':{$detail_page_id},//详情页id
                            },
                            success: function (res) {
                                //console.log(res)
                                if(res.code==200){   
                                    if(res.data.total==0)
                                    {
                                        s += '<p class=\"alert alert-warning\">暂无数据</p>';
                                    }
                                    else
                                    {
                                        for(let i=0;i<res.data.data.length;i++){
                                            s +='<div class=\"box box-page'+i+'\" style=\'display: block\'>';
                                            s +='<a href=\"'+res.data.data[i].link+'\">';
                                            s +='  <div class=\"imgBox\" style=\"width: 100%;\">';
                                            s +='      <img src=\"'+res.data.data[i].image_intro+'\" alt=\"\">';
                                            s +='  </div>';
                                            s +='  <h2 class=\"title\" >'+res.data.data[i].title+'</h2>';
                                            s +='</a>';
                                            s +='</div>';
                                    }
                                    }
                                    $(\"{$addon_id} .cqr-tulisu-xianzhi-1\").html(s);
                                    var pg = '';
                                    var xiao = 1;
                                    var da = res.page_limit;
                                    var spage = page-1<1?1:page-1;
                                    var xpage = page+1>da?da:page+1;
                                    if(page!=1){     
                                    pg += \"<div class='pageUp' onclick='ajaxList".$shiyong_type.$addonss_id."(\"+spage+','+catid+\")'>上一页</div>\";
                                    }
                                    pg += '<div class=\"pageList\">';
                                    pg += '<ul>';
                                    page = Number(page);
                                         for(let j=page-2;j<page+pagenumber;j++){   
                                             if(j>=xiao && j<=da){
                                                if( j == page )
                                                {
                                                    pg += \"<li class='page_content' onclick='ajaxList".$shiyong_type.$addonss_id."(\"+j+','+catid+\")'>\"+j+\"</li>\";
                                                }
                                                else
                                                {
                                                    pg += \"<li onclick='ajaxList".$shiyong_type.$addonss_id."(\"+j+','+catid+\")'>\"+j+\"</li>\";
                                                }
                                            }
                                         }
                                    pg += '</ul>';
                                    pg +='</div>';
                                    if(page<da){
                                    pg +=\"<div class='pageUp' onclick='ajaxList".$shiyong_type.$addonss_id."(\"+xpage+','+catid+\")'>下一页</div>\";
                                    }
                                    $(\"{$addon_id} .cb\").html(pg);
                                }
                            }
                        })
                    }
                
                    function setCookie(name,value)
                    {
                        var Days = 30;
                        var exp = new Date();
                        exp.setTime(exp.getTime() + Days*24*60*60*1000);
                        document.cookie = name + \"=\"+ escape (value) + \";expires=\" + exp.toGMTString();
                    }
                
                    function getCookie(name)
                    {
                        var arr,reg=new RegExp(\"(^| )\"+name+\"=([^;]*)(;|$)\");
                        if(arr=document.cookie.match(reg))
                        return unescape(arr[2]);
                        else
                        return null;
                    }
                </script>
        ";


        //导航
        $output .="
            <script>
                //下拉列表事件
                jQuery('{$addon_id} .sec_dj').click(function(){
                    var dis = jQuery(this).parents(' .sec-content-box').find(' .sec-content-box-b').css('display')
                //                    console.log(dis=='block')
                    
                    if(jQuery(this).parents('.sec-content-box').find('.sec-content-box-b').css('display') == 'none'){
                        jQuery('{$addon_id} .sec-content-box-b').slideUp(300)
                        jQuery(this).parents('.sec-content-box').find('.sec-content-box-b').slideDown(300)
                        jQuery('{$addon_id} .sec_dj').removeClass('sec_check');
                        jQuery(this).addClass('sec_check');
                        
                    }else{
                        jQuery(this).parents('.sec-content-box').find('.sec-content-box-b').slideUp(300)
                        jQuery(this).removeClass('sec_check');
                    }
                })
                //二级三级下拉列表
                jQuery('{$addon_id} .sec-content-box-bli-t span').click(function(){
                    if(jQuery(this).parents('.sec-content-box-bli').find('.sec-content-box-bli-b').css('display') == 'none'){
                        jQuery('{$addon_id} .sec-content-box-bli-t span').parents('.sec-content-box-bli').find('.sec-content-box-bli-b').slideUp(300)
                        jQuery(this).parents('.sec-content-box-bli').find('.sec-content-box-bli-b').slideDown(300)
                    }else{
                        jQuery(this).parents('.sec-content-box-bli').find('.sec-content-box-bli-b').slideUp(300)
                    }
                })
                
                
            </script>
        ";

        return $output;
    }

    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }

    public static function getTemplate()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        };

        $output = '';
        $output .= '
        <#
            var addonId = "jwpf-addon-"+data.id;

            var nav_bg_check = data.nav_bg_check || "0";
            var nav_bgxz_color = data.nav_bgxz_color || "#baab9e";
            var nav_font_color = data.nav_font_color || "#fff";
            
        #>
        <style type="text/css">
            <# if(data.nav_bg_check){ #>
                #{{ addonId }} .aat{
                    background:{{data.nav_bgxz_color}}!important;
                    color:{{data.nav_font_color}}!important;
                }
            <# } #>
            #{{ addonId }} .content{
                width: 1100px;
            }
            #{{ addonId }} .content .sec-content{
                width: 246px;
                float: left;
                background: {{data.nav_bg_color}};
                padding-top: 20px;
                margin-right: 20px;
                padding-bottom: 60px;
                
            }
            #{{ addonId }} .content .sec-content .sec-content-box{
                width: 100%;
            }
            #{{ addonId }} .content .sec-content .sec-content-box-t{
                cursor: pointer;
                position: relative;
                width: 100%;
                margin-left: {{data.nav_margin_left_1}}px;
                height: {{data.nav_height_1}}px;
                background: {{data.nav_bg_color_1}};
                font-size: {{data.nav_1_size||18}}px;
                line-height: 50px;
                color: {{data.nav_color_1}};
                text-align: center;
                margin-bottom: 10px;
            }
             #{{ addonId }} .content .sec-content .sec-content-box-t a{
                    color:{{data.nav_color_1}};
             }
            #{{ addonId }} .content .sec-content .sec-content-box-t .sec_dj div{
                position: absolute;
                top: 0;
                right: 10px;
                bottom: 0;
                margin: auto;
                font-size: 25px;
                line-height: 46px;
                color: #fff;
            }
            #{{ addonId }} .sec-content-box-b{
        
                width: calc(100% - 20px);
                margin: 0 auto;
            }
            #{{ addonId }} .sec-content-box-bli{
                transition: .3s;
                display: block;
                text-decoration: none;
                width: 100%;
                box-sizing: border-box;
                margin-bottom: 10px;
            }
            #{{ addonId }} .sec-content-box-bli-t{
                display: block;
                text-decoration: none;
                transition: .3s;
                width: {{data.nav_width_2}}%;
                margin-left:{{data.nav_margin_left_2}}px;
                font-size: {{data.nav_2_size||16}}px;
                color: {{data.nav_color_2}};
                line-height: 46px;
                background: {{data.nav_bg_color_2}};
                padding-left: 20px;
                margin-bottom: 10px;
                box-sizing: border-box;
            }
            #{{ addonId }} .sec-content-box-bli-t a{
                color: {{data.nav_color_2}};
            }
            #{{ addonId }} .sec-content-box-bli-b{
                width: {{data.nav_width_3}}%;
                background: {{data.nav_bg_color_3}};
                margin-left:{{data.nav_margin_left_3}}px;
            }
            #{{ addonId }} .sec-content-box-bli-bli{
                transition: .3s;
                display: block;
                text-decoration: none;
                width: 100%;
                font-size: {{data.nav_3_size||14}}px;
                line-height: 40px;
                background: {{data.nav_bg_color_3}};
                color: {{data.nav_color_3}};
                text-align: center;
                border-bottom: 1px dashed #ddd;
            }
            #{{ addonId }} .sec-content-box-t .sec_dj div:last-child{
                display: none;
                font-size: 35px;
                position: absolute;
                top: 0;
                right: 10px;
                bottom: 0;
                margin: auto;
                line-height: 46px;
                color: #fff;
            }
            #{{ addonId }} .sec-content-box-bli-t span{
                font-size: 20px;
                float:left;
            }
            #{{ addonId }} .sec-content-box-bli-t p{
                color: {{data.nav_color_2}};
                text-decoration: none;
                margin-left: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        
        
            /*右侧列表*/
            #{{ addonId }} .cqr-tulisu-xianzhi{
                width: 50%;
                margin: 0;
                float: left;
                width: 50%;
                margin: 0;
                float: left;
                position: relative;
                top: {{data.vertical_position}}px;
                left: {{data.left_position}}px;
            }
            #{{ addonId }} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3{
                clear: both;
                text-align: left;
                width: 856px;
                display: flex;
                flex-wrap: wrap;
                background:{{data.page2_cont_bgcolor}};
            }
            #{{ addonId }} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3 .box{
                width: {{data.left_cont_width}}%;
                overflow: hidden;
                box-sizing: border-box;
                margin: {{data.left_cont_width_jg}}%;
                position: relative;
                display: inline-block;
                text-align: center;
                padding: {{data.page2_list_padding}}
            }
            #{{ addonId }} .cqr-tulisu-xianzhi .imgBox img{
                transition: all 0.6s linear;
                display: block;
            }
            #{{ addonId }} .cqr-tulisu-xianzhi .box a{
                display: flex;
                flex-direction: column;
                align-items: center;
                color: #535353;
                text-decoration: none; 
            }
            #{{ addonId }} .cqr-tulisu-xianzhi .title{
                padding: 20px 0;
                width: 100%;
                height:{{data.title_font_height}}px;
                text-align: center;
                font-size: {{data.page2_list_size}}px;
                background: {{data.page2_list_bgcolor}};
                color: {{data.page2_list_color}};
            }
            #{{ addonId }} .pages-box{
                display: flex;
                width: 856px;
                justify-content: center;
            }
            #{{ addonId }} .pages-c1{
                display: inline-block;
            }
            #{{ addonId }} .pages-c2{
                text-align: center;
            }
            #{{ addonId }} .pages-c2>a{
                display: inline-block;
                width: 55px;
                height: 30px;
                border-radius: 4px;
                box-sizing: border-box;
                font-size: 14px;
                line-height: 30px;
                color: #737373;
                text-align: center;
                transition: .3s;
                margin: 0 2px;
                background: #E5E5E5;
                text-decoration: none;
                color: {{data.page2_tab_fontcolor}};
                border:1px solid {{data.page2_tab_bordercolor}};
                background: {{data.page2_ud_bgcolor}};
            }
            #{{ addonId }} .pages-c3{
                display: inline-block;
                margin: 0 5px;
            }
            #{{ addonId }} .pages-c3>a{
                font-size: 14px;
                line-height: 30px;
                color: #737373;
                transition: 0.5s;
                margin: 0 3px;
                display: inline-block;
                width: 30px;
                background: #E5E5E5;
                border-radius: 3px;
                text-decoration: none;
                color: {{data.page2_ud_col}};
                border:1px solid {{data.page2_ud_bor_col}};
                background: {{data.page2_tab_bgcolor}};
            }
            #{{ addonId }} .pages-sel{
                color:{{data.show_page_bom_}}!important;
                border:1px solid {{data.page2_bom_col}}!important;
                background: {{data.page2_bom_bor_col}}!important;
            }
            
            @media (max-width: 768px) {
                #{{ addonId }} .content{
                    width: 100%;
                }
                #{{ addonId }} .content .sec-content{
                    width: 100%;
                    height: 252PX;
                    background: #fff;
                    display: flex;
                    flex-wrap: wrap;
                    background:{{data.nav_bg_color}};
                    z-index: 1000;
                    padding:0;
                }
                #{{ addonId }} .content .sec-content .sec-content-box{
                    width: {{data.nav_width_1}}%;
                    height: {{data.nav_height_1}}px;
                    margin-left: {{data.nav_margin_left_1}}px;
                }
                #{{ addonId }} .sec-content-box-bli-t p{
                    float: left;
                }
                #{{ addonId }} .sec-content-box-bli-t p{
                    margin-left: 2px;
                    width:60px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                #{{ addonId }} .sec-content-box-bli-bli{
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                #{{ addonId }} .content .sec-content .sec-content-box-t{
                    cursor: pointer;
                    position: relative;
                    width: 100%;
                    height: {{data.nav_height_1}}px;
                    background: {{data.nav_bg_color_1}};
                    font-size: {{data.nav_1_size||18}}px;
                    line-height: 50px;
                    color: {{data.nav_color_1}};
                    text-align: center;
                    margin-bottom: 10px;
                    margin-left: {{data.nav_margin_left_1}}px;
                }
                #{{ addonId }} .content .sec-content .sec-content-box-t a{
                    color:{{data.nav_color_1}};
                }
                #{{ addonId }} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3{
                    clear: both;
                    text-align: left;
                    width: 431px;
                    display: flex;
                    flex-wrap: wrap;
                    background: {{data.page2_cont_bgcolor}};
                }
                #{{ addonId }} .cqr-tulisu-xianzhi .cqr-tulisu-xianzhi-3 .box{
                    width: 44.33%;
                    overflow: hidden;
                    box-sizing: border-box;
                    position: relative;
                    display: inline-block;
                    text-align: center;
                    margin: 0.5%;
                    padding: {{data.page2_list_padding}}
                }
                #{{ addonId }} .pages-box{
                    display: flex;
                    width: 455px;
                    justify-content: center;
                }
                #{{ addonId }} .pages-box-dis{
                    width: 471px!important;
                }
                #{{ addonId }} .sec-content-box-b{
                    display: none;
                }
                #{{ addonId }} .content .sec-content .sec-content-box-t{
                    height: 45px;
                    line-height: 42px;
                }
                #{{ addonId }} .cqr-tulisu-xianzhi .title{
                    background: {{data.page2_list_bgcolor}};
                    color: {{data.page2_list_color}};
                    font-size:{{data.page2_list_size}};
                    height: {{data.title_font_height_yd}}px;
                }
            }
        </style>
        
        <div class="content">
            <div class="sec-content">
                <div class="sec-content-box">
                    <div class="sec-content-box-t">
                        <a href="/pages/index.php?Itemid=8314&amp;cate_id=2609">产品中心</a>
                        <div class="sec_dj">
                            <div>+</div>
                            <div>-</div>
                        </div>
                        <div class="sec-content-box-b" style="display: block;">
                            <div class="sec-content-box-bli">
                                <div class="sec-content-box-bli-t">
                                    <span>+</span> 
                                    <p class="anv_li" data-id="list_tl_1391" onclick="ajaxcatList(1390)">2级导航</p>
                                </div>
                                <div class="sec-content-box-bli-b aat" style="display: block;">
                                    <p data-id="list_tl_5113" onclick="ajaxcatList(1390)" class="sec-content-box-bli-bli anv_li aat">3级导航</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sec-content-box-b">
                        <div class="sec-content-box-bli">
                            <div class="sec-content-box-bli-t"><span>+</span> <a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=2529">LED显示屏</a></div>
                            <div class="sec-content-box-bli-b"><a href="/pages/index.php?Itemid=8314&amp;cate_id=5551"
                                                                  class="sec-content-box-bli-bli">小间距</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5605"
                                    class="sec-content-box-bli-bli">分销系列</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5607"
                                    class="sec-content-box-bli-bli">商显</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5608"
                                    class="sec-content-box-bli-bli">传媒</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5609"
                                    class="sec-content-box-bli-bli">租赁</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5610"
                                    class="sec-content-box-bli-bli">建筑多媒体</a></div>
                        </div>
                    </div>
                </div>
                <div class="sec-content-box">
                    <div class="sec-content-box-t">
                        <a href="/pages/index.php?Itemid=8314&amp;cate_id=5611">LED专业照明</a>
                        <div class="sec_dj">
                            <div>+</div>
                            <div>-</div>
                        </div>
                    </div>
                    <div class="sec-content-box-b">
                        <div class="sec-content-box-bli">
                            <div class="sec-content-box-bli-t"><span>+</span> <a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5612">户外路灯</a></div>
                        </div>
                        <div class="sec-content-box-bli">
                            <div class="sec-content-box-bli-t"><span>+</span> <a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5613">隧道灯</a></div>
                        </div>
                        <div class="sec-content-box-bli">
                            <div class="sec-content-box-bli-t"><span>+</span> <a
                                    href="/pages/index.php?Itemid=8314&amp;cate_id=5614">智慧杆</a></div>
                        </div>
                    </div>
                </div>
            </div>
    
    
            <div class="cqr-tulisu-xianzhi" style="background:url() no-repeat ;">
                <!--一个分类下的内容-->
                <div class="cqr-tulisu-xianzhi-1 cqr-tulisu-xianzhi-2 cqr-tulisu-xianzhi-3">
    
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5610&amp;xcv=1&amp;products_id=53399">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/7.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >建筑多媒体</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5610&amp;xcv=1&amp;products_id=53398">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/5.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >建筑多媒体</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5610&amp;xcv=1&amp;products_id=53397">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/2.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >建筑多媒体</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5609&amp;xcv=1&amp;products_id=53380">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/10.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >租赁</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5609&amp;xcv=1&amp;products_id=53379">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/4.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >租赁</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5609&amp;xcv=1&amp;products_id=53378">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/8.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >租赁</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5609&amp;xcv=1&amp;products_id=53378">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/9.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >租赁</h2>
                        </a>
                    </div>
                    <div class="box">
                        <a href="/pages/index.php/component/content/article/?catid=5609&amp;xcv=1&amp;products_id=53378">
                            <div class="imgBox">
                                <img src="https://ijzt.china9.cn/images/blog/1.jpg"
                                     alt="">
                            </div>
                            <h2 class="title" >租赁</h2>
                        </a>
                    </div>
                </div>
                <!--分页-->
                <# if(data.show_page==1) { #>
                <div class="pages-box">
                    <div class="pages-c1">
                        <div class="pages-c2">
                            <a href="/pages/index.php?Itemid=8314&amp;cacheget=1234&amp;page=1">上一页</a>
                            <div class="pages-c3"><a href="/pages/index.php?Itemid=8314&amp;cacheget=1234&amp;page=1"
                                                     class="pages-sel">1</a><a
                                    href="/pages/index.php?Itemid=8314&amp;cacheget=1234&amp;page=2">2</a></div>
                            <a href="/pages/index.php?Itemid=8314&amp;cacheget=1234&amp;page=2">下一页</a>
                        </div>
                    </div>
                </div>
                <# } #>
            </div>
        </div>

        ';
        return $output;
    }

    //数组多层级 递归
    public function subTree($data, $pid = 1, $deep = 0)
    {   //用来存放数据
        $arr = [];
        //遍历数据库中的字段
        foreach ($data as $val)
        {
            //判断字段pid相等时放行
            if ($pid == $val['parent_id'])
            {
                //不同的层级
                $val['deep'] = $deep;
                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
                $val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
                //如果遇到pid==本条id时，将其存入数组
                $arr[] = $val;
            }
        }

        //返回数组
        return $arr;
    }

    /**
     * 排序
     */
    public function Sort(array$item,string$field,string $sort){
        if($sort!='DESC'){
            $last_names = array_column($item,$field);
            array_multisort($last_names,SORT_ASC,$item);
        }else{
            $last_names = array_column($item,$field);
            array_multisort($last_names,SORT_DESC,$item);
        }
        return $item;
    }
}
