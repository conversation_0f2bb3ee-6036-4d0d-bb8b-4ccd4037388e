<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'service_section',
		'title' => '分类选项卡',
		'desc' => '',
		'category' => '选项卡',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),
//	            'section_main_color' => array(
//		            'type' => 'color',
//		            'title' => '主色',
//		            'std' => '#E29728',
//	            ),
				'content_style' => array(
					'type' => 'buttons',
					'title' => '样式选项',
					'std' => 'column_style',
					'values' => array(
						array(
							'label' => '栏目',
							'value' => 'column_style'
						),
						array(
							'label' => '选项卡',
							'value' => 'tab_style'
						),
						array(
							'label' => '图片内容',
							'value' => 'content_style'
						),
					),
					'tabs' => true,
				),
				'section_name_w' => array(
					'type' => 'slider',
					'title' => '栏目宽度',
					'std' => array(
						'md' => 300,
						'sm' => 300,
						'xs' => 300,
					),
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
					'responsive' => true,
					'max' => 1800,
				),
				'section_name' => array(
					'type' => 'text',
					'title' => '栏目名称',
					'std' => '服务项目',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'type_parent' => array(
					'type' => 'select',
					'title' => '栏目分类',
					'desc' => '栏目分类',
					'values' => array(
						'type1' => '一级分类',
						'type2' => '二级分类',
						'type3' => '三级分类'
					),
					'std' => 'type1',
				),
				'type_start' => array(
					'type' => 'number',
					'title' => '从第n个栏目开始显示',
					'desc' => '从第n个栏目开始显示',
					'std' => '1'
				),
				'type_num' => array(
					'type' => 'number',
					'title' => '显示n个栏目',
					'desc' => '显示n个栏目',
					'std' => '10'
				),
				'section_name_color' => array(
					'type' => 'color',
					'title' => '栏目文字颜色',
					'std' => '#333',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'section_name_fontsize' => array(
					'type' => 'slider',
					'title' => '栏目字体大小',
					'std' => array(
						'md' => 30,
						'sm' => 26,
						'xs' => 20,
					),
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
					'responsive' => true,
					'max' => 80,
				),
				'section_tips' => array(
					'type' => 'text',
					'title' => '栏目别称',
					'std' => 'product display',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'section_tips_color' => array(
					'type' => 'color',
					'title' => '栏目别称文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'section_tips_fontsize' => array(
					'type' => 'slider',
					'title' => '栏目别称字体大小',
					'std' => array(
						'md' => 14,
						'sm' => 14,
						'xs' => 12,
					),
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
					'responsive' => true,
					'max' => 40,
				),
				'section_tips_H' => array(
					'type' => 'slider',
					'title' => '栏目别称高度',
					'std' => array(
						'md' => 22,
						'sm' => 20,
						'xs' => 18,
					),
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_column_color' => array(
					'type' => 'color',
					'title' => '栏目主色',
					'std' => '#E29728',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'section_column_borderColor' => array(
					'type' => 'color',
					'title' => '栏目边框颜色',
					'std' => '#EAEAEA',
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
				),
				'section_column_mgL' => array(
					'type' => 'slider',
					'title' => '栏目左边距',
					'std' => array(
						'md' => 40,
						'sm' => 0,
						'xs' => 0,
					),
					'depends' => array(
						array('content_style', '=', 'column_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_bgColor' => array(
					'type' => 'color',
					'title' => '选项卡背景色',
					'std' => '#333',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_color' => array(
					'type' => 'color',
					'title' => '选项卡文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_fontsize' => array(
					'type' => 'slider',
					'title' => '选项卡文字大小',
					'std' => array(
						'md' => 16,
						'sm' => 16,
						'xs' => 14,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_active_fontsize' => array(
					'type' => 'slider',
					'title' => '选项卡选中文字大小',
					'std' => array(
						'md' => 16,
						'sm' => 16,
						'xs' => 14,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_w' => array(
					'type' => 'slider',
					'title' => '选项卡宽度',
					'std' => array(
						'md' => 700,
						'sm' => 700,
						'xs' => 700,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 1800,
				),
				'section_tab_H' => array(
					'type' => 'slider',
					'title' => '选项卡高度',
					'std' => array(
						'md' => 52,
						'sm' => 52,
						'xs' => 36,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_active_bgColor' => array(
					'type' => 'color',
					'title' => '选项卡选中背景色',
					'std' => '#E29728',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_active_color' => array(
					'type' => 'color',
					'title' => '选项卡选中文字颜色',
					'std' => '#fff',
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_tab_active_H' => array(
					'type' => 'slider',
					'title' => '选项卡选中高度',
					'std' => array(
						'md' => 68,
						'sm' => 68,
						'xs' => 52,
					),
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
					'responsive' => true,
					'max' => 200,
				),
				'section_tab_active_time' => array(
					'type' => 'slider',
					'title' => '选项卡动画效果时间',
					'desc' => '',
					'min' => 100,
					'max' => 500,
					'std' => 200,
					'depends' => array(
						array('content_style', '=', 'tab_style'),
					),
				),
				'section_content_large_w' => array(
					'type' => 'slider',
					'title' => '左侧大图宽度',
					'std' => array(
						'md' => 500,
						'sm' => 500,
						'xs' => 500,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 1000,
				),
				'section_content_large_h' => array(
					'type' => 'slider',
					'title' => '左侧大图高度',
					'std' => array(
						'md' => 500,
						'sm' => 500,
						'xs' => 300,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 1000,
				),
				'section_content_title_fontsize' => array(
					'type' => 'slider',
					'title' => '内容标题文字大小',
					'std' => array(
						'md' => 22,
						'sm' => 22,
						'xs' => 22,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 40,
				),
				'section_content_title_mgB' => array(
					'type' => 'slider',
					'title' => '内容标题下边距',
					'std' => array(
						'md' => 20,
						'sm' => 20,
						'xs' => 20,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'section_content_sTitle_fontsize' => array(
					'type' => 'slider',
					'title' => '内容小标题文字大小',
					'std' => array(
						'md' => 18,
						'sm' => 18,
						'xs' => 18,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 40,
				),
				'section_content_sTitle_mgB' => array(
					'type' => 'slider',
					'title' => '内容小标题下边距',
					'std' => array(
						'md' => 20,
						'sm' => 20,
						'xs' => 20,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 100,
				),
				'section_content_textIcon_w' => array(
					'type' => 'slider',
					'title' => '内容下方小图标大小',
					'std' => array(
						'md' => 12,
						'sm' => 12,
						'xs' => 12,
					),
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
					'responsive' => true,
					'max' => 50,
				),
				'service_content_textIcon' => array(
					'type' => 'media',
					'title' => '内容下方小图标',
					'std' => 'https://oss.lcweb01.cn/joomla/20210618/95d20e4cf055478bd8e620fd6164aac5.png',
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
				),
				'section_content_active_time' => array(
					'type' => 'slider',
					'title' => '动画效果时间',
					'desc' => '',
					'min' => 100,
					'max' => 500,
					'std' => 200,
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
				),
				'isLinkDetail' => array(
					'type' => 'checkbox',
					'title' => '是否链接详情页',
					'desc' => '',
					'std' => 0,
					'depends' => array(
						array('content_style', '=', 'content_style'),
					),
				),
				'detail_page_id' => array(
					'type' => 'select',
					'title' => '详情页模版',
					'desc' => '显示文章详情页模版',
					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
					'depends' => array(
						array('content_style', '=', 'content_style'),
						array('isLinkDetail', '=', '1'),
					),
				),
			),
		),
	)
);
