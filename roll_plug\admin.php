<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'roll_plug', 
        'title' => JText::_('滚动弹幕'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' =>'其他',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'roll_plug_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                    ),
                    'std' => 'type1',
                ),
                'roll_fx_type1' => array(
                    'type' => 'select',
                    'title' => '滑动方向',
                    'desc' => '滑动方向',
                    'values' => array(
                        'left' => '左',
                        'right' => '右',
                    ),
                    'std' => 'left',
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                // Repeatable Item
                'jw_tab_item_goods' => array(
                    'title' => JText::_('自定义内容列表'),
                    'attr' => array(
                        'scrollamount' => array(
                            'type' => 'slider',
                            'title' => JText::_('滑动速度'),
                            'max' => 100,
                            'min' => 0,
                            'std' => 2,
                        ),
                        'content' => array(
                            'title' => JText::_('自定义标题icon列表'),
                            'attr' => array(
                                'title' => array(
                                    'type' => 'text',
                                    'title' => JText::_('标题'),
                                    'desc' => JText::_('标题'),
                                    'std' => '标题',
                                ),
                                'icon' => array(
                                    'type' => 'media',
                                    'title' => JText::_('图标'),
                                    'desc' => JText::_('图标'),
                                    'std' => '',
                                ),
                            ),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'scrollamount' => 6,
                            'content' => array(
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                            ),
                        ),
                        array(
                            'scrollamount' => 4,
                            'content' => array(
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                            ),
                        ),
                        array(
                            'scrollamount' => 5,
                            'content' => array(
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '最近有什么优惠活动吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '可以详细介绍下吗？',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                                array(
                                    'title' => '详细了解下费用情况',
                                    'icon' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220823/0678d269a573163fd19b2a2f14d6c896.png',
                                ),
                            ),
                        ),
                    ),
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'margin_bottom_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('行间距'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'height_pc_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc高度'),
                    'max' => 500,
                    'min' => 0,
                    'std' => 100,
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'height_sj_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('移动端高度'),
                    'max' => 500,
                    'min' => 0,
                    'std' => 50,
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'font_size_pc_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc字体大小'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 40,
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'font_size_sj_type1' => array(
                    'type' => 'slider',
                    'title' => JText::_('移动端字体大小'),
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'font_color_odd_type1' => array(
                    'type' => 'color',
                    'title' => JText::_('奇数块背景颜色'),
                    'std' => '#ff0000',
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'font_color_even_type1' => array(
                    'type' => 'color',
                    'title' => JText::_('偶数块背景颜色'),
                    'std' => '#f88b39',
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
                'even_button_xf_type1' => array(
                    'type' => 'select',
                    'title' => '偶数行块颜色是否相反',
                    'desc' => '偶数行块颜色是否相反',
                    'values' => array(
                        'xf' => '相反',
                        'bxf' => '不相反',
                    ),
                    'std' => 'xf',
                    'depends' => array(array('roll_plug_type', '=', 'type1')),
                ),
            ),
        ),
    )
);
