<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonPage_anchor extends JwpagefactoryAddons
{

	public function render()
	{
		$layout_id = $_GET['layout_id'] ?? 0;
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$addonId = $this->addon->id;
		$settings = $this->addon->settings;
		$section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
		$tz_style = (isset($settings->tz_style) && $settings->tz_style) ? $settings->tz_style : '_blank';
		$list_style = (isset($settings->list_style) && $settings->list_style) ? $settings->list_style : 'outside';
		$list_style_color = (isset($settings->list_style_color) && $settings->list_style_color) ? $settings->list_style_color : '#000000';
		$list_style_color_hg = (isset($settings->list_style_color_hg) && $settings->list_style_color_hg) ? $settings->list_style_color_hg : '#ff0000';
		$title_color = (isset($settings->title_color) && $settings->title_color) ? $settings->title_color : '#000000';
		$title_color_hg = (isset($settings->title_color_hg) && $settings->title_color_hg) ? $settings->title_color_hg : '#000000';
		$title_border_color_hg = (isset($settings->title_border_color_hg) && $settings->title_border_color_hg) ? $settings->title_border_color_hg : 'transparent';
		$title_bg_color = (isset($settings->title_bg_color) && $settings->title_bg_color) ? $settings->title_bg_color : 'none';
		$title_bg_color_hg = (isset($settings->title_bg_color_hg) && $settings->title_bg_color_hg) ? $settings->title_bg_color_hg : 'none';
		$title_jj = isset($settings->title_jj) ? $settings->title_jj : 50;

		/*新增*/
		// 文字大小
		$title_fontsize = (isset($settings->title_fontsize) && $settings->title_fontsize) ? $settings->title_fontsize : "16";
		// 导航项宽度
		$item_width_bai = (isset($settings->item_width_bai) && $settings->item_width_bai) ? $settings->item_width_bai : "0";
		$item_width_bfb = (isset($settings->item_width_bfb) && $settings->item_width_bfb) ? $settings->item_width_bfb : "33";

		$item_width = (isset($settings->item_width) && $settings->item_width) ? $settings->item_width : "auto";
		// 导航项高度
		$item_height = (isset($settings->item_height) && $settings->item_height) ? $settings->item_height : "auto";
		// 导航位置
		$nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : "";
		// 导航下划线三角
		$title_bottom_icon = (isset($settings->title_bottom_icon) && $settings->title_bottom_icon) ? $settings->title_bottom_icon : 0;
		// 导航项文字行度
		$item_text_height = (isset($settings->item_text_height) && $settings->item_text_height) ? $settings->item_text_height : "";
		// 导航间隔样式
		$list_style_after = (isset($settings->list_style_after) && $settings->list_style_after) ? $settings->list_style_after : "none";

		$list_xhx_dh = (isset($settings->list_xhx_dh) && $settings->list_xhx_dh) ? $settings->list_xhx_dh : 0;
		$list_xhx_color = (isset($settings->list_xhx_color) && $settings->list_xhx_color) ? $settings->list_xhx_color : "#818181";
		$list_xhx_width = (isset($settings->list_xhx_width) && $settings->list_xhx_width) ? $settings->list_xhx_width : "30";

		//轮播布局2
		$media_bj = (isset($settings->media_bj) && $settings->media_bj) ? $settings->media_bj : 'ym1';
		$section_tab_item1 = (isset($settings->section_tab_item1) && $settings->section_tab_item1) ? $settings->section_tab_item1 : array();
		$active_img = (isset($settings->active_img) && $settings->active_img) ? $settings->active_img : 'https://oss.lcweb01.cn/joomla/20220119/7bfdb787f28471bf6470e48b69c24a72.png';
		$dh_width = (isset($settings->dh_width) && $settings->dh_width) ? $settings->dh_width : '1200';
		$cont_color = (isset($settings->cont_color) && $settings->cont_color) ? $settings->cont_color : '#f0f0f0';
		$cont_left = isset($settings->cont_left) ? $settings->cont_left : 0;
		$cont_img_width = (isset($settings->cont_img_width) && $settings->cont_img_width) ? $settings->cont_img_width : '35';
		$img_left = isset($settings->img_left) ? $settings->img_left : 55;
		$titlemd_color_hg = (isset($settings->titlemd_color_hg) && $settings->titlemd_color_hg) ? $settings->titlemd_color_hg : '#1196DB';
		$cont_text_color = (isset($settings->cont_text_color) && $settings->cont_text_color) ? $settings->cont_text_color : '#000';
		$cont_text_size = (isset($settings->cont_text_size) && $settings->cont_text_size) ? $settings->cont_text_size : '24';
		$gd_color = (isset($settings->gd_color) && $settings->gd_color) ? $settings->gd_color : '#1196DB';
		$gd_color_active = (isset($settings->gd_color_active) && $settings->gd_color_active) ? $settings->gd_color_active : '#1196DB';


		if ($media_bj != 'ym1') {
			$media_style = (isset($settings->media_style) && $settings->media_style) ? $settings->media_style : 'style1';
			$list_mark_size = (isset($settings->list_mark_size) && $settings->list_mark_size) ? $settings->list_mark_size : '';
			$list_mark_color = (isset($settings->list_mark_color) && $settings->list_mark_color) ? $settings->list_mark_color : '#0065cc';
			if (isset($settings->list_item_lineheight) && $settings->list_item_lineheight) {
				if (is_object($settings->list_item_lineheight)) {
					$list_item_lineheight = $settings->list_item_lineheight->md;
					$list_item_lineheight_sm = $settings->list_item_lineheight->sm;
					$list_item_lineheight_xs = $settings->list_item_lineheight->xs;
				} else {
					$list_item_lineheight = $settings->list_item_lineheight;
					$list_item_lineheight_sm = $settings->list_item_lineheight_sm;
					$list_item_lineheight_xs = $settings->list_item_lineheight_xs;
				}
			} else {
				$list_item_lineheight = 2;
				$list_item_lineheight_sm = 2;
				$list_item_lineheight_xs = 1.5;
			}
			$list_item_color = (isset($settings->list_item_color) && $settings->list_item_color) ? $settings->list_item_color : '#333';
			$cont_img_width_phone = (isset($settings->cont_img_width_phone) && $settings->cont_img_width_phone) ? $settings->cont_img_width_phone : 35;
			$img_left_phone = (isset($settings->img_left_phone) && $settings->img_left_phone) ? $settings->img_left_phone : 55;
			if (isset($settings->list_margin_top) && $settings->list_margin_top) {
				if (is_object($settings->list_margin_top)) {
					$list_margin_top = $settings->list_margin_top->md;
					$list_margin_top_sm = $settings->list_margin_top->sm;
					$list_margin_top_xs = $settings->list_margin_top->xs;
				} else {
					$list_margin_top = $settings->list_margin_top;
					$list_margin_top_sm = $settings->list_margin_top_sm;
					$list_margin_top_xs = $settings->list_margin_top_xs;
				}
			} else {
				$list_margin_top = 44;
				$list_margin_top_sm = 44;
				$list_margin_top_xs = 10;
			}
			if (isset($settings->button_margin_top) && $settings->button_margin_top) {
				if (is_object($settings->button_margin_top)) {
					$button_margin_top = $settings->button_margin_top->md;
					$button_margin_top_sm = $settings->button_margin_top->sm;
					$button_margin_top_xs = $settings->button_margin_top->xs;
				} else {
					$button_margin_top = $settings->button_margin_top;
					$button_margin_top_sm = $settings->button_margin_top_sm;
					$button_margin_top_xs = $settings->button_margin_top_xs;
				}
			} else {
				$button_margin_top = 80;
				$button_margin_top_sm = 60;
				$button_margin_top_xs = 20;
			}
			if (isset($settings->list_height) && $settings->list_height) {
				if (is_object($settings->list_height)) {
					$list_height = $settings->list_height->md;
					$list_height_sm = $settings->list_height->sm;
					$list_height_xs = $settings->list_height->xs;
				} else {
					$list_height = $settings->list_height;
					$list_height_sm = $settings->list_height_sm;
					$list_height_xs = $settings->list_height_xs;
				}
			} else {
				$list_height = '';
				$list_height_sm = '';
				$list_height_xs = '300';
			}
			if (isset($settings->list_item_font_size) && $settings->list_item_font_size) {
				if (is_object($settings->list_item_font_size)) {
					$list_item_font_size = $settings->list_item_font_size->md;
					$list_item_font_size_sm = $settings->list_item_font_size->sm;
					$list_item_font_size_xs = $settings->list_item_font_size->xs;
				} else {
					$list_item_font_size = $settings->list_item_font_size;
					$list_item_font_size_sm = $settings->list_item_font_size_sm;
					$list_item_font_size_xs = $settings->list_item_font_size_xs;
				}
			} else {
				$list_item_font_size = 60;
				$list_item_font_size_sm = 60;
				$list_item_font_size_xs = 50;
			}
			if (isset($settings->list_width) && $settings->list_width) {
				if (is_object($settings->list_width)) {
					$list_width = $settings->list_width->md;
					$list_width_sm = $settings->list_width->sm;
					$list_width_xs = $settings->list_width->xs;
				} else {
					$list_width = $settings->list_width;
					$list_width_sm = $settings->list_width_sm;
					$list_width_xs = $settings->list_width_xs;
				}
			} else {
				$list_width = 42;
				$list_width_sm = 42;
				$list_width_xs = 100;
			}
			$list_mark_settings = (isset($settings->list_mark_settings) && $settings->list_mark_settings) ? $settings->list_mark_settings : 'color';
			$list_mark_img = (isset($settings->list_mark_img) && $settings->list_mark_img) ? $settings->list_mark_img : '';
			$list_mark_width = (isset($settings->list_mark_width) && $settings->list_mark_width) ? $settings->list_mark_width : '5';
			$list_padding_left = (isset($settings->list_padding_left) && $settings->list_padding_left) ? $settings->list_padding_left : '20';

			$output = '
			<style>
				@media screen and (min-width:1000px) and (max-width:1600px) {
					' . $addon_id . ' .solute2-info{left: ' . $cont_left . '%;}
					' . $addon_id . ' .solute2-nav-list ul li img{margin-left: ' . $img_left . '%;}
				}
				@media screen and (max-width:650px) {
					' . $addon_id . ' .solute-type-list {
						position: relative;
					}
					' . $addon_id . ' .solute2-nav-list ul li img {
					    margin-left: ' . $img_left . '%;
					}
				}
				' . $addon_id . ' .solute2-info h1{color:' . $cont_text_color . ';font-size:' . $cont_text_size . 'px;}
				' . $addon_id . ' ul{list-style: none;}
				' . $addon_id . ' a{display: inline-block;text-decoration: none;color: inherit;}
				' . $addon_id . ' .width12{width:' . $dh_width . 'px;margin:0 auto;}
				' . $addon_id . ' .clearfix:after{/*伪元素是行内元素 正常浏览器清除浮动方法*/
					content: "";
					display: block;
					height: 0;
					clear:both;
					visibility: hidden;
				}
				' . $addon_id . ' .clearfix{
					*zoom: 1;/*ie6清除浮动的方式 *号只有IE6-IE7执行，其他浏览器不执行*/
				}
				/* swiper */

				/* 解决方案 */
				' . $addon_id . ' .solute-titlt{margin: auto;width: auto;text-align: center;padding: 50px 0 15px;margin-bottom: 30px;}
				' . $addon_id . ' .solute-titlt h1{display: inline;color: #333;padding-bottom: 18px;font-size: 46px;position: relative;font-weight: 500;}
				' . $addon_id . ' .solute-titlt h1::after{position: absolute;content: "";bottom: 0;left: 4.3%;width:90%;height: 5px;border-radius: 3px;background: #999;-moz-width:88%; 	/* Firefox */}
				' . $addon_id . ' .solute-type-list .swiper-slide-thumb-active .solute-type{display: none;}
				' . $addon_id . ' .solute-type-list .swiper-slide-thumb-active .solute-type-on{display: flex;background: url(../images/127.png) no-repeat;background-size: 50% ;background-position:center 100% ;padding-bottom: 20px; color: #1196DB;}
				' . $addon_id . ' .solute-type-list .swiper-slide-thumb-active .solute-type-on img{filter:brightness(100%);
					-webkit-filter: brightness(100%);
					-moz-filter: brightness(100%);
					-o-filter: brightness(100%);
					-ms-filter: brightness(100%);}
				' . $addon_id . ' .solute-type{display: flex;flex-direction: column;align-items: center;}
				' . $addon_id . ' .solute-type-on{display: flex;flex-direction: column;align-items: center;}
				' . $addon_id . ' .solute-type-on img{filter:brightness(0);
				-webkit-filter: brightness(0);
				-moz-filter: brightness(0);
				-o-filter: brightness(0);
				-ms-filter: brightness(0);}
				' . $addon_id . ' .solute-type img{width: 35%;margin-bottom: 10px;}
				' . $addon_id . ' .solute-type-on img{width: 35%;margin-bottom: 10px;}
				' . $addon_id . ' .gallery-info{position: absolute;left: 20%;top: 40%;}
				' . $addon_id . ' .gallery-more{background-color: ' . $gd_color . ';color: white;height: 40px;
				    width: 130px;border-radius: 20px;margin-top: 15px;display: block;align-items: center;line-height: 32px;text-align: center;}
				' . $addon_id . ' .gallery-more:hover{background-color: ' . $gd_color_active . ';}
				' . $addon_id . ' .more-an{margin-left: 10px;display: inline-block;width: 28px;height: 30px;position: relative;overflow: hidden;margin-bottom: -10px;
				    margin-top: 5px;
				}
				' . $addon_id . ' .more-an>span{display: inline-block;width: 30px;height: 30px;position: absolute;border: 1px solid white;border-radius: 50%;right: 0;top: 0;}
				' . $addon_id . ' .more-an>span::before{content: "";position: absolute;border-top: 1px solid white;border-right: 1px solid white; width: 10px;height: 10px;
				transform: rotateZ(45deg);
				-ms-transform:rotate(45deg); 	/* IE 9 */
				-moz-transform:rotate(45deg); 	/* Firefox */
				-webkit-transform:rotate(45deg); /* Safari 和 Chrome */
				-o-transform:rotate(45deg); 
				right: 5px;top: 8px;}
				' . $addon_id . ' .more-an>span::after{content: "";position: absolute;width: 16px;height: 1px;background: white;right: 5px;top: 13px;}
				@keyframes morean{
					from{right: 30px;}
					to{right:-20px}
				}
				' . $addon_id . ' .gallery-more:hover .more-an>span::before{animation:morean 1s ease-in-out 0s infinite;}
				' . $addon_id . ' .gallery-more:hover .more-an>span::after{animation:morean 1s ease-in-out 0s infinite;}
				' . $addon_id . ' .solute-type-list{margin-bottom: 7px;}

				' . $addon_id . ' .solute2-nav-tit ul li{width: 20%;float: left;padding-bottom: 5px;}
				' . $addon_id . ' .solute2-nav-tit ul li div{text-align: center;font-size:' . $title_fontsize . 'px;}
				' . $addon_id . ' .solute2-type div{color:' . $title_color . ';}

				' . $addon_id . ' .solute2-nav-tit ul li img{width: 36%;margin:5px 32% 20px;}
				' . $addon_id . ' .solute2-nav-tit ul li .solute2-type-on{color: ' . $titlemd_color_hg . ';display: none;background: url(' . $active_img . ') no-repeat;background-size: 50% ;background-position:center 100% ;padding-bottom: 15px;}
				' . $addon_id . ' .solute2-nav-tit ul li.on .solute2-type-on{display: block;}
				' . $addon_id . ' .solute2-nav-tit ul li .solute2-type{display: block;padding-bottom: 15px;}
				' . $addon_id . ' .solute2-nav-tit ul li.on .solute2-type{display: none;}
				' . $addon_id . ' .solute2-nav-list{overflow: hidden;background-color: ' . $cont_color . ';}
				' . $addon_id . ' .solute2-nav-list ul{overflow: hidden;width: 500%;}
				' . $addon_id . ' .solute2-nav-list ul li{float: left;width: 20%;position: relative;}
				' . $addon_id . ' .solute2-info{position: absolute;left: ' . $cont_left . '%;top: 40%;}
				' . $addon_id . ' .solute2-nav-list ul li img{width: ' . $cont_img_width . '%;margin-left: ' . $img_left . '%;}

				@media screen and (max-width:650px){
					' . $addon_id . ' html{width: 100%;}
					' . $addon_id . ' .width12{width: 100%;  overflow-x: scroll;}
					' . $addon_id . ' .solute2-info h1{font-size: 13px;}
					' . $addon_id . ' .solute2-info{left: 5%;top: 30%;}
					' . $addon_id . ' .solute2-nav-tit ul li img{margin: 5px 32% 8px;}
					' . $addon_id . ' .solute2-nav-tit ul li div{font-size: 13px;}
				}
			</style>';

			if ($media_style == 'style2') {
				$output .= '<style>
					' . $addon_id . ' ul{
						padding-left: 0;
					}
					' . $addon_id . ' .solute2-nav-list ul{
						height: 100%;
					}
					' . $addon_id . ' .solute2-nav-list{
						height: ' . $list_height . 'px;
					}
					' . $addon_id . ' .solute2-info{
						width: ' . $list_width . '%;
						top: 50%;
						transform: translateY(-50%);
					}
					' . $addon_id . ' .solute2-info h1{
						font-weight: bold;
					}
					' . $addon_id . ' .solute2-info .list{
						width: 100%;
						margin-top: ' . $list_margin_top . 'px;
					}
					' . $addon_id . ' .solute2-info .list ul{
						width: 100%;
						padding-left: ' . $list_padding_left . 'px;
						height: 100%;
					}';
				if ($list_mark_settings == 'color') {
					$output .= $addon_id . ' .solute2-info .list ul li::marker{
							font-size: ' . $list_mark_size . 'px;
							color: ' . $list_mark_color . ';
						}
						' . $addon_id . ' .solute2-info .list ul li::before{
							display: none;
						}';
				} else {
					$output .= $addon_id . ' .solute2-info .list ul li::before{
							content: "";
							display: inline-block;
							position: absolute;
							top: 50%;
							transform: translateY(-50%);
							left: -16px;
							width: ' . $list_mark_width . 'px;
							height: ' . $list_mark_width . 'px;
							background: url(' . $list_mark_img . ') no-repeat;background-size: 100%;background-position:center 100%;
						}
						' . $addon_id . ' .solute2-info .list ul li{
							list-style: none;
						}';
				}
				$output .= $addon_id . ' .solute2-nav-list .list ul li{
						width: 100%;
						font-size: ' . $list_item_font_size . 'px;
						line-height: ' . $list_item_lineheight . ';
						color: ' . $list_item_color . ';
						height: 100%;
					}
					' . $addon_id . ' .solute2-nav-list ul li{
						height: 100%;
					}
					' . $addon_id . ' .solute2-nav-list ul li img{
						width: ' . $cont_img_width . '%;
						line-height: ' . $list_height . 'px;
						display: inline-block;
					}
					' . $addon_id . ' .gallery-more{
						margin-top: ' . $button_margin_top . 'px;
					}';
				foreach ($section_tab_item1 as $key => $tab) {
					if ($tab->need_content == 1) {
						$cols_style2 = $tab->cont_list_cols > 0 ? $tab->cont_list_cols : 1;
						$output .= $addon_id . ' .solute2-nav-list li:nth-of-type(' . ($key + 1) . ') .list ul li{
								width: calc(100% / ' . $cols_style2 . ');
							}';
					}
				}
				$output .= '
					@media (max-width: 991px) and (min-width: 768px){
						' . $addon_id . ' .solute2-nav-list{
							height: ' . $list_height_sm . 'px;
						}
						' . $addon_id . ' .solute2-info .list{
							margin-top: ' . $list_margin_top_sm . 'px;
						}
						' . $addon_id . ' .solute2-nav-list .list ul li{
							line-height: ' . $list_item_lineheight_sm . ';
							font-size: ' . $list_item_font_size_sm . 'px;
						}
						' . $addon_id . ' .gallery-more{
							margin-top: ' . $button_margin_top_sm . 'px;
						}
						' . $addon_id . ' .solute2-info{
							width: ' . $list_width_sm . '%;
						}
					}
					@media (max-width: 767px){
						' . $addon_id . ' .solute2-info{
							width: ' . $list_width_xs . '%;
						}
						' . $addon_id . ' .solute2-nav-list{
							height: ' . $list_height_xs . 'px;
						}
						' . $addon_id . ' .solute2-info .list{
							margin-top: ' . $list_margin_top_xs . 'px;
						}
						' . $addon_id . ' .solute2-nav-list .list ul li{
							line-height: ' . $list_item_lineheight_xs . ';
							font-size: ' . $list_item_font_size_xs . 'px;
							width: 100%!important;
						}
						' . $addon_id . ' .solute2-nav-list ul li img{
							width: ' . $cont_img_width_phone . '%;
							margin-left: ' . $img_left_phone . '%;
						}
						' . $addon_id . ' .gallery-more{
							margin-top: ' . $button_margin_top_xs . 'px;
						}';
				foreach ($section_tab_item1 as $key => $tab) {
					if ($tab->need_content == 0) {
						$output .= $addon_id . ' .solute2-nav-list li:nth-of-type(' . ($key + 1) . ') .solute2-info{top: 30%;}';
					} else {
						$output .= $addon_id . ' .solute2-nav-list li:nth-of-type(' . ($key + 1) . ') .solute2-info{
									top: 0%;
									transform: translateY(0);
								}';
					}
				}
				$output .= '
					}
				</style>';
			}

			$output .= '
				<div class="solute2">
					<div class="width12 solute2-nav-tit">
						<ul class="clearfix">';
			foreach ($section_tab_item1 as $key => $tab) {
				$link = '';
				if ($tab->detail_page_id) {
					$id = base64_encode($tab->detail_page_id);
					$link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
				}
				if ($tab->title_id) {
					$link .= '#' . $tab->title_id;
				}

				$output .= '<li ';
				if ($key == 0) {
					$output .= ' class="on" ';
				}
				$output .= '>
								<div class="solute2-type-on">';
				if ($tab->icon_img_hover) {
					$output .= '<img src="' . $tab->icon_img_hover . '" />';
				}
				$output .= '<div class="">
										' . $tab->name . '		
									</div>
								</div>
								<div class="solute2-type">';
				if ($tab->icon_img) {
					$output .= '<img src="' . $tab->icon_img . '" />';
				}
				$output .= '<div class="">
										' . $tab->name . '	
									</div>
								</div>
							</li>';
			}
			$output .= '
						</ul>
					</div>
					<div class="solute2-nav-list">
						<ul>';
			foreach ($section_tab_item1 as $key => $tab) {
				$link = '';
				if ($tab->detail_page_id) {
					$id = base64_encode($tab->detail_page_id);
					$link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
				}
				if ($tab->title_id) {
					$link .= '#' . $tab->title_id;
				}

				$output .= '
	                        <li>
								<div class="solute2-info">
									<h1>' . $tab->cont . '</h1>';
				if ($media_style == 'style2') {
					if ($tab->need_content == 1) {
						if ($tab->custom_content == 1) {
							$output .= '<div class="list">' . $tab->cont_list . '</div>';
						} else {
							$tab_content = $tab->content_list_item ? $tab->content_list_item : array();
							if (count($tab_content)) {
								$output .= '<div class="list">
														<ul>';
								foreach ($tab_content as $k => $li) {
									$output .= '<li>' . $li->text . '</li>';
								}
								$output .= '</ul>
													</div>';
							}
						}
					}
				}
				$output .= '<a ';
				if ($link != '') {
					$output .= 'target="' . $tz_style . '" href="' . $link . '" ';
				}
				$output .= ' class="gallery-more">了解更多<span class="more-an"><span></span></span></a>
								</div>';
				if ($tab->banner_img) {
					$output .= '<img src="' . $tab->banner_img . '" />';
				}
				$output .= '</li>';
			}
			$output .= '
							
						</ul>
					</div>
				</div>
			';
			$output .= '
				<script type="text/javascript">
					$(function() {
						var DEFAULT_VERSION = 9;
						var ua = navigator.userAgent.toLowerCase();
						var isIE = ua.indexOf("msie") > -1;
						var safariVersion;
						if (isIE) {
							safariVersion = ua.match(/msie ([\d.]+)/)[1];
							var sa = parseInt(safariVersion);
							if (safariVersion <= DEFAULT_VERSION) {
								//ie9以下
								$("' . $addon_id . ' .solute2-nav-tit ul li").eq(0).addClass("on");
								$("' . $addon_id . ' .solute").css("display","none");
								$("' . $addon_id . ' .solute2").css("display","block");
							} else {
								//ie9以上，调用swiper2
								$("' . $addon_id . ' .solute2-nav-tit ul li").eq(0).addClass("on");
								$("' . $addon_id . ' .solute").css("display","none");
								$("' . $addon_id . ' .solute2").css("display","block");
								// swiper2()
							}
						} else {
							//非ie
							// swiper2()
						}
						$("' . $addon_id . ' .solute2-nav-tit ul li").eq(0).addClass("on");
						$("' . $addon_id . ' .solute").css("display","none");
						$("' . $addon_id . ' .solute2").css("display","block");
						$("' . $addon_id . ' .solute2-nav-tit ul").on("click","li",function(){
							$(this).addClass("on").siblings().removeClass("on");
							var width = $("' . $addon_id . ' .solute2-nav-list ul li").width();
							
							var index = $(this).index();
							console.log(index);
							$("' . $addon_id . ' .solute2-nav-list").animate({"scrollLeft": width*index}, 500);
							$("' . $addon_id . ' html,body").animate({
								"scroll-top": $(window).scrollTop()+1
								}, 500);
						})
					});
				</script>
			';
		} else {
			$output = '
			<style>
				' . $addon_id . ' .box ul {
					list-style: ' . $list_style . ';
					display: flex;
					flex-wrap: wrap;
					padding: 0;
					margin: 0;
					justify-content: ' . $nav_position . ';';
			if ($title_bottom_icon != 1) {
				$output .= 'overflow:hidden;';
			} else {
				$output .= 'flex-wrap: wrap;
                        ';
			}
			$output .= '}
			    ' . $addon_id . ' .box ul li {
					background: ' . $title_bg_color . ';';
			if ($item_width_bai == 1) {
				$output .= 'width: ' . $item_width_bfb . '%;';
			} else {
				$output .= 'width: ' . $item_width . 'px;';
			}
			$output .= 'height: ' . $item_height . 'px;
					line-height: ' . $item_height . 'px;
					text-align: center;';
			if ($title_border_color_hg != 'transparent') {
				$output .= 'border-bottom: 3px solid transparent;';
			}

			$output .= 'transition: all ease-in-out 0.3s;
					cursor: pointer;
					position: relative;
					
			    }
				' . $addon_id . ' .box ul li a {
					color: ' . $title_color . ';
					font-size: ' . $title_fontsize . 'px;
					text-decoration: none;
					transition: all ease-in-out 0.3s;
					display: block;
				}
				' . $addon_id . ' .box ul li a span {
					display: inline-block;
				    line-height: ' . $item_text_height . 'px;
				}
				' . $addon_id . ' .box ul li a .li-icon {
					margin: auto;
				    max-width: 100%;
				}
				' . $addon_id . ' .box ul li a .li-icon.hover {
					display: none;
				}
				' . $addon_id . ' .box li:not(:first-child) {
					margin-left: ' . $title_jj . 'px;
				}
				' . $addon_id . ' .box li::marker {
					color: ' . $list_style_color . ';
				}
				' . $addon_id . ' .box li:hover::marker {
					color: ' . $list_style_color_hg . ';
				}
				' . $addon_id . ' .box li:hover {';
			if ($title_border_color_hg != 'transparent') {
				$output .= 'border-bottom: 3px solid ' . $title_border_color_hg . ';';
			}
			$output .= '
					background: ' . $title_bg_color_hg . ';
				}';
			if ($title_bottom_icon == 1) {
				$output .= $addon_id . ' .box li:hover::after, ' . $addon_id . ' .box li.actives::after {
	                    content: "";
	                    position: absolute;
	                    top: calc(100% + 3px);
	                    left: calc(50% - 6px);
	                    width: 0;
	                    height: 0;
	                    border: 6px solid transparent;
	                    border-top-color: ' . $title_border_color_hg . ' ;
	                }';
			}
			$output .= $addon_id . ' .box li:hover a {
					color: ' . $title_color_hg . ';
				}
				' . $addon_id . ' .box ul li:hover a .li-icon, ' . $addon_id . ' .box ul li.actives a .li-icon {
					display: none;
				}
				' . $addon_id . ' .box ul li:hover a .li-icon.hover, ' . $addon_id . ' .box ul li.actives a .li-icon.hover {
					display: block;
				}
				' . $addon_id . ' .box ul .actives{
					background:' . $title_bg_color_hg . ';
					color:' . $title_color_hg . ';';
			if ($title_border_color_hg != 'transparent') {
				$output .= 'border-bottom: 3px solid ' . $title_border_color_hg . ';';
			}
			$output .= '}
				' . $addon_id . ' .box ul .actives a{
					color:' . $title_color_hg . ';
				}' .
				$addon_id . ' .box ul .actives a span,
				' . $addon_id . ' .box ul li:hover a span
				{';
					if($settings->title_underline == 1) {
						$output .= 'text-decoration: underline;';
					}
				$output .= '
				}';
			if ($list_style_after == "one") {
				$output .= $addon_id . ' .box ul>li::after {
					    content: "";
					    position: absolute;
					    width: 1px;
					    height: 10px;
					    background: rgba(170, 170, 170, .3);
					    top: calc( ( ' . $item_height . 'px - 10px) / 2);
					    right: calc( -' . $title_jj . 'px / 2 );
					}
					' . $addon_id . ' .box ul li:last-child::after {
					    content: "";
					    position: absolute;
					    width: 0px;
					    height: 0px;
					    background: none;
					}';
			}

			$output .= $addon_id . ' .box ul>li .cad .bsimg{
					transition:all 0.36s;
					width:50%;
					height:14px;
					vertical-align:middle;
					display:inline;
				}
				' . $addon_id . ' .box ul>li .cad{width:100%;height:' . $item_height . 'px;background:' . $title_bg_color_hg . ';text-align:center;line-height:' . $item_height . 'px;position:relative;z-index:12;}
				' . $addon_id . ' .box ul>li:hover .cad{
					transition:all 0.36s;
					margin-top:-' . $item_height . 'px;
				}';

			if ($list_xhx_dh == 1) {
				$output .= $addon_id . ' .box li a span::before {
                        position: absolute;
                        width: 0;
                        height: 1px;
                        margin-top: calc( ' . $item_text_height . 'px - 1px);
                        margin-left: 0;
                        content: "";
                        transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
                        text-align: left;
                        background-color: ' . $list_xhx_color . ';
                    }
                    ' . $addon_id . ' .box li a span:hover::before {
                        width:' . $list_xhx_width . 'px;
                    }';
			}

			$output .= '</style>
			';

			$output .= '
	            <div class="box">
	                <ul>';
			foreach ($section_tab_item as $key => $tab) {
				$link = '';
				if ($tab->detail_page_type == 'external_links') {
					if ($tab->detail_url) {
						// 判断是不是http或者https链接
						if (filter_var($tab->detail_url, FILTER_VALIDATE_URL) !== false) {
							$link = $tab->detail_url;
						} else {
							$link = 'http://' . $tab->detail_url;
						}
					}
				} else {
					if($tab->detail_page_id){
							$id = base64_encode($tab->detail_page_id);
							$link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
					}
					if ($tab->title_id) {
						$link .= '#' . $tab->title_id;
					}
				}
				$output .= '<li ';
				if ($tab->is_checkbox == 1) {
					$output .= 'class="actives"';
				}
				$output .= '>
	                            <a ';
				if ($settings->nav_mounting_js == 1 && $settings->nav_mounting == 1) {
					$output .= 'onclick=toPage' . $addonId . '("' . $tab->title_id . '")';
				} else {
					if ($link != '') {
						$output .= 'target="' . $tz_style . '" href="' . $link . '" ';
					}
				}
				$output .= '>';
				if ($tab->icon_img) {
					$output .= '<img src="' . $tab->icon_img . '" class="li-icon"/>';
				}
				if ($tab->icon_img_hover) {
					$output .= '<img src="' . $tab->icon_img_hover . '" class="li-icon hover"/>';
				}
				$output .= '<span>' . $tab->name . '</span>';


				if ($tab->is_hover_img == 1) {
					if ($tab->hover_bg_img) {
						$output .= '<div class="cad" ><img class="bsimg" src="' . $tab->hover_bg_img . '" /></div>';
					} else {
						$output .= '<div class="cad" ><img class="bsimg" src="https://oss.lcweb01.cn/joomla/20220301/b5dda18263f088e3f61f6d2063888e5a.png" /></div>';
					}
				}
				$output .= '</a>
	                        </li>';
			}
			$output .= '
	                </ul>
	            </div>';
		}

		return $output;
	}

	public function scripts()
	{
	}

	public  function js()
	{

		$settings = $this->addon->settings;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$addonId = $this->addon->id;
		// 锚点项目
		$section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
		// 是否开启吸顶
		$nav_mounting = (isset($settings->nav_mounting) && $settings->nav_mounting) ? $settings->nav_mounting : 0;
		// 吸顶区块距顶部距离
		$nav_mounting_top = (isset($settings->nav_mounting_top) && $settings->nav_mounting_top) ? $settings->nav_mounting_top : 0;
		$js = '';
		if ($nav_mounting == 1) {
			$js .= '
                jQuery(document).ready(function($){
                    var pageItem' . $addonId . ' = ' . json_encode($section_tab_item) . ';
                    var section' . $addonId . ' = $("' . $addon_id . '").parents(".jwpf-section");
                    var sectionH' . $addonId . ' = section' . $addonId . '.outerHeight();
                    var sTop' . $addonId . ' = section' . $addonId . '.offset().top;
                    var hasAfter' . $addonId . ' = false;
                    // console.log(section' . $addonId . ');
                    // console.log(sTop' . $addonId . ');
                    $(window).scroll(function(){
                        if($(window).scrollTop()>sTop' . $addonId . '){
                            section' . $addonId . '.css({"position":"fixed","top":' . $nav_mounting_top . ',"width":"100%","z-index": 999,"transition":"all 0.36s"});
                            if(!hasAfter' . $addonId . ') {
                                section' . $addonId . '.after("<div id=page-anchor-' . $addonId . '></div>");
                                $("#page-anchor-' . $addonId . '").height(section' . $addonId . '.outerHeight())
                                hasAfter' . $addonId . ' = true;
                            }
                        }else{
                            section' . $addonId . '.css({"position":"relative", "top": "auto"});
                            if(hasAfter' . $addonId . ') {
                                $("#page-anchor-' . $addonId . '").remove();
                                hasAfter' . $addonId . ' = false;
                            }
                        }';
			if ($settings->nav_mounting_active) {
				$js .= '
                                for(let i = 0; i < pageItem' . $addonId . '.length; i++) {
                                    let pageItemId = pageItem' . $addonId . '[i].title_id
                                    if(pageItemId) {
                                        //console.log("----------" + i + "---------")
                                        //console.log(i + "=====" + pageItemId)
                                        let sectionItemTop = $("#" + pageItemId).offset().top;
                                        //console.log(i + "元素距离顶部距离=====" + sectionItemTop)
                                        //console.log(i + "=====" + (sectionItemTop - sectionH' . $addonId . '))
                                        //console.log(i + "scrollTop=====" + $(window).scrollTop())
                                        if($(window).scrollTop() >= (sectionItemTop - sectionH' . $addonId . ' - 20)) {
                                            //console.log(i + "=====" + "滑到了" + i);
                                            //console.log(i + "=====" + $("' . $addon_id . ' .box ul li").eq(i))
                                            $("' . $addon_id . ' .box ul li").eq(i).addClass("actives").siblings().removeClass("actives")
                                        }
                                    }
                                }
                            ';
			}
			$js .= '});
                });';
			if ($settings->nav_mounting_js == 1) {
				$js .= '
                        function toPage' . $addonId . '(sectionId) {
                            var section' . $addonId . ' = $("' . $addon_id . '").parents(".jwpf-section");
                            var sectionH' . $addonId . ' = section' . $addonId . '.outerHeight();
                            //console.log(sectionId);
                            if(sectionId) {
                                var sectionTop = $("#" + sectionId).offset().top;
                                //console.log(sectionTop)
                                //console.log(sectionH' . $addonId . ')
                                var $body = (window.opera) ? (document.compatMode == "CSS1Compat" ? $("html") : $("body")) : $("html,body");
                                $body.animate({scrollTop: sectionTop - sectionH' . $addonId . '}, 1000)
                            }
                        }
                    ';
			}
			$js .= '';
		}

		return $js;
	}

	public function css()
	{
	}

	public static function getTemplate()
	{
		$output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		    var addonId = "#jwpf-addon-"+data.id;
		    /*新增*/
            // 文字大小
		    var title_fontsize = data.title_fontsize || "16";
		    // 导航项宽度
			var item_width_bai = data.item_width_bai || "0";
			var item_width_bfb = data.item_width_bfb || "33";
		    var item_width = data.item_width || "auto";
		    // 导航项高度
		    var item_height = data.item_height || "auto";
		    // 导航位置
		    var nav_position = data.nav_position || "";
		    // 导航下划线三角
		    var title_bottom_icon = data.title_bottom_icon || 0;
		    // 导航项文字行度
		    var item_text_height = data.item_text_height || "";
	
		    var media_bj = data.media_bj || "ym1";

			// 样式二配置项
			var media_style = data.media_style || "style1";
			var list_mark_size = data.list_mark_size || "";
			var list_mark_color = data.list_mark_color || "#0065cc";
			var list_margin_top = data.list_margin_top || 42;
			var list_item_font_size = data.list_item_font_size || 20;
			var list_item_color = data.list_item_color || "#333";
			var button_margin_top = data.button_margin_top || 80;
			var cont_img_width = data.cont_img_width || 35;
			var cont_img_width_phone = data.cont_img_width_phone || 35;
			var img_left_phone = data.img_left_phone || 55;
			
			var list_item_lineheight,list_item_lineheight_sm,list_item_lineheight_xs;
			if(Object.prototype.toString.call(data.list_item_lineheight) === "[object Object]"){
				list_item_lineheight = data.list_item_lineheight.md || 2;
				list_item_lineheight_sm = data.list_item_lineheight.sm || 2;
				list_item_lineheight_xs = data.list_item_lineheight.xs || 1.2;
			}else{
				list_item_lineheight = data.list_item_lineheight || 2;
				list_item_lineheight_sm = data.list_item_lineheight || 2;
				list_item_lineheight_xs = data.list_item_lineheight_xs || 1.2; 
			}
			var list_margin_top,list_margin_top_sm,list_margin_top_xs;
			if(Object.prototype.toString.call(data.list_margin_top) === "[object Object]"){
				list_margin_top = data.list_margin_top.md || 44;
				list_margin_top_sm = data.list_margin_top.sm || 44;
				list_margin_top_xs = data.list_margin_top.xs || 10;
			}else{
				list_margin_top = data.list_margin_top || 44;
				list_margin_top_sm = data.list_margin_top_sm || 44;
				list_margin_top_xs = data.list_margin_top_xs || 10;
			}
			var button_margin_top,button_margin_top_sm,button_margin_top_xs;
			if(Object.prototype.toString.call(data.button_margin_top) === "[object Object]"){
				button_margin_top = data.button_margin_top.md || 80;
				button_margin_top_sm = data.button_margin_top.sm || 60;
				button_margin_top_xs = data.button_margin_top.xs || 20;
			}else{
				button_margin_top = data.button_margin_top || 80;
				button_margin_top_sm = data.button_margin_top_sm || 60;
				button_margin_top_xs = data.button_margin_top_xs || 20;
			}
			var list_height,list_height_sm,list_height_xs;
			if(Object.prototype.toString.call(data.list_height) === "[object Object]"){
				list_height = data.list_height.md || "";
				list_height_sm = data.list_height.sm || "";
				list_height_xs = data.list_height.xs || 300;
			}else{
				list_height = data.list_height || "";
				list_height_sm = data.list_height_sm || "";
				list_height_xs = data.list_height_xs || "300";
			}
			var list_item_font_size,list_item_font_size_sm,list_item_font_size_xs;
			if(Object.prototype.toString.call(data.list_height) === "[object Object]"){
				list_item_font_size = data.list_item_font_size.md || 60;
				list_item_font_size_sm = data.list_item_font_size.sm || 60;
				list_item_font_size_xs = data.list_item_font_size.xs || 50;
			}else{
				list_item_font_size = data.list_item_font_size || 60;
				list_item_font_size_sm = data.list_item_font_size_sm || 60;
				list_item_font_size_xs = data.list_item_font_size_xs || 50;
			}
			var list_width,list_width_sm,list_width_xs;
			if(Object.prototype.toString.call(data.list_width) === "[object Object]"){
				list_width = data.list_width.md || 42;
				list_width_sm = data.list_width.sm || 42;
				list_width_xs = data.list_width.xs || 100;
			}else{
				list_item_lineheight = data.list_item_lineheight || 42;
				list_item_lineheight_sm = data.list_item_lineheight || 42;
				list_item_lineheight_xs = data.list_item_lineheight_xs || 100; 
			}

			var list_mark_settings = data.list_mark_settings || "color";
			var list_mark_img = data.list_mark_img || "";
			var list_mark_width = data.list_mark_width || "5";
			var list_padding_left = data.list_padding_left || "20";
		#>
        <style type="text/css">

        	<# if(media_bj != "ym1") { #>
				@media screen and (min-width:1000px) and (max-width:1600px) {
					{{ addonId }} .solute2-info{left: {{data.cont_left}}%;}
					{{ addonId }} .solute2-nav-list ul li img{margin-left: {{data.img_left}}%;}
				}
				@media screen and (max-width:650px) {
					{{ addonId }} .solute-type-list {
						position: relative;
					}
					{{ addonId }} .solute2-nav-list ul li img {
					    margin-left: {{data.img_left}}%;
					}
				}
				
				{{ addonId }} ul{list-style: none;}
				{{ addonId }} a{display: inline-block;text-decoration: none;color: inherit;}
				{{ addonId }} .clearfix:after{/*伪元素是行内元素 正常浏览器清除浮动方法*/
					content: "";
					display: block;
					height: 0;
					clear:both;
					visibility: hidden;
				}
				{{ addonId }} .clearfix{
					*zoom: 1;/*ie6清除浮动的方式 *号只有IE6-IE7执行，其他浏览器不执行*/
				}
				{{ addonId }} .width12{width:{{data.dh_width}}px;margin:0 auto;}
				/* swiper */

				/* 解决方案 */
				{{ addonId }} .solute-titlt{margin: auto;width: auto;text-align: center;padding: 50px 0 15px;margin-bottom: 30px;}
				{{ addonId }} .solute-titlt h1{display: inline;color: #333;padding-bottom: 18px;font-size: 46px;position: relative;font-weight: 500;}

				{{ addonId }} .solute-titlt h1::after{position: absolute;content: "";bottom: 0;left: 4.3%;width:90%;height: 5px;border-radius: 3px;background: #999;-moz-width:88%;}
				{{ addonId }} .solute-type-list .swiper-slide-thumb-active .solute-type{display: none;}
				{{ addonId }} .solute-type-list .swiper-slide-thumb-active .solute-type-on{display: flex;background: url(../images/127.png) no-repeat;background-size: 50% ;background-position:center 100% ;padding-bottom: 20px; color: #1196DB;}
				{{ addonId }} .solute-type-list .swiper-slide-thumb-active .solute-type-on img{filter:brightness(100%);
				-webkit-filter: brightness(100%);
				-moz-filter: brightness(100%);
				-o-filter: brightness(100%);
				-ms-filter: brightness(100%);}
				{{ addonId }} .solute-type{display: flex;flex-direction: column;align-items: center;}
				{{ addonId }} .solute-type-on{display: flex;flex-direction: column;align-items: center;}
				{{ addonId }} .solute-type-on img{filter:brightness(0);
				-webkit-filter: brightness(0);
				-moz-filter: brightness(0);
				-o-filter: brightness(0);
				-ms-filter: brightness(0);}
				{{ addonId }} .solute-type img{width: 35%;margin-bottom: 10px;}
				{{ addonId }} .solute-type-on img{width: 35%;margin-bottom: 10px;}
				{{ addonId }} .gallery-info{position: absolute;left: 20%;top: 40%;}
				{{ addonId }} .gallery-more{background-color: {{data.gd_color}};color: white;height: 40px;
				    width: 130px;border-radius: 20px;margin-top: 15px;display: block;align-items: center;line-height: 32px;text-align: center;}
				{{ addonId }} .more-an{margin-left: 10px;display: inline-block;width: 28px;height: 30px;position: relative;overflow: hidden;margin-bottom: -10px;
				    margin-top: 5px;
				}
				{{ addonId }} .more-an>span{display: inline-block;width: 30px;height: 30px;position: absolute;border: 1px solid white;border-radius: 50%;right: 0;top: 0;}
				{{ addonId }} .more-an>span::before{content: "";position: absolute;border-top: 1px solid white;border-right: 1px solid white; width: 10px;height: 10px;
				transform: rotateZ(45deg);
				-ms-transform:rotate(45deg); 	/* IE 9 */
				-moz-transform:rotate(45deg); 	/* Firefox */
				-webkit-transform:rotate(45deg); /* Safari 和 Chrome */
				-o-transform:rotate(45deg); 
				right: 5px;top: 8px;}
				{{ addonId }} .more-an>span::after{content: "";position: absolute;width: 16px;height: 1px;background: white;right: 5px;top: 13px;}
				@keyframes morean{
					from{right: 30px;}
					to{right:-20px}
				}
				{{ addonId }} .gallery-more:hover .more-an>span::before{animation:morean 1s ease-in-out 0s infinite;}
				{{ addonId }} .gallery-more:hover .more-an>span::after{animation:morean 1s ease-in-out 0s infinite;}
				{{ addonId }} .solute-type-list{margin-bottom: 7px;}

				{{ addonId }} .solute2-nav-tit ul li{width: 20%;float: left;padding-bottom: 5px;}
				{{ addonId }} .solute2-nav-tit ul li div{text-align: center;font-size:{{data.title_fontsize}}px;}
				{{ addonId }} .solute2-nav-tit ul li img{width: 36%;margin:5px 32% 20px;}
				{{ addonId }} .solute2-nav-tit ul li .solute2-type-on{color: #1196DB;display: none;background: url({{data.active_img}}) no-repeat;background-size: 50% ;background-position:center 100% ;padding-bottom: 15px;}
				{{ addonId }} .solute2-nav-tit ul li.on .solute2-type-on{display: block;}
				{{ addonId }} .solute2-nav-tit ul li .solute2-type{display: block;padding-bottom: 15px;}
				{{ addonId }} .solute2-nav-tit ul li.on .solute2-type{display: none;}
				{{ addonId }} .solute2-nav-list{overflow: hidden;background-color: {{data.cont_color}};}
				{{ addonId }} .solute2-nav-list ul{overflow: hidden;width: 500%;}
				{{ addonId }} .solute2-nav-list ul li{float: left;width: 20%;position: relative;}
				{{ addonId }} .solute2-info{position: absolute;left: {{data.cont_left}}%;top: 40%;color:{{data.cont_text_color}};}
				{{ addonId }} .solute2-nav-list ul li img{width: {{data.cont_img_width}}%;margin-left: {{data.img_left}}%;}
				{{ addonId }} .solute2-info h1{font-size:{{data.cont_text_size}}px;}
				@media screen and (max-width:1200px) {
					{{ addonId }} html{width: 1200px;}
				}
				@media screen and (max-width:650px){
					{{ addonId }} html{width: 100%;}
					{{ addonId }} .width12{width: 100%;    overflow-x: scroll;}
					
					{{ addonId }} .solute2-info h1{font-size: 13px;}
					{{ addonId }} .solute2-info{left: 5%;top: 30%;}
					{{ addonId }} .solute2-nav-tit ul li img{margin: 5px 32% 8px;}
					{{ addonId }} .solute2-nav-tit ul li div{font-size: 13px;}

				}

				<# if(media_style == "style2"){ #>
					{{ addonId }} .solute2-info{
						width: {{list_width}}%;
						top: 50%;
						transform: translateY(-50%);
					}
					{{ addonId }} .solute2-info h1{
						font-weight: bold;
					}
					{{ addonId }} .solute2-info .list{
						width: 100%;
						margin-top: {{list_margin_top}}px;
					}
					{{ addonId }} .solute2-info .list ul{
						width: 100%;
						margin-left: -19px;
					}
					
					<# if(list_mark_settings == "color"){ #>
						{{ addonId }} .solute2-info .list ul li::marker{
							font-size: {{list_mark_size}}px;
							color: {{list_mark_color}};
						}
						{{ addonId }} .solute2-info .list ul li::before{
							display: none;
						}
					<# }else{ #>
						{{ addonId }} .solute2-info .list ul li::before{
							content: "";
							display: inline-block;
							position: absolute;
							top: 50%;
							transform: translateY(-50%);
							left: -16px;
							width: {{list_mark_width}}px;
							height: {{list_mark_width}}px;
							background: url({{list_mark_img}}) no-repeat;background-size: 100%;background-position:center 100%;
						}
						{{ addonId }} .solute2-info .list ul li{
							list-style: none;
						}
					<# } #>
					{{ addonId }} .solute2-nav-list  .list ul li{
						width: 100%;
						font-size: {{list_item_font_size}}px;
						line-height: {{list_item_lineheight}};
						color: {{list_item_color}};
					}
					{{ addonId }} .solute2-nav-list ul li img{
						width: {{cont_img_width}}%;
					}
					{{ addonId }} .gallery-more{
						margin-top: {{button_margin_top}}px;
					}
					<# _.each(data.section_tab_item1, function(tab, key){
						if(tab.need_content == 1){
							var cols_style2 = tab.cont_list_cols > 0 ? tab.cont_list_cols : 1; #>
							{{ addonId }} .solute2-nav-list li:nth-of-type({{key + 1}}) .list ul li{
								width: calc(100% / {{cols_style2}});
							}
						<# }
					}) #>
					@media (max-width: 991px) and (min-width: 768px){
						{{ addonId }} .solute2-info{
							width: {{list_width_sm}}%;
						}
						{{ addonId }} .solute2-nav-list{
							height: {{list_height_sm}}px;
						}
						{{ addonId }} .solute2-info .list{
							margin-top: {{list_margin_top_sm}}px;
						}
						{{ addonId }} .solute2-nav-list .list ul li{
							line-height: {{list_item_lineheight_sm}};
							font-size: {{list_item_font_size_sm}}px;
						}
						{{ addonId }} .gallery-more{
							margin-top: {{button_margin_top_sm}}px;
						}
					}
					@media (max-width: 767px){
						{{ addonId }} .solute2-info{
							width: {{list_width_xs}}%;
						}
						{{ addonId }} .solute2-nav-list{
							height: {{list_height_xs}}px;
						}
						{{ addonId }} .solute2-info .list{
							margin-top: {{list_margin_top_xs}}px;
						}
						{{ addonId }} .solute2-nav-list .list ul li{
							line-height: {{list_item_lineheight_xs}};
							font-size: {{list_item_font_size_xs}}px;
							width: 100%!important;
						}
						{{ addonId }} .solute2-nav-list ul li img{
							width: {{cont_img_width_phone}}%;
							margin-left: {{img_left_phone}}%;
						}
						{{ addonId }} .gallery-more{
							margin-top: {{button_margin_top_xs}}px;
						}
						<# _.each(data.section_tab_item1, function(tab, key){
							if(tab.need_content){ #>
								{{addonId}} .solute2-nav-list li:nth-of-type({{key + 1}}) .solute2-info{
									top: 0%;
									transform: translateY(0);
								}
							<# }else{ #>
								{{addonId}} .solute2-nav-list li:nth-of-type({{key + 1}}) .solute2-info{top: 30%;}
							<# }
						}) #>
					}
				<# } #>
			<# }else{ #>

				{{ addonId }} .box ul {
					list-style: {{data.list_style}};
					display: flex;
					flex-wrap: wrap;
					padding: 0;
					margin: 0;
					justify-content: {{nav_position}};
					<# if(data.title_bottom_icon == 1 ){ #>
					flex-wrap: wrap;
					<# } else { #>
					overflow:hidden;
					<# } #>
				}
				{{ addonId }} .box li {
					background:{{data.title_bg_color}};
					<# if(item_width_bai==1){ #>
						width: {{item_width_bfb}}%;
					<# }else{ #>
						width: {{item_width}}px;
					<# } #>
					height: {{ item_height }}px;
					line-height: {{ item_height }}px;
					text-align: center;
					border-bottom: 3px solid transparent;
					transition: all ease-in-out 0.3s;
					cursor: pointer;
					position: relative;
				}
				{{ addonId }} .box li:not(:first-child) {
					margin-left: {{data.title_jj}}px;
				}
				{{ addonId }} .box li::marker {
					color: {{data.list_style_color}};
				}
				{{ addonId }} .box li:hover::marker {
					color: {{data.list_style_color_hg}};
				}
				{{ addonId }} .box li:hover, {{ addonId }} .box li.actives {
					background:{{data.title_bg_color_hg}};
					border-bottom: 3px solid {{data.title_border_color_hg || "transparent"}};
				}
				<# if(title_bottom_icon == 1) { #>
	                {{ addonId }} .box li:hover::after, {{ addonId }} .box li.actives::after {
	                    content: "";
	                    position: absolute;
	                    top: calc(100% + 3px);
	                    left: calc(50% - 6px);
	                    width: 0;
	                    height: 0;
	                    border: 6px solid transparent;
	                    border-top-color: {{data.title_border_color_hg || "transparent"}} ;
	                }
				<# } #>
				{{ addonId }} .box ul li a {
					color: {{data.title_color}};
					font-size: {{ title_fontsize }}px;
					text-decoration: none;
					transition: all ease-in-out 0.3s;
					display: block;
				}
				{{ addonId }} .box ul li a .li-icon {
				    margin: auto;
				    max-width: 100%;
				}
				{{ addonId }} .box li a .li-icon.hover {
				    display: none;
				}
				{{ addonId }} .box li:hover a {
					color: {{data.title_color_hg}};
				}
				{{ addonId }} .box li:hover a .li-icon, {{ addonId }} .box li.actives a .li-icon {
				    display: none;
				}
				{{ addonId }} .box li:hover a .li-icon.hover, {{ addonId }} .box li.actives a .li-icon.hover {
				    display: block;
				}
				{{ addonId }} .box li a span {
				    display: inline-block;
				    line-height: {{item_text_height}}px;
				}

				<# if(data.list_xhx_dh == 1) { #>
					{{ addonId }} .box li a span::before {
						    position: absolute;
						    width: 0;
						    height: 1px;
						    margin-top: calc( {{ data.item_text_height }}px - 1px );
						    margin-left: 0;
						    content: "";
						    transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
						    text-align: left;
						    background-color: {{data.list_xhx_color}};
					}
					{{ addonId }} .box li a span:hover::before {
						width:{{data.list_xhx_width}}px;
					}
				<# } #>
				{{ addonId }} .box .actives{
					color: {{data.title_color_hg}};
					background:{{data.title_bg_color_hg}};
				}
				{{ addonId }} .box .actives a {
					color: {{data.title_color_hg}};
				}
				{{ addonId }} .box .actives a span,
				{{ addonId }} .box li:hover a span
				{
					<# if(data.title_underline == 1) { #>
						text-decoration: underline;
					<# } #>
				}
				
				<# if(data.list_style_after=="one"){ #>

					{{ addonId }} .box ul>li::after {
					    content: "";
					    position: absolute;
					    width: 1px;
					    height: 10px;
					    background: rgba(170, 170, 170, .3);
					    top: calc( ( {{ item_height }}px - 10px) / 2);
					    right: calc( -{{data.title_jj}}px / 2 );
					}	

				<# } #>

				{{ addonId }} .box ul>li .cad .bsimg{
					transition:all 0.36s;
					width:50%;
					height:14px;
					vertical-align:middle;
					display:inline;
				}
				{{ addonId }} .box ul>li .cad{
				    width:100%;
				    height:{{ item_height }}px;
				    background:{{data.title_bg_color_hg}};
				    text-align:center;
				    line-height:{{ item_height }}px;
				    position:relative;
				    z-index:12;
                }
				{{ addonId }} .box ul>li:hover .cad{
					transition:all 0.36s;
					margin-top:-{{ item_height }}px;
				}


			<# } #>
		</style>
		<# if(media_bj == "ym2") { #>
			<div class="solute2">
				<div class="width12 solute2-nav-tit">
					<ul class="clearfix">
						<# _.each(data.section_tab_item1, function(tab, key){ #>
							<li >
								<div class="solute2-type-on">
									<# if(tab.icon_img_hover) { #>
										<img src=\'{{tab.icon_img_hover}}\'>
									<# } #>
									<div class="">
										{{tab.name}}
									</div>
								</div>
								<div class="solute2-type">
									<# if(tab.icon_img) { #>
										<img src=\'{{tab.icon_img}}\'">
									<# } #>
									<div class="">
										{{tab.name}}				
									</div>
								</div>
							</li>
						<# }); #>					
					</ul>
				</div>
				<div class="solute2-nav-list">
					<ul>
						<# _.each(data.section_tab_item1, function(tab, key){ #>
							<li>
								<div class="solute2-info">
									<h1>{{tab.cont}}</h1>
									
									<# if(media_style == "style2"){
										if(tab.need_content == 1){
											if(tab.custom_content == 1){ #>
												<div class="list">{{{tab.cont_list}}}</div>
											<# }else{
												var tab_content = tab.content_list_item || [];
												if(tab_content.length > 0){ #>
													<div class="list">
														<ul>
															<# _.each(tab_content, function(li, k){ #>
																<li>{{{li.text}}}</li>
															<# }) #>
														</ul>
													</div>
												<# }
										 	}
										}
									} #>

									<a href="" class="gallery-more">了解更多<span class="more-an"><span></span></span></a>
								</div>
								<# if(tab.banner_img) { #>
									<img src=\'{{tab.banner_img}}\'>
								<# } #>
							</li>
						<# }); #>											
					</ul>
				</div>
			</div>
		<# }else{ #>
	        <div class="box">
	        	<ul>
				<# _.each(data.section_tab_item, function(tab, key){ #>
					<li <# if(tab.is_checkbox==1){ #> class="actives" <# } #> >
					    <a href="">
					        <# if(tab.icon_img) { #>
					            <img src=\'{{tab.icon_img}}\' class="li-icon"/>
					        <# } #>
					        <# if(tab.icon_img_hover) { #>
					            <img src=\'{{tab.icon_img_hover}}\' class="li-icon hover"/>
					        <# } #>
					        <span>{{tab.name}}</span>

					        <# if(tab.is_hover_img==1) {
                            	if(tab.hover_bg_img){ #>
                            		<div class="cad" ><img class="bsimg" src=\'{{tab.hover_bg_img}}\' /></div>
                            	<# }else{ #>
                            		<div class="cad" ><img class="bsimg" src="https://oss.lcweb01.cn/joomla/20220301/b5dda18263f088e3f61f6d2063888e5a.png" /></div>
                            	<# }
                            } #>

					    </a>
	                </li>
				<# }); #>
				</ul>
			</div>
		<# } #>
		';

		return $output;
	}
}
