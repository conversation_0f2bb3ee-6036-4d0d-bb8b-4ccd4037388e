<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonRecruit_list extends JwpagefactoryAddons
{
    public function pageLink($page_id)
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        if($page_id){
            $id = base64_encode($page_id);
            return 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;

        }
    }

	public function render()
	{
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';

        $settings = $this->addon->settings;

        // 列表布局
        $recruit_type_selector = (isset($settings->recruit_type_selector) && $settings->recruit_type_selector) ? $settings->recruit_type_selector : "type01";

        // 招聘分类
        $zp_catid = (isset($settings->zp_catid) && $settings->zp_catid) ? $settings->zp_catid : 0;

        // 显示表格头
        $table_th = (isset($settings->table_th) && $settings->table_th) ? $settings->table_th : 0;
        // 显示【职位名称】
        $table_th_01 = (isset($settings->table_th_01) && $settings->table_th_01) ? $settings->table_th_01 : 0;
        // 【职位名称】标题文字
        $table_th_title01 = (isset($settings->table_th_title01)&& $settings->table_th_title01) ? $settings->table_th_title01 : "职位名称";
        // 显示【招聘人数】
        $table_th_02 = (isset($settings->table_th_02) && $settings->table_th_02) ? $settings->table_th_02 : 0;
        // 【招聘人数】标题文字
        $table_th_title02 = (isset($settings->table_th_title02) && $settings->table_th_title02) ? $settings->table_th_title02 : "招聘人数";
        // 显示【职位说明】
        $table_th_03 = (isset($settings->table_th_03) && $settings->table_th_03) ? $settings->table_th_03 : 0;
        // 【职位说明】标题文字
        $table_th_title03 = (isset($settings->table_th_title03) && $settings->table_th_title03) ? $settings->table_th_title03 : "职位说明";
        // 关闭【应聘】
        $table_th_04 = (isset($settings->table_th_04) && $settings->table_th_04) ? $settings->table_th_04 : 0;
        // 【应聘】标题文字
        $table_th_title04 = (isset($settings->table_th_title04) && $settings->table_th_title04) ? $settings->table_th_title04 : "应聘";
        // 【应聘】文字
        $table_td_btn = (isset($settings->table_td_btn) && $settings->table_td_btn) ? $settings->table_td_btn : "应聘";
        // 显示【最低学历】
        $table_th_05 = (isset($settings->table_th_05) && $settings->table_th_05) ? $settings->table_th_05 : 0;
        // 【最低学历】标题文字
        $table_th_title05 = (isset($settings->table_th_title05) && $settings->table_th_title05) ? $settings->table_th_title05 : "最低学历";
        // 显示【工作经验】
        $table_th_06 = (isset($settings->table_th_06) && $settings->table_th_06) ? $settings->table_th_06 : 0;
        // 【工作经验】标题文字
        $table_th_title06 = (isset($settings->table_th_title06) && $settings->table_th_title06) ? $settings->table_th_title06 : "工作经验";
        // 显示【薪资待遇】
        $table_th_07 = (isset($settings->table_th_07) && $settings->table_th_07) ? $settings->table_th_07 : 0;
        // 【薪资待遇】标题文字
        $table_th_title07 = (isset($settings->table_th_title07) && $settings->table_th_title07) ? $settings->table_th_title07 : "薪资待遇";
        // 显示【岗位详情】
        $table_th_08 = (isset($settings->table_th_08) && $settings->table_th_08) ? $settings->table_th_08 : 0;
        // 【岗位详情】标题文字
        $table_th_title08 = (isset($settings->table_th_title08) && $settings->table_th_title08) ? $settings->table_th_title08 : "岗位详情";

        // 职位名称跳转页面
		$title_detail_page_id = (isset($settings->title_detail_page_id)) ? $settings->title_detail_page_id : 0;
        $title_link = $this->pageLink($title_detail_page_id);
        $title_target = (isset($settings->title_target) && $settings->title_target) ? $settings->title_target : "_self";
        // 【应聘】跳转页面
        $action_detail_page_id = (isset($settings->action_detail_page_id)) ? $settings->action_detail_page_id : 0;
        $action_link = $this->pageLink($action_detail_page_id);

        $action_target = (isset($settings->action_target) && $settings->action_target) ? $settings->action_target : "_self";
        $table_td_zwsm = (isset($settings->table_td_zwsm) && $settings->table_td_zwsm) ? $settings->table_td_zwsm : "30";


        $tab_item = array(
            (object)array(
                'title' => '行政前台',
                'recruit_num' => '2人',
                'fulltext' => '前台访客接待，总机电话接听',
            ),
            (object)array(
                'title' => '大数据开发工程师',
                'recruit_num' => '1人',
                'fulltext' => '负责深度学习平台大数据工程，负责数据流加速工作',
            ),
            (object)array(
                'title' => '电话客服',
                'recruit_num' => '若干',
                'fulltext' => '前台访客接待，总机电话接听',
            ),
        );

        require_once $article_helper;
        $tab_item = JwpagefactoryHelperArticles::getRecruitList(100, 'desc', $zp_catid, '', $title_detail_page_id, $page, $company_id, $layout_id, $site_id);


        $output = '<div class="table ' . $recruit_type_selector . '">';
        if($table_th == 1) {
            $output .= '<div class="table-tr table-h">';
            if($table_th_01 == 1) {
                $output .= '<div class="table-th">' . $table_th_title01 . '</div>';
            }
            if($table_th_05 == 1) {
                $output .= '<div class="table-th">' . $table_th_title05 . '</div>';
            }
            if($table_th_06 == 1) {
                $output .= '<div class="table-th">' . $table_th_title06 . '</div>';
            }
            if($table_th_02 == 1) {
                $output .= '<div class="table-th">' . $table_th_title02 . '</div>';
            }
            if($table_th_03 == 1) {
                $output .= '<div class="table-th">' . $table_th_title03 . '</div>';
            }
            if($table_th_07 == 1) {
                $output .= '<div class="table-th">' . $table_th_title07 . '</div>';
            }
            if($table_th_04 != 1) {
                $output .= '<div class="table-th">' . $table_th_title04 . '</div>';
            }
            if($table_th_08 == 1) {
                $output .= '<div class="table-th">' . $table_th_title08 . '</div>';
            }
            $output .= '</div>';
        }
        if($tab_item){
            foreach($tab_item as $key => $item){
                $jianjie = mb_substr(strip_tags($item->fulltext), 0, $table_td_zwsm, 'UTF-8');
                // 应聘链接id
                $action_linka = $action_link.'&detail='.$item->id;

                $actives = '';
                $action_icon = (isset($settings->action_icon) && $settings->action_icon) ? $settings->action_icon : 'https://oss.lcweb01.cn/joomla/20220329/3fd0b8508d083a6ad3f029af19561837.png';
                $action_icon_active = (isset($settings->action_icon_active) && $settings->action_icon_active) ? $settings->action_icon_active : 'https://oss.lcweb01.cn/joomla/20220329/41f1a566a2a664c2b83d455f53cec8e5.png';
                $imgSrc = $action_icon;

                if($key == 0 && $recruit_type_selector == 'type02') {
                    $actives = 'actives';
                    $imgSrc = $action_icon_active;
                }

                $output .= '<div class="table-tr ' . $actives . '">';
                if($table_th_01 == 1) {
                    $output .= '<div class="table-td"><a target="' . $title_target . '" href="' . $item->link . '">' . $item->title . '</a></div>';
                }
                if($table_th_05 == 1) {
                    $output .= '<div class="table-td">' . $item->education . '</div>';
                }
                if($table_th_06 == 1) {
                    $output .= '<div class="table-td">' . $item->experience . '</div>';
                }
                if($table_th_02 == 1) {
                    $output .= '<div class="table-td">' . $item->recruit_num . '</div>';
                }
                if($table_th_03 == 1) {
                    $output .= '<div class="table-td">' . $jianjie . '...</div>';
                }
                if($table_th_07 == 1) {
                    $output .= '<div class="table-td">' . $item->treatment . '</div>';
                }
                if($table_th_04 != 1) {
                    $output .= '<div class="table-td action"><a target="' . $action_target . '" href="' . $action_linka . '">' . $table_td_btn . '</a></div>';
                }
                if($table_th_08 == 1 && $recruit_type_selector == 'type02') {
                    $output .= '<div class="table-td open" data-index="open-' . $this->addon->id . '-' . $key . '"><img class="action-icon" src=\'' . $imgSrc . '\' alt=""></div>';
                }
                $output .= '</div>';
                if($recruit_type_selector == 'type02') {
                    $output .= '<div class="table-content ' . $actives . '">' . $item->fulltext . '</div>';
                }
            }
        }else{
            $output .= '<div class="table-tr">暂无招聘信息</div>';

        }
        $output .= '</div>';

		return $output;
	}

	public function scripts()
	{
//		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/jquery.magnific-popup.min.js');
	}

	public function js()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $addonId = $this->addon->id;
		$settings = $this->addon->settings;

        // 列表布局
        $recruit_type_selector = (isset($settings->recruit_type_selector) && $settings->recruit_type_selector) ? $settings->recruit_type_selector : "type01";

        $action_icon = (isset($settings->action_icon) && $settings->action_icon) ? $settings->action_icon : 'https://oss.lcweb01.cn/joomla/20220329/3fd0b8508d083a6ad3f029af19561837.png';
        $action_icon_active = (isset($settings->action_icon_active) && $settings->action_icon_active) ? $settings->action_icon_active : 'https://oss.lcweb01.cn/joomla/20220329/41f1a566a2a664c2b83d455f53cec8e5.png';


        $js = 'jQuery(function($){';
        if($recruit_type_selector == 'type02') {
            $js .= '
                $("' . $addon_id . ' .table.type02 .table-tr .open").click(function(){
                    var index = $(this).attr("data-index");
                    //console.log(index);
                    var parent = $(this).parent(".table-tr");
                    //console.log(parent);
                    // 更换按钮
                    $("' . $addon_id . ' .table.type02 .action-icon").attr("src", "' . $action_icon . '");
                    $(this).children(".action-icon").attr("src", "' . $action_icon_active . '");
                    // 选中当前行
                    $("' . $addon_id . ' .table.type02 .table-tr").removeClass("actives");
                    parent.addClass("actives");
                    // 展开当前内容
                    $("' . $addon_id . ' .table.type02 .table-content").hide();
                    parent.next().fadeIn(500);
                })
            ';
        }
        $js .= '});';
        return $js;
    }

	public function stylesheets()
	{
//		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/magnific-popup.css');
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;

        // 列表布局
        $recruit_type_selector = (isset($settings->recruit_type_selector) && $settings->recruit_type_selector) ? $settings->recruit_type_selector : "type01";

		// 表格头部高度
        $table_th_height = (isset($settings->table_th_height) && $settings->table_th_height) ? $settings->table_th_height : 30;
        // 表格头背景颜色
        $table_th_bgColor = (isset($settings->table_th_bgColor) && $settings->table_th_bgColor) ? $settings->table_th_bgColor : "rgba(255, 255, 255, 0)";
        // 表格头文字位置
        $table_th_align = (isset($settings->table_th_align) && $settings->table_th_align) ? $settings->table_th_align : "center";
        // 表格头文字大小
        $table_th_fontsize = (isset($settings->table_th_fontsize) && $settings->table_th_fontsize) ? $settings->table_th_fontsize : 14;
        // 表格头文字颜色
        $table_th_color = (isset($settings->table_th_color) && $settings->table_th_color) ? $settings->table_th_color : "rgba(0, 0, 0, 1)";
        // 表格头文字加粗
        $table_th_fontWeight = (isset($settings->table_th_fontWeight) && $settings->table_th_fontWeight) ? $settings->table_th_fontWeight : 0;
        // 表格头部高度
        $table_td_height = (isset($settings->table_td_height) && $settings->table_td_height) ? $settings->table_td_height : 30;
        // 表格内容文字位置
        $table_td_align = (isset($settings->table_td_align) && $settings->table_td_align) ? $settings->table_td_align : "center";
        // 表格内容文字大小
        $table_td_fontsize = (isset($settings->table_td_fontsize) && $settings->table_td_fontsize) ? $settings->table_td_fontsize : 14;
        // 表格内容文字颜色
        $table_td_color = (isset($settings->table_td_color) && $settings->table_td_color) ? $settings->table_td_color : "rgba(0, 0, 0, 1)";
        // 【应聘】文字颜色
        $table_td_btnColor = (isset($settings->table_td_btnColor) && $settings->table_td_btnColor) ? $settings->table_td_btnColor : "rgba(0, 0, 0, 1)";

        // 开启单元格间距
        $table_mg = (isset($settings->table_mg) && $settings->table_mg) ? $settings->table_mg : 0;
        // 单元格列间距
        $table_border_r = (isset($settings->table_border_r) && $settings->table_border_r) ? $settings->table_border_r : 0;
        // 单元格行间距
        $table_border_c = (isset($settings->table_border_c) && $settings->table_border_c) ? $settings->table_border_c : 0;

        // 表格头部圆角
        $table_th_border = (isset($settings->table_th_border) && $settings->table_th_border) ? $settings->table_th_border : 0;

        // 开启行外边框
        $table_td_border = (isset($settings->table_td_border) && $settings->table_td_border) ? $settings->table_td_border : 0;
        // 行外边框颜色
        $table_td_border_color = (isset($settings->table_td_border_color) && $settings->table_td_border_color) ? $settings->table_td_border_color : '#eee';
        // 选中表格内容颜色
        $table_td_color_active = (isset($settings->table_td_color_active) && $settings->table_td_color_active) ? $settings->table_td_color_active : '#0065ff';
        // 【岗位详情】内容部分按钮宽度
        $action_icon_w = (isset($settings->action_icon_w) && $settings->action_icon_w) ? $settings->action_icon_w : 20;
        // 【岗位详情】内容部分按钮高度
        $action_icon_h = (isset($settings->action_icon_h) && $settings->action_icon_h) ? $settings->action_icon_h : 20;


        $css = $addon_id . ' .table, ' . $addon_id . ' .table * {
            margin: 0 auto;
            padding: 0;
        }   
        ' . $addon_id . ' .table {
            display: table;
            width: 100%;
            border-collapse: collapse;';
            if($table_mg == 1) {
                $css .= "border-collapse: separate;
                border-spacing: {$table_border_r}px {$table_border_c}px;";
            }
        $css .= '}   
        ' . $addon_id . ' .table-tr {
            display: table-row;
            /*height: 30px;*/
        } 
        ' . $addon_id . ' .table-tr a {
            color: inherit;
        }
        ' . $addon_id . ' .table-h {
            background-color: ' . $table_th_bgColor . ';
        }
        ' . $addon_id . ' .table-th {
            display: table-cell;';
            if($table_th_fontWeight == 1) {
                $css .= 'font-weight: bold;';
            }
            $css .= 'height: ' . $table_th_height . 'px;
            padding: 0 10px;
            text-align: ' . $table_th_align . ';
            vertical-align: middle;
            font-size: ' . $table_th_fontsize . 'px;
            color: ' . $table_th_color . ';
        }
        ' . $addon_id . ' .table-th:first-child {
            border-radius: ' . $table_th_border . ';
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        ' . $addon_id . ' .table-th:last-child {
            border-radius: ' . $table_th_border . ';
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        ' . $addon_id . ' .table-td {
            display: table-cell;
            height: ' . $table_td_height . 'px;';
            if($table_td_border && $recruit_type_selector == 'type01') {
                $css .= 'border: 1px solid ' . $table_td_border_color . ';
                border-left-width: 0;
                border-right-width: 0;';
            }
            $css .= 'text-align: ' . $table_td_align . ';
            padding: 0 10px;
            vertical-align: middle;
            font-size: ' . $table_td_fontsize . 'px;
            color: ' . $table_td_color . ';
        }
        ' . $addon_id . ' .table-td:first-child {';
            if($table_td_border && $recruit_type_selector == "type01") {
                $css .= 'border-left-width: 1px;';
            }
        $css .= '}
        ' . $addon_id . ' .table-td:last-child {';
            if($table_td_border && $recruit_type_selector == "type01") {
                $css .= 'border-right-width: 1px;';
            }
        $css .= '}
        ' . $addon_id . ' .table-td.action {
            color: ' . $table_td_btnColor . ';
        }
        /*布局02*/
        ' . $addon_id . ' .table.type02 {
            display: block;
        }
        ' . $addon_id . ' .table.type02 .table-h {
            border-radius: ' . $table_th_border . ';
        }
        ' . $addon_id . ' .table.type02 .table-tr, ' . $addon_id . ' .table.type02 .table-h {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(10px, 4fr));
        } 
        ' . $addon_id . ' .table.type02 .table-tr {
            align-items: center;';
            if($table_td_border) {
                $css .= 'border: 1px solid ' . $table_td_border_color .';';
            }
            if($table_mg == 1) {
                $css .= 'margin-top: ' . $table_border_c .'px;';
            }
        $css .= '}
        ' . $addon_id . ' .table.type02 .table-tr.actives {
            
        } 
        ' . $addon_id . ' .table.type02 .table-tr.actives .table-td {
            color: ' . $table_td_color_active .';
        }
        ' . $addon_id . ' .table.type02 .table-th, ' . $addon_id . ' .table.type02 .table-td {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        ' . $addon_id . ' .table.type02 .table-td {
            width: 100%;';
            if($table_mg == 1) {
                $css .= 'margin-right: ' . $table_border_r . 'px;';
            }
        $css .= '}
        ' . $addon_id . ' .table.type02 .table-td:last-child {';
            if($table_mg == 1) {
                $css .= 'margin-right: 0px;';
            }
        $css .= '}
        ' . $addon_id . ' .table.type02 .action-icon {
            width: ' . $action_icon_w . 'px;
            height: ' . $action_icon_h . 'px;
            object-fit: contain;
        }
        ' . $addon_id . ' .table.type02 .table-content {
            display: none;
            padding: 20px 30px;
            font-size: 14px;';
            if($table_td_border) {
                $css .= 'border: 1px solid ' . $table_td_border_color . ';
                border-bottom: 3px solid ' . $table_td_color_active . ';';
            }
        $css .= '}
        ' . $addon_id . ' .table.type02 .table-content.actives {
            display: block;
        }
        ';
		return $css;

	}

	public static function getTemplate()
	{

		$output = '
		<#
		    var addonId = "#jwpf-addon-" + data.id;
		    
		    // 列表布局
		    var recruit_type_selector = data.recruit_type_selector || "type01";
		    
		    // 显示表格头
		    var table_th = data.table_th || 0;
		    // 显示【职位名称】
		    var table_th_01 = data.table_th_01 || 0;
		    // 【职位名称】标题文字
		    var table_th_title01 = data.table_th_title01 || "职位名称";
		    // 显示【招聘人数】
		    var table_th_02 = data.table_th_02 || 0;
		    // 【招聘人数】标题文字
		    var table_th_title02 = data.table_th_title02 || "招聘人数";
		    // 显示【职位说明】
		    var table_th_03 = data.table_th_03 || 0;
		    // 【职位说明】标题文字
		    var table_th_title03 = data.table_th_title03 || "职位说明";
		    // 关闭【应聘】
		    var table_th_04 = data.table_th_04 || 0;
		    // 【应聘】标题文字
		    var table_th_title04 = data.table_th_title04 || "应聘";
		    // 显示【最低学历】
		    var table_th_05 = data.table_th_05 || 0;
		    // 【最低学历】标题文字
		    var table_th_title05 = data.table_th_title05 || "最低学历";
		    // 显示【工作经验】
		    var table_th_06 = data.table_th_06 || 0;
		    // 【工作经验】标题文字
		    var table_th_title06 = data.table_th_title06 || "工作经验";
		    // 显示【薪资待遇】
		    var table_th_07 = data.table_th_07 || 0;
		    // 【薪资待遇】标题文字
		    var table_th_title07 = data.table_th_title07 || "薪资待遇";
		    // 显示【岗位详情】
		    var table_th_08 = data.table_th_08 || 0;
		    // 【岗位详情】标题文字
		    var table_th_title08 = data.table_th_title08 || "岗位详情";
		    // 表格头部高度
		    var table_th_height = data.table_th_height || 30;
		    // 表格头背景颜色
		    var table_th_bgColor = data.table_th_bgColor || "rgba(255, 255, 255, 0)";
		    // 表格头部圆角
		    var table_th_border = data.table_th_border || "0";
		    // 表格头文字位置
		    var table_th_align = data.table_th_align || "center";
		    // 表格头文字大小
		    var table_th_fontsize = data.table_th_fontsize || 14;
		    // 表格头文字颜色
		    var table_th_color = data.table_th_color || "rgba(0, 0, 0, 1)";
		    // 表格头文字加粗
		    var table_th_fontWeight = data.table_th_fontWeight || 0;
		    // 表格头部高度
		    var table_td_height = data.table_td_height || 30;
		    // 表格内容文字位置
		    var table_td_align = data.table_td_align || "center";
		    // 表格内容文字大小
		    var table_td_fontsize = data.table_td_fontsize || 14;
		    // 表格内容文字颜色
		    var table_td_color = data.table_td_color || "rgba(0, 0, 0, 1)";
		    // 【应聘】文字
		    var table_td_btn = data.table_td_btn || "应聘";
		    // 【应聘】文字颜色
		    var table_td_btnColor = data.table_td_btnColor || "rgba(0, 0, 0, 1)";
		    
		    // 开启单元格间距
		    var table_mg = data.table_mg || 0;
		    // 单元格列间距
		    var table_border_r = data.table_border_r || 0;
		    // 单元格行间距
		    var table_border_c = data.table_border_c || 0;
		    
		    // 行外边框颜色
		    var table_td_border_color = data.table_td_border_color || "#eee";
		    // 选中表格内容颜色
		    var table_td_color_active = data.table_td_color_active || "#0065ff";
		    // 【岗位详情】内容部分按钮宽度
		    var action_icon_w = data.action_icon_w || 20;
		    // 【岗位详情】内容部分按钮高度
		    var action_icon_h = data.action_icon_h || 20;
		    
		    // 表格内容
		    var tab_item = [{
		        name: "行政前台",
		        experience: "三年以上",
		        education: "本科",
		        treatment: "面议",
		        num: "2人",
		        content: "前台访客接待，总机电话接听"
		    },{
		        name: "大数据开发工程师",
		        experience: "三年以上",
		        education: "本科",
		        treatment: "面议",
		        num: "1人",
		        content: "负责深度学习平台大数据工程，负责数据流加速工作"
		    },{
		        name: "电话客服",
		        experience: "三年以上",
		        education: "本科",
		        treatment: "面议",
		        num: "若干",
		        content: "前台访客接待，总机电话接听"
		    }]
		#>
		<style>
			{{ addonId }} .table, {{ addonId }} .table * {
			    margin: 0 auto;
			    padding: 0;
            }   
            {{ addonId }} .table {
                display: table;
                width: 100%;
                border-collapse: collapse;
                <# if(table_mg == 1) { #>
                border-collapse: separate;
                border-spacing: {{table_border_r}}px {{table_border_c}}px;
                <# } #>
            }   
            {{ addonId }} .table-tr {
                display: table-row;
            } 
            {{ addonId }} .table-h {
                display: table-row;
                background-color: {{ table_th_bgColor }};
            }
            {{ addonId }} .table-th {
                display: table-cell;
                <# if(table_th_fontWeight == 1) { #>
                    font-weight: bold;
                <# } #>
                height: {{ table_th_height }}px;
                padding: 0 10px;
                text-align: {{ table_th_align }};
                vertical-align: middle;
                font-size: {{ table_th_fontsize }}px;
                color: {{ table_th_color }};
            }
            {{ addonId }} .table-th:first-child {
                border-radius: {{table_th_border}};
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
            {{ addonId }} .table-th:last-child {
                border-radius: {{table_th_border}};
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
            {{ addonId }} .table-td {
                display: table-cell;
                height: {{ table_td_height }}px;
                <# if(data.table_td_border && recruit_type_selector == "type01") { #>
                border: 1px solid {{table_td_border_color}};
                border-left-width: 0;
                border-right-width: 0;
                <# } #>
                text-align: {{ table_td_align }};
                padding: 0 10px;
                vertical-align: middle;
                font-size: {{ table_td_fontsize }}px;
                color: {{ table_td_color }};
            }
            {{ addonId }} .table-td:first-child {
                <# if(data.table_td_border && recruit_type_selector == "type01") { #>
                border-left-width: 1px;
                <# } #>
            }
            {{ addonId }} .table-td:last-child {
                <# if(data.table_td_border && recruit_type_selector == "type01") { #>
                border-right-width: 1px;
                <# } #>
            }
            {{ addonId }} .table-td.action {
                color: {{ table_td_btnColor }};
            }
            /*布局02*/
            {{addonId}} .table.type02 {
                display: block;
            }
            {{ addonId }} .table.type02 .table-h {
                border-radius: {{table_th_border}};
            }
            {{ addonId }} .table.type02 .table-tr, {{ addonId }} .table.type02 .table-h {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(10px, 4fr));
            } 
            {{ addonId }} .table.type02 .table-tr {
                align-items: center;
                <# if(data.table_td_border) { #> 
                border: 1px solid {{table_td_border_color}};
                <# } #>
                <# if(table_mg == 1) { #>
                margin-top: {{table_border_c}}px;
                <# } #>
            }
            {{ addonId }} .table.type02 .table-tr.actives {
                
            } 
            {{ addonId }} .table.type02 .table-tr.actives .table-td {
                color: {{table_td_color_active}};
            }
            {{ addonId }} .table.type02 .table-th, {{ addonId }} .table.type02 .table-td {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            {{ addonId }} .table.type02 .table-td {
                width: 100%;
                <# if(table_mg == 1) { #>
                margin-right: {{table_border_r}}px;
                <# } #>
            }
            {{ addonId }} .table.type02 .table-td:last-child {
                <# if(table_mg == 1) { #>
                margin-right: 0px;
                <# } #>
            }
            {{ addonId }} .table.type02 .action-icon {
                width: {{action_icon_w}}px;
                height: {{action_icon_h}}px;
                object-fit: contain;
            }
            {{ addonId }} .table.type02 .table-content {
                padding: 20px 30px;
                font-size: 14px;
                <# if(data.table_td_border) { #> 
                border: 1px solid {{table_td_border_color}};
                border-bottom: 3px solid {{table_td_color_active}};
                <# } #>
            }
		</style>
		<div class="table {{recruit_type_selector}}">
		    <# if(table_th == 1) { #>
            <div class="table-h">
                <# if(table_th_01 == 1) { #>
                    <div class="table-th">{{ table_th_title01 }}</div> 
                <# } #> 
                <# if(table_th_05 == 1) { #>
                    <div class="table-th">{{ table_th_title05 }}</div> 
                <# } #> 
                <# if(table_th_06 == 1) { #>
                    <div class="table-th">{{ table_th_title06 }}</div> 
                <# } #> 
                <# if(table_th_02 == 1) { #>
                    <div class="table-th">{{ table_th_title02 }}</div> 
                <# } #> 
                <# if(table_th_03 == 1) { #> 
                    <div class="table-th">{{ table_th_title03 }}</div>  
                <# } #> 
                <# if(table_th_07 == 1) { #> 
                    <div class="table-th">{{ table_th_title07 }}</div>  
                <# } #> 
                <# if(table_th_04 != 1) { #>
                <div class="table-th">{{ table_th_title04 }}</div> 
                <# } #> 
                <# if(table_th_08 == 1 && recruit_type_selector == "type02") { #>
                <div class="table-th">{{ table_th_title08 }}</div> 
                <# } #> 
            </div>  
            <# } #>
            <# _.each(tab_item, function (item, key){ 
                var actives = "";
                var imgSrc = "";
                var imgIcon = data.action_icon || "https://oss.lcweb01.cn/joomla/20220329/3fd0b8508d083a6ad3f029af19561837.png";
                var imgIconActive = data.action_icon_active || "https://oss.lcweb01.cn/joomla/20220329/41f1a566a2a664c2b83d455f53cec8e5.png";
                imgSrc = imgIcon;
                if(key == 0 && recruit_type_selector == "type02") {
                    actives = "actives";
                    imgSrc = imgIconActive;
                }
            #>
                <div class="table-tr {{actives}}">
                    <# if(table_th_01 == 1) { #>  
                        <div class="table-td">{{ item.name }}</div>  
                    <# } #> 
                    <# if(table_th_05 == 1) { #>  
                        <div class="table-td">{{ item.education }}</div>  
                    <# } #> 
                    <# if(table_th_06 == 1) { #>  
                        <div class="table-td">{{ item.experience }}</div>  
                    <# } #> 
                    <# if(table_th_02 == 1) { #>  
                        <div class="table-td">{{ item.num }}</div>  
                    <# } #> 
                    <# if(table_th_03 == 1) { #>  
                        <div class="table-td">{{ item.content }}</div>
                    <# } #>  
                    <# if(table_th_07 == 1) { #>  
                        <div class="table-td">{{ item.treatment }}</div>
                    <# } #> 
                    <# if(table_th_04 != 1) { #>
                    <div class="table-td action">{{ table_td_btn }}</div> 
                    <# } #> 
                    <# if(table_th_08 == 1 && recruit_type_selector == "type02") { #>
                    <div class="table-td action"><img class="action-icon" src=\'{{imgSrc}}\' alt=""></div> 
                    <# } #> 
                </div>
                <# if(recruit_type_selector == "type02" && key == 0) { #>
                <div class="table-content">
                    这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容这里是示例内容
                </div>
                <# } #>
            <# }); #>
        </div>
		';

		return $output;
	}

}
