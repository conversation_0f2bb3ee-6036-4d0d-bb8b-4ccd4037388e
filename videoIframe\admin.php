<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(

    array(
        'type' => 'general', //插件
        'addon_name' => 'videoIframe',
        'title' => '视频外链',
        'desc' => '',
        'category' => '媒体', //插件分组
        'attr' => array(
            //配置项主体
            'general' => array(
                //【基本】选项卡

                // 动态生成的配置项

				'links' => array(
					'type' => 'text',
					'title' => JText::_('链接'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => 'https://v.qq.com/txp/iframe/player.html?vid=s3532nzox2n'
				),

                'start_paly' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('开启自动播放(仅限腾讯视频外链可用)'),
                    'std' => 0
                ),
                'center' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('居中显示'),
                    'desc' => JText::_('居中显示'),
                    'std' => 0
                ),
                'quanp' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('全屏显示'),
                    'desc' => JText::_('全屏显示'),
                    'std' => 1
                ),
                'sp_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置视频宽度'),
                    'std' => array('md' => '1000', 'sm' => '600', 'xs' => '300'),
                    'max' => 2000,
                    'depends' => array(array('quanp', '=', '0')),
                    'responsive' => true
                ),
                'sp_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('设置视频高度'),
                    'std' => array('md' => '550', 'sm' => '550', 'xs' => '550'),
                    'max' => 1000,
                    'responsive' => true
                ),
                

            ),
        ),
    )
);