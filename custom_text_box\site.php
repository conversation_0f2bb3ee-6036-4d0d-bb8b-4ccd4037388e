<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON>ker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonCustom_text_box extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $zcpcatid = $_GET['zcpcatid'] ?? 1000000000000;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;

        $art_type_selector_ctb = (isset($settings->art_type_selector_ctb) && $settings->art_type_selector_ctb) ? $settings->art_type_selector_ctb : 'type1';

        $output = '';
        if ($art_type_selector_ctb == 'type1') {
            $type1_item_icon = (isset($settings->type1_item_icon) && $settings->type1_item_icon) ? $settings->type1_item_icon : 'https://oss.lcweb01.cn/joomla/20210927/bc1ad484e5e249a3bc3d1387047ae4e4.png';
            $type1_item_title = (isset($settings->type1_item_title) && $settings->type1_item_title) ? $settings->type1_item_title : '推广账户搭建';
            $type1_item_text = (isset($settings->type1_item_text) && $settings->type1_item_text) ? $settings->type1_item_text : array(
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                    'title' => '建立账户机构'
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png',
                    'title' => '建立账户机构'
                ),
            );
            $output .= '
                <div class="section-container flex">
                    <div class="img-box">
                        <img src=\'' . $type1_item_icon . '\' alt="">
                    </div>
                    <div class="title">' . $type1_item_title . '</div>
                    <div class="item-box flex">';
                        foreach($type1_item_text as $key => $tab) {
                            $tab->icon ? $tab->icon : '';
                            $img = '';
                            if($tab->icon){
                                $img = '<img src=\'' . $tab->icon . '\' alt="">' ;
                            }
                            $output .= '<p class="item flex">
                                ' . $img . '
                                <span>' . $tab->title . '</span>
                            </p>';
                        }
                    $output .= '</div>
                </div>
            ';
        }
        if ($art_type_selector_ctb == 'type2' || $art_type_selector_ctb == 'type3' || $art_type_selector_ctb == 'type4') {
            $type2_item_text = (isset($settings->type2_item_text) && $settings->type2_item_text) ? $settings->type2_item_text : array(
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                    'icon_width' => 182,
                    'icon_height' => 52,
                    'title' => '渐进式框架',
                    's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                    'icon_width' => 182,
                    'icon_height' => 52,
                    'title' => '渐进式框架',
                    's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                    'icon_width' => 182,
                    'icon_height' => 52,
                    'title' => '渐进式框架',
                    's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png',
                    'icon_width' => 182,
                    'icon_height' => 52,
                    'title' => '渐进式框架',
                    's_title' => '采用业内公认的主流框架作为基础，程序响应速度更快',
                ),
            );
            $type3_item_text = (isset($settings->type3_item_text) && $settings->type3_item_text) ? $settings->type3_item_text : array(
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#8a6eff",
                        "color2" => "#874eff",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                    'icon_bg' => '#6e2df6',
                    'title_shadow' => '0px 7px 18px rgb(138, 110, 255, .35)',
                    'title' => '不断升级的购物体验',
                    's_title' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#8a6eff",
                        "color2" => "#874eff",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                    'icon_bg' => '#6e2df6',
                    'title_shadow' => '0px 7px 18px rgb(138, 110, 255, .35)',
                    'title' => '不断升级的购物体验',
                    's_title' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#8a6eff",
                        "color2" => "#874eff",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                    'icon_bg' => '#6e2df6',
                    'title_shadow' => '0px 7px 18px rgb(138, 110, 255, .35)',
                    'title' => '不断升级的购物体验',
                    's_title' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#8a6eff",
                        "color2" => "#874eff",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png',
                    'icon_bg' => '#6e2df6',
                    'title_shadow' => '0px 7px 18px rgb(138, 110, 255, .35)',
                    'title' => '不断升级的购物体验',
                    's_title' => '从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅',
                ),
            );
            $type4_item_text = (isset($settings->type4_item_text) && $settings->type4_item_text) ? $settings->type4_item_text : array(
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#ff4258",
                        "color2" => "#ff6b84",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                    'title_color' => '#ff4a60',
                    'title_shadow' => '0px 7px 18px rgb(255, 65, 86, .35)',
                    'title' => '会员数据',
                    's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#ff4258",
                        "color2" => "#ff6b84",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                    'title_color' => '#ff4a60',
                    'title_shadow' => '0px 7px 18px rgb(255, 65, 86, .35)',
                    'title' => '会员数据',
                    's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#ff4258",
                        "color2" => "#ff6b84",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                    'title_color' => '#ff4a60',
                    'title_shadow' => '0px 7px 18px rgb(255, 65, 86, .35)',
                    'title' => '会员数据',
                    's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                ),
                (object)array(
                    'background_gradient' => (object)array(
                        "color" => "#ff4258",
                        "color2" => "#ff6b84",
                        "deg" => "-35",
                        "type" => "linear"
                    ),
                    'icon' => 'https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png',
                    'title_color' => '#ff4a60',
                    'title_shadow' => '0px 7px 18px rgb(255, 65, 86, .35)',
                    'title' => '会员数据',
                    's_title' => '挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。',
                ),
            );
            if($art_type_selector_ctb == 'type3') {
                $type2_item_text = $type3_item_text;
            }
            if($art_type_selector_ctb == 'type4') {
                $type2_item_text = $type4_item_text;
            }
            $output .= '<div class="section-container flex">';
            foreach($type2_item_text as $key => $tab) {
                $titleCss = '';
                $iconCss = '';
                $pCss = '';
                if($art_type_selector_ctb == 'type2') {
                    $titleCss .= 'background-image: url(' . $tab->icon . ');';
                    $titleCss .= 'width: ' . $tab->icon_width . 'px;';
                    $titleCss .= 'height: ' . $tab->icon_height . 'px;';
                }
                if($art_type_selector_ctb == 'type3' || $art_type_selector_ctb == 'type4') {
                    if($tab->background_gradient) {
                        $titleCss .= 'background-image: ' . ($tab->background_gradient->type ?: "linear") .'-gradient(';
                        if($tab->background_gradient->type && $tab->background_gradient->type == "radial") {
                            $titleCss .= 'at ' . ($tab->background_gradient->radialPos ?: "center center");
                        } else {
                            $titleCss .= ($tab->background_gradient->deg ?: 0) . 'deg';
                        }
                        $titleCss .= ',
                                        ' . $tab->background_gradient->color . ' ' . ($tab->background_gradient->pos ?: 0) .'%,';
                        $titleCss .= $tab->background_gradient->color2 . ' ' . ($tab->background_gradient->pos2 ?: 100) .'%);';
                    }
                    if ($tab->title_shadow) {
                        if (is_object($tab->title_shadow)) {
                            $ho = (isset($tab->title_shadow->ho) && $tab->title_shadow->ho != '') ? $tab->title_shadow->ho . 'px' : '0px';
                            $vo = (isset($tab->title_shadow->vo) && $tab->title_shadow->vo != '') ? $tab->title_shadow->vo . 'px' : '0px';
                            $blur = (isset($tab->title_shadow->blur) && $tab->title_shadow->blur != '') ? $tab->title_shadow->blur . 'px' : '0px';
                            $spread = (isset($tab->title_shadow->spread) && $tab->title_shadow->spread != '') ? $tab->title_shadow->spread . 'px' : '0px';
                            $color = (isset($tab->title_shadow->color) && $tab->title_shadow->color != '') ? $tab->title_shadow->color : '#fff';

                            $titleCss .= "box-shadow: ${ho} ${vo} ${blur} ${spread} ${color};";
                        } else {
                            $titleCss .= "box-shadow: " . $tab->title_shadow . ";";
                        }
                    }
                    $iconCss .= 'background: ' . $tab->icon_bg . ';';
                    if($art_type_selector_ctb == 'type4') {
                        $iconCss = $titleCss;
                        $titleCss = '';
                        $pCss = 'color: ' . $tab->title_color . ';';
                    }
                }
                $output .= '<div class="item flex">
                    <h3 class="title flex" style="' . $titleCss . '">';
                        if($art_type_selector_ctb == 'type3' || $art_type_selector_ctb == 'type4') {
                            $output .= '<span style="' . $iconCss . '">
                                <img src=\'' . $tab->icon . '\' alt="">
                            </span>';
                        }
                        $output .= '<p style="' . $pCss . '">' . $tab->title . '</p>
                    </h3>
                    <p class="s-title">' . $tab->s_title . '</p>
                </div>';
            }
            $output .= '</div>';
        }
        if ($settings->art_type_selector_ctb == 'type5') {
            $type5_item_text = (isset($settings->type5_item_text) && $settings->type5_item_text) ? $settings->type5_item_text : array(
                (Object)array(
                    'title' => '爱好者生态',
                    'left' => 186,
                    'top' => 100
                ),
                (Object)array(
                    'title' => '明星生态',
                    'left' => 838,
                    'top' => 74
                ),
                (Object)array(
                    'title' => '场馆生态',
                    'left' => 130,
                    'top' => 338
                ),
                (Object)array(
                    'title' => '从业者生态',
                    'left' => 914,
                    'top' => 320
                ),
                (Object)array(
                    'title' => '厂商生态',
                    'left' => 538,
                    'top' => 518
                ),
            );
            $output .= '<div class="section-container flex">';
            foreach($type5_item_text as $key => $tab) {
                $titleCss = 'left: ' . $tab->left . 'px;';
                $titleCss .= 'top: ' . $tab->top . 'px;';
                $output .= '<div class="item" style="' . $titleCss . '">' . $tab->title . '</div>';
            }
            $output .= '</div>';
        }
        if ($art_type_selector_ctb == 'type6') {
            $type6_item_text = (isset($settings->type6_item_text) && $settings->type6_item_text) ? $settings->type6_item_text : array(
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20220523/1423e5b9643c3cb0117dfd9da4ec80e8.png',
                    'title' => '支持视频、音频、word、excel、ppt、<br> pdf等各种格式的资源播放',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20220523/3ca093e8022a06cb5b93315f1472d955.png',
                    'title' => '支持章节目录、模拟考试、课后练习 <br> 课后作业、章节测验、批改作业',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20220523/f3d710c224ae4c26e5aac4f1f5107fce.png',
                    'title' => '课程评论、课件资料下载，重点内容收藏',
                ),
                (object)array(
                    'icon' => 'https://oss.lcweb01.cn/joomla/20220523/ff792e3d204153541cdec7f088f28e39.png',
                    'title' => '窗口聊天、试听购买、微信分享、 <br> 学习进度跟踪、数据统计',
                ),
            );
            $output .= '
                <div class="web_fz">
                    <img src="https://oss.lcweb01.cn/joomla/20220523/dfd5e47c602106503295e85eea4185fb.png">
                    <div class="web_fz_list">
                        <ul>';
                        foreach($type6_item_text as $key => $tab) {
                            
                            $output .= '
                                <li>
                                    <div class="web_fz_p">
                                    ' . $tab->title . '
                                    </div>
                                </li>
                            ';
                        }

                        $output .= '</ul>
                    </div>
                </div>

                <ul class="part-sevev">';

                    foreach($type6_item_text as $key => $tab) {
                        $tab->icon ? $tab->icon : "";
                        $output .= '<li class="part-sevev-item">
                                        <i class="part-sevev-item-img"><img src="'.$tab->icon.'" width="100%"></i>
                                        <div class="part-sevev-item-txt">
                                        ' . $tab->title . '
                                        </div>
                                    </li>';
                    }
                    $output .= '</ul>
            ';
 
        }
        return $output;
    }

    public function css()
    {
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $art_type_selector_ctb = (isset($settings->art_type_selector_ctb) && $settings->art_type_selector_ctb) ? $settings->art_type_selector_ctb : 'type1';
        $css = '';
        $css .= $addon_id . ' * {
            margin: 0;
            padding: 0;
        }
        ' . $addon_id . ' .flex {
            display: flex;
            align-items: center;
        }';
        if ($art_type_selector_ctb == 'type1') {
            $type1_title_fontsize = (isset($settings->type1_title_fontsize) && $settings->type1_title_fontsize) ? $settings->type1_title_fontsize : '24';
            $type1_title_margin = (isset($settings->type1_title_margin) && $settings->type1_title_margin) ? $settings->type1_title_margin : '30px 0px 15px 0px';
            $type1_title_color = (isset($settings->type1_title_color) && $settings->type1_title_color) ? $settings->type1_title_color : '#454545';
            $type1_item_fontsize = (isset($settings->type1_item_fontsize) && $settings->type1_item_fontsize) ? $settings->type1_item_fontsize : '16';
            $type1_item_lineHeight = (isset($settings->type1_item_lineHeight) && $settings->type1_item_lineHeight) ? $settings->type1_item_lineHeight : '44';
            $type1_item_color = (isset($settings->type1_item_color) && $settings->type1_item_color) ? $settings->type1_item_color : '#454545';
            $css .= $addon_id . ' .section-container {
                flex-direction: column;
                justify-content: center;
            }
            ' . $addon_id . ' .section-container .title {
                font-size: ' . $type1_title_fontsize . 'px;
                margin: ' . $type1_title_margin . ';
                color: ' . $type1_title_color . ';
                font-weight: bold;
            }
            ' . $addon_id . ' .section-container .item-box {
                flex-wrap: wrap;
            }
            ' . $addon_id . ' .section-container .item-box .item {
                width: calc(100% / 2 - 10px);
                margin-right: 20px;
                font-size: ' . $type1_item_fontsize . 'px;
                line-height: ' . $type1_item_lineHeight . 'px;
                color: ' . $type1_item_color . ';
            }
            ' . $addon_id . ' .section-container .item-box .item:nth-child(2n) {
                margin-right: 0;
            }
            ' . $addon_id . ' .section-container .item-box .item img {
                margin-right: 10px;
            }
            ';
        }
        if($art_type_selector_ctb == 'type2' || $art_type_selector_ctb == 'type3' || $art_type_selector_ctb == 'type4') {
            $type2_img = (isset($settings->type2_img) && $settings->type2_img) ? $settings->type2_img : 'https://oss.lcweb01.cn/joomla/20210927/f5da9024182f6c12c9c004be5bb1c294.png';
            $type2_img_width = (isset($settings->type2_img_width) && $settings->type2_img_width) ? $settings->type2_img_width : '50';
            $type2_container_height = (isset($settings->type2_container_height) && $settings->type2_container_height) ? $settings->type2_container_height : '538';
            $type2_item_width = (isset($settings->type2_item_width) && $settings->type2_item_width) ? $settings->type2_item_width : '40';
            $type2_title_fontsize = (isset($settings->type2_title_fontsize) && $settings->type2_title_fontsize) ? $settings->type2_title_fontsize : '20';
            $type2_title_lineHeight = (isset($settings->type2_title_lineHeight) && $settings->type2_title_lineHeight) ? $settings->type2_title_lineHeight : '42';
            $type2_title_color = (isset($settings->type2_title_color) && $settings->type2_title_color) ? $settings->type2_title_color : '#fff';
            $type2_sTitle_fontsize = (isset($settings->type2_sTitle_fontsize) && $settings->type2_sTitle_fontsize) ? $settings->type2_sTitle_fontsize : '14';
            $type2_sTitle_lineHeight = (isset($settings->type2_sTitle_lineHeight) && $settings->type2_sTitle_lineHeight) ? $settings->type2_sTitle_lineHeight : '22';
            $type2_sTitle_color = (isset($settings->type2_sTitle_color) && $settings->type2_sTitle_color) ? $settings->type2_sTitle_color : '#454545';
            $css .= $addon_id . ' .section-container {
                flex-wrap: wrap;
                justify-content: space-between;
                height: ' . $type2_container_height . 'px;
                background: url("' . $type2_img . '") no-repeat center;
                background-size: ' . $type2_img_width . '%;
            }
            ' . $addon_id . ' .section-container .item {';
                $css .= 'width: calc(' . $type2_item_width . '%);
                margin-right: ' . (50 -$type2_item_width) . '%;';
                $css .= 'flex-direction: column;
                align-items: flex-start;
            }
            ' . $addon_id . ' .section-container .item:nth-child(2n) {';
                if($art_type_selector_ctb == 'type2') {
                    $css .= 'text-align: right;
                    justify-content: flex-end;
                    align-items: flex-end;';
                }
                $css .= 'margin-right: 0;
                margin-left: ' . (50 -$type2_item_width) . '%;';
            $css .= '}
            ' . $addon_id . ' .section-container .item .title {';
                if($art_type_selector_ctb == 'type2') {
                    $css .= 'width: 182px;
                    height: 52px;
                    background: url("https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png") no-repeat;
                    display: inline-block;';
                }
                if($art_type_selector_ctb == 'type3') {
                    $css .= 'padding: 0 22px 0 0;
                    border-radius: ' . ($type2_title_lineHeight / 2) . 'px;
                    height: ' . $type2_title_lineHeight . 'px;';
                }
                if($art_type_selector_ctb == 'type4') {
                    $css .= 'flex-direction: column;
                    align-items: flex-start;';
                }
                $css .= 'text-align: center;
                line-height: ' . $type2_title_lineHeight . 'px;
                color: ' . $type2_title_color . ';
                font-size: ' . $type2_title_fontsize . 'px;
                font-weight: 400;
            }
            ' . $addon_id . ' .section-container .item .s-title {
                font-size: ' . $type2_sTitle_fontsize . 'px;
                line-height: ' . $type2_sTitle_lineHeight . 'px;
                color: ' . $type2_sTitle_color . ';
                margin-top: 15px;';
            $css .= '}';
            if($art_type_selector_ctb == 'type3') {
                $css .= $addon_id . ' .section-container .item .title span {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    margin-right: 10px; 
                }';
            }
            if($art_type_selector_ctb == 'type4') {
                $css .= $addon_id . ' .section-container .item .title span {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    margin-bottom: 28px;
                }';
            }
        }
        if($art_type_selector_ctb == 'type5') {
            $type5_container_height = (isset($settings->type5_container_height) && $settings->type5_container_height) ? $settings->type5_container_height : '600';
            $type5_item_icon = (isset($settings->type5_item_icon) && $settings->type5_item_icon) ? $settings->type5_item_icon : 'https://oss.lcweb01.cn/joomla/20210928/0fe675cbdf6a78537c0a6ae3d7186827.png';
            $type5_img_width = (isset($settings->type5_img_width) && $settings->type5_img_width) ? $settings->type5_img_width : '60';
            $type5_title_fontsize = (isset($settings->type5_title_fontsize) && $settings->type5_title_fontsize) ? $settings->type5_title_fontsize : '18';
            $type5_title_lineHeight = (isset($settings->type5_title_lineHeight) && $settings->type5_title_lineHeight) ? $settings->type5_title_lineHeight : '42';
            $type5_title_color = (isset($settings->type5_title_color) && $settings->type5_title_color) ? $settings->type5_title_color : '#2a66e4';
            $type5_title_border_width = (isset($settings->type5_title_border_width) && $settings->type5_title_border_width) ? $settings->type5_title_border_width : '1';
            $type5_title_border_color = (isset($settings->type5_title_border_color) && $settings->type5_title_border_color) ? $settings->type5_title_border_color : '#2a66e4';
            $type5_title_border_style = (isset($settings->type5_title_border_style) && $settings->type5_title_border_style) ? $settings->type5_title_border_style : 'solid';
            $type5_title_border_radius = (isset($settings->type5_title_border_radius) && $settings->type5_title_border_radius) ? $settings->type5_title_border_radius : '40';
            $css .= $addon_id . ' .section-container {
                height: ' . $type5_container_height . 'px;
                background: url("' . $type5_item_icon . '") no-repeat center;
                background-size: ' . $type5_img_width . '%;
                position: relative;
            }
            ' . $addon_id . ' .section-container .item {
                position: absolute;
                color: ' . $type5_title_color . ';
                font-size: ' . $type5_title_fontsize . 'px;
                line-height: ' . $type5_title_lineHeight . 'px;
                padding: 0 20px;
                border: ' . $type5_title_border_style . ' ' . $type5_title_border_color . ' ' . $type5_title_border_width . 'px;
                border-radius: ' . $type5_title_border_radius . 'px;
            }';
        }
        if($art_type_selector_ctb == 'type6') {
            $type6_bgcolor = (isset($settings->type6_bgcolor) && $settings->type6_bgcolor) ? $settings->type6_bgcolor : '#195bff';
            $type2_title_fontsize = (isset($settings->type2_title_fontsize) && $settings->type2_title_fontsize) ? $settings->type2_title_fontsize : '20';
            $type2_title_lineHeight = (isset($settings->type2_title_lineHeight) && $settings->type2_title_lineHeight) ? $settings->type2_title_lineHeight : '42';
            $type2_title_color = (isset($settings->type2_title_color) && $settings->type2_title_color) ? $settings->type2_title_color : '#fff';

            $css .= $addon_id . ' .web_fz {
                width: 1400px;
                margin: 0 auto;
                position: relative;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(1) {
                left: 107px;
                top: 159px;
            }
            '.$addon_id . ' .web_fz_list ul li {
                position: absolute;
                width: 400px;
                padding-left: 30px;
                height: 92px;
                display: flex;
                align-items: center;
                cursor: pointer;
                border-radius: 46px 0 0 46px;
                transition: all .36s;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(2) {
                right: 109px;
                top: 159px;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(2n) {
                padding-left: 0;
                padding-right: 30px;
                justify-content: flex-end;
                text-align: right;
                border-radius: 0 46px 46px 0;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(3) {
                left: 107px;
                bottom: 151px;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(4) {
                right: 109px;
                bottom: 151px;
            }
        
            '.$addon_id . ' .web_fz_list ul li:hover::before {
                width: 150%;
            }
            '.$addon_id . ' .web_fz_list ul li::before {
                position: absolute;
                content: "";
                width: 0;
                left: 0;
                top: 0;
                height: 100%;
                border-radius: 46px;
                background: linear-gradient(to right,'.$type6_bgcolor.' 32%,rgba(25,91,255,0) 78%,rgba(255,255,255,0));
                transition: all .36s;
            }
            '.$addon_id . ' .web_fz_list ul li:hover .web_fz_p {
                color: #fff;
            }
            '.$addon_id . ' .web_fz_p {
                color: '.$type2_title_color.';
                font-size: '.$type2_title_fontsize.'px;
                line-height: '.$type2_title_lineHeight.'px;
                font-weight: bold;
                transition: all .36s;
                position: relative;
            }
            '.$addon_id . ' .web_fz_list ul li:nth-child(2n)::before {
                left: auto;
                right: 0;
                background: linear-gradient(to left,'.$type6_bgcolor.' 32%,rgba(25,91,255,0) 78%,rgba(255,255,255,0));
            }
            '.$addon_id . ' .part-sevev{display: none;}
        
            @media screen and (max-width: 1440px) {
                '.$addon_id . ' .web_fz {
                    width: 1300px;
                }
                '.$addon_id . ' .web_fz_list ul li {
                    height: 82px;
                }
                '.$addon_id . ' .web_fz_list ul li:nth-child(1),
                '.$addon_id . ' .web_fz_list ul li:nth-child(2) {
                    top: 144px;
                }
                '.$addon_id . ' .web_fz_list ul li:nth-child(3),
                '.$addon_id . ' .web_fz_list ul li:nth-child(4){
                    bottom: 140px;
                }
        
            }
            /*手机*/
            @media only screen and (max-width: 991px) {
                '.$addon_id . ' .web_fz{display: none;}
                '.$addon_id . ' .part-sevev{
                    display: block;
                    padding: 0 22.8px 75.2px;
                }
                '.$addon_id . ' .part-sevev-item{
                    margin-top: 18.8px;
                    display: flex;
                    margin-bottom: 18.8px;
                    justify-content: center;
                }
                '.$addon_id . ' .part-sevev-item-img{
                    width: 110.92px;
                    flex-shrink: 0;
                }
                '.$addon_id . ' .part-sevev-item-txt{
                    width: 570px;
                    height: 115px;
                    box-shadow: 4px 2px 4px 2px #f5f6f7;
                    border-radius: 5px;
                    position: relative;
                    top: 7px;
                    left: -9px;
                    background: #fff;
                    padding: 0 34px;
                    display: flex;
                    align-items: center;
                }
            }
            @media only screen and (max-width: 540px) {
                '.$addon_id . ' .part-sevev{
                    padding: 0 13px 44px;
                }
                '.$addon_id . ' .part-sevev-item{
                    margin-top: 11px;
                    display: flex;
                    margin-bottom: 11px;
                }
                '.$addon_id . ' .part-sevev-item-img{
                    width: 65px;
                    flex-shrink: 0;
                }
                '.$addon_id . ' .part-sevev-item-txt{
                    width: 333px;
                    height: 67.65px;
                    box-shadow: 4px 2px 4px 2px #f5f6f7;
                    border-radius: 5px;
                    position: relative;
                    top: 7px;
                    left: -9px;
                    background: #fff;
                    padding: 0 20px;
                    display: flex;
                    align-items: center;
                }
            }
            
            ';
        }
        return $css;
    }

    public static function getTemplate(){
        $output = '
            <#
                var addonId = "#jwpf-addon-" + data.id;
                var art_type_selector_ctb = data.art_type_selector_ctb || "type1";
            #>
            <style>
                {{ addonId }} * {
                    margin: 0;
                    padding: 0;
                }
                {{ addonId }} .flex {
                    display: flex;
                    align-items: center;
                }
            </style>
            <# if(art_type_selector_ctb == "type1") { 
                var type1_item_icon = data.type1_item_icon || "https://oss.lcweb01.cn/joomla/20210927/bc1ad484e5e249a3bc3d1387047ae4e4.png";
                var type1_item_title = data.type1_item_title || "推广账户搭建";
                var type1_item_text = data.type1_item_text || [{
                    icon: "https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png", title: "建立账户机构"},
                {icon: "https://oss.lcweb01.cn/joomla/20210927/65837878dffa49eb841146c6c360a814.png", title: "建立账户机构"}];
                var type1_title_fontsize = data.type1_title_fontsize || "24";
                var type1_title_margin = data.type1_title_margin || "30px 0px 15px 0px";
                var type1_title_color = data.type1_title_color || "#454545";
                var type1_item_fontsize = data.type1_item_fontsize || "16";
                var type1_item_lineHeight = data.type1_item_lineHeight || "44";
                var type1_item_color = data.type1_item_color || "#454545";
            #>
                <style>
                    {{ addonId }} .section-container {
                        flex-direction: column;
                        justify-content: center;
                    }
                    {{ addonId }} .section-container .title {
                        font-size: {{ type1_title_fontsize }}px;
                        margin: {{ type1_title_margin }};
                        color: {{ type1_title_color }};
                        font-weight: bold;
                    }
                    {{ addonId }} .section-container .item-box {
                        flex-wrap: wrap;
                    }
                    {{ addonId }} .section-container .item-box .item {
                        width: calc(100% / 2 - 10px);
                        margin-right: 20px;
                        font-size: {{ type1_item_fontsize }}px;
                        line-height: {{ type1_item_lineHeight }}px;
                        color: {{ type1_item_color }};
                    }
                    {{ addonId }} .section-container .item-box .item:nth-child(2n) {
                        margin-right: 0;
                    }
                    {{ addonId }} .section-container .item-box .item img {
                        margin-right: 10px;
                    }
                </style>
                <div class="section-container flex">
                    <div class="img-box">
                        <img src=\'{{ type1_item_icon }}\' alt="">
                    </div>
                    <div class="title">{{ type1_item_title }}</div>
                    <div class="item-box flex">
                        <# _.each(type1_item_text, function(tab, key){ #>
                        <p class="item flex">
                            <img src=\'{{ tab.icon }}\' alt="">
                            <span>{{ tab.title }}</span>
                        </p>
                        <# }); #>
                    </div>
                </div>
            <# } #>
            <# if(art_type_selector_ctb == "type2" || art_type_selector_ctb == "type3" || art_type_selector_ctb == "type4") { 
                var type2_img = data.type2_img || "https://oss.lcweb01.cn/joomla/20210927/f5da9024182f6c12c9c004be5bb1c294.png";
                var type2_img_width = data.type2_img_width || "50";
                var type2_item_text = data.type2_item_text || [{
                        icon: "https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png",
                        icon_width: 182,
                        icon_height: 52,
                        title: "渐进式框架",
                        s_title: "采用业内公认的主流框架作为基础，程序响应速度更快"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png",
                        icon_width: 182,
                        icon_height: 52,
                        title: "渐进式框架",
                        s_title: "采用业内公认的主流框架作为基础，程序响应速度更快"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png",
                        icon_width: 182,
                        icon_height: 52,
                        title: "渐进式框架",
                        s_title: "采用业内公认的主流框架作为基础，程序响应速度更快"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png",
                        icon_width: 182,
                        icon_height: 52,
                        title: "渐进式框架",
                        s_title: "采用业内公认的主流框架作为基础，程序响应速度更快"
                    }];
                var type2_container_height = data.type2_container_height || "538";
                var type2_item_width = data.type2_item_width || "40";
                var type2_title_fontsize = data.type2_title_fontsize || "20";
                var type2_title_lineHeight = data.type2_title_lineHeight || "42";
                var type2_title_color = data.type2_title_color || "#fff";
                var type2_sTitle_fontsize = data.type2_sTitle_fontsize || "14";
                var type2_sTitle_lineHeight = data.type2_sTitle_lineHeight || "22";
                var type2_sTitle_color = data.type2_sTitle_color || "#454545";
                
                // type3
                var type3_item_text = data.type3_item_text || [{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png",
                        icon_bg: "#6e2df6",
                        background_gradient: {
                           color: "#8a6eff",
                           color2: "#874eff",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(138, 110, 255, .35)",
                        title: "不断升级的购物体验",
                        s_title: "从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png",
                        icon_bg: "#6e2df6",
                        background_gradient: {
                           color: "#8a6eff",
                           color2: "#874eff",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(138, 110, 255, .35)",
                        title: "不断升级的购物体验",
                        s_title: "从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png",
                        icon_bg: "#6e2df6",
                        background_gradient: {
                           color: "#8a6eff",
                           color2: "#874eff",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(138, 110, 255, .35)",
                        title: "不断升级的购物体验",
                        s_title: "从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/ae19882a5f316342792f38dcda4699eb.png",
                        icon_bg: "#6e2df6",
                        background_gradient: {
                           color: "#8a6eff",
                           color2: "#874eff",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(138, 110, 255, .35)",
                        title: "不断升级的购物体验",
                        s_title: "从客户体验出发，让你的社区团购平台体验更好经过多次打磨，界面简洁大气，更受客户喜爱操作流程简单，学习和使用难度低，更方便上手系统性能稳定，不惧订单爆发，下单更顺畅"
                    }];
                // type4
                var type4_item_text = data.type4_item_text || [{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png",
                        title_color: "#ff4a60",
                        background_gradient: {
                           color: "#ff4258",
                           color2: "#ff6b84",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(255, 65, 86, .35)",
                        title: "会员数据",
                        s_title: "挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png",
                        title_color: "#ff4a60",
                        background_gradient: {
                           color: "#ff4258",
                           color2: "#ff6b84",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(255, 65, 86, .35)",
                        title: "会员数据",
                        s_title: "挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png",
                        title_color: "#ff4a60",
                        background_gradient: {
                           color: "#ff4258",
                           color2: "#ff6b84",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(255, 65, 86, .35)",
                        title: "会员数据",
                        s_title: "挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。"
                    },{
                        icon: "https://oss.lcweb01.cn/joomla/20210928/799a296f081eb3dd38653aa9fd67ced2.png",
                        title_color: "#ff4a60",
                        background_gradient: {
                           color: "#ff4258",
                           color2: "#ff6b84",
                           deg: "-35",
                           type: "linear",
                        },
                        title_shadow: "0px 7px 18px rgb(255, 65, 86, .35)",
                        title: "会员数据",
                        s_title: "挖掘会员浏览轨迹、购物频次等数据，建立会员复购率、忠诚度、流失率等机制，实现精准营销，实现黏性客户积累。"
                    }];
                if(art_type_selector_ctb == "type3") {
                    type2_item_text = type3_item_text;
                }
                if(art_type_selector_ctb == "type4") {
                    type2_item_text = type4_item_text;
                }
            #>
                <style>
                    {{ addonId }} .section-container {
                        flex-wrap: wrap;
                        justify-content: space-between;
                        height: {{ type2_container_height }}px;
                        background: url("{{ type2_img }}") no-repeat center;
                        background-size: {{ type2_img_width }}%;
                    }
                    {{ addonId }} .section-container .item {
                        width: calc({{ type2_item_width }}%);
                        margin-right: {{ 50 - type2_item_width }}%;
                        flex-direction: column;
                        align-items: flex-start;
                    }
                    {{ addonId }} .section-container .item:nth-child(2n) {
                        <# if(art_type_selector_ctb == "type2") { #>
                            text-align: right;
                            justify-content: flex-end;
                            align-items: flex-end;
                        <# } #>
                        margin-right: 0;
                        margin-left: {{ 50 - type2_item_width }}%;
                    }
                    {{ addonId }} .section-container .item .title {
                        <# if(art_type_selector_ctb == "type2") { #>
                            width: 182px;
                            height: 52px;
                            background: url("https://oss.lcweb01.cn/joomla/20210927/a087927d24b159ea9c6670f67b97a96a.png") no-repeat;
                            display: inline-block;
                        <# } #>
                        <# if(art_type_selector_ctb == "type3") { #>
                            padding: 0 22px 0 0;
                            border-radius: {{ type2_title_lineHeight / 2 }}px;
                            height: {{ type2_title_lineHeight }}px;
                        <# } #>
                        <# if(art_type_selector_ctb == "type4") { #>
                            flex-direction: column;
                            align-items: flex-start;
                        <# } #>
                        text-align: center;
                        line-height: {{ type2_title_lineHeight }}px;
                        color: {{ type2_title_color }};
                        font-size: {{ type2_title_fontsize }}px;
                        font-weight: 400;
                    }
                    {{ addonId }} .section-container .item .s-title {
                        font-size: {{ type2_sTitle_fontsize }}px;
                        line-height: {{ type2_sTitle_lineHeight }}px;
                        color: {{ type2_sTitle_color }};
                        margin-top: 15px;
                    }
                    <# if(art_type_selector_ctb == "type3") { #>
                        {{ addonId }} .section-container .item .title span {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            margin-right: 10px;
                        }
                    <# } #>
                    <# if(art_type_selector_ctb == "type4") { #>
                        {{ addonId }} .section-container .item .title span {
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            margin-bottom: 28px;
                        }
                    <# } #>
                </style>
                <div class="section-container flex">
                    <# _.each(type2_item_text, function(tab, key){
                        if(art_type_selector_ctb == "type2"){
                            var titleCss = "background-image: url(" + tab.icon + ");";
                            titleCss += "width: " + tab.icon_width + "px;";
                            titleCss += "height: " + tab.icon_height + "px;";
                        }
                        if(art_type_selector_ctb == "type3" || art_type_selector_ctb == "type4"){
                            var titleCss = "";
                            if(_.isObject(tab.background_gradient)) {
                                titleCss += "background-image: " + (tab.background_gradient.type || "linear") + "-gradient(";
                                if(tab.background_gradient.type && tab.background_gradient.type == "radial") {
                                    titleCss += "at " + (tab.background_gradient.radialPos || "center center");
                                } else {
                                    titleCss += (tab.background_gradient.deg || 0) + "deg";
                                }
                                titleCss += "," + tab.background_gradient.color + " " + (tab.background_gradient.pos || 0) + "%,";
                                titleCss += tab.background_gradient.color2 + " " + (tab.background_gradient.pos2 || 100) + "%);";
                            }
                            var boxShadow = "";
                            if(_.isObject(tab.title_shadow)){
                                let ho = tab.title_shadow.ho || 0,
                                    vo = tab.title_shadow.vo || 0,
                                    blur = tab.title_shadow.blur || 0,
                                    spread = tab.title_shadow.spread || 0,
                                    color = tab.title_shadow.color || 0;
                                boxShadow = ho + "px " + vo+"px " + blur + "px " + spread + "px " + color;
                            } else {
                                boxShadow = tab.title_shadow || "";
                            }
                            titleCss += "box-shadow: " + boxShadow + ";";
                            var iconCss = "background: " + tab.icon_bg + ";";
                            if(art_type_selector_ctb == "type4") {
                                iconCss = titleCss;
                                titleCss = "";
                                var pCss = "color: " + tab.title_color + ";";
                            }
                        }
                    #>
                        <div class="item flex">
                            <h3 class="title flex" style="{{ titleCss }}">
                                <# if(art_type_selector_ctb == "type3" || art_type_selector_ctb == "type4") { #>
                                    <span style="{{ iconCss }}">
                                        <img src=\'{{ tab.icon }}\' alt="">
                                    </span>
                                <# } #>
                                <p style="{{ pCss }}">{{ tab.title }}</p>
                            </h3>
                            <p class="s-title">{{ tab.s_title }}</p>
                        </div>
                    <# }); #>
                </div>
            <# } #>
            <# if(art_type_selector_ctb == "type5") {
                var type5_container_height = data.type5_container_height || "600";
                var type5_item_icon = data.type5_item_icon || "https://oss.lcweb01.cn/joomla/20210928/0fe675cbdf6a78537c0a6ae3d7186827.png";
                var type5_img_width = data.type5_img_width || 60;
                var type5_item_text = data.type5_item_text || [{
                    title: "爱好者生态",
                    left: 186,
                    top: 100
                },{
                    title: "明星生态",
                    left: 838,
                    top: 74
                },{
                    title: "场馆生态",
                    left: 130,
                    top: 338
                },{
                    title: "从业者生态",
                    left: 914,
                    top: 320
                },{
                    title: "厂商生态",
                    left: 538,
                    top: 518
                }];
                var type5_title_fontsize = data.type5_title_fontsize || "18";
                var type5_title_lineHeight = data.type5_title_lineHeight || "42";
                var type5_title_color = data.type5_title_color || "#2a66e4";
                var type5_title_border_width = data.type5_title_border_width || 1;
                var type5_title_border_color = data.type5_title_border_color || "#2a66e4";
                var type5_title_border_style = data.type5_title_border_style || "solid";
                var type5_title_border_radius = data.type5_title_border_radius || 40;
            #>
                <style>
                    {{ addonId }} .section-container {
                        height: {{ type5_container_height }}px;
                        background: url("{{ type5_item_icon }}") no-repeat center;
                        background-size: {{ type5_img_width }}%;
                        position: relative;
                    }
                    {{ addonId }} .section-container .item {
                        position: absolute;
                        color: {{ type5_title_color }};
                        font-size: {{ type5_title_fontsize }}px;
                        line-height: {{ type5_title_lineHeight }}px;
                        padding: 0 20px;
                        border: {{ type5_title_border_style }} {{ type5_title_border_color }} {{ type5_title_border_width }}px;
                        border-radius: {{ type5_title_border_radius }}px;
                    }
                </style>
                <div class="section-container">
                    <# _.each(type5_item_text, function(tab, key){
                        var titleCss = "left: " + tab.left + "px;";
                        titleCss += "top: " + tab.top + "px;";
                    #>
                        <div class="item" style="{{ titleCss }}">
                            {{ tab.title }}
                        </div>
                    <# }); #>
                </div>
            <# } #>
            <# if(art_type_selector_ctb == "type6") { 
                
                var type6_bgcolor = data.type6_bgcolor || "#195bff";
                var type2_title_fontsize = data.type2_title_fontsize || "20";
                var type2_title_lineHeight = data.type2_title_lineHeight || "42";
                var type2_title_color = data.type2_title_color || "#fff";
                var type6_item_text = data.type6_item_text || [
                    
                    {icon: "https://oss.lcweb01.cn/joomla/20220523/1423e5b9643c3cb0117dfd9da4ec80e8.png", title: "支持视频、音频、word、excel、ppt、<br> pdf等各种格式的资源播放"},
                    {icon: "https://oss.lcweb01.cn/joomla/20220523/3ca093e8022a06cb5b93315f1472d955.png", title: "支持视频、音频、word、excel、ppt、<br> pdf等各种格式的资源播放"},
                    {icon: "https://oss.lcweb01.cn/joomla/20220523/f3d710c224ae4c26e5aac4f1f5107fce.png", title: "课程评论、课件资料下载，重点内容收藏"},
                    {icon: "https://oss.lcweb01.cn/joomla/20220523/ff792e3d204153541cdec7f088f28e39.png", title: "窗口聊天、试听购买、微信分享、 <br> 学习进度跟踪、数据统计"},
                    
                    ];
            #>
                <style>
                    
                    {{ addonId }} .web_fz {
                        max-width: 1400px;
                        margin: 0 auto;
                        position: relative;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(1) {
                        left: 107px;
                        top: 159px;
                    }
                    {{ addonId }} .web_fz_list ul li {
                        position: absolute;
                        width: 400px;
                        padding-left: 30px;
                        height: 92px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        border-radius: 46px 0 0 46px;
                        transition: all .36s;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(2) {
                        right: 109px;
                        top: 159px;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(2n) {
                        padding-left: 0;
                        padding-right: 30px;
                        justify-content: flex-end;
                        text-align: right;
                        border-radius: 0 46px 46px 0;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(3) {
                        left: 107px;
                        bottom: 151px;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(4) {
                        right: 109px;
                        bottom: 151px;
                    }
                
                    {{ addonId }} .web_fz_list ul li:hover::before {
                        width: 150%;
                    }
                    {{ addonId }} .web_fz_list ul li::before {
                        position: absolute;
                        content: "";
                        width: 0;
                        left: 0;
                        top: 0;
                        height: 100%;
                        border-radius: 46px;
                        background: linear-gradient(to right,{{ type6_bgcolor }} 32%,rgba(25,91,255,0) 78%,rgba(255,255,255,0));
                        transition: all .36s;
                    }
                    {{ addonId }} .web_fz_list ul li:hover .web_fz_p {
                        color: #fff;
                    }
                    {{ addonId }} .web_fz_p {
                        color: {{type2_title_color}};
                        font-size: {{type2_title_fontsize}}px;
                        line-height: {{type2_title_lineHeight}}px;
                        font-weight: bold;
                        transition: all .36s;
                        position: relative;
                    }
                    {{ addonId }} .web_fz_list ul li:nth-child(2n)::before {
                        left: auto;
                        right: 0;
                        background: linear-gradient(to left,{{ type6_bgcolor }} 32%,rgba(25,91,255,0) 78%,rgba(255,255,255,0));
                    }
                    {{ addonId }} .part-sevev{display: none;}
                
                    @media screen and (max-width: 1440px) {
                        {{ addonId }} .web_fz {
                            width: 1300px;
                        }
                        {{ addonId }} .web_fz_list ul li {
                            height: 82px;
                        }
                        {{ addonId }} .web_fz_list ul li:nth-child(1),
                        {{ addonId }} .web_fz_list ul li:nth-child(2) {
                            top: 144px;
                        }
                        {{ addonId }} .web_fz_list ul li:nth-child(3),
                        {{ addonId }} .web_fz_list ul li:nth-child(4){
                            bottom: 140px;
                        }
                
                    }
                    /*手机*/
                    @media only screen and (max-width: 991px) {
                        {{ addonId }} .web_fz{display: none;}
                        {{ addonId }} .part-sevev{
                            display: block;
                            padding: 0 22.8px 75.2px;
                        }
                        {{ addonId }} .part-sevev-item{
                            margin-top: 18.8px;
                            display: flex;
                            margin-bottom: 18.8px;
                            justify-content: center;
                        }
                        {{ addonId }} .part-sevev-item-img{
                            width: 110.92px;
                            flex-shrink: 0;
                        }
                        {{ addonId }} .part-sevev-item-txt{
                            width: 570px;
                            height: 115px;
                            box-shadow: 4px 2px 4px 2px #f5f6f7;
                            border-radius: 5px;
                            position: relative;
                            top: 7px;
                            left: -9px;
                            background: #fff;
                            padding: 0 34px;
                            display: flex;
                            align-items: center;
                        }
                    }
                    @media only screen and (max-width: 540px) {
                        {{ addonId }} .part-sevev{
                            padding: 0 13px 44px;
                        }
                        {{ addonId }} .part-sevev-item{
                            margin-top: 11px;
                            display: flex;
                            margin-bottom: 11px;
                        }
                        {{ addonId }} .part-sevev-item-img{
                            width: 65px;
                            flex-shrink: 0;
                        }
                        {{ addonId }} .part-sevev-item-txt{
                            width: 333px;
                            height: 67.65px;
                            box-shadow: 4px 2px 4px 2px #f5f6f7;
                            border-radius: 5px;
                            position: relative;
                            top: 7px;
                            left: -9px;
                            background: #fff;
                            padding: 0 20px;
                            display: flex;
                            align-items: center;
                        }
                    }
                </style>
                <div class="web_fz">
                    <img src="https://oss.lcweb01.cn/joomla/20220523/dfd5e47c602106503295e85eea4185fb.png">
                    <div class="web_fz_list">
                        <ul>
                            <# _.each(type6_item_text, function(tab, key){ #>
                                <li>
                                    <div class="web_fz_p">
                                        {{ tab.title }}
                                    </div>
                                </li>
                            <# }); #>
                            
                        </ul>
                    </div>
                </div>

                <ul class="part-sevev">
                    <# _.each(type6_item_text, function(tab, key){ #>
                        <li class="part-sevev-item">
                            <i class="part-sevev-item-img"><img src=\'{{ tab.icon }}\' width="100%"></i>
                            <div class="part-sevev-item-txt">
                                {{ tab.title }}
                            </div>
                        </li>
                    <# }); #>

                </ul>

            <# } #>
        ';
        return $output;

    }

}