<?php
/**
 * <AUTHOR> /用户名|ID/
 * @email        #EMAIL#
 * @url          #URL#
 * @copyright    Copyright (c) 2021 - #YEAR# 建站通
 * @license      GNU General Public License version 2 or later
 * @date         #DATETIME#
 * @engine       AddonBuilder v0.1.21.0224 By ShaneJhu
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();

JwAddonsConfig::addonConfig(

    array(
        'type' => 'general', //插件
        'addon_name' => 'JZT_ADDON_LSGA_4D2L2F02N8',
        'title' => '文章详情',
        'desc' => '',
        'category' => '文章', //插件分组
        'attr' => array(
            //配置项主体
            'general' => array(
                //【基本】选项卡

                // 动态生成的配置项
                'theme' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'values' => array(
                        'site01' => '布局1',
                        'site02' => '布局2',
                        'site03' => '布局3',
                        'site04' => '布局4',
                    ),
                    'std' => 'site01'
                ),
                'tdk_button'           => array(
                    'type'        => 'select',
                    'desc'        => JText::_(''),
                    'placeholder' => JText::_(''),
                    'title'       => '动态TDK设置',
                    'std'         => 'yes',
                    'values'      => array(
                        'yes' => '启用',
                        'no'  => '不启用',
                    ),
                ),
                // 'title_h2_button' => array(
	            //     'type' => 'select',
                //     'desc' => JText::_('文章详情的标题设置为网页标题'),
                //     'placeholder' => '文章详情的标题设置为网页标题',
	            //     'title' => '动态标题设置',
	            //     'std' => 'yes',
                //     'values' => array(
                //         'yes' => '启用',
                //         'no' => '不启用',
                //     ),
                // ),
                // 'keywords_h2_button' => array(
	            //     'type' => 'select',
                //     'desc' => JText::_('文章详情的简介设置为网页关键字'),
                //     'placeholder' => '文章详情的简介设置为网页关键字',
	            //     'title' => '动态关键词设置',
	            //     'std' => 'yes',
                //     'values' => array(
                //         'yes' => '启用',
                //         'no' => '不启用',
                //     ),
                // ),
                // 'description_h2_button' => array(
	            //     'type' => 'select',
                //     'desc' => JText::_('文章详情的详情设置为网页描述'),
                //     'placeholder' => '文章详情的详情设置为网页描述',
	            //     'title' => '动态描述设置',
	            //     'std' => 'yes',
                //     'values' => array(
                //         'yes' => '启用',
                //         'no' => '不启用',
                //     ),
                // ),
                'biaoqian_type' => array(
	                'type' => 'select',
                    'desc' => JText::_('动态标签设置'),
	                'title' => '动态标签设置',
	                'std' => 'no',
                    'values' => array(
                        'yes' => '启用',
                        'no' => '不启用',
                    ),
                ),
                'biaoqian_peizhi' => array(
                    'title' => JText::_('动态标签内容设置'),
                    'std' => array(
                        array(
                            'title' => '首页',
                            'title_color' => '',
                        ),
                        array(
                            'title' => '产品中心',
                            'title_color' => '',
                        ),
                        array(
                            'title' => '文章详情',
                            'title_color' => '',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('选项的名称'),
                            'std' => '',
                        ),
                        'title_color' => array(
                            'type' => 'color',
                            'title' => '文字颜色',
                            'std' => '',
                        ),
                    ),
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_bg_color' => array(
                    'type' => 'color',
                    'title' => '标签背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_line_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标签行高'),
                    'max' => 500,
                    'min' => 10,
                    'std' => 10,
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_size' => array(
	                'type' => 'slider',
                    'title' => JText::_('标签字体大小'),
                    'max' => 500,
                    'min' => 10,
                    'std' => 14,
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_type' => array(
	                'type' => 'select',
                    'desc' => JText::_('标签最后是否展示文章标题'),
	                'title' => '标签最后是否展示文章标题',
	                'std' => 'no',
                    'values' => array(
                        'yes' => '展示',
                        'no' => '不展示',
                    ),
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                    ),
                ),
                'biaoqian_title_color' => array(
	                'type' => 'color',
                    'desc' => JText::_('标签最后文章标题字体颜色'),
	                'title' => '标签最后文章标题字体颜色',
	                'std' => '',
                    'depends' => array(
                        array('biaoqian_type', '=', 'yes'),
                        array('biaoqian_title_type', '=', 'yes'),
                    ),
                ),
                'ordering_select' => array(
	                'type' => 'select',
                    'desc' => JText::_('排序方式，请和文章列表排序方式一致'),
                    'placeholder' => '排序方式',
	                'title' => '排序方式',
	                'std' => '',
                    'values' => array(
                        'orderdesc' => '排序id倒序',
                        'orderasc' => '排序id正序',
                        'timeasc' => '添加时间正序',
                        'timedesc' => '添加时间倒序',
                    ),
                ),
                'color1615443385649' => array(
                    'type' => 'color',
                    'title' => '正文颜色',
                    'desc' => '',
                    'std' => '#555555',
                ),
                'content_height03' => array(
                    'type' => 'slider',
                    'title' => JText::_('正文高度'),
                    'max' => 1000,
                    'min' => 100,
                    'std' => '500',
                    'depends' => array(
                        array('theme', '=', 'site03'),
                    ),
                ),
                'select1615452416171' => array(
                    'type' => 'select',
                    'title' => '标题布局',
                    'desc' => '',
                    'std' => 'left',
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                ),
                'font_style_type1' => array(
                    'type' => 'select',
                    'title' => '字体样式',
                    'desc' => '',
                    'std' => '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol"',
                    'values' => array(
                        'Founder_small_label_Song_Simplified' => '方正小标宋简体',
                        'imitate_song' => '仿宋',
                        '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol"' => '默认',
                    ),
                ),

                'color1615452944411' => array(
                    'type' => 'color',
                    'title' => '标题颜色',
                    'desc' => '',
                    'std' => '#333333',
                ),
                'font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'max' => 100,
                    'min' => 12,
                    'std' => '28'
                ),
                'title_font_weight' => array(
                    'type' => 'checkbox',
                    'title' => '标题文字加粗',
                    'std' => 0
                ),
                'content_top' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题与文章距离'),
                    'max' => 50,
                    'min' => 12,
                    'std' => '15',
                    'depends' => array(
                        array('theme', '=', 'site01'),
                    ),
                ),
                //日期可关闭
                'date1_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭日期显示',
                    'std' => 0,

                ),
                'select1646020218' => array(
                    'type' => 'select',
                    'title' => '日期布局',
                    'desc' => '',
                    'std' => 'right',
                    'values' => array(
                        'left' => '居左',
                        'center' => '居中',
                        'right' => '居右',
                    ),
                    'depends' => array(
                        array('theme', '=', 'site01'),
                        array('date1_hide', '!=', 1),
                    ),
                ),
                'font_size_date' => array(
                    'type' => 'slider',
                    'title' => JText::_('日期字体大小'),
                    'max' => 100,
                    'min' => 10,
                    'std' => '14',
                    'depends' => array(
                        array('date1_hide', '!=', 1),
                    ),
                ),
                'color1615453171634' => array(
                    'type' => 'color',
                    'title' => '时间颜色',
                    'desc' => '',
                    'std' => '#888888',
                    'depends' => array(
                        array('date1_hide', '!=', 1),
                    ),
                ),
                'date_text' => array(
                    'type' => 'text',
                    'title' => '时间前文字显示',
                    'std' => '编辑：',
                    'depends' => array(
                        array('theme', '!=', 'site04'),
                        array('date1_hide', '!=', 1),
                    ),
                ),
                // 设置文章详情富文本内容 标签间距
                'content_p_top' => array(
                    'type' => 'slider',
                    'title' => '编辑器内容p标签上边距',
                    'std' => 0,
                    'depends' => array(
                        // array('theme', '!=', 'site04'),
                    ),
                ),
                'content_p_bot' => array(
                    'type' => 'slider',
                    'title' => '编辑器内容p标签下边距',
                    'std' => 16,
                    'depends' => array(
                        // array('theme', '!=', 'site04'),
                    ),
                ),

                //03
                'dianzan03_autor' => array(
                    'type' => 'text',
                    'title' => '作者名',
                    'desc' => '仅适用于所有文章都是同一个作者',
                    'std' => '',
                    'depends' => array(
                        array('theme', '=', 'site03'),
                    ),
                ),
                'dianzan03_color' => array(
                    'type' => 'color',
                    'title' => '点赞字体颜色',
                    'desc' => '',
                    'std' => '#0051ca',
                    'depends' => array(
                        array('theme', '=', 'site03'),
                    ),
                ),
                'font_size03_dz' => array(
                    'type' => 'slider',
                    'title' => JText::_('点赞字体大小'),
                    'max' => 100,
                    'min' => 10,
                    'std' => '24',
                    'depends' => array(
                        array('theme', '=', 'site03'),
                    ),
                ),
                //
                // 04

                'intro_fontsize04' => array(
                    'type' => 'slider',
                    'title' => JText::_('简介字体大小'),
                    'max' => 30,
                    'min' => 10,
                    'std' => '13',
                    'depends' => array(
                        array('theme', '!=', 'site01'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                    ),
                ),
                'intro_color04' => array(
                    'type' => 'color',
                    'title' => '简介字体颜色',
                    'std' => '#888',
                    'depends' => array(
                        array('theme', '!=', 'site01'),
                        array('theme', '!=', 'site02'),
                        array('theme', '!=', 'site03'),
                    ),
                ),

                //
                'theme02_type_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭标题下方分类显示',
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                'font_size_fl' => array(
                    'type' => 'slider',
                    'title' => JText::_('分类字体大小'),
                    'max' => 100,
                    'min' => 10,
                    'std' => '14',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                        array('theme02_type_hide', '!=', '1'),
                    ),
                ),
                'color1615453171634fl' => array(
                    'type' => 'color',
                    'title' => '分类颜色',
                    'desc' => '',
                    'std' => '#888888',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                        array('theme02_type_hide', '!=', '1'),
                    ),
                ),
                'theme02_read_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭标题下方阅读显示',
                    'std' => 0,
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                'color1615453171634f' => array(
	                'type' => 'color',
	                'title' => '阅读数字颜色',
	                'desc' => '',
	                'std' => '#888888',
	                'depends' => array(
		                array('theme', '=', 'site02'),
		                array('theme02_read_hide', '!=', '1'),
	                ),
                ),
                'color1615453171634f2' => array(
	                'type' => 'color',
	                'title' => '阅读文字颜色',
	                'desc' => '',
	                'std' => '#888888',
	                'depends' => array(
		                array('theme', '=', 'site02'),
                        array('theme02_read_hide', '!=', '1'),
	                ),
                ),
                'd_font_posi' => array(
                    'type' => 'select',
                    'title' => '日期和分类位置',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                'd_margin_right' => array(
                    'type' => 'slider',
                    'title' => JText::_('日期分类阅读间距'),
                    'max' => 600,
                    'min' => 10,
                    'std' => '20',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                'd_mtop' => array(
                    'type' => 'slider',
                    'title' => JText::_('日期分类阅读上间距'),
                    'max' => 600,
                    'min' => 10,
                    'std' => '10',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                'd_mbottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('日期分类阅读下间距'),
                    'max' => 600,
                    'min' => 10,
                    'std' => '10',
                    'depends' => array(
                        array('theme', '=', 'site02'),
                    ),
                ),
                // 严格使用富文本样式，用于设置图片对齐问题
                'strict_editor' => array(
                    'type' => 'checkbox',
                    'title' => '是否使用图片位置处理',
                    'std' => 0,
                    'depends' => array(
                        array('theme', '!=','site03'),
                        array('theme', '!=','site04'),
                    )
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '翻页部分配置'
                ),
                'page_button' => array(
	                'type' => 'checkbox',
	                'title' => '翻页按钮是否关闭',
	                'std' => 0
                ),
                'page_dttitle' => array(
                    'type' => 'checkbox',
                    'title' => '翻页开启动态标题',
                    'std' => 0,
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme' ,'!=', 'site03'),
                        array('theme' ,'!=', 'site04'),
                    ),
                ),
                'site04_fy_height' => array(
                    'type' => 'slider',
                    'title' => '翻页高度',
                    'std' => '120',
                    'max' => '200',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme', '=', 'site04'),
                    ),
                ),
                'next_page_text' => array(
	                'type' => 'text',
	                'title' => '下一页按钮文字',
	                'std' => '下一页',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'up_page_text' => array(
	                'type' => 'text',
	                'title' => '上一页按钮文字',
	                'std' => '上一页',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_a_font' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮文字大小',
                    'std' => '14',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_button_back' => array(
                    'type' => 'checkbox',
                    'title' => '开启翻页按钮【返回列表】按钮',
                    'std' => 0,
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '选择【返回列表】按钮跳转页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('page_button_back', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_a_icon_show' => array(
                    'type' => 'checkbox',
                    'title' => '开启翻页左右箭头',
                    'std' => 0,
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                // 翻页按钮宽度
                'page_width' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮宽度（单位%）',
                    'std' => '48',
                    'max' => '100',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                // 翻页按钮高度
                'page_height' => array(
                    'type' => 'slider',
                    'title' => '翻页按钮高度',
                    'std' => '40',
                    'max' => '200',
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'pageState' => array(
					'type' => 'buttons',
					'title' => '翻页状态',
					'std' => 'normal',
					'values' => array(
						array(
							'label' => '正常',
							'value' => 'normal'
						),
						array(
							'label' => '移入',
							'value' => 'hover'
						),
					),
					'tabs' => true,
                    'depends' => array(
                        array('page_button', '=', '0'),
                    ),
				),
                'pageColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页文字颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'pageBorderColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页边框颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'pageBgColor' => array(
                    'type' => 'color',
                    'title' => '正常翻页背景颜色',
                    'desc' => '',
                    'std' => '#fff',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'page_a_icon_prev' => array(
                    'type' => 'media',
                    'title' => '正常上翻页图标',
                    'std' => '',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal'),
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('page_a_icon_show', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_a_icon_next' => array(
                    'type' => 'media',
                    'title' => '正常下翻页图标',
                    'std' => '',
                    'depends' => array(
                        array( 'pageState' ,'=', 'normal'),
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('page_a_icon_show', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'pageColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页文字颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'pageBorderColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页边框颜色',
                    'desc' => '',
                    'std' => '#1237ce',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'pageBgColorhover' => array(
                    'type' => 'color',
                    'title' => '移入翻页背景颜色',
                    'desc' => '',
                    'std' => '#fff',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover'),
                        array('page_button', '=', '0'),
                    ),
                ),
                'page_a_line' => array(
                    'type' => 'checkbox',
                    'title' => '开启移入下划线',
                    'std' => 0,
                    'depends' => array(
                        array('page_button', '=', '0'),
                        array( 'pageState' ,'=', 'hover'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_a_icon_prev_hover' => array(
                    'type' => 'media',
                    'title' => '移入上翻页图标',
                    'std' => '',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover'),
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('page_a_icon_show', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),
                'page_a_icon_next_hover' => array(
                    'type' => 'media',
                    'title' => '移入下翻页图标',
                    'std' => '',
                    'depends' => array(
                        array( 'pageState' ,'=', 'hover'),
                        array('page_button', '=', '0'),
                        array('page_dttitle', '=', '1'),
                        array('page_a_icon_show', '=', '1'),
                        array('theme', '!=', 'site03'),
                        array('theme', '!=', 'site04'),
                    ),
                ),

            ),
        ),
    )
);