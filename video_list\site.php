<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonVideo_list extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;

        $addon_id = $this->addon->id;
        $addon_Id = '#jwpf-addon-' . $this->addon->id;

        $theme = (isset($settings->theme) && $settings->theme) ? $settings->theme : 'theme01';

        $output = '';

        if ($theme == 'theme01')
        {
            $jw_tab_item01 = (isset($settings->jw_tab_item01) && $settings->jw_tab_item01) ? $settings->jw_tab_item01 : array(
                (Object)array(
                    'title' => '投资创新的未来',
                    'title_s' => 'The future of investment innovation',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
                (Object)array(
                    'title' => '我们正朝着更加光明的未来努力。',
                    'title_s' => 'We\'re working towards a brighter future',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
                (Object)array(
                    'title' => '天空中有更多的空间',
                    'title_s' => 'More Space in the Sky',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
                (Object)array(
                    'title' => '把开关翻转到玻璃飞行甲板上',
                    'title_s' => 'Flipping the Switch to a Glass Flight Deck',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
            );
            $video_icon = (isset($settings->video_icon) && $settings->video_icon) ? $settings->video_icon : 'https://oss.lcweb01.cn/joomla/20220301/44b419a30024df68e93805d02f470c99.png';
            $title_icon = (isset($settings->title_icon) && $settings->title_icon) ? $settings->title_icon : 'https://oss.lcweb01.cn/joomla/20220301/ea48c4d318676b06e0c52b359581f5d8.png';
            $video_display_item01 = (isset($settings->video_display_item01) && $settings->video_display_item01) ? $settings->video_display_item01 : 'list';
            $item_num_md = (isset($settings->item_num) && $settings->item_num) ? $settings->item_num : 4;
            $item_num_sm = (isset($settings->item_num_sm) && $settings->item_num_sm) ? $settings->item_num_sm : 4;
            $item_num_xs = (isset($settings->item_num_xs) && $settings->item_num_xs) ? $settings->item_num_xs : 3;

            $item_mg_md = (isset($settings->item_mg) && $settings->item_mg) ? $settings->item_mg : 20;
            $item_mg_sm = (isset($settings->item_mg_sm) && $settings->item_mg_sm) ? $settings->item_mg_sm : 20;
            $item_mg_xs = (isset($settings->item_mg_xs) && $settings->item_mg_xs) ? $settings->item_mg_xs : 20;


            if($video_display_item01=='swiper'){
                $output .= '
                    <style>
                        .mySwiper .swiper-wrapper{
                            -webkit-transition-timing-function: linear; /*之前是ease-out*/
                            -moz-transition-timing-function: linear;
                            -ms-transition-timing-function: linear;
                            -o-transition-timing-function: linear;
                            transition-timing-function: linear;
                        }
                    </style>
                ';
                $output .= '<div class="video-box swiper mySwiper" style="overflow:hidden;"><div class="swiper-wrapper" >';
                foreach ($jw_tab_item01 as $key => $item) {
                    $output .= '
                    <div class="video-item swiper-slide" onclick="showVideo_' . $addon_id . '(' . $key . ')">
                        <div class="cover-img-box">';
                    if ($item->cover_img) {
                        $output .= '<img src=\'' . $item->cover_img . '\' class="cover-img" alt="">';
                    }
                    $output .= '
                            <div class="img-mark"></div>
                            <div class="icon" onclick="showVideo_' . $addon_id . '(' . $key . ')">';
                    if ($video_icon) {
                        $output .= '<img src=\'' . $video_icon . '\' alt="">';
                    }
                    $output .= '
                            </div>
                        </div>
                        <div class="info-box">
                            <div class="text-box">
                                <div class="v-title ellipsis">' . $item->title . '</div>
                                <div class="v-title_s ellipsis">' . $item->title_s . '</div>
                            </div>
                            <div class="text-icon">';
                    if ($title_icon) {
                        $output .= '<img src=\'' . $title_icon . '\' alt="">';
                    }
                    $output .= '
                            </div>
                        </div>
                    </div>';
                }
                $output .= '</div></div>
                    <script>
                    var wigt=$(window).width();

                    if(wigt<=768){
                        var clm='.$item_num_xs.';
                        var jian='.$item_mg_xs.';

                    }else if(wigt<=998){
                        var clm='.$item_num_sm.';
                        var jian='.$item_mg_sm.';
                    }else{
                        var clm='.$item_num_md.';
                        var jian='.$item_mg_md.';
                    }
                    console.log(clm);
                        var swiper = new Swiper(".mySwiper", {
                            speed:10000,//匀速时间
                            freeMode:true,
                            loop:true,
                            slidesPerView: clm,
                            loopedSlides: clm,
                            autoplay: {
                                delay: 0,
                            },
                            spaceBetween: jian,
                        });
                        //鼠标覆盖停止自动切换
                        swiper.onmouseover=function(){
                          swiper.stopAutoplay();
                        }
                        //鼠标移出开始自动切换
                        swiper.onmouseout=function(){
                          swiper.startAutoplay();
                        }
                    </script>

                ';
            }else{

                $output .= '<div class="video-box">';
                foreach ($jw_tab_item01 as $key => $item) {
                    $output .= '
                    <div class="video-item" onclick="showVideo_' . $addon_id . '(' . $key . ')">
                        <div class="cover-img-box">';
                    if ($item->cover_img) {
                        $output .= '<img src=\'' . $item->cover_img . '\' class="cover-img" alt="">';
                    }
                    $output .= '
                            <div class="img-mark"></div>
                            <div class="icon" onclick="showVideo_' . $addon_id . '(' . $key . ')">';
                    if ($video_icon) {
                        $output .= '<img src=\'' . $video_icon . '\' alt="">';
                    }
                    $output .= '
                            </div>
                        </div>
                        <div class="info-box">
                            <div class="text-box">
                                <div class="v-title ellipsis">' . $item->title . '</div>
                                <div class="v-title_s ellipsis">' . $item->title_s . '</div>
                            </div>
                            <div class="text-icon">';
                    if ($title_icon) {
                        $output .= '<img src=\'' . $title_icon . '\' alt="">';
                    }
                    $output .= '
                            </div>
                        </div>
                    </div>';
                }
                $output .= '</div>';

            }

            $output .= '
                <div class="video-show-cover">
                    <div class="video-cover" onclick="closeVideo_' . $addon_id . '()"></div>
                    <div class="video-content">
                        <video id="video" src="https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4" controls></video>
                        <div class="text-box">
                            <div class="txt-title" id="t-title">把开关翻转到玻璃飞行甲板上</div>
                            <div class="txt-title-s" id="s-title">Flipping the Switch to a Glass Flight Deck</div>
                        </div>
                    </div>
                    <div class="btn-icon prv-icon" onclick="prevVideo_' . $addon_id . '()">
                        <img src="https://oss.lcweb01.cn/joomla/20220302/03a8f5c3dc8a76fe76527e81cfba78d7.png" alt="">
                    </div>
                    <div class="btn-icon next-icon" onclick="nextVideo_' . $addon_id . '()">
                        <img src="https://oss.lcweb01.cn/joomla/20220302/9dab9d19db4709c747e0716ef167085e.jpg" alt="">
                    </div>
                </div>
            ';
            $output .= '
                <script>
                    var jw_tab_item01_' . $addon_id . ' = ' . json_encode($jw_tab_item01) . ';
                    var itemLength_' . $addon_id . ' = jw_tab_item01_' . $addon_id . '.length;
                    var nowIndex_' . $addon_id . ' = 0;
                    function showVideo_' . $addon_id . '(key) {
                        nowIndex_' . $addon_id . ' = key;
                        changeVideo_' . $addon_id . '(key);
                        $("' . $addon_Id . ' .video-show-cover").fadeIn(500);
                    }
                    function changeVideo_' . $addon_id . '(index) {
                        $("' . $addon_Id . ' #video").attr("src", jw_tab_item01_' . $addon_id . '[index].video);
                        $("' . $addon_Id . ' #t-title").html(jw_tab_item01_' . $addon_id . '[index].title);
                        $("' . $addon_Id . ' #s-title").html(jw_tab_item01_' . $addon_id . '[index].title_s);
                    }
                    function prevVideo_' . $addon_id . '() {
                        if(nowIndex_' . $addon_id . ' == 0) {
                            nowIndex_' . $addon_id . ' = itemLength_' . $addon_id . ' - 1;
                        }else {
                            nowIndex_' . $addon_id . ' --;
                        }
                        changeVideo_' . $addon_id . '(nowIndex_' . $addon_id . ');
                    }
                    function nextVideo_' . $addon_id . '() {
                        if(nowIndex_' . $addon_id . ' == itemLength_' . $addon_id . ' - 1) {
                            nowIndex_' . $addon_id . ' = 0;
                        }else {
                            nowIndex_' . $addon_id . ' ++;
                        }
                        changeVideo_' . $addon_id . '(nowIndex_' . $addon_id . ');
                    }
                    function closeVideo_' . $addon_id . '() {
                        $("' . $addon_Id . ' .video-show-cover").fadeOut(500); 
                    }
                </script>
            ';
        }
        elseif ($theme == 'theme02')
        {
            $jw_tab_item02 = (isset($settings->jw_tab_item02) && $settings->jw_tab_item02) ? $settings->jw_tab_item02 : array(
                (Object)array(
                    'title' => '手工精选优质茶树尖嫩芽',
                    'title_s' => '嫩度一般嫩度好的茶叶，符合外形要求（“光、扁、平、直”）但是不能仅从茸毛多少来判别嫩度，因各种茶的具体要求不一样，如极好的狮峰龙井是体表无茸毛的',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
                (Object)array(
                    'title' => '讲述适当喝茶对身体的保健作用',
                    'title_s' => '绿茶需冷藏保存，将茶叶密封后放入罐子内，放置冰箱冷藏保存；红茶常温密封保存；乌龙茶用铁罐、锡罐等双层盖容器常温储存；白茶需要用纸箱与铝箔袋密封保存；',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
                (Object)array(
                    'title' => '品茶论道悟人生,把酒听禅观世态',
                    'title_s' => '人生就像一杯茶。第一口苦,第二口涩,第三口甜。回味一下,甘甜清香。平淡是它的本色,苦涩是它的历程,清甜是它的馈赠，细品人的一生，恰似品茶，看似苦涩，可香味尽在其中。一杯茶，品人生沉浮; 平常心，造万年世界。',
                    'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                    'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                ),
            );
            $video_icon_item02 = (isset($settings->video_icon_item02) && $settings->video_icon_item02) ? $settings->video_icon_item02 : 'fa-play';
            $video_display_item02= (isset($settings->video_display_item02) && $settings->video_display_item02) ? $settings->video_display_item02 : 'swiper';
            $item_left_show_item02= (isset($settings->item_left_show_item02) && ($settings->item_left_show_item02||$settings->item_left_show_item02==0)) ? $settings->item_left_show_item02 : 1;
            $item_right_show_item02= (isset($settings->item_right_show_item02) && ($settings->item_right_show_item02||$settings->item_right_show_item02==0)) ? $settings->item_right_show_item02 : 1;

            $company_id = $_GET['company_id'] ?? 0;
            $site_id = $_GET['site_id'] ?? 0;
            $layout_id = $_GET['layout_id'] ?? 0;


            if($video_display_item02=='swiper'){
                $output .= '<div class="video-box pc">';
                //                轮播模式左侧 视频标题和简介
                if($item_left_show_item02==1){
                    $output.='<div class="info swiper-container">
                        <div class="swiper-wrapper">';
                    foreach ($jw_tab_item02 as $key => $item) {
                        $output .= '<div class="swiper-slide swiper-no-swiping '.($key==0?"active":"").'" data-id="'.$key.'">
                                    <p class="title" style="line-height: 1">
                                        '.$item->title.'
                                    </p>
                                    <p class="title_s">
                                        '.$item->title_s.'
                                    </p>
                                </div>';
                    }
                    $output .= '</div>
                    </div>';
                }
                //                中间圆圈
                $output.='<div class="video-item swiper-container">
                    <div class="swiper-wrapper">';
                foreach ($jw_tab_item02 as $key => $item) {
                    $output .= '<div class="cover-img-box swiper-slide" data-id="'.$key.'">
                                <img style="'.($item->cover_img?'':'display:none;').'" src="'.$item->cover_img.'" alt="" class="cover-img">
                                <i class="fa '.$video_icon_item02.' icon" onclick="showVideo_' . $addon_id . '(' . $key . ')"></i>
                            </div>';
                }
                $output .= '</div>
                </div>';
                //                右侧标题
                if($item_right_show_item02==1){
                    $output.='<div class="title-control">';
                    foreach ($jw_tab_item02 as $key => $item) {
                        $output.='<p class="ellipsis" data-id="'.$key.'">';
                        $tz_page_type = (isset($item->tz_page_type)) ? $item->tz_page_type : 'Internal_pages';
                        $detail_page_id = (isset($item->detail_page_id)) ? $item->detail_page_id : 0;
                        $attribs='';
                        if($detail_page_id){
                            $id=base64_encode($detail_page_id);
                            $host=$_SERVER['HTTP_HOST'];
                            $return= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                            $attribs .= ' href="' . $return . '"';
                        }

                        if($item->is_link){
                            if($item->tz_page_type=='Internal_pages')
                            {
                                $output .= ($detail_page_id) ? '<a data-id="'.$key.'" ' . $attribs . '>' : '';
                            }
                            else
                            {
                                $output .= '<a data-id="'.$key.'" href="' . $item->link . '">';
                            }
                        }
                        else
                        {
                            $output .= '<a data-id="'.$key.'" href="javascript:;">';
                        }
                        $output.=$item->title.'</a>
                        </p>';
                    }
                    $output.='</div>';
                }
                $output.='</div>
            <div class="video-box mobile swiper-container">
                <div class="swiper-wrapper">';
                foreach ($jw_tab_item02 as $key => $item) {
                    $output.='<p class="ellipsis" data-id="'.$key.'">';
                    $tz_page_type = (isset($item->tz_page_type)) ? $item->tz_page_type : 'Internal_pages';
                    $detail_page_id = (isset($item->detail_page_id)) ? $item->detail_page_id : 0;
                    $attribs='';
                    if($detail_page_id){
                        $id=base64_encode($detail_page_id);
                        $host=$_SERVER['HTTP_HOST'];
                        $return= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        $attribs .= ' href="' . $return . '"';
                    }
                    $output .= '<div class="swiper-slide">
                            <div class="video-item">
                                <div class="cover-img-box">
                                    <img src="'.$item->cover_img.'" alt="" class="cover-img">
                                    <i class="fa '.$video_icon_item02.' icon" onclick="showVideo_' . $addon_id . '(' . $key . ')"></i>
                                </div>
                            </div>
                            <div class="info">';
                    if($item->is_link){
                        if($item->tz_page_type=='Internal_pages')
                        {
                            $output .= ($detail_page_id) ? '<a data-id="'.$key.'" ' . $attribs . '>' : '';
                        }
                        else
                        {
                            $output .= '<a data-id="'.$key.'" href="' . $item->link . '">';
                        }
                    }
                    else
                    {
                        $output .= '<a data-id="'.$key.'" href="javascript:;">';
                    }
                    $output.='<div class="title">'.$item->title.'</div>
                                    <div class="title_s">'.$item->title_s.'</div>';
                    $output.='</a>
                            </div>
                        </div>';
                }
                $output.='</div>
                <div class="swiper-pagination"></div>
            </div>';
            }elseif ($video_display_item02=='list'){
                $output .= '<div class="video-box">';
                foreach ($jw_tab_item02 as $key => $item) {
                    $tz_page_type = (isset($item->tz_page_type)) ? $item->tz_page_type : 'Internal_pages';
                    $detail_page_id = (isset($item->detail_page_id)) ? $item->detail_page_id : 0;
                    $attribs='';
                    if($detail_page_id){
                        $id=base64_encode($detail_page_id);
                        $host=$_SERVER['HTTP_HOST'];
                        $return= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                        $attribs .= ' href="' . $return . '"';
                    }

                    $output .= '
                <div class="video-item">
                    <div class="cover-img-box" onclick="showVideo_' . $addon_id . '(' . $key . ')">';
                    if ($item->cover_img) {
                        $output .= '<img src=\'' . $item->cover_img . '\' class="cover-img" alt="">';
                    }
                    $output .= '
                        <i class="icon fa '.$video_icon_item02.'"></i>
                    </div>
                    <div class="info-box">';
                    if($item->is_link){
                        if($item->tz_page_type=='Internal_pages')
                        {
                            $output .= ($detail_page_id) ? '<a data-id="'.$key.'" ' . $attribs . '>' : '';
                        }
                        else
                        {
                            $output .= '<a data-id="'.$key.'" href="' . $item->link . '">';
                        }
                    }
                    else
                    {
                        $output .= '<a data-id="'.$key.'" href="javascript:;">';
                    }
                    $output.='<div class="text-box">
                                <div class="v-title ellipsis">' . $item->title . '</div>
                                <div class="v-title_s">' . $item->title_s . '</div>
                            </div>
                            <div class="text-icon">';
                    $output .= '
                            </div>
                        </a>
                    </div>
                </div>';
                }
                $output .= '</div>';
            }
            $output.='
            <div class="video-show-cover">
                <div class="video-cover" onclick="closeVideo_' . $addon_id . '()"></div>
                <div class="video-content">
                    <video id="video" src="https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4" controls></video>
                    <div class="text-box">
                        <div class="txt-title" id="t-title">把开关翻转到玻璃飞行甲板上</div>
                        <div class="txt-title-s" id="s-title">Flipping the Switch to a Glass Flight Deck</div>
                    </div>
                </div>
                <div class="btn-icon prv-icon" onclick="prevVideo_' . $addon_id . '()">
                    <img src="https://oss.lcweb01.cn/joomla/20220302/03a8f5c3dc8a76fe76527e81cfba78d7.png" alt="">
                </div>
                <div class="btn-icon next-icon" onclick="nextVideo_' . $addon_id . '()">
                    <img src="https://oss.lcweb01.cn/joomla/20220302/9dab9d19db4709c747e0716ef167085e.jpg" alt="">
                </div>
            </div>';
            $output .= '
                <script>
                    window.addEventListener("load",function() {
                        if(window.innerWidth > 768){
                            let swiperInfo = new Swiper("'.$addon_Id.' .pc .info.swiper-container", {
                                direction: "vertical",
                                noSwiping : true,
                                loop: false,
                                observer: true,
                                observeParents:true,
                            });
                            let swiper = new Swiper("'.$addon_Id.' .pc .video-item", {
                                loop: false,
                                observer: true,
                                observeParents:true,
                                on:{
                                    init: function(swiper){
                                        swiperInfo.slideTo(swiper.realIndex);
                                        $("'.$addon_Id.' .title-control p[data-id="+swiper.realIndex+"]").addClass("active");
                                    },
                                    transitionEnd:function(swiper){
                                        swiperInfo.slideTo(swiper.realIndex);
                                    }
                                }
                            });
                            
                            $("' . $addon_Id . ' .video-show-cover").mouseleave(function(){
                                setTimeout(function(){
                                    $("'.$addon_Id.' .video-content .text-box").css({opacity: 0});
                                    $("'.$addon_Id.' .video-content").css({background: "transparent",border:"1px solid transparent"});
                                    $("'.$addon_Id.' .video-cover").css({background: "rgba(0,0,0,.9)"});
                                },5000)
                            }).mouseenter(function(){
                                $("'.$addon_Id.' .video-content .text-box").css({opacity: 1});
                                $("'.$addon_Id.' .video-content").css({background: "#fff",border:"1px solid transparent"});
                                $("'.$addon_Id.' .video-cover").css({background: "rgba(0,0,0,.6)"});
                            })
                            
                            $("'.$addon_Id.' .title-control").on("click",function(e){
                                if(e.target.tagName==="A"){
                                    let target=$(e.target);
                                    target.parent().addClass("active").siblings("p").removeClass("active");
                                    swiperInfo.slideTo(target.data("id"));
                                    swiper.slideTo(target.data("id"));
                                }
                            })
                        }else{
                            let swiper = new Swiper("'.$addon_Id.' .video-box.mobile", {
                                loop: true,
                                observer: true,
                                observeParents:true,
                                pagination: {
                                  el: "'.$addon_Id.' .swiper-pagination",
                                },
                            });
                        }
                    })
                    
                        var jw_tab_item02_' . $addon_id . ' = ' . json_encode($jw_tab_item02) . ';
                        var itemLength_' . $addon_id . ' = jw_tab_item02_' . $addon_id . '.length;
                        var nowIndex_' . $addon_id . ' = 0;
                        function showVideo_' . $addon_id . '(key) {
                            nowIndex_' . $addon_id . ' = key;
                            changeVideo_' . $addon_id . '(key);
                            $("' . $addon_Id . ' .video-show-cover").fadeIn(500);                        
                        }
                        function changeVideo_' . $addon_id . '(index) {
                            $("' . $addon_Id . ' #video").attr("src", jw_tab_item02_' . $addon_id . '[index].video);
                            $("' . $addon_Id . ' #t-title").html(jw_tab_item02_' . $addon_id . '[index].title);
                            $("' . $addon_Id . ' #s-title").html(jw_tab_item02_' . $addon_id . '[index].title_s);
                        }
                        function prevVideo_' . $addon_id . '() {
                            if(nowIndex_' . $addon_id . ' == 0) {
                                nowIndex_' . $addon_id . ' = itemLength_' . $addon_id . ' - 1;
                            }else {
                                nowIndex_' . $addon_id . ' --;
                            }
                            changeVideo_' . $addon_id . '(nowIndex_' . $addon_id . ');
                        }
                        function nextVideo_' . $addon_id . '() {
                            if(nowIndex_' . $addon_id . ' == itemLength_' . $addon_id . ' - 1) {
                                nowIndex_' . $addon_id . ' = 0;
                            }else {
                                nowIndex_' . $addon_id . ' ++;
                            }
                            changeVideo_' . $addon_id . '(nowIndex_' . $addon_id . ');
                        }
                        function closeVideo_' . $addon_id . '() {
                            $("' . $addon_Id . ' .video-show-cover").fadeOut(500); 
                        }
                </script>
            ';
        }

        return $output;

    }

    public function css(){
        $settings = $this->addon->settings;

        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $theme = (isset($settings->theme) && $settings->theme) ? $settings->theme : 'theme01';

        $item_img_fit = (isset($settings->item_img_fit) && $settings->item_img_fit) ? $settings->item_img_fit : 'cover';

        $css = '';
        if ($theme == 'theme01')
        {
            // 一行展示列数
            $item_num_md = (isset($settings->item_num) && $settings->item_num) ? $settings->item_num : 4;
            $item_num_sm = (isset($settings->item_num_sm) && $settings->item_num_sm) ? $settings->item_num_sm : 4;
            $item_num_xs = (isset($settings->item_num_xs) && $settings->item_num_xs) ? $settings->item_num_xs : 3;
            // 列间距
            $item_mg_md = (isset($settings->item_mg) && $settings->item_mg) ? $settings->item_mg : 20;
            $item_mg_sm = (isset($settings->item_mg_sm) && $settings->item_mg_sm) ? $settings->item_mg_sm : 20;
            $item_mg_xs = (isset($settings->item_mg_xs) && $settings->item_mg_xs) ? $settings->item_mg_xs : 20;
            // 视频封面图高度
            $item_img_height_md = (isset($settings->item_img_height) && $settings->item_img_height) ? $settings->item_img_height : 154;
            $item_img_height_sm = (isset($settings->item_img_height_sm) && $settings->item_img_height_sm) ? $settings->item_img_height_sm : 120;
            $item_img_height_xs = (isset($settings->item_img_height_xs) && $settings->item_img_height_xs) ? $settings->item_img_height_xs : 120;

            $width_md = $item_mg_md / $item_num_md;
            $width_sm = $item_mg_sm / $item_num_sm;
            $width_xs = $item_mg_xs / $item_num_xs;

            $css .= "
                {$addonId} .ellipsis {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: block;
                }
                {$addonId} .video-box {
                    display: flex;
                    flex-wrap: wrap;
                }
                {$addonId} .video-box .video-item {
                    width: calc(100% / {$item_num_md} - {$item_mg_md}px + {$width_md}px);
                    margin-right: {$item_mg_md}px;
                    margin-bottom: {$item_mg_md}px;
                    cursor: pointer;
                }
                {$addonId} .video-box .video-item:nth-child({$item_num_md}n) {
                    margin-right: 0;
                }
                {$addonId} .video-box .video-item .cover-img-box {
                    width: 100%;
                    height: {$item_img_height_md}px;
                    position: relative;
                    overflow: hidden;
                }
                {$addonId} .video-box .video-item .cover-img {
                    width: 100%;
                    height: 100%;
                    object-fit: {$item_img_fit};
                }
                {$addonId} .video-box .video-item .cover-img-box .img-mark {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0);
                    transition: all 0.36s ease;
                }
                {$addonId} .video-box .video-item .cover-img-box .icon {
                    position: absolute;
                    width: 100%;
                    top: -100%;
                    left: 0;
                    height: 100%;
                    transition: all 0.6s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                {$addonId} .video-box .video-item .cover-img-box .icon img {
                    display: block;
                }
                {$addonId} .video-box .video-item .info-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                {$addonId} .video-box .video-item .info-box .text-box {
                    width: 100%;
                    padding-top: 15px;
                    padding-bottom: 15px;
                    padding-left: 0;
                    transition: all 0.6s ease 0s;
                    box-sizing: content-box;
                }
                {$addonId} .video-box .video-item .info-box .text-box .v-title {
                    font-size: 16px;
                    color: #535353;
                    margin-top: 10px;
                }
                {$addonId} .video-box .video-item .info-box .text-box .v-title_s {
                    font-size: 13px;
                    color: #b7b7b7;
                    margin-top: 6px;
                }
                {$addonId} .video-box .video-item .info-box .text-icon {
                    opacity: 0;
                    transition: all 0.6s ease 0s;
                }
                {$addonId} .video-box .video-item:hover .cover-img-box .img-mark {
                    background: rgba(0, 0, 0, 0.47);
                }
                {$addonId} .video-box .video-item:hover .cover-img-box .icon {
                    top: 0;
                }
                {$addonId} .video-box .video-item:hover .info-box .text-box {
                    padding-left: 10px;
                }
                {$addonId} .video-box .video-item:hover .info-box .text-icon {
                    padding-right: 10px;
                    opacity: 1;
                }
                /*遮罩层*/
                {$addonId} .video-show-cover {
                    position: fixed;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    display: none;
                }
                {$addonId} .video-show-cover .video-cover {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    background: rgba(0, 0, 0, 0.6);
                }
                {$addonId} .video-show-cover .video-content {
                    width: 960px;
                    height: 618px;
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    z-index: 1001;
                    padding: 10px;
                    background: #252525;
                    border: 1px solid #252525;
                    transition: margin 0.36s ease, background 1s ease, border-color 1s ease;
                    box-sizing: content-box;
                }
                {$addonId} .video-show-cover .video-content #video {
                    width: 100%;
                    height: 540px;
                }
                {$addonId} .video-show-cover .video-content .text-box {
                    width: 100%;
                    height: auto;
                    padding: 15px 0 0;
                    transition: opacity 1s ease;
                    color: #fff;
                }
                {$addonId} .video-show-cover .video-content .text-box .txt-title {
                    font-size: 16px;
                }
                {$addonId} .video-show-cover .video-content .text-box .txt-title-s {
                    color: #7d7d7d;
                    font-size: 13px;
                    line-height: 22px;
                    margin-top: 6px;
                }
                {$addonId} .video-show-cover .btn-icon {
                    width: 48px;
                    height: 48px;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    z-index: 1002;
                    cursor: pointer;
                }
                {$addonId} .video-show-cover .prv-icon {
                    left: 0;
                }
                {$addonId} .video-show-cover .next-icon {
                    right: 0;
                }
                {$addonId} .video-show-cover .btn-icon img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    {$addonId} .video-box .video-item {
                        width: calc(100% / {$item_num_sm} - {$item_mg_sm}px + {$width_sm}px);
                        margin-right: {$item_mg_sm}px;
                        margin-bottom: {$item_mg_sm}px;
                    }";
            if($item_num_md != $item_num_sm) {
                $css .= "
                        {$addonId} .video-box .video-item:nth-child({$item_num_md}n) {
                            margin-right: {$item_mg_sm}px;
                        }";
            }
            $css .= "{$addonId} .video-box .video-item:nth-child({$item_num_sm}}n) {
                        margin-right: 0;
                    }
                    {$addonId} .video-box .video-item .cover-img-box {
                        height: {$item_img_height_sm}px;
                    }
                    {$addonId} .video-show-cover .video-content {
                        width: calc(100% - 100px);
                        height: max-content;
                    }
                    {$addonId} .video-show-cover .video-content #video {
                        height: auto;
                    }
                    {$addonId} .video-show-cover .btn-icon {
                        width: 20px;
                        height: 24px;
                    }
                }
                @media (max-width: 767px) {
                    {$addonId} .video-box .video-item {
                        width: calc(100% / {$item_num_xs} - {$item_mg_xs}px + {$width_xs}px);
                        margin-right: {$item_mg_xs}px;
                        margin-bottom: {$item_mg_xs}px;
                    }";
            if($item_num_md != $item_num_xs) {
                $css .= "
                        {$addonId} .video-box .video-item:nth-child({$item_num_md}n) {
                            margin-right: {$item_mg_xs}px;
                        }";
            }
            $css .= "
                    {$addonId} .video-box .video-item:nth-child({$item_num_xs}n) {
                        margin-right: 0;
                    }
                    {$addonId} .video-box .video-item .cover-img-box {
                        height: {$item_img_height_xs}px;
                    }
                    {$addonId} .video-show-cover .video-content {
                        width: calc(100% - 100px);
                        height: max-content;
                    }
                    {$addonId} .video-show-cover .video-content #video {
                        height: auto;
                    }
                    {$addonId} .video-show-cover .btn-icon {
                        width: 20px;
                        height: 24px;
                    }
                }
            ";
        }
        elseif ($theme == 'theme02')
        {
            $video_icon_size_item02 = (isset($settings->video_icon_size_item02) && $settings->video_icon_size_item02) ? $settings->video_icon_size_item02 : 30;
            $item_width_item02 = (isset($settings->item_width_item02) && $settings->item_width_item02) ? $settings->item_width_item02 : 28;
            $item_width_item02_sm = (isset($settings->item_width_item02_sm) && $settings->item_width_item02_sm) ? $settings->item_width_item02_sm : 28;
            $item_border_width_item02 = (isset($settings->item_border_width_item02) && $settings->item_border_width_item02) ? $settings->item_border_width_item02 : 4;
            $item_border_width_item02_sm = (isset($settings->item_border_width_item02_sm) && $settings->item_border_width_item02_sm) ? $settings->item_border_width_item02_sm : 4;
            $item_border_width_item02_xs= (isset($settings->item_border_width_item02_xs) && $settings->item_border_width_item02_xs) ? $settings->item_border_width_item02_xs : 9;
            $item_video_title_font_size_item02= (isset($settings->item_video_title_font_size_item02) && $settings->item_video_title_font_size_item02) ? $settings->item_video_title_font_size_item02 : 16;
            $item_video_title_color_item02= (isset($settings->item_video_title_color_item02) && $settings->item_video_title_color_item02) ? $settings->item_video_title_color_item02 : "#2e2e2e";
            $item_video_intro_font_size_item02= (isset($settings->item_video_intro_font_size_item02) && $settings->item_video_intro_font_size_item02) ? $settings->item_video_intro_font_size_item02 : 13;
            $item_video_intro_color_item02= (isset($settings->item_video_intro_color_item02) && $settings->item_video_intro_color_item02) ? $settings->item_video_intro_color_item02 : "#7d7d7d";
            $item_video_intro_top_item02= (isset($settings->item_video_intro_top_item02) && $settings->item_video_intro_top_item02) ? $settings->item_video_intro_top_item02 : 6;
            $item_page_dot_bg_item02= (isset($settings->item_page_dot_bg_item02) && $settings->item_page_dot_bg_item02) ? $settings->item_page_dot_bg_item02 : '#d9d9d9';
            $item_page_dot_border_item02= (isset($settings->item_page_dot_border_item02) && $settings->item_page_dot_border_item02) ? $settings->item_page_dot_border_item02 : '#e6ab43';
            $item_border_color_item02= (isset($settings->item_border_color_item02) && $settings->item_border_color_item02) ? $settings->item_border_color_item02 : '#fff';
            $item_intro_width_item02= (isset($settings->item_intro_width_item02) && $settings->item_intro_width_item02) ? $settings->item_intro_width_item02 : 32;
            $item_intro_width_item02_sm= (isset($settings->item_intro_width_item02_sm) && $settings->item_intro_width_item02_sm) ? $settings->item_intro_width_item02_sm : 32;
            $item_intro_width_item02_xs= (isset($settings->item_intro_width_item02_xs) && $settings->item_intro_width_item02_xs) ? $settings->item_intro_width_item02_xs : 100;
            $item_intro_title_font_size_item02= (isset($settings->item_intro_title_font_size_item02) && $settings->item_intro_title_font_size_item02) ? $settings->item_intro_title_font_size_item02 : 24;
            $item_intro_title_font_size_item02_sm= (isset($settings->item_intro_title_font_size_item02_sm) && $settings->item_intro_title_font_size_item02_sm) ? $settings->item_intro_title_font_size_item02_sm : 24;
            $item_intro_title_font_size_item02_xs= (isset($settings->item_intro_title_font_size_item02_xs) && $settings->item_intro_title_font_size_item02_xs) ? $settings->item_intro_title_font_size_item02_xs : 13;
            $item_intro_title_color_item02= (isset($settings->item_intro_title_color_item02) && $settings->item_intro_title_color_item02) ? $settings->item_intro_title_color_item02 : '#404040';
            $item_intro_intro_item02= (isset($settings->item_intro_intro_item02) && $settings->item_intro_intro_item02) ? $settings->item_intro_intro_item02 : 41;
            $item_intro_intro_item02_sm= (isset($settings->item_intro_intro_item02_sm) && $settings->item_intro_intro_item02_sm) ? $settings->item_intro_intro_item02_sm : 41;
            $item_intro_intro_item02_xs= (isset($settings->item_intro_intro_item02_xs) && $settings->item_intro_intro_item02_xs) ? $settings->item_intro_intro_item02_xs : 20;
            $item_intro_intro_font_size_item02= (isset($settings->item_intro_intro_font_size_item02) && $settings->item_intro_intro_font_size_item02) ? $settings->item_intro_intro_font_size_item02 : 16;
            $item_intro_intro_font_size_item02_sm= (isset($settings->item_intro_intro_font_size_item02_sm) && $settings->item_intro_intro_font_size_item02_sm) ? $settings->item_intro_intro_font_size_item02_sm : 16;
            $item_intro_intro_font_size_item02_xs= (isset($settings->item_intro_intro_font_size_item02_xs) && $settings->item_intro_intro_font_size_item02_xs) ? $settings->item_intro_intro_font_size_item02_xs : 13;
            $item_intro_intro_color_item02= (isset($settings->item_intro_intro_color_item02) && $settings->item_intro_intro_color_item02) ? $settings->item_intro_intro_color_item02 : 'grey';
            $item_intro_height_item02= (isset($settings->item_intro_height_item02) && $settings->item_intro_height_item02) ? $settings->item_intro_height_item02 : 166;
            $item_right_right_item02= (isset($settings->item_right_right_item02) && ($settings->item_right_right_item02 || $settings->item_right_right_item02==0)) ? $settings->item_right_right_item02 : 10;
            $item_right_title_margin_item02= (isset($settings->item_right_title_margin_item02) && $settings->item_right_title_margin_item02) ? $settings->item_right_title_margin_item02 : '0px 0px 0px 28px';
            $item_right_letter_spacing_item02= (isset($settings->item_right_letter_spacing_item02) && $settings->item_right_letter_spacing_item02) ? $settings->item_right_letter_spacing_item02 : 4;
            $item_right_font_size_item02= (isset($settings->item_right_font_size_item02) && $settings->item_right_font_size_item02) ? $settings->item_right_font_size_item02 : 16;
            $item_right_line_height_item02= (isset($settings->item_right_line_height_item02) && $settings->item_right_line_height_item02) ? $settings->item_right_line_height_item02 : 2;
            $item_right_color_item02= (isset($settings->item_right_color_item02) && $settings->item_right_color_item02) ? $settings->item_right_color_item02 : '#737373';
            $item_right_active_color_item02= (isset($settings->item_right_active_color_item02) && $settings->item_right_active_color_item02) ? $settings->item_right_active_color_item02 : '#e6ab43';
            $item_bottom_item02= (isset($settings->item_bottom_item02) && $settings->item_bottom_item02) ? $settings->item_bottom_item02 : 50;
            $video_display_item02= (isset($settings->video_display_item02) && $settings->video_display_item02) ? $settings->video_display_item02 : 'swiper';

            $item_num_site02_md = (isset($settings->item_num_site02) && $settings->item_num_site02) ? $settings->item_num_site02 : 2;
            $item_num_site02_sm = (isset($settings->item_num_site02_sm) && $settings->item_num_site02_sm) ? $settings->item_num_site02_sm : 2;
            $item_num_site02_xs = (isset($settings->item_num_site02_xs) && $settings->item_num_site02_xs) ? $settings->item_num_site02_xs : 1;

            $item_mg_site02_md = (isset($settings->item_mg_site02) && $settings->item_mg_site02) ? $settings->item_mg_site02 : 275;
            $item_mg_site02_sm = (isset($settings->item_mg_site02_sm) && $settings->item_mg_site02_sm) ? $settings->item_mg_site02_sm : 100;
            $item_mg_site02_xs = (isset($settings->item_mg_site02_xs) && $settings->item_mg_site02_xs) ? $settings->item_mg_site02_xs : 0;

            $item_mg_bottom_site02_md = (isset($settings->item_mg_bottom_site02) && $settings->item_mg_bottom_site02) ? $settings->item_mg_bottom_site02 : 110;
            $item_mg_bottom_site02_sm = (isset($settings->item_mg_bottom_site02_sm) && $settings->item_mg_bottom_site02_sm) ? $settings->item_mg_bottom_site02_sm : 90;
            $item_mg_bottom_site02_xs = (isset($settings->item_mg_bottom_site02_xs) && $settings->item_mg_bottom_site02_xs) ? $settings->item_mg_bottom_site02_xs : 50;

            $item_intro_top_item02_md = (isset($settings->item_intro_top_item02) && $settings->item_intro_top_item02) ? $settings->item_intro_top_item02 : 60;
            $item_intro_top_item02_sm = (isset($settings->item_intro_top_item02_sm) && $settings->item_intro_top_item02_sm) ? $settings->item_intro_top_item02_sm : 40;
            $item_intro_top_item02_xs = (isset($settings->item_intro_top_item02_xs) && $settings->item_intro_top_item02_xs) ? $settings->item_intro_top_item02_xs : 20;

            $item_intro_title_hover_color_item02 = (isset($settings->item_intro_title_hover_color_item02) && $settings->item_intro_title_hover_color_item02) ? $settings->item_intro_title_hover_color_item02 : '#e6ab43';
            $item_page_dot_size_item02 = (isset($settings->item_page_dot_size_item02) && $settings->item_page_dot_size_item02) ? $settings->item_page_dot_size_item02 : 6;

            $width_md = $item_mg_site02_md / $item_num_site02_md;
            $width_sm = $item_mg_site02_sm / $item_num_site02_sm;
            $width_xs = $item_mg_site02_xs / $item_num_site02_xs;

            $css.="*{
                    padding: 0;
                    margin: 0;
                }
                {$addonId} a{
                    display: block;
                    text-decoration: none;
                }
                {$addonId} .ellipsis {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: block;
                }
                {$addonId} .video-box {
                    overflow: hidden;
                    position: relative;
                }
                
                {$addonId} .video-box .video-item .cover-img {
                    width: 100%;
                    height: 100%;
                    object-fit: {$item_img_fit};
                }
                {$addonId} .video-box .video-item .icon{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    color: #fff;
                    font-size: {$video_icon_size_item02}px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
                }
                {$addonId} .video-box .video-item:hover .cover-img-box .icon {
                    transform: scale(1.5);
                }
                {$addonId} .video-box.mobile .swiper-slide{
                    padding: 0 8vw;
                }
                
                /*遮罩层*/
                {$addonId} .video-show-cover {
                    position: fixed;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    display: none;
                }
                {$addonId} .video-show-cover .video-cover {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    background: rgba(0, 0, 0, 0.6);
                }
                {$addonId} .video-show-cover .video-content {
                    width: 960px;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%,-50%);
                    margin: auto;
                    z-index: 1001;
                    padding: 10px;
                    background: #fff;
                    border: 1px solid #fff;
                    transition: background 1s ease, border-color 1s ease;
                    box-sizing: content-box;
                }
                {$addonId} .video-show-cover .video-content #video {
                    width: 100%;
                    height: 540px;
                }
                {$addonId} .video-show-cover .video-content .text-box {
                    width: 100%;
                    height: auto;
                    padding: 15px 0 0;
                    transition: opacity 1s ease;
                    color: #fff;
                }
                {$addonId} .video-show-cover .video-content .text-box .txt-title {
                    font-size: {$item_video_title_font_size_item02}px;
                    color: {$item_video_title_color_item02};
                }
                {$addonId} .video-show-cover .video-content .text-box .txt-title-s {
                    color: {$item_video_intro_color_item02};
                    font-size: {$item_video_intro_font_size_item02}px;
                    line-height: 1.8;
                    margin-top: {$item_video_intro_top_item02}px;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    max-height: 50px;
                }
                {$addonId} .video-show-cover .btn-icon {
                    width: 48px;
                    height: 48px;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    z-index: 1002;
                    cursor: pointer;
                }
                {$addonId} .video-show-cover .prv-icon {
                    left: 0;
                }
                {$addonId} .video-show-cover .next-icon {
                    right: 0;
                }
                {$addonId} .video-show-cover .btn-icon img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                {$addonId} .video-box.mobile{
                    display: none;
                }
                {$addonId} .video-box.pc .title,{$addonId} .info-box .v-title{
                    font-size: {$item_intro_title_font_size_item02}px;
                    color: {$item_intro_title_color_item02};
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 1.2;
                }
                {$addonId} .video-box.pc .title_s,{$addonId} .info-box .v-title_s{
                    display: -webkit-box;
                    overflow: hidden;
                    max-height: 96px;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    font-size: {$item_intro_intro_font_size_item02}px;
                    line-height: 2;
                    margin-top: {$item_intro_intro_item02}px;
                    color: {$item_intro_intro_color_item02};
                }
                @media (max-width: 767px) {
                    {$addonId} .video-box{
                        display: block;
                    }
                    {$addonId} .video-box.pc{
                        display: none;
                    }
                    {$addonId} .video-box.mobile{
                        display: block;
                        padding-bottom: {$item_bottom_item02}px;
                    }
                    {$addonId} .video-box.pc .info{
                        width: {$item_intro_width_item02_xs}%;
                    }
                    {$addonId} .video-box .title{
                        font-size: {$item_intro_title_font_size_item02_xs}px;
                        color: {$item_intro_title_color_item02};
                    }
                    {$addonId} .title_s{
                        max-height: 70px;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 3;
                        -webkit-box-orient: vertical;
                        line-height: 1.5;
                        margin-top: {$item_intro_intro_item02_xs}px;
                        color: {$item_intro_intro_color_item02};
                        font-size: {$item_intro_intro_font_size_item02_xs}px;
                    }
                    {$addonId} .swiper-pagination-bullet{
                        width: {$item_page_dot_size_item02}px;
                        height: {$item_page_dot_size_item02}px;
                        background: {$item_page_dot_bg_item02};
                        opacity: 1;
                    }
                    {$addonId} .swiper-pagination-bullet-active{
                        background: #fff;
                        border: 1px solid {$item_page_dot_border_item02};
                    }
                    {$addonId} .video-show-cover .video-content #video {
                        height: auto;
                    }
                    {$addonId} .video-show-cover {
                        background: #000;
                    }
                    {$addonId} .video-show-cover .video-content{
                        width: 100vw;
                        height: 33vh;
                        padding: 0;
                        background: #000;
                        border: none;
                    }
                    {$addonId} .video-content .text-box{
                        display: none;
                    }
                    {$addonId} .video-show-cover .video-content #video{
                        height: 100%;
                    }
                    {$addonId} .btn-icon{
                        display: none;
                    }
                }";
            if($video_display_item02=='swiper'){
                $css .= "{$addonId} .video-box .video-item {
                    width: {$item_width_item02}vw;
                    height: {$item_width_item02}vw;
                    overflow: hidden;
                    cursor: pointer;
                    background: #fff;
                    border-radius: 50%;
                    box-shadow: 0 0 100px 0 rgb(0, 0, 0, 0.05);
                    border: {$item_border_width_item02}vw solid {$item_border_color_item02};
                    margin: auto;
                    box-sizing: border-box;
                }
                {$addonId} .video-box.pc .info{
                    width: {$item_intro_width_item02}%;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    overflow: hidden;
                    height: {$item_intro_height_item02}px;
                    cursor: default;
                    z-index: 50;
                }
                {$addonId} .video-box.mobile .info{
                    margin-top: 22px;
                }
                {$addonId} .video-box.mobile .info .title{
                    text-align: center;
                }
                {$addonId} .video-box.pc .title {
                    text-align: right;
                    padding-left: 10px;
                }
                {$addonId} .video-box.pc .title {
                    text-align: right;
                }
                {$addonId} .title-control{
                    position: absolute;
                    top: 50%;
                    right: {$item_right_right_item02}%;
                    transform: translateY(-50%);
                    writing-mode: vertical-lr;
                    z-index: 50;
                }
                {$addonId} .title-control p{
                    margin: {$item_right_title_margin_item02};
                    letter-spacing: {$item_right_letter_spacing_item02}px;
                    font-size: {$item_right_font_size_item02}px;
                    line-height: {$item_right_line_height_item02};
                    display: inline-block;
                    cursor: pointer;
                    transition: all .3s ease-in-out 0s;
                    position: relative;
                }
                {$addonId} .title-control p::before{
                    height: 0;
                    content: '';
                    display: block;
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 1px;
                    background: {$item_right_active_color_item02};
                    transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
                }
                {$addonId} .title-control p.active a,{$addonId} .title-control p:hover a{
                    color: {$item_right_active_color_item02};
                }
                {$addonId} .title-control p.active::before,{$addonId} .title-control p:hover::before{
                    height: 100%;
                }
                {$addonId} .title-control p a{
                    display: block;
                    width: 100%;
                    height: 100%;
                    color: {$item_right_color_item02};
                    text-decoration: none;
                }
                
                @media (min-width: 768px) and (max-width: 991px) {
                    {$addonId} .video-box.pc .title{
                        font-size: {$item_intro_title_font_size_item02_sm}px;
                    }
                    {$addonId} .video-box.pc .title_s{
                        font-size: {$item_intro_intro_font_size_item02_sm}px;
                        margin-top: {$item_intro_intro_item02_sm}px;
                    }
                    {$addonId} .video-box .video-item {
                        width: {$item_width_item02_sm}vw;
                        height: {$item_width_item02_sm}vw;
                        border: {$item_border_width_item02_sm}vw solid {$item_border_color_item02};
                    }
                    {$addonId} .video-box.pc .info{
                        width: {$item_intro_width_item02_sm}%;
                    }
                    {$addonId} .video-show-cover .video-content #video {
                        height: auto;
                    }
                }
                @media (max-width: 767px) {
                    {$addonId} .video-box .video-item {
                        width: 100%;
                        padding-top: 100%;
                        margin: auto;
                        position: relative;
                        border: none;
                        overflow: visible;
                    }
                    {$addonId} .video-box .video-item .cover-img-box{
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        margin: auto;
                        border-radius: 50%;
                        overflow: hidden;
                        border: {$item_border_width_item02_xs}vw solid {$item_border_color_item02};
                    }
                    
                    {$addonId} .video-box.pc .title, {$addonId} .video-box .v-title{
                        font-size: {$item_intro_title_font_size_item02_xs}px;
                    }
                    {$addonId} .video-box.pc .title_s, {$addonId} .video-box .v-title_s{
                        font-size: {$item_intro_intro_font_size_item02_xs}px;
                        margin-top: {$item_intro_intro_item02_xs}px;
                    }
                }
            ";
            }elseif($video_display_item02=='list'){
                $css.="{$addonId} .video-box {
                    display: flex;
                    flex-wrap: wrap;
                }
                {$addonId} .video-box .video-item {
                    width: calc(100% / {$item_num_site02_md} - {$item_mg_site02_md}px + {$width_md}px);
                    margin-right: {$item_mg_site02_md}px;
                    margin-bottom: {$item_mg_bottom_site02_md}px;
                    cursor: pointer;
                }
                {$addonId} .video-box .video-item:nth-child({$item_num_site02_md}n) {
                    margin-right: 0;
                }
                {$addonId} .video-box .video-item:hover .v-title{
                    color: {$item_intro_title_hover_color_item02};
                }
                {$addonId} .video-box .video-item .cover-img-box{
                    width: 100%;
                    height: 0;
                    padding-top: 100%;
                    position: relative;
                    border-radius: 50%;
                    overflow: hidden;
                    box-sizing: content-box;
                }
                {$addonId} .video-box .video-item .cover-img-box img{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                }
                {$addonId} .video-box .video-item .info-box{
                    margin-top: {$item_intro_top_item02_md}px;
                    text-align: center;
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    {$addonId} .video-box .video-item .info-box{
                        margin-top: {$item_intro_top_item02_sm}px;
                        text-align: center;
                    }
                    {$addonId} .video-box .video-item {
                        width: calc(100% / {$item_num_site02_sm} - {$item_mg_site02_sm}px + {$width_sm}px);
                        margin-right: {$item_mg_site02_sm}px;
                        margin-bottom: {$item_mg_bottom_site02_sm}px;
                    }";
                if($item_num_site02_md != $item_num_site02_sm) {
                    $css .= "{$addonId} .video-box .video-item:nth-child({$item_num_site02_md}n) {
                            margin-right: {$item_mg_site02_sm}px;
                        }";
                }
                $css .= "{$addonId} .video-box .video-item:nth-child({$item_num_site02_sm}n) {
                            margin-right: 0;
                        }
                    {$addonId} .video-show-cover .video-content #video {
                        height: auto;
                    }
                    {$addonId} .video-show-cover .btn-icon {
                        width: 20px;
                        height: 24px;
                    }
                    {$addonId} .video-box .video-item .info-box{
                        margin-top: {$item_intro_top_item02_sm}px;
                    }
                    {$addonId} .video-box .v-title{
                        font-size: {$item_intro_title_font_size_item02_sm}px;
                    }
                    {$addonId} .video-box .v-title_s{
                        font-size: {$item_intro_intro_font_size_item02_sm}px;
                        margin-top: {$item_intro_intro_item02_sm}px;
                    }
                }
                @media (max-width: 767px) {
                    {$addonId} .video-box .video-item {
                        width: calc(100% / {$item_num_site02_xs} - {$item_mg_site02_xs}px + {$width_xs}px);
                        margin-right: {$item_mg_site02_xs}px;
                        margin-bottom: {$item_mg_bottom_site02_xs}px;
                    }
                    {$addonId} .video-box .video-item .info-box{
                        margin-top: {$item_intro_top_item02_xs}px;
                        text-align: center;
                    }";
                if($item_num_site02_md != $item_num_site02_xs) {
                    $css .= "{$addonId} .video-box .video-item:nth-child({$item_num_site02_md}n) {
                            margin-right: {$item_mg_site02_xs}px;
                        }";
                }
                $css .= "
                        {$addonId} .video-box .video-item:nth-child({$item_num_site02_xs}n) {
                            margin-right: 0;
                        }
                        {$addonId} .video-show-cover .video-content #video {
                            height: auto;
                        }
                        {$addonId} .video-show-cover .btn-icon {
                            width: 20px;
                            height: 24px;
                        }
                        
                        {$addonId} .video-box .v-title{
                            font-size: {$item_intro_title_font_size_item02_xs}px;
                        }
                        {$addonId} .video-box .v-title_s{
                            font-size: {$item_intro_intro_font_size_item02_xs}px;
                            margin-top: {$item_intro_intro_item02_xs}px;
                        }
                    }
                ";
            }

        }
        return $css;
    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js');
        return $js;
    }

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css', JURI::base(true) . '/components/com_jwpagefactory/addons/image_swiper/assets/css/lg.css', JURI::base(true) . '/components/com_jwpagefactory/addons/image_swiper/assets/css/lc_web.min.css');
        return $style_sheet;
    }

    public static function getTemplate()
    {
        $output = '
			<#
			    var addonId = "#jwpf-addon-" + data.id;
			    var theme = data.theme || "theme01";
			    var item_img_fit = data.item_img_fit || "cover";
			#>
			<# if(theme == "theme01") { 
			    var jw_tab_item01 = data.jw_tab_item01 || [{
			        title: "投资创新的未来",
			        title_s: "The future of investment innovation",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    },{
			        title: "我们正朝着更加光明的未来努力。",
			        title_s: "We\'re working towards a brighter future",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    },{
			        title: "天空中有更多的空间",
			        title_s: "More Space in the Sky",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    },{
			        title: "把开关翻转到玻璃飞行甲板上",
			        title_s: "Flipping the Switch to a Glass Flight Deck",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    }];
			    // 一行展示列数
		        var item_num = (data.item_num && data.item_num.md) || 4;
		        var item_num_sm = (data.item_num && data.item_num.sm) || 4;
		        var item_num_xs = (data.item_num && data.item_num.xs) || 3;
		        // 列间距
		        var item_mg = (data.item_mg && data.item_mg.md) || 20;
		        var item_mg_sm = (data.item_mg && data.item_mg.sm) || 20;
		        var item_mg_xs = (data.item_mg && data.item_mg.xs) || 20;
		        // 视频封面图高度
		        var item_img_height = (data.item_img_height && data.item_img_height.md) || 154;
		        var item_img_height_sm = (data.item_img_height && data.item_img_height.sm) || 120;
		        var item_img_height_xs = (data.item_img_height && data.item_img_height.xs) || 120;
		        // 视频播放图标
			    var video_icon  = data.video_icon || "https://oss.lcweb01.cn/joomla/20220301/44b419a30024df68e93805d02f470c99.png";
		        // 标题右侧箭头图标
			    var title_icon  = data.title_icon || "https://oss.lcweb01.cn/joomla/20220301/ea48c4d318676b06e0c52b359581f5d8.png";
			#>
			    <style>
			        {{addonId}} .ellipsis {
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                        display: block;
                    }
			        {{addonId}} .video-box {
			            display: flex;
			            flex-wrap: wrap;
			        }
			        {{addonId}} .video-box .video-item {
			            <# var width = item_mg / item_num; #>
			            width: calc(100% / {{ item_num }} - {{ item_mg }}px + {{ width }}px);
			            margin-right: {{ item_mg }}px;
			            margin-bottom: {{ item_mg }}px;
			            cursor: pointer;
			        }
			        {{addonId}} .video-box .video-item:nth-child({{ item_num }}n) {
			            margin-right: 0;
			        }
			        {{addonId}} .video-box .video-item .cover-img-box {
			            width: 100%;
			            height: {{ item_img_height }}px;
			            position: relative;
			            overflow: hidden;
			        }
			        {{addonId}} .video-box .video-item .cover-img {
                        width: 100%;
                        height: 100%;
                        object-fit: {{item_img_fit}};
			        }
			        {{addonId}} .video-box .video-item .cover-img-box .img-mark {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0);
                        transition: all 0.36s ease;
			        }
			        {{addonId}} .video-box .video-item .cover-img-box .icon {
			            position: absolute;
			            width: 100%;
                        top: -100%;
                        left: 0;
                        height: 100%;
                        transition: all 0.6s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
			        }
			        {{addonId}} .video-box .video-item .cover-img-box .icon img {
			            display: block;
			        }
			        {{ addonId }} .video-box .video-item .info-box {
			            display: flex;
			            align-items: center;
			            justify-content: space-between;
			        }
			        {{ addonId }} .video-box .video-item .info-box .text-box {
			            width: 100%;
			            padding-top: 15px;
                        padding-bottom: 15px;
                        padding-left: 0;
                        transition: all 0.6s ease 0s;
                        box-sizing: content-box;
			        }
			        {{ addonId }} .video-box .video-item .info-box .text-box .v-title {
			            font-size: 16px;
                        color: #535353;
                        margin-top: 10px;
			        }
			        {{ addonId }} .video-box .video-item .info-box .text-box .v-title_s {
			            font-size: 13px;
                        color: #b7b7b7;
                        margin-top: 6px;
			        }
			        {{ addonId }} .video-box .video-item .info-box .text-icon {
			            opacity: 0;
                        transition: all 0.6s ease 0s;
			        }
			        {{addonId}} .video-box .video-item:hover .cover-img-box .img-mark {
			            background: rgba(0, 0, 0, 0.47);
			        }
			        {{addonId}} .video-box .video-item:hover .cover-img-box .icon {
			            top: 0;
			        }
                    {{ addonId }} .video-box .video-item:hover .info-box .text-box {
			            padding-left: 10px;
                    }
                    {{ addonId }} .video-box .video-item:hover .info-box .text-icon {
                        padding-right: 10px;
                        opacity: 1;
                    }
                    @media (min-width: 768px) and (max-width: 991px) {
                        {{addonId}} .video-box .video-item {
                            <# var width = item_mg_sm / item_num_sm; #>
                            width: calc(100% / {{ item_num_sm }} - {{ item_mg_sm }}px + {{ width }}px);
                            margin-right: {{ item_mg_sm }}px;
                            margin-bottom: {{ item_mg_sm }}px;
                        }
                        {{addonId}} .video-box .video-item:nth-child({{ item_num }}n) {
                            margin-right: {{ item_mg_sm }}px;
                        }
                        {{addonId}} .video-box .video-item:nth-child({{ item_num_sm }}n) {
                            margin-right: 0;
                        }
                        {{addonId}} .video-box .video-item .cover-img-box {
			                height: {{ item_img_height_sm }}px;
			            }
                    }
                    @media (max-width: 767px) {
                        {{addonId}} .video-box .video-item {
                            <# var width = item_mg_xs / item_num_xs; #>
                            width: calc(100% / {{ item_num_xs }} - {{ item_mg_xs }}px + {{ width }}px);
                            margin-right: {{ item_mg_xs }}px;
                            margin-bottom: {{ item_mg_xs }}px;
                        }
                        {{addonId}} .video-box .video-item:nth-child({{ item_num }}n) {
                            margin-right: {{ item_mg_xs }}px;
                        }
                        {{addonId}} .video-box .video-item:nth-child({{ item_num_xs }}n) {
                            margin-right: 0;
                        }
                        {{addonId}} .video-box .video-item .cover-img-box {
			                height: {{ item_img_height_xs }}px;
			            }
                    }
                </style>
			    <div class="video-box">
			        <# _.each(jw_tab_item01, function(item, key){ #>
			        <div class="video-item">
			            <div class="cover-img-box">
			                <# if (item.cover_img) { #>
			                <img src=\'{{ item.cover_img }}\' class="cover-img" alt="">
			                <# } #>
			                <div class="img-mark"></div>
			                <div class="icon">
			                    <# if (video_icon) { #>
			                    <img src=\'{{ video_icon }}\' alt="">
			                <# } #>
                            </div>
                        </div>
                        <div class="info-box">
                            <div class="text-box">
                                <div class="v-title ellipsis">{{ item.title }}</div>
                                <div class="v-title_s ellipsis">{{ item.title_s }}</div>
                            </div>
                            <div class="text-icon">
			                    <# if (title_icon) { #>
                                <img src=\'{{ title_icon }}\' alt="">
			                    <# } #>
                            </div>
                        </div>
                    </div>
                    <# }); #>
                </div>
            			<# }else if(theme == "theme02"){
			    var jw_tab_item02 = data.jw_tab_item02 || [{
			        title: "手工精选优质茶树尖嫩芽",
			        title_s: "嫩度一般嫩度好的茶叶，符合外形要求（“光、扁、平、直”）但是不能仅从茸毛多少来判别嫩度，因各种茶的具体要求不一样，如极好的狮峰龙井是体表无茸毛的",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    },{
			        title: "讲述适当喝茶对身体的保健作用。",
			        title_s: "绿茶需冷藏保存，将茶叶密封后放入罐子内，放置冰箱冷藏保存；红茶常温密封保存；乌龙茶用铁罐、锡罐等双层盖容器常温储存；白茶需要用纸箱与铝箔袋密封保存；",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    },{
			        title: "品茶论道悟人生,把酒听禅观世态",
			        title_s: "人生就像一杯茶。第一口苦,第二口涩,第三口甜。回味一下,甘甜清香。平淡是它的本色,苦涩是它的历程,清甜是它的馈赠，细品人的一生，恰似品茶，看似苦涩，可香味尽在其中。一杯茶，品人生沉浮; 平常心，造万年世界。",
			        cover_img: "https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg",
			        video: "https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4"
			    }];
			    
			    let video_icon_size_item02=data.video_icon_size_item02 || 30;
			    let item_width_item02=(data.item_width_item02 && data.item_width_item02.md) || 28;
			    let item_width_item02_sm=(data.item_width_item02 && data.item_width_item02.sm) || 28;
			    let item_border_width_item02=(data.item_border_width_item02 && data.item_border_width_item02.md) || 4;
			    let item_border_width_item02_sm=(data.item_border_width_item02 && data.item_border_width_item02.sm) || 4;
			    let item_border_width_item02_xs=(data.item_border_width_item02 && data.item_border_width_item02.xs) || 9;
			    let item_video_title_font_size_item02=data.item_video_title_font_size_item02||16;
			    let item_video_title_color_item02=data.item_video_title_color_item02||"#2e2e2e";
			    let item_video_intro_font_size_item02=data.item_video_intro_font_size_item02||13;
			    let item_video_intro_color_item02=data.item_video_intro_color_item02||"#7d7d7d";
			    let item_video_intro_top_item02=data.item_video_intro_top_item02||6;
			    let item_page_dot_bg_item02=data.item_page_dot_bg_item02||"#d9d9d9";
			    let item_page_dot_border_item02=data.item_page_dot_border_item02||"#e6ab43";
			    let item_intro_width_item02 = (data.item_intro_width_item02 && data.item_intro_width_item02.md) || 32;
			    let item_intro_width_item02_sm = (data.item_intro_width_item02 && data.item_intro_width_item02.sm)|| 32;
			    let item_intro_width_item02_xs = (data.item_intro_width_item02 && data.item_intro_width_item02.xs) || 32;
			    
			    let item_intro_title_font_size_item02 = (data.item_intro_title_font_size_item02 && data.item_intro_title_font_size_item02.md) || 24;
			    let item_intro_title_font_size_item02_sm = (data.item_intro_title_font_size_item02 && data.item_intro_title_font_size_item02.sm)|| 24;
			    let item_intro_title_font_size_item02_xs = (data.item_intro_title_font_size_item02 && data.item_intro_title_font_size_item02.xs) || 13;
                
                let item_intro_title_color_item02= data.item_intro_title_color_item02 || "#404040";
                
                let item_intro_intro_item02 = (data.item_intro_intro_item02 && data.item_intro_intro_item02.md) || 41;
			    let item_intro_intro_item02_sm = (data.item_intro_intro_item02 && data.item_intro_intro_item02.sm) || 41;
			    let item_intro_intro_item02_xs = (data.item_intro_intro_item02 && data.item_intro_intro_item02.xs) || 20;
                
                let item_intro_intro_font_size_item02 = (data.item_intro_intro_font_size_item02 && data.item_intro_intro_font_size_item02.md) || 16;
			    let item_intro_intro_font_size_item02_sm = (data.item_intro_intro_font_size_item02 && data.item_intro_intro_font_size_item02.sm)|| 16;
			    let item_intro_intro_font_size_item02_xs = (data.item_intro_intro_font_size_item02 && data.item_intro_intro_font_size_item02.xs) || 13;
                
                let item_intro_intro_color_item02= data.item_intro_intro_color_item02 || "grey";
                let item_intro_height_item02= data.item_intro_height_item02 || 166;
                let item_border_color_item02 = data.item_border_color_item02 || "#fff";
                
                let item_right_right_item02= data.item_right_right_item02 || 10;
                let item_right_title_margin_item02= data.item_right_title_margin_item02 || "0px 0px 0px 28px";
                let item_right_letter_spacing_item02= data.item_right_letter_spacing_item02 || 4;
                let item_right_font_size_item02= data.item_right_font_size_item02 || 16;
                let item_right_line_height_item02= data.item_right_line_height_item02 || 2;
                let item_right_color_item02= data.item_right_color_item02 || "#737373";
                let item_right_active_color_item02= data.item_right_active_color_item02 || "#e6ab43";
                
                let item_bottom_item02= data.item_bottom_item02 || 50;
                let video_display_item02= data.video_display_item02 || "swiper";
                let item_num_site02_md = (data.item_num_site02 && data.item_num_site02.md) || 2;
                let item_num_site02_sm = (data.item_num_site02 && data.item_num_site02.sm) || 2;
                let item_num_site02_xs = (data.item_num_site02 && data.item_num_site02.xs) || 1;
                
                let item_mg_site02_md = (data.item_mg_site02 && data.item_mg_site02.md) || 275;
                let item_mg_site02_sm = (data.item_mg_site02 && data.item_mg_site02.sm) || 100;
                let item_mg_site02_xs = (data.item_mg_site02 && data.item_mg_site02.xs) || 0;
                
                let item_mg_bottom_site02_md = (data.item_mg_bottom_site02 && data.item_mg_bottom_site02.md) || 110;
                let item_mg_bottom_site02_sm = (data.item_mg_bottom_site02 && data.item_mg_bottom_site02.sm) || 90;
                let item_mg_bottom_site02_xs = (data.item_mg_bottom_site02 && data.item_mg_bottom_site02.xs) || 50;
                
                let item_intro_top_item02_md = (data.item_intro_top_item02 && data.item_intro_top_item02.md) || 60;
                let item_intro_top_item02_sm = (data.item_intro_top_item02 && data.item_intro_top_item02.sm) || 40;
                let item_intro_top_item02_xs = (data.item_intro_top_item02 && data.item_intro_top_item02.xs) || 20;
                
                let item_intro_title_hover_color_item02= data.item_intro_title_hover_color_item02 || "#e6ab43";

                let width_md = item_mg_site02_md / item_num_site02_md;
                let width_sm = item_mg_site02_sm / item_num_site02_sm;
                let width_xs = item_mg_site02_xs / item_num_site02_xs;
			#>
			
            <style>
                *{
                    padding: 0;
                    margin: 0;
                }
                {{addonId}} .ellipsis {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: block;
                }
                {{addonId}} .video-box {
                    overflow: hidden;
                    position: relative;
                }
                
                {{addonId}} .video-box .video-item .cover-img {
                    width: 100%;
                    height: 100%;
                    object-fit: {{item_img_fit}};
                }
                {{addonId}} .video-box .video-item .icon{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    color: #fff;
                    font-size: {{video_icon_size_item02}}px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
                }
                {{addonId}} .video-box .video-item:hover .cover-img-box .icon {
                    transform: scale(1.5);
                }
                {{addonId}} .video-box.mobile .swiper-slide{
                    padding: 0 8vw;
                }
                
                /*遮罩层*/
                {{addonId}} .video-show-cover {
                    position: fixed;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    display: none;
                }
                {{addonId}} .video-show-cover .video-cover {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    background: rgba(0, 0, 0, 0.6);
                }
                {{addonId}} .video-show-cover .video-content {
                    width: 960px;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%,-50%);
                    margin: auto;
                    z-index: 1001;
                    padding: 10px;
                    background: #fff;
                    border: 1px solid #fff;
                    transition: background 1s ease, border-color 1s ease;
                    box-sizing: content-box;
                }
                {{addonId}} .video-show-cover .video-content #video {
                    width: 100%;
                    height: 540px;
                }
                {{addonId}} .video-show-cover .video-content .text-box {
                    width: 100%;
                    height: auto;
                    padding: 15px 0 0;
                    transition: opacity 1s ease;
                    color: #fff;
                }
                {{addonId}} .video-show-cover .video-content .text-box .txt-title {
                    font-size: {{item_video_title_font_size_item02}}px;
                    color: {{item_video_title_color_item02}};
                }
                {{addonId}} .video-show-cover .video-content .text-box .txt-title-s {
                    color: {{item_video_intro_color_item02}};
                    font-size: {{item_video_intro_font_size_item02}}px;
                    line-height: 1.8;
                    margin-top: {{item_video_intro_top_item02}}px;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    max-height: 50px;
                }
                {{addonId}} .video-show-cover .btn-icon {
                    width: 48px;
                    height: 48px;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    z-index: 1002;
                    cursor: pointer;
                }
                {{addonId}} .video-show-cover .prv-icon {
                    left: 0;
                }
                {{addonId}} .video-show-cover .next-icon {
                    right: 0;
                }
                {{addonId}} .video-show-cover .btn-icon img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                {{addonId}} .video-box.mobile{
                    display: none;
                }
                {{addonId}} .video-box.pc .title,{{addonId}} .info-box .v-title{
                    font-size: {{item_intro_title_font_size_item02}}px;
                    color: {{item_intro_title_color_item02}};
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 1.2;
                }
                {{addonId}} .video-box.pc .title_s,{{addonId}} .info-box .v-title_s{
                    display: -webkit-box;
                    overflow: hidden;
                    max-height: 96px;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    font-size: {{item_intro_intro_font_size_item02}}px;
                    line-height: 2;
                    margin-top: {{item_intro_intro_item02}}px;
                    color: {{item_intro_intro_color_item02}};
                }
                @media (max-width: 767px) {
                    {{addonId}} .video-box{
                        display: block;
                    }
                    {{addonId}} .video-box.pc{
                        display: none;
                    }
                    {{addonId}} .video-box.mobile{
                        display: block;
                        padding-bottom: {{item_bottom_item02}}px;
                    }
                    {{addonId}} .video-box.pc .info{
                        width: {{item_intro_width_item02_xs}}%;
                    }
                    {{addonId}} .video-box .title{
                        font-size: {{item_intro_title_font_size_item02_xs}}px;
                        color: {{item_intro_title_color_item02}};
                    }
                    {{addonId}} .title_s{
                        max-height: 70px;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 3;
                        -webkit-box-orient: vertical;
                        line-height: 1.5;
                        margin-top: {{item_intro_intro_item02_xs}}px;
                        color: {{item_intro_intro_color_item02}};
                        font-size: {{item_intro_intro_font_size_item02_xs}}px;
                    }
                    {{addonId}} .swiper-pagination-bullet{
                        width: 6px;
                        height: 6px;
                        background: {{item_page_dot_bg_item02}};
                        opacity: 1;
                    }
                    {{addonId}} .swiper-pagination-bullet-active{
                        background: #fff;
                        border: 1px solid {{item_page_dot_border_item02}};
                    }
                    {{addonId}} .video-show-cover .video-content #video {
                        height: auto;
                    }
                    {{addonId}} .video-show-cover {
                        background: #000;
                    }
                    {{addonId}} .video-show-cover .video-content{
                        width: 100vw;
                        height: 33vh;
                        padding: 0;
                        background: #000;
                        border: none;
                    }
                    {{addonId}} .video-content .text-box{
                        display: none;
                    }
                    {{addonId}} .video-show-cover .video-content #video{
                        height: 100%;
                    }
                    {{addonId}} .btn-icon{
                        display: none;
                    }
                }
                <# if(video_display_item02=="swiper"){ #>
                    {{addonId}} .video-box .video-item {
                        width: {{item_width_item02}}vw;
                        height: {{item_width_item02}}vw;
                        overflow: hidden;
                        cursor: pointer;
                        background: #fff;
                        border-radius: 50%;
                        box-shadow: 0 0 100px 0 rgb(0, 0, 0, 0.05);
                        border: {{item_border_width_item02}}vw solid {{item_border_color_item02}};
                        margin: auto;
                        box-sizing: border-box;
                    }
                    {{addonId}} .video-box.pc .info{
                        width: {{item_intro_width_item02}}%;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        overflow: hidden;
                        height: {{item_intro_height_item02}}px;
                        cursor: default;
                        z-index: 50;
                    }
                    {{addonId}} .video-box.mobile .info{
                        margin-top: 22px;
                    }
                    {{addonId}} .video-box.mobile .info .title{
                        text-align: center;
                    }
                    {{addonId}} .video-box.pc .title {
                        text-align: right;
                        padding-left: 10px;
                    }
                    {{addonId}} .video-box.pc .title {
                        text-align: right;
                    }
                    {{addonId}} .title-control{
                        position: absolute;
                        top: 50%;
                        right: {{item_right_right_item02}}%;
                        transform: translateY(-50%);
                        writing-mode: vertical-lr;
                        z-index: 50;
                    }
                    {{addonId}} .title-control p{
                        margin: {{item_right_title_margin_item02}};
                        letter-spacing: {{item_right_letter_spacing_item02}}px;
                        font-size: {{item_right_font_size_item02}}px;
                        line-height: {{item_right_line_height_item02}};
                        display: inline-block;
                        cursor: pointer;
                        transition: all .3s ease-in-out 0s;
                        position: relative;
                    }
                    {{addonId}} .title-control p::before{
                        height: 0;
                        content: "";
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 1px;
                        background: {{item_right_active_color_item02}};
                        transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
                    }
                    {{addonId}} .title-control p.active a,{{addonId}} .title-control p:hover a{
                        color: {{item_right_active_color_item02}};
                    }
                    {{addonId}} .title-control p.active::before,{{addonId}} .title-control p:hover::before{
                        height: 100%;
                    }
                    {{addonId}} .title-control p a{
                        display: block;
                        width: 100%;
                        height: 100%;
                        color: {{item_right_color_item02}};
                        text-decoration: none;
                    }
                    
                    @media (min-width: 768px) and (max-width: 991px) {
                        {{addonId}} .video-box .video-item {
                            width: {{item_width_item02_sm}}vw;
                            height: {{item_width_item02_sm}}vw;
                            border: {{item_border_width_item02_sm}}vw solid {{item_border_color_item02}};
                        }
                        {{addonId}} .video-box.pc .info{
                            width: {{item_intro_width_item02_sm}}%;
                        }
                        {{addonId}} .video-show-cover .video-content #video {
                            height: auto;
                        }
                        {{addonId}} .video-box.pc .title{
                            font-size: {{item_intro_title_font_size_item02_sm}}px;
                        }
                        {{addonId}} .video-box.pc .title_s{
                            font-size: {{item_intro_intro_font_size_item02_sm}}px;
                            margin-top: {{item_intro_intro_item02_sm}}px;
                        }
                    }
                    @media (max-width: 767px) {
                        {{addonId}} .video-box .video-item {
                            width: 100%;
                            padding-top: 100%;
                            margin: auto;
                            position: relative;
                            border: none;
                            overflow: visible;
                        }
                        {{addonId}} .video-box .video-item .cover-img-box{
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                            border-radius: 50%;
                            overflow: hidden;
                            border: {{item_border_width_item02_xs}}vw solid {{item_border_color_item02}};
                        }
                    }
                <# }else if(video_display_item02=="list"){ #>
                    {{addonId}} .video-box {
                        display: flex;
                        flex-wrap: wrap;
                    }
                    {{addonId}} .video-box .video-item {
                        width: calc(100% / {{item_num_site02_md}} - {{item_mg_site02_md}}px + {{width_md}}px);
                        margin-right: {{item_mg_site02_md}}px;
                        margin-bottom: {{item_mg_bottom_site02_md}}px;
                        cursor: pointer;
                    }
                    {{addonId}} .video-box .video-item:nth-child({{item_num_site02_md}}n) {
                        margin-right: 0;
                    }
                    {{addonId}} .video-box .video-item:hover .v-title{
                        color: {{item_intro_title_hover_color_item02}};
                    }
                    {{addonId}} .video-box .video-item .cover-img-box{
                        width: 100%;
                        height: 0;
                        padding-top: 100%;
                        position: relative;
                        border-radius: 50%;
                        overflow: hidden;
                        box-sizing: content-box;
                    }
                    {{addonId}} .video-box .video-item .cover-img-box img{
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        margin: auto;
                    }
                    {{addonId}} .video-box .video-item .info-box{
                        margin-top: {{item_intro_top_item02_md}}px;
                        text-align: center;
                    }
                    @media (min-width: 768px) and (max-width: 991px) {
                        {{addonId}} .video-box .video-item {
                            width: calc(100% / {{item_num_site02_sm}} - {{item_mg_site02_sm}}px + {{width_sm}}px);
                            margin-right: {{item_mg_site02_sm}}px;
                            margin-bottom: {{item_mg_bottom_site02_sm}}px;
                        }
                        <# if(item_num_site02_md != item_num_site02_sm) { #>
                            {{addonId}} .video-box .video-item:nth-child({{item_num_site02_md}}n) {
                                margin-right: {{item_mg_site02_sm}}px;
                            }
                        <# } #>
                        {{addonId}} .video-box .video-item:nth-child({{item_num_site02_sm}}n) {
                            margin-right: 0;
                        }
                        {{addonId}} .video-show-cover .video-content #video {
                            height: auto;
                        }
                        {{addonId}} .video-show-cover .btn-icon {
                            width: 20px;
                            height: 24px;
                        }
                        {{addonId}} .video-box .video-item .info-box{
                            margin-top: {{item_intro_top_item02_sm}}px;
                        }
                        {{addonId}} .video-box .v-title{
                            font-size: {{item_intro_title_font_size_item02_sm}}px;
                        }
                        {{addonId}} .video-box .v-title_s{
                            font-size: {{item_intro_intro_font_size_item02_sm}}px;
                            margin-top: {{item_intro_intro_item02_sm}}px;
                        }
                    }
                    @media (max-width: 767px) {
                        {{addonId}} .video-box .video-item {
                            width: calc(100% / {{item_num_site02_xs}} - {{item_mg_site02_xs}}px + {{width_xs}}px);
                            margin-right: {{item_mg_site02_xs}}px;
                            margin-bottom: {{item_mg_bottom_site02_xs}}px;
                        }
                        {{addonId}} .video-box .video-item .info-box{
                            margin-top: {{item_intro_top_item02_xs}}px;
                            text-align: center;
                        }
                        <# if(item_num_site02_md != item_num_site02_xs) { #>
                            {{addonId}} .video-box .video-item:nth-child({{item_num_site02_md}}n) {
                                margin-right: {{item_mg_site02_xs}}px;
                            }
                        <# } #>
                        {{addonId}} .video-box .video-item:nth-child({{item_num_site02_xs}}n) {
                            margin-right: 0;
                        }
                        {{addonId}} .video-show-cover .video-content #video {
                            height: auto;
                        }
                        {{addonId}} .video-show-cover .btn-icon {
                            width: 20px;
                            height: 24px;
                        }
                        {{addonId}} .video-box .v-title{
                            font-size: {{item_intro_title_font_size_item02_xs}}px;
                        }
                        {{addonId}} .video-box .v-title_s{
                            font-size: {{item_intro_intro_font_size_item02_xs}}px;
                            margin-top: {{item_intro_intro_item02_xs}}px;
                        }
                    }
                <# } #>
            </style>
            <# if(data.video_display_item02=="swiper"){ #>
                <div class="video-box pc">
                    <# if(data.item_left_show_item02 == 1){ #>
                        <div class="info swiper-container">
                            <div class="swiper-wrapper">
                                <# _.each(jw_tab_item02, function(item, key){ #>
                                    <div class="swiper-slide swiper-no-swiping">
                                        <p class="title" style="line-height: 1">
                                            {{item.title}}
                                        </p>
                                        <p class="title_s">
                                            {{item.title_s}}
                                        </p>
                                    </div>
                                <# }) #>
                            </div>
                        </div>
                    <# } #>
                    <div class="video-item swiper-container">
                        <div class="swiper-wrapper">
                            <# _.each(jw_tab_item02, function(item, key){ #>
                                <div class="cover-img-box swiper-slide">
                                    <img style="{{!item.cover_img?\'display: none;\':\'\'}}" src=\'{{ item.cover_img }}\' alt="" class="cover-img">
                                    <i class="fa {{data.video_icon_item02||"fa-play"}} icon"></i>
                                </div>
                            <# }) #>
                        </div>
                    </div>
                    <# if(data.item_right_show_item02 == 1){ #>
                        <div class="title-control">
                            <# _.each(jw_tab_item02, function(item, key){ #>
                                <p class="ellipsis {{key==0?\'active\':\'\'}}" data-id="{{key}}"><a>{{item.title}}</a></p>
                            <# }) #>
                        </div>
                    <# } #>
                </div>
                <div class="video-box mobile swiper-container">
                    <div class="swiper-wrapper">
                        <# _.each(jw_tab_item02, function(item, key){ #>
                            <div class="swiper-slide">
                                <div class="video-item">
                                    <div class="cover-img-box">
                                        <img style="{{!item.cover_img?\'display: none;\':\'\'}}" src=\'{{item.cover_img}}\' alt="" class="cover-img">
                                        <i class="fa {{data.video_icon_item02||"fa-play"}} icon"></i>
                                    </div>
                                </div>
                                <div class="info">
                                    <div class="title">{{item.title}}</div>
                                    <div class="title_s">{{item.title_s}}</div>
                                </div>
                            </div>
                        <# }) #>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            <# }else{ #>
                <div class="video-box">
                    <# _.each(jw_tab_item02, function(item, key){ #>
                        <div class="video-item">
                            <div class="cover-img-box">
                                <# if (item.cover_img) { #>
                                    <img style="{{!item.cover_img?\'display: none;\':\'\'}}" src=\'{{item.cover_img}}\' class="cover-img" alt="">
                                <# } #>
                                <i class="icon fa {{data.video_icon_item02||"fa-play"}}"></i>
                            </div>
                            <div class="info-box">
                                <div class="text-box">
                                    <div class="v-title ellipsis">{{{item.title}}}</div>
                                    <div class="v-title_s">{{{item.title_s}}}</div>
                                </div>
                                <div class="text-icon"></div>
                            </div>
                        </div>
                    <# }) #>
                </div>
            <# } #>
        <# } #>
		';

        return $output;
    }
}
