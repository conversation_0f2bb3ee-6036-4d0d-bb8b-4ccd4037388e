<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');
$app = JFactory::getApplication();
$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'full_page',
        'title' => '全屏滚动网页-1',
        'desc' => '全屏滚动网页(支持更改文字/图片)',
        'category' => '全屏滚动网页',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '切换项配置'
                ),
                'slide_page' => array(
                    'type' => 'buttons',
                    'title' => '全屏每页配置（切换效果预览页查看）',
                    'std' => 'page01',
                    'values' => array(
                        array(
                            'label' => '第一页',
                            'value' => 'page01'
                        ),
                        array(
                            'label' => '第二页',
                            'value' => 'page02'
                        ),
                        array(
                            'label' => '第三页',
                            'value' => 'page03'
                        ),
                        array(
                            'label' => '第四页',
                            'value' => 'page04'
                        ),
                        array(
                            'label' => '第五页',
                            'value' => 'page05'
                        ),
                        array(
                            'label' => '第六页',
                            'value' => 'page06'
                        ),
                    ),
                    'tabs' => true,
                ),
                'page01_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_slider_video' => array(
                    'type' => 'media',
                    'format' => 'video',
                    'title' => '背景视频(mp4格式)',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211012/3df6aa5afa92f8c2320d38379007aa5b.mp4',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_slider_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '瓦轴集团-中国轴承从这里开始...',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_slider_desc' => array(
                    'type' => 'text',
                    'title' => '简介',
                    'std' => 'ZWZ - China\'s bearing startsfrom here...',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_index_page_id' => array(
                    'type' => 'select',
                    'title' => '进入首页链接页面',
                    'desc' => '点击进入首页需要跳转的站内链接页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_slider_content' => array(
                    'type' => 'editor',
                    'title' => '中间内容（进入首页部分下方）',
                    'std' => '瓦房店轴承产品：瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 |TWB轴承
								 友情链接：  瓦轴    ZWZ轴承     封闭母线槽     哈尔滨轴承   真空灌胶机  eja变送器    智能锁招商   HRB轴承     电子商务站   RTO     whl轴承   恒温恒湿试验箱价格 ',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_part01' => array(
                    'type' => 'separator',
                    'title' => '底部新闻配置',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_resource' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE_DESC'),
                    'values' => array(
                        'article' => JText::_('建站通'),
                        'k2' => JText::_('无'),
                    ),
                    'std' => 'article',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'depends' => array('resource' => 'article'),
                    'multiple' => true,
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_post_type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_ALL'),
                        'standard' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STANDARD'),
                        'audio' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_AUDIO'),
                        'video' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_VIDEO'),
                        'gallery' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_GALLERY'),
                        'link' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_LINK'),
                        'quote' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_QUOTE'),
                        'status' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STATUS'),
                    ),
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                        array('page01_resource', '=', 'article')
                    )
                ),
                'page01_include_subcat' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES_DESC'),
                    'values' => array(
                        1 => JText::_('COM_JWPAGEFACTORY_YES'),
                        0 => JText::_('COM_JWPAGEFACTORY_NO'),
                    ),
                    'std' => 1,
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(

                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),

                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),

                        'sortdesc' => JText::_('排序id倒序'),
                        'sortasc' => JText::_('排序id正序'),

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'sortdesc',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page01_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10',
                    'depends' => array(
                        array('slide_page', '=', 'page01'),
                    ),
                ),
                'page02_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/cfae18bc4ced7695fdbb90136385ef3f.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img01' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '图片一',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/b27c3e46e08f0dcec316a180bbf64d34.png',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img02' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '图片二',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/4d4fe59f37db8f7038a103c17ec4eff2.png',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img03' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '图片三',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/d90f5d3417da39918ed842da4350d317.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_slider_img04' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '图片四',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/4ce6f49ce79cc6e739c159913539bd79.png',
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page02_image_item' => array(
                    'title' => '切换图项目（预览查看切换效果）',
                    'attr' => array(
                        'slider_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '大图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211015/64274e83bbf3e9c2f3bcddf1c32b857e.png',
                        ),
                    ),
                    'std' => array(
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/64274e83bbf3e9c2f3bcddf1c32b857e.png',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/0380b9306ab6c11c78c29d95bf466cc9.png',
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/f99978701cbac2bcc2dea7b0de0cf681.png',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page02'),
                    ),
                ),
                'page03_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/09d9ca14da78c0b91996f534478f2597.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_part01' => array(
                    'type' => 'separator',
                    'title' => '切换图左侧部分配置',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_slider_title' => array(
                    'type' => 'text',
                    'title' => '切换图左侧部分标题',
                    'std' => '走进江轴',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_slider_desc' => array(
                    'type' => 'text',
                    'title' => '切换图左侧部分副标题',
                    'std' => 'WALK INTO JIANGZHOU',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_slider_content' => array(
                    'type' => 'editor',
                    'title' => '切换图左侧部分公司介绍文字',
                    'std' => '<p> 江苏江轴轴承，着重于全面承接瓦房店轴承集团江苏销售平台的管理工作，承担着代表瓦轴对江苏区域轴承市场的开发和管理。在江瓦轴承的努力下，各行业用户的需求能更快捷更高效地与瓦轴对接，提升了响应效率，让解决方案更具针对性，为客户带来更多的效益！</p>
										<p>公司常备库存7000万元以上，提供12000多个不同规格的轴承产品，在华东乃至全国具有显著的市场地位。产品广泛应用于航天、航空、铁路、风电、核电、矿山、汽车、船舶、海洋工程等涉及国计民生的重点行业和领域。</p>',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_index_page_id' => array(
                    'type' => 'select',
                    'title' => '了解详情链接页面',
                    'desc' => '点击了解详情需要跳转的站内链接页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_part02' => array(
                    'type' => 'separator',
                    'title' => '切换图配置',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_image_item' => array(
                    'title' => '切换图项目（预览查看切换效果）',
                    'attr' => array(
                        'slider_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '大图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211014/7e6a0e1bd250f285a43989c8d2b6dd15.png',
                        ),
                        'slider_img_s' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '小图',
                            'std' => 'https://oss.lcweb01.cn/joomla/20211014/8e7ae1c75789d7b5fea59fb3a2d082ab.png',
                        ),
                    ),
                    'std' => array(
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/7e6a0e1bd250f285a43989c8d2b6dd15.png',
                            'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/8e7ae1c75789d7b5fea59fb3a2d082ab.png'
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/7d19437a959c6fb7a29be0516e5c5c8d.jpg',
                            'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/addbb12cee6925111985a9eac848d71b.jpg'
                        ),
                        array(
                            'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/ffda657c37d2c27f9ae5bb170a3fea5e.jpg',
                            'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/7e596e363f6462e19ef3a6afcd402624.jpg'
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_part03' => array(
                    'type' => 'separator',
                    'title' => '底部配置',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_bot_title' => array(
                    'type' => 'text',
                    'title' => '底部标题',
                    'std' => '江轴轴承中心',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_bot_desc' => array(
                    'type' => 'text',
                    'title' => '底部副标题',
                    'std' => 'PRODUCTS',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page03_bot_content' => array(
                    'type' => 'editor',
                    'title' => '底部文字',
                    'std' => '瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承 | 瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承  | 瓦房店轴承风电系列 ',
                    'depends' => array(
                        array('slide_page', '=', 'page03'),
                    ),
                ),
                'page04_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/7d54100747c53974bbe108680e852f5a.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_slider_title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'std' => '轴承传动行业的优质方案提供者',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_slider_desc' => array(
                    'type' => 'text',
                    'title' => '副标题',
                    'std' => '库存充足、品种齐全、专业销售世界优质品质轴承',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_slider_content' => array(
                    'type' => 'editor',
                    'title' => '文字',
                    'std' => '<p>常备库存<span>7000万元</span>以上，为数十个行业客户提供<span>12000多种</span>不同规格的轴承</p>
									<p style="font-size: 14px;margin-top: 10px;">The company\'s standing stock is more than 70 million yuan, providing more than 12,000 products of different specifications for dozens of industry customers.</p>',
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page04_index_page_id' => array(
                    'type' => 'select',
                    'title' => '产品中心链接页面',
                    'desc' => '点击产品中心需要跳转的站内链接页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page04'),
                    ),
                ),
                'page05_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/78e56070c0b01583e60220dde00ce1f6.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_desc_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '标题介绍图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211014/37d95cf9379f31e27d5893f97e913850.png',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_ani_title01' => array(
                    'type' => 'text',
                    'title' => '标题一',
                    'std' => '货源稳定',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_ani_title02' => array(
                    'type' => 'text',
                    'title' => '标题二',
                    'std' => '库存充足',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_ani_title03' => array(
                    'type' => 'text',
                    'title' => '标题三',
                    'std' => '物流快捷',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_title_item' => array(
                    'title' => '产品介绍',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '描述',
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '瓦轴实力授权经销商',
                        ),
                        array(
                            'title' => '常备7000万元以上库存',
                        ),
                        array(
                            'title' => '20年服务品质 实力保证',
                        ),
                        array(
                            'title' => '提供一站式轴承技术支持',
                        ),
                    ),
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page05_goods_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '产品图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211014/0d47fca7f1e2f474bcb1c0c96c15842e.png',
                    'depends' => array(
                        array('slide_page', '=', 'page05'),
                    ),
                ),
                'page06_hide' => array(
                    'type' => 'checkbox',
                    'title' => '关闭当前页',
                    'std' => 0,
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_slider_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/cf664f5f0b11ea6fae1b02d04bead2b3.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_map_page_id' => array(
                    'type' => 'select',
                    'title' => '查看地图链接页面',
                    'desc' => '点击查看地图需要跳转的站内链接页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_message_page_id' => array(
                    'type' => 'select',
                    'title' => '在线留言链接页面',
                    'desc' => '点击在线留言需要跳转的站内链接页面',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_ewm_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '微信二维码图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211015/c87bd2547328cd4aa812aa4e6c1940fb.jpg',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_concat' => array(
                    'type' => 'editor',
                    'title' => '地址联系人信息',
                    'std' => '<p>地址：江苏省无锡市北塘区康桥丽景家园18-46号</p>
										<p>电话：15061858253<br />联系人：董经理</p>
										<p>电话：19895733877<br />联系人：董经理</p>',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_copyright' => array(
                    'type' => 'text',
                    'title' => '版权信息',
                    'std' => '江苏江瓦轴承有限公司无锡分公司',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_record' => array(
                    'type' => 'text',
                    'title' => '备案号',
                    'std' => '苏ICP备19016653号-2',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_record_link' => array(
                    'type' => 'text',
                    'title' => '备案号跳转链接',
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_bot_concat' => array(
                    'type' => 'editor',
                    'title' => '新闻上方内容',
                    'std' => '<p>企业愿景：瓦轴第一总代理   永居销售量榜首</p>
								<p>企业价值观：服务每一位客户，成就每一位员工</p>
								<p>企业精神：诚信  创新  协作  共赢</p>',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_part01' => array(
                    'type' => 'separator',
                    'title' => '底部新闻配置',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_detail_page_id' => array(
                    'type' => 'select',
                    'title' => '详情页模版',
                    'desc' => '显示文章详情页模版',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_resource' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLE_RESOURCE_DESC'),
                    'values' => array(
                        'article' => JText::_('建站通'),
                        'k2' => JText::_('无'),
                    ),
                    'std' => 'article',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'depends' => array('resource' => 'article'),
                    'multiple' => true,
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_post_type' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_DESC'),
                    'values' => array(
                        '' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_ALL'),
                        'standard' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STANDARD'),
                        'audio' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_AUDIO'),
                        'video' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_VIDEO'),
                        'gallery' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_GALLERY'),
                        'link' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_LINK'),
                        'quote' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_QUOTE'),
                        'status' => JText::_('COM_JWPAGEFACTORY_ADDON_POST_TYPE_STATUS'),
                    ),
                    'std' => '',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                        array('page06_resource', '=', 'article')
                    )
                ),
                'page06_include_subcat' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_INCLUDE_SUBCATEGORIES_DESC'),
                    'values' => array(
                        1 => JText::_('COM_JWPAGEFACTORY_YES'),
                        0 => JText::_('COM_JWPAGEFACTORY_NO'),
                    ),
                    'std' => 1,
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(

                        'latest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_OLDEST'),

                        'timedesc' => JText::_('添加时间降序'),
                        'timeasc' => JText::_('添加时间升序'),

                        'sortdesc' => JText::_('排序id倒序'),
                        'sortasc' => JText::_('排序id正序'),

                        'hits' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'alphabet_asc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_ASC'),
                        'alphabet_desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_ALPHABET_DESC'),
                        'random' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'sortdesc',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
                'page06_limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_LIMIT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIMIT_LIST_DESC'),
                    'std' => '10',
                    'depends' => array(
                        array('slide_page', '=', 'page06'),
                    ),
                ),
            )
        )
    )
);