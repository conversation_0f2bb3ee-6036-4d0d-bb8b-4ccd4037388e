<?php

defined('_JEXEC') or die('resticted aceess');

$app = JFactory::getApplication();

$input      = $app->input;
$layout_id  = $input->get('layout_id', '');
$site_id    = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
// print_r(JwAddonsConfig::addons());
JwAddonsConfig::addonConfig(
    array(
        'type'       => 'content',
        'addon_name' => 'banner_info',
        'title'      => JText::_('banner'),
        'desc'       => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category'   => '轮播',
        'attr'       => array(
            'general' => array(
                'site_id'                                 => array(
                    'std' => $site_id,
                ),
                'company_id'                              => array(
                    'std' => $company_id,
                ),
                'banner_info_type'=> array(
                    'type'   => 'select',
                    'title'  => '选择布局',
                    'desc'   => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                    ),
                    'std'    => 'type1',
                ),
                'title_type1' => array(
                    'type' => 'text',
                    'title' => JText::_('标题'),
                    'desc' => JText::_('标题'),
                    'std' => '百度百青藤',
                    'depends' => array(array('banner_info_type', '=', 'type1')),
                ),
                'content_type1' => array(
                    'type' => 'text',
                    'title' => JText::_('简介'),
                    'desc' => JText::_('简介'),
                    'std' => '百度系+百青藤资源，碎片化场景全覆盖为您拓展更多商机。',
                    'depends' => array(array('banner_info_type', '=', 'type1')),
                ),
                'big_img_pc_type1' => array(
                    'type' => 'media',
                    'title' => JText::_('pc背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/86084d2a99208a208e50c4dd861501a4.jpeg',
                    'depends' => array(array('banner_info_type', '=', 'type1')),
                ),
                'big_img_sj_type1' => array(
                    'type' => 'media',
                    'title' => JText::_('手机端背景图片'),
                    'desc' => JText::_('背景图片'),
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/1e87fedd8cc1065f5c6f614c76ce8a7e.jpeg',
                    'depends' => array(array('banner_info_type', '=', 'type1')),
                ),
            ),
        ),
    )
);
