<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);
$config = new JConfig();
$imgurl = $config->img_url;

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'cut_box',
        'title' => JText::_('切换图'),
        'desc' => JText::_(''),
        'category' => '图片',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '模块总体设置'
                ),
                'model_height' => array(
                    'type' => 'slider',
                    'title' => '模块高度',
                    'max' => 600,
                    'min' => 0,
                    'std' => '200'
                ),
                'model_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('模块内边距(格式：数字px)'),
                    'std' => '0px 10px 0px 10px',
                ),
                'is_animation' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启动画效果'),
                    'std' => 0,
                    'depends' => array(

                    ),
                ),
                'show_link' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启链接'),
                    'desc' => JText::_(''),
                    'std' => 0,
                    'depends' => array(
                    ),
                ),
                'open_type' => array(
                    'type' => 'select',
                    'title' => '选择链接打开类型',
                    'desc' => '',
                    'values' => array(
                        '_top' => '当前页打开',
                        '_blank' => '新页面打开',
                    ),
                    'std' => '_top',
                    'depends' => array(
                        array('show_link', '=', 1),
                    ),
                ),
                'link_type' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否站内页面链接'),
                    'std' => 0,
                    'depends' => array(
                        array('show_link', '=', 1),
                    ),
                ),
                'detail_page_id' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('show_link', '=', 1),
                        array('link_type', '=', 1),
                    )
                ),
                'link' => array(
                    'type' => 'media',
                    'format' => 'attachment',
                    'hide_preview' => true,
                    'title' => JText::_('链接'),
                    'desc' => JText::_(''),
                    'std' => '',
                    'depends' => array(
                        array('show_link', '=', 1),
                        array('link_type', '!=', 1),
                    ),
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '模块选项'
                ),
                'item' => array(
                    'type' => 'buttons',
                    'title' => '模块选项',
                    'std' => 'icon',
                    'values' => array(
                        array(
                            'label' => '图标',
                            'value' => 'icon'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'title'
                        ),
                        array(
                            'label' => '副标题',
                            'value' => 's_title'
                        ),
                    ),
                    'tabs' => true,
                ),
                'image_height' => array(
                    'type' => 'slider',
                    'title' => '图片高度',
                    'max' => 600,
                    'min' => 0,
                    'std' => '200',
                    'depends' => array(
                        array('item', '=', 'icon'),
                    ),
                ),
                'input_text' => array(
                    'type' => 'text',
                    'title' => JText::_('文本内容'),
                    'desc' => JText::_(''),
                    'std' => '我是文本内容',
                    'depends' => array(
                        array('item', '=', 'title'),
                    ),
                ),
                'second_con' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否开启副文本内容'),
                    'desc' => JText::_(''),
                    'std' => 0,
                    'depends' => array(
                        array('item', '=', 's_title'),
                    ),
                ),
                'second_con_top' => array(
                    'type' => 'slider',
                    'title' => '副标题距离标题的距离',
                    'max' => 600,
                    'min' => 0,
                    'std' => '20',
                    'depends' => array(
                        array('item', '=', 's_title'),
                        array('second_con', '=', 1),
                    ),
                ),
                'second_con_text' => array(
                    'type' => 'editor',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CONTENT'),
                    'std' => '我为你祈祷，为你奉献。整只鱼有一种孤栖和非孤栖的习性。我的心很痛，我的心很痛，我的心很痛，我的心很痛。非常聪明的人，这是不可能的。我为你祈祷，为你奉献。我的生命有三分之一的危险。所有的元素都是元素。人们常用刺鼻龙。',
                    'depends' => array(
                        array('item', '=', 's_title'),
                        array('second_con', '=', 1),
                    ),
                ),
                'status' => array(
                    'type' => 'buttons',
                    'title' => '插件状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                ),
                // 正常
                'image_t' => array(
                    'type' => 'media',
                    'title' => '当前图片',
                    'std' => $imgurl . 'picshu.png',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'normal'),
                    )
                ),
                'bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('模块背景颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'normal'),
                    )
                ),
                'bg_color_border' => array(
                    'type' => 'color',
                    'title' => JText::_('模块边框颜色'),
                    'std' => '#eeeeee',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'normal'),
                    )
                ),
                'font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('item', '=', 'title'),
                        array('status', '=', 'normal'),
                    )
                ),
                'font_size' => array(
                    'type' => 'slider',
                    'title' => '字体大小',
                    'max' => 600,
                    'min' => 12,
                    'std' => '16',
                    'depends' => array(
                        array('item', '=', 'title'),
                        array('status', '=', 'normal'),
                    )
                ),
                'title_top' => array(
                    'type' => 'slider',
                    'title' => '字体位置',
                    'max' => 600,
                    'std' => 0,
                    'depends' => array(
                        array('item', '=', 'title'),
                        array('status', '=', 'normal'),
                    )
                ),
                'second_con_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('副文本内容字体颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('item', '=', 's_title'),
                        array('status', '=', 'normal'),
                        array('second_con', '=', 1),
                    )
                ),
                'second_con_font_size' => array(
                    'type' => 'slider',
                    'title' => '副文本内容字体大小',
                    'max' => 600,
                    'min' => 12,
                    'std' => '16',
                    'depends' => array(
                        array('item', '=', 's_title'),
                        array('status', '=', 'normal'),
                        array('second_con', '=', 1),
                    )
                ),
                // 滑过
                'image_t_hover' => array(
                    'type' => 'media',
                    'title' => '鼠标滑入图片',
                    'std' => $imgurl . 'picshua.png',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'hover'),
                    )
                ),
                'bg_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标滑入模块背景颜色'),
                    'std' => '#187771',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'hover'),
                    )
                ),
                'bg_color_border_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标滑入模块边框颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('item', '=', 'icon'),
                        array('status', '=', 'hover'),
                    )
                ),
                'font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('鼠标滑入字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('item', '=', 'title'),
                        array('status', '=', 'hover'),
                    )
                ),
                'second_con_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('副文本内容鼠标滑入字体颜色'),
                    'std' => '#ffffff',
                    'depends' => array(
                        array('item', '=', 's_title'),
                        array('status', '=', 'hover'),
                        array('second_con', '=', 1),
                    )
                ),
            ),
        ),
    )
);
