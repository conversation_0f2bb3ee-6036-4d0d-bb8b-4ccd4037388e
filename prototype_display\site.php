<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonPrototype_display extends JwpagefactoryAddons
{

	public function render()
	{
		// 电脑
		$computerBg='/components/com_jwpagefactory/addons/prototype_display/assets/images/computer-bg.png';
		// 手机
		$phoneBg='/components/com_jwpagefactory/addons/prototype_display/assets/images/phone-bg.png';
		$settings = $this->addon->settings;
		$items=(isset($settings->image_item) && $settings->image_item) ? $settings->image_item : '';
		$price=(isset($settings->price) && $settings->price) ? $settings->price : 2400;
		$button_link=(isset($settings->button_link) && $settings->button_link) ? $settings->button_link : '';
		$output='
			<div class="template-box">
				<!-- 样机 -->
				<div class="template-box-left">
					<div class="computer">
						<img src="'.JURI::base(true).$computerBg.'" class="computer-img">
						<div class="computer-imgs">';
						foreach ($items as $key => $item){
							$output.='
								<img src=\''.$item->pc_image.'\' class="'.($key===0?'active':'').'">
							';
						}
						$output.='</div>
					</div>
					<div class="phone">
						<img src="'.JURI::base(true).$phoneBg.'" class="phone-img">
						<div class="phone-imgs">';
							foreach ($items as $key => $item){
								$output.='<img src=\''.$item->phone_image.'\' class="'.($key===0?'active':'').'">';
							}
                        $output.='</div>
					</div>
				</div>
				<!-- 列表 -->
				<div class="template-box-right">
					<div class="template-list">';
						foreach ($items as $key => $item){
							$output.='
								<div class="template-list-img" data-id="'.$key.'">
									<img src=\''.$item->pc_image.'\'>
								</div>
							';
						}
					$output.='</div>
					<div class="template-in">
						<span>
							适用PC+手机
						</span> 付费模板归属于第三方设计师，购买后无法退换！
					</div>
					<div class="template-price">
						<div class="template-prices">
							建站套餐:
							<div class="price">
								<span>
									￥
								</span>
								<div class="money number-font">
									'.$price.'
								</div>
								<span class="number-font">
									.00
								</span>
								<time>
									/年起
								</time>
							</div>
						</div>
						<a href=\''.$button_link.'\' target="_blank" class="station-Introduction-buttons">
							立即咨询
						</a>
					</div>
				</div>
			</div>
		';
		return $output;
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$prototypeId = '#jwpf-addon-' . $this->addon->id;
		$image_width = (isset($settings->image_width) && $settings->image_width) ? $settings->image_width : 45;
        $list_width = (isset($settings->list_width) && $settings->list_width) ? $settings->list_width : 50;
		$pc_image_top = (isset($settings->pc_image_top) && $settings->pc_image_top) ? $settings->pc_image_top : 4.2;
		$pc_image_left = (isset($settings->pc_image_left) && $settings->pc_image_left) ? $settings->pc_image_left : 11.7;
		$mobile_image_top = (isset($settings->mobile_image_top) && $settings->mobile_image_top) ? $settings->mobile_image_top : 5.8;
		$mobile_image_left = (isset($settings->mobile_image_left) && $settings->mobile_image_left) ? $settings->mobile_image_left : 6;
		$price_color = (isset($settings->price_color) && $settings->price_color) ? $settings->price_color : '#ba1b16';
		$price_font_size = (isset($settings->price_font_size) && $settings->price_font_size) ? $settings->price_font_size : 32;
		$button_background = (isset($settings->button_background) && $settings->button_background) ? $settings->button_background : '#ba1b16';
		$button_color = (isset($settings->button_color) && $settings->button_color) ? $settings->button_color : '#fff';
		$button_hover_background = (isset($settings->button_hover_background) && $settings->button_hover_background) ? $settings->button_hover_background : '#ba1b168f';
		$button_hover_color = (isset($settings->button_hover_color) && $settings->button_hover_color) ? $settings->button_hover_color : '#fff';
		$output='
			/*样机*/
			'.$prototypeId.' .template-box{
				height: 556px;
			}
			'.$prototypeId.' .template-box-left{
				position: relative;
				width: '.$image_width.'%;
				float: left;
				height: 100%;
			}
			'.$prototypeId.' .computer{
				width: 100%;
				position: relative;
			}
			'.$prototypeId.' .computer-img{
				width: 100%;
				object-fit: cover;
			}
			'.$prototypeId.' .computer-imgs{
				position: absolute;
				top: '.$pc_image_top.'%;
				left: '.$pc_image_left.'%;
				width: 77%;
				overflow: hidden;
			}
			'.$prototypeId.' .computer-imgs img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				display: none;
			}
			'.$prototypeId.' .computer-imgs img.active {
				display: block;
			}
			'.$prototypeId.' .phone {
				position: absolute;
				right: 0;
				bottom: 0;
			}
			'.$prototypeId.' .phone .phone-img {
				object-fit: cover;
			}
			'.$prototypeId.' .phone-imgs {
				position: absolute;
				top: '.$mobile_image_top.'%;
				left: '.$mobile_image_left.'%;
				box-sizing: border-box;
				width: 77%;
				height: 85.7%;
				border-radius: 20px;
				border-top-left-radius: 0;
				border-top-right-radius: 0;
				overflow: hidden;
			}
			'.$prototypeId.' .phone-imgs img {
				width: 100%;
				height: 100%;
				display: none;
			}
			'.$prototypeId.' .phone-imgs img.active {
				display: block;
			}
			/*列表*/
			'.$prototypeId.' .template-box-right {
				float: right;
				width: '.$list_width.'%;
			}
			'.$prototypeId.' .template-list {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
			}
			'.$prototypeId.' .template-list-img {
				width: 48%;
				border: 3px solid #dbdbdb;
				margin-bottom: 20px;
				cursor: pointer;
				object-fit: cover;
			}
			'.$prototypeId.' .template-list-img img {
				width: 100%;
				object-fit: cover;
				pointer-events: none;
			}
			'.$prototypeId.' .template-in {
				color: #969494;
				font-size: 14px;
			}
			'.$prototypeId.' .template-in span {
				font-size: 16px;
				margin-right: 10px;
				color: #454545;
			}
			'.$prototypeId.' .template-price {
				font-size: 16px;
				color: #454545;
				margin-top: 8px;
			}
			'.$prototypeId.' .template-price::after {
				content: "";
				display: block;
				clear: both;
			}
			'.$prototypeId.' .template-prices {
				padding-top: 0;
				display: flex;
				align-items: center;
				float: left;
			}
			'.$prototypeId.' .template-prices .price {
				display: flex;
				align-items: center;
				align-items: baseline;
				-webkit-animation: bounce-down 1.5s linear infinite;
				animation: bounce-down 1.5s linear infinite;
			}
			@keyframes bounce-down{
				25% {
					transform: translateY(-5px);
				}
				50%, 100% {
					transform: translateY(0);
				}
				75% {
					transform: translateY(0);
				}
			}
			'.$prototypeId.' .template-prices .price span {
				color: #ba1b16;
			}
			'.$prototypeId.' .template-prices .price .money {
				color: '.$price_color.';
				font-size: '.$price_font_size.'px;
				line-height: 1.8;
			}
			'.$prototypeId.' .template-prices .price time {
				margin-right: 20px;
				color: #333333;
			}
			'.$prototypeId.' .station-Introduction-buttons {
				float: right;
				display: block;
				width: 157px;
				height: 57px;
				background: '.$button_background.';
				font-size: 18px;
				color: '.$button_color.';
				text-align: center;
				line-height: 57px;
				text-decoration: none;
			}	
			'.$prototypeId.' .station-Introduction-buttons:hover {
				background: '.$button_hover_background.';
				color: '.$button_hover_color.';
			}	
			@media (max-width: 991px) and (min-width: 768px){
				'.$prototypeId.' .template-box-left{
					width: 100%;
				}
			}
			@media (max-width: 767px){
				'.$prototypeId.' .template-box-left{
					width: 100%;
					float: none;
					height: auto;
				}
				'.$prototypeId.' .phone img{
					width: 75px;
				}
				'.$prototypeId.' .template-list {
					width: 94%;
					margin: 0 auto;
					height: auto;
				}
				'.$prototypeId.' .template-list-img{
					width: 48%;
					height: 90px;
				}
				'.$prototypeId.' .station-Introduction-buttons,
				'.$prototypeId.' .template-prices{
					float: none;
				}
				'.$prototypeId.' .station-Introduction-buttons{
					width: 100%;
				}
			}	
		';
		return $output;
	}

	public function js()
	{
		$prototypeId = '#jwpf-addon-' . $this->addon->id;
		$output='jQuery(document).ready(function($){
			$("'.$prototypeId.' .template-list").click(function(e){
				let id=$(e.target).attr("data-id");
				if(id){
					$($(".computer-imgs img")[id]).addClass("active").siblings().removeClass("active");
					$($(".phone-imgs img")[id]).addClass("active").siblings().removeClass("active");
				}
			})
		})';
		return $output;
	}

	public static function getTemplate()
	{
		// 电脑
		$computerBg='/components/com_jwpagefactory/addons/prototype_display/assets/images/computer-bg.png';
		// 手机
		$phoneBg='/components/com_jwpagefactory/addons/prototype_display/assets/images/phone-bg.png';

		$output='
			<# 
				let price = (!_.isEmpty(data.price) && data.price) ? data.price : 2400;
				let button_link = (!_.isEmpty(data.button_link) && data.button_link) ? data.button_link : "";
			#>
			<div class="tips">本图片仅为布局样式，请在预览页面中查看该插件点击效果</div>
			<div class="template-box">
				<!-- 样机 -->
				<div class="template-box-left">
					<div class="computer">
						<img src="'.JURI::base(true).$computerBg.'" class="computer-img">
						<div class="computer-imgs">
							<#  _.each(data.image_item, function(image, key){ #>
								<img src=\'{{image.pc_image}}\' class="{{key===0?"active":""}}">
							<# }) #>
						</div>
					</div>
					<div class="phone">
						<img src="'.JURI::base(true).$phoneBg.'" class="phone-img">
						<div class="phone-imgs">
							<#  _.each(data.image_item, function(image, key){ #>
                            	<img src=\'{{image.phone_image}}\' class="{{key===0?"active":""}}">
							<# }) #>
                        </div>
					</div>
				</div>
				<!-- 列表 -->
				<div class="template-box-right">
					<div class="template-list">
						<#  _.each(data.image_item, function(image, key){ #>
							<div class="template-list-img">
								<img src=\'{{image.pc_image}}\'>
							</div>
						<# }) #>
					<div class="template-in">
						<span>
							适用PC+手机
						</span> 付费模板归属于第三方设计师，购买后无法退换！
					</div>
					<div class="template-price">
						<div class="template-prices">
							建站套餐:
							<div class="price">
								<span>
									￥
								</span>
								<div class="money number-font">
									{{price}}
								</div>
								<span class="number-font">
									.00
								</span>
								<time>
									/年起
								</time>
							</div>
						</div>
						<a href=\'{{button_link}}\' target="_blank" class="station-Introduction-buttons">
							立即咨询
						</a>
					</div>
				</div>
			</div>
		';

		return self::staticCss().$output;
	}

	public static function staticCss()
	{
		$output='
		<# 
			let addon_id = "#jwpf-addon-" + data.id;
			let image_width = (!_.isEmpty(data.image_width) && data.image_width) ? data.image_width : 45;
			let list_width = (!_.isEmpty(data.list_width) && data.list_width) ? data.list_width : 53;
			let pc_image_top = (!_.isEmpty(data.pc_image_top) && data.pc_image_top) ? data.pc_image_top : 4.2;
			let pc_image_left = (!_.isEmpty(data.pc_image_left) && data.pc_image_left) ? data.pc_image_left : 11.7;
			let mobile_image_top = (!_.isEmpty(data.mobile_image_top) && data.mobile_image_top) ? data.mobile_image_top : 5.8;
			let mobile_image_left = (!_.isEmpty(data.mobile_image_left) && data.mobile_image_left) ? data.mobile_image_left : 6;
			let price_color = (!_.isEmpty(data.price_color) && data.price_color) ? data.price_color : "#ba1b16";
			let price_font_size = (!_.isEmpty(data.price_font_size) && data.price_font_size) ? data.price_font_size : 32;
			let button_background = (!_.isEmpty(data.button_background) && data.button_background) ? data.button_background : "#ba1b16";
			let button_color = (!_.isEmpty(data.button_color) && data.button_color) ? data.button_color : "#fff";
			let button_hover_background = (!_.isEmpty(data.button_hover_background) && data.button_hover_background) ? data.button_hover_background : "#ba1b168f";
			let button_hover_color = (!_.isEmpty(data.button_hover_color) && data.button_hover_color) ? data.button_hover_color : "#fff";
		#>
			<style>
			    {{addon_id}} .tips{
                    height: 40px;
                    line-height: 40px;
                    margin-bottom: 30px;
                    background: rgba(255,141,115,0.88);
                    box-sizing: border-box;
                    padding: 0 10px;
                }
				/*样机*/
				{{addon_id}} .template-box{
					height: 556px;
				}
				{{addon_id}} .template-box-left{
					position: relative;
					width: {{image_width}}%;
					float: left;
					height: 100%;
				}
				{{addon_id}} .computer{
					width: 100%;
					position: relative;
				}
				{{addon_id}} .computer-img{
					width: 100%;
					object-fit: cover;
				}
				{{addon_id}} .computer-imgs{
					position: absolute;
					top: {{pc_image_top}}%;
					left: {{pc_image_left}}%;
					width: 77%;
					overflow: hidden;
				}
				{{addon_id}} .computer-imgs img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					display: none;
				}
				{{addon_id}} .computer-imgs img.active{
					display: block;
				}
				{{addon_id}} .phone {
					position: absolute;
					right: 0;
					bottom: 0;
				}
				{{addon_id}} .phone .phone-img {
					object-fit: cover;
				}
				{{addon_id}} .phone-imgs {
					position: absolute;
					top: {{mobile_image_top}}%;
					left: {{mobile_image_left}}%;
					box-sizing: border-box;
					width: 77%;
					height: 85.7%;
					border-radius: 20px;
					border-top-left-radius: 0;
					border-top-right-radius: 0;
					overflow: hidden;
				}
				{{addon_id}} .phone-imgs img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					display: none;
				}
				{{addon_id}} .phone-imgs img.active{
					display: block;
				}
				/*列表*/
				{{addon_id}} .template-box-right {
					width: {{list_width}}%;
					float: right;
				}
				{{addon_id}} .template-list {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;
				}
				{{addon_id}} .template-list-img {
					width: 48%;
					border: 3px solid #dbdbdb;
					margin-bottom: 20px;
					cursor: pointer;
					object-fit: cover;
				}
				{{addon_id}} .template-list-img img {
					width: 100%;
					object-fit: cover;
					height: 100%;
				}
				{{addon_id}} .template-in {
					color: #969494;
					font-size: 14px;
				}
				{{addon_id}} .template-in span {
					font-size: 16px;
					margin-right: 10px;
					color: #454545;
				}
				{{addon_id}} .template-price {
					font-size: 16px;
					color: #454545;
					margin-top: 8px;
					width: 100%;
				}
				{{addon_id}} .template-price::after {
					content: "";
					display: block;
					clear: both;
				}
				{{addon_id}} .template-prices {
					padding-top: 0;
					display: flex;
					align-items: center;
					float: left;
				}
				{{addon_id}} .template-prices .price {
					display: flex;
					align-items: center;
					align-items: baseline;
					-webkit-animation: bounce-down 1.5s linear infinite;
					animation: bounce-down 1.5s linear infinite;
				}
				@keyframes bounce-down{
					25% {
						transform: translateY(-5px);
					}
					50%, 100% {
						transform: translateY(0);
					}
					75% {
						transform: translateY(0);
					}
				}
				{{addon_id}} .template-prices .price span {
					color: {{price_color}};
				}
				{{addon_id}} .template-prices .price .money {
					color: {{price_color}};
					font-size: {{price_font_size}}px;
					line-height: 1.8;
				}
				{{addon_id}} .template-prices .price time {
					margin-right: 20px;
					color: #333333;
				}
				{{addon_id}} .station-Introduction-buttons {
					float: right;
					display: block;
					width: 157px;
					height: 57px;
					background: {{button_background}};
					font-size: 18px;
					color: {{button_color}};
					text-align: center;
					line-height: 57px;
					text-decoration: none;
				}	
				{{addon_id}} .station-Introduction-buttons:hover{
					background: {{button_hover_background}};
					color: {{button_hover_color}};
				}
				@media (max-width: 991px) and (min-width: 768px){
					{{addon_id}} .template-box-left{
						width: 100%;
					}
				}
				@media (max-width: 767px){
					{{addon_id}} .template-box-left{
						width: 100%;
						float: none;
						height: auto;
					}
					{{addon_id}} .phone img{
						width: 75px;
					}
					{{addon_id}} .template-list {
						width: 94%;
						margin: 0 auto;
						height: auto;
					}
					{{addon_id}} .template-list-img{
						width: 48%;
						height: 90px;
					}
					{{addon_id}} .station-Introduction-buttons,
					{{addon_id}} .template-prices{
						float: none;
					}
					{{addon_id}} .station-Introduction-buttons{
						width: 100%;
					}
				}			
			</style>
		';
		return $output;
	}
}