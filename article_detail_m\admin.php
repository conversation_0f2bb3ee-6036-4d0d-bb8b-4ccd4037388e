<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(

    array(
        'type' => 'general', //插件
        'addon_name' => 'article_detail_m',
        'title' => '自定义文章详情',
        'desc' => '',
        'category' => '文章', //插件分组
        'attr' => array(
            //配置项主体
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                //【基本】选项卡
                // 动态生成的配置项
//                'theme' => array(
//                    'type' => 'select',
//                    'title' => '选择布局',
//                    'values' => array(
//                        'site01' => '布局1',
//                    ),
//                    'std' => 'site01'
//                ),
                'menu_bg_color' => array(
                    'type' => 'color',
                    'title' => '菜单背景颜色',
                    'desc' => '',
                    'std' => '#257d17',
                ),
                'menu_bg_borderColor' => array(
                    'type' => 'color',
                    'title' => '菜单边框颜色',
                    'desc' => '',
                    'std' => '#86c260',
                ),
                'menu_bg_fontColor' => array(
                    'type' => 'color',
                    'title' => '菜单文字颜色',
                    'desc' => '',
                    'std' => '#fff',
                ),
                'content_bg' => array(
                    'type' => 'media',
                    'title' => '大背景图',
                    'desc' => '整个页面的背景图',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210804/9800279eb39ba5445d228b1b38ec77b3.jpg',
                ),
                'content_text' => array(
                    'type' => 'text',
                    'title' => '名字下方文字',
                    'desc' => '',
                    'std' => '之墓',
                ),
                'content_text_top' => array(
                    'type' => 'slider',
                    'title' => '中间文字上边距(百分比)',
                    'max' => 100,
                    'min' => 0,
                    'std' => 24
                ),

//                'select1615452416171' => array(
//                    'type' => 'select',
//                    'title' => '标题布局',
//                    'desc' => '',
//                    'std' => 'left',
//                    'values' => array(
//                        'left' => '居左',
//                        'center' => '居中',
//                    ),
//                ),
//                'color1615452944411' => array(
//                    'type' => 'color',
//                    'title' => '标题颜色',
//                    'desc' => '',
//                    'std' => '#333333',
//                ),
//                'font_size' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('标题字体大小'),
//                    'max' => 100,
//                    'min' => 12,
//                    'std' => '28'
//                ),
//                'content_top' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('标题与文章距离'),
//                    'max' => 50,
//                    'min' => 12,
//                    'std' => '15'
//                ),
//                'font_size_date' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('日期字体大小'),
//                    'max' => 100,
//                    'min' => 10,
//                    'std' => '14',
//                ),
//                'color1615453171634' => array(
//                    'type' => 'color',
//                    'title' => '时间颜色',
//                    'desc' => '',
//                    'std' => '#888888',
//                ),
//                'font_size_fl' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('分类字体大小'),
//                    'max' => 100,
//                    'min' => 10,
//                    'std' => '14',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'color1615453171634fl' => array(
//                    'type' => 'color',
//                    'title' => '分类颜色',
//                    'desc' => '',
//                    'std' => '#888888',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'color1615453171634f' => array(
//	                'type' => 'color',
//	                'title' => '阅读颜色',
//	                'desc' => '',
//	                'std' => '#888888',
//	                'depends' => array(
//		                array('theme', '=', 'site02'),
//	                ),
//                ),
//                'd_font_posi' => array(
//                    'type' => 'select',
//                    'title' => '日期和分类位置',
//                    'values' => array(
//                        'start' => '左',
//                        'center' => '中',
//                        'flex-end' => '右',
//                    ),
//                    'std' => 'site01',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'd_margin_right' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('日期分类间距'),
//                    'max' => 600,
//                    'min' => 10,
//                    'std' => '20',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'd_mtop' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('日期分类上间距'),
//                    'max' => 600,
//                    'min' => 10,
//                    'std' => '10',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'd_mbottom' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('日期分类下间距'),
//                    'max' => 600,
//                    'min' => 10,
//                    'std' => '10',
//                    'depends' => array(
//                        array('theme', '=', 'site02'),
//                    ),
//                ),
//                'pageState' => array(
//					'type' => 'buttons',
//					'title' => '翻页状态',
//					'std' => 'normal',
//					'values' => array(
//						array(
//							'label' => '正常',
//							'value' => 'normal'
//						),
//						array(
//							'label' => '移入',
//							'value' => 'hover'
//						),
//					),
//					'tabs' => true
//				),
//                'pageColor' => array(
//                    'type' => 'color',
//                    'title' => '正常翻页文字颜色',
//                    'desc' => '',
//                    'std' => '#1237ce',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'normal')
//                    ),
//                ),
//                'pageBorderColor' => array(
//                    'type' => 'color',
//                    'title' => '正常翻页边框颜色',
//                    'desc' => '',
//                    'std' => '#1237ce',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'normal')
//                    ),
//                ),
//                'pageBgColor' => array(
//                    'type' => 'color',
//                    'title' => '正常翻页背景颜色',
//                    'desc' => '',
//                    'std' => '#fff',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'normal')
//                    ),
//                ),
//                'pageColorhover' => array(
//                    'type' => 'color',
//                    'title' => '移入翻页文字颜色',
//                    'desc' => '',
//                    'std' => '#1237ce',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'hover')
//                    ),
//                ),
//                'pageBorderColorhover' => array(
//                    'type' => 'color',
//                    'title' => '移入翻页边框颜色',
//                    'desc' => '',
//                    'std' => '#1237ce',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'hover')
//                    ),
//                ),
//                'pageBgColorhover' => array(
//                    'type' => 'color',
//                    'title' => '移入翻页背景颜色',
//                    'desc' => '',
//                    'std' => '#fff',
//                    'depends' => array(
//                        array( 'pageState' ,'=', 'hover')
//                    ),
//                ),
//                'page_button' => array(
//	                'type' => 'checkbox',
//	                'title' => '翻页按钮是否关闭',
//	                'std' => 0
//                ),
            ),
        ),
    )
);