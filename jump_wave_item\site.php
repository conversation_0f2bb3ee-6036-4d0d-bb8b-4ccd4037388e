<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonJump_wave_item extends JwpagefactoryAddons
{

	public function render()
	{

        $addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
        
        $tdbj = (isset($settings->tdbj) && $settings->tdbj) ? $settings->tdbj : 'https://oss.lcweb01.cn/joomla/20220630/5a8d93ae54853835ae38029c26d1c2c5.jpg';
        $tdtitle = (isset($settings->tdtitle) && $settings->tdtitle) ? $settings->tdtitle : '新增趣味助学工具 灵活开展线上教学';
        $tdintro = (isset($settings->tdintro) && $settings->tdintro) ? $settings->tdintro : '激励学员自发学习，提升学员完课率';
        

		$output .= ' <style>';
        $output .= $addonId . ' .web_section7{height:670px;background-size:cover;background-position:center;background-repeat:no-repeat;background-image:url('.$tdbj.')}';
        $output .= $addonId . ' .container{width:1300px;margin:0 auto}';
        $output .= $addonId . ' .web_title1{text-align:center;padding:5rem 0 5rem}';
        $output .= $addonId . ' .title{max-width:100%;overflow:unset;text-align:left;padding:0}';
        $output .= $addonId . ' .web_title1 .title .web_label1{margin-right:-10px;margin-top:-4px}';
        $output .= $addonId . ' .web_title1 .title img{height:33px}';
        $output .= $addonId . ' .web_title2 .title p,.web_title2 .txt{color:#fff}';
        $output .= $addonId . ' .web_title1 .title p{color:#2a2a2a;font-size:36px;display:flex;position:relative;margin-top:0;line-height:inherit}';
        $output .= $addonId . ' .web_title1 .title{display:flex;justify-content:center;margin-bottom:12px}';
        $output .= $addonId . ' .web_title1 .title .web_label2{margin-top:24px;margin-left:-8px}';
        $output .= $addonId . ' .web_title1 .title img{height:33px}';
        $output .= $addonId . ' .web_title2 .title p,.web_title2 .txt{color:#fff}';
        $output .= $addonId . ' .web_title1 .txt{color:#676c7f;font-size:20px}';
        $output .= $addonId . ' .web_xd1{margin-top:0px}';
        $output .= $addonId . ' .web_xd_list ul{position:relative;height:310px}';
        $output .= $addonId . ' .web_xd_list ul li{position:absolute;display:flex;flex-direction:column;align-items:center;width:90px;animation:arrow 3s -0.5s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li h2{border-radius:50%;width:90px;height:90px;background:#fff;box-shadow:0 0 8px 0px rgba(255,255,255,.4);box-sizing:content-box;margin:0}';
        $output .= $addonId . ' .web_xd_list ul li p{color:#fff;font-size:20px;margin-top:15px}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(2n){top:auto;bottom:0}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(2){left:236px;animation:arrow 3.4s -.8s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(3){left:474px;animation:arrow 2.8s -1.2s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(4){left:712px;animation:arrow 3.6s -.5s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(5){left:auto;right:236px;animation:arrow 3s -.8s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(6){left:auto;right:0;animation:arrow 2.6s -1.5s linear infinite}';
        $output .= ' @keyframes arrow{0%{transform:translateY(0)}50%{transform:translateY(-20px)}100%{transform:translateY(0)}}';
        $output .= ' @media only screen and (max-width:1023px){';
        $output .= $addonId . ' .web_section7 {width: 100%;}';
        $output .= $addonId . ' .container {width: 100%;margin: 0 auto;}';
        $output .= $addonId . ' .web_title1 .title p{color:#fff;font-size:24px;display:flex;position:relative;margin-top:0;line-height:inherit}';
           $output .= $addonId . ' .web_xd_list ul li h2{border-radius:50%;width:60px;height:60px;background:#fff;box-shadow:0 0 8px 0px rgba(255,255,255,.4);box-sizing:content-box;margin:0}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(1){left:0.3rem;bottom:13rem;animation:arrow 3.4s -.8s linear infinite}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(2){left:3rem;bottom:5rem;animation:arrow 3.4s -.8s linear infinite;}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(3){left:8rem;bottom:13rem;animation:arrow 2.8s -1.2s linear infinite;}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(4){left:11rem;bottom: 5rem;animation:arrow 3.6s -.5s linear infinite;}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(5){left:auto;right: 4rem;bottom: 13rem;animation:arrow 3s -.8s linear infinite;}';
        $output .= $addonId . ' .web_xd_list ul li:nth-child(6){left:auto;right:1rem;bottom: 5rem;animation:arrow 2.6s -1.5s linear infinite;}';
        $output .=  ' }';
        $output .= ' </style>';
        $output .= '<div class="web_section7">';
        $output .= '  <div class="container">';
        $output .= '    <div class="web_con7">';
        $output .= '      <div class="web_title1 web_title2">';
        $output .= '        <div class="title"> <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_label1.png" class="web_label1">';
        $output .= '          <p>'.$tdtitle.'</p>';
        $output .= '          <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_label2.png" class="web_label2"> </div>';
        $output .= '        <div class="txt"> '.$tdintro.' </div>';
        $output .= '      </div>';
        $output .= '      <div class="web_xd1">';
        $output .= '        <div class="web_xd_list">';
        $output .= '          <ul>';


            $jw_tab_item = (isset($settings->jw_tab_item) && $settings->jw_tab_item) ? $settings->jw_tab_item : array(
                array(
                    'title' => '用户分群',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/88135b1101302330625816a2187fe8ea.png',
                ),
                array(
                    'title' => '表单问券',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/05dc53fa375ad0ddf6efb32d42a25235.png',
                ),
                array(
                    'title' => '在线打卡',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/78981796509e684eae48af1eec8bb6d5.png',
                ),
                array(
                    'title' => '排课表',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/9b4ab3600c094c4da3802706fd26400c.png',
                ),
                array(
                    'title' => '模拟考试',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/9f752976715ecc8c068b263ad5b093ac.png',
                ),
                array(
                    'title' => '营销游戏',
                    'img' => 'https://oss.lcweb01.cn/joomla/20220630/70536711526855b3a4b8a380598c01a6.png',
                ),
            );

            if (isset($jw_tab_item) && !empty($jw_tab_item)) {
                foreach ($jw_tab_item as $item_key => $tab_item) {
                    $output .= '            <li>';
                    $output .= '              <h2><img src="'.$tab_item['img'].'"></h2>';
                    $output .= '              <p>'.$tab_item['title'].'</p>';
                    $output .= '            </li>';
                }
            }
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d1.png"></h2>';
        // $output .= '              <p>用户分群</p>';
        // $output .= '            </li>';
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d2.png"></h2>';
        // $output .= '              <p>表单问券</p>';
        // $output .= '            </li>';
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d3.png"></h2>';
        // $output .= '              <p>在线打卡</p>';
        // $output .= '            </li>';
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d4.png"></h2>';
        // $output .= '              <p>排课表</p>';
        // $output .= '            </li>';
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d5.png"></h2>';
        // $output .= '              <p>模拟考试</p>';
        // $output .= '            </li>';
        // $output .= '            <li>';
        // $output .= '              <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d6.png"></h2>';
        // $output .= '              <p>营销游戏</p>';
        // $output .= '            </li>';
        $output .= '          </ul>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '    </div>';
        $output .= '  </div>';
        $output .= '</div>';



		return $output;

	}

    public function stylesheets()
    {
        $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css');
        return $style_sheet;
    }

	public function css()
	{
		$addonId = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
        // 上翻页按钮
		$swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
		$swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;

        //正常
        // 轮播点宽度
        $swiper_p_width_md = (isset($settings->swiper_p_width) && $settings->swiper_p_width) ? $settings->swiper_p_width : 8;
        $swiper_p_width_sm = (isset($settings->swiper_p_width_sm) && $settings->swiper_p_width_sm) ? $settings->swiper_p_width_sm : 8;
        $swiper_p_width_xs = (isset($settings->swiper_p_width_xs) && $settings->swiper_p_width_xs) ? $settings->swiper_p_width_xs : 8;
        // 轮播点高度
        $swiper_p_height_md = (isset($settings->swiper_p_height) && $settings->swiper_p_height) ? $settings->swiper_p_height : 8;
        $swiper_p_height_sm = (isset($settings->swiper_p_height_sm) && $settings->swiper_p_height_sm) ? $settings->swiper_p_height_sm : 8;
        $swiper_p_height_xs = (isset($settings->swiper_p_height_xs) && $settings->swiper_p_height_xs) ? $settings->swiper_p_height_xs : 8;
        // 轮播点间距
        $swiper_p_margin_md = (isset($settings->swiper_p_margin) && $settings->swiper_p_margin) ? $settings->swiper_p_margin : 5;
        $swiper_p_margin_sm = (isset($settings->swiper_p_margin_sm) && $settings->swiper_p_margin_sm) ? $settings->swiper_p_margin_sm : 5;
        $swiper_p_margin_xs = (isset($settings->swiper_p_margin_xs) && $settings->swiper_p_margin_xs) ? $settings->swiper_p_margin_xs : 5;
        // 轮播点颜色
        $swiper_p_color = (isset($settings->swiper_p_color) && $settings->swiper_p_color) ? $settings->swiper_p_color : '#f0f0f0';

        //选中
        // 轮播点宽度
        $swiper_p_width_a_md = (isset($settings->swiper_p_width_a) && $settings->swiper_p_width_a) ? $settings->swiper_p_width_a : null;
        $swiper_p_width_a_sm = (isset($settings->swiper_p_width_a_sm) && $settings->swiper_p_width_a_sm) ? $settings->swiper_p_width_a_sm : null;
        $swiper_p_width_a_xs = (isset($settings->swiper_p_width_a_xs) && $settings->swiper_p_width_a_xs) ? $settings->swiper_p_width_a_xs : null;
        // 轮播点高度
        $swiper_p_height_a_md = (isset($settings->swiper_p_height_a) && $settings->swiper_p_height_a) ? $settings->swiper_p_height_a : null;
        $swiper_p_height_a_sm = (isset($settings->swiper_p_height_a_sm) && $settings->swiper_p_height_a_sm) ? $settings->swiper_p_height_a_sm : null;
        $swiper_p_height_a_xs = (isset($settings->swiper_p_height_a_xs) && $settings->swiper_p_height_a_xs) ? $settings->swiper_p_height_a_xs : null;
        // 轮播点颜色
        $swiper_p_color_a = (isset($settings->swiper_p_color_a) && $settings->swiper_p_color_a) ? $settings->swiper_p_color_a : '#007aff';


        $css = '
		    /* 组件盒子 */
		    ' . $addonId . ' .swiper-box-main {
		        position: relative;
		    }
		    /* 切换 配置样式 */
	        ' . $addonId . ' .swiper-button {
	            width: auto;
	            height: auto;
                top: ' . $swiper_button_top_md . '%;
	        }
	        ' . $addonId . ' .swiper-button:after {
	            content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev {
	            left: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }
            ' . $addonId . ' .swiper-button-next {
	            right: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url(' . $swiper_button_next . ');
	        }
	        /*轮播点*/
	        ' . $addonId . ' .swiper-pagination {
	            width: 100%;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet {
	            margin-right: ' .$swiper_p_margin_md . 'px;
	            width: ' . $swiper_p_width_md . 'px;
                height: ' . $swiper_p_height_md . 'px;
                background: ' . $swiper_p_color . ';
	            opacity: 1;
	        }
	        ' . $addonId . ' .swiper-pagination-bullet-active {
	            width: ' . $swiper_p_width_a_md . 'px;
                height: ' . $swiper_p_height_a_md . 'px;
                background: ' . $swiper_p_color_a . ';
	        }
	        ' . $addonId . ' .swiper-pagination-bullet:last-child {
	            margin-right: 0px;
	        }
	        @media (min-width: 768px) and (max-width: 991px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_sm . 'px;
                    width: ' . $swiper_p_width_sm . 'px;
                    height: ' . $swiper_p_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_sm . 'px;
                    height: ' . $swiper_p_height_a_sm . 'px;
                }
	        }
	        @media (max-width: 767px) {
	            /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs . '%;
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 轮播点 */
                ' . $addonId . ' .swiper-pagination-bullet {
                    margin-right: ' .$swiper_p_margin_xs . 'px;
                    width: ' . $swiper_p_width_xs . 'px;
                    height: ' . $swiper_p_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-pagination-bullet-active {
                    width: ' . $swiper_p_width_a_xs . 'px;
                    height: ' . $swiper_p_height_a_xs . 'px;
                }
	        }';

		return $css;
	}

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
         //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;


        $js = 'jQuery(function($){';
        $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: true,
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                    clickable: true,
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
            });';
        $js .= '})';

        return $js;
    }

	public static function getTemplate()
	{
		$output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item;

        #>
		

        <style>
            {{addonId}} .web_section7{height:670px;background-size:cover;background-position:center;background-repeat:no-repeat;background-image:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_bg4.jpg)}
            {{addonId}} .container{width:1300px;margin:0 auto}
            {{addonId}} .web_title1{text-align:center;padding:5rem 0 5rem}
            {{addonId}} .title{max-width:100%;overflow:unset;text-align:left;padding:0}
            {{addonId}} .web_title1 .title .web_label1{margin-right:-10px;margin-top:-4px}
            {{addonId}} .web_title1 .title img{height:33px}
            {{addonId}} .web_title2 .title p,.web_title2 .txt{color:#fff}
            {{addonId}} .web_title1 .title p{color:#2a2a2a;font-size:36px;display:flex;position:relative;margin-top:0;line-height:inherit}
            {{addonId}} .web_title1 .title{display:flex;justify-content:center;margin-bottom:12px}
            {{addonId}} .web_title1 .title .web_label2{margin-top:24px;margin-left:-8px}
            {{addonId}} .web_title1 .title img{height:33px}
            {{addonId}} .web_title2 .title p,.web_title2 .txt{color:#fff}
            {{addonId}} .web_title1 .txt{color:#676c7f;font-size:20px}
            {{addonId}} .web_xd1{margin-top:0px}
            {{addonId}} .web_xd_list ul{position:relative;height:310px}
            {{addonId}} .web_xd_list ul li{position:absolute;display:flex;flex-direction:column;align-items:center;width:90px;animation:arrow 3s -0.5s linear infinite}
            {{addonId}} .web_xd_list ul li h2{border-radius:50%;width:90px;height:90px;background:#fff;box-shadow:0 0 8px 0px rgba(255,255,255,.4);box-sizing:content-box;margin:0}
            {{addonId}} .web_xd_list ul li p{color:#fff;font-size:20px;margin-top:15px}
            {{addonId}} .web_xd_list ul li:nth-child(2n){top:auto;bottom:0}
            {{addonId}} .web_xd_list ul li:nth-child(2){left:236px;animation:arrow 3.4s -.8s linear infinite}
            {{addonId}} .web_xd_list ul li:nth-child(3){left:474px;animation:arrow 2.8s -1.2s linear infinite}
            {{addonId}} .web_xd_list ul li:nth-child(4){left:712px;animation:arrow 3.6s -.5s linear infinite}
            {{addonId}} .web_xd_list ul li:nth-child(5){left:auto;right:236px;animation:arrow 3s -.8s linear infinite}
            {{addonId}} .web_xd_list ul li:nth-child(6){left:auto;right:0;animation:arrow 2.6s -1.5s linear infinite}
            @keyframes arrow{0%{transform:translateY(0)}50%{transform:translateY(-20px)}100%{transform:translateY(0)}}
            @media only screen and (max-width:1023px){
                {{addonId}} .web_section7 {width: 100%;}
                {{addonId}} .container {width: 100%;margin: 0 auto;}
                {{addonId}} .web_title1 .title p{color:#fff;font-size:24px;display:flex;position:relative;margin-top:0;line-height:inherit}
                {{addonId}} .web_xd_list ul li h2{border-radius:50%;width:60px;height:60px;background:#fff;box-shadow:0 0 8px 0px rgba(255,255,255,.4);box-sizing:content-box;margin:0}
                {{addonId}} .web_xd_list ul li:nth-child(1){left:0.3rem;bottom:13rem;animation:arrow 3.4s -.8s linear infinite}
                {{addonId}} .web_xd_list ul li:nth-child(2){left:3rem;bottom:5rem;animation:arrow 3.4s -.8s linear infinite;}
                {{addonId}} .web_xd_list ul li:nth-child(3){left:8rem;bottom:13rem;animation:arrow 2.8s -1.2s linear infinite;}
                {{addonId}} .web_xd_list ul li:nth-child(4){left:11rem;bottom: 5rem;animation:arrow 3.6s -.5s linear infinite;}
                {{addonId}} .web_xd_list ul li:nth-child(5){left:auto;right: 4rem;bottom: 13rem;animation:arrow 3s -.8s linear infinite;}
                {{addonId}} .web_xd_list ul li:nth-child(6){left:auto;right:1rem;bottom: 5rem;animation:arrow 2.6s -1.5s linear infinite;}
            }
        </style>

		      

        <div class="web_section7">
            <div class="container">
                <div class="web_con7">
                    <div class="web_title1 web_title2">
                        <div class="title"> 
                            <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_label1.png" class="web_label1">
                            <p>新增趣味助学工具 灵活开展线上教学</p>
                            <img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_label2.png" class="web_label2"> 
                        </div>
                        <div class="txt"> 激励学员自发学习，提升学员完课率 </div>
                    </div>
                    <div class="web_xd1">
                        <div class="web_xd_list">
                            <ul>
                                <li>
                                  <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d1.png"></h2>
                                    <p>用户分群</p>
                                </li>
                               <li>
                                <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d2.png"></h2>
                                   <p>表单问券</p>
                               </li>
                                <li>
                                   <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d3.png"></h2>
                                 <p>在线打卡</p>
                                 </li>
                                  <li>
                                <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d4.png"></h2>
                                    <p>排课表</p>
                                 </li>
                                <li>
                                 <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d5.png"></h2>
                               <p>模拟考试</p>
                                 </li>
                                <li>
                                <h2><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/jump_wave_item/assets/images/web_d6.png"></h2>
                               <p>营销游戏</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

		';

		return $output;
	}

}