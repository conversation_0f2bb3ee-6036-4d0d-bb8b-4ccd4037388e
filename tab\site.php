<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonTab extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;


        $cpcatid = $_GET['cpcatid'] ?? 0;
        if($cpcatid==0){
            $cpcatid = $_GET['zcpcatid'] ?? 0;
        }

        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $nav_icon_postion = (isset($settings->nav_icon_postion) && $settings->nav_icon_postion) ? $settings->nav_icon_postion : '';
        $nav_image_postion = (isset($settings->nav_image_postion) && $settings->nav_image_postion) ? $settings->nav_image_postion : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
        $nav_text_align = (isset($settings->nav_text_align) && $settings->nav_text_align) ? $settings->nav_text_align : 'jwpf-text-left';
        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $fix_img_height = (isset($settings->fix_img_height)) ? $settings->fix_img_height : 0;
        $fix_img_height_input = (isset($settings->fix_img_height_input)) ? $settings->fix_img_height_input : 300;
        $fix_img_height_input_m = (isset($settings->fix_img_height_input_m)) ? $settings->fix_img_height_input_m : 100;

        $wrap = (isset($settings->wrap)) ? $settings->wrap : 0;//是否换行
        //导航间填充
        $nav_stuffing = (isset($settings->nav_stuffing)) ? $settings->nav_stuffing : 0;
        $nav_stuffing_m = (isset($settings->nav_stuffing_m)) ? $settings->nav_stuffing_m : 0;
        $nav_stuffing_m = explode(" ",$nav_stuffing_m);
        $nav_stuffing_w = (isset($settings->nav_stuffing_w)) ? $settings->nav_stuffing_w : 0;
        $nav_stuffing_h = (isset($settings->nav_stuffing_h)) ? $settings->nav_stuffing_h : 0;
        $nav_stuffing_c = (isset($settings->nav_stuffing_c)) ? $settings->nav_stuffing_c : '';

        /*7.2新增*/
        //导航背景渐变色1
        $nav_col1 = (isset($settings->nav_col)) ? $settings->nav_col : '';
        //导航背景渐变色2
        $nav_col2 = (isset($settings->nav_col)) ? $settings->nav_col : '';
        //标签颜色
        $icon_c = (isset($settings->icon_c)) ? $settings->icon_c : '#000';
        //标签边距
        $icon_m = (isset($settings->icon_m)) ? $settings->icon_m : 0;
        //选项下面的图文
        $select_tuwen = (isset($settings->select_tuwen)) ? $settings->select_tuwen : 'no';
        $tuwen_font_content = (isset($settings->tuwen_font_content)) ? $settings->tuwen_font_content : '联系我们';
        $tuwen_font_width = (isset($settings->tuwen_font_width)) ? $settings->tuwen_font_width : 200;
        $tuwen_font_height = (isset($settings->tuwen_font_height)) ? $settings->tuwen_font_height : 50;
        $tuwen_bg_color = (isset($settings->tuwen_bg_color)) ? $settings->tuwen_bg_color : '';
        $tuwen_font_color = (isset($settings->tuwen_font_color)) ? $settings->tuwen_font_color : 'white';
        $tuwen_desc_content = (isset($settings->tuwen_desc_content)) ? $settings->tuwen_desc_content : '';
        $tuwen_desc_bjnum = (isset($settings->tuwen_desc_bjnum)) ? $settings->tuwen_desc_bjnum : 10;
        $tuwen_desc_bjnum2 = (isset($settings->tuwen_desc_bjnum2)) ? $settings->tuwen_desc_bjnum2 : 10;

        $tuwen_font_side = (isset($settings->tuwen_font_side)) ? $settings->tuwen_font_side : 'center';
        $tuwen_desc_bg_color = (isset($settings->tuwen_desc_bg_color)) ? $settings->tuwen_desc_bg_color : 'none';
        $tuwen_desc_border_color = (isset($settings->tuwen_desc_border_color)) ? $settings->tuwen_desc_border_color : '#ccc';
        //文本居中
        $nav_font_position = (isset($settings->nav_font_position) && $settings->nav_font_position) ? $settings->nav_font_position  : 'flex-start';
        $nav_bg_color = (isset($settings->nav_bg_color) && $settings->nav_bg_color) ? $settings->nav_bg_color  : '';//背景色
        $nav_back_col = (isset($settings->nav_back_col) && $settings->nav_back_col) ? $settings->nav_back_col  : 0;//开启渐变色背景
        $img_cent = (isset($settings->img_cent) && $settings->img_cent) ? $settings->img_cent  : 0;//图片居中

        if($style === 'type06'){
            $active_color_type6 = (isset($settings->active_color_type6) && $settings->active_color_type6) ? $settings->active_color_type6  : '#d90029';
            $tab_width_type6 = (isset($settings->tab_width_type6) && $settings->tab_width_type6) ? $settings->tab_width_type6  : 46;
            $tab_margin_top_type6 = (isset($settings->tab_margin_top_type6) && $settings->tab_margin_top_type6) ? $settings->tab_margin_top_type6  : 166;
            $scroll_type6 = (isset($settings->scroll_type6) && ($settings->scroll_type6 || $settings->scroll_type6)) ? $settings->scroll_type6  : 0;
            $item_margin_right_type6 = (isset($settings->item_margin_right_type6) && ($settings->item_margin_right_type6 || $settings->item_margin_right_type6)) ? $settings->item_margin_right_type6  : 10;

            $output ='<style type="text/css">
                '.$addon_id.' .type6{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                '.$addon_id.' .type6 .jwpf-addon-tab{
                    min-width: '.$tab_width_type6.'%;
                    margin-top: '.$tab_margin_top_type6.'px;
                }
                '.$addon_id.' .jwpf-tab-type06-content{
                    flex-grow: 1;
                }
                '.$addon_id.' *{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                '.$addon_id.' a,
                '.$addon_id.' a:hover{
                    text-decoration: none;
                    color: inherit;
                }
                '.$addon_id.' .ind8-a3 {
                    width: 214px;
                    position: relative;
                    margin-bottom: 66px;
                }
                '.$addon_id.' .ind8-a4 {
                    width: 90px;
                    height: 48px;
                    position: relative;
                    margin-left: 28px;
                    cursor: pointer;
                    flex-grow: 1;
                    display: flex;
                    align-items: center;
                }
                '.$addon_id.' .ind8-a4 > div:nth-child(2) {
                    font-size: 22px;
                    line-height: 48px;
                    color: #454545;
                    font-weight: bold;
                    transition: 0.5s;
                    white-space: nowrap;
                    padding: 0 10px;
                }
                '.$addon_id.' .ind8-a4.on1 > div:nth-child(2) {
                    color: '.$active_color_type6.';
                    transition: 0.5s;
                }
                '.$addon_id.' .ind8-a4 > div:nth-child(1) {
                    width: 0;
                    height: 5px;
                    background: '.$active_color_type6.';
                    position: absolute;
                    top: calc(50% - 5px/2);
                    left: -28px;
                    transition: 0.5s;
                }
                '.$addon_id.' .ind8-a4.on1 > div:nth-child(1) {
                    width: 5px;
                    transition: 0.5s;
                    min-width: 5px;
                }
                '.$addon_id.' .ind8-a4 > div:nth-child(3) {
                    width: 0;
                    height: 5px;
                    background: '.$active_color_type6.';
                    transition: 0.5s;
                }
                '.$addon_id.' .ind8-a4.on1 > div:nth-child(3) {
                    width: 80px;
                    transition: 0.5s;
                    min-width: 80px;
                }
                '.$addon_id.' .ind8-a5 {
                    position: relative;
                }
                '.$addon_id.' .f_more {
                    font-size: 16px;
                    width: 160px;
                    height: 55px;
                    border: solid 1px #949494;
                    border-radius: 26px;
                    text-align: center;
                    line-height: 50px;
                    display: block;
                    position: relative;
                    overflow: hidden;
                }
                '.$addon_id.' .f_more span{
                    position: relative;
                    z-index: 1;
                    transition: all 0.2s;
                }
                '.$addon_id.' .f_more::before {
                    content: "";
                    width: 0;
                    height: 100%;
                    background: '.$active_color_type6.';
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    transition: 0.5s;
                }
                '.$addon_id.' .f_more::after {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    z-index: -2;
                    content: "";
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    border-radius: 28px;
                }
                '.$addon_id.' .f_more:hover, '.$addon_id.'  .f_more.active {
                    color: #fff;
                    border-color: '.$active_color_type6.';
                    transition-delay: 0.1s;
                }
                '.$addon_id.' .f_more:hover::before, .f_more.active::before {
                    width: 100%;
                    right: auto;
                    left: 0;
                }
                @media (max-width: 1023px){
                    '.$addon_id.' .type6{
                        display: block;
                    }
                    '.$addon_id.' .ind8-a3{
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                    }
                    '.$addon_id.' .ind8-a4{
                        margin-left: 0;
                    }
                    '.$addon_id.' .ind8-a2 .t2-a1{
                        margin-bottom: 40px;
                    }
                    '.$addon_id.' .ind8-a4 i{
                        width: 0;
                        transition: .3s;
                    }
                    '.$addon_id.' .ind8-a4 > div{
                        position: static!important;
                        display: inline-block;
                    }
                    '.$addon_id.' .ind8-a4.on1 > div:nth-of-type(3){
                        display: none;
                    }
                    '.$addon_id.' .ind8-a4 > div:nth-child(1){
                        position: static!important;
                        width: 0;
                        height: 2px;
                        transition: all .3s;
                    }
                    '.$addon_id.' .ind8-a4.on1 > div:nth-child(1){
                        width: 5px;
                        vertical-align: middle;
                    }
                    '.$addon_id.' .ind8-a4 > div:nth-child(2){
                        color: #333333;
                        font-weight: bolder;
                        font-size: 16px;
                        overflow:hidden;
                        text-overflow:ellipsis;
                        white-space:nowrap
                    }
                    '.$addon_id.' .ind8-a5{
                        display: none;
                    }
                    '.$addon_id.' .phone-list{
                        width: 100%;
                        display: block;
                        overflow: hidden;
                        height: 360px;
                        position: relative;
                    }
                    '.$addon_id.' .ind8-a3{
                        margin-bottom: 26px;
                    }';
                    if($scroll_type6 == 1){
                        $output.=$addon_id.' .ind8-a3{
                            width: 100%;
                            display: block;
                        }';
                        $output.=$addon_id.' .type6-box{
                            width: 100%;
                            overflow: auto;
                            display: flex;
                            justify-content: flex-start;
                        }';
                        $output.=$addon_id.' .ind8-a4{
                            width: auto;
                            margin-right: '.$item_margin_right_type6.'px;
                        }';
                    }
                $output.='}
            </style>';

            $server_name = $_SERVER['SERVER_NAME'];
            $detail_page_id = (isset($settings->detail_page_id) && $settings->detail_page_id) ? $settings->detail_page_id : 0;
            $target = (isset($settings->target) && $settings->target) ? $settings->target : '_self';
            $section_id_type6 = (isset($settings->section_id_type6) && $settings->section_id_type6) ? $settings->section_id_type6 : '';

            $output.='<div class="type6 wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                <div class="jwpf-addon jwpf-addon-tab ' . $class . '">
                    <div role="tablist" class="ind8-a3 wow animated jwpf-nav jwpf-nav-' . $style . '" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">';
                        if($scroll_type6 == 1){
                            $output.='<div class="type6-box">';
                        }
                            foreach ($settings->jw_tab_item as $key => $tab) {
                                // 导航关联内容
                                if($tab->aboutcase == 1) {
                                    if($tab->resource=='product'){
                                        $cid=$tab->goods_catid;
                                    }else{
                                        $cid=$tab->catid;
                                    }
                                }else{
                                    $cid=0;
                                }
                                // 判断未点击翻页时默认选中首个分类
                                if($cpcatid!=0 && $cid!=0 ){
                                    $output .= '<div class="ind8-a4 ' . (($cpcatid == $cid) ? "on1" : "") . '" cid="'. $cid .'" data-cid="'. $cid .'" data-id="jwpf-tab-' . ($this->addon->id + $key) . '">';
                                }else{
                                    $output .= '<div data-id="jwpf-tab-' . ($this->addon->id + $key) . '" cid="'. $cid .'" class="ind8-a4 '.($key === 0 ? 'on1' : '').'" data-cid="'.$cid.'">';
                                }
                                $output .= '<div></div>
                                    <div>'.$tab->title.'</div>
                                    <div></div>
                                </div>';
                            }
                        if($scroll_type6 == 1){
                            $output.='</div>';
                        }
                    $output .= '</div>
                    <div class="ind8-a5">';
                        foreach ($settings->jw_tab_item as $key => $tab) {
                            // 导航关联内容
                            if($tab->aboutcase == 1) {
                                if($tab->resource=='product'){
                                    $cid=$tab->goods_catid;
                                }else{
                                    $cid=$tab->catid;
                                }
                            }else{
                                $cid=0;
                            }
                            $url = '/index.php/component/jwpagefactory/?view=page';
                            $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0).'&id='.base64_encode($detail_page_id).'&cpcatid='.$cid.'&page=1&section_id_type6='.$section_id_type6.'&tab='.$key;

                            $output.='<div class="ind8-a6" data-id="jwpf-tab-' . ($this->addon->id + $key) . '" style="display: '.($key === 0 ? 'block' : 'none').';">
                                <a class="f_more" href=\''.$url.'\' target="'.$target.'">
                                    <span>进一步了解</span>
                                </a>
                            </div>';
                        }
                    $output .= '</div>
                </div>';

                // 内容
                $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';
                    foreach ($settings->jw_tab_item as $key => $tab) {
                        if($cpcatid!=0 && $cid!=0 ){
                            $output .= '<div id="jwpf-tab-' . ($this->addon->id + $key) . '" class="jwpf-tab-pane jwpf-fade ' . (($cid == $cpcatid) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $key) . '">' . $tab->content . '</div>';
                        }else{
                            $output .= '<div id="jwpf-tab-' . ($this->addon->id + $key) . '" class="jwpf-tab-pane jwpf-fade ' . (($key == 0) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $key) . '">' . $tab->content . '</div>';
                        }
                    }
                $output .= '</div>
            </div>';

            $output.='<script type="text/javascript">
                function changeTab'.$this->addon_id.'(i,v){
                    let id = $(v).data("id");
                    $(v).addClass("on1").siblings().removeClass("on1");
                    $(".ind8-a6[data-id=\""+id+"\"]").show().siblings().hide();
                    $(".jwpf-tab-pane#"+id).addClass("active").addClass("in").siblings().removeClass("active").removeClass("in");
                }
                $("'.$addon_id.' .ind8-a3 .ind8-a4").each(function(i,v) {
                    $(v).on("mouseenter",function(){
                        changeTab'.$this->addon_id.'(i,v);
                    }).on("click",function(){
                        changeTab'.$this->addon_id.'(i,v);
                    })
                })
                function getQueryVariable(variable) {
                    var query = window.location.search.substring(1);
                    var vars = query.split("&");
                    for (var i=0;i<vars.length;i++) {
                      var pair = vars[i].split("=");
                      if(pair[0] == variable){return pair[1];}
                    }
                    return(false);
                }
                if(getQueryVariable("section_id_type6")){
                    if(getQueryVariable("tab")){
                        $("#"+getQueryVariable("section_id_type6")+" .ind8-a3 .ind8-a4").eq(Number(getQueryVariable("tab"))).addClass("on1").siblings().removeClass("on1");
                        $("#"+getQueryVariable("section_id_type6")+" .ind8-a6").eq(Number(getQueryVariable("tab"))).show().siblings().hide();
                        $("#"+getQueryVariable("section_id_type6")+" .jwpf-tab-content>.jwpf-tab-pane").eq(Number(getQueryVariable("tab"))).addClass("active").addClass("in").siblings().removeClass("active").removeClass("in");
                    }
                }
            </script>';
        }else{
            //Output
            $output = '<div class="jwpf-addon jwpf-addon-tab '. $addon_id . $class . '">';
            $output .= ($title) ? '<' . $heading_selector . ' class="jwpf-addon-title">' . $title . '</' . $heading_selector . '>' : '';
            $output .= '<div class="jwpf-addon-content minBox jwpf-tab jwpf-' . $style . '-tab jwpf-tab-' . $nav_position . '">';
            // echo "<pre>";
            // var_dump($settings->jw_tab_item) ;
            //Tab Title
            $output .= '<ul class="jwpf-nav minUl jwpf-nav-' . $style . '" role="tablist">';
            foreach ($settings->jw_tab_item as $key => $tab) {

                // if($key == 0){
                //     echo $key;
                //     var_dump($tab);
                // }
                $icon_top = '';
                $icon_bottom = '';
                $icon_right = '';
                $icon_left = '';
                $icon_block = '';
                //Image
                $image_top = '';
                $image_bottom = '';
                $image_right = '';
                $image_left = '';

                if($tab->aboutcase == 1) {

                    if($tab->resource=='product'){
                        $cid=$tab->goods_catid;
                    }else{
                        $cid=$tab->catid;
                    }
                }else{
                    $cid=0;
                }

                //Lazy load image
                $dimension = $this->get_image_dimension($tab->image);
                $dimension = implode(' ', $dimension);

                $placeholder = $tab->image == '' ? false : $this->get_image_placeholder($tab->image);
                if($tab->image) {
                    if (strpos($tab->image, "http://") !== false || strpos($tab->image, "https://") !== false) {
                        $tab->image = $tab->image;
                    } else {
                        $tab->image = JURI::base() . $tab->image;
                    }
                }

                $title = (isset($tab->title) && $tab->title) ? ' ' . $tab->title . ' ' : '';
                //是否换行
                if($wrap == 1){
                    $subtitle = (isset($tab->subtitle) && $tab->subtitle) ? '<span class="jwpf-tab-subtitle" style="display: block">' . $tab->subtitle . '</span>' : '';
                }else{
                    $subtitle = (isset($tab->subtitle) && $tab->subtitle) ? '<span class="jwpf-tab-subtitle">' . $tab->subtitle . '</span>' : '';
                }

                if (isset($tab->image_or_icon) && $tab->image_or_icon == 'image' && $tab->image) {
                    if ($tab->image && $nav_image_postion == 'top') {
                        $image_top = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'bottom') {
                        $image_bottom = '<img class="jwpf-tab-image tab-image-block' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } elseif ($tab->image && $nav_image_postion == 'right') {
                        $image_right = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    } else {
                        $image_left = '<img class="jwpf-tab-image' . ($placeholder ? ' jwpf-element-lazy' : '') . '" src="' . ($placeholder ? $placeholder : $tab->image) . '" alt="' . trim(strip_tags($title)) . '" ' . ($placeholder ? 'data-large="' . $tab->image . '"' : '') . ' ' . ($dimension ? $dimension : '') . ' loading="lazy"/>';
                    }
                } else {
                    if (isset($tab->icon) && $tab->icon) {
                        $icon_arr = array_filter(explode(' ', $tab->icon));
                        if (count($icon_arr) === 1) {
                            $tab->icon = 'fa ' . $tab->icon;
                        }
                        if ($tab->icon && $nav_icon_postion == 'top') {
                            $icon_top = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'bottom') {
                            //标签
                            $icon_bottom = '<span class="jwpf-tab-icon tab-icon-block" aria-label="' . trim(strip_tags($title)) . '"><i style="color:'.$icon_c.';margin: '.($icon_m[0]?$icon_m[0]:0).'px '.($icon_m[1]?$icon_m[1]:0).'px '.($icon_m[2]?$icon_m[2]:0).'px '.($icon_m[3]?$icon_m[3]:0).'px ;" class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } elseif ($tab->icon && $nav_icon_postion == 'right') {
                            $icon_right = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        } else {
                            $icon_left = '<span class="jwpf-tab-icon" aria-label="' . trim(strip_tags($title)) . '"><i class="' . $tab->icon . '" aria-hidden="true"></i></span>';
                        }
                    }
                }
                if ($nav_icon_postion == 'top' || $nav_icon_postion == 'bottom' || $nav_image_postion == 'top' || $nav_image_postion == 'bottom') {
                    $icon_block = 'tab-img-or-icon-block-wrap';
                }

                // 导航前数字显示
                $nav_open_num = (isset($settings->nav_open_num) && $settings->nav_open_num) ? $settings->nav_open_num : 0;
                $nav_num = '';
                if($nav_open_num == 1) {
                    $nav_num .= '<span class="tab-num">';
                    if($key < 9) {
                        $nav_num .=  '0';
                    }
                    $nav_num .= $key + 1;
                    $nav_num .= '</span>';
                }

                // 判断未点击翻页时默认选中首个分类
                if($cpcatid!=0 && $cid!=0 ){
                    $output .= '<li class="page_n ' . (($cpcatid == $cid) ? "active" : "") . '" cid="'. $cid .'" data-cid="'. $cid .'">';

                }else{
                    $output .= '<li class="page_n ' . (($key == 0) ? "active" : "") . '" data-cid="'. $cid .'">';

                }

                if($img_cent){//图片居中
                    $output .= '<a onclick="ccck()" style="justify-content:'.$nav_font_position.'; display:block"';  //字体居中
                }else{
                    $output .= '<a onclick="ccck()" style="justify-content:'.$nav_font_position.';"';  //字体居中
                }
                if($tab->closeClick != 1) {
                    $output .= 'data-toggle="jwpf-tab"';
                }
                $nav_arrow = '';
                if($settings->nav_open_arrow == 1) {
                    $nav_arrow .= "<div class='tab-a-arrow'>";
                    if($settings->tab_arrow) {
                        $nav_arrow .= "<img src='" . $settings->tab_arrow . "' class='right-arrow'>";
                    }
                    if($settings->active_tab_arrow) {
                        $nav_arrow .= "<img src='" . $settings->active_tab_arrow . "' class='right-arrow actives'>";
                    }
                    $nav_arrow .= "</div>";
                }
                //是否换行
                if ($wrap == 1)
                {
                    $output .= 'id="jwpf-content-' . ($this->addon->id + $key) . '" class="cccc ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . ' href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($key == 0) ? "true" : "false") . '">'
                        . '<div>' . $nav_num
                            . '<div class="tab-a-content">' . $image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . '<br/>' . $subtitle
                        . '</div>
                        </div>' . $nav_arrow . '
                        </a>';
                }
                else
                {
                    $output .= 'id="jwpf-content-' . ($this->addon->id + $key) . '" class="cccc ' . ($style == 'custom' ? $nav_text_align : '') . ' ' . $icon_block . '"style="display:flex" href="#jwpf-tab-' . ($this->addon->id + $key) . '" role="tab" aria-controls="jwpf-tab-' . ($this->addon->id + $key) . '" aria-selected="' . (($key == 0) ? "true" : "false") . '">'
                        . '<div>' . $nav_num
                            . '<div class="tab-a-content">' .$image_top . $image_left . $icon_top . $icon_left . $title . $image_right . $image_bottom . $icon_right . $icon_bottom . $subtitle
                        . '</div>
                        </div>' . $nav_arrow . '
                        </a>';
                }
                $output .= '</li>';

                //            if($img_cent) {//图片居中
                //                $output .= '<div class="page_n_a"> </div>';
                //            }
                    //导航间填充
                // if ($nav_stuffing == 1) {
                //     $output .= '<div style=" 
                //         width: '.$nav_stuffing_w.'px;
                //         height: '.$nav_stuffing_h.'px;
                //         background-color: '.$nav_stuffing_c.';
                //         margin: '.($nav_stuffing_m[0]?$nav_stuffing_m[0]:0).'px '.($nav_stuffing_m[1]?$nav_stuffing_m[1]:0).'px '.($nav_stuffing_m[2]?$nav_stuffing_m[2]:0).'px '.($nav_stuffing_m[3]?$nav_stuffing_m[3]:0).'px ;"></div>';
                // }

            }
            if($style=='custom' && $select_tuwen=='yes')
            {
                $output .= '<div style="width:'.$tuwen_font_width.'px;height:'.$tuwen_font_height.'px;background-color:'.$tuwen_bg_color.';color:'.$tuwen_font_color.';line-height:'.$tuwen_font_height.'px;text-align:'.$tuwen_font_side.';margin-top:'.$tuwen_desc_bjnum.'px;">'.$tuwen_font_content.'</div>';
                $output .= '<div style="background:'.$tuwen_desc_bg_color.';border:1px solid '.$tuwen_desc_border_color.';margin-top:'.$tuwen_desc_bjnum2.'px;">'.$tuwen_desc_content.'</div>';
            }

            $output .= '</ul>';



            //Tab Contnet
            $output .= '<div class="jwpf-tab-content jwpf-tab-' . $style . '-content">';

            foreach ($settings->jw_tab_item as $k => $tab) {
                // print_r($k . '<br>');
                //            var_dump($tab->content);

                if($tab->aboutcase == 1) {
                    if($tab->resource=='product'){
                        $cid=$tab->goods_catid;
                    }else{
                        $cid=$tab->catid;
                    }
                }else{
                    $cid=0;
                }

                if($cpcatid!=0 && $cid!=0 ){
                    $output .= '<div id="jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($cid == $cpcatid) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $key) . '">' . $tab->content . '</div>';
                }else{
                    $output .= '<div id="jwpf-tab-' . ($this->addon->id + $k) . '" class="jwpf-tab-pane jwpf-fade ' . (($k == 0) ? " active in" : " ") . '" role="tabpanel" aria-labelledby="jwpf-content-' . ($this->addon->id + $key) . '">' . $tab->content . '</div>';
                }


            }
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
            if ($nav_stuffing == 1) {
                $output .= '
                <style>
                    '.$addon_id.' .page_n {
                        border-right: '.$nav_stuffing_w.'px solid '.$nav_stuffing_c.';
                        margin: '.($nav_stuffing_m[0]?$nav_stuffing_m[0]:0).'px '.($nav_stuffing_m[1]?$nav_stuffing_m[1]:0).'px '.($nav_stuffing_m[2]?$nav_stuffing_m[2]:0).'px '.($nav_stuffing_m[3]?$nav_stuffing_m[3]:0).'px ;
                    }
                </style>
                ';
            }
            $output .='
                    <script>
                    function ccck()
                    {
                        $(window).resize()
                    }
                    
                        function getQueryVariable(variable) {
                            var query = window.location.search.substring(1);
                            var vars = query.split("&");
                            for (var i=0;i<vars.length;i++) {
                            var pair = vars[i].split("=");
                            if(pair[0] == variable){return pair[1];}
                            }
                            return(false);
                        }
                        if(getQueryVariable("section_id_type6")){
                            if(getQueryVariable("tab")){
                                $("#"+getQueryVariable("section_id_type6")+" .jwpf-addon-tab .jwpf-nav-tabs>li.page_n").eq(Number(getQueryVariable("tab"))).addClass("active").siblings().removeClass("active");
                                $("#"+getQueryVariable("section_id_type6")+" .jwpf-tab-content>.jwpf-tab-pane").eq(Number(getQueryVariable("tab"))).addClass("active").addClass("in").siblings().removeClass("active").removeClass("in");
                            }
                        }
                    </script>
            ';
        }


        // // 固定图片高度$(".jwpf-tab-content").load(location.href+$(this).attr("href")+" .jwpf-carousel-extended");
        // if($fix_img_height) {
        //     $output .= "<style>

        //              {$addon_id} .jwpf-img-responsive{
        //                  height:{$fix_img_height_input}px !important;
        //                }
        //                @media (max-width: 992px) {
        //              {$addon_id} .jwpf-img-responsive{
        //                 height:{$fix_img_height_input_m}px !important;
        //                 }
        //             }

        //      </style>";
        // }



        return $output;

    }

    public function scripts()
    {
        $js = array(JURI::base(true) . '/components/com_jwpagefactory/addons/tab/assets/cs.js',JURI::base(true) . '/components/com_jwpagefactory/assets/js/jw_carousel.js');
        return $js;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $nav_position = (isset($settings->nav_position) && $settings->nav_position) ? $settings->nav_position : 'nav-left';

        $openbg = (isset($settings->openbg) && $settings->openbg) ? $settings->openbg : '0';
        $bgImage = (isset($settings->bgImage) && $settings->bgImage) ? $settings->bgImage : 'https://oss.lcweb01.cn/joomla/20211104/4c74cc44c34854cd4cf1e6050f3a0025.png';
        $openbg_hover = (isset($settings->openbg_hover) && $settings->openbg_hover) ? $settings->openbg_hover : '0';
        $bgImage_hover = (isset($settings->bgImage_hover) && $settings->bgImage_hover) ? $settings->bgImage_hover : 'https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png';
        $openbg_active = (isset($settings->openbg_active) && $settings->openbg_active) ? $settings->openbg_active : '0';
        $bgImage_active = (isset($settings->bgImage_active) && $settings->bgImage_active) ? $settings->bgImage_active : 'https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png';
        $opennavbg = (isset($settings->opennavbg) && $settings->opennavbg) ? $settings->opennavbg : '0';
        $navbgImage = (isset($settings->navbgImage) && $settings->navbgImage) ? $settings->navbgImage : 'https://oss.lcweb01.cn/joomla/20211104/2b2cecb35a36294c2301732801fc7662.jpg';
        //导航外间距
        $bgmargin = (isset($settings->bgmargin) && trim($settings->bgmargin)) ? 'margin: ' . $settings->bgmargin . ';' : 'margin: 0px 0px 0px 0px;';
        $bgmargin_sm = (isset($settings->bgmargin_sm) && trim($settings->bgmargin_sm)) ? 'margin: ' . $settings->bgmargin_sm . ';' : '';
        $bgmargin_xs = (isset($settings->bgmargin_xs) && trim($settings->bgmargin_xs)) ? 'margin: ' . $settings->bgmargin_xs . ';' : '';


        $tab_style = (isset($settings->style) && $settings->style) ? $settings->style : '';
        $style = '';
        $style .= (isset($settings->active_tab_color) && $settings->active_tab_color) ? 'color: ' . $settings->active_tab_color . ';' : '';
        $style .= (isset($settings->active_tab_border_width) && trim($settings->active_tab_border_width)) ? 'border-width: ' . $settings->active_tab_border_width . ';border-style: solid;' : '';
        $style .= (isset($settings->active_tab_border_color) && $settings->active_tab_border_color) ? 'border-color: ' . $settings->active_tab_border_color . ';' : '';
        $style .= (isset($settings->active_tab_border_radius) && trim($settings->active_tab_border_radius)) ? 'border-radius: ' . $settings->active_tab_border_radius . ';' : '';

        //Font style
        $font_style = '';
        $font_style .= (isset($settings->nav_fontsize) && $settings->nav_fontsize) ? 'font-size: ' . $settings->nav_fontsize . 'px;' : '';
        $font_style .= (isset($settings->nav_lineheight) && $settings->nav_lineheight) ? 'line-height: ' . $settings->nav_lineheight . 'px;' : '';
        //Font style object
        $nav_font_style = (isset($settings->nav_font_style) && $settings->nav_font_style) ? $settings->nav_font_style : '';

        if (isset($nav_font_style->underline) && $nav_font_style->underline) {
            $font_style .= 'text-decoration:underline;';
        }
        if (isset($nav_font_style->italic) && $nav_font_style->italic) {
            $font_style .= 'font-style:italic;';
        }
        if (isset($nav_font_style->uppercase) && $nav_font_style->uppercase) {
            $font_style .= 'text-transform:uppercase;';
        }
        if (isset($nav_font_style->weight) && $nav_font_style->weight) {
            $font_style .= 'font-weight:' . $nav_font_style->weight . ';';
        }
        $nav_border = (isset($settings->nav_border) && trim($settings->nav_border)) ? $settings->nav_border : '';
        if (strpos($nav_border, 'px')) {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border-width: ' . $settings->nav_border . ';border-style:solid;' : '';
        } else {
            $font_style .= (isset($settings->nav_border) && trim($settings->nav_border)) ? 'border: ' . $settings->nav_border . 'px solid;' : '';
        }
        $font_style .= (isset($settings->nav_border_color) && $settings->nav_border_color) ? 'border-color: ' . $settings->nav_border_color . ';' : '';
        $font_style .= (isset($settings->nav_color) && $settings->nav_color) ? 'color: ' . $settings->nav_color . ';' : '';
        $font_style .= (isset($settings->nav_bg_color) && $settings->nav_bg_color) ? 'background-color: ' . $settings->nav_bg_color . ';' : '';

        $font_style .= (isset($settings->nav_border_radius) && $settings->nav_border_radius) ? 'border-radius: ' . $settings->nav_border_radius . 'px;' : '';
        $font_style .= (isset($settings->nav_padding) && trim($settings->nav_padding)) ? 'padding: ' . $settings->nav_padding . ';' : '';

        $font_style_sm = (isset($settings->nav_fontsize_sm) && $settings->nav_fontsize_sm) ? 'font-size: ' . $settings->nav_fontsize_sm . 'px;' : '';
        $font_style_sm .= (isset($settings->nav_padding_sm) && trim($settings->nav_padding_sm)) ? 'padding: ' . $settings->nav_padding_sm . ';' : '';
        $font_style_sm .= (isset($settings->nav_lineheight_sm) && $settings->nav_lineheight_sm) ? 'line-height: ' . $settings->nav_lineheight_sm . 'px;' : '';

        $font_style_xs = (isset($settings->nav_fontsize_xs) && $settings->nav_fontsize_xs) ? 'font-size: ' . $settings->nav_fontsize_xs . 'px;' : '';
        $font_style_xs .= (isset($settings->nav_padding_xs) && trim($settings->nav_padding_xs)) ? 'padding: ' . $settings->nav_padding_xs . ';' : '';
        $font_style_xs .= (isset($settings->nav_lineheight_xs) && $settings->nav_lineheight_xs) ? 'line-height: ' . $settings->nav_lineheight_xs . 'px;' : '';

        //Nav Width
        $nav_width = (isset($settings->nav_width) && $settings->nav_width) ? $settings->nav_width : 30;
        $nav_width_sm = (isset($settings->nav_width_sm) && $settings->nav_width_sm) ? $settings->nav_width_sm : 30;
        $nav_width_xs = (isset($settings->nav_width_xs) && $settings->nav_width_xs) ? $settings->nav_width_xs : 30;
        //Nav Margin
        $nav_margin = (isset($settings->nav_margin) && trim($settings->nav_margin)) ? 'padding: ' . $settings->nav_margin . ';' : 'padding: 0px 0px 5px 0px;';
        $nav_margin_sm = (isset($settings->nav_margin_sm) && trim($settings->nav_margin_sm)) ? 'padding: ' . $settings->nav_margin_sm . ';' : '';
        $nav_margin_xs = (isset($settings->nav_margin_xs) && trim($settings->nav_margin_xs)) ? 'padding: ' . $settings->nav_margin_xs . ';' : '';
        $nav_gutter = (isset($settings->nav_gutter) && $settings->nav_gutter) ? $settings->nav_gutter : 0;
        //Nav Gutter
        if ($nav_position == 'nav-right') {
            $nav_gutter_right = (isset($settings->nav_gutter) && $settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_sm = (isset($settings->nav_gutter_sm) && $settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_right_xs = (isset($settings->nav_gutter_xs) && $settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';

            $nav_gutter_left = (isset($settings->nav_gutter) && $settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_sm = (isset($settings->nav_gutter_sm) && $settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_left_xs = (isset($settings->nav_gutter_xs) && $settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';
        } else {
            $nav_gutter_right = (isset($settings->nav_gutter) && $settings->nav_gutter) ? 'padding-right: ' . $settings->nav_gutter . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_sm = (isset($settings->nav_gutter_sm) && $settings->nav_gutter_sm) ? 'padding-right: ' . $settings->nav_gutter_sm . 'px;' : 'padding-right: 15px;';
            $nav_gutter_right_xs = (isset($settings->nav_gutter_xs) && $settings->nav_gutter_xs) ? 'padding-right: ' . $settings->nav_gutter_xs . 'px;' : 'padding-right: 15px;';

            $nav_gutter_left = (isset($settings->nav_gutter) && $settings->nav_gutter) ? 'padding-left: ' . $settings->nav_gutter . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_sm = (isset($settings->nav_gutter_sm) && $settings->nav_gutter_sm) ? 'padding-left: ' . $settings->nav_gutter_sm . 'px;' : 'padding-left: 15px;';
            $nav_gutter_left_xs = (isset($settings->nav_gutter_xs) && $settings->nav_gutter_xs) ? 'padding-left: ' . $settings->nav_gutter_xs . 'px;' : 'padding-left: 15px;';
        }

        //Content Style
        $content_style = '';
        $content_style .= (isset($settings->content_backround) && $settings->content_backround) ? 'background-color: ' . $settings->content_backround . ';' : '';
        $content_style .= (isset($settings->content_border) && $settings->content_border) ? 'border: ' . $settings->content_border . 'px solid;' : '';
        $content_style .= (isset($settings->content_color) && $settings->content_color) ? 'color: ' . $settings->content_color . ';' : '';
        $content_style .= (isset($settings->content_border_color) && $settings->content_border_color) ? 'border-color: ' . $settings->content_border_color . ';' : '';
        $content_style .= (isset($settings->content_border_radius) && $settings->content_border_radius) ? 'border-radius: ' . $settings->content_border_radius . 'px;' : '';
        $content_style .= (isset($settings->content_margin) && trim($settings->content_margin)) ? 'margin: ' . $settings->content_margin . ';' : '';
        $content_style .= (isset($settings->content_padding) && trim($settings->content_padding)) ? 'padding: ' . $settings->content_padding . ';' : '';
        $content_style .= (isset($settings->content_fontsize) && $settings->content_fontsize) ? 'font-size: ' . $settings->content_fontsize . 'px;' : '';
        $content_style .= (isset($settings->content_lineheight) && $settings->content_lineheight) ? 'line-height: ' . $settings->content_lineheight . 'px;' : '';
        //Font style object
        $content_font_style = (isset($settings->content_font_style) && $settings->content_font_style) ? $settings->content_font_style : '';
        if (isset($content_font_style->underline) && $content_font_style->underline) {
            $content_style .= 'text-decoration:underline;';
        }
        if (isset($content_font_style->italic) && $content_font_style->italic) {
            $content_style .= 'font-style:italic;';
        }
        if (isset($content_font_style->uppercase) && $content_font_style->uppercase) {
            $content_style .= 'text-transform:uppercase;';
        }
        if (isset($content_font_style->weight) && $content_font_style->weight) {
            $content_style .= 'font-weight:' . $content_font_style->weight . ';';
        }
        //Content tablet style
        $content_style_sm = (isset($settings->content_margin_sm) && trim($settings->content_margin_sm)) ? 'margin: ' . $settings->content_margin_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_padding_sm) && $settings->content_padding_sm) ? 'padding: ' . $settings->content_padding_sm . ';' : '';
        $content_style_sm .= (isset($settings->content_fontsize_sm) && $settings->content_fontsize_sm) ? 'font-size: ' . $settings->content_fontsize_sm . 'px;' : '';
        $content_style_sm .= (isset($settings->content_lineheight_sm) && $settings->content_lineheight_sm) ? 'line-height: ' . $settings->content_lineheight_sm . 'px;' : '';

        //Content Mobile style
        $content_style_xs = (isset($settings->content_margin_xs) && trim($settings->content_margin_xs)) ? 'margin: ' . $settings->content_margin_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_padding_xs) && $settings->content_padding_xs) ? 'padding: ' . $settings->content_padding_xs . ';' : '';
        $content_style_xs .= (isset($settings->content_fontsize_xs) && $settings->content_fontsize_xs) ? 'font-size: ' . $settings->content_fontsize_xs . 'px;' : '';
        $content_style_xs .= (isset($settings->content_lineheight_xs) && $settings->content_lineheight_xs) ? 'line-height: ' . $settings->content_lineheight_xs . 'px;' : '';
        //Box shadow
        $show_boxshadow = (isset($settings->show_boxshadow) && $settings->show_boxshadow) ? $settings->show_boxshadow : '';
        $box_shadow = '';
        if ($show_boxshadow) {
            $box_shadow .= (isset($settings->shadow_horizontal) && $settings->shadow_horizontal) ? $settings->shadow_horizontal . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_vertical) && $settings->shadow_vertical) ? $settings->shadow_vertical . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_blur) && $settings->shadow_blur) ? $settings->shadow_blur . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_spread) && $settings->shadow_spread) ? $settings->shadow_spread . 'px ' : '0 ';
            $box_shadow .= (isset($settings->shadow_color) && $settings->shadow_color) ? $settings->shadow_color : 'rgba(0, 0, 0, .5)';
        }
        //Icon Style
        $icon_style = '';
        $icon_style .= (isset($settings->icon_fontsize) && $settings->icon_fontsize) ? 'font-size: ' . $settings->icon_fontsize . 'px;' : '';
        $icon_style .= (isset($settings->icon_margin) && trim($settings->icon_margin)) ? 'margin: ' . $settings->icon_margin . ';' : '';
        $icon_style .= (isset($settings->icon_color) && $settings->icon_color) ? 'color: ' . $settings->icon_color . ';' : '';

        $icon_style_sm = (isset($settings->icon_fontsize_sm) && $settings->icon_fontsize_sm) ? 'font-size: ' . $settings->icon_fontsize_sm . 'px;' : '';
        $icon_style_sm .= (isset($settings->icon_margin_sm) && trim($settings->icon_margin_sm)) ? 'margin: ' . $settings->icon_margin_sm . ';' : '';

        $icon_style_xs = (isset($settings->icon_fontsize_xs) && $settings->icon_fontsize_xs) ? 'font-size: ' . $settings->icon_fontsize_xs . 'px;' : '';
        $icon_style_xs .= (isset($settings->icon_margin_xs) && trim($settings->icon_margin_xs)) ? 'margin: ' . $settings->icon_margin_xs . ';' : '';

        //Image Style
        $image_style = '';
        $image_style .= (isset($settings->image_height) && $settings->image_height) ? 'height: ' . $settings->image_height . 'px;' : '';
        $image_style .= (isset($settings->image_width) && $settings->image_width) ? 'width: ' . $settings->image_width . 'px;' : '';
        $image_style .= (isset($settings->image_margin) && trim($settings->image_margin)) ? 'margin: ' . $settings->image_margin . ';' : '';

        $image_style_sm = '';
        $image_style_sm .= (isset($settings->image_height_sm) && $settings->image_height_sm) ? 'height: ' . $settings->image_height_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_width_sm) && $settings->image_width_sm) ? 'width: ' . $settings->image_width_sm . 'px;' : '';
        $image_style_sm .= (isset($settings->image_margin_sm) && trim($settings->image_margin_sm)) ? 'margin: ' . $settings->image_margin_sm . ';' : '';

        $image_style_xs = '';
        $image_style_xs .= (isset($settings->image_height_xs) && $settings->image_height_xs) ? 'height: ' . $settings->image_height_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_width_xs) && $settings->image_width_xs) ? 'width: ' . $settings->image_width_xs . 'px;' : '';
        $image_style_xs .= (isset($settings->image_margin_xs) && trim($settings->image_margin_xs)) ? 'margin: ' . $settings->image_margin_xs . ';' : '';



        // min start
        $nav_all_positon = (isset($settings->nav_all_positon) && $settings->nav_all_positon) ? $settings->nav_all_positon : '';  //除自定义导航位置
        $nav_all_bg_color = (isset($settings->nav_all_bg_color) && $settings->nav_all_bg_color) ? $settings->nav_all_bg_color : '';//除自定义导航背景颜色
        $nav_block_positon = (isset($settings->nav_block_positon) && $settings->nav_block_positon) ? $settings->nav_block_positon . ';' : '';
        $nav_font_position = (isset($settings->nav_font_position) && $settings->nav_font_position) ? $settings->nav_font_position  : 'flex-start';
        // min end--------
        //Css output
        $css = '';

        if ($tab_style == 'pills') {
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            
            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-pills > li.active > a,' . $addon_id . ' .jwpf-nav-pills > li.active > a:hover,' . $addon_id . ' .jwpf-nav-pills > li.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
        } else if ($tab_style == 'lines') {
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'border-bottom-color: ' . $settings->active_tab_bg . ';' : '';
            if ($style) {
                $css .= $addon_id . 'li.page_n a::after {
                    content: "";
                    display: block;
                    width: 100%;
                    height: 100%;
                }';

                $css .= $addon_id . ' .jwpf-nav-lines > li.active > a,' . $addon_id . ' .jwpf-nav-lines > li.active > a:hover,' . $addon_id . ' .jwpf-nav-lines > li.active > a:focus {';
                $css .= $style;
                $css .= '}';
            }
        } else if ($tab_style == 'custom') {
            //Active Nav style
            $style .= (isset($settings->active_tab_bg) && $settings->active_tab_bg) ? 'background-color: ' . $settings->active_tab_bg . ';' : '';
            $active_show_boxshadow = (isset($settings->active_show_boxshadow) && $settings->active_show_boxshadow) ? $settings->active_show_boxshadow : '';
            $active_tab_box_shadow = '';
            if ($active_show_boxshadow) {
                $active_tab_box_shadow .= (isset($settings->active_shadow_horizontal) && $settings->active_shadow_horizontal) ? $settings->active_shadow_horizontal . 'px ' : '0 ';
                $active_tab_box_shadow .= (isset($settings->active_shadow_vertical) && $settings->active_shadow_vertical) ? $settings->active_shadow_vertical . 'px ' : '0 ';
                $active_tab_box_shadow .= (isset($settings->active_shadow_blur) && $settings->active_shadow_blur) ? $settings->active_shadow_blur . 'px ' : '0 ';
                $active_tab_box_shadow .= (isset($settings->active_shadow_spread) && $settings->active_shadow_spread) ? $settings->active_shadow_spread . 'px ' : '0 ';
                $active_tab_box_shadow .= (isset($settings->active_shadow_color) && $settings->active_shadow_color) ? $settings->active_shadow_color : 'rgba(0, 0, 0, .5)';
            }
            // if($nav_all_positon ==)
            if ($style) {
                $css .= $addon_id . ' .jwpf-nav-custom > li.active > a,' . $addon_id . ' .jwpf-nav-custom > li.active > a:hover,' . $addon_id . ' .jwpf-nav-custom > li.active > a:focus {';
                $css .= $style;
                //				$css .= 'display:flex;';
                                $css .= 'justify-content:' . $nav_font_position . ';';
                                if($active_show_boxshadow) {
                                    $css .= 'box-shadow: ' . $active_tab_box_shadow . ';';
                                }
                                if($openbg_active==1){
                                    $css .='background:url('.$bgImage_active.')no-repeat center center/100%;';
                                }

                                $css .= '}';
            }
            $css .= $addon_id . ' .jwpf-nav-custom {';
            $css .= 'width:'. $nav_width . '%;';
            $css .= $nav_gutter_right;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            $css .= 'width:' . (100 - $nav_width) . '%;';
            $css .= $nav_gutter_left;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style;
            $css .= 'box-shadow:' . $box_shadow . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= 'display: flex;';
            $css .= $font_style;
            // $css .= 'box-shadow:' . $box_shadow . ';';
            if($openbg==1){
                $css .= 'background:url('.$bgImage.')no-repeat center center/100%;';
            }

            $css .= '}';

            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin;
            if($openbg==1){
                $css .= $bgmargin;
            }
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style;
            $css .= '}';
            //Nav Hover Style
            $hover_style = '';
            $hover_style .= (isset($settings->hover_tab_color) && $settings->hover_tab_color) ? 'color: ' . $settings->hover_tab_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_border_width) && trim($settings->hover_tab_border_width)) ? 'border-width: ' . $settings->hover_tab_border_width . ';border-style: solid;' : '';
            $hover_style .= (isset($settings->hover_tab_border_color) && $settings->hover_tab_border_color) ? 'border-color: ' . $settings->hover_tab_border_color . ';' : '';
            $hover_style .= (isset($settings->hover_tab_bg) && $settings->hover_tab_bg) ? 'background-color: ' . $settings->hover_tab_bg . ';' : '';

            if ($hover_style) {
                $css .= $addon_id . ' .jwpf-nav-custom > li > a:hover,' . $addon_id . ' .jwpf-nav-custom > li > a:focus,' . $addon_id . ' .jwpf-nav-custom > li > a::before{';
                $css .= $hover_style;

                if($openbg_hover==1){
                    $css .='background:url('.$bgImage_hover.')no-repeat center center/100%;';
                }

                $css .= '}';
            }

            //Icon hover and active color
            $icon_color_hover = (isset($settings->icon_color_hover) && $settings->icon_color_hover) ? 'color: ' . $settings->icon_color_hover . ';' : '';
            if ($icon_color_hover) {
                $css .= $addon_id . ' .jwpf-nav-custom > li > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom > li > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_hover;
                $css .= '}';
            }
            $icon_color_active = (isset($settings->icon_color_active) && $settings->icon_color_active) ? 'color: ' . $settings->icon_color_active . ';' : '';
            if ($icon_color_active) {
                $css .= $addon_id . ' .jwpf-nav-custom > li.active > a > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom > li.active > a:hover  > .jwpf-tab-icon,' . $addon_id . ' .jwpf-nav-custom > li.active > a:focus > .jwpf-tab-icon {';
                $css .= $icon_color_active;
                $css .= '}';
            }

            //数字相关
            $nav_open_num = (isset($settings->nav_open_num) && $settings->nav_open_num) ? $settings->nav_open_num : 0;
            $nav_num_color = (isset($settings->nav_num_color) && $settings->nav_num_color) ? $settings->nav_num_color : '';

            $nav_num_fontsize_md = (isset($settings->nav_num_fontsize) && $settings->nav_num_fontsize) ? $settings->nav_num_fontsize : '';
            $nav_num_fontsize_sm = (isset($settings->nav_num_fontsize_sm) && $settings->nav_num_fontsize_sm) ? $settings->nav_num_fontsize_sm : '';
            $nav_num_fontsize_xs = (isset($settings->nav_num_fontsize_xs) && $settings->nav_num_fontsize_xs) ? $settings->nav_num_fontsize_xs : '';

            if($nav_open_num == 1) {
                $css .= $addon_id . ' .jwpf-nav-custom a .tab-num {
                    display: inline-block;
                    vertical-align: middle;';
                $css .= 'color: ' . $nav_num_color . ';';
                $css .= 'font-size: ' . $nav_num_fontsize_md . 'px;';
                $css .= 'font-family: Bahnschrift;';
                $css .= 'margin-right: 20px;';
                $css .= '}';

                $css .= $addon_id . ' .jwpf-nav-custom a .tab-a-content {';
                $css .= 'display: inline-block;
                        vertical-align: middle;';
                $css .= '}';
            }

            // 选中背景图相关
            foreach ($settings->jw_tab_item as $key => $tab) {
                if($tab->active_bg_image) {
                    $css .= $addon_id . ' .jwpf-nav-custom > li:nth-child(' . ($key + 1) .').active > a,
                    ' . $addon_id . ' .jwpf-nav-custom > li:nth-child(' . ($key + 1) .').active > a:hover,
                    ' . $addon_id . ' .jwpf-nav-custom > li:nth-child(' . ($key + 1) .').active > a:focus {';
                        $css .= 'background-image: url(' . $tab->active_bg_image . ');
                        background-repeat: no-repeat;';
                    $css .= '}';
                }
            }

            // 右侧箭头
            if(isset($settings->nav_open_arrow) && $settings->nav_open_arrow == 1) {
                $css .= $addon_id . ' .jwpf-nav-custom > li > a ,
                ' . $addon_id . ' .jwpf-nav-custom > li > a:hover,
                ' . $addon_id . ' .jwpf-nav-custom > li > a:focus {';
                    $css .= 'display: flex;';
                    $css .= 'align-items: center;';
                    $css .= 'justify-content: space-between !important;';
                $css .= '}';

                $css .= $addon_id . ' .jwpf-nav-custom li a .tab-a-arrow {';
                    $css .= 'display: inline-block;
                    vertical-align: middle;';
                $css .= '}';
                $css .= $addon_id . ' .jwpf-nav-custom li a .tab-a-arrow .right-arrow.actives {';
                    $css .= 'display: none;';
                $css .= '}';

                $css .= $addon_id . ' .jwpf-nav-custom > li.active > a .tab-a-arrow .right-arrow,
                ' . $addon_id . ' .jwpf-nav-custom > li.active > a:hover .tab-a-arrow .right-arrow,
                ' . $addon_id . ' .jwpf-nav-custom > li.active > a:focus .tab-a-arrow .right-arrow {';
                $css .= 'display: none;';
                $css .= '}';

                $css .= $addon_id . ' .jwpf-nav-custom > li.active > a .tab-a-arrow .right-arrow.actives,
                ' . $addon_id . ' .jwpf-nav-custom > li.active > a:hover .tab-a-arrow .right-arrow.actives,
                ' . $addon_id . ' .jwpf-nav-custom > li.active > a:focus .tab-a-arrow .right-arrow.actives {';
                    $css .= 'display: block;';
                $css .= '}';
            }

            // 导航副标题
            $nav_sub_color = (isset($settings->nav_sub_color) && $settings->nav_sub_color) ? $settings->nav_sub_color : '';
            $hover_nav_sub_color = (isset($settings->hover_nav_sub_color) && $settings->hover_nav_sub_color) ? $settings->hover_nav_sub_color : '';
            $active_nav_sub_color = (isset($settings->active_nav_sub_color) && $settings->active_nav_sub_color) ? $settings->active_nav_sub_color : '';

            $nav_sub_fontsize_md = (isset($settings->nav_sub_fontsize) && $settings->nav_sub_fontsize) ? $settings->nav_sub_fontsize : '';
            $nav_sub_fontsize_sm = (isset($settings->nav_sub_fontsize_sm) && $settings->nav_sub_fontsize_sm) ? $settings->nav_sub_fontsize_sm : '';
            $nav_sub_fontsize_xs = (isset($settings->nav_sub_fontsize_xs) && $settings->nav_sub_fontsize_xs) ? $settings->nav_sub_fontsize_xs : '';

            $nav_sub_lineheight_md = (isset($settings->nav_sub_lineheight) && $settings->nav_sub_lineheight) ? $settings->nav_sub_lineheight : '';
            $nav_sub_lineheight_sm = (isset($settings->nav_sub_lineheight_sm) && $settings->nav_sub_lineheight_sm) ? $settings->nav_sub_lineheight_sm : '';
            $nav_sub_lineheight_xs = (isset($settings->nav_sub_lineheight_xs) && $settings->nav_sub_lineheight_xs) ? $settings->nav_sub_lineheight_xs : '';

            $css .= $addon_id . ' .jwpf-nav-custom a .jwpf-tab-subtitle {';
            $css .= 'color: ' . $nav_sub_color . ';';
            $css .= 'font-size: ' . $nav_sub_fontsize_md . 'px;';
            $css .= 'line-height: ' . $nav_sub_lineheight_md . 'px;';
            $css .= '}';

            $css .= $addon_id . ' .jwpf-nav-custom > li > a:hover .jwpf-tab-subtitle,
            ' . $addon_id . ' .jwpf-nav-custom > li > a:focus .jwpf-tab-subtitle {';
            $css .= 'color: ' . $hover_nav_sub_color . ';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom > li.active > a .jwpf-tab-subtitle,
            '. $addon_id . ' .jwpf-nav-custom > li.active > a:hover .jwpf-tab-subtitle,
            ' . $addon_id . ' .jwpf-nav-custom > li.active > a:focus .jwpf-tab-subtitle {';
            $css .= 'color: ' . $active_nav_sub_color . ';';
            $css .= '}';

            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
                $css .= $addon_id . ' .jwpf-nav-custom a .jwpf-tab-subtitle {';
                $css .= 'font-size: ' . $nav_sub_fontsize_sm . 'px;';
                $css .= 'line-height: ' . $nav_sub_lineheight_sm . 'px;';
                $css .= '}';
                if($nav_open_num == 1) {
                    $css .= $addon_id . ' .jwpf-nav-custom a .tab-num {';
                    $css .= 'font-size: ' . $nav_num_fontsize_sm . 'px;';
                    $css .= '}';
                }
            $css .= '}';
            $css .= '@media (max-width: 767px) {';
                $css .= $addon_id . ' .jwpf-nav-custom a .jwpf-tab-subtitle {';
                $css .= 'font-size: ' . $nav_sub_fontsize_xs . 'px;';
                $css .= 'line-height: ' . $nav_sub_lineheight_xs . 'px;';
                $css .= '}';
                if($nav_open_num == 1) {
                    $css .= $addon_id . ' .jwpf-nav-custom a .tab-num {';
                    $css .= 'font-size: ' . $nav_num_fontsize_xs . 'px;';
                    $css .= '}';
                }
            $css .= '}';

            // 是否一直展示副标题
            $show_subtitle_always = (isset($settings->show_subtitle_always) && ($settings->show_subtitle_always || $settings->show_subtitle_always == 0)) ? $settings->show_subtitle_always : 0;

            if($show_subtitle_always){
                $css .= $addon_id . ' .jwpf-nav-custom a .jwpf-tab-subtitle{
                    display: block;
                }';
            }

            // 是否自定义边框
            $custom_border_settings = (isset($settings->custom_border_settings) && ($settings->custom_border_settings || $settings->custom_border_settings == 0)) ? $settings->custom_border_settings : 0;
            $custom_border_styles = (isset($settings->custom_border_styles) && $settings->custom_border_styles) ? $settings->custom_border_styles : 'style1';
            $custom_border_height = (isset($settings->custom_border_height) && $settings->custom_border_height) ? $settings->custom_border_height : 70;
            $custom_border_width = (isset($settings->custom_border_width) && $settings->custom_border_width) ? $settings->custom_border_width : 2;
            $nav_border_color = (isset($settings->nav_border_color) && $settings->nav_border_color) ? $settings->nav_border_color : '';

            if($custom_border_settings){
                if($custom_border_styles === 'style1'){
                    $css .= $addon_id . ' .jwpf-nav-custom a{
                        position: relative;
                        border-width: 0 0 0 0;
                    }';
                    $css .= $addon_id . ' .jwpf-nav-custom>li:not(:last-of-type) a::after{
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        margin: auto;
                        content: "";
                        width: '.$custom_border_width.'px;
                        height: '.$custom_border_height.'%;
                        background: '.$nav_border_color.';
                        display: block;
                    }';
                }
            }

            $open_line_animate = (isset($settings->open_line_animate) && $settings->open_line_animate) ? $settings->open_line_animate : 0;
            if($open_line_animate == 1){
                $hover_tab_border_color = (isset($settings->hover_tab_border_color) && $settings->hover_tab_border_color) ? $settings->hover_tab_border_color : '';

                $css.= $addon_id.' .jwpf-nav-custom > li > a::after{
                    content: "";
                    display: block;
                    position: absolute;
                    left: 50%;
                    bottom: 0;
                    width: 0;
                    height: 2px;
                    background: '.$hover_tab_border_color.';
                    content: "";
                    transition: 0.5s;
                    transform: translateX(-50%);
                }
                '.$addon_id.' .jwpf-nav-custom > li > a{
                    position: relative;
                }
                '.$addon_id.' .jwpf-nav-custom > li.active > a, '.$addon_id.' .jwpf-nav-custom > li.active > a:hover, '.$addon_id.' .jwpf-nav-custom > li.active > a:focus,
                '.$addon_id.' .jwpf-nav-custom > li > a, '.$addon_id.' .jwpf-nav-custom > li > a:hover, '.$addon_id.' .jwpf-nav-custom > li > a:focus{
                    border: none;
                    border-color: transparent;
                }
                '.$addon_id.' .jwpf-nav-custom > li > a:hover::after,'.$addon_id.' .jwpf-nav-custom > li.active > a::after, '.$addon_id.' .jwpf-nav-custom > li.active > a:hover::after, '.$addon_id.' .jwpf-nav-custom > li.active > a:focus::after{
                    width: 100%;
                }';
            }
        }
        if (!empty($font_style_sm) || !empty($nav_width_sm) || !empty($content_style_sm) || !empty($nav_margin_sm) || !empty($image_style_sm)) {
            $css .= '@media (min-width: 768px) and (max-width: 991px) {';

            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_sm)) {
                $css .= 'width:'. $nav_width_sm . '%;';
            }
            $css .= $nav_gutter_right_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_sm) && $nav_width_sm != 100) {
                $css .= 'width:' . (100 - $nav_width_sm) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_sm;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_sm;
            $css .= '}';

            $css .= '}';
        }
        if (!empty($font_style_xs) || !empty($nav_width_xs) || !empty($content_style_xs) || !empty($nav_margin_xs) || !empty($image_style_xs)) {
            $css .= '@media (max-width: 767px) {';

            $css .= $addon_id . ' .jwpf-nav-custom {';
            if (!empty($nav_width_xs)) {
                $css .= 'width:'. $nav_width_xs. '%;';
            }
            $css .= $nav_gutter_right_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content {';
            if (!empty($nav_width_xs) && $nav_width_xs != 100) {
                $css .= 'width:' . (100 - $nav_width_xs) . '%;';
            } else {
                $css .= 'width: 100%;';
            }
            $css .= $nav_gutter_left_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-custom-content > div {';
            $css .= $content_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-icon {';
            $css .= $icon_style_xs;
            $css .= '}';
            $css .= $addon_id . ' .jwpf-tab-image {';
            $css .= $image_style_xs;
            $css .= '}';

            $css .= '}';
        }
        // 除自定义导航外的背景颜色 以及导航块的位置
        if($tab_style != 'custom') {
            $style .= (isset($settings->nav_all_bg_color) && $settings->nav_all_bg_color) ? 'background-color: ' . $settings->nav_all_bg_color . ';' : '';
            $style .= (isset($settings->nav_all_positon) && $settings->nav_all_positon) ? 'justify-content: ' . $settings->nav_all_positon . ';' : '';
            
            $tab_color_repair = (isset($settings->tab_color_repair) && $settings->tab_color_repair) ? $settings->tab_color_repair : '';

            if($style) {
                $css .= $addon_id . ' .minUl {';
                $css .= 'display:flex;';
                $css .= $style;
                $css .= 'color: '.$tab_color_repair.';';
                $css .= '}';
            }
        }
        //自定义导航条背景图
        if($tab_style == 'custom') {
            if($opennavbg==1) {
                $css .= $addon_id . ' .minUl {';
                $css .= 'background:url('.$navbgImage.')no-repeat center center/100% ;';
                $css .= '}';
            }
        }

        $css .= $addon_id . ' .jwpf-nav-custom li {';

        $css .= 'text-align:'. $nav_width . ';';
        $css .= '}';
        if($nav_position == 'nav-top') {
            $css .= $addon_id . ' .minUl {';
            $css .= 'flex-wrap:wrap;';
            $css .= '}';
            $css .= $addon_id . ' .minBox {';
            $css .= 'flex-direction: column;';
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-nav-custom {';
            $css .= 'width:100%;display: flex;padding-right:0px;justify-content:' .  $nav_block_positon;
            $css .= '}';

            $css .= $addon_id . ' .minBox .jwpf-tab-custom-content{';
            $css .= 'width:100%;margin-top:'.$nav_gutter.'px;padding-left:0px;';
            $css .= '}';

            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_md;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'justify-content: '.$nav_font_position.';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_md;
            $css .= 'width:'. $nav_width . '%;';
            $css .= '}';

            $css .= '@media (min-width: 768px) and (max-width: 991px) {';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_sm;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'justify-content: '.$nav_font_position.';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_sm;
            $css .= 'width:'. $nav_width_sm. '%;';
            $css .= '}';
            $css .= '}';

            $css .= '@media (max-width: 768px) {';
            $css .= $addon_id . ' .jwpf-nav-custom a {';
            $css .= $font_style_xs;
            $css .= 'width: 100%;';
            $css .= 'height: 100%;';
            $css .= 'display: flex;';
            $css .= 'justify-content: '.$nav_font_position.';';
            $css .= '}';
            $css .= $addon_id . ' .jwpf-nav-custom li {';
            $css .= $nav_margin_xs;
            $css .= 'width:'. $nav_width. '%;';
            $css .= '}';
            $css .= '}';
        }
        return $css;
    }

    public static function getTemplate()
    {
        $iddd = $_COOKIE["xx"];
        $output = '
		<style type="text/css">
            <#
                var openbg = data.openbg || 0;
                var bgImage = data.bgImage || "https://oss.lcweb01.cn/joomla/20211104/4c74cc44c34854cd4cf1e6050f3a0025.png";
                var openbg_hover = data.openbg_hover || 0;
                var bgImage_hover = data.bgImage_hover || "https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png";
                var openbg_active = data.openbg_active || 0;
                var bgImage_active = data.bgImage_active || "https://oss.lcweb01.cn/joomla/20211104/fb52a3d43059bfb9d1a8fe935f3dc8fe.png";
                var opennavbg = data.opennavbg || 0;
                var navbgImage = data.navbgImage || "https://oss.lcweb01.cn/joomla/20211104/2b2cecb35a36294c2301732801fc7662.jpg";


                var navPosition = data.nav_position || "nav-left";
                var box_shadow = "";
                if(data.show_boxshadow){
                    box_shadow += (!_.isEmpty(data.active_shadow_horizontal) && data.shadow_horizontal) ?  data.shadow_horizontal + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_vertical) && data.shadow_vertical) ?  data.shadow_vertical + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_blur) && data.shadow_blur) ?  data.shadow_blur + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_spread) && data.shadow_spread) ?  data.shadow_spread + \'px \' : "0 ";
                    box_shadow += (!_.isEmpty(data.shadow_color) && data.shadow_color) ?  data.shadow_color : "rgba(0, 0, 0, .5)";
                }
                if(data.style == "pills"){
            #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:hover,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-pills > li.active > a:focus{
                        color: {{ data.active_tab_color }};
                        background-color: {{ data.active_tab_bg }};
                    }
			<# } #> 
            <# if(data.nav_position == "nav-top"){ #>
                #jwpf-addon-{{ data.id }} .minUl {
                    display:flex;
                    flex-wrap:wrap;
                    justify-content:{{data.nav_block_positon}};
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top {
                    display:flex;
                    flex-direction:column;
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-nav-top .jwpf-tab-custom-content {
                    width:100% ;
                    margin-top:{{data.nav_gutter}}px;
                    padding-left:0px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    display:flex;
                    width:100% !important;
                    padding-right:0px !important;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                    display:flex;
                    width:{{data.nav_width}}%;
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                    border:none !important;
                    width:100%;
                    display:flex;
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width.sm}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        border:none !important;
                        width:100%;
                        display:flex;
                    } 
                }
                @media (max-width: 768px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        display:flex;
                        width:{{data.nav_width}}%;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        border:none !important;
                        width:100%;
                        display:flex;
                    } 
                }
            <# } #>
			<# if(data.style == "lines"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-lines > li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-bottom-color: {{ data.active_tab_bg }};
                }
			<# } #>
            <# if (data.style == "custom") { 
                var active_tab_box_shadow = "box-shadow: ";
                if(data.active_show_boxshadow){
                    active_tab_box_shadow += (!_.isEmpty(data.active_shadow_horizontal) && data.active_shadow_horizontal) ?  data.active_shadow_horizontal + \'px \' : "0 ";
                    active_tab_box_shadow += (!_.isEmpty(data.active_shadow_vertical) && data.active_shadow_vertical) ?  data.active_shadow_vertical + \'px \' : "0 ";
                    active_tab_box_shadow += (!_.isEmpty(data.active_shadow_blur) && data.active_shadow_blur) ?  data.active_shadow_blur + \'px \' : "0 ";
                    active_tab_box_shadow += (!_.isEmpty(data.active_shadow_spread) && data.active_shadow_spread) ?  data.active_shadow_spread + \'px \' : "0 ";
                    active_tab_box_shadow += (!_.isEmpty(data.active_shadow_color) && data.active_shadow_color) ?  data.active_shadow_color : "rgba(0, 0, 0, .5)";
                }
                active_tab_box_shadow += ";";
            #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:focus{
                    color: {{ data.hover_tab_color }};
                    border-width: {{ data.hover_tab_border_width }};
                    border-color: {{ data.hover_tab_border_color }};
                    background-color: {{ data.hover_tab_bg }};
                    <# if(openbg_hover==1){ #>
                       background:url({{bgImage_hover}})no-repeat center center/100%;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:hover,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:focus{
                    color: {{ data.active_tab_color }};
                    border-width: {{ data.active_tab_border_width }};
                    border-color: {{ data.active_tab_border_color }};
                    <# if (data.active_tab_border_radius) { #>
                    border-radius: {{ data.active_tab_border_radius}};
                    <# } #>
                    background-color: {{ data.active_tab_bg }};

                    <# if(openbg_active==1){ #>
                       background:url({{bgImage_active}})no-repeat center center/100%;
                    <# } #>
                    {{active_tab_box_shadow}}
                    display:flex;
                    justify-content:{{data.nav_font_position}};
                }
                <# _.each(data.jw_tab_item, function(tab, key){ 
                    if(tab.active_bg_image) {
                #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li:nth-child({{key + 1}}).active > a,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li:nth-child({{key + 1}}).active > a:hover,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li:nth-child({{key + 1}}).active > a:focus{
                        background-image: url({{tab.active_bg_image}});
                        background-repeat: no-repeat;
                    }
                <# }
                }); #>
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:hover .jwpf-tab-subtitle,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:focus .jwpf-tab-subtitle {
                    color: {{ data.hover_nav_sub_color }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a .jwpf-tab-subtitle,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:hover .jwpf-tab-subtitle,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:focus .jwpf-tab-subtitle {
                    color: {{ data.active_nav_sub_color }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_hover }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:hover > .jwpf-tab-icon,
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:focus > .jwpf-tab-icon{
                    color: {{ data.icon_color_active }};
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                    <# if(_.isObject(data.nav_margin)){ #>
                        padding: {{data.nav_margin.xs}};
                    <# } else { #>
                        padding: {{data.nav_margin}};
                    <# } #>

                    <# if(openbg==1){ #>
                        <# if(_.isObject(data.bgmargin)){ #>
                            margin: {{data.bgmargin.xs}};
                        <# } else { #>
                            margin: {{data.bgmargin}};
                        <# } #>
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                    <# if(_.isObject(data.nav_fontsize)){ #>
                        font-size: {{data.nav_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_fontsize}}px;
                    <# } #>
                    <# if(_.endsWith(data.nav_border, "x")) { #>
                        border-width: {{data.nav_border}};
                        border-style: solid;
                    <# } else { #>
                        border: {{_.trim(data.nav_border, " ")}}px solid;
                    <# } #>
                    border-color: {{data.nav_border_color}};
                    color: {{data.nav_color}};
                    background-color: {{data.nav_bg_color}};
                    <# if(openbg==1){ #>
                       background:url({{bgImage}})no-repeat center center/100%;
                    <# } #>
                    border-radius: {{data.nav_border_radius}}px;
                    display:flex;
                    justify-content:{{data.nav_font_position}};
                    <# if(_.isObject(data.nav_padding)){ #>
                        padding: {{data.nav_padding.md}};
                    <# } else { #>
                        padding: {{data.nav_padding}};
                    <# } #>
                    /*box-shadow: {{box_shadow}};*/
                    font-family:{{data.nav_font_family}};
                    <# if(_.isObject(data.nav_lineheight)){ #>
                        line-height:{{data.nav_lineheight.md}}px;
                    <# } else { #>
                        line-height: {{data.nav_lineheight}}px;
                    <# } 
                        if(_.isObject(data.nav_font_style)){
                            if(data.nav_font_style.underline){
                        #>
                            text-decoration:underline;
                        <# }
                        if(data.nav_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.nav_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.nav_font_style.weight){
                        #>
                            font-weight:{{data.nav_font_style.weight}};
                        <# }
                    } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .jwpf-tab-subtitle {
                    color: {{data.nav_sub_color}};
                    <# if(_.isObject(data.nav_sub_fontsize)){ #>
                        font-size: {{data.nav_sub_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.nav_sub_fontsize}}px;
                    <# } #>
                    <# if(_.isObject(data.nav_sub_lineheight)){ #>
                        line-height:{{data.nav_sub_lineheight.md}}px;
                    <# } else { #>
                        line-height: {{data.nav_sub_lineheight}}px;
                    <# }  #>
                }
                <# if(data.nav_open_num == 1) { #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-num {
                        display: inline-block;
                        vertical-align: middle;
                        color: {{data.nav_num_color}};
                        <# if(_.isObject(data.nav_num_fontsize)){ #>
                            font-size: {{data.nav_num_fontsize.md}}px;
                        <# } else { #>
                            font-size: {{data.nav_num_fontsize}}px;
                        <# } #>
                        font-family: Bahnschrift;
                        margin-right: 20px;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-a-content {
                        display: inline-block;
                        vertical-align: middle;
                    }
                <# } #>
                <# if(data.nav_open_arrow == 1) { #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-a-arrow {
                        display: inline-block;
                        vertical-align: middle;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-a-arrow  .right-arrow.actives {
                        display: none;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a .tab-a-arrow .right-arrow,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:hover .tab-a-arrow .right-arrow,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:focus  .tab-a-arrow .right-arrow {
                        display: none;
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a .tab-a-arrow .right-arrow.actives,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:hover .tab-a-arrow .right-arrow.actives,
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom > li.active > a:focus  .tab-a-arrow .right-arrow.actives {
                        display: block;
                    }
                <# } #>
                #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                    <# if(data.icon_color){ #>
                        color:{{data.icon_color}};
                    <# } #>
                    <# if(_.isObject(data.icon_fontsize)){ #>
                        font-size: {{data.icon_fontsize.md}}px;
                    <# } else { #>
                        font-size: {{data.icon_fontsize}}px;
                    <# } #>
                    <# if(_.isObject(data.icon_margin)){ #>
                        margin: {{data.icon_margin.md}};
                    <# } else { #>
                        margin: {{data.icon_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                    <# if(_.isObject(data.image_height)){ #>
                        height: {{data.image_height.md}}px;
                    <# } else { #>
                        height: {{data.image_height}}px;
                    <# }
                    if(_.isObject(data.image_width)){
                    #>
                        width: {{data.image_width.md}}px;
                    <# } else { #>
                        width: {{data.image_width}}px;
                    <# }
                    if(_.isObject(data.image_margin)){
                    #>
                        margin: {{data.image_margin.md}};
                    <# } else { #>
                        margin: {{data.image_margin}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                    <# if(_.isObject(data.nav_width)){ #>
                        width: {{data.nav_width.md}}%;
                    <# } else { #>
                        width: {{data.nav_width}}%;
                    <# }
                    if(navPosition == "nav-right") {
                    #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-left: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-left: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else { 
                        if(_.isObject(data.nav_gutter)){
                    #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# }
                    } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                    <# if(_.isObject(data.nav_width)){ #>
                        width: {{100-data.nav_width.md}}%;
                    <# } else { #>
                        width: {{100-data.nav_width}}%; 
                    <# } #>
                    <# if(navPosition == "nav-right"){ #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-right: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-right: {{data.nav_gutter}}px;
                        <# } #>
                    <# } else { #>
                        <# if(_.isObject(data.nav_gutter)){ #>
                            padding-left: {{data.nav_gutter.md}}px;
                        <# } else { #>
                            padding-left: {{data.nav_gutter}}px;
                        <# } #>
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                    background-color: {{data.content_backround}};
                    border: {{data.content_border}}px solid;
                    border-color: {{data.content_border_color}};
                    border-radius: {{data.content_border_radius}}px;
                    color: {{data.content_color}};
                    <# if(_.isObject(data.content_padding)){ #>
                        padding: {{data.content_padding.md}};
                    <# } else { #>
                        padding: {{data.content_padding}};
                    <# } #>
                    <# if(_.isObject(data.content_margin)){ #>
                        margin: {{data.content_margin.md}};
                    <# } else { #>
                        margin: {{data.content_margin}};
                    <# } #>
                    box-shadow: {{box_shadow}};
                    font-family:{{data.content_font_family}};
                    <# if(_.isObject(data.content_fontsize)){ #>
                        font-size:{{data.content_fontsize.md}}px;
                    <# }
                    if(_.isObject(data.content_lineheight)){ #>
                        line-height:{{data.content_lineheight.md}}px;
                    <# }
                    if(_.isObject(data.content_font_style)){
                        if(data.content_font_style.underline){
                    #>
                            text-decoration:underline;
                        <# }
                        if(data.content_font_style.italic){
                        #>
                            font-style:italic;
                        <# }
                        if(data.content_font_style.uppercase){
                        #>
                            text-transform:uppercase;
                        <# }
                        if(data.content_font_style.weight){
                        #>
                            font-weight:{{data.content_font_style.weight}};
                        <# }
                    } #>
                }
                @media (min-width: 768px) and (max-width: 991px) {
                    
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.sm}};
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.sm}}px;
                        <# } #>
                    }
                    <# if(data.nav_open_num == 1) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-num {
                            <# if(_.isObject(data.nav_num_fontsize)){ #>
                                font-size: {{data.nav_num_fontsize.sm}}px;
                            <# } else { #>
                                font-size: {{data.nav_num_fontsize}}px;
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .jwpf-tab-subtitle {
                        <# if(_.isObject(data.nav_sub_fontsize)){ #>
                            font-size: {{data.nav_sub_fontsize.sm}}px;
                        <# } else { #>
                            font-size: {{data.nav_sub_fontsize}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_sub_lineheight)){ #>
                            line-height:{{data.nav_sub_lineheight.sm}}px;
                        <# } else { #>
                            line-height: {{data.nav_sub_lineheight}}px;
                        <# }  #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.sm}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.sm}};
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: {{data.nav_width.sm}}%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.sm !== "100"){ #>
                                width: {{100-data.nav_width.sm}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.sm}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.sm}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.sm}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.sm}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.sm}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>
                            line-height:{{data.content_lineheight.sm}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.sm}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.sm}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.sm}};
                        <# } #>
                    }
                }
                @media (max-width: 767px) {
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li {
                        <# if(_.isObject(data.nav_margin)){ #>
                            padding: {{data.nav_margin.xs}};
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a {
                        <# if(_.isObject(data.nav_fontsize)){ #>
                            font-size: {{data.nav_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_padding)){ #>
                            padding: {{data.nav_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.nav_lineheight)){ #>
                            line-height:{{data.nav_lineheight.xs}}px;
                        <# } #>
                    }
                    <# if(data.nav_open_num == 1) { #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .tab-num {
                            <# if(_.isObject(data.nav_num_fontsize)){ #>
                                font-size: {{data.nav_num_fontsize.xs}}px;
                            <# } else { #>
                                font-size: {{data.nav_num_fontsize}}px;
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom li a .jwpf-tab-subtitle {
                        <# if(_.isObject(data.nav_sub_fontsize)){ #>
                            font-size: {{data.nav_sub_fontsize.xs}}px;
                        <# } else { #>
                            font-size: {{data.nav_sub_fontsize}}px;
                        <# } #>
                        <# if(_.isObject(data.nav_sub_lineheight)){ #>
                            line-height:{{data.nav_sub_lineheight.xs}}px;
                        <# } else { #>
                            line-height: {{data.nav_sub_lineheight}}px;
                        <# }  #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-icon {
                        <# if(_.isObject(data.icon_fontsize)){ #>
                            font-size: {{data.icon_fontsize.xs}}px;
                        <# } #>
                        <# if(_.isObject(data.icon_margin)){ #>
                            margin: {{data.icon_margin.xs}};
                        <# } #>
                    }
                    <# if(_.isObject(data.nav_width)){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom {
                            width: {{data.nav_width.xs}}%;
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content {
                            <# if(data.nav_width.xs !== "100"){ #>
                                width: {{100-data.nav_width.xs}}%;
                            <# } else { #>
                                width: 100%;
                            <# } #>
                            <# if(_.isObject(data.nav_gutter)){ #>
                                <# if(navPosition == "nav-right") { #>
                                    padding-right: {{data.nav_gutter.xs}}px;
                                <# } else { #>
                                    padding-left: {{data.nav_gutter.xs}}px;
                                <# } #>
                            <# } #>
                        }
                    <# } #>
                    #jwpf-addon-{{ data.id }} .jwpf-tab-custom-content > div {
                        <# if(_.isObject(data.content_padding)){ #>
                            padding: {{data.content_padding.xs}};
                        <# } #>
                        <# if(_.isObject(data.content_margin)){ #>
                            margin: {{data.content_margin.xs}};
                        <# }
                        if(_.isObject(data.content_fontsize)){ #>
                            font-size:{{data.content_fontsize.xs}}px;
                        <# }
                        if(_.isObject(data.content_lineheight)){ #>
                            line-height:{{data.content_lineheight.xs}}px;
                        <# } #>
                    }
                    #jwpf-addon-{{ data.id }} .jwpf-tab-image {
                        <# if(_.isObject(data.image_height)){ #>
                            height: {{data.image_height.xs}}px;
                        <# }
                        if(_.isObject(data.image_width)){
                        #>
                            width: {{data.image_width.xs}}px;
                        <# }
                        if(_.isObject(data.image_margin)){
                        #>
                            margin: {{data.image_margin.xs}};
                        <# } #>
                    }
                }

                /*是否一直显示副标题*/
                <# 
                    var show_subtitle_always = data.show_subtitle_always || 0;
                #>

                <# if(show_subtitle_always){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-nav-custom a .jwpf-tab-subtitle{
                        display: block;
                    }
                <# } #>
                <#
                    var custom_border_settings = data.custom_border_settings || 0;
                    var custom_border_styles = data.custom_border_styles || "style1";
                    var custom_border_height = data.custom_border_height || 70;
                    var custom_border_width = data.custom_border_width || 2;
                    var nav_border_color = data.nav_border_color || "";
                #>

                <# if(custom_border_settings){
                    if(custom_border_styles === "style1"){ #>
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom a{
                            position: relative;
                            border-width: 0 0 0 0;
                        }
                        #jwpf-addon-{{ data.id }} .jwpf-nav-custom>li:not(:last-of-type) a::after{
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                            content: "";
                            width: {{custom_border_width}}px;
                            height: {{custom_border_height}}%;
                            background: {{nav_border_color}};
                            display: block;
                        }
                    <# }
                } #>
            <# } #>
            <# if(data.style == "type06"){ 
                var active_color_type6 = data.active_color_type6  || "#d90029";
                var tab_width_type6 = data.tab_width_type6  || 46;
                var tab_margin_top_type6 = data.tab_margin_top_type6  || 166;
            #>
                #jwpf-addon-{{ data.id }} .type6{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                #jwpf-addon-{{ data.id }} .type6 .jwpf-addon-tab{
                    min-width: {{tab_width_type6}}%;
                    margin-top: {{tab_margin_top_type6}}px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-type06-tab{
                    flex-grow: 1;
                }
                #jwpf-addon-{{ data.id }} *{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                #jwpf-addon-{{ data.id }} a,
                #jwpf-addon-{{ data.id }} a:hover{
                    text-decoration: none;
                    color: inherit;
                }
                #jwpf-addon-{{ data.id }} .ind8-a3 {
                    width: 214px;
                    position: relative;
                    margin-bottom: 66px;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4 {
                    width: 90px;
                    height: 48px;
                    position: relative;
                    margin-left: 28px;
                    cursor: pointer;
                    flex-grow: 1;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4 > div:nth-child(2) {
                    font-size: 22px;
                    line-height: 48px;
                    color: #454545;
                    font-weight: bold;
                    transition: 0.5s;
                    white-space: nowrap;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4.on1 > div:nth-child(2) {
                    color: {{active_color_type6}};
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4 > div:nth-child(1) {
                    width: 0;
                    height: 5px;
                    background: {{active_color_type6}};
                    position: absolute;
                    top: calc(50% - 5px/2);
                    left: -28px;
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4.on1 > div:nth-child(1) {
                    width: 5px;
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4 > div:nth-child(3) {
                    width: 0;
                    height: 5px;
                    background: {{active_color_type6}};
                    position: absolute;
                    top: calc(50% - 5px/2);
                    left: 106px;
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .ind8-a4.on1 > div:nth-child(3) {
                    width: 80px;
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .ind8-a5 {
                    position: relative;
                }
                #jwpf-addon-{{ data.id }} .f_more {
                    font-size: 16px;
                    width: 160px;
                    height: 55px;
                    border: solid 1px #949494;
                    border-radius: 26px;
                    text-align: center;
                    line-height: 50px;
                    display: block;
                    position: relative;
                    overflow: hidden;
                }
                #jwpf-addon-{{ data.id }} .f_more span{
                    position: relative;
                    z-index: 1;
                    transition: all 0.2s;
                }
                #jwpf-addon-{{ data.id }} .f_more::before {
                    content: "";
                    width: 0;
                    height: 100%;
                    background: {{active_color_type6}};
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    transition: 0.5s;
                }
                #jwpf-addon-{{ data.id }} .f_more::after {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    z-index: -2;
                    content: "";
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    border-radius: 28px;
                }
                #jwpf-addon-{{ data.id }} .f_more:hover, #jwpf-addon-{{ data.id }} .f_more.active {
                    color: #fff;
                    border-color: {{active_color_type6}};
                    transition-delay: 0.1s;
                }
                #jwpf-addon-{{ data.id }} .f_more:hover::before, .f_more.active::before {
                    width: 100%;
                    right: auto;
                    left: 0;
                }
                @media (max-width: 1023px){
                    #jwpf-addon-{{ data.id }} .type6{
                        display: block;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a3{
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4{
                        margin-left: 0;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a2 .t2-a1{
                        margin-bottom: 40px;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4 i{
                        width: 0;
                        transition: .3s;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4 > div{
                        position: static!important;
                        display: inline-block;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4.on1 > div:nth-of-type(3){
                        display: none;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4 > div:nth-child(1){
                        position: static!important;
                        width: 0;
                        height: 2px;
                        transition: all .3s;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4.on1 > div:nth-child(1){
                        width: 5px;
                        vertical-align: middle;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a4 > div:nth-child(2){
                        color: #333333;
                        font-weight: bolder;
                        font-size: 16px;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a5{
                        display: none;
                    }
                    #jwpf-addon-{{ data.id }} .phone-list{
                        width: 100%;
                        display: block;
                        overflow: hidden;
                        height: 360px;
                        position: relative;
                    }
                    #jwpf-addon-{{ data.id }} .ind8-a3{
                        margin-bottom: 26px;
                    }
                }
            <# } #>

            <# if (data.style != "custom") { #>
                #jwpf-addon-{{ data.id }} .minUl {
                    display:flex;
                    justify-content:{{data.nav_all_positon}};
                    background-color:{{data.nav_all_bg_color}};
                }
            <# } #>
            <# if (data.style == "custom") { #>
                <# if(opennavbg==1){ #>
                    #jwpf-addon-{{ data.id }} .minUl {
                        background:url({{navbgImage}})no-repeat center center/100% ;
                    }

                <# } #>
            <# } #>
		</style>
        <# if(data.style == "type06"){ #>
            <div class="type6">
                <div class="jwpf-addon jwpf-addon-tab {{ data.class }}">
                    <div role="tablist" class="ind8-a3 wow animated jwpf-nav jwpf-nav-{{data.style}}" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
                        <# _.each(data.jw_tab_item, function(tab, key){
                            var active = key === 0 ? "on1" : "";
                            ';
                                if(isset($_COOKIE["xx"]) && $_COOKIE["xx"]!='')
                                {
                                    $output.='var xx = '.$_COOKIE["xx"].' ;';
                                }
                                else
                                {
                                    $output.='var xx = 0 ;';
                                }
                            $output.='
                                    
                                    if(xx)
                                    {
                                        if(key == xx)
                                        {
                                            active = "active";
                                        }
                                    }
                                    else
                                    {
                                        if(key == 0)
                                        {
                                            active = "active";
                                        }
                                    }
                                    

                                    var nav_num = "";
                                    var title = "";
                                    var subtitle = "";
                                    var icon_top ="";
                                    var icon_bottom = "";
                                    var icon_right = "";
                                    var icon_left = "";
                                    var icon_block = "";
                                    
                                    let icon_arr = (typeof tab.icon !== "undefined" && tab.icon) ? tab.icon.split(" ") : "";
                                    let icon_name = icon_arr.length === 1 ? "fa "+tab.icon : tab.icon;

                                    var image_top ="";
                                    var image_bottom = "";
                                    var image_right = "";
                                    var image_left = "";
                                    
                                    var closeClick = tab.closeClick;
                                    
                                    if(tab.title){
                                        title = tab.title;
                                    }
                                    if(tab.subtitle){
                                        var style = "";
                                        if(data.wrap == 1) {
                                            style = "display: block;"
                                        }
                                        subtitle += `<span class="jwpf-tab-subtitle" style="${style}">${tab.subtitle}</span>`;
                                    }
                            #>
                            <div class="ind8-a4 {{active}}">
                                <div></div>
                                <div>{{title}}</div>
                                <div></div>
                            </div>
                        <# }); #> 
                    </div>
                    <div class="ind8-a5">
                        <# _.each(data.jw_tab_item, function(tab, key){
                            var active = key === 0 ? "on1" : "";
                            ';
                            if(isset($_COOKIE["xx"]) && $_COOKIE["xx"]!='')
                            {
                                $output.='var xx = '.$_COOKIE["xx"].' ;';
                            }
                            else
                            {
                                $output.='var xx = 0 ;';
                            }
                            $output.='
                                
                                if(xx)
                                {
                                    if(key == xx)
                                    {
                                        active = "active";
                                    }
                                }
                                else
                                {
                                    if(key == 0)
                                    {
                                        active = "active";
                                    }
                                }
                                

                                var nav_num = "";
                                var title = "";
                                var subtitle = "";
                                var icon_top ="";
                                var icon_bottom = "";
                                var icon_right = "";
                                var icon_left = "";
                                var icon_block = "";
                                
                                let icon_arr = (typeof tab.icon !== "undefined" && tab.icon) ? tab.icon.split(" ") : "";
                                let icon_name = icon_arr.length === 1 ? "fa "+tab.icon : tab.icon;

                                var image_top ="";
                                var image_bottom = "";
                                var image_right = "";
                                var image_left = "";
                                
                                var closeClick = tab.closeClick;
                                
                                if(tab.title){
                                    title = tab.title;
                                }
                                if(tab.subtitle){
                                    var style = "";
                                    if(data.wrap == 1) {
                                        style = "display: block;"
                                    }
                                    subtitle += `<span class="jwpf-tab-subtitle" style="${style}">${tab.subtitle}</span>`;
                                }
                                var display = key === 0 ? "block" : "none";
                        #>
                    
                            <div class="ind8-a6" style="display: {{display}};">
                                <a class="f_more" href="" target="">
                                    <span>进一步了解</span>
                                </a>
                            </div>
                        <# }); #> 
                    </div>
                </div>

                <div class="jwpf-addon-content  jwpf-tab jwpf-{{data.style}}-tab jwpf-tab-{{navPosition}}">
                    <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                        <# _.each(data.jw_tab_item, function(tab, key){ #>
                            <#
                            var active = "";
                            ';
                            if(isset($_COOKIE["xx"]) && $_COOKIE["xx"]!='')
                            {
                                $output.='var xx = '.$_COOKIE["xx"].' ;';
                            }
                            else
                            {
                                $output.='var xx = 0 ;';
                            }
                            $output.='
                            if(xx)
                            {
                                if(key == xx)
                                {
                                    active = "active in";
                                }
                            }
                            else
                            {
                                if(key == 0)
                                {
                                    active = "active in";
                                }
                            }
                            #>
                            <div id="jwpf-tab-{{ data.id }}{{ key }}" class="jwpf-tab-pane jwpf-fade {{ active }}">
                                <#
                                var htmlContent = "";
                                _.each(tab.content, function(content){
                                    htmlContent += content;
                                });
                                #>
                                {{{ htmlContent }}}
                            </div>
                        <# }); #>
                    </div>
                </div>
            </div>
        <# } else { #>
            <div class="jwpf-addon jwpf-addon-tab {{ data.class }}">
                <# if( !_.isEmpty( data.title )){ #>
                    <{{ data.heading_selector }} class="jwpf-addon-title">{{{ data.title }}}</{{ data.heading_selector }}>
                <# } 
                    let icon_postion = (data.nav_icon_postion == \'top\' || data.nav_icon_postion == \'bottom\') ? \'tab-icon-block\' : \'\';

                #>
                <div class="jwpf-addon-content  jwpf-tab jwpf-{{data.style}}-tab jwpf-tab-{{navPosition}}">
                    <ul class="jwpf-nav minUl jwpf-nav-{{ data.style }}">
                        <#
                            _.each(data.jw_tab_item, function(tab, key){
                                var active = "";
                        ';
                            if(isset($_COOKIE["xx"]) && $_COOKIE["xx"]!='')
                            {
                                $output.='var xx = '.$_COOKIE["xx"].' ;';
                            }
                            else
                            {
                                $output.='var xx = 0 ;';
                            }
                        $output.='
                                
                                if(xx)
                                {
                                    if(key == xx)
                                    {
                                        active = "active";
                                    }
                                }
                                else
                                {
                                    if(key == 0)
                                    {
                                        active = "active";
                                    }
                                }
                                

                                var nav_num = "";
                                var title = "";
                                var subtitle = "";
                                var icon_top ="";
                                var icon_bottom = "";
                                var icon_right = "";
                                var icon_left = "";
                                var icon_block = "";
                                
                                let icon_arr = (typeof tab.icon !== "undefined" && tab.icon) ? tab.icon.split(" ") : "";
                                let icon_name = icon_arr.length === 1 ? "fa "+tab.icon : tab.icon;

                                var image_top ="";
                                var image_bottom = "";
                                var image_right = "";
                                var image_left = "";
                                
                                var closeClick = tab.closeClick;
                                
                                if(!_.isEmpty(tab.image_or_icon) && tab.image_or_icon == "image" && tab.image){
                                    if(!_.isEmpty(tab.image) && tab.image && data.nav_image_postion == "top"){
                                        image_top = \'<img class="jwpf-tab-image tab-image-block" src="\' + tab.image + \'"/>\';
                                    } else if (!_.isEmpty(tab.icon) && tab.icon && data.nav_image_postion == "bottom") {
                                        image_bottom = \'<img class="jwpf-tab-image tab-image-block" src="\' + tab.image + \'"/>\';
                                    } else if (!_.isEmpty(tab.icon) && tab.icon && data.nav_image_postion == "right") {
                                        image_right = \'<img class="jwpf-tab-image" src="\' + tab.image + \'"/>\';
                                    } else {
                                        image_left = \'<img class="jwpf-tab-image" src="\' + tab.image + \'"/>\';
                                    }
                                } else if(icon_name) { 
                                    if(!_.isEmpty(tab.icon) && tab.icon && data.nav_icon_postion == "top"){
                                        icon_top = \'<span class="jwpf-tab-icon tab-icon-block"><i class="\' + icon_name + \'"></i></span>\';
                                    } else if (!_.isEmpty(tab.icon) && tab.icon && data.nav_icon_postion == "bottom") {
                                        icon_bottom = \'<span class="jwpf-tab-icon tab-icon-block"><i class="\' + icon_name + \'"></i></span>\';
                                    } else if (!_.isEmpty(tab.icon) && tab.icon && data.nav_icon_postion == "right") {
                                        icon_right = \'<span class="jwpf-tab-icon"><i class="\' + icon_name + \'"></i></span>\';
                                    } else {
                                        icon_left = \'<span class="jwpf-tab-icon"><i class="\' + icon_name + \'"></i></span>\';
                                    }
                                }
                                
                                
                                if(data.nav_open_num) {
                                    nav_num += \'<span class="tab-num">\';
                                    if(key < 9) {
                                        nav_num += "0";
                                    }
                                    nav_num += key + 1;
                                    nav_num += \'</span>\';
                                }
                                if(tab.title){
                                    title = tab.title;
                                }
                                if(tab.subtitle){
                                    var style = "";
                                    if(data.wrap == 1) {
                                        style = "display: block;"
                                    }
                                    subtitle += `<span class="jwpf-tab-subtitle" style="${style}">${tab.subtitle}</span>`;
                                }
                                if(data.nav_icon_postion == "top" || data.nav_icon_postion == "bottom" || data.nav_image_postion == "top" || data.nav_image_postion == "bottom"){
                                    icon_block = "tab-img-or-icon-block-wrap";
                                }
                                var display = "display: flex;";
                                if(data.wrap == 1) {
                                    //display = "display: block;";
                                }
                                var toggle = \'data-toggle="jwpf-tab"\';
                                if(closeClick == 1) { 
                                    toggle = "";
                                }
                                
                                var nav_arrow = "";
                                if(data.nav_open_arrow == 1) {
                                    display += "align-items: center;justify-content: space-between;";
                                    nav_arrow += "<div class=\'tab-a-arrow\'>";
                                    if(data.tab_arrow) {
                                        nav_arrow += "<img src=\'" + data.tab_arrow + "\' class=\'right-arrow\'>";
                                    }
                                    if(data.active_tab_arrow) {
                                        nav_arrow += "<img src=\'" + data.active_tab_arrow + "\' class=\'right-arrow actives\'>";
                                    }
                                    nav_arrow += "</div>";
                                }
                        #>
                        <li class="{{ active }}">
                            <a {{{toggle}}} onclick="kkk({{ key }})" class="{{data.style === "custom" ? data.nav_text_align : ""}} {{icon_block}}" style="{{display}}" href="#jwpf-tab-{{ data.id }}{{ key }}">
                                <div>
                                    {{{ nav_num }}}
                                    <div class="tab-a-content">
                                        {{{icon_top}}} {{{icon_left}}} {{{image_top}}} {{{image_left}}} {{title}} {{{icon_right}}} {{{icon_bottom}}} {{{image_right}}} {{{image_bottom}}} {{{subtitle}}}
                                    </div>
                                </div>
                                {{{ nav_arrow }}}
                            </a>
                        </li> 
                        <# }); #> 
                    </ul>
                    
                    <div class="jwpf-tab-content jwpf-tab-{{ data.style }}-content">
                        <# _.each(data.jw_tab_item, function(tab, key){ #>
                            <#
                            var active = "";
                            ';
                            if(isset($_COOKIE["xx"]) && $_COOKIE["xx"]!='')
                            {
                                $output.='var xx = '.$_COOKIE["xx"].' ;';
                            }
                            else
                            {
                                $output.='var xx = 0 ;';
                            }
                $output.='
                            if(xx)
                            {
                                if(key == xx)
                                {
                                    active = "active in";
                                }
                            }
                            else
                            {
                                if(key == 0)
                                {
                                    active = "active in";
                                }
                            }

                            #>
                            <div id="jwpf-tab-{{ data.id }}{{ key }}" class="jwpf-tab-pane jwpf-fade {{ active }}">
                                <#
                                var htmlContent = "";
                                _.each(tab.content, function(content){
                                    htmlContent += content;
                                });
                                #>
                                {{{ htmlContent }}}
                            </div>
                        <# }); #>
                    </div>
                </div>
            </div>
        <# } #>
		';

        return $output;
    }

}
