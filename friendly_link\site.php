<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonFriendly_link extends JwpagefactoryAddons
{

	public function render()
	{
		$layout_id = $_GET['layout_id'] ?? 0;
		$company_id = $_GET['company_id'] ?? 0;
		$site_id = $_GET['site_id'] ?? 0;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
		$section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
		$tz_style = (isset($settings->tz_style) && $settings->tz_style) ? $settings->tz_style : '_blank';
		$a_title = (isset($settings->a_title) && $settings->a_title) ? $settings->a_title : '友情链接';
		$tc_site = (isset($settings->tc_site) && $settings->tc_site) ? $settings->tc_site : 'x';

		// 右侧图标
		$a_right_icon = (isset($settings->a_right_icon) && $settings->a_right_icon) ? $settings->a_right_icon : '';
		// 整体边框宽度
		$a_width_md = (isset($settings->a_width) && $settings->a_width) ? $settings->a_width : 328;
		$a_width_sm = (isset($settings->a_width_sm) && $settings->a_width_sm) ? $settings->a_width_sm : '';
		$a_width_xs = (isset($settings->a_width_xs) && $settings->a_width_xs) ? $settings->a_width_xs : '';
		// 整体边框高度
		$a_height_md = (isset($settings->a_height) && $settings->a_height) ? $settings->a_height : 30;
		$a_height_sm = (isset($settings->a_height_sm) && $settings->a_height_sm) ? $settings->a_height_sm : '';
		$a_height_xs = (isset($settings->a_height_xs) && $settings->a_height_xs) ? $settings->a_height_xs : '';
		// 整体背景颜色
		$a_bg_color = (isset($settings->a_bg_color) && $settings->a_bg_color) ? $settings->a_bg_color : '#fff';
		// 提示文字颜色
		$a_link_color = (isset($settings->a_link_color) && $settings->a_link_color) ? $settings->a_link_color : '#b8b8b8';
		// 提示 文字大小
		$a_link_fontsize_md = (isset($settings->a_link_fontsize) && $settings->a_link_fontsize) ? $settings->a_link_fontsize : '';
		$a_link_fontsize_sm = (isset($settings->a_link_fontsize_sm) && $settings->a_link_fontsize_sm) ? $settings->a_link_fontsize_sm : '';
		$a_link_fontsize_xs = (isset($settings->a_link_fontsize_xs) && $settings->a_link_fontsize_xs) ? $settings->a_link_fontsize_xs : '';
		// 边框颜色 
		$a_border_color = (isset($settings->a_border_color) && $settings->a_border_color) ? $settings->a_border_color : '#999999';
		// 边框 宽度
		$a_border_width_md = (isset($settings->a_border_width) && $settings->a_border_width) ? $settings->a_border_width : 1;
		$a_border_width_sm = (isset($settings->a_border_width_sm) && $settings->a_border_width_sm) ? $settings->a_border_width_sm : '';
		$a_border_width_xs = (isset($settings->a_border_width_xs) && $settings->a_border_width_xs) ? $settings->a_border_width_xs : '';
		// 边框 圆角
		$a_border_radius_md = (isset($settings->a_border_radius) && $settings->a_border_radius) ? $settings->a_border_radius : 0;
		$a_border_radius_sm = (isset($settings->a_border_radius_sm) && $settings->a_border_radius_sm) ? $settings->a_border_radius_sm : '';
		$a_border_radius_xs = (isset($settings->a_border_radius_xs) && $settings->a_border_radius_xs) ? $settings->a_border_radius_xs : '';

		// 友情提示 文字行高
		$a_link_lHeight_md = $a_border_width_md ? ($a_height_md - $a_border_width_md * 2) : $a_height_md;
		$a_link_lHeight_sm = $a_border_width_sm ? ($a_height_sm - $a_border_width_sm * 2) : $a_height_sm;
		$a_link_lHeight_xs = $a_border_width_xs ? ($a_height_xs - $a_border_width_xs * 2) : $a_height_xs;

		$output = '
		<style>
			' . $addon_id . ' .rss_input {
				display: block;
				overflow: visible;
				width: ' . $a_width_md . 'px;
				height: ' . $a_height_md . 'px;
				position: relative;
				border: ' . $a_border_color . ' solid ' . $a_border_width_md . 'px;
				border-radius: ' . $a_border_radius_md . 'px;
			}
		
			' . $addon_id . ' .rss_input a.link {
				display: block;
				background: url("';
				if($a_right_icon) {
					$output .= $a_right_icon;
				}else {
					if ($tc_site == 'x') {
						$output .= '/components/com_jwpagefactory/addons/friendly_link/assets/images/x-.jpg';
					}else {
						$output .= '/components/com_jwpagefactory/addons/friendly_link/assets/images/s-.jpg';
					}
				}
				if ($tc_site == 'x') {
					// $output .= ' background: url("https://ijzt.china9.cn/components/com_jwpagefactory/addons/friendly_link/assets/images/x.png")/*tpa=https://www.shenhuagroup.com.cn/cs/shenhua/sh/style/images/footer_input_bg.png*/ left top no-repeat;';
					// $output .= ' background: url("/components/com_jwpagefactory/addons/friendly_link/assets/images/x-.jpg") right top no-repeat;';
				} else {
					// $output .= ' background: url("https://ijzt.china9.cn/components/com_jwpagefactory/addons/friendly_link/assets/images/x.png")/*tpa=https://www.shenhuagroup.com.cn/cs/shenhua/sh/style/images/footer_input_bg.png*/ left top no-repeat;';
					// $output .= ' background: url("/components/com_jwpagefactory/addons/friendly_link/assets/images/s-.jpg") right top no-repeat;';
				}
				$output .= '") right top no-repeat;
				background-color: ' . $a_bg_color . ';
				position: absolute;
				top: 0px;
				left: 0px;
				overflow: hidden;
				height: 100%;
				line-height: ' . $a_link_lHeight_md . 'px;
				margin: 0;
				padding: 0 10px;
				width: 100%;
				color: ' . $a_link_color . ';
				font-size: ' . $a_link_fontsize_md . 'px;
				border: none;
				text-decoration: none;
			}
			' . $addon_id . ' .rss_input ul {
				display: none;
				position: absolute;
				width: ' . ($a_width_md - $a_border_width_md * 2) . 'px;
				border: 1px solid #d8d8d8;
				background: #fff;
				';
				if ($tc_site == 'x') {
					$output .= ' top: ' . ($a_height_md - $a_border_width_md) . 'px;';
				} else {
					$output .= ' bottom: ' . ($a_height_md - $a_border_width_md) . 'px;';
				}
				$output .= '
				left: 0px;
				margin: 0;
				padding: 0;
				border: 0px;
				list-style-type: none;
				text-decoration: none;
			}
			' . $addon_id . ' .rss_input li {
				display: block;
				line-height: ' . $a_height_md . 'px;
				height: ' . $a_height_md . 'px;
				overflow: hidden;
				vertical-align: bottom;
				width: ' . ($a_width_md - $a_border_width_md * 2) . 'px;
			}
			' . $addon_id . ' .rss_input li a {
				display: block;
				overflow: hidden;
				width: ' . ($a_width_md - $a_border_width_md * 2) . 'px;
				line-height: ' . $a_height_md . 'px;
				font-size: 12px;
				color: #666;
				padding: 0 8px;
				text-decoration: none;
			}
			' . $addon_id . ' .rss_input li a:hover {
				background: #d8d8d8;
				text-decoration: none;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				' . $addon_id . ' .rss_input {
					width: ' . $a_width_sm . 'px;
					height: ' . $a_height_sm . 'px;
					border-width: ' . $a_border_width_sm . 'px;
					border-radius: ' . $a_border_radius_sm . 'px;
				}
				' . $addon_id . ' .rss_input a.link {
					font-size: ' . $a_link_fontsize_sm . 'px;
					line-height: ' . $a_link_lHeight_sm . 'px;
				}
				' . $addon_id . ' .rss_input ul {
					width: ' . ($a_width_sm - $a_border_width_sm * 2) . 'px;
					';
					if ($tc_site == 'x') {
						$output .= ' top: ' . ($a_height_sm - $a_border_width_sm) . 'px;';
					} else {
						$output .= ' bottom: ' . ($a_height_sm - $a_border_width_sm) . 'px;';
					}
					$output .= '
				}
			}
			@media (max-width: 767px) {
				' . $addon_id . ' .rss_input {
					width: ' . $a_width_xs . 'px;
					height: ' . $a_height_xs . 'px;
					border-width: ' . $a_border_width_xs . 'px;
					border-radius: ' . $a_border_radius_xs . 'px;
				}
				' . $addon_id . ' .rss_input a.link {
					font-size: ' . $a_link_fontsize_xs . 'px;
					line-height: ' . $a_link_lHeight_xs . 'px;
				}
			}
		</style>
		';

		$output .= '
		<div class="rss_input mt20">
			<a href="javascript:;" class="link">' . $a_title . '</a>
			<ul style="display: none;">
			';
				foreach ($section_tab_item as $key => $tab) {
					if ($tab->tz_link_style == 'nei') {
						$output .= '<li><a href="' . $tab->link1 . '" target="' . $tz_style . '">' . $tab->title . '</a></li>';
					} else {
						$output .= '<li><a href="' . $tab->link2 . '" target="' . $tz_style . '">' . $tab->title . '</a></li>';
					}
				}
			$output .= '
			</ul>
		</div>
		<!-- <script src="https://ijzt.china9.cn/components/com_jwpagefactory/addons/product_list/assets/js/jquery.min.js"></script> -->
		<script>
			$(\'.rss_input\').hover(function(){
						$(this).find(\'ul\').slideDown(200);
				},function(){
						$(this).find(\'ul\').stop(true,true);
						$(this).find(\'ul\').slideUp(200);
				})
		</script>';

		return $output;
	}

	public function scripts()
	{
	}

	public  function js()
	{
	}

	public function css()
	{
	}

	public static function getTemplate()
	{
		$output = '
		<!--<div>
			<div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>
		</div>-->
			<#
				var addonId = "#jwpf-addon-" + data.id;
				// 右侧图标
				var a_right_icon = data.a_right_icon || "/components/com_jwpagefactory/addons/friendly_link/assets/images/x-.jpg";
				// 整体边框宽度
				var a_width = data.a_width
				// 整体边框高度
				var a_height = data.a_height
				// 整体背景颜色
				var a_bg_color = data.a_bg_color || "#fff";
				// 友情链接文字颜色
				var a_link_color = data.a_link_color || "#b8b8b8";
				// 提示标题文字大小
				var a_link_fontsize = data.a_link_fontsize
				// 描边边框颜色
				var a_border_color = data.a_border_color || "#999999";
				// 描边边框宽度
				var a_border_width = data.a_border_width
				// 描边边框圆角
				var a_border_radius = data.a_border_radius

				// 处理适配数据
				function useData(key, value) {
					var str = "";
					if(key) {
						if(key[value]) {
							str = key[value]
						}
					}
					return str
				}
			#>
			<style type="text/css">
			{{ addonId }} .rss_input {
				display: block;
				overflow: visible;
				width: {{ useData(a_width, "md") || 328 }}px;
				height: {{ useData(a_height, "md") || 30 }}px;
				position: relative;
				border: {{ a_border_color }} solid 1px;
				border-width: {{ useData(a_border_width, "md") }}px;
				border-radius: {{ useData(a_border_radius, "md") }}px;
			}
			{{ addonId }} .rss_input a.link {
				display: block;
				/*background: url("https://ijzt.china9.cn/components/com_jwpagefactory/addons/friendly_link/assets/images/x.png") left top no-repeat;*/
				background: url(\'{{a_right_icon}}\') right top no-repeat;
				background-color: {{ a_bg_color }};
				position: absolute;
				top: 0px;
				left: 0px;
				overflow: hidden;
				height: 100%;
				line-height: {{ useData(a_height, "md") || 30 }}px;
				margin: 0;
				padding: 0 10px;
				width: 100%;
				color: {{ a_link_color }};
				border: none;
				text-decoration: none;
				font-size: {{ useData(a_link_fontsize, "md") }}px;
			}
			{{ addonId }} .rss_input ul {
				display: none;
				position: absolute;
				width: {{ useData(a_width, "md") || 328 }}326px;
				border: 1px solid #d8d8d8;
				background: #fff;
				bottom: 33px;
				left: 0px;
				margin: 0;
				padding: 0;
				border: 0px;
				list-style-type: none;
				text-decoration: none;
			}
			{{ addonId }} .rss_input li {
				display: block;
				line-height: {{ useData(a_height, "md") || 30 }}px;
				height: {{ useData(a_height, "md") || 30 }}px;
				overflow: hidden;
				vertical-align: bottom;
				width: 326px;
				
			}
			{{ addonId }} .rss_input li a {
				display: block;
				overflow: hidden;
				width: 310px;
				line-height: 30px;
				font-size: 12px;
				color: #666;
				padding: 0 8px;
				text-decoration: none;
			}
			{{ addonId }} .rss_input li a:hover {
				background: #d8d8d8;
				text-decoration: none;
			}
			@media (min-width: 768px) and (max-width: 991px) {
				{{ addonId }} .rss_input {
					width: {{ useData(a_width, "sm") }}px;
					height: {{ useData(a_height, "sm") }}px;
					border-width: {{ useData(a_border_width, "sm") }}px;
					border-radius: {{ useData(a_border_radius, "sm") }}px;
				}
				{{ addonId }} .rss_input a.link {
					font-size: {{ useData(a_link_fontsize, "sm") }}px;
					line-height: {{ useData(a_height, "sm") }}px;
				}
			}
			@media (max-width: 767px) {
				{{ addonId }} .rss_input {
					width: {{ useData(a_width, "xs") }}px;
					height: {{ useData(a_height, "xs") }}px;
					border-width: {{ useData(a_border_width, "xs") }}px;
					border-radius: {{ useData(a_border_radius, "xs") }}px;
				}
				{{ addonId }} .rss_input a.link {
					font-size: {{ useData(a_link_fontsize, "xs") }}px;
					line-height: {{ useData(a_height, "xs") }}px;
				}
			}
		</style>
		<div class="rss_input mt20">
			<a href="javascript:;" class="link">友情链接</a>
			<ul style="display: none;">
				<li><a href="https://www.cnljxh.com/" target="_top">中国炼焦行业协会</a></li>
				<li><a href="https://www.sxjh.com.cn/" target="_top">山西焦化集团</a></li>
				<li><a href="https://feiye.sxhuaxin.cn/" target="_top">山西华鑫肥业股份有限公司</a></li>
				<li><a href="https://jiaohua.sxhuaxin.cn/" target="_top">山西华鑫煤焦化实业集团有限公司</a></li>
			</ul>
		</div>
		
		';

		return $output;
	}
}
