<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'repeatable',
		'addon_name' => 'swiper_tabtop',
		'title' => JText::_('头部分类选项卡轮播组件'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_DESC'),
		'category' => '轮播',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				// 轮播项目
				'jw_tab_item_goods_eight' => array(
                    'title' => JText::_('自定义分类名称'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('高端网站'),
                            'desc' => JText::_('产品标题'),
                            'std' => '标题',
                        ),
                        'goods_desc' => array(
                            'title' => JText::_('案例列表'),
                            'attr' => array(
                                'icon_img' => array(
                                    'type' => 'media',
                                    'title' => JText::_('PC案例图'),
                                    'std' => '',
                                ),
                                'phone_img' => array(
                                    'type' => 'media',
                                    'title' => JText::_('手机案例图'),
                                    'std' => '',
                                ),
                                'title_list' => array(
                                    'type' => 'text',
                                    'title' => JText::_('标题'),
                                    'desc' => JText::_('标题'),
                                    'std' => '标题',
                                ),
                                'intro_list' => array(
                                    'type' => 'text',
                                    'title' => JText::_('简介'),
                                    'desc' => JText::_('简介'),
                                    'std' => '简介',
                                ),
                            ),
                            'std' => '',
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '高端网站',
                            'goods_desc' => array(
                                array(
                                    'title_list' => 'KC皮草',
                                    'intro_list' => '高端响应式网站',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/1f556e21b9c9c99998df67954343337b.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/d7fcbb15832de2d07ca29d8306af5b79.png',

                                ),
                                array(
                                    'title_list' => '敷尔佳Voolga',
                                    'intro_list' => '高端网站定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/4650350f9b85f3ee99e5ec468cf624a7.png',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/8e634a831b70f978e3d8f1b292d5ff4c.png',

                                ),


                            ),
                        ),
                        array(
                            'title' => 'APP案例',
                            'goods_desc' => array(
                                array(
                                    'title_list' => 'SHEGRIE',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/8903d1dff5acdd0ab5bba094abda6154.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/b5e14a4eca5e73bcd12382a5e972190f.png',

                                ),
                                array(
                                    'title_list' => '东运优宜酷爱车家',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/e1ca697bae88cdc5484f1be238ef7765.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/76d4b9ab5ffc6f47f1be34dfae4c0221.jpg',

                                ),
                                array(
                                    'title_list' => '吉林敖东',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/6a6c0124183a4cb04a0b50eaa85d431f.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/1f2a463fa4ef1aec0f1767cad31cdb29.jpg',

                                ),
                                array(
                                    'title_list' => '九亿直播',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/91d8e56c0df14e7dc120f78ad85afc18.png',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c3ffacc9d92bb1bd030bee2e22cedd6e.png',

                                ),
                                array(
                                    'title_list' => '如祭',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/ffe836ae195715e86697b2076af83b13.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/0ac894a3ff40cdcbe00bc8512ff3dae0.jpg',

                                ),
                                array(
                                    'title_list' => '疯天然',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/92f32f52d4d029f904d7e3caa1928275.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c256a0302ad1171150852d4c007e6a67.jpg',

                                ),
                                array(
                                    'title_list' => '正和',
                                    'intro_list' => '高端APP定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/26193ebbbc419f808ad1af27a7a32c02.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c218dd3c0ac8fe6fae3f2f1687b1c487.jpg',

                                ),

                            ),
                        ),
                        array(
                            'title' => '电商案例',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '资海E店',
                                    'intro_list' => '高端电商定制方案',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/446dade6fd1a6dfe4166bed765affe56.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c6cb4fead4c5211650783349373a03a1.jpg',

                                ),
                            ),
                        ),
                        array(
                            'title' => '小程序案例',
                            'goods_desc' => array(
                                array(
                                    'title_list' => '艺星整形',
                                    'intro_list' => '高端小程序定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/7a1bf1e1edc841ef4a0cee11f9eb12b3.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/597e98baaad7a47bddd890e8a879bac6.jpg',

                                ),
                                array(
                                    'title_list' => '宝宇雪猪肉',
                                    'intro_list' => '高端小程序定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/3a6ae478d4c61ebd5277a0f7f9a85e92.png',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/08976148d8b3ab3566ed90d1aa560673.png',

                                ),
                                array(
                                    'title_list' => '海芸青再生资源回收',
                                    'intro_list' => '高端小程序定制',
                                    'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/dd6daa918ccbf10037180451f791065f.jpg',
                                    'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/e3f5719a1dae3b75972d1689fe96a4ef.jpg',

                                ),
                            ),
                        ),
                    ),
                    
                ),

				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
                'title_color_pc' => array(
                    'type' => 'color',
                    'title' => JText::_('pc标题字体颜色'),
                    'std' => '#303030'
                ),
                'title_color_sj' => array(
                    'type' => 'color',
                    'title' => JText::_('手机标题字体颜色'),
                    'std' => '#fff'
                ),
                'title_size_pc' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc标题字体大小'),
                    'std' => 36,
                    'min' => 10,
                    'max' => 100,
                ),
                'desc_color_pc' => array(
                    'type' => 'color',
                    'title' => JText::_('pc简介字体颜色'),
                    'std' => '#535353'
                ),
                'desc_color_sj' => array(
                    'type' => 'color',
                    'title' => JText::_('手机简介字体颜色'),
                    'std' => '#f0f0f0'
                ),
                'desc_size_pc' => array(
                    'type' => 'slider',
                    'title' => JText::_('pc简介字体大小'),
                    'std' => 24,
                    'min' => 10,
                    'max' => 100,
                ),
			),
		),
	)
);
