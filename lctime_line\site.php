<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonLctime_line extends JwpagefactoryAddons
{

    public function render()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $time_line_title = (isset($settings->time_line_title) && $settings->time_line_title) ? $settings->time_line_title : 'Future';
        $time_line_content = (isset($settings->time_line_content) && $settings->time_line_content) ? $settings->time_line_content : '未来';


        $output .= '<style>';
        $output .= $addonId . ' a{color:inherit;text-decoration:none}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title>div>span{font-weight:bolder}';
        $output .= $addonId . ' .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;background:rgba(0,0,0,.03);position:absolute;top:0;left:0}';
        $output .= $addonId . ' .about-wrap{width:100%;position:relative;overflow:hidden;height:645px}';
        $output .= $addonId . ' .about-wrap .slide-wrap{width:100%;height:100%;padding-top:80px;background:rgba(0,0,0,.03);position:absolute;top:0;left:0;padding-left:calc(50% - 1560px / 2)}';
        $output .= $addonId . ' .slide-content-wrap{height:100%;padding-left:2.3rem;cursor:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lctime_line/assets/' . 'img/mouse.png),move;overflow:hidden;-ms-touch-action:none;touch-action:none;position:relative}';
        $output .= $addonId . ' .slide-content-wrap,.slide-content-wrap .content-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}';
        $output .= $addonId . ' .slide-content-wrap .content-container{position:absolute;top:0;left:0;height:100%}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem;padding-right:1.54rem;}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;max-width:500px;height:100%}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block{white-space:normal}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item h3{line-height:36px;font-size:36px;color:#d9012a;font-family:"rubik";font-weight:bold}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{width:25.1rem; padding-right:3rem; height:22rem;}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-child(1){width:11rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title{padding-right:2rem !important}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar{width:6px;height:10px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar{width:6px;height:10px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:#fff;border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(0,0,0,0);border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,0);border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,1);border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb:hover{background:#333}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-corner{background:#179a16}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{padding-right:.3rem;font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}';

        $output .= $addonId . ' .about-wrap{overflow-y:auto}';
        $output .= $addonId . ' .about-wrap::-webkit-scrollbar{width:0 !important}';
        $output .= $addonId . ' .about-wrap{-ms-overflow-style:none}';
        $output .= $addonId . ' .about-wrap{overflow:-moz-scrollbars-none}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title div{font-size:18px;padding-top:18px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.05rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title p{position:relative;width:100%;font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 7.5rem);top:1rem;right:1rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top: 1rem;right:1rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top;width:100%;height: 450px;overflow: auto;}';
        $output .= ' @media only screen and (max-width:1479px) and (min-width:1024px){';
            $output.=$addonId . ' .about-wrap .slide-wrap{
                padding-left: 20px;
            }';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar{width:6px;height:10px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-track{background:rgba(239,239,239,0);border-radius:2px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,1);border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,0);border-radius:6px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb:hover{background:#333}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-corner{background:#179a16}';
        $output .= $addonId . ' .about-wrap{width:100%;position:relative;overflow:hidden;height:600px}';
        $output .= $addonId . ' .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-bottom:1rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;padding-left:20px;left:0}';
        $output .= $addonId . ' .slide-content-wrap{height:100%;padding-left:2.3rem;cursor:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lctime_line/assets/' . 'img/mouse.png),move;overflow:hidden;-ms-touch-action:none;touch-action:none;position:relative}';
        $output .= $addonId . ' .slide-content-wrap,.slide-content-wrap .content-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}';
        $output .= $addonId . ' .slide-content-wrap .content-container{position:absolute;top:0;left:0;height:100%}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;padding-right:1.54rem;height:4.4rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item h3{line-height:36px;font-size:36px;color:#d9012a;font-family:"rubik";font-weight:bold}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{font-size:14px;color:#505050;white-space:normal;padding-left:.07rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title div{font-size:20px;padding-top:20px}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.05rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title p{position:relative;width:100%;font-size:14px;color:#505050;white-space:normal}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 7.2rem);right:1.3rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top:.35rem;right:.15rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{width:100%}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{padding-right:10px;width:300px}';
        $output .= $addonId . ' ';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title{max-height:8rem;overflow-y:auto;width:5.1rem;max-width:5.1rem;padding-right:.3rem}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (max-width:1023px){';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block{white-space:normal}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{min-width:3rem;max-width:5rem}';
        $output .= $addonId . ' .about-wrap{width:100%;position:relative;padding-bottom:20px}';
        $output .= $addonId . ' .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-bottom:1rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;left:0}';
        $output .= $addonId . ' .slide-content-wrap{height:100%;padding-left:.3rem;position:relative}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{padding-right:.35rem !important}';
        $output .= $addonId . ' .slide-content-wrap,.slide-content-wrap .content-container{}';
        $output .= $addonId . ' .slide-content-wrap .content-container{width:100%;white-space:nowrap;overflow-x:auto;height:100%}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;vertical-align:top;padding-right:1.54rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-child(1){width:7rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item h3{line-height:1.9rem;font-size:1.3rem;color:#d9012a;font-family:"rubik";font-weight:bold}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title div{font-size:.26rem;padding-top:.2rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.5rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title>div>span{font-size:.9rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{max-width:39rem;width:21rem;font-size:.24rem;color:#505050;white-space:normal;line-height:.42rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .date-title{position:relative;max-width:39rem;width:21rem;font-size:.24rem;color:#505050;white-space:normal;padding-left:.07rem;line-height:1rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 5rem);top:1rem;right:0.6rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top:1rem;right:0.5rem;border-top:1px solid #ddd}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (max-width:1590px) and (min-width:1480px){';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item .inline-block .date-title{padding-right:.15rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item{padding-right:.2rem}';
        $output .= $addonId . ' .lc_inner{width:1440px}';
        $output .= $addonId . ' .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;padding-left:calc(50% - 1440px / 2);left:0}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (min-width:1480px){';
        $output .= $addonId . ' .slide-content-wrap .content-container ';
        $output .= $addonId . ' .list-item:nth-child(1){width:9rem}';
        $output .= $addonId . ' .slide-content-wrap .content-container .list-item:nth-child(1):after{width:2rem}';
        $output .= $addonId . ' .date-title{overflow-y:auto}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (min-width:1480px){';
        $output .= $addonId . ' .about-wrap{overflow-y:auto}';
        $output .= $addonId . ' }';
        $output .= ' @media only screen and (min-width:1480px){';
        $output .= $addonId . ' .about-wrap{width:100%;position:relative;overflow:hidden;height:645px}';
        $output .= $addonId . ' }';
        // 隐藏滚动条
        $output.= $addonId . ' .list-item>.inline-block::-webkit-scrollbar {
            display: none; /* Chrome Safari */
          }
          ' . $addonId . ' .list-item>.inline-block {
            scrollbar-width: none; /* firefox */
            -ms-overflow-style: none; /* IE 10+ */
            overflow-x: hidden;
            overflow-y: auto;
          }';
        $output .= '</style>';
        $output .= '<div class="about-wrap">';
        $output .= '  <div class="slide-wrap">';
        $output .= '    <div class="slide-content-wrap ">';
        $output .= '      <div class="content-container">';
        $output .= '        <div class="list-item">';
        $output .= '          <h3 class="slide-title">'.$time_line_title.'</h3>';
        $output .= '          <div class="date-title">';
        $output .= '            <div><span>'.$time_line_content.'</span></div>';
        $output .= '          </div>';
        $output .= '        </div>';
        foreach ($settings->jw_lctime_line_item as $key => $tline) {
            $output .= '        <div class="list-item">';
            $output .= '          <h3 class="slide-title">' . $tline->time . '</h3>';
            $output .= '          <div class="date-title">';
            $output .= '            <div><span>' . $tline->title . '</span></div>';
            $output .= '          </div>';
            $output .= '          <div class="inline-block">';
            $output .= '            <div class="date-title">';
            $output .=              $tline->content;
            $output .= '            </div>';
            $output .= '          </div>';
            $output .= '        </div>';
        }
        $output .= '      </div>';
        $output .= '    </div>';
        $output .= '    <div></div>';
        $output .= '  </div>';
        $output .= '</div>';
        // $output .= '<script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lctime_line/assets/' . 'js/jquery.min.js"></script>';
        $output .= ' <script>';
        $output .= '$(function($){';
            $output .=" if ($(window).width() < 1024) {
                $('.about-wrap').mousedown(function(e) {
                    console.log('width：'+$(window).width())
                    console.log(222)
                    aa3 = parseInt($('.content-container').css('left'))
                    aa1 = e.clientX
                    console.log('走走走', aa1)
                    sta1 = true
                    $('.about-wrap ').mousemove(function(e) {
                        if (sta1) {
                            aa2 = e.clientX
                            if (((aa2 - aa1 + aa3) <= 0) && (aa2 - aa1 + aa3) >= ($('.slide-wrap').width() - $('.content-container').width())) {
                                $('.content-container').css('left', aa2 - aa1 + aa3)
                                console.log(aa2 - aa1 + aa3)
                            }
                        }
                    });
                });
                $('.about-wrap').mouseup(function(e) {
                    sta1 = false
                })
            } else {
                $('.about-wrap').mousedown(function(e) {

                    console.log('width：'+$(window).width())
                    console.log(111)
                    aa3 = parseInt($('.content-container').css('left'))
                    aa1 = e.clientX
                    console.log('走走走', aa1)
                    sta1 = true
                    $('.about-wrap ').mousemove(function(e) {
                        if (sta1) {
                            aa2 = e.clientX
                            if (((aa2 - aa1 + aa3) <= 0) && (aa2 - aa1 + aa3) >= ($('.slide-wrap').width() - $('.content-container').width())) {
                                $('.content-container').css('left', aa2 - aa1 + aa3)
                                console.log(aa2 - aa1 + aa3)
                            }
                        }
                    });
                });
                $('.about-wrap').mouseup(function(e) {
                    sta1 = false
                })
            }";
        $output .='});';
        $output .= '</script>';
        return $output;

    }


    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项
        var jw_tab_item = data.jw_tab_item || "";

        #>


        <style>
        {{ addonId }} a{color:inherit;text-decoration:none}
        {{ addonId }} .slide-content-wrap .content-container .list-item .date-title>div>span{font-weight:bolder}
        {{ addonId }} .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;background:rgba(0,0,0,.03);position:absolute;top:0;left:0}
        {{ addonId }} .about-wrap{width:100%;position:relative;overflow:hidden;height:645px}
        {{ addonId }} .about-wrap .slide-wrap{width:100%;height:100%;padding-top:80px;background:rgba(0,0,0,.03);position:absolute;top:0;left:0;padding-left:calc(50% - 1560px / 2)}
        {{ addonId }} .slide-content-wrap{height:100%;padding-left:2.3rem;cursor:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lctime_line/assets/' . 'img/mouse.png),move;overflow:hidden;-ms-touch-action:none;touch-action:none;position:relative}
        {{ addonId }} .slide-content-wrap,.slide-content-wrap .content-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}
        {{ addonId }} .slide-content-wrap .content-container{position:absolute;top:0;left:0;height:100%}
        {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem;padding-right:1.54rem;}
        {{ addonId }} .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;max-width:500px;height:100%}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block{white-space:normal}
        {{ addonId }} .slide-content-wrap .content-container .list-item h3{line-height:36px;font-size:36px;color:#d9012a;font-family:"rubik";font-weight:bold}
        {{ addonId }} .slide-content-wrap .content-container .list-item{width:25.1rem; padding-right:3rem; height:22rem;}
        {{ addonId }} .slide-content-wrap .content-container .list-item:nth-child(1){width:11rem}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title{padding-right:2rem !important}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar{width:6px;height:10px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar{width:6px;height:10px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:#fff;border-radius:6px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(0,0,0,0);border-radius:6px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,0);border-radius:6px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,1);border-radius:6px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb:hover{background:#333}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-corner{background:#179a16}
        {{ addonId }}.slide-content-wrap .content-container .list-item .date-title{padding-right:.3rem;font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}
        {{ addonId }} .about-wrap{overflow-y:auto}
        {{ addonId }} .about-wrap::-webkit-scrollbar{width:0 !important}
        {{ addonId }} .about-wrap{-ms-overflow-style:none}
        {{ addonId }} .about-wrap{overflow:-moz-scrollbars-none}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}
        {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}
        {{ addonId }} .slide-content-wrap .content-container .list-item .date-title div{font-size:18px;padding-top:18px}
        {{ addonId }} .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.05rem}
        {{ addonId }} .slide-content-wrap .content-container .list-item .date-title p{position:relative;width:100%;font-size:14px;color:#505050;white-space:normal;line-height:32px;text-align:justify}
        {{ addonId }} .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 7.5rem);top:1rem;right:1rem;border-top:1px solid #ddd}
        {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top: 1rem;right:1rem;border-top:1px solid #ddd}
        {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top;width:100%;height: 450px;overflow: auto;}

        {{ addonId }} .slide-content-wrap .content-container .list-item {
            width: 25.1rem;
            padding-right: 3rem;
            height: 22rem;
        }

        @media only screen and (max-width:1479px) and (min-width:1024px){
            {{ addonId }} .about-wrap .slide-wrap{
                padding-left: 20px;
            }
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar{width:6px;height:10px}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-track{background:rgba(239,239,239,0);border-radius:2px}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:hover .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,1);border-radius:6px}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb{background:rgba(205,205,205,0);border-radius:6px}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-thumb:hover{background:#333}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title::-webkit-scrollbar-corner{background:#179a16}
            {{ addonId }} .about-wrap{width:100%;position:relative;overflow:hidden;height:600px}
            {{ addonId }} .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-bottom:1rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;padding-left:20px;left:0}
            {{ addonId }} .slide-content-wrap{height:100%;padding-left:2.3rem;cursor:url(' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/lctime_line/assets/' . 'img/mouse.png),move;overflow:hidden;-ms-touch-action:none;touch-action:none;position:relative}
            {{ addonId }} .slide-content-wrap,.slide-content-wrap .content-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}
            {{ addonId }} .slide-content-wrap .content-container{position:absolute;top:0;left:0;height:100%}
            {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;padding-right:1.54rem;height:4.4rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item h3{line-height:36px;font-size:36px;color:#d9012a;font-family:"rubik";font-weight:bold}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{font-size:14px;color:#505050;white-space:normal;padding-left:.07rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title div{font-size:20px;padding-top:20px}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.05rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title p{position:relative;width:100%;font-size:14px;color:#505050;white-space:normal}
            {{ addonId }} .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 7.2rem);right:1.3rem;border-top:1px solid #ddd}
            {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top:.35rem;right:.15rem;border-top:1px solid #ddd}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{width:100%}
            {{ addonId }} .slide-content-wrap .content-container .list-item{padding-right:10px;width:300px}
            
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title{max-height:8rem;overflow-y:auto;width:5.1rem;max-width:5.1rem;padding-right:.3rem}
        }
        @media only screen and (max-width:1023px){
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block{white-space:normal}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{min-width:3rem;max-width:5rem}
            {{ addonId }} .about-wrap{width:100%;position:relative;padding-bottom:20px}
            {{ addonId }} .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-bottom:1rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;left:0}
            {{ addonId }} .slide-content-wrap{height:100%;padding-left:.3rem;position:relative}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{padding-right:.35rem !important}
            {{ addonId }} .slide-content-wrap,.slide-content-wrap .content-container{}
            {{ addonId }} .slide-content-wrap .content-container{width:100%;white-space:nowrap;overflow-x:auto;height:100%}
            {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type{padding-left:.1rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item{position:relative;display:inline-block;vertical-align:top;padding-right:1.54rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item:nth-child(1){width:7rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item h3{line-height:1.9rem;font-size:1.3rem;color:#d9012a;font-family:"rubik";font-weight:bold}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block:first-of-type{margin-right:.9rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title div{font-size:.26rem;padding-top:.2rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title>div{margin-bottom:.5rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title>div>span{font-size:.9rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title{max-width:39rem;width:21rem;font-size:.24rem;color:#505050;white-space:normal;line-height:.42rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item .date-title {position:relative;max-width:39rem;width:21rem;font-size:.24rem;color:#505050;white-space:normal;padding-left:.07rem;line-height:1rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item:nth-of-type(n+2):after{position:absolute;content:"";height:0;width:calc(100% - 5rem);top:1rem;right:0.6rem;border-top:1px solid #ddd}
            {{ addonId }} .slide-content-wrap .content-container .list-item:first-of-type:after{position:absolute;content:"";height:0;width:1.6rem;top:1rem;right:0.5rem;border-top:1px solid #ddd}
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block{display:inline-block;vertical-align:top}
        }
        @media only screen and (max-width:1590px) and (min-width:1480px){
            {{ addonId }} .slide-content-wrap .content-container .list-item .inline-block .date-title{padding-right:.15rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item{padding-right:.2rem}
            {{ addonId }} .lc_inner{width:1440px}
            {{ addonId }} .about-wrap .slide-wrap{width:100%;height:100%;padding-top:.8rem;margin-top:1rem;background:rgba(0,0,0,.03);position:absolute;top:0;padding-left:calc(50% - 1440px / 2);left:0}
        }
        @media only screen and (min-width:1480px){
            {{ addonId }} .slide-content-wrap .content-container 
            {{ addonId }} .list-item:nth-child(1){width:9rem}
            {{ addonId }} .slide-content-wrap .content-container .list-item:nth-child(1):after{width:2rem}
            {{ addonId }} .date-title{overflow-y:auto}
        }
        @media only screen and (min-width:1480px){
            {{ addonId }} .about-wrap{overflow-y:auto}
        }
        @media only screen and (min-width:1480px){
            {{ addonId }} .about-wrap{width:100%;position:relative;overflow:hidden;height:645px}
        }
            {{ addonId }} .list-item>.inline-block::-webkit-scrollbar {
                display: none; /* Chrome Safari */
            }
            {{ addonId }} .list-item>.inline-block {
                scrollbar-width: none; /* firefox */
                -ms-overflow-style: none; /* IE 10+ */
                overflow-x: hidden;
                overflow-y: auto;
            }
        </style>

        
        <div class="about-wrap">
            <div class="slide-wrap">
                <div class="slide-content-wrap ">
                    <div class="content-container">
                        <div class="list-item">
                            <h3 class="slide-title">{{data.time_line_title}}</h3>
                            <div><span>{{ data.time_line_content }}</span></div>
                        </div>
                    </div>
                    <# _.each(data.jw_lctime_line_item, function(tline, key){    
                    #>
                        <div class="list-item">
                            <h3 class="slide-title">{{ tline.time }}</h3>
                            <div class="date-title">
                                <div><span>{{{ tline.title }}}</span></div>
                            </div>
                            <div class="inline-block">
                                <div class="date-title">
                                    {{{tline.content}}}
                                </div>
                            </div>
                        </div>
                    <# }); #>
                </div>
            </div>
        </div>

       
        ';

        return $output;
    }

}
