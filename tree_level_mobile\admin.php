<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
	array(
		'type' => 'general',
		'addon_name' => 'tree_level_mobile',
		'title' => JText::_('手机端产品三级分类'),    //三级分类
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TREE_LEVEL_MOBILE_DESC'),
		'category' => '产品',
		'attr' => array(
			'general' => array(
				'site_id' => array(
					'std' => $site_id,
				),
				'company_id' => array(
					'std' => $company_id,
				),
				'tab_list_bgcolor' => array(
					'type' => 'color',
					'title' => '左侧菜单背景',
					'desc' => '',
					'std' => '#f5f5f5'
				),
				'tab_active_color' => array(
					'type' => 'color',
					'title' => '左侧选中文字颜色',
					'desc' => '',
					'std' => '#ff5500'
				),
				'tab_active_bgcolor' => array(
					'type' => 'color',
					'title' => '左侧选中背景颜色',
					'desc' => '',
					'std' => '#ffffff'
				),
				'right_list_img' => array(
					'type' => 'slider',
					'title' => '自定义右侧图标大小',
					'desc' => '宽度等于高度',
					'std' => '60',
					'min' => 50,
					'max' => 100
				),
                'tab_list_width' => array(
                    'type' => 'slider',
                    'title' => '左侧列表宽度',
                    'desc' => '',
                    'responsive' => false,
                    'std' => '160',
                    'min' => 110,
                    'max' => 150
                ),

				 'detail_page_id' => array(
				 	'type' => 'select',
				 	'title' => '列表模版',
				 	'desc' => '显示文章详情页模版',
				 	'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
				 ),
			)
		)
	)
);
