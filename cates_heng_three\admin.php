<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
//defined('_JEXEC') or die ('resticted aceess');
//
///*Cq 内容*/
//
//JwAddonsConfig::addonConfig(
//    array(
//         'type' => 'content',
//         'addon_name' => 'jw_cates_heng_three',
//         'title' => JText::_('分类-横向居中显示'),
//         'desc' => JText::_('分类-横向居中显示描述'),
//         'category' => '分类模块',
//         'attr' => array(
//             'general' => array(
//
//                 'cate_type' => array(
//                     'type' => 'select',
//                     'title' => JText::_('选择分类'),
//                     'values' => array(
//                         '1' => '内容分类',
//                         '2' => '图文分类',
//                     ),
//                     'std' => '1',
//                 ),
//
//                 'link_page' => array(
//                     'type' => 'select',
//                     'title' => JText::_('选择点击后跳转的页面'),
//                     'desc' => JText::_('点击跳转页面'),
////                     'values' => JwPageFactoryBase::getPagesMenu(),
//                     'multiple' => false,
//                 ),
//
//                'offset' => array(
//                     'type' => 'number',
//                     'title' => JText::_('从第几个开始显示'),
//                     'desc' => JText::_('从第几个开始显示'),
//                     'std' => '1'
//                 ),
//
//                 'limit' => array(
//                     'type' => 'number',
//                     'title' => JText::_('分类显示个数'),
//                     'desc' => JText::_('分类显示个数描述'),
//                     'std' => '3'
//                 ),
//
//                 'cates_color' => array(
//                     'type' => 'color',
//                     'title' => JText::_('分类字体颜色'),
//                     'desc' => JText::_('分类字体颜色描述'),
//                     'std' => ''
//                 ),
//
//                 'cates_background' => array(
//                     'type' => 'color',
//                     'title' => JText::_('分类背景颜色'),
//                     'desc' => JText::_('分类背景颜色描述'),
//                     'std' => ''
//                 ),
//
////                 'cates_hover' => array(
////                     'type' => 'color',
////                     'title' => JText::_('分类悬停颜色'),
////                     'desc' => JText::_('分类悬停颜色描述'),
////                     'std' => '3'
////                 ),
//
//
//             ),
//
//         ),
//    )
//);
