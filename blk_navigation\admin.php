<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'general',
        'addon_name' => 'blk_navigation',
        'title' => "锚点导航",
        'desc' => JText::_('定制导航'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'nav_section' => array(
                    'type' => 'buttons',
                    'title' => '锚点选项',
                    'std' => 'left_path',
                    'values' => array(
                        array(
                            'label' => '左侧导航选项',
                            'value' => 'left_path'
                        ),
                        array(
                            'label' => '右侧锚点导航',
                            'value' => 'right_path'
                        ),
                    ),
                    'tabs' => true,
                ),
                'left-nav' => array(
                    'type' => 'separator',
                    'title' => JText::_('左侧导航选项'),
                    'depends' => array(
                        array('nav_section', '=', 'left_path')
                    ),
                ),
                'is_biao' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示导航图标'),
                    'desc' => JText::_('打开显示左侧导航图标'),
                    'std' => 1,
                    'depends' => array(
                        array('nav_section', '=', 'left_path')
                    ),
                ),
                'biao_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('导航图标'),
                    'desc' => JText::_(''),
                    'format' => 'image',
                    'depends' => array(
                        array('nav_section', '=', 'left_path'),
                        array('is_biao', '=', 1)
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210915/9315cc58cb2a7008189b70a04b56d918.png',
                ),
                'biao_bg_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航图标宽度'),
                    'depends' => array(
                        array('nav_section', '=', 'left_path'),
                        array('is_biao', '=', 1)
                    ),
                    'std' => 20,
                ),
//                'biao_bg_h' => array(
//                    'type' => 'slider',
//                    'title' => JText::_('导航图标高度'),
//                    'depends' => array(
//                        array('nav_section', '=', 'left_path'),
//                        array('is_biao', '=', 1)
//                    ),
//                    'std' => 10,
//                ),
                'biao_title' => array(
                    'type' => 'text',
                    'title' => JText::_('左边标题名称'),
                    'depends' => array(
                        array('nav_section', '=', 'left_path')
                    ),
                    'std' => '这里可以填写一个左边导航名称',
                ),
                'right-nav' => array(
                    'type' => 'separator',
                    'title' => JText::_('右侧锚点导航选项'),
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                    ),
                ),
                'blk_navigation_item' => array(
                    'title' => JText::_('右侧锚点导航内容'),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('标题名称'),
                            'std' => '这里可以填写一个导航名称',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否开启链接跳转'),
                            'desc' => JText::_('是否开启链接跳转'),
                            'values' => array(
                                1 => JText::_('是'),
                                0 => JText::_('否'),
                            ),
                            'std' => 0,
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页页',
                            'desc' => '',
                            'depends' => array('is_link' => 1),
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                        'title_id' => array(
                            'type' => 'text',
                            'title' => JText::_('标题跳转ID（容器章节ID）'),
                            'std' => '这里可以填写跳转ID',
                            'depends' => array('is_link' => 0),
                        ),
                    ),
                    'std' => array(
                        array(
                            'title' => '公司简介',
                        ),
                        array(
                            'title' => '资质荣誉',
                        ),
                        array(
                            'title' => '营销团队',
                        ),
                        array(
                            'title' => '合作伙伴',
                        )
                    ),
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                    ),
                ),
                'nav_style' => array(
                    'type' => 'buttons',
                    'title' => '样式选项',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                ),
                'title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文字颜色'),
                    'std' => '#000',
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'normal'),
                    ),
                ),
                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('文字大小'),
                    'max' => 100,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'normal'),
                    ),
                    'std' => array('md' => 16, 'sm' => 14, 'xs' => 10),
                    'responsive' => true,
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('文字行高'),
                    'max' => 100,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'normal'),
                    ),
                    'std' => array('md' => 54, 'sm' => 46, 'xs' => 20),
                    'responsive' => true,
                ),
                'title_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('文字内边距'),
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                    'responsive' => true,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'normal'),
                    ),
                ),
                'title_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('文字外边距'),
                    'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '0px 0px 0px 0px'),
                    'responsive' => true,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'normal'),
                    ),
                ),
                'title_active_color' => array(
                    'type' => 'color',
                    'title' => JText::_('文字滑过颜色'),
                    'std' => '#730a0b',
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                ),
                'tx_bg' => array(
                    'type' => 'separator',
                    'title' => JText::_('锚点滑过背景选项'),
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                ),
                'top_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('锚点滑过上方图标'),
                    'desc' => JText::_(''),
                    'format' => 'image',
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210915/cc61a2072e68cec623518eff0185bfa6.png',
                ),
                'bottom_bg' => array(
                    'type' => 'media',
                    'title' => JText::_('锚点滑过下方图标'),
                    'desc' => JText::_(''),
                    'format' => 'image',
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                    'std' => 'https://oss.lcweb01.cn/joomla/20210915/522d688f40165a81504e14c35e4d02c6.png',
                ),
                'tx_w' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑过图标宽度'),
                    'max' => 100,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                    'std' => array('md' => 38, 'sm' => 26, 'xs' => 13),
                    'responsive' => true,
                ),
                'tx_h' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑过图标高度'),
                    'max' => 100,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                    'std' => array('md' => 17, 'sm' => 12, 'xs' => 10),
                    'responsive' => true,
                ),
                'tx_top_bottom' => array(
                    'type' => 'slider',
                    'title' => JText::_('滑过图标上下间距'),
                    'max' => 100,
                    'depends' => array(
                        array('nav_section', '=', 'right_path'),
                        array('nav_style', '=', 'hover'),
                    ),
                    'std' => array('md' => 5, 'sm' => 5, 'xs' => 5),
                    'responsive' => true,
                ),
            ),
        ),
    )
);
