<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonTiyuyunguan_zuoyou_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $output = '<style>';
        $output .= 
        '#jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' a {
            color:#333;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' a:link{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' a:visited{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' a:hover{text-decoration:none;cursor:pointer;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' a:active{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .hidden{display:none;visibility:hidden;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap{border: 0;padding:0;margin:0 auto;*zoom:1;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap:after{clear: both;content:"";display: table;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .clear{clear:both;font-size:0;line-height:0;height:0;visibility:hidden;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .clearfix:before, .clearfix:after {
            content:"";
            display:table;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .clearfix:after {
            clear:both;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .clearfix {
            zoom:1;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap {
            padding:0 100px;
            margin:0 auto;
            position:relative;
        }
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap {padding:0 4.5%;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .mc-hidden {display:none}
        }
        @media (min-width:990px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .lg-hidden1 {display:none}
        }
        @media (max-width:767px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .sm-hidden {display:none;}
        }
        @media (min-width:768px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .lg-hidden {display:none;}
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap:after,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap:before,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container:after,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container:before{
            content:"";
            display:table;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .wrap:after,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container:after {clear:both;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container {width:80%;margin:0 auto;}
        @media (min-width:1200px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container {width:1200px;}
        }
        @media (min-width:1450px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container {width:1330px;}
        }
        @media (min-width:1530px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container {width:1530px;}
        }
        @media (min-width:1450px) {
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .ourwork-page .container {width:88%;}
        }
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .container {width:91%;}
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-page-block {
            /*z-index:99;*/
            /*position:relative;*/
            background:#fff;
        }

        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title {
            z-index:2;
            position:relative;
            padding-bottom:40px;
            text-align:center;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title {
            position:relative;
            height:40px;
            line-height:40px;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .big {
            z-index:1;
            position:relative;
            display:inline-block;
            padding:0 18px;
            font-size:28px;
            color:#333;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .line {
            position:absolute;
            top:22px;
            left:50%;
            display:block;
            width:65px;
            height:1px;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .l-line {margin-left:-147.5px;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .r-line {margin-left:80.5px;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .small {
            display:block;
            font:normal 16px/1.5 "gotham-medium";
            color:#999;
        }

        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work {
            position:relative;
            padding:85px 0 130px;
            background:#f0eeef;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {
            float:right;
            width:395px;
            background:#fff;
            overflow:hidden;
        }

        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text {
            position:relative;
            min-height:595px;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-scroll {
            width:100%;
            height:100%;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each{
            position: absolute;
            left: 0;
            top: 0;
            width: 88%;
            height: 595px;
            padding:70px 60px 50px;
            z-index: 0;
            background:#fff;
            opacity:0;
            transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
            -webkit-transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
            -moz-transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each.active {
            opacity: 1;
            z-index: 1;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .slide-title {
            font-size:24px;
            color:#333;
            line-height:1.5;
            text-align:center;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .slide-title .small {
            display:block;
            font-size:12px;
            color:#666;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .line {
            display:block;
            width:24px;
            height:2px;
            margin:15px auto;
            background-color:#999;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .slide-descrition {
            font-size:14px;
            color:#333;
            line-height:2;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap {
            position:absolute;
            left:0;
            top:0;
            width:100%;
            height:100%;
        }


        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image{
            z-index:0;
            position:absolute;
            height:100%;
            width:100%;
            opacity: 0;
            /*visibility: hidden;*/
            transition: all .85s ease-in-out 0s;
            -moz-transition: all .85s ease-in-out 0s;
            /*transform:translateX(15%);*/
        }

        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image.active {
            width: 100%;
            /*visibility: visible;*/
            opacity: 1;
            z-index: 1;
            /*transform:none;*/
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap img {
            position:absolute;
            left:50%;
            width:1920px;
            height:992px;
            margin-left:-960px;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons {
            z-index:2;
            position: absolute;
            left: 0;
            bottom: 50px;
            width: 100%;
            /*height: 50%;*/
            display: table;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count{ text-align: center; margin-bottom: 5px;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count span,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count i{
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            color: #999;
            letter-spacing: 1px;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count span {
            font-family:"gotham-medium";
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count span.current {color:#333;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .count i{ margin: 0 5px;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button{ display: table-cell; vertical-align: middle;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar{ text-align: center;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar a{
            display: inline-block;
            vertical-align: middle;
            width: 30px;
            height: 30px;
            margin:0 10px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-size: 17px;
            font-weight: bold;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar a.prev {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/left-icon1.png) no-repeat center center;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar a.next {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/right-icon1.png) no-repeat center center;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar .lines{
            display: inline-block;
            vertical-align: middle;
            width: 108px;
            height: 1px;
            background-color: #d9d9d9;
            position: relative;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .prograss-bar .lines .nline{
            width: 25%;
            height: 5px;
            background-color: #339ec1;
            position: absolute;
            left: 0;
            top: -2px;
            transition: all 300ms ease-in-out 0s;
            -webkit-transition: all 300ms ease-in-out 0s;
            -moz-transition: all 300ms ease-in-out 0s;
        }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons  .work-button .sum a:hover{color: #C80000;}

        @media (max-width:1450px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work {padding:45px 0 80px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .container {width:91%;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {padding-top:25px;width:300px}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text {min-height:450px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each {height:450px;padding: 50px 0 30px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .slide-descrition {max-height:168px;padding:0 40px;overflow:hidden;}
        }

        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap {height:20rem;margin-right:0;text-align:center;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap img {max-height:20rem;width:auto;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap,
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each{width:100%;background:transparent;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text {min-height:180px;padding:30px 40px; }
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each {height:200px;padding:0;margin-top: 50px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .slide-descrition,.index-work .swiper-slide-text .line {display:none;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons {bottom:0;display:block;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .work-buttons .work-button {display:block;}
        }

        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title,.index-service-scroll-wrap {
                opacity:1;
                margin-top:30px;
                transition:all .5s ease-in-out;
            }
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work {opacity:0;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .active .index-title,
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work.active{opacity:1;margin-top:0;}
        #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .swiper-container {position:relative;width:100%;height:100%;}
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title {height:30px;line-height:30px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .big {font-size:22px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .big-title .line {top:15px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-title .small {font-size:14px;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work {padding:45px 0;}
        }
        @media (max-width:768px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work { height:40rem;overflow:hidden; }
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap {height:100%;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {min-height:12rem;padding-top:22rem;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text .swiper-slide-text-each {height:10rem;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text {min-height:10rem;}
            }

        @media (max-width:600px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {padding-top:20rem;}
        }
        @media (max-width:420px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {padding-top:16.5rem;}
        }
        @media(min-width:768px) and (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap.sm-hidden {display:none;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap.lg-hidden {display:block;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-image-wrap {height:100%;}
            #jwpf-tiyuyunguan-swiper2-' . $this->addon->id . ' .index-work .swiper-slide-text-wrap {padding-top:22rem;}
        }';
        $output .= '</style>';
        $output .= '<div class="js-container2" id="jwpf-tiyuyunguan-swiper2-' . $this->addon->id . '">';
            $output .= '<div class="index-page-block">';
              $output .= '<div class="index-work index-wrap active">';
                $output .= '<div class="container">';
                  $output .= '<h2 class="index-title">';
                    $output .= '<div class="big-title">';
                      $output .= '<strong class="big"></strong><span class="line l-line"></span><span class="line r-line"></span>';
                    $output .= '</div>';
                    $output .= '<span class="small"></span>';
                  $output .= '</h2>';
           
                  $output .= '<div class="index-work-content">';
                    $output .= '<div class="work-swiper-container">';
                      $output .= '<div class="swiper-slide-image-wrap sm-hidden">';
                     foreach ($settings->jw_tiyuyunguan_zuoyou_item as $key => $value) {   
                                  
                       $output .= '<div class="swiper-slide-image '.(($key == 0) ? 'active' : '') .'" style="background: url(' . $value->bg . ') no-repeat center center;background-size:auto 100%">';
                        $output .= '<a style="display:block;width:100%;height:100%;" href="'.$value->button_url.'" rel="nofollow">&nbsp;</a>';
                          $output .= '</div>';

                       }
                       $output .= '</div>';
                      $output .= '<div class="swiper-slide-image-wrap lg-hidden">';
                     foreach ($settings->jw_tiyuyunguan_zuoyou_item as $key2 => $value2) {   
                                  
                        $output .= '<div class="swiper-slide-image '.(($key2 == 0) ? 'active' : '') .'" style="background:url(' . $value2->wapbg . ') no-repeat center center;background-size:auto 100%">';
                         $output .= '<a style="display:block;width:100%;height:100%;" href="'.$value2->button_url.'" rel="nofollow">&nbsp;</a>';
                        $output .= '</div>';
                    }

                       $output .= '</div>';
                      $output .= '<div class="swiper-slide-text-wrap">';
                        $output .= '<div class="swiper-slide-text">';
                       $output .= '<div class="swiper-slide-text-scroll">';
                        foreach ($settings->jw_tiyuyunguan_zuoyou_item as $key3 => $value3) {   
                              $output .= '<div class="swiper-slide-text-each '.(($key3 == 0) ? 'active' : '') .'">';
                              $output .= '<h2 class="slide-title">'.$value3->title.'<span class="small"></span></h2>';
                              $output .= '<span class="line"></span>';
                              $output .= '<div class="slide-descrition">'.$value3->content.'</div>';
                              $output .= '</div> ';
                              
                            }

                           $output .= '</div>';
                          $output .= '<div class="work-buttons">';
                            $output .= '<div class="work-button">';
                              $output .= '<div class="count">';
                              $output .= '<span class="current">01</span><i>/</i><span class="total">01</span>';
                              $output .= '</div>';
                              $output .= '<div class="prograss-bar">';
                                $output .= '<a class="prev iconfont"></a>';
                                $output .= '<div class="lines">';
                                  $output .= '<div class="nline" style="width: 54px; left: 0px;"></div>';
                                $output .='</div>';
                                $output .=' <a class="next iconfont"></a>';
                              $output .=' </div>';
                            $output .=' </div>';
                          $output .= '</div>';
                        $output .= '</div>';
                      $output .= '</div>';
                    $output .= '</div>';
                  $output .= '</div>';
               $output .=  '</div>';
             $output .=  '</div>';
            $output .= '</div>';
          $output .= '</div>';
                 $output .= "
                    <script>
                      
        (function(jQuery){
            
            jsContainer = jQuery('.js-container2');
            //index-work
            jQuery.fn.pictureScroll = function(){
                return this.each(function(){
                    var jQuerythis = jQuery(this),
                        p = jQuerythis.find('.work-buttons .work-button a.prev'),
                        l = jQuerythis.find('.work-buttons .work-button a.next'),
                        g = jQuerythis.find('.lg-hidden .swiper-slide-image'),
                        g0 = jQuerythis.find('.sm-hidden .swiper-slide-image'),
                        k = jQuerythis.find('.swiper-slide-text-scroll .swiper-slide-text-each'),
                        g1 = jQuerythis.find('.lg-hidden .swiper-slide-image:first-child'),
                        g2 = jQuerythis.find('.sm-hidden .swiper-slide-image:first-child'),
                        k1 = jQuerythis.find('.swiper-slide-text-scroll .swiper-slide-text-each:first-child'),
                        q = jQuerythis.find('.work-buttons .work-button .prograss-bar .lines').width(),
                        d = jQuerythis.find('.work-buttons .work-button .prograss-bar .lines .nline'),
                        m = jQuerythis.find('.work-buttons .work-button .count span.current'),
                        r = jQuerythis.find('.work-buttons .work-button .count span.total'),
                        c = g.length - 1,
                        n = k.length - 1,
                        a = 0,
                        b = 0,
                        e = q / (c + 1),
                        timer = null;
                    g1.addClass('active');
                    g2.addClass('active');
                    k1.addClass('active');
                    function f() {
                        g.eq(a).addClass('active').siblings().removeClass('active');
                        g0.eq(a).addClass('active').siblings().removeClass('active');
                    }
                    function h() {
                        k.eq(b).addClass('active').siblings().removeClass('active');
                    }
                    d.width(e);
                    r.html('0' + (c + 1));
                    p.click(function () {
                        clearInterval(timer);
                        a--;
                        b--;
                        a = 0 > a ? c : a;
                        b = 0 > b ? n : b;
                        f();
                        h();
                        d.css({
                            left: e * a
                        });
                        m.html('0' + (a + 1))
                    });
                    l.click(function () {
                        a++;
                        b++;
                        a = a > c ? 0 : a;
                        b = b > n ? 0 : b;
                        f();
                        h();
                        m.html('0' + (a + 1));
                        d.css({
                            left: e * a
                        })
                    });
                    var timer = setInterval(function () {
                        l.click()
                    }, 6000)
                })
            }
            jQuery('.index-work').pictureScroll();
        })(jQuery);
                    </script>
                    ";
                return $output;
    }
    public static function getTemplate()
    {   $output = '<style>';
        $output .= 
        '#jwpf-tiyuyunguan-swiper2-{{data.id}} a {
            color:#333;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} a:link{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} a:visited{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} a:hover{text-decoration:none;cursor:pointer;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} a:active{text-decoration:none;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .hidden{display:none;visibility:hidden;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap{border: 0;padding:0;margin:0 auto;*zoom:1;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap:after{clear: both;content:"";display: table;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .clear{clear:both;font-size:0;line-height:0;height:0;visibility:hidden;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .clearfix:before, .clearfix:after {
            content:"";
            display:table;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .clearfix:after {
            clear:both;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .clearfix {
            zoom:1;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap {
            padding:0 100px;
            margin:0 auto;
            position:relative;
        }
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap {padding:0 4.5%;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .mc-hidden {display:none}
        }
        @media (min-width:990px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .lg-hidden1 {display:none}
        }
        @media (max-width:767px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .sm-hidden {display:none;}
        }
        @media (min-width:768px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .lg-hidden {display:none;}
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap:after,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap:before,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .container:after,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .container:before{
            content:"";
            display:table;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .wrap:after,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .container:after {clear:both;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .container {width:80%;margin:0 auto;}
        @media (min-width:1200px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .container {width:1200px;}
        }
        @media (min-width:1450px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .container {width:1330px;}
        }
        @media (min-width:1530px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .container {width:1530px;}
        }
        @media (min-width:1450px) {
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .ourwork-page .container {width:88%;}
        }
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .container {width:91%;}
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-page-block {
            /*z-index:99;*/
            /*position:relative;*/
            background:#fff;
        }

        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title {
            z-index:2;
            position:relative;
            padding-bottom:40px;
            text-align:center;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title {
            position:relative;
            height:40px;
            line-height:40px;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .big {
            z-index:1;
            position:relative;
            display:inline-block;
            padding:0 18px;
            font-size:28px;
            color:#333;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .line {
            position:absolute;
            top:22px;
            left:50%;
            display:block;
            width:65px;
            height:1px;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .l-line {margin-left:-147.5px;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .r-line {margin-left:80.5px;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .small {
            display:block;
            font:normal 16px/1.5 "gotham-medium";
            color:#999;
        }

        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work {
            position:relative;
            padding:85px 0 130px;
            background:#f0eeef;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {
            float:right;
            width:395px;
            background:#fff;
            overflow:hidden;
        }

        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text {
            position:relative;
            min-height:595px;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-scroll {
            width:100%;
            height:100%;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each{
            position: absolute;
            left: 0;
            top: 0;
            width: 88%;
            height: 595px;
            padding:70px 60px 50px;
            z-index: 0;
            background:#fff;
            opacity:0;
            transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
            -webkit-transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
            -moz-transition: all 0.85s cubic-bezier(0.55, 0.65, 0.55, 1) 0s;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each.active {
            opacity: 1;
            z-index: 1;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .slide-title {
            font-size:24px;
            color:#333;
            line-height:1.5;
            text-align:center;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .slide-title .small {
            display:block;
            font-size:12px;
            color:#666;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .line {
            display:block;
            width:24px;
            height:2px;
            margin:15px auto;
            background-color:#999;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .slide-descrition {
            font-size:14px;
            color:#333;
            line-height:2;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap {
            position:absolute;
            left:0;
            top:0;
            width:100%;
            height:100%;
        }


        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image{
            z-index:0;
            position:absolute;
            height:100%;
            width:100%;
            opacity: 0;
            /*visibility: hidden;*/
            transition: all .85s ease-in-out 0s;
            -moz-transition: all .85s ease-in-out 0s;
            /*transform:translateX(15%);*/
        }

        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image.active {
            width: 100%;
            /*visibility: visible;*/
            opacity: 1;
            z-index: 1;
            /*transform:none;*/
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap img {
            position:absolute;
            left:50%;
            width:1920px;
            height:992px;
            margin-left:-960px;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons {
            z-index:2;
            position: absolute;
            left: 0;
            bottom: 50px;
            width: 100%;
            /*height: 50%;*/
            display: table;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count{ text-align: center; margin-bottom: 5px;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count span,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count i{
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            color: #999;
            letter-spacing: 1px;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count span {
            font-family:"gotham-medium";
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count span.current {color:#333;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .count i{ margin: 0 5px;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button{ display: table-cell; vertical-align: middle;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar{ text-align: center;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar a{
            display: inline-block;
            vertical-align: middle;
            width: 30px;
            height: 30px;
            margin:0 10px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-size: 17px;
            font-weight: bold;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar a.prev {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/left-icon1.png) no-repeat center center;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar a.next {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_zuoyou_swiper/assets/images/right-icon1.png) no-repeat center center;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar .lines{
            display: inline-block;
            vertical-align: middle;
            width: 108px;
            height: 1px;
            background-color: #d9d9d9;
            position: relative;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .prograss-bar .lines .nline{
            width: 25%;
            height: 5px;
            background-color: #339ec1;
            position: absolute;
            left: 0;
            top: -2px;
            transition: all 300ms ease-in-out 0s;
            -webkit-transition: all 300ms ease-in-out 0s;
            -moz-transition: all 300ms ease-in-out 0s;
        }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons  .work-button .sum a:hover{color: #C80000;}

        @media (max-width:1450px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work {padding:45px 0 80px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .container {width:91%;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {padding-top:25px;width:300px}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text {min-height:450px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each {height:450px;padding: 50px 0 30px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .slide-descrition {max-height:168px;padding:0 40px;overflow:hidden;}
        }

        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap {height:20rem;margin-right:0;text-align:center;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap img {max-height:20rem;width:auto;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap,
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each{width:100%;background:transparent;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text {min-height:180px;padding:30px 40px; }
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each {height:200px;padding:0;margin-top: 50px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .slide-descrition,.index-work .swiper-slide-text .line {display:none;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons {bottom:0;display:block;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .work-buttons .work-button {display:block;}
        }

        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title,.index-service-scroll-wrap {
                opacity:1;
                margin-top:30px;
                transition:all .5s ease-in-out;
            }
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work {opacity:0;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .active .index-title,
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work.active{opacity:1;margin-top:0;}
        #jwpf-tiyuyunguan-swiper2-{{data.id}} .swiper-container {position:relative;width:100%;height:100%;}
        @media (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title {height:30px;line-height:30px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .big {font-size:22px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .big-title .line {top:15px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-title .small {font-size:14px;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work {padding:45px 0;}
        }
        @media (max-width:768px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work { height:40rem;overflow:hidden; }
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap {height:100%;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {min-height:12rem;padding-top:22rem;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text .swiper-slide-text-each {height:10rem;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text {min-height:10rem;}
            }

        @media (max-width:600px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {padding-top:20rem;}
        }
        @media (max-width:420px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {padding-top:16.5rem;}
        }
        @media(min-width:768px) and (max-width:991px){
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap.sm-hidden {display:none;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap.lg-hidden {display:block;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-image-wrap {height:100%;}
            #jwpf-tiyuyunguan-swiper2-{{data.id}} .index-work .swiper-slide-text-wrap {padding-top:22rem;}
        }';
        $output .= '</style>';
        $output .= '<div class="js-container2" id="jwpf-tiyuyunguan-swiper2-{{data.id}}">';
            $output .= '<div class="index-page-block">';
              $output .= '<div class="index-work index-wrap active">';
                $output .= '<div class="container">';
                  $output .= '<h2 class="index-title">';
                    $output .= '<div class="big-title">';
                      $output .= '<strong class="big"></strong><span class="line l-line"></span><span class="line r-line"></span>';
                    $output .= '</div>';
                    $output .= '<span class="small"></span>';
                  $output .= '</h2>';
           
                  $output .= '<div class="index-work-content">';
                    $output .= '<div class="work-swiper-container">';
                      $output .= '<div class="swiper-slide-image-wrap sm-hidden">';
                    
                    // $output .= ' <# _.each(data.jw_tiyuyunguan_zuoyou_item, function (jw_tiyuyunguan_zuoyou_item, key){ #> 
                    //      <#
                    //         var classNames = (key == 0) ? "active" : "";
                    //     #>    
                    //   <div class="swiper-slide-image  {{ classNames }}" style="background: url({{ jw_tiyuyunguan_zuoyou_item.bg }}) no-repeat center center;background-size:auto 100%">
                    //     <a style="display:block;width:100%;height:100%;" href="{{ jw_tiyuyunguan_zuoyou_item.button_url }}" rel="nofollow">&nbsp;</a>
                    //      </div>
                    //       <# }); #>
                    //            ';


                       $output .= '</div>';
                      $output .= '<div class="swiper-slide-image-wrap lg-hidden">';
                      
                   //   $output .= ' <# _.each(data.jw_tiyuyunguan_zuoyou_item, function (jw_tiyuyunguan_zuoyou_item2, key2){ #> 
                   //       <#
                   //          var classNames2 = (key2 == 0) ? "active" : "";
                   //      #>    
                                  
                   //      <div class="swiper-slide-image  {{ classNames2 }}" style="background:url({{ jw_tiyuyunguan_zuoyou_item2.wapbg }}) no-repeat center center;background-size:auto 100%">
                   //      <a style="display:block;width:100%;height:100%;" href="{{ jw_tiyuyunguan_zuoyou_item2.button_url }}" rel="nofollow">&nbsp;</a>
                   //     </div>
                   // <# }); #>
                   //             ';

                       $output .= '</div>';
                      $output .= '<div class="swiper-slide-text-wrap">';
                        $output .= '<div class="swiper-slide-text">';
                       $output .= '<div class="swiper-slide-text-scroll">';

                     // $output .= ' <# _.each(data.jw_tiyuyunguan_zuoyou_item, function (jw_tiyuyunguan_zuoyou_item3, key3){ #> 
                     //     <#
                     //        var classNames3 = (key3 == 0) ? "active" : "";
                     //    #>    
                     //          <div class="swiper-slide-text-each  {{ classNames3 }}">
                     //          <h2 class="slide-title">{{ jw_tiyuyunguan_zuoyou_item2.title }}<span class="small"></span></h2>
                     //          <span class="line"></span>
                     //          <div class="slide-descrition">{{ jw_tiyuyunguan_zuoyou_item2.content }}</div>
                     //          </div>
                     //           <# }); #>
                     //           ';

                           $output .= '</div>';
                          $output .= '<div class="work-buttons">';
                            $output .= '<div class="work-button">';
                              $output .= '<div class="count">';
                              $output .= '<span class="current">01</span><i>/</i><span class="total">01</span>';
                              $output .= '</div>';
                              $output .= '<div class="prograss-bar">';
                                $output .= '<a class="prev iconfont"></a>';
                                $output .= '<div class="lines">';
                                  $output .= '<div class="nline" style="width: 54px; left: 0px;"></div>';
                                $output .='</div>';
                                $output .=' <a class="next iconfont"></a>';
                              $output .=' </div>';
                            $output .=' </div>';
                          $output .= '</div>';
                        $output .= '</div>';
                      $output .= '</div>';
                    $output .= '</div>';
                  $output .= '</div>';
               $output .=  '</div>';
             $output .=  '</div>';
            $output .= '</div>';
          $output .= '</div>';
                 $output .= "
                    <script>
                      
        (function(jQuery){
            var mainWit = jQuery(window).width(),
                mainHit = jQuery(window).height(),
                jsContainer = jQuery('#jwpf-tiyuyunguan-swiper2-{{data.id}} ');
            
         
            
            //index-work
            jQuery.fn.pictureScroll = function(){
                return this.each(function(){
                    var jQuerythis = jQuery(this),
                        p = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button a.prev'),
                        l = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button a.next'),
                        g = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .lg-hidden .swiper-slide-image'),
                        g0 = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .sm-hidden .swiper-slide-image'),
                        k = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .swiper-slide-text-scroll .swiper-slide-text-each'),
                        g1 = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .lg-hidden .swiper-slide-image:first-child'),
                        g2 = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .sm-hidden .swiper-slide-image:first-child'),
                        k1 = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .swiper-slide-text-scroll .swiper-slide-text-each:first-child'),
                        q = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button .prograss-bar .lines').width(),
                        d = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button .prograss-bar .lines .nline'),
                        m = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button .count span.current'),
                        r = jQuerythis.find('#jwpf-tiyuyunguan-swiper2-{{data.id}} .work-buttons .work-button .count span.total'),
                        c = g.length - 1,
                        n = k.length - 1,
                        a = 0,
                        b = 0,
                        e = q / (c + 1),
                        timer = null;
                    g1.addClass('active');
                    g2.addClass('active');
                    k1.addClass('active');
                    function f() {
                        g.eq(a).addClass('active').siblings().removeClass('active');
                        g0.eq(a).addClass('active').siblings().removeClass('active');
                    }
                    function h() {
                        k.eq(b).addClass('active').siblings().removeClass('active');
                    }
                    d.width(e);
                    r.html('0' + (c + 1));
                    p.click(function () {
                        clearInterval(timer);
                        a--;
                        b--;
                        a = 0 > a ? c : a;
                        b = 0 > b ? n : b;
                        f();
                        h();
                        d.css({
                            left: e * a
                        });
                        m.html('0' + (a + 1))
                    });
                    l.click(function () {
                        a++;
                        b++;
                        a = a > c ? 0 : a;
                        b = b > n ? 0 : b;
                        f();
                        h();
                        m.html('0' + (a + 1));
                        d.css({
                            left: e * a
                        })
                    });
                    var timer = setInterval(function () {
                        l.click()
                    }, 6000)
                })
            }
            jQuery('.index-work').pictureScroll();
        })(jQuery);
                    </script>
                    ";
         return $output;
    }
}