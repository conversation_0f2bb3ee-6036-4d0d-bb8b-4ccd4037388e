<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonCq_ty_list00010 extends JwpagefactoryAddons
{

    public function render()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $page_view_name = isset($_GET['view']);

        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        $style = (isset($settings->style) && $settings->style) ? $settings->style : 'panel-default';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';

        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';

        // Addon options
        $resource = (isset($settings->resource) && $settings->resource) ? $settings->resource : 'article';
        $catid = (isset($settings->catid) && $settings->catid) ? $settings->catid : 0;
        //   新闻产品选择
        $product_catid = (isset($settings->product_catid) && $settings->product_catid) ? $settings->product_catid : 0;
        //   新闻产品选择
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $k2catid = (isset($settings->k2catid) && $settings->k2catid) ? $settings->k2catid : 0;
        $include_subcat = (isset($settings->include_subcat)) ? $settings->include_subcat : 1;
        $post_type = (isset($settings->post_type) && $settings->post_type) ? $settings->post_type : '';
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';
        $limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 8;
        $columns = (isset($settings->columns) && $settings->columns) ? $settings->columns : 3;
        $show_intro = (isset($settings->show_intro)) ? $settings->show_intro : 1;
        $intro_limit = (isset($settings->intro_limit) && $settings->intro_limit) ? $settings->intro_limit : 20;
        $hide_thumbnail = (isset($settings->hide_thumbnail)) ? $settings->hide_thumbnail : 0;
        $show_author = (isset($settings->show_author)) ? $settings->show_author : 1;
        $show_category = (isset($settings->show_category)) ? $settings->show_category : 1;
        $show_date = (isset($settings->show_date)) ? $settings->show_date : 1;
        $show_readmore = (isset($settings->show_readmore)) ? $settings->show_readmore : 1;
        $readmore_text = (isset($settings->readmore_text) && $settings->readmore_text) ? $settings->readmore_text : 'Read More';
        $link_articles = (isset($settings->link_articles)) ? $settings->link_articles : 0;
        $link_catid = (isset($settings->link_catid)) ? $settings->link_catid : 0;
        $link_k2catid = (isset($settings->link_k2catid)) ? $settings->link_k2catid : 0;

        $all_articles_btn_text = (isset($settings->all_articles_btn_text) && $settings->all_articles_btn_text) ? $settings->all_articles_btn_text : 'See all posts';
        $all_articles_btn_class = (isset($settings->all_articles_btn_size) && $settings->all_articles_btn_size) ? ' jwpf-btn-' . $settings->all_articles_btn_size : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? ' jwpf-btn-' . $settings->all_articles_btn_type : ' jwpf-btn-default';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_shape) && $settings->all_articles_btn_shape) ? ' jwpf-btn-' . $settings->all_articles_btn_shape : ' jwpf-btn-rounded';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? ' jwpf-btn-' . $settings->all_articles_btn_appearance : '';
        $all_articles_btn_class .= (isset($settings->all_articles_btn_block) && $settings->all_articles_btn_block) ? ' ' . $settings->all_articles_btn_block : '';
        $all_articles_btn_icon = (isset($settings->all_articles_btn_icon) && $settings->all_articles_btn_icon) ? $settings->all_articles_btn_icon : '';
        $all_articles_btn_icon_position = (isset($settings->all_articles_btn_icon_position) && $settings->all_articles_btn_icon_position) ? $settings->all_articles_btn_icon_position : 'left';
        $title_font_size_set = (isset($settings->title_font_size_set) && $settings->title_font_size_set) ? $settings->title_font_size_set : 18;
        $title_color = (isset($settings->title_color) && $settings->title_color) ? $settings->title_color : '#353535';
        $hover_background = (isset($settings->hover_background) && $settings->hover_background) ? $settings->hover_background : 'rgba(0,0,0,0.4)';

        $subtitle_color = (isset($settings->subtitle_color) && $settings->subtitle_color) ? $settings->subtitle_color : '#9a9a9a';
        $meta_font_size_set = (isset($settings->meta_font_size_set) && $settings->meta_font_size_set) ? $settings->meta_font_size_set : 13;
        $meta_color = (isset($settings->meta_color) && $settings->meta_color) ? $settings->meta_color : '#fff';
        $meta_desc_color = (isset($settings->meta_desc_color) && $settings->meta_desc_color) ? $settings->meta_desc_color : '#9c9c9c';

        $meta_subtitle_color = (isset($settings->meta_subtitle_color) && $settings->meta_subtitle_color) ? $settings->meta_subtitle_color : '#9c9c9c';
        $meta_background = (isset($settings->meta_background) && $settings->meta_background) ? $settings->meta_background : '#383838';
        $center_color = (isset($settings->center_color) && $settings->center_color) ? $settings->center_color : 'rgba(255, 255, 255, 0)';

        $instFormId = (isset($settings->instFormId) && $settings->instFormId) ? $settings->instFormId : time() . rand(000, 999);
        $attr_1 = (isset($settings->attr_1)) ? $settings->attr_1 . 'px' : ' 16px';
        $attr_2 = (isset($settings->attr_2)) ? $settings->attr_2 . 'px' : ' 16px';
        $attr_3 = (isset($settings->attr_3)) ? $settings->attr_3 : ' #60667E';
        $attr_4 = (isset($settings->attr_4)) ? $settings->attr_4 : ' #60667E';
        $attr_5 = (isset($settings->attr_5)) ? $settings->attr_5 . 'px' : ' 17px';
        $attr_6 = (isset($settings->attr_6)) ? $settings->attr_6 . 'px' : ' 17px';
        $attr_7 = (isset($settings->attr_7)) ? $settings->attr_7 : ' #AFB2C0';
        $attr_8 = (isset($settings->attr_8)) ? $settings->attr_8 . 'px' : ' 14px';
        $attr_9 = (isset($settings->attr_9)) ? $settings->attr_9 . 'px' : ' 24px';
        $attr_10 = (isset($settings->attr_10)) ? $settings->attr_10 : ' #9797A1';
        $attr_11 = (isset($settings->attr_11)) ? $settings->attr_11 : 'lightcoral';
        $a_link_open = (isset($settings->a_link_open)) ? $settings->a_link_open : 0; // 是否开启链接


        //第一组
        $image1 = (isset($settings->image1) && $settings->image1) ? $settings->image1 : '';
        $title1 = (isset($settings->title1)) ? $settings->title1 : '标题'; // 第一组标题
        $dec1 = (isset($settings->dec1)) ? $settings->dec1 : '描述'; // 第一组描述
        $keyword1_1 = (isset($settings->keyword1_1)) ? $settings->keyword1_1 : '关键字1'; // 第一组关键字1
        $keyword1_2 = (isset($settings->keyword1_2)) ? $settings->keyword1_2 : '关键字2'; // 第一组关键字2
        $image1_hover = (isset($settings->image1_hover)) ? $settings->image1_hover : ''; // 鼠标进入第一组图片
        $link1 = (isset($settings->link1) && $settings->link1) ? $settings->link1 : ''; // 链接
        $detail_page_id1 = (isset($settings->detail_page_id1)) ? $settings->detail_page_id1 : 0; // 页面id
        $target1 = (isset($settings->target1) && $settings->target1) ? 'target="' . $settings->target1 . '" rel="noopener noreferrer"' : ''; // 打开页面状态
        //第二组
        $image2 = (isset($settings->image2) && $settings->image2) ? $settings->image2 : '';
        $title2 = (isset($settings->title2)) ? $settings->title2 : '标题';
        $dec2 = (isset($settings->dec2)) ? $settings->dec2 : '描述';
        $keyword2_1 = (isset($settings->keyword2_1)) ? $settings->keyword2_1 : '关键字1';
        $keyword2_2 = (isset($settings->keyword2_2)) ? $settings->keyword2_2 : '关键字2';
        $image2_hover = (isset($settings->image2_hover)) ? $settings->image2_hover : '';
        $link2 = (isset($settings->link2) && $settings->link2) ? $settings->link2 : '';
        $detail_page_id2 = (isset($settings->detail_page_id2)) ? $settings->detail_page_id2 : 0;
        $target2 = (isset($settings->target2) && $settings->target2) ? 'target="' . $settings->target2 . '" rel="noopener noreferrer"' : '';

        //第三组
        $image3 = (isset($settings->image3) && $settings->image3) ? $settings->image3 : '';
        $title3 = (isset($settings->title3)) ? $settings->title3 : '标题'; // 第3组标题
        $dec3 = (isset($settings->dec3)) ? $settings->dec3 : '描述'; // 第3组描述
        $keyword3_1 = (isset($settings->keyword3_1)) ? $settings->keyword3_1 : '关键字1';
        $keyword3_2 = (isset($settings->keyword3_2)) ? $settings->keyword3_2 : '关键字2';
        $image3_hover = (isset($settings->image3_hover)) ? $settings->image3_hover : '';
        $link3 = (isset($settings->link3) && $settings->link3) ? $settings->link3 : '';
        $detail_page_id3 = (isset($settings->detail_page_id3)) ? $settings->detail_page_id3 : 0;
        $target3 = (isset($settings->target3) && $settings->target3) ? 'target="' . $settings->target3 . '" rel="noopener noreferrer"' : '';


        //第四组
        $image4 = (isset($settings->image4) && $settings->image4) ? $settings->image4 : '';
        $title4 = (isset($settings->title4)) ? $settings->title4 : '标题';
        $dec4 = (isset($settings->dec4)) ? $settings->dec4 : '描述';
        $keyword4_1 = (isset($settings->keyword4_1)) ? $settings->keyword4_1 : '关键字1';
        $keyword4_2 = (isset($settings->keyword4_2)) ? $settings->keyword4_2 : '关键字2';
        $image4_hover = (isset($settings->image4_hover)) ? $settings->image4_hover : '';
        $link4 = (isset($settings->link4) && $settings->link4) ? $settings->link4 : '';
        $detail_page_id4 = (isset($settings->detail_page_id4)) ? $settings->detail_page_id4 : 0;
        $target4 = (isset($settings->target4) && $settings->target4) ? 'target="' . $settings->target4 . '" rel="noopener noreferrer"' : '';

        //第五组
        $image5 = (isset($settings->image5) && $settings->image5) ? $settings->image5 : '';
        $title5 = (isset($settings->title5)) ? $settings->title5 : '标题';
        $dec5 = (isset($settings->dec5)) ? $settings->dec5 : '描述';
        $keyword5_1 = (isset($settings->keyword5_1)) ? $settings->keyword5_1 : '关键字1';
        $keyword5_2 = (isset($settings->keyword5_2)) ? $settings->keyword5_2 : '关键字2';
        $image5_hover = (isset($settings->image5_hover)) ? $settings->image5_hover : '';
        $link5 = (isset($settings->link5) && $settings->link5) ? $settings->link5 : '';
        $detail_page_id5 = (isset($settings->detail_page_id5)) ? $settings->detail_page_id5 : 0;
        $target5 = (isset($settings->target5) && $settings->target5) ? 'target="' . $settings->target5 . '" rel="noopener noreferrer"' : '';

        //第六组
        $image6 = (isset($settings->image6) && $settings->image6) ? $settings->image6 : '';
        $title6 = (isset($settings->title6)) ? $settings->title6 : '标题';
        $dec6 = (isset($settings->dec6)) ? $settings->dec6 : '描述';
        $keyword6_1 = (isset($settings->keyword6_1)) ? $settings->keyword6_1 : '关键字1';
        $keyword6_2 = (isset($settings->keyword6_2)) ? $settings->keyword6_2 : '关键字2';
        $image6_hover = (isset($settings->image6_hover)) ? $settings->image6_hover : '';
        $link6 = (isset($settings->link6) && $settings->link6) ? $settings->link6 : '';
        $detail_page_id6 = (isset($settings->detail_page_id6)) ? $settings->detail_page_id6 : 0;
        $target6 = (isset($settings->target6) && $settings->target6) ? 'target="' . $settings->target6 . '" rel="noopener noreferrer"' : '';

        //第七组
        $image7 = (isset($settings->image7) && $settings->image7) ? $settings->image7 : '';
        $title7 = (isset($settings->title7)) ? $settings->title7 : '标题';
        $dec7 = (isset($settings->dec7)) ? $settings->dec7 : '描述';
        $keyword7_1 = (isset($settings->keyword7_1)) ? $settings->keyword7_1 : '关键字1';
        $keyword7_2 = (isset($settings->keyword7_2)) ? $settings->keyword7_2 : '关键字2';
        $image7_hover = (isset($settings->image7_hover)) ? $settings->image7_hover : '';
        $link7 = (isset($settings->link7) && $settings->link7) ? $settings->link7 : '';
        $detail_page_id7 = (isset($settings->detail_page_id7)) ? $settings->detail_page_id7 : 0;
        $target7 = (isset($settings->target7) && $settings->target7) ? 'target="' . $settings->target7 . '" rel="noopener noreferrer"' : '';

        //第八组
        $image8 = (isset($settings->image8) && $settings->image8) ? $settings->image8 : '';
        $title8 = (isset($settings->title8)) ? $settings->title8 : '标题';
        $dec8 = (isset($settings->dec8)) ? $settings->dec8 : '描述';
        $keyword8_1 = (isset($settings->keyword8_1)) ? $settings->keyword8_1 : '关键字1';
        $keyword8_2 = (isset($settings->keyword8_2)) ? $settings->keyword8_2 : '关键字2';
        $image8_hover = (isset($settings->image8_hover)) ? $settings->image8_hover : '';
        $link8 = (isset($settings->link8) && $settings->link8) ? $settings->link8 : '';
        $detail_page_id8 = (isset($settings->detail_page_id8)) ? $settings->detail_page_id8 : 0;
        $target8 = (isset($settings->target8) && $settings->target8) ? 'target="' . $settings->target8 . '" rel="noopener noreferrer"' : '';

        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        // 要跳转页面的id
        if($detail_page_id1){
            $id=base64_encode($detail_page_id1);
            $link1 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id2){
            $id=base64_encode($detail_page_id2);
            $link2 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id3){
            $id=base64_encode($detail_page_id3);
            $link3 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id4){
            $id=base64_encode($detail_page_id4);
            $link4 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id5){
            $id=base64_encode($detail_page_id5);
            $link5 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id6){
            $id=base64_encode($detail_page_id6);
            $link6 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id7){
            $id=base64_encode($detail_page_id7);
            $link7 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        if($detail_page_id8){
            $id=base64_encode($detail_page_id8);
            $link8 = 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        // 打开a链接
        if($a_link_open==0){
            $link1="javascript:void(0);";
            $link2="javascript:void(0);";
            $link3="javascript:void(0);";
            $link4="javascript:void(0);";
            $link5="javascript:void(0);";
            $link6="javascript:void(0);";
            $link7="javascript:void(0);";
            $link8="javascript:void(0);";
        }
        $hide_fenye = (isset($settings->hide_fenye)) ? $settings->hide_fenye : 0;
        $output = '';

        /*背景图*/
        $global_background_image = (isset($settings->global_background_image) && $settings->global_background_image) ? $settings->global_background_image : '';
        $global_background_color = (isset($settings->global_background_color) && $settings->global_background_color) ? $settings->global_background_color : '';
        $back_img = 'style="background:url(' . $global_background_image . ') no-repeat ' . $global_background_color . ';"';

        $class = ' cloud-wrapper-1';
        if ($columns >= 5) {
            $class = '  cloud-wrapper-5';
        }
        if ($columns == 4) {
            $class = '  cloud-wrapper-4';
        }
        if ($columns == 3) {
            $class = '  cloud-wrapper-3';
        }
        if ($columns == 2) {
            $class = ' cloud-wrapper-2';
        }


        $output .= '<div class="zh-hover-box clear">';

        $output .= '
                    <a '. $target1 . ' href="' . $link1 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image1 . '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title1 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec1 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword1_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword1_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;"><img src="' . $image1_hover .  '" alt=""></div>
                     </a>

                    <a ' . $target2 . ' href="' . $link2 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image2 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title2 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec2 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword2_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword2_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;"><img src="' . $image2_hover .  '" alt=""></div>
                     </a> 
                     
                     <a ' . $target3 . ' href="' . $link3 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image3 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title3 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec3 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword3_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword2_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image3_hover .  '" alt="">
                            </div>
                     </a>
                     
                     <a ' . $target4 . ' href="' . $link4 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image4 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title4 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec4 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword4_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword4_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image4_hover .  '" alt="">
                            </div>
                     </a>
                     
                      <a ' . $target5 . ' href="' . $link5 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image5 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title5 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec5 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword5_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword5_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image5_hover .  '" alt="">
                            </div>
                     </a>
                     
                      <a ' . $target6 . ' href="' . $link6 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image6 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title6 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec6 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword6_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword6_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image6_hover .  '" alt="">
                            </div>
                     </a>
                     
                      <a ' . $target7 . ' href="' . $link7 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image7 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title7 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec7 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword7_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword7_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image7_hover .  '" alt="">
                            </div>
                     </a>
                      <a ' . $target8 . ' href="' . $link8 . '" class="zh-hover-box-c">
                             <div class="zh-hover-box-ci">
                                  <img src="' . $image8 .  '" alt="">
                             </div>
                             <div class="zh-hover-box-cw1">' . $title8 .  '
                                   <div class="zh-hover-box-cwx"></div>
                             </div>
                            <div class="zh-hover-box-cw2">' . $dec8 .  '</div>
                            <div class="zh-hover-box-b">
                            <div class="zh-hover-box-bw1">' . $keyword8_1 .  '</div><div class="zh-hover-box-bw1">' . $keyword8_2 .  '</div>
                            </div>
                            <div class="content trans" style="top: 0px; left: 100%;">
                                <img src="' . $image8_hover .  '" alt="">
                            </div>
                     </a>
                     ';

        $output .= ' </div>';
        $output .= '';

        $output .= '<style>
        ' . $addon_id . ' .clear:after{content: "";display: block;clear: both;}
        ' . $addon_id . ' .zh-hover-box{width: 1200px;margin: 0 auto;}
        ' . $addon_id . ' .zh-hover-box-c{display: block;text-decoration: none;box-sizing: border-box;width: calc((100% - 90px)/4);padding-bottom: 10px;float: left;margin: 10px auto;margin-right: 30px;border: 1px solid #ccc;position: relative;overflow: hidden;border-radius: 6px;}
        ' . $addon_id . ' .zh-hover-box-c:nth-child(4n){margin-right: 0;}
        ' . $addon_id . ' .zh-hover-box-ci{width: 100%;height: 152px;margin-bottom: 30px;}
        ' . $addon_id . ' .zh-hover-box-ci img{width: 100%;height: 100%;}
        ' . $addon_id . ' .zh-hover-box-cw1{width: 100%;margin-bottom: 10px;font-size:' . $attr_1 . ';line-height:' . $attr_2 . ';color:' . $attr_3 . ';text-align: center;position: relative;padding-bottom: 15px;;}

        ' . $addon_id . ' .zh-hover-box-cwx{width: 20px;height: 2px;background:' . $attr_4 . ';position: absolute;bottom: 0;left: 0;right: 0;margin: auto;;}

        ' . $addon_id . ' .zh-hover-box-cw2{width: 100%;font-size:' . $attr_5 . ';line-height:' . $attr_6 . ';color:' . $attr_7 . ';text-align: center;margin-bottom: 25px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}

        ' . $addon_id . ' .zh-hover-box-b{width: 100%;text-align: center;}
        ' . $addon_id . ' .zh-hover-box-bw1{font-size:' . $attr_8 . ';line-height:' . $attr_9 . ';color:' . $attr_10 . ';padding: 0 16px;border: 1px solid #E3E2E5;box-sizing: border-box;display: inline-block;margin: 0 5px;;}

        ' . $addon_id . ' .content{position: absolute;z-index: 2;background-color: ' . $attr_11 . ';width: 100%;height: 100%;left: -100%;top: -100%;line-height: 198px;text-align: center;color: #fff;font-size: 70px;;}

        ' . $addon_id . ' .content.trans{transition: all .2s;backface-visibility: hidden;align-items: center;display: flex;justify-content: center;}
        ' . $addon_id . ' .content.trans img{width: 200px;height: auto;}
        ' . $addon_id . ' .content img{width: 100%;height: 100%;display: block;}
         @media (max-width:992px) {
            ' . $addon_id . ' .zh-hover-box{
                width: 100% !important;
            }
            ' . $addon_id . ' .zh-hover-box-c{
                width: 100% !important;
            }
         }
</style>';

        return $output;
    }

    public function css()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_path = JPATH_ROOT . '/components/com_jwpagefactory/layouts';
        $css_path = new JLayoutFile('addon.css.button', $layout_path);
        $settings = $this->addon->settings;

        $options = new stdClass;
        $options->button_type = (isset($settings->all_articles_btn_type) && $settings->all_articles_btn_type) ? $settings->all_articles_btn_type : '';
        $options->button_appearance = (isset($settings->all_articles_btn_appearance) && $settings->all_articles_btn_appearance) ? $settings->all_articles_btn_appearance : '';
        $options->button_color = (isset($settings->all_articles_btn_color) && $settings->all_articles_btn_color) ? $settings->all_articles_btn_color : '';
        $options->button_color_hover = (isset($settings->all_articles_btn_color_hover) && $settings->all_articles_btn_color_hover) ? $settings->all_articles_btn_color_hover : '';
        $options->button_background_color = (isset($settings->all_articles_btn_background_color) && $settings->all_articles_btn_background_color) ? $settings->all_articles_btn_background_color : '';
        $options->button_background_color_hover = (isset($settings->all_articles_btn_background_color_hover) && $settings->all_articles_btn_background_color_hover) ? $settings->all_articles_btn_background_color_hover : '';
        $options->button_fontstyle = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_font_style = (isset($settings->all_articles_btn_font_style) && $settings->all_articles_btn_font_style) ? $settings->all_articles_btn_font_style : '';
        $options->button_letterspace = (isset($settings->all_articles_btn_letterspace) && $settings->all_articles_btn_letterspace) ? $settings->all_articles_btn_letterspace : '';

        return $css_path->render(array('addon_id' => $addon_id, 'options' => $options, 'id' => 'btn-' . $this->addon->id));
    }

    public function js()
    {
        $js = '';
        //这个模块完成鼠标方向判断的功能
        $js .= '
        var MouseDirection = function (element, opts) {
        var $element = jQuery(element);
        //enter leave代表鼠标移入移出时的回调
        opts = jQuery.extend({}, {
            enter: jQuery.noop,
        leave: jQuery.noop
        }, opts || {});
        var dirs = [\'top\', \'right\', \'bottom\', \'left\'];
            var calculate = function (element, e) {
                /*以浏览器可视区域的左上角建立坐标系*/
                //表示左上角和右下角及中心点坐标
                var x1, y1, x4, y4, x0, y0;
                //表示左上角和右下角的对角线斜率
                var k;
                //用getBoundingClientRect比较省事，而且它的兼容性还不错
                var rect = element.getBoundingClientRect();
                if (!rect.width) {
                    rect.width = rect.right - rect.left;
                }
                if (!rect.height) {
                    rect.height = rect.bottom - rect.top;
                }
                //求各个点坐标 注意y坐标应该转换为负值，因为浏览器可视区域左上角为(0,0)，整个可视区域属于第四象限
                x1 = rect.left;
                y1 = -rect.top;
                x4 = rect.left + rect.width;
                y4 = -(rect.top + rect.height);
                x0 = rect.left + rect.width / 2;
                y0 = -(rect.top + rect.height / 2);
                //矩形不够大，不考虑
                if (Math.abs(x1 - x4) < 0.0001) return 4;
                //计算对角线斜率
                k = (y1 - y4) / (x1 - x4);
                var range = [k, -k];
                //表示鼠标当前位置的点坐标
                var x, y;
                x = e.clientX;
                y = -e.clientY;
                //表示鼠标当前位置的点与元素中心点连线的斜率
                var kk;
                kk = (y - y0) / (x - x0);
                //如果斜率在range范围内，则鼠标是从左右方向移入移出的
                if (isFinite(kk) && range[0] < kk && kk < range[1]) {
                    //根据x与x0判断左右
                    return x > x0 ? 1 : 3;
                } else {
                    //根据y与y0判断上下
                    return y > y0 ? 0 : 2;
                }
            };
            $element.on(\'mouseenter\', function (e) {
                var r = calculate(this, e);
                opts.enter($element, dirs[r]);
            }).on(\'mouseleave\', function (e) {
                var r = calculate(this, e);
                opts.leave($element, dirs[r]);
            });
        }';

        $js .= ' 
            var DIR_POS = {
                left: {top: \'0\',left: \'-100%\'},
                right: {top: \'0\',left: \'100%\'},
                bottom: {top: \'100%\',left: \'0\'},
                top: {top: \'-100%\',left: \'0\'}
             }';

        $js .= ' 
            jQuery(function(){
                jQuery(\'.zh-hover-box-c\').each(function(){
                    new MouseDirection(this, {
                        enter: function ($element, dir) {
                            //每次进入前先把.trans类移除掉，以免后面调整位置的时候也产生过渡效果
                            var $content = $element.find(\'.content\').removeClass(\'trans\');
                            //调整位置
                            $content.css(DIR_POS[dir]);
                            //reflow
                            $content[0].offsetWidth;
                            //启用过渡
                            $content.addClass(\'trans\');
                            //滑入
                            $content.css({left: \'0\', top: \'0\'});
                        },
                        leave: function ($element, dir) {
                            $element.find(\'.content\').css(DIR_POS[dir]);
                        }
                    });
                });
            })';
        return $js;
    }
}