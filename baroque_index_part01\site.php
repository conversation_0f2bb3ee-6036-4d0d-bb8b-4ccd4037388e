<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonBaroque_index_part01 extends JwpagefactoryAddons
{

    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();

        $output = '<div class="section-2">
			<div class="bg-img">
				<div class="s_tab">';
                foreach ($section_tab_item as $key => $tab) {
                    $link = '';
                    if($tab->detail_page_id){
                        $id = base64_encode($tab->detail_page_id);
                        $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
                    }
					if($tab->title_id){
						$link .= '#'.$tab->title_id;
					}
                    $output .= '<a ';
                    if($link != ''){
                        $output .= 'target="_blank" href="' . $link .'" ';
                    }
                    $output .= 'class="s_item';
                    if($key == 0) {
                        $output .= ' active';
                    }
                    $output .= '" data-image="' . $tab->bgImg . '" data-index="' . $key . '">' . $tab->name . '</a>';
                }
        $output .= '</div>
			</div>';
            $output .= '<div class="content-box">';
                foreach ($section_tab_item as $key => $tab) {
                    if($key == 0) {
                        $output .= '<div class="content" data-index="' . $key . '">' . $tab->content . '</div>';
                    }else {
                        $output .= '<div class="content" style="display:none" data-index="' . $key . '">' . $tab->content . '</div>';
                    }
                }
            $output .= '</div>';
		$output .= '</div>';

        return $output;
    }

    public function scripts()
    {

    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;

        $js = 'jQuery(function($){
		    $("' .$addonId .' .bg-img .s_tab .s_item").mouseover(function() {
                let image = $(this).attr(\'data-image\');
                let index_ = $(this).attr(\'data-index\');
                $(this).addClass(\'active\').siblings().removeClass("active");
                $(\'.bg-img\').css(\'background-image\', \'url(\' + image + \')\');
                $("' .$addonId .' .content").each(function(index, item){
                    let key = $(this).attr("data-index");
                    if(key == index_) {
                        $("' .$addonId .' .content").fadeOut(500);
                        $(this).fadeIn(500);
                    }
                })
            });
		})';

        return $js;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();
        //选项卡高度
        $section_tab_height_md = (isset($settings->section_tab_height) && $settings->section_tab_height) ? $settings->section_tab_height : 288;
        $section_tab_height_sm = (isset($settings->section_tab_height_sm) && $settings->section_tab_height_sm) ? $settings->section_tab_height_sm : 288;
        $section_tab_height_xs = (isset($settings->section_tab_height_xs) && $settings->section_tab_height_xs) ? $settings->section_tab_height_xs : 158;
        //文字内容高度
        $section_content_height_md = (isset($settings->section_content_height) && $settings->section_content_height) ? $settings->section_content_height : 120;
        $section_content_height_sm = (isset($settings->section_content_height_sm) && $settings->section_content_height_sm) ? $settings->section_content_height_sm : 300;
        $section_content_height_xs = (isset($settings->section_content_height_xs) && $settings->section_content_height_xs) ? $settings->section_content_height_xs : 400;

        $defaultImg = '';
        if(count($section_tab_item) > 0){
            $defaultImg = $section_tab_item[0]->bgImg;
        }else {
            $defaultImg = 'https://oss.lcweb01.cn/joomla/20210918/93095423b9d25d7c7f5c57aab1fb8864.png';
        }

        $css =
            $addonId . ' * {
				margin: 0;
			}
			' . $addonId . ' .section-2 .bg-img {
				width: 100%;
				height: ' . $section_tab_height_md . 'px;
				background: url(' . $defaultImg .') no-repeat center;
				background-size: cover;
				position: relative;
				transition: all ease-in-out 300ms;
				overflow: hidden;
				overflow-x: auto;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab {
				position: absolute;
				width: 100%;
				min-width: 500px;
				height: 138px;
				background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7));
				bottom: 0;
				left: 0;
				display: flex;
				align-items: flex-end;
				justify-content: space-around;
				color: #fff;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab .s_item {
				font-size: 16px;
				width: calc(100% / 5);
				text-align: center;
				font-family: Microsoft YaHei;
				font-weight: bold;
				font-style: italic;
				color: #FFFFFF;
				line-height: 18px;
				height: 90px;
				position: relative;
				transform: translateY(25px);
				transition: all ease-in-out 300ms;
				cursor: pointer;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab .s_item:hover, ' . $addonId . ' .section-2 .bg-img .s_tab .s_item.active {
				transform: translateY(0px);
				font-size: 18px;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab .s_item::after {
				content: "";
				width: 2px;
				height: 50px;
				background-color: #FFFFFF;
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				bottom: 0;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab .s_item::before {
				content: "";
				width: 8px;
				height: 8px;
				background-color: #FFFFFF;
				border-radius: 50%;
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				bottom: 50px;
				opacity: 0;
				transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .section-2 .bg-img .s_tab .s_item:hover::before, ' . $addonId . ' .section-2 .bg-img .s_tab .s_item.active::before {
				opacity: 1;;
			}
			' . $addonId . ' .content-box {
			    position: relative;
			    height: ' . $section_content_height_md . 'px;
			}
			' . $addonId . ' .content-box .content {
			    position: absolute;
			    top: 0;
			    left: 0;
			    width: 100%;
			    height: max-content;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    ' . $addonId . ' .section-2 .bg-img {
				    height: ' . $section_tab_height_sm . 'px;
				}
				' . $addonId . ' .content-box {
                    height: ' . $section_content_height_sm . 'px;
                }
			}
			@media (max-width: 767px) {
			    ' . $addonId . ' .section-2 .bg-img {
				    height: ' . $section_tab_height_xs . 'px;
				}
				' . $addonId . ' .content-box {
                    height: ' . $section_content_height_xs . 'px;
                }
			}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		var section_tab_item = data.section_tab_item || array();
		//选项卡高度
		var section_tab_height = data.section_tab_height || 288;
		//文字内容高度
		var section_content_height = data.section_content_height || 288;
		#>
        <style type="text/css">
			{{ addonId }} * {
				margin: 0;
			}
			{{ addonId }} .section-2 .bg-img {
				width: 100%;
				height: {{ section_tab_height.md || 288 }}px;
				background: url(https://oss.lcweb01.cn/joomla/20210918/93095423b9d25d7c7f5c57aab1fb8864.png) no-repeat center;
				background-size: cover;
				position: relative;
				transition: all ease-in-out 300ms;
				overflow: hidden;
				overflow-x: auto;
			}
			{{ addonId }} .section-2 .bg-img .s_tab {
				position: absolute;
				width: 100%;
				min-width: 500px;
				height: 138px;
				background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7));
				bottom: 0;
				left: 0;
				display: flex;
				align-items: flex-end;
				justify-content: space-around;
				color: #fff;
			}
			{{ addonId }} .section-2 .bg-img .s_tab .s_item {
				font-size: 16px;
				width: calc(100% / 5);
				text-align: center;
				font-family: Microsoft YaHei;
				font-weight: bold;
				font-style: italic;
				color: #FFFFFF;
				line-height: 18px;
				height: 90px;
				position: relative;
				transform: translateY(25px);
				transition: all ease-in-out 300ms;
				cursor: pointer;
			}
			{{ addonId }} .section-2 .bg-img .s_tab .s_item:hover, {{ addonId }} .section-2 .bg-img .s_tab .s_item.active {
				transform: translateY(0px);
				font-size: 18px;
			}
			{{ addonId }} .section-2 .bg-img .s_tab .s_item::after {
				content: "";
				width: 2px;
				height: 50px;
				background-color: #FFFFFF;
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				bottom: 0;
			}
			{{ addonId }} .section-2 .bg-img .s_tab .s_item::before {
				content: "";
				width: 8px;
				height: 8px;
				background-color: #FFFFFF;
				border-radius: 50%;
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				bottom: 50px;
				opacity: 0;
				transition: all ease-in-out 300ms;
			}
			{{ addonId }} .section-2 .bg-img .s_tab .s_item:hover::before, {{ addonId }} .section-2 .bg-img .s_tab .s_item.active::before {
				opacity: 1;;
			}
			{{ addonId }} .content-box {
			    position: relative;
			    height: {{section_content_height.md || 120 }}px;
			}
			{{ addonId }} .content-box .content {
			    position: absolute;
			    top: 0;
			    left: 0;
			    width: 100%;
			    height: max-content;
			}
			@media (min-width: 768px) and (max-width: 991px) {
			    {{ addonId }} .section-2 .bg-img {
				    height: {{ section_tab_height.sm || 288 }}px;
				}
				{{ addonId }} .content-box {
                    height: {{section_content_height.sm || 300 }}px;
                }
			}
			@media (max-width: 767px) {
			    {{ addonId }} .section-2 .bg-img {
				    height: {{ section_tab_height.xs || 158 }}px;
				}
				{{ addonId }} .content-box {
                    height: {{section_content_height.xs || 300 }}px;
                }
			}
		</style>
        <div class="section-2">
			<div class="bg-img">
				<div class="s_tab">
				    <# _.each(section_tab_item, function(accordion_item, key){
				        var active = "";
                        if(key == 0){
                            active = "active";
                        } #>
					    <div class="s_item {{active}}" data-image="{{accordion_item.bgImg}}" data-index="{{key}}">{{accordion_item.name}}</div>
                    <# }); #>
				</div>
			</div>
			 <div class="content-box">
			 <# _.each(section_tab_item, function(accordion_item, key){
                if(key == 0){ #>
                    <div class="content" data-index="{{key}}">{{{accordion_item.content}}}</div>
                <# } else {#>
			        <div class="content" data-index="{{key}}" style="display: none">{{{accordion_item.content}}}</div>
			    <# } #>
			 <# }); #>
            </div>
		</div>
		';

        return $output;
    }
}
