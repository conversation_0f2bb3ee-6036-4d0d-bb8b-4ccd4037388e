<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonBanner_info extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id    = '#jwpf-addon-' . $this->addon->id;
        $settings    = $this->addon->settings;
        $banner_info_type = (isset($settings->banner_info_type) && $settings->banner_info_type) ? $settings->banner_info_type : 'type1';

        $output = '';
        if($banner_info_type=='type1')
        {
            $title_type1 = (isset($settings->title_type1) && $settings->title_type1) ? $settings->title_type1 : '百度百青藤';
            $content_type1 = (isset($settings->content_type1) && $settings->content_type1) ? $settings->content_type1 : '百度系+百青藤资源，碎片化场景全覆盖为您拓展更多商机。';
            $big_img_pc_type1 = (isset($settings->big_img_pc_type1) && $settings->big_img_pc_type1) ? $settings->big_img_pc_type1 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/86084d2a99208a208e50c4dd861501a4.jpeg';
            $big_img_sj_type1 = (isset($settings->big_img_sj_type1) && $settings->big_img_sj_type1) ? $settings->big_img_sj_type1 : 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220517/1e87fedd8cc1065f5c6f614c76ce8a7e.jpeg';
            $output .= '
                <style>
                    '.$addon_id.' * {
                        padding: 0;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    '.$addon_id.' .i100 {
                        overflow: hidden;
                    }
                    @media only screen and (min-width: 1440px){
                        '.$addon_id.' .t-ind1-a {
                            width: 100%;
                            position: relative;
                        }
                        '.$addon_id.' .t-ind1-a1 {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a1 img {
                            width: 100%;
                            animation: big 10s linear infinite;
                        }
                        '.$addon_id.' .t-ind1-a1 img:nth-child(2) {
                            display: none;
                        }
                        '.$addon_id.' .t-ind1-a2 {
                            position: absolute;
                            top: calc(100%*253/685);
                            left: calc(50% - 1440px/2);
                        }
                        '.$addon_id.' .t-ind1-a2a {
                            font-size: 75px;
                            line-height: 75px;
                            color: #fff;
                            font-weight: bold;
                        }
                        '.$addon_id.' .t-ind1-a2b {
                            font-size: 24px;
                            line-height: 40px;
                            color: #fff;
                            margin-top: 32px;
                            max-width: 420px;
                            font-weight: lighter;
                        }
                        '.$addon_id.' .t-ind1-a2c {
                            width: 70px;
                            height: 8px;
                            background: #135efc;
                            margin-top: 40px;
                            transition: 0.5s;
                        }
                        '.$addon_id.' .t-ind1-a:hover .t-ind1-a2c {
                            width: 100px;
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 1439px) and (min-width: 1024px){
                        '.$addon_id.' .t-ind1-a {
                            width: 100%;
                            position: relative;
                        }
                        '.$addon_id.' .t-ind1-a1 {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a1 img {
                            width: 100%;
                            animation: big 10s;
                        }
                        '.$addon_id.' .t-ind1-a1 img:nth-child(2) {
                            display: none;
                        }
                        '.$addon_id.' .t-ind1-a2 {
                            position: absolute;
                            top: calc(100%*253/685);
                            left: calc(50% - 960px/2);
                        }
                        '.$addon_id.' .t-ind1-a2a {
                            font-size: 60px;
                            line-height: 60px;
                            color: #fff;
                            font-weight: bold;
                        }
                        '.$addon_id.' .t-ind1-a2b {
                            font-size: 16px;
                            line-height: 22px;
                            color: #fff;
                            margin-top: 32px;
                            max-width: calc(960px*420/1440);
                            font-weight: lighter;
                        }
                        '.$addon_id.' .t-ind1-a2c {
                            width: 70px;
                            height: 6px;
                            background: #135efc;
                            margin-top: 40px;
                            transition: 0.5s;
                        }
                        '.$addon_id.' .t-ind1-a:hover .t-ind1-a2c {
                            width: 100px;
                            transition: 0.5s;
                        }
                    }
                    @media only screen and (max-width: 1023px){
                        '.$addon_id.' .t-ind1-a {
                            width: 100%;
                            position: relative;
                        }
                        '.$addon_id.' .t-ind1-a1 {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a1 img:nth-child(1) {
                            display: none;
                        }
                        '.$addon_id.' .i100>img {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a2 {
                            position: absolute;
                            top: 140px;
                            left: 0;
                        }
                        '.$addon_id.' .t-ind1-a2a {
                            font-size: 75px;
                            line-height: 75px;
                            color: #fff;
                            font-weight: bold;
                            text-align: center;
                        }
                        '.$addon_id.' .t-ind1-a2b {
                            font-size: 30px;
                            line-height: 48px;
                            color: #fff;
                            margin-top: 40px;
                            font-weight: lighter;
                            text-align: center;
                            padding: 0 108px 0 108px;
                        }
                        '.$addon_id.' .t-ind1-a2c {
                            width: 70px;
                            height: 8px;
                            background: #135efc;
                            margin: 0 auto;
                            margin-top: 40px;
                        }
                    }
                    @media only screen and (max-width: 650px){
                        '.$addon_id.' .t-ind1-a {
                            width: 100%;
                            position: relative;
                        }
                        '.$addon_id.' .t-ind1-a1 {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a1 img:nth-child(1) {
                            display: none;
                        }
                        '.$addon_id.' .i100>img {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a2 {
                            position: absolute;
                            top: 105px;
                            left: 0;
                        }
                        '.$addon_id.' .t-ind1-a2a {
                            font-size: 56.25px;
                            line-height: 56.25px;
                            color: #fff;
                            font-weight: bold;
                            text-align: center;
                        }
                        '.$addon_id.' .t-ind1-a2b {
                            font-size: 22.5px;
                            line-height: 36px;
                            color: #fff;
                            margin-top: 30px;
                            font-weight: lighter;
                            text-align: center;
                            padding: 0 81px 0 81px;
                        }
                        '.$addon_id.' .t-ind1-a2c {
                            width: 52.5;
                            height: 6px;
                            background: #135efc;
                            margin: 0 auto;
                            margin-top: 30px;
                        }
                    }
                    @media only screen and (max-width: 499px){
                        '.$addon_id.' .t-ind1-a {
                            width: 100%;
                            position: relative;
                        }
                        '.$addon_id.' .t-ind1-a1 {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a1 img:nth-child(1) {
                            display: none;
                        }
                        '.$addon_id.' .i100>img {
                            width: 100%;
                        }
                        '.$addon_id.' .t-ind1-a2 {
                            position: absolute;
                            top: 70px;
                            left: 0;
                        }
                        '.$addon_id.' .t-ind1-a2a {
                            font-size: 37.5px;
                            line-height: 37.5px;
                            color: #fff;
                            font-weight: bold;
                            text-align: center;
                        }
                        '.$addon_id.' .t-ind1-a2b {
                            font-size: 15px;
                            line-height: 24px;
                            color: #fff;
                            margin-top: 20px;
                            font-weight: lighter;
                            text-align: center;
                            padding: 0 54px 0 54px;
                        }
                        '.$addon_id.' .t-ind1-a2c {
                            width: 35px;
                            height: 4px;
                            background: #135efc;
                            margin: 0 auto;
                            margin-top: 20px;
                        }
                    }
                    @keyframes big {
                        0%{
                            transform: scale(1)
                        } 
                        50%{
                            transform: scale(1.1)
                        } 
                        100%{
                            transform: scale(1)
                        }
                    }
                </style>
            ';
            $output .='
                <div class="t-ind1-a">
                    <div class="t-ind1-a1 i100">
                        <img src="'.$big_img_pc_type1.'" oncontextmenu="return false;">
                        <img src="'.$big_img_sj_type1.'" oncontextmenu="return false;">
                    </div>
                    <div class="t-ind1-a2 wow fadeInUp animated animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s;">
                        <div class="t-ind1-a2a">'.$title_type1.'</div>
                        <div class="t-ind1-a2b">'.$content_type1.'</div>
                        <div class="t-ind1-a2c"></div>
                    </div>
                </div>
            ';
        }
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
//        $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.min.js');
        $scripts = '';
        return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
