<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonA_Btn_Down extends JwpagefactoryAddons
{

    public function render()
    {

        $settings = $this->addon->settings;
        $id       = $this->addon->id;
        if (isset($settings->a_options_padding) && $settings->a_options_padding) {
            if (trim($settings->a_options_padding) != "") {
                $a_padding_md  = '';
                $a_paddings_md = explode(' ', $settings->a_options_padding);
                foreach ($a_paddings_md as $a_padding_md) {
                    $a_padding_md = trim($a_padding_md);
                    if (empty($a_padding_md)) {
                        $a_padding_md .= ' 0';
                    } else {
                        $a_padding_md .= ' ' . $a_padding_md;
                    }
                }
                $a_padding_css .= "padding: " . $a_padding_md . ";\n";
            }
        }

        if (isset($settings->a_options_margin) && $settings->a_options_margin) {
            if (trim($settings->a_options_margin) != "") {
                $a_margin_md  = '';
                $a_margins_md = explode(' ', $settings->a_options_margin);
                foreach ($a_margins_md as $a_margin_md) {
                    $a_margin_md = trim($a_margin_md);
                    if (empty($a_margin_md)) {
                        $a_margin_md .= ' 0';
                    } else {
                        $a_margin_md .= ' ' . $a_margin_md;
                    }
                }
                $a_margin_css .= "margin: " . $a_margin_md . ";\n";
            }
        }
        $output .= '<style type="text/css">';
        if ($settings->is_btn == 1) {
            $output .= '#jwpf-addon-a' . $id . '{';
            $output .= 'width:' . $settings->btn_options_width . 'px;';
            $output .= 'height:' . $settings->btn_options_height . 'px;';
            $output .= 'line-height:' . $settings->btn_options_line_height . 'px;';
            $output .= 'text-align: center;';
            $output .= 'padding: 0 10px;';
            $output .= 'border-radius: ' . $settings->btn_options_radius . 'px;';
            $output .= 'color: ' . $settings->btn_options_color . ';';
            $output .= 'background: ' . $settings->btn_options_bg_color . ';';
            $output .= 'display: inline-block;';
            $output .= 'font-size: ' . $settings->btn_options_fontsize . 'px; ';
            $output .= '}';
            $output .= '#jwpf-addon-a' . $id . ':link {color:' . $settings->btn_options_color . ';background: ' . $settings->btn_options_bg_color . ';}';
            $output .= '#jwpf-addon-a' . $id . ':visited {color:' . $settings->btn_options_color . '; background: ' . $settings->btn_options_bg_color . ';}';
            $output .= '#jwpf-addon-a' . $id . ':hover {color:' . $settings->btn_options_hover_color . '; background: ' . $settings->btn_options_hover_bg_color . ';} ';
        } else {
            $output .= '#jwpf-addon-a' . $id . '{';

            $output .= 'font-size: ' . $settings->a_options_fontsize . 'px  !important; ';
            $output .= $a_padding_css;
            $output .= '}';
            $output .= '#jwpf-addon-a' . $id . ':link {color:' . $settings->a_options_color . '  !important;';
            if (!$settings->is_a_decoration) {
                $output .= ' text-decoration: none  !important;';

            } else {
                $output .= ' text-decoration: underline !important;';
            }

            $output .= '}';

            $output .= '#jwpf-addon-a' . $id . ':visited {color:' . $settings->a_options_color . '  !important;';
            if (!$settings->is_a_decoration) {
                $output .= ' text-decoration: none  !important;';

            } else {
                $output .= ' text-decoration: underline !important;';
            }

            $output .= '}';
            $output .= '#jwpf-addon-a' . $id . ':hover {color:' . $settings->a_options_hover_color . '  !important;}';
        }
        $output .= '</style>';
        if ($settings->is_btn == 1) {
            $output .= '<a id="jwpf-addon-a' . $id . '" class="btn" href="' . $settings->a_url . '" target="' . $settings->btn_options_target . '" >' . $settings->a_label . '</a>';
        } else {
            $output .= '<a id="jwpf-addon-a' . $id . '" class="one" href="' . $settings->a_url . '" target="' . $settings->a_options_target . '" >' . $settings->a_label . '</a>';
        }
        return $output;
    }

    public static function getTemplate()
    {
        $output = '
<style>
<#
 let a_options_padding = _.isObject(data.a_options_padding)&&data.a_options_padding?window.getMarginPadding(data.a_options_padding.md, "padding"):window.getMarginPadding(data.a_options_padding, "padding");
 let a_options_padding_sm = _.isObject(data.a_options_padding)&&data.a_options_padding?window.getMarginPadding(data.a_options_padding.sm, "padding"):window.getMarginPadding(data.a_options_padding, "padding");
 let a_options_padding_xs = _.isObject(data.a_options_padding)&&data.a_options_padding?window.getMarginPadding(data.a_options_padding.xs, "padding"):window.getMarginPadding(data.a_options_padding, "padding");

 let a_options_margin = _.isObject(data.a_options_margin)&&data.a_options_margin?window.getMarginPadding(data.a_options_margin.md, "padding"):window.getMarginPadding(data.a_options_margin, "padding");
 let a_options_margin_sm = _.isObject(data.a_options_margin)&&data.a_options_margin?window.getMarginPadding(data.a_options_margin.sm, "padding"):window.getMarginPadding(data.a_options_margin, "padding");
 let a_options_margin_xs = _.isObject(data.a_options_margin)&&data.a_options_margin?window.getMarginPadding(data.a_options_margin.xs, "padding"):window.getMarginPadding(data.a_options_margin, "padding");


 #>

<# if(data.is_btn===1){ #>
        #jwpf-addon-a{{ data.id }}{
            width: {{data.btn_options_width}}px;
            height: {{data.btn_options_height}}px;
            text-align: center;
            line-height: {{data.btn_options_line_height}}px;
            padding: 0 10px;
            border-radius: {{data.btn_options_radius}}px;
            color:{{data.btn_options_color}};
            background: {{data.btn_options_bg_color}};
            display: inline-block;
            font-size: {{data.btn_options_fontsize}}px; 
        }
#jwpf-addon-a{{ data.id }}:link {color:{{data.btn_options_color}} !important; background: {{data.btn_options_bg_color}}  !important;}
#jwpf-addon-a{{ data.id }}:visited {color:{{data.btn_options_color}}  !important; background: {{data.btn_options_bg_color}}  !important;}
#jwpf-addon-a{{ data.id }}:hover {color:{{data.btn_options_hover_color}} !important; background: {{data.btn_options_hover_bg_color}}  !important;}
        <# }else{ #>


#jwpf-addon-a{{ data.id }} {



           font-size: {{data.a_options_fontsize}}px !important;
            {{a_options_padding}}

        }
#jwpf-addon-a{{ data.id }}:link {color:{{data.a_options_color}} !important;

<# if(!data.is_a_decoration){ #>
        text-decoration: none !important;
     <# }else{ #>
        text-decoration: underline !important;
          <# } #>
    }
#jwpf-addon-a{{ data.id }}:visited {color:{{data.a_options_color}} !important;
<# if(!data.is_a_decoration){ #>
        text-decoration: none !important;
     <# }else{ #>
        text-decoration: underline !important;
          <# } #>

    }
#jwpf-addon-a{{ data.id }}:hover {color:{{data.a_options_hover_color}} !important;}
        <# } #>
        </style>
        <# if(data.is_btn===1){ #>
          <a id="jwpf-addon-a{{data.id}}" class="btn" href="{{data.a_url}}" target="{{data.a_options_target}}" >{{data.a_label}}</a>
        <# }else{ #>
           <a id="jwpf-addon-a{{data.id}}" class="one" href="{{data.a_url}}" target="{{data.a_options_target}}" >{{data.a_label}}</a>
         <# } #>
        ';

        return $output;
    }
}
