// JavaScript Document     /*ind5*/
    if ($('.ind5-a3').length) {
        $('.ind5-a3').eq(0).addClass('on1');
        $('.ind5-b1').eq(0).addClass('on1');
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            $('.ind5-b1').eq(i).find('.swiper-container').addClass('ind5' + (i + 1) + '1');
            $('.ind5-b1').eq(i).find('.swiper-button-prev').addClass('ind5' + (i + 1) + '2');
            $('.ind5-b1').eq(i).find('.swiper-button-next').addClass('ind5' + (i + 1) + '3');
            /*swiper1('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3',5000,500);
            swiper4('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3');*/
            if ($('.ind5-b1').eq(i).find('.swiper-slide').length <= 1) {
                $('.ind5-b1').eq(i).find('.swiper-button-prev').hide();
                $('.ind5-b1').eq(i).find('.swiper-button-next').hide();
            }
        }
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            for (var j = 0; j < $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').length; j++) {
                var el1 = $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').html();
                $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').empty();
                for (var k = 0; k < 12; k++) {
                    $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').append('<div class="ind5-b7"><div class="ind5-b8">' + el1 + '</div></div>');
                }
            }
        }
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(0).addClass('on1');
        }
        var ind5tap1 = true;
        $('.ind5-b1 .swiper-button-next').click(function() {
            if (ind5tap1) {
                ind5tap1 = false;
                var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                var el1 = $(this).parents('.ind5-b1');
                console.log(idx1)
                if (idx1 == leng1 - 1) {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(0).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                } else {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 + 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                }
            }
        });
        $('.ind5-b1 .swiper-button-prev').click(function() {
            if (ind5tap1) {
                ind5tap1 = false;
                var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                var el1 = $(this).parents('.ind5-b1');
                console.log(idx1)
                if (idx1 == 0) {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(leng1 - 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                } else {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 - 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                }
            }
        });
    }
    $('.ind5-a3').mouseenter(function() {
        $('.ind5-a3').removeClass('on1');
        $(this).addClass('on1');
        $('.ind5-b1').removeClass('on1');
        $('.ind5-b1').eq($(this).index()).addClass('on1');
    });
    $(".p-index-a4-list").click(function() {
        $(".p-index-a4-list").removeClass("p-index-a4-list-ac");
        $(this).addClass("p-index-a4-list-ac");
        $(".p-index-a4-line").removeClass("on1").eq($(this).index()).addClass("on1");
        $(".p-index-a4-line").removeClass("on1").eq($(this).index()).addClass("on1");
         var c= $(".in-page11 .swiper-pagination-current").html();
          console.log("current:"+c);
          if(c !==1){
           $(".in-page11 .swiper-pagination-current").html(1);
           $(".p-index-a4 .p-index-a4-box .on1 .swiper-container3 .swiper-wrapper").find(".swiper-slide").removeClass("swiper-slide-prev");
           $(".p-index-a4 .p-index-a4-box .on1 .swiper-container3 .swiper-wrapper").find(".swiper-slide").removeClass("swiper-slide-active").eq(0).addClass("swiper-slide-active");
           $(".p-index-a4 .p-index-a4-box .on1 .swiper-container3 .swiper-wrapper").find(".swiper-slide").removeClass("swiper-slide-next").eq(1).addClass("swiper-slide-next");
           
          }
          var a= $(".p-index-a4 .p-index-a4-box .on1 .swiper-container3 .swiper-wrapper").find(".swiper-slide").length;
          $(".in-page11 .swiper-pagination-total").html(a);
          console.log("total:"+a);
         var d= $(".in-page11 .swiper-pagination-current").html();
          console.log("d_current:"+d);
        
    })
