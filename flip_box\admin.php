<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

JwAddonsConfig::addonConfig(
	array(
		'type' => 'content',
		'addon_name' => 'jw_flip_box',
		'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIP_BOX'),
		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIP_BOX_DESC'),
		'category' => '图片',
		'attr' => array(
			'general' => array(
				'admin_label' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
					'std' => ''
				),
				'flip_bhave' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_BHAVE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_BHAVE_DESC'),
					'values' => array(
						'hover' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_BHAVE_HOVER'),
						'click' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_BHAVE_CLICK'),
					),
					'std' => 'hover',
				),
				'front_text' => array(
					'type' => 'textarea',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_TEXT_DESC'),
					'std' => '<i class="fa fa-signal" style="font-size:25px;background-color:#fff;display:inline-block;color:#007BF8;width:60px;height:60px;line-height:60px;border-radius: 50%;" aria-hidden="true"></i><h2>翻转前文本，这里可以不用代码写的</h2>'
				),
				'flip_text' => array(
					'type' => 'textarea',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_TEXT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP_TEXT_DESC'),
					'std' => '<h3>Product Design</h3><p>翻转后文本，这里可以不用代码</p><p>翻转后文本，这里可以不用代码</p>'
				),
				'class' => array(
					'type' => 'text',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_CLASS_DESC'),
					'std' => ''
				),
				'flip_style' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLE_DESC'),
					'values' => array(
						'rotate_style' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_ROTATE_STYLE'),
						'slide_style' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_SLIDE_STYLE'),
						'fade_style' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FADE_STYLE'),
						'threeD_style' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_3D_STYLE'),
					),
					'std' => 'flat_style'
				),
				'rotate' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_ROTATE'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_ROTATE_DESC'),
					'values' => array(
						'flip_top' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_FROM_TOP'),
						'flip_bottom' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BOTTOM'),
						'flip_left' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_FROM_LEFT'),
						'flip_right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'flip_right',
					'depends' => array(array('flip_style', '!=', 'fade_style')),
				),
				'height' => array(
					'type' => 'slider',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_HEIGHT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_HEIGHT_DESC'),
					'std' => '',
					'responsive' => true,
					'max' => 1000,
				),
				'text_align' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TEXT_ALIGN'),
					'values' => array(
						'left' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LEFT'),
						'center' => JText::_('COM_JWPAGEFACTORY_GLOBAL_CENTER'),
						'right' => JText::_('COM_JWPAGEFACTORY_GLOBAL_RIGHT'),
					),
					'std' => 'center',
				),
				'border_styles' => array(
					'type' => 'select',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES_DESC'),
					'values' => array(
						'solid' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES_BORDER_SOLID'),
						'dashed' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES_BORDER_DASHED'),
						'dotted' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES_BORDER_DOTTED'),
						'border_radius' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_STYLES_BORDER_RADIUS'),
					),
					'std' => '',
				),
				'border_color' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BORDER_COLOR_DESC'),
					'std' => '#000',
				),
				//Admin Only
				'separator_front' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK'),
				),
				'front_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BG_COLOR_FRONT'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BG_COLOR_FRONT_DESC'),
					'std' => '',
				),
				'front_bgimg' => array(
					'type' => 'media',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FRONT_BG_IMG'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FRONT_BG_IMG_DESC'),
					'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/f973656579a41ae4b16a6e3f5df09e85.jpeg',
				),
				'front_textcolor' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FRONT_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FRONT_TEXT_COLOR_DESC'),
					'std' => '#fff',
				),
				'separator_back' => array(
					'type' => 'separator',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_FLIP'),
				),
				'back_bgcolor' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_BG_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_BG_COLOR_DESC'),
					'std' => '#2E3B3E',
				),
				'back_bgimg' => array(
					'type' => 'media',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_BG_IMG'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_BG_IMG_DESC'),
					'std' => '',
				),
				'back_textcolor' => array(
					'type' => 'color',
					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_TEXT_COLOR'),
					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_FLIPBOX_BACK_TEXT_COLOR_DESC'),
					'std' => '#fff',
				),
			),
		)
	)
);
