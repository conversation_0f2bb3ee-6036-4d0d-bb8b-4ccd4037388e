<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>or<PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonFull_page extends JwpagefactoryAddons
{
    public function render() {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;

        /*
         * 第一页变量
         * */
        $page01_slider_video = (isset($settings->page01_slider_video) && $settings->page01_slider_video) ? $settings->page01_slider_video : 'https://oss.lcweb01.cn/joomla/20211012/3df6aa5afa92f8c2320d38379007aa5b.mp4';
        $page01_slider_title = (isset($settings->page01_slider_title) && $settings->page01_slider_title) ? $settings->page01_slider_title : '瓦轴集团-中国轴承从这里开始...';
        $page01_slider_desc = (isset($settings->page01_slider_desc) && $settings->page01_slider_desc) ? $settings->page01_slider_desc : 'ZWZ - China\'s bearing startsfrom here...';
        $page01_index_page_id = $settings->page01_index_page_id;
        $page01_index_link = '';
        if($page01_index_page_id){
            $id = base64_encode($page01_index_page_id);
            $page01_index_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        $page01_slider_content = (isset($settings->page01_slider_content) && $settings->page01_slider_content) ? $settings->page01_slider_content : '瓦房店轴承产品：瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 |TWB轴承
								 友情链接：  瓦轴    ZWZ轴承     封闭母线槽     哈尔滨轴承   真空灌胶机  eja变送器    智能锁招商   HRB轴承     电子商务站   RTO     whl轴承   恒温恒湿试验箱价格 ';
        //文章列表配置项
        $page01_include_subcat = (isset($settings->page01_include_subcat)) ? $settings->page01_include_subcat : 1;
        $page01_post_type = (isset($settings->page01_post_type) && $settings->page01_post_type) ? $settings->page01_post_type : '';
        $page01_ordering = (isset($settings->page01_ordering) && $settings->page01_ordering) ? $settings->page01_ordering : 'latest';
        $page01_limit = (isset($settings->page01_limit) && $settings->page01_limit) ? $settings->page01_limit : 3;
        $page01_tagids = (isset($settings->page01_tagids) && $settings->page01_tagids) ? $settings->page01_tagids : array();
        $page01_catid = (isset($settings->page01_catid) && $settings->page01_catid) ? $settings->page01_catid : 0;
        $page01_detail_page_id = (isset($settings->page01_detail_page_id)) ? $settings->page01_detail_page_id : 0;

        /*
         * 第二页变量
         * */
        $page02_slider_img01 = (isset($settings->page02_slider_img01) && $settings->page02_slider_img01) ? $settings->page02_slider_img01 : 'https://oss.lcweb01.cn/joomla/20211015/b27c3e46e08f0dcec316a180bbf64d34.png';
        $page02_slider_img02 = (isset($settings->page02_slider_img02) && $settings->page02_slider_img02) ? $settings->page02_slider_img02 : 'https://oss.lcweb01.cn/joomla/20211015/4d4fe59f37db8f7038a103c17ec4eff2.png';
        $page02_slider_img03 = (isset($settings->page02_slider_img03) && $settings->page02_slider_img03) ? $settings->page02_slider_img03 : 'https://oss.lcweb01.cn/joomla/20211015/d90f5d3417da39918ed842da4350d317.jpg';
        $page02_slider_img04 = (isset($settings->page02_slider_img04) && $settings->page02_slider_img04) ? $settings->page02_slider_img04 : 'https://oss.lcweb01.cn/joomla/20211015/4ce6f49ce79cc6e739c159913539bd79.png';
        $page02_image_item = (isset($settings->page02_image_item) && $settings->page02_image_item) ? $settings->page02_image_item : array(
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/64274e83bbf3e9c2f3bcddf1c32b857e.png',
            ),
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/0380b9306ab6c11c78c29d95bf466cc9.png',
            ),
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211015/f99978701cbac2bcc2dea7b0de0cf681.png',
            ),
        );

        /*
         * 第三页变量
         * */
        $page03_slider_title = (isset($settings->page03_slider_title) && $settings->page03_slider_title) ? $settings->page03_slider_title : '走进江轴';
        $page03_slider_desc = (isset($settings->page03_slider_desc) && $settings->page03_slider_desc) ? $settings->page03_slider_desc : 'WALK INTO JIANGZHOU';
        $page03_index_page_id = $settings->page03_index_page_id;
        $page03_index_link = '';
        if($page03_index_page_id){
            $id = base64_encode($page03_index_page_id);
            $page03_index_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        $page03_slider_content = (isset($settings->page03_slider_content) && $settings->page03_slider_content) ? $settings->page03_slider_content : '<p> 江苏江轴轴承，着重于全面承接瓦房店轴承集团江苏销售平台的管理工作，承担着代表瓦轴对江苏区域轴承市场的开发和管理。在江瓦轴承的努力下，各行业用户的需求能更快捷更高效地与瓦轴对接，提升了响应效率，让解决方案更具针对性，为客户带来更多的效益！</p>
										<p>公司常备库存7000万元以上，提供12000多个不同规格的轴承产品，在华东乃至全国具有显著的市场地位。产品广泛应用于航天、航空、铁路、风电、核电、矿山、汽车、船舶、海洋工程等涉及国计民生的重点行业和领域。</p>';
        $page03_image_item = (isset($settings->page03_image_item) && $settings->page03_image_item) ? $settings->page03_image_item : array(
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/7e6a0e1bd250f285a43989c8d2b6dd15.png',
                'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/8e7ae1c75789d7b5fea59fb3a2d082ab.png'
            ),
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/7d19437a959c6fb7a29be0516e5c5c8d.jpg',
                'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/addbb12cee6925111985a9eac848d71b.jpg'
            ),
            (object)array(
                'slider_img' => 'https://oss.lcweb01.cn/joomla/20211014/ffda657c37d2c27f9ae5bb170a3fea5e.jpg',
                'slider_img_s' => 'https://oss.lcweb01.cn/joomla/20211014/7e596e363f6462e19ef3a6afcd402624.jpg'
            ),
        );
        $page03_bot_title = (isset($settings->page03_bot_title) && $settings->page03_bot_title) ? $settings->page03_bot_title : '江轴轴承中心';
        $page03_bot_desc = (isset($settings->page03_bot_desc) && $settings->page03_bot_desc) ? $settings->page03_bot_desc : 'PRODUCTS';
        $page03_bot_content = (isset($settings->page03_bot_content) && $settings->page03_bot_content) ? $settings->page03_bot_content : '瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承 | 瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承  | 瓦房店轴承风电系列 ';
        /*
         * 第四页变量
         * */
        $page04_slider_title = (isset($settings->page04_slider_title) && $settings->page04_slider_title) ? $settings->page04_slider_title : '轴承传动行业的优质方案提供者';
        $page04_slider_desc = (isset($settings->page04_slider_desc) && $settings->page04_slider_desc) ? $settings->page04_slider_desc : '库存充足、品种齐全、专业销售世界优质品质轴承';
        $page04_slider_content = (isset($settings->page04_slider_content) && $settings->page04_slider_content) ? $settings->page04_slider_content : '<p>常备库存<span>7000万元</span>以上，为数十个行业客户提供<span>12000多种</span>不同规格的轴承</p><p style="font-size: 14px;margin-top: 10px;">The company\'s standing stock is more than 70 million yuan, providing more than 12,000 products of different specifications for dozens of industry customers.</p>';
        $page04_index_page_id = $settings->page04_index_page_id;
        $page04_index_link = '';
        if($page04_index_page_id){
            $id = base64_encode($page04_index_page_id);
            $page04_index_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        /*
         * 第五页变量
         * */
        $page05_ani_title01 = (isset($settings->page05_ani_title01) && $settings->page05_ani_title01) ? $settings->page05_ani_title01 : '货源稳定';
        $page05_ani_title02 = (isset($settings->page05_ani_title02) && $settings->page05_ani_title02) ? $settings->page05_ani_title02 : '库存充足';
        $page05_ani_title03 = (isset($settings->page05_ani_title03) && $settings->page05_ani_title03) ? $settings->page05_ani_title03 : '物流快捷';
        $page05_title_item = (isset($settings->page05_title_item) && $settings->page05_title_item) ? $settings->page05_title_item : array(
            (object)array(
                'title' => '瓦轴实力授权经销商',
            ),
            (object)array(
                'title' => '常备7000万元以上库存',
            ),
            (object)array(
                'title' => '20年服务品质 实力保证',
            ),
            (object)array(
                'title' => '提供一站式轴承技术支持',
            ),
        );
        $page05_desc_img = (isset($settings->page05_desc_img) && $settings->page05_desc_img) ? $settings->page05_desc_img : 'https://oss.lcweb01.cn/joomla/20211014/37d95cf9379f31e27d5893f97e913850.png';
        $page05_goods_img = (isset($settings->page05_goods_img) && $settings->page05_goods_img) ? $settings->page05_goods_img : 'https://oss.lcweb01.cn/joomla/20211014/0d47fca7f1e2f474bcb1c0c96c15842e.png';

        /*
         * 第六页变量
         * */
        // 地图链接
        $page06_map_page_id = $settings->page06_map_page_id;
        $page06_map_link = '';
        if($page06_map_page_id){
            $id = base64_encode($page06_map_page_id);
            $page06_map_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        // 留言链接
        $page06_message_page_id = $settings->page06_message_page_id;
        $page06_message_link = '';
        if($page06_message_page_id){
            $id = base64_encode($page06_message_page_id);
            $page06_message_link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
        }
        $page06_ewm_img = (isset($settings->page06_ewm_img) && $settings->page06_ewm_img) ? $settings->page06_ewm_img : 'https://oss.lcweb01.cn/joomla/20211015/c87bd2547328cd4aa812aa4e6c1940fb.jpg';
        $page06_concat = (isset($settings->page06_concat) && $settings->page06_concat) ? $settings->page06_concat : '<p>地址：江苏省无锡市北塘区康桥丽景家园18-46号</p><p>电话：15061858253<br />联系人：董经理</p><p>电话：19895733877<br />联系人：董经理</p>';
        $page06_copyright = (isset($settings->page06_copyright) && $settings->page06_copyright) ? $settings->page06_copyright : '江苏江瓦轴承有限公司无锡分公司';
        $page06_record = (isset($settings->page06_record) && $settings->page06_record) ? $settings->page06_record : '';
        // 备案号链接
        $page06_record_link = (isset($settings->page06_record_link) && $settings->page06_record_link) ? $settings->page06_record_link : '';
        $page06_bot_concat = (isset($settings->page06_bot_concat) && $settings->page06_bot_concat) ? $settings->page06_bot_concat : '<p>企业愿景：瓦轴第一总代理   永居销售量榜首</p><p>企业价值观：服务每一位客户，成就每一位员工</p><p>企业精神：诚信  创新  协作  共赢</p>';

        $page06_include_subcat = (isset($settings->page06_include_subcat)) ? $settings->page06_include_subcat : 1;
        $page06_post_type = (isset($settings->page06_post_type) && $settings->page06_post_type) ? $settings->page06_post_type : '';
        $page06_ordering = (isset($settings->page06_ordering) && $settings->page06_ordering) ? $settings->page06_ordering : 'latest';
        $page06_limit = (isset($settings->page06_limit) && $settings->page06_limit) ? $settings->page06_limit : 3;
        $page06_tagids = (isset($settings->page06_tagids) && $settings->page06_tagids) ? $settings->page06_tagids : array();
        $page06_catid = (isset($settings->page06_catid) && $settings->page06_catid) ? $settings->page06_catid : 0;
        $page06_detail_page_id = (isset($settings->page06_detail_page_id)) ? $settings->page06_detail_page_id : 0;

        //文章列表数据读取
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        $page01_items = JwpagefactoryHelperArticles::getArticlesList($page01_limit, $page01_ordering, $page01_catid, $page01_include_subcat, $page01_post_type, $page01_tagids, $page01_detail_page_id, 1, $company_id, $layout_id, $site_id);
        $page06_items = JwpagefactoryHelperArticles::getArticlesList($page06_limit, $page06_ordering, $page06_catid, $page06_include_subcat, $page06_post_type, $page06_tagids, $page06_detail_page_id, 1, $company_id, $layout_id, $site_id);
        // print_r($page01_items);die;

        // 第一页是否展示
        $page01_hide = (isset($settings->page01_hide) && $settings->page01_hide) ? $settings->page01_hide : 0;
        // 第二页是否展示
        $page02_hide = (isset($settings->page02_hide) && $settings->page02_hide) ? $settings->page02_hide : 0;
        // 第三页是否展示
        $page03_hide = (isset($settings->page03_hide) && $settings->page03_hide) ? $settings->page03_hide : 0;
        // 第四页是否展示
        $page04_hide = (isset($settings->page04_hide) && $settings->page04_hide) ? $settings->page04_hide : 0;
        // 第五页是否展示
        $page05_hide = (isset($settings->page05_hide) && $settings->page05_hide) ? $settings->page05_hide : 0;
        // 第六页是否展示
        $page06_hide = (isset($settings->page06_hide) && $settings->page06_hide) ? $settings->page06_hide : 0;




        $output = '<div class="swiper-container full-page" id="full-page">
			<div class="swiper-wrapper">';
            if($page01_hide == 0) {
                $output .= '
                <div class="swiper-slide">
                    <div class="page page-1">
                        <video muted="muted" loop="loop" autoplay="autoplay" width="1920" height="1080">
                            <source src="' . $page01_slider_video . '" type="video/mp4">
                        </video>
                        <div class="video-pup"></div>
                        <div class="content">
                            <div class="part01 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
                                <p class="name">' . $page01_slider_title . '</p>
                                <p class="desc">' . $page01_slider_desc . '</p>
                            </div>
                            <div class="part02 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">
                                <a href="' . $page01_index_link . '">进入首页</a>
                                <p>向下滚动查看更多精彩内容</p>
                            </div>
                            <div class="part03 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s">' . $page01_slider_content . '</div>
                            <div class="part04 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="2.4s">
                                <div class="new-list">';
                                    foreach($page01_items as $l => $v)
                                    {
                                        $output.='<a class="item" href="' . $v->link .'">';
                                        $output.='<p>'. $v->title . '</p>';
                                        $output.='<span>'. date('Y-m-d',strtotime($v->created)) .'</span>';
                                        $output.='</a>';
                                    }
                                $output.='
                                </div>
                            </div>
                        </div>
                    </div>
                </div>';
            }
            if($page02_hide == 0) {
                $output .= '	
				<div class="swiper-slide">
					<div class="page page-2">
						<div class="content">
							<div class="down-box">
								<div class="img-1 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0s">
									<img src="' . $page02_slider_img01 . '" alt="" >
								</div>
								<div class="img-2 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="1s" swiper-animate-delay="0.6s">
									<img src="' . $page02_slider_img02 . '" alt="" >
								</div>
								<div class="img-3 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="1.2s">
									<img src="' . $page02_slider_img03 . '" alt="" >
								</div>
							</div>
							<div class="swiper-container page2-swiper">
								<div class="swiper-wrapper">';
                                    foreach ($page02_image_item as $key => $val) {
                                        $output .= '<div class="swiper-slide" style="background-image:url(' . $val->slider_img . ')">';
                                            if($key == 0) {
                                                $output .= '<img src="' . $page02_slider_img04 . '" alt="" >';
                                            }
                                        $output .= '</div>';
                                    }
                    $output .= '</div>
							</div>
						</div>
					</div>
				</div>';
            }
            if($page03_hide == 0) {
                $output .= '
				<div class="swiper-slide">
					<div class="page page-3">
						<div class="content">
							<div class="top-box">
								<div class="part01 swiper-no-swiping">
									<h3 class="title ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">' . $page03_slider_title . '<span>' . $page03_slider_desc . '</span></h3>
									<div class="html ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.3s">
										' . $page03_slider_content . '
									</div>
									<a href="' . $page03_index_link . '" class="btn-more ani" swiper-animate-style-cache="" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.8s">了解详情</a>
								</div>
								<div class="part02 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="2s" swiper-animate-delay="1.2s">
									<!-- Swiper -->
									<div class="swiper-container gallery-top">
										<div class="swiper-wrapper">';
                                        foreach ($page03_image_item as $key => $val) {
                                            $output .= '<div class="swiper-slide" style="background-image:url(' . $val->slider_img . ')"></div>';
                                        }
                                        $output .= '
                                        </div>
                                    </div>
                                    <div class="swiper-container gallery-thumbs">
                                        <div class="swiper-wrapper">';
                                        foreach ($page03_image_item as $key => $val) {
                                            $output .= '<div class="swiper-slide" style="background-image:url(' . $val->slider_img_s . ')"></div>';
                                        }
                                        $output .= '
										</div>
									</div>
								</div>
							</div>
							<div class="bot-box swiper-no-swiping ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.5s" swiper-animate-delay="1.8s">
								<div class="part01">
									<p>' . $page03_bot_title . '</p>
									<p>' . $page03_bot_desc . '</p>
								</div>
								<div class="part02">' . $page03_bot_content . '</div>
							</div>
						</div>
					</div>
				</div>';
            }
            if($page04_hide == 0) {
                $output .= '<div class="swiper-slide">
					<div class="page page-4">
						<div class="content">
							<div class="part01 ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.4s" swiper-animate-delay="0s">
								<h3 class="title ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.4s">' . $page04_slider_title . '</h3>
								<h5 class="desc ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">' . $page04_slider_desc . '</h5>
								<div class="ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s">
									<div>' . $page04_slider_content . '</div>
									<a href="' . $page04_index_link . '" class="btn-more">产品中心</a>
								</div>
							</div>
						</div>
					</div>
				</div>';
            }
            if($page05_hide == 0) {
                $output .= '
				<div class="swiper-slide">
					<div class="page page-5">
						<div class="content">
							<img class=" ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s" src="' . $page05_desc_img .'" style="max-width: 100%;" >
							<div class="part01 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.6s">
								<div class="item">'.$page05_ani_title01.'</div>
								<div class="item">'.$page05_ani_title02.'</div>
								<div class="item">'.$page05_ani_title03.'</div>
							</div>
							<div class="part02 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s">';
                                foreach ($page05_title_item as $key => $val)
                                {
                                    $output .= '<div class="item">'.$val->title.'</div>';
                                }
                                $output.='
							</div>
							<img class="ani" swiper-animate-effect="bounceIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s"src="'.$page05_goods_img.'" style="max-width: 100%;" >
						</div>
					</div>
				</div>';
            }
            if($page06_hide == 0) {
                $output .= '
				<div class="swiper-slide">
					<div class="page page-6">
						<div class="content swiper-no-swiping">
							<div class="part01">
								<a href="' . $page06_map_link . '" target="_blank" class="action-box ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img src="https://oss.lcweb01.cn/joomla/20211015/eb23f734f5f115a28df7fa14935b96a0.png" alt="">
								</a>
								<div class="mobile ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img class="ewm" src="' . $page06_ewm_img . '" alt="">
									<p class="ewm-t">微信二维码</p>
									<div class="address">' . $page06_concat . '</div>
								</div>
								<a href="' . $page06_message_link . '" target="_blank" class="action-box ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img src="https://oss.lcweb01.cn/joomla/20211015/0d7776bc4f9d4da040d74898c1f7c857.png" alt="">
								</a>
							</div>
							<div class="part02 ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">
								<p>版权所有：' . $page06_copyright . '</p>
								<a href="' . $page06_record_link . '" target="_blank"><img src="https://oss.lcweb01.cn/joomla/20211015/d2b288ec8fca02e2036d473db62f96d6.png" alt="">&nbsp;&nbsp;备案号：' . $page06_record . '</a>
							</div>
							<div class="part03 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.2s">' . $page06_bot_concat . '</div>
							<div class="part04 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.4s">
								<div class="new-list">';
                                    foreach($page06_items as $l => $v)
                                    {
                                        $output.='<a class="item" href="' . $v->link .'">';
                                        $output.='<p>'. $v->title . '</p>';
                                        $output.='<span>'. date('Y-m-d',strtotime($v->created)) .'</span>';
                                        $output.='</a>';
                                    }
                                $output.='
								</div>
							</div>
						</div>
					</div>
				</div>';
            }
			$output .= '</div>
			<!-- Add Pagination -->
			<div class="swiper-pagination"></div>
			<!-- Add Arrows -->
			<div class="swiper-button-next"></div>
			<!-- <div class="swiper-button-prev"></div> -->
		</div>
		<!--<script src="/components/com_jwpagefactory/addons/product_list/assets/js/jquery.scrollbox.js"></script>-->
		<script src="/components/com_jwpagefactory/addons/full_page/assets/js/jquery.liMarquee.js"></script>
		<script>
		    jQuery(function($){
            });
		</script>
		';
        return $output;
    }
    public function css() {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;

        // 第二页
        $page02_slider_img = (isset($settings->page02_slider_img) && $settings->page02_slider_img) ? $settings->page02_slider_img : "https://oss.lcweb01.cn/joomla/20211015/cfae18bc4ced7695fdbb90136385ef3f.jpg";
        // 第三页
        $page03_slider_img = (isset($settings->page03_slider_img) && $settings->page03_slider_img) ? $settings->page03_slider_img : "https://oss.lcweb01.cn/joomla/20211015/09d9ca14da78c0b91996f534478f2597.jpg";
        // 第四页
        $page04_slider_img = (isset($settings->page04_slider_img) && $settings->page04_slider_img) ? $settings->page04_slider_img : "https://oss.lcweb01.cn/joomla/20211015/7d54100747c53974bbe108680e852f5a.jpg";
        // 第五页
        $page05_slider_img = (isset($settings->page05_slider_img) && $settings->page05_slider_img) ? $settings->page05_slider_img : "https://oss.lcweb01.cn/joomla/20211015/78e56070c0b01583e60220dde00ce1f6.jpg";
        // 第六页
        $page06_slider_img = (isset($settings->page06_slider_img) && $settings->page06_slider_img) ? $settings->page06_slider_img : "https://oss.lcweb01.cn/joomla/20211015/cf664f5f0b11ea6fae1b02d04bead2b3.jpg";

        $css = $addonId . ' * {
				margin: 0;
				padding: 0;
			}
			
			' . $addonId . ' .full-page {
				width: 100%;
				height: 100vh;
				min-width: 1200px;
				margin-left: auto;
				margin-right: auto;
			}


			/* 新增样式 */

			/* 下一页按钮 */
			' . $addonId . ' .full-page .swiper-button-next {
				left: 0;
				right: 0;
				top: auto;
				margin: auto;
				bottom: 30px;
				transform: rotateZ(90deg);
				-webkit-animation: fade 2s infinite linear;
				animation: fade 3s infinite linear;
				color: #fff;
			}

			' . $addonId . ' .full-page .swiper-button-next.swiper-button-disabled {
				display: none;
			}

			/* 切换点 */
			' . $addonId . ' .swiper-container-vertical>.swiper-pagination-bullets {
				right: 17px !important;
			}

			' . $addonId . ' .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
				width: 14px;
				height: 14px;
				background-color: transparent;
				border: #fff solid 2px;
				opacity: 1;
				margin: 10px 0;
				position: relative;
			}

			' . $addonId . ' .full-page .swiper-pagination-bullet-active::after {
				content: \'\';
				position: absolute;
				width: 10px;
				height: 10px;
				background-color: #FFFFFF;
				border-radius: 50%;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}

			@-webkit-keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}

			@keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}
			@-webkit-keyframes scale {
				0% {
					transform: scale(1);
				}
			
				50% {
					transform: scale(1.1);
				}
			
				100% {
					transform: scale(1);
				}
			}
			
			@keyframes scale {
				0% {
					transform: scale(1);
				}
			
				50% {
					transform: scale(1.1);
				}
			
				100% {
					transform: scale(1);
				}
			}

			' . $addonId . ' .page {
				width: 100%;
				height: 100%;
				position: relative;
				background-color: rgb(255, 255, 255);
				background-repeat: no-repeat;
				background-position: 50% 100%;
				background-size: cover;
				/* Center slide text vertically */
				display: -webkit-box;
				display: -ms-flexbox;
				display: -webkit-flex;
				display: flex;
				-webkit-box-pack: center;
				-ms-flex-pack: center;
				-webkit-justify-content: center;
				justify-content: center;
				-webkit-box-align: center;
				-ms-flex-align: center;
				-webkit-align-items: center;
				align-items: center;
				overflow: hidden;
			}
			' . $addonId . ' .page .content {
				width: 1200px;
				box-sizing: border-box;
			}

			' . $addonId . ' .full-page .swiper-slide .page-2 {
				background-image: url("' . $page02_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-3 {
				background-image: url("' . $page03_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-4 {
				background-image: url("' . $page04_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-5 {
				background-image: url("' . $page05_slider_img . '");
			}

			' . $addonId . ' .full-page .swiper-slide .page-6 {
				background-image: url("' . $page06_slider_img . '");
			}
			
			
			' . $addonId . ' .page .btn-more {
				display: block;
				font-size: 14px;
				color: #FFF;
				text-decoration: none;
				background: url("https://oss.lcweb01.cn/joomla/20211014/7d606ee026f3cf4ec1df7180bb9a3a20.jpg") no-repeat center;
				width: 220px;
				height: 40px;
				line-height: 40px;
				text-align: center;
			}
			' . $addonId . ' .page .btn-more:hover {
				text-decoration: underline;
			}
			
			' . $addonId . ' .page-1 video {
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				top: 0;
				z-index: 1;
			}
			' . $addonId . ' .page-1 .video-pup {
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				top: 0;
				z-index: 2;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.4);
			}
			' . $addonId . ' .page-1 .content {
				position: relative;
				z-index: 10;
				text-align: center;
				width: 1100px;
			}
			' . $addonId . ' .page-1 .content .part01 .name {
				color: #e2e3e5;
				font-size: 36px;
				margin-bottom: 14px;
			}
			' . $addonId . ' .page-1 .content .part01 .desc {
				color: #a6a6a6;
				font-size: 18px;
			}
			' . $addonId . ' .page-1 .content .part02 {
				margin-top: 12%;
			}
			' . $addonId . ' .page-1 .content .part02 a {
				width: 160px;
				height: 40px;
				line-height: 40px;
				text-align: center;
				border: 1px solid #fafafa;
				display: block;
				font-size: 14px;
				color: #FFF;
				text-decoration: none;
				margin: auto;
				transition: all ease-in-out 0.3;
				
			}
			' . $addonId . ' .page-1 .content .part02 a:hover {
			    color: #0ba7ff;
				border: 1px solid #0ba7ff;
			}
			' . $addonId . ' .page-1 .content .part02 p {
				color: #fafafa;
				font-size: 12px;
				margin-top: 20px;
			}
			' . $addonId . ' .page-1 .content .part03 {
				margin-top: 8%;
				color: #fff;
				font-size: 14px;
				line-height: 2;
			}
			' . $addonId . ' .page-1 .part04, ' . $addonId . ' .page-6 .part04 {
				width: 60%;
				margin: auto;
				margin-top: 60px;
			}
			' . $addonId . ' .page-1 .part04 .new-list, ' . $addonId . ' .page-6 .part04 .new-list {
				width: 100%;
				height: 150px;
				background: transparent;
			}
			' . $addonId . ' .page-1 .part04 .new-list .item, ' . $addonId . ' .page-6 .part04 .new-list .item {
				width: 100%;
				height: 40px;
				border-bottom: 1px dotted #CCC;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #333;
				font-size: 12px;
			}
			' . $addonId . ' .page-1 .part04 .new-list .item span, ' . $addonId . ' .page-6 .part04 .new-list .item span {
				color: #999;
			}
			' . $addonId . ' .page-2 .content {
				height: 75%;
				position: relative;
			}
			' . $addonId . ' .page-2 .down-box .img-1 {
				width: 510px;
				margin-bottom: 20px;
			}
			' . $addonId . ' .page-2 .down-box .img-2 {
				width: 262px;
				height: 44px;
				padding-left: 20px;
				box-sizing: border-box;
				background: #fff;
				display: flex;
				align-items: center;
				margin-bottom: 30px;
			}
			' . $addonId . ' .page-2 .down-box .img-3 {
				width: 538px;
			}
			' . $addonId . ' .page-2 .down-box .img-3 img {
				width: 100%;
			}
			' . $addonId . ' .page-2 .page2-swiper {
				position: absolute;
				width: 100vw;
				height: 100%;
				top: 0;
				left: 0;
			}
			' . $addonId . ' .page-2 .page2-swiper .swiper-slide {
				background: url() no-repeat center;
				background-size: cover;
				position: relative;
			}
			' . $addonId . ' .page-2 .page2-swiper .swiper-slide img {
				position: absolute;
				left: 62px;
				bottom: 185px;
				-webkit-animation: scale 2s infinite linear;
				animation: scale 2s infinite linear;
			}
			
			' . $addonId . ' .gallery-top {
				height: 80%;
				width: 100%;
			}
			' . $addonId . ' .gallery-top .swiper-slide {
				width: 100%;
				height: 100%;
				background: url() no-repeat center;
				background-size: cover;
			}
			' . $addonId . ' .gallery-thumbs {
				height: 80px;
				box-sizing: border-box;
				padding: 10px 0;
				margin: auto;
			}
			' . $addonId . ' .gallery-thumbs .swiper-slide {
				height: 100%;
				opacity: 0.4;
				border: 2px transparent solid;
				background: url() no-repeat center;
				background-size: cover;
				transition: all ease-in-out 0.3s;
			}
			' . $addonId . ' .gallery-thumbs.swiper-container-free-mode>.swiper-wrapper {
				display: flex;
				justify-content: center;
			}
			' . $addonId . ' .gallery-thumbs .swiper-slide-thumb-active {
				opacity: 1;
				border: 2px #609ee9 solid;
			}
			' . $addonId . ' .page-3 .content .top-box {
				display: flex;
			}
			' . $addonId . ' .page-3 .content .top-box .part01 {
				width: calc(100% - 660px);
				color: #e2e3e5;
				font-size: 16px;
				line-height: 1.5;
				padding-right: 50px;
				box-sizing: border-box;
			}
			' . $addonId . ' .page-3 .content .top-box .part01 .title {
				font-size: 30px;
				color: #fff;
			}
			' . $addonId . ' .page-3 .content .top-box .part01 .title span {
				font-size: 16px;
				font-weight: 500;
				margin-left: 20px;
			}
			' . $addonId . ' .page-3 .content .top-box .part01 .html {
				font-size: 16px;
				line-height: 2;
				text-indent: 2rem;
				margin-bottom: 60px;
				margin-top: 20px;
			}
			' . $addonId . ' .page-3 .content .top-box .part01 .html p {
				margin-bottom: 20px;
			}
			' . $addonId . ' .page-3 .content .top-box .part02 {
				width: 660px;
				height: 520px;
			}
			' . $addonId . ' .page-3 .content .bot-box {
				display: flex;
				align-items: center;
				color: #fff;
				margin-top: 70px;
			}
			' . $addonId . ' .page-3 .content .bot-box .part01 {
				width: 140px;
				font-size: 20px;
				font-weight: bold;
			}
			' . $addonId . ' .page-3 .content .bot-box .part01 p:nth-child(2) {
				color: #fafafa;
				margin-top: 5px;
			}
			' . $addonId . ' .page-3 .content .bot-box .part02 {
				width: calc(100% - 150px);
				text-align: center;
				line-height: 2.4;
				font-size: 14px;
			}
			' . $addonId . ' .page-4 .content {
				height: 60%;
			}
			' . $addonId . ' .page-4 .part01 {
				width: 611px;
				height: auto;
				background-color: rgba(0, 0, 0, 0.8);
				color: #fff;
				padding: 50px 40px;
				box-sizing: border-box;
				margin-left: -30px;
				color: #eeeeee;
			}
			' . $addonId . ' .page-4 .part01 .title {
				font-size: 30px;
				font-weight: bold;
				margin-bottom: 20px;
			}
			' . $addonId . ' .page-4 .part01 .desc {
				font-size: 18px;
				font-weight: 400;
				margin-bottom: 16px;
			}
			' . $addonId . ' .page-4 .part01 p {
				font-size: 16px;
			}
			' . $addonId . ' .page-4 .part01 span {
				color: #fca308;
			}
			' . $addonId . ' .page-4 .part01 .btn-more {
				margin-top: 30px;
			}
			' . $addonId . ' .page-5 .content {
				padding-left: 580px;
			}
			' . $addonId . ' .page-5 .part01 {
				width: 540px;
				height: 192px;
				background: url("https://oss.lcweb01.cn/joomla/20211014/e22c6cdb54e14944365f12875cbbe2ea.png") no-repeat center;
				margin-top: 20px;
				position: relative;
			}
			' . $addonId . ' .page-5 .part01 .item {
				position: absolute;
				color: #0758b3;
				font-size: 20px;
				width: 55px;
				font-weight: bold;
				text-align: center;
			}
			' . $addonId . ' .page-5 .part01 .item:nth-child(1) {
				top: 68px;
				left: 70px;
			}
			' . $addonId . ' .page-5 .part01 .item:nth-child(2) {
				top: 28px;
				left: 243px;
			}
			' . $addonId . ' .page-5 .part01 .item:nth-child(3) {
				top: 60px;
				left: 418px;
			}
			' . $addonId . ' .page-5 .part02 {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				margin-top: 30px;
				margin-bottom: 80px;
			}
			' . $addonId . ' .page-5 .part02 .item {
				width: calc(100% /2);
				font-size: 16px;
				line-height: 40px;
				color: #fff;
				font-weight: bold;
				background: url("https://oss.lcweb01.cn/joomla/20211014/a45823b200747db062eec47b050ea604.png") no-repeat left center;
				background-size: 24px;
				padding-left: 30px;
				box-sizing: border-box;
			}
			' . $addonId . ' .page-6 .part01 {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
				font-size: 14px;
				line-height: 1.75;
				margin-top: 80px;
			}
			' . $addonId . ' .page-6 .part01 img {
				display: block;
			}
			' . $addonId . ' .page-6 .part01 .action-box {
				cursor: pointer;
			}
			' . $addonId . ' .page-6 .part01 .mobile {
				display: flex;
				flex-direction: column;
				align-items: center;
				background: url("https://oss.lcweb01.cn/joomla/20211015/9a6e4d2d413c157f0a30d24672767ba4.png") no-repeat center;
				background-size: contain;
				width: 250px;
				height: 490px;
				margin: 0 100px;
			}
			' . $addonId . ' .page-6 .part01 .mobile .ewm {
				width: 100px;
				margin-top: 90px;
				image-rendering: -moz-crisp-edges;
				image-rendering: -o-crisp-edges;
				image-rendering: -webkit-optimize-contrast;
				image-rendering: crisp-edges;
				-ms-interpolation-mode: nearest-neighbor;
				margin-bottom: 10px;
			}
			' . $addonId . ' .page-6 .part01 .mobile .address {
				margin: 0 38px;
				border-top: #fff solid 1px;
				margin-top: 10px;
				padding-top: 10px;
				text-align: center;
			}
			' . $addonId . ' .page-6 .part01 .mobile .address p {
				margin-bottom: 10px;
			}
			' . $addonId . ' .page-6 .part02 {
				display: flex;
				justify-content: center;
				color: #555555;
				font-size: 11px;
				margin-top: 20px;
			}
			' . $addonId . ' .page-6 .part02 a {
				color: #555555;
				text-decoration: none;
				margin-left: 30px;
				display: flex;
				align-items: center;
			}
			' . $addonId . ' .page-6 .part03 {
				color: #ffffff;
				font-size: 16px;
				line-height: 2;
				text-align: center;
				margin-top: 40px;
			}
			' . $addonId . ' .page-6 .part04 {
				margin-top: 10px;
				width: 38%;
			}';
        return $css;
    }
    public function js() {
        $settings = $this->addon->settings;
        $id = $this->addon->id;
        $addonId = '#jwpf-addon-' . $id;
        return '
            jQuery(document).ready(function($){
		        var swiper = new Swiper(\'' . $addonId . ' #full-page\', {
                    direction: \'vertical\',
                    slidesPerView: 1,
                    mousewheel: true,
                    noSwiping : true,
                    pagination: {
                        el: \'' . $addonId . ' #full-page .swiper-pagination\',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: \'' . $addonId . ' #full-page .swiper-button-next\',
                        // prevEl: \'.swiper-button-prev\',
                    },
                    on:{
                        init: function(){
                            swiperAnimateCache(this); //隐藏动画元素 
                            swiperAnimate(this); //初始化完成开始动画
                        }, 
                        slideChangeTransitionEnd: function(){ 
                            swiperAnimateCache(this); //隐藏动画元素 
                            swiperAnimate(this); //每个slide切换结束时也运行当前slide动画
                            // this.slides.eq(this.activeIndex).find(\'.ani\').removeClass(\'ani\'); 动画只展现一次，去除ani类名
                        } 
                    }
                });
                var page2Swiper = new Swiper(\'' . $addonId . ' .page2-swiper\', {
                    slidesPerView: 1,
                    autoplay: true,
                    speed: 800,
                    loop: true
                });
                // 第三页 图片切换 缩略图
                var galleryThumbs = new Swiper(\'' . $addonId . ' .gallery-thumbs\', {
                    spaceBetween: 10,
                    slidesPerView: 6,
                    freeMode: true,
                    watchSlidesVisibility: true,
                    watchSlidesProgress: true,
                });
                var galleryTop = new Swiper(\'' . $addonId . ' .gallery-top\', {
                    spaceBetween: 10,
                    autoplay: true,
                    speed: 3000,
                    effect : \'fade\',
                    thumbs: {
                        swiper: galleryThumbs,
                        slidesPerView: 5,
                    }
                });
                // 新闻滚动
                $(\'' . $addonId . ' .new-list\').liMarquee({
                    direction: \'up\',
                    drag: false,
                    scrollamount: 10
                });
//                $(\'' . $addonId . ' .new-list\').scrollbox({
//                  linear: true,
//                  step: 1,
//                  delay: 0,
//                  speed: 100
//                });
		    })
        ';
    }
    public function scripts() {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.animate1.0.3.min.js',
//            JURI::base(true) . '/components/com_jwpagefactory/addons/full_page/assets/js/jquery.liMarquee.js',
//            JURI::base(true) . '/components/com_jwpagefactory/addons/product_list/assets/js/jquery.scrollbox.js',
        );

        return $js;
    }
    public function stylesheets() {
        $style_sheet = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
            JURI::base(true) . '/components/com_jwpagefactory/addons/full_page/assets/css/animate.min.css',
            JURI::base(true) . '/components/com_jwpagefactory/addons/full_page/assets/css/liMarquee.css',
        );

        return $style_sheet;
    }
    public static function getTemplate() {
        $output = '
        <#
            var addonId = "#jwpf-addon-" + data.id;
            var slide_page = data.slide_page || "page01"
            // 第二页
            var page02_slider_img = data.page02_slider_img || "https://oss.lcweb01.cn/joomla/20211015/cfae18bc4ced7695fdbb90136385ef3f.jpg";
            // 第三页
            var page03_slider_img = data.page03_slider_img || "https://oss.lcweb01.cn/joomla/20211015/09d9ca14da78c0b91996f534478f2597.jpg";
            // 第四页
            var page04_slider_img = data.page04_slider_img || "https://oss.lcweb01.cn/joomla/20211015/7d54100747c53974bbe108680e852f5a.jpg";
            // 第五页
            var page05_slider_img = data.page05_slider_img || "https://oss.lcweb01.cn/joomla/20211015/78e56070c0b01583e60220dde00ce1f6.jpg";
            // 第六页
            var page06_slider_img = data.page06_slider_img || "https://oss.lcweb01.cn/joomla/20211015/cf664f5f0b11ea6fae1b02d04bead2b3.jpg";
        #>
        <style>
            {{ addonId }} * {
				margin: 0;
				padding: 0;
			}
			
			{{ addonId }} .full-page {
				width: 100%;
				height: 100vh;
				min-width: 1200px;
				margin-left: auto;
				margin-right: auto;
			}


			/* 新增样式 */

			/* 下一页按钮 */
			{{ addonId }} .full-page .swiper-button-next {
				left: 0;
				right: 0;
				top: auto;
				margin: auto;
				bottom: 30px;
				transform: rotateZ(90deg);
				-webkit-animation: fade 2s infinite linear;
				animation: fade 3s infinite linear;
				color: #fff;
			}

			{{ addonId }} .full-page .swiper-button-next.swiper-button-disabled {
				display: none;
			}

			/* 切换点 */
			{{ addonId }} .swiper-container-vertical>.swiper-pagination-bullets {
				right: 17px !important;
			}

			{{ addonId }} .swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
				width: 14px;
				height: 14px;
				background-color: transparent;
				border: #fff solid 2px;
				opacity: 1;
				margin: 10px 0;
				position: relative;
			}

			{{ addonId }} .full-page .swiper-pagination-bullet-active::after {
				content: \'\';
				position: absolute;
				width: 10px;
				height: 10px;
				background-color: #FFFFFF;
				border-radius: 50%;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
			}

			@-webkit-keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}

			@keyframes fade {
				0% {
					opacity: 1;
					bottom: 20px;
				}

				50% {
					opacity: 0.3;
					bottom: 10px;
				}

				100% {
					opacity: 1;
					bottom: 20px;
				}
			}
			@-webkit-keyframes scale {
				0% {
					transform: scale(1);
				}
			
				50% {
					transform: scale(1.1);
				}
			
				100% {
					transform: scale(1);
				}
			}
			
			@keyframes scale {
				0% {
					transform: scale(1);
				}
			
				50% {
					transform: scale(1.1);
				}
			
				100% {
					transform: scale(1);
				}
			}

			{{ addonId }} .page {
				width: 100%;
				height: 100%;
				position: relative;
				background-color: rgb(255, 255, 255);
				background-repeat: no-repeat;
				background-position: 50% 100%;
				background-size: cover;
				/* Center slide text vertically */
				display: -webkit-box;
				display: -ms-flexbox;
				display: -webkit-flex;
				display: flex;
				-webkit-box-pack: center;
				-ms-flex-pack: center;
				-webkit-justify-content: center;
				justify-content: center;
				-webkit-box-align: center;
				-ms-flex-align: center;
				-webkit-align-items: center;
				align-items: center;
				overflow: hidden;
			}
			{{ addonId }} .page .content {
				width: 1200px;
				box-sizing: border-box;
			}

			{{ addonId }} .full-page .swiper-slide .page-2 {
				background-image: url("{{ page02_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-3 {
				background-image: url("{{ page03_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-4 {
				background-image: url("{{ page04_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-5 {
				background-image: url("{{ page05_slider_img }}");
			}

			{{ addonId }} .full-page .swiper-slide .page-6 {
				background-image: url("{{ page06_slider_img }}");
			}
			
			
			{{ addonId }} .page .btn-more {
				display: block;
				font-size: 14px;
				color: #FFF;
				text-decoration: none;
				background: url("https://oss.lcweb01.cn/joomla/20211014/7d606ee026f3cf4ec1df7180bb9a3a20.jpg") no-repeat center;
				width: 220px;
				height: 40px;
				line-height: 40px;
				text-align: center;
			}
			{{ addonId }} .page .btn-more:hover {
				text-decoration: underline;
			}
			
			{{ addonId }} .page-1 video {
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				top: 0;
				z-index: 1;
			}
			{{ addonId }} .page-1 .video-pup {
				position: absolute;
				left: 0;
				right: 0;
				margin: auto;
				top: 0;
				z-index: 2;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.4);
			}
			{{ addonId }} .page-1 .content {
				position: relative;
				z-index: 10;
				text-align: center;
				width: 1100px;
			}
			{{ addonId }} .page-1 .content .part01 .name {
				color: #e2e3e5;
				font-size: 36px;
				margin-bottom: 14px;
			}
			{{ addonId }} .page-1 .content .part01 .desc {
				color: #a6a6a6;
				font-size: 18px;
			}
			{{ addonId }} .page-1 .content .part02 {
				margin-top: 12%;
			}
			{{ addonId }} .page-1 .content .part02 a {
				width: 160px;
				height: 40px;
				line-height: 40px;
				text-align: center;
				border: 1px solid #fafafa;
				display: block;
				font-size: 14px;
				color: #FFF;
				text-decoration: none;
				margin: auto;
				transition: all ease-in-out 0.3;
			}
			{{ addonId }} .page-1 .content .part02 a:hover {
			    color: #0ba7ff;
				border: 1px solid #0ba7ff;
			}
			{{ addonId }} .page-1 .content .part02 p {
				color: #fafafa;
				font-size: 12px;
				margin-top: 20px;
			}
			{{ addonId }} .page-1 .content .part03 {
				margin-top: 8%;
				color: #fff;
				font-size: 14px;
				line-height: 2;
			}
			{{ addonId }} .page-1 .part04, {{ addonId }} .page-6 .part04 {
				width: 60%;
				margin: auto;
				margin-top: 60px;
			}
			{{ addonId }} .page-1 .part04 .new-list, {{ addonId }} .page-6 .part04 .new-list {
				width: 100%;
				height: 150px;
				background: transparent;
			}
			{{ addonId }} .page-1 .part04 .new-list .item, {{ addonId }} .page-6 .part04 .new-list .item {
				width: 100%;
				height: 40px;
				border-bottom: 1px dotted #CCC;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #333;
				font-size: 12px;
			}
			{{ addonId }} .page-1 .part04 .new-list .item span, {{ addonId }} .page-6 .part04 .new-list .item span {
				color: #999;
			}
			{{ addonId }} .page-2 .content {
				height: 75%;
				position: relative;
			}
			{{ addonId }} .page-2 .down-box .img-1 {
				width: 510px;
				margin-bottom: 20px;
			}
			{{ addonId }} .page-2 .down-box .img-2 {
				width: 262px;
				height: 44px;
				padding-left: 20px;
				box-sizing: border-box;
				background: #fff;
				display: flex;
				align-items: center;
				margin-bottom: 30px;
			}
			{{ addonId }} .page-2 .down-box .img-3 {
				width: 538px;
			}
			{{ addonId }} .page-2 .down-box .img-3 img {
				width: 100%;
			}
			{{ addonId }} .page-2 .page2-swiper {
				position: absolute;
				width: 100vw;
				height: 100%;
				top: 0;
				left: 0;
			}
			{{ addonId }} .page-2 .page2-swiper .swiper-slide {
				background: url() no-repeat center;
				background-size: cover;
				position: relative;
			}
			{{ addonId }} .page-2 .page2-swiper .swiper-slide img {
				position: absolute;
				left: 62px;
				bottom: 185px;
				-webkit-animation: scale 2s infinite linear;
				animation: scale 2s infinite linear;
			}
			
			{{ addonId }} .gallery-top {
				height: 80%;
				width: 100%;
			}
			{{ addonId }} .gallery-top .swiper-slide {
				width: 100%;
				height: 100%;
				background: url() no-repeat center;
				background-size: cover;
			}
			{{ addonId }} .gallery-thumbs {
				height: 80px;
				box-sizing: border-box;
				padding: 10px 0;
				margin: auto;
			}
			{{ addonId }} .gallery-thumbs .swiper-slide {
				height: 100%;
				opacity: 0.4;
				border: 2px transparent solid;
				background: url() no-repeat center;
				background-size: cover;
				transition: all ease-in-out 0.3s;
			}
			{{ addonId }} .gallery-thumbs.swiper-container-free-mode>.swiper-wrapper {
				display: flex;
				justify-content: center;
			}
			{{ addonId }} .gallery-thumbs .swiper-slide-thumb-active {
				opacity: 1;
				border: 2px #609ee9 solid;
			}
			{{ addonId }} .page-3 .content .top-box {
				display: flex;
			}
			{{ addonId }} .page-3 .content .top-box .part01 {
				width: calc(100% - 660px);
				color: #e2e3e5;
				font-size: 16px;
				line-height: 1.5;
				padding-right: 50px;
				box-sizing: border-box;
			}
			{{ addonId }} .page-3 .content .top-box .part01 .title {
				font-size: 30px;
				color: #fff;
			}
			{{ addonId }} .page-3 .content .top-box .part01 .title span {
				font-size: 16px;
				font-weight: 500;
				margin-left: 20px;
			}
			{{ addonId }} .page-3 .content .top-box .part01 .html {
				font-size: 16px;
				line-height: 2;
				text-indent: 2rem;
				margin-bottom: 60px;
				margin-top: 20px;
			}
			{{ addonId }} .page-3 .content .top-box .part01 .html p {
				margin-bottom: 20px;
			}
			{{ addonId }} .page-3 .content .top-box .part02 {
				width: 660px;
				height: 520px;
			}
			{{ addonId }} .page-3 .content .bot-box {
				display: flex;
				align-items: center;
				color: #fff;
				margin-top: 70px;
			}
			{{ addonId }} .page-3 .content .bot-box .part01 {
				width: 140px;
				font-size: 20px;
				font-weight: bold;
			}
			{{ addonId }} .page-3 .content .bot-box .part01 p:nth-child(2) {
				color: #fafafa;
				margin-top: 5px;
			}
			{{ addonId }} .page-3 .content .bot-box .part02 {
				width: calc(100% - 150px);
				text-align: center;
				line-height: 2.4;
				font-size: 14px;
			}
			{{ addonId }} .page-4 .content {
				height: 60%;
			}
			{{ addonId }} .page-4 .part01 {
				width: 611px;
				height: auto;
				background-color: rgba(0, 0, 0, 0.8);
				color: #fff;
				padding: 50px 40px;
				box-sizing: border-box;
				margin-left: -30px;
				color: #eeeeee;
			}
			{{ addonId }} .page-4 .part01 .title {
				font-size: 30px;
				font-weight: bold;
				margin-bottom: 20px;
			}
			{{ addonId }} .page-4 .part01 .desc {
				font-size: 18px;
				font-weight: 400;
				margin-bottom: 16px;
			}
			{{ addonId }} .page-4 .part01 p {
				font-size: 16px;
			}
			{{ addonId }} .page-4 .part01 span {
				color: #fca308;
			}
			{{ addonId }} .page-4 .part01 .btn-more {
				margin-top: 30px;
			}
			{{ addonId }} .page-5 .content {
				padding-left: 580px;
			}
			{{ addonId }} .page-5 .part01 {
				width: 540px;
				height: 192px;
				background: url("https://oss.lcweb01.cn/joomla/20211014/e22c6cdb54e14944365f12875cbbe2ea.png") no-repeat center;
				margin-top: 20px;
				position: relative;
			}
			{{ addonId }} .page-5 .part01 .item {
				position: absolute;
				color: #0758b3;
				font-size: 20px;
				width: 55px;
				font-weight: bold;
				text-align: center;
			}
			{{ addonId }} .page-5 .part01 .item:nth-child(1) {
				top: 68px;
				left: 70px;
			}
			{{ addonId }} .page-5 .part01 .item:nth-child(2) {
				top: 28px;
				left: 243px;
			}
			{{ addonId }} .page-5 .part01 .item:nth-child(3) {
				top: 60px;
				left: 418px;
			}
			{{ addonId }} .page-5 .part02 {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				margin-top: 30px;
				margin-bottom: 80px;
			}
			{{ addonId }} .page-5 .part02 .item {
				width: calc(100% /2);
				font-size: 16px;
				line-height: 40px;
				color: #fff;
				font-weight: bold;
				background: url("https://oss.lcweb01.cn/joomla/20211014/a45823b200747db062eec47b050ea604.png") no-repeat left center;
				background-size: 24px;
				padding-left: 30px;
				box-sizing: border-box;
			}
			{{ addonId }} .page-6 .part01 {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
				font-size: 14px;
				line-height: 1.75;
				margin-top: 80px;
			}
			{{ addonId }} .page-6 .part01 img {
				display: block;
			}
			{{ addonId }} .page-6 .part01 .action-box {
				cursor: pointer;
			}
			{{ addonId }} .page-6 .part01 .mobile {
				display: flex;
				flex-direction: column;
				align-items: center;
				background: url("https://oss.lcweb01.cn/joomla/20211015/9a6e4d2d413c157f0a30d24672767ba4.png") no-repeat center;
				background-size: contain;
				width: 250px;
				height: 490px;
				margin: 0 100px;
			}
			{{ addonId }} .page-6 .part01 .mobile .ewm {
				width: 100px;
				margin-top: 90px;
				image-rendering: -moz-crisp-edges;
				image-rendering: -o-crisp-edges;
				image-rendering: -webkit-optimize-contrast;
				image-rendering: crisp-edges;
				-ms-interpolation-mode: nearest-neighbor;
				margin-bottom: 10px;
			}
			{{ addonId }} .page-6 .part01 .mobile .address {
				margin: 0 38px;
				border-top: #fff solid 1px;
				margin-top: 10px;
				padding-top: 10px;
				text-align: center;
			}
			{{ addonId }} .page-6 .part01 .mobile .address p {
				margin-bottom: 10px;
			}
			{{ addonId }} .page-6 .part02 {
				display: flex;
				justify-content: center;
				color: #555555;
				font-size: 11px;
				margin-top: 20px;
			}
			{{ addonId }} .page-6 .part02 a {
				color: #555555;
				text-decoration: none;
				margin-left: 30px;
				display: flex;
				align-items: center;
			}
			{{ addonId }} .page-6 .part03 {
				color: #ffffff;
				font-size: 16px;
				line-height: 2;
				text-align: center;
				margin-top: 40px;
			}
			{{ addonId }} .page-6 .part04 {
				margin-top: 10px;
				width: 38%;
			}
        </style>
        <div class="swiper-container full-page" id="full-page">
			<div class="swiper-wrapper">
			    <# if(slide_page == "page01") { 
			        var page01_slider_video = (typeof data.page01_slider_video !== "undefined" && data.page01_slider_video) ? data.page01_slider_video : "https://oss.lcweb01.cn/joomla/20211012/3df6aa5afa92f8c2320d38379007aa5b.mp4";
                    var srcs = "src=" + page01_slider_video;
                    var page01_slider_title = data.page01_slider_title || "瓦轴集团-中国轴承从这里开始...";
                    var page01_slider_desc = data.page01_slider_desc || "ZWZ - China\'s bearing startsfrom here...";
                    var page01_slider_content = data.page01_slider_content || "瓦房店轴承产品：瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 |TWB轴承 <br/> 友情链接：  瓦轴    ZWZ轴承     封闭母线槽     哈尔滨轴承   真空灌胶机  eja变送器    智能锁招商   HRB轴承     电子商务站   RTO     whl轴承   恒温恒湿试验箱价格 ";
			    #>
				<div class="swiper-slide">
					<div class="page page-1">
						<video muted="muted" loop="loop" autoplay="autoplay" width="1920" height="1080">
							<source {{srcs}} type="video/mp4">
						</video>
						<div class="video-pup"></div>
						<div class="content">
							<div class="part01 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
								<p class="name">{{ page01_slider_title }}</p>
								<p class="desc">{{ page01_slider_desc }}</p>
							</div>
							<div class="part02 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">
								<a href="">进入首页</a>
								<p>向下滚动查看更多精彩内容</p>
							</div>
							<div class="part03 ani swiper-no-swiping" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s">
								{{{ page01_slider_content }}}	 
							</div>
							<div class="part04 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="2.4s">
								<div class="new-list">
									<div class="item">
										<p>1轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>2轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>3轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>4轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>5轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# if(slide_page == "page02") { 
				    var page02_slider_img01 = data.page02_slider_img01 || "https://oss.lcweb01.cn/joomla/20211015/b27c3e46e08f0dcec316a180bbf64d34.png";
				    var page02_slider_img02 = data.page02_slider_img02 || "https://oss.lcweb01.cn/joomla/20211015/4d4fe59f37db8f7038a103c17ec4eff2.png";
				    var page02_slider_img03 = data.page02_slider_img03 || "https://oss.lcweb01.cn/joomla/20211015/d90f5d3417da39918ed842da4350d317.jpg";
				    var page02_slider_img04 = data.page02_slider_img04 || "https://oss.lcweb01.cn/joomla/20211015/4ce6f49ce79cc6e739c159913539bd79.png";
				    var page02_image_item = data.page02_image_item || [{
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211015/64274e83bbf3e9c2f3bcddf1c32b857e.png\',
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211015/0380b9306ab6c11c78c29d95bf466cc9.png\',
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211015/f99978701cbac2bcc2dea7b0de0cf681.png\',
                    }];
				#>
				<div class="swiper-slide">
					<div class="page page-2">
						<div class="content">
							<div class="down-box">
								<div class="img-1 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0s">
									<img src=\'{{ page02_slider_img01 }}\' alt="" >
								</div>
								<div class="img-2 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="1s" swiper-animate-delay="0.6s">
									<img src=\'{{ page02_slider_img02 }}\' alt="" >
								</div>
								<div class="img-3 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="1.2s">
									<img src=\'{{ page02_slider_img03 }}\' alt="" >
								</div>
							</div>
							<div class="swiper-container page2-swiper">
								<div class="swiper-wrapper">
								    <# _.each(page02_image_item, function (item, key){ 
                                        var style = "background-image: url(" + item.slider_img + ");";
                                    #>
                                        <div class="swiper-slide" style="{{style}}">
                                            <# if(key == 0) { #>
                                                <img src=\'{{ page02_slider_img04 }}\' alt="" >
                                            <# } #>
                                        </div>
                                    <# }); #>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# if(slide_page == "page03") { 
				    var page03_slider_title = data.page03_slider_title || "走进江轴";
                    var page03_slider_desc = data.page03_slider_desc || "WALK INTO JIANGZHOU";
                    var page03_slider_content = data.page03_slider_content || "<p> 江苏江轴轴承，着重于全面承接瓦房店轴承集团江苏销售平台的管理工作，承担着代表瓦轴对江苏区域轴承市场的开发和管理。在江瓦轴承的努力下，各行业用户的需求能更快捷更高效地与瓦轴对接，提升了响应效率，让解决方案更具针对性，为客户带来更多的效益！</p><p>公司常备库存7000万元以上，提供12000多个不同规格的轴承产品，在华东乃至全国具有显著的市场地位。产品广泛应用于航天、航空、铁路、风电、核电、矿山、汽车、船舶、海洋工程等涉及国计民生的重点行业和领域。</p>";
                    var page03_image_item = data.page03_image_item || [{
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211014/7e6a0e1bd250f285a43989c8d2b6dd15.png\',
                        slider_img_s: \'https://oss.lcweb01.cn/joomla/20211014/8e7ae1c75789d7b5fea59fb3a2d082ab.png\'
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211014/7d19437a959c6fb7a29be0516e5c5c8d.jpg\',
                        slider_img_s: \'https://oss.lcweb01.cn/joomla/20211014/addbb12cee6925111985a9eac848d71b.jpg\'
                    },
                    {
                        slider_img: \'https://oss.lcweb01.cn/joomla/20211014/ffda657c37d2c27f9ae5bb170a3fea5e.jpg\',
                        slider_img_s: \'https://oss.lcweb01.cn/joomla/20211014/7e596e363f6462e19ef3a6afcd402624.jpg\'
                    }];
                    var page03_bot_title = data.page03_bot_title || "江轴轴承中心";
                    var page03_bot_desc = data.page03_bot_desc || "PRODUCTS";
                    var page03_bot_content = data.page03_bot_content || "瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承 | 瓦房店轴承重载系列 | 瓦房店轴承精密系列 | 瓦房店轴承铁路系列  | 瓦房店轴承风电系列  | 瓦房店轴承特种系列  |  瓦轴地区 | 哈轴地区 | TWB轴承  | 瓦房店轴承风电系列 ";
				#>
				<div class="swiper-slide">
					<div class="page page-3">
						<div class="content">
							<div class="top-box">
								<div class="part01 swiper-no-swiping">
									<h3 class="title ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">{{ page03_slider_title }}<span>{{ page03_slider_desc }}</span></h3>
									<div class="html ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.3s">
										{{{ page03_slider_content }}}
									</div>
									<a href="" class="btn-more ani" swiper-animate-style-cache="" swiper-animate-effect="fadeInUp" swiper-animate-duration="1s" swiper-animate-delay="0.8s">了解详情</a>
								</div>
								<div class="part02 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="2s" swiper-animate-delay="1.2s">
									<!-- Swiper -->
									<div class="swiper-container gallery-top">
										<div class="swiper-wrapper">
										    <# _.each(page03_image_item, function (item, key){ 
										        var style = "background-image: url(" + item.slider_img + ");";
										    #>
											    <div class="swiper-slide" style="{{style}}"></div>
											<# }); #>
										</div>
									</div>
									<div class="swiper-container gallery-thumbs">
										<div class="swiper-wrapper">
											<# _.each(page03_image_item, function (item, key){ 
                                                var style = "background-image: url(" + item.slider_img_s + ");";
                                            #>
											    <div class="swiper-slide" style="{{style}}"></div>
											<# }); #>
										</div>
									</div>
								</div>
							</div>
							<div class="bot-box swiper-no-swiping ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.5s" swiper-animate-delay="1.8s">
								<div class="part01">
									<p>{{ page03_bot_title }}</p>
									<p>{{ page03_bot_desc }}</p>
								</div>
								<div class="part02">{{{ page03_bot_content }}}</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# if(slide_page == "page04") { 
				    var page04_slider_title = data.page04_slider_title || "轴承传动行业的优质方案提供者";
                    var page04_slider_desc = data.page04_slider_desc || "库存充足、品种齐全、专业销售世界优质品质轴承";
                    var page04_slider_content = data.page04_slider_content || "<p>常备库存<span>7000万元</span>以上，为数十个行业客户提供<span>12000多种</span>不同规格的轴承</p><p style=\"font-size: 14px;margin-top: 10px;\">The company\'s standing stock is more than 70 million yuan, providing more than 12,000 products of different specifications for dozens of industry customers.</p>";
				#>
				<div class="swiper-slide">
					<div class="page page-4">
						<div class="content">
							<div class="part01 ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.4s" swiper-animate-delay="0s">
								<h3 class="title ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.4s">{{ page04_slider_title }}</h3>
								<h5 class="desc ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">{{ page04_slider_desc }}</h5>
								<div class="ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s">
									<div>{{{ page04_slider_content }}}</div>
									<a href="" class="btn-more">产品中心</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
				<# if(slide_page == "page05") { 
				    var page05_desc_img = data.page05_desc_img || "https://oss.lcweb01.cn/joomla/20211014/37d95cf9379f31e27d5893f97e913850.png";
				    var page05_ani_title01 = data.page05_ani_title01 || "货源稳定";
				    var page05_ani_title02 = data.page05_ani_title02 || "库存充足";
				    var page05_ani_title03 = data.page05_ani_title03 || "物流快捷";
				    var page05_title_item = data.page05_title_item || [{
				        title: "瓦轴实力授权经销商"
				    },{
				        title: "常备7000万元以上库存"
				    },{
				        title: "20年服务品质 实力保证"
				    },{
				        title: "提供一站式轴承技术支持"
				    }];
				    var page05_goods_img = data.page05_goods_img || "https://oss.lcweb01.cn/joomla/20211014/0d47fca7f1e2f474bcb1c0c96c15842e.png";
				#>
				<div class="swiper-slide">
					<div class="page page-5">
						<div class="content">
							<img class=" ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s" src=\'{{ page05_desc_img }}\' style="max-width: 100%;" >
							<div class="part01 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0.6s">
								<div class="item">{{ page05_ani_title01 }}</div>
								<div class="item">{{ page05_ani_title02 }}</div>
								<div class="item">{{ page05_ani_title03 }}</div>
							</div>
							<div class="part02 ani" swiper-animate-effect="bounceIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.4s">
								<# _.each(page05_title_item, function (item, key){ 
								#>
									<div class="item">{{ item.title }}</div>
								<# }); #>
							</div>
							<img class="ani" swiper-animate-effect="bounceIn" swiper-animate-duration="0.8s" swiper-animate-delay="1.6s"src=\'{{ page05_goods_img }}\' style="max-width: 100%;" >
						</div>
					</div>
				</div>
				<# } #>
				<# if(slide_page == "page06") { 
				    var page06_ewm_img = data.page06_ewm_img || "https://oss.lcweb01.cn/joomla/20211015/c87bd2547328cd4aa812aa4e6c1940fb.jpg";
                    var page06_concat = data.page06_concat || "<p>地址：江苏省无锡市北塘区康桥丽景家园18-46号</p><p>电话：15061858253<br />联系人：董经理</p><p>电话：19895733877<br />联系人：董经理</p>";
                    var page06_copyright = data.page06_copyright || "江苏江瓦轴承有限公司无锡分公司";
                    var page06_record = data.page06_record || "苏ICP备19016653号-2";
                    var page06_bot_concat = data.page06_bot_concat || "<p>企业愿景：瓦轴第一总代理   永居销售量榜首</p><p>企业价值观：服务每一位客户，成就每一位员工</p><p>企业精神：诚信  创新  协作  共赢</p>";
				#>
				<div class="swiper-slide">
					<div class="page page-6">
						<div class="content swiper-no-swiping">
							<div class="part01">
								<a href="" target="_blank" class="action-box ani" swiper-animate-effect="fadeInLeft" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img src="https://oss.lcweb01.cn/joomla/20211015/eb23f734f5f115a28df7fa14935b96a0.png" alt="">
								</a>
								<div class="mobile ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img class="ewm" src=\'{{ page06_ewm_img }}\' alt="">
									<p class="ewm-t">微信二维码</p>
									<div class="address">{{{ page06_concat }}}</div>
								</div>
								<a href="" target="_blank"  class="action-box ani" swiper-animate-effect="fadeInRight" swiper-animate-duration="0.8s" swiper-animate-delay="0s">
									<img src="https://oss.lcweb01.cn/joomla/20211015/0d7776bc4f9d4da040d74898c1f7c857.png" alt="">
								</a>
							</div>
							<div class="part02 ani" swiper-animate-effect="fadeIn" swiper-animate-duration="0.8s" swiper-animate-delay="0.8s">
								<p>版权所有：{{ page06_copyright }}</p>
								<a href=""><img src="https://oss.lcweb01.cn/joomla/20211015/d2b288ec8fca02e2036d473db62f96d6.png" alt="">&nbsp;&nbsp;备案号：{{ page06_record }}</a>
							</div>
							<div class="part03 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.2s">{{{ page06_bot_concat }}}</div>
							<div class="part04 ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="0.8s" swiper-animate-delay="1.4s">
								<div class="new-list">
									<div class="item">
										<p>1轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>2轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>3轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>4轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
									<div class="item">
										<p>5轴承在使用前的安装要素</p>
										<span>2021-10-11</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<# } #>
			</div>
			<!-- Add Pagination -->
			<div class="swiper-pagination"></div>
			<!-- Add Arrows -->
			<div class="swiper-button-next"></div>
			<!-- <div class="swiper-button-prev"></div> -->
		</div>
        ';
        return $output;
    }
}