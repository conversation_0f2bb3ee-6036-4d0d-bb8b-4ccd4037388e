<?php
// /**
//  * <AUTHOR>
//  * @email        <EMAIL>
//  * @url          http://www.joomla.work
//  * @copyright    Copyright (c) 2010 - 2019 JoomWorker
//  * @license      GNU General Public License version 2 or later
//  * @date         2019/01/01 09:30
//  */
// //no direct accees
// defined('_JEXEC') or die ('Restricted access');

// JwAddonsConfig::addonConfig(
// 	array(
// 		'type' => 'content',
// 		'addon_name' => 'jw_image_title',
// 		'title' => JText::_('图标文字'),
// 		'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_DESC'),
// 		'category' => '常用插件',
// 		'attr' => array(
// 			'general' => array(
// 				'admin_label' => array(
// 					'type' => 'text',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
// 					'std' => ''
// 				),

// 				'title' => array(
// 					'type' => 'text',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
// 					'std' => '名称'
// 				),

// 				'title_fontsize' => array(
// 					'type' => 'slider',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
// 					'std' => 14,
// 					'depends' => array(array('title', '!=', '')),
// 					'responsive' => true,
// 					'max' => 400,
// 				),

// 				'title_lineheight' => array(
// 					'type' => 'slider',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
// 					'std' => 40,
// 					'depends' => array(array('title', '!=', '')),
// 					'responsive' => true,
// 					'max' => 400,
// 				),

				
// 				'title_text_color' => array(
// 					'type' => 'color',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
// 					'depends' => array(array('title', '!=', '')),
// 					'std' => '#333',

// 				),
// 				'title_hg_color' => array(
// 					'type' => 'color',
// 					'title' => JText::_('划过字体颜色'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
// 					'depends' => array(array('title', '!=', '')),
// 					'std' => '#555',

// 				),

// 				'title_margin' => array(
// 					'type' => 'margin',
// 					'title' => JText::_('外边距'),
// 					'placeholder' => '10',
// 					'responsive' => true,
//                     'std' => array('md' => '0px 0px 0px 0px', 'sm' => '0px 0px 0px 0px', 'xs' => '10px 10px 10px 10px'),
					
// 				),

// 				'title_padding' => array(
// 					'type' => 'padding',
// 					'title' => JText::_('内边距'),
// 					'placeholder' => '10',
// 					'responsive' => true,
//                     'std' => array('md' => '20px 20px 20px 20px', 'sm' => '10px 10px 10px 10px', 'xs' => '10px 10px 10px 10px'),

// 				),
// 				'wbk_color' => array(
// 					'type' => 'color',
// 					'title' => JText::_('外边框颜色'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
// 					'std' => '#eee'
					
// 				),
// 				'bg_color' => array(
// 					'type' => 'color',
// 					'title' => JText::_('背景色'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
// 					'std' => '#fff'
					
// 				),
// 				'hgbg_color' => array(
// 					'type' => 'color',
// 					'title' => JText::_('划过背景色'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_TEXT_COLOR_DESC'),
// 					'std' => '#fff'
					
// 				),

// 				'option_type' => array(
// 					'type' => 'buttons',
// 					'title' => '设置',
// 					'std' => 'plain',
// 					'values' => array(
// 						array(
// 							'label' => '普通',
// 							'value' => 'plain'
// 						),
// 						array(
// 							'label' => '高级',
// 							'value' => 'senior'
// 						),
// 					),
// 					'tabs' => true,
// 				),

// 				'image' => array(
// 					'type' => 'media',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_SELECT_DESC'),
// 					'show_input' => true,
// 					'std' => 'components/com_jwpagefactory/addons/image_title/assets/images/icon.png',
// 					'depends' => array(
// 						array('option_type', '=', 'plain')
// 					)
// 				),

// 				'image_width' => array(
// 					'type' => 'slider',
// 					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_WIDTH'),
// 					'max' => 2000,
// 					'min' => 0,
// 					'std' => 30,

// 					'responsive' => true,
// 					'depends' => array(
// 						array('option_type', '=', 'plain')
// 					)
// 				),

// 				'image_height' => array(
// 					'type' => 'slider',
// 					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_HEIGHT'),
// 					'max' => 2000,
// 					'min' => 0,
// 					'std' => 30,

// 					'responsive' => true,
// 					'depends' => array(
// 						array('option_type', '=', 'plain')
// 					)
// 				),

// 				'border_radius' => array(
// 					'type' => 'slider',
// 					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_BORDER_RADIUS'),
// 					'std' => 0,
// 					'max' => 1200,
// 					'depends' => array(
// 						array('option_type', '=', 'plain')
// 					)
// 				),

// 				'alt_text' => array(
// 					'type' => 'text',
// 					'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ALT_TEXT'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_ALT_TEXT_DESC'),
// 					'std' => 'Image',
// 					'depends' => array(
// 						array('image', '!=', ''),
// 						array('option_type', '=', 'plain')
// 					),
// 				),

// 				'link' => array(
// 					'type' => 'text',
// 					'title' => JText::_('外部链接->http(https)开头'),
// 					'std' => '',
// 					'depends' => array(
// 						array('image', '!=', ''),
// 						array('open_lightbox', '!=', 1),
// 						array('option_type', '=', 'plain')
// 					),
// 				),


// 				'detail_page_id' => array(
// 					'type' => 'select',
// 					'title' => '内部页面链接',
// 					'desc' => '',
// 					'depends' => array(
// 						array('image', '!=', ''),
// 						array('open_lightbox', '!=', 1),
// 						array('option_type', '=', 'plain')
// 					),
// 					'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
// 				),
// 				'target' => array(
// 					'type' => 'select',
// 					'title' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET'),
// 					'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_DESC'),
// 					'values' => array(
// 						'' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
// 						'_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
// 					),

// 					'depends' => array(
// 						array('image', '!=', ''),
// 						array('open_lightbox', '!=', 1),
// 						array('option_type', '=', 'plain')
// 					),
// 				),

// 				//高级设置
// 				'isFixed' => array(
// 					'type' => 'checkbox',
// 					'title' => '开启动画效果',
// 					'std' => 0,
// 					'depends' => array(
// 						array('option_type', '=', 'senior')
// 					)
// 				),
// 				'fixed_style' => array(
// 					'type' => 'select',
// 					'title' => '动画方式',
// 					'std' => 'top',
// 					'values' => array(
// 						'top' => '上浮动',
//                         'fanzhuan' => '360翻转',
//                         'huaguo' => '划过图标放大',
// 					),
// 					'depends' => array(
// 						array('option_type', '=', 'senior'),
// 						array('isFixed', '=', 1)
// 					)
// 				),


// 			),
// 		),
// 	)
// );
