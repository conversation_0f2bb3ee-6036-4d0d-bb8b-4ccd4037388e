<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'menu_custom',
        'title' => '自定义菜单',
        'desc' => '',
        'category' => '常用插件',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'part06' => array(
                    'type' => 'separator',
                    'title' => '选项卡配置'
                ),
                'is_tab' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启选项卡切换',
                    'std' => 0,
                ),
                'tab_theme' => array(
                    'type' => 'select',
                    'title' => '选项卡布局',
                    'values' => array(
                        'type01' => '布局01'
                    ),
                    'std' => 'type01',
                    'depends' => array(
                        array('is_tab', '=', '1')
                    ),
                ),
                'change_tab_item' => array(
                    'title' => '选项卡列表',
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '选项卡标题',
                            'std' => '选项卡标题',
                        ),
                        'part07' => array(
                            'type' => 'separator',
                            'title' => '菜单显示配置'
                        ),
                        'start_item_num' => array(
                            'type' => 'slider',
                            'title' => '从第n个菜单开始显示',
                            'max' => 100,
                            'min' => 0,
                        ),
                        'end_item_num' => array(
                            'type' => 'slider',
                            'title' => '到n个菜单结束显示',
                            'max' => 100,
                            'min' => 0,
                        ),
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1')
                    ),
                ),
                'part08' => array(
                    'type' => 'separator',
                    'title' => '选项卡样式配置',
                    'depends' => array(
                        array('is_tab', '=', '1')
                    ),
                ),
                'change_tab_style' => array(
                    'type' => 'buttons',
                    'title' => '选项卡配置选项',
                    'std' => 'tab_box',
                    'values' => array(
                        array(
                            'label' => '选项卡整体配置',
                            'value' => 'tab_box'
                        ),
                        array(
                            'label' => '选项卡项目配置',
                            'value' => 'tab_item'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('is_tab', '=', '1')
                    ),
                ),
                'change_tab_num' => array(
                    'type' => 'slider',
                    'title' => '选项卡一行显示个数',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 4,
                        'sm' => 4,
                        'xs' => 2
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    ),
                ),
                'tab_align' => array(
                    'type' => 'select',
                    'title' => '选项卡对齐方式',
                    'values' => array(
                        'flex-start' => '左对齐',
                        'flex-end' => '右对齐',
                        'center' => '居中',
                        'space-between' => '间隔对齐(头尾没有间距)',
                        'space-around' => '间隔对齐(头尾有间距)',
                        'space-evenly' => '间隔对齐(项目之间的间隔都相等)',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    ),
                ),
                'tab_box_border_width' => array(
                    'type' => 'margin',
                    'title' => '边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    )
                ),
                'tab_box_border_color' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    )
                ),
                'tab_box_border_style' => array(
                    'type' => 'select',
                    'title' => '边框样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    )
                ),
                'tab_box_border_radius' => array(
                    'type' => 'margin',
                    'title' => '边框半径',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    )
                ),
                'tab_box_bg_style' => array(
                    'type' => 'buttons',
                    'title' => '选项卡整体背景选项',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '颜色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    ),
                ),
                'tab_box_bgColor' => array(
                    'type' => 'color',
                    'title' => '选项卡整体背景颜色',
                    'depends' => array(
                        array('tab_box_bg_style', '=', 'color'),
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    )
                ),
                'tab_box_bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '选项卡整体渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('tab_box_bg_style', '=', 'gradient'),
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_box')
                    ),
                ),
                'tab_item_height' => array(
                    'type' => 'slider',
                    'title' => '选项卡项目高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 67,
                        'sm' => 67,
                        'xs' => 67
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item')
                    ),
                ),
                'tab_item_style' => array(
                    'type' => 'buttons',
                    'title' => '选项卡项目样式选项',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                        array(
                            'label' => '选中',
                            'value' => 'active'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item')
                    ),
                ),
                'tab_item_fontsize' => array(
                    'type' => 'slider',
                    'title' => '文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 18,
                        'sm' => 18,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'normal')
                    )
                ),
                'tab_item_fontW' => array(
                    'type' => 'checkbox',
                    'title' => '文字加粗',
                    'std' => 0,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'normal')
                    ),
                ),
                'tab_item_fontcolor' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#979A9F',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'normal')
                    )
                ),
                'tab_item_fontsize_hover' => array(
                    'type' => 'slider',
                    'title' => '文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 18,
                        'sm' => 18,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'hover')
                    )
                ),
                'tab_item_fontW_hover' => array(
                    'type' => 'checkbox',
                    'title' => '文字加粗',
                    'std' => 0,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'hover')
                    ),
                ),
                'tab_item_fontcolor_hover' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#111111',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'hover')
                    )
                ),
                'tab_item_fontsize_active' => array(
                    'type' => 'slider',
                    'title' => '文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 18,
                        'sm' => 18,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'active')
                    )
                ),
                'tab_item_fontW_active' => array(
                    'type' => 'checkbox',
                    'title' => '文字加粗',
                    'std' => 0,
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'active')
                    ),
                ),
                'tab_item_fontcolor_active' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#111111',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'active')
                    )
                ),
                'tab_item_bor_h_active' => array(
                    'type' => 'slider',
                    'title' => '下划线高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 4,
                        'sm' => 4,
                        'xs' => 4,
                    ),
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'active')
                    )
                ),
                'tab_item_bor_c_active' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#B71B28',
                    'depends' => array(
                        array('is_tab', '=', '1'),
                        array('change_tab_style', '=', 'tab_item'),
                        array('tab_item_style', '=', 'active')
                    )
                ),


                'part07' => array(
                    'type' => 'separator',
                    'title' => '菜单配置'
                ),
                // 切换item
                'section_tab_item' => array(
                    'title' => '菜单列表',
                    'attr' => array(
                        'botname' => array(
                            'type' => 'text',
                            'title' => '需加粗处理的字符',
                            'std' => '',
                        ),
                        'name' => array(
                            'type' => 'text',
                            'title' => '菜单标题',
                            'std' => '菜单标题',
                        ),
                        's_name' => array(
                            'type' => 'text',
                            'title' => '菜单副标题',
                            'std' => '',
                        ),
                        'desc' => array(
                            'type' => 'editor',
                            'title' => '菜单简介',
                            'std' => '这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介',
                        ),
                        'icon' => array(
                            'type' => 'media',
                            'title' => '菜单图标',
                            'desc' => '',
                            'std' => 'components/com_jwpagefactory/addons/menu_custom/assets/images/youji.png',
                        ),
                        'hover_icon' => array(
                            'type' => 'media',
                            'title' => '鼠标滑过菜单图标',
                            'desc' => '',
                            'std' => 'components/com_jwpagefactory/addons/menu_custom/assets/images/youji.png',
                        ),
                        'c_border_width' => array(
                            'type' => 'margin',
                            'title' => '边框宽度',
                            'max' => 100,
                            'min' => 0,
                            'responsive' => true,
                            'depends' => array(
                                //array('section_style', '=', 'normal'),
                                //array('is_border', '=', 1),
                                //array('custom_border_width', '=', 1)
                            )
                        ),
                        'item_bg_style' => array(
                            'type' => 'buttons',
                            'title' => '背景选项',
                            'std' => 'color',
                            'values' => array(
                                array(
                                    'label' => '颜色',
                                    'value' => 'color'
                                ),
                                array(
                                    'label' => '渐变色',
                                    'value' => 'gradient'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                )
                            ),
                            'tabs' => true,
                        ),
                        'bgColor' => array(
                            'type' => 'color',
                            'title' => '菜单背景颜色',
                            'std' => '#585656',
                            'depends' => array(
                                array('item_bg_style', '=', 'color')
                            )
                        ),
                        'bgGradient' => array(
	                        'type' => 'gradient',
	                        'title' => '菜单渐变色背景',
	                        'std' => array(
		                        "color" => "#00c6fb",
		                        "color2" => "#005bea",
		                        "deg" => "45",
		                        "type" => "linear"
	                        ),
	                        'depends' => array(
		                        array('item_bg_style', '=', 'gradient')
	                        ),
                        ),
                        'bgImg' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '菜单背景图片（需启用背景图片）',
                            'std' => '',
                            'depends' => array(
                                array('item_bg_style', '=', 'img')
                            )
                        ),
                        'hover_bgColor' => array(
                            'type' => 'color',
                            'title' => '鼠标滑过菜单背景颜色',
                            'std' => '#585656',
                            'depends' => array(
                                array('item_bg_style', '=', 'color')
                            )
                        ),
                        'hover_bgGradient' => array(
                            'type' => 'gradient',
                            'title' => '鼠标滑过菜单渐变色背景',
                            'std' => array(
                                "color" => "#00c6fb",
                                "color2" => "#005bea",
                                "deg" => "45",
                                "type" => "linear"
                            ),
                            'depends' => array(
                                array('item_bg_style', '=', 'gradient')
                            ),
                        ),
                        'hover_bgImg' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '鼠标滑过菜单背景图片',
                            'std' => '',
                            'depends' => array(
                                array('item_bg_style', '=', 'img')
                            )
                        ),
                        'detail_page_type' => array(
                            'type' => 'select',
                            'title' => '跳转方式',
                            'desc' => '跳转方式',
                            'values' => array(
                                'Internal_pages' => '内部页面',
                                'external_links' => '外部链接',
                            ),
                            'std' => 'Internal_pages'
                        ),
                        // 新加跳转的链接地址
                        'detail_url' => array(
                            'type' => 'text',
                            'title' => '链接跳转地址',
                            'desc' => '链接必须以http://或https://开始',
                            'placeholder' => 'http://',
                            'std' => '',
                            'depends' => array(
                                array('detail_page_type', '=','external_links'),
                            )
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('detail_page_type', '=','Internal_pages'),
                            )
                        ),
                        'link_target' => array(
                            'type' => 'select',
                            'title' => '跳转方式',
                            'std' => '_blank',
                            'values' => array(
                                '_blank' => '新页面',
                                '_self' => '当前页'
                            ),
                        ),
                        'item_part' => array(
                            'type' => 'separator',
                            'title' => '内容部分'
                        ),
                        'content_title' => array(
                            'type' => 'text',
                            'title' => '内容标题',
                            'std' => '内容标题',
                        ),
                        'content_desc' => array(
                            'type' => 'editor',
                            'title' => '内容简介',
                            'std' => '这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介这里是简介',
                        ),
                        'content_img' => array(
                            'type' => 'media',
                            'title' => '内容图片',
                            'desc' => '',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                        ),
                        'content' => array(
							'type' => 'builder',
							'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT'),
							'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TAB_ITEM_TEXT_DESC'),
							'std' => 'Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et.'
						),
                    ),
                    'std' => array(
//                        array(
//                            'name'=> '菜单标题',
//                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/youji.png',
//                            'bgColor' => '#585656',
//                            'item_bg_style' => 'color',
//                        ),
                        array(
                            'name'=> '菜单标题',
                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/youji.png',
                            'bgColor' => '#585656',
	                        'item_bg_style' => 'color',
                        ),
                        array(
                            'name'=> '菜单标题',
                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/minsu.png',
                            'bgColor' => '#773936',
                            'item_bg_style' => 'color',
                        ),
                        array(
                            'name'=> '菜单标题',
                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/jiaotong.png',
                            'bgColor' => '#BBA766',
                            'item_bg_style' => 'color',
                        ),
                        array(
                            'name'=> '菜单标题',
                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/jingqu.png',
                            'bgColor' => '#464F69',
                            'item_bg_style' => 'color',
                        ),
                        array(
                            'name'=> '菜单标题',
                            'icon'=> '/components/com_jwpagefactory/addons/menu_custom/assets/images/lianxi.png',
                            'bgColor' => '#74A099',
                            'item_bg_style' => 'color',
                        ),
                    ),
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '切换项配置'
                ),
                'is_swiper' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启切换',
                    'std' => 0,
                ),
                'swiper_style' => array(
                    'type' => 'buttons',
                    'title' => '切换选项状态',
                    'std' => 'item_s',
                    'values' => array(
                        array(
                            'label' => '轮播项配置',
                            'value' => 'item_s'
                        ),
                        array(
                            'label' => '翻页配置',
                            'value' => 'button_s'
                        ),
                       array(
                           'label' => '轮播点配置',
                           'value' => 'pagination_s'
                       )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('is_swiper', '=', 1)
                    ),
                ),
                'is_swiper_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启自动切换',
                    'std' => 0,
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                'is_swiper_loop' => array(
                    'type' => 'checkbox',
                    'title' => '是否循环切换',
                    'std' => 0,
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
                'swiper_container_p' => array(
                    'type' => 'margin',
                    'title' => '轮播框内边距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'item_s'),
                    ),
                ),
               'is_swiper_pagination' => array(
                   'type' => 'checkbox',
                   'title' => '是否开启轮播点',
                   'std' => 0,
                   'depends' => array(
                       array('is_swiper', '=', 1),
                       array('swiper_style', '=', 'pagination_s'),
                   ),
               ),
                'is_swiper_button' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启切换按钮',
                    'std' => 0,
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                    ),
                ),
                'swiper_button_prev' => array(
                    'type' => 'media',
                    'title' => '上一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png',
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_next' => array(
                    'type' => 'media',
                    'title' => '下一页',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png',
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_prev_hover' => array(
                    'type' => 'media',
                    'title' => '移入上一页',
                    'std' => '',
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_next_hover' => array(
                    'type' => 'media',
                    'title' => '移入下一页',
                    'std' => '',
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_width' => array(
                    'type' => 'slider',
                    'title' => '切换按钮宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 24
                    ),
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_height' => array(
                    'type' => 'slider',
                    'title' => '切换按钮高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 24
                    ),
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'swiper_button_top' => array(
                    'type' => 'slider',
                    'title' => '切换按钮上边距（百分比）',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 48,
                        'sm' => 48,
                        'xs' => 48
                    ),
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'is_swiper_button_top_px' => array(
                    'type' => 'checkbox',
                    'title' => '切换按钮上边距使用px单位',
                    'std' => 0,
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                    ),
                ),
                'swiper_button_left' => array(
                    'type' => 'slider',
                    'title' => '切换按钮两侧边距（px）',
                    'max' => 800,
                    'min' => -100,
                    'responsive' => true,
                    'std' => array(
                        'md' => 10,
                        'sm' => 10,
                        'xs' => 10
                    ),
                    'depends' => array(
                        array('is_swiper', '=', 1),
                        array('swiper_style', '=', 'button_s'),
                        array('is_swiper_button', '=', 1),
                    ),
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '菜单项配置'
                ),
                'is_item_active' => array(
                    'type' => 'checkbox',
                    'title' => '是否开启选中效果',
                    'std' => 0,
                ),
                'section_tab_active' => array(
                    'type' => 'slider',
                    'title' => '默认选中第几个',
                    'std' => 1,
                    'depends' => array(
                        array('is_item_active', '=', 1)
                    ),
                ),
                'section_tab_num' => array(
                    'type' => 'slider',
                    'title' => '菜单一行显示个数',
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 5,
                        'sm' => 5,
                        'xs' => 3
                    ),
                ),
                'section_tab_height' => array(
                    'type' => 'slider',
                    'title' => '菜单高度',
                    'max' => 600,
                    'min' => 150,
                    'responsive' => true,

                ),
                'section_tab_m' => array(
                    'type' => 'slider',
                    'title' => '菜单外边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                ),
                'section_tab_p' => array(
                    'type' => 'margin',
                    'title' => '菜单内边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                ),
                'section_name_m' => array(
                    'type' => 'margin',
                    'title' => '标题外边距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_name', '=', 1)
                    ),
                ),
                'section_name_p' => array(
                    'type' => 'margin',
                    'title' => '标题内边距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_name', '=', 1)
                    ),
                ),
                'section_icon_m' => array(
	                'type' => 'margin',
	                'title' => '图标外边距',
	                'max' => 100,
	                'min' => 0,
	                'responsive' => true,
                ),
                'section_line_m' => array(
                    'type' => 'slider',
                    'title' => '下划线下边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_name_line', '=', 1)
                    ),
                ),
                'section_desc_p' => array(
                    'type' => 'margin',
                    'title' => '简介内边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_desc', '=', 1)
                    ),
                ),
                'section_desc_height' => array(
                    'type' => 'slider',
                    'title' => '简介高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_desc', '=', 1)
                    ),
                ),
                'content_style' => array(
                    'type' => 'select',
                    'title' => '排列方式',
                    'values' => array(
                        'column' => '上图下文',
                        'column-reverse' => '上文下图',
                        'row' => '左图右文',
                        'row-reverse' => '左文右图',
                    ),
                    'std' => 'column',
                ),
                'content_icon_align' => array(
                    'type' => 'select',
                    'title' => '图标对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'center',
                ),
                'content_icon_position' => array(
                    'type' => 'select',
                    'title' => '图标位置',
                    'values' => array(
                        'type01' => '标题上方',
                        'type02' => '标题下方',
                        'type03' => '标题左侧',
                    ),
                    'std' => 'type01',
                ),
                'content_name_align' => array(
                    'type' => 'select',
                    'title' => '标题对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                        'type01' => '奇偶交错',
                    ),
                    'std' => 'center',
                ),
                'content_s_name_align' => array(
                    'type' => 'select',
                    'title' => '副标题对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('is_show_s_name', '=', 1)
                    ),
                ),
                'content_desc_align' => array(
                    'type' => 'select',
                    'title' => '简介对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'center',
                ),
                'is_zhong_label' => array(
                    'type' => 'checkbox',
                    'title' => '菜单之间添加箭头图标',
                    'std' => 0,
                ),
                'jintou_icon' => array(
                    'type' => 'media',
                    'title' => '箭头图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220516/a778388170b2a96c37c594cfe88300bc.png',
                    'depends' => array(
                        array('is_zhong_label', '=', 1),
                    ),
                ),
                'jiantou_width' => array(
                    'type' => 'slider',
                    'title' => '箭头宽',
                    'std' => 36,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_height' => array(
                    'type' => 'slider',
                    'title' => '箭头高',
                    'std' => 36,
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_top' => array(
                    'type' => 'slider',
                    'title' => '箭头上侧间距',
                    'std' => 50,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_right' => array(
                    'type' => 'slider',
                    'title' => '箭头左侧间距',
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_nth' => array(
                    'type' => 'slider',
                    'title' => '第几个箭头旋转90°',
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 0,
                        'sm' => 0,
                        'xs' => 3,
                    ),
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_nthtop' => array(
                    'type' => 'slider',
                    'title' => '旋转箭头的上侧间距',
                    'std' => 50,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),
                'jiantou_nthright' => array(
                    'type' => 'slider',
                    'title' => '旋转箭头的左侧间距',
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('is_zhong_label', '=', '1')
                    )
                ),



                'is_bgImg' => array(
                    'type' => 'checkbox',
                    'title' => '启用背景图片',
                    'std' => 0,
                ),
                'is_unite_bgColor' => array(
                    'type' => 'checkbox',
                    'title' => '是否统一背景',
                    'std' => 0,
                ),
                'bgColor_wz' => array(
                    'type' => 'select',
                    'title' => '背景色位置',
                    'std' => '1',
                    'values' => array(
                        '1' => '菜单',
                        '2' => '标题',
                        '3' => '简介',
                    ),
                    'depends' => array(
                        array('is_unite_bgColor', '=', 1),
                    )
                ),
                'is_hidden_icon' => array(
                    'type' => 'checkbox',
                    'title' => '图标超出隐藏',
                    'std' => 0,
                ),
                'is_show_name' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示标题',
                    'std' => 1,
                ),
                'is_show_s_name' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示副标题',
                    'std' => 0,
                ),
                'is_show_name_line' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示标题下方下划线',
                    'std' => 0,
                ),
                'line_border_align' => array(
                    'type' => 'select',
                    'title' => '下划线对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'center',
                    'depends' => array(
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_position' => array(
                    'type' => 'select',
                    'title' => '下划线位置',
                    'values' => array(
                        'type01' => '标题下方',
                        'type02' => '标题上方',
                    ),
                    'std' => 'type01',
                    'depends' => array(
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'is_show_desc' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示简介',
                    'std' => 0,
                ),
                'is_show_content' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示内容（需开启选中效果）',
                    'std' => 0,
                ),
                'part11' => array(
                    'type' => 'separator',
                    'title' => '菜单项内容配置',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_p' => array(
                    'type' => 'margin',
                    'title' => '菜单项内容边距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    ),
                ),
                'item_content_style' => array(
                    'type' => 'select',
                    'title' => '排列方式（pc及平板）',
                    'values' => array(
                        'column' => '上文下图',
                        'column-reverse' => '上图下文',
                        'row' => '左文右图',
                        'row-reverse' => '左图右文',
                    ),
                    'std' => 'row',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_style_xs' => array(
                    'type' => 'select',
                    'title' => '排列方式（手机端）',
                    'values' => array(
                        'column' => '上文下图',
                        'column-reverse' => '上图下文',
                        'row' => '左文右图',
                        'row-reverse' => '左图右文',
                    ),
                    'std' => 'column',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_title_align' => array(
                    'type' => 'select',
                    'title' => '内容标题对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_between' => array(
                    'type' => 'slider',
                    'title' => '图文之间间距',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 154,
                        'sm' => 80,
                        'xs' => 40,
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_bg' => array(
                    'type' => 'color',
                    'title' => '内容部分整体背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_img_border_radius' => array(
                    'type' => 'slider',
                    'title' => '内容部分图片边框半径',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 4
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_styles' => array(
                    'type' => 'buttons',
                    'title' => '内容配置项',
                    'std' => 'content',
                    'values' => array(
                        array(
                            'label' => '文字',
                            'value' => 'content'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'image'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('is_show_content', '=', 1),
                    )
                ),
                'item_content_t_fontsize' => array(
                    'type' => 'slider',
                    'title' => '内容标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_t_fontW' => array(
                    'type' => 'checkbox',
                    'title' => '内容标题文字加粗',
                    'std' => 0,
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_t_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '内容标题文字行高',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 32,
                        'sm' => 32,
                        'xs' => 32,
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_t_fontcolor' => array(
                    'type' => 'color',
                    'title' => '内容标题文字颜色',
                    'std' => '#111111',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_t_mb' => array(
                    'type' => 'slider',
                    'title' => '内容标题下边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 54,
                        'sm' => '',
                        'xs' => '',
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_d_fontsize' => array(
                    'type' => 'slider',
                    'title' => '内容简介文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 16,
                        'sm' => 16,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_d_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '内容简介文字行高',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 24,
                        'sm' => 24,
                        'xs' => 24,
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_d_fontcolor' => array(
                    'type' => 'color',
                    'title' => '内容简介文字颜色',
                    'std' => '#979A9F',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'content'),
                    )
                ),
                'item_content_image_width' => array(
                    'type' => 'slider',
                    'title' => '内容图片宽度(不填写则为100%展示)',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 550,
                        'sm' => '',
                        'xs' => '',
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'image'),
                    )
                ),
                'item_content_image_height' => array(
                    'type' => 'slider',
                    'title' => '内容图片高度(不填写则为100%展示)',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 310,
                        'sm' => '',
                        'xs' => '',
                    ),
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'image'),
                    )
                ),
                'item_image_fit' => array(
                    'type' => 'select',
                    'title' => '图片填充方式',
                    'values' => array(
                        'none' => '保留原有元素内容的长度和宽度',
                        'fill' => '不保证保持原有的比例，拉伸填充',
                        'contain' => '保持原有尺寸比例，缩放',
                        'cover' => '保持原有尺寸比例，铺满',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('is_show_content', '=', 1),
                        array('item_content_styles', '=', 'image'),
                    )
                ),
                'part05' => array(
                    'type' => 'separator',
                    'title' => '更多按钮配置',
                    'depends' => array()
                ),
                'is_show_more' => array(
                    'type' => 'checkbox',
                    'title' => '是否显示更多按钮',
                    'std' => 0,
                ),
                'more_icon' => array(
                    'type' => 'media',
                    'title' => '更多按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210817/cd03035e0bdb7530651387167c7645ed.png',
                    'depends' => array(
                        array('is_show_more', '=', 1),
                    ),
                ),
                'more_icon_width' => array(
                    'type' => 'slider',
                    'title' => '更多按钮图标宽度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 100,
                        'sm' => 80,
                        'xs' => 40,
                    ),
                    'depends' => array(
                        array('is_show_more', '=', 1),
                    )
                ),
                'more_icon_m' => array(
                    'type' => 'margin',
                    'title' => '更多按钮外边距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('is_show_more', '=', 1),
                    )
                ),
                'more_icon_align' => array(
                    'type' => 'select',
                    'title' => '更多按钮对齐方式',
                    'values' => array(
                        'flex-start' => '左对齐',
                        'center' => '居中',
                        'flex-end' => '右对齐',
                    ),
                    'std' => 'flex-start',
                    'depends' => array(
                        array('is_show_more', '=', 1),
                    )
                ),
                'is_show_more_normal' => array(
                    'type' => 'checkbox',
                    'title' => '正常状态关闭更多按钮',
                    'std' => 0,
                    'depends' => array(
                        array('is_show_more', '=', 1),
                    )
                ),
                'section_style' => array(
                    'type' => 'buttons',
                    'title' => '菜单状态',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        )
                    ),
                    'tabs' => true,
                ),
                'section_tab_icon_width' => array(
                    'type' => 'slider',
                    'title' => '图标宽度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 100,
                        'sm' => 80,
                        'xs' => 50,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_icon_height' => array(
                    'type' => 'slider',
                    'title' => '图标高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 100,
                        'sm' => 80,
                        'xs' => 50,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'icon_zdywz' => array(
                    'type' => 'checkbox',
                    'title' => '图标自定义位置',
                    'std' => 0,
                ),
                'icon_top' => array(
                    'type' => 'slider',
                    'title' => '上间距',
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('icon_zdywz', '=', '1')
                    )
                ),
                'icon_left' => array(
                    'type' => 'slider',
                    'title' => '左间距',
                    'std' => 0,
                    'max' => 200,
                    'min' => 0,
                    'depends' => array(
                        array('icon_zdywz', '=', '1')
                    )
                ),

                'section_tab_fontW' => array(
                    'type' => 'checkbox',
                    'title' => '标题加粗',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    ),
                ),
                'section_name_first' => array(
                    'type' => 'checkbox',
                    'title' => '标题首字放大',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    ),
                ),
                'fontsize_first' => array(
                    'type' => 'slider',
                    'title' => '字体大小',
                    'std' => 45,
                    'max-width' => 100,
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('section_name_first', '=', '1')
                    ),
                ),
                'section_tab_fontsize' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 18,
                        'sm' => 18,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '标题文字行高',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 32,
                        'sm' => 32,
                        'xs' => 32,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_text_Height' => array(
                    'type' => 'slider',
                    'title' => '标题高度',
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_fontcolor' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_name_bgColor' => array(
                    'type' => 'color',
                    'title' => '标题背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_name_bgImg' => array(
                    'type' => 'media',
                    'title' => '标题背景图片',
                    'std' => '',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'part13' => array(
                    'type' => 'separator',
                    'title' => '标题需要加粗处理的字符设置部分',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_item_name_b_bgImg' => array(
                    'type' => 'media',
                    'title' => '标题需要加粗处理的字符背景图片',
                    'std' => '',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'part12' => array(
                    'type' => 'separator',
                    'title' => '副标题设置部分',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_s_name_fontsize' => array(
                    'type' => 'slider',
                    'title' => '副标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 14,
                        'sm' => 12,
                        'xs' => 12,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_s_name_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '副标题文字行高',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 32,
                        'sm' => 32,
                        'xs' => 32,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_tab_s_name_fontcolor' => array(
                    'type' => 'color',
                    'title' => '副标题文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_desc_fontsize' => array(
                    'type' => 'slider',
                    'title' => '简介文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 16,
                        'sm' => 16,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_desc_lineHeight' => array(
                    'type' => 'slider',
                    'title' => '简介文字行高',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 16,
                        'sm' => 16,
                        'xs' => 16,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'section_desc_fontcolor' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal')
                    )
                ),
                'part03' => array(
                    'type' => 'separator',
                    'title' => '下划线配置',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_width' => array(
                    'type' => 'slider',
                    'title' => '下划线宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_height' => array(
                    'type' => 'slider',
                    'title' => '下划线高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_color_style' => array(
                    'type' => 'buttons',
                    'title' => '下划线样式',
                    'std' => 'color',
                    'values' => array(
                        array(
                            'label' => '纯色',
                            'value' => 'color'
                        ),
                        array(
                            'label' => '渐变色',
                            'value' => 'gradient'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1),
                    )
                ),
                'line_border_color' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'color')
                    )
                ),
                'line_border_style' => array(
                    'type' => 'select',
                    'title' => '下划线样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'color')
                    )
                ),
                'line_border_bgGradient' => array(
                    'type' => 'gradient',
                    'title' => '渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'gradient')
                    ),
                ),
                'section_tab_bgColor' => array(
                    'type' => 'color',
                    'title' => '统一背景颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_unite_bgColor', '=', 1)
                    )
                ),
                'is_shadow' => array(
                    'type' => 'checkbox',
                    'title' => '开启投影',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                    )
                ),
                'box_color' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_shadow', '=', 1)
                    )
                ),
                'box_h_shadow' => array(
                    'type' => 'slider',
                    'title' => '水平偏移',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_shadow', '=', 1)
                    )
                ),
                'box_v_shadow' => array(
                    'type' => 'slider',
                    'title' => '垂直偏移',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_shadow', '=', 1)
                    )
                ),
                'box_blur' => array(
                    'type' => 'slider',
                    'title' => '模糊',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_shadow', '=', 1)
                    )
                ),
                'box_spread' => array(
                    'type' => 'slider',
                    'title' => '扩展',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_shadow', '=', 1)
                    )
                ),
                'is_border' => array(
                    'type' => 'checkbox',
                    'title' => '使用边框',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                    )
                ),
                'border_width' => array(
                    'type' => 'slider',
                    'title' => '边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_border', '=', 1)
                    )
                ),
                'custom_border_width' => array(
                    'type' => 'checkbox',
                    'title' => '自定义边框宽度（开启后需要每项单独设置）',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_border', '=', 1)
                    )
                ),

                'border_color' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_border', '=', 1)
                    )
                ),
                'border_style' => array(
                    'type' => 'select',
                    'title' => '边框样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_border', '=', 1)
                    )
                ),
                'border_radius' => array(
                    'type' => 'slider',
                    'title' => '边框半径',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 10,
                        'sm' => 10,
                        'xs' => 10,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'normal'),
                        array('is_border', '=', 1)
                    )
                ),
                /*滑过*/
                'is_ngImg_scale' => array(
                    'type' => 'checkbox',
                    'title' => '背景图滑过放大',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_bgImg', '=', '1'),
                    )
                ),
                'section_tab_icon_width_hover' => array(
                    'type' => 'slider',
                    'title' => '图标宽度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_icon_height_hover' => array(
                    'type' => 'slider',
                    'title' => '图标高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_fontsize_hover' => array(
                    'type' => 'slider',
                    'title' => '标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_fontcolor_hover' => array(
                    'type' => 'color',
                    'title' => '标题文字颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_name_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '标题背景颜色',
                    'std' => '',
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_name_bgImg_hover' => array(
                    'type' => 'media',
                    'title' => '标题背景图片',
                    'std' => '',
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_s_name_fontsize_hover' => array(
                    'type' => 'slider',
                    'title' => '副标题文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_tab_s_name_fontcolor_hover' => array(
                    'type' => 'color',
                    'title' => '副标题文字颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_desc_fontsize_hover' => array(
                    'type' => 'slider',
                    'title' => '简介文字大小',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'section_desc_fontcolor_hover' => array(
                    'type' => 'color',
                    'title' => '简介文字颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover')
                    )
                ),
                'part04' => array(
                    'type' => 'separator',
                    'title' => '下划线配置',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_width_hover' => array(
                    'type' => 'slider',
                    'title' => '下划线宽度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_height_hover' => array(
                    'type' => 'slider',
                    'title' => '下划线高度',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1)
                    )
                ),
                'line_border_color_hover' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#ffffff',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'color')
                    )
                ),
                'line_border_style_hover' => array(
                    'type' => 'select',
                    'title' => '下划线样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'std' => 'solid',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'color')
                    )
                ),
                'line_border_bgGradient_hover' => array(
                    'type' => 'gradient',
                    'title' => '渐变色背景',
                    'std' => array(
                        "color" => "#00c6fb",
                        "color2" => "#005bea",
                        "deg" => "45",
                        "type" => "linear"
                    ),
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_show_name_line', '=', 1),
                        array('line_border_color_style', '=', 'gradient')
                    ),
                ),
                'section_tab_bgColor_hover' => array(
                    'type' => 'color',
                    'title' => '统一背景颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_unite_bgColor', '=', 1)
                    )
                ),
                'is_shadow_hover' => array(
                    'type' => 'checkbox',
                    'title' => '开启投影',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                    )
                ),
                'box_color_hover' => array(
                    'type' => 'color',
                    'title' => '投影颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_shadow_hover', '=', 1)
                    )
                ),
                'box_h_shadow_hover' => array(
                    'type' => 'slider',
                    'title' => '水平偏移',
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_shadow_hover', '=', 1)
                    )
                ),
                'box_v_shadow_hover' => array(
                    'type' => 'slider',
                    'title' => '垂直偏移',
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_shadow_hover', '=', 1)
                    )
                ),
                'box_blur_hover' => array(
                    'type' => 'slider',
                    'title' => '模糊',
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_shadow_hover', '=', 1)
                    )
                ),
                'box_spread_hover' => array(
                    'type' => 'slider',
                    'title' => '扩展',
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_shadow_hover', '=', 1)
                    )
                ),
                'is_border_hover' => array(
                    'type' => 'checkbox',
                    'title' => '使用边框',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                    )
                ),
                'border_width_hover' => array(
                    'type' => 'slider',
                    'title' => '边框宽度',
                    'max' => 100,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_border_hover', '=', 1)
                    )
                ),
                'border_color_hover' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_border_hover', '=', 1)
                    )
                ),
                'border_style_hover' => array(
                    'type' => 'select',
                    'title' => '边框样式',
                    'values' => array(
                        'none' => '无边框',
                        'solid' => '实线',
                        'dashed' => '虚线',
                        'dotted' => '点状边框',
                        'double' => '双线',
                        'groove' => '3D 凹槽边框',
                        'ridge' => '3D 垄状边框',
                        'inset' => '3D inset 边框',
                        'outset' => '3D outset 边框',
                    ),
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_border_hover', '=', 1)
                    )
                ),
                'border_radius_hover' => array(
                    'type' => 'slider',
                    'title' => '边框半径',
                    'max' => 1000,
                    'min' => 0,
                    'std' => array(
                        'md' => 1,
                        'sm' => 1,
                        'xs' => 1,
                    ),
                    'responsive' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_border_hover', '=', 1)
                    )
                ),
                'is_animate_hover' => array(
                    'type' => 'checkbox',
                    'title' => '使用动画',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                    )
                ),
                'animate_style' => array(
                    'type' => 'buttons',
                    'title' => '动画作用于',
                    'std' => 'icon',
                    'values' => array(
                        array(
                            'label' => '背景色',
                            'value' => 'bg'
                        ),
                        array(
                            'label' => '标题',
                            'value' => 'name'
                        ),
                        array(
                            'label' => '副标题',
                            'value' => 's_name'
                        ),
                        array(
                            'label' => '下划线',
                            'value' => 'name_line'
                        ),
                        array(
                            'label' => '图标',
                            'value' => 'icon'
                        ),
                        array(
                            'label' => '边框',
                            'value' => 'border_line'
                        ),
                        array(
                            'label' => '菜单',
                            'value' => 'menu'
                        )
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_bg' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'middleTopExpansion' => '中间上下展开',
                        'middleLeftExpansion' => '中间左右展开',
                        'bottomExpansion' => '底部展开',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'bg'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_bg_time' => array(
                    'type' => 'slider',
                    'title' => '动画时长(毫秒)',
                    'std' => '300',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'bg'),
                        array('is_animate_hover', '=', 1),
                        array('animate_icon', '!=', 'none'),
                    )
                ),
                'animate_name' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'translateY' => 'Y轴移动',
                        'translateX' => 'X轴移动',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'name'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_name_translate' => array(
                    'type' => 'slider',
                    'title' => '移动距离(可以为负值)',
                    'std' => -12,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'name'),
                        array('is_animate_hover', '=', 1),
                        array('animate_name', '!=', "none"),
                    )
                ),
                'animate_s_name' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'translateY' => 'Y轴移动',
                        'translateX' => 'X轴移动',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 's_name'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_s_name_translate' => array(
                    'type' => 'slider',
                    'title' => '移动距离(可以为负值)',
                    'std' => -12,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 's_name'),
                        array('is_animate_hover', '=', 1),
                        array('animate_name', '!=', "none"),
                    )
                ),
                'animate_name_line' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'translateY' => 'Y轴移动',
                        'translateX' => 'X轴移动',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'name_line'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_name_line_translate' => array(
                    'type' => 'slider',
                    'title' => '移动距离(可以为负值)',
                    'std' => -12,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'name_line'),
                        array('is_animate_hover', '=', 1),
                        array('animate_name', '!=', "none"),
                    )
                ),
                'animate_icon' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'bounce' => '反弹',
                        'flash' => '闪烁',
                        'pulse' => '放大',
                        'rubberBand' => '橡皮筋',
                        'shake' => '抖动',
                        'swing' => '摆动',
                        'flipInY' => 'Y轴翻转',
                        'proRotate' => 'Y轴翻转-放大',
                        'flipInX' => 'X轴翻转',
                        'rotateZ(360deg)' => 'Z轴旋转',
                        'rotateX(360deg)' => 'X轴旋转',
                        'rotateY(360deg)' => 'Y轴旋转',
                        'translateY' => 'Y轴移动',

                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'icon'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_time' => array(
                    'type' => 'slider',
                    'title' => '动画时长(毫秒)',
                    'std' => '300',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'icon'),
                        array('is_animate_hover', '=', 1),
                        array('animate_icon', '!=', 'none'),
                        array('animate_icon', '!=', 'bounce'),
                        array('animate_icon', '!=', 'flash'),
                        array('animate_icon', '!=', 'pulse'),
                        array('animate_icon', '!=', 'rubberBand'),
                        array('animate_icon', '!=', 'shake'),
                        array('animate_icon', '!=', 'swing'),
                        array('animate_icon', '!=', 'flipInY'),
                        array('animate_icon', '!=', 'proRotate'),
                        array('animate_icon', '!=', 'flipInX'),
                    )
                ),
                'animate_border_line' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'type01' => '环绕变色(边框半径为0)',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'border_line'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_menu' => array(
                    'type' => 'select',
                    'title' => '动画效果',
                    'values' => array(
                        'none' => '无',
                        'translateY' => 'Y轴移动',
                        'translateX' => 'X轴移动',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'menu'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_translate' => array(
                    'type' => 'slider',
                    'title' => '移动距离(可以为负值)',
                    'std' => -12,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'menu'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_translate_ruchang' => array(
                    'type' => 'checkbox',
                    'title' => '开启入场动画',
                    'std' => 0,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'menu'),
                        array('is_animate_hover', '=', 1),
                    )
                ),
                'animate_ruch_delay' => array(
                    'type' => 'slider',
                    'title' => '菜单入场间隔时间',
                    'std' => 0.2,
                    'depends' => array(
                        array('section_style', '=', 'hover'),
                        array('animate_style', '=', 'menu'),
                        array('is_animate_hover', '=', 1),
                        array('animate_translate_ruchang', '=', 1),

                    )
                ),
            ),
        ),
    )
);
