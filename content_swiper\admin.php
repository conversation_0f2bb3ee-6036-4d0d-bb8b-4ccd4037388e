<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'content_swiper',
        'title' => JText::_('图文轮播'),
        'desc' => JText::_('图文轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播项样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'carousel_item_img' => '/components/com_jwpagefactory/addons/content_swiper/assets/images/font.png',
                            'carousel_item_img_width' =>array('md'=>384,'sm'=>200,'xs'=>100),
                            'item_description' => '这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。',
                            'carousel_item_img_padding' =>array('md'=>'52px 42px 53px 254px','sm'=>'20px 10px 60px 200px','xs'=>'15px 15px 30px 150px'),
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'carousel_item_img' => '/components/com_jwpagefactory/addons/content_swiper/assets/images/font.png',
                            'carousel_item_img_width' =>array('md'=>384,'sm'=>200,'xs'=>100),
                            'item_description' => '这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。',
                            'carousel_item_img_padding' =>array('md'=>'52px 42px 53px 254px','sm'=>'20px 10px 60px 200px','xs'=>'15px 15px 30px 150px'),
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                            'carousel_item_img' => '/components/com_jwpagefactory/addons/content_swiper/assets/images/font.png',
                            'carousel_item_img_width' => array('md'=>384,'sm'=>200,'xs'=>100),
                            'item_description' => '这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。',
                            'carousel_item_img_padding' =>array('md'=>'52px 42px 53px 254px','sm'=>'20px 10px 60px 200px','xs'=>'15px 15px 30px 150px'),
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

//                        字和图片
                        'item_content_type' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => '文字',
                                    'value' => 'font'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('轮播项描述'),
                            'desc' => JText::_('轮播项描述'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            ),
                            'std' => '					这里是一个户外的群众性大戏台，定期举办表演，种类包含民乐、相声、京东大鼓、魔术、杂技、京剧、评剧等节目，将演艺与民俗、旅游与文化、古典与传统相结合，在丰富哈尔滨市民的业余文化生活的同时，也让来哈尔滨观光游览的外地游客有机会欣赏到哈尔滨民间的传统文化。
'
                        ),
                        'carousel_item_img' => array(
                            'type' => 'media',
                            'title' => '轮播图中间图片链接',
                            'desc' => '轮播图中间图片链接',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'std' => '/components/com_jwpagefactory/addons/content_swiper/assets/images/font.png',
                        ),
                        'carousel_item_img_width' => array(
                            'type' => 'slider',
                            'title' => '图片宽度',
                            'desc' => '图片宽度',
                            'std' => array('md'=>384,'sm'=>300,'xs'=>200),
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'max' => 1000,
                            'responsive'=>true
                        ),

                        'carousel_item_img_padding' => array(
                            'type' => 'margin',
                            'title' => JText::_('图片内边距'),
                            'max' => 100,
                            'min' => 0,
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'std' => array('md'=>'52px 42px 53px 254px','sm'=>'20px 10px 60px 200px','xs'=>'15px 15px 30px 150px'),
                            'responsive'=>true
                        ),
                    ),
                ),
                'carousel_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播容器左边距'),
                    'desc' => JText::_('轮播容器左边距'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('paved', '=', 0),
                    ),
                    'min' => 0,
                    'max' => 1000,
                    'std' => 0,
                    'responsive' => true,
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 800,
                        'sm' => 600,
                        'xs' => 400
                    ),
                    'responsive' => true,
                ),
                'carousel_view_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播图片区域宽度'),
                    'desc' => JText::_('轮播图片区域宽度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('paved', '=', 0),
                    ),
                    'min' => 100,
                    'max' => 2000,
                    'std' => array(
                        'md' => 800,
                        'sm' => 600,
                        'xs' => 300
                    ),
                    'responsive' => true,
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 1
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 2500
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 4500
                ),

                'content_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('内容布局'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std' => 'desc_style',
                    'values' => array(
                        array(
                            'label' => '文字',
                            'value' => 'desc_style'
                        ),
                        array(
                            'label' => '背景',
                            'value' => 'background_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'content' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),


                //Description style
                'description_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => array('md'=>16,'sm'=>14,'xs'=>12),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 30,
                    'responsive'=>true
                ),
                'description_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => array(
                        'md'=>40,'sm'=>30,'xs'=>20
                    ),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                    'responsive'=>true
                ),

                'description_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std' => '#fff'
                ),

//                背景设置
                'background_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('背景宽度'),
                    'max' => 1000,
                    'std' => array('md'=>680,'sm'=>500,'xs'=>300),
                    'depends' => array(
                        array('content_style', '=', 'background_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive'=>true
                ),
                'background_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('背景高度'),
                    'max' => 2000,
                    'std' => array('md'=>739,'sm'=>500,'xs'=>300),
                    'depends' => array(
                        array('content_style', '=', 'background_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive'=>true
                ),
                /*'background_color' => array(
                    'type' => 'color',
                    'title' => JText::_('背景颜色'),
                    'std' => '#000000AB',
                    'depends' => array(
                        array('content_style', '=', 'background_style'),
                        array('carousel_options', '=', 'item_style'),
                    )
                ),*/
                'background_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('背景左边距（%）'),
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(
                        array('content_style', '=', 'background_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std' => array('md'=>52,'sm'=>40,'xs'=>20),
                    'responsive'=>true
                ),

                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器设置'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),


                // Arrow style
                'carousel_arrow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否展示翻页按钮'),
                    'desc' => JText::_('是否展示翻页按钮'),
                    'std' => 1,
                    'depends' => array(

                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'arrow_position_verti' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮下边距'),
                    'desc' => JText::_('翻页按钮下边距'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 200,
                    'std' => 55,
                    'responsive'=>true
                ),
                'arrow_position_hori' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮右边距'),
                    'desc' => JText::_('翻页按钮右边距'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 200,
                    'std' => 45,
                    'responsive'=>true
                ),
                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮容器宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 80,
                ),
                'arrow_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮容器高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 28,
                ),
                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => '16',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),

                    )
                ),
                //Arrow hover
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),

                    )
                ),
            ),
        )
    )
);