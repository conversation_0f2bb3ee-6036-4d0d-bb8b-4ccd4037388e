<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'baroque_part03',
        'title' => '巴洛克图片布局',
        'desc' => '',
        'category' => '图片',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                // 切换item
                'section_tab_item' => array(
                    'title' => '选项卡列表',
                    'attr' => array(
                        'control_type' => array(
                            'type' => 'buttons',
                            'title' => '配置项',
                            'std' => 'img',
                            'values' => array(
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                ),
                                array(
                                    'label' => '遮罩',
                                    'value' => 'cover'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'img' => array(
                            'type' => 'media',
                            'title' => '背景图片',
                            'desc' => '',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png',
                            'depends' => array(
                                array('control_type', '=', 'img')
                            )
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => '是否跳转站内链接',
                            'std' => 0,
                            'depends' => array(
                                array('control_type', '=', 'img')
                            )
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('control_type', '=', 'img'),
                                array('is_link', '=', 1)
                            )
                        ),
                        'is_show_cover' => array(
                            'type' => 'checkbox',
                            'title' => '开启遮罩',
                            'std' => 0,
                            'depends' => array(
                                array('control_type', '=', 'cover')
                            )
                        ),
                        'is_cover' => array(
                            'type' => 'checkbox',
                            'title' => '默认是否显示遮罩',
                            'std' => 0,
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            )
                        ),
                        'bg_color' => array(
                            'type' => 'color',
                            'title' => '遮罩颜色',
                            'std' => 'rgba(94, 5, 0, 0.6)',
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'cover_content' => array(
                            'type' => 'media',
                            'title' => 'pc遮罩内容',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210703/cc1a7fef7c871b6487114cf16553070f.png',
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'cover_content_wap' => array(
                            'type' => 'media',
                            'title' => '手机遮罩内容',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210703/cc1a7fef7c871b6487114cf16553070f.png',
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'cover_content_width' => array(
                            'type' => 'slider',
                            'title' => '内容图片宽度',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 256,
                                'sm' => 240,
                                'xs' => 158,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover')
                            ),
                        ),
                        'cover_content_height' => array(
                            'type' => 'slider',
                            'title' => '内容图片高度',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 256,
                                'sm' => 240,
                                'xs' => 158,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover')
                            ),
                        ),
                        'content_top' => array(
                            'type' => 'slider',
                            'title' => '内容top距离',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'content_right' => array(
                            'type' => 'slider',
                            'title' => '内容right距离',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'content_bottom' => array(
                            'type' => 'slider',
                            'title' => '内容bottom距离',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),
                        'content_left' => array(
                            'type' => 'slider',
                            'title' => '内容left距离',
                            'max' => 2000,
                            'min' => 0,
                            'responsive' => true,
                            'std' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'depends' => array(
                                array('control_type', '=', 'cover'),
                                array('is_show_cover', '=', 1)
                            ),
                        ),

                    ),
                    'std' => array(
                        array(
                            'img'=> 'https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png',
                            'is_link'=> 0,
                            'is_show_cover' => 1,
                            'is_cover' => 1,
                            'bg_color' => 'rgba(255, 255, 255, 0)',
                            'cover_content' => 'https://oss.lcweb01.cn/joomla/20210703/cc1a7fef7c871b6487114cf16553070f.png',
                            'content_top' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'content_right' => array(
                                'md' => 50,
                                'sm' => 50,
                                'xs' => 50,
                            ),
                            'content_bottom' => array(
                                'md' => 0,
                                'sm' => 0,
                                'xs' => 0,
                            ),
                            'content_left' => array(
                                'md' => 0,
                                'sm' => 0,
                                'xs' => 0,
                            ),
                            'cover_content_width' => array(
                                'md' => 338,
                                'sm' => 338,
                                'xs' => 338,
                            ),
                            'cover_content_height' => array(
                                'md' => 297,
                                'sm' => 297,
                                'xs' => 297,
                            ),
                        ),
                        array(
                            'img'=> 'https://oss.lcweb01.cn/joomla/20210626/bd1f5b30849c47ba8c4779210344cd1f.png',
                            'is_link'=> 0,
                            'is_show_cover' => 0,
                            'is_cover' => 0,
                        ),
                        array(
                            'img'=> 'https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png',
                            'is_link'=> 0,
                            'is_show_cover' => 0,
                            'is_cover' => 0,
                        ),
                        array(
                            'img'=> 'https://oss.lcweb01.cn/joomla/20210702/55f146149fec215ccb809237d6cc5e46.png',
                            'is_link'=> 1,
                            'is_show_cover' => 1,
                            'is_cover' => 1,
                            'bg_color' => 'rgba(94, 5, 0, 0.9)',
                            'cover_content' => 'https://oss.lcweb01.cn/joomla/20210703/a26c5d2c978e38bfc239c6db32885aef.png',
                            'content_top' => array(
                                'md' => 40,
                                'sm' => 40,
                                'xs' => 40,
                            ),
                            'content_right' => array(
                                'md' => 0,
                                'sm' => 0,
                                'xs' => 0,
                            ),
                            'content_bottom' => array(
                                'md' => 0,
                                'sm' => 0,
                                'xs' => 0,
                            ),
                            'content_left' => array(
                                'md' => 50,
                                'sm' => 50,
                                'xs' => 50,
                            ),
                            'cover_content_width' => array(
                                'md' => 80,
                                'sm' => 80,
                                'xs' => 80,
                            ),
                            'cover_content_height' => array(
                                'md' => 264,
                                'sm' => 264,
                                'xs' => 264,
                            ),
                        ),
                    ),
                ),
                'section_img_mg' => array(
                    'type' => 'slider',
                    'title' => '图片区域的边距',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 12
                ),
                'section_left_num' => array(
                    'type' => 'select',
                    'title' => '左侧图显示个数',
                    'values' => array(
                        1 => '一个',
                        2 => '两个',
                    ),
                    'std' => 1,
                ),
                'section_responsive' => array(
                    'type' => 'buttons',
                    'title' => '适配设置',
                    'std' => 'pc',
                    'values' => array(
                        array(
                            'label' => 'pc',
                            'value' => 'pc'
                        ),
                        array(
                            'label' => '平板',
                            'value' => 'pad'
                        ),
                        array(
                            'label' => '手机',
                            'value' => 'wap'
                        ),
                    ),
                    'tabs' => true,
                ),
                'section_height' => array(
                    'type' => 'slider',
                    'title' => 'pc 图片区域的高度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 800,
                    'depends' => array(
                        array('section_responsive', '=', 'pc')
                    )
                ),
                'section_left_width' => array(
                    'type' => 'slider',
                    'title' => 'pc 左侧图宽度（不填默认50%）',
                    'max' => 2000,
                    'min' => 0,
                    'depends' => array(
                        array('section_responsive', '=', 'pc')
                    )
                ),
                'section_left_top_img_height_sm' => array(
                    'type' => 'slider',
                    'title' => '平板 左侧第一张图高度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 120,
                    'depends' => array(
                        array('section_responsive', '=', 'pad'),
                        array('section_left_num', '=', 2)
                    )
                ),
                'section_right_top_height_sm' => array(
                    'type' => 'slider',
                    'title' => '平板 右侧上方图高度（不填默认50%）',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 400,
                    'depends' => array(
                        array('section_responsive', '=', 'pad')
                    )
                ),
                'section_right_bottom_first_width_sm' => array(
                    'type' => 'slider',
                    'title' => '平板 右侧下方第一张图宽度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 358,
                    'depends' => array(
                        array('section_responsive', '=', 'pad')
                    )
                ),
                'section_height_sm' => array(
                    'type' => 'slider',
                    'title' => '平板 图片区域的高度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 500,
                    'depends' => array(
                        array('section_responsive', '=', 'pad')
                    )
                ),
                'section_left_width_sm' => array(
                    'type' => 'slider',
                    'title' => '平板 左侧图宽度（不填默认50%）',
                    'max' => 2000,
                    'min' => 0,
                    'depends' => array(
                        array('section_responsive', '=', 'pad')
                    )
                ),

                'section_left_top_img_height' => array(
                    'type' => 'slider',
                    'title' => 'pc 左侧第一张图高度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 120,
                    'depends' => array(
                        array('section_responsive', '=', 'pc'),
                        array('section_left_num', '=', 2)
                    )
                ),
                'section_right_top_height' => array(
                    'type' => 'slider',
                    'title' => 'pc 右侧上方图高度（不填默认50%）',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 400,
                    'depends' => array(
                        array('section_responsive', '=', 'pc')
                    )
                ),
                'section_right_bottom_first_width' => array(
                    'type' => 'slider',
                    'title' => 'pc 右侧下方第一张图宽度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 358,
                    'depends' => array(
                        array('section_responsive', '=', 'pc')
                    )
                ),
                'item_img_height' => array(
                    'type' => 'slider',
                    'title' => '手机图高度',
                    'max' => 2000,
                    'min' => 0,
                    'std' => 358,
                    'depends' => array(
                        array('section_responsive', '=', 'wap')
                    )
                ),
                'section_padding' => array(
                    'type' => 'slider',
                    'title' => '手机区域内边距',
                    'max' => 200,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('section_responsive', '=', 'wap')
                    )
                ),
            ),
        ),
    )
);
